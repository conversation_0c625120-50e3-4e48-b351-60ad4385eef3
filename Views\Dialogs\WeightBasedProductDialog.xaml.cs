using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using POSSystem.Models;

namespace POSSystem.Views.Dialogs
{
    public partial class WeightBasedProductDialog : Window
    {
        public Product Product { get; private set; }
        public decimal SelectedAmount { get; private set; }
        public decimal SelectedWeight { get; private set; }
        public bool IsConfirmed { get; private set; }

        private bool _isUpdating = false;
        private decimal _availableStock = 0;

        public WeightBasedProductDialog(Product product)
        {
            InitializeComponent();
            Product = product ?? throw new ArgumentNullException(nameof(product));
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            try
            {
                // Calculate available stock using the same logic as SaleViewModel
                CalculateAvailableStock();

                // Set product information
                ProductNameText.Text = Product.Name;
                PricePerUnitText.Text = $"Price per unit: {Product.SellingPrice:C}";

                // Set stock information
                string unitName = Product.UnitOfMeasure?.Abbreviation ?? "units";
                StockInfoText.Text = $"Available stock: {_availableStock:F3} {unitName}";

                // Ensure amount tab is selected by default
                InputMethodTabControl.SelectedItem = AmountTab;

                // Set focus to amount input by default using Dispatcher to ensure UI is ready
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    AmountTextBox.Focus();
                    AmountTextBox.SelectAll();
                }), DispatcherPriority.Loaded);

                // Initialize calculation display
                ResetCalculation();

                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Initialized for product: {Product.Name}, Price: {Product.SellingPrice:C}, Available Stock: {_availableStock}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error in InitializeDialog: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculate available stock using the same logic as SaleViewModel.AddToCart
        /// </summary>
        private void CalculateAvailableStock()
        {
            try
            {
                if (Product.TrackBatches)
                {
                    // For batch-tracked products, get the most accurate stock from batches
                    var batchStock = Product.GetTotalStockDecimal();

                    // If batch calculation returns 0 but StockQuantity is positive, use StockQuantity
                    if (batchStock == 0 && Product.StockQuantity > 0)
                    {
                        _availableStock = Product.StockQuantity;
                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Batch-tracked product {Product.Name}: Batches not loaded properly, using StockQuantity = {_availableStock}");
                    }
                    else
                    {
                        _availableStock = batchStock;
                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Batch-tracked product {Product.Name}: Using batch total stock = {_availableStock}");
                    }
                }
                else
                {
                    // For regular products, use StockQuantity
                    _availableStock = Product.StockQuantity;
                    System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Regular product {Product.Name}: Using StockQuantity = {_availableStock}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error calculating available stock: {ex.Message}");
                _availableStock = Product.StockQuantity; // Fallback to basic stock quantity
            }
        }

        private void TabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Check if UI elements are initialized before accessing them
            if (InputMethodTabControl == null || AmountTab == null || WeightTab == null ||
                AmountTextBox == null || WeightTextBox == null)
            {
                System.Diagnostics.Debug.WriteLine("[WEIGHT-DIALOG] UI elements not yet initialized, skipping tab selection change");
                return;
            }

            try
            {
                if (InputMethodTabControl.SelectedItem == AmountTab)
                {
                    // Use Dispatcher to ensure UI is ready before setting focus
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        AmountTextBox.Focus();
                        AmountTextBox.SelectAll();
                    }), DispatcherPriority.Loaded);

                    System.Diagnostics.Debug.WriteLine("[WEIGHT-DIALOG] Switched to amount input tab");
                }
                else if (InputMethodTabControl.SelectedItem == WeightTab)
                {
                    // Use Dispatcher to ensure UI is ready before setting focus
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        WeightTextBox.Focus();
                        WeightTextBox.SelectAll();
                    }), DispatcherPriority.Loaded);

                    System.Diagnostics.Debug.WriteLine("[WEIGHT-DIALOG] Switched to weight input tab");
                }

                UpdateCalculation();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error in TabControl_SelectionChanged: {ex.Message}");
            }
        }

        private void AmountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (_isUpdating) return;

            UpdateCalculation();
        }

        private void WeightTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (_isUpdating) return;

            UpdateCalculation();
        }

        private void UpdateCalculation()
        {
            // Check if UI elements are initialized before accessing them
            if (InputMethodTabControl == null || AmountTab == null || WeightTab == null ||
                AmountTextBox == null || WeightTextBox == null ||
                TotalAmountText == null || TotalWeightText == null ||
                CalculationText == null || AddToCartButton == null)
            {
                System.Diagnostics.Debug.WriteLine("[WEIGHT-DIALOG] UI elements not yet initialized, skipping calculation update");
                return;
            }

            try
            {
                _isUpdating = true;

                if (InputMethodTabControl.SelectedItem == AmountTab)
                {
                    // Calculate weight from amount
                    if (decimal.TryParse(AmountTextBox.Text, out decimal amount) && amount > 0)
                    {
                        decimal weight = amount / Product.SellingPrice;

                        SelectedAmount = amount;
                        SelectedWeight = weight;

                        TotalAmountText.Text = amount.ToString("C");
                        TotalWeightText.Text = weight.ToString("F2");

                        // Check stock validation and update UI accordingly
                        bool isValidStock = ValidateStock(weight);
                        UpdateCalculationDisplay(amount, weight, isValidStock);

                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Amount calculation: ${amount:F2} → {weight:F2} units (Stock valid: {isValidStock})");
                    }
                    else
                    {
                        ResetCalculation();
                    }
                }
                else if (InputMethodTabControl.SelectedItem == WeightTab)
                {
                    // Calculate amount from weight
                    if (decimal.TryParse(WeightTextBox.Text, out decimal weight) && weight > 0)
                    {
                        decimal amount = weight * Product.SellingPrice;

                        SelectedAmount = amount;
                        SelectedWeight = weight;

                        TotalAmountText.Text = amount.ToString("C");
                        TotalWeightText.Text = weight.ToString("F2");

                        // Check stock validation and update UI accordingly
                        bool isValidStock = ValidateStock(weight);
                        UpdateCalculationDisplay(amount, weight, isValidStock);

                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Weight calculation: {weight:F2} units → ${amount:F2} (Stock valid: {isValidStock})");
                    }
                    else
                    {
                        ResetCalculation();
                    }
                }
                else
                {
                    ResetCalculation();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Calculation error: {ex.Message}");
                ResetCalculation();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void ResetCalculation()
        {
            try
            {
                SelectedAmount = 0;
                SelectedWeight = 0;

                // ✅ FIX: Check if UI elements exist before updating them
                if (TotalAmountText != null)
                    TotalAmountText.Text = "$0.00";

                if (TotalWeightText != null)
                    TotalWeightText.Text = "0.00";

                if (CalculationText != null)
                {
                    string unitName = Product?.UnitOfMeasure?.Abbreviation ?? "units";
                    CalculationText.Text = $"Select a tab and enter a value to see the calculation\nAvailable stock: {_availableStock:F3} {unitName}";
                }

                if (AddToCartButton != null)
                    AddToCartButton.IsEnabled = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error in ResetCalculation: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates if the requested weight is within available stock
        /// </summary>
        private bool ValidateStock(decimal requestedWeight)
        {
            return requestedWeight <= _availableStock;
        }

        /// <summary>
        /// Updates the calculation display with stock validation information
        /// </summary>
        private void UpdateCalculationDisplay(decimal amount, decimal weight, bool isValidStock)
        {
            try
            {
                string unitName = Product.UnitOfMeasure?.Abbreviation ?? "units";
                string baseCalculation;

                if (InputMethodTabControl.SelectedItem == AmountTab)
                {
                    baseCalculation = $"${amount:F2} ÷ ${Product.SellingPrice:F2} per unit = {weight:F2} {unitName}";
                }
                else
                {
                    baseCalculation = $"{weight:F2} {unitName} × ${Product.SellingPrice:F2} per unit = ${amount:F2}";
                }

                if (isValidStock)
                {
                    CalculationText.Text = $"{baseCalculation}\n✅ Available stock: {_availableStock:F3} {unitName}";
                    AddToCartButton.IsEnabled = true;
                }
                else
                {
                    CalculationText.Text = $"{baseCalculation}\n⚠️ Exceeds available stock: {_availableStock:F3} {unitName}";
                    AddToCartButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error updating calculation display: {ex.Message}");
            }
        }

        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            Regex regex = new Regex(@"^[0-9]*\.?[0-9]*$");
            
            var textBox = sender as System.Windows.Controls.TextBox;
            string newText = textBox.Text.Insert(textBox.SelectionStart, e.Text);
            
            // Check if the new text is valid
            if (!regex.IsMatch(newText))
            {
                e.Handled = true;
                return;
            }

            // Prevent multiple decimal points
            if (e.Text == "." && textBox.Text.Contains("."))
            {
                e.Handled = true;
                return;
            }

            // Limit to reasonable decimal places (2 for amount, 3 for weight)
            if (textBox.Text.Contains("."))
            {
                string[] parts = newText.Split('.');
                if (parts.Length > 1)
                {
                    int maxDecimals = textBox == AmountTextBox ? 2 : 3;
                    if (parts[1].Length > maxDecimals)
                    {
                        e.Handled = true;
                        return;
                    }
                }
            }
        }

        private void AddToCartButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedAmount > 0 && SelectedWeight > 0)
            {
                // Final stock validation before confirming
                if (!ValidateStock(SelectedWeight))
                {
                    string unitName = Product.UnitOfMeasure?.Abbreviation ?? "units";
                    MessageBox.Show($"Cannot add {SelectedWeight:F3} {unitName}. Only {_availableStock:F3} {unitName} available in stock.",
                                  "Insufficient Stock", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsConfirmed = true;
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Confirmed: Amount=${SelectedAmount:F2}, Weight={SelectedWeight:F2}");
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("Please enter a valid amount or weight.", "Invalid Input",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            System.Diagnostics.Debug.WriteLine("[WEIGHT-DIALOG] Cancelled by user");
            DialogResult = false;
            Close();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Enter && AddToCartButton.IsEnabled)
            {
                AddToCartButton_Click(this, new RoutedEventArgs());
            }
            else if (e.Key == Key.Escape)
            {
                CancelButton_Click(this, new RoutedEventArgs());
            }
            
            base.OnKeyDown(e);
        }
    }
}
