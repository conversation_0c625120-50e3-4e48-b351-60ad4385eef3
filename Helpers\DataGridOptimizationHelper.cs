using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Helper class for optimizing DataGrid performance
    /// </summary>
    public static class DataGridOptimizationHelper
    {
        /// <summary>
        /// Apply performance optimizations to a DataGrid
        /// </summary>
        public static void OptimizeDataGrid(DataGrid dataGrid)
        {
            if (dataGrid == null) return;
            
            // Enable virtualization
            dataGrid.EnableColumnVirtualization = true;
            dataGrid.EnableRowVirtualization = true;
            
            VirtualizingPanel.SetIsVirtualizing(dataGrid, true);
            VirtualizingPanel.SetVirtualizationMode(dataGrid, VirtualizationMode.Recycling);
            VirtualizingPanel.SetCacheLengthUnit(dataGrid, VirtualizationCacheLengthUnit.Page);
            VirtualizingPanel.SetCacheLength(dataGrid, new VirtualizationCacheLength(1, 2));
            VirtualizingPanel.SetScrollUnit(dataGrid, ScrollUnit.Pixel);
            
            // Apply rendering optimizations
            dataGrid.CacheMode = new BitmapCache { EnableClearType = true, SnapsToDevicePixels = true };
            RenderOptions.SetBitmapScalingMode(dataGrid, BitmapScalingMode.HighQuality);
            RenderOptions.SetClearTypeHint(dataGrid, ClearTypeHint.Enabled);
            RenderOptions.SetCachingHint(dataGrid, CachingHint.Cache);
            
            // Enable ScrollViewer optimizations
            ScrollViewer scrollViewer = FindVisualChild<ScrollViewer>(dataGrid);
            if (scrollViewer != null)
            {
                scrollViewer.CanContentScroll = true;
                scrollViewer.IsDeferredScrollingEnabled = true;
                scrollViewer.CacheMode = new BitmapCache { EnableClearType = true, SnapsToDevicePixels = true };
            }
            
            // Ensure container recycling for better memory usage
            dataGrid.RowDetailsVisibilityMode = DataGridRowDetailsVisibilityMode.Collapsed;
            
            // Set text formatting for better rendering
            dataGrid.UseLayoutRounding = true;
            dataGrid.SnapsToDevicePixels = true;
            TextOptions.SetTextFormattingMode(dataGrid, TextFormattingMode.Ideal);
            TextOptions.SetTextRenderingMode(dataGrid, TextRenderingMode.ClearType);
        }
        
        /// <summary>
        /// Create an optimized CollectionViewSource for a data source
        /// </summary>
        public static CollectionViewSource CreateOptimizedCollectionView<T>(IEnumerable<T> source)
        {
            CollectionViewSource cvs = new CollectionViewSource
            {
                Source = source
            };
            
            return cvs;
        }
        
        /// <summary>
        /// Find the first child of the specified type
        /// </summary>
        private static T FindVisualChild<T>(DependencyObject obj) where T : DependencyObject
        {
            if (obj == null) return null;
            
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(obj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(obj, i);
                
                if (child is T typedChild)
                {
                    return typedChild;
                }
                
                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                {
                    return childOfChild;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Optimize DataGrid binding with specific property bindings to avoid reflection overhead
        /// </summary>
        public static void OptimizeProductColumnBindings(DataGrid dataGrid)
        {
            if (dataGrid == null) return;
            
            try
            {
                foreach (var column in dataGrid.Columns.OfType<DataGridTextColumn>())
                {
                    // Check for resource-intensive bindings and optimize them
                    if (column.Binding is Binding existingBinding)
                    {
                        try
                        {
                            // Create a new binding instead of modifying the existing sealed one
                            var newBinding = new Binding
                            {
                                Path = new PropertyPath(existingBinding.Path?.Path ?? ""),
                                Mode = existingBinding.Mode,
                                Converter = existingBinding.Converter,
                                ConverterParameter = existingBinding.ConverterParameter,
                                StringFormat = existingBinding.StringFormat,
                                IsAsync = true, // Set async binding for non-UI thread processing
                                NotifyOnSourceUpdated = true,
                                NotifyOnTargetUpdated = false
                            };
                            
                            // Special handling for path "." binding
                            if (newBinding.Path?.Path == ".")
                            {
                                newBinding.Path = new PropertyPath("Name");
                            }
                            
                            // Apply the new binding
                            column.Binding = newBinding;
                        }
                        catch (Exception ex)
                        {
                            // Log exception but don't crash
                            System.Diagnostics.Debug.WriteLine($"Error optimizing binding: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the overall method exception but don't crash
                System.Diagnostics.Debug.WriteLine($"Error in OptimizeProductColumnBindings: {ex.Message}");
            }
        }
    }
} 