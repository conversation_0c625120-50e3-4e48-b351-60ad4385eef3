using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;

namespace POSSystem.Views
{
    public partial class CustomerSelectionWindow : Window
    {
        private readonly DatabaseService _dbService;
        public Customer SelectedCustomer { get; private set; }

        public CustomerSelectionWindow()
        {
            InitializeComponent();
            _dbService = new DatabaseService();
            
            // Set up the window
            Loaded += CustomerSelectionWindow_Loaded;
        }
        
        private void CustomerSelectionWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Focus the search box on window load
            txtSearch.Focus();
            
            // Load all customers initially
            LoadCustomers();
        }

        private void LoadCustomers(string searchText = "")
        {
            var customers = _dbService.GetAllCustomers();
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                searchText = searchText.ToLower();
                customers = customers.Where(c =>
                    (c.FirstName != null && c.FirstName.ToLower().Contains(searchText)) ||
                    (c.LastName != null && c.LastName.ToLower().Contains(searchText)) ||
                    (c.Phone != null && c.Phone.Contains(searchText)) ||
                    (c.Email != null && c.Email.ToLower().Contains(searchText))
                ).ToList();
                
                // Show the clear search button when there's search text
                btnClearSearch.Visibility = Visibility.Visible;
            }
            else
            {
                // Hide the clear button when search is empty
                btnClearSearch.Visibility = Visibility.Collapsed;
            }
            
            dgCustomers.ItemsSource = customers;
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            LoadCustomers(txtSearch.Text);
        }
        
        private void BtnClearSearch_Click(object sender, RoutedEventArgs e)
        {
            txtSearch.Clear();
            txtSearch.Focus();
            LoadCustomers();
        }

        private void DgCustomers_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (dgCustomers.SelectedItem is Customer customer)
            {
                SelectedCustomer = customer;
                DialogResult = true;
            }
        }
        
        private void BtnSelectItem_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Customer customer)
            {
                SelectedCustomer = customer;
                DialogResult = true;
            }
        }

        private void BtnSelect_Click(object sender, RoutedEventArgs e)
        {
            if (dgCustomers.SelectedItem is Customer customer)
            {
                SelectedCustomer = customer;
                DialogResult = true;
            }
            else
            {
                MessageBox.Show(
                    (string)Application.Current.Resources["PleaseSelectCustomer"], 
                    (string)Application.Current.Resources["SelectionRequired"],
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
        
        private async void BtnAddNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            // Create customer dialog instance with the correct dialog identifier
            var customerDialog = new CustomerDialog(null, "CustomerSelectionDialog");
            
            // Show the dialog
            var result = await DialogHost.Show(customerDialog, "CustomerSelectionDialog");
            
            if (result is Customer newCustomer)
            {
                // Select the newly added customer and close the window
                SelectedCustomer = newCustomer;
                DialogResult = true;
            }
            else
            {
                // If canceled or closed without adding, just reload the customer list
                LoadCustomers(txtSearch.Text);
            }
        }
    }
} 