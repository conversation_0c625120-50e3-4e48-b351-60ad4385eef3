using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Windows;

namespace POSSystem.ViewModels.Dashboard
{
    public class ExpensesStatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private bool _isLoading;
        private string _title;
        private string _subtitle;
        
        // Statistics
        private decimal _totalExpenses;
        private decimal _averageMonthlyExpenses;
        private decimal _recurringExpenses;
        private decimal _recurringExpensesPercentage;
        private string _expensesGrowth;
        private string _monthlyAverageGrowth;
        private Brush _expensesGrowthColor;
        private Brush _monthlyAverageGrowthColor;
        
        // Collections
        private ObservableCollection<BusinessExpense> _expenses;
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;
        private List<string> _expenseFrequencies;
        private string _selectedFrequency;
        
        // Chart data
        private SeriesCollection _trendSeries;
        private string[] _trendLabels;
        private SeriesCollection _categoryDistributionSeries;

        public event PropertyChangedEventHandler PropertyChanged;

        public ExpensesStatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;

            // Initialize collections
            Expenses = new ObservableCollection<BusinessExpense>();
            
            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };
            
            // Initialize expense frequencies
            ExpenseFrequencies = new List<string>
            {
                "All",
                "OneTime",
                "Daily",
                "Weekly",
                "Monthly",
                "Quarterly",
                "Annually"
            };
            
            // Set initial values
            SelectedTrendPeriod = TrendPeriods.First();
            SelectedFrequency = ExpenseFrequencies.First();
            
            // Set initial title
            Title = Application.Current.TryFindResource("ExpensesStats")?.ToString() ?? "Expenses Statistics";
            Subtitle = Application.Current.TryFindResource("DetailedExpensesMetrics")?.ToString() ?? "Detailed expenses metrics and trends";
            
            // Initialize chart series
            TrendSeries = new SeriesCollection();
            CategoryDistributionSeries = new SeriesCollection();
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set { _totalExpenses = value; OnPropertyChanged(); }
        }

        public decimal AverageMonthlyExpenses
        {
            get => _averageMonthlyExpenses;
            set { _averageMonthlyExpenses = value; OnPropertyChanged(); }
        }

        public decimal RecurringExpenses
        {
            get => _recurringExpenses;
            set { _recurringExpenses = value; OnPropertyChanged(); }
        }

        public decimal RecurringExpensesPercentage
        {
            get => _recurringExpensesPercentage;
            set { _recurringExpensesPercentage = value; OnPropertyChanged(); }
        }

        public string ExpensesGrowth
        {
            get => _expensesGrowth;
            set { _expensesGrowth = value; OnPropertyChanged(); }
        }

        public string MonthlyAverageGrowth
        {
            get => _monthlyAverageGrowth;
            set { _monthlyAverageGrowth = value; OnPropertyChanged(); }
        }

        public Brush ExpensesGrowthColor
        {
            get => _expensesGrowthColor;
            set { _expensesGrowthColor = value; OnPropertyChanged(); }
        }

        public Brush MonthlyAverageGrowthColor
        {
            get => _monthlyAverageGrowthColor;
            set { _monthlyAverageGrowthColor = value; OnPropertyChanged(); }
        }

        public ObservableCollection<BusinessExpense> Expenses
        {
            get => _expenses;
            set { _expenses = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set 
            { 
                _selectedTrendPeriod = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public List<string> ExpenseFrequencies
        {
            get => _expenseFrequencies;
            set { _expenseFrequencies = value; OnPropertyChanged(); }
        }

        public string SelectedFrequency
        {
            get => _selectedFrequency;
            set 
            { 
                _selectedFrequency = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public SeriesCollection TrendSeries
        {
            get => _trendSeries;
            set { _trendSeries = value; OnPropertyChanged(); }
        }

        public string[] TrendLabels
        {
            get => _trendLabels;
            set { _trendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection CategoryDistributionSeries
        {
            get => _categoryDistributionSeries;
            set { _categoryDistributionSeries = value; OnPropertyChanged(); }
        }

        public Func<double, string> CurrencyFormatter => value => $"{value:N2} DA";

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var previousStartDate = startDate.AddDays(-(endDate - startDate).Days);
                var previousEndDate = startDate.AddSeconds(-1);

                // Load expenses data
                var currentExpenses = await LoadExpensesForPeriod(startDate, endDate);
                var previousExpenses = await LoadExpensesForPeriod(previousStartDate, previousEndDate);

                // Calculate metrics
                CalculateMetrics(currentExpenses, previousExpenses);

                // Update charts
                await UpdateTrendChart(startDate, endDate);
                UpdateCategoryDistribution(currentExpenses);

                // Update expenses list
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Expenses.Clear();
                    foreach (var expense in currentExpenses.OrderByDescending(e => e.Date))
                    {
                        Expenses.Add(expense);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading expenses data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task<List<BusinessExpense>> LoadExpensesForPeriod(DateTime startDate, DateTime endDate)
        {
            var expenses = await Task.Run(() => _dbService.GetBusinessExpensesForPeriodAsync(startDate, endDate));
            
            if (SelectedFrequency != "All")
            {
                expenses = expenses.Where(e => e.Frequency.ToString() == SelectedFrequency).ToList();
            }
            
            return expenses;
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Removed async keyword since no await operations are performed
        /// </summary>
        private void CalculateMetrics(List<BusinessExpense> currentExpenses, List<BusinessExpense> previousExpenses)
        {
            // Calculate total expenses
            decimal currentTotal = currentExpenses.Sum(e => CalculateExpenseAmountForPeriod(e));
            decimal previousTotal = previousExpenses.Sum(e => CalculateExpenseAmountForPeriod(e));
            
            TotalExpenses = currentTotal;

            // Calculate growth
            if (previousTotal > 0)
            {
                decimal growth = ((currentTotal - previousTotal) / previousTotal) * 100;
                ExpensesGrowth = $"{growth:+0.0;-0.0;0}%";
                ExpensesGrowthColor = growth < 0 ? new SolidColorBrush(Colors.Green) : new SolidColorBrush(Colors.Red);
            }
            else
            {
                ExpensesGrowth = "N/A";
                ExpensesGrowthColor = new SolidColorBrush(Colors.Gray);
            }

            // Calculate monthly averages
            decimal currentMonthlyAvg = CalculateMonthlyAverage(currentExpenses);
            decimal previousMonthlyAvg = CalculateMonthlyAverage(previousExpenses);
            
            AverageMonthlyExpenses = currentMonthlyAvg;

            if (previousMonthlyAvg > 0)
            {
                decimal growth = ((currentMonthlyAvg - previousMonthlyAvg) / previousMonthlyAvg) * 100;
                MonthlyAverageGrowth = $"{growth:+0.0;-0.0;0}%";
                MonthlyAverageGrowthColor = growth < 0 ? new SolidColorBrush(Colors.Green) : new SolidColorBrush(Colors.Red);
            }
            else
            {
                MonthlyAverageGrowth = "N/A";
                MonthlyAverageGrowthColor = new SolidColorBrush(Colors.Gray);
            }

            // Calculate recurring expenses
            var recurringExpenses = currentExpenses.Where(e => e.Frequency != ExpenseFrequency.OneTime);
            RecurringExpenses = recurringExpenses.Sum(e => CalculateExpenseAmountForPeriod(e));
            RecurringExpensesPercentage = TotalExpenses > 0 ? (RecurringExpenses / TotalExpenses) * 100 : 0;
        }

        private decimal CalculateMonthlyAverage(List<BusinessExpense> expenses)
        {
            if (!expenses.Any()) return 0;

            var totalAmount = expenses.Sum(e => CalculateExpenseAmountForPeriod(e));
            var months = (expenses.Max(e => e.Date) - expenses.Min(e => e.Date)).Days / 30.0m;
            
            return months > 0 ? totalAmount / months : totalAmount;
        }

        private decimal CalculateExpenseAmountForPeriod(BusinessExpense expense)
        {
            var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
            decimal daysBetween = (decimal)(endDate - startDate).TotalDays;

            switch (expense.Frequency)
            {
                case ExpenseFrequency.Daily:
                    return expense.Amount * daysBetween;
                case ExpenseFrequency.Weekly:
                    return expense.Amount * (daysBetween / 7.0m);
                case ExpenseFrequency.Monthly:
                    return expense.Amount * (daysBetween / 30.0m);
                case ExpenseFrequency.Quarterly:
                    return expense.Amount * (daysBetween / 90.0m);
                case ExpenseFrequency.Annually:
                    return expense.Amount * (daysBetween / 365.0m);
                default: // OneTime
                    if (expense.Date >= startDate && expense.Date <= endDate)
                        return expense.Amount;
                    return 0;
            }
        }

        private async Task UpdateTrendChart(DateTime startDate, DateTime endDate)
        {
            try
            {
                var trendData = new List<(DateTime date, decimal amount)>();
                var currentDate = startDate;

                while (currentDate <= endDate)
                {
                    var dayExpenses = await LoadExpensesForPeriod(currentDate, currentDate.AddDays(1));
                    var totalAmount = dayExpenses.Sum(e => CalculateExpenseAmountForPeriod(e));
                    trendData.Add((currentDate, totalAmount));
                    currentDate = currentDate.AddDays(1);
                }

                var values = new ChartValues<decimal>(trendData.Select(d => d.amount));
                var labels = trendData.Select(d => d.date.ToString("MM/dd")).ToArray();

                Application.Current.Dispatcher.Invoke(() =>
                {
                    TrendSeries = new SeriesCollection
                    {
                        new LineSeries
                        {
                            Title = "Expenses",
                            Values = values,
                            PointGeometry = DefaultGeometries.Circle,
                            PointGeometrySize = 8
                        }
                    };
                    TrendLabels = labels;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating trend chart: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Removed async keyword since no await operations are performed
        /// </summary>
        private void UpdateCategoryDistribution(List<BusinessExpense> expenses)
        {
            try
            {
                var categoryData = expenses
                    .GroupBy(e => e.Category.ToString())
                    .Select(g => new
                    {
                        Category = g.Key,
                        Amount = g.Sum(e => CalculateExpenseAmountForPeriod(e))
                    })
                    .OrderByDescending(x => x.Amount)
                    .ToList();

                var categoryColors = new List<Color>
                {
                    Colors.DodgerBlue,
                    Colors.OrangeRed,
                    Colors.ForestGreen,
                    Colors.Purple,
                    Colors.Gold
                };

                Application.Current.Dispatcher.Invoke(() =>
                {
                    var categorySeries = new SeriesCollection();
                    var categoryIndex = 0;
                    foreach (var category in categoryData)
                    {
                        var color = categoryColors[categoryIndex % categoryColors.Count];
                        categorySeries.Add(new PieSeries
                        {
                            Title = category.Category,
                            Values = new ChartValues<decimal> { category.Amount },
                            DataLabels = true,
                            Fill = new SolidColorBrush(color)
                        });
                        categoryIndex++;
                    }
                    CategoryDistributionSeries = categorySeries;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating category distribution: {ex.Message}");
            }
        }

        private (DateTime start, DateTime end) GetDateRangeFromPeriod(TrendPeriodItem period)
        {
            var now = DateTime.Now;
            var startDate = now;
            var endDate = now;

            switch (period?.ResourceKey)
            {
                case "TimePeriod_Today":
                    startDate = now.Date;
                    endDate = now;
                    break;
                case "TimePeriod_ThisWeek":
                    startDate = now.Date.AddDays(-(int)now.DayOfWeek);
                    endDate = now;
                    break;
                case "TimePeriod_ThisMonth":
                    startDate = new DateTime(now.Year, now.Month, 1);
                    endDate = now;
                    break;
                case "TimePeriod_ThisYear":
                    startDate = new DateTime(now.Year, 1, 1);
                    endDate = now;
                    break;
                default:
                    startDate = now.Date;
                    endDate = now;
                    break;
            }

            return (startDate, endDate);
        }
    }
} 