# POS System Test Database Setup

This folder contains SQL scripts for creating and populating a SQLite test database for the POS System application.

## Files Overview

1. **TestDatabase_Schema.sql** - Creates the database schema with all tables and relationships
2. **TestDatabase_SampleData.sql** - Inserts initial sample data for testing
3. **TestDatabase_Procedures.sql** - Creates stored procedures, triggers, and views

## How to Create the Test Database

### Option 1: Using SQLite Command Line

1. Open a command prompt or terminal window
2. Navigate to the directory containing these scripts
3. Run the following commands:

```bash
# Create a new database file
sqlite3 POSSystem_Test.db

# In the SQLite shell, run the scripts in order
.read TestDatabase_Schema.sql
.read TestDatabase_SampleData.sql
.read TestDatabase_Procedures.sql

# Verify the setup
.tables
SELECT COUNT(*) FROM Users;
SELECT COUNT(*) FROM Products;
.quit
```

### Option 2: Using DB Browser for SQLite

1. Download and install [DB Browser for SQLite](https://sqlitebrowser.org/) if you don't have it
2. Open DB Browser for SQLite
3. Click "New Database" and save as "POSSystem_Test.db"
4. Go to "Execute SQL" tab
5. Open and run each script in order:
   - First TestDatabase_Schema.sql
   - Then TestDatabase_SampleData.sql
   - Finally TestDatabase_Procedures.sql
6. Click "Write Changes" to save

## Test Database Contents

### Sample Users
- Admin (Username: admin, Password: admin123)
- Manager (Username: manager, Password: manager123)
- Cashier (Username: cashier, Password: cashier123)

### Sample Data Includes
- 3 user roles with different permissions
- 12 sample products across 3 categories
- 4 sample customers with loyalty data
- 3 sample completed sales with line items
- Discount types, reasons, and permissions
- Unit of measure definitions
- Sample suppliers

### Discount System
- Different discount types: Percentage, Fixed Amount, Price Override
- Role-based permissions for discounts
- Stored procedures for applying and validating discounts

## Important Notes

1. This database uses SQLite which may have some differences from the production database.
2. All passwords are stored in plain text for testing purposes only.
3. The datetime('now') function is used in triggers and will reflect the current time when executed.

## Using the Views

The database includes several helpful views:

- **SalesSummary** - Overview of all sales with customer and product details
- **InventoryStatus** - Current inventory levels with stock status indicators
- **DiscountUsage** - Records of all discounts applied

Example query:
```sql
SELECT * FROM SalesSummary;
SELECT * FROM InventoryStatus WHERE StockStatus = 'Low';
SELECT * FROM DiscountUsage;
```

## Testing the Discount Functionality

To test the discount functionality, you can use the ApplyDiscount procedure:

```sql
-- Apply a 10% discount to an entire sale
CALL ApplyDiscount(1, 1, 10, 5, 'Test discount', 2, NULL);

-- Apply a $5 discount to a specific sale item
CALL ApplyDiscount(1, 2, 5, 1, 'Manager special', 2, 1);
```

To check if a user can apply a discount:

```sql
DECLARE @CanApply INT;
DECLARE @RequiresApproval INT;
DECLARE @Message TEXT;

CALL CanApplyDiscount(3, 1, 15, @CanApply, @RequiresApproval, @Message);

SELECT @CanApply, @RequiresApproval, @Message;
``` 