using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using POSSystem.Models;

namespace POSSystem.Converters
{
    public class PrimaryBarcodeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ICollection<ProductBarcode> barcodes)
            {
                var primaryBarcode = barcodes.FirstOrDefault(b => b.IsPrimary);
                if (primaryBarcode != null)
                    return primaryBarcode.Barcode;
                
                // If no primary barcode, return the first barcode if any exists
                return barcodes.FirstOrDefault()?.Barcode ?? "-";
            }
            return "-";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 