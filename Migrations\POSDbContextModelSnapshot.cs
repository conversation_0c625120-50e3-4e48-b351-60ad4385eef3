﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using POSSystem.Data;

#nullable disable

namespace POSSystem.Migrations
{
    [DbContext(typeof(POSDbContext))]
    partial class POSDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.2");

            modelBuilder.Entity("Audit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .HasColumnType("TEXT");

                    b.Property<string>("NewValues")
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .HasColumnType("TEXT");

                    b.Property<int>("RecordId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TableName")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("POSSystem.Models.BatchStock", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ManufactureDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("BatchNumber", "ProductId")
                        .IsUnique();

                    b.ToTable("BatchStock", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.BusinessExpense", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CashDrawerId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Category")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("Frequency")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("NextDueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CashDrawerId");

                    b.HasIndex("UserId");

                    b.ToTable("BusinessExpenses", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ActualBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ClosedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ClosedById")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("CurrentBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Difference")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExpectedBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OpenedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("OpenedById")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("OpeningBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ClosedById");

                    b.HasIndex("OpenedById");

                    b.ToTable("CashDrawers", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.CashTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CashDrawerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PerformedById")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Reason")
                        .HasColumnType("TEXT");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CashDrawerId");

                    b.HasIndex("PerformedById");

                    b.ToTable("CashTransactions", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ParentCategoryId");

                    b.ToTable("Categories", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("Id");

                    b.Property<string>("Address")
                        .HasColumnType("TEXT")
                        .HasColumnName("Address");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Email")
                        .HasColumnType("TEXT")
                        .HasColumnName("Email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("FirstName");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER")
                        .HasColumnName("IsActive");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("LastName");

                    b.Property<DateTime?>("LastVisit")
                        .HasColumnType("TEXT")
                        .HasColumnName("LastVisit");

                    b.Property<string>("LoyaltyCode")
                        .HasColumnType("TEXT")
                        .HasColumnName("LoyaltyCode");

                    b.Property<decimal>("LoyaltyPoints")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue(0m)
                        .HasColumnName("LoyaltyPoints");

                    b.Property<int?>("LoyaltyTierId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT")
                        .HasColumnName("Phone");

                    b.Property<decimal>("TotalSpent")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue(0m)
                        .HasColumnName("TotalSpent");

                    b.Property<int>("TotalVisits")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0)
                        .HasColumnName("TotalVisits");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id");

                    b.HasIndex("LoyaltyTierId");

                    b.ToTable("Customers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "123 Main St",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7967),
                            Email = "<EMAIL>",
                            FirstName = "John",
                            IsActive = 1,
                            LastName = "Doe",
                            LastVisit = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7897),
                            LoyaltyCode = "JD12345",
                            LoyaltyPoints = 100m,
                            Phone = "************",
                            TotalSpent = 500.00m,
                            TotalVisits = 5,
                            UpdatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7971)
                        },
                        new
                        {
                            Id = 2,
                            Address = "456 Oak Ave",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7987),
                            Email = "<EMAIL>",
                            FirstName = "Jane",
                            IsActive = 1,
                            LastName = "Smith",
                            LastVisit = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7983),
                            LoyaltyCode = "JS67890",
                            LoyaltyPoints = 250m,
                            Phone = "************",
                            TotalSpent = 800.00m,
                            TotalVisits = 8,
                            UpdatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7988)
                        });
                });

            modelBuilder.Entity("POSSystem.Models.Discount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AppliedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("AppliedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ApprovedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comment")
                        .HasColumnType("TEXT");

                    b.Property<int>("DiscountTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<decimal>("FinalPrice")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<int>("ReasonId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SaleItemId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("AppliedByUserId");

                    b.HasIndex("ApprovedByUserId");

                    b.HasIndex("DiscountTypeId");

                    b.HasIndex("ReasonId");

                    b.HasIndex("SaleId");

                    b.HasIndex("SaleItemId");

                    b.ToTable("Discounts");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("ApprovalThreshold")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DiscountTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("MaxFixedAmount")
                        .HasPrecision(10, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("MaxPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("MinPricePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("TEXT");

                    b.Property<bool>("RequiresApproval")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DiscountTypeId");

                    b.HasIndex("RoleId");

                    b.ToTable("DiscountPermissions", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.DiscountReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("DiscountReasons");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "MANAGER",
                            Description = "Manager Special",
                            IsActive = true
                        },
                        new
                        {
                            Id = 2,
                            Code = "DAMAGED",
                            Description = "Damaged Item",
                            IsActive = true
                        },
                        new
                        {
                            Id = 3,
                            Code = "PRICEMATCH",
                            Description = "Price Match",
                            IsActive = true
                        },
                        new
                        {
                            Id = 4,
                            Code = "CUSTOMER",
                            Description = "Customer Satisfaction",
                            IsActive = true
                        },
                        new
                        {
                            Id = 5,
                            Code = "PROMO",
                            Description = "Promotion",
                            IsActive = true
                        },
                        new
                        {
                            Id = 6,
                            Code = "BULK",
                            Description = "Bulk Purchase",
                            IsActive = true
                        },
                        new
                        {
                            Id = 7,
                            Code = "LOYALTY",
                            Description = "Loyalty Discount",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("POSSystem.Models.DiscountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("DiscountTypes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Percentage off the original price",
                            Name = "Percentage"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Fixed amount off the original price",
                            Name = "Fixed Amount"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Override with a specific price",
                            Name = "Price Override"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.InventoryTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Reference")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("TransactionType")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("ProductId", "TransactionDate")
                        .HasDatabaseName("IX_InventoryTransactions_ProductId_Date");

                    b.ToTable("InventoryTransactions");
                });

            modelBuilder.Entity("POSSystem.Models.Invoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DiscountAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GrandTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTerms")
                        .HasColumnType("TEXT");

                    b.Property<string>("Reference")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Draft");

                    b.Property<decimal>("Subtotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Invoice", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.InvoiceItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SellingPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Total")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("ProductId");

                    b.ToTable("InvoiceItem", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.InvoicePayment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ReferenceNumber")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.ToTable("InvoicePayment", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyProgram", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<int>("ExpiryMonths")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MinimumPointsRedemption")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MonetaryValuePerPoint")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PointsPerDollar")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("LoyaltyPrograms");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 247, DateTimeKind.Local).AddTicks(1519),
                            Description = "Standard customer loyalty program",
                            ExpiryMonths = 12,
                            IsActive = true,
                            MinimumPointsRedemption = 100m,
                            MonetaryValuePerPoint = 0.01m,
                            Name = "Standard Rewards",
                            PointsPerDollar = 1.0m
                        });
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Benefits")
                        .HasColumnType("TEXT");

                    b.Property<int>("LoyaltyProgramId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MinimumPoints")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PointsMultiplier")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("LoyaltyProgramId");

                    b.ToTable("LoyaltyTiers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Benefits = "Basic rewards earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 0m,
                            Name = "Bronze",
                            PointsMultiplier = 1.0m
                        },
                        new
                        {
                            Id = 2,
                            Benefits = "25% bonus points earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 1000m,
                            Name = "Silver",
                            PointsMultiplier = 1.25m
                        },
                        new
                        {
                            Id = 3,
                            Benefits = "50% bonus points earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 5000m,
                            Name = "Gold",
                            PointsMultiplier = 1.5m
                        });
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Points")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("LoyaltyTransactions");
                });

            modelBuilder.Entity("POSSystem.Models.Payment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("TEXT");

                    b.Property<string>("ReferenceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("POSSystem.Models.Printing.PrinterConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConnectionSettings")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("Copies")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PaperSize")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PrintQuality")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PrinterName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("PrinterType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ReceiptTemplateId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ReceiptTemplateId");

                    b.ToTable("PrinterConfigurations", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Printing.ReceiptPrintJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("PrinterConfigId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ReceiptTemplateId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RetryCount")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("PrinterConfigId");

                    b.HasIndex("ReceiptTemplateId");

                    b.HasIndex("SaleId");

                    b.HasIndex("UserId");

                    b.ToTable("ReceiptPrintJobs", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Printing.ReceiptPrintSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("AutoPrintEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("DefaultPrinterConfigId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("DefaultReceiptTemplateId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("EnablePrintPreview")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MaxRetryAttempts")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PdfBackupPath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("PrintTimeoutSeconds")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("RetryFailedPrints")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("SaveAsPdfBackup")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("ShowPrintDialog")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("DefaultPrinterConfigId");

                    b.HasIndex("DefaultReceiptTemplateId");

                    b.ToTable("ReceiptPrintSettings", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Printing.ReceiptTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AdvancedSettings")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("FontSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FooterText")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IncludeBarcode")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IncludeCompanyInfo")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IncludeCustomerInfo")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IncludeItemDetails")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IncludeLogo")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IncludePaymentInfo")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("PaperWidth")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TemplateType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ReceiptTemplates", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .HasColumnType("TEXT");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DefaultPrice")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageData")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsWeightBased")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<decimal>("LoyaltyPoints")
                        .HasColumnType("TEXT");

                    b.Property<int>("MinimumStock")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PurchasePrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("ReorderPoint")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SKU")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SellingPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("TrackBatches")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<int?>("UnitOfMeasureId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("WeightUnitId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Description");

                    b.HasIndex("Name");

                    b.HasIndex("SupplierId");

                    b.HasIndex("UnitOfMeasureId");

                    b.HasIndex("WeightUnitId");

                    b.HasIndex("IsActive", "StockQuantity")
                        .HasDatabaseName("IX_Products_IsActive_StockQuantity");

                    b.HasIndex("Name", "SKU");

                    b.HasIndex("SKU", "Description");

                    b.ToTable("Products", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.ProductAlert", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AlertType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<string>("Message")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ReferenceType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductAlerts", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.ProductBarcode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("Barcode")
                        .IsUnique();

                    b.HasIndex("ProductId");

                    b.ToTable("ProductBarcodes");
                });

            modelBuilder.Entity("POSSystem.Models.ProductPrice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("TEXT");

                    b.Property<string>("PriceType")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPrices");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GrandTotal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentReference")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Pending");

                    b.Property<decimal>("Subtotal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("PurchaseOrders", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PurchaseOrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitCost")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderId");

                    b.ToTable("PurchaseOrderItems", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9766),
                            Description = "System Administrator",
                            IsActive = 1,
                            Name = "Admin"
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9772),
                            Description = "Store Manager",
                            IsActive = 1,
                            Name = "Manager"
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9778),
                            Description = "Store Cashier",
                            IsActive = 1,
                            Name = "Cashier"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountPaid")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Change")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DiscountAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GrandTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SaleDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Subtotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("TotalItems")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("SaleDate")
                        .HasDatabaseName("IX_Sales_SaleDate");

                    b.HasIndex("CustomerId", "SaleDate")
                        .HasDatabaseName("IX_Sales_CustomerId_SaleDate");

                    b.HasIndex("PaymentStatus", "SaleDate")
                        .HasDatabaseName("IX_Sales_PaymentStatus_SaleDate");

                    b.HasIndex("SaleDate", "GrandTotal")
                        .HasDatabaseName("IX_Sales_SaleDate_GrandTotal");

                    b.HasIndex("SaleDate", "Status")
                        .HasDatabaseName("IX_Sales_SaleDate_Status");

                    b.HasIndex("UserId", "SaleDate")
                        .HasDatabaseName("IX_Sales_UserId_SaleDate");

                    b.ToTable("Sales", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.SaleHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ActionDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("AdjustmentAmount")
                        .HasColumnType("TEXT");

                    b.Property<string>("Reason")
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.HasIndex("UserId");

                    b.ToTable("SaleHistory", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.SaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 3)
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Total")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Weight")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 3)
                        .HasColumnType("TEXT")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.HasIndex("ProductId", "SaleId")
                        .HasDatabaseName("IX_SaleItems_ProductId_SaleId");

                    b.ToTable("SaleItems", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Suppliers", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("BaseUnitId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("ConversionFactor")
                        .HasPrecision(18, 6)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BaseUnitId");

                    b.ToTable("UnitsOfMeasure", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Abbreviation = "pc",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1759),
                            IsActive = 1,
                            Name = "Piece",
                            Type = "Unit"
                        },
                        new
                        {
                            Id = 2,
                            Abbreviation = "kg",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1768),
                            IsActive = 1,
                            Name = "Kilogram",
                            Type = "Weight"
                        },
                        new
                        {
                            Id = 3,
                            Abbreviation = "g",
                            BaseUnitId = 2,
                            ConversionFactor = 0.001m,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1789),
                            IsActive = 1,
                            Name = "Gram",
                            Type = "Weight"
                        },
                        new
                        {
                            Id = 4,
                            Abbreviation = "L",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1795),
                            IsActive = 1,
                            Name = "Liter",
                            Type = "Volume"
                        },
                        new
                        {
                            Id = 5,
                            Abbreviation = "mL",
                            BaseUnitId = 4,
                            ConversionFactor = 0.001m,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1804),
                            IsActive = 1,
                            Name = "Milliliter",
                            Type = "Volume"
                        },
                        new
                        {
                            Id = 6,
                            Abbreviation = "box",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1811),
                            IsActive = 1,
                            Name = "Box",
                            Type = "Package"
                        },
                        new
                        {
                            Id = 7,
                            Abbreviation = "ctn",
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1817),
                            IsActive = 1,
                            Name = "Carton",
                            Type = "Package"
                        },
                        new
                        {
                            Id = 8,
                            Abbreviation = "dz",
                            BaseUnitId = 1,
                            ConversionFactor = 12m,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1831),
                            IsActive = 1,
                            Name = "Dozen",
                            Type = "Unit"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhotoPath")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("default-user.png");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 232, DateTimeKind.Local).AddTicks(6763),
                            Email = "<EMAIL>",
                            FirstName = "Admin",
                            IsActive = 1,
                            LastName = "User",
                            Password = "admin123",
                            Phone = "************",
                            PhotoPath = "default-user.png",
                            RoleId = 1,
                            UpdatedAt = new DateTime(2025, 7, 14, 12, 16, 2, 232, DateTimeKind.Local).AddTicks(6773),
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.UserFavorite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserId", "ProductId")
                        .IsUnique();

                    b.ToTable("UserFavorites", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.UserPermissions", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanAccessSettings")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanAdjustInventory")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanApplyDiscount")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanCreateSales")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageCashDrawer")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageCategories")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageCustomers")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageExpenses")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManagePrices")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageProducts")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageRoles")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageSuppliers")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanManageUsers")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanViewInventory")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanViewLogs")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanViewReports")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanViewSalesHistory")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CanVoidSales")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("UserPermissions", (string)null);
                });

            modelBuilder.Entity("Audit", b =>
                {
                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany("Audits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.BatchStock", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Batches")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.BusinessExpense", b =>
                {
                    b.HasOne("POSSystem.Models.CashDrawer", "CashDrawer")
                        .WithMany()
                        .HasForeignKey("CashDrawerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CashDrawer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.HasOne("POSSystem.Models.User", "ClosedBy")
                        .WithMany()
                        .HasForeignKey("ClosedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.User", "OpenedBy")
                        .WithMany()
                        .HasForeignKey("OpenedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ClosedBy");

                    b.Navigation("OpenedBy");
                });

            modelBuilder.Entity("POSSystem.Models.CashTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.CashDrawer", null)
                        .WithMany("Transactions")
                        .HasForeignKey("CashDrawerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "PerformedBy")
                        .WithMany()
                        .HasForeignKey("PerformedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("PerformedBy");
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.HasOne("POSSystem.Models.Category", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentCategoryId");

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.HasOne("POSSystem.Models.LoyaltyTier", "LoyaltyTier")
                        .WithMany()
                        .HasForeignKey("LoyaltyTierId");

                    b.Navigation("LoyaltyTier");
                });

            modelBuilder.Entity("POSSystem.Models.Discount", b =>
                {
                    b.HasOne("POSSystem.Models.User", "AppliedByUser")
                        .WithMany()
                        .HasForeignKey("AppliedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.DiscountType", "DiscountType")
                        .WithMany("Discounts")
                        .HasForeignKey("DiscountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.DiscountReason", "Reason")
                        .WithMany("Discounts")
                        .HasForeignKey("ReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany()
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.SaleItem", "SaleItem")
                        .WithMany()
                        .HasForeignKey("SaleItemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AppliedByUser");

                    b.Navigation("ApprovedByUser");

                    b.Navigation("DiscountType");

                    b.Navigation("Reason");

                    b.Navigation("Sale");

                    b.Navigation("SaleItem");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountPermission", b =>
                {
                    b.HasOne("POSSystem.Models.DiscountType", "DiscountType")
                        .WithMany("Permissions")
                        .HasForeignKey("DiscountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Role", "Role")
                        .WithMany("DiscountPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DiscountType");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("POSSystem.Models.InventoryTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("InventoryTransactions")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.Invoice", b =>
                {
                    b.HasOne("POSSystem.Models.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Customer");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("POSSystem.Models.InvoiceItem", b =>
                {
                    b.HasOne("POSSystem.Models.Invoice", "Invoice")
                        .WithMany("Items")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Invoice");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.InvoicePayment", b =>
                {
                    b.HasOne("POSSystem.Models.Invoice", "Invoice")
                        .WithMany("Payments")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTier", b =>
                {
                    b.HasOne("POSSystem.Models.LoyaltyProgram", "Program")
                        .WithMany("Tiers")
                        .HasForeignKey("LoyaltyProgramId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Program");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.Customer", "Customer")
                        .WithMany("LoyaltyTransactions")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("POSSystem.Models.Payment", b =>
                {
                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany("Payments")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POSSystem.Models.Printing.PrinterConfiguration", b =>
                {
                    b.HasOne("POSSystem.Models.Printing.ReceiptTemplate", "ReceiptTemplate")
                        .WithMany()
                        .HasForeignKey("ReceiptTemplateId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ReceiptTemplate");
                });

            modelBuilder.Entity("POSSystem.Models.Printing.ReceiptPrintJob", b =>
                {
                    b.HasOne("POSSystem.Models.Printing.PrinterConfiguration", "PrinterConfig")
                        .WithMany()
                        .HasForeignKey("PrinterConfigId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Printing.ReceiptTemplate", "ReceiptTemplate")
                        .WithMany()
                        .HasForeignKey("ReceiptTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany()
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PrinterConfig");

                    b.Navigation("ReceiptTemplate");

                    b.Navigation("Sale");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.Printing.ReceiptPrintSettings", b =>
                {
                    b.HasOne("POSSystem.Models.Printing.PrinterConfiguration", "DefaultPrinterConfig")
                        .WithMany()
                        .HasForeignKey("DefaultPrinterConfigId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("POSSystem.Models.Printing.ReceiptTemplate", "DefaultReceiptTemplate")
                        .WithMany()
                        .HasForeignKey("DefaultReceiptTemplateId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DefaultPrinterConfig");

                    b.Navigation("DefaultReceiptTemplate");
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.HasOne("POSSystem.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId");

                    b.HasOne("POSSystem.Models.UnitOfMeasure", "UnitOfMeasure")
                        .WithMany("Products")
                        .HasForeignKey("UnitOfMeasureId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.UnitOfMeasure", "WeightUnit")
                        .WithMany()
                        .HasForeignKey("WeightUnitId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("Supplier");

                    b.Navigation("UnitOfMeasure");

                    b.Navigation("WeightUnit");
                });

            modelBuilder.Entity("POSSystem.Models.ProductAlert", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.ProductBarcode", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Barcodes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.ProductPrice", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("PriceHistory")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.HasOne("POSSystem.Models.Supplier", "Supplier")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrderItem", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Items")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrder");
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.HasOne("POSSystem.Models.Customer", "Customer")
                        .WithMany("Sales")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany("Sales")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.SaleHistory", b =>
                {
                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany()
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Sale");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.SaleItem", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Sales")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany("Items")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.HasOne("POSSystem.Models.UnitOfMeasure", "BaseUnit")
                        .WithMany("DerivedUnits")
                        .HasForeignKey("BaseUnitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("BaseUnit");
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.HasOne("POSSystem.Models.Role", "UserRole")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("POSSystem.Models.UserFavorite", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("FavoritedBy")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany("Favorites")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.UserPermissions", b =>
                {
                    b.HasOne("POSSystem.Models.User", "User")
                        .WithOne("Permissions")
                        .HasForeignKey("POSSystem.Models.UserPermissions", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.Navigation("LoyaltyTransactions");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountReason", b =>
                {
                    b.Navigation("Discounts");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountType", b =>
                {
                    b.Navigation("Discounts");

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("POSSystem.Models.Invoice", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyProgram", b =>
                {
                    b.Navigation("Tiers");
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.Navigation("Barcodes");

                    b.Navigation("Batches");

                    b.Navigation("FavoritedBy");

                    b.Navigation("InventoryTransactions");

                    b.Navigation("PriceHistory");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("POSSystem.Models.Role", b =>
                {
                    b.Navigation("DiscountPermissions");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("POSSystem.Models.Supplier", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.Navigation("DerivedUnits");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.Navigation("Audits");

                    b.Navigation("Favorites");

                    b.Navigation("Permissions");

                    b.Navigation("Sales");
                });
#pragma warning restore 612, 618
        }
    }
}
