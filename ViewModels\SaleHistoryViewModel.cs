using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Windows;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.ViewModels
{
    public class SaleHistoryViewModel : ViewModelBase
    {
        // Static event that can be raised when a new sale is created
        public static event EventHandler SaleCreated;

        // Method to trigger the event from other parts of the application
        public static void NotifyNewSale()
        {
            SaleCreated?.Invoke(null, EventArgs.Empty);
        }

        private readonly DatabaseService _dbService = new();
        private ObservableCollection<Sale> _sales;
        private List<Sale> _allSales;
        private string _searchText;
        private bool _isCustomPeriod;
        private DateTime _currentStartDate;
        private DateTime _currentEndDate;
        private Sale _selectedSale;
        private bool _isLoading;
        private int _currentPage = 1;
        private const int PageSize = 50;
        private int _totalItems;
        private string _statusMessage;
        private readonly EventHandler _saleCreatedHandler;

        public SaleHistoryViewModel()
        {
            // Load initial data (last 30 days by default)
            _currentStartDate = DateTime.Today.AddDays(-30);
            _currentEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
            Sales = new ObservableCollection<Sale>();
            BindingOperations.EnableCollectionSynchronization(Sales, new object());

            // ✅ PERFORMANCE FIX: Properly await async call to prevent CS4014 warning
            _ = Task.Run(async () => await LoadSalesAsync(_currentStartDate, _currentEndDate));

            // Store the event handler so we can unsubscribe later
            _saleCreatedHandler = (s, e) => Application.Current.Dispatcher.Invoke(RefreshSales);

            // Subscribe to the SaleCreated event
            SaleCreated += _saleCreatedHandler;
        }

        private decimal CalculateDiscountAmount(Sale sale)
        {
            decimal totalDiscount = 0;
            
            var context = _dbService.Context;
            {
                // Get all discounts for this sale
                var discounts = context.Discounts
                    .Include(d => d.DiscountType)
                    .Where(d => d.SaleId == sale.Id)
                    .ToList();

                foreach (var discount in discounts)
                {
                    if (discount.DiscountType.Name == "Percentage")
                    {
                        // For percentage discounts, calculate based on the original price
                        totalDiscount += sale.Subtotal * (discount.DiscountValue / 100);
                    }
                    else
                    {
                        // For fixed amount discounts, use the value directly
                        totalDiscount += discount.DiscountValue;
                    }
                }
            }
            
            return totalDiscount;
        }

        private async Task LoadSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Loading sales...";

                var context = _dbService.Context;
                {
                    // ✅ OPTIMIZED: Use projection to load only needed data
                    var query = context.Sales
                        .AsNoTracking()
                        .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                        .Select(s => new Sale
                        {
                            Id = s.Id,
                            InvoiceNumber = s.InvoiceNumber,
                            SaleDate = s.SaleDate,
                            GrandTotal = s.GrandTotal,
                            Status = s.Status,
                            Customer = s.Customer != null ? new Customer
                            {
                                Id = s.Customer.Id,
                                FirstName = s.Customer.FirstName,
                                LastName = s.Customer.LastName
                            } : null,
                            User = new User
                            {
                                Id = s.User.Id,
                                Username = s.User.Username
                            },
                            Items = s.Items.Select(i => new SaleItem
                            {
                                Id = i.Id,
                                Quantity = i.Quantity,
                                UnitPrice = i.UnitPrice,
                                Total = i.Total, // ✅ FIX: Include the stored Total value from database
                                Product = new Product
                                {
                                    Id = i.Product.Id,
                                    Name = i.Product.Name
                                }
                            }).ToList()
                        });

                    if (!string.IsNullOrWhiteSpace(SearchText))
                    {
                        query = query.Where(s =>
                            s.InvoiceNumber.Contains(SearchText) ||
                            (s.Customer != null && s.Customer.Name.Contains(SearchText)) ||
                            s.User.Username.Contains(SearchText));
                    }

                    // Get total count for pagination
                    TotalItems = await query.CountAsync();

                    // Get paginated results
                    var sales = await query
                        .OrderByDescending(s => s.SaleDate)
                        .Skip((CurrentPage - 1) * PageSize)
                        .Take(PageSize)
                        .ToListAsync();



                    // Calculate correct discount amounts
                    foreach (var sale in sales)
                    {
                        sale.DiscountAmount = CalculateDiscountAmount(sale);
                    }

                    Sales.Clear();
                    foreach (var sale in sales)
                    {
                        Sales.Add(sale);
                    }
                }

                StatusMessage = $"Loaded {Sales.Count} sales";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading sales: {ex.Message}";
                MessageBox.Show($"Error loading sales: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        public ObservableCollection<Sale> Sales
        {
            get => _sales;
            set
            {
                _sales = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                }
            }
        }

        public bool IsCustomPeriod
        {
            get => _isCustomPeriod;
            set
            {
                if (_isCustomPeriod != value)
                {
                    _isCustomPeriod = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CurrentStartDate
        {
            get => _currentStartDate;
            set
            {
                if (_currentStartDate != value)
                {
                    _currentStartDate = value;
                    OnPropertyChanged();
                    _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                }
            }
        }

        public DateTime CurrentEndDate
        {
            get => _currentEndDate;
            set
            {
                if (_currentEndDate != value)
                {
                    _currentEndDate = value;
                    OnPropertyChanged();
                    _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                }
            }
        }

        public Sale SelectedSale
        {
            get => _selectedSale;
            set
            {
                if (_selectedSale != value)
                {
                    _selectedSale = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (_currentPage != value)
                {
                    _currentPage = value;
                    OnPropertyChanged();
                    _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                }
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                if (_totalItems != value)
                {
                    _totalItems = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalPages));
                }
            }
        }

        public int TotalPages => (int)Math.Ceiling((double)TotalItems / PageSize);

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public void RefreshSales()
        {
            _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
        }

        /// <summary>
        /// Override disposal to unsubscribe from static events
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from static event to prevent memory leaks
                if (_saleCreatedHandler != null)
                {
                    SaleCreated -= _saleCreatedHandler;
                }

                // Clear collections
                Sales?.Clear();
                _allSales?.Clear();

                System.Diagnostics.Debug.WriteLine("SaleHistoryViewModel disposed successfully");
            }

            base.Dispose(disposing);
        }
    }
} 