using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class DiscountType
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Name { get; set; }
        
        public string Description { get; set; }
        
        [NotMapped]
        public string InternalName { get; set; }
        
        public virtual ICollection<Discount> Discounts { get; set; }
        public virtual ICollection<DiscountPermission> Permissions { get; set; }
    }

    public class DiscountReason
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Code { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Description { get; set; }
        
        public bool IsActive { get; set; }
        
        [NotMapped]
        public string InternalCode { get; set; }
        
        public virtual ICollection<Discount> Discounts { get; set; }
    }

    public class DiscountPermission
    {
        public int Id { get; set; }
        
        [ForeignKey("Role")]
        public int RoleId { get; set; }
        
        [ForeignKey("DiscountType")]
        public int DiscountTypeId { get; set; }
        
        public decimal? MaxPercentage { get; set; }
        public decimal? MaxFixedAmount { get; set; }
        public decimal? MinPricePercentage { get; set; }
        public bool RequiresApproval { get; set; }
        public decimal? ApprovalThreshold { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual Role Role { get; set; }
        public virtual DiscountType DiscountType { get; set; }
    }

    public class Discount
    {
        public int Id { get; set; }
        public int? SaleId { get; set; }
        public int? SaleItemId { get; set; }
        public int DiscountTypeId { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal FinalPrice { get; set; }
        public int ReasonId { get; set; }
        public string Comment { get; set; }
        public int AppliedByUserId { get; set; }
        public int? ApprovedByUserId { get; set; }
        public DateTime AppliedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public bool IsActive { get; set; }

        public virtual Sale Sale { get; set; }
        public virtual SaleItem SaleItem { get; set; }
        public virtual DiscountType DiscountType { get; set; }
        public virtual DiscountReason Reason { get; set; }
        public virtual User AppliedByUser { get; set; }
        public virtual User ApprovedByUser { get; set; }
    }
} 