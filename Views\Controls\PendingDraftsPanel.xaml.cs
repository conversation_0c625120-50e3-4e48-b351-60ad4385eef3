using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Views.Controls
{
    /// <summary>
    /// Interaction logic for PendingDraftsPanel.xaml
    /// </summary>
    public partial class PendingDraftsPanel : UserControl
    {
        public PendingDraftsPanel()
        {
            InitializeComponent();
        }

        private void PendingDraft_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element && element.Tag is Invoice draftInvoice)
                {
                    // Get the ViewModel from DataContext
                    if (DataContext is PendingDraftsViewModel viewModel)
                    {
                        // Execute the complete draft command
                        if (viewModel.CompleteDraftCommand?.CanExecute(draftInvoice) == true)
                        {
                            viewModel.CompleteDraftCommand.Execute(draftInvoice);
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_PANEL] Error in PendingDraft_Click: {ex.Message}");
            }
        }
    }
}
