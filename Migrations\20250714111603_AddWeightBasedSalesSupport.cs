﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddWeightBasedSalesSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Weight",
                table: "SaleItems",
                type: "TEXT",
                precision: 18,
                scale: 3,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<bool>(
                name: "IsWeightBased",
                table: "Products",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Products",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "WeightUnitId",
                table: "Products",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7967), new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7897), new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7971) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7987), new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7983), new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(7988) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 247, DateTimeKind.Local).AddTicks(1519));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9766));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9772));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 231, DateTimeKind.Local).AddTicks(9778));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1759));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1768));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1789));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1795));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1804));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1811));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1817));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 14, 12, 16, 2, 251, DateTimeKind.Local).AddTicks(1831));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 14, 12, 16, 2, 232, DateTimeKind.Local).AddTicks(6763), new DateTime(2025, 7, 14, 12, 16, 2, 232, DateTimeKind.Local).AddTicks(6773) });

            migrationBuilder.CreateIndex(
                name: "IX_Products_WeightUnitId",
                table: "Products",
                column: "WeightUnitId");

            migrationBuilder.AddForeignKey(
                name: "FK_Products_UnitsOfMeasure_WeightUnitId",
                table: "Products",
                column: "WeightUnitId",
                principalTable: "UnitsOfMeasure",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Products_UnitsOfMeasure_WeightUnitId",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Products_WeightUnitId",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "Weight",
                table: "SaleItems");

            migrationBuilder.DropColumn(
                name: "IsWeightBased",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "WeightUnitId",
                table: "Products");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6738), new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6667), new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6741) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6765), new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6759), new DateTime(2025, 7, 10, 21, 38, 59, 200, DateTimeKind.Local).AddTicks(6767) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 208, DateTimeKind.Local).AddTicks(7352));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 201, DateTimeKind.Local).AddTicks(1609));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 201, DateTimeKind.Local).AddTicks(1621));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 201, DateTimeKind.Local).AddTicks(1632));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1548));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1558));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1600));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1608));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1619));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1637));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1649));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 10, 21, 38, 59, 215, DateTimeKind.Local).AddTicks(1711));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 38, 59, 203, DateTimeKind.Local).AddTicks(2896), new DateTime(2025, 7, 10, 21, 38, 59, 203, DateTimeKind.Local).AddTicks(2930) });
        }
    }
}
