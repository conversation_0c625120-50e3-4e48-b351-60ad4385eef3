using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows;
using POSSystem.Models;

namespace POSSystem.ViewModels
{
    public class BusinessExpenseEditViewModel : INotifyPropertyChanged
    {
        private readonly Window _dialogWindow;
        private BusinessExpense _expense;
        private bool _isNewExpense;

        public BusinessExpenseEditViewModel(BusinessExpense expense, Window dialogWindow)
        {
            Expense = expense;
            _dialogWindow = dialogWindow;
            IsNewExpense = expense.Id == 0;
            InitializeCommands();
        }

        public BusinessExpense Expense
        {
            get => _expense;
            set
            {
                _expense = value;
                OnPropertyChanged();
            }
        }

        public bool IsNewExpense
        {
            get => _isNewExpense;
            set
            {
                _isNewExpense = value;
                OnPropertyChanged();
            }
        }

        public ICommand SaveCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }

        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(ExecuteSave);
            CancelCommand = new RelayCommand(ExecuteCancel);
        }

        private void ExecuteSave(object parameter)
        {
            if (!ValidateExpense())
            {
                return;
            }

            // Ensure required fields are set
            Expense.Date = Expense.Date == default ? DateTime.Now : Expense.Date;
            
            _dialogWindow.DialogResult = true;
            _dialogWindow.Close();
        }

        private bool ValidateExpense()
        {
            if (string.IsNullOrWhiteSpace(Expense.Description))
            {
                MessageBox.Show("Description is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (Expense.Amount <= 0)
            {
                MessageBox.Show("Amount must be greater than zero.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate Category
            if (!Enum.IsDefined(typeof(ExpenseCategory), Expense.Category))
            {
                MessageBox.Show("Please select a valid category.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate Frequency
            if (!Enum.IsDefined(typeof(ExpenseFrequency), Expense.Frequency))
            {
                MessageBox.Show("Please select a valid frequency.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Ensure Notes is never null
            Expense.Notes = Expense.Notes ?? "";

            return true;
        }

        private void ExecuteCancel(object parameter)
        {
            _dialogWindow.DialogResult = false;
            _dialogWindow.Close();
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 