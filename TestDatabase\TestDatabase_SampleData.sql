-- POS System Test Database - Sample Data
-- This script inserts basic sample data for testing

-- Clear existing data
DELETE FROM UserFavorites;
DELETE FROM DiscountPermissions;
DELETE FROM Discounts;
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM CashTransactions;
DELETE FROM CashDrawers;
DELETE FROM ProductBarcodes;
DELETE FROM Products;
DELETE FROM Categories;
DELETE FROM Customers;
DELETE FROM LoyaltyTiers;
DELETE FROM LoyaltyPrograms;
DELETE FROM DiscountReasons;
DELETE FROM DiscountTypes;
DELETE FROM Users;
DELETE FROM Roles;
DELETE FROM UnitsOfMeasure;
DELETE FROM Suppliers;

-- Insert roles
INSERT INTO Roles (Id, Name, Description, IsActive, CreatedAt) VALUES
(1, 'Admin', 'System Administrator', 1, datetime('now')),
(2, 'Manager', 'Store Manager', 1, datetime('now')),
(3, 'Cashier', 'Store Cashier', 1, datetime('now'));

-- Insert Users
INSERT INTO Users (Id, Username, Password, FirstName, LastName, Email, Phone, PhotoPath, RoleId, IsActive, CreatedAt, UpdatedAt) VALUES
(1, 'admin', 'admin123', 'Admin', 'User', '<EMAIL>', '************', 'default-user.png', 1, 1, datetime('now'), datetime('now')),
(2, 'manager', 'manager123', 'Store', 'Manager', '<EMAIL>', '************', 'default-user.png', 2, 1, datetime('now'), datetime('now')),
(3, 'cashier', 'cashier123', 'Store', 'Cashier', '<EMAIL>', '************', 'default-user.png', 3, 1, datetime('now'), datetime('now'));

-- Insert LoyaltyProgram
INSERT INTO LoyaltyPrograms (Id, Name, Description, PointsPerDollar, MonetaryValuePerPoint, PointsCalculationMethod, MinimumPointsRedemption, ExpiryMonths, IsActive, CreatedAt) VALUES
(1, 'Standard Rewards', 'Standard customer loyalty program', 1.0, 0.01, 'PerDollar', 100, 12, 1, datetime('now'));

-- Insert LoyaltyTiers
INSERT INTO LoyaltyTiers (Id, Name, LoyaltyProgramId, MinimumPoints, PointsMultiplier, Benefits) VALUES
(1, 'Bronze', 1, 0, 1.0, 'Basic rewards earning'),
(2, 'Silver', 1, 1000, 1.25, '25% bonus points earning'),
(3, 'Gold', 1, 5000, 1.5, '50% bonus points earning');

-- Insert DiscountTypes
INSERT INTO DiscountTypes (Id, Name, Description) VALUES
(1, 'Percentage', 'Percentage off the original price'),
(2, 'Fixed Amount', 'Fixed amount off the original price'),
(3, 'Price Override', 'Override with a specific price');

-- Insert DiscountReasons
INSERT INTO DiscountReasons (Id, Code, Description, IsActive) VALUES
(1, 'MANAGER', 'Manager Special', 1),
(2, 'DAMAGED', 'Damaged Item', 1),
(3, 'PRICEMATCH', 'Price Match', 1),
(4, 'CUSTOMER', 'Customer Satisfaction', 1),
(5, 'PROMO', 'Promotion', 1),
(6, 'BULK', 'Bulk Purchase', 1),
(7, 'LOYALTY', 'Loyalty Discount', 1);

-- Insert discount permissions
INSERT INTO DiscountPermissions (RoleId, DiscountTypeId, MaxPercentage, MaxFixedAmount, MinPricePercentage, RequiresApproval, ApprovalThreshold, IsActive, CreatedAt) VALUES
-- Admin permissions
(1, 1, 100.00, NULL, 0.00, 0, NULL, 1, datetime('now')), -- Admin can give 100% percentage discount
(1, 2, NULL, 1000.00, 0.00, 0, NULL, 1, datetime('now')), -- Admin can give up to $1000 fixed discount
(1, 3, NULL, NULL, 0.00, 0, NULL, 1, datetime('now')), -- Admin can override price

-- Manager permissions
(2, 1, 50.00, NULL, 20.00, 0, 25.00, 1, datetime('now')), -- Manager can give 50% discount
(2, 2, NULL, 500.00, 20.00, 0, 250.00, 1, datetime('now')), -- Manager can give up to $500 fixed discount
(2, 3, NULL, NULL, 50.00, 0, NULL, 1, datetime('now')), -- Manager can override price

-- Cashier permissions
(3, 1, 10.00, NULL, 80.00, 1, 5.00, 1, datetime('now')), -- Cashier can give 10% discount, requires approval above 5%
(3, 2, NULL, 50.00, 80.00, 1, 20.00, 1, datetime('now')); -- Cashier can give up to $50 fixed discount, requires approval above $20

-- Units of measure
INSERT INTO UnitsOfMeasure (Id, Name, Abbreviation, Type, BaseUnitId, ConversionFactor, IsActive, CreatedAt) VALUES
(1, 'Piece', 'pc', 'Unit', NULL, NULL, 1, datetime('now')),
(2, 'Kilogram', 'kg', 'Weight', NULL, NULL, 1, datetime('now')),
(3, 'Gram', 'g', 'Weight', 2, 0.001, 1, datetime('now')),
(4, 'Liter', 'L', 'Volume', NULL, NULL, 1, datetime('now')),
(5, 'Milliliter', 'mL', 'Volume', 4, 0.001, 1, datetime('now')),
(6, 'Box', 'box', 'Package', NULL, NULL, 1, datetime('now')),
(7, 'Carton', 'ctn', 'Package', NULL, NULL, 1, datetime('now')),
(8, 'Dozen', 'dz', 'Unit', 1, 12.0, 1, datetime('now'));

-- Insert categories
INSERT INTO Categories (Id, Name, Description, ParentCategoryId, IsActive) VALUES
(1, 'Electronics', 'Electronic devices and accessories', NULL, 1),
(2, 'Smartphones', 'Mobile phones and accessories', 1, 1),
(3, 'Laptops', 'Portable computers', 1, 1),
(4, 'Groceries', 'Food and household items', NULL, 1),
(5, 'Dairy', 'Milk and dairy products', 4, 1),
(6, 'Bakery', 'Bread and baked goods', 4, 1),
(7, 'Clothing', 'Apparel and fashion items', NULL, 1),
(8, 'Men''s Wear', 'Clothing for men', 7, 1),
(9, 'Women''s Wear', 'Clothing for women', 7, 1),
(10, 'Accessories', 'Fashion accessories', 7, 1);

-- Insert suppliers
INSERT INTO Suppliers (Id, Name, ContactName, Email, Phone, Address, Website, Notes, IsActive, CreatedAt) VALUES
(1, 'ABC Electronics', 'John Smith', '<EMAIL>', '************', '123 Tech Blvd, Silicon Valley, CA', 'www.abcelectronics.com', 'Reliable electronics supplier', 1, datetime('now')),
(2, 'Fresh Foods Inc.', 'Sarah Johnson', '<EMAIL>', '************', '456 Produce Lane, Farmville, OR', 'www.freshfoodsinc.com', 'Organic food supplier', 1, datetime('now')),
(3, 'Fashion Forward', 'Michael Brown', '<EMAIL>', '************', '789 Style Ave, New York, NY', 'www.fashionforward.com', 'Trendy clothing supplier', 1, datetime('now'));

-- Insert customers
INSERT INTO Customers (Id, FirstName, LastName, Email, Phone, Address, LoyaltyCode, LoyaltyPoints, TotalSpent, LoyaltyTierId, TotalVisits, LastVisit, IsActive, CreatedAt, UpdatedAt) VALUES
(1, 'John', 'Doe', '<EMAIL>', '************', '123 Main St', 'JD12345', 100, 500.00, 1, 5, datetime('now'), 1, datetime('now'), datetime('now')),
(2, 'Jane', 'Smith', '<EMAIL>', '************', '456 Oak Ave', 'JS67890', 250, 800.00, 1, 8, datetime('now'), 1, datetime('now'), datetime('now')),
(3, 'Bob', 'Johnson', '<EMAIL>', '************', '789 Pine Rd', 'BJ24680', 1200, 1500.00, 2, 12, datetime('now'), 1, datetime('now'), datetime('now')),
(4, 'Alice', 'Williams', '<EMAIL>', '************', '321 Maple Dr', 'AW13579', 6000, 8000.00, 3, 25, datetime('now'), 1, datetime('now'), datetime('now'));

-- Insert products
INSERT INTO Products (Id, Name, Description, SKU, CategoryId, UnitOfMeasureId, SellingPrice, PurchasePrice, StockQuantity, MinimumStock, ReorderPoint, SupplierId, LoyaltyPoints, IsActive, CreatedAt, UpdatedAt) VALUES
-- Electronics
(1, 'Smartphone X', 'Latest model smartphone with high-resolution camera', 'SP-X01', 2, 1, 699.99, 499.99, 15, 5, 10, 1, 70, 1, datetime('now'), datetime('now')),
(2, 'Laptop Pro', '15-inch laptop with fast processor and SSD', 'LP-P01', 3, 1, 1299.99, 999.99, 8, 3, 5, 1, 130, 1, datetime('now'), datetime('now')),
(3, 'Wireless Earbuds', 'Bluetooth earbuds with noise cancellation', 'WE-01', 1, 1, 129.99, 79.99, 25, 10, 15, 1, 13, 1, datetime('now'), datetime('now')),
(4, 'Smart Watch', 'Fitness and health tracking smartwatch', 'SW-01', 1, 1, 249.99, 149.99, 12, 5, 8, 1, 25, 1, datetime('now'), datetime('now')),

-- Groceries
(5, 'Milk 1L', 'Fresh whole milk', 'GR-ML01', 5, 4, 2.99, 1.99, 50, 20, 30, 2, 3, 1, datetime('now'), datetime('now')),
(6, 'Bread', 'Whole wheat bread loaf', 'GR-BR01', 6, 1, 3.49, 1.99, 30, 15, 20, 2, 3, 1, datetime('now'), datetime('now')),
(7, 'Cheese 500g', 'Cheddar cheese block', 'GR-CH01', 5, 3, 5.99, 3.99, 25, 10, 15, 2, 6, 1, datetime('now'), datetime('now')),
(8, 'Eggs 12pk', 'Farm fresh eggs', 'GR-EG01', 4, 8, 4.49, 2.99, 40, 15, 25, 2, 4, 1, datetime('now'), datetime('now')),

-- Clothing
(9, 'Men''s T-Shirt', 'Cotton crew neck t-shirt', 'CL-MT01', 8, 1, 19.99, 9.99, 50, 20, 30, 3, 20, 1, datetime('now'), datetime('now')),
(10, 'Women''s Jeans', 'Slim fit denim jeans', 'CL-WJ01', 9, 1, 39.99, 19.99, 35, 15, 25, 3, 40, 1, datetime('now'), datetime('now')),
(11, 'Baseball Cap', 'Adjustable cotton cap', 'CL-BC01', 10, 1, 14.99, 7.99, 45, 20, 30, 3, 15, 1, datetime('now'), datetime('now')),
(12, 'Leather Belt', 'Genuine leather belt', 'CL-LB01', 10, 1, 29.99, 14.99, 30, 10, 20, 3, 30, 1, datetime('now'), datetime('now'));

-- Insert barcodes
INSERT INTO ProductBarcodes (ProductId, Barcode, Description, IsPrimary, CreatedAt) VALUES
(1, '123456789012', 'Smartphone X primary barcode', 1, datetime('now')),
(2, '223456789012', 'Laptop Pro primary barcode', 1, datetime('now')),
(3, '323456789012', 'Wireless Earbuds primary barcode', 1, datetime('now')),
(4, '423456789012', 'Smart Watch primary barcode', 1, datetime('now')),
(5, '523456789012', 'Milk 1L primary barcode', 1, datetime('now')),
(6, '623456789012', 'Bread primary barcode', 1, datetime('now')),
(7, '723456789012', 'Cheese 500g primary barcode', 1, datetime('now')),
(8, '823456789012', 'Eggs 12pk primary barcode', 1, datetime('now')),
(9, '923456789012', 'Men''s T-Shirt primary barcode', 1, datetime('now')),
(10, '023456789012', 'Women''s Jeans primary barcode', 1, datetime('now')),
(11, '123456789013', 'Baseball Cap primary barcode', 1, datetime('now')),
(12, '223456789013', 'Leather Belt primary barcode', 1, datetime('now'));

-- Create a cash drawer
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, Difference, Status, OpenedAt, OpenedById, Notes)
VALUES (500.00, 500.00, 500.00, 500.00, 0.00, 'Open', datetime('now'), 1, 'Initial cash drawer for testing');

-- Create some test sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES 
('INV-001', datetime('now', '-2 days'), 1, 3, 752.99, 0, 0, 752.99, 752.99, 0, 'Cash', 'Paid', 'Completed', 3),
('INV-002', datetime('now', '-1 days'), 2, 3, 169.98, 17.00, 0, 152.98, 152.98, 0, 'Card', 'Paid', 'Completed', 2),
('INV-003', datetime('now'), 3, 2, 1329.98, 0, 0, 1329.98, 1329.98, 0, 'Card', 'Paid', 'Completed', 1);

-- Add sale items
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES 
(1, 1, 1, 699.99, 699.99),  -- Smartphone X
(1, 3, 1, 129.99, 129.99),  -- Wireless Earbuds, in first sale
(2, 9, 2, 19.99, 39.98),    -- Men's T-Shirt x2
(2, 11, 2, 14.99, 29.98),   -- Baseball Cap x2
(3, 2, 1, 1299.99, 1299.99); -- Laptop Pro

-- Create some discounts
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
VALUES 
(1, 10, 169.98, 152.98, 5, 'Promotional discount', 2, NULL, 2, datetime('now', '-1 days'), 1);  -- 10% off entire sale #2

-- Create user favorites
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
VALUES 
(3, 1, datetime('now')),  -- Cashier favorites Smartphone X
(3, 5, datetime('now')),  -- Cashier favorites Milk
(3, 6, datetime('now')),  -- Cashier favorites Bread
(2, 2, datetime('now')),  -- Manager favorites Laptop Pro
(2, 10, datetime('now')); -- Manager favorites Women's Jeans 