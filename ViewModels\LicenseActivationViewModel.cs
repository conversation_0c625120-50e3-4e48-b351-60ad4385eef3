using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using POSSystem.Services;
using POSSystem.Commands;
using System.Windows;

namespace POSSystem.ViewModels
{
    public class LicenseActivationViewModel : INotifyPropertyChanged
    {
        private readonly LicenseService _licenseService;
        private string _businessName;
        private string _licenseKey;
        private string _errorMessage;
        private bool _hasError;
        private string _systemId;
        private readonly RelayCommand _activateCommand;
        private readonly RelayCommand _exitCommand;
        private readonly RelayCommand _generateSystemIdCommand;
        private readonly RelayCommand _copySystemIdCommand;

        public LicenseActivationViewModel()
        {
            _licenseService = new LicenseService();
            _activateCommand = new RelayCommand(ExecuteActivate, CanExecuteActivate);
            _exitCommand = new RelayCommand(_ => System.Windows.Application.Current.Shutdown());
            _generateSystemIdCommand = new RelayCommand(_ => ExecuteGenerateSystemId());
            _copySystemIdCommand = new RelayCommand(_ => ExecuteCopySystemId(), _ => !string.IsNullOrEmpty(SystemId));
        }

        public string BusinessName
        {
            get => _businessName;
            set
            {
                _businessName = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string LicenseKey
        {
            get => _licenseKey;
            set
            {
                _licenseKey = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string SystemId
        {
            get => _systemId;
            set
            {
                _systemId = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged();
                HasError = !string.IsNullOrEmpty(value);
            }
        }

        public bool HasError
        {
            get => _hasError;
            set
            {
                _hasError = value;
                OnPropertyChanged();
            }
        }

        public ICommand ActivateCommand => _activateCommand;
        public ICommand ExitCommand => _exitCommand;
        public ICommand GenerateSystemIdCommand => _generateSystemIdCommand;
        public ICommand CopySystemIdCommand => _copySystemIdCommand;

        private bool CanExecuteActivate(object parameter)
        {
            return !string.IsNullOrWhiteSpace(BusinessName) && 
                   !string.IsNullOrWhiteSpace(LicenseKey) &&
                   !string.IsNullOrWhiteSpace(SystemId);
        }

        private void ExecuteActivate(object parameter)
        {
            try
            {
                if (_licenseService.ActivateLicense(LicenseKey, BusinessName))
                {
                    // Restart application to apply license
                    System.Windows.Application.Current.Shutdown();
                    System.Diagnostics.Process.Start(System.Windows.Application.ResourceAssembly.Location);
                }
                else
                {
                    ErrorMessage = "Invalid license key. Please check your input and try again.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error activating license: {ex.Message}";
            }
        }

        private void ExecuteGenerateSystemId()
        {
            try
            {
                SystemId = _licenseService.GetHardwareId();
                ErrorMessage = string.Empty;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error generating System ID: {ex.Message}";
                SystemId = string.Empty;
            }
        }

        private void ExecuteCopySystemId()
        {
            try
            {
                Clipboard.SetText(SystemId);
                MessageBox.Show("System ID copied to clipboard!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error copying to clipboard: {ex.Message}";
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 