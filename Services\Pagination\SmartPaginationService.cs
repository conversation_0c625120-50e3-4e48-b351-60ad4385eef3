using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Services.Pagination
{
    /// <summary>
    /// ✅ NEW: Smart pagination service with virtual scrolling for large datasets
    /// Implements intelligent page sizing and on-demand loading for optimal performance
    /// </summary>
    public class SmartPaginationService<T> : INotifyPropertyChanged where T : class
    {
        private readonly Func<int, int, Task<PagedResult<T>>> _dataLoader;
        private readonly int _defaultPageSize;
        private readonly Dictionary<int, CachedPage<T>> _pageCache = new();
        
        private int _currentPage = 1;
        private int _totalItems = 0;
        private int _totalPages = 0;
        private bool _isLoading = false;
        private string _searchFilter = string.Empty;

        public event PropertyChangedEventHandler PropertyChanged;

        public SmartPaginationService(Func<int, int, Task<PagedResult<T>>> dataLoader, int defaultPageSize = 50)
        {
            _dataLoader = dataLoader ?? throw new ArgumentNullException(nameof(dataLoader));
            _defaultPageSize = defaultPageSize;
        }

        #region Properties

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (_currentPage != value && value > 0 && value <= TotalPages)
                {
                    _currentPage = value;
                    OnPropertyChanged();
                    _ = LoadPageAsync(value);
                }
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            private set
            {
                if (_totalItems != value)
                {
                    _totalItems = value;
                    OnPropertyChanged();
                    UpdateTotalPages();
                }
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            private set
            {
                if (_totalPages != value)
                {
                    _totalPages = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            private set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SearchFilter
        {
            get => _searchFilter;
            set
            {
                if (_searchFilter != value)
                {
                    _searchFilter = value;
                    OnPropertyChanged();
                    _ = RefreshAsync();
                }
            }
        }

        public ObservableCollection<T> CurrentPageItems { get; } = new ObservableCollection<T>();

        public bool CanGoToPreviousPage => CurrentPage > 1;
        public bool CanGoToNextPage => CurrentPage < TotalPages;

        #endregion

        /// <summary>
        /// ✅ CORE: Load a specific page with caching
        /// </summary>
        public async Task LoadPageAsync(int pageNumber)
        {
            if (pageNumber < 1 || IsLoading)
                return;

            try
            {
                IsLoading = true;
                var stopwatch = Stopwatch.StartNew();

                // Check cache first
                if (_pageCache.TryGetValue(pageNumber, out var cachedPage) && 
                    cachedPage.IsValid && 
                    cachedPage.SearchFilter == SearchFilter)
                {
                    Debug.WriteLine($"SmartPaginationService: Using cached page {pageNumber}");
                    UpdateCurrentPageItems(cachedPage.Items);
                    _currentPage = pageNumber;
                    OnPropertyChanged(nameof(CurrentPage));
                    return;
                }

                // Load from data source
                var result = await _dataLoader(pageNumber, _defaultPageSize);
                
                if (result != null)
                {
                    // Update totals
                    TotalItems = result.TotalCount;
                    
                    // Cache the page
                    _pageCache[pageNumber] = new CachedPage<T>
                    {
                        Items = result.Items.ToList(),
                        PageNumber = pageNumber,
                        LoadedAt = DateTime.Now,
                        SearchFilter = SearchFilter
                    };

                    // Update UI
                    UpdateCurrentPageItems(result.Items);
                    _currentPage = pageNumber;
                    OnPropertyChanged(nameof(CurrentPage));
                    OnPropertyChanged(nameof(CanGoToPreviousPage));
                    OnPropertyChanged(nameof(CanGoToNextPage));

                    stopwatch.Stop();
                    Debug.WriteLine($"SmartPaginationService: Loaded page {pageNumber} in {stopwatch.ElapsedMilliseconds}ms ({result.Items.Count} items)");

                    // Preload adjacent pages in background
                    _ = Task.Run(() => PreloadAdjacentPagesAsync(pageNumber));
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading page {pageNumber}: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Preload adjacent pages for smooth scrolling
        /// </summary>
        private async Task PreloadAdjacentPagesAsync(int currentPage)
        {
            try
            {
                var preloadTasks = new List<Task>();

                // Preload previous page
                if (currentPage > 1 && !_pageCache.ContainsKey(currentPage - 1))
                {
                    preloadTasks.Add(PreloadPageAsync(currentPage - 1));
                }

                // Preload next page
                if (currentPage < TotalPages && !_pageCache.ContainsKey(currentPage + 1))
                {
                    preloadTasks.Add(PreloadPageAsync(currentPage + 1));
                }

                if (preloadTasks.Count > 0)
                {
                    await Task.WhenAll(preloadTasks);
                    Debug.WriteLine($"SmartPaginationService: Preloaded {preloadTasks.Count} adjacent pages");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading adjacent pages: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ HELPER: Preload a specific page without updating UI
        /// </summary>
        private async Task PreloadPageAsync(int pageNumber)
        {
            try
            {
                var result = await _dataLoader(pageNumber, _defaultPageSize);
                if (result != null)
                {
                    _pageCache[pageNumber] = new CachedPage<T>
                    {
                        Items = result.Items.ToList(),
                        PageNumber = pageNumber,
                        LoadedAt = DateTime.Now,
                        SearchFilter = SearchFilter
                    };
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading page {pageNumber}: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ NAVIGATION: Go to next page
        /// </summary>
        public async Task GoToNextPageAsync()
        {
            if (CanGoToNextPage)
            {
                await LoadPageAsync(CurrentPage + 1);
            }
        }

        /// <summary>
        /// ✅ NAVIGATION: Go to previous page
        /// </summary>
        public async Task GoToPreviousPageAsync()
        {
            if (CanGoToPreviousPage)
            {
                await LoadPageAsync(CurrentPage - 1);
            }
        }

        /// <summary>
        /// ✅ NAVIGATION: Go to first page
        /// </summary>
        public async Task GoToFirstPageAsync()
        {
            await LoadPageAsync(1);
        }

        /// <summary>
        /// ✅ NAVIGATION: Go to last page
        /// </summary>
        public async Task GoToLastPageAsync()
        {
            if (TotalPages > 0)
            {
                await LoadPageAsync(TotalPages);
            }
        }

        /// <summary>
        /// ✅ REFRESH: Refresh current data and clear cache
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                Debug.WriteLine("SmartPaginationService: Refreshing data...");
                
                // Clear cache
                _pageCache.Clear();
                
                // Reload current page
                await LoadPageAsync(Math.Max(1, CurrentPage));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing pagination: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ HELPER: Update current page items on UI thread
        /// </summary>
        private void UpdateCurrentPageItems(IEnumerable<T> items)
        {
            try
            {
                CurrentPageItems.Clear();
                foreach (var item in items)
                {
                    CurrentPageItems.Add(item);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating current page items: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ HELPER: Update total pages calculation
        /// </summary>
        private void UpdateTotalPages()
        {
            TotalPages = TotalItems > 0 ? (int)Math.Ceiling((double)TotalItems / _defaultPageSize) : 0;
        }

        /// <summary>
        /// ✅ CLEANUP: Clear expired cache entries
        /// </summary>
        public void ClearExpiredCache(TimeSpan maxAge)
        {
            try
            {
                var cutoff = DateTime.Now - maxAge;
                var expiredKeys = _pageCache
                    .Where(kvp => kvp.Value.LoadedAt < cutoff)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _pageCache.Remove(key);
                }

                if (expiredKeys.Count > 0)
                {
                    Debug.WriteLine($"SmartPaginationService: Cleared {expiredKeys.Count} expired cache entries");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing expired cache: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// ✅ RESULT: Paged result container
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage => PageNumber * PageSize < TotalCount;
        public bool HasPreviousPage => PageNumber > 1;
    }

    /// <summary>
    /// ✅ CACHE: Cached page entry
    /// </summary>
    internal class CachedPage<T>
    {
        public List<T> Items { get; set; }
        public int PageNumber { get; set; }
        public DateTime LoadedAt { get; set; }
        public string SearchFilter { get; set; }
        
        public bool IsValid => DateTime.Now - LoadedAt < TimeSpan.FromMinutes(5);
    }
}
