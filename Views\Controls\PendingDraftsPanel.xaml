<UserControl x:Class="POSSystem.Views.Controls.PendingDraftsPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        
        <!-- Pending Draft Item Style -->
        <Style x:Key="PendingDraftCard" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,4"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <materialDesign:Card Padding="16" 
                        Background="{DynamicResource MaterialDesignPaper}"
                        materialDesign:ShadowAssist.ShadowDepth="Depth2">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="FileDocumentAlert" 
                                       Width="24" Height="24" 
                                       Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                       VerticalAlignment="Center"/>
                <TextBlock Text="{DynamicResource PendingDraftInvoices}"
                          Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                          Margin="8,0,0,0"
                          VerticalAlignment="Center"/>
                <materialDesign:Chip Content="{Binding PendingCount}" 
                                   Margin="12,0,0,0" 
                                   Background="{DynamicResource SecondaryHueMidBrush}"
                                   Foreground="White"
                                   Visibility="{Binding HasPendingDrafts, Converter={StaticResource BoolToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Pending Drafts List -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto"
                         MaxHeight="400">
                <StackPanel>
                    <!-- No Pending Drafts Message -->
                    <StackPanel Visibility="{Binding HasPendingDrafts, Converter={StaticResource InverseBoolToVisibilityConverter}}"
                               HorizontalAlignment="Center"
                               Margin="0,32">
                        <materialDesign:PackIcon Kind="CheckCircle" 
                                               Width="48" Height="48" 
                                               Foreground="{DynamicResource SuccessBrush}"
                                               HorizontalAlignment="Center"/>
                        <TextBlock Text="{DynamicResource NoPendingDraftInvoices}"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  HorizontalAlignment="Center"
                                  Margin="0,8,0,0"/>
                        <TextBlock Text="{DynamicResource AllDraftInvoicesProcessed}"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <!-- Pending Drafts Items -->
                    <ItemsControl ItemsSource="{Binding PendingDrafts}"
                                 Visibility="{Binding HasPendingDrafts, Converter={StaticResource BoolToVisibilityConverter}}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <materialDesign:Card Style="{StaticResource PendingDraftCard}"
                                                   Tag="{Binding}"
                                                   MouseLeftButtonUp="PendingDraft_Click">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- Priority Indicator -->
                                        <Border Grid.Column="0" 
                                               Width="4" 
                                               CornerRadius="2"
                                               Margin="0,0,12,0">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsHighPriority}" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource SecondaryHueMidBrush}"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsExpired}" Value="True">
                                                            <Setter Property="Background" Value="#f44336"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                        </Border>
                                        
                                        <!-- Draft Information -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding InvoiceNumber}" 
                                                      FontWeight="Medium"
                                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                            <TextBlock Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}">
                                                <TextBlock.Text>
                                                    <MultiBinding StringFormat="Created by {0} • {1} items • {2}">
                                                        <Binding Path="CreatedByUserName"/>
                                                        <Binding Path="ItemCount"/>
                                                        <Binding Path="TimeAgoDisplay"/>
                                                    </MultiBinding>
                                                </TextBlock.Text>
                                            </TextBlock>
                                            <TextBlock Text="{Binding CustomerName}" 
                                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      Visibility="{Binding HasCustomer, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                        </StackPanel>
                                        
                                        <!-- Amount -->
                                        <StackPanel Grid.Column="2" 
                                                   HorizontalAlignment="Right"
                                                   VerticalAlignment="Center"
                                                   Margin="8,0">
                                            <TextBlock Text="{Binding SubtotalDisplay}" 
                                                      FontWeight="Bold"
                                                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                      HorizontalAlignment="Right"/>
                                            <TextBlock Text="{DynamicResource Estimated}"
                                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      HorizontalAlignment="Right"/>
                                        </StackPanel>
                                        
                                        <!-- Action Button -->
                                        <Button Grid.Column="3" 
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Command="{Binding DataContext.CompleteDraftCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               ToolTip="{DynamicResource CompleteDraft}"
                                               Margin="8,0,0,0">
                                            <materialDesign:PackIcon Kind="FileDocumentCheck" 
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        </Button>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer Actions -->
            <StackPanel Grid.Row="2" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right" 
                       Margin="0,16,0,0"
                       Visibility="{Binding HasPendingDrafts, Converter={StaticResource BoolToVisibilityConverter}}">
                <Button Content="{DynamicResource Refresh}"
                       Command="{Binding RefreshCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,8,0"/>
                <Button Content="{DynamicResource ViewAll}"
                       Command="{Binding ViewAllCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
