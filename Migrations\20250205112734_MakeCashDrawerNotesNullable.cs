﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class MakeCashDrawerNotesNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "SKU",
                table: "Products",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Products",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<bool>(
                name: "IsRead",
                table: "ProductAlerts",
                type: "INTEGER",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "INTEGER");

            migrationBuilder.AddColumn<int>(
                name: "ReferenceId",
                table: "ProductAlerts",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReferenceType",
                table: "ProductAlerts",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "CashDrawers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OpeningBalance = table.Column<decimal>(type: "TEXT", precision: 18, scale: 2, nullable: false),
                    CurrentBalance = table.Column<decimal>(type: "TEXT", precision: 18, scale: 2, nullable: false),
                    ExpectedBalance = table.Column<decimal>(type: "TEXT", precision: 18, scale: 2, nullable: false),
                    ActualBalance = table.Column<decimal>(type: "TEXT", precision: 18, scale: 2, nullable: false),
                    Difference = table.Column<decimal>(type: "TEXT", precision: 18, scale: 2, nullable: false),
                    OpenedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ClosedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    OpenedById = table.Column<int>(type: "INTEGER", nullable: false),
                    ClosedById = table.Column<int>(type: "INTEGER", nullable: true),
                    Status = table.Column<string>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashDrawers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CashDrawers_Users_ClosedById",
                        column: x => x.ClosedById,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CashDrawers_Users_OpenedById",
                        column: x => x.OpenedById,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CashTransactions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CashDrawerId = table.Column<int>(type: "INTEGER", nullable: false),
                    Type = table.Column<string>(type: "TEXT", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Reason = table.Column<string>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Timestamp = table.Column<DateTime>(type: "TEXT", nullable: false),
                    PerformedById = table.Column<int>(type: "INTEGER", nullable: true),
                    Reference = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CashTransactions_CashDrawers_CashDrawerId",
                        column: x => x.CashDrawerId,
                        principalTable: "CashDrawers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CashTransactions_Users_PerformedById",
                        column: x => x.PerformedById,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5212), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5183), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5213) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5221), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5219), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5221) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 53, DateTimeKind.Local).AddTicks(9452));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6821));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6825));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6828));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2588));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2593));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2602));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2606));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2610));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2614));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2617));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2621));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 52, DateTimeKind.Local).AddTicks(391), new DateTime(2025, 2, 5, 12, 27, 34, 52, DateTimeKind.Local).AddTicks(400) });

            migrationBuilder.CreateIndex(
                name: "IX_CashDrawers_ClosedById",
                table: "CashDrawers",
                column: "ClosedById");

            migrationBuilder.CreateIndex(
                name: "IX_CashDrawers_OpenedById",
                table: "CashDrawers",
                column: "OpenedById");

            migrationBuilder.CreateIndex(
                name: "IX_CashTransactions_CashDrawerId",
                table: "CashTransactions",
                column: "CashDrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTransactions_PerformedById",
                table: "CashTransactions",
                column: "PerformedById");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CashTransactions");

            migrationBuilder.DropTable(
                name: "CashDrawers");

            migrationBuilder.DropColumn(
                name: "ReferenceId",
                table: "ProductAlerts");

            migrationBuilder.DropColumn(
                name: "ReferenceType",
                table: "ProductAlerts");

            migrationBuilder.AlterColumn<string>(
                name: "SKU",
                table: "Products",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Products",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsRead",
                table: "ProductAlerts",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "INTEGER",
                oldDefaultValue: false);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8021), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(7986), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8022) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8033), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8030), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8034) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 365, DateTimeKind.Local).AddTicks(2499));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9861));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9867));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9872));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6057));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6063));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6075));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6079));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6084));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6088));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6092));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6132));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 361, DateTimeKind.Local).AddTicks(5979), new DateTime(2025, 2, 5, 0, 21, 45, 361, DateTimeKind.Local).AddTicks(5989) });
        }
    }
}
