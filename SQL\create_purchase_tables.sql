-- Create PurchaseOrders table
CREATE TABLE IF NOT EXISTS PurchaseOrders (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OrderNumber TEXT NOT NULL,
    OrderDate TEXT NOT NULL,
    SupplierId INTEGER NOT NULL,
    Status TEXT NOT NULL DEFAULT 'Pending',
    UpdatedAt TEXT,
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
);

-- Create PurchaseOrderItems table
CREATE TABLE IF NOT EXISTS PurchaseOrderItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PurchaseOrderId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id),
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON PurchaseOrders(OrderDate);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON PurchaseOrders(SupplierId);
CREATE INDEX IF NOT EXISTS idx_purchase_order_items_order ON PurchaseOrderItems(PurchaseOrderId);
CREATE INDEX IF NOT EXISTS idx_purchase_order_items_product ON PurchaseOrderItems(ProductId); 