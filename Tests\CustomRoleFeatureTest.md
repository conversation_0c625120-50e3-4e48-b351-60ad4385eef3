# Custom Role Feature Test Plan

## Overview
This test plan validates the "Custom" role functionality that automatically switches the role selection to "Custom" when users modify permission checkboxes from standard role defaults.

## Feature Description
When a user modifies any permission checkbox after a role has been selected, the role selection automatically changes to "Custom" to indicate that the permissions no longer match the standard role defaults. This provides clear visual feedback that the user has created a custom permission set.

## Key Features Implemented

### ✅ 1. Custom Role Option
- Added "Custom" option to the role ComboBox in UserDialog
- Custom role has ID -1 to distinguish from database roles
- Custom role represents user-defined permission combinations

### ✅ 2. Automatic Role Switching
- When any permission checkbox is modified by the user, role automatically switches to "Custom"
- Only triggers after initial role-based population is complete
- Works for both new user creation and existing user editing scenarios

### ✅ 3. Smart Permission Detection
- Compares current permissions with selected role's defaults
- Switches to Custom only when permissions deviate from role standards
- Preserves existing custom permissions in edit mode

### ✅ 4. Integration with Existing Features
- Works seamlessly with role-based permission population
- Doesn't interfere with existing role selection functionality
- Maintains all existing user management capabilities

## Test Scenarios

### Test 1: New User Creation - Automatic Custom Role Switch
**Objective**: Verify that modifying permissions automatically switches to Custom role

**Steps**:
1. Open Users management and click "Add User"
2. Fill in basic user information
3. Select "Admin" role - observe all permissions are checked
4. Manually uncheck one permission (e.g., "Can Manage Users")
5. Observe the role selection

**Expected Results**:
- Role selection should automatically change to "Custom"
- Modified permissions should remain as set by user
- Debug output should show "Permissions modified from Admin defaults - switching to Custom role"

### Test 2: Role Selection After Custom Modification
**Objective**: Verify behavior when selecting different roles after Custom is set

**Steps**:
1. Follow Test 1 to get to Custom role state
2. Select "Manager" role from dropdown
3. Observe permission checkboxes update to Manager defaults
4. Modify a permission again
5. Observe role selection

**Expected Results**:
- Manager role selection should populate Manager permissions
- After modification, should switch back to Custom role
- Permissions should reflect the user's modifications

### Test 3: Edit Mode - Existing Custom Permissions
**Objective**: Verify that existing users with custom permissions show Custom role

**Steps**:
1. Create a user with custom permissions (using Test 1)
2. Save the user
3. Edit the same user
4. Observe the initial role selection and permissions

**Expected Results**:
- If permissions don't match any role defaults, should show "Custom" role
- If permissions match a role exactly, should show that role
- Existing permissions should be preserved

### Test 4: Custom Role Selection Behavior
**Objective**: Verify that selecting Custom role doesn't populate any permissions

**Steps**:
1. Open Users management and click "Add User"
2. Fill in basic user information
3. Select "Custom" role directly from dropdown
4. Observe permission checkboxes

**Expected Results**:
- No automatic permission population should occur
- All permissions should remain in their default state
- User can manually set any combination of permissions

### Test 5: Permission Comparison Accuracy
**Objective**: Verify that permission comparison correctly identifies matches/mismatches

**Steps**:
1. Create user with Admin role (all permissions enabled)
2. Save and edit the user
3. Verify role shows as "Admin" (permissions match defaults)
4. Modify one permission and save
5. Edit user again and verify role shows as "Custom"

**Expected Results**:
- Exact matches should show the corresponding role
- Any deviation should show "Custom" role
- Permission comparison should be accurate across all permission types

## Technical Implementation Details

### Custom Role Creation
```csharp
private Role CreateCustomRole()
{
    return new Role
    {
        Id = -1, // Negative ID to distinguish from database roles
        Name = "Custom",
        Description = "User-defined permission combination",
        IsActive = true,
        CreatedAt = DateTime.Now
    };
}
```

### Permission Change Detection
- Uses checkbox event handlers to detect user modifications
- Suppresses events during role-based permission loading
- Compares current permissions with role defaults to determine if switch is needed

### Role Validation for Saving
- Custom role users are assigned a default database role for storage
- Permissions are saved as custom regardless of assigned role
- System handles Custom role transparently during save operations

## Debug Output Examples

**Role-based permission loading**:
```
Loading default permissions for role: Admin
  - CanCreateSales: True
  - CanVoidSales: True
  - CanManageProducts: True
  - CanManageUsers: True
```

**Custom role switch**:
```
Permissions modified from Admin defaults - switching to Custom role
```

**Custom role selection**:
```
Custom role selected - not updating permissions
```

## Integration Points

### With Role-Based Permission Updates
- Custom role functionality builds on the role-based permission update feature
- Maintains all existing role selection and permission population behavior
- Adds automatic detection and visual feedback for custom configurations

### With User Management
- Seamlessly integrates with existing user creation and editing workflows
- Preserves all existing validation and save logic
- Enhances user experience without breaking existing functionality

## Success Criteria

✅ **Automatic Detection**: Permission modifications automatically trigger Custom role switch
✅ **Visual Feedback**: Users immediately see when they've created custom permissions
✅ **Preservation**: Custom permissions are preserved during editing
✅ **Integration**: Works seamlessly with existing role-based features
✅ **Accuracy**: Permission comparison correctly identifies matches and deviations
✅ **User Experience**: Intuitive behavior that enhances rather than complicates workflow

## Manual Testing Checklist

- [ ] Custom role appears in role dropdown
- [ ] Modifying permissions switches to Custom role automatically
- [ ] Custom role selection doesn't populate permissions
- [ ] Role switching works correctly after Custom is set
- [ ] Edit mode preserves custom permissions and shows Custom role
- [ ] Permission comparison accurately detects matches/mismatches
- [ ] Save functionality works correctly with Custom role
- [ ] Debug output shows appropriate messages
- [ ] No errors or exceptions during role switching
- [ ] Integration with existing features is seamless

## Benefits

1. **Clear Visual Feedback**: Users immediately know when they've deviated from standard roles
2. **Intuitive Workflow**: Automatic switching reduces confusion about permission states
3. **Flexible Permission Management**: Supports both standard roles and custom configurations
4. **Enhanced User Experience**: Makes permission customization more transparent
5. **Maintains Consistency**: Standard roles remain clearly defined while allowing customization
