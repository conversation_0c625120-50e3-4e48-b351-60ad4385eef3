<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.Dialogs.ReceiptPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d" 
        Title="Receipt Preview" 
        Height="700" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           UniformCornerRadius="0"
                           Margin="0,0,0,8">
            <Grid Margin="16,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0"
                                       Kind="PrinterEye" 
                                       Width="24" 
                                       Height="24"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                       Margin="0,0,12,0"/>

                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="Receipt Preview" 
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             FontWeight="Medium"/>
                    <TextBlock x:Name="InvoiceNumberText"
                             Text="Invoice #: Loading..."
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             Opacity="0.8"/>
                </StackPanel>

                <Button Grid.Column="2"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Width="32"
                      Height="32"
                      Click="CloseWindow_Click"
                      Foreground="{DynamicResource PrimaryHueMidForegroundBrush}">
                    <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Preview Content -->
        <Border Grid.Row="1" 
              Background="White"
              BorderBrush="{DynamicResource MaterialDesignDivider}"
              BorderThickness="1"
              Margin="16,0,16,8">
            <ScrollViewer x:Name="PreviewScrollViewer"
                        VerticalScrollBarVisibility="Auto"
                        HorizontalScrollBarVisibility="Auto"
                        Padding="16">
                <DocumentViewer x:Name="ReceiptDocumentViewer"
                              Background="White"
                              Zoom="100"/>
            </ScrollViewer>
        </Border>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" 
                           Background="{DynamicResource MaterialDesignCardBackground}"
                           UniformCornerRadius="0"
                           Margin="0,8,0,0">
            <Grid Margin="16,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Left side - Additional options -->
                <StackPanel Grid.Column="0" 
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <Button x:Name="SaveAsPdfButton"
                          Content="Save as PDF"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="SaveAsPdf_Click"
                          Margin="0,0,8,0"
                          Height="36">
                        <Button.ToolTip>
                            <ToolTip>
                                <TextBlock Text="Save receipt as PDF file"/>
                            </ToolTip>
                        </Button.ToolTip>
                    </Button>

                    <Button x:Name="ZoomInButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Click="ZoomIn_Click"
                          Margin="4,0"
                          Width="36"
                          Height="36">
                        <materialDesign:PackIcon Kind="MagnifyPlus" Width="18" Height="18"/>
                        <Button.ToolTip>
                            <ToolTip>
                                <TextBlock Text="Zoom In"/>
                            </ToolTip>
                        </Button.ToolTip>
                    </Button>

                    <Button x:Name="ZoomOutButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Click="ZoomOut_Click"
                          Margin="4,0"
                          Width="36"
                          Height="36">
                        <materialDesign:PackIcon Kind="MagnifyMinus" Width="18" Height="18"/>
                        <Button.ToolTip>
                            <ToolTip>
                                <TextBlock Text="Zoom Out"/>
                            </ToolTip>
                        </Button.ToolTip>
                    </Button>

                    <Button x:Name="FitToWidthButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Click="FitToWidth_Click"
                          Margin="4,0"
                          Width="36"
                          Height="36">
                        <materialDesign:PackIcon Kind="FitToPageOutline" Width="18" Height="18"/>
                        <Button.ToolTip>
                            <ToolTip>
                                <TextBlock Text="Fit to Width"/>
                            </ToolTip>
                        </Button.ToolTip>
                    </Button>
                </StackPanel>

                <!-- Right side - Main actions -->
                <StackPanel Grid.Column="1" 
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <Button x:Name="PrintButton"
                          Content="Print"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="{DynamicResource PrimaryHueMidBrush}"
                          Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                          Click="Print_Click"
                          Margin="0,0,8,0"
                          Height="36"
                          Width="100">
                        <Button.ToolTip>
                            <ToolTip>
                                <TextBlock Text="Print receipt"/>
                            </ToolTip>
                        </Button.ToolTip>
                    </Button>

                    <Button Content="Close"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="CloseWindow_Click"
                          Height="36"
                          Width="80"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
