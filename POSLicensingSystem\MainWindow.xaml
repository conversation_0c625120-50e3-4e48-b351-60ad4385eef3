﻿<Window x:Class="POSLicensingSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:data="clr-namespace:System.Windows.Data;assembly=PresentationFramework"
        mc:Ignorable="d"
        Title="POS Licensing Management" Height="550" Width="750"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        Icon="{StaticResource AppIconImage}"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <materialDesign:Card Grid.Row="0" Margin="16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Kind="License" 
                                         Height="72" 
                                         Width="72" 
                                         VerticalAlignment="Center"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                
                <StackPanel Grid.Column="1" Margin="16,0,0,0">
                    <TextBlock Text="POS Licensing Management Tool" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Margin="0,0,0,8"/>
                    
                    <TextBlock Text="Use this tool to activate and manage licenses for the POS System"
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             TextWrapping="Wrap"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            
            <!-- License Activation -->
            <Button Grid.Column="0" Grid.Row="0"
                    Margin="8" 
                    Height="120"
                    Command="{Binding ActivateLicenseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="KeyVariant" 
                                             Height="48" 
                                             Width="48" 
                                             HorizontalAlignment="Center" />
                    <TextBlock Text="Activate License" 
                               HorizontalAlignment="Center"
                               Margin="0,8,0,0" />
                </StackPanel>
            </Button>
            
            <!-- Generate License Key -->
            <Button Grid.Column="1" Grid.Row="0"
                    Margin="8" 
                    Height="120"
                    Command="{Binding GenerateLicenseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="KeyPlus" 
                                             Height="48" 
                                             Width="48" 
                                             HorizontalAlignment="Center" />
                    <TextBlock Text="Generate License Key" 
                               HorizontalAlignment="Center" 
                               Margin="0,8,0,0"
                               TextWrapping="Wrap"
                               TextAlignment="Center" />
                </StackPanel>
            </Button>
            
            <!-- Reset/Clean License -->
            <Button Grid.Column="0" Grid.Row="1"
                    Margin="8" 
                    Height="120"
                    Command="{Binding CleanLicenseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="KeyRemove" 
                                             Height="48" 
                                             Width="48" 
                                             HorizontalAlignment="Center" />
                    <TextBlock Text="Clean License Files" 
                               HorizontalAlignment="Center" 
                               Margin="0,8,0,0" />
                </StackPanel>
            </Button>
            
            <!-- Test Licensing -->
            <Button Grid.Column="1" Grid.Row="1"
                    Margin="8" 
                    Height="120"
                    Command="{Binding TestLicensingCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="Tools" 
                                             Height="48" 
                                             Width="48" 
                                             HorizontalAlignment="Center" />
                    <TextBlock Text="Test License System" 
                               HorizontalAlignment="Center" 
                               Margin="0,8,0,0"
                               TextWrapping="Wrap"
                               TextAlignment="Center" />
                </StackPanel>
            </Button>
        </Grid>
        
        <materialDesign:Card Grid.Row="2" Margin="16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="LicenseStatusText" 
                               Text="{Binding LicenseStatus}" 
                               Style="{StaticResource MaterialDesignBody1TextBlock}"
                               TextWrapping="Wrap"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Content="Exit"
                        Command="{Binding ExitCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}" />
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
