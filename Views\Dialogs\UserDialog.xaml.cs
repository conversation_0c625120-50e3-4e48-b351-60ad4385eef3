using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Collections.ObjectModel;
using System.Linq;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Views.Dialogs
{
    public partial class UserDialog : UserControl, INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private readonly UserPermissionsService _permissionsService;
        private readonly bool _isEditMode;
        private readonly User _existingUser;
        private readonly string _dialogIdentifier;
        private bool _isUpdatingPermissionsFromRole = false; // Flag to prevent recursive updates
        private string _username;
        private string _firstName;
        private string _lastName;
        private string _email;
        private string _phone;
        private bool _isActive;
        private Role _selectedRole;
        private ObservableCollection<Role> _roles;
        private readonly List<Role> _rolesList;
        private readonly string _defaultPhotoPath = "/Resources/Images/default-user.png";
        private bool _isPhotoChanged = false;
        private string _selectedPhotoPath;

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        public string FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                OnPropertyChanged(nameof(FirstName));
            }
        }

        public string LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                OnPropertyChanged(nameof(LastName));
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                _email = value;
                OnPropertyChanged(nameof(Email));
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                _phone = value;
                OnPropertyChanged(nameof(Phone));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public Role SelectedRole
        {
            get => _selectedRole;
            set
            {
                _selectedRole = value;
                OnPropertyChanged(nameof(SelectedRole));
            }
        }

        public ObservableCollection<Role> Roles
        {
            get => _roles;
            set
            {
                _roles = value;
                OnPropertyChanged(nameof(Roles));
            }
        }

        public User Result { get; private set; }

        public UserDialog(User existingUser, List<Role> roles, string dialogIdentifier = "RootDialog")
        {
            try
            {
                InitializeComponent();

                if (roles == null || !roles.Any())
                {
                    throw new ArgumentException((string)Application.Current.Resources["NoRolesAvailable"]);
                }

                _dbService = new DatabaseService();
                _permissionsService = new UserPermissionsService(_dbService);
                _existingUser = existingUser;
                _rolesList = roles;
                _dialogIdentifier = dialogIdentifier;
                _isEditMode = existingUser != null;

                DataContext = this;

                System.Diagnostics.Debug.WriteLine($"Initializing UserDialog with {roles.Count} roles");
                foreach (var role in roles)
                {
                    System.Diagnostics.Debug.WriteLine($"Available role: {role.Name} (ID: {role.Id})");
                }

                // Note: ComboBox will be bound to Roles property via XAML binding
                // The ItemsSource will be set when Roles property is populated in Initialize methods

                if (_isEditMode)
                {
                    InitializeExistingUser();
                    DialogTitle.Text = (string)Application.Current.Resources["EditUser"];
                }
                else
                {
                    InitializeNewUser();
                }

                cmbRole.SelectionChanged += OnRoleSelectionChanged;

                // Subscribe to permission changes
                permissionsControl.PermissionChanged += OnPermissionChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UserDialog constructor: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        private void InitializeExistingUser()
        {
            Username = _existingUser.Username;
            FirstName = _existingUser.FirstName;
            LastName = _existingUser.LastName;
            Email = _existingUser.Email;
            Phone = _existingUser.Phone;
            IsActive = _existingUser.IsActive;
            
            // Add Custom role option and existing roles
            var rolesWithCustom = new List<Role>(_rolesList);
            rolesWithCustom.Add(CreateCustomRole());
            Roles = new ObservableCollection<Role>(rolesWithCustom);

            System.Diagnostics.Debug.WriteLine($"InitializeExistingUser: Added {Roles.Count} roles including Custom");
            foreach (var role in Roles)
            {
                System.Diagnostics.Debug.WriteLine($"  - {role.Name} (ID: {role.Id})");
            }

            SelectedRole = Roles.FirstOrDefault(r => r.Id == _existingUser.RoleId);

            // Load existing permissions
            var permissions = _dbService.GetUserPermissions(_existingUser.Id);
            if (permissions != null)
            {
                _isUpdatingPermissionsFromRole = true;
                permissionsControl.LoadPermissions(permissions);
                _isUpdatingPermissionsFromRole = false;

                // Check if permissions match the selected role's defaults
                if (SelectedRole != null && !IsCustomRole(SelectedRole))
                {
                    var roleDefaults = _permissionsService.CreateDefaultPermissions(0, SelectedRole.Id);
                    if (!PermissionsMatchDefaults(permissions, roleDefaults))
                    {
                        // Permissions don't match role defaults, set to Custom
                        SelectedRole = Roles.FirstOrDefault(r => IsCustomRole(r));
                    }
                }
            }

            btnSave.Content = (string)Application.Current.Resources["Update"];
        }

        private void InitializeNewUser()
        {
            try
            {
                Username = string.Empty;
                FirstName = string.Empty;
                LastName = string.Empty;
                Email = string.Empty;
                Phone = string.Empty;
                IsActive = true;

                // Get available roles from the database
                var availableRoles = _dbService.GetAllRoles().Where(r => r.IsActive).ToList();
                
                if (!availableRoles.Any())
                {
                    MessageBox.Show("No active roles available. Please configure roles first.", 
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    DialogHost.Close(_dialogIdentifier);
                    return;
                }

                System.Diagnostics.Debug.WriteLine("Available roles:");
                foreach (var role in availableRoles)
                {
                    System.Diagnostics.Debug.WriteLine($"- {role.Name} (ID: {role.Id}, Description: {role.Description})");
                }

                // Add Custom role option to available roles
                availableRoles.Add(CreateCustomRole());
                Roles = new ObservableCollection<Role>(availableRoles);

                System.Diagnostics.Debug.WriteLine($"InitializeNewUser: Added {Roles.Count} roles including Custom");
                foreach (var role in Roles)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {role.Name} (ID: {role.Id})");
                }

                // Set default role to Admin or first available role (excluding Custom)
                SelectedRole = Roles.FirstOrDefault(r => r.Name == "Admin") ??
                              Roles.FirstOrDefault(r => !IsCustomRole(r));
                
                if (SelectedRole == null)
                {
                    MessageBox.Show("No active roles available. Please configure roles first.", 
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    DialogHost.Close(_dialogIdentifier);
                    return;
                }

                btnSave.Content = (string)Application.Current.Resources["Save"];
                
                System.Diagnostics.Debug.WriteLine($"Initialized new user with role: {SelectedRole.Name} (ID: {SelectedRole.Id})");

                // Initialize permissions control with default permissions for the selected role
                UpdatePermissionsForRole(SelectedRole);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in InitializeNewUser: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                MessageBox.Show($"Error initializing new user: {ex.Message}", 
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                DialogHost.Close(_dialogIdentifier);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.Close(_dialogIdentifier);
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Save button clicked. Validating fields:");
                System.Diagnostics.Debug.WriteLine($"- Username: '{Username}'");
                System.Diagnostics.Debug.WriteLine($"- FirstName: '{FirstName}'");
                System.Diagnostics.Debug.WriteLine($"- LastName: '{LastName}'");
                System.Diagnostics.Debug.WriteLine($"- Role: {SelectedRole?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"- Password box is null: {passwordBox == null}");
                if (passwordBox != null)
                {
                    System.Diagnostics.Debug.WriteLine($"- Password length: {passwordBox.Password?.Length ?? 0}");
                }

                // Validate required fields
                if (string.IsNullOrWhiteSpace(Username))
                {
                    MessageBox.Show((string)Application.Current.Resources["UsernameRequired"], 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(FirstName))
                {
                    MessageBox.Show((string)Application.Current.Resources["FirstNameRequired"], 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(LastName))
                {
                    MessageBox.Show((string)Application.Current.Resources["LastNameRequired"], 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (SelectedRole == null)
                {
                    var availableRoles = _dbService.GetAllRoles().Where(r => r.IsActive).ToList();
                    var roleNames = string.Join(", ", availableRoles.Select(r => r.Name));
                    MessageBox.Show($"Please select a role. Available roles are: {roleNames}", 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Handle Custom role - need to assign a real database role
                Role validRole;
                if (IsCustomRole(SelectedRole))
                {
                    // For Custom role, we need to assign a default database role
                    // We'll use the first available role (typically Admin) but the permissions will be custom
                    var dbRoles = _dbService.GetAllRoles();
                    validRole = dbRoles.FirstOrDefault(r => r.IsActive);
                    if (validRole == null)
                    {
                        MessageBox.Show("No active roles available in the database. Please contact an administrator.",
                            "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                    System.Diagnostics.Debug.WriteLine($"Custom role selected - using database role: {validRole.Name} with custom permissions");
                }
                else
                {
                    // Validate that the selected role exists in the database
                    var dbRoles = _dbService.GetAllRoles();
                    validRole = dbRoles.FirstOrDefault(r => r.Id == SelectedRole.Id && r.IsActive);
                    if (validRole == null)
                    {
                        var roleNames = string.Join(", ", dbRoles.Where(r => r.IsActive).Select(r => r.Name));
                        MessageBox.Show(
                            $"Selected role '{SelectedRole.Name}' is not valid or no longer active.\n" +
                            $"Please select one of the following roles: {roleNames}",
                            "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                if (!_isEditMode && string.IsNullOrWhiteSpace(passwordBox?.Password))
                {
                    MessageBox.Show((string)Application.Current.Resources["PasswordRequired"], 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var user = new User
                {
                    Id = _existingUser?.Id ?? 0,
                    Username = Username,
                    FirstName = FirstName,
                    LastName = LastName,
                    Email = Email,
                    Phone = Phone,
                    RoleId = validRole.Id,
                    UserRole = validRole,
                    IsActive = IsActive,
                    PhotoPath = null // Use default photo
                };

                if (!_isEditMode)
                {
                    System.Diagnostics.Debug.WriteLine($"Creating new user with role: {validRole.Name} (ID: {validRole.Id})");

                    // Double check that the role exists and is active
                    var role = _dbService.GetAllRoles().FirstOrDefault(r => r.Id == validRole.Id && r.IsActive);
                    if (role == null)
                    {
                        MessageBox.Show($"The selected role (ID: {validRole.Id}) is no longer available. Please select a different role.",
                            "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // Check if username already exists
                    var existingUser = _dbService.GetAllUsers()
                        .FirstOrDefault(u => u.Username.Equals(Username, StringComparison.OrdinalIgnoreCase));
                    if (existingUser != null)
                    {
                        MessageBox.Show("This username is already taken. Please choose a different username.",
                            "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    try
                    {
                        // Set the password before adding the user
                        if (!string.IsNullOrWhiteSpace(passwordBox.Password))
                        {
                            user.Password = passwordBox.Password;
                        }
                        user.CreatedAt = DateTime.Now;
                        user.UpdatedAt = DateTime.Now;

                        // Store the role ID but detach the UserRole object to prevent EF from trying to insert it
                        var roleId = user.RoleId;
                        user.UserRole = null;  // Detach the role object
                        user.RoleId = roleId;  // Keep just the ID reference

                        // Get custom permissions from the control before creating user
                        var uiPermissions = permissionsControl.GetPermissions();

                        // CRITICAL FIX: Create a deep copy of the permissions to prevent modification during AddUser
                        var customPermissions = new UserPermissions
                        {
                            CanCreateSales = uiPermissions.CanCreateSales,
                            CanVoidSales = uiPermissions.CanVoidSales,
                            CanApplyDiscount = uiPermissions.CanApplyDiscount,
                            CanViewSalesHistory = uiPermissions.CanViewSalesHistory,
                            CanManageProducts = uiPermissions.CanManageProducts,
                            CanManageCategories = uiPermissions.CanManageCategories,
                            CanViewInventory = uiPermissions.CanViewInventory,
                            CanAdjustInventory = uiPermissions.CanAdjustInventory,
                            CanManageExpenses = uiPermissions.CanManageExpenses,
                            CanManageCashDrawer = uiPermissions.CanManageCashDrawer,
                            CanViewReports = uiPermissions.CanViewReports,
                            CanManagePrices = uiPermissions.CanManagePrices,
                            CanManageCustomers = uiPermissions.CanManageCustomers,
                            CanManageSuppliers = uiPermissions.CanManageSuppliers,
                            CanManageUsers = uiPermissions.CanManageUsers,
                            CanManageRoles = uiPermissions.CanManageRoles,
                            CanAccessSettings = uiPermissions.CanAccessSettings,
                            CanViewLogs = uiPermissions.CanViewLogs,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };

                        System.Diagnostics.Debug.WriteLine("Custom permissions from UI:");
                        System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {customPermissions.CanCreateSales}");
                        System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {customPermissions.CanVoidSales}");
                        System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {customPermissions.CanManageProducts}");
                        System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {customPermissions.CanManageUsers}");

                        // Log to file as well
                        LogToFile($"[USERDIALOG] Custom permissions from UI before AddUser (COPIED):");
                        LogToFile($"[USERDIALOG]   - CanManageUsers: {customPermissions.CanManageUsers}");
                        LogToFile($"[USERDIALOG]   - CanAccessSettings: {customPermissions.CanAccessSettings}");
                        LogToFile($"[USERDIALOG]   - CanManageProducts: {customPermissions.CanManageProducts}");
                        LogToFile($"[USERDIALOG]   - CanViewReports: {customPermissions.CanViewReports}");

                        // Add the user first (this will create default permissions based on role)
                        _dbService.AddUser(user);

                        System.Diagnostics.Debug.WriteLine($"User created successfully with ID: {user.Id}");

                        // Log permissions again after AddUser to see if they changed
                        LogToFile($"[USERDIALOG] Custom permissions after AddUser (before update):");
                        LogToFile($"[USERDIALOG]   - CanManageUsers: {customPermissions.CanManageUsers}");
                        LogToFile($"[USERDIALOG]   - CanAccessSettings: {customPermissions.CanAccessSettings}");
                        LogToFile($"[USERDIALOG]   - CanManageProducts: {customPermissions.CanManageProducts}");
                        LogToFile($"[USERDIALOG]   - CanViewReports: {customPermissions.CanViewReports}");

                        // Now update the permissions with custom values from the UI
                        // The AddUser method creates default permissions, so we need to update them
                        customPermissions.UserId = user.Id;
                        customPermissions.UpdatedAt = DateTime.Now;

                        // Update the permissions with custom values
                        _dbService.UpdateUserPermissions(customPermissions);

                        System.Diagnostics.Debug.WriteLine("User permissions updated with custom values successfully");

                        // Verify the permissions were saved correctly
                        var savedPermissions = _dbService.GetUserPermissions(user.Id);
                        if (savedPermissions != null)
                        {
                            System.Diagnostics.Debug.WriteLine("Verified saved permissions:");
                            System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {savedPermissions.CanCreateSales}");
                            System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {savedPermissions.CanVoidSales}");
                            System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {savedPermissions.CanManageProducts}");
                            System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {savedPermissions.CanManageUsers}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("WARNING: Could not retrieve saved permissions for verification");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error during user creation: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                        }
                        MessageBox.Show($"Error creating user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }
                else
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"Updating user with role: {validRole.Name} (ID: {validRole.Id})");
                        
                        if (!string.IsNullOrWhiteSpace(passwordBox.Password))
                        {
                            user.Password = passwordBox.Password;
                        }
                        user.UpdatedAt = DateTime.Now;
                        
                        // Store the role ID but detach the UserRole object to prevent EF from trying to insert it
                        var roleId = user.RoleId;
                        user.UserRole = null;  // Detach the role object
                        user.RoleId = roleId;  // Keep just the ID reference
                        
                        _dbService.UpdateUser(user);

                        var permissions = permissionsControl.GetPermissions();
                        permissions.UserId = user.Id;
                        permissions.UpdatedAt = DateTime.Now;

                        System.Diagnostics.Debug.WriteLine("Updating user permissions:");
                        System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {permissions.CanCreateSales}");
                        System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {permissions.CanVoidSales}");
                        System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {permissions.CanManageProducts}");
                        System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {permissions.CanManageUsers}");

                        _dbService.UpdateUserPermissions(permissions);

                        System.Diagnostics.Debug.WriteLine("User updated successfully");

                        // Verify the permissions were saved correctly
                        var savedPermissions = _dbService.GetUserPermissions(user.Id);
                        if (savedPermissions != null)
                        {
                            System.Diagnostics.Debug.WriteLine("Verified updated permissions:");
                            System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {savedPermissions.CanCreateSales}");
                            System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {savedPermissions.CanVoidSales}");
                            System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {savedPermissions.CanManageProducts}");
                            System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {savedPermissions.CanManageUsers}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("WARNING: Could not retrieve updated permissions for verification");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error during user update: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                        }
                        MessageBox.Show($"Error updating user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                // Reload the user with role information before returning
                if (user.Id > 0)
                {
                    var refreshedUser = _dbService.GetUserById(user.Id);
                    if (refreshedUser != null)
                    {
                        user = refreshedUser;
                    }
                }

                Result = user;
                DialogHost.Close(_dialogIdentifier, Result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in BtnSave_Click: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnUploadPhoto_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = (string)Application.Current.Resources["ImageFilesFilter"],
                Title = (string)Application.Current.Resources["SelectUserPhoto"]
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    string photoPath = openFileDialog.FileName;
                    BitmapImage image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.CreateOptions = BitmapCreateOptions.IgnoreImageCache;
                    image.UriSource = new Uri(photoPath);
                    image.EndInit();
                    imgUserPhoto.Source = image;
                    
                    _selectedPhotoPath = photoPath;
                    _isPhotoChanged = true;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        string.Format((string)Application.Current.Resources["ErrorLoadingImage"], ex.Message),
                        (string)Application.Current.Resources["Error"],
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnRemovePhoto_Click(object sender, RoutedEventArgs e)
        {
            // Reset to default photo
            imgUserPhoto.Source = new BitmapImage(new Uri(_defaultPhotoPath, UriKind.Relative));
            _selectedPhotoPath = null;
            _isPhotoChanged = true;
        }

        /// <summary>
        /// Handles role selection changes and updates permission checkboxes with role defaults
        /// </summary>
        private void OnRoleSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbRole.SelectedItem is Role selectedRole)
                {
                    System.Diagnostics.Debug.WriteLine($"Role selection changed to: {selectedRole.Name} (ID: {selectedRole.Id})");
                    SelectedRole = selectedRole;

                    // Update permissions control with default permissions for the selected role
                    UpdatePermissionsForRole(selectedRole);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnRoleSelectionChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the permissions control to show default permissions for the specified role
        /// </summary>
        private void UpdatePermissionsForRole(Role role, bool forceUpdate = false)
        {
            try
            {
                if (role == null || permissionsControl == null)
                    return;

                // Don't update permissions for Custom role - it has no defaults
                if (IsCustomRole(role))
                {
                    System.Diagnostics.Debug.WriteLine("Custom role selected - not updating permissions");
                    return;
                }

                // For edit mode, only update permissions if this is the initial load
                // or if the user explicitly wants to reset to role defaults
                if (_isEditMode && _existingUser != null && !forceUpdate)
                {
                    // In edit mode, we don't automatically override existing custom permissions
                    // The user can manually adjust them if needed
                    System.Diagnostics.Debug.WriteLine($"Edit mode: Not automatically updating permissions for role {role.Name}");
                    return;
                }

                // Set flag to prevent permission change detection during role-based updates
                _isUpdatingPermissionsFromRole = true;

                // Create default permissions for the selected role
                // Use a temporary user ID of 0 since this is just for display purposes
                var defaultPermissions = _permissionsService.CreateDefaultPermissions(0, role.Id);

                System.Diagnostics.Debug.WriteLine($"Loading default permissions for role: {role.Name}");
                System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {defaultPermissions.CanCreateSales}");
                System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {defaultPermissions.CanVoidSales}");
                System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {defaultPermissions.CanManageProducts}");
                System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {defaultPermissions.CanManageUsers}");

                // Load the default permissions into the permissions control
                permissionsControl.LoadPermissions(defaultPermissions);

                // Clear the flag after updating
                _isUpdatingPermissionsFromRole = false;

                if (forceUpdate)
                {
                    System.Diagnostics.Debug.WriteLine($"Permissions reset to role defaults for: {role.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating permissions for role {role?.Name}: {ex.Message}");
                _isUpdatingPermissionsFromRole = false; // Ensure flag is cleared on error
            }
        }

        /// <summary>
        /// Resets permissions to the default values for the currently selected role
        /// This can be called from a button or menu item to allow users to reset permissions
        /// </summary>
        public void ResetPermissionsToRoleDefaults()
        {
            if (SelectedRole != null && !IsCustomRole(SelectedRole))
            {
                var result = MessageBox.Show(
                    $"This will reset all permissions to the default values for the '{SelectedRole.Name}' role. Any custom permission changes will be lost.\n\nAre you sure you want to continue?",
                    "Reset Permissions",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdatePermissionsForRole(SelectedRole, forceUpdate: true);
                }
            }
        }

        /// <summary>
        /// Creates a Custom role object for representing user-defined permission combinations
        /// </summary>
        private Role CreateCustomRole()
        {
            return new Role
            {
                Id = -1, // Use negative ID to distinguish from database roles
                Name = "Custom",
                Description = "User-defined permission combination",
                IsActive = true,
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// Checks if the given role is the Custom role
        /// </summary>
        private bool IsCustomRole(Role role)
        {
            return role != null && (role.Id == -1 || role.Name == "Custom");
        }

        /// <summary>
        /// Compares current permissions with role defaults to determine if they match
        /// </summary>
        private bool PermissionsMatchDefaults(UserPermissions current, UserPermissions defaults)
        {
            if (current == null || defaults == null)
                return false;

            return current.CanCreateSales == defaults.CanCreateSales &&
                   current.CanVoidSales == defaults.CanVoidSales &&
                   current.CanApplyDiscount == defaults.CanApplyDiscount &&
                   current.CanViewSalesHistory == defaults.CanViewSalesHistory &&
                   current.CanManageProducts == defaults.CanManageProducts &&
                   current.CanManageCategories == defaults.CanManageCategories &&
                   current.CanViewInventory == defaults.CanViewInventory &&
                   current.CanAdjustInventory == defaults.CanAdjustInventory &&
                   current.CanManageExpenses == defaults.CanManageExpenses &&
                   current.CanManageCashDrawer == defaults.CanManageCashDrawer &&
                   current.CanViewReports == defaults.CanViewReports &&
                   current.CanManagePrices == defaults.CanManagePrices &&
                   current.CanManageCustomers == defaults.CanManageCustomers &&
                   current.CanManageSuppliers == defaults.CanManageSuppliers &&
                   current.CanManageUsers == defaults.CanManageUsers &&
                   current.CanManageRoles == defaults.CanManageRoles &&
                   current.CanAccessSettings == defaults.CanAccessSettings &&
                   current.CanViewLogs == defaults.CanViewLogs;
        }

        /// <summary>
        /// Handles permission changes and automatically switches to Custom role if needed
        /// </summary>
        public void OnPermissionChanged()
        {
            // Don't process permission changes if we're updating from role selection
            if (_isUpdatingPermissionsFromRole)
                return;

            // Don't process if already on Custom role
            if (SelectedRole != null && IsCustomRole(SelectedRole))
                return;

            // Check if current permissions match the selected role's defaults
            if (SelectedRole != null && !IsCustomRole(SelectedRole))
            {
                var currentPermissions = permissionsControl.GetPermissions();
                var roleDefaults = _permissionsService.CreateDefaultPermissions(0, SelectedRole.Id);

                if (!PermissionsMatchDefaults(currentPermissions, roleDefaults))
                {
                    System.Diagnostics.Debug.WriteLine($"Permissions modified from {SelectedRole.Name} defaults - switching to Custom role");

                    // Switch to Custom role to indicate permissions have been modified
                    var customRole = Roles.FirstOrDefault(r => IsCustomRole(r));
                    if (customRole != null)
                    {
                        _isUpdatingPermissionsFromRole = true; // Prevent recursive updates
                        SelectedRole = customRole;
                        _isUpdatingPermissionsFromRole = false;
                    }
                }
            }
        }

        private static void LogToFile(string message)
        {
            try
            {
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "permissions_debug.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}