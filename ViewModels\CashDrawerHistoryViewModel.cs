using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using POSSystem.Models;
using POSSystem.Services;
using System.Linq;
using Microsoft.Win32;
using ClosedXML.Excel;

namespace POSSystem.ViewModels
{
    public class CashDrawerHistoryViewModel : INotifyPropertyChanged
    {
        private readonly CashDrawerService _cashDrawerService;
        private ObservableCollection<CashDrawer> _cashDrawers;

        public CashDrawerHistoryViewModel()
        {
            _cashDrawerService = new CashDrawerService(new DatabaseService());
            CashDrawers = new ObservableCollection<CashDrawer>();
            LoadCashDrawers();
        }

        public ObservableCollection<CashDrawer> CashDrawers
        {
            get => _cashDrawers;
            set
            {
                _cashDrawers = value;
                OnPropertyChanged();
            }
        }

        public void LoadCashDrawers(DateTime? startDate = null, DateTime? endDate = null)
        {
            var drawers = _cashDrawerService.GetAllCashDrawers(startDate, endDate);
            CashDrawers = new ObservableCollection<CashDrawer>(drawers);
        }

        public void ExportToExcel()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    DefaultExt = ".xlsx",
                    FileName = $"CashDrawerHistory_{DateTime.Now:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    using var workbook = new XLWorkbook();
                    var worksheet = workbook.Worksheets.Add("Cash Drawer History");

                    // Add headers
                    worksheet.Cell(1, 1).Value = "Opened At";
                    worksheet.Cell(1, 2).Value = "Opened By";
                    worksheet.Cell(1, 3).Value = "Opening Balance";
                    worksheet.Cell(1, 4).Value = "Status";
                    worksheet.Cell(1, 5).Value = "Total Sales";
                    worksheet.Cell(1, 6).Value = "Total Payouts";
                    worksheet.Cell(1, 7).Value = "Expected Balance";
                    worksheet.Cell(1, 8).Value = "Actual Balance";
                    worksheet.Cell(1, 9).Value = "Difference";
                    worksheet.Cell(1, 10).Value = "Closed At";
                    worksheet.Cell(1, 11).Value = "Closed By";

                    // Add data
                    int row = 2;
                    foreach (var drawer in CashDrawers)
                    {
                        worksheet.Cell(row, 1).Value = drawer.OpenedAt;
                        worksheet.Cell(row, 2).Value = drawer.OpenedBy?.Username;
                        worksheet.Cell(row, 3).Value = drawer.OpeningBalance;
                        worksheet.Cell(row, 4).Value = drawer.Status;
                        worksheet.Cell(row, 5).Value = _cashDrawerService.GetTotalCashSales(drawer);
                        worksheet.Cell(row, 6).Value = _cashDrawerService.GetTotalPayouts(drawer);
                        worksheet.Cell(row, 7).Value = drawer.ExpectedBalance;
                        worksheet.Cell(row, 8).Value = drawer.ActualBalance;
                        worksheet.Cell(row, 9).Value = drawer.Difference;
                        worksheet.Cell(row, 10).Value = drawer.ClosedAt;
                        worksheet.Cell(row, 11).Value = drawer.ClosedBy?.Username;
                        row++;
                    }

                    // Format the worksheet
                    var range = worksheet.Range(1, 1, row - 1, 11);
                    range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                    worksheet.Columns().AdjustToContents();

                    // Save the workbook
                    workbook.SaveAs(saveDialog.FileName);
                    System.Windows.MessageBox.Show("Export completed successfully!", "Success",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting to Excel: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 