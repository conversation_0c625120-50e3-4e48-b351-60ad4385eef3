using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Media;
using POSSystem.ViewModels;
using System.Windows;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Manages chart parameters and configurations.
    /// Extracted from DashboardViewModel to improve code organization and maintainability.
    /// </summary>
    public class ChartParameterManager : IChartParameterManager
    {
        private ObservableCollection<MetricType> _metricTypes;
        private ObservableCollection<ChartParameter> _chartParameters;
        private ObservableCollection<TimePeriod> _timePeriods;
        private ObservableCollection<ProductMetric> _productMetrics;
        
        public ChartParameterManager()
        {
            _metricTypes = new ObservableCollection<MetricType>();
            _chartParameters = new ObservableCollection<ChartParameter>();
            _timePeriods = new ObservableCollection<TimePeriod>();
            InitializeProductMetrics();
        }
        
        /// <summary>
        /// Gets the collection of chart parameters
        /// </summary>
        public ObservableCollection<ChartParameter> ChartParameters
        {
            get => _chartParameters;
            set => _chartParameters = value;
        }
        
        /// <summary>
        /// Gets the collection of time periods
        /// </summary>
        public ObservableCollection<TimePeriod> TimePeriods
        {
            get => _timePeriods;
            set => _timePeriods = value;
        }
        
        /// <summary>
        /// Gets the collection of metric types
        /// </summary>
        public ObservableCollection<MetricType> MetricTypes
        {
            get => _metricTypes;
            set => _metricTypes = value;
        }
        
        /// <summary>
        /// Gets the collection of product metrics
        /// </summary>
        public ObservableCollection<ProductMetric> ProductMetrics => _productMetrics;
        
        /// <summary>
        /// Gets a default chart parameter
        /// </summary>
        public ChartParameter DefaultChartParameter => new ChartParameter 
        { 
            Name = "Sales", 
            Key = "sales", 
            Description = "Total sales amount",
            Color = Colors.Blue,
            SecondaryMetric = "growth"
        };
        
        /// <summary>
        /// Gets a default time period
        /// </summary>
        public TimePeriod DefaultTimePeriod => new TimePeriod 
        { 
            Type = TimePeriodType.Today, 
            DisplayName = "Today" 
        };
        
        /// <summary>
        /// Gets a default metric type
        /// </summary>
        public MetricType DefaultMetricType => new MetricType 
        { 
            DisplayName = "Sales", 
            Key = "sales", 
            IsCurrency = true 
        };
        
        /// <summary>
        /// Gets a default product metric
        /// </summary>
        public ProductMetric DefaultProductMetric => _productMetrics.FirstOrDefault(p => p.Key == "revenue");
        
        /// <summary>
        /// Gets the appropriate date range based on a time period
        /// </summary>
        public (DateTime start, DateTime end) GetDateRange(TimePeriod period)
        {
            var now = DateTime.Now;
            var today = now.Date;

            if (period == null)
            {
                // Default to last 30 days if no period specified
                return (today.AddDays(-30), now);
            }

            switch (period.Type)
            {
                case TimePeriodType.Today:
                    return (today, now);
                    
                case TimePeriodType.Yesterday:
                    var yesterday = today.AddDays(-1);
                    return (yesterday, yesterday.AddDays(1).AddSeconds(-1));
                    
                case TimePeriodType.Week:
                    // Last 7 days
                    return (today.AddDays(-7), now);
                    
                case TimePeriodType.Month:
                    if (period.IsCurrentMonth)
                    {
                        // Current month
                        var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfMonth, now);
                    }
                    else if (period.IsLastMonth)
                    {
                        // Last month
                        var firstDayOfLastMonth = new DateTime(today.Year, today.Month, 1).AddMonths(-1);
                        var firstDayOfThisMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfLastMonth, firstDayOfThisMonth.AddSeconds(-1));
                    }
                    else if (period.Days > 0)
                    {
                        // Last N days
                        return (today.AddDays(-period.Days), now);
                    }
                    else
                    {
                        // Default to current month if no specific configuration is set
                        var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfMonth, now);
                    }
                    
                case TimePeriodType.Year:
                    // Last 365 days
                    return (today.AddDays(-365), now);
                    
                case TimePeriodType.Custom:
                    // Use the specified days
                    return (today.AddDays(-period.Days), now);
                    
                default:
                    return (today.AddDays(-period.Days), now);
            }
        }
        
        /// <summary>
        /// Gets the previous period range based on the current period
        /// </summary>
        public (DateTime start, DateTime end) GetPreviousPeriodRange(DateTime start, DateTime end)
        {
            TimeSpan span = end - start;
            return (start.AddDays(-span.TotalDays), start.AddSeconds(-1));
        }
        
        /// <summary>
        /// Formats a value based on a chart parameter
        /// </summary>
        public string FormatValueByParameter(double value, string parameter)
        {
            // Format based on parameter type
            switch (parameter?.ToLower())
            {
                case "sales":
                case "revenue":
                case "profit":
                case "grossprofit":
                    return $"{value:N2} DA";
                case "margin":
                    return $"{value:N2}%";
                case "items":
                    return value.ToString("0.###"); // ✅ WEIGHT-BASED FIX: Show decimal quantities instead of rounding to whole numbers
                case "orders":
                    return value.ToString("N0");
                default:
                    return value.ToString("N2");
            }
        }
        
        /// <summary>
        /// Initializes the product metrics
        /// </summary>
        private void InitializeProductMetrics()
        {
            _productMetrics = new ObservableCollection<ProductMetric>
            {
                new ProductMetric
                {
                    Key = "revenue",
                    DisplayName = "Revenue",
                    Description = "Total sales revenue",
                    Color = Colors.Green,
                    ValueFormatter = (value) => $"{value:N2} DA",
                    ValueSelector = (p) => p.Revenue
                },
                new ProductMetric
                {
                    Key = "profit",
                    DisplayName = "Profit",
                    Description = "Gross profit",
                    Color = Colors.Blue,
                    ValueFormatter = (value) => $"{value:N2} DA",
                    ValueSelector = (p) => p.Profit
                },
                new ProductMetric
                {
                    Key = "margin",
                    DisplayName = "Margin %",
                    Description = "Profit margin percentage",
                    Color = Colors.Purple,
                    ValueFormatter = (value) => $"{value:N2}%",
                    ValueSelector = (p) => p.Margin
                },
                new ProductMetric
                {
                    Key = "items",
                    DisplayName = "Items Sold",
                    Description = "Number of items sold",
                    Color = Colors.Orange,
                    ValueFormatter = (value) => value.ToString("N0"),
                    ValueSelector = (p) => p.ItemsSold
                }
            };
        }
    }
} 