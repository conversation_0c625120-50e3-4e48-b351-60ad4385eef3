<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.AccountsPayableView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource MaterialDesignBackground}">

    <UserControl.Resources>
        <!-- DataGrid Column Header Style -->
        <Style x:Key="CustomDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,12"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,4"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="4"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                  Text="Accounts Payable Management"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"/>

        <!-- Stats Cards -->
        <WrapPanel Grid.Row="1" Margin="0,0,0,20">
            <md:Card Background="White" 
                    UniformCornerRadius="8"
                    Margin="0,0,16,0" 
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="16">
                    <TextBlock Text="Unpaid Purchases" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <md:PackIcon Kind="CashRemove" 
                                    Width="24" Height="24"
                                    Foreground="#FFA726"/>
                        <TextBlock Text="{Binding UnpaidPurchaseOrdersCount}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="#FFA726"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>

            <md:Card Background="White" 
                    UniformCornerRadius="8"
                    Margin="0,0,16,0" 
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="16">
                    <TextBlock Text="Total Amount Due" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <md:PackIcon Kind="CurrencyUsd" 
                                    Width="24" Height="24"
                                    Foreground="#FF7043"/>
                        <TextBlock Text="{Binding TotalAmountDue, StringFormat=C2}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="#FF7043"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>

            <md:Card Background="White" 
                    UniformCornerRadius="8"
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="16">
                    <TextBlock Text="Due This Week" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <md:PackIcon Kind="CalendarClock" 
                                    Width="24" Height="24"
                                    Foreground="#5C6BC0"/>
                        <TextBlock Text="{Binding DueThisWeek}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="#5C6BC0"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>
        </WrapPanel>

        <!-- Filter Options -->
        <md:Card Grid.Row="2" 
                Background="White"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" Margin="20,15">
                <ComboBox Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Width="150" 
                         Margin="0,0,16,0"
                         md:HintAssist.Hint="{DynamicResource Status}"
                         SelectedIndex="0"
                         SelectionChanged="StatusFilter_Changed">
                    <ComboBoxItem Content="{DynamicResource AllOrders}"/>
                    <ComboBoxItem Content="{DynamicResource PendingPayment}"/>
                    <ComboBoxItem Content="{DynamicResource PaymentDue}"/>
                    <ComboBoxItem Content="{DynamicResource Overdue}"/>
                    <ComboBoxItem Content="{DynamicResource ProcessingPayment}"/>
                </ComboBox>

                <ComboBox Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Width="200"
                         Margin="0,0,16,0"
                         md:HintAssist.Hint="Supplier"
                         ItemsSource="{Binding Suppliers}"
                         DisplayMemberPath="Name"
                         SelectedValuePath="Id"
                         SelectionChanged="SupplierFilter_Changed"/>

                <DatePicker Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Width="150"
                           Margin="0,0,16,0"
                           md:HintAssist.Hint="Start Date"
                           SelectedDateChanged="StartDate_Changed"/>

                <DatePicker Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Width="150"
                           md:HintAssist.Hint="End Date"
                           SelectedDateChanged="EndDate_Changed"/>
            </StackPanel>
        </md:Card>

        <!-- Purchase Orders List -->
        <md:Card Grid.Row="3" 
                Background="White"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1">
            <DockPanel Margin="20">
                <TextBlock DockPanel.Dock="Top" 
                         Text="Purchase Orders List" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,15"/>
                
                <DataGrid ItemsSource="{Binding UnpaidPurchaseOrders}"
                         Style="{StaticResource MaterialDesignDataGrid}"
                         AutoGenerateColumns="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         BorderThickness="0"
                         Background="Transparent">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource CustomDataGridColumnHeaderStyle}"/>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Order #" 
                                          Binding="{Binding OrderNumber}"
                                          Width="120"/>
                        <DataGridTextColumn Header="Date" 
                                          Binding="{Binding OrderDate, StringFormat=\{0:MM/dd/yyyy\}}"
                                          Width="100"/>
                        <DataGridTextColumn Header="Due Date" 
                                          Binding="{Binding DueDate, StringFormat=\{0:MM/dd/yyyy\}}"
                                          Width="100"/>
                        <DataGridTextColumn Header="Supplier" 
                                          Binding="{Binding Supplier.CompanyName}"
                                          Width="200"/>
                        <DataGridTextColumn Header="Items" 
                                          Width="80">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Items.Count" StringFormat="{}{0} items"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Amount" 
                                          Binding="{Binding GrandTotal, StringFormat={}{0:N2} DA}"
                                          Width="120"/>
                        <DataGridTextColumn Header="Status" 
                                          Binding="{Binding Status}"
                                          Width="100"/>
                        <DataGridTemplateColumn Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="View Details"
                                                Click="ViewDetails_Click"
                                                Style="{StaticResource ActionButton}"/>
                                        <Button Content="Process Payment"
                                                Click="ProcessPayment_Click"
                                                Style="{StaticResource ActionButton}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </md:Card>

        <!-- Payment Details Popup -->
        <Popup x:Name="paymentDetailsPopup" 
               PlacementTarget="{Binding ElementName=mainGrid}" 
               Placement="Center"
               AllowsTransparency="True"
               PopupAnimation="Fade"
               IsOpen="False">
            <Grid>
                <Rectangle Fill="Black" Opacity="0.5" 
                         Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         MouseDown="ClosePopupOnOutsideClick"/>
                <md:Card Background="White"
                        UniformCornerRadius="8"
                        md:ElevationAssist.Elevation="Dp3"
                        Width="400"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Margin="20">
                    <StackPanel Margin="24">
                        <TextBlock Text="Process Payment"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Margin="0,0,0,20"/>
                        
                        <ComboBox x:Name="cmbPaymentMethod"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 md:HintAssist.Hint="Payment Method"
                                 Margin="0,0,0,16">
                            <ComboBoxItem Content="Bank Transfer"/>
                            <ComboBoxItem Content="Check"/>
                            <ComboBoxItem Content="Cash"/>
                        </ComboBox>
                        
                        <TextBox x:Name="txtReferenceNumber"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                md:HintAssist.Hint="Reference Number"
                                Margin="0,0,0,16"/>
                        
                        <TextBox x:Name="txtNotes"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                md:HintAssist.Hint="Notes"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"
                                Margin="0,0,0,24"/>
                        
                        <StackPanel Orientation="Horizontal" 
                                  HorizontalAlignment="Right">
                            <Button Content="Cancel"
                                    Click="CancelPayment_Click"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="0,0,8,0"/>
                            <Button Content="Process"
                                    Click="ConfirmPayment_Click"
                                    Style="{StaticResource MaterialDesignRaisedButton}"/>
                        </StackPanel>
                    </StackPanel>
                </md:Card>
            </Grid>
        </Popup>
    </Grid>
</UserControl> 