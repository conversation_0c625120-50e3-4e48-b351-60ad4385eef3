using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Models.DTOs;
using POSSystem.Services;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using md = MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Dialog for creating and editing pricing tiers for bulk/pack pricing functionality.
    /// Provides a professional interface for configuring quantity-based pricing.
    /// </summary>
    public partial class PriceTierDialog : UserControl
    {
        private readonly BulkPricingService _bulkPricingService;
        private Product _product;
        private PriceTierDto _priceTier;
        private bool _isEditMode;
        private string _dialogIdentifier = "RootDialog";

        public PriceTierDto Result { get; private set; }

        // Events for window-based dialog handling
        public event Action<PriceTierDto> SaveClicked;
        public event Action CancelClicked;

        public PriceTierDialog(Product product, PriceTierDto priceTier = null)
        {
            InitializeComponent();

            // Set FlowDirection based on current language for RTL support
            string language = ConfigurationManager.AppSettings["Language"] ?? "en";
            this.FlowDirection = language == "ar" ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;

            _bulkPricingService = new BulkPricingService();
            _product = product ?? throw new ArgumentNullException(nameof(product));
            _isEditMode = priceTier != null;

            if (_isEditMode)
            {
                _priceTier = priceTier;
                DialogTitle.Text = "Edit Bulk Discount";

                // Set pack pricing checkbox based on existing data
                chkPackPricing.IsChecked = _priceTier.IsPackPricing;
            }
            else
            {
                _priceTier = PriceTierDto.CreateNew(product.Id);
                // Set better defaults for new tiers
                _priceTier.MinimumQuantity = 5m; // Default to 5 items minimum
                _priceTier.UnitPrice = Math.Round((product?.SellingPrice ?? 0m) * 0.9m, 2); // Default 10% discount
                _priceTier.IsActive = true; // Default to active
                DialogTitle.Text = "Bulk Discount";

                // Default to unit pricing
                chkPackPricing.IsChecked = false;
            }

            DataContext = _priceTier;

            // Subscribe to text changed events for real-time savings preview
            txtMinimumQuantity.TextChanged += (s, e) => UpdateSavingsPreview();
            txtUnitPrice.TextChanged += (s, e) => UpdateSavingsPreview();
            txtPackPrice.TextChanged += (s, e) => UpdateSavingsPreview();

            // Initial UI state update
            UpdatePackPricingUI();
            UpdateSavingsPreview();
        }

        private void PackPricing_Changed(object sender, RoutedEventArgs e)
        {
            UpdatePackPricingUI();
            UpdateSavingsPreview();
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ValidateDateRange();
        }

        private void DecimalValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            var textBox = sender as TextBox;
            var fullText = textBox.Text.Insert(textBox.SelectionStart, e.Text);

            // Allow decimal numbers with up to 3 decimal places for quantities, 2 for prices
            var isQuantityField = textBox.Name.Contains("Quantity");
            var pattern = isQuantityField ? @"^\d*\.?\d{0,3}$" : @"^\d*\.?\d{0,2}$";

            e.Handled = !Regex.IsMatch(fullText, pattern);
        }

        private void UpdatePackPricingUI()
        {
            bool isPackPricing = chkPackPricing.IsChecked == true;

            if (isPackPricing)
            {
                // Pack pricing mode
                lblUnitPrice.Text = "Calculated Price Per Item";
                txtUnitPrice.IsReadOnly = true;
                txtUnitPrice.Opacity = 0.7;
                txtCalculatedUnitPrice.Visibility = Visibility.Visible;

                // Calculate unit price from pack price
                if (decimal.TryParse(txtPackPrice.Text, out decimal packPrice) &&
                    decimal.TryParse(txtMinimumQuantity.Text, out decimal minQty) && minQty > 0)
                {
                    decimal calculatedUnitPrice = packPrice / minQty;
                    txtUnitPrice.Text = calculatedUnitPrice.ToString("F2");
                    txtCalculatedUnitPrice.Text = $"= {FormatCurrency(calculatedUnitPrice)} per item";
                }
            }
            else
            {
                // Unit pricing mode
                lblUnitPrice.Text = "Discounted Price Per Item *";
                txtUnitPrice.IsReadOnly = false;
                txtUnitPrice.Opacity = 1.0;
                txtCalculatedUnitPrice.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Updates the savings preview text to show customers how much they'll save
        /// </summary>
        private void UpdateSavingsPreview()
        {
            try
            {
                if (txtSavingsPreview == null || _product == null)
                    return;

                bool isPackPricing = chkPackPricing.IsChecked == true;
                decimal effectiveUnitPrice = 0m;
                decimal minQty = 0m;

                // Parse minimum quantity
                if (!decimal.TryParse(txtMinimumQuantity.Text, out minQty) || minQty <= 0)
                {
                    txtSavingsPreview.Text = "Enter valid quantity to see savings";
                    txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Gray);
                    return;
                }

                if (isPackPricing)
                {
                    // Pack pricing mode - calculate unit price from pack price
                    if (decimal.TryParse(txtPackPrice.Text, out decimal packPrice) && packPrice > 0)
                    {
                        effectiveUnitPrice = packPrice / minQty;
                        // Update the calculated unit price display
                        txtUnitPrice.Text = effectiveUnitPrice.ToString("F2");
                        UpdatePackPricingUI(); // Update the calculated display
                    }
                    else
                    {
                        txtSavingsPreview.Text = "Enter valid pack price to see savings";
                        txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Gray);
                        return;
                    }
                }
                else
                {
                    // Unit pricing mode
                    if (!decimal.TryParse(txtUnitPrice.Text, out effectiveUnitPrice) || effectiveUnitPrice <= 0)
                    {
                        txtSavingsPreview.Text = "Enter valid unit price to see savings";
                        txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Gray);
                        return;
                    }
                }

                // Calculate savings
                var regularPrice = _product.SellingPrice;
                var savings = regularPrice - effectiveUnitPrice;
                var savingsPercentage = regularPrice > 0 ? (savings / regularPrice) * 100 : 0;

                if (savings > 0)
                {
                    string savingsText = isPackPricing
                        ? $"Customers save {FormatCurrency(savings)} per item ({savingsPercentage:F0}% off) • Pack: {FormatCurrency(effectiveUnitPrice * minQty)}"
                        : $"Customers save {FormatCurrency(savings)} per item ({savingsPercentage:F0}% off)";

                    txtSavingsPreview.Text = savingsText;
                    txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Green);
                }
                else if (savings < 0)
                {
                    txtSavingsPreview.Text = $"Price is {FormatCurrency(Math.Abs(savings))} higher than regular price";
                    txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Red);
                }
                else
                {
                    txtSavingsPreview.Text = "Same as regular price - no discount";
                    txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Orange);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Error updating savings preview: {ex.Message}");
                txtSavingsPreview.Text = "Unable to calculate savings";
                txtSavingsPreview.Foreground = new SolidColorBrush(Colors.Gray);
            }
        }

        /// <summary>
        /// Formats currency using the application's currency system
        /// </summary>
        private string FormatCurrency(decimal amount)
        {
            try
            {
                // Use the application's currency format from resources
                var currencyFormat = Application.Current.Resources["CurrencyFormat"] as string;
                if (!string.IsNullOrEmpty(currencyFormat))
                {
                    return string.Format(currencyFormat, amount);
                }

                // Fallback to currency symbol
                var currencySymbol = Application.Current.Resources["CurrencySymbol"] as string ?? "DA";
                return $"{amount:N2} {currencySymbol}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Error formatting currency: {ex.Message}");
                return $"{amount:N2} DA"; // Final fallback
            }
        }

        /// <summary>
        /// Validates that expiration date is after effective date
        /// </summary>
        private void ValidateDateRange()
        {
            if (dpEffectiveDate.SelectedDate.HasValue && dpExpirationDate.SelectedDate.HasValue)
            {
                if (dpExpirationDate.SelectedDate.Value <= dpEffectiveDate.SelectedDate.Value)
                {
                    // Show warning but don't prevent saving - let validation handle it
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Warning: Expiration date should be after effective date");
                }
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Save_Click called");

                if (ValidateInput())
                {
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Validation passed");

                    // Update the DTO with current values
                    UpdateDtoFromUI();

                    // Validate business rules
                    var entity = _priceTier.ToEntity();
                    entity.Product = _product; // Set for validation

                    var validationResult = _bulkPricingService.ValidatePriceTier(entity, _product);

                    if (!validationResult.IsValid)
                    {
                        var errorMessage = string.Join("\n", validationResult.Errors);
                        MessageBox.Show(errorMessage, "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    if (validationResult.HasWarnings)
                    {
                        var warningMessage = string.Join("\n", validationResult.Warnings);
                        var result = MessageBox.Show(
                            $"{warningMessage}\n\nDo you want to continue?",
                            "Warning",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result != MessageBoxResult.Yes)
                            return;
                    }

                    Result = _priceTier;
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Result set: TierName='{_priceTier.TierName}', MinQty={_priceTier.MinimumQuantity}, UnitPrice={_priceTier.UnitPrice}");
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Result set, attempting to close dialog");

                    // Try DialogHost first, then fallback to event
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Trying DialogHost.CloseDialogCommand with parameter: true");

                        // Check if we can find the DialogHost
                        var dialogHost = DialogHost.GetDialogSession(this);
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] DialogHost session found: {dialogHost != null}");

                        // Try multiple methods to close the dialog
                        try
                        {
                            // Method 1: Use CloseDialogCommand with true parameter
                            DialogHost.CloseDialogCommand.Execute(true, this);
                            System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Method 1: CloseDialogCommand executed");
                        }
                        catch (Exception cmdEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 1 failed: {cmdEx.Message}");
                        }

                        // Add a small delay to see if dialog closes
                        await System.Threading.Tasks.Task.Delay(100);

                        var dialogHostAfter = DialogHost.GetDialogSession(this);
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] DialogHost session after close attempt: {dialogHostAfter != null}");

                        // If dialog is still open, try alternative methods
                        if (dialogHostAfter != null)
                        {
                            System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Dialog still open, trying alternative close methods");

                            try
                            {
                                // Method 2: Use DialogHost.Close with identifier
                                DialogHost.Close(_dialogIdentifier, true);
                                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Method 2: DialogHost.Close with identifier executed");
                                await System.Threading.Tasks.Task.Delay(100);
                            }
                            catch (Exception closeEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 2 failed: {closeEx.Message}");
                            }

                            // Check again
                            var dialogHostAfter2 = DialogHost.GetDialogSession(this);
                            if (dialogHostAfter2 != null)
                            {
                                try
                                {
                                    // Method 3: Use CloseDialogCommand without parameter
                                    DialogHost.CloseDialogCommand.Execute(null, this);
                                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Method 3: CloseDialogCommand without parameter executed");
                                }
                                catch (Exception cmd2Ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 3 failed: {cmd2Ex.Message}");
                                }
                            }
                        }

                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] DialogHost close attempts completed");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] DialogHost failed: {ex.Message}, trying event");
                        // If DialogHost fails, trigger the event for window-based dialog
                        SaveClicked?.Invoke(_priceTier);
                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] SaveClicked event invoked");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Validation failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Save_Click exception: {ex.Message}");
                MessageBox.Show($"Error saving pricing tier: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Cancel_Click called");
                Result = null;

                // Force close the dialog using the most reliable method
                try
                {
                    // Check if we have a dialog session
                    var dialogSession = DialogHost.GetDialogSession(this);
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Dialog session found: {dialogSession != null}");

                    if (dialogSession != null)
                    {
                        // Use the session to close directly
                        dialogSession.Close(false);
                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Dialog closed using session.Close(false)");
                        return;
                    }

                    // Fallback methods if no session found
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] No dialog session, trying alternative methods");

                    // Method 1: Try with dialog identifier
                    try
                    {
                        if (DialogHost.IsDialogOpen(_dialogIdentifier))
                        {
                            DialogHost.Close(_dialogIdentifier, false);
                            System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Closed using DialogHost.Close with identifier");
                            return;
                        }
                    }
                    catch (Exception ex1)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 1 failed: {ex1.Message}");
                    }

                    // Method 2: Try CloseDialogCommand
                    try
                    {
                        DialogHost.CloseDialogCommand.Execute(false, this);
                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Closed using CloseDialogCommand");
                        return;
                    }
                    catch (Exception ex2)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 2 failed: {ex2.Message}");
                    }

                    // Method 3: Try without parameter
                    try
                    {
                        DialogHost.CloseDialogCommand.Execute(null, this);
                        System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Closed using CloseDialogCommand with null");
                        return;
                    }
                    catch (Exception ex3)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Method 3 failed: {ex3.Message}");
                    }

                    // If all DialogHost methods fail, try the event
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] All DialogHost methods failed, invoking CancelClicked event");
                    CancelClicked?.Invoke();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Unexpected error in cancel: {ex.Message}");
                    // Last resort: try the event
                    CancelClicked?.Invoke();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Cancel_Click exception: {ex.Message}");
                MessageBox.Show($"Error canceling dialog: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] ValidateInput called");
                var errors = new List<string>();

                // Ensure controls are loaded
                if (txtMinimumQuantity == null || txtUnitPrice == null)
                {
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Controls not initialized");
                    errors.Add("Dialog controls are not properly initialized.");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Controls are initialized, proceeding with validation");

                // Validate minimum quantity
                if (!decimal.TryParse(txtMinimumQuantity.Text, out decimal minQty) || minQty <= 0)
                {
                    errors.Add("Minimum quantity must be a positive number.");
                }

                bool isPackPricing = chkPackPricing.IsChecked == true;
                decimal effectiveUnitPrice = 0m;

                if (isPackPricing)
                {
                    // Validate pack price
                    if (!decimal.TryParse(txtPackPrice.Text, out decimal packPrice) || packPrice <= 0)
                    {
                        errors.Add("Pack price must be a positive number when using pack pricing.");
                    }
                    else
                    {
                        effectiveUnitPrice = packPrice / Math.Max(minQty, 1m);
                    }
                }
                else
                {
                    // Validate unit price
                    if (!decimal.TryParse(txtUnitPrice.Text, out effectiveUnitPrice) || effectiveUnitPrice <= 0)
                    {
                        errors.Add("Unit price must be a positive number.");
                    }
                }

                // Validate date range
                if (dpEffectiveDate.SelectedDate.HasValue && dpExpirationDate.SelectedDate.HasValue)
                {
                    if (dpExpirationDate.SelectedDate.Value <= dpEffectiveDate.SelectedDate.Value)
                    {
                        errors.Add("Expiration date must be after the effective date.");
                    }
                }

                // Check if pricing provides a discount (warning, not error)
                if (effectiveUnitPrice >= _product.SellingPrice)
                {
                    string priceType = isPackPricing ? "pack pricing" : "unit pricing";
                    var result = MessageBox.Show(
                        $"The {priceType} ({FormatCurrency(effectiveUnitPrice)} per item) is not less than the regular price ({FormatCurrency(_product.SellingPrice)}).\n\nThis won't provide a discount to customers. Do you want to continue anyway?",
                        "No Discount Warning",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        return false;
                    }
                }

                if (errors.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Validation errors: {string.Join(", ", errors)}");
                    MessageBox.Show(string.Join("\n", errors), "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Validation successful");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] ValidateInput exception: {ex.Message}");
                MessageBox.Show($"Error during validation: {ex.Message}", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void UpdateDtoFromUI()
        {
            // Ensure controls are loaded before accessing them
            if (txtMinimumQuantity == null || txtUnitPrice == null || txtTierName == null)
            {
                return; // Skip update if controls are not ready
            }

            // Update quantity values
            if (decimal.TryParse(txtMinimumQuantity.Text, out decimal minQty))
                _priceTier.MinimumQuantity = minQty;

            // No maximum quantity in this UI
            _priceTier.MaximumQuantity = null;

            // Update pricing values based on pack pricing mode
            bool isPackPricing = chkPackPricing.IsChecked == true;

            if (isPackPricing)
            {
                // Pack pricing mode
                if (decimal.TryParse(txtPackPrice.Text, out decimal packPrice))
                {
                    _priceTier.PackPrice = packPrice;
                    // Unit price is calculated from pack price
                    _priceTier.UnitPrice = packPrice / Math.Max(minQty, 1m);
                }
            }
            else
            {
                // Unit pricing mode
                if (decimal.TryParse(txtUnitPrice.Text, out decimal unitPrice))
                    _priceTier.UnitPrice = unitPrice;

                _priceTier.PackPrice = null;
            }

            // Update tier name (optional)
            _priceTier.TierName = string.IsNullOrWhiteSpace(txtTierName.Text) ? null : txtTierName.Text.Trim();

            // Update date range
            _priceTier.EffectiveDate = dpEffectiveDate.SelectedDate;
            _priceTier.ExpirationDate = dpExpirationDate.SelectedDate;

            // Set defaults
            _priceTier.Description = null; // No description field in this UI
            _priceTier.IsActive = true; // Always active in this UI
        }

        /// <summary>
        /// Shows the pricing tier dialog and returns the result.
        /// </summary>
        /// <param name="product">Product to create pricing tier for</param>
        /// <param name="existingTier">Existing tier to edit, or null for new tier</param>
        /// <param name="dialogIdentifier">Dialog host identifier</param>
        /// <returns>The created/edited pricing tier DTO, or null if cancelled</returns>
        public static async Task<PriceTierDto> ShowAsync(Product product, PriceTierDto existingTier = null, string dialogIdentifier = "RootDialog")
        {
            System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] ShowAsync called with dialogIdentifier: {dialogIdentifier}");
            System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Product: {product?.Name ?? "NULL"}, ExistingTier: {existingTier?.TierName ?? "NULL"}");

            var dialog = new PriceTierDialog(product, existingTier);
            dialog._dialogIdentifier = dialogIdentifier; // Set the dialog identifier for closing
            System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Dialog created, initial Result: {dialog.Result?.TierName ?? "NULL"}");

            try
            {
                // First try using the provided identifier
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Attempting DialogHost.Show");
                var result = await DialogHost.Show(dialog, dialogIdentifier);
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] DialogHost.Show returned: {result} (Type: {result?.GetType().Name ?? "NULL"})");
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] dialog.Result after show: {(dialog.Result != null ? $"TierName='{dialog.Result.TierName}', MinQty={dialog.Result.MinimumQuantity}, UnitPrice={dialog.Result.UnitPrice}" : "NULL")}");

                // Check if result is boolean true (successful save)
                if (result is bool success && success)
                {
                    System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] Result is boolean true - returning dialog.Result");
                    var finalResult = dialog.Result;
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Final result: {(finalResult != null ? $"TierName='{finalResult.TierName}', MinQty={finalResult.MinimumQuantity}, UnitPrice={finalResult.UnitPrice}" : "NULL")}");
                    return finalResult;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Result is not boolean true (value: {result}) - returning NULL");
                    return null;
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("already open"))
            {
                // If DialogHost is busy, try opening as a regular WPF window instead
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] DialogHost busy, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") || ex.Message.Contains("Identifier"))
            {
                // If DialogHost identifier doesn't exist, try opening as a regular WPF window
                System.Diagnostics.Debug.WriteLine("[PRICE_TIER_DIALOG] DialogHost not found, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
            catch (Exception ex)
            {
                // For any other exception, try opening as a regular WPF window
                System.Diagnostics.Debug.WriteLine($"[PRICE_TIER_DIALOG] Unexpected error: {ex.Message}, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
        }

        private static async Task<PriceTierDto> ShowAsWindowAsync(PriceTierDialog dialog)
        {
            return await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                PriceTierDto dialogResult = null;

                // Create a window to host the dialog content
                var window = new Window
                {
                    Title = "Pricing Tier",
                    Content = dialog,
                    SizeToContent = SizeToContent.WidthAndHeight,
                    ResizeMode = ResizeMode.NoResize,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = System.Windows.Application.Current.MainWindow,
                    ShowInTaskbar = false
                };

                // Handle the dialog result when Save is clicked
                dialog.SaveClicked += (result) =>
                {
                    dialogResult = result;
                    window.DialogResult = true;
                    window.Close();
                };

                // Handle cancel
                dialog.CancelClicked += () =>
                {
                    dialogResult = null;
                    window.DialogResult = false;
                    window.Close();
                };

                // Show the window as a modal dialog
                window.ShowDialog();

                return dialogResult;
            });
        }
    }
}
