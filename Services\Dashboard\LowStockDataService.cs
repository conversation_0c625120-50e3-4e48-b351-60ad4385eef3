using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;

namespace POSSystem.Services.Dashboard
{
    /// <summary>
    /// ✅ OPTIMIZED: Lightweight data service specifically for low stock dialog
    /// Provides fast, cached access to low stock data with minimal overhead
    /// </summary>
    public class LowStockDataService
    {
        private readonly POSDbContext _context;
        private readonly Dictionary<string, (object Data, DateTime Expiry)> _cache;
        private const int CACHE_DURATION_MINUTES = 10; // Shorter cache for more frequent updates

        public LowStockDataService(POSDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _cache = new Dictionary<string, (object Data, DateTime Expiry)>();
        }

        /// <summary>
        /// ⚡ OPTIMIZED: Get low stock products with minimal data loading
        /// Uses projection to load only required fields for better performance
        /// </summary>
        public async Task<List<LowStockProductData>> GetLowStockProductDataAsync(int maxRecords = 1000)
        {
            const string cacheKey = "low_stock_products_lightweight";
            
            // Try cache first
            if (TryGetFromCache<List<LowStockProductData>>(cacheKey, out var cachedData))
            {
                Debug.WriteLine($"LowStockDataService: Returning {cachedData.Count} products from cache");
                return cachedData;
            }

            var stopwatch = Stopwatch.StartNew();

            // ⚡ PERFORMANCE: Use optimized query with same logic as statistics
            var lowStockData = await _context.Products
                .AsNoTracking()
                .Where(p => p.IsActive && p.Type != ProductType.Service &&
                           (p.StockQuantity <= p.MinimumStock || p.StockQuantity <= p.ReorderPoint || p.StockQuantity == 0))
                .OrderBy(p => p.StockQuantity) // Most critical first
                .Take(maxRecords) // ⚡ PERFORMANCE: Limit records
                .Select(p => new LowStockProductData
                {
                    Id = p.Id,
                    Name = p.Name,
                    SKU = p.SKU,
                    StockQuantity = p.StockQuantity,
                    MinimumStock = p.MinimumStock,
                    ReorderPoint = p.ReorderPoint,
                    PurchasePrice = p.PurchasePrice,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name ?? "Uncategorized"
                })
                .ToListAsync();

            stopwatch.Stop();
            Debug.WriteLine($"LowStockDataService: Loaded {lowStockData.Count} products in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            AddToCache(cacheKey, lowStockData, TimeSpan.FromMinutes((double)CACHE_DURATION_MINUTES));

            return lowStockData;
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get aggregated metrics without loading full product data
        /// </summary>
        public async Task<LowStockMetrics> GetLowStockMetricsAsync()
        {
            const string cacheKey = "low_stock_metrics";
            
            // Try cache first
            if (TryGetFromCache<LowStockMetrics>(cacheKey, out var cachedMetrics))
            {
                Debug.WriteLine("LowStockDataService: Returning metrics from cache");
                return cachedMetrics;
            }

            var stopwatch = Stopwatch.StartNew();

            // ✅ PERFORMANCE: Calculate metrics directly in database
            var metrics = await _context.Products
                .AsNoTracking()
                .Where(p => p.IsActive && p.StockQuantity <= p.ReorderPoint)
                .GroupBy(p => 1) // Group all records together
                .Select(g => new LowStockMetrics
                {
                    TotalProducts = g.Count(),
                    OutOfStockProducts = g.Count(p => p.StockQuantity == 0),
                    NearLowStockProducts = g.Count(p => p.StockQuantity > 0 && p.StockQuantity <= p.MinimumStock),
                    // Workaround SQLite decimal aggregation limitation by casting to double then back to decimal
                    RestockValue = (decimal)g.Sum(p => (double)(((decimal)p.MinimumStock - p.StockQuantity) * p.PurchasePrice))
                })
                .FirstOrDefaultAsync() ?? new LowStockMetrics();

            stopwatch.Stop();
            Debug.WriteLine($"LowStockDataService: Calculated metrics in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            AddToCache(cacheKey, metrics, TimeSpan.FromMinutes((double)CACHE_DURATION_MINUTES));

            return metrics;
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get category distribution for charts
        /// </summary>
        public async Task<List<CategoryDistributionData>> GetCategoryDistributionAsync()
        {
            const string cacheKey = "low_stock_category_distribution";
            
            // Try cache first
            if (TryGetFromCache<List<CategoryDistributionData>>(cacheKey, out var cachedData))
            {
                Debug.WriteLine("LowStockDataService: Returning category distribution from cache");
                return cachedData;
            }

            var stopwatch = Stopwatch.StartNew();

            var distribution = await _context.Products
                .AsNoTracking()
                .Where(p => p.IsActive && p.StockQuantity <= p.ReorderPoint)
                .GroupBy(p => new { p.CategoryId, p.Category.Name })
                .Select(g => new CategoryDistributionData
                {
                    CategoryId = g.Key.CategoryId,
                    CategoryName = g.Key.Name ?? "Uncategorized",
                    ProductCount = g.Count()
                })
                .OrderByDescending(c => c.ProductCount)
                .ToListAsync();

            stopwatch.Stop();
            Debug.WriteLine($"LowStockDataService: Loaded category distribution in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            AddToCache(cacheKey, distribution, TimeSpan.FromMinutes((double)CACHE_DURATION_MINUTES));

            return distribution;
        }

        /// <summary>
        /// ✅ NEW: Invalidate cache when products are updated
        /// </summary>
        public void InvalidateCache()
        {
            _cache.Clear();
            Debug.WriteLine("LowStockDataService: Cache invalidated");
        }

        /// <summary>
        /// ✅ NEW: Preload data in background for instant access
        /// </summary>
        public async Task PreloadDataAsync()
        {
            try
            {
                // Load all data types in parallel
                var tasks = new Task[]
                {
                    GetLowStockProductDataAsync(),
                    GetLowStockMetricsAsync(),
                    GetCategoryDistributionAsync()
                };

                await Task.WhenAll(tasks);
                Debug.WriteLine("LowStockDataService: Data preloaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LowStockDataService: Preload failed - {ex.Message}");
            }
        }

        private bool TryGetFromCache<T>(string key, out T value)
        {
            value = default(T);
            
            if (!_cache.ContainsKey(key))
                return false;

            var (data, expiry) = _cache[key];
            if (expiry < DateTime.Now)
            {
                _cache.Remove(key);
                return false;
            }

            if (data is T cachedValue)
            {
                value = cachedValue;
                return true;
            }

            return false;
        }

        private void AddToCache<T>(string key, T data, TimeSpan duration)
        {
            _cache[key] = (data, DateTime.Now.Add(duration));
        }
    }

    /// <summary>
    /// ✅ NEW: Lightweight data structure for low stock products
    /// </summary>
    public class LowStockProductData
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string SKU { get; set; }
        public decimal StockQuantity { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderPoint { get; set; }
        public decimal PurchasePrice { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// ✅ NEW: Pre-calculated metrics for instant display
    /// </summary>
    public class LowStockMetrics
    {
        public int TotalProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public int NearLowStockProducts { get; set; }
        public decimal RestockValue { get; set; }
    }

    /// <summary>
    /// ✅ NEW: Category distribution data for charts
    /// </summary>
    public class CategoryDistributionData
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public int ProductCount { get; set; }
    }
}
