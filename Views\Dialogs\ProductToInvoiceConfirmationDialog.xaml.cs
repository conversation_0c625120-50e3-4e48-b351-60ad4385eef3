using System.Configuration;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;
using POSSystem.Helpers;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for ProductToInvoiceConfirmationDialog.xaml
    /// </summary>
    public partial class ProductToInvoiceConfirmationDialog : UserControl
    {
        public ProductToInvoiceConfirmationDialog()
        {
            InitializeComponent();

            // Set FlowDirection based on current language for RTL support
            string language = ConfigurationManager.AppSettings["Language"] ?? "en";
            this.FlowDirection = language == "ar" ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
        }

        public ProductToInvoiceConfirmationDialog(Product product) : this()
        {
            // Create ViewModel with the product
            // Note: In a real application, these services would be injected via DI
            var dbService = ServiceLocator.Current?.GetInstance<DatabaseService>();
            var permissionsService = dbService != null ? new UserPermissionsService(dbService) : null;

            if (permissionsService != null && dbService != null)
            {
                var viewModel = new ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                DataContext = viewModel;

                // Subscribe to the RequestClose event
                viewModel.RequestClose += OnRequestClose;
            }
        }

        public ProductToInvoiceConfirmationDialog(ProductToInvoiceConfirmationViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to the RequestClose event
            if (viewModel != null)
            {
                viewModel.RequestClose += OnRequestClose;
            }
        }

        private void OnRequestClose()
        {
            System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_DIALOG] OnRequestClose called");

            // Close the MaterialDesign DialogHost with the dialog result
            var dialogResult = (DataContext as ProductToInvoiceConfirmationViewModel)?.DialogResult;
            System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_DIALOG] Closing dialog with result: {dialogResult?.Confirmed}");

            DialogHost.Close("SalesDialog", dialogResult);
        }

        /// <summary>
        /// Gets the dialog result from the ViewModel
        /// </summary>
        public ProductToInvoiceResult DialogResult => (DataContext as ProductToInvoiceConfirmationViewModel)?.DialogResult;
    }
}
