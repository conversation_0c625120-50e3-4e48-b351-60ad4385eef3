CREATE TABLE IF NOT EXISTS BatchStock (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    BatchNumber TEXT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL DEFAULT 0,
    ManufactureDate TEXT NOT NULL,
    ExpiryDate TEXT,
    PurchasePrice DECIMAL(18,2) NOT NULL DEFAULT 0,
    SellingPrice DECIMAL(18,2) NOT NULL DEFAULT 0,
    Location TEXT,
    Notes TEXT,
    CreatedAt TEXT NOT NULL,
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

CREATE INDEX idx_batchstock_product ON BatchStock(ProductId);
CREATE INDEX idx_batchstock_expiry ON BatchStock(ExpiryDate); 