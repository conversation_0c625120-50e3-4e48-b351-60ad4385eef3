using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    /// <summary>
    /// Converts MaterialDesignTheme.BaseTheme to a specific color value based on light/dark mode
    /// Parameter should be in the format: "LightValue|DarkValue"
    /// </summary>
    public class BaseThemeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is BaseTheme baseTheme && parameter is string colorParams)
            {
                var colorValues = colorParams.Split('|');
                if (colorValues.Length == 2)
                {
                    // Return different values based on the current theme
                    string colorValue = baseTheme == BaseTheme.Light ? colorValues[0] : colorValues[1];
                    
                    // If converting to a Color
                    if (targetType == typeof(Color))
                    {
                        return ColorConverter.ConvertFromString(colorValue);
                    }
                    
                    // If converting to a Brush
                    if (targetType == typeof(Brush))
                    {
                        return new SolidColorBrush((Color)ColorConverter.ConvertFromString(colorValue));
                    }
                    
                    // Return the string value
                    return colorValue;
                }
            }
            
            // Default return
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 