-- =====================================================
-- Add Invoice Permissions to UserPermissions Table
-- =====================================================
-- This script adds the missing invoice permission columns to the UserPermissions table
-- Run this script to fix the "no such column: u.CanCompleteInvoiceDrafts" error

-- Check if columns already exist before adding them
PRAGMA table_info(UserPermissions);

-- Add invoice permission columns to UserPermissions table (if they don't exist)
-- Note: SQLite will ignore ALTER TABLE ADD COLUMN if the column already exists

-- Add CanCreateFullInvoices column
ALTER TABLE UserPermissions ADD COLUMN CanCreateFullInvoices INTEGER NOT NULL DEFAULT 0;

-- Add CanCreateDraftInvoices column  
ALTER TABLE UserPermissions ADD COLUMN CanCreateDraftInvoices INTEGER NOT NULL DEFAULT 1;

-- Add CanCompleteInvoiceDrafts column
ALTER TABLE UserPermissions ADD COLUMN CanCompleteInvoiceDrafts INTEGER NOT NULL DEFAULT 0;

-- Add CanViewPendingDrafts column
ALTER TABLE UserPermissions ADD COLUMN CanViewPendingDrafts INTEGER NOT NULL DEFAULT 0;

-- Add CanModifyInvoicePricing column
ALTER TABLE UserPermissions ADD COLUMN CanModifyInvoicePricing INTEGER NOT NULL DEFAULT 0;

-- Add CanSetPaymentTerms column
ALTER TABLE UserPermissions ADD COLUMN CanSetPaymentTerms INTEGER NOT NULL DEFAULT 0;

-- Add CanSelectCustomersForInvoices column
ALTER TABLE UserPermissions ADD COLUMN CanSelectCustomersForInvoices INTEGER NOT NULL DEFAULT 1;

-- Add CanDeleteDraftInvoices column
ALTER TABLE UserPermissions ADD COLUMN CanDeleteDraftInvoices INTEGER NOT NULL DEFAULT 0;

-- Add CanRejectDraftInvoices column
ALTER TABLE UserPermissions ADD COLUMN CanRejectDraftInvoices INTEGER NOT NULL DEFAULT 0;

-- Add CanManageInvoiceSettings column
ALTER TABLE UserPermissions ADD COLUMN CanManageInvoiceSettings INTEGER NOT NULL DEFAULT 0;

-- =====================================================
-- Update Existing User Permissions Based on Roles
-- =====================================================

-- Admin users get full invoice permissions
UPDATE UserPermissions 
SET CanCreateFullInvoices = 1,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 1,
    CanViewPendingDrafts = 1,
    CanModifyInvoicePricing = 1,
    CanSetPaymentTerms = 1,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 1,
    CanRejectDraftInvoices = 1,
    CanManageInvoiceSettings = 1,
    UpdatedAt = datetime('now')
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name = 'Admin'
);

-- Manager users get most invoice permissions except full invoice creation and settings management
UPDATE UserPermissions 
SET CanCreateFullInvoices = 0,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 1,
    CanViewPendingDrafts = 1,
    CanModifyInvoicePricing = 0,
    CanSetPaymentTerms = 1,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 0,
    CanRejectDraftInvoices = 1,
    CanManageInvoiceSettings = 0,
    UpdatedAt = datetime('now')
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name = 'Manager'
);

-- Regular users (Employee/Cashier) get basic draft creation permissions
UPDATE UserPermissions 
SET CanCreateFullInvoices = 0,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 0,
    CanViewPendingDrafts = 0,
    CanModifyInvoicePricing = 0,
    CanSetPaymentTerms = 0,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 0,
    CanRejectDraftInvoices = 0,
    CanManageInvoiceSettings = 0,
    UpdatedAt = datetime('now')
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name IN ('Employee', 'Cashier')
);

-- =====================================================
-- Verification Query
-- =====================================================
-- Run this to verify the columns were added successfully
SELECT 'UserPermissions table structure after migration:' as Info;
PRAGMA table_info(UserPermissions);

-- Check updated permissions
SELECT 'Sample user permissions after migration:' as Info;
SELECT u.Username, r.Name as Role,
       up.CanCreateFullInvoices, up.CanCreateDraftInvoices, up.CanCompleteInvoiceDrafts,
       up.CanViewPendingDrafts, up.CanModifyInvoicePricing, up.CanSetPaymentTerms
FROM Users u
INNER JOIN Roles r ON u.RoleId = r.Id
LEFT JOIN UserPermissions up ON u.Id = up.UserId
LIMIT 5;
