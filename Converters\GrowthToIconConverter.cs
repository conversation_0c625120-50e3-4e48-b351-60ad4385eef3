using System;
using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    public class GrowthToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal growth)
            {
                if (growth > 0)
                    return PackIconKind.ArrowUpBold;
                else if (growth < 0)
                    return PackIconKind.ArrowDownBold;
                else
                    return PackIconKind.ArrowRightBold;
            }

            return PackIconKind.ArrowRightBold; // Default
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 