using System;
using System.Linq;
using System.Windows;
using System.Text;
using POSSystem.Models;
using System.Collections.ObjectModel;

namespace POSSystem.Utilities
{
    public static class DiagnosticHelper
    {
        public static void DebugInvoiceState(ObservableCollection<InvoiceItem> items)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("=== INVOICE ITEMS DIAGNOSTIC INFO ===");
                
                if (items == null)
                {
                    sb.AppendLine("Items collection is NULL");
                    MessageBox.Show("Items collection is NULL", "Debug Info", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }
                
                sb.AppendLine($"Items count: {items.Count}");
                
                decimal manualTotal = 0;
                int index = 0;
                foreach (var item in items)
                {
                    if (item == null)
                    {
                        sb.AppendLine($"  [{index}]: NULL ITEM (this will cause NullReferenceException)");
                    }
                    else
                    {
                        sb.AppendLine($"  [{index}]: ProductId={item.ProductId}, Qty={item.Quantity}, Price={item.UnitPrice}, Total={item.Total}");
                        manualTotal += item.Total;
                    }
                    index++;
                }
                
                sb.AppendLine($"Manually calculated total: {manualTotal}");
                
                MessageBox.Show(sb.ToString(), "Invoice Items Debug", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in diagnostic helper: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        public static void CheckSaveConditions(ViewModels.InvoiceViewModel viewModel)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("=== SAVE BUTTON CONDITIONS ===");
            bool canSave = true;
            
            // Check invoice number
            if (string.IsNullOrWhiteSpace(viewModel.InvoiceNumber))
            {
                sb.AppendLine("❌ Invoice Number is missing");
                canSave = false;
            }
            else
            {
                sb.AppendLine("✓ Invoice Number is present");
            }
            
            // Check invoice type
            if (string.IsNullOrWhiteSpace(viewModel.InvoiceType))
            {
                sb.AppendLine("❌ Invoice Type is missing");
                canSave = false;
            }
            else
            {
                sb.AppendLine("✓ Invoice Type is present");
            }
            
            // Check status
            if (string.IsNullOrWhiteSpace(viewModel.Status))
            {
                sb.AppendLine("❌ Status is missing");
                canSave = false;
            }
            else
            {
                sb.AppendLine("✓ Status is present");
            }
            
            // Check items
            if (viewModel.InvoiceItems == null || viewModel.InvoiceItems.Count == 0)
            {
                sb.AppendLine("❌ No invoice items added");
                canSave = false;
            }
            else
            {
                sb.AppendLine($"✓ Invoice has {viewModel.InvoiceItems.Count} items");
            }
            
            // Check customer/supplier
            if (viewModel.InvoiceType == "Sales")
            {
                if (!viewModel.CustomerId.HasValue)
                {
                    sb.AppendLine("❌ Customer is not selected for Sales invoice");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine("✓ Customer is selected");
                }
            }
            
            if (viewModel.InvoiceType == "Purchase")
            {
                if (!viewModel.SupplierId.HasValue)
                {
                    sb.AppendLine("❌ Supplier is not selected for Purchase invoice");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine("✓ Supplier is selected");
                }
            }
            
            sb.AppendLine($"FINAL RESULT: Can{(canSave ? "" : "not")} save invoice");
            
            System.Diagnostics.Debug.WriteLine(sb.ToString());
            
            // Popup message for testing
            MessageBox.Show(sb.ToString(), "Save Button Diagnostic", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        public static void AnalyzeRecalculateTotals(ObservableCollection<InvoiceItem> items)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("=== RECALCULATE TOTALS ANALYSIS ===");
                
                if (items == null)
                {
                    sb.AppendLine("Items collection is NULL");
                    System.Diagnostics.Debug.WriteLine(sb.ToString());
                    return;
                }
                
                sb.AppendLine($"Items count: {items.Count}");
                
                decimal manualTotal = 0;
                int index = 0;
                foreach (var item in items)
                {
                    if (item == null)
                    {
                        sb.AppendLine($"  [{index}]: NULL ITEM (this will cause NullReferenceException)");
                    }
                    else
                    {
                        decimal itemTotal = item.Total; // This would throw if item.Total is accessed and null
                        sb.AppendLine($"  [{index}]: ProductId={item.ProductId}, Total={itemTotal}");
                        manualTotal += itemTotal;
                    }
                    index++;
                }
                
                sb.AppendLine($"Manually calculated total: {manualTotal}");
                
                System.Diagnostics.Debug.WriteLine(sb.ToString());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error analyzing totals: {ex.Message}");
            }
        }
    }
} 