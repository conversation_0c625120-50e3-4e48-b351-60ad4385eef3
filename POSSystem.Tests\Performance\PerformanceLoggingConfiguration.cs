using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Services.Logging;
using POSSystem.Services.Monitoring;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Performance Logging Configuration
    /// 
    /// This class configures and enhances the existing debugging infrastructure
    /// for comprehensive performance testing. It integrates with:
    /// 1. EnhancedLoggingService for structured logging
    /// 2. PerformanceMonitoringService for metrics collection
    /// 3. File-based output for detailed analysis
    /// 4. Real-time performance tracking
    /// </summary>
    public static class PerformanceLoggingConfiguration
    {
        private static readonly Dictionary<string, TextWriterTraceListener> _traceListeners = new();
        private static readonly Dictionary<string, StreamWriter> _logWriters = new();
        private static string _performanceLogDirectory;
        private static bool _isConfigured = false;

        /// <summary>
        /// Configures enhanced performance logging for comprehensive testing
        /// </summary>
        public static async Task ConfigurePerformanceLoggingAsync(IServiceProvider serviceProvider, string sessionId)
        {
            if (_isConfigured)
                return;

            try
            {
                // Set up performance logging directory
                _performanceLogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceTestLogs", sessionId);
                Directory.CreateDirectory(_performanceLogDirectory);

                // Configure trace listeners for different log types
                await SetupTraceListenersAsync(sessionId);

                // Configure enhanced logging service
                await ConfigureEnhancedLoggingServiceAsync(serviceProvider, sessionId);

                // Configure performance monitoring service
                await ConfigurePerformanceMonitoringServiceAsync(serviceProvider, sessionId);

                // Set up real-time performance tracking
                SetupRealTimePerformanceTracking();

                _isConfigured = true;

                LogPerformanceMessage("Performance logging configuration completed successfully", "SYSTEM");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error configuring performance logging: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up trace listeners for different types of performance data
        /// </summary>
        private static async Task SetupTraceListenersAsync(string sessionId)
        {
            var logTypes = new[]
            {
                "performance_metrics",
                "database_operations",
                "ui_operations",
                "memory_usage",
                "system_diagnostics",
                "error_tracking"
            };

            foreach (var logType in logTypes)
            {
                var logPath = Path.Combine(_performanceLogDirectory, $"{logType}_{sessionId}.log");
                var listener = new TextWriterTraceListener(logPath, $"{logType}_listener");
                
                Trace.Listeners.Add(listener);
                _traceListeners[logType] = listener;

                // Also create dedicated stream writers for structured logging
                var writer = new StreamWriter(logPath, append: true) { AutoFlush = true };
                _logWriters[logType] = writer;
            }

            Trace.AutoFlush = true;
            await Task.CompletedTask;
        }

        /// <summary>
        /// Configures the enhanced logging service for performance testing
        /// </summary>
        private static async Task ConfigureEnhancedLoggingServiceAsync(IServiceProvider serviceProvider, string sessionId)
        {
            try
            {
                var enhancedLogger = serviceProvider.GetService<IEnhancedLoggingService>();
                if (enhancedLogger != null)
                {
                    // Set global context for performance testing
                    enhancedLogger.SetGlobalContext("PerformanceTestSession", sessionId);
                    enhancedLogger.SetGlobalContext("TestStartTime", DateTime.Now);
                    enhancedLogger.SetGlobalContext("TestEnvironment", "Performance");
                    enhancedLogger.SetGlobalContext("LogDirectory", _performanceLogDirectory);

                    // Log the start of performance testing session
                    await enhancedLogger.LogBusinessEventAsync("PerformanceTestSessionStart", new
                    {
                        SessionId = sessionId,
                        StartTime = DateTime.Now,
                        LogDirectory = _performanceLogDirectory,
                        Configuration = "Enhanced Performance Testing"
                    });

                    LogPerformanceMessage($"Enhanced logging service configured for session: {sessionId}", "ENHANCED_LOGGING");
                }
            }
            catch (Exception ex)
            {
                LogPerformanceMessage($"Warning: Could not configure enhanced logging service: {ex.Message}", "WARNING");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Configures the performance monitoring service for detailed metrics collection
        /// </summary>
        private static async Task ConfigurePerformanceMonitoringServiceAsync(IServiceProvider serviceProvider, string sessionId)
        {
            try
            {
                var performanceMonitor = serviceProvider.GetService<PerformanceMonitoringService>();
                if (performanceMonitor != null)
                {
                    // Configure performance thresholds for testing
                    var thresholds = new Dictionary<string, long>
                    {
                        ["ProductLookup"] = 100,        // 100ms for product lookups
                        ["CartOperation"] = 50,         // 50ms for cart operations
                        ["PaymentProcessing"] = 200,    // 200ms for payment processing
                        ["DashboardLoad"] = 2000,       // 2 seconds for dashboard loading
                        ["ChartGeneration"] = 1000,     // 1 second for chart generation
                        ["DatabaseQuery"] = 500,        // 500ms for database queries
                        ["ReportGeneration"] = 3000     // 3 seconds for report generation
                    };

                    // Note: In a real implementation, we would configure these thresholds
                    // For now, we'll log that they would be configured
                    LogPerformanceMessage($"Performance monitoring thresholds configured: {thresholds.Count} operations", "PERFORMANCE_MONITOR");
                }
            }
            catch (Exception ex)
            {
                LogPerformanceMessage($"Warning: Could not configure performance monitoring service: {ex.Message}", "WARNING");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Sets up real-time performance tracking
        /// </summary>
        private static void SetupRealTimePerformanceTracking()
        {
            // Set up performance counters and real-time monitoring
            var timer = new System.Timers.Timer(5000); // Every 5 seconds
            timer.Elapsed += (sender, e) =>
            {
                try
                {
                    // Capture system performance metrics
                    var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
                    var workingSet = Environment.WorkingSet / (1024 * 1024); // MB
                    
                    LogPerformanceMetric("system_memory_usage", memoryUsage, "MB");
                    LogPerformanceMetric("working_set", workingSet, "MB");
                    LogPerformanceMetric("gc_generation_0", GC.CollectionCount(0), "count");
                    LogPerformanceMetric("gc_generation_1", GC.CollectionCount(1), "count");
                    LogPerformanceMetric("gc_generation_2", GC.CollectionCount(2), "count");
                }
                catch (Exception ex)
                {
                    LogPerformanceMessage($"Error in real-time performance tracking: {ex.Message}", "ERROR");
                }
            };
            
            timer.Start();
            LogPerformanceMessage("Real-time performance tracking started", "REAL_TIME_TRACKING");
        }

        /// <summary>
        /// Logs a performance message to the appropriate log file
        /// </summary>
        public static void LogPerformanceMessage(string message, string category = "GENERAL")
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] [{category}] {message}";
            
            // Write to trace
            Trace.WriteLine(logEntry);
            
            // Write to appropriate log file
            var logType = GetLogTypeForCategory(category);
            if (_logWriters.TryGetValue(logType, out var writer))
            {
                writer.WriteLine(logEntry);
            }
        }

        /// <summary>
        /// Logs a performance metric with structured data
        /// </summary>
        public static void LogPerformanceMetric(string metricName, double value, string unit, Dictionary<string, object> metadata = null)
        {
            var metricData = new
            {
                Timestamp = DateTime.Now,
                MetricName = metricName,
                Value = value,
                Unit = unit,
                Metadata = metadata ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(metricData, new JsonSerializerOptions { WriteIndented = false });
            
            if (_logWriters.TryGetValue("performance_metrics", out var writer))
            {
                writer.WriteLine(json);
            }

            LogPerformanceMessage($"METRIC: {metricName} = {value} {unit}", "METRIC");
        }

        /// <summary>
        /// Logs database operation performance
        /// </summary>
        public static void LogDatabaseOperation(string operation, long durationMs, Dictionary<string, object> metadata = null)
        {
            var operationData = new
            {
                Timestamp = DateTime.Now,
                Operation = operation,
                DurationMs = durationMs,
                Metadata = metadata ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(operationData, new JsonSerializerOptions { WriteIndented = false });
            
            if (_logWriters.TryGetValue("database_operations", out var writer))
            {
                writer.WriteLine(json);
            }

            LogPerformanceMessage($"DB_OP: {operation} took {durationMs}ms", "DATABASE");
        }

        /// <summary>
        /// Logs UI operation performance
        /// </summary>
        public static void LogUIOperation(string operation, long durationMs, Dictionary<string, object> metadata = null)
        {
            var operationData = new
            {
                Timestamp = DateTime.Now,
                Operation = operation,
                DurationMs = durationMs,
                Metadata = metadata ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(operationData, new JsonSerializerOptions { WriteIndented = false });
            
            if (_logWriters.TryGetValue("ui_operations", out var writer))
            {
                writer.WriteLine(json);
            }

            LogPerformanceMessage($"UI_OP: {operation} took {durationMs}ms", "UI");
        }

        /// <summary>
        /// Gets the appropriate log type for a given category
        /// </summary>
        private static string GetLogTypeForCategory(string category)
        {
            return category.ToLower() switch
            {
                "database" or "db_op" => "database_operations",
                "ui" or "ui_op" => "ui_operations",
                "metric" => "performance_metrics",
                "memory" => "memory_usage",
                "error" or "warning" => "error_tracking",
                _ => "system_diagnostics"
            };
        }

        /// <summary>
        /// Generates a comprehensive performance report
        /// </summary>
        public static async Task<string> GeneratePerformanceReportAsync(string sessionId)
        {
            try
            {
                var reportPath = Path.Combine(_performanceLogDirectory, $"performance_summary_{sessionId}.html");
                
                // Read all log files and generate report
                var reportContent = await GenerateHtmlReportAsync();
                await File.WriteAllTextAsync(reportPath, reportContent);

                LogPerformanceMessage($"Performance report generated: {reportPath}", "REPORT");
                return reportPath;
            }
            catch (Exception ex)
            {
                LogPerformanceMessage($"Error generating performance report: {ex.Message}", "ERROR");
                throw;
            }
        }

        /// <summary>
        /// Generates HTML report content
        /// </summary>
        private static async Task<string> GenerateHtmlReportAsync()
        {
            // This would generate a comprehensive HTML report
            // For now, return a placeholder
            var html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; border-left: 4px solid #007acc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>POS System Performance Test Report</h1>
    <p>Generated: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + @"</p>
    <div class='metric'>Detailed performance metrics would be displayed here</div>
</body>
</html>";

            await Task.CompletedTask;
            return html;
        }

        /// <summary>
        /// Cleans up performance logging resources
        /// </summary>
        public static void CleanupPerformanceLogging()
        {
            try
            {
                LogPerformanceMessage("Cleaning up performance logging resources", "CLEANUP");

                // Close all log writers
                foreach (var writer in _logWriters.Values)
                {
                    writer?.Dispose();
                }
                _logWriters.Clear();

                // Remove trace listeners
                foreach (var listener in _traceListeners.Values)
                {
                    Trace.Listeners.Remove(listener);
                    listener?.Dispose();
                }
                _traceListeners.Clear();

                _isConfigured = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error cleaning up performance logging: {ex.Message}");
            }
        }
    }
}
