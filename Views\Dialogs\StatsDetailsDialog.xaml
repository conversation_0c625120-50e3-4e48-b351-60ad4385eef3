<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.StatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             mc:Ignorable="d"
             MinWidth="640" MinHeight="480"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.Resources>
        <!-- Chart style -->
        <Style x:Key="MetricChart" TargetType="lvc:CartesianChart">
            <Setter Property="LegendLocation" Value="Right"/>
            <Setter Property="DisableAnimations" Value="False"/>
            <Setter Property="Margin" Value="0,16,0,0"/>
            <Setter Property="MinHeight" Value="300"/>
        </Style>
        
        <!-- Metric Card Style -->
        <Style x:Key="MetricCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="UniformCornerRadius" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>

        <Style x:Key="ChartCard" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>

        <!-- Enhanced Chart Card Style for new layout -->
        <Style x:Key="ChartCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="UniformCornerRadius" Value="12"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        </Style>

        <Style x:Key="ChartTitle" TargetType="TextBlock"
               BasedOn="{StaticResource MaterialDesignBody1TextBlock}">
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        </Style>

        <!-- Enhanced Chart Title Style for new layout -->
        <Style x:Key="ChartTitleStyle" TargetType="TextBlock"
               BasedOn="{StaticResource MaterialDesignSubtitle1TextBlock}">
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                   HorizontalScrollBarVisibility="Disabled"
                   CanContentScroll="True">
        <Grid Margin="24"
              MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
              MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource SalesStats}"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       VerticalAlignment="Center"/>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Click="CloseButton_Click"
                    ToolTip="{DynamicResource Close}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Period Filter -->
            <TextBlock Grid.Column="0" 
                       Text="{DynamicResource Period}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       VerticalAlignment="Center"
                       Margin="0,0,8,0"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding TrendPeriods}"
                      SelectedItem="{Binding SelectedTrendPeriod}"
                      DisplayMemberPath="DisplayName"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      MinWidth="150"
                      Margin="0,0,16,0"/>

            <!-- Category Filter -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,8,0">
                <CheckBox IsChecked="{Binding IsCategoryFilterEnabled}"
                          Content="{DynamicResource Category}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            <ComboBox Grid.Column="3"
                      IsEnabled="{Binding IsCategoryFilterEnabled}"
                      ItemsSource="{Binding Categories}"
                      SelectedItem="{Binding SelectedCategory}"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,16,0">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

            <!-- Product Filter -->
            <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="0,0,8,0">
                <CheckBox IsChecked="{Binding IsProductFilterEnabled}"
                          Content="{DynamicResource Product}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            <Grid Grid.Column="5" Margin="0,0,16,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         Text="{Binding BarcodeSearch, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="{DynamicResource SearchByBarcode}"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         VerticalAlignment="Center">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Return" Command="{Binding SearchByBarcodeCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <Button Grid.Column="1"
                        Command="{Binding SearchByBarcodeCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="{DynamicResource SearchByBarcode}"
                        Margin="8,0,0,0"
                        Height="40"
                        Width="40">
                    <materialDesign:PackIcon Kind="Barcode" Height="24" Width="24"/>
                </Button>

                <Button Grid.Column="2"
                        Command="{Binding OpenProductSelectionCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="{DynamicResource SelectProduct}"
                        Margin="8,0,0,0"
                        Height="40"
                        Width="40">
                    <materialDesign:PackIcon Kind="Search" Height="24" Width="24"/>
                </Button>
            </Grid>
        </Grid>

        <!-- Subtitle -->
        <TextBlock Grid.Row="2" 
                   Text="{Binding Subtitle}"
                   Style="{StaticResource MaterialDesignBody1TextBlock}"
                   Opacity="0.6"
                   Margin="0,0,0,8"/>

        <!-- Main Content -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metrics Cards -->
            <UniformGrid Rows="1" Margin="0,0,0,16">
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource TotalSales}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="TotalSales"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding SalesGrowth}"
                                 Foreground="{Binding SalesGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="ShoppingCart" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource Transactions}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Text="{Binding TransactionCount}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2"/>
                        <TextBlock Text="{Binding TransactionGrowth}"
                                 Foreground="{Binding TransactionGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Calculator" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource AverageTransaction}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="AverageTransaction"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding AvgTransactionGrowth}"
                                 Foreground="{Binding AvgTransactionGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource ChartCardStyle}">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Hourly Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource HourlyDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource HourlyDistribution}"
                                           Style="{StaticResource ChartTitleStyle}"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding HourlyDistributionSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding HourlyLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Daily Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource DailyDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource SalesByDays}"
                                           Style="{StaticResource ChartTitleStyle}"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding SalesByDaysSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding SalesByDaysLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Sales Trend -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource SalesTrend}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource SalesTrend}"
                                           Style="{StaticResource ChartTitleStyle}"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding TrendSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding TrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 4: Top Products -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="Star"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource TopProducts}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Star"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource TopSellingProducts}"
                                           Style="{StaticResource ChartTitleStyle}"/>
                            </DockPanel>

                            <DataGrid Grid.Row="1"
                                      ItemsSource="{Binding TopProducts}"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      Style="{StaticResource MaterialDesignDataGrid}"
                                      Height="400"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}"
                                                      Binding="{Binding Product.Name}"
                                                      Width="300"
                                                      MaxWidth="300"/>
                                    <DataGridTextColumn Header="{DynamicResource Quantity}"
                                                      Binding="{Binding TotalQuantity, StringFormat=N3}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                    <DataGridTextColumn Header="{DynamicResource Sales}"
                                                      Width="120"
                                                      MaxWidth="120">
                                        <DataGridTextColumn.Binding>
                                            <MultiBinding StringFormat="{}{0:N2} {1}">
                                                <Binding Path="TotalSales"/>
                                                <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                            </MultiBinding>
                                        </DataGridTextColumn.Binding>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4"
              Background="{DynamicResource MaterialDesignPaper}"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                      VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="24"
                           Height="24"/>
                <TextBlock Text="{DynamicResource LoadingData}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Margin="0,8,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>