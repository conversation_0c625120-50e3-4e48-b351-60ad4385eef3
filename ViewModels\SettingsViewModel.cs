using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Diagnostics;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Configuration;
using MaterialDesignThemes.Wpf;

namespace POSSystem.ViewModels
{
    public class SettingsViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private readonly IAuthenticationService _authService;
        private readonly ISettingsService _settingsService;
        private readonly IThemeService _themeService;
        private AppSettings _settings;
        private bool _performanceLiteUI;
        private string _currentTheme;
        private string _selectedTheme;
        private Color _selectedColor;
        private bool _isDarkTheme;
        private string _databaseLocation;
        private bool _isBackupInProgress;
        private bool _isRestoreInProgress;
        private ImageSource _logoImageSource;
        private bool _hasLogo;
        private string _performanceProfile;

        public SettingsViewModel(IAuthenticationService authService, ISettingsService settingsService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _settingsService = settingsService ?? throw new ArgumentNullException(nameof(settingsService));
            _themeService = new ThemeService();
            _dbService = new DatabaseService();

            // Initialize settings
            _settings = new AppSettings();
            _databaseLocation = _settingsService.GetSetting("DatabaseLocation") ?? "pos.db";
            LoadSettings();

            // Initialize Performance Lite UI setting
            _performanceLiteUI = (_settingsService.GetSetting("PerformanceLiteUI") ?? "false").Equals("true", StringComparison.OrdinalIgnoreCase);

            // Initialize profile setting
            _performanceProfile = _settingsService.GetSetting("PerformanceProfile") ?? "Standard";

            // Initialize commands
            SaveCommand = new RelayCommand(obj => SaveSettings());
            ChangeDatabaseLocationCommand = new RelayCommand(obj => ChangeDatabaseLocation());
            BackupDatabaseCommand = new RelayCommand(async obj => await BackupDatabase(), obj => !_isBackupInProgress);
            RestoreDatabaseCommand = new RelayCommand(async obj => await RestoreDatabase(), obj => !_isRestoreInProgress);
            SetColorCommand = new RelayCommand(colorParam => SetThemeColor(colorParam));
            UploadLogoCommand = new RelayCommand(_ => UploadLogo());
            RemoveLogoCommand = new RelayCommand(_ => RemoveLogo());

            // Initialize available options
            // Note: in a real application, these might come from a repository or API
            InitializeAvailableOptions();
        }

        public System.Windows.FlowDirection CurrentFlowDirection
        {
            get
            {
                // Immediately return the correct flow direction based on current language setting
                return Settings.Display.Language == "ar" ? 
                       System.Windows.FlowDirection.RightToLeft : 
                       System.Windows.FlowDirection.LeftToRight;
            }
        }

        public bool HasLoyaltyAccess => _authService.HasPermission("loyalty.manage") || _authService.HasPermission("settings.access");

        public AppSettings Settings
        {
            get => _settings;
            set
            {
                if (_settings != value)
                {
                    _settings = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<LanguageOption> AvailableLanguages { get; } = new()
        {
            new LanguageOption { DisplayName = "English", Code = "en" },
            new LanguageOption { DisplayName = "Français", Code = "fr" },
            new LanguageOption { DisplayName = "العربية", Code = "ar" },
            new LanguageOption { DisplayName = "Español", Code = "es" }
        };

        public ObservableCollection<CurrencyOption> AvailableCurrencies { get; } = new()
        {
            new CurrencyOption { DisplayName = "Algerian Dinar (DA)", Code = "DZD" },
            new CurrencyOption { DisplayName = "US Dollar ($)", Code = "USD" },
            new CurrencyOption { DisplayName = "Euro (€)", Code = "EUR" },
            new CurrencyOption { DisplayName = "British Pound (£)", Code = "GBP" }
        };

        public ObservableCollection<DateFormatOption> AvailableDateFormats { get; } = new()
        {
            new DateFormatOption { DisplayName = "MM/DD/YYYY", Code = "US" },
            new DateFormatOption { DisplayName = "DD/MM/YYYY", Code = "EU" },
            new DateFormatOption { DisplayName = "YYYY-MM-DD", Code = "ISO" }
        };

        public ObservableCollection<ThemeOption> AvailableThemes { get; } = new ObservableCollection<ThemeOption>();

        public ObservableCollection<SalesLayoutThemeOption> AvailableSalesLayoutThemes { get; } = new ObservableCollection<SalesLayoutThemeOption>();

        public string SelectedTheme
        {
            get => _selectedTheme;
            set
            {
                if (_selectedTheme != value)
                {
                    _selectedTheme = value;
                    OnPropertyChanged();
                    ApplyThemePreset();
                }
            }
        }

        public bool IsDarkTheme
        {
            get => _isDarkTheme;
            set
            {
                if (_isDarkTheme != value)
                {
                    _isDarkTheme = value;
                    OnPropertyChanged();
                    ApplyCurrentTheme();
                }
            }
        }

        public Color SelectedColor
        {
            get => _selectedColor;
            set
            {
                if (_selectedColor != value)
                {
                    _selectedColor = value;
                    OnPropertyChanged();
                    ApplyCurrentTheme();
                }
            }
        }

        public string DatabaseLocation
        {
            get => _databaseLocation;
            set
            {
                if (_databaseLocation != value)
                {
                    _databaseLocation = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsBackupInProgress
        {
            get => _isBackupInProgress;
            set
            {
                if (_isBackupInProgress != value)
                {
                    _isBackupInProgress = value;
                    OnPropertyChanged();
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public bool IsRestoreInProgress
        {
            get => _isRestoreInProgress;
            set
            {
                if (_isRestoreInProgress != value)
                {
                    _isRestoreInProgress = value;
                    OnPropertyChanged();
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public bool PerformanceLiteUI
        {
            get => _performanceLiteUI;
            set
            {
                if (_performanceLiteUI != value)
                {
                    _performanceLiteUI = value;
                    OnPropertyChanged();
                    try { _settingsService.SetSetting("PerformanceLiteUI", value ? "true" : "false"); }
                    catch (Exception ex) { Debug.WriteLine($"[SETTINGS] Failed to save PerformanceLiteUI: {ex.Message}"); }
                }
            }
        }

        public string PerformanceProfile
        {
            get => _performanceProfile;
            set
            {
                if (_performanceProfile != value)
                {
                    _performanceProfile = value;
                    OnPropertyChanged();
                    try { _settingsService.SetSetting("PerformanceProfile", value ?? "Standard"); }
                    catch (Exception ex) { Debug.WriteLine($"[SETTINGS] Failed to save PerformanceProfile: {ex.Message}"); }
                }
            }
        }

        public ICommand SaveCommand { get; }
        public ICommand ChangeDatabaseLocationCommand { get; }
        public ICommand BackupDatabaseCommand { get; }
        public ICommand RestoreDatabaseCommand { get; }
        public ICommand SetColorCommand { get; }
        public ICommand UploadLogoCommand { get; private set; }
        public ICommand RemoveLogoCommand { get; private set; }
        
        public ImageSource LogoImageSource
        {
            get => _logoImageSource;
            private set
            {
                if (_logoImageSource != value)
                {
                    _logoImageSource = value;
                    OnPropertyChanged();
                }
            }
        }
        
        public bool IsLogoEmpty => string.IsNullOrEmpty(Settings.Company.Logo);
        
        public bool HasLogo
        {
            get => _hasLogo;
            private set
            {
                if (_hasLogo != value)
                {
                    _hasLogo = value;
                    OnPropertyChanged();
                }
            }
        }

        private void LoadSettings()
        {
            try
            {
                // Load display settings
                Settings.Display.Language = _settingsService.GetSetting("Language") ?? "en";
                Settings.Display.Theme = _settingsService.GetSetting("Theme") ?? "Light";
                _currentTheme = Settings.Display.Theme;
                _selectedTheme = _settingsService.GetSetting("SelectedTheme") ?? "Default";
                Settings.Display.SalesLayoutTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

                // Load theme settings
                string savedColorHex = _settingsService.GetSetting("ThemeColor") ?? "#2196F3";
                _selectedColor = (Color)ColorConverter.ConvertFromString(savedColorHex);
                _isDarkTheme = _settingsService.GetSetting("IsDarkTheme")?.ToLower() == "true";

                // Load regional settings
                Settings.Regional.DateFormat = _settingsService.GetSetting("DateFormat") ?? "US";
                Settings.Regional.Currency = _settingsService.GetSetting("Currency") ?? "DZD";

                // Load company logo
                Settings.Company.Logo = _settingsService.GetSetting("CompanyLogo") ?? string.Empty;

                // Load company settings from database
                LoadCompanySettings();

                // Apply the current theme
                ApplyCurrentTheme();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCompanySettings()
        {
            // TODO: Load from database using _dbService
            // For now using default values from the model
            
            // Load logo if it exists
            UpdateLogoImageSource();
        }

        private void UpdateLogoImageSource()
        {
            if (!string.IsNullOrEmpty(Settings.Company.Logo))
            {
                try
                {
                    var imageSource = new BitmapImage();
                    byte[] imageData = Convert.FromBase64String(Settings.Company.Logo);
                    
                    using (var ms = new MemoryStream(imageData))
                    {
                        imageSource.BeginInit();
                        imageSource.StreamSource = ms;
                        imageSource.CacheOption = BitmapCacheOption.OnLoad;
                        imageSource.EndInit();
                    }
                    
                    LogoImageSource = imageSource;
                    HasLogo = true;
                }
                catch (Exception ex)
                {
                    // Log the error and reset logo
                    Debug.WriteLine($"Error loading logo: {ex.Message}");
                    Settings.Company.Logo = string.Empty;
                    LogoImageSource = null;
                    HasLogo = false;
                }
            }
            else
            {
                LogoImageSource = null;
                HasLogo = false;
            }
            
            OnPropertyChanged(nameof(IsLogoEmpty));
        }

        private void ApplyCurrentTheme()
        {
            _themeService.ApplyCustomTheme(_isDarkTheme, _selectedColor);
            
            // Manually update app background colors based on theme
            Application.Current.Resources["AppMainBackgroundColor"] = 
                (Color)ColorConverter.ConvertFromString(_isDarkTheme ? "#1E1E1E" : "#EEF2F7");
            Application.Current.Resources["AppSecondaryBackgroundColor"] = 
                (Color)ColorConverter.ConvertFromString(_isDarkTheme ? "#2D2D30" : "#F5F8FC");
        }

        private void ApplyThemePreset()
        {
            // Find the theme preset
            foreach (var theme in AvailableThemes)
            {
                if (theme.Code == _selectedTheme)
                {
                    // Apply the theme settings based on presets
                    switch (theme.Code)
                    {
                        case "Default":
                            IsDarkTheme = false;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#2196F3"); // Default blue
                            break;
                        case "Dark":
                            IsDarkTheme = true;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#2196F3"); // Blue
                            break;
                        case "Green":
                            IsDarkTheme = false;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#4CAF50"); // Green
                            break;
                        case "DarkGreen":
                            IsDarkTheme = true;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#4CAF50"); // Green
                            break;
                        case "Red":
                            IsDarkTheme = false;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#F44336"); // Red
                            break;
                        case "DarkRed":
                            IsDarkTheme = true;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#F44336"); // Red
                            break;
                        case "Purple":
                            IsDarkTheme = false;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#9C27B0"); // Purple
                            break;
                        case "DarkPurple":
                            IsDarkTheme = true;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#9C27B0"); // Purple
                            break;
                        case "Orange":
                            IsDarkTheme = false;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#FF9800"); // Orange
                            break;
                        case "DarkOrange":
                            IsDarkTheme = true;
                            SelectedColor = (Color)ColorConverter.ConvertFromString("#FF9800"); // Orange
                            break;
                        case "Custom":
                            // Do nothing, keep current settings
                            break;
                    }
                    break;
                }
            }
        }

        private void SetThemeColor(object colorParam)
        {
            if (colorParam is string colorHex)
            {
                try
                {
                    SelectedColor = (Color)ColorConverter.ConvertFromString(colorHex);
                    SelectedTheme = "Custom"; // Switch to custom theme when using color buttons
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error setting color: {ex.Message}");
                }
            }
        }

        private void SaveSettings()
        {
            try
            {
                var originalLanguage = _settingsService.GetSetting("Language");
                var languageChanged = originalLanguage != Settings.Display.Language;

                // Save display settings
                _settingsService.SaveSetting("Language", Settings.Display.Language);
                _settingsService.SaveSetting("Theme", Settings.Display.Theme);
                _settingsService.SaveSetting("SalesLayoutTheme", Settings.Display.SalesLayoutTheme);
                _settingsService.SaveSetting("SelectedTheme", _selectedTheme);
                
                // Save theme settings with proper color format
                _settingsService.SaveSetting("ThemeColor", _selectedColor.ToString());
                _settingsService.SaveSetting("IsDarkTheme", _isDarkTheme.ToString());

                // Save regional settings
                _settingsService.SaveSetting("DateFormat", Settings.Regional.DateFormat);
                _settingsService.SaveSetting("Currency", Settings.Regional.Currency);

                // Apply theme immediately
                _themeService.ApplyCustomTheme(_isDarkTheme, _selectedColor);

                // Save company settings to database
                SaveCompanySettings();

                // If language changed, always apply it immediately
                if (languageChanged)
                {
                    // Update config to ensure it's available for App.ApplyLanguage
                    var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                    if (config.AppSettings.Settings["Language"] == null)
                    {
                        config.AppSettings.Settings.Add("Language", Settings.Display.Language);
                    }
                    else
                    {
                        config.AppSettings.Settings["Language"].Value = Settings.Display.Language;
                    }
                    config.Save(ConfigurationSaveMode.Modified);
                    ConfigurationManager.RefreshSection("appSettings");
                    
                    // Apply the language change
                    App.ApplyLanguage(Settings.Display.Language);
                    
                    // Notify property change for flow direction
                    OnPropertyChanged(nameof(CurrentFlowDirection));
                    
                    // Ask if user wants to restart
                    var restartMessage = Application.Current.TryFindResource("LanguageChangeRestartPrompt") as string ?? 
                                        "Some changes require restarting the application for full effect. Would you like to restart now?";
                    var restartTitle = Application.Current.TryFindResource("RestartRequired") as string ?? "Restart Required";
                    
                    if (MessageBox.Show(restartMessage, restartTitle, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
                    {
                        // Restart the application
                        System.Diagnostics.Process.Start(Application.ResourceAssembly.Location);
                        Application.Current.Shutdown();
                        return;
                    }
                }

                // Show success message
                MessageBox.Show(
                    Application.Current.TryFindResource("SettingsSaved") as string ?? "Settings saved successfully!",
                    Application.Current.TryFindResource("Success") as string ?? "Success",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{(Application.Current.TryFindResource("ErrorSavingSettings") as string ?? "Error saving settings")}: {ex.Message}",
                    Application.Current.TryFindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveCompanySettings()
        {
            try
            {
                // Save logo to settings service
                _settingsService.SaveSetting("CompanyLogo", Settings.Company.Logo);
                
                // TODO: Save other company settings to database using _dbService
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{(Application.Current.TryFindResource("ErrorSavingCompanySettings") as string ?? "Error saving company settings")}: {ex.Message}",
                    Application.Current.TryFindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChangeDatabaseLocation()
        {
            var dialog = new SaveFileDialog
            {
                Title = "Select Database Location",
                Filter = "SQLite Database (*.db)|*.db",
                FileName = "pos.db",
                InitialDirectory = Path.GetDirectoryName(DatabaseLocation)
            };

            if (dialog.ShowDialog() == true)
            {
                string newLocation = dialog.FileName;
                if (newLocation != DatabaseLocation)
                {
                    try
                    {
                        // If the database already exists at the new location
                        if (File.Exists(newLocation))
                        {
                            var result = MessageBox.Show(
                                "A database file already exists at this location. Do you want to overwrite it?",
                                "Database Exists",
                                MessageBoxButton.YesNo,
                                MessageBoxImage.Warning);

                            if (result != MessageBoxResult.Yes)
                                return;
                        }

                        // Copy the current database to the new location
                        File.Copy(DatabaseLocation, newLocation, true);

                        // Save the new location in settings
                        _settingsService.SaveSetting("DatabaseLocation", newLocation);
                        DatabaseLocation = newLocation;

                        MessageBox.Show(
                            "Database location has been changed. The application will need to restart to use the new location.",
                            "Restart Required",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(
                            $"Error changing database location: {ex.Message}",
                            "Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
        }

        private async Task BackupDatabase()
        {
            IsBackupInProgress = true;
            try
            {
                var dialog = new SaveFileDialog
                {
                    Title = "Save Database Backup",
                    Filter = "SQLite Database Backup (*.bak)|*.bak",
                    FileName = $"pos_backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                };

                if (dialog.ShowDialog() == true)
                {
                    // Show loading indicator
                    await Task.Delay(100); // Allow UI to refresh

                    // Copy the database file to the backup location
                    File.Copy(DatabaseLocation, dialog.FileName, true);

                    MessageBox.Show(
                        "Database backup completed successfully.",
                        "Backup Completed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error backing up database: {ex.Message}",
                    "Backup Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsBackupInProgress = false;
            }
        }

        private async Task RestoreDatabase()
        {
            IsRestoreInProgress = true;
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = "Restore Database from Backup",
                    Filter = "SQLite Database Backup (*.bak;*.db)|*.bak;*.db",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                };

                if (dialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "Restoring the database will replace all current data. Are you sure you want to continue?",
                        "Confirm Restore",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result != MessageBoxResult.Yes)
                        return;

                    // Show loading indicator
                    await Task.Delay(100); // Allow UI to refresh

                    // Create a backup of the current database before restoring
                    string backupLocation = $"{DatabaseLocation}.beforerestore_{DateTime.Now:yyyyMMdd_HHmmss}";
                    File.Copy(DatabaseLocation, backupLocation, true);

                    // Replace the current database with the backup
                    File.Copy(dialog.FileName, DatabaseLocation, true);

                    MessageBox.Show(
                        "Database restored successfully. The application will now restart to apply the changes.",
                        "Restore Completed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    // Restart the application
                    Process.Start(Process.GetCurrentProcess().MainModule.FileName);
                    Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error restoring database: {ex.Message}",
                    "Restore Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsRestoreInProgress = false;
            }
        }

        private void InitializeAvailableOptions()
        {
            // Theme options
            AvailableThemes.Clear();
            AvailableThemes.Add(new ThemeOption { DisplayName = "Default", Code = "Default" });
            AvailableThemes.Add(new ThemeOption { DisplayName = "Modern", Code = "Modern" });
            AvailableThemes.Add(new ThemeOption { DisplayName = "Classic", Code = "Classic" });
            AvailableThemes.Add(new ThemeOption { DisplayName = "Professional", Code = "Professional" });
            AvailableThemes.Add(new ThemeOption { DisplayName = "Colorful", Code = "Colorful" });

            // Point of Sale layout themes
            AvailableSalesLayoutThemes.Clear();
            AvailableSalesLayoutThemes.Add(new SalesLayoutThemeOption 
            { 
                DisplayName = "Standard", 
                Code = "Standard",
                Description = "Standard grid layout with products, categories, and cart" 
            });
            AvailableSalesLayoutThemes.Add(new SalesLayoutThemeOption 
            { 
                DisplayName = "Compact", 
                Code = "Compact",
                Description = "Compact layout optimized for smaller screens" 
            });
            AvailableSalesLayoutThemes.Add(new SalesLayoutThemeOption 
            { 
                DisplayName = "Touch", 
                Code = "Touch",
                Description = "Large buttons and controls optimized for touch screens" 
            });
        }
        
        private void UploadLogo()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Select Company Logo",
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.gif, *.bmp)|*.jpg;*.jpeg;*.png;*.gif;*.bmp",
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var imagePath = openFileDialog.FileName;
                    byte[] imageData = File.ReadAllBytes(imagePath);
                    
                    // Check file size - limit to 1MB to prevent performance issues
                    if (imageData.Length > 1048576) // 1MB in bytes
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("LogoTooLarge") as string ?? "Logo image is too large. Please select an image smaller than 1MB.",
                            Application.Current.TryFindResource("Error") as string ?? "Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    
                    // Convert to Base64 for storage
                    Settings.Company.Logo = Convert.ToBase64String(imageData);
                    
                    // Update the image source
                    UpdateLogoImageSource();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"{(Application.Current.TryFindResource("ErrorLoadingLogo") as string ?? "Error loading logo")}: {ex.Message}",
                        Application.Current.TryFindResource("Error") as string ?? "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private void RemoveLogo()
        {
            // Confirm removal
            var result = MessageBox.Show(
                Application.Current.TryFindResource("ConfirmRemoveLogo") as string ?? "Are you sure you want to remove the company logo?",
                Application.Current.TryFindResource("Confirmation") as string ?? "Confirmation",
                MessageBoxButton.YesNo, MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                Settings.Company.Logo = string.Empty;
                UpdateLogoImageSource();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }

    public class LanguageOption
    {
        public string DisplayName { get; set; }
        public string Code { get; set; }
    }

    public class CurrencyOption
    {
        public string DisplayName { get; set; }
        public string Code { get; set; }
    }

    public class DateFormatOption
    {
        public string DisplayName { get; set; }
        public string Code { get; set; }
    }

    public class ThemeOption
    {
        public string DisplayName { get; set; }
        public string Code { get; set; }
    }

    public class SalesLayoutThemeOption
    {
        public string DisplayName { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
    }
} 