using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;
using POSSystem.Data;
using POSSystem.Services;
using POSSystem.Services.Monitoring;
using POSSystem.Services.Logging;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.DataAccess;
using POSSystem.Models;
using POSSystem.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Comprehensive Performance Test Suite for POS System
    /// 
    /// This test suite provides comprehensive performance testing capabilities that:
    /// 1. Leverages existing debugging infrastructure for detailed logging
    /// 2. Tests real-world usage scenarios with appropriate data volumes
    /// 3. Measures key performance metrics across all major POS operations
    /// 4. Provides detailed analysis and recommendations for performance improvements
    /// 5. Automatically logs all performance data to files for analysis
    /// </summary>
    public class ComprehensivePerformanceTestSuite : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly IServiceProvider _serviceProvider;
        private readonly POSDbContext _context;
        private readonly PerformanceMonitoringService _performanceMonitor;
        private readonly IEnhancedLoggingService _enhancedLogger;
        private readonly string _testSessionId;
        private readonly string _performanceLogPath;
        private readonly List<PerformanceTestResult> _testResults;
        private readonly Stopwatch _sessionStopwatch;

        public ComprehensivePerformanceTestSuite(ITestOutputHelper output)
        {
            _output = output;
            _testSessionId = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            _testResults = new List<PerformanceTestResult>();
            _sessionStopwatch = Stopwatch.StartNew();

            // Set up enhanced logging for performance testing
            SetupPerformanceLogging();

            // Configure services with performance monitoring
            var services = new ServiceCollection();
            ConfigureTestServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // Get required services
            _context = _serviceProvider.GetRequiredService<POSDbContext>();
            _performanceMonitor = _serviceProvider.GetRequiredService<PerformanceMonitoringService>();
            _enhancedLogger = _serviceProvider.GetRequiredService<IEnhancedLoggingService>();

            // Initialize test session
            InitializeTestSession();
        }

        private void SetupPerformanceLogging()
        {
            try
            {
                // Create performance test logs directory
                var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceTestLogs");
                Directory.CreateDirectory(logsDirectory);

                // Create timestamped performance log file for this test session
                _performanceLogPath = Path.Combine(logsDirectory, $"performance_test_{_testSessionId}.log");

                // Set up basic trace listeners (enhanced configuration will be done later)
                var performanceListener = new TextWriterTraceListener(_performanceLogPath, "PerformanceTestLogger");
                Trace.Listeners.Add(performanceListener);
                Trace.AutoFlush = true;

                // Log test session start
                LogPerformanceMessage("=".PadRight(100, '='));
                LogPerformanceMessage($"COMPREHENSIVE PERFORMANCE TEST SESSION STARTED - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                LogPerformanceMessage($"Session ID: {_testSessionId}");
                LogPerformanceMessage($"Performance Log: {_performanceLogPath}");
                LogPerformanceMessage("=".PadRight(100, '='));
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Warning: Could not set up basic performance logging: {ex.Message}");
            }
        }

        private void ConfigureTestServices(IServiceCollection services)
        {
            // Add logging with enhanced configuration for performance testing
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add in-memory database for testing
            services.AddDbContext<POSDbContext>(options =>
            {
                options.UseInMemoryDatabase(databaseName: $"PerformanceTest_{_testSessionId}");
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Add performance monitoring services
            services.AddSingleton<PerformanceMonitoringService>();
            services.AddSingleton<IEnhancedLoggingService, EnhancedLoggingService>();

            // Add core POS services
            services.AddScoped<DatabaseService>();
            services.AddScoped<UnifiedDataService>();
            services.AddScoped<IProductManagementService, ProductManagementService>();
            services.AddScoped<ISalesManagementService, SalesManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagementService>();
            services.AddScoped<IInventoryManagementService, InventoryManagementService>();

            // Add ViewModels for UI performance testing
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<ProductsViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<CustomersViewModel>();
        }

        private async void InitializeTestSession()
        {
            try
            {
                // Configure enhanced performance logging
                await PerformanceLoggingConfiguration.ConfigurePerformanceLoggingAsync(_serviceProvider, _testSessionId);

                await _enhancedLogger.LogBusinessEventAsync("PerformanceTestSessionStart", new
                {
                    SessionId = _testSessionId,
                    Timestamp = DateTime.Now,
                    TestEnvironment = "In-Memory Database",
                    LogPath = _performanceLogPath
                });

                LogPerformanceMessage("Performance test session initialized successfully with enhanced logging");
            }
            catch (Exception ex)
            {
                LogPerformanceMessage($"Warning: Could not initialize enhanced logging: {ex.Message}");
            }
        }

        private void LogPerformanceMessage(string message)
        {
            var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [PERF] {message}";
            Trace.WriteLine(logEntry);
            _output.WriteLine(logEntry);
        }

        /// <summary>
        /// Sets up test data with specified volume for performance testing
        /// </summary>
        public async Task SetupPerformanceTestDataAsync(int productCount = 1000, int customerCount = 500, int salesCount = 2000)
        {
            await SetupPerformanceTestDataAsync(PerformanceTestDataConfiguration.DataVolumeLevel.Medium, productCount, customerCount, salesCount);
        }

        /// <summary>
        /// Sets up test data using predefined volume configurations
        /// </summary>
        public async Task SetupPerformanceTestDataAsync(PerformanceTestDataConfiguration.DataVolumeLevel volumeLevel,
            int? customProductCount = null, int? customCustomerCount = null, int? customSalesCount = null)
        {
            using var tracker = _performanceMonitor.StartTracking("SetupPerformanceTestData", "DataSetup");

            var config = PerformanceTestDataConfiguration.VolumeConfigurations[volumeLevel];
            var productCount = customProductCount ?? config.ProductCount;
            var customerCount = customCustomerCount ?? config.CustomerCount;
            var salesCount = customSalesCount ?? config.SalesCount;

            LogPerformanceMessage($"Setting up performance test data: {config.Description}");
            LogPerformanceMessage($"Target: {productCount} products, {customerCount} customers, {salesCount} sales");

            try
            {
                // Clear existing data
                _context.Database.EnsureDeleted();
                _context.Database.EnsureCreated();

                // Use the performance test data configuration
                await PerformanceTestDataConfiguration.SetupPerformanceTestDataAsync(
                    _context, volumeLevel, _serviceProvider.GetService<ILogger<ComprehensivePerformanceTestSuite>>());

                tracker.AddMetadata("VolumeLevel", volumeLevel.ToString());
                tracker.AddMetadata("ProductCount", productCount);
                tracker.AddMetadata("CustomerCount", customerCount);
                tracker.AddMetadata("SalesCount", salesCount);

                LogPerformanceMessage($"Performance test data setup completed successfully");
            }
            catch (Exception ex)
            {
                LogPerformanceMessage($"Error setting up performance test data: {ex.Message}");
                throw;
            }
        }

        private List<Category> CreateTestCategories()
        {
            return new List<Category>
            {
                new Category { Id = 1, Name = "Electronics", Description = "Electronic devices and accessories", IsActive = true },
                new Category { Id = 2, Name = "Clothing", Description = "Apparel and fashion items", IsActive = true },
                new Category { Id = 3, Name = "Food & Beverages", Description = "Food and drink items", IsActive = true },
                new Category { Id = 4, Name = "Books & Media", Description = "Books, magazines, and media", IsActive = true },
                new Category { Id = 5, Name = "Home & Garden", Description = "Home improvement and garden supplies", IsActive = true },
                new Category { Id = 6, Name = "Sports & Outdoors", Description = "Sports equipment and outdoor gear", IsActive = true },
                new Category { Id = 7, Name = "Health & Beauty", Description = "Health and beauty products", IsActive = true },
                new Category { Id = 8, Name = "Automotive", Description = "Car parts and accessories", IsActive = true }
            };
        }

        private List<Product> CreateTestProducts(int count, List<Category> categories)
        {
            var products = new List<Product>();
            var random = new Random(42); // Fixed seed for consistent tests

            for (int i = 1; i <= count; i++)
            {
                var category = categories[random.Next(categories.Count)];
                var basePrice = 10 + random.Next(1, 500);
                
                products.Add(new Product
                {
                    Id = i,
                    Name = $"Test Product {i:D4}",
                    SKU = $"SKU{i:D6}",
                    Description = $"Performance test product {i} in {category.Name} category",
                    SellingPrice = basePrice,
                    PurchasePrice = basePrice * 0.6m,
                    StockQuantity = 50 + random.Next(0, 200),
                    CategoryId = category.Id,
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddDays(-random.Next(0, 365))
                });
            }

            return products;
        }

        private List<Customer> CreateTestCustomers(int count)
        {
            var customers = new List<Customer>();
            var random = new Random(42);

            for (int i = 1; i <= count; i++)
            {
                customers.Add(new Customer
                {
                    Id = i,
                    Name = $"Test Customer {i:D4}",
                    Email = $"customer{i}@test.com",
                    Phone = $"555-{random.Next(1000, 9999)}",
                    Address = $"{random.Next(100, 9999)} Test Street",
                    City = "Test City",
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddDays(-random.Next(0, 365))
                });
            }

            return customers;
        }

        private async Task CreateTestSalesAsync(int count, List<Product> products, List<Customer> customers)
        {
            var random = new Random(42);
            var batchSize = 100; // Process in batches for better performance

            for (int batch = 0; batch < count; batch += batchSize)
            {
                var sales = new List<Sale>();
                var currentBatchSize = Math.Min(batchSize, count - batch);

                for (int i = 0; i < currentBatchSize; i++)
                {
                    var saleId = batch + i + 1;
                    var customer = customers[random.Next(customers.Count)];
                    var saleDate = DateTime.Now.AddDays(-random.Next(0, 90));
                    
                    var sale = new Sale
                    {
                        Id = saleId,
                        CustomerId = customer.Id,
                        SaleDate = saleDate,
                        CreatedAt = saleDate,
                        Status = "Completed",
                        PaymentStatus = random.Next(0, 10) > 8 ? "Unpaid" : "Paid",
                        SaleItems = CreateSaleItems(saleId, products, random)
                    };

                    // Calculate totals
                    sale.Subtotal = sale.SaleItems.Sum(item => item.Quantity * item.UnitPrice);
                    sale.TaxAmount = sale.Subtotal * 0.1m; // 10% tax
                    sale.GrandTotal = sale.Subtotal + sale.TaxAmount;

                    sales.Add(sale);
                }

                _context.Sales.AddRange(sales);
                await _context.SaveChangesAsync();
            }
        }

        private List<SaleItem> CreateSaleItems(int saleId, List<Product> products, Random random)
        {
            var itemCount = random.Next(1, 8); // 1-7 items per sale
            var saleItems = new List<SaleItem>();

            for (int i = 0; i < itemCount; i++)
            {
                var product = products[random.Next(products.Count)];
                var quantity = random.Next(1, 5);

                saleItems.Add(new SaleItem
                {
                    SaleId = saleId,
                    ProductId = product.Id,
                    Quantity = quantity,
                    UnitPrice = product.SellingPrice,
                    TotalPrice = quantity * product.SellingPrice
                });
            }

            return saleItems;
        }

        public void Dispose()
        {
            try
            {
                _sessionStopwatch.Stop();

                LogPerformanceMessage("=".PadRight(100, '='));
                LogPerformanceMessage($"PERFORMANCE TEST SESSION COMPLETED - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                LogPerformanceMessage($"Total Session Duration: {_sessionStopwatch.Elapsed:hh\\:mm\\:ss\\.fff}");
                LogPerformanceMessage($"Total Tests Executed: {_testResults.Count}");
                LogPerformanceMessage($"Performance Log Location: {_performanceLogPath}");
                LogPerformanceMessage("=".PadRight(100, '='));

                // Generate final performance report
                try
                {
                    var reportPath = PerformanceLoggingConfiguration.GeneratePerformanceReportAsync(_testSessionId).Result;
                    LogPerformanceMessage($"Final performance report generated: {reportPath}");
                }
                catch (Exception ex)
                {
                    LogPerformanceMessage($"Warning: Could not generate final performance report: {ex.Message}");
                }

                // Clean up enhanced logging
                PerformanceLoggingConfiguration.CleanupPerformanceLogging();

                _context?.Dispose();
                _serviceProvider?.Dispose();
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error during test cleanup: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Represents the result of a performance test
    /// </summary>
    public class PerformanceTestResult
    {
        public string TestName { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Passed { get; set; }
        public string ErrorMessage { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new Dictionary<string, object>();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
