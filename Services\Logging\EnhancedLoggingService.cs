using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace POSSystem.Services.Logging
{
    /// <summary>
    /// Enhanced logging service that provides structured logging, performance tracking,
    /// and comprehensive diagnostic information for the POS system.
    /// </summary>
    /// <remarks>
    /// <para>This service extends standard logging capabilities with:</para>
    /// <list type="bullet">
    /// <item><description>Structured Logging: JSON-formatted logs with consistent structure</description></item>
    /// <item><description>Performance Tracking: Automatic timing and performance metrics</description></item>
    /// <item><description>Context Preservation: Maintains operation context across log entries</description></item>
    /// <item><description>Multiple Outputs: File, console, and external system logging</description></item>
    /// <item><description>Log Aggregation: Centralized collection and analysis</description></item>
    /// <item><description>Diagnostic Information: System state and environment details</description></item>
    /// </list>
    /// </remarks>
    public class EnhancedLoggingService : IEnhancedLoggingService
    {
        private readonly ILogger<EnhancedLoggingService> _logger;
        private readonly string _logDirectory;
        private readonly Dictionary<string, object> _globalContext;
        private readonly object _lockObject = new object();

        /// <summary>
        /// Initializes a new instance of the EnhancedLoggingService.
        /// </summary>
        /// <param name="logger">Base logger for standard logging operations</param>
        public EnhancedLoggingService(ILogger<EnhancedLoggingService> logger = null)
        {
            _logger = logger;
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            _globalContext = new Dictionary<string, object>();

            // Ensure log directory exists
            Directory.CreateDirectory(_logDirectory);

            // Initialize global context
            InitializeGlobalContext();
        }

        #region Public Methods

        /// <summary>
        /// Logs an operation with automatic timing and context preservation.
        /// </summary>
        /// <param name="operationName">Name of the operation being logged</param>
        /// <param name="operation">The operation to execute and log</param>
        /// <param name="context">Additional context information</param>
        /// <returns>The result of the operation</returns>
        public async Task<T> LogOperationAsync<T>(string operationName, Func<Task<T>> operation, Dictionary<string, object> context = null)
        {
            var operationId = Guid.NewGuid().ToString("N")[..8];
            var stopwatch = Stopwatch.StartNew();
            var operationContext = CreateOperationContext(operationName, operationId, context);

            try
            {
                LogOperationStart(operationName, operationId, operationContext);

                var result = await operation();

                stopwatch.Stop();
                LogOperationSuccess(operationName, operationId, stopwatch.ElapsedMilliseconds, operationContext);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogOperationError(operationName, operationId, ex, stopwatch.ElapsedMilliseconds, operationContext);
                throw;
            }
        }

        /// <summary>
        /// Logs a business event with structured data.
        /// </summary>
        /// <param name="eventName">Name of the business event</param>
        /// <param name="eventData">Data associated with the event</param>
        /// <param name="severity">Severity level of the event</param>
        public async Task LogBusinessEventAsync(string eventName, object eventData, LogLevel severity = LogLevel.Information)
        {
            try
            {
                var logEntry = new BusinessEventLogEntry
                {
                    EventId = Guid.NewGuid().ToString("N")[..8],
                    EventName = eventName,
                    Timestamp = DateTime.Now,
                    Severity = severity,
                    EventData = eventData,
                    Context = new Dictionary<string, object>(_globalContext)
                };

                await WriteStructuredLogAsync("business-events", logEntry);

                _logger?.Log(severity, "Business Event: {EventName} (ID: {EventId})", eventName, logEntry.EventId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error logging business event: {EventName}", eventName);
            }
        }

        /// <summary>
        /// Logs performance metrics for monitoring and analysis.
        /// </summary>
        /// <param name="metricName">Name of the performance metric</param>
        /// <param name="value">Value of the metric</param>
        /// <param name="unit">Unit of measurement</param>
        /// <param name="tags">Additional tags for categorization</param>
        public async Task LogPerformanceMetricAsync(string metricName, double value, string unit = null, Dictionary<string, string> tags = null)
        {
            try
            {
                var metricEntry = new PerformanceMetricLogEntry
                {
                    MetricId = Guid.NewGuid().ToString("N")[..8],
                    MetricName = metricName,
                    Value = value,
                    Unit = unit,
                    Tags = tags ?? new Dictionary<string, string>(),
                    Timestamp = DateTime.Now,
                    Context = new Dictionary<string, object>(_globalContext)
                };

                await WriteStructuredLogAsync("performance-metrics", metricEntry);

                _logger?.LogInformation("Performance Metric: {MetricName} = {Value} {Unit}", metricName, value, unit ?? "");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error logging performance metric: {MetricName}", metricName);
            }
        }

        /// <summary>
        /// Logs system diagnostic information.
        /// </summary>
        /// <param name="diagnosticType">Type of diagnostic information</param>
        /// <param name="data">Diagnostic data</param>
        public async Task LogDiagnosticAsync(string diagnosticType, object data)
        {
            try
            {
                var diagnosticEntry = new DiagnosticLogEntry
                {
                    DiagnosticId = Guid.NewGuid().ToString("N")[..8],
                    DiagnosticType = diagnosticType,
                    Timestamp = DateTime.Now,
                    Data = data,
                    SystemInfo = GetSystemInfo(),
                    Context = new Dictionary<string, object>(_globalContext)
                };

                await WriteStructuredLogAsync("diagnostics", diagnosticEntry);

                _logger?.LogInformation("Diagnostic: {DiagnosticType} (ID: {DiagnosticId})", diagnosticType, diagnosticEntry.DiagnosticId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error logging diagnostic information: {DiagnosticType}", diagnosticType);
            }
        }

        /// <summary>
        /// Sets global context that will be included in all log entries.
        /// </summary>
        /// <param name="key">Context key</param>
        /// <param name="value">Context value</param>
        public void SetGlobalContext(string key, object value)
        {
            lock (_lockObject)
            {
                _globalContext[key] = value;
            }
        }

        /// <summary>
        /// Removes a global context entry.
        /// </summary>
        /// <param name="key">Context key to remove</param>
        public void RemoveGlobalContext(string key)
        {
            lock (_lockObject)
            {
                _globalContext.Remove(key);
            }
        }

        /// <summary>
        /// Gets current log statistics for monitoring.
        /// </summary>
        /// <param name="timeRange">Time range for statistics</param>
        /// <returns>Log statistics for the specified time range</returns>
        public async Task<LogStatistics> GetLogStatisticsAsync(TimeSpan timeRange)
        {
            try
            {
                // In a real implementation, this would analyze log files or database
                // For now, returning placeholder statistics
                var stats = new LogStatistics
                {
                    TimeRange = timeRange,
                    TotalLogEntries = 0,
                    ErrorCount = 0,
                    WarningCount = 0,
                    InfoCount = 0,
                    DebugCount = 0,
                    AverageOperationTime = 0,
                    TopOperations = new List<string>(),
                    LogSizeBytes = 0
                };

                await Task.Delay(50); // Simulate async operation
                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving log statistics");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes global context with system information.
        /// </summary>
        private void InitializeGlobalContext()
        {
            lock (_lockObject)
            {
                _globalContext["ApplicationName"] = "POSSystem";
                _globalContext["Version"] = "1.0.0";
                _globalContext["MachineName"] = Environment.MachineName;
                _globalContext["UserName"] = Environment.UserName;
                _globalContext["ProcessId"] = Environment.ProcessId;
                _globalContext["StartTime"] = DateTime.Now;
            }
        }

        /// <summary>
        /// Creates operation context with timing and identification information.
        /// </summary>
        private Dictionary<string, object> CreateOperationContext(string operationName, string operationId, Dictionary<string, object> additionalContext)
        {
            var context = new Dictionary<string, object>(_globalContext)
            {
                ["OperationName"] = operationName,
                ["OperationId"] = operationId,
                ["ThreadId"] = Environment.CurrentManagedThreadId
            };

            if (additionalContext != null)
            {
                foreach (var kvp in additionalContext)
                {
                    context[kvp.Key] = kvp.Value;
                }
            }

            return context;
        }

        /// <summary>
        /// Logs the start of an operation.
        /// </summary>
        private void LogOperationStart(string operationName, string operationId, Dictionary<string, object> context)
        {
            _logger?.LogInformation("Operation Started: {OperationName} (ID: {OperationId})", operationName, operationId);
        }

        /// <summary>
        /// Logs successful completion of an operation.
        /// </summary>
        private void LogOperationSuccess(string operationName, string operationId, long durationMs, Dictionary<string, object> context)
        {
            _logger?.LogInformation("Operation Completed: {OperationName} (ID: {OperationId}) in {Duration}ms", 
                operationName, operationId, durationMs);
        }

        /// <summary>
        /// Logs an operation error.
        /// </summary>
        private void LogOperationError(string operationName, string operationId, Exception exception, long durationMs, Dictionary<string, object> context)
        {
            _logger?.LogError(exception, "Operation Failed: {OperationName} (ID: {OperationId}) after {Duration}ms", 
                operationName, operationId, durationMs);
        }

        /// <summary>
        /// Writes structured log data to file.
        /// </summary>
        private async Task WriteStructuredLogAsync(string logType, object logEntry)
        {
            try
            {
                var fileName = $"{logType}-{DateTime.Now:yyyy-MM-dd}.json";
                var filePath = Path.Combine(_logDirectory, fileName);

                var json = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.AppendAllTextAsync(filePath, json + Environment.NewLine);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error writing structured log for type: {LogType}", logType);
            }
        }

        /// <summary>
        /// Gets current system information for diagnostic purposes.
        /// </summary>
        private object GetSystemInfo()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return new
                {
                    WorkingSetMB = process.WorkingSet64 / (1024 * 1024),
                    PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount,
                    GCTotalMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024),
                    GCGen0Collections = GC.CollectionCount(0),
                    GCGen1Collections = GC.CollectionCount(1),
                    GCGen2Collections = GC.CollectionCount(2)
                };
            }
            catch
            {
                return new { Error = "Unable to retrieve system information" };
            }
        }

        #endregion
    }

    /// <summary>
    /// Interface for enhanced logging capabilities.
    /// </summary>
    public interface IEnhancedLoggingService
    {
        /// <summary>
        /// Logs an operation with automatic timing and context preservation.
        /// </summary>
        Task<T> LogOperationAsync<T>(string operationName, Func<Task<T>> operation, Dictionary<string, object> context = null);

        /// <summary>
        /// Logs a business event with structured data.
        /// </summary>
        Task LogBusinessEventAsync(string eventName, object eventData, LogLevel severity = LogLevel.Information);

        /// <summary>
        /// Logs performance metrics for monitoring and analysis.
        /// </summary>
        Task LogPerformanceMetricAsync(string metricName, double value, string unit = null, Dictionary<string, string> tags = null);

        /// <summary>
        /// Logs system diagnostic information.
        /// </summary>
        Task LogDiagnosticAsync(string diagnosticType, object data);

        /// <summary>
        /// Sets global context that will be included in all log entries.
        /// </summary>
        void SetGlobalContext(string key, object value);

        /// <summary>
        /// Removes a global context entry.
        /// </summary>
        void RemoveGlobalContext(string key);

        /// <summary>
        /// Gets current log statistics for monitoring.
        /// </summary>
        Task<LogStatistics> GetLogStatisticsAsync(TimeSpan timeRange);
    }

    /// <summary>
    /// Represents a business event log entry.
    /// </summary>
    public class BusinessEventLogEntry
    {
        public string EventId { get; set; }
        public string EventName { get; set; }
        public DateTime Timestamp { get; set; }
        public LogLevel Severity { get; set; }
        public object EventData { get; set; }
        public Dictionary<string, object> Context { get; set; }
    }

    /// <summary>
    /// Represents a performance metric log entry.
    /// </summary>
    public class PerformanceMetricLogEntry
    {
        public string MetricId { get; set; }
        public string MetricName { get; set; }
        public double Value { get; set; }
        public string Unit { get; set; }
        public Dictionary<string, string> Tags { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Context { get; set; }
    }

    /// <summary>
    /// Represents a diagnostic log entry.
    /// </summary>
    public class DiagnosticLogEntry
    {
        public string DiagnosticId { get; set; }
        public string DiagnosticType { get; set; }
        public DateTime Timestamp { get; set; }
        public object Data { get; set; }
        public object SystemInfo { get; set; }
        public Dictionary<string, object> Context { get; set; }
    }

    /// <summary>
    /// Represents log statistics for monitoring.
    /// </summary>
    public class LogStatistics
    {
        public TimeSpan TimeRange { get; set; }
        public int TotalLogEntries { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int InfoCount { get; set; }
        public int DebugCount { get; set; }
        public double AverageOperationTime { get; set; }
        public List<string> TopOperations { get; set; }
        public long LogSizeBytes { get; set; }
    }
}
