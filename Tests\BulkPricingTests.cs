using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.Tests
{
    [TestClass]
    public class BulkPricingTests
    {
        private BulkPricingService _bulkPricingService;
        private Product _testProduct;

        [TestInitialize]
        public void Setup()
        {
            _bulkPricingService = new BulkPricingService();
            _testProduct = CreateTestProduct();
        }

        [TestMethod]
        public void CalculateBestPricing_RegularQuantity_ReturnsRegularPrice()
        {
            // Arrange
            var quantity = 3m;

            // Act
            var result = _bulkPricingService.CalculateBestPricing(_testProduct, quantity);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(quantity, result.RequestedQuantity);
            Assert.AreEqual(_testProduct.SellingPrice, result.EffectiveUnitPrice);
            Assert.AreEqual(quantity * _testProduct.SellingPrice, result.TotalPrice);
            Assert.AreEqual(0, result.TotalSavings);
            Assert.IsNull(result.AppliedTier);
        }

        [TestMethod]
        public void CalculateBestPricing_BulkQuantity_AppliesBulkPricing()
        {
            // Arrange
            var quantity = 10m; // Qualifies for 10-unit tier

            // Act
            var result = _bulkPricingService.CalculateBestPricing(_testProduct, quantity);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(quantity, result.RequestedQuantity);
            Assert.IsNotNull(result.AppliedTier);
            Assert.AreEqual(0.75m, result.EffectiveUnitPrice); // 10-unit tier price
            Assert.AreEqual(7.50m, result.TotalPrice); // 10 * 0.75
            Assert.AreEqual(2.50m, result.TotalSavings); // (10 * 1.00) - 7.50
            Assert.IsTrue(result.HasBulkDiscount);
        }

        [TestMethod]
        public void CalculateBestPricing_LargeQuantity_SelectsBestTier()
        {
            // Arrange
            var quantity = 25m; // Should use 20-unit tier

            // Act
            var result = _bulkPricingService.CalculateBestPricing(_testProduct, quantity);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.AppliedTier);
            Assert.AreEqual(20m, result.AppliedTier.MinimumQuantity);
            Assert.AreEqual(0.60m, result.EffectiveUnitPrice); // 20-unit tier price
            Assert.AreEqual(15.00m, result.TotalPrice); // 25 * 0.60
            Assert.AreEqual(10.00m, result.TotalSavings); // (25 * 1.00) - 15.00
        }

        [TestMethod]
        public void GetQuantitySuggestions_SmallQuantity_SuggestsUpgrade()
        {
            // Arrange
            var currentQuantity = 3m;

            // Act
            var suggestions = _bulkPricingService.GetQuantitySuggestions(_testProduct, currentQuantity);

            // Assert
            Assert.IsNotNull(suggestions);
            Assert.IsTrue(suggestions.Count > 0);
            
            var firstSuggestion = suggestions.First();
            Assert.AreEqual(5m, firstSuggestion.SuggestedQuantity); // 5-unit tier
            Assert.AreEqual(2m, firstSuggestion.AdditionalQuantity); // 5 - 3
            Assert.IsTrue(firstSuggestion.TotalSavings > 0);
        }

        [TestMethod]
        public void HandlePartialQuantity_BelowMinimum_ProvidesOptions()
        {
            // Arrange
            var edgeCaseHandler = new BulkPricingEdgeCaseHandler();
            var desiredQuantity = 3m; // Below 5-unit minimum

            // Act
            var result = edgeCaseHandler.HandlePartialQuantity(_testProduct, desiredQuantity);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(desiredQuantity, result.DesiredQuantity);
            Assert.IsTrue(result.Options.Count >= 2); // At least regular price and upgrade options
            Assert.IsNotNull(result.RecommendedOption);
            
            // Should have regular price option
            var regularOption = result.Options.FirstOrDefault(o => o.Type == PartialQuantityOptionType.RegularPrice);
            Assert.IsNotNull(regularOption);
            Assert.AreEqual(3m, regularOption.Quantity);
            Assert.AreEqual(_testProduct.SellingPrice, regularOption.UnitPrice);
        }

        [TestMethod]
        public void ValidatePriceTier_ValidTier_PassesValidation()
        {
            // Arrange
            var validTier = new ProductPriceTier
            {
                ProductId = _testProduct.Id,
                MinimumQuantity = 15m,
                UnitPrice = 0.70m,
                IsActive = true,
                TierName = "15-Unit Bulk"
            };

            // Act
            var result = _bulkPricingService.ValidatePriceTier(validTier, _testProduct);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.Errors.Count);
        }

        [TestMethod]
        public void ValidatePriceTier_InvalidTier_FailsValidation()
        {
            // Arrange
            var invalidTier = new ProductPriceTier
            {
                ProductId = _testProduct.Id,
                MinimumQuantity = 0, // Invalid: must be positive
                UnitPrice = 1.50m, // Invalid: higher than regular price
                MaximumQuantity = -5m, // Invalid: negative
                IsActive = true
            };

            // Act
            var result = _bulkPricingService.ValidatePriceTier(invalidTier, _testProduct);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsValid);
            Assert.IsTrue(result.Errors.Count > 0);
        }

        [TestMethod]
        public void CartItem_ApplyBulkPricing_UpdatesPricing()
        {
            // Arrange
            var cartItem = new CartItem
            {
                Product = _testProduct,
                Quantity = 10m,
                UnitPrice = _testProduct.SellingPrice
            };

            // Act
            cartItem.ApplyBulkPricing();

            // Assert
            Assert.IsTrue(cartItem.HasBulkPricing);
            Assert.IsNotNull(cartItem.AppliedPriceTier);
            Assert.AreEqual(0.75m, cartItem.UnitPrice); // 10-unit tier price
            Assert.AreEqual(2.50m, cartItem.BulkSavings); // Savings amount
        }

        [TestMethod]
        public void Cart_AddItemWithBulkPricing_CalculatesTotals()
        {
            // Arrange
            var cart = new Cart();

            // Act
            cart.AddItem(_testProduct, 10m);

            // Assert
            Assert.IsTrue(cart.HasBulkPricing);
            Assert.AreEqual(2.50m, cart.TotalBulkSavings);
            Assert.AreEqual(10.00m, cart.TotalRegularPrice); // 10 * 1.00
            Assert.AreEqual(7.50m, cart.Total); // With bulk pricing applied
        }

        [TestMethod]
        public void ProductPriceTier_CalculatePriceForQuantity_ReturnsCorrectPrice()
        {
            // Arrange
            var tier = new ProductPriceTier
            {
                MinimumQuantity = 5m,
                UnitPrice = 0.80m,
                IsActive = true
            };

            // Act
            var price = tier.CalculatePriceForQuantity(8m);

            // Assert
            Assert.AreEqual(6.40m, price); // 8 * 0.80
        }

        [TestMethod]
        public void ProductPriceTier_QualifiesForTier_ChecksQuantityRange()
        {
            // Arrange
            var tier = new ProductPriceTier
            {
                MinimumQuantity = 5m,
                MaximumQuantity = 15m,
                IsActive = true,
                EffectiveDate = DateTime.Now.AddDays(-1),
                ExpirationDate = DateTime.Now.AddDays(30)
            };

            // Act & Assert
            Assert.IsTrue(tier.QualifiesForTier(10m)); // Within range
            Assert.IsFalse(tier.QualifiesForTier(3m)); // Below minimum
            Assert.IsFalse(tier.QualifiesForTier(20m)); // Above maximum
        }

        [TestMethod]
        public void Product_GetBestPriceTierForQuantity_ReturnsHighestQualifyingTier()
        {
            // Arrange
            var quantity = 12m; // Should qualify for both 5-unit and 10-unit tiers

            // Act
            var bestTier = _testProduct.GetBestPriceTierForQuantity(quantity);

            // Assert
            Assert.IsNotNull(bestTier);
            Assert.AreEqual(10m, bestTier.MinimumQuantity); // Should return 10-unit tier (higher)
            Assert.AreEqual(0.75m, bestTier.UnitPrice);
        }

        [TestMethod]
        public void Product_GetNextPriceTier_ReturnsCorrectTier()
        {
            // Arrange
            var currentQuantity = 7m; // Between 5-unit and 10-unit tiers

            // Act
            var nextTier = _testProduct.GetNextPriceTier(currentQuantity);

            // Assert
            Assert.IsNotNull(nextTier);
            Assert.AreEqual(10m, nextTier.MinimumQuantity); // Next tier is 10-unit
        }

        [TestMethod]
        public void BulkPricingCalculator_GetOptimizedPurchase_ReturnsOptimalRecommendation()
        {
            // Arrange
            var desiredQuantity = 8m;

            // Act
            var recommendation = BulkPricingCalculator.GetOptimizedPurchase(_testProduct, desiredQuantity);

            // Assert
            Assert.IsNotNull(recommendation);
            Assert.AreEqual(desiredQuantity, recommendation.DesiredQuantity);
            Assert.IsTrue(recommendation.RecommendedQuantity >= desiredQuantity);
            
            // Should recommend upgrading to 10-unit tier for better pricing
            if (recommendation.RecommendedQuantity > desiredQuantity)
            {
                Assert.AreEqual(10m, recommendation.RecommendedQuantity);
                Assert.IsTrue(recommendation.TotalPrice <= desiredQuantity * _testProduct.SellingPrice);
            }
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ProductPriceTier_CalculatePriceForQuantity_ThrowsForInvalidQuantity()
        {
            // Arrange
            var tier = new ProductPriceTier
            {
                MinimumQuantity = 5m,
                UnitPrice = 0.80m
            };

            // Act
            tier.CalculatePriceForQuantity(3m); // Below minimum - should throw
        }

        [TestMethod]
        public void BulkPricingService_GetQuantitySuggestions_NoSuggestions_ReturnsEmpty()
        {
            // Arrange
            var productWithoutBulkPricing = new Product
            {
                Id = 999,
                Name = "Regular Product",
                SellingPrice = 5.00m,
                PriceTiers = new List<ProductPriceTier>() // No bulk pricing
            };

            // Act
            var suggestions = _bulkPricingService.GetQuantitySuggestions(productWithoutBulkPricing, 5m);

            // Assert
            Assert.IsNotNull(suggestions);
            Assert.AreEqual(0, suggestions.Count);
        }

        private Product CreateTestProduct()
        {
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 1.00m,
                PriceTiers = new List<ProductPriceTier>
                {
                    new ProductPriceTier
                    {
                        Id = 1,
                        ProductId = 1,
                        MinimumQuantity = 5m,
                        UnitPrice = 0.80m,
                        TierName = "5-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new ProductPriceTier
                    {
                        Id = 2,
                        ProductId = 1,
                        MinimumQuantity = 10m,
                        UnitPrice = 0.75m,
                        TierName = "10-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new ProductPriceTier
                    {
                        Id = 3,
                        ProductId = 1,
                        MinimumQuantity = 20m,
                        UnitPrice = 0.60m,
                        TierName = "Bulk Rate",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }
            };

            // Set up the navigation property
            foreach (var tier in product.PriceTiers)
            {
                tier.Product = product;
            }

            return product;
        }
    }
}
