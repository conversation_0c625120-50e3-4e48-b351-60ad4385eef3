using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.ViewModels;
using POSSystem.ViewModels.Dashboard;
using CommunityToolkit.Mvvm.Input;

namespace POSSystem.Views.Dialogs
{
    public partial class CategoryDetailsDialog : UserControl
    {
        private readonly CategoryDetailsViewModel _viewModel;
        
        public CategoryDetailsDialog(RefactoredDashboardViewModel dashboardViewModel)
        {
            InitializeComponent();
            
            // Create and set the view model
            _viewModel = new CategoryDetailsViewModel(dashboardViewModel);
            DataContext = _viewModel;
            
            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }
        
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Close the dialog
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    DialogHost.Close("RootDialog");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error closing dialog: {ex.Message}");
            }
        }
        
        private async void CalendarButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create a date picker dialog
                var datePickerDialog = new DateRangePickerDialog();
                
                // Show the dialog
                bool? dialogResult = datePickerDialog.ShowDialog();
                
                // If user selected a date range, apply it
                if (dialogResult == true && datePickerDialog.DateRangeSelected)
                {
                    // Notify the view model of the selected date range
                    await _viewModel.SetCustomDateRangeAsync(datePickerDialog.StartDate, datePickerDialog.EndDate);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing date picker: {ex.Message}");
            }
        }
        
        private async void PreviousDay_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await _viewModel.NavigateToDateAsync(-1);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to previous day: {ex.Message}");
            }
        }
        
        private async void NextDay_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await _viewModel.NavigateToDateAsync(1);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to next day: {ex.Message}");
            }
        }
        
        // New optimized row loading method that replaces the old CategoriesDataGrid_Loaded
        private void DataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            try
            {
                // Apply any row-level formatting or optimizations here
                if (e.Row.GetIndex() % 2 == 0)
                {
                    e.Row.Background = (System.Windows.Media.Brush)Application.Current.Resources["MaterialDesignDivider"];
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DataGrid_LoadingRow: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// ViewModel for detailed category statistics
    /// </summary>
    public class CategoryDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private ObservableCollection<CategoryStatistics> _categories;
        private ObservableCollection<CategoryStatistics> _filteredCategories;
        private ObservableCollection<TimePeriod> _timePeriods;
        private TimePeriod _selectedTimePeriod;
        private string _searchText;
        private bool _isLoading;
        private DateTime? _customStartDate;
        private DateTime? _customEndDate;
        private bool _isUsingCustomDateRange;
        private string _currentDateDisplay;
        
        public CategoryDetailsViewModel(RefactoredDashboardViewModel dashboardViewModel)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = new DatabaseService();
            
            Categories = new ObservableCollection<CategoryStatistics>();
            FilteredCategories = new ObservableCollection<CategoryStatistics>();
            
            // Use the new method to properly initialize time periods with translations
            InitializeTimePeriods();
            
            // Initialize with today's data
            _customStartDate = DateTime.Today;
            _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1); // End of today
            _isUsingCustomDateRange = true;
            
            // Create a custom time period for today
            var todayPeriod = new TimePeriod
            {
                DisplayName = TryFindResource("TimePeriod_Today"),
                Type = TimePeriodType.Today,
                Days = 1
            };
            
            // Find an existing "Today" period or use our custom one
            var existingTodayPeriod = TimePeriods.FirstOrDefault(p => p.Type == TimePeriodType.Today);
            SelectedTimePeriod = existingTodayPeriod ?? todayPeriod;
            
            // Set default date display with translation
            var currentDateText = TryFindResource("Current date") ?? "Current date";
            CurrentDateDisplay = $"{currentDateText}: {DateTime.Today:dddd, MMMM d, yyyy}";
        }
        
        /// <summary>
        /// Helper method to find localized resource strings
        /// </summary>
        private string TryFindResource(string key)
        {
            if (string.IsNullOrEmpty(key)) return key;
            
            try
            {
                // Direct lookup for resource keys (used for TimePeriod objects coming from QuickStatsPeriods)
                // This handles cases where a key like "TimePeriod_Today" is directly passed
                if (key.StartsWith("TimePeriod_"))
                {
                    var periodResource = Application.Current.TryFindResource(key);
                    if (periodResource != null)
                    {
                        return periodResource.ToString();
                    }
                }
                
                // For other keys, try a lookup
                var resource = Application.Current.TryFindResource(key);
                return resource as string ?? key;
            }
            catch
            {
                return key;
            }
        }
        
        // Helper method for initializing time periods with proper translation
        private void InitializeTimePeriods()
        {
            // Get periods from dashboard view model
            var periodsFromVM = _dashboardViewModel.QuickStatsPeriods.ToList();
            TimePeriods = new ObservableCollection<TimePeriod>();
            
            // Create new TimePeriod objects with direct resource keys to ensure proper translation
            foreach (var period in periodsFromVM)
            {
                // Get the proper resource key - handle both direct keys and already translated values
                string resourceKey = period.DisplayName;
                if (!resourceKey.StartsWith("TimePeriod_"))
                {
                    // Try to map back to a resource key if it's already translated
                    switch (period.Type)
                    {
                        case TimePeriodType.Today: resourceKey = "TimePeriod_Today"; break;
                        case TimePeriodType.Yesterday: resourceKey = "TimePeriod_Yesterday"; break;
                        case TimePeriodType.Week: resourceKey = "TimePeriod_Last7Days"; break;
                        case TimePeriodType.Month: resourceKey = "TimePeriod_ThisMonth"; break;
                        case TimePeriodType.Year: resourceKey = "TimePeriod_ThisYear"; break;
                        case TimePeriodType.Custom: resourceKey = "TimePeriod_Custom"; break;
                    }
                }
                
                // Look up the translation using the resource key
                var localizedDisplayName = TryFindResource(resourceKey);
                
                // Create a new TimePeriod with the localized display name
                TimePeriods.Add(new TimePeriod
                {
                    DisplayName = localizedDisplayName,
                    Days = period.Days,
                    Type = period.Type,
                    IsCurrentMonth = period.IsCurrentMonth,
                    IsLastMonth = period.IsLastMonth
                });
            }
        }
        
        public ObservableCollection<CategoryStatistics> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }
        
        public ObservableCollection<CategoryStatistics> FilteredCategories
        {
            get => _filteredCategories;
            set
            {
                _filteredCategories = value;
                OnPropertyChanged();
            }
        }
        
        public ObservableCollection<TimePeriod> TimePeriods
        {
            get => _timePeriods;
            set
            {
                _timePeriods = value;
                OnPropertyChanged();
            }
        }
        
        public TimePeriod SelectedTimePeriod
        {
            get => _selectedTimePeriod;
            set
            {
                _selectedTimePeriod = value;
                OnPropertyChanged();
                
                // Update date range based on selected period
                if (value != null)
                {
                    UpdateDateRangeFromPeriod(value);
                }
                
                // Reload data when period changes
                _ = LoadDataAsync();
            }
        }
        
        // Extracted method to make the code more maintainable
        private void UpdateDateRangeFromPeriod(TimePeriod period)
        {
            switch (period.Type)
            {
                case TimePeriodType.Today:
                    _customStartDate = DateTime.Today;
                    _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    var currentDateText = TryFindResource("Current date") ?? "Current date";
                    CurrentDateDisplay = $"{currentDateText}: {DateTime.Today:dddd, MMMM d, yyyy}";
                    break;
                    
                case TimePeriodType.Yesterday:
                    _customStartDate = DateTime.Today.AddDays(-1);
                    _customEndDate = DateTime.Today.AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    var yesterdayDateText = TryFindResource("Current date") ?? "Current date";
                    CurrentDateDisplay = $"{yesterdayDateText}: {_customStartDate:dddd, MMMM d, yyyy}";
                    break;
                    
                case TimePeriodType.Week:
                    // Last 7 days
                    _customStartDate = DateTime.Today.AddDays(-6);
                    _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    var dateRangeText = TryFindResource("Date range") ?? "Date range";
                    CurrentDateDisplay = $"{dateRangeText}: {_customStartDate:MMM d} - {DateTime.Today:MMM d, yyyy}";
                    break;
                    
                case TimePeriodType.Month:
                    // Current month
                    _customStartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    var monthRangeText = TryFindResource("Date range") ?? "Date range";
                    CurrentDateDisplay = $"{monthRangeText}: {_customStartDate:MMM d} - {DateTime.Today:MMM d, yyyy}";
                    break;
                    
                case TimePeriodType.Year:
                    // Current year to date
                    _customStartDate = new DateTime(DateTime.Today.Year, 1, 1);
                    _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    var yearRangeText = TryFindResource("Date range") ?? "Date range";
                    CurrentDateDisplay = $"{yearRangeText}: {_customStartDate:MMM d} - {DateTime.Today:MMM d, yyyy}";
                    break;
                    
                case TimePeriodType.Custom:
                    // Keep existing custom dates
                    if (!_isUsingCustomDateRange)
                    {
                        _customStartDate = DateTime.Today;
                        _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                        _isUsingCustomDateRange = true;
                        var customDateText = TryFindResource("Current date") ?? "Current date";
                        CurrentDateDisplay = $"{customDateText}: {DateTime.Today:dddd, MMMM d, yyyy}";
                    }
                    break;
            }
        }
        
        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
                
                // Filter the categories based on search text
                ApplyFilter();
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }
        
        public string CurrentDateDisplay
        {
            get => _currentDateDisplay;
            set
            {
                _currentDateDisplay = value;
                OnPropertyChanged();
            }
        }
        
        /// <summary>
        /// Loads category statistics from the dashboard view model
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                // Get date range for filtering
                DateTime startDate = _customStartDate ?? DateTime.Today;
                DateTime endDate = _customEndDate ?? DateTime.Today.AddDays(1).AddSeconds(-1); // End of today
                
                Debug.WriteLine($"Loading data for date range: {startDate:MM/dd/yyyy} to {endDate:MM/dd/yyyy}");
                
                // Get actual sales data for the selected date range - use the cached database service
                var sales = await Task.Run(() => _dbService.GetSalesByDateRangePaged(startDate, endDate, 0, int.MaxValue));
                Debug.WriteLine($"Found {sales.Count} sales for date range {startDate:MM/dd/yyyy} to {endDate:MM/dd/yyyy}");
                
                if (sales.Count == 0)
                {
                    // No transactions for this date range
                    Debug.WriteLine("No sales data for selected date range");
                    
                    // Clear all collections
                    await Application.Current.Dispatcher.InvokeAsync(() => {
                        Categories = new ObservableCollection<CategoryStatistics>();
                        FilteredCategories = new ObservableCollection<CategoryStatistics>();
                    });
                    
                    return;
                }
                
                // Get categories and products needed for processing
                var allCategories = await Task.Run(() => _dbService.GetAllCategories());
                var products = await Task.Run(() => _dbService.GetAllProducts());
                
                // Process category statistics from actual transaction data
                var categoryStats = await Task.Run(() => {
                    // Extract all sale items from transactions
                    var allSaleItems = sales.SelectMany(s => s.Items).ToList();
                    Debug.WriteLine($"Processing {allSaleItems.Count} sale items");
                    
                    // Create a dictionary for faster product lookups
                    var productDict = products.ToDictionary(p => p.Id, p => p);
                    
                    // Group items by product category
                    return allSaleItems
                        .GroupBy(item => {
                            // Get the product's category or default to "Uncategorized"
                            string categoryName = "Uncategorized";
                            if (productDict.TryGetValue(item.ProductId, out var product) && product.Category != null)
                            {
                                categoryName = product.Category.Name;
                            }
                            return categoryName;
                        })
                        .Select(g => {
                            // Get the category object
                            var category = allCategories.FirstOrDefault(c => c.Name == g.Key);
                            var categoryName = g.Key; // Using the key as name (already handled null case)
                            
                            // Calculate metrics for this category
                            decimal revenue = g.Sum(item => item.UnitPrice * item.Quantity);
                            decimal cost = g.Sum(item => {
                                // Get product cost
                                return productDict.TryGetValue(item.ProductId, out var product) 
                                    ? product.PurchasePrice * item.Quantity 
                                    : 0;
                            });
                            decimal profit = revenue - cost;
                            decimal margin = revenue > 0 ? Math.Round((profit / revenue) * 100, 2) : 0;
                            decimal itemsSold = g.Sum(item => item.Quantity);
                            
                            return new CategoryStatistics {
                                Id = category?.Id ?? 0,
                                Name = categoryName,
                                Revenue = revenue,
                                Profit = profit,
                                Margin = margin,
                                ItemsSold = itemsSold,
                                ProductCount = g.Select(item => item.ProductId).Distinct().Count()
                            };
                        })
                        .OrderByDescending(c => c.Revenue)
                        .ToList();
                });
                
                // Update UI collections
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    Categories = new ObservableCollection<CategoryStatistics>(categoryStats);
                    
                    // Apply filter to update filtered categories
                    ApplyFilter();
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading category data: {ex.Message}");
                
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    MessageBox.Show(
                        $"Error loading category data: {ex.Message}",
                        "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                    
                    // Clear collections on error
                    Categories = new ObservableCollection<CategoryStatistics>();
                    FilteredCategories = new ObservableCollection<CategoryStatistics>();
                });
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        /// <summary>
        /// Applies the current search filter to categories
        /// </summary>
        private void ApplyFilter()
        {
            if (Categories == null)
                return;
                
            IEnumerable<CategoryStatistics> filtered = Categories;
            
            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var search = SearchText.ToLower().Trim();
                filtered = filtered.Where(c => 
                    c.Name.ToLower().Contains(search));
            }
            
            // Update filtered collection
            FilteredCategories = new ObservableCollection<CategoryStatistics>(filtered);
        }
        
        /// <summary>
        /// Sets a custom date range and refreshes the data
        /// </summary>
        public async Task SetCustomDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            _customStartDate = startDate;
            _customEndDate = endDate;
            _isUsingCustomDateRange = true;
            
            // Create a custom time period
            SelectedTimePeriod = new TimePeriod 
            { 
                DisplayName = $"{startDate:MMM d, yyyy} - {endDate:MMM d, yyyy}",
                Type = TimePeriodType.Custom,
                Days = (int)(endDate - startDate).TotalDays + 1
            };
            
            // Update current date display
            if (startDate.Date == endDate.Date)
            {
                CurrentDateDisplay = $"{TryFindResource("Current date")}: {startDate:dddd, MMMM d, yyyy}";
            }
            else
            {
                CurrentDateDisplay = $"{TryFindResource("Date range")}: {startDate:MMM d} - {endDate:MMM d, yyyy}";
            }
            
            // Reload data with the custom date range
            await LoadDataAsync();
        }
        
        /// <summary>
        /// Navigates to a specific date and refreshes the data
        /// </summary>
        public async Task NavigateToDateAsync(int daysOffset)
        {
            try
            {
                // If offset is 0, go to today
                if (daysOffset == 0)
                {
                    // Today
                    _customStartDate = DateTime.Today;
                    _customEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                    _isUsingCustomDateRange = true;
                    
                    var currentDateText = TryFindResource("Current date") ?? "Current date";
                    CurrentDateDisplay = $"{currentDateText}: {DateTime.Today:dddd, MMMM d, yyyy}";
                    
                    // Find today period or create new one
                    var todayPeriod = TimePeriods.FirstOrDefault(p => p.Type == TimePeriodType.Today);
                    if (todayPeriod != null)
                    {
                        SelectedTimePeriod = todayPeriod;
                    }
                    else
                    {
                        SelectedTimePeriod = new TimePeriod
                        {
                            DisplayName = TryFindResource("TimePeriod_Today"),
                            Type = TimePeriodType.Today,
                            Days = 1
                        };
                    }
                    
                    await LoadDataAsync();
                    return;
                }
                
                // Calculate the new date
                DateTime newDate;
                
                if (_customStartDate.HasValue)
                {
                    // Use current custom date as base
                    newDate = _customStartDate.Value.AddDays(daysOffset);
                }
                else
                {
                    // Default to today if no custom date set
                    newDate = DateTime.Today.AddDays(daysOffset);
                }
                
                // Create a custom time period for the new date
                SelectedTimePeriod = new TimePeriod
                {
                    DisplayName = $"{newDate:MMM d, yyyy}",
                    Type = TimePeriodType.Custom,
                    Days = 1 // Single day
                };
                
                // Set custom date range internally
                _customStartDate = newDate;
                _customEndDate = newDate.AddDays(1).AddSeconds(-1); // End of the same day
                _isUsingCustomDateRange = true;
                
                // Update current date display
                var dateText = TryFindResource("Current date") ?? "Current date";
                CurrentDateDisplay = $"{dateText}: {newDate:dddd, MMMM d, yyyy}";
                
                // Reload data for the new date
                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to date: {ex.Message}");
            }
        }
        
        #region INotifyPropertyChanged
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        #endregion
    }
    
    /// <summary>
    /// Model for category statistics
    /// </summary>
    public class CategoryStatistics
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal Revenue { get; set; }
        public decimal Profit { get; set; }
        public decimal Margin { get; set; }
        public decimal ItemsSold { get; set; }
        public int ProductCount { get; set; }
    }
} 