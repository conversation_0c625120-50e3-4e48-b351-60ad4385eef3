using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for AdminDraftCompletionDialog.xaml
    /// </summary>
    public partial class AdminDraftCompletionDialog : UserControl
    {
        public AdminDraftCompletionDialog()
        {
            InitializeComponent();
        }

        public AdminDraftCompletionDialog(Invoice draftInvoice) : this()
        {
            // Create ViewModel with the draft invoice
            // Note: In a real application, these services would be injected via DI
            var draftInvoiceService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<DraftInvoiceService>();
            var authService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<AuthenticationService>();
            var permissionsService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<UserPermissionsService>();

            if (draftInvoiceService != null && authService != null && permissionsService != null)
            {
                var viewModel = new AdminDraftCompletionViewModel(draftInvoice, draftInvoiceService, authService, permissionsService);
                DataContext = viewModel;

                // Subscribe to the RequestClose event
                viewModel.RequestClose += OnRequestClose;
            }
        }

        public AdminDraftCompletionDialog(AdminDraftCompletionViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to the RequestClose event
            if (viewModel != null)
            {
                viewModel.RequestClose += OnRequestClose;
            }
        }

        private void OnRequestClose()
        {
            // This will be handled by the dialog host (MaterialDesign DialogHost)
            // The dialog result will be available through the ViewModel's DialogResult property
        }

        /// <summary>
        /// Gets the dialog result from the ViewModel
        /// </summary>
        public bool? DialogResult => (DataContext as AdminDraftCompletionViewModel)?.DialogResult;
    }
}
