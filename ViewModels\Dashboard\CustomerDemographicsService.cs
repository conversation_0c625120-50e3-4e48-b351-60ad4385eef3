using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using POSSystem.Models;
using LiveCharts;
using LiveCharts.Wpf;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Service responsible for customer demographics data and analysis.
    /// </summary>
    public class CustomerDemographicsService
    {
        private readonly IDashboardDataProvider _dataProvider;
        private readonly Random _random = new Random(); // For random colors

        public CustomerDemographicsService(IDashboardDataProvider dataProvider)
        {
            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
        }

        /// <summary>
        /// Loads customer demographics data
        /// </summary>
        public async Task<ObservableCollection<CustomerDemographic>> LoadCustomerDemographicsAsync()
        {
            // Get customers and their sales
            var customers = await _dataProvider.GetCustomersAsync();
            var startDate = DateTime.Now.AddYears(-1);
            var endDate = DateTime.Now;

            // Create customer demographics list (not ObservableCollection on background thread)
            var demographicsList = new List<CustomerDemographic>();
            var categories = new Dictionary<string, CustomerDemographic>();
            
            foreach (var customer in customers)
            {
                // Get customer sales
                if (customer.Id <= 0) continue;
                
                var sales = await _dataProvider.GetCustomerSalesAsync(customer.Id, startDate, endDate);
                if (sales == null || !sales.Any()) continue;
                
                // Determine customer category
                var category = DetermineCustomerCategory(customer, sales);
                
                // Add to existing category or create new one
                if (!categories.TryGetValue(category, out var demographic))
                {
                    demographic = new CustomerDemographic
                    {
                        Category = category,
                        Count = 0,
                        TotalSpent = 0,
                        Color = GenerateRandomBrush()
                    };
                    categories[category] = demographic;
                    demographicsList.Add(demographic);
                }

                // Update category stats
                demographic.Count++;
                demographic.TotalSpent += sales.Sum(s => s.GrandTotal);
            }

            // Create ObservableCollection on UI thread
            return await Application.Current.Dispatcher.InvokeAsync(() =>
                new ObservableCollection<CustomerDemographic>(demographicsList));
        }
        
        /// <summary>
        /// Creates a pie chart series for customer demographics
        /// </summary>
        public SeriesCollection CreateCustomerDemographicsSeries(ObservableCollection<CustomerDemographic> demographics)
        {
            var series = new SeriesCollection();
            
            if (demographics == null || !demographics.Any())
                return series;
                
            var pieSeries = new PieSeries
            {
                Title = "Customer Demographics",
                Values = new ChartValues<decimal>(demographics.Select(d => d.TotalSpent)),
                DataLabels = true,
                LabelPoint = chartPoint => $"{demographics[(int)chartPoint.Key].Category}: {chartPoint.Participation:P1}"
            };
            
            series.Add(pieSeries);
            return series;
        }
        
        /// <summary>
        /// Determines a customer's category based on sales data
        /// </summary>
        private string DetermineCustomerCategory(Customer customer, IEnumerable<Sale> sales)
        {
            // Calculate metrics
            var totalSpent = sales.Sum(s => s.GrandTotal);
            var orderCount = sales.Count();
            var avgOrderValue = orderCount > 0 ? totalSpent / orderCount : 0;
            
            // Determine category
            if (totalSpent > 10000)
                return "VIP";
            else if (totalSpent > 5000)
                return "High Value";
            else if (totalSpent > 1000)
                return "Regular";
            else if (avgOrderValue > 500)
                return "Big Ticket";
            else if (orderCount > 10)
                return "Frequent";
            else if (orderCount == 1)
                return "One-time";
            else
                return "Occasional";
        }
        
        /// <summary>
        /// Generates a random brush for charts
        /// </summary>
        private SolidColorBrush GenerateRandomBrush()
        {
            byte[] colorBytes = new byte[3];
            _random.NextBytes(colorBytes);
            
            return new SolidColorBrush(
                Color.FromRgb(
                    Math.Max((byte)50, colorBytes[0]), 
                    Math.Max((byte)50, colorBytes[1]), 
                    Math.Max((byte)50, colorBytes[2])
                )
            );
        }

        /// <summary>
        /// Gets detailed information for a specific customer category
        /// </summary>
        public async Task<CustomerDemographic> GetCategoryDetailsAsync(CustomerDemographic category)
        {
            if (category == null) return null;

            try
            {
                // Get all customers in this category
                var customers = await _dataProvider.GetCustomersAsync();
                var startDate = DateTime.Now.AddYears(-1);
                var endDate = DateTime.Now;
                
                decimal totalRevenue = 0;
                int totalTransactions = 0;
                var customerCount = 0;

                foreach (var customer in customers)
                {
                    var sales = await _dataProvider.GetCustomerSalesAsync(customer.Id, startDate, endDate);
                    if (sales == null || !sales.Any()) continue;

                    var customerCategory = DetermineCustomerCategory(customer, sales);
                    if (customerCategory != category.Category) continue;

                    customerCount++;
                    totalRevenue += sales.Sum(s => s.GrandTotal);
                    totalTransactions += sales.Count;
                }

                return new CustomerDemographic
                {
                    Category = category.Category,
                    Count = customerCount,
                    TotalSpent = totalRevenue,
                    Color = category.Color,
                    TransactionCount = totalTransactions,
                    AverageTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting category details: {ex.Message}");
                return null;
            }
        }
    }
    
    /// <summary>
    /// Represents a customer demographic category
    /// </summary>
    public class CustomerDemographic
    {
        public string Category { get; set; }
        public int Count { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal AverageSpent => Count > 0 ? TotalSpent / Count : 0;
        public SolidColorBrush Color { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }
} 