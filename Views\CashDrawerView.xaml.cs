using System.Windows.Controls;
using System.Windows;
using POSSystem.ViewModels;

namespace POSSystem.Views
{
    public partial class CashDrawerView : UserControl
    {
        public CashDrawerView()
        {
            InitializeComponent();
        }

        private void ViewHistory_Click(object sender, RoutedEventArgs e)
        {
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.NavigateToView(new CashDrawerHistoryView());
            }
        }
    }
} 