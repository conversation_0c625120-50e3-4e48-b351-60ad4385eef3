<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.BarcodeManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource BarcodeManagement}"
        Width="600" Height="500"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignBackground}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Text="{DynamicResource BarcodeManagement}"
                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                     Margin="0,0,0,8"/>
            <TextBlock x:Name="txtProductInfo"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     Opacity="0.6"/>
        </StackPanel>

        <!-- Barcodes List -->
        <md:Card Grid.Row="1" Margin="0,0,0,16">
            <DockPanel>
                <!-- Add Barcode Section -->
                <Grid DockPanel.Dock="Top" Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtBarcode"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            md:HintAssist.Hint="{DynamicResource BarcodeValue}"
                            Margin="0,0,8,0"/>

                    <Button Grid.Column="1"
                            Content="{DynamicResource Generate}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="GenerateBarcode_Click"
                            Margin="0,0,8,0"/>

                    <Button Grid.Column="2"
                            Content="{DynamicResource Add}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="AddBarcode_Click"/>
                </Grid>

                <!-- Barcodes List -->
                <DataGrid x:Name="BarcodesList"
                         Style="{StaticResource MaterialDesignDataGrid}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         Margin="16">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="{DynamicResource Barcode}" 
                                          Binding="{Binding Barcode}"
                                          Width="*"/>
                        <DataGridTemplateColumn Header="{DynamicResource Primary}" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <RadioButton IsChecked="{Binding IsPrimary, UpdateSourceTrigger=PropertyChanged}"
                                               Click="SetPrimaryBarcode_Click"
                                               GroupName="PrimaryBarcodeGroup"
                                               HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="{DynamicResource Description}" 
                                          Binding="{Binding Description}"
                                          Width="150"/>
                        <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            Click="RemoveBarcode_Click"
                                            ToolTip="{DynamicResource RemoveBarcode}">
                                        <md:PackIcon Kind="Delete"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </md:Card>

        <!-- Actions -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="{DynamicResource Save}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="SaveChanges_Click"
                    Margin="0,0,8,0"/>
            <Button Content="{DynamicResource Cancel}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window> 