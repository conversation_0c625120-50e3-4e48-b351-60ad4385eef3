using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class CategoryButtonTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // If Tag is null, it's in "Add" mode, otherwise it's in "Update" mode
            bool isUpdateMode = value != null;
            
            var resourceKey = isUpdateMode ? "UpdateCategory" : "AddCategory";
            return Application.Current.TryFindResource(resourceKey) as string ?? resourceKey;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 