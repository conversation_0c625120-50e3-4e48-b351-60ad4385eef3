-- Remove PointsCalculationMethod column from LoyaltyPrograms table
-- First create a backup of the table
CREATE TABLE LoyaltyPrograms_Backup AS SELECT 
    Id, 
    Name, 
    Description, 
    PointsPerDollar, 
    MonetaryValuePerPoint, 
    ExpiryMonths, 
    MinimumPointsRedemption, 
    IsActive, 
    CreatedAt 
FROM LoyaltyPrograms;

-- Drop the original table
DROP TABLE LoyaltyPrograms;

-- Recreate the table without the PointsCalculationMethod column
CREATE TABLE LoyaltyPrograms (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    PointsPerDollar REAL NOT NULL,
    MonetaryValuePerPoint REAL NOT NULL,
    ExpiryMonths INTEGER NOT NULL,
    MinimumPointsRedemption REAL NOT NULL,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL
);

-- Restore the data
INSERT INTO LoyaltyPrograms (
    Id, 
    Name, 
    Description, 
    PointsPerDollar, 
    MonetaryV<PERSON>uePerPoint, 
    ExpiryMonths, 
    MinimumPointsRedemption, 
    IsActive, 
    CreatedAt
) 
SELECT 
    Id, 
    Name, 
    Description, 
    PointsPerDollar, 
    MonetaryValuePerPoint, 
    ExpiryMonths, 
    MinimumPointsRedemption, 
    IsActive, 
    CreatedAt 
FROM LoyaltyPrograms_Backup;

-- Drop the backup table
DROP TABLE LoyaltyPrograms_Backup; 