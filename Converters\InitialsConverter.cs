using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    /// <summary>
    /// Converts a person's name to their initials (first letter)
    /// </summary>
    public class InitialsConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return "?";

            string text = value.ToString();
            if (string.IsNullOrWhiteSpace(text))
                return "?";

            // Just return the first character of the name
            return text.Substring(0, 1).ToUpper();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 