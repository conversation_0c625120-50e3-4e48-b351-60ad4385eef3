using System.Windows.Controls;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    public partial class ExpensesStatsDetailsDialog : UserControl
    {
        private readonly ExpensesStatsDetailsViewModel _viewModel;
        
        public ExpensesStatsDetailsDialog(RefactoredDashboardViewModel dashboardViewModel)
        {
            InitializeComponent();
            var dbService = new DatabaseService();
            _viewModel = new ExpensesStatsDetailsViewModel(dashboardViewModel, dbService);
            DataContext = _viewModel;
            
            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }
        
        private void CloseButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            DialogHost.CloseDialogCommand.Execute(null, null);
        }
    }
} 