# Changelog

## [Unreleased]

### Changed
- Updated currency format to support Algerian Dinar (DA):
  - Set DA as the default currency symbol
  - Modified number format to display values as "123.45 DA"
  - Updated currency settings in App.xaml.cs for consistent formatting
  - Added proper currency patterns for positive and negative values
  - Set Algerian Dinar as default in Settings view
  - Updated CurrencyConverter for consistent DA display
- Improved Business Expense dialog UI and functionality:
  - Increased dialog size for better visibility (1000x700)
  - Added proper MaterialDesign styling to the DataGrid
  - Improved data loading with loading indicator
  - Added alternating row colors and grid lines
  - Fixed dialog close functionality with both header and footer buttons
  - Added proper cell padding and formatting
  - Improved async data loading and UI updates

## [1.0.0] - 2024-03-XX

### Added
- Initial release of the POS System
- Modern Material Design UI
- Real-time notification system
- Dashboard with metrics
- Product management
- Customer management
- Supplier management
- Sales processing
- Reporting system
- Barcode printing with multiple formats (EAN-13, Code 128, QR Code)
- Cash Drawer management with transaction history
- Cash Drawer reconciliation system
- Improved navigation between Cash Drawer and History views

### Fixed
- Notification count updates
- Alert icons compatibility
- Resource cleanup
- Database operations
- Cash Drawer navigation accessibility
- Transaction history display and filtering

### Changed
- Updated UI packages
- Improved notifications
- Enhanced alerts
- Optimized performance
- Removed tax calculation and display from the sales system
- Removed SKU display from cart items
- Removed column headers (Product and Qty) from cart items grid
- Simplified cart display for better user experience
- Moved Cash Drawer History access to within Cash Drawer view
- Streamlined Cash Drawer navigation

### Security
- Implemented BCrypt password hashing
- Secure password storage and verification
- Password migration system for existing users
- Role-based access control
- User authentication
- Input validation

### Fixed
- Fixed NullReferenceException in license activation by properly handling Application.Current?.Dispatcher
- Improved application restart process after license activation
- Added proper error handling for application restart failures
- Enhanced window management during license activation process

### Changed
- Updated license activation UI flow to include OK/Cancel option for restart
- Improved activation success message to be more informative
- Modified window closing logic to use LINQ for better reliability
- Added graceful fallback for manual restart when automatic restart fails

### Security
- Maintained consistent encryption key size (32 bytes) for AES-256 encryption
- Added proper cleanup of activation window on successful license activation

### Technical
- Improved error logging for license activation process
- Added additional debug information for troubleshooting activation issues
- Enhanced hardware ID handling with proper base64 padding removal
