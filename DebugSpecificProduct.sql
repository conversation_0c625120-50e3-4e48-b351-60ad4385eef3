-- Debug Script: Check Specific Product (ID 483)
-- Run this in SQLite DB Browser to debug why the product isn't appearing in search

-- 1. Check the specific product details
SELECT 'Product Details for ID 483:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    Description,
    IsActive,
    IsWeightBased,
    Type,
    StockQuantity,
    CategoryId
FROM Products 
WHERE Id = 483;

-- 2. Check if the product has any search-friendly content
SELECT 'Search Content Analysis:' as Info;
SELECT 
    Id,
    'Name: "' || COALESCE(Name, 'NULL') || '"' as NameContent,
    'SKU: "' || COALESCE(SKU, 'NULL') || '"' as SKUContent,
    'Description: "' || COALESCE(Description, 'NULL') || '"' as DescContent,
    CASE 
        WHEN Name IS NULL OR Name = '' THEN 'Name is empty/null'
        WHEN SKU IS NULL OR SKU = '' THEN 'SKU is empty/null'
        ELSE 'Has searchable content'
    END as SearchabilityStatus
FROM Products 
WHERE Id = 483;

-- 3. Test different search scenarios
SELECT 'Testing search for "pw1":' as TestInfo;
SELECT Id, Name, SKU, IsWeightBased, IsActive
FROM Products 
WHERE (Name LIKE '%pw1%' OR SKU LIKE '%pw1%' OR Description LIKE '%pw1%')
  AND IsActive = 1;

-- 4. Test case-insensitive search
SELECT 'Testing case-insensitive search for "PW1":' as TestInfo;
SELECT Id, Name, SKU, IsWeightBased, IsActive
FROM Products 
WHERE (UPPER(Name) LIKE '%PW1%' OR UPPER(SKU) LIKE '%PW1%' OR UPPER(Description) LIKE '%PW1%')
  AND IsActive = 1;

-- 5. Check if there are any other products that would appear in search
SELECT 'All active products with names:' as Info;
SELECT Id, Name, SKU, IsWeightBased, IsActive
FROM Products 
WHERE IsActive = 1 
  AND (Name IS NOT NULL AND Name != '')
ORDER BY Id DESC
LIMIT 10;

-- 6. Check for barcodes on this product
SELECT 'Barcodes for product 483:' as Info;
SELECT pb.Barcode, pb.IsPrimary, pb.Description
FROM ProductBarcodes pb
WHERE pb.ProductId = 483;

-- 7. Check the category
SELECT 'Category information:' as Info;
SELECT 
    p.Id as ProductId,
    p.Name as ProductName,
    p.CategoryId,
    c.Name as CategoryName
FROM Products p
LEFT JOIN Categories c ON p.CategoryId = c.Id
WHERE p.Id = 483;

-- 8. Test the exact search query that the application might use
SELECT 'Simulating application search query:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    CategoryId,
    SellingPrice,
    StockQuantity,
    IsActive,
    IsWeightBased,
    Type
FROM Products
WHERE IsActive = 1 
  AND (Name LIKE '%pw1%' OR SKU LIKE '%pw1%' OR Description LIKE '%pw1%');

-- 9. Check if there are any NULL or problematic values
SELECT 'Data quality check:' as Info;
SELECT 
    Id,
    CASE WHEN Name IS NULL THEN 'Name is NULL' 
         WHEN Name = '' THEN 'Name is empty' 
         ELSE 'Name OK: ' || Name END as NameStatus,
    CASE WHEN SKU IS NULL THEN 'SKU is NULL' 
         WHEN SKU = '' THEN 'SKU is empty' 
         ELSE 'SKU OK: ' || SKU END as SKUStatus,
    CASE WHEN IsActive = 1 THEN 'Active' ELSE 'Inactive' END as ActiveStatus,
    CASE WHEN IsWeightBased = 1 THEN 'Weight-based' ELSE 'Unit-based' END as TypeStatus
FROM Products 
WHERE Id = 483;
