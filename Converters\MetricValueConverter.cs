using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.ViewModels;

namespace POSSystem.Converters
{
    public class MetricValueConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length != 2 || values[0] == null || values[1] == null)
                return "0";

            // ✅ CRITICAL FIX: Safe conversion to prevent FormatException
            decimal value;
            try
            {
                value = System.Convert.ToDecimal(values[0]);
            }
            catch (FormatException)
            {
                // Handle invalid format gracefully
                System.Diagnostics.Debug.WriteLine($"[CONVERTER] Invalid format in MetricValueConverter: {values[0]}");
                return "0";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CONVERTER] Error in MetricValueConverter: {ex.Message}");
                return "0";
            }

            string metricKey = values[1]?.ToString() ?? "";

            switch (metricKey.ToLower())
            {
                case "avg_transaction":
                case "revenue_per_customer":
                    return $"{value:N2} DA";
                case "customer_count":
                case "items_sold":
                    return value.ToString("N0");
                default:
                    return $"{value:N2} DA";
            }
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 