using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using POSSystem.Services.InventoryManagement;

namespace POSSystem.Views
{
    public partial class InvoicesView : UserControl
    {
        public InvoicesView()
        {
            InitializeComponent();

            // Get the ViewModel from DI container with proper error handling
            try
            {
                DataContext = App.ServiceProvider?.GetService<InvoiceViewModel>() ??
                             throw new InvalidOperationException("InvoiceViewModel not available from DI container. Ensure ServiceConfiguration is properly initialized.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICESVIEW] Error getting InvoiceViewModel from DI: {ex.Message}");
                // Fallback: create manually with required services
                var dbService = App.ServiceProvider?.GetService<IDatabaseService>() ?? new DatabaseService();
                var authService = App.ServiceProvider?.GetService<IAuthenticationService>() ??
                                 new AuthenticationService(dbService);
                var stockService = App.ServiceProvider?.GetService<IStockService>() ?? new POSSystem.Services.InventoryManagement.StockService(dbService);
                DataContext = new InvoiceViewModel(dbService, authService, stockService);
            }
        }
    }
} 