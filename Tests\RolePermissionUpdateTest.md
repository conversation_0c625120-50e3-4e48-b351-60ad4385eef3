# Role-Based Permission Update Test Plan

## Overview
This test plan validates that the role selection automatically updates permission checkboxes with appropriate default values in the UserDialog.

## Test Scenarios

### Test 1: New User Creation - Role Selection Updates Permissions
**Objective**: Verify that selecting different roles automatically updates permission checkboxes

**Steps**:
1. Open Users management
2. Click "Add User" button
3. Fill in basic user information (username, name, email)
4. Observe the initial permission checkboxes (should show Admin defaults)
5. Change role selection to "Manager"
6. Observe permission checkboxes update automatically
7. Change role selection to "Cashier"
8. Observe permission checkboxes update automatically
9. Change back to "Admin"
10. Observe permission checkboxes update automatically

**Expected Results**:
- **Admin Role**: All permissions should be checked (enabled)
- **Manager Role**: Most permissions checked except user management
- **Cashier Role**: Limited permissions (sales, view products, view customers)
- Permission changes should happen immediately when role is selected
- Debug output should show permission loading messages

### Test 2: Edit Mode - Permissions Preserved
**Objective**: Verify that existing user permissions are not automatically overwritten in edit mode

**Steps**:
1. Create a user with custom permissions (different from role defaults)
2. Save the user
3. Edit the same user
4. Change the role selection
5. Verify that custom permissions are preserved (not reset to role defaults)

**Expected Results**:
- Custom permissions should remain unchanged when role is changed in edit mode
- Debug output should show "Edit mode: Not automatically updating permissions"
- User can still manually adjust permissions if needed

### Test 3: Permission Reset Functionality
**Objective**: Test the ResetPermissionsToRoleDefaults method

**Steps**:
1. Create or edit a user
2. Modify some permissions manually
3. Call ResetPermissionsToRoleDefaults() method (if UI button is available)
4. Confirm the reset action
5. Verify permissions are reset to role defaults

**Expected Results**:
- Confirmation dialog should appear
- After confirmation, permissions should reset to role defaults
- Debug output should show "Permissions reset to role defaults"

### Test 4: Role-Specific Permission Patterns
**Objective**: Verify that each role has appropriate default permissions

**Admin Role Expected Permissions**:
- ✅ CanCreateSales: true
- ✅ CanVoidSales: true
- ✅ CanApplyDiscount: true
- ✅ CanViewSalesHistory: true
- ✅ CanManageProducts: true
- ✅ CanManageCategories: true
- ✅ CanViewInventory: true
- ✅ CanAdjustInventory: true
- ✅ CanManageExpenses: true
- ✅ CanManageCashDrawer: true
- ✅ CanViewReports: true
- ✅ CanManagePrices: true
- ✅ CanManageCustomers: true
- ✅ CanManageSuppliers: true
- ✅ CanManageUsers: true
- ✅ CanManageRoles: true
- ✅ CanAccessSettings: true
- ✅ CanViewLogs: true

**Manager Role Expected Permissions**:
- ✅ CanCreateSales: true
- ✅ CanVoidSales: true
- ✅ CanApplyDiscount: true
- ✅ CanViewSalesHistory: true
- ✅ CanManageProducts: true
- ❌ CanManageCategories: false (Admin only)
- ✅ CanViewInventory: true
- ✅ CanAdjustInventory: true
- ❌ CanManageExpenses: false (Admin only)
- ✅ CanManageCashDrawer: true
- ✅ CanViewReports: true
- ❌ CanManagePrices: false (Admin only)
- ✅ CanManageCustomers: true
- ❌ CanManageSuppliers: false (Admin only)
- ❌ CanManageUsers: false (Admin only)
- ❌ CanManageRoles: false (Admin only)
- ❌ CanAccessSettings: false (Admin only)
- ❌ CanViewLogs: false (Admin only)

**Cashier Role Expected Permissions**:
- ✅ CanCreateSales: true
- ❌ CanVoidSales: false
- ❌ CanApplyDiscount: false
- ✅ CanViewSalesHistory: true
- ❌ CanManageProducts: false
- ❌ CanManageCategories: false
- ✅ CanViewInventory: true
- ❌ CanAdjustInventory: false
- ❌ CanManageExpenses: false
- ❌ CanManageCashDrawer: false
- ❌ CanViewReports: false
- ❌ CanManagePrices: false
- ❌ CanManageCustomers: false
- ❌ CanManageSuppliers: false
- ❌ CanManageUsers: false
- ❌ CanManageRoles: false
- ❌ CanAccessSettings: false
- ❌ CanViewLogs: false

### Test 5: Error Handling
**Objective**: Verify graceful handling of edge cases

**Steps**:
1. Test with null role selection
2. Test with invalid role data
3. Test when permissions control is not initialized

**Expected Results**:
- No crashes or exceptions
- Appropriate debug messages for error conditions
- System remains stable

## Manual Testing Checklist

- [ ] Role selection immediately updates permission checkboxes in new user mode
- [ ] Admin role enables all permissions
- [ ] Manager role enables appropriate subset of permissions
- [ ] Cashier role enables minimal permissions
- [ ] Edit mode preserves existing custom permissions
- [ ] Role changes in edit mode don't automatically reset permissions
- [ ] Debug output shows appropriate messages
- [ ] No errors or exceptions during role changes
- [ ] Permission patterns match expected role-based defaults
- [ ] System handles edge cases gracefully

## Automated Testing Code

```csharp
// Example test code to validate role-based permissions
var permissionsService = new UserPermissionsService(new DatabaseService());

// Test Admin permissions (Role ID 1)
var adminPermissions = permissionsService.CreateDefaultPermissions(0, 1);
Console.WriteLine($"Admin - CanManageUsers: {adminPermissions.CanManageUsers}"); // Should be true
Console.WriteLine($"Admin - CanManageProducts: {adminPermissions.CanManageProducts}"); // Should be true

// Test Manager permissions (Role ID 2)
var managerPermissions = permissionsService.CreateDefaultPermissions(0, 2);
Console.WriteLine($"Manager - CanManageUsers: {managerPermissions.CanManageUsers}"); // Should be false
Console.WriteLine($"Manager - CanManageProducts: {managerPermissions.CanManageProducts}"); // Should be true

// Test Cashier permissions (Role ID 3)
var cashierPermissions = permissionsService.CreateDefaultPermissions(0, 3);
Console.WriteLine($"Cashier - CanManageUsers: {cashierPermissions.CanManageUsers}"); // Should be false
Console.WriteLine($"Cashier - CanCreateSales: {cashierPermissions.CanCreateSales}"); // Should be true
```

## Success Criteria

✅ **Role Selection Updates**: Permission checkboxes update immediately when role is changed
✅ **Appropriate Defaults**: Each role shows correct default permissions
✅ **Edit Mode Preservation**: Existing permissions are preserved in edit mode
✅ **Visual Feedback**: Users can see what permissions each role provides
✅ **No Errors**: System handles all scenarios without crashes
✅ **Debug Logging**: Appropriate debug messages for troubleshooting

## Integration with Custom Permissions Feature

This role-based permission update feature enhances the custom permissions system by:

1. **Providing Starting Points**: Users see appropriate defaults for each role
2. **Visual Guidance**: Clear indication of what each role typically allows
3. **Customization Flexibility**: Users can still modify permissions after seeing defaults
4. **Consistency**: Ensures role-based permissions are applied consistently
5. **User Experience**: Immediate feedback when selecting different roles
