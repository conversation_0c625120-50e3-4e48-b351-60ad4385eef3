<Window x:Class="POSLicensingSystem.Views.LicenseGeneratorView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:POSLicensingSystem.ViewModels"
        xmlns:data="clr-namespace:System.Windows.Data;assembly=PresentationFramework"
        xmlns:converters="clr-namespace:POSLicensingSystem.Converters"
        mc:Ignorable="d"
        Title="License Key Generator" Height="600" Width="650"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        Icon="{StaticResource AppIconImage}"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <converters:BooleanInverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.DataContext>
        <viewmodels:LicenseGeneratorViewModel />
    </Window.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" 
                   Text="POS System License Key Generator" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,0,0,20">
                <!-- Business Name -->
                <TextBox Text="{Binding BusinessName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="Business Name"
                         IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                         Margin="0,0,0,20"/>

                <!-- System ID -->
                <TextBox Text="{Binding SystemId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="System ID (provided by customer)"
                         IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                         Margin="0,0,0,20"/>

                <!-- License Type -->
                <ComboBox ItemsSource="{Binding LicenseTypes}"
                          SelectedItem="{Binding LicenseType}"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          materialDesign:HintAssist.Hint="License Type"
                          IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                          Margin="0,0,0,20"/>

                <!-- Terminal Count -->
                <TextBox Text="{Binding TerminalCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="Terminal Count (1-9)"
                         IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                         Margin="0,0,0,20"/>

                <!-- Expiration Date -->
                <DatePicker SelectedDate="{Binding ExpirationDate, Mode=TwoWay}"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            materialDesign:HintAssist.Hint="Expiration Date"
                            IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                            Margin="0,0,0,20"/>
                            
                <!-- Error Message -->
                <materialDesign:Card 
                    Background="#FFEBEE"
                    Padding="8"
                    Margin="0,10,0,0"
                    Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Alert" Foreground="#D32F2F" VerticalAlignment="Top" Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding ErrorMessage}"
                                   Foreground="#D32F2F"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Generated License Key -->
        <GroupBox Grid.Row="2" 
                  Header="Generated License Key" 
                  Style="{StaticResource MaterialDesignGroupBox}"
                  Margin="0,0,0,20">
            <Grid Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         Text="{Binding GeneratedLicenseKey, Mode=TwoWay}"
                         IsReadOnly="True"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Height="80"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,10,0"/>
                
                <Button Grid.Column="1" 
                        Content="Copy" 
                        Command="{Binding CopyLicenseKeyCommand}"
                        IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"/>
            </Grid>
        </GroupBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="Close"
                    Command="{Binding CloseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsEnabled="{Binding IsGenerating, Converter={StaticResource BooleanInverter}}"
                    Margin="0,0,10,0"/>
            
            <!-- Normal Generate button (shown when not generating) -->
            <Button Content="Generate License Key"
                    Command="{Binding GenerateLicenseCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Visibility="{Binding IsGenerating, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
            
            <!-- Progress button (shown when generating) -->
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                    IsEnabled="False"
                    Visibility="{Binding IsGenerating, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Loading" Margin="0,0,8,0">
                        <materialDesign:PackIcon.RenderTransform>
                            <RotateTransform x:Name="pacIconRotation" Angle="0" />
                        </materialDesign:PackIcon.RenderTransform>
                    </materialDesign:PackIcon>
                    <TextBlock Text="Generating..."/>
                </StackPanel>
                <Button.Triggers>
                    <EventTrigger RoutedEvent="Button.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation
                                    Storyboard.TargetName="pacIconRotation"
                                    Storyboard.TargetProperty="Angle"
                                    From="0"
                                    To="360"
                                    Duration="0:0:1"
                                    RepeatBehavior="Forever" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Button.Triggers>
            </Button>
        </StackPanel>
    </Grid>
</Window> 