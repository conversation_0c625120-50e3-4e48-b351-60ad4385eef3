using System;
using System.Globalization;
using Xunit;
using FluentAssertions;
using POSSystem.Views.Dialogs;

namespace POSSystem.Tests.Converters
{
    public class HistoricalPriceConverterTests
    {
        private readonly HistoricalPriceConverter _converter;

        public HistoricalPriceConverterTests()
        {
            _converter = new HistoricalPriceConverter();
        }

        [Fact]
        public void Convert_WithMatchingCalculatedAndStoredPrice_ShouldReturnStoredPrice()
        {
            // Arrange
            decimal total = 20.00m;
            decimal quantity = 2.0m;
            decimal storedUnitPrice = 10.00m;
            object[] values = { total, quantity, storedUnitPrice };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(10.00m, "because the calculated price matches the stored price");
        }

        [Fact]
        public void Convert_WithDifferentCalculatedAndStoredPrice_ShouldReturnCalculatedPrice()
        {
            // Arrange - This simulates the exact pricing issue from the screenshot
            // Individual line item shows: 1.0 × 130.00 DA = 130.00 DA
            // But subtotal shows: 120.00 DA (incorrect)
            // This test verifies our converter fixes the display issue
            decimal total = 130.00m;        // Actual total charged (correct from line item)
            decimal quantity = 1.0m;        // Quantity sold
            decimal storedUnitPrice = 120.00m; // Wrong stored unit price (matches incorrect subtotal)
            object[] values = { total, quantity, storedUnitPrice };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(130.00m, "because the calculated price from total should be used when it differs significantly from stored price");
        }

        [Fact]
        public void Convert_WithZeroQuantity_ShouldReturnStoredPrice()
        {
            // Arrange
            decimal total = 10.00m;
            decimal quantity = 0.0m;
            decimal storedUnitPrice = 5.00m;
            object[] values = { total, quantity, storedUnitPrice };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(5.00m, "because division by zero should be avoided and stored price used as fallback");
        }

        [Fact]
        public void Convert_WithSmallRoundingDifference_ShouldReturnStoredPrice()
        {
            // Arrange - Small rounding difference within tolerance
            decimal total = 10.001m;
            decimal quantity = 1.0m;
            decimal storedUnitPrice = 10.00m;
            object[] values = { total, quantity, storedUnitPrice };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(10.00m, "because small rounding differences should use stored price for display consistency");
        }

        [Fact]
        public void Convert_WithInvalidValues_ShouldReturnFallback()
        {
            // Arrange
            object[] values = { "invalid", "values", 5.00m };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(5.00m, "because invalid values should fallback to stored unit price");
        }

        [Fact]
        public void Convert_WithInsufficientValues_ShouldReturnZero()
        {
            // Arrange
            object[] values = { 10.00m };

            // Act
            var result = _converter.Convert(values, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            result.Should().Be(0m, "because insufficient values should return zero");
        }

        [Fact]
        public void ConvertBack_ShouldThrowNotImplementedException()
        {
            // Act & Assert
            Action act = () => _converter.ConvertBack(10.00m, new Type[] { typeof(decimal) }, null, CultureInfo.InvariantCulture);
            act.Should().Throw<NotImplementedException>("because ConvertBack is not supported");
        }
    }
}
