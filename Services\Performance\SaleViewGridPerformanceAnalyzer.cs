using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Threading;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// Comprehensive performance analyzer for SaleViewGrid component
    /// Validates recent optimizations and measures key performance metrics
    /// </summary>
    public class SaleViewGridPerformanceAnalyzer
    {
        private readonly Stopwatch _stopwatch = new Stopwatch();
        private readonly List<PerformanceMetric> _metrics = new List<PerformanceMetric>();
        private readonly PerformanceCounter _memoryCounter;
        private readonly PerformanceCounter _cpuCounter;

        public SaleViewGridPerformanceAnalyzer()
        {
            try
            {
                _memoryCounter = new PerformanceCounter("Process", "Working Set", Process.GetCurrentProcess().ProcessName);
                _cpuCounter = new PerformanceCounter("Process", "% Processor Time", Process.GetCurrentProcess().ProcessName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERFORMANCE] Warning: Could not initialize performance counters: {ex.Message}");
            }
        }

        /// <summary>
        /// Comprehensive performance analysis of SaleViewGrid
        /// </summary>
        public async Task<PerformanceAnalysisReport> AnalyzePerformanceAsync(
            Views.Layouts.SalesViewGrid gridView, 
            SaleViewModel viewModel)
        {
            var report = new PerformanceAnalysisReport
            {
                TestStartTime = DateTime.Now,
                GridViewType = "SalesViewGrid with UniformGrid + Dynamic Spacing"
            };

            try
            {
                Debug.WriteLine("[PERFORMANCE] Starting comprehensive SaleViewGrid performance analysis...");

                // 1. Baseline measurements
                await MeasureBaselinePerformance(report, gridView, viewModel);

                // 2. Load time testing with varying product counts
                await TestLoadTimePerformance(report, viewModel);

                // 3. Grid layout and spacing validation
                await ValidateGridLayoutPerformance(report, gridView, viewModel);

                // 4. Memory and resource usage analysis
                await AnalyzeMemoryUsage(report, viewModel);

                // 5. UI responsiveness testing
                await TestUIResponsiveness(report, gridView);

                // 6. Virtualization effectiveness validation
                await ValidateVirtualizationPerformance(report, gridView, viewModel);

                // 7. Event subscription and memory leak detection
                await CheckForMemoryLeaks(report, gridView, viewModel);

                report.TestEndTime = DateTime.Now;
                report.TotalTestDuration = report.TestEndTime - report.TestStartTime;

                Debug.WriteLine($"[PERFORMANCE] Analysis completed in {report.TotalTestDuration.TotalSeconds:F2} seconds");
                return report;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERFORMANCE] Error during analysis: {ex.Message}");
                report.Errors.Add($"Analysis failed: {ex.Message}");
                return report;
            }
        }

        /// <summary>
        /// Measure baseline performance metrics
        /// </summary>
        private async Task MeasureBaselinePerformance(PerformanceAnalysisReport report, 
            Views.Layouts.SalesViewGrid gridView, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Measuring baseline performance...");

            var baseline = new BaselineMetrics();

            // Current memory usage
            baseline.InitialMemoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
            baseline.InitialProductCount = viewModel.FilteredProducts?.Count ?? 0;

            // Grid state
            var uniformGrid = FindUniformGrid(gridView);
            if (uniformGrid != null)
            {
                baseline.CurrentColumns = uniformGrid.Columns;
                baseline.GridWidth = uniformGrid.ActualWidth;
                baseline.GridHeight = uniformGrid.ActualHeight;
            }

            // UI thread responsiveness
            _stopwatch.Restart();
            await Application.Current.Dispatcher.InvokeAsync(() => { }, DispatcherPriority.Normal);
            _stopwatch.Stop();
            baseline.UIThreadResponseTime = _stopwatch.ElapsedMilliseconds;

            report.BaselineMetrics = baseline;
            Debug.WriteLine($"[PERFORMANCE] Baseline: {baseline.InitialProductCount} products, {baseline.CurrentColumns} columns, {baseline.InitialMemoryUsage:F1}MB memory");
        }

        /// <summary>
        /// Test load time performance with varying product counts
        /// </summary>
        private async Task TestLoadTimePerformance(PerformanceAnalysisReport report, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Testing load time performance...");

            var loadTests = new List<LoadTimeTest>();
            var testCounts = new[] { 10, 50, 100, 250, 500, 1000 };

            foreach (var count in testCounts)
            {
                try
                {
                    var test = new LoadTimeTest { ProductCount = count };
                    
                    // Measure memory before
                    test.MemoryBefore = GC.GetTotalMemory(true) / (1024 * 1024);

                    // Simulate loading products
                    _stopwatch.Restart();
                    
                    await Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        // Simulate product loading (would normally come from database)
                        var testProducts = GenerateTestProducts(count);
                        
                        // Measure collection update time
                        var updateStart = Stopwatch.StartNew();
                        viewModel.FilteredProducts.Clear();
                        foreach (var product in testProducts)
                        {
                            viewModel.FilteredProducts.Add(product);
                        }
                        updateStart.Stop();
                        test.CollectionUpdateTime = updateStart.ElapsedMilliseconds;
                        
                        // Allow UI to process
                        await Task.Delay(100);
                    }, DispatcherPriority.Background);

                    _stopwatch.Stop();
                    test.TotalLoadTime = _stopwatch.ElapsedMilliseconds;
                    test.MemoryAfter = GC.GetTotalMemory(false) / (1024 * 1024);
                    test.MemoryIncrease = test.MemoryAfter - test.MemoryBefore;

                    loadTests.Add(test);
                    Debug.WriteLine($"[PERFORMANCE] Load test {count} products: {test.TotalLoadTime}ms total, {test.CollectionUpdateTime}ms collection update");

                    // Brief pause between tests
                    await Task.Delay(500);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[PERFORMANCE] Load test failed for {count} products: {ex.Message}");
                }
            }

            report.LoadTimeTests = loadTests;
        }

        /// <summary>
        /// Validate grid layout and spacing performance
        /// </summary>
        private async Task ValidateGridLayoutPerformance(PerformanceAnalysisReport report, 
            Views.Layouts.SalesViewGrid gridView, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Validating grid layout performance...");

            var layoutTests = new List<GridLayoutTest>();
            var testScenarios = new[]
            {
                new { ProductCount = 3, ExpectedColumns = 3, Scenario = "Few products" },
                new { ProductCount = 7, ExpectedColumns = 4, Scenario = "Small catalog" },
                new { ProductCount = 25, ExpectedColumns = 6, Scenario = "Medium catalog" },
                new { ProductCount = 100, ExpectedColumns = 9, Scenario = "Large catalog" }
            };

            foreach (var scenario in testScenarios)
            {
                try
                {
                    var test = new GridLayoutTest
                    {
                        Scenario = scenario.Scenario,
                        ProductCount = scenario.ProductCount,
                        ExpectedColumns = scenario.ExpectedColumns
                    };

                    // Set up test products
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        viewModel.FilteredProducts.Clear();
                        var testProducts = GenerateTestProducts(scenario.ProductCount);
                        foreach (var product in testProducts)
                        {
                            viewModel.FilteredProducts.Add(product);
                        }
                    });

                    // Measure grid recalculation time
                    _stopwatch.Restart();
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // Trigger grid update (this would normally happen automatically)
                        var method = gridView.GetType().GetMethod("UpdateGridColumns", 
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        method?.Invoke(gridView, null);
                    });
                    _stopwatch.Stop();

                    test.CalculationTime = _stopwatch.ElapsedMilliseconds;

                    // Validate results
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        var uniformGrid = FindUniformGrid(gridView);
                        if (uniformGrid != null)
                        {
                            test.ActualColumns = uniformGrid.Columns;
                            test.GridWidth = uniformGrid.ActualWidth;
                            test.SpacingRatio = test.GridWidth > 0 ? 
                                (test.GridWidth / test.ActualColumns) / 138.0 : 0; // 138 = card width
                        }
                    });

                    test.IsOptimal = test.ActualColumns <= test.ExpectedColumns && test.SpacingRatio <= 1.5;
                    layoutTests.Add(test);

                    Debug.WriteLine($"[PERFORMANCE] Layout test '{scenario.Scenario}': {test.ActualColumns} columns (expected ≤{test.ExpectedColumns}), spacing ratio {test.SpacingRatio:F2}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[PERFORMANCE] Layout test failed for '{scenario.Scenario}': {ex.Message}");
                }
            }

            report.GridLayoutTests = layoutTests;
        }

        /// <summary>
        /// Generate test products for performance testing
        /// </summary>
        private List<Product> GenerateTestProducts(int count)
        {
            var products = new List<Product>();
            for (int i = 1; i <= count; i++)
            {
                products.Add(new Product
                {
                    Id = i,
                    Name = $"Test Product {i}",
                    SellingPrice = 10.00m + (i % 100),
                    PurchasePrice = 8.00m + (i % 80),
                    SKU = $"TEST{i:D4}",
                    IsActive = true,
                    StockQuantity = 50 + (i % 20)
                });
            }
            return products;
        }

        /// <summary>
        /// Find UniformGrid in the visual tree
        /// </summary>
        private UniformGrid FindUniformGrid(DependencyObject parent)
        {
            if (parent == null) return null;

            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is UniformGrid uniformGrid)
                    return uniformGrid;

                var result = FindUniformGrid(child);
                if (result != null)
                    return result;
            }
            return null;
        }

        /// <summary>
        /// Analyze memory usage patterns
        /// </summary>
        private async Task AnalyzeMemoryUsage(PerformanceAnalysisReport report, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Analyzing memory usage...");

            var memoryTest = new MemoryUsageTest();

            // Force garbage collection for accurate baseline
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            memoryTest.BaselineMemory = GC.GetTotalMemory(false) / (1024 * 1024);

            // Test memory usage with large product set
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var largeProductSet = GenerateTestProducts(1000);
                viewModel.FilteredProducts.Clear();
                foreach (var product in largeProductSet)
                {
                    viewModel.FilteredProducts.Add(product);
                }
            });

            await Task.Delay(1000); // Allow UI to settle
            memoryTest.PeakMemory = GC.GetTotalMemory(false) / (1024 * 1024);

            // Clear and measure cleanup
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                viewModel.FilteredProducts.Clear();
            });

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            memoryTest.PostCleanupMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            memoryTest.MemoryLeakIndicator = memoryTest.PostCleanupMemory - memoryTest.BaselineMemory;

            report.MemoryUsageTest = memoryTest;
            Debug.WriteLine($"[PERFORMANCE] Memory: Baseline {memoryTest.BaselineMemory:F1}MB, Peak {memoryTest.PeakMemory:F1}MB, Cleanup {memoryTest.PostCleanupMemory:F1}MB");
        }

        /// <summary>
        /// Test UI responsiveness during various operations
        /// </summary>
        private async Task TestUIResponsiveness(PerformanceAnalysisReport report, Views.Layouts.SalesViewGrid gridView)
        {
            Debug.WriteLine("[PERFORMANCE] Testing UI responsiveness...");

            var responsivenessTest = new UIResponsivenessTest();

            // Test window resize responsiveness
            _stopwatch.Restart();
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var window = Window.GetWindow(gridView);
                if (window != null)
                {
                    var originalWidth = window.Width;
                    window.Width = originalWidth * 0.7; // Resize to 70%
                    window.UpdateLayout();
                    window.Width = originalWidth; // Restore
                    window.UpdateLayout();
                }
            });
            _stopwatch.Stop();
            responsivenessTest.ResizeResponseTime = _stopwatch.ElapsedMilliseconds;

            // Test scroll performance simulation
            _stopwatch.Restart();
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var listView = FindListView(gridView);
                if (listView?.Items.Count > 0)
                {
                    // Simulate scrolling by changing selection
                    for (int i = 0; i < Math.Min(10, listView.Items.Count); i++)
                    {
                        listView.SelectedIndex = i;
                        listView.UpdateLayout();
                    }
                }
            });
            _stopwatch.Stop();
            responsivenessTest.ScrollPerformance = _stopwatch.ElapsedMilliseconds;

            report.UIResponsivenessTest = responsivenessTest;
            Debug.WriteLine($"[PERFORMANCE] UI Responsiveness: Resize {responsivenessTest.ResizeResponseTime}ms, Scroll {responsivenessTest.ScrollPerformance}ms");
        }

        /// <summary>
        /// Validate virtualization effectiveness
        /// </summary>
        private async Task ValidateVirtualizationPerformance(PerformanceAnalysisReport report,
            Views.Layouts.SalesViewGrid gridView, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Validating virtualization performance...");

            var virtualizationTest = new VirtualizationTest();

            // Test with large dataset
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var largeDataset = GenerateTestProducts(2000);
                viewModel.FilteredProducts.Clear();
                foreach (var product in largeDataset)
                {
                    viewModel.FilteredProducts.Add(product);
                }
            });

            await Task.Delay(500); // Allow virtualization to settle

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var listView = FindListView(gridView);
                if (listView != null)
                {
                    virtualizationTest.TotalItems = listView.Items.Count;

                    // Count realized containers (visible items)
                    var realizedContainers = 0;
                    for (int i = 0; i < listView.Items.Count; i++)
                    {
                        var container = listView.ItemContainerGenerator.ContainerFromIndex(i);
                        if (container != null)
                        {
                            realizedContainers++;
                        }
                    }

                    virtualizationTest.RealizedContainers = realizedContainers;
                    virtualizationTest.VirtualizationRatio = virtualizationTest.TotalItems > 0 ?
                        (double)virtualizationTest.RealizedContainers / virtualizationTest.TotalItems : 0;
                    virtualizationTest.IsVirtualizationEffective = virtualizationTest.VirtualizationRatio < 0.1; // Less than 10% realized
                }
            });

            report.VirtualizationTest = virtualizationTest;
            Debug.WriteLine($"[PERFORMANCE] Virtualization: {virtualizationTest.RealizedContainers}/{virtualizationTest.TotalItems} realized ({virtualizationTest.VirtualizationRatio:P1})");
        }

        /// <summary>
        /// Check for potential memory leaks from event subscriptions
        /// </summary>
        private async Task CheckForMemoryLeaks(PerformanceAnalysisReport report,
            Views.Layouts.SalesViewGrid gridView, SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Checking for memory leaks...");

            var leakTest = new MemoryLeakTest();

            // Baseline measurement
            GC.Collect();
            leakTest.InitialMemory = GC.GetTotalMemory(false) / (1024 * 1024);

            // Simulate multiple load/unload cycles
            for (int cycle = 0; cycle < 5; cycle++)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Load products
                    var products = GenerateTestProducts(100);
                    viewModel.FilteredProducts.Clear();
                    foreach (var product in products)
                    {
                        viewModel.FilteredProducts.Add(product);
                    }
                });

                await Task.Delay(200);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Clear products
                    viewModel.FilteredProducts.Clear();
                });

                await Task.Delay(200);
            }

            // Final measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            leakTest.FinalMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            leakTest.MemoryGrowth = leakTest.FinalMemory - leakTest.InitialMemory;
            leakTest.HasPotentialLeak = leakTest.MemoryGrowth > 10; // More than 10MB growth indicates potential leak

            report.MemoryLeakTest = leakTest;
            Debug.WriteLine($"[PERFORMANCE] Memory leak test: {leakTest.MemoryGrowth:F1}MB growth over 5 cycles");
        }

        /// <summary>
        /// Find ListView in the visual tree
        /// </summary>
        private ListView FindListView(DependencyObject parent)
        {
            if (parent == null) return null;

            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is ListView listView)
                    return listView;

                var result = FindListView(child);
                if (result != null)
                    return result;
            }
            return null;
        }
    }
}
