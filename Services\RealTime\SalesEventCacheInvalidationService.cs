using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.ViewModels;
using POSSystem.Services.Caching;

namespace POSSystem.Services.RealTime
{
    /// <summary>
    /// Subscribes to SaleViewModel events and invalidates relevant caches to keep UI and data fresh system-wide.
    /// </summary>
    public class SalesEventCacheInvalidationService
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<SalesEventCacheInvalidationService> _logger;

        public SalesEventCacheInvalidationService(
            IServiceScopeFactory scopeFactory,
            ILogger<SalesEventCacheInvalidationService> logger = null)
        {
            _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
            _logger = logger;

            // Subscribe to global events
            SaleViewModel.SaleCompleted += OnSaleCompleted;
            SaleViewModel.ProductStockChanged += OnProductStockChanged;

            _logger?.LogInformation("[CACHE_INVALIDATION] SalesEventCacheInvalidationService initialized and event subscriptions set up");
        }

        private void OnSaleCompleted(object sender, EventArgs e)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    using var scope = _scopeFactory.CreateScope();

                    // Invalidate product-related caches in the cached repository layer (if available)
                    var cachedRepo = scope.ServiceProvider.GetService<CachedRepositoryService>();
                    if (cachedRepo != null)
                    {
                        await cachedRepo.InvalidateProductCacheAsync();
                        _logger?.LogDebug("[CACHE_INVALIDATION] Invalidated CachedRepositoryService product caches after sale completion");
                    }

                    // Optionally clear broader caches via ICacheService dependency tag if used elsewhere
                    var cache = scope.ServiceProvider.GetService<ICacheService>();
                    if (cache != null)
                    {
                        // If dependencies are used, invalidate a common 'products' namespace
                        await cache.InvalidateDependenciesAsync("products");
                        _logger?.LogDebug("[CACHE_INVALIDATION] Invalidated 'products' cache dependencies after sale completion");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[CACHE_INVALIDATION] Error invalidating caches on SaleCompleted");
                }
            });
        }

        private void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    using var scope = _scopeFactory.CreateScope();

                    var cachedRepo = scope.ServiceProvider.GetService<CachedRepositoryService>();
                    if (cachedRepo != null)
                    {
                        await cachedRepo.InvalidateProductCacheAsync(e.ProductId);
                        _logger?.LogDebug("[CACHE_INVALIDATION] Invalidated product cache for product {ProductId}", e.ProductId);
                    }

                    var cache = scope.ServiceProvider.GetService<ICacheService>();
                    if (cache != null)
                    {
                        await cache.InvalidateDependenciesAsync($"product:{e.ProductId}");
                        _logger?.LogDebug("[CACHE_INVALIDATION] Invalidated dependency for product {ProductId}", e.ProductId);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[CACHE_INVALIDATION] Error invalidating caches on ProductStockChanged for product {ProductId}", e.ProductId);
                }
            });
        }
    }
}

