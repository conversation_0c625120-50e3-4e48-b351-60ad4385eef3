using POSSystem.Models;
using POSSystem.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.Services
{
    public class CustomerManagementService : ICustomerService
    {
        private readonly IDatabaseService _dbService;

        public CustomerManagementService(IDatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
        }

        // Legacy constructor for backward compatibility
        public CustomerManagementService() : this(new DatabaseService())
        {
        }

        public List<Customer> GetAllCustomers()
        {
            return _dbService.GetAllCustomers();
        }

        public Customer GetCustomerById(int id)
        {
            return _dbService.GetCustomerById(id);
        }

        public Customer GetCustomerByLoyaltyCode(string loyaltyCode)
        {
            return _dbService.GetCustomerByLoyaltyCode(loyaltyCode);
        }

        public void AddCustomer(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            _dbService.AddCustomer(customer);
        }

        public void UpdateCustomer(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            _dbService.UpdateCustomer(customer);
        }

        public void DeleteCustomer(int id)
        {
            _dbService.DeleteCustomer(id);
        }

        public void UpdateLoyaltyPoints(int customerId, int points)
        {
            var customer = GetCustomerById(customerId);
            if (customer != null)
            {
                customer.LoyaltyPoints = customer.LoyaltyPoints + points;
                UpdateCustomer(customer);
            }
        }

        public void AddLoyaltyTransaction(Customer customer, decimal points, string description)
        {
            if (customer == null || customer.Id <= 0)
            {
                throw new ArgumentException("Invalid customer. Cannot add loyalty points.");
            }

            var transaction = new LoyaltyTransaction
            {
                CustomerId = customer.Id,
                Points = points,
                TransactionDate = DateTime.Now,
                Description = description
            };

            // Save the loyalty transaction
            _dbService.SaveLoyaltyTransaction(transaction);

            // Update customer's total points
            customer.LoyaltyPoints += points;
            _dbService.UpdateCustomer(customer);

            // Update customer's tier if needed
            var loyaltyProgram = _dbService.GetActiveLoyaltyProgram();
            if (loyaltyProgram != null)
            {
                var newTier = loyaltyProgram.Tiers
                    .OrderByDescending(t => t.MinimumPoints)
                    .FirstOrDefault(t => customer.LoyaltyPoints >= t.MinimumPoints);

                if (newTier != null && (customer.LoyaltyTier == null || customer.LoyaltyTier.Id != newTier.Id))
                {
                    customer.LoyaltyTier = newTier;
                    _dbService.UpdateCustomer(customer);
                }
            }
        }
    }
}