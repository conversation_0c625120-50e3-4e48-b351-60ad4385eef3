using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Views
{
    /// <summary>
    /// Interaction logic for PaymentProcessingView.xaml
    /// </summary>
    public partial class PaymentProcessingView : UserControl
    {
        public PaymentProcessingView()
        {
            InitializeComponent();
            
            // Add Loaded event handler to ensure ViewModel is initialized
            Loaded += PaymentProcessingView_Loaded;
        }
        
        private void PaymentProcessingView_Loaded(object sender, RoutedEventArgs e)
        {
            // This will force all properties to update their values and notify the UI
            if (ViewModel != null)
            {
                // Call UpdateAllProperties if it exists, otherwise nothing happens
                Type type = ViewModel.GetType();
                var method = type.GetMethod("UpdateAllProperties", 
                    System.Reflection.BindingFlags.Instance | 
                    System.Reflection.BindingFlags.NonPublic);
                
                method?.Invoke(ViewModel, null);
            }
        }
        
        public PaymentProcessingViewModel ViewModel => DataContext as PaymentProcessingViewModel;
        
        // Note: LookupCustomer_Click event handler has been replaced with LookupCustomerCommand 
        // in the ViewModel to fix XAML DataTrigger event handling issue
        /* 
        private void LookupCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (ViewModel?.ParentViewModel != null)
            {
                // Use the existing CustomerSelectionWindow
                var customerWindow = new CustomerSelectionWindow
                {
                    Owner = Window.GetWindow(this)
                };
                
                // Show the window as dialog
                if (customerWindow.ShowDialog() == true && customerWindow.SelectedCustomer != null)
                {
                    // Set customer in parent ViewModel
                    ViewModel.ParentViewModel.SelectedCustomer = customerWindow.SelectedCustomer;
                    
                    // Update the CustomerName in this ViewModel
                    ViewModel.CustomerName = customerWindow.SelectedCustomer.FullName;
                    
                    // Update loyalty information in the ViewModel
                    if (ViewModel.ParentViewModel.HasLoyaltyCustomer)
                    {
                        ViewModel.HasLoyaltyCustomer = true;
                        
                        // Calculate loyalty points using the parent ViewModel's logic
                        ViewModel.LoyaltyPointsEarned = ViewModel.ParentViewModel.LoyaltyPointsEarned;
                    }
                }
            }
        }
        */
    }
} 