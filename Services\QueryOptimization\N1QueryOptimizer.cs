using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using System.Diagnostics;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// ✅ CRITICAL DATABASE OPTIMIZATION: Service to eliminate N+1 query problems and optimize database access patterns
    /// </summary>
    public class N1QueryOptimizer : IDisposable
    {
        private readonly ILogger<N1QueryOptimizer> _logger;
        private readonly string _connectionString;
        private bool _disposed;

        public N1QueryOptimizer(ILogger<N1QueryOptimizer> logger = null)
        {
            _logger = logger;
            _connectionString = "Data Source=pos.db";
        }

        /// <summary>
        /// ✅ CRITICAL: Get sales with all related data in a single optimized query (eliminates N+1)
        /// </summary>
        public async Task<List<Sale>> GetSalesWithAllRelatedDataAsync(DateTime startDate, DateTime endDate, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var context = new POSDbContext();
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var sales = await context.Sales
                    .AsNoTracking()
                    .Include(s => s.Customer)
                    .Include(s => s.User)
                    .Include(s => s.Items)
                        .ThenInclude(i => i.Product)
                            .ThenInclude(p => p.Category)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync(cancellationToken);

                stopwatch.Stop();
                Debug.WriteLine($"[N1-OPTIMIZER] GetSalesWithAllRelatedDataAsync: {sales.Count} sales loaded in {stopwatch.ElapsedMilliseconds}ms");

                return sales;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in GetSalesWithAllRelatedDataAsync");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Get products with all related data in a single optimized query (eliminates N+1)
        /// </summary>
        public async Task<List<Product>> GetProductsWithAllRelatedDataAsync(int? categoryId = null, 
            string searchTerm = null, int page = 1, int pageSize = 50, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var context = new POSDbContext();
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var query = context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Supplier)
                    .Include(p => p.UnitOfMeasure)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches)
                    .Where(p => p.IsActive);

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    var term = searchTerm.ToLower();
                    query = query.Where(p => p.Name.ToLower().Contains(term) ||
                                           p.SKU.ToLower().Contains(term) ||
                                           p.Barcodes.Any(b => b.Barcode.ToLower().Contains(term)));
                }

                var products = await query
                    .OrderBy(p => p.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync(cancellationToken);

                stopwatch.Stop();
                Debug.WriteLine($"[N1-OPTIMIZER] GetProductsWithAllRelatedDataAsync: {products.Count} products loaded in {stopwatch.ElapsedMilliseconds}ms");

                return products;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in GetProductsWithAllRelatedDataAsync");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Get customer sales history with all related data (eliminates N+1)
        /// </summary>
        public async Task<List<Sale>> GetCustomerSalesHistoryOptimizedAsync(int customerId, 
            DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var context = new POSDbContext();
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var query = context.Sales
                    .AsNoTracking()
                    .Include(s => s.Customer)
                    .Include(s => s.User)
                    .Include(s => s.Items)
                        .ThenInclude(i => i.Product)
                    .Where(s => s.CustomerId == customerId);

                if (startDate.HasValue)
                {
                    query = query.Where(s => s.SaleDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(s => s.SaleDate <= endDate.Value);
                }

                var sales = await query
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync(cancellationToken);

                stopwatch.Stop();
                Debug.WriteLine($"[N1-OPTIMIZER] GetCustomerSalesHistoryOptimizedAsync: {sales.Count} sales for customer {customerId} loaded in {stopwatch.ElapsedMilliseconds}ms");

                return sales;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in GetCustomerSalesHistoryOptimizedAsync");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Get profit data with pre-calculated values (eliminates N+1 in profit calculations)
        /// </summary>
        public async Task<List<ProfitDataPoint>> GetProfitDataOptimizedAsync(DateTime startDate, DateTime endDate, 
            string groupBy = "day", CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var context = new POSDbContext();
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                // Single query that calculates profit data without N+1 problems
                var query = from s in context.Sales
                           join si in context.SaleItems on s.Id equals si.SaleId
                           join p in context.Products on si.ProductId equals p.Id
                           where s.SaleDate >= startDate && s.SaleDate <= endDate && s.Status == "Completed"
                           select new
                           {
                               s.SaleDate,
                               Profit = si.Quantity * (p.SellingPrice - p.PurchasePrice),
                               Revenue = si.Total
                           };

                List<ProfitDataPoint> result;

                switch (groupBy.ToLower())
                {
                    case "hour":
                        result = await query
                            .GroupBy(x => new { x.SaleDate.Date, x.SaleDate.Hour })
                            .Select(g => new ProfitDataPoint
                            {
                                Date = g.Key.Date.AddHours(g.Key.Hour),
                                Profit = g.Sum(x => x.Profit),
                                Revenue = g.Sum(x => x.Revenue)
                            })
                            .OrderBy(p => p.Date)
                            .ToListAsync(cancellationToken);
                        break;

                    case "month":
                        result = await query
                            .GroupBy(x => new { x.SaleDate.Year, x.SaleDate.Month })
                            .Select(g => new ProfitDataPoint
                            {
                                Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                                Profit = g.Sum(x => x.Profit),
                                Revenue = g.Sum(x => x.Revenue)
                            })
                            .OrderBy(p => p.Date)
                            .ToListAsync(cancellationToken);
                        break;

                    default: // day
                        result = await query
                            .GroupBy(x => x.SaleDate.Date)
                            .Select(g => new ProfitDataPoint
                            {
                                Date = g.Key,
                                Profit = g.Sum(x => x.Profit),
                                Revenue = g.Sum(x => x.Revenue)
                            })
                            .OrderBy(p => p.Date)
                            .ToListAsync(cancellationToken);
                        break;
                }

                stopwatch.Stop();
                Debug.WriteLine($"[N1-OPTIMIZER] GetProfitDataOptimizedAsync: {result.Count} data points loaded in {stopwatch.ElapsedMilliseconds}ms");

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in GetProfitDataOptimizedAsync");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Get top products with pre-calculated metrics (eliminates N+1)
        /// </summary>
        public async Task<List<TopProductData>> GetTopProductsOptimizedAsync(DateTime startDate, DateTime endDate, 
            int limit = 10, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var context = new POSDbContext();
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var topProducts = await (from si in context.SaleItems
                                       join s in context.Sales on si.SaleId equals s.Id
                                       join p in context.Products on si.ProductId equals p.Id
                                       where s.SaleDate >= startDate && s.SaleDate <= endDate && s.Status == "Completed"
                                       group new { si, p } by new { p.Id, p.Name } into g
                                       select new TopProductData
                                       {
                                           ProductId = g.Key.Id,
                                           ProductName = g.Key.Name,
                                           TotalQuantitySold = g.Sum(x => x.si.Quantity),
                                           TotalRevenue = g.Sum(x => x.si.Total),
                                           TotalProfit = g.Sum(x => x.si.Quantity * (x.p.SellingPrice - x.p.PurchasePrice)),
                                           TransactionCount = g.Count()
                                       })
                                       .OrderByDescending(p => p.TotalRevenue)
                                       .Take(limit)
                                       .ToListAsync(cancellationToken);

                stopwatch.Stop();
                Debug.WriteLine($"[N1-OPTIMIZER] GetTopProductsOptimizedAsync: {topProducts.Count} top products loaded in {stopwatch.ElapsedMilliseconds}ms");

                return topProducts;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in GetTopProductsOptimizedAsync");
                throw;
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
        }
    }

    /// <summary>
    /// Data transfer objects for optimized queries
    /// </summary>
    public class ProfitDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Profit { get; set; }
        public decimal Revenue { get; set; }
    }

    public class TopProductData
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalProfit { get; set; }
        public int TransactionCount { get; set; }
    }
}
