using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using POSSystem.Models;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface defining data access methods required by dashboard services.
    /// This abstraction allows for easier testing and decoupling from the specific database implementation.
    /// </summary>
    public interface IDashboardDataProvider
    {
        /// <summary>
        /// Gets sales data for a specified date range
        /// </summary>
        Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Gets sales data for a specified date range - alternate method name used in some services
        /// </summary>
        Task<List<Sale>> GetSalesAsync(DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Gets dashboard alert data including low stock and expiring products
        /// </summary>
        Task<DashboardAlertData> GetDashboardAlertsAsync();
        
        /// <summary>
        /// Gets top selling products with their sales data
        /// </summary>
        Task<List<Product>> GetTopSellingProductsAsync(int count);
        
        /// <summary>
        /// Gets a category by ID
        /// </summary>
        Category GetCategoryById(int categoryId);

        /// <summary>
        /// Gets customer data for demographics reporting
        /// </summary>
        Task<List<Customer>> GetCustomersAsync();

        /// <summary>
        /// Gets sales data for a specific date range for customer demographics
        /// </summary>
        Task<List<Sale>> GetCustomerSalesAsync(int customerId, DateTime startDate, DateTime endDate);
    }
    
    /// <summary>
    /// Data class for dashboard alert information
    /// </summary>
    public class DashboardAlertData
    {
        public List<Product> LowStockProducts { get; set; } = new List<Product>();
        public List<Product> ExpiringProducts { get; set; } = new List<Product>();
    }
} 