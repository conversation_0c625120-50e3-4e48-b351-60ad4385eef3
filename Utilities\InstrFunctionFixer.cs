using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Diagnostics;
using System.Linq;
using System.Collections.Generic;
using System.Text;

namespace POSSystem.Utilities
{
    /// <summary>
    /// Specific utility to fix the INSTRO typo in SQL queries
    /// </summary>
    public static class InstrFunctionFixer
    {
        /// <summary>
        /// Fix all instances of "INSTRO" to "INSTR" in the specified file
        /// </summary>
        public static bool FixFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                Debug.WriteLine($"File not found: {filePath}");
                return false;
            }
            
            try
            {
                string content = File.ReadAllText(filePath);
                bool containsInstro = content.IndexOf("INSTRO", StringComparison.OrdinalIgnoreCase) >= 0;
                
                if (containsInstro)
                {
                    // Create a backup of the original file
                    string backupPath = $"{filePath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                    File.Copy(filePath, backupPath);
                    
                    // Replace all occurrences of "INSTRO" with "INSTR" (case-insensitive)
                    string fixedContent = Regex.Replace(content, "INSTRO", "INSTR", RegexOptions.IgnoreCase);
                    
                    // Write the fixed content back to the file
                    File.WriteAllText(filePath, fixedContent);
                    
                    Debug.WriteLine($"Fixed INSTRO typo in {filePath}");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error fixing INSTRO typo in {filePath}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Fix SQL query string in memory
        /// </summary>
        public static string FixQuery(string sqlQuery)
        {
            if (string.IsNullOrEmpty(sqlQuery))
                return sqlQuery;
                
            return Regex.Replace(sqlQuery, "INSTRO", "INSTR", RegexOptions.IgnoreCase);
        }
        
        /// <summary>
        /// Find all files in a directory that contain the INSTRO typo
        /// </summary>
        public static List<string> FindFilesWithTypo(string directoryPath)
        {
            List<string> results = new List<string>();
            
            try
            {
                if (!Directory.Exists(directoryPath))
                {
                    Debug.WriteLine($"Directory not found: {directoryPath}");
                    return results;
                }
                
                // Get all CS and SQL files
                string[] files = Directory.GetFiles(directoryPath, "*.cs", SearchOption.AllDirectories)
                    .Concat(Directory.GetFiles(directoryPath, "*.sql", SearchOption.AllDirectories))
                    .ToArray();
                
                foreach (string file in files)
                {
                    // Skip files in obj and bin directories
                    if (file.Contains("\\obj\\") || file.Contains("\\bin\\"))
                        continue;
                        
                    string content = File.ReadAllText(file);
                    if (content.IndexOf("INSTRO", StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        results.Add(file);
                    }
                }
                
                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error finding files with INSTRO typo: {ex.Message}");
                return results;
            }
        }
        
        /// <summary>
        /// Fix all files in a directory that contain the INSTRO typo
        /// </summary>
        public static int FixAllFiles(string directoryPath)
        {
            int fixedCount = 0;
            
            try
            {
                List<string> files = FindFilesWithTypo(directoryPath);
                
                foreach (string file in files)
                {
                    if (FixFile(file))
                    {
                        fixedCount++;
                    }
                }
                
                return fixedCount;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error fixing all files with INSTRO typo: {ex.Message}");
                return fixedCount;
            }
        }
    }
}
