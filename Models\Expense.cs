using System;
using System.Collections.Generic;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class Expense
    {
        public int Id { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }
        public string Category { get; set; }
        public bool IsRecurring { get; set; }
        public string RecurringPeriod { get; set; }  // Daily, Weekly, Monthly, Yearly
        public string PaymentMethod { get; set; }
        public string Reference { get; set; }
        public int UserId { get; set; }
        public virtual User User { get; set; }
        public virtual ICollection<ExpenseDocument> Documents { get; set; }
    }
} 