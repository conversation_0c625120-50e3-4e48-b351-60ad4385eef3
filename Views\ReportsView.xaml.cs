using POSSystem.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ClosedXML.Excel;
using System.Linq;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Threading;
using System.Collections.Generic;
using System.Windows.Data;

namespace POSSystem.Views
{
    public partial class ReportsView : UserControl
    {
        private ReportsViewModel ViewModel => (ReportsViewModel)DataContext;
        private DispatcherTimer _debounceTimer;
        private Dictionary<string, List<DataGridColumn>> _columnCache = new Dictionary<string, List<DataGridColumn>>();

        public ReportsView()
        {
            InitializeComponent();

            // ✅ PERFORMANCE FIX: Don't create ViewModel here - let MainWindow set it via DI
            // This prevents UI thread blocking during constructor execution
            // DataContext will be set by MainWindow using dependency injection

            // Initialize debounce timer with longer interval
            _debounceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(800)
            };
            _debounceTimer.Tick += async (s, e) =>
            {
                _debounceTimer.Stop();
                if (DataContext is ReportsViewModel vm && !vm.IsLoading)
                {
                    await vm.LoadReportAsync();
                }
            };

            // ✅ PERFORMANCE FIX: Load data when view is actually displayed
            Loaded += ReportsView_Loaded;
        }

        private async void ReportsView_Loaded(object sender, RoutedEventArgs e)
        {
            // 🐛 DEBUG: Track view loading
            System.Diagnostics.Debug.WriteLine($"[REPORTS VIEW] ReportsView_Loaded called - DataContext: {DataContext?.GetType().Name}");

            // Only load data once when the view is first loaded
            if (DataContext is ReportsViewModel vm)
            {
                System.Diagnostics.Debug.WriteLine($"[REPORTS VIEW] Calling LoadInitialDataAsync");
                // Load initial data in background to prevent UI thread blocking
                await vm.LoadInitialDataAsync();
                System.Diagnostics.Debug.WriteLine($"[REPORTS VIEW] LoadInitialDataAsync completed");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[REPORTS VIEW] DataContext is not ReportsViewModel: {DataContext}");
            }
        }

        private void ReportType_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is ReportsViewModel vm)
            {
                SetupColumns(vm.SelectedReportType);
                // No need to restart the debounce timer here as the property change on ViewModel will trigger LoadReportAsync
            }
        }

        private void SetupColumns(string reportType)
        {
            if (_columnCache.ContainsKey(reportType))
            {
                reportDataGrid.Columns.Clear();
                foreach (var column in _columnCache[reportType])
                {
                    reportDataGrid.Columns.Add(column);
                }
                return;
            }

            reportDataGrid.Columns.Clear();
            var columns = new List<DataGridColumn>();

            switch (reportType)
            {
                case "SalesSummary":
                    columns.AddRange(CreateSalesColumns());
                    break;
                case "PurchaseOrdersSummary":
                    columns.AddRange(CreatePurchaseInvoiceColumns());
                    break;
                case "InventoryStatus":
                    columns.AddRange(CreateInventoryColumns());
                    break;
                case "TopProducts":
                    columns.AddRange(CreateTopProductsColumns());
                    break;
                case "CustomerActivity":
                    columns.AddRange(CreateCustomerActivityColumns());
                    break;
                case "SupplierActivity":
                    columns.AddRange(CreateSupplierActivityColumns());
                    break;
            }

            _columnCache[reportType] = columns;
            foreach (var column in columns)
            {
                reportDataGrid.Columns.Add(column);
            }
        }

        private IEnumerable<DataGridColumn> CreateSalesColumns()
        {
            return new[]
            {
                CreateColumn("Date", "Date", "d"),
                CreateColumn("TransactionNumber", "TransactionNumber"),
                CreateColumn("Type", "Type"),
                CreateColumn("Name", "Name"),
                CreateColumn("ItemCount", "ItemCount", "N0"),
                CreateColumn("Amount", "Amount", "N2"),
                CreateColumn("Status", "Status")
            };
        }

        private IEnumerable<DataGridColumn> CreatePurchaseInvoiceColumns()
        {
            return new[]
            {
                CreateColumn("Date", "Date", "d"),
                CreateColumn("TransactionNumber", "TransactionNumber"),
                CreateColumn("Type", "Type"),
                CreateColumn("Name", "Name"),
                CreateColumn("ItemCount", "ItemCount", "N0"),
                CreateColumn("Amount", "Amount", "N2"),
                CreateColumn("Status", "Status")
            };
        }

        private IEnumerable<DataGridColumn> CreateInventoryColumns()
        {
            return new[]
            {
                CreateColumn("ProductName", "ProductName"),
                CreateColumn("SKU", "SKU"),
                CreateColumn("Category", "Category"),
                CreateColumn("Stock", "Stock", "N0"),
                CreateColumn("ReorderLevel", "ReorderLevel", "N0"),
                CreateColumn("Status", "Status"),
                CreateColumn("UnitCost", "UnitCost", "N2"),
                CreateColumn("UnitPrice", "UnitPrice", "N2"),
                CreateColumn("TotalValue", "TotalValue", "N2")
            };
        }

        private IEnumerable<DataGridColumn> CreateTopProductsColumns()
        {
            return new[]
            {
                CreateColumn("ProductName", "ProductName"),
                CreateColumn("Category", "Category"),
                CreateColumn("QuantitySold", "QuantitySold", "N0"),
                CreateColumn("Revenue", "Revenue", "N2"),
                CreateColumn("Cost", "Cost", "N2"),
                CreateColumn("Profit", "Profit", "N2"),
                CreateColumn("ProfitMargin", "ProfitMargin", "N2")
            };
        }

        private IEnumerable<DataGridColumn> CreateCustomerActivityColumns()
        {
            return new[]
            {
                CreateColumn("CustomerName", "CustomerName"),
                CreateColumn("TransactionCount", "TransactionCount", "N0"),
                CreateColumn("TotalSpent", "TotalSpent", "N2"),
                CreateColumn("AverageSpent", "AverageSpent", "N2"),
                CreateColumn("LastPurchaseDate", "LastPurchaseDate", "d")
            };
        }

        private IEnumerable<DataGridColumn> CreateSupplierActivityColumns()
        {
            return new[]
            {
                CreateColumn("SupplierName", "SupplierName"),
                CreateColumn("OrderCount", "OrderCount", "N0"),
                CreateColumn("TotalSpent", "TotalSpent", "N2"),
                CreateColumn("AverageOrderValue", "AverageOrderValue", "N2"),
                CreateColumn("LastOrderDate", "LastOrderDate", "d")
            };
        }

        private DataGridColumn CreateColumn(string header, string binding, string stringFormat = null)
        {
            string displayHeader = header;
            try
            {
                // Try to find the resource, but fall back to the header if not found
                var resource = FindResource(header);
                if (resource is string resourceString)
                {
                    displayHeader = resourceString;
                }
            }
            catch (ResourceReferenceKeyNotFoundException)
            {
                // Resource not found, use the header as-is
                displayHeader = header;
            }

            var column = new DataGridTextColumn
            {
                Header = displayHeader,
                Binding = new Binding(binding)
                {
                    StringFormat = stringFormat != null ? "{0:" + stringFormat + "}" : null
                },
                Width = DataGridLength.Auto
            };
            return column;
        }

        private void StartDate_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is ReportsViewModel vm && sender is DatePicker dp)
            {
                // Let the property setter handle the report loading
                vm.StartDate = dp.SelectedDate ?? DateTime.Now.AddDays(-30);
            }
        }

        private void EndDate_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is ReportsViewModel vm && sender is DatePicker dp)
            {
                // Let the property setter handle the report loading
                vm.EndDate = dp.SelectedDate ?? DateTime.Now;
            }
        }

        private void RestartDebounceTimer()
        {
            if (DataContext is ReportsViewModel vm && vm.IsLoading)
            {
                // Don't start a new timer if already loading
                return;
            }
            
            _debounceTimer.Stop();
            _debounceTimer.Start();
        }

        private void ReportDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        private async void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    DefaultExt = ".xlsx",
                    FileName = $"{FindResource(ViewModel.SelectedReportType)}_{DateTime.Now:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    using (var workbook = new XLWorkbook())
                    {
                        var worksheet = workbook.Worksheets.Add(FindResource(ViewModel.SelectedReportType) as string);
                        
                        // Add report header
                        var titleCell = worksheet.Cell(1, 1);
                        titleCell.Value = FindResource(ViewModel.SelectedReportType) as string;
                        titleCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                        titleCell.Style.Font.Bold = true;

                        var periodCell = worksheet.Cell(2, 1);
                        periodCell.Value = $"{FindResource("Period")}: {ViewModel.StartDate:d} {FindResource("To")} {ViewModel.EndDate:d}";
                        periodCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                        
                        // Add summary statistics
                        var row = 4;
                        if (ViewModel.TotalSales > 0)
                        {
                            worksheet.Cell(row, 1).Value = (FindResource("TotalSales") as string) + ":";
                            worksheet.Cell(row, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            worksheet.Cell(row, 2).Value = ViewModel.TotalSales;
                            worksheet.Cell(row, 2).Style.NumberFormat.Format = "#,##0.00 \"DA\"";
                            worksheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            row++;
                        }
                        if (ViewModel.TotalPurchases > 0)
                        {
                            worksheet.Cell(row, 1).Value = (FindResource("TotalPurchases") as string) + ":";
                            worksheet.Cell(row, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            worksheet.Cell(row, 2).Value = ViewModel.TotalPurchases;
                            worksheet.Cell(row, 2).Style.NumberFormat.Format = "#,##0.00 \"DA\"";
                            worksheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            row++;
                        }
                        if (ViewModel.NetProfit != 0)
                        {
                            worksheet.Cell(row, 1).Value = (FindResource("NetProfit") as string) + ":";
                            worksheet.Cell(row, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            worksheet.Cell(row, 2).Value = ViewModel.NetProfit;
                            worksheet.Cell(row, 2).Style.NumberFormat.Format = "#,##0.00 \"DA\"";
                            worksheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            row++;
                        }
                        worksheet.Cell(row, 1).Value = (FindResource("TotalTransactions") as string) + ":";
                        worksheet.Cell(row, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                        worksheet.Cell(row, 2).Value = ViewModel.TotalTransactions;
                        worksheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                        
                        // Add data headers
                        row += 2;
                        var headerRow = row;
                        var col = 1;
                        foreach (var column in reportDataGrid.Columns)
                        {
                            var headerText = column.Header.ToString();
                            var translatedHeader = TryGetTranslatedColumnHeader(headerText);
                            var colHeaderCell = worksheet.Cell(headerRow, col);
                            colHeaderCell.Value = translatedHeader;
                            colHeaderCell.Style.Font.Bold = true;
                            colHeaderCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                            colHeaderCell.Style.Fill.BackgroundColor = XLColor.FromArgb(240, 240, 240);
                            col++;
                        }

                        // Add data
                        row++;
                        foreach (var item in ViewModel.ReportData)
                        {
                            col = 1;
                            foreach (var column in reportDataGrid.Columns)
                            {
                                var property = item.GetType().GetProperty(column.SortMemberPath);
                                if (property != null)
                                {
                                    var cell = worksheet.Cell(row, col);
                                    var value = property.GetValue(item);
                                    
                                    if (property.Name == "Status")
                                    {
                                        // Translate status values
                                        cell.Value = TryGetTranslatedStatus(value?.ToString());
                                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                                    }
                                    else if (property.Name == "Amount" || property.Name == "Revenue" || 
                                            property.Name == "TotalSpent" || property.Name == "Value")
                                    {
                                        // Apply currency format to monetary values
                                        cell.Value = value != null ? Convert.ToDouble(value) : 0.0;
                                        cell.Style.NumberFormat.Format = "#,##0.00 \"DA\"";
                                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                                    }
                                    else if (property.Name == "Date" || property.Name == "LastPurchase")
                                    {
                                        // Handle date values
                                        cell.Value = value?.ToString() ?? "";
                                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                                        if (value is DateTime)
                                        {
                                            cell.Style.DateFormat.Format = "yyyy-MM-dd";
                                        }
                                    }
                                    else
                                    {
                                        cell.Value = value?.ToString() ?? "";
                                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                                    }
                                }
                                col++;
                            }
                            row++;
                        }

                        // Auto-fit columns
                        worksheet.Columns().AdjustToContents();
                        
                        // Add borders to the data range only if we have data
                        if (headerRow < row && col > 1)  // Ensure we have at least one row of data and columns
                        {
                            try
                            {
                                var lastRow = row - 1;  // Last row with data
                                var lastCol = col - 1;  // Last column with data
                                
                                // Validate the range
                                if (lastRow >= headerRow && lastCol >= 1)
                                {
                                    var dataRange = worksheet.Range(headerRow, 1, lastRow, lastCol);
                                    dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                                    dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log the error but continue with saving the file
                                System.Diagnostics.Debug.WriteLine($"Error applying borders: {ex.Message}");
                            }
                        }

                        workbook.SaveAs(saveDialog.FileName);
                        MessageBox.Show(
                            FindResource("ReportExportedSuccessfully") as string, 
                            FindResource("Success") as string, 
                            MessageBoxButton.OK, 
                            MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{FindResource("ErrorExportingToExcel")}: {ex.Message}", 
                    FindResource("Error") as string, 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            }
        }

        private string TryGetTranslatedColumnHeader(string headerText)
        {
            // Common column headers mapping
            var resourceKey = headerText switch
            {
                "Date" => "Date",
                "TransactionNumber" => "TransactionNumber",
                "Type" => "Type",
                "Name" => "Name",
                "ItemCount" => "ItemCount",
                "Amount" => "Amount",
                "Status" => "Status",
                "ProductName" => "ProductName",
                "Category" => "Category",
                "QuantitySold" => "QuantitySold",
                "Revenue" => "Revenue",
                "Customer" => "Customer",
                "TotalPurchases" => "TotalPurchases",
                "TotalSpent" => "TotalSpent",
                "LastPurchase" => "LastPurchase",
                "SKU" => "SKU",
                "StockQuantity" => "StockQuantity",
                "MinimumStock" => "MinimumStock",
                "Value" => "Value",
                _ => headerText // Default to original text if no mapping found
            };

            return FindResource(resourceKey) as string ?? headerText;
        }

        private string TryGetTranslatedStatus(string status)
        {
            // Status values mapping
            var resourceKey = status switch
            {
                "Active" => "StatusActive",
                "Inactive" => "StatusInactive",
                "Pending" => "StatusPending",
                "Completed" => "StatusCompleted",
                "Cancelled" => "StatusCancelled",
                "In Stock" => "StatusInStock",
                "Low Stock" => "StatusLowStock",
                "Out of Stock" => "StatusOutOfStock",
                _ => status // Default to original text if no mapping found
            };

            return FindResource(resourceKey) as string ?? status;
        }
    }
} 