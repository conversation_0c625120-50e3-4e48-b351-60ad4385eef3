-- Pre-Migration Database Check
-- Run this first to verify your current database state

PRINT '=== PRE-MIGRATION DATABASE CHECK ===';
PRINT '';

-- Check if Products table exists
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Products')
BEGIN
    PRINT '✅ Products table exists';
    
    -- Check current Products table structure
    PRINT 'Current Products table columns:';
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Products'
    ORDER BY ORDINAL_POSITION;
    
    -- Check if IsWeightBased column already exists
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
    BEGIN
        PRINT '⚠️  IsWeightBased column already exists in Products table';
    END
    ELSE
    BEGIN
        PRINT '✅ IsWeightBased column does not exist - migration needed';
    END
    
    -- Count existing products
    DECLARE @ProductCount INT;
    SELECT @ProductCount = COUNT(*) FROM Products;
    PRINT 'Total products in database: ' + CAST(@ProductCount AS VARCHAR(10));
END
ELSE
BEGIN
    PRINT '❌ Products table does not exist - please check your database';
END

PRINT '';

-- Check if SaleItems table exists
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SaleItems')
BEGIN
    PRINT '✅ SaleItems table exists';
    
    -- Check current SaleItems Quantity column type
    SELECT 
        'SaleItems.Quantity current type:' as Info,
        DATA_TYPE,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';
    
    -- Count existing sale items
    DECLARE @SaleItemCount INT;
    SELECT @SaleItemCount = COUNT(*) FROM SaleItems;
    PRINT 'Total sale items in database: ' + CAST(@SaleItemCount AS VARCHAR(10));
END
ELSE
BEGIN
    PRINT '❌ SaleItems table does not exist - please check your database';
END

PRINT '';

-- Check if UnitsOfMeasure table exists (needed for weight-based products)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UnitsOfMeasure')
BEGIN
    PRINT '✅ UnitsOfMeasure table exists';
    
    -- Show available units of measure
    PRINT 'Available Units of Measure:';
    SELECT Id, Name, Abbreviation, Type FROM UnitsOfMeasure WHERE IsActive = 1;
END
ELSE
BEGIN
    PRINT '⚠️  UnitsOfMeasure table does not exist - you may need to create weight units';
END

PRINT '';
PRINT '=== PRE-MIGRATION CHECK COMPLETE ===';
PRINT 'Review the output above before proceeding with migration.';
