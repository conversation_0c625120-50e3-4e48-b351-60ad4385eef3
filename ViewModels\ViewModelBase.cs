using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: Advanced ViewModelBase with performance optimizations for UI rendering
    /// </summary>
    public class ViewModelBase : INotifyPropertyChanged, IDisposable
    {
        private bool _disposed = false;
        private bool _isBatchUpdate = false;
        private readonly HashSet<string> _batchedProperties = new HashSet<string>();
        private readonly object _batchLock = new object();
        private DispatcherTimer _batchTimer;
        private readonly ConcurrentDictionary<string, object> _propertyCache = new ConcurrentDictionary<string, object>();

        // Performance optimization: Cache PropertyChangedEventArgs to reduce allocations
        private static readonly ConcurrentDictionary<string, PropertyChangedEventArgs> _eventArgsCache =
            new ConcurrentDictionary<string, PropertyChangedEventArgs>();

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Optimized property change notification with batching support
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            if (string.IsNullOrEmpty(propertyName) || _disposed) return;

            // During batch updates, collect property names instead of firing immediately
            if (_isBatchUpdate)
            {
                lock (_batchLock)
                {
                    _batchedProperties.Add(propertyName);
                }
                return;
            }

            // Fire property change notification
            FirePropertyChanged(propertyName);
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Efficient property setter with caching and change detection
        /// </summary>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            // Fast equality check
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;

            // Update cache for future comparisons
            if (propertyName != null)
            {
                _propertyCache.AddOrUpdate(propertyName, value, (key, oldValue) => value);
            }

            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Start batch update mode to reduce property change notifications
        /// </summary>
        protected void BeginBatchUpdate()
        {
            _isBatchUpdate = true;
            lock (_batchLock)
            {
                _batchedProperties.Clear();
            }
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: End batch update mode and fire all collected property changes
        /// </summary>
        protected void EndBatchUpdate()
        {
            if (!_isBatchUpdate) return;

            _isBatchUpdate = false;

            HashSet<string> propertiesToNotify;
            lock (_batchLock)
            {
                propertiesToNotify = new HashSet<string>(_batchedProperties);
                _batchedProperties.Clear();
            }

            // Fire all batched property changes on UI thread
            if (propertiesToNotify.Count > 0)
            {
                if (Application.Current?.Dispatcher != null)
                {
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        foreach (var propertyName in propertiesToNotify)
                        {
                            FirePropertyChanged(propertyName);
                        }
                    }), DispatcherPriority.DataBind);
                }
                else
                {
                    foreach (var propertyName in propertiesToNotify)
                    {
                        FirePropertyChanged(propertyName);
                    }
                }
            }
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Delayed batch update with automatic timer
        /// </summary>
        protected void BeginDelayedBatchUpdate(int delayMs = 50)
        {
            BeginBatchUpdate();

            if (_batchTimer == null)
            {
                _batchTimer = new DispatcherTimer(DispatcherPriority.DataBind)
                {
                    Interval = TimeSpan.FromMilliseconds(delayMs)
                };
                _batchTimer.Tick += (s, e) =>
                {
                    _batchTimer.Stop();
                    EndBatchUpdate();
                };
            }
            else
            {
                _batchTimer.Stop();
                _batchTimer.Interval = TimeSpan.FromMilliseconds(delayMs);
            }

            _batchTimer.Start();
        }

        /// <summary>
        /// ✅ INTERNAL: Fire property change notification with optimizations
        /// </summary>
        private void FirePropertyChanged(string propertyName)
        {
            var handler = PropertyChanged;
            if (handler == null) return;

            // Use cached PropertyChangedEventArgs to reduce allocations
            var eventArgs = _eventArgsCache.GetOrAdd(propertyName, name => new PropertyChangedEventArgs(name));

            // Ensure we're on the UI thread for property notifications
            if (Application.Current?.Dispatcher != null && !Application.Current.Dispatcher.CheckAccess())
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    handler(this, eventArgs);
                }), DispatcherPriority.DataBind);
            }
            else
            {
                handler(this, eventArgs);
            }
        }

        #region IDisposable Implementation

        /// <summary>
        /// Dispose of resources. Override in derived classes to add specific disposal logic.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Enhanced dispose method with UI resource cleanup
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // End any pending batch updates
                if (_isBatchUpdate)
                {
                    EndBatchUpdate();
                }

                // Dispose timer
                _batchTimer?.Stop();
                _batchTimer = null;

                // Clear caches
                _propertyCache.Clear();
                lock (_batchLock)
                {
                    _batchedProperties.Clear();
                }

                // Clear event handlers to prevent memory leaks
                PropertyChanged = null;

                _disposed = true;
            }
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~ViewModelBase()
        {
            Dispose(false);
        }

        /// <summary>
        /// Check if the object has been disposed
        /// </summary>
        protected void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        #endregion

        #region Performance Monitoring

        /// <summary>
        /// ✅ MONITORING: Get performance statistics for this ViewModel
        /// </summary>
        public virtual ViewModelPerformanceStats GetPerformanceStats()
        {
            return new ViewModelPerformanceStats
            {
                ViewModelType = GetType().Name,
                CachedPropertiesCount = _propertyCache.Count,
                IsBatchUpdateActive = _isBatchUpdate,
                BatchedPropertiesCount = _batchedProperties.Count,
                IsDisposed = _disposed
            };
        }

        #endregion
    }

    /// <summary>
    /// Performance statistics for ViewModels
    /// </summary>
    public class ViewModelPerformanceStats
    {
        public string ViewModelType { get; set; }
        public int CachedPropertiesCount { get; set; }
        public bool IsBatchUpdateActive { get; set; }
        public int BatchedPropertiesCount { get; set; }
        public bool IsDisposed { get; set; }
    }
}