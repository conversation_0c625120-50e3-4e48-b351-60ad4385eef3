using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using POSSystem.Models;
using POSSystem.Services;
using ZXing;
using ZXing.Windows.Compatibility;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Documents;
using System.Windows.Markup;
using POSSystem.Helpers;
using Point = System.Windows.Point;
using Brushes = System.Windows.Media.Brushes;

namespace POSSystem.Views
{
    public partial class BarcodePrintingWindow : Window
    {
        private readonly DatabaseService _dbService;
        private List<Product> _allProducts;
        private BarcodeWriter _barcodeWriter;
        private PrintDocument _printDocument;

        public BarcodePrintingWindow(DatabaseService dbService)
        {
            InitializeComponent();
            _dbService = dbService;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Load all products
            _allProducts = _dbService.GetAllProductsWithFullDetails().ToList();
            
            // Set all products as not selected by default
            foreach (var product in _allProducts)
            {
                product.IsSelected = false;
            }
            
            ProductsList.ItemsSource = _allProducts;

            // Initialize barcode writer
            _barcodeWriter = new BarcodeWriter
            {
                Format = BarcodeFormat.EAN_13,
                Options = new ZXing.Common.EncodingOptions
                {
                    Width = 300,
                    Height = 150,
                    Margin = 10
                }
            };

            // Set default values
            cmbBarcodeType.SelectedIndex = 0;
            cmbLabelSize.SelectedIndex = 1;
            cmbBarcodesPerPage.SelectedIndex = 0; // Set default barcodes per page to 1
            chkIncludeName.IsChecked = true;
            chkIncludePrice.IsChecked = true;

            // Add event handlers
            ProductsList.SelectionChanged += ProductsList_SelectionChanged;
            cmbBarcodeType.SelectionChanged += Settings_Changed;
            cmbLabelSize.SelectionChanged += Settings_Changed;
            cmbBarcodesPerPage.SelectionChanged += Settings_Changed; // Add handler for barcodes per page change
            chkIncludeName.Checked += Settings_Changed;
            chkIncludeName.Unchecked += Settings_Changed;
            chkIncludePrice.Checked += Settings_Changed;
            chkIncludePrice.Unchecked += Settings_Changed;
        }

        private void Settings_Changed(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
        }

        private void ProductsList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Update IsSelected property based on ListView selection
            foreach (Product product in e.RemovedItems)
            {
                product.IsSelected = false;
            }
            
            foreach (Product product in e.AddedItems)
            {
                product.IsSelected = true;
            }
            
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            if (ProductsList.SelectedItem is Product selectedProduct)
            {
                try
                {
                    var barcode = selectedProduct.Barcodes.FirstOrDefault()?.Barcode;
                    if (string.IsNullOrEmpty(barcode))
                    {
                        PreviewImage.Source = null;
                        return;
                    }

                    // Update barcode format based on selection
                    switch (cmbBarcodeType.SelectedIndex)
                    {
                        case 0:
                            _barcodeWriter.Format = BarcodeFormat.EAN_13;
                            break;
                        case 1:
                            _barcodeWriter.Format = BarcodeFormat.CODE_128;
                            break;
                        case 2:
                            _barcodeWriter.Format = BarcodeFormat.QR_CODE;
                            break;
                    }

                    // Generate barcode
                    var bitmap = _barcodeWriter.Write(barcode);

                    // Create drawing visual for additional elements
                    var drawingVisual = new DrawingVisual();
                    using (var drawingContext = drawingVisual.RenderOpen())
                    {
                        // Draw barcode
                        var barcodeImage = BitmapFrame.Create(bitmap.ToBitmapSource());
                        drawingContext.DrawImage(barcodeImage, new Rect(0, 0, barcodeImage.Width, barcodeImage.Height));

                        double currentY = barcodeImage.Height + 5;

                        // Add product name if checked
                        if (chkIncludeName.IsChecked == true)
                        {
                            var formattedText = new FormattedText(
                                selectedProduct.Name,
                                System.Globalization.CultureInfo.CurrentCulture,
                                FlowDirection.LeftToRight,
                                new Typeface("Segoe UI"),
                                12,
                                Brushes.Black,
                                VisualTreeHelper.GetDpi(this).PixelsPerDip);

                            drawingContext.DrawText(formattedText, 
                                new Point((barcodeImage.Width - formattedText.Width) / 2, currentY));
                            currentY += formattedText.Height + 5;
                        }

                        // Add price if checked
                        if (chkIncludePrice.IsChecked == true)
                        {
                            var priceText = new FormattedText(
                                $"{selectedProduct.SellingPrice:N2} DA",
                                System.Globalization.CultureInfo.CurrentCulture,
                                FlowDirection.LeftToRight,
                                new Typeface("Segoe UI"),
                                14,
                                Brushes.Black,
                                VisualTreeHelper.GetDpi(this).PixelsPerDip);

                            drawingContext.DrawText(priceText, 
                                new Point((barcodeImage.Width - priceText.Width) / 2, currentY));
                        }
                    }

                    // Convert drawing visual to bitmap
                    var renderTarget = new RenderTargetBitmap(
                        (int)_barcodeWriter.Options.Width,
                        (int)(_barcodeWriter.Options.Height * 1.5), // Make it 1.5 times taller for text
                        96,
                        96,
                        PixelFormats.Pbgra32);

                    renderTarget.Render(drawingVisual);

                    // Set the preview image
                    PreviewImage.Source = renderTarget;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        string.Format((string)Application.Current.Resources["ErrorGeneratingPreview"], ex.Message),
                        (string)Application.Current.Resources["PreviewError"], 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PreviewImage.Source = null;
                }
            }
            else
            {
                PreviewImage.Source = null;
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = txtSearch.Text.ToLower();
            
            // Remember which products were selected
            var selectedProductIds = _allProducts.Where(p => p.IsSelected).Select(p => p.Id).ToList();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                ProductsList.ItemsSource = _allProducts;
            }
            else
            {
                // Filter products
                var filteredProducts = _allProducts.Where(p =>
                    p.Name.ToLower().Contains(searchText) ||
                    (p.Barcodes != null && p.Barcodes.Any(b => b.Barcode.ToLower().Contains(searchText)))).ToList();
                
                ProductsList.ItemsSource = filteredProducts;
            }
            
            // Temporarily disable selection changed event to avoid unnecessary UI updates
            ProductsList.SelectionChanged -= ProductsList_SelectionChanged;
            
            // Clear selection and restore it
            ProductsList.SelectedItems.Clear();
            
            // Update the ListView selection based on the IsSelected property
            foreach (Product product in ProductsList.Items)
            {
                if (selectedProductIds.Contains(product.Id))
                {
                    ProductsList.SelectedItems.Add(product);
                }
            }
            
            // Re-attach the event handler
            ProductsList.SelectionChanged += ProductsList_SelectionChanged;
            
            // Force refresh to ensure checkboxes reflect the correct state
            if (ProductsList.ItemsSource is System.Collections.IEnumerable)
            {
                ProductsList.Items.Refresh();
            }
        }

        private void BtnPreview_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsList.SelectedItems.Count == 0)
            {
                MessageBox.Show(
                    (string)Application.Current.Resources["PleaseSelectProduct"], 
                    (string)Application.Current.Resources["NoSelection"], 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var product = (Product)ProductsList.SelectedItems[0];
            var barcode = product.Barcodes.FirstOrDefault()?.Barcode;
            if (string.IsNullOrEmpty(barcode))
            {
                MessageBox.Show(
                    (string)Application.Current.Resources["ProductHasNoBarcode"], 
                    (string)Application.Current.Resources["NoBarcode"], 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Update barcode format based on selection
            switch (cmbBarcodeType.SelectedIndex)
            {
                case 0:
                    _barcodeWriter.Format = BarcodeFormat.EAN_13;
                    break;
                case 1:
                    _barcodeWriter.Format = BarcodeFormat.CODE_128;
                    break;
                case 2:
                    _barcodeWriter.Format = BarcodeFormat.QR_CODE;
                    break;
            }

            try
            {
                // Generate barcode
                var bitmap = _barcodeWriter.Write(barcode);

                // Convert to WPF image
                using (var memory = new MemoryStream())
                {
                    bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                    memory.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.StreamSource = memory;
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.EndInit();

                    PreviewImage.Source = bitmapImage;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    string.Format((string)Application.Current.Resources["ErrorGeneratingBarcodePreview"], ex.Message),
                    (string)Application.Current.Resources["ErrorTitle"], 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            // Get all products that have the IsSelected property set to true
            var selectedProducts = _allProducts.Where(p => p.IsSelected).ToList();

            if (selectedProducts.Count == 0)
            {
                MessageBox.Show(
                    (string)Application.Current.Resources["PleaseSelectProduct"], 
                    (string)Application.Current.Resources["NoSelection"], 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var printDialog = new PrintDialog();
                
                if (printDialog.ShowDialog() == true)
                {
                    int copies = int.Parse(txtCopies.Text);
                    int barcodesPerPage = GetBarcodesPerPage();
                    
                    // Create the list of all barcodes to print (with copies)
                    var allBarcodesToPrint = new List<Product>();
                    foreach (var product in selectedProducts)
                    {
                        var barcode = product.Barcodes.FirstOrDefault()?.Barcode;
                        if (string.IsNullOrEmpty(barcode)) continue;
                        
                        // Add copies of the product
                        for (int i = 0; i < copies; i++)
                        {
                            allBarcodesToPrint.Add(product);
                        }
                    }
                    
                    // Create document with multiple barcodes per page
                    var document = new FixedDocument();
                    
                    // Process in chunks of barcodesPerPage
                    for (int i = 0; i < allBarcodesToPrint.Count; i += barcodesPerPage)
                    {
                        var barcodeChunk = allBarcodesToPrint.Skip(i).Take(barcodesPerPage).ToList();
                        var page = CreateBarcodeGridPage(barcodeChunk, printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
                        
                        var pageContent = new PageContent();
                        ((IAddChild)pageContent).AddChild(page);
                        document.Pages.Add(pageContent);
                    }
                    
                    // Print the document
                    printDialog.PrintDocument(document.DocumentPaginator, "Barcode Labels");
                    
                    MessageBox.Show(
                        (string)Application.Current.Resources["PrintJobSentSuccessfully"], 
                        (string)Application.Current.Resources["SuccessTitle"], 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    string.Format((string)Application.Current.Resources["ErrorPrintingBarcodes"], ex.Message),
                    (string)Application.Current.Resources["ErrorTitle"], 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FixedPage CreateBarcodeGridPage(List<Product> products, double pageWidth, double pageHeight)
        {
            var page = new FixedPage();
            page.Width = pageWidth;
            page.Height = pageHeight;
            
            // Create a grid container for the whole page with 1cm margins
            var pageContainer = new Grid();
            pageContainer.Width = pageWidth - 57; // ~1cm margin on each side (assuming 28.35 pixels/cm)
            pageContainer.Height = pageHeight - 57; // ~1cm margin on top and bottom
            Canvas.SetLeft(pageContainer, 28.5); // ~0.5cm from left
            Canvas.SetTop(pageContainer, 28.5); // ~0.5cm from top
            
            // Calculate grid dimensions based on barcode count
            int columns, rows;
            
            switch (products.Count)
            {
                case 1: columns = 1; rows = 1; break;
                case 2: columns = 1; rows = 2; break;
                case 3: 
                case 4: columns = 2; rows = 2; break;
                case 5:
                case 6: columns = 2; rows = 3; break;
                case 8: 
                case 9: columns = 3; rows = 3; break;
                case 10:
                case 11:
                case 12: columns = 3; rows = 4; break;
                default: 
                    if (products.Count <= 16)
                    {
                        columns = 4; rows = 4;
                    }
                    else
                    {
                        columns = 4; rows = 5; // Max 20 barcodes per page
                    }
                    break;
            }
            
            // Create a uniform grid to hold the barcodes
            var uniformGrid = new UniformGrid();
            uniformGrid.Columns = columns;
            uniformGrid.Rows = rows;
            
            // Add each barcode to the grid
            foreach (var product in products)
            {
                var barcode = product.Barcodes.FirstOrDefault()?.Barcode;
                if (string.IsNullOrEmpty(barcode)) continue;
                
                // Create a border for the barcode cell with margins
                var borderCell = new Border();
                borderCell.Margin = new Thickness(5);
                borderCell.BorderThickness = new Thickness(0);
                
                // Create a stack panel for the barcode and text
                var stackPanel = new StackPanel();
                stackPanel.Orientation = Orientation.Vertical;
                stackPanel.HorizontalAlignment = HorizontalAlignment.Center;
                stackPanel.VerticalAlignment = VerticalAlignment.Center;
                
                // Generate barcode image
                _barcodeWriter.Format = cmbBarcodeType.SelectedIndex switch
                {
                    0 => BarcodeFormat.EAN_13,
                    1 => BarcodeFormat.CODE_128,
                    2 => BarcodeFormat.QR_CODE,
                    _ => BarcodeFormat.EAN_13
                };
                
                // Calculate barcode size based on cell dimensions
                double cellWidth = pageContainer.Width / columns - 10; // 5px margin on each side
                double cellHeight = pageContainer.Height / rows - 10; // 5px margin on top and bottom
                
                _barcodeWriter.Options = new ZXing.Common.EncodingOptions
                {
                    Width = (int)(cellWidth * 0.9),
                    Height = (int)(cellHeight * 0.4),
                    Margin = 1
                };
                
                var bitmapImage = _barcodeWriter.Write(barcode).ToBitmapSource();
                
                // Create barcode image element
                var barcodeImage = new System.Windows.Controls.Image
                {
                    Source = bitmapImage,
                    Stretch = Stretch.None,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                
                stackPanel.Children.Add(barcodeImage);
                
                // Calculate font sizes based on cell dimensions
                double nameFontSize = Math.Max(6, Math.Min(12, cellHeight * 0.09));
                double priceFontSize = Math.Max(7, Math.Min(14, cellHeight * 0.11));
                
                // Add product name if checked
                if (chkIncludeName.IsChecked == true)
                {
                    var textBlock = new TextBlock
                    {
                        Text = product.Name,
                        FontSize = nameFontSize,
                        TextAlignment = TextAlignment.Center,
                        TextWrapping = TextWrapping.Wrap,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(2, 0, 2, 3),
                        MaxWidth = cellWidth - 4 // 2px margin on each side
                    };
                    
                    stackPanel.Children.Add(textBlock);
                }
                
                // Add price if checked
                if (chkIncludePrice.IsChecked == true)
                {
                    var priceBlock = new TextBlock
                    {
                        Text = $"{product.SellingPrice:N2} DA",
                        FontSize = priceFontSize,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    stackPanel.Children.Add(priceBlock);
                }
                
                // Add stack panel to the border
                borderCell.Child = stackPanel;
                
                // Add to the uniform grid
                uniformGrid.Children.Add(borderCell);
            }
            
            // Add the uniform grid to the page container
            pageContainer.Children.Add(uniformGrid);
            
            // Add the page container to the page
            page.Children.Add(pageContainer);
            
            return page;
        }

        private System.Windows.Size GetLabelSize()
        {
            // Return sizes in points (1/72 inch)
            switch (cmbLabelSize.SelectedIndex)
            {
                case 0: // Small (30x20mm)
                    return new System.Windows.Size(226, 151); // ~80x53mm
                case 1: // Medium (50x30mm)
                    return new System.Windows.Size(283, 189); // ~100x67mm
                case 2: // Large (70x40mm)
                    return new System.Windows.Size(340, 227); // ~120x80mm
                default:
                    return new System.Windows.Size(283, 189); // Medium as default
            }
        }

        private int GetBarcodesPerPage()
        {
            if (cmbBarcodesPerPage.SelectedItem is ComboBoxItem selectedItem)
            {
                return int.Parse(selectedItem.Content.ToString());
            }
            return 1; // Default to 1 barcode per page
        }

        private FixedPage CreateBarcodePage(Product product, System.Windows.Size labelSize)
        {
            var page = new FixedPage();
            page.Width = labelSize.Width;
            page.Height = labelSize.Height;

            // Adjust barcode writer size based on label size
            _barcodeWriter.Options.Width = (int)(labelSize.Width * 0.9);  // Make barcode wider
            _barcodeWriter.Options.Height = (int)(labelSize.Height * 0.4); // Adjust height ratio
            _barcodeWriter.Options.Margin = 1;
            _barcodeWriter.Options.PureBarcode = false; // Show the numbers below barcode

            // Generate barcode
            var bitmap = _barcodeWriter.Write(product.Barcodes.First().Barcode);

            // Create drawing visual for consistent rendering
            var drawingVisual = new DrawingVisual();
            using (var drawingContext = drawingVisual.RenderOpen())
            {
                // Draw barcode
                var barcodeImage = BitmapFrame.Create(bitmap.ToBitmapSource());
                var barcodeRect = new Rect(
                    (page.Width - barcodeImage.Width) / 2,
                    10, // Start a bit lower from the top
                    barcodeImage.Width,
                    barcodeImage.Height);
                drawingContext.DrawImage(barcodeImage, barcodeRect);

                double currentY = barcodeRect.Bottom + 15; // Increase spacing after barcode

                // Add product name if checked
                if (chkIncludeName.IsChecked == true)
                {
                    var fontSize = labelSize.Height * 0.15; // Increase font size
                    var formattedText = new FormattedText(
                        product.Name,
                        System.Globalization.CultureInfo.CurrentCulture,
                        FlowDirection.LeftToRight,
                        new Typeface("Arial"), // Change to Arial for better readability
                        fontSize,
                        Brushes.Black,
                        96);

                    // If text is too wide, scale it down
                    if (formattedText.Width > page.Width - 20)
                    {
                        fontSize *= (page.Width - 20) / formattedText.Width;
                        formattedText = new FormattedText(
                            product.Name,
                            System.Globalization.CultureInfo.CurrentCulture,
                            FlowDirection.LeftToRight,
                            new Typeface("Arial"),
                            fontSize,
                            Brushes.Black,
                            96);
                    }

                    drawingContext.DrawText(formattedText,
                        new Point((page.Width - formattedText.Width) / 2, currentY));
                    currentY += formattedText.Height + 10; // Increase spacing after name
                }

                // Add price if checked
                if (chkIncludePrice.IsChecked == true)
                {
                    var fontSize = labelSize.Height * 0.18; // Increase price font size
                    var priceText = new FormattedText(
                        $"{product.SellingPrice:N2} DA",
                        System.Globalization.CultureInfo.CurrentCulture,
                        FlowDirection.LeftToRight,
                        new Typeface("Arial Bold"), // Make price bold
                        fontSize,
                        Brushes.Black,
                        96);

                    drawingContext.DrawText(priceText,
                        new Point((page.Width - priceText.Width) / 2, currentY));
                }
            }

            // Add the visual to the page
            var visual = new DrawingVisualPresenter { Visual = drawingVisual };
            page.Children.Add(visual);
            return page;
        }

        private FixedPage CreateMultiBarcodePages(List<Product> products, System.Windows.Size pageSize)
        {
            var page = new FixedPage();
            page.Width = pageSize.Width;
            page.Height = pageSize.Height;

            int barcodesPerPage = GetBarcodesPerPage();
            
            // Determine optimal grid layout based on barcode count
            int columns, rows;
            if (barcodesPerPage <= 2) {
                columns = 1;
                rows = barcodesPerPage;
            } 
            else if (barcodesPerPage <= 4) {
                columns = 2;
                rows = 2;
            }
            else if (barcodesPerPage <= 6) {
                columns = 2;
                rows = 3;
            }
            else if (barcodesPerPage <= 9) {
                columns = 3;
                rows = 3;
            }
            else if (barcodesPerPage <= 12) {
                columns = 3;
                rows = 4;
            }
            else {
                columns = 3;
                rows = 5; // Maximum of 15 per page
            }

            // Calculate size of the label area for each barcode
            double labelWidth = page.Width / columns;
            double labelHeight = page.Height / rows;

            // Calculate size of the actual barcode element (smaller than the label area to create margins)
            double barcodeWidth = labelWidth * 0.75;  // Even more spacing between columns
            double barcodeHeight = labelHeight * 0.75; // Even more spacing between rows

            for (int i = 0; i < Math.Min(barcodesPerPage, products.Count); i++)
            {
                Product product = products[i];
                var barcode = product.Barcodes.FirstOrDefault()?.Barcode;
                if (string.IsNullOrEmpty(barcode)) continue;

                // Calculate position
                int row = i / columns;
                int col = i % columns;
                
                // Calculate the center point of this barcode's cell
                double cellCenterX = (col + 0.5) * labelWidth;
                double cellCenterY = (row + 0.5) * labelHeight;
                
                // Create a Grid container for our barcode - Grids handle layout better
                var container = new Grid();
                container.Width = barcodeWidth;
                container.Height = barcodeHeight;
                
                // Create a StackPanel for vertical layout of elements
                var stackPanel = new StackPanel();
                stackPanel.Orientation = Orientation.Vertical;
                stackPanel.HorizontalAlignment = HorizontalAlignment.Center;
                
                // Determine barcode size based on density
                double scaleFactor = Math.Min(1.0, 6.0 / Math.Sqrt(barcodesPerPage));
                int barcodeImageWidth = (int)(barcodeWidth * 0.9);
                int barcodeImageHeight = (int)(barcodeHeight * 0.4);

                // Generate the barcode
                _barcodeWriter.Format = cmbBarcodeType.SelectedIndex switch
                {
                    0 => BarcodeFormat.EAN_13,
                    1 => BarcodeFormat.CODE_128,
                    2 => BarcodeFormat.QR_CODE,
                    _ => BarcodeFormat.EAN_13
                };
                
                _barcodeWriter.Options = new ZXing.Common.EncodingOptions
                {
                    Width = barcodeImageWidth,
                    Height = barcodeImageHeight,
                    Margin = 1
                };
                
                var bitmapImage = _barcodeWriter.Write(barcode).ToBitmapSource();
                
                // Create the barcode image element
                var barcodeImage = new System.Windows.Controls.Image
                {
                    Source = bitmapImage,
                    Stretch = Stretch.None,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                
                stackPanel.Children.Add(barcodeImage);
                
                // Add product name if checked
                if (chkIncludeName.IsChecked == true)
                {
                    // Determine appropriate font size based on available space
                    double fontSize = Math.Max(8, Math.Min(12, barcodeHeight * 0.1));
                    
                    var textBlock = new TextBlock
                    {
                        Text = product.Name,
                        FontSize = fontSize,
                        TextAlignment = TextAlignment.Center,
                        TextWrapping = TextWrapping.Wrap,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 2, 0, 2),
                        MaxWidth = barcodeWidth * 0.95
                    };
                    
                    stackPanel.Children.Add(textBlock);
                }
                
                // Add price if checked
                if (chkIncludePrice.IsChecked == true)
                {
                    // Use slightly larger font for price
                    double fontSize = Math.Max(9, Math.Min(14, barcodeHeight * 0.12));
                    
                    var priceBlock = new TextBlock
                    {
                        Text = $"{product.SellingPrice:N2} DA",
                        FontWeight = FontWeights.Bold,
                        FontSize = fontSize,
                        TextAlignment = TextAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 2, 0, 0)
                    };
                    
                    stackPanel.Children.Add(priceBlock);
                }
                
                // Add the stack panel to the grid
                container.Children.Add(stackPanel);
                
                // Position the container on the page using center coordinates
                Canvas.SetLeft(container, cellCenterX - barcodeWidth / 2);
                Canvas.SetTop(container, cellCenterY - barcodeHeight / 2);
                
                // Add to page
                page.Children.Add(container);
            }

            return page;
        }

        // Helper class to present DrawingVisual
        private class DrawingVisualPresenter : FrameworkElement
        {
            public Visual Visual { get; set; }
            protected override int VisualChildrenCount => 1;
            protected override Visual GetVisualChild(int index) => Visual;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void BarcodeCheckbox_Click(object sender, RoutedEventArgs e)
        {
            // Update selection in the list view when checkboxes are clicked
            if (sender is CheckBox checkBox && checkBox.DataContext is Product product)
            {
                product.IsSelected = checkBox.IsChecked == true;
                
                // If checkbox is checked, ensure the item is selected
                if (checkBox.IsChecked == true)
                {
                    if (!ProductsList.SelectedItems.Contains(product))
                    {
                        ProductsList.SelectedItems.Add(product);
                    }
                }
                else
                {
                    // If checkbox is unchecked, remove the item from selection
                    if (ProductsList.SelectedItems.Contains(product))
                    {
                        ProductsList.SelectedItems.Remove(product);
                    }
                }
            }
        }

        private void BtnSelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var product in _allProducts)
            {
                product.IsSelected = true;
            }
            
            // Update UI to reflect selection
            ProductsList.SelectAll();
            ProductsList.Items.Refresh();
        }

        private void BtnDeselectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var product in _allProducts)
            {
                product.IsSelected = false;
            }
            
            // Update UI to reflect deselection
            ProductsList.UnselectAll();
            ProductsList.Items.Refresh();
        }
    }
} 