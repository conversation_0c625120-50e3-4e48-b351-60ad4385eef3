using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Views.Dialogs
{
    public partial class CategoryDialog : UserControl
    {
        private readonly DatabaseService _dbService;
        private Category _existingCategory;
        private bool _isEditMode;
        private readonly string _dialogIdentifier;

        public Category Result { get; private set; }

        public CategoryDialog(Category category = null, string dialogIdentifier = "RootDialog")
        {
            InitializeComponent();
            
            _dbService = new DatabaseService();
            _dialogIdentifier = dialogIdentifier;
            
            // If a category is provided, we're in edit mode
            if (category != null)
            {
                _existingCategory = category;
                _isEditMode = true;
                LoadCategoryData(category);
                DialogTitle.Text = (string)Application.Current.Resources["EditCategory"];
                btnSave.Content = (string)Application.Current.Resources["UpdateCategory"];
            }
            else
            {
                _isEditMode = false;
                DialogTitle.Text = (string)Application.Current.Resources["AddCategory"];
                btnSave.Content = (string)Application.Current.Resources["AddCategory"];
            }
        }

        private void LoadCategoryData(Category category)
        {
            txtName.Text = category.Name;
            txtDescription.Text = category.Description;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.Close(_dialogIdentifier);
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["CategoryNameRequired"],
                        (string)Application.Current.Resources["ValidationError"],
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var category = _isEditMode ? _existingCategory : new Category();
                
                category.Name = txtName.Text.Trim();
                category.Description = string.IsNullOrWhiteSpace(txtDescription.Text) 
                    ? txtName.Text.Trim() // Set description to category name if empty
                    : txtDescription.Text.Trim();

                if (_isEditMode)
                {
                    _dbService.UpdateCategory(category);
                }
                else
                {
                    _dbService.AddCategory(category);
                }

                Result = category;
                DialogHost.Close(_dialogIdentifier, category);
            }
            catch (Exception ex)
            {
                // Get the error message resource or use a default message
                var errorMessageFormat = Application.Current.Resources["ErrorSavingCategory"] as string ?? "Error saving category: {0}";
                var errorMessage = ex.Message ?? "Unknown error occurred";
                
                MessageBox.Show(
                    string.Format(errorMessageFormat, errorMessage),
                    (string)Application.Current.Resources["ErrorTitle"] ?? "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 