using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using POSSystem.Views.Layouts;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Views
{
    public partial class SalesViewWithLayouts : UserControl
    {
        private readonly ISettingsService _settingsService;
        private readonly IDialogService _dialogService;
        private SaleViewModel ViewModel => (SaleViewModel)DataContext;
        private UserControl _currentLayout;

        public SalesViewWithLayouts()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            _dialogService = App.ServiceProvider?.GetService<IDialogService>() ?? new DialogService();

            this.Loaded += SalesViewWithLayouts_Loaded;
            this.DataContextChanged += SalesViewWithLayouts_DataContextChanged;
        }
        
        private async void SalesViewWithLayouts_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // When DataContext changes, reload the selected layout
            LoadSelectedLayout();

            // ✅ CRITICAL FIX: Move product refresh to background to prevent UI blocking
            if (e.NewValue is SaleViewModel viewModel && viewModel.FilteredProducts.Count == 0)
            {
                // Start background refresh without blocking UI
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await viewModel.RefreshProducts();
                        System.Diagnostics.Debug.WriteLine("[SALES-LAYOUT] Background refresh on DataContext change completed");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error refreshing products on DataContext change: {ex.Message}");
                    }
                });
            }
        }

        private async void SalesViewWithLayouts_Loaded(object sender, RoutedEventArgs e)
        {
            // Load the selected layout immediately (this is fast)
            LoadSelectedLayout();

            // ✅ CRITICAL FIX: Load products asynchronously to prevent UI blocking
            if (ViewModel != null && ViewModel.FilteredProducts.Count == 0)
            {
                // Show loading state immediately
                ViewModel.IsLoading = true;

                // Start product loading in background without blocking UI
                _ = Task.Run(async () =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[SALES-LAYOUT] Starting background product refresh...");
                        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                        await ViewModel.RefreshProducts();

                        stopwatch.Stop();
                        System.Diagnostics.Debug.WriteLine($"[SALES-LAYOUT] ✅ Background product refresh completed in {stopwatch.ElapsedMilliseconds}ms");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALES-LAYOUT] ❌ Error refreshing products on load: {ex.Message}");

                        // Show error on UI thread if needed
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            System.Diagnostics.Debug.WriteLine($"Product loading failed: {ex.Message}");
                        });
                    }
                    finally
                    {
                        // Ensure loading state is cleared
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            ViewModel.IsLoading = false;
                        });
                    }
                });
            }
        }
        
        private void LoadSelectedLayout()
        {
            if (DataContext == null)
                return;

            // Clear existing layout
            layoutContainer.Children.Clear();

            // Get the selected layout theme - Default to Grid instead of Standard
            string layoutTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

            // Create the appropriate layout
            UserControl layoutView = layoutTheme switch
            {
                "Grid" => new Layouts.SalesViewGrid(),
                "Compact" => new Layouts.SalesViewCompact(),
                "Modern" => new Layouts.SalesViewModern(),
                "Standard" => new Layouts.SalesViewStandard(),
                _ => new Layouts.SalesViewGrid()  // Default to grid layout
            };
            
            // Set the DataContext
            layoutView.DataContext = DataContext;
            
            // Add to container
            layoutContainer.Children.Add(layoutView);
            _currentLayout = layoutView;
            
            // Add theme selector button directly in our container
            CreateThemeSelectionButton();
        }
        
        private void CreateThemeSelectionButton()
        {
            // Create a button to open theme selection
            var themeButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignIconButton"),
                ToolTip = Application.Current.FindResource("ChangeLayout") as string,
                Margin = new Thickness(8),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Width = 36,
                Height = 36
            };
            
            themeButton.Content = new PackIcon
            {
                Kind = PackIconKind.ViewDashboard,
                Width = 24,
                Height = 24
            };
            
            themeButton.Click += ThemeButton_Click;
            
            // Add to the container
            layoutContainer.Children.Add(themeButton);
        }
        
        private async void ThemeButton_Click(object sender, RoutedEventArgs e)
        {
            // Create the dialog content
            var stackPanel = new StackPanel { Margin = new Thickness(16) };
            
            // Title
            var title = new TextBlock
            {
                Text = Application.Current.FindResource("SelectLayout") as string,
                Style = (Style)FindResource("MaterialDesignHeadline6TextBlock"),
                Margin = new Thickness(0, 0, 0, 16)
            };
            stackPanel.Children.Add(title);
            
            // Current theme
            var currentTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";
            
            // Add theme options
            var themeOptions = new WrapPanel();
            
            var themes = new[]
            {
                new { Code = "Standard", Name = Application.Current.FindResource("StandardLayout") as string },
                new { Code = "Compact", Name = Application.Current.FindResource("CompactLayout") as string },
                new { Code = "Modern", Name = Application.Current.FindResource("ModernLayout") as string },
                new { Code = "Grid", Name = Application.Current.FindResource("GridLayout") as string }
            };
            
            foreach (var theme in themes)
            {
                var card = new Card
                {
                    Margin = new Thickness(8),
                    Padding = new Thickness(8),
                    Width = 100,
                    Height = 100,
                    Tag = theme.Code
                };
                
                if (theme.Code == currentTheme)
                {
                    card.Background = (System.Windows.Media.Brush)FindResource("PrimaryHueLightBrush");
                }
                
                var cardContent = new StackPanel();
                cardContent.Children.Add(new PackIcon
                {
                    Kind = theme.Code switch
                    {
                        "Standard" => PackIconKind.ViewAgenda,
                        "Compact" => PackIconKind.ViewCompact,
                        "Modern" => PackIconKind.ViewColumn,
                        "Grid" => PackIconKind.ViewGrid,
                        _ => PackIconKind.ViewAgenda
                    },
                    Width = 32,
                    Height = 32,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 8, 0, 8)
                });
                
                cardContent.Children.Add(new TextBlock
                {
                    Text = theme.Name,
                    TextAlignment = TextAlignment.Center,
                    TextWrapping = TextWrapping.Wrap
                });
                
                card.Content = cardContent;
                
                var border = new Border();
                border.Child = card;
                border.MouseDown += ThemeCard_MouseDown;
                
                themeOptions.Children.Add(border);
            }
            
            stackPanel.Children.Add(themeOptions);
            
            // Add note about restart
            var noteText = new TextBlock
            {
                Text = Application.Current.FindResource("LayoutChangeRestart") as string,
                Style = (Style)FindResource("MaterialDesignBody2TextBlock"),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 16, 0, 0)
            };
            stackPanel.Children.Add(noteText);
            
            // Show dialog using DialogService instead of direct DialogHost call
            await _dialogService.ShowDialog(stackPanel);
        }
        
        private void ThemeCard_MouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (sender is Border border && 
                border.Child is Card card && 
                card.Tag is string themeCode)
            {
                // Save the selected theme
                _settingsService.SaveSetting("SalesLayoutTheme", themeCode);
                
                // Ask user if they want to restart
                var result = MessageBox.Show(
                    (Application.Current.FindResource("LayoutChangeRestart") as string) + " " +
                    (Application.Current.FindResource("RestartNow") as string),
                    (Application.Current.FindResource("ThemeChanged") as string),
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // Restart the application
                    System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    Application.Current.Shutdown();
                }
                
                // Close the dialog using DialogService
                _dialogService.CloseDialog();
            }
        }
    }
} 