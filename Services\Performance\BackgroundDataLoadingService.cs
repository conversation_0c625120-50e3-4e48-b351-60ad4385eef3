using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Helpers;
using POSSystem.Models;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// ✅ PERFORMANCE OPTIMIZATION: Service for loading data in background to prevent UI thread blocking
    /// Specifically designed for POS system requirements where UI responsiveness is critical
    /// </summary>
    public class BackgroundDataLoadingService
    {
        private readonly SemaphoreSlim _loadingSemaphore = new(1, 1);
        private readonly Dictionary<string, DateTime> _lastLoadTimes = new();
        private readonly object _lockObject = new();

        /// <summary>
        /// Load products with batch data in background to prevent UI blocking
        /// </summary>
        public async Task<List<Product>> LoadProductsWithBatchesAsync(
            int page = 1, 
            int pageSize = 50, 
            int? categoryId = null,
            CancellationToken cancellationToken = default)
        {
            return await DatabasePerformanceHelper.ExecuteQueryAsync(async context =>
            {
                var query = context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches) // ✅ Pre-load batches to prevent UI thread blocking
                    .Where(p => p.IsActive);

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                return await query
                    .OrderByDescending(p => p.CreatedAt)
                    .ThenBy(p => p.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
            }, $"LoadProductsWithBatches_Page{page}_Size{pageSize}");
        }

        /// <summary>
        /// Load dashboard data in background with progress reporting
        /// </summary>
        public async Task<DashboardData> LoadDashboardDataAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            IProgress<int> progress = null,
            CancellationToken cancellationToken = default)
        {
            var cacheKey = $"Dashboard_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Check if we've loaded this recently (within 5 minutes)
            lock (_lockObject)
            {
                if (_lastLoadTimes.TryGetValue(cacheKey, out var lastLoad) && 
                    DateTime.UtcNow - lastLoad < TimeSpan.FromMinutes(5))
                {
                    Debug.WriteLine($"[BG-LOAD] Skipping dashboard load - cached data is recent");
                    return null; // Indicate cached data should be used
                }
            }

            await _loadingSemaphore.WaitAsync(cancellationToken);
            
            try
            {
                progress?.Report(10);

                var (salesData, productData) = await DatabasePerformanceHelper.ExecuteParallelQueriesAsync(
                    async context => await LoadSalesDataAsync(context, startDate, endDate, cancellationToken),
                    async context => await LoadProductStatsAsync(context, cancellationToken),
                    "LoadSalesData",
                    "LoadProductStats");

                progress?.Report(70);

                var customerData = await DatabasePerformanceHelper.ExecuteQueryAsync(
                    async context => await LoadCustomerStatsAsync(context, startDate, endDate, cancellationToken),
                    "LoadCustomerStats");

                progress?.Report(90);

                var dashboardData = new DashboardData
                {
                    SalesData = salesData,
                    ProductStats = productData,
                    CustomerStats = customerData,
                    LoadedAt = DateTime.UtcNow
                };

                // Update cache timestamp
                lock (_lockObject)
                {
                    _lastLoadTimes[cacheKey] = DateTime.UtcNow;
                }

                progress?.Report(100);
                return dashboardData;
            }
            finally
            {
                _loadingSemaphore.Release();
            }
        }

        /// <summary>
        /// Load data with UI update callback to prevent blocking
        /// </summary>
        public async Task LoadDataWithUIUpdateAsync<T>(
            Func<Task<T>> dataLoader,
            Action<T> uiUpdater,
            string operationName = "Background Data Load",
            Action<Exception> errorHandler = null)
        {
            try
            {
                // Load data on background thread
                var data = await Task.Run(dataLoader);

                // Update UI on main thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    uiUpdater(data);
                }, DispatcherPriority.Normal);

                Debug.WriteLine($"[BG-LOAD] ✅ {operationName} completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BG-LOAD] ❌ {operationName} failed: {ex.Message}");
                
                if (errorHandler != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        errorHandler(ex);
                    });
                }
                else
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// Preload frequently accessed data to improve responsiveness
        /// </summary>
        public async Task PreloadFrequentDataAsync()
        {
            var tasks = new List<Task>
            {
                // Preload categories (small, frequently accessed)
                DatabasePerformanceHelper.ExecuteQueryAsync(
                    async context => await context.Categories.AsNoTracking().ToListAsync(),
                    "PreloadCategories"),

                // Preload units of measure (small, frequently accessed)
                DatabasePerformanceHelper.ExecuteQueryAsync(
                    async context => await context.UnitsOfMeasure.AsNoTracking().ToListAsync(),
                    "PreloadUnitsOfMeasure"),

                // Preload top products (for quick access)
                DatabasePerformanceHelper.ExecuteQueryAsync(
                    async context => await context.Products
                        .Include(p => p.Batches)
                        .Where(p => p.IsActive)
                        .OrderByDescending(p => p.CreatedAt)
                        .Take(20)
                        .AsNoTracking()
                        .ToListAsync(),
                    "PreloadTopProducts")
            };

            await Task.WhenAll(tasks);
            Debug.WriteLine("[BG-LOAD] ✅ Frequent data preloading completed");
        }

        private async Task<SalesData> LoadSalesDataAsync(POSDbContext context, DateTime? startDate, DateTime? endDate, CancellationToken cancellationToken)
        {
            var query = context.Sales.AsNoTracking();
            
            if (startDate.HasValue)
                query = query.Where(s => s.SaleDate >= startDate.Value);
            
            if (endDate.HasValue)
                query = query.Where(s => s.SaleDate <= endDate.Value);

            var sales = await query
                .Select(s => new { s.SaleDate, s.GrandTotal, s.CustomerId })
                .ToListAsync(cancellationToken);

            return new SalesData
            {
                TotalSales = sales.Sum(s => s.GrandTotal),
                SalesCount = sales.Count,
                AverageSale = sales.Count > 0 ? sales.Average(s => s.GrandTotal) : 0,
                UniqueCustomers = sales.Where(s => s.CustomerId.HasValue).Select(s => s.CustomerId).Distinct().Count()
            };
        }

        private async Task<ProductStats> LoadProductStatsAsync(POSDbContext context, CancellationToken cancellationToken)
        {
            var products = await context.Products
                .Where(p => p.IsActive)
                .Select(p => new { p.StockQuantity, p.MinimumStock, p.SellingPrice, p.PurchasePrice })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            return new ProductStats
            {
                TotalProducts = products.Count,
                LowStockCount = products.Count(p => p.StockQuantity <= p.MinimumStock),
                OutOfStockCount = products.Count(p => p.StockQuantity == 0),
                TotalInventoryValue = products.Sum(p => p.StockQuantity * p.SellingPrice),
                TotalInventoryCost = products.Sum(p => p.StockQuantity * p.PurchasePrice)
            };
        }

        private async Task<CustomerStats> LoadCustomerStatsAsync(POSDbContext context, DateTime? startDate, DateTime? endDate, CancellationToken cancellationToken)
        {
            var query = context.Customers.AsNoTracking();
            
            var customers = await query
                .Select(c => new { c.Id, c.CreatedAt })
                .ToListAsync(cancellationToken);

            var recentCustomers = startDate.HasValue 
                ? customers.Where(c => c.CreatedAt >= startDate.Value).Count()
                : customers.Count;

            return new CustomerStats
            {
                TotalCustomers = customers.Count,
                NewCustomers = recentCustomers
            };
        }
    }

    // Data models for background loading
    public class DashboardData
    {
        public SalesData SalesData { get; set; }
        public ProductStats ProductStats { get; set; }
        public CustomerStats CustomerStats { get; set; }
        public DateTime LoadedAt { get; set; }
    }

    public class SalesData
    {
        public decimal TotalSales { get; set; }
        public int SalesCount { get; set; }
        public decimal AverageSale { get; set; }
        public int UniqueCustomers { get; set; }
    }

    public class ProductStats
    {
        public int TotalProducts { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal TotalInventoryCost { get; set; }
    }

    public class CustomerStats
    {
        public int TotalCustomers { get; set; }
        public int NewCustomers { get; set; }
    }
}
