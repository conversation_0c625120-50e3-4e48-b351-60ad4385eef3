-- Verification Script: Check Weight-Based Product Support
-- Run this script to verify if the weight-based product migration has been applied

PRINT '=== Weight-Based Product Support Verification ===';
PRINT '';

-- Check if IsWeightBased column exists in Products table
PRINT '1. Checking Products table structure...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
BEGIN
    PRINT '✅ IsWeightBased column EXISTS in Products table';
    
    -- Check the data type and default value
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased';
END
ELSE
BEGIN
    PRINT '❌ IsWeightBased column MISSING from Products table';
    PRINT '   → Need to run migration: Migrations/AddWeightBasedProductSupport.sql';
END

PRINT '';

-- Check SaleItems table Quantity column type
PRINT '2. Checking SaleItems table Quantity column...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity' 
           AND DATA_TYPE = 'decimal')
BEGIN
    PRINT '✅ SaleItems.Quantity is DECIMAL type (supports weight-based products)';
    
    -- Show the precision and scale
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';
END
ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity' 
                AND DATA_TYPE = 'int')
BEGIN
    PRINT '⚠️  SaleItems.Quantity is still INT type (needs migration)';
    PRINT '   → Need to run migration: Migrations/AddWeightBasedProductSupport.sql';
END
ELSE
BEGIN
    PRINT '❌ SaleItems.Quantity column not found or unexpected type';
END

PRINT '';

-- Check CartItems table Quantity column type
PRINT '3. Checking CartItems table Quantity column...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'CartItems' AND COLUMN_NAME = 'Quantity' 
           AND DATA_TYPE = 'decimal')
BEGIN
    PRINT '✅ CartItems.Quantity is DECIMAL type (supports weight-based products)';
END
ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'CartItems' AND COLUMN_NAME = 'Quantity' 
                AND DATA_TYPE = 'int')
BEGIN
    PRINT '⚠️  CartItems.Quantity is still INT type (needs migration)';
END
ELSE
BEGIN
    PRINT '❌ CartItems.Quantity column not found or unexpected type';
END

PRINT '';

-- Check if any weight-based products exist
PRINT '4. Checking for existing weight-based products...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
BEGIN
    DECLARE @WeightBasedCount INT;
    SELECT @WeightBasedCount = COUNT(*) FROM Products WHERE IsWeightBased = 1;
    
    IF @WeightBasedCount > 0
    BEGIN
        PRINT '✅ Found ' + CAST(@WeightBasedCount AS VARCHAR(10)) + ' weight-based products';
        
        -- Show some examples
        PRINT '   Examples:';
        SELECT TOP 5 
            Id,
            Name,
            SKU,
            IsWeightBased,
            StockQuantity
        FROM Products 
        WHERE IsWeightBased = 1;
    END
    ELSE
    BEGIN
        PRINT '⚠️  No weight-based products found';
        PRINT '   → Create test weight-based products to verify functionality';
    END
END

PRINT '';

-- Check for any products with barcodes (for barcode testing)
PRINT '5. Checking products with barcodes...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ProductBarcodes')
BEGIN
    DECLARE @ProductsWithBarcodes INT;
    SELECT @ProductsWithBarcodes = COUNT(DISTINCT ProductId) FROM ProductBarcodes;
    
    PRINT '✅ Found ' + CAST(@ProductsWithBarcodes AS VARCHAR(10)) + ' products with barcodes';
    
    -- Check if any weight-based products have barcodes
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
    BEGIN
        DECLARE @WeightBasedWithBarcodes INT;
        SELECT @WeightBasedWithBarcodes = COUNT(DISTINCT p.Id)
        FROM Products p
        INNER JOIN ProductBarcodes pb ON p.Id = pb.ProductId
        WHERE p.IsWeightBased = 1;
        
        IF @WeightBasedWithBarcodes > 0
        BEGIN
            PRINT '✅ Found ' + CAST(@WeightBasedWithBarcodes AS VARCHAR(10)) + ' weight-based products with barcodes';
        END
        ELSE
        BEGIN
            PRINT '⚠️  No weight-based products have barcodes yet';
            PRINT '   → Add barcodes to weight-based products for barcode scanning tests';
        END
    END
END
ELSE
BEGIN
    PRINT '❌ ProductBarcodes table not found';
END

PRINT '';
PRINT '=== Summary ===';
PRINT 'If you see ❌ or ⚠️ above, you need to:';
PRINT '1. Run the migration script: Migrations/AddWeightBasedProductSupport.sql';
PRINT '2. Create test weight-based products';
PRINT '3. Add barcodes to test products';
PRINT '4. Test search and barcode functionality';
PRINT '';
PRINT 'Migration script location: Migrations/AddWeightBasedProductSupport.sql';
