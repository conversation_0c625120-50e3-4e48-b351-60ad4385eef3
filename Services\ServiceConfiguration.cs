using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Repositories;
using POSSystem.Services.ErrorHandling;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.UserManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.DataAccess;
using POSSystem.ViewModels;
using POSSystem.ViewModels.Dashboard;
using System;
using System.IO;

namespace POSSystem.Services
{
    /// <summary>
    /// Configures dependency injection for the POS System application
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// Configures all services for dependency injection
        /// </summary>
        /// <param name="services">The service collection to configure</param>
        /// <param name="databasePath">Optional database path override</param>
        public static void ConfigureServices(IServiceCollection services, string databasePath = null)
        {
            // ✅ LOAD CONFIGURATION: Load appsettings.json if it exists for logging configuration
            var configuration = LoadConfiguration();
            ConfigureLoggingFromConfiguration(services, configuration);
            ConfigureEntityFramework(services, databasePath);
        }

        /// <summary>
        /// Configures services with explicit configuration
        /// </summary>
        /// <param name="services">The service collection to configure</param>
        /// <param name="databasePath">Optional database path override</param>
        /// <param name="configuration">Configuration to use</param>
        public static void ConfigureServicesWithConfiguration(IServiceCollection services, string databasePath = null, IConfiguration configuration = null)
        {
            ConfigureLoggingFromConfiguration(services, configuration);
            ConfigureEntityFramework(services, databasePath);
        }

        private static void ConfigureLoggingFromConfiguration(IServiceCollection services, IConfiguration configuration)
        {
            // Configure logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);

                // ✅ SUPPRESS EF CORE SQL QUERY LOGGING: Disable Entity Framework database command logging
                builder.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.None);
                builder.AddFilter("Microsoft.EntityFrameworkCore.Query", LogLevel.None);
                builder.AddFilter("Microsoft.EntityFrameworkCore.Update", LogLevel.None);
                builder.AddFilter("Microsoft.EntityFrameworkCore.Infrastructure", LogLevel.Warning);

                // Apply configuration from appsettings.json if available
                if (configuration != null)
                {
                    builder.AddConfiguration(configuration.GetSection("Logging"));
                }
            });
        }

        private static void ConfigureEntityFramework(IServiceCollection services, string databasePath)
        {
            // Configure database
            ConfigureDatabase(services, databasePath);

            // Register core services
            RegisterCoreServices(services);

            // Register business services
            RegisterBusinessServices(services);

            // Register ViewModels
            RegisterViewModels(services);
        }

        /// <summary>
        /// Configures database services
        /// </summary>
        private static void ConfigureDatabase(IServiceCollection services, string databasePath)
        {
            // Determine database path
            if (string.IsNullOrEmpty(databasePath))
            {
                databasePath = GetDefaultDatabasePath();
            }

            // ✅ ENHANCED: Ensure database directory exists and handle file access issues
            try
            {
                var dbDirectory = Path.GetDirectoryName(databasePath);
                if (!string.IsNullOrEmpty(dbDirectory) && !Directory.Exists(dbDirectory))
                {
                    Directory.CreateDirectory(dbDirectory);
                    System.Diagnostics.Debug.WriteLine($"Created database directory: {dbDirectory}");
                }

                // Test file access before configuring EF
                if (!File.Exists(databasePath))
                {
                    // Create empty database file to test write access
                    File.WriteAllText(databasePath, "");
                    System.Diagnostics.Debug.WriteLine($"Created database file: {databasePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database path setup error: {ex.Message}");
                // Fall back to a safe location
                databasePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "POSSystem", "pos.db");
                var fallbackDir = Path.GetDirectoryName(databasePath);
                Directory.CreateDirectory(fallbackDir);
                System.Diagnostics.Debug.WriteLine($"Using fallback database path: {databasePath}");
            }

            // ✅ CRITICAL FIX: Configure Entity Framework with performance optimizations
            services.AddDbContext<POSDbContext>(options =>
            {
                options.UseSqlite($"Data Source={databasePath}");

#if DEBUG
                // Only enable sensitive data logging and detailed errors in debug builds
                options.EnableSensitiveDataLogging(true);  // Enable for debugging
                options.EnableDetailedErrors(true);  // Enable for debugging
#endif

                options.EnableServiceProviderCaching(true);
                options.ConfigureWarnings(warnings =>
                {
                    warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.MultipleCollectionIncludeWarning);
                });
            }, ServiceLifetime.Scoped);

            // Register database service
            services.AddScoped<IDatabaseService, DatabaseService>();

            // Also register the concrete DatabaseService for components that need it directly
            services.AddScoped<DatabaseService>();

            // Also register the legacy interface for backward compatibility
            services.AddScoped<IDbService>(provider => provider.GetService<IDatabaseService>() as IDbService);
        }

        /// <summary>
        /// Registers core application services
        /// </summary>
        private static void RegisterCoreServices(IServiceCollection services)
        {
            // Error handling service (register first for use by other services)
            services.AddSingleton<POSSystem.Services.ErrorHandling.IErrorHandlingService, POSSystem.Services.ErrorHandling.ErrorHandlingService>();

            // Authentication and security
            services.AddSingleton<IPasswordService, PasswordService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>(); // Changed to Scoped to consume IDatabaseService
            services.AddSingleton<ILicenseService, LicenseService>();

            // ✅ FIX: Register UserPermissionsService for DI
            services.AddScoped<UserPermissionsService>();
            services.AddScoped<IUserPermissionsService, UserPermissionsService>();

            // Settings and configuration
            services.AddSingleton<ISettingsService, SettingsService>();
            services.AddSingleton<IThemeService, ThemeService>();

            // UI services
            services.AddTransient<IDialogService, DialogService>();
            services.AddSingleton<ISoundService, SoundService>();
        }

        /// <summary>
        /// Registers business logic services
        /// </summary>
        private static void RegisterBusinessServices(IServiceCollection services)
        {
            // Business services
            services.AddScoped<IAlertService, AlertService>();
            services.AddScoped<ICashDrawerService, CashDrawerService>();
            services.AddScoped<ICustomerService, POSSystem.Services.CustomerManagementService>();
            services.AddScoped<IDiscountService, DiscountService>();
            services.AddScoped<IFavoriteService, FavoriteService>();
            services.AddScoped<IUserPermissionsService, UserPermissionsService>();

            // ✅ NEW: Focused management services (extracted from DatabaseService)

            // Inventory stock service (centralized stock mutations)
            services.AddScoped<POSSystem.Services.InventoryManagement.IStockService, POSSystem.Services.InventoryManagement.StockService>();

            services.AddScoped<IProductManagementService, ProductManagementService>();
            services.AddScoped<ISalesManagementService, SalesManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagement.CustomerManagementService>();
            services.AddScoped<IUserManagementService, UserManagementService>();
            services.AddScoped<IInventoryManagementService, InventoryManagementService>();

            // ✅ NEW: Unified data access service (standardizes all data patterns)
            services.AddScoped<IUnifiedDataService, UnifiedDataService>();

            // NEW: Repository services (working alongside existing DatabaseService)
            RegisterRepositories(services);

            // Adapter for gradual migration
            services.AddScoped<RepositoryServiceAdapter>();

            // Printing and reporting services
            services.AddTransient<IInvoicePrintService, InvoicePrintService>();
            services.AddTransient<ISalePrintService, SalePrintService>();
            services.AddScoped<POSSystem.Services.Interfaces.IChartService, POSSystem.Services.ChartService>();

            // Dashboard services
            RegisterDashboardServices(services);

            // Utility services
            services.AddSingleton<IInvoiceNumberService, InvoiceNumberService>();
            services.AddTransient<IProductLookupService, ProductLookupService>();

            // Draft Invoice Services
            services.AddScoped<DraftInvoiceNotificationService>();
            services.AddScoped<DraftInvoiceService>();

            // ✅ Register ViewModels as Transient for proper DI (SaleViewModel registered separately in RegisterViewModels)
            services.AddTransient<POSSystem.ViewModels.ProductsViewModel>();
            services.AddTransient<POSSystem.ViewModels.DashboardViewModel>();
            services.AddTransient<POSSystem.ViewModels.CustomersViewModel>();
            services.AddTransient<POSSystem.ViewModels.SalesHistoryViewModel>();
            // ✅ Register RefactoredDashboardViewModel with explicit dependencies including real-time updates
            services.AddTransient<POSSystem.ViewModels.Dashboard.RefactoredDashboardViewModel>(provider =>
            {
                var dataProvider = provider.GetRequiredService<POSSystem.ViewModels.Dashboard.IDashboardDataProvider>();
                var dataService = provider.GetRequiredService<POSSystem.ViewModels.Dashboard.DashboardDataService>();
                var chartService = provider.GetRequiredService<POSSystem.ViewModels.Dashboard.ChartService>();
                var metricsService = provider.GetRequiredService<POSSystem.ViewModels.Dashboard.MetricsCalculationService>();
                var parameterManager = provider.GetRequiredService<POSSystem.ViewModels.Dashboard.ChartParameterManager>();

                var realTimeService = provider.GetService<POSSystem.Services.RealTime.DashboardUpdateService>(); // Optional

                return new POSSystem.ViewModels.Dashboard.RefactoredDashboardViewModel(
                    dataProvider, dataService, chartService, metricsService, parameterManager, realTimeService);
            });
        }

        /// <summary>
        /// Registers ViewModels for dependency injection with repository support
        /// </summary>
        private static void RegisterViewModels(IServiceCollection services)
        {
            // ✅ Main ViewModels with repository pattern support
            services.AddTransient<SaleViewModel>(provider => new SaleViewModel(
                provider.GetRequiredService<IDatabaseService>(),
                provider.GetRequiredService<IAuthenticationService>(),
                provider.GetRequiredService<ISettingsService>(),
                provider.GetRequiredService<IThemeService>(),
                provider.GetRequiredService<ICustomerService>(),
                provider.GetRequiredService<ICashDrawerService>(),
                provider.GetRequiredService<ISoundService>(),
                provider.GetRequiredService<IDiscountService>(),
                provider.GetRequiredService<IInvoiceNumberService>(),
                provider.GetService<RepositoryServiceAdapter>(), // Optional
                provider.GetRequiredService<POSSystem.Services.InventoryManagement.IStockService>()
            ));

            services.AddTransient<ProductsViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<CustomersViewModel>();
            services.AddTransient<CategoriesViewModel>();
            services.AddTransient<UsersViewModel>();
            services.AddTransient<SuppliersViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>(provider => new SettingsViewModel(
                provider.GetRequiredService<IAuthenticationService>(),
                provider.GetRequiredService<ISettingsService>()
            ));
            services.AddTransient<CashDrawerViewModel>();
            services.AddTransient<SalesHistoryViewModel>();
            services.AddTransient<InvoiceViewModel>(provider => new InvoiceViewModel(
                provider.GetRequiredService<IDatabaseService>(),
                provider.GetRequiredService<IAuthenticationService>(),
                provider.GetRequiredService<POSSystem.Services.InventoryManagement.IStockService>()));
            services.AddTransient<LoyaltyProgramViewModel>();
            services.AddTransient<BusinessExpenseViewModel>();
            services.AddTransient<UnpaidTransactionsViewModel>();
            services.AddTransient<CashDrawerHistoryViewModel>();

            // Dialog ViewModels
            services.AddTransient<LicenseActivationViewModel>();
            services.AddTransient<PaymentProcessingViewModel>();
            services.AddTransient<ReceiptViewModel>();
        }

        /// <summary>
        /// Registers repository services for improved database access
        /// SAFE: These work alongside existing DatabaseService
        /// </summary>
        private static void RegisterRepositories(IServiceCollection services)
        {
            // Repository pattern for clean data access
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<ISaleRepository, SaleRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();

            // TODO: Add more repositories as we implement them
            // services.AddScoped<IUserRepository, UserRepository>();
            // services.AddScoped<ISupplierRepository, SupplierRepository>();
        }

        /// <summary>
        /// Registers dashboard-specific services
        /// </summary>
        private static void RegisterDashboardServices(IServiceCollection services)
        {
            // ✅ Advanced caching services (singleton for performance)
            services.AddSingleton<POSSystem.Services.Caching.ICacheService, POSSystem.Services.Caching.AdvancedCacheService>();

            // ✅ Register CachedRepositoryService only if repositories are available
            services.AddScoped<POSSystem.Services.Caching.CachedRepositoryService>(provider =>
            {
                var cache = provider.GetRequiredService<POSSystem.Services.Caching.ICacheService>();
                var databaseService = provider.GetRequiredService<DatabaseService>();
                var productRepo = provider.GetService<IProductRepository>();
                var customerRepo = provider.GetService<ICustomerRepository>();
                var logger = provider.GetService<ILogger<POSSystem.Services.Caching.CachedRepositoryService>>();

                return new POSSystem.Services.Caching.CachedRepositoryService(cache, databaseService, productRepo, customerRepo, logger);
            });

            // ✅ Query optimization services
            services.AddScoped<POSSystem.Services.QueryOptimization.OptimizedQueryService>();
            services.AddScoped<POSSystem.Services.QueryOptimization.DashboardQueryService>();
            services.AddScoped<POSSystem.Services.QueryOptimization.DatabaseIndexService>();

            // ✅ Performance monitoring services
            services.AddSingleton<POSSystem.Services.Monitoring.PerformanceMonitoringService>();
            services.AddScoped<POSSystem.Services.Monitoring.PerformanceDashboardService>();
            services.AddScoped<POSSystem.Services.Monitoring.PerformanceMonitoringInterceptor>();

            // ✅ Enhanced error handling and logging services
            services.AddScoped<POSSystem.Services.ErrorHandling.IErrorHandlingService, POSSystem.Services.ErrorHandling.ErrorHandlingService>();
            services.AddScoped<POSSystem.Services.Logging.IEnhancedLoggingService, POSSystem.Services.Logging.EnhancedLoggingService>();

            // ✅ PERFORMANCE CRITICAL: Async database service for UI thread protection
            services.AddScoped<POSSystem.Services.Performance.AsyncDatabaseService>();

            // ✅ MEMORY CRITICAL: Advanced memory management for leak prevention
            services.AddSingleton<POSSystem.Services.Memory.AdvancedMemoryManager>();
            services.AddSingleton<POSSystem.Services.Memory.ViewModelMemoryTracker>();

            // ✅ DATABASE CRITICAL: Query optimization and N+1 problem elimination
            services.AddScoped<POSSystem.Services.QueryOptimization.N1QueryOptimizer>();
            services.AddSingleton<POSSystem.Services.QueryOptimization.DatabaseIndexOptimizer>();
            services.AddSingleton<POSSystem.Services.QueryOptimization.QueryPerformanceMonitor>();

            // ✅ UI CRITICAL: Rendering optimization and virtualization services
            services.AddSingleton<POSSystem.Services.UI.UIRenderingPerformanceMonitor>();
            services.AddTransient(typeof(POSSystem.Services.UI.AdvancedVirtualizationService<>));

            // ✅ STARTUP CRITICAL: Performance optimization and lazy loading services
            services.AddSingleton<POSSystem.Services.Startup.StartupPerformanceMonitor>();
            services.AddSingleton<POSSystem.Services.Startup.ParallelServiceInitializer>();
            services.AddSingleton<POSSystem.Services.Startup.LazyLoadingManager>();
            services.AddSingleton<POSSystem.Services.Startup.ProgressiveUILoader>();
            services.AddSingleton<POSSystem.Services.Startup.OptimizedStartupOrchestrator>();

            // ✅ DI cleanup and validation services
            services.AddScoped<POSSystem.Services.DependencyInjection.DICleanupService>();

            // ✅ Background services for performance
            services.AddHostedService<POSSystem.Services.BackgroundServices.DashboardPreloadService>();
            services.AddHostedService<POSSystem.Services.BackgroundServices.DatabaseOptimizationService>();
            services.AddHostedService<POSSystem.Services.BackgroundServices.PerformanceMonitoringBackgroundService>();
            services.AddHostedService<POSSystem.Services.Memory.MemoryOptimizationService>();

            // ✅ Manual optimization services
            services.AddScoped<POSSystem.Services.BackgroundServices.ManualDatabaseOptimizationService>();
            services.AddScoped<POSSystem.Services.BackgroundServices.ManualPerformanceMonitoringService>();

            // ✅ Real-time services
            services.AddScoped<POSSystem.Services.RealTime.DashboardUpdateService>(); // Changed to Scoped to consume IDatabaseService
            services.AddSingleton<POSSystem.Services.RealTime.SalesEventCacheInvalidationService>(); // Subscribes to sale events and invalidates caches

            // ✅ Chart optimization services
            services.AddSingleton<POSSystem.Services.ChartOptimization.ChartPerformanceService>();

            // ✅ Pagination services
            services.AddTransient(typeof(POSSystem.Services.Pagination.SmartPaginationService<>));

            // ✅ Performance monitoring services
            services.AddSingleton<POSSystem.Services.Monitoring.DashboardPerformanceMonitor>();

            // ✅ Background loading services
            services.AddScoped<POSSystem.Services.BackgroundLoading.BackgroundDataLoader>(); // Changed to Scoped to consume scoped services
            services.AddScoped<POSSystem.Services.BackgroundLoading.ProgressiveDataLoader>();

            // ✅ Database health and transaction management
            services.AddScoped<POSSystem.Services.DatabaseHealth.DatabaseHealthService>(); // Changed to Scoped to consume POSDbContext
            services.AddScoped<POSSystem.Services.DatabaseHealth.TransactionManager>();

            // Dashboard data provider (adapter pattern)
            services.AddScoped<IDashboardDataProvider, DatabaseServiceAdapter>();

            // Dashboard business logic services (concrete classes only for now)
            services.AddScoped<DashboardDataService>();
            services.AddScoped<POSSystem.ViewModels.Dashboard.ChartService>();
            services.AddScoped<MetricsCalculationService>();
            services.AddScoped<ChartParameterManager>();


            // Refactored dashboard ViewModel (already registered above with full namespace)
        }

        /// <summary>
        /// Gets the default database path
        /// </summary>
        private static string GetDefaultDatabasePath()
        {
            try
            {
                var settingsService = new SettingsService();
                string configuredPath = settingsService.GetSetting("DatabaseLocation");

                if (!string.IsNullOrEmpty(configuredPath) && File.Exists(configuredPath))
                {
                    return configuredPath;
                }

                // Default to application directory
                string dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
                return dbPath;
            }
            catch (Exception)
            {
                // Fallback to safe location
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
            }
        }

        /// <summary>
        /// Creates a configured service provider
        /// </summary>
        /// <param name="databasePath">Optional database path override</param>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider CreateServiceProvider(string databasePath = null)
        {
            var services = new ServiceCollection();
            ConfigureServices(services, databasePath);
            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Creates a host builder with configured services
        /// </summary>
        /// <param name="databasePath">Optional database path override</param>
        /// <returns>Configured host builder</returns>
        public static IHostBuilder CreateHostBuilder(string databasePath = null)
        {
            return Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    ConfigureServices(services, databasePath);
                });
        }

        /// <summary>
        /// Loads configuration from appsettings.json files if they exist
        /// </summary>
        /// <returns>Configuration or null if files don't exist</returns>
        private static IConfiguration LoadConfiguration()
        {
            try
            {
                var builder = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory);

                // Add appsettings.json if it exists
                var appSettingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                if (File.Exists(appSettingsPath))
                {
                    builder.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                }

                // Add appsettings.Development.json if it exists and we're in debug mode
#if DEBUG
                var devSettingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.Development.json");
                if (File.Exists(devSettingsPath))
                {
                    builder.AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true);
                }
#endif

                return builder.Build();
            }
            catch
            {
                // If configuration loading fails, return null to use default settings
                return null;
            }
        }
    }
}
