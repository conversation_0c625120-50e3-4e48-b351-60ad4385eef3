<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CategoriesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800"
             Background="{DynamicResource AppBackgroundGradient}">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:CategoryButtonTextConverter x:Key="CategoryButtonTextConverter"/>
        
        <!-- Loading Overlay Style -->
        <Style x:Key="LoadingOverlayStyle" TargetType="Grid">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="Opacity" Value="0.8"/>
            <Setter Property="Visibility" Value="Collapsed"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                    <Setter Property="Visibility" Value="Visible"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <!-- Add DialogHost wrapper -->
    <md:DialogHost Identifier="RootDialog">
        <Grid Margin="20" Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Statistics Panel -->
            <md:Card Grid.Row="0" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    md:ElevationAssist.Elevation="Dp2"
                    UniformCornerRadius="4"
                    Margin="0,0,0,10">
                <StackPanel Margin="16">
                    <TextBlock Text="{DynamicResource CategoryDetails}" 
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Foreground="{DynamicResource MaterialDesignBody}"
                              Margin="0,0,0,15"/>
                          
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Total Categories -->
                        <StackPanel Grid.Column="0" Margin="10">
                            <TextBlock Text="{DynamicResource TotalCategories}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="FolderMultiple" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="{Binding TotalCategories}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Total Products -->
                        <StackPanel Grid.Column="1" Margin="10">
                            <TextBlock Text="{DynamicResource TotalProducts}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="Package" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                <TextBlock Text="{Binding TotalProducts}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Average Products per Category -->
                        <StackPanel Grid.Column="2" Margin="10">
                            <TextBlock Text="{DynamicResource CategoryTotalProducts}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="ChartBar" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource PrimaryHueLightBrush}"/>
                                <TextBlock Text="{Binding AverageProductsPerCategory, StringFormat={}{0:N1}}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </md:Card>

            <!-- Search and Tools Panel -->
            <md:Card Grid.Row="1" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    md:ElevationAssist.Elevation="Dp2"
                    UniformCornerRadius="4"
                    Margin="0,0,0,10">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Search Box -->
                    <TextBox Grid.Column="0"
                            x:Name="txtSearch"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            md:HintAssist.Hint="{DynamicResource Search}"
                            KeyDown="SearchBox_KeyDown"
                            TextChanged="SearchBox_TextChanged"
                            Margin="0,0,10,0"
                            Foreground="{DynamicResource MaterialDesignBody}"
                            Background="{DynamicResource MaterialDesignPaper}"/>
                    
                    <!-- Add New Category Button -->
                    <Button Grid.Column="1"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                           BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                           md:ButtonAssist.CornerRadius="4"
                           Click="AddNewCategory_Click">
                        <StackPanel Orientation="Horizontal">
                            <md:PackIcon Kind="FolderPlus" 
                                       Width="24" 
                                       Height="24"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource AddCategory}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </md:Card>

            <!-- Categories DataGrid -->
            <md:Card Grid.Row="2" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    md:ElevationAssist.Elevation="Dp2"
                    UniformCornerRadius="4">
                <Grid>
                    <DataGrid ItemsSource="{Binding Categories}"
                             Style="{StaticResource AppDataGridStyle}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             SelectionMode="Single"
                             IsReadOnly="True"
                             Background="Transparent"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             BorderBrush="{DynamicResource MaterialDesignDivider}"
                             RowBackground="{DynamicResource MaterialDesignPaper}"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             GridLinesVisibility="None"
                             IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                        <DataGrid.Resources>
                            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                                           Color="{DynamicResource MaterialDesignSelection}"/>
                            <SolidColorBrush x:Key="{x:Static SystemColors.ControlBrushKey}" 
                                           Color="Transparent"/>
                            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" 
                                           Color="{DynamicResource MaterialDesignPaper}"/>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="{DynamicResource ID}" Binding="{Binding Id}" Width="50">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource CategoryName}" Binding="{Binding Name}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource CategoryDescription}" Binding="{Binding Description}" Width="2*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="{DynamicResource Products}" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Products.Count}" 
                                                 HorizontalAlignment="Center"
                                                 Foreground="{DynamicResource MaterialDesignBody}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <!-- Action Buttons -->
                            <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="{DynamicResource Edit}"
                                                    Click="EditCategory_Click"
                                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                                    Height="35"
                                                    Width="80"
                                                    Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                    BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                                    Margin="0,0,4,0"/>
                                            <Button Content="{DynamicResource Delete}"
                                                    Click="DeleteCategory_Click"
                                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                                    Height="35"
                                                    Width="80"
                                                    Foreground="Red"
                                                    BorderBrush="Red"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Loading Overlay -->
                    <Grid Style="{StaticResource LoadingOverlayStyle}">
                        <StackPanel HorizontalAlignment="Center" 
                                  VerticalAlignment="Center">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Value="0"
                                       IsIndeterminate="True"
                                       Width="50"
                                       Height="50"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource Loading}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Margin="0,16,0,0"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </md:Card>
        </Grid>
    </md:DialogHost>
</UserControl> 