-- POS System Test Database - Minimal Data Generator
-- This script generates just enough sample data for testing

PRAGMA foreign_keys = OFF; -- Temporarily disable foreign key constraints for easier data loading

BEGIN TRANSACTION;

-- Clean up existing test data 
DELETE FROM UserFavorites;
DELETE FROM Discounts;
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM CashTransactions;
DELETE FROM CashDrawers WHERE Id > 0;
DELETE FROM InventoryTransactions;
DELETE FROM PurchaseOrderItems;
DELETE FROM PurchaseOrders;
DELETE FROM LoyaltyTransactions;

-- Reset product stocks to initial values
UPDATE Products SET StockQuantity = 50;

-- Create a few cash drawers (one per day for the last week)
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
VALUES
  (500.00, 1250.00, 1200.00, 1250.00, 50.00, 'Closed', datetime('now', '-7 days', '09:00:00'), datetime('now', '-7 days', '18:00:00'), 1, 2, 'Normal business day'),
  (500.00, 980.00, 1000.00, 980.00, -20.00, 'Closed', datetime('now', '-6 days', '09:00:00'), datetime('now', '-6 days', '18:00:00'), 3, 2, 'Slow Monday'),
  (500.00, 1100.00, 1100.00, 1100.00, 0.00, 'Closed', datetime('now', '-5 days', '09:00:00'), datetime('now', '-5 days', '18:00:00'), 1, 2, 'Normal business day'),
  (500.00, 1350.00, 1300.00, 1350.00, 50.00, 'Closed', datetime('now', '-4 days', '09:00:00'), datetime('now', '-4 days', '18:00:00'), 3, 2, 'Busy day'),
  (500.00, 1180.00, 1200.00, 1180.00, -20.00, 'Closed', datetime('now', '-3 days', '09:00:00'), datetime('now', '-3 days', '18:00:00'), 1, 2, 'Normal business day'),
  (500.00, 1800.00, 1800.00, 1800.00, 0.00, 'Closed', datetime('now', '-2 days', '09:00:00'), datetime('now', '-2 days', '18:00:00'), 3, 2, 'Busy weekend'),
  (500.00, 1600.00, 1650.00, 1600.00, -50.00, 'Closed', datetime('now', '-1 days', '09:00:00'), datetime('now', '-1 days', '18:00:00'), 1, 2, 'Weekend');

-- Create some sample sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                   TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  ('INV-**********', datetime('now', '-7 days', '10:15:00'), 1, 3, 100.00, 0.00, 0.00, 100.00, 100.00, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('INV-**********', datetime('now', '-7 days', '14:30:00'), NULL, 3, 75.50, 0.00, 0.00, 75.50, 80.00, 4.50, 'Cash', 'Paid', 'Completed', 1),
  ('INV-**********', datetime('now', '-7 days', '16:45:00'), 2, 3, 250.00, 25.00, 0.00, 225.00, 225.00, 0.00, 'Card', 'Paid', 'Completed', 3),
  ('INV-**********', datetime('now', '-6 days', '11:20:00'), NULL, 3, 45.00, 0.00, 0.00, 45.00, 50.00, 5.00, 'Cash', 'Paid', 'Completed', 1),
  ('INV-**********', datetime('now', '-6 days', '15:10:00'), 3, 2, 125.00, 12.50, 0.00, 112.50, 112.50, 0.00, 'Mobile', 'Paid', 'Completed', 2),
  ('INV-**********', datetime('now', '-5 days', '09:30:00'), 1, 3, 85.25, 0.00, 0.00, 85.25, 85.25, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('INV-**********', datetime('now', '-5 days', '13:45:00'), NULL, 3, 55.00, 0.00, 0.00, 55.00, 60.00, 5.00, 'Cash', 'Paid', 'Completed', 1),
  ('INV-230503-003', datetime('now', '-5 days', '17:15:00'), 4, 2, 320.00, 32.00, 0.00, 288.00, 288.00, 0.00, 'Card', 'Paid', 'Completed', 4),
  ('INV-230504-001', datetime('now', '-4 days', '10:30:00'), 2, 3, 75.00, 0.00, 0.00, 75.00, 75.00, 0.00, 'Card', 'Paid', 'Completed', 1),
  ('INV-230504-002', datetime('now', '-4 days', '14:00:00'), NULL, 3, 110.00, 0.00, 0.00, 110.00, 110.00, 0.00, 'Mobile', 'Paid', 'Completed', 3),
  ('INV-230504-003', datetime('now', '-4 days', '16:30:00'), 1, 3, 95.50, 0.00, 0.00, 95.50, 100.00, 4.50, 'Cash', 'Paid', 'Completed', 2),
  ('INV-**********', datetime('now', '-3 days', '11:15:00'), 3, 2, 200.00, 20.00, 0.00, 180.00, 180.00, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('INV-230505-002', datetime('now', '-3 days', '15:45:00'), NULL, 3, 65.00, 0.00, 0.00, 65.00, 70.00, 5.00, 'Cash', 'Paid', 'Completed', 1),
  ('INV-230506-001', datetime('now', '-2 days', '10:00:00'), 4, 3, 150.00, 0.00, 0.00, 150.00, 150.00, 0.00, 'Card', 'Paid', 'Completed', 3),
  ('INV-230506-002', datetime('now', '-2 days', '13:30:00'), 1, 3, 80.00, 0.00, 0.00, 80.00, 80.00, 0.00, 'Mobile', 'Paid', 'Completed', 2),
  ('INV-230506-003', datetime('now', '-2 days', '16:00:00'), NULL, 3, 120.00, 0.00, 0.00, 120.00, 120.00, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('INV-230506-004', datetime('now', '-2 days', '17:30:00'), 2, 2, 350.00, 35.00, 0.00, 315.00, 315.00, 0.00, 'Card', 'Paid', 'Completed', 4),
  ('INV-230507-001', datetime('now', '-1 days', '10:30:00'), 3, 3, 90.00, 0.00, 0.00, 90.00, 90.00, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('INV-230507-002', datetime('now', '-1 days', '14:15:00'), NULL, 3, 45.00, 0.00, 0.00, 45.00, 50.00, 5.00, 'Cash', 'Paid', 'Completed', 1),
  ('INV-230507-003', datetime('now', '-1 days', '16:45:00'), 1, 2, 275.00, 27.50, 0.00, 247.50, 247.50, 0.00, 'Card', 'Paid', 'Completed', 3);

-- Add sale items to each sale (with appropriate products)
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES
  -- Sale 1: Customer 1, 2 items
  (1, 5, 2, 25.00, 50.00),  -- Grocery item
  (1, 9, 1, 50.00, 50.00),  -- Clothing item
  
  -- Sale 2: Walk-in customer, 1 item
  (2, 7, 3, 25.17, 75.50),  -- Grocery item
  
  -- Sale 3: Customer 2, 3 items with discount
  (3, 2, 1, 150.00, 150.00), -- Electronics item
  (3, 5, 2, 25.00, 50.00),   -- Grocery item
  (3, 10, 1, 50.00, 50.00),  -- Clothing item
  
  -- Continue for other sales with appropriate product types and quantities
  (4, 6, 3, 15.00, 45.00),   -- Grocery item for Sale 4
  
  (5, 3, 1, 100.00, 100.00), -- Electronics for Sale 5
  (5, 8, 1, 25.00, 25.00),   -- Grocery item for Sale 5
  
  (6, 5, 3, 25.00, 75.00),   -- Grocery for Sale 6
  (6, 11, 1, 10.25, 10.25),  -- Clothing for Sale 6
  
  (7, 7, 2, 27.50, 55.00),   -- Grocery for Sale 7
  
  (8, 1, 1, 200.00, 200.00), -- Electronics for Sale 8
  (8, 5, 3, 25.00, 75.00),   -- Grocery for Sale 8
  (8, 9, 1, 45.00, 45.00),   -- Clothing for Sale 8
  
  (9, 4, 1, 75.00, 75.00),   -- Electronics for Sale 9
  
  (10, 6, 2, 15.00, 30.00),  -- Grocery for Sale 10
  (10, 7, 2, 25.00, 50.00),  -- Grocery for Sale 10
  (10, 12, 1, 30.00, 30.00), -- Clothing for Sale 10
  
  (11, 5, 2, 25.00, 50.00),  -- Grocery for Sale 11
  (11, 10, 1, 45.50, 45.50), -- Clothing for Sale 11
  
  (12, 2, 1, 150.00, 150.00), -- Electronics for Sale 12
  (12, 8, 2, 25.00, 50.00),   -- Grocery for Sale 12
  
  (13, 6, 3, 15.00, 45.00),   -- Grocery for Sale 13
  (13, 11, 1, 20.00, 20.00),  -- Clothing for Sale 13
  
  (14, 5, 2, 25.00, 50.00),   -- Grocery for Sale 14
  (14, 7, 2, 25.00, 50.00),   -- Grocery for Sale 14
  (14, 9, 1, 50.00, 50.00),   -- Clothing for Sale 14
  
  (15, 8, 2, 25.00, 50.00),   -- Grocery for Sale 15
  (15, 10, 1, 30.00, 30.00),  -- Clothing for Sale 15
  
  (16, 6, 3, 15.00, 45.00),   -- Grocery for Sale 16
  (16, 7, 3, 25.00, 75.00),   -- Grocery for Sale 16
  
  (17, 1, 1, 200.00, 200.00), -- Electronics for Sale 17
  (17, 3, 1, 100.00, 100.00), -- Electronics for Sale 17
  (17, 9, 1, 50.00, 50.00),   -- Clothing for Sale 17
  
  (18, 5, 2, 25.00, 50.00),   -- Grocery for Sale 18
  (18, 10, 1, 40.00, 40.00),  -- Clothing for Sale 18
  
  (19, 8, 3, 15.00, 45.00),   -- Grocery for Sale 19
  
  (20, 2, 1, 150.00, 150.00), -- Electronics for Sale 20
  (20, 6, 3, 15.00, 45.00),   -- Grocery for Sale 20
  (20, 11, 2, 40.00, 80.00);  -- Clothing for Sale 20

-- Add discounts for specific sales
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
VALUES
  -- 10% discount on Sale 3
  (1, 10, 250.00, 225.00, 1, 'Loyal customer discount', 3, NULL, 3, datetime('now', '-7 days', '16:45:00'), 1),
  
  -- 10% discount on Sale 5
  (1, 10, 125.00, 112.50, 2, 'Special promotion', 5, NULL, 2, datetime('now', '-6 days', '15:10:00'), 1),
  
  -- 10% discount on Sale 8
  (1, 10, 320.00, 288.00, 3, 'Manager approval', 8, NULL, 2, datetime('now', '-5 days', '17:15:00'), 1),
  
  -- 10% discount on Sale 12
  (1, 10, 200.00, 180.00, 4, 'Product display model', 12, NULL, 2, datetime('now', '-3 days', '11:15:00'), 1),
  
  -- 10% discount on Sale 17
  (1, 10, 350.00, 315.00, 1, 'Loyal customer discount', 17, NULL, 2, datetime('now', '-2 days', '17:30:00'), 1),
  
  -- 10% discount on Sale 20
  (1, 10, 275.00, 247.50, 2, 'Special promotion', 20, NULL, 2, datetime('now', '-1 days', '16:45:00'), 1);

-- Add inventory transactions for sales (Remove TotalPrice as it doesn't exist)
INSERT INTO InventoryTransactions (ProductId, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
SELECT
  si.ProductId,
  -si.Quantity, -- Negative for sales
  si.UnitPrice,
  s.InvoiceNumber AS Reference,
  'Sale transaction' AS Notes,
  s.SaleDate AS TransactionDate,
  s.UserId
FROM SaleItems si
JOIN Sales s ON si.SaleId = s.Id;

-- Add a purchase order - simplified with no OrderNumber constraints
INSERT INTO PurchaseOrders (OrderNumber, OrderDate, DueDate, SupplierId, Subtotal, TaxAmount, 
                           GrandTotal, Status, Notes, CreatedByUserId, PaymentMethod, PaymentReference, 
                           PaymentDate, CreatedAt, UpdatedAt)
VALUES
  ('PO-**********', datetime('now', '-10 days'), datetime('now', '-7 days'), 1, 1500.00, 150.00, 
   1650.00, 'Received', 'Regular electronics order', 1, 'Bank Transfer', 'TRX-123456', 
   datetime('now', '-9 days'), datetime('now', '-10 days'), datetime('now', '-10 days')),
   
  ('PO-**********', datetime('now', '-8 days'), datetime('now', '-5 days'), 2, 2000.00, 200.00, 
   2200.00, 'Received', 'Weekly grocery restock', 2, 'Credit Card', 'CC-AUTH-789012', 
   datetime('now', '-7 days'), datetime('now', '-8 days'), datetime('now', '-8 days')),
   
  ('PO-**********', datetime('now', '-6 days'), datetime('now', '-3 days'), 3, 1800.00, 180.00, 
   1980.00, 'Received', 'Clothing inventory replenishment', 1, 'Bank Transfer', 'TRX-345678', 
   datetime('now', '-5 days'), datetime('now', '-6 days'), datetime('now', '-6 days')),
   
  ('PO-230507-001', datetime('now', '-3 days'), datetime('now'), 2, 1500.00, 150.00, 
   1650.00, 'In Transit', 'Weekly grocery restock', 2, 'Credit Card', 'CC-AUTH-901234', 
   datetime('now', '-2 days'), datetime('now', '-3 days'), datetime('now', '-3 days'));

-- Add purchase order items
INSERT INTO PurchaseOrderItems (PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, 
                              BatchNumber, Location, Notes, ExpiryDate)
VALUES
  -- Electronics order
  (1, 1, 5, 160.00, 200.00, 'BATCH-2305-1', 'Electronics Section', 'Regular stock replenishment', NULL),
  (1, 2, 5, 120.00, 150.00, 'BATCH-2305-2', 'Electronics Section', 'Regular stock replenishment', NULL),
  (1, 3, 5, 80.00, 100.00, 'BATCH-2305-3', 'Electronics Section', 'Regular stock replenishment', NULL),
  (1, 4, 5, 60.00, 75.00, 'BATCH-2305-4', 'Electronics Section', 'Regular stock replenishment', NULL),
  
  -- Grocery order
  (2, 5, 40, 20.00, 25.00, 'BATCH-2305-5', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+30 days')),
  (2, 6, 40, 12.00, 15.00, 'BATCH-2305-6', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+60 days')),
  (2, 7, 40, 20.00, 25.00, 'BATCH-2305-7', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+45 days')),
  (2, 8, 40, 20.00, 25.00, 'BATCH-2305-8', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+30 days')),
  
  -- Clothing order
  (3, 9, 20, 40.00, 50.00, 'BATCH-2305-9', 'Apparel Area', 'Regular stock replenishment', NULL),
  (3, 10, 20, 32.00, 40.00, 'BATCH-2305-10', 'Apparel Area', 'Regular stock replenishment', NULL),
  (3, 11, 20, 32.00, 40.00, 'BATCH-2305-11', 'Apparel Area', 'Regular stock replenishment', NULL),
  (3, 12, 20, 24.00, 30.00, 'BATCH-2305-12', 'Apparel Area', 'Regular stock replenishment', NULL),
  
  -- Another grocery order
  (4, 5, 30, 20.00, 25.00, 'BATCH-2305-13', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+45 days')),
  (4, 6, 30, 12.00, 15.00, 'BATCH-2305-14', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+60 days')),
  (4, 7, 30, 20.00, 25.00, 'BATCH-2305-15', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+30 days')),
  (4, 8, 30, 20.00, 25.00, 'BATCH-2305-16', 'Grocery Department', 'Regular stock replenishment', datetime('now', '+45 days'));

-- Add inventory transactions for received purchase orders (Remove TransactionType as it doesn't exist)
INSERT INTO InventoryTransactions (ProductId, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
SELECT
  poi.ProductId,
  poi.Quantity,
  poi.UnitCost,
  po.OrderNumber,
  'Purchase order received',
  po.DueDate,
  po.CreatedByUserId
FROM PurchaseOrderItems poi
JOIN PurchaseOrders po ON poi.PurchaseOrderId = po.Id
WHERE po.Status = 'Received';

-- Add cash transactions
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT
  Id AS CashDrawerId,
  'Opening Balance' AS Type,
  OpeningBalance AS Amount,
  OpenedAt AS Timestamp,
  'OPEN-' || Id AS Reference,
  'Daily opening' AS Reason,
  'Cash drawer opened for the day' AS Notes,
  OpenedById AS PerformedById
FROM CashDrawers;

-- Add cash transactions for each cash payment
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT
  cd.Id AS CashDrawerId,
  'Sale Payment' AS Type,
  s.AmountPaid AS Amount,
  s.SaleDate AS Timestamp,
  s.InvoiceNumber AS Reference,
  'Cash sale' AS Reason,
  'Cash payment received' AS Notes,
  s.UserId AS PerformedById
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash';

-- Add closing balance transactions
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT
  Id AS CashDrawerId,
  'Closing Balance' AS Type,
  -ActualBalance AS Amount, -- Negative as money is removed
  ClosedAt AS Timestamp,
  'CLOSE-' || Id AS Reference,
  'Daily closing' AS Reason,
  CASE
    WHEN ABS(ExpectedBalance - ActualBalance) < 0.01 THEN 'Balanced perfectly'
    WHEN ActualBalance > ExpectedBalance THEN 'Over by $' || ROUND(ActualBalance - ExpectedBalance, 2)
    ELSE 'Short by $' || ROUND(ExpectedBalance - ActualBalance, 2)
  END AS Notes,
  ClosedById AS PerformedById
FROM CashDrawers;

-- Add loyalty transactions for customers (Remove TransactionType which doesn't exist)
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
SELECT
  s.CustomerId,
  CAST(s.GrandTotal * (
    SELECT lp.PointsPerDollar * lt.PointsMultiplier
    FROM Customers c
    JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
    JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
    WHERE c.Id = s.CustomerId
  ) AS INTEGER) AS Points,
  'Points earned from purchase ' || s.InvoiceNumber,
  s.SaleDate
FROM Sales s
WHERE s.CustomerId IS NOT NULL;

-- Add a few point redemptions
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
VALUES
  (1, -200, 'Points redeemed for $2.00 discount', datetime('now', '-5 days', '13:30:00')),
  (2, -300, 'Points redeemed for $3.00 discount', datetime('now', '-3 days', '14:00:00')),
  (3, -150, 'Points redeemed for $1.50 discount', datetime('now', '-2 days', '11:00:00'));

-- Update customer loyalty points based on transactions
UPDATE Customers
SET LoyaltyPoints = (
  SELECT COALESCE(SUM(Points), 0)
  FROM LoyaltyTransactions
  WHERE CustomerId = Customers.Id
);

-- Update loyalty tier based on points
UPDATE Customers
SET LoyaltyTierId = (
  SELECT lt.Id
  FROM LoyaltyTiers lt
  WHERE lt.LoyaltyProgramId = 1 -- Only one loyalty program
  AND lt.MinimumPoints <= Customers.LoyaltyPoints
  ORDER BY lt.MinimumPoints DESC
  LIMIT 1
);

-- Add some user favorites
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
VALUES
  (1, 1, datetime('now', '-20 days')),
  (1, 5, datetime('now', '-15 days')),
  (1, 9, datetime('now', '-10 days')),
  (2, 2, datetime('now', '-18 days')),
  (2, 6, datetime('now', '-12 days')),
  (3, 3, datetime('now', '-25 days')),
  (3, 7, datetime('now', '-15 days')),
  (3, 10, datetime('now', '-5 days'));

PRAGMA foreign_keys = ON; -- Re-enable foreign key constraints

COMMIT; 