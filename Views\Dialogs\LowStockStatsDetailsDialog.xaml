<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.LowStockStatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:viewmodels="clr-namespace:POSSystem.ViewModels.Dashboard"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type=viewmodels:LowStockStatsDetailsViewModel}"
             MinWidth="640"
             MinHeight="480">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid Margin="24"
            MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
            MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- ⚡ PERFORMANCE: Loading Indicator Overlay -->
        <Grid Grid.RowSpan="4"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
              Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="48"
                           Height="48"
                           Margin="0,0,0,16"/>
                <TextBlock Text="{DynamicResource Loading}"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         Foreground="White"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0">
                <TextBlock Text="{Binding Title}" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,8"/>
                <TextBlock Text="{Binding Subtitle}"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         Opacity="0.8"/>
            </StackPanel>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters -->
        <Grid Grid.Row="1" Margin="16,0,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="{DynamicResource TimePeriod}"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     VerticalAlignment="Center"
                     Margin="0,0,16,0"/>

            <ComboBox Grid.Column="1"
                    ItemsSource="{Binding TrendPeriods}"
                    SelectedItem="{Binding SelectedTrendPeriod}"
                    DisplayMemberPath="DisplayName"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    Margin="0,0,16,0"/>

            <TextBlock Grid.Column="2" 
                     Text="{DynamicResource FilterByCategory}"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     VerticalAlignment="Center"
                     Margin="0,0,16,0"/>

            <StackPanel Grid.Column="3">
                <CheckBox Content="{DynamicResource FilterByCategoryStats}"
                       IsChecked="{Binding IsCategoryFilterEnabled}"
                       Margin="0,0,0,8"/>
                <ComboBox ItemsSource="{Binding Categories}"
                       SelectedItem="{Binding SelectedCategory}"
                       DisplayMemberPath="Name"
                       IsEnabled="{Binding IsCategoryFilterEnabled}"
                       Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
            </StackPanel>
        </Grid>

        <!-- Content -->
        <Grid Grid.Row="2" Margin="16,0,16,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metric Cards -->
            <WrapPanel Grid.Row="0" Margin="0,0,0,16">
                <!-- Total Low Stock Products -->
                <materialDesign:Card Margin="4" Padding="8">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource TotalProducts}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.8"/>
                        <TextBlock Text="{Binding TotalLowStockProducts}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Out of Stock Products -->
                <materialDesign:Card Margin="4" Padding="8"
                                 Background="{DynamicResource SystemAlertBrush}">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource OutOfStock}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Foreground="White"
                                 Opacity="0.8"/>
                        <TextBlock Text="{Binding OutOfStockProducts}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Near Low Stock Products -->
                <materialDesign:Card Margin="4" Padding="8"
                                   Background="#FF9800">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource LowStock}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Foreground="White"
                                 Opacity="0.8"/>
                        <TextBlock Text="{Binding NearLowStockProducts}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Restock Value -->
                <materialDesign:Card Margin="4" Padding="8">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource RestockValue}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.8"/>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}">
                            <Run Text="{Binding RestockValue, StringFormat={}{0:N2}}" />
                            <Run Text=" " />
                            <Run Text="{DynamicResource CurrencySymbol}" />
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Inventory Value -->
                <materialDesign:Card Margin="4" Padding="8">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource TotalInventoryValue}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.8"/>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}">
                            <Run Text="{Binding TotalInventoryValue, StringFormat={}{0:N2}}" />
                            <Run Text=" " />
                            <Run Text="{DynamicResource CurrencySymbol}" />
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>
            </WrapPanel>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Padding="8">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Stock Trends -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource StockTrends}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource StockTrends}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding StockTrendSeries}"
                                              LegendLocation="Right"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding StockTrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            LabelFormatter="{Binding NumberFormatter}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Category Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource CategoryDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource CategoryDistribution}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:PieChart Grid.Row="1"
                                        Series="{Binding CategoryDistributionSeries}"
                                        LegendLocation="Right"
                                        InnerRadius="40"
                                        Margin="8">
                                <lvc:PieChart.ChartLegend>
                                    <lvc:DefaultLegend BulletSize="15"
                                                      FontSize="12"
                                                      Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:PieChart.ChartLegend>
                                <lvc:PieChart.DataTooltip>
                                    <lvc:DefaultTooltip Background="{DynamicResource MaterialDesignPaper}"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:PieChart.DataTooltip>
                            </lvc:PieChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Stock Levels -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ChartBar"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource StockLevels}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartBar"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource StockLevels}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding StockLevelsSeries}"
                                              LegendLocation="Right"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding StockLevelsLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            LabelFormatter="{Binding NumberFormatter}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 4: Products Needing Attention -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ProductsNeedingAttention}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ProductsNeedingAttention}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <!-- ✅ PERFORMANCE: Optimized DataGrid settings -->
                            <DataGrid Grid.Row="1"
                                     ItemsSource="{Binding LowStockProducts}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     CanUserSortColumns="False"
                                     IsReadOnly="True"
                                     Style="{StaticResource MaterialDesignDataGrid}"
                                     materialDesign:DataGridAssist.CellPadding="16 12"
                                     materialDesign:DataGridAssist.ColumnHeaderPadding="16 12"
                                     EnableRowVirtualization="True"
                                     EnableColumnVirtualization="True"
                                     VirtualizingPanel.ScrollUnit="Pixel"
                                     VirtualizingPanel.IsVirtualizing="True"
                                     ScrollViewer.CanContentScroll="True"
                                     ScrollViewer.IsDeferredScrollingEnabled="True"
                                     HorizontalScrollBarVisibility="Auto"
                                     VerticalScrollBarVisibility="Auto"
                                     FontSize="14"
                                     Height="400">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        <Setter Property="MinHeight" Value="48"/>
                                    </Style>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="MinHeight" Value="56"/>
                                        <Setter Property="Padding" Value="16 12"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}"
                                                      Binding="{Binding Name}"
                                                      Width="200"
                                                      MaxWidth="200">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="TextWrapping" Value="Wrap"/>
                                                <Setter Property="Margin" Value="12"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                <Setter Property="FontSize" Value="14"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="{DynamicResource StockQuantity}"
                                                      Binding="{Binding StockQuantity}"
                                                      Width="120"
                                                      MaxWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="Margin" Value="12"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                <Setter Property="FontSize" Value="14"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="{DynamicResource MinimumStock}"
                                                      Binding="{Binding MinimumStock}"
                                                      Width="120"
                                                      MaxWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="Margin" Value="12"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                <Setter Property="FontSize" Value="14"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <!-- ✅ PERFORMANCE: Status column instead of row styling -->
                                    <DataGridTextColumn Header="{DynamicResource Status}"
                                                      Binding="{Binding StockStatus}"
                                                      Width="100"
                                                      MaxWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="Margin" Value="12"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTemplateColumn Header="{DynamicResource Actions}"
                                                          Width="80"
                                                          MaxWidth="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Command="{Binding DataContext.EditProductCommand,
                                                            RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignIconButton}"
                                                        Margin="8"
                                                        Height="32"
                                                        Width="32"
                                                        ToolTip="{DynamicResource Edit}">
                                                    <materialDesign:PackIcon Kind="Edit"
                                                                               Width="18"
                                                                               Height="18"
                                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                </Button>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                                <!-- ✅ PERFORMANCE: Simplified row style without data triggers -->
                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                        <Setter Property="MinHeight" Value="48"/>
                                        <!-- Removed DataTriggers to prevent binding evaluation performance issues -->
                                    </Style>
                                </DataGrid.RowStyle>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay - Same as other dialogs -->
        <Grid Grid.Row="0" Grid.RowSpan="4"
            Background="{DynamicResource MaterialDesignBackground}"
            Opacity="0.8"
            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                       Value="0"
                       IsIndeterminate="True"/>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>