<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.UnpaidSalesStatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:viewmodels="clr-namespace:POSSystem.ViewModels.Dashboard"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type=viewmodels:UnpaidSalesStatsDetailsViewModel}"
             MinWidth="640"
             MinHeight="480">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Converters.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid
            MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
            MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0">
                <TextBlock Text="{Binding Title}" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,8"/>
                <TextBlock Text="{Binding Subtitle}"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         Opacity="0.8"/>
            </StackPanel>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters and Customer Info -->
        <Grid Grid.Row="1" Margin="16,0,16,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Customer Info Card (visible only when filtering by customer) -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16"
                               Visibility="{Binding IsCustomerFiltered, Converter={StaticResource BooleanToVisibilityConverter}}"
                               Background="{DynamicResource MaterialDesignCardBackground}">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <materialDesign:PackIcon Grid.Column="0" Kind="Account"
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"/>

                    <StackPanel Grid.Column="1">
                        <TextBlock Text="{Binding CustomerName}"
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                 FontWeight="SemiBold"/>
                        <TextBlock Text="{Binding CustomerInfo}"
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 Opacity="0.7"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Time Period Filter -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                         Text="{DynamicResource TimePeriod}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         VerticalAlignment="Center"
                         Margin="0,0,16,0"/>

                <ComboBox Grid.Column="1"
                        ItemsSource="{Binding TrendPeriods}"
                        SelectedItem="{Binding SelectedTrendPeriod}"
                        DisplayMemberPath="DisplayName"
                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                        Margin="0,0,16,0"/>
            </Grid>
        </Grid>

        <!-- Content -->
        <Grid Grid.Row="2" Margin="16,0,16,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metric Cards -->
            <UniformGrid Grid.Row="0" Rows="1" Margin="0,0,0,16">
                <!-- Total Unpaid Sales -->
                <materialDesign:Card Margin="4" Padding="8">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource TotalUnpaidSales}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.8"/>
                        <TextBlock Text="{Binding TotalUnpaidSales}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Unpaid Amount -->
                <materialDesign:Card Margin="4" Padding="8">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource TotalUnpaidAmount}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.8"/>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}">
                            <Run Text="{Binding TotalUnpaidAmount, StringFormat={}{0:N2}}" />
                            <Run Text=" " />
                            <Run Text="{DynamicResource CurrencySymbol}" />
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Past Due Sales -->
                <materialDesign:Card Margin="4" Padding="8"
                                   Background="#FF9800">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource PastDueSales}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Foreground="White"
                                 Opacity="0.8"/>
                        <TextBlock Text="{Binding PastDueSales}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Past Due Amount -->
                <materialDesign:Card Margin="4" Padding="8"
                                 Background="{DynamicResource SystemAlertBrush}">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource PastDueAmount}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Foreground="White"
                                 Opacity="0.8"/>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="White">
                            <Run Text="{Binding PastDueAmount, StringFormat={}{0:N2}}" />
                            <Run Text=" " />
                            <Run Text="{DynamicResource CurrencySymbol}" />
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Padding="8">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Unpaid Trends -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource UnpaidTrends}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource UnpaidTrends}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding UnpaidTrendSeries}"
                                              LegendLocation="Right"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding UnpaidTrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Age Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource AgeDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource AgeDistribution}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:PieChart Grid.Row="1"
                                        Series="{Binding AgeDistributionSeries}"
                                        LegendLocation="Right"
                                        InnerRadius="40"
                                        Margin="8">
                                <lvc:PieChart.ChartLegend>
                                    <lvc:DefaultLegend BulletSize="15"
                                                      FontSize="12"
                                                      Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:PieChart.ChartLegend>
                                <lvc:PieChart.DataTooltip>
                                    <lvc:DefaultTooltip Background="{DynamicResource MaterialDesignPaper}"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:PieChart.DataTooltip>
                            </lvc:PieChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Amount Ranges -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="CurrencyUsd"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource AmountRanges}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="CurrencyUsd"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource AmountRanges}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding AmountRangeSeries}"
                                              LegendLocation="Right"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding AmountRangeLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 4: Unpaid Sales List -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="FormatListBulleted"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource UnpaidSalesList}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="FormatListBulleted"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource UnpaidSalesList}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <DataGrid Grid.Row="1"
                                     ItemsSource="{Binding UnpaidSales}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     IsReadOnly="True"
                                     Style="{StaticResource MaterialDesignDataGrid}"
                                     materialDesign:DataGridAssist.CellPadding="16 12"
                                     materialDesign:DataGridAssist.ColumnHeaderPadding="16 12"
                                     EnableRowVirtualization="True"
                                     EnableColumnVirtualization="True"
                                     VirtualizingPanel.ScrollUnit="Pixel"
                                     VirtualizingPanel.IsVirtualizing="True"
                                     ScrollViewer.CanContentScroll="True"
                                     ScrollViewer.IsDeferredScrollingEnabled="True"
                                     FontSize="14"
                                     Height="400"
                                     HorizontalScrollBarVisibility="Auto"
                                     VerticalScrollBarVisibility="Auto">
                            
                            <DataGrid.Resources>
                                <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    <Setter Property="MinHeight" Value="48"/>
                                </Style>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="MinHeight" Value="56"/>
                                    <Setter Property="Padding" Value="16 12"/>
                                </Style>
                            </DataGrid.Resources>
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource CustomerLabel}"
                                                  Binding="{Binding CustomerName}"
                                                  Width="2*"
                                                  MinWidth="200">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                            <Setter Property="Margin" Value="12"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                            <Setter Property="FontSize" Value="14"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                
                                <DataGridTextColumn Header="{DynamicResource DueDate}"
                                                  Binding="{Binding DueDate, StringFormat=d}"
                                                  Width="*"
                                                  MinWidth="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="Margin" Value="12"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                            <Setter Property="FontSize" Value="14"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                
                                <DataGridTextColumn Header="{DynamicResource RemainingAmount}"
                                                  Width="*"
                                                  MinWidth="150">
                                    <DataGridTextColumn.Binding>
                                        <MultiBinding StringFormat="{}{0:N2} {1}">
                                            <Binding Path="RemainingAmount"/>
                                            <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                        </MultiBinding>
                                    </DataGridTextColumn.Binding>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="Margin" Value="12"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                            <Setter Property="FontSize" Value="14"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="{DynamicResource Status}"
                                                  Binding="{Binding StatusText}"
                                                  Width="*"
                                                  MinWidth="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="Margin" Value="12"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontSize" Value="14"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsPastDue}" Value="True">
                                                    <Setter Property="Text" Value="{DynamicResource Past Due}"/>
                                                    <Setter Property="Foreground" Value="Red"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTemplateColumn Header="{DynamicResource Actions}"
                                                      Width="Auto"
                                                      MinWidth="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Command="{Binding DataContext.ViewSaleCommand, 
                                                        RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource MaterialDesignIconButton}"
                                                    Margin="8"
                                                    Height="32"
                                                    Width="32"
                                                    ToolTip="{DynamicResource View}">
                                                <materialDesign:PackIcon Kind="Eye" 
                                                                       Width="18" 
                                                                       Height="18"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </Button>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- No Data Message -->
                        <Grid Grid.Row="1"
                              Visibility="{Binding HasUnpaidSales, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                            <StackPanel HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Margin="20">
                                <materialDesign:PackIcon Kind="InformationOutline"
                                                       Width="48"
                                                       Height="48"
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                       HorizontalAlignment="Center"
                                                       Margin="0,0,0,16"/>
                                <TextBlock Text="{Binding NoDataMessage}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         HorizontalAlignment="Center"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         TextAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="4"
            Background="{DynamicResource MaterialDesignBackground}"
            Opacity="0.8"
            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                       Value="0"
                       IsIndeterminate="True"/>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>