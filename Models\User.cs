using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Windows.Media.Imaging;
using System.IO;
using System.Windows;

namespace POSSystem.Models
{
    public class User
    {
        private static BitmapImage _defaultPhoto;
        public static BitmapImage DefaultPhoto
        {
            get
            {
                if (_defaultPhoto == null)
                {
                    try
                    {
                        var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                        var resourceName = "POSSystem.Resources.Images.default-user.png";
                        using (var stream = assembly.GetManifestResourceStream(resourceName))
                        {
                            if (stream != null)
                            {
                                var image = new BitmapImage();
                                image.BeginInit();
                                image.CacheOption = BitmapCacheOption.OnLoad;
                                image.StreamSource = stream;
                                image.EndInit();
                                image.Freeze();
                                _defaultPhoto = image;
                                System.Diagnostics.Trace.WriteLine($"Successfully loaded default photo from resource: {resourceName}");
                            }
                            else
                            {
                                System.Diagnostics.Trace.WriteLine($"Error: Could not find embedded resource: {resourceName}");
                                _defaultPhoto = new BitmapImage();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Trace.WriteLine($"Error loading default photo: {ex.Message}");
                        System.Diagnostics.Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                        _defaultPhoto = new BitmapImage();
                    }
                }
                return _defaultPhoto;
            }
        }

        public int Id { get; set; }
        
        [Required]
        public string Username { get; set; }
        
        [Required]
        public string Password { get; set; }
        
        [Required]
        public string FirstName { get; set; }
        
        [Required]
        public string LastName { get; set; }
        
        public string Email { get; set; }
        public string Phone { get; set; }
        
        [ForeignKey("UserRole")]
        public int RoleId { get; set; }
        public virtual Role UserRole { get; set; }

        public bool IsActive { get; set; }

        /// <summary>
        /// Gets the display role name - shows "Custom" if user has custom permissions, otherwise shows the actual role name
        /// </summary>
        public string DisplayRoleName
        {
            get
            {
                // Simple fallback first
                var fallbackRole = UserRole?.Name ?? "Unknown Role";

                try
                {
                    System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Checking DisplayRoleName for user {Id} ({Username ?? "NULL"})");

                    // Safety checks
                    if (Id <= 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Invalid user ID: {Id}");
                        return fallbackRole;
                    }

                    if (RoleId <= 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Invalid role ID: {RoleId}");
                        return fallbackRole;
                    }

                    // Check if user has custom permissions
                    var dbService = new POSSystem.Services.DatabaseService();
                    var userPermissions = dbService.GetUserPermissions(Id);

                    if (userPermissions == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] No custom permissions found for user {Id} - returning role name");
                        return fallbackRole;
                    }

                    System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Found custom permissions for user {Id}");

                    // For now, if user has ANY custom permissions, show "Custom"
                    // This is a simplified approach to test if the basic logic works
                    System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] User {Id} has custom permissions - returning 'Custom'");
                    return "Custom";
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Error determining display role for user {Id}: {ex.Message}");
                    return fallbackRole;
                }
            }
        }

        /// <summary>
        /// Compares user permissions with role defaults to determine if they're custom
        /// </summary>
        private bool PermissionsMatchDefaults(POSSystem.Models.UserPermissions userPermissions, POSSystem.Models.UserPermissions roleDefaults)
        {
            var matches = new List<(string name, bool userPerm, bool rolePerm, bool match)>
            {
                ("CanCreateSales", userPermissions.CanCreateSales, roleDefaults.CanCreateSales, userPermissions.CanCreateSales == roleDefaults.CanCreateSales),
                ("CanVoidSales", userPermissions.CanVoidSales, roleDefaults.CanVoidSales, userPermissions.CanVoidSales == roleDefaults.CanVoidSales),
                ("CanApplyDiscount", userPermissions.CanApplyDiscount, roleDefaults.CanApplyDiscount, userPermissions.CanApplyDiscount == roleDefaults.CanApplyDiscount),
                ("CanViewSalesHistory", userPermissions.CanViewSalesHistory, roleDefaults.CanViewSalesHistory, userPermissions.CanViewSalesHistory == roleDefaults.CanViewSalesHistory),
                ("CanManageProducts", userPermissions.CanManageProducts, roleDefaults.CanManageProducts, userPermissions.CanManageProducts == roleDefaults.CanManageProducts),
                ("CanManageCategories", userPermissions.CanManageCategories, roleDefaults.CanManageCategories, userPermissions.CanManageCategories == roleDefaults.CanManageCategories),
                ("CanViewInventory", userPermissions.CanViewInventory, roleDefaults.CanViewInventory, userPermissions.CanViewInventory == roleDefaults.CanViewInventory),
                ("CanAdjustInventory", userPermissions.CanAdjustInventory, roleDefaults.CanAdjustInventory, userPermissions.CanAdjustInventory == roleDefaults.CanAdjustInventory),
                ("CanManageExpenses", userPermissions.CanManageExpenses, roleDefaults.CanManageExpenses, userPermissions.CanManageExpenses == roleDefaults.CanManageExpenses),
                ("CanManageCashDrawer", userPermissions.CanManageCashDrawer, roleDefaults.CanManageCashDrawer, userPermissions.CanManageCashDrawer == roleDefaults.CanManageCashDrawer),
                ("CanViewReports", userPermissions.CanViewReports, roleDefaults.CanViewReports, userPermissions.CanViewReports == roleDefaults.CanViewReports),
                ("CanManagePrices", userPermissions.CanManagePrices, roleDefaults.CanManagePrices, userPermissions.CanManagePrices == roleDefaults.CanManagePrices),
                ("CanManageCustomers", userPermissions.CanManageCustomers, roleDefaults.CanManageCustomers, userPermissions.CanManageCustomers == roleDefaults.CanManageCustomers),
                ("CanManageSuppliers", userPermissions.CanManageSuppliers, roleDefaults.CanManageSuppliers, userPermissions.CanManageSuppliers == roleDefaults.CanManageSuppliers),
                ("CanManageUsers", userPermissions.CanManageUsers, roleDefaults.CanManageUsers, userPermissions.CanManageUsers == roleDefaults.CanManageUsers),
                ("CanManageRoles", userPermissions.CanManageRoles, roleDefaults.CanManageRoles, userPermissions.CanManageRoles == roleDefaults.CanManageRoles),
                ("CanAccessSettings", userPermissions.CanAccessSettings, roleDefaults.CanAccessSettings, userPermissions.CanAccessSettings == roleDefaults.CanAccessSettings),
                ("CanViewLogs", userPermissions.CanViewLogs, roleDefaults.CanViewLogs, userPermissions.CanViewLogs == roleDefaults.CanViewLogs)
            };

            System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] Permission comparison details:");
            foreach (var match in matches)
            {
                if (!match.match)
                {
                    System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE]   MISMATCH: {match.name} - User: {match.userPerm}, Role: {match.rolePerm}");
                }
            }

            bool allMatch = matches.All(m => m.match);
            System.Diagnostics.Debug.WriteLine($"[DISPLAYROLE] All permissions match: {allMatch}");
            return allMatch;
        }
        public string PhotoPath { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Audit> Audits { get; set; } = new List<Audit>();
        public virtual ICollection<UserFavorite> Favorites { get; set; } = new List<UserFavorite>();
        public virtual UserPermissions Permissions { get; set; }

        // ✅ CRITICAL MEMORY FIX: Cache BitmapImage to prevent memory leaks
        private static readonly Dictionary<string, WeakReference> _imageCache = new Dictionary<string, WeakReference>();
        private static readonly object _imageCacheLock = new object();

        [NotMapped]
        public BitmapImage Photo
        {
            get
            {
                try
                {
                    if (string.IsNullOrEmpty(PhotoPath) || PhotoPath == "default-user.png")
                    {
                        System.Diagnostics.Trace.WriteLine("Using default photo (PhotoPath is null or default)");
                        return DefaultPhoto;
                    }

                    // ✅ CRITICAL: Check cache first to prevent memory leaks
                    lock (_imageCacheLock)
                    {
                        if (_imageCache.TryGetValue(PhotoPath, out var weakRef) && weakRef.IsAlive)
                        {
                            return (BitmapImage)weakRef.Target;
                        }

                        // Create new image with memory optimization
                        var image = new BitmapImage();
                        image.BeginInit();
                        image.CacheOption = BitmapCacheOption.OnLoad;
                        image.CreateOptions = BitmapCreateOptions.IgnoreImageCache;
                        // Memory optimization: Reduce image size for UI display
                        image.DecodePixelWidth = 100; // Limit to 100px width for UI
                        image.DecodePixelHeight = 100; // Limit to 100px height for UI
                    
                        if (PhotoPath.StartsWith("pack://"))
                        {
                            System.Diagnostics.Trace.WriteLine($"Loading photo from pack URI: {PhotoPath}");
                            image.UriSource = new Uri(PhotoPath);
                        }
                        else if (File.Exists(PhotoPath))
                        {
                            System.Diagnostics.Trace.WriteLine($"Loading photo from file: {PhotoPath}");
                            image.UriSource = new Uri(PhotoPath, UriKind.Absolute);
                        }
                        else
                        {
                            System.Diagnostics.Trace.WriteLine($"Photo file not found: {PhotoPath}, using default photo");
                            return DefaultPhoto;
                        }

                        image.EndInit();
                        image.Freeze(); // ✅ CRITICAL: Freeze for thread safety and memory optimization

                        // ✅ CRITICAL: Cache the image to prevent recreation
                        _imageCache[PhotoPath] = new WeakReference(image);

                        // Clean up old cache entries periodically
                        if (_imageCache.Count > 50)
                        {
                            CleanupImageCache();
                        }

                        return image;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.WriteLine($"Error loading user photo: {ex.Message}");
                    System.Diagnostics.Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                    return DefaultPhoto;
                }
            }
        }

        /// <summary>
        /// ✅ CRITICAL MEMORY FIX: Clean up dead image cache entries
        /// </summary>
        private static void CleanupImageCache()
        {
            try
            {
                var deadKeys = _imageCache
                    .Where(kvp => !kvp.Value.IsAlive)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in deadKeys)
                {
                    _imageCache.Remove(key);
                }

                System.Diagnostics.Trace.WriteLine($"[USER-IMAGE-CACHE] Cleaned up {deadKeys.Count} dead cache entries");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.WriteLine($"[USER-IMAGE-CACHE] Cleanup error: {ex.Message}");
            }
        }
    }
}