using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;

namespace POSSystem.Services
{
    /// <summary>
    /// Utility class to disable trend charts in the dashboard
    /// </summary>
    public static class TrendChartDisabler
    {
        /// <summary>
        /// Disables a trend chart and replaces it with a message
        /// </summary>
        /// <param name="chart">The chart to disable</param>
        /// <param name="message">Optional custom message to display</param>
        public static void DisableChart(CartesianChart chart, string message = null)
        {
            if (chart == null) return;
            
            try
            {
                // Set empty data for the chart
                chart.Series = new SeriesCollection();
                
                // Create a message to display
                var textBlock = new TextBlock
                {
                    Text = message ?? "Trend analysis charts have been disabled.",
                    FontSize = 16,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 20),
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Center
                };
                
                // Try to find the parent grid to replace the chart
                if (chart.Parent is Panel parent)
                {
                    // Get the chart's position
                    var row = Grid.GetRow(chart);
                    var column = Grid.GetColumn(chart);
                    
                    // Set the position for the text block
                    if (parent is Grid)
                    {
                        Grid.SetRow(textBlock, row);
                        Grid.SetColumn(textBlock, column);
                    }
                    
                    // Replace the chart with the message
                    parent.Children.Remove(chart);
                    parent.Children.Add(textBlock);
                }
                else
                {
                    // If we can't find the parent, just hide the chart
                    chart.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't crash
                System.Diagnostics.Debug.WriteLine($"Error disabling trend chart: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Gets empty chart data for use in trend charts
        /// </summary>
        public static SeriesCollection GetEmptySeriesCollection()
        {
            return new SeriesCollection();
        }
        
        /// <summary>
        /// Gets empty labels for use in trend charts
        /// </summary>
        public static string[] GetEmptyLabels()
        {
            return Array.Empty<string>();
        }
    }
} 