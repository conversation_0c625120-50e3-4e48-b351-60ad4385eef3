using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services
{
    /// <summary>
    /// High-performance caching service for product data
    /// Reduces database queries and improves SaleViewGrid performance
    /// </summary>
    public class ProductCacheService : IDisposable
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<ProductCacheService> _logger;
        private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps;
        
        // Cache configuration
        private readonly TimeSpan _defaultCacheExpiry = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _searchCacheExpiry = TimeSpan.FromMinutes(5);
        private readonly int _maxCacheSize = 1000;

        public ProductCacheService(IMemoryCache cache = null, ILogger<ProductCacheService> logger = null)
        {
            _cache = cache ?? new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = _maxCacheSize,
                CompactionPercentage = 0.25
            });
            _logger = logger;
            _cacheTimestamps = new ConcurrentDictionary<string, DateTime>();
        }

        /// <summary>
        /// Get cached products by category with automatic cache management
        /// </summary>
        public async Task<List<Product>> GetProductsByCategoryAsync(int? categoryId, Func<int?, Task<List<Product>>> dataLoader)
        {
            var cacheKey = $"products_category_{categoryId ?? 0}";
            
            if (_cache.TryGetValue(cacheKey, out List<Product> cachedProducts))
            {
                _logger?.LogDebug("Cache hit for category {CategoryId}: {Count} products", categoryId, cachedProducts.Count);
                return cachedProducts;
            }

            // Load from database
            var products = await dataLoader(categoryId);
            
            // Cache with size-based eviction
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _defaultCacheExpiry,
                Size = products.Count,
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(cacheKey, products, cacheOptions);
            _cacheTimestamps[cacheKey] = DateTime.Now;
            
            _logger?.LogDebug("Cached {Count} products for category {CategoryId}", products.Count, categoryId);
            return products;
        }

        /// <summary>
        /// Get cached search results with intelligent caching
        /// </summary>
        public async Task<List<Product>> GetSearchResultsAsync(string searchText, int? categoryId, Func<string, int?, Task<List<Product>>> searchLoader)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return new List<Product>();

            var normalizedSearch = searchText.Trim().ToLowerInvariant();
            var cacheKey = $"search_{normalizedSearch}_{categoryId ?? 0}";
            
            if (_cache.TryGetValue(cacheKey, out List<Product> cachedResults))
            {
                _logger?.LogDebug("Search cache hit for '{SearchText}': {Count} results", searchText, cachedResults.Count);
                return cachedResults;
            }

            // Load search results
            var results = await searchLoader(searchText, categoryId);
            
            // Cache search results with shorter expiry
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _searchCacheExpiry,
                Size = results.Count,
                Priority = CacheItemPriority.Low // Search results have lower priority
            };

            _cache.Set(cacheKey, results, cacheOptions);
            _cacheTimestamps[cacheKey] = DateTime.Now;
            
            _logger?.LogDebug("Cached {Count} search results for '{SearchText}'", results.Count, searchText);
            return results;
        }

        /// <summary>
        /// Invalidate cache when products are modified
        /// </summary>
        public void InvalidateProductCache(int? categoryId = null)
        {
            if (categoryId.HasValue)
            {
                var cacheKey = $"products_category_{categoryId.Value}";
                _cache.Remove(cacheKey);
                _cacheTimestamps.TryRemove(cacheKey, out _);
                _logger?.LogDebug("Invalidated cache for category {CategoryId}", categoryId);
            }
            else
            {
                // Invalidate all product caches
                var keysToRemove = _cacheTimestamps.Keys
                    .Where(k => k.StartsWith("products_category_") || k.StartsWith("search_"))
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _cache.Remove(key);
                    _cacheTimestamps.TryRemove(key, out _);
                }
                
                _logger?.LogDebug("Invalidated all product caches");
            }
        }

        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        public CacheStatistics GetCacheStatistics()
        {
            var productCacheCount = _cacheTimestamps.Keys.Count(k => k.StartsWith("products_category_"));
            var searchCacheCount = _cacheTimestamps.Keys.Count(k => k.StartsWith("search_"));
            
            return new CacheStatistics
            {
                ProductCacheEntries = productCacheCount,
                SearchCacheEntries = searchCacheCount,
                TotalEntries = _cacheTimestamps.Count,
                OldestEntry = _cacheTimestamps.Values.Any() ? _cacheTimestamps.Values.Min() : DateTime.Now,
                NewestEntry = _cacheTimestamps.Values.Any() ? _cacheTimestamps.Values.Max() : DateTime.Now
            };
        }

        /// <summary>
        /// Preload frequently accessed categories
        /// </summary>
        public async Task PreloadFrequentCategoriesAsync(List<int> categoryIds, Func<int?, Task<List<Product>>> dataLoader)
        {
            var preloadTasks = categoryIds.Select(async categoryId =>
            {
                try
                {
                    await GetProductsByCategoryAsync(categoryId, dataLoader);
                    _logger?.LogDebug("Preloaded category {CategoryId}", categoryId);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Failed to preload category {CategoryId}", categoryId);
                }
            });

            await Task.WhenAll(preloadTasks);
            _logger?.LogInformation("Preloaded {Count} categories", categoryIds.Count);
        }

        public void Dispose()
        {
            _cache?.Dispose();
            _cacheTimestamps?.Clear();
        }
    }

    /// <summary>
    /// Cache performance statistics
    /// </summary>
    public class CacheStatistics
    {
        public int ProductCacheEntries { get; set; }
        public int SearchCacheEntries { get; set; }
        public int TotalEntries { get; set; }
        public DateTime OldestEntry { get; set; }
        public DateTime NewestEntry { get; set; }
        
        public TimeSpan CacheAge => NewestEntry - OldestEntry;
    }
}
