using POSSystem.Models;
using Xunit;
using FluentAssertions;

namespace POSSystem.Tests.BusinessLogic
{
    public class SalesCalculationTests
    {
        #region Sale Total Calculations

        [Fact]
        public void Sale_ShouldCalculateSubtotalCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                Items = new List<SaleItem>
                {
                    new SaleItem { Quantity = 2, UnitPrice = 10.50m },
                    new SaleItem { Quantity = 1, UnitPrice = 25.99m },
                    new SaleItem { Quantity = 3, UnitPrice = 5.00m }
                }
            };

            // Act
            var subtotal = sale.Items.Sum(item => item.Quantity * item.UnitPrice);

            // Assert
            subtotal.Should().Be(61.99m); // (2*10.50) + (1*25.99) + (3*5.00) = 21.00 + 25.99 + 15.00
        }

        [Fact]
        public void Sale_ShouldCalculateTaxCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 100.00m,
                TaxRate = 0.08m // 8% tax
            };

            // Act
            var taxAmount = sale.Subtotal * sale.TaxRate;

            // Assert
            taxAmount.Should().Be(8.00m);
        }

        [Fact]
        public void Sale_ShouldCalculateDiscountCorrectly_WithPercentage()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 100.00m,
                DiscountPercentage = 15.0m // 15% discount
            };

            // Act
            var discountAmount = sale.Subtotal * (sale.DiscountPercentage / 100);

            // Assert
            discountAmount.Should().Be(15.00m);
        }

        [Fact]
        public void Sale_ShouldCalculateDiscountCorrectly_WithFixedAmount()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 100.00m,
                DiscountAmount = 20.00m
            };

            // Act
            var discountAmount = sale.DiscountAmount;

            // Assert
            discountAmount.Should().Be(20.00m);
        }

        [Fact]
        public void Sale_ShouldCalculateGrandTotalCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 100.00m,
                TaxAmount = 8.00m,
                DiscountAmount = 10.00m
            };

            // Act
            var grandTotal = sale.Subtotal + sale.TaxAmount - sale.DiscountAmount;

            // Assert
            grandTotal.Should().Be(98.00m); // 100 + 8 - 10
        }

        [Fact]
        public void Sale_ShouldCalculateChangeCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                GrandTotal = 87.50m,
                AmountPaid = 100.00m
            };

            // Act
            var change = sale.AmountPaid - sale.GrandTotal;

            // Assert
            change.Should().Be(12.50m);
        }

        #endregion

        #region SaleItem Calculations

        [Fact]
        public void SaleItem_ShouldCalculateTotalCorrectly()
        {
            // Arrange
            var saleItem = new SaleItem
            {
                Quantity = 5,
                UnitPrice = 12.99m
            };

            // Act
            var total = saleItem.Quantity * saleItem.UnitPrice;

            // Assert
            total.Should().Be(64.95m);
        }

        [Fact]
        public void SaleItem_ShouldHandleZeroQuantity()
        {
            // Arrange
            var saleItem = new SaleItem
            {
                Quantity = 0,
                UnitPrice = 10.00m
            };

            // Act
            var total = saleItem.Quantity * saleItem.UnitPrice;

            // Assert
            total.Should().Be(0.00m);
        }

        [Fact]
        public void SaleItem_ShouldHandleDecimalQuantity()
        {
            // Arrange
            var saleItem = new SaleItem
            {
                Quantity = 2.5m,
                UnitPrice = 8.00m
            };

            // Act
            var total = saleItem.Quantity * saleItem.UnitPrice;

            // Assert
            total.Should().Be(20.00m);
        }

        #endregion

        #region Profit Calculations

        [Fact]
        public void Sale_ShouldCalculateProfitCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                Items = new List<SaleItem>
                {
                    new SaleItem 
                    { 
                        Quantity = 2, 
                        UnitPrice = 15.00m,
                        Product = new Product { PurchasePrice = 10.00m }
                    },
                    new SaleItem 
                    { 
                        Quantity = 1, 
                        UnitPrice = 25.00m,
                        Product = new Product { PurchasePrice = 18.00m }
                    }
                }
            };

            // Act
            var totalRevenue = sale.Items.Sum(item => item.Quantity * item.UnitPrice);
            var totalCost = sale.Items.Sum(item => item.Quantity * (item.Product?.PurchasePrice ?? 0));
            var profit = totalRevenue - totalCost;

            // Assert
            totalRevenue.Should().Be(55.00m); // (2*15) + (1*25)
            totalCost.Should().Be(38.00m); // (2*10) + (1*18)
            profit.Should().Be(17.00m);
        }

        [Fact]
        public void Sale_ShouldHandleMissingPurchasePrice()
        {
            // Arrange
            var sale = new Sale
            {
                Items = new List<SaleItem>
                {
                    new SaleItem 
                    { 
                        Quantity = 1, 
                        UnitPrice = 20.00m,
                        Product = null // No product information
                    }
                }
            };

            // Act
            var totalCost = sale.Items.Sum(item => item.Quantity * (item.Product?.PurchasePrice ?? 0));

            // Assert
            totalCost.Should().Be(0.00m); // Should default to 0 when product is null
        }

        #endregion

        #region Edge Cases and Validation

        [Fact]
        public void Sale_ShouldHandleEmptyItemsList()
        {
            // Arrange
            var sale = new Sale
            {
                Items = new List<SaleItem>()
            };

            // Act
            var subtotal = sale.Items.Sum(item => item.Quantity * item.UnitPrice);

            // Assert
            subtotal.Should().Be(0.00m);
        }

        [Fact]
        public void Sale_ShouldHandleNullItemsList()
        {
            // Arrange
            var sale = new Sale
            {
                Items = null
            };

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                sale.Items.Sum(item => item.Quantity * item.UnitPrice));
        }

        [Fact]
        public void Sale_ShouldHandleNegativeDiscount()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 100.00m,
                DiscountAmount = -10.00m // Negative discount (should be treated as 0 or error)
            };

            // Act
            var grandTotal = sale.Subtotal - Math.Max(0, sale.DiscountAmount);

            // Assert
            grandTotal.Should().Be(100.00m); // Negative discount should be ignored
        }

        [Fact]
        public void Sale_ShouldHandleDiscountGreaterThanSubtotal()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 50.00m,
                DiscountAmount = 75.00m // Discount greater than subtotal
            };

            // Act
            var grandTotal = Math.Max(0, sale.Subtotal - sale.DiscountAmount);

            // Assert
            grandTotal.Should().Be(0.00m); // Grand total should not be negative
        }

        [Theory]
        [InlineData(0.00, 0.00)]
        [InlineData(10.00, 0.80)]
        [InlineData(100.00, 8.00)]
        [InlineData(999.99, 79.9992)]
        public void Sale_ShouldCalculateTaxCorrectly_ForVariousAmounts(decimal subtotal, decimal expectedTax)
        {
            // Arrange
            var taxRate = 0.08m; // 8%
            var sale = new Sale
            {
                Subtotal = subtotal,
                TaxRate = taxRate
            };

            // Act
            var taxAmount = sale.Subtotal * sale.TaxRate;

            // Assert
            taxAmount.Should().Be(expectedTax);
        }

        [Theory]
        [InlineData(100.00, 5.0, 5.00)]
        [InlineData(100.00, 10.0, 10.00)]
        [InlineData(100.00, 25.0, 25.00)]
        [InlineData(50.00, 20.0, 10.00)]
        public void Sale_ShouldCalculatePercentageDiscountCorrectly(decimal subtotal, decimal discountPercentage, decimal expectedDiscount)
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = subtotal,
                DiscountPercentage = discountPercentage
            };

            // Act
            var discountAmount = sale.Subtotal * (sale.DiscountPercentage / 100);

            // Assert
            discountAmount.Should().Be(expectedDiscount);
        }

        #endregion

        #region Rounding Tests

        [Fact]
        public void Sale_ShouldRoundCalculationsCorrectly()
        {
            // Arrange
            var sale = new Sale
            {
                Items = new List<SaleItem>
                {
                    new SaleItem { Quantity = 3, UnitPrice = 3.333m } // Should result in 9.999
                }
            };

            // Act
            var subtotal = sale.Items.Sum(item => item.Quantity * item.UnitPrice);
            var roundedSubtotal = Math.Round(subtotal, 2);

            // Assert
            subtotal.Should().Be(9.999m);
            roundedSubtotal.Should().Be(10.00m);
        }

        [Fact]
        public void Sale_ShouldHandleCurrencyPrecision()
        {
            // Arrange
            var sale = new Sale
            {
                Subtotal = 99.996m,
                TaxRate = 0.08m
            };

            // Act
            var taxAmount = Math.Round(sale.Subtotal * sale.TaxRate, 2);
            var grandTotal = Math.Round(sale.Subtotal + taxAmount, 2);

            // Assert
            taxAmount.Should().Be(8.00m); // 99.996 * 0.08 = 7.99968, rounded to 8.00
            grandTotal.Should().Be(108.00m); // 99.996 + 8.00 = 107.996, rounded to 108.00
        }

        #endregion
    }
}
