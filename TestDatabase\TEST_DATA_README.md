# POS System Test Data Generation

This folder contains scripts for generating test data for the POS System database. The test data is designed to allow testing of various system features, particularly the discount functionality.

## Scripts Overview

### Working_Test_Data.sql
This is the main SQL script that generates a set of test data including:
- Sales with different payment methods and customers
- Sale items for each sale
- Discounts with appropriate permissions
- Inventory transactions to track stock changes
- Cash drawers with opening/closing balances
- Cash transactions for tracking money movement
- Loyalty transactions for customer rewards
- User favorites

The script handles the schema mismatch issues by temporarily dropping problematic triggers that reference columns that don't exist in the tables.

### Run_Test_Data.bat (Windows)
A batch script for Windows users that:
1. Backs up the current database
2. Runs the Working_Test_Data.sql script
3. Verifies the data was inserted correctly by showing counts

### run_test_data.sh (Linux/macOS)
A shell script for Linux and macOS users that performs the same functions as the Windows batch script.

## Known Issues and Solutions

The database schema has some inconsistencies between trigger definitions and table structures:

1. **Trigger Issue 1**: The `update_inventory_after_sale` trigger tries to insert into `InventoryTransactions` with columns `TotalPrice` and `ReferenceNumber`, but the table actually has `Reference` and no `TotalPrice` column.

2. **Trigger Issue 2**: The `update_loyalty_points_after_sale` trigger tries to insert into `LoyaltyTransactions` with columns that don't exist in the table, like `TransactionType`, `PointsBalance`, `SaleId`, and `CreatedAt`.

Our solution temporarily drops these triggers before inserting data, which allows the data generation to proceed without errors.

## How to Use

### For Windows:
```
.\Run_Test_Data.bat
```

### For Linux/macOS:
```
chmod +x run_test_data.sh
./run_test_data.sh
```

## Data Generated

The generated test data includes:
- 3 sales with different payment methods
- 5 sale items across those sales
- 1 discount applied to a sale
- 7 inventory transactions
- 2 loyalty transactions
- 3 cash transactions (opening, sale, closing)
- 1 user favorite
- 1 cash drawer record

This provides enough data to test basic functionality of the system, particularly around sales processing and discounts.

## Restoring the Original Database

The script automatically creates a backup of your database as `POSSystem_Test_backup.db` before adding test data. To restore:

1. Close any applications using the database
2. Delete or rename the current `POSSystem_Test.db`
3. Copy or rename `POSSystem_Test_backup.db` to `POSSystem_Test.db` 