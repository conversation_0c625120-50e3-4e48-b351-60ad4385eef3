using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Runtime.CompilerServices;

namespace POSSystem.Models
{
    public class UserPermissions : INotifyPropertyChanged
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public User User { get; set; }

        // Sales Permissions
        private bool _canCreateSales;
        public bool CanCreateSales
        {
            get => _canCreateSales;
            set { _canCreateSales = value; OnPropertyChanged(); }
        }

        private bool _canVoidSales;
        public bool CanVoidSales
        {
            get => _canVoidSales;
            set { _canVoidSales = value; OnPropertyChanged(); }
        }

        private bool _canApplyDiscount;
        public bool CanApplyDiscount
        {
            get => _canApplyDiscount;
            set { _canApplyDiscount = value; OnPropertyChanged(); }
        }

        private bool _canViewSalesHistory;
        public bool CanViewSalesHistory
        {
            get => _canViewSalesHistory;
            set { _canViewSalesHistory = value; OnPropertyChanged(); }
        }

        // Product Permissions
        private bool _canManageProducts;
        public bool CanManageProducts
        {
            get => _canManageProducts;
            set { _canManageProducts = value; OnPropertyChanged(); }
        }

        private bool _canManageCategories;
        public bool CanManageCategories
        {
            get => _canManageCategories;
            set { _canManageCategories = value; OnPropertyChanged(); }
        }

        private bool _canViewInventory;
        public bool CanViewInventory
        {
            get => _canViewInventory;
            set { _canViewInventory = value; OnPropertyChanged(); }
        }

        private bool _canAdjustInventory;
        public bool CanAdjustInventory
        {
            get => _canAdjustInventory;
            set { _canAdjustInventory = value; OnPropertyChanged(); }
        }

        // Financial Permissions
        private bool _canManageExpenses;
        public bool CanManageExpenses
        {
            get => _canManageExpenses;
            set { _canManageExpenses = value; OnPropertyChanged(); }
        }

        private bool _canManageCashDrawer;
        public bool CanManageCashDrawer
        {
            get => _canManageCashDrawer;
            set { _canManageCashDrawer = value; OnPropertyChanged(); }
        }

        private bool _canViewReports;
        public bool CanViewReports
        {
            get => _canViewReports;
            set { _canViewReports = value; OnPropertyChanged(); }
        }

        private bool _canManagePrices;
        public bool CanManagePrices
        {
            get => _canManagePrices;
            set { _canManagePrices = value; OnPropertyChanged(); }
        }

        // Customer & Supplier Permissions
        private bool _canManageCustomers;
        public bool CanManageCustomers
        {
            get => _canManageCustomers;
            set { _canManageCustomers = value; OnPropertyChanged(); }
        }

        private bool _canManageSuppliers;
        public bool CanManageSuppliers
        {
            get => _canManageSuppliers;
            set { _canManageSuppliers = value; OnPropertyChanged(); }
        }

        // Administrative Permissions
        private bool _canManageUsers;
        public bool CanManageUsers
        {
            get => _canManageUsers;
            set
            {
                var logMessage = $"[USERPERMISSIONS] CanManageUsers setter called: {_canManageUsers} -> {value}";
                System.Diagnostics.Debug.WriteLine(logMessage);
                LogToFile(logMessage);
                _canManageUsers = value;
                OnPropertyChanged();
                var logMessage2 = $"[USERPERMISSIONS] CanManageUsers changed and PropertyChanged fired";
                System.Diagnostics.Debug.WriteLine(logMessage2);
                LogToFile(logMessage2);
            }
        }

        private bool _canManageRoles;
        public bool CanManageRoles
        {
            get => _canManageRoles;
            set { _canManageRoles = value; OnPropertyChanged(); }
        }

        private bool _canAccessSettings;
        public bool CanAccessSettings
        {
            get => _canAccessSettings;
            set
            {
                var logMessage = $"[USERPERMISSIONS] CanAccessSettings setter called: {_canAccessSettings} -> {value}";
                System.Diagnostics.Debug.WriteLine(logMessage);
                LogToFile(logMessage);
                _canAccessSettings = value;
                OnPropertyChanged();
                var logMessage2 = $"[USERPERMISSIONS] CanAccessSettings changed and PropertyChanged fired";
                System.Diagnostics.Debug.WriteLine(logMessage2);
                LogToFile(logMessage2);
            }
        }

        private bool _canViewLogs;
        public bool CanViewLogs
        {
            get => _canViewLogs;
            set { _canViewLogs = value; OnPropertyChanged(); }
        }

        // Invoice Permissions (Two-Tier System)
        private bool _canCreateFullInvoices;
        public bool CanCreateFullInvoices
        {
            get => _canCreateFullInvoices;
            set { _canCreateFullInvoices = value; OnPropertyChanged(); }
        }

        private bool _canCreateDraftInvoices;
        public bool CanCreateDraftInvoices
        {
            get => _canCreateDraftInvoices;
            set { _canCreateDraftInvoices = value; OnPropertyChanged(); }
        }

        private bool _canCompleteInvoiceDrafts;
        public bool CanCompleteInvoiceDrafts
        {
            get => _canCompleteInvoiceDrafts;
            set { _canCompleteInvoiceDrafts = value; OnPropertyChanged(); }
        }

        private bool _canViewPendingDrafts;
        public bool CanViewPendingDrafts
        {
            get => _canViewPendingDrafts;
            set { _canViewPendingDrafts = value; OnPropertyChanged(); }
        }

        private bool _canModifyInvoicePricing;
        public bool CanModifyInvoicePricing
        {
            get => _canModifyInvoicePricing;
            set { _canModifyInvoicePricing = value; OnPropertyChanged(); }
        }

        private bool _canSetPaymentTerms;
        public bool CanSetPaymentTerms
        {
            get => _canSetPaymentTerms;
            set { _canSetPaymentTerms = value; OnPropertyChanged(); }
        }

        private bool _canSelectCustomersForInvoices;
        public bool CanSelectCustomersForInvoices
        {
            get => _canSelectCustomersForInvoices;
            set { _canSelectCustomersForInvoices = value; OnPropertyChanged(); }
        }

        private bool _canDeleteDraftInvoices;
        public bool CanDeleteDraftInvoices
        {
            get => _canDeleteDraftInvoices;
            set { _canDeleteDraftInvoices = value; OnPropertyChanged(); }
        }

        private bool _canRejectDraftInvoices;
        public bool CanRejectDraftInvoices
        {
            get => _canRejectDraftInvoices;
            set { _canRejectDraftInvoices = value; OnPropertyChanged(); }
        }

        private bool _canManageInvoiceSettings;
        public bool CanManageInvoiceSettings
        {
            get => _canManageInvoiceSettings;
            set { _canManageInvoiceSettings = value; OnPropertyChanged(); }
        }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private static void LogToFile(string message)
        {
            try
            {
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "permissions_debug.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}