namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for password hashing and verification services
    /// </summary>
    public interface IPasswordService
    {
        /// <summary>
        /// Hashes a password using a secure hashing algorithm
        /// </summary>
        /// <param name="password">Plain text password to hash</param>
        /// <returns>Hashed password</returns>
        string HashPassword(string password);

        /// <summary>
        /// Verifies a password against a stored hash
        /// </summary>
        /// <param name="password">Plain text password to verify</param>
        /// <param name="hashedPassword">Stored password hash</param>
        /// <returns>True if password matches, false otherwise</returns>
        bool VerifyPassword(string password, string hashedPassword);
    }
}
