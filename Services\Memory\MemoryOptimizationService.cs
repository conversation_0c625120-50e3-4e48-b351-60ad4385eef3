using System;
using System.Diagnostics;
using System.Runtime;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.Memory
{
    /// <summary>
    /// Service for optimizing memory usage and performing periodic cleanup
    /// </summary>
    public class MemoryOptimizationService : BackgroundService
    {
        private readonly ILogger<MemoryOptimizationService> _logger;
        private readonly Timer _cleanupTimer;
        private readonly Timer _gcTimer;
        private long _lastMemoryUsage = 0;
        private int _consecutiveHighMemoryCount = 0;

        // Memory thresholds
        private const long HIGH_MEMORY_THRESHOLD_MB = 300; // 300 MB
        private const long CRITICAL_MEMORY_THRESHOLD_MB = 500; // 500 MB
        private const int CLEANUP_INTERVAL_MINUTES = 5;
        private const int GC_INTERVAL_MINUTES = 10;

        public MemoryOptimizationService(ILogger<MemoryOptimizationService> logger)
        {
            _logger = logger;
            
            // Setup periodic cleanup timer
            _cleanupTimer = new Timer(PerformMemoryCleanup, null, 
                TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES), 
                TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES));
                
            // Setup GC optimization timer
            _gcTimer = new Timer(OptimizeGarbageCollection, null,
                TimeSpan.FromMinutes(GC_INTERVAL_MINUTES),
                TimeSpan.FromMinutes(GC_INTERVAL_MINUTES));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Memory Optimization Service started");
            
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await MonitorMemoryUsage();
                    await Task.Delay(TimeSpan.FromMinutes(2), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in memory monitoring cycle");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }
        }

        private async Task MonitorMemoryUsage()
        {
            try
            {
                var currentMemoryMB = GetCurrentMemoryUsageMB();
                
                // Log memory usage changes
                if (Math.Abs(currentMemoryMB - _lastMemoryUsage) > 20) // 20MB change
                {
                    _logger.LogInformation("Memory usage: {CurrentMB}MB (Change: {Change}MB)", 
                        currentMemoryMB, currentMemoryMB - _lastMemoryUsage);
                }

                // Check for high memory usage
                if (currentMemoryMB > HIGH_MEMORY_THRESHOLD_MB)
                {
                    _consecutiveHighMemoryCount++;
                    _logger.LogWarning("High memory usage detected: {MemoryMB}MB (Count: {Count})", 
                        currentMemoryMB, _consecutiveHighMemoryCount);

                    // Trigger cleanup if memory is consistently high
                    if (_consecutiveHighMemoryCount >= 3)
                    {
                        await TriggerAggressiveCleanup();
                        _consecutiveHighMemoryCount = 0;
                    }
                }
                else
                {
                    _consecutiveHighMemoryCount = 0;
                }

                // Critical memory usage
                if (currentMemoryMB > CRITICAL_MEMORY_THRESHOLD_MB)
                {
                    _logger.LogError("Critical memory usage: {MemoryMB}MB - Triggering emergency cleanup", 
                        currentMemoryMB);
                    await TriggerEmergencyCleanup();
                }

                _lastMemoryUsage = currentMemoryMB;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error monitoring memory usage");
            }
        }

        private void PerformMemoryCleanup(object state)
        {
            try
            {
                var beforeMemory = GetCurrentMemoryUsageMB();
                
                // Clear various caches
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    // Clear WPF bitmap cache
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window.CacheMode != null)
                        {
                            window.CacheMode = null;
                            window.CacheMode = new System.Windows.Media.BitmapCache();
                        }
                    }
                });

                // Force garbage collection
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect(2, GCCollectionMode.Optimized);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Optimized);

                var afterMemory = GetCurrentMemoryUsageMB();
                var freedMemory = beforeMemory - afterMemory;

                if (freedMemory > 5) // Only log if significant memory was freed
                {
                    _logger.LogInformation("Memory cleanup completed: {FreedMB}MB freed ({BeforeMB}MB -> {AfterMB}MB)", 
                        freedMemory, beforeMemory, afterMemory);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during memory cleanup");
            }
        }

        private void OptimizeGarbageCollection(object state)
        {
            try
            {
                // Optimize GC settings for better performance
                if (GCSettings.IsServerGC)
                {
                    // Server GC is already optimized for throughput
                    return;
                }

                // For workstation GC, optimize for lower latency
                GCSettings.LatencyMode = GCLatencyMode.Interactive;
                
                // Compact large object heap periodically
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                
                _logger.LogDebug("GC optimization applied");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing garbage collection");
            }
        }

        private async Task TriggerAggressiveCleanup()
        {
            try
            {
                _logger.LogInformation("Triggering aggressive memory cleanup");
                
                await Application.Current?.Dispatcher.InvokeAsync(() =>
                {
                    // Clear image caches
                    foreach (Window window in Application.Current.Windows)
                    {
                        ClearImageCaches(window);
                    }
                });

                // Multiple GC passes
                for (int i = 0; i < 3; i++)
                {
                    GC.Collect(2, GCCollectionMode.Forced);
                    GC.WaitForPendingFinalizers();
                    await Task.Delay(100);
                }

                _logger.LogInformation("Aggressive cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during aggressive cleanup");
            }
        }

        private async Task TriggerEmergencyCleanup()
        {
            try
            {
                _logger.LogWarning("Triggering emergency memory cleanup");
                
                // Emergency cleanup - more aggressive
                await TriggerAggressiveCleanup();
                
                // Trim working set
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    try
                    {
                        // Use Windows API to trim working set
                        var process = Process.GetCurrentProcess();
                        process.MinWorkingSet = process.MinWorkingSet;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not trim working set");
                    }
                }

                _logger.LogWarning("Emergency cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during emergency cleanup");
            }
        }

        private void ClearImageCaches(DependencyObject parent)
        {
            try
            {
                // This would need to be implemented based on your specific image caching strategy
                // For now, just clear bitmap caches
                if (parent is FrameworkElement element && element.CacheMode != null)
                {
                    element.CacheMode = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error clearing image caches");
            }
        }

        private long GetCurrentMemoryUsageMB()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return process.WorkingSet64 / (1024 * 1024);
            }
            catch
            {
                return 0;
            }
        }

        public override void Dispose()
        {
            _cleanupTimer?.Dispose();
            _gcTimer?.Dispose();
            base.Dispose();
        }
    }
}
