using System;
using System.Windows;
using System.Windows.Input;
using POSLicensingSystem.Views;

namespace POSLicensingSystem.ViewModels
{
    public class MainViewModel : ViewModelBase
    {
        private string _licenseStatus = "License Status: Checking...";

        public string LicenseStatus
        {
            get => _licenseStatus;
            set => SetProperty(ref _licenseStatus, value);
        }

        // Commands
        public ICommand ActivateLicenseCommand { get; }
        public ICommand GenerateLicenseCommand { get; }
        public ICommand CleanLicenseCommand { get; }
        public ICommand ExitCommand { get; }
        public ICommand TestLicensingCommand { get; }
        public ICommand OpenTestCommand { get; }
        public ICommand AboutCommand { get; }

        public MainViewModel()
        {
            // Initialize commands
            ActivateLicenseCommand = new RelayCommand(_ => OpenActivationWindow());
            GenerateLicenseCommand = new RelayCommand(_ => OpenGeneratorWindow());
            CleanLicenseCommand = new RelayCommand(_ => CleanLicense());
            ExitCommand = new RelayCommand(_ => Application.Current.Shutdown());
            TestLicensingCommand = new RelayCommand(_ => OpenTestLicensingWindow());
            OpenTestCommand = new RelayCommand(_ => OpenTestLicensing());
            AboutCommand = new RelayCommand(_ => ShowAboutDialog());

            // Check license status on startup
            CheckLicenseStatus();
        }

        private void OpenActivationWindow()
        {
            try
            {
                var activationWindow = new LicenseActivationView();
                activationWindow.Owner = Application.Current.MainWindow;
                activationWindow.ShowDialog();

                // Refresh license status after dialog is closed
                CheckLicenseStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening activation window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenGeneratorWindow()
        {
            try
            {
                var generatorWindow = new LicenseGeneratorView();
                generatorWindow.Owner = Application.Current.MainWindow;
                generatorWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening generator window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void OpenTestLicensingWindow()
        {
            try
            {
                var testWindow = new TestLicensingView();
                testWindow.Owner = Application.Current.MainWindow;
                testWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening test window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CleanLicense()
        {
            try
            {
                // Display a confirmation dialog
                var result = MessageBox.Show(
                    "Are you sure you want to clean all license files? This will remove any existing license.",
                    "Confirm License Cleanup",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // Placeholder for license cleanup logic
                    // In a real implementation, this would call into a license service
                    
                    MessageBox.Show("License files have been removed successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Update license status
                    LicenseStatus = "License Status: No license found.";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error cleaning license files: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CheckLicenseStatus()
        {
            try
            {
                // Placeholder for license status checking logic
                // In a real implementation, this would call into a license service
                
                // For demonstration, just set a status message
                LicenseStatus = "License Status: No license found. Please activate a license.";
            }
            catch (Exception ex)
            {
                LicenseStatus = $"License Status: Error checking license. {ex.Message}";
            }
        }

        private void OpenTestLicensing()
        {
            var window = new TestLicensingView();
            window.Owner = Application.Current.MainWindow;
            window.ShowDialog();
        }

        private void ShowAboutDialog()
        {
            MessageBox.Show(
                "POS Licensing System\n" +
                "Version 1.0\n\n" +
                "A tool for generating and activating license keys for the POS System.",
                "About POS Licensing System",
                MessageBoxButton.OK,
                MessageBoxImage.Information
            );
        }
    }
} 