-- Migration: Add Receipt Printing Tables
-- Description: Creates tables for receipt templates, printer configurations, and print job tracking
-- Date: 2025-01-10

-- Create ReceiptTemplates table
CREATE TABLE IF NOT EXISTS ReceiptTemplates (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    TemplateType TEXT NOT NULL DEFAULT 'Standard',
    PaperWidth INTEGER NOT NULL DEFAULT 48,
    FontSize INTEGER NOT NULL DEFAULT 12,
    IncludeLogo INTEGER NOT NULL DEFAULT 1,
    IncludeCompanyInfo INTEGER NOT NULL DEFAULT 1,
    IncludeCustomerInfo INTEGER NOT NULL DEFAULT 1,
    IncludeItemDetails INTEGER NOT NULL DEFAULT 1,
    IncludePaymentInfo INTEGER NOT NULL DEFAULT 1,
    IncludeBarcode INTEGER NOT NULL DEFAULT 0,
    FooterText TEXT,
    IsDefault INTEGER NOT NULL DEFAULT 0,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    AdvancedSettings TEXT
);

-- Create PrinterConfigurations table
CREATE TABLE IF NOT EXISTS PrinterConfigurations (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    PrinterType TEXT NOT NULL DEFAULT 'Standard',
    PrinterName TEXT,
    PaperSize TEXT NOT NULL DEFAULT 'A4',
    PrintQuality TEXT NOT NULL DEFAULT 'Normal',
    Copies INTEGER NOT NULL DEFAULT 1,
    IsDefault INTEGER NOT NULL DEFAULT 0,
    IsActive INTEGER NOT NULL DEFAULT 1,
    ConnectionSettings TEXT,
    ReceiptTemplateId INTEGER,
    FOREIGN KEY (ReceiptTemplateId) REFERENCES ReceiptTemplates(Id)
);

-- Create ReceiptPrintSettings table
CREATE TABLE IF NOT EXISTS ReceiptPrintSettings (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    AutoPrintEnabled INTEGER NOT NULL DEFAULT 1,
    ShowPrintDialog INTEGER NOT NULL DEFAULT 0,
    SaveAsPdfBackup INTEGER NOT NULL DEFAULT 0,
    PdfBackupPath TEXT,
    DefaultPrinterConfigId INTEGER,
    DefaultReceiptTemplateId INTEGER,
    EnablePrintPreview INTEGER NOT NULL DEFAULT 0,
    PrintTimeoutSeconds INTEGER NOT NULL DEFAULT 30,
    RetryFailedPrints INTEGER NOT NULL DEFAULT 1,
    MaxRetryAttempts INTEGER NOT NULL DEFAULT 3,
    FOREIGN KEY (DefaultPrinterConfigId) REFERENCES PrinterConfigurations(Id),
    FOREIGN KEY (DefaultReceiptTemplateId) REFERENCES ReceiptTemplates(Id)
);

-- Create ReceiptPrintJobs table
CREATE TABLE IF NOT EXISTS ReceiptPrintJobs (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    Status TEXT NOT NULL DEFAULT 'Pending',
    PrinterConfigId INTEGER NOT NULL,
    ReceiptTemplateId INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    StartedAt TEXT,
    CompletedAt TEXT,
    ErrorMessage TEXT,
    RetryCount INTEGER NOT NULL DEFAULT 0,
    UserId INTEGER NOT NULL,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id),
    FOREIGN KEY (PrinterConfigId) REFERENCES PrinterConfigurations(Id),
    FOREIGN KEY (ReceiptTemplateId) REFERENCES ReceiptTemplates(Id),
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);

-- Insert default receipt template
INSERT OR IGNORE INTO ReceiptTemplates (
    Name, Description, TemplateType, PaperWidth, FontSize,
    IncludeLogo, IncludeCompanyInfo, IncludeCustomerInfo, 
    IncludeItemDetails, IncludePaymentInfo, FooterText, IsDefault, IsActive
) VALUES (
    'Default Receipt Template',
    'Standard receipt template with all basic information',
    'Standard',
    48,
    12,
    1, 1, 1, 1, 1,
    'Thank you for your business!',
    1,
    1
);

-- Insert thermal receipt template
INSERT OR IGNORE INTO ReceiptTemplates (
    Name, Description, TemplateType, PaperWidth, FontSize,
    IncludeLogo, IncludeCompanyInfo, IncludeCustomerInfo, 
    IncludeItemDetails, IncludePaymentInfo, FooterText, IsDefault, IsActive
) VALUES (
    'Thermal Receipt Template',
    'Optimized template for thermal printers (80mm)',
    'Thermal',
    32,
    10,
    0, 1, 1, 1, 1,
    'Thank you for your business!',
    0,
    1
);

-- Insert default printer configuration
INSERT OR IGNORE INTO PrinterConfigurations (
    Name, PrinterType, PaperSize, PrintQuality, Copies, IsDefault, IsActive
) VALUES (
    'Default Printer',
    'Standard',
    'A4',
    'Normal',
    1,
    1,
    1
);

-- Insert thermal printer configuration
INSERT OR IGNORE INTO PrinterConfigurations (
    Name, PrinterType, PaperSize, PrintQuality, Copies, IsDefault, IsActive
) VALUES (
    'Thermal Printer',
    'Thermal',
    'Thermal80mm',
    'Normal',
    1,
    0,
    1
);

-- Insert default print settings
INSERT OR IGNORE INTO ReceiptPrintSettings (
    AutoPrintEnabled, ShowPrintDialog, SaveAsPdfBackup, 
    EnablePrintPreview, PrintTimeoutSeconds, RetryFailedPrints, MaxRetryAttempts,
    DefaultPrinterConfigId, DefaultReceiptTemplateId
) VALUES (
    1, 0, 0, 1, 30, 1, 3,
    (SELECT Id FROM PrinterConfigurations WHERE IsDefault = 1 LIMIT 1),
    (SELECT Id FROM ReceiptTemplates WHERE IsDefault = 1 LIMIT 1)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_receipt_print_jobs_sale_id ON ReceiptPrintJobs(SaleId);
CREATE INDEX IF NOT EXISTS idx_receipt_print_jobs_status ON ReceiptPrintJobs(Status);
CREATE INDEX IF NOT EXISTS idx_receipt_print_jobs_created_at ON ReceiptPrintJobs(CreatedAt);
CREATE INDEX IF NOT EXISTS idx_receipt_templates_is_default ON ReceiptTemplates(IsDefault);
CREATE INDEX IF NOT EXISTS idx_receipt_templates_is_active ON ReceiptTemplates(IsActive);
CREATE INDEX IF NOT EXISTS idx_printer_configs_is_default ON PrinterConfigurations(IsDefault);
CREATE INDEX IF NOT EXISTS idx_printer_configs_is_active ON PrinterConfigurations(IsActive);

-- Update schema version (if you have a schema versioning system)
-- UPDATE SchemaVersion SET Version = Version + 1 WHERE Id = 1;
