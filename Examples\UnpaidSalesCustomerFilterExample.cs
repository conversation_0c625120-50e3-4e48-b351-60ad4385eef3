using System;
using System.Threading.Tasks;
using System.Windows;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Views;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels.Dashboard;

namespace POSSystem.Examples
{
    /// <summary>
    /// Example demonstrating how to use the UnpaidSalesStatsDetailsDialog 
    /// with customer filtering functionality.
    /// </summary>
    public class UnpaidSalesCustomerFilterExample
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;

        public UnpaidSalesCustomerFilterExample(RefactoredDashboardViewModel dashboardViewModel)
        {
            _dashboardViewModel = dashboardViewModel;
        }

        /// <summary>
        /// Shows unpaid sales for all customers (original behavior)
        /// </summary>
        public async Task ShowAllUnpaidSalesAsync()
        {
            try
            {
                var dialog = new UnpaidSalesStatsDetailsDialog(_dashboardViewModel);
                await DialogHost.Show(dialog, "RootDialog");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing unpaid sales: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Shows unpaid sales for a specific customer
        /// </summary>
        public async Task ShowUnpaidSalesForCustomerAsync(Customer customer)
        {
            if (customer == null)
            {
                MessageBox.Show("Please select a customer first.", "No Customer Selected", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var dialog = new UnpaidSalesStatsDetailsDialog(_dashboardViewModel, customer);
                await DialogHost.Show(dialog, "RootDialog");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing unpaid sales for {customer.FullName}: {ex.Message}", 
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Shows customer selection dialog and then displays unpaid sales for selected customer
        /// </summary>
        public async Task ShowCustomerSelectionAndUnpaidSalesAsync()
        {
            try
            {
                // Show customer selection window
                var customerWindow = new CustomerSelectionWindow();
                
                // Set owner if MainWindow is available
                if (Application.Current.MainWindow != null && 
                    Application.Current.MainWindow.IsLoaded)
                {
                    customerWindow.Owner = Application.Current.MainWindow;
                }

                var result = customerWindow.ShowDialog();
                
                if (result == true && customerWindow.SelectedCustomer != null)
                {
                    // Show unpaid sales for the selected customer
                    await ShowUnpaidSalesForCustomerAsync(customerWindow.SelectedCustomer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in customer selection: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Example of how to integrate this into a menu or button click
        /// </summary>
        public async Task OnViewCustomerUnpaidSalesMenuClick()
        {
            await ShowCustomerSelectionAndUnpaidSalesAsync();
        }

        /// <summary>
        /// Example of how to show unpaid sales from a customer context menu
        /// (e.g., right-click on customer in customer list)
        /// </summary>
        public async Task OnCustomerContextMenuUnpaidSalesClick(Customer customer)
        {
            await ShowUnpaidSalesForCustomerAsync(customer);
        }
    }

    /// <summary>
    /// Extension methods to make it easier to use the customer-filtered dialog
    /// </summary>
    public static class UnpaidSalesDialogExtensions
    {
        /// <summary>
        /// Extension method to show unpaid sales dialog for a specific customer
        /// </summary>
        public static async Task ShowUnpaidSalesForCustomerAsync(
            this RefactoredDashboardViewModel dashboardViewModel, 
            Customer customer)
        {
            if (customer == null) return;

            try
            {
                var dialog = new UnpaidSalesStatsDetailsDialog(dashboardViewModel, customer);
                await DialogHost.Show(dialog, "RootDialog");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing unpaid sales for {customer.FullName}: {ex.Message}", 
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Extension method to show all unpaid sales (original behavior)
        /// </summary>
        public static async Task ShowAllUnpaidSalesAsync(
            this RefactoredDashboardViewModel dashboardViewModel)
        {
            try
            {
                var dialog = new UnpaidSalesStatsDetailsDialog(dashboardViewModel);
                await DialogHost.Show(dialog, "RootDialog");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error showing unpaid sales: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
