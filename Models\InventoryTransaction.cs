using POSSystem.Models;

namespace POSSystem.Models
{
    public class InventoryTransaction
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string TransactionType { get; set; } // Purchase, Sale, Adjustment
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string Reference { get; set; }
        public DateTime TransactionDate { get; set; }
        public string Notes { get; set; }
        public int UserId { get; set; }
        public virtual Product Product { get; set; }
        public virtual User User { get; set; }
    }
} 