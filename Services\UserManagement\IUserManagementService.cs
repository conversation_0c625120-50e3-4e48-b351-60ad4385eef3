using POSSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.UserManagement
{
    /// <summary>
    /// Interface for user management operations
    /// </summary>
    public interface IUserManagementService
    {
        /// <summary>
        /// Authenticate user with username and password
        /// </summary>
        Task<User> AuthenticateUserAsync(string username, string password);

        /// <summary>
        /// Get all users
        /// </summary>
        Task<List<User>> GetAllUsersAsync();

        /// <summary>
        /// Get user by ID
        /// </summary>
        Task<User> GetUserByIdAsync(int id);

        /// <summary>
        /// Add new user
        /// </summary>
        Task<int> AddUserAsync(User user, string password);

        /// <summary>
        /// Update existing user
        /// </summary>
        Task<bool> UpdateUserAsync(User user);

        /// <summary>
        /// Update user password
        /// </summary>
        Task<bool> UpdateUserPasswordAsync(int userId, string newPassword);

        /// <summary>
        /// Delete user (soft delete if has sales, hard delete otherwise)
        /// </summary>
        Task<bool> DeleteUserAsync(int id);

        /// <summary>
        /// Get default user (for system operations)
        /// </summary>
        Task<User> GetDefaultUserAsync();

        /// <summary>
        /// Get user permissions
        /// </summary>
        Task<UserPermissions> GetUserPermissionsAsync(int userId);

        /// <summary>
        /// Update user permissions
        /// </summary>
        Task<bool> UpdateUserPermissionsAsync(UserPermissions permissions);

        /// <summary>
        /// Get all roles
        /// </summary>
        Task<List<Role>> GetAllRolesAsync();
    }
}
