using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Dashboard;
using static POSSystem.Services.DatabaseService;
using System.Windows;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels;
using POSSystem.Data;
using System.Windows.Threading;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// ✅ ANTI-FLICKER: ViewModel with optimized chart initialization and smooth loading
    /// </summary>
    public class LowStockStatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private readonly ProductsViewModel _productsViewModel;
        private readonly LowStockDataService _lowStockDataService;
        private bool _isLoading;
        private bool _isInitialLoad = true;
        private string _title;
        private string _subtitle;
        private ICommand _editProductCommand;
        
        // Statistics
        private int _totalLowStockProducts;
        private int _outOfStockProducts;
        private int _nearLowStockProducts;
        private decimal _restockValue;
        private decimal _totalInventoryValue;
        
        // Collections
        private ObservableCollection<ProductWithStockStatus> _lowStockProducts;
        private ObservableCollection<Category> _categories;
        private Category _selectedCategory;
        private bool _isCategoryFilterEnabled;
        
        // Chart data
        private SeriesCollection _stockTrendSeries;
        private string[] _stockTrendLabels;
        private SeriesCollection _categoryDistributionSeries;
        private SeriesCollection _stockLevelsSeries;
        private string[] _stockLevelsLabels;
        
        // Period selection
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;

        public event PropertyChangedEventHandler PropertyChanged;

        public LowStockStatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;
            _productsViewModel = new ProductsViewModel(new AlertService(new POSDbContext(), dbService));
            _lowStockDataService = new LowStockDataService(new POSDbContext());

            // Initialize collections
            LowStockProducts = new ObservableCollection<ProductWithStockStatus>();
            Categories = new ObservableCollection<Category>();
            IsCategoryFilterEnabled = false;
            
            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };
            
            // Set initial period
            SelectedTrendPeriod = TrendPeriods.First();
            
            // Set initial title
            Title = Application.Current.TryFindResource("LowStockAlert")?.ToString() ?? "Low Stock Alert";
            Subtitle = Application.Current.TryFindResource("DetailedStockMetrics")?.ToString() ?? "Detailed stock metrics and trends";
            
            // ✅ SIMPLIFIED: Initialize chart series like other dialogs
            StockTrendSeries = new SeriesCollection();
            CategoryDistributionSeries = new SeriesCollection();
            StockLevelsSeries = new SeriesCollection();

            // ✅ CRITICAL FIX: Initialize data in background like other working dialogs
            _ = InitializeAsync();
        }

        /// <summary>
        /// ✅ ULTRA-FAST: Initialize data in background (same pattern as ProfitStatsDetailsViewModel)
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                await Task.WhenAll(
                    LoadCategoriesAsync(),
                    LoadDataAsync()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing low stock stats: {ex.Message}");
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public bool IsInitialLoad
        {
            get => _isInitialLoad;
            private set { _isInitialLoad = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public int TotalLowStockProducts
        {
            get => _totalLowStockProducts;
            set { _totalLowStockProducts = value; OnPropertyChanged(); }
        }

        public int OutOfStockProducts
        {
            get => _outOfStockProducts;
            set { _outOfStockProducts = value; OnPropertyChanged(); }
        }

        public int NearLowStockProducts
        {
            get => _nearLowStockProducts;
            set { _nearLowStockProducts = value; OnPropertyChanged(); }
        }

        public decimal RestockValue
        {
            get => _restockValue;
            set { _restockValue = Math.Round(value, 2); OnPropertyChanged(); } // ✅ FORMATTING: Round to 2 decimal places
        }

        public decimal TotalInventoryValue
        {
            get => _totalInventoryValue;
            set { _totalInventoryValue = Math.Round(value, 2); OnPropertyChanged(); } // ✅ FORMATTING: Round to 2 decimal places
        }

        public ObservableCollection<ProductWithStockStatus> LowStockProducts
        {
            get => _lowStockProducts;
            set { _lowStockProducts = value; OnPropertyChanged(); }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set { _categories = value; OnPropertyChanged(); }
        }

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set 
            { 
                if (_selectedCategory != value)
                {
                    _selectedCategory = value;
                    OnPropertyChanged();
                    _ = LoadDataAsync();
                }
            }
        }

        public bool IsCategoryFilterEnabled
        {
            get => _isCategoryFilterEnabled;
            set 
            { 
                if (_isCategoryFilterEnabled != value)
                {
                    _isCategoryFilterEnabled = value;
                    OnPropertyChanged();
                    _ = LoadDataAsync();
                }
            }
        }

        public SeriesCollection StockTrendSeries
        {
            get => _stockTrendSeries;
            set { _stockTrendSeries = value; OnPropertyChanged(); }
        }

        public string[] StockTrendLabels
        {
            get => _stockTrendLabels;
            set { _stockTrendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection CategoryDistributionSeries
        {
            get => _categoryDistributionSeries;
            set { _categoryDistributionSeries = value; OnPropertyChanged(); }
        }

        public SeriesCollection StockLevelsSeries
        {
            get => _stockLevelsSeries;
            set { _stockLevelsSeries = value; OnPropertyChanged(); }
        }

        public string[] StockLevelsLabels
        {
            get => _stockLevelsLabels;
            set { _stockLevelsLabels = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set 
            { 
                if (_selectedTrendPeriod != value)
                {
                    _selectedTrendPeriod = value;
                    OnPropertyChanged();
                    _ = LoadDataAsync();
                }
            }
        }

        public Func<double, string> CurrencyFormatter => value => $"{value:N2} DA";

        public Func<double, string> NumberFormatter => value => value.ToString("N0");

        public ICommand EditProductCommand
        {
            get
            {
                return _editProductCommand ?? (_editProductCommand = new RelayCommand<Product>(async (product) =>
                {
                    if (product != null)
                    {
                        try
                        {
                            // Store the current state
                            var currentCategory = SelectedCategory;
                            var currentPeriod = SelectedTrendPeriod;
                            var currentFilter = IsCategoryFilterEnabled;

                            // Show the product edit dialog directly without closing current dialog
                            var dialog = new ProductDialog(_productsViewModel, "RootDialog", product);
                            var result = await DialogHost.Show(dialog, "RootDialog");

                            // Only refresh data if edit was successful (result is true)
                            if (result is bool editResult && editResult)
                            {
                                // Temporarily disable property change notifications
                                using (new BatchUpdate(this))
                                {
                                    // Restore filters without triggering updates
                                    _selectedCategory = currentCategory;
                                    _selectedTrendPeriod = currentPeriod;
                                    _isCategoryFilterEnabled = currentFilter;
                                }

                                // Load new data
                                await LoadDataAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(
                                $"Error editing product: {ex.Message}",
                                "Error",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }));
            }
        }






        /// <summary>
        /// ⚡ ULTRA-FAST: Optimized parallel loading with caching
        /// </summary>
        public async Task LoadDataAsync()
        {
            if (_isLoading) return; // Prevent multiple simultaneous loads

            try
            {
                IsLoading = true;

                // ⚡ PERFORMANCE: Load data in optimized order - metrics first (fastest), then UI data
                await LoadMetricsAsync();

                // Load UI components in parallel
                await Task.WhenAll(
                    LoadChartDataAsync(),
                    LoadProductListAsync()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading low stock stats data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// ⚡ ULTRA-FAST: Load metrics using cached aggregated query
        /// </summary>
        private async Task LoadMetricsAsync()
        {
            try
            {
                var stats = await _dbService.GetLowStockStatisticsAsync();

                // Update UI on main thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    TotalLowStockProducts = stats.TotalLowStockProducts;
                    OutOfStockProducts = stats.OutOfStockProducts;
                    NearLowStockProducts = stats.NearLowStockProducts;
                    RestockValue = stats.RestockValue;
                    TotalInventoryValue = stats.TotalInventoryValue;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ⚡ ULTRA-FAST: Load chart data using cached lightweight query
        /// </summary>
        private async Task LoadChartDataAsync()
        {
            try
            {
                // ⚡ PERFORMANCE: Use cached data service for faster loading
                var chartData = await _lowStockDataService.GetLowStockProductDataAsync(500);

                // Prepare chart data in background
                var trendData = PrepareOptimizedTrendData(chartData);
                var categoryData = PrepareOptimizedCategoryData(chartData);
                var stockLevelsData = PrepareOptimizedStockLevelsData(chartData);

                // Update charts on UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UpdateChartsFromOptimizedData(trendData, categoryData, stockLevelsData);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading chart data: {ex.Message}");
            }
        }



        private (int TotalProducts, int OutOfStock, int NearLowStock, decimal RestockValue) CalculateMetrics(List<Product> products)
        {
            return (
                TotalProducts: products.Count,
                OutOfStock: products.Count(p => p.StockQuantity == 0),
                NearLowStock: products.Count(p => p.StockQuantity > 0 && p.StockQuantity <= p.MinimumStock),
                // ✅ FORMATTING: Ensure proper calculation and rounding for restock value
                RestockValue: Math.Round(products.Sum(p => Math.Max(0, (p.MinimumStock - p.StockQuantity)) * p.PurchasePrice), 2)
            );
        }

        /// <summary>
        /// ✅ ULTRA-FAST: Load categories only if needed (same pattern as other fast dialogs)
        /// </summary>
        private async Task LoadCategoriesAsync()
        {
            try
            {
                if (!Categories.Any())
                {
                    var categories = await _dbService.GetAllCategoriesAsync();
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Categories.Clear();
                        foreach (var category in categories)
                        {
                            Categories.Add(category);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading categories: {ex.Message}");
            }
        }

        /// <summary>
        /// ⚡ ULTRA-FAST: Load product list using cached data with optimized conversion
        /// </summary>
        private async Task LoadProductListAsync()
        {
            try
            {
                // ⚡ PERFORMANCE: Use cached data service and convert efficiently
                var chartData = await _lowStockDataService.GetLowStockProductDataAsync(500);

                // Convert to ProductWithStockStatus in background thread
                var productList = chartData
                    .OrderBy(p => p.StockQuantity) // Most critical first
                    .Select(item => new ProductWithStockStatus
                    {
                        Id = item.Id,
                        Name = item.Name,
                        StockQuantity = item.StockQuantity,
                        MinimumStock = item.MinimumStock,
                        ReorderPoint = item.ReorderPoint,
                        PurchasePrice = item.PurchasePrice,
                        StockStatus = GetStockStatus(item.StockQuantity, item.MinimumStock, item.ReorderPoint),
                        RestockQuantity = Math.Max(0, (item.ReorderPoint > 0 ? item.ReorderPoint : item.MinimumStock) - item.StockQuantity),
                        RestockCost = Math.Max(0, (item.ReorderPoint > 0 ? item.ReorderPoint : item.MinimumStock) - item.StockQuantity) * item.PurchasePrice
                    })
                    .ToList();

                // Update UI in single operation
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    LowStockProducts.Clear();
                    foreach (var product in productList)
                    {
                        LowStockProducts.Add(product);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading product list: {ex.Message}");
            }
        }





        /// <summary>
        /// ✅ PERFORMANCE: Background-only update that skips charts to prevent duplicate loading
        /// </summary>
        private async Task UpdateUIWithProductsBackgroundOnly(List<Product> products)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Calculate metrics only
                var metricsStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var metrics = CalculateMetrics(products);
                metricsStopwatch.Stop();

                // Update UI in a single batch
                var uiStopwatch = System.Diagnostics.Stopwatch.StartNew();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    using (new BatchUpdate(this))
                    {
                        // Update only metrics, not the product collection (to avoid UI refresh)
                        TotalLowStockProducts = metrics.TotalProducts;
                        OutOfStockProducts = metrics.OutOfStock;
                        NearLowStockProducts = metrics.NearLowStock;
                        RestockValue = metrics.RestockValue;
                    }
                }, DispatcherPriority.Background);
                uiStopwatch.Stop();

                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"LowStockDialog: UI updated from fresh in {stopwatch.ElapsedMilliseconds}ms " +
                    $"(metrics: {metricsStopwatch.ElapsedMilliseconds}ms, UI: {uiStopwatch.ElapsedMilliseconds}ms, products: {products.Count})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in background UI update: {ex.Message}");
            }
        }







        /// <summary>
        /// ✅ OPTIMIZED: Create category series with performance optimizations
        /// </summary>
        private SeriesCollection CreateCategorySeriesOptimized(List<(string Category, int Count)> categoryData)
        {
            var series = new SeriesCollection();
            var colors = new[] { Colors.DodgerBlue, Colors.OrangeRed, Colors.ForestGreen, Colors.Purple, Colors.Gold };

            for (int i = 0; i < categoryData.Count; i++)
            {
                var color = colors[i % colors.Length];
                series.Add(new PieSeries
                {
                    Title = categoryData[i].Category,
                    Values = new ChartValues<int> { categoryData[i].Count },
                    DataLabels = false, // Disable for performance
                    Fill = new SolidColorBrush(color)
                });
            }

            return series;
        }

        private (List<int> Values, string[] Labels) PrepareChartData(List<Product> products)
        {
            var trendData = products
                .GroupBy(p => DateTime.Now.Date)
                .OrderBy(g => g.Key)
                .Take(30)
                .Select(g => new { Date = g.Key, Count = g.Count() })
                .ToList();

            return (
                Values: trendData.Select(d => d.Count).ToList(),
                Labels: trendData.Select(d => d.Date.ToString("MMM dd, yyyy")).ToArray()
            );
        }

        private List<(string Category, int Count)> PrepareCategoryData(List<Product> products)
        {
            return products
                .GroupBy(p => p.CategoryId)
                .Select(g => (
                    Category: Categories.FirstOrDefault(c => c.Id == g.Key)?.Name ?? "Uncategorized",
                    Count: g.Count()
                ))
                .OrderByDescending(x => x.Count)
                .ToList();
        }

        private (List<int> Values, string[] Labels) PrepareStockLevelsData(List<Product> products)
        {
            var stockLevelsData = products
                .GroupBy(p => GetStockLevel(p))
                .OrderBy(g => g.Key)
                .Select(g => new { Level = g.Key, Count = g.Count() })
                .ToList();

            return (
                Values: stockLevelsData.Select(d => d.Count).ToList(),
                Labels: stockLevelsData.Select(d => d.Level).ToArray()
            );
        }

        private string GetStockLevel(Product product)
        {
            if (product.StockQuantity == 0)
                return "Out of Stock";
            else if (product.StockQuantity <= product.MinimumStock * 0.5m)
                return "Critical";
            else if (product.StockQuantity <= product.MinimumStock)
                return "Low";
            else
                return "Normal";
        }

        // Helper class for batching property changes
        private class BatchUpdate : IDisposable
        {
            private readonly LowStockStatsDetailsViewModel _viewModel;
            private readonly bool _previousState;

            public BatchUpdate(LowStockStatsDetailsViewModel viewModel)
            {
                _viewModel = viewModel;
                _previousState = _viewModel._isBatchUpdate;
                _viewModel._isBatchUpdate = true;
            }

            public void Dispose()
            {
                _viewModel._isBatchUpdate = _previousState;
                _viewModel.OnPropertyChanged(string.Empty); // Notify all properties changed
            }
        }

        private bool _isBatchUpdate;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            if (!_isBatchUpdate) // Only raise property changed if not in batch update
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        /// <summary>
        /// ⚡ ULTRA-FAST: Optimized chart data preparation using LowStockProductData
        /// </summary>
        private (List<int> Values, string[] Labels) PrepareOptimizedTrendData(List<LowStockProductData> chartData)
        {
            var count = chartData.Count;
            var today = DateTime.Now.ToString("MMM dd");
            return (new List<int> { count }, new string[] { today });
        }

        private List<(string Category, int Count)> PrepareOptimizedCategoryData(List<LowStockProductData> chartData)
        {
            return chartData
                .GroupBy(p => p.CategoryId)
                .Select(g => (
                    Category: g.First().CategoryName,
                    Count: g.Count()
                ))
                .OrderByDescending(x => x.Count)
                .Take(5)
                .ToList();
        }

        private (List<int> Values, string[] Labels) PrepareOptimizedStockLevelsData(List<LowStockProductData> chartData)
        {
            var stockLevels = new Dictionary<string, int>
            {
                ["Out of Stock"] = chartData.Count(p => p.StockQuantity == 0),
                ["Low Stock"] = chartData.Count(p => p.StockQuantity > 0 && p.StockQuantity <= p.MinimumStock),
                ["Normal"] = chartData.Count(p => p.StockQuantity > p.MinimumStock)
            };

            return (stockLevels.Values.ToList(), stockLevels.Keys.ToArray());
        }

        private void UpdateChartsFromOptimizedData(
            (List<int> Values, string[] Labels) trendData,
            List<(string Category, int Count)> categoryData,
            (List<int> Values, string[] Labels) stockLevelsData)
        {
            StockTrendSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "Low Stock Products",
                    Values = new ChartValues<int>(trendData.Values)
                }
            };
            StockTrendLabels = trendData.Labels;

            CategoryDistributionSeries = CreateOptimizedCategorySeries(categoryData);

            StockLevelsSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "Products",
                    Values = new ChartValues<int>(stockLevelsData.Values)
                }
            };
            StockLevelsLabels = stockLevelsData.Labels;
        }



        private SeriesCollection CreateOptimizedCategorySeries(List<(string Category, int Count)> categoryData)
        {
            var series = new SeriesCollection();
            var colors = new[] { Colors.DodgerBlue, Colors.OrangeRed, Colors.ForestGreen, Colors.Purple, Colors.Gold };

            for (int i = 0; i < categoryData.Count; i++)
            {
                var color = colors[i % colors.Length];
                series.Add(new PieSeries
                {
                    Title = categoryData[i].Category,
                    Values = new ChartValues<int> { categoryData[i].Count },
                    Fill = new SolidColorBrush(color)
                });
            }

            return series;
        }

        /// <summary>
        /// ✅ ULTRA-FAST: Helper method for stock status (same pattern as other fast dialogs)
        /// ✅ FIXED: Updated to handle both MinimumStock and ReorderPoint with decimal support
        /// </summary>
        private string GetStockStatus(decimal stockQuantity, int minimumStock, int reorderPoint)
        {
            if (stockQuantity == 0)
                return "Out of Stock";
            else if (stockQuantity <= minimumStock || stockQuantity <= reorderPoint)
                return "Low Stock";
            else
                return "Normal";
        }

        // Keep the old method for backward compatibility
        private string GetStockStatus(decimal stockQuantity, int reorderPoint)
        {
            return GetStockStatus(stockQuantity, reorderPoint, reorderPoint);
        }

        /// <summary>
        /// ✅ ANTI-FLICKER: Initialize charts with placeholder data to prevent empty rendering
        /// </summary>
        private void InitializeChartsWithPlaceholders()
        {
            // Initialize with minimal placeholder data to prevent flickering
            StockTrendSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "Loading...",
                    Values = new ChartValues<int> { 0 },
                    PointGeometry = null,
                    LineSmoothness = 0,
                    Stroke = System.Windows.Media.Brushes.Transparent
                }
            };
            StockTrendLabels = new[] { "Loading..." };

            CategoryDistributionSeries = new SeriesCollection
            {
                new PieSeries
                {
                    Title = "Loading...",
                    Values = new ChartValues<int> { 1 },
                    Fill = System.Windows.Media.Brushes.Transparent
                }
            };

            StockLevelsSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "Loading...",
                    Values = new ChartValues<int> { 0 },
                    Fill = System.Windows.Media.Brushes.Transparent
                }
            };
            StockLevelsLabels = new[] { "Loading..." };
        }
    }



    /// <summary>
    /// ✅ OPTIMIZED: Chart data container for parallel processing
    /// </summary>
    public class ChartDataSet
    {
        public (List<int> Values, string[] Labels) TrendData { get; set; }
        public List<(string Category, int Count)> CategoryData { get; set; }
        public (List<int> Values, string[] Labels) StockLevelsData { get; set; }
    }

    /// <summary>
    /// ✅ ULTRA-OPTIMIZED: Static data structure for maximum performance (same pattern as other fast dialogs)
    /// No property change notifications, no complex calculations, pure data
    /// </summary>
    public class ProductWithStockStatus
    {
        // ✅ PERFORMANCE: Simple properties for ultra-fast binding
        public int Id { get; set; }
        public string Name { get; set; }
        public string SKU { get; set; }
        public decimal StockQuantity { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderPoint { get; set; }
        public decimal PurchasePrice { get; set; }
        public string StockStatus { get; set; }
        public decimal RestockQuantity { get; set; }
        public decimal RestockCost { get; set; }
    }
}