using POSSystem.Models;

public class Audit
{
    public int Id { get; set; }
    public string TableName { get; set; }
    public string Action { get; set; } // Insert/Update/Delete
    public int RecordId { get; set; }
    public string OldValues { get; set; } // JSON string
    public string NewValues { get; set; } // JSON string
    public DateTime Timestamp { get; set; }
    public int UserId { get; set; }
    public virtual User User { get; set; }
} 