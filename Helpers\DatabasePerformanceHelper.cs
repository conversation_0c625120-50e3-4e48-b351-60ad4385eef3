using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ PERFORMANCE OPTIMIZATION: Specialized helper for database operations to prevent UI thread blocking
    /// </summary>
    public static class DatabasePerformanceHelper
    {
        private static readonly Dictionary<string, List<double>> _operationTimes = new();
        private static readonly object _lockObject = new();

        /// <summary>
        /// Execute a database query on background thread with performance monitoring
        /// </summary>
        public static async Task<T> ExecuteQueryAsync<T>(
            Func<POSDbContext, Task<T>> query,
            string operationName = "Database Query",
            int timeoutSeconds = 30)
        {
            var stopwatch = Stopwatch.StartNew();
            var operationId = Guid.NewGuid().ToString("N")[..8];

            try
            {
                Debug.WriteLine($"[DB-{operationId}] Starting {operationName}...");

                // Execute on background thread to prevent UI blocking
                var result = await Task.Run(async () =>
                {
                    using var context = new POSDbContext();
                    
                    // Configure context for optimal performance
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    context.ChangeTracker.AutoDetectChangesEnabled = false;
                    context.Database.SetCommandTimeout(timeoutSeconds);

                    return await query(context);
                });

                stopwatch.Stop();
                var elapsedMs = stopwatch.ElapsedMilliseconds;

                // Track performance metrics
                lock (_lockObject)
                {
                    if (!_operationTimes.ContainsKey(operationName))
                        _operationTimes[operationName] = new List<double>();
                    
                    _operationTimes[operationName].Add(elapsedMs);
                    
                    // Keep only last 50 measurements
                    if (_operationTimes[operationName].Count > 50)
                        _operationTimes[operationName].RemoveAt(0);
                }

                // Log performance based on POS system requirements
                if (elapsedMs > 1000)
                {
                    Debug.WriteLine($"[DB-{operationId}] 🔴 SLOW QUERY: {operationName} took {elapsedMs}ms - UNACCEPTABLE for POS");
                }
                else if (elapsedMs > 500)
                {
                    Debug.WriteLine($"[DB-{operationId}] 🟠 MODERATE: {operationName} took {elapsedMs}ms - May impact UX");
                }
                else if (elapsedMs > 200)
                {
                    Debug.WriteLine($"[DB-{operationId}] 🟡 ACCEPTABLE: {operationName} took {elapsedMs}ms");
                }
                else
                {
                    Debug.WriteLine($"[DB-{operationId}] ✅ FAST: {operationName} took {elapsedMs}ms");
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[DB-{operationId}] ❌ FAILED: {operationName} after {stopwatch.ElapsedMilliseconds}ms - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Execute multiple database queries in parallel with performance monitoring
        /// </summary>
        public static async Task<(T1, T2)> ExecuteParallelQueriesAsync<T1, T2>(
            Func<POSDbContext, Task<T1>> query1,
            Func<POSDbContext, Task<T2>> query2,
            string operation1Name = "Query 1",
            string operation2Name = "Query 2")
        {
            var task1 = ExecuteQueryAsync(query1, operation1Name);
            var task2 = ExecuteQueryAsync(query2, operation2Name);

            await Task.WhenAll(task1, task2);

            return (await task1, await task2);
        }

        /// <summary>
        /// Execute database operation with UI update on completion
        /// </summary>
        public static async Task ExecuteWithUIUpdateAsync<T>(
            Func<POSDbContext, Task<T>> query,
            Action<T> uiUpdate,
            string operationName = "Database Operation",
            Action<Exception> errorHandler = null)
        {
            try
            {
                var result = await ExecuteQueryAsync(query, operationName);
                
                // Update UI on main thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    uiUpdate(result);
                }, DispatcherPriority.Normal);
            }
            catch (Exception ex)
            {
                if (errorHandler != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        errorHandler(ex);
                    });
                }
                else
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// Get performance statistics for database operations
        /// </summary>
        public static void LogPerformanceStatistics()
        {
            lock (_lockObject)
            {
                if (_operationTimes.Count == 0)
                {
                    Debug.WriteLine("📊 No database performance data available");
                    return;
                }

                Debug.WriteLine("📊 DATABASE PERFORMANCE STATISTICS:");
                Debug.WriteLine("=====================================");

                foreach (var kvp in _operationTimes.OrderBy(x => x.Key))
                {
                    var times = kvp.Value;
                    if (times.Count == 0) continue;

                    var avg = times.Average();
                    var min = times.Min();
                    var max = times.Max();
                    var count = times.Count;

                    var status = avg > 500 ? "🔴 NEEDS OPTIMIZATION" : 
                                avg > 200 ? "🟡 MONITOR" : "✅ GOOD";

                    Debug.WriteLine($"{kvp.Key}: Avg={avg:F0}ms, Min={min:F0}ms, Max={max:F0}ms, Count={count} {status}");
                }

                Debug.WriteLine("=====================================");
            }
        }

        /// <summary>
        /// Check if any database operations are performing poorly
        /// </summary>
        public static bool HasPerformanceIssues()
        {
            lock (_lockObject)
            {
                return _operationTimes.Values
                    .Where(times => times.Count > 0)
                    .Any(times => times.Average() > 500);
            }
        }

        /// <summary>
        /// Clear performance statistics
        /// </summary>
        public static void ClearStatistics()
        {
            lock (_lockObject)
            {
                _operationTimes.Clear();
            }
        }

        /// <summary>
        /// Execute a database command that doesn't return data (INSERT, UPDATE, DELETE)
        /// </summary>
        public static async Task ExecuteCommandAsync(
            Func<POSDbContext, Task<int>> command,
            string operationName = "Database Command")
        {
            await ExecuteQueryAsync(async context =>
            {
                var result = await command(context);
                return result;
            }, operationName);
        }

        /// <summary>
        /// Execute database operation with retry logic for transient failures
        /// </summary>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<POSDbContext, Task<T>> query,
            string operationName = "Database Query",
            int maxRetries = 3,
            int delayMs = 1000)
        {
            Exception lastException = null;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await ExecuteQueryAsync(query, $"{operationName} (Attempt {attempt})");
                }
                catch (Exception ex) when (IsTransientException(ex) && attempt < maxRetries)
                {
                    lastException = ex;
                    Debug.WriteLine($"[DB-RETRY] Attempt {attempt} failed for {operationName}: {ex.Message}. Retrying in {delayMs}ms...");
                    await Task.Delay(delayMs);
                    delayMs *= 2; // Exponential backoff
                }
            }

            throw lastException ?? new Exception($"All {maxRetries} attempts failed for {operationName}");
        }

        /// <summary>
        /// Check if an exception is transient and worth retrying
        /// </summary>
        private static bool IsTransientException(Exception ex)
        {
            // Common transient exceptions that might be worth retrying
            return ex is TimeoutException ||
                   ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
                   ex.Message.Contains("connection", StringComparison.OrdinalIgnoreCase) ||
                   ex.Message.Contains("network", StringComparison.OrdinalIgnoreCase);
        }
    }
}
