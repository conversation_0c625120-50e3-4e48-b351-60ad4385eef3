using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// Repository interface for Sale operations
    /// SAFE APPROACH: Works alongside existing DatabaseService
    /// </summary>
    public interface ISaleRepository
    {
        // Core CRUD operations
        Task<Sale> GetByIdAsync(int id);
        Task<IEnumerable<Sale>> GetAllAsync();
        Task<IEnumerable<Sale>> GetPagedAsync(int page, int pageSize);
        Task<Sale> CreateAsync(Sale sale);
        Task UpdateAsync(Sale sale);
        Task DeleteAsync(int id);

        // Date-based queries (high performance versions)
        Task<IEnumerable<Sale>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Sale>> GetByDateAsync(DateTime date);
        Task<IEnumerable<Sale>> GetRecentAsync(int limit = 10);

        // Business queries
        Task<IEnumerable<Sale>> GetUnpaidSalesAsync();
        Task<IEnumerable<Sale>> GetByCustomerAsync(int customerId);
        Task<IEnumerable<Sale>> GetByUserAsync(int userId);

        // Statistics (replacing heavy DatabaseService calculations)
        Task<decimal> GetTotalSalesAsync(DateTime date);
        Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate);
        Task<int> GetSalesCountAsync(DateTime startDate, DateTime endDate);
        Task<Dictionary<string, decimal>> GetSalesByPaymentMethodAsync(DateTime startDate, DateTime endDate);

        // Performance optimized queries
        Task<IEnumerable<Sale>> GetTopSalesAsync(int limit = 10);
        Task<bool> HasSalesForDateAsync(DateTime date);
    }
}
