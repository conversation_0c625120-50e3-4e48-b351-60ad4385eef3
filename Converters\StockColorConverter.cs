using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace POSSystem.Converters
{
    public class StockColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isStockSufficient)
            {
                // If stock is sufficient, return green, otherwise red
                return isStockSufficient 
                    ? new SolidColorBrush(Color.FromRgb(76, 175, 80)) // Green 
                    : new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red
            }
            
            // Default to green
            return new SolidColorBrush(Color.FromRgb(76, 175, 80));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 