-- POS System Test Database - Generate One Month of Simulated Data
-- This script generates a month's worth of simulated data for testing and demonstration purposes

-- Set pragmas for better performance
PRAGMA synchronous = OFF;
PRAGMA journal_mode = MEMORY;
PRAGMA temp_store = MEMORY;
PRAGMA foreign_keys = ON;

-- Define the date range for our simulated month (last 30 days)
-- Using variables to store the start and end dates
BEGIN TRANSACTION;

-- Helper function to generate random numbers between min and max
CREATE TEMPORARY TABLE RandomHelper AS SELECT 1 AS dummy;
CREATE TEMPORARY FUNCTION RandomInt(min INT, max INT) 
RETURNS INT AS 
BEGIN
    RETURN min + ABS(RANDOM()) % (max - min + 1);
END;

CREATE TEMPORARY FUNCTION RandomDecimal(min DECIMAL, max DECIMAL)
RETURNS DECIMAL AS
BEGIN
    RETURN min + (ABS(RANDOM()) % 1000000) / 1000000.0 * (max - min);
END;

-- Clean up existing data we don't want to duplicate
DELETE FROM UserFavorites;
DELETE FROM Discounts;
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM CashTransactions;
DELETE FROM CashDrawers WHERE Id > 0;
DELETE FROM InventoryTransactions;
DELETE FROM PurchaseOrderItems;
DELETE FROM PurchaseOrders;
DELETE FROM LoyaltyTransactions;

-- Reset all product stock to initial values
UPDATE Products SET StockQuantity = 
    CASE 
        WHEN Id BETWEEN 1 AND 4 THEN 50  -- Electronics
        WHEN Id BETWEEN 5 AND 8 THEN 100 -- Groceries
        WHEN Id BETWEEN 9 AND 12 THEN 80 -- Clothing
        ELSE 50
    END;

-- Reset autoincrement counters
DELETE FROM sqlite_sequence WHERE name IN (
    'Sales', 'SaleItems', 'Discounts', 'CashDrawers', 'CashTransactions', 
    'InventoryTransactions', 'PurchaseOrders', 'PurchaseOrderItems', 'LoyaltyTransactions'
);

-- Create a month worth of dates (30 days from today backwards)
CREATE TEMPORARY TABLE DatesInMonth AS
WITH RECURSIVE DateSeries(date_value, day_number) AS (
    SELECT datetime('now', '-30 days'), 1
    UNION ALL
    SELECT datetime(date_value, '+1 day'), day_number + 1
    FROM DateSeries
    WHERE day_number < 30
)
SELECT date_value, day_number, 
    -- Generate a "busyness factor" for each day (1-10)
    -- Weekends (Saturday/Sunday) are busier
    CASE 
        WHEN strftime('%w', date_value) IN ('0', '6') THEN RandomInt(7, 10)  -- Weekend: busier
        WHEN strftime('%w', date_value) = '5' THEN RandomInt(6, 9)           -- Friday: quite busy
        WHEN strftime('%w', date_value) = '1' THEN RandomInt(3, 5)           -- Monday: slower
        ELSE RandomInt(4, 7)                                                -- Other days: average
    END as busyness_factor
FROM DateSeries;

-- Add some special event days with higher traffic
UPDATE DatesInMonth SET busyness_factor = 10 
WHERE day_number IN (SELECT RandomInt(1, 30) FROM RandomHelper LIMIT 3);

-- Create cash drawers for each day
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
SELECT 
    500.00 AS OpeningBalance,
    500.00 + (100.0 * busyness_factor) + RandomDecimal(-50.0, 50.0) AS CurrentBalance, -- Approximate based on busyness
    500.00 + (100.0 * busyness_factor) AS ExpectedBalance,
    500.00 + (100.0 * busyness_factor) + RandomDecimal(-50.0, 50.0) AS ActualBalance,
    RandomDecimal(-10.0, 10.0) AS Difference, -- Small discrepancies
    'Closed' AS Status,
    datetime(date_value, '09:00:00') AS OpenedAt,
    datetime(date_value, '18:00:00') AS ClosedAt,
    RandomInt(1, 3) AS OpenedById, -- Random user opens
    2 AS ClosedById, -- Manager always closes
    CASE 
        WHEN RandomInt(1, 10) = 1 THEN 'Had to recount at end of day'
        WHEN RandomInt(1, 20) = 1 THEN 'Register had technical issue'
        ELSE 'Normal business day'
    END AS Notes
FROM DatesInMonth;

-- Generate a varying number of sales for each day based on busyness factor
CREATE TEMPORARY TABLE SaleGenerator AS
WITH RECURSIVE NumRange(n) AS (
    SELECT 1
    UNION ALL
    SELECT n + 1 FROM NumRange WHERE n < 100
)
SELECT 
    date_value, 
    cd.Id AS CashDrawerId,
    n AS SaleNumber,
    busyness_factor,
    -- Distribute sales throughout the day
    time(
        printf('%02d', 9 + (n * 9 / (busyness_factor * 5)) / 3600),
        printf('%02d', ((n * 9 / (busyness_factor * 5)) % 3600) / 60),
        printf('%02d', ((n * 9 / (busyness_factor * 5)) % 60))
    ) AS SaleTime,
    RandomInt(1, 4) AS NumItems, -- Random number of items per sale
    -- Customer ID (NULL = guest, or customer ID 1-4)
    CASE WHEN RandomInt(1, 4) = 1 THEN NULL ELSE RandomInt(1, 4) END AS CustomerId,
    -- Cashier (User ID 2-3, mostly cashier)
    CASE WHEN RandomInt(1, 10) <= 8 THEN 3 ELSE 2 END AS UserId,
    -- Payment method
    CASE 
        WHEN RandomInt(1, 10) <= 6 THEN 'Card' -- 60% card
        WHEN RandomInt(1, 10) <= 9 THEN 'Cash' -- 30% cash
        ELSE 'Mobile'                          -- 10% mobile payment
    END AS PaymentMethod
FROM DatesInMonth
JOIN CashDrawers cd ON datetime(cd.OpenedAt) = datetime(DatesInMonth.date_value, '09:00:00')
JOIN NumRange ON NumRange.n <= (busyness_factor * 5) -- Scale number of sales by busyness
ORDER BY date_value, SaleTime;

-- Create the sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                   TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
SELECT 
    'INV-' || strftime('%y%m%d', date_value) || '-' || printf('%03d', SaleNumber) AS InvoiceNumber,
    datetime(date_value, SaleTime) AS SaleDate,
    CustomerId,
    UserId,
    0 AS Subtotal, -- Will be updated later when items are added
    0 AS DiscountAmount, -- Will be updated if discounts are applied
    0 AS TaxAmount, -- No tax in our sample
    0 AS GrandTotal, -- Will be updated
    0 AS AmountPaid, -- Will be updated
    0 AS Change, -- Will be updated
    PaymentMethod,
    'Paid' AS PaymentStatus,
    'Completed' AS Status,
    NumItems AS TotalItems
FROM SaleGenerator;

-- Generate sale items
CREATE TEMPORARY TABLE ProductWeights AS
SELECT 
    Id AS ProductId,
    CASE 
        -- Groceries sell more frequently
        WHEN Id BETWEEN 5 AND 8 THEN 30
        -- Electronics are expensive but sell less frequently
        WHEN Id BETWEEN 1 AND 4 THEN 5
        -- Clothing is in the middle
        ELSE 15
    END AS Weight
FROM Products;

-- Add items to each sale
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
WITH SaleItemGenerator AS (
    SELECT 
        s.Id AS SaleId,
        s.CustomerId,
        s.UserId,
        -- Use weighted random selection for products
        p.Id AS ProductId,
        p.SellingPrice AS UnitPrice,
        -- Randomly choose quantity (groceries typically have higher quantities)
        CASE 
            WHEN p.Id BETWEEN 5 AND 8 THEN RandomInt(1, 5)  -- Groceries
            WHEN p.Id BETWEEN 1 AND 4 THEN 1                -- Electronics (usually just 1)
            ELSE RandomInt(1, 3)                           -- Clothing (1-3 pieces)
        END AS Quantity
    FROM Sales s
    CROSS JOIN (
        SELECT p.Id, p.SellingPrice
        FROM Products p
        JOIN ProductWeights pw ON p.Id = pw.ProductId
        ORDER BY RANDOM() * pw.Weight DESC
        LIMIT 1
    ) p
    -- Add more items to match TotalItems
    UNION ALL
    SELECT 
        s.Id AS SaleId,
        s.CustomerId,
        s.UserId,
        p.Id AS ProductId,
        p.SellingPrice AS UnitPrice,
        CASE 
            WHEN p.Id BETWEEN 5 AND 8 THEN RandomInt(1, 5)
            WHEN p.Id BETWEEN 1 AND 4 THEN 1
            ELSE RandomInt(1, 3)
        END AS Quantity
    FROM Sales s
    CROSS JOIN (
        SELECT p.Id, p.SellingPrice 
        FROM Products p
        JOIN ProductWeights pw ON p.Id = pw.ProductId
        WHERE p.Id NOT IN (
            SELECT ProductId FROM SaleItems WHERE SaleId = s.Id
        )
        ORDER BY RANDOM() * pw.Weight DESC
        LIMIT 1
    ) p
    WHERE (SELECT COUNT(*) FROM SaleItems WHERE SaleId = s.Id) < s.TotalItems - 1
)
SELECT 
    SaleId,
    ProductId,
    Quantity,
    UnitPrice,
    Quantity * UnitPrice AS Total
FROM SaleItemGenerator;

-- Fix any sales that have fewer items than TotalItems due to exclusions
UPDATE Sales
SET TotalItems = (SELECT COUNT(*) FROM SaleItems WHERE SaleId = Sales.Id)
WHERE TotalItems > (SELECT COUNT(*) FROM SaleItems WHERE SaleId = Sales.Id);

-- Calculate subtotals and grand totals for each sale
UPDATE Sales
SET 
    Subtotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    GrandTotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    AmountPaid = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    Change = CASE 
        WHEN PaymentMethod = 'Cash' THEN 
            -- For cash payments, calculate some change based on rounding up
            ROUND((SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id) + 0.01, 0) - 
            (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id)
        ELSE 0
    END;

-- Add some discounts (applied to ~20% of sales)
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
SELECT
    -- Mostly percentage discounts, some fixed amount
    CASE WHEN RandomInt(1, 10) <= 7 THEN 1 ELSE 2 END AS DiscountTypeId,
    -- Value based on discount type and user role
    CASE 
        -- Percentage discount
        WHEN RandomInt(1, 10) <= 7 THEN 
            CASE 
                WHEN s.UserId = 1 THEN RandomInt(5, 50)     -- Admin: 5-50%
                WHEN s.UserId = 2 THEN RandomInt(5, 25)     -- Manager: 5-25%
                ELSE RandomInt(5, 10)                      -- Cashier: 5-10%
            END
        -- Fixed amount discount (capped at 50% of price)
        ELSE 
            CASE 
                WHEN s.UserId = 1 THEN RandomDecimal(10, MIN(100, s.Subtotal * 0.5))  -- Admin: $10-$100 or 50%
                WHEN s.UserId = 2 THEN RandomDecimal(5, MIN(50, s.Subtotal * 0.5))    -- Manager: $5-$50 or 50%
                ELSE RandomDecimal(5, MIN(20, s.Subtotal * 0.5))                     -- Cashier: $5-$20 or 50%
            END
    END AS DiscountValue,
    s.Subtotal AS OriginalPrice,
    -- Calculate final price based on discount type
    CASE 
        -- Percentage discount
        WHEN RandomInt(1, 10) <= 7 THEN 
            s.Subtotal * (1 - (
                CASE 
                    WHEN s.UserId = 1 THEN RandomInt(5, 50)
                    WHEN s.UserId = 2 THEN RandomInt(5, 25)
                    ELSE RandomInt(5, 10)
                END / 100.0
            ))
        -- Fixed amount discount
        ELSE 
            s.Subtotal - (
                CASE 
                    WHEN s.UserId = 1 THEN RandomDecimal(10, MIN(100, s.Subtotal * 0.5))
                    WHEN s.UserId = 2 THEN RandomDecimal(5, MIN(50, s.Subtotal * 0.5))
                    ELSE RandomDecimal(5, MIN(20, s.Subtotal * 0.5))
                END
            )
    END AS FinalPrice,
    -- Random discount reason
    (SELECT Id FROM DiscountReasons ORDER BY RANDOM() LIMIT 1) AS ReasonId,
    -- Generate comments
    CASE RandomInt(1, 5)
        WHEN 1 THEN 'Regular customer discount'
        WHEN 2 THEN 'Special promotion'
        WHEN 3 THEN 'Manager approval for loyal customer'
        WHEN 4 THEN 'Product slightly damaged'
        ELSE 'Customer satisfaction'
    END AS Comment,
    s.Id AS SaleId,
    NULL AS SaleItemId, -- Discount applies to whole sale
    s.UserId AS AppliedByUserId,
    s.SaleDate AS AppliedAt,
    1 AS IsActive
FROM Sales s
WHERE RandomInt(1, 5) = 1  -- Apply to ~20% of sales
AND s.TotalItems > 0;      -- Only to sales with items

-- Item-specific discounts (applied to ~10% of sale items in the remaining 80% of sales)
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
SELECT
    -- Mostly percentage discounts for items
    CASE WHEN RandomInt(1, 10) <= 8 THEN 1 ELSE 2 END AS DiscountTypeId,
    -- Value based on discount type and user role
    CASE 
        -- Percentage discount
        WHEN RandomInt(1, 10) <= 8 THEN 
            CASE 
                WHEN s.UserId = 1 THEN RandomInt(10, 75)     -- Admin: 10-75%
                WHEN s.UserId = 2 THEN RandomInt(10, 40)     -- Manager: 10-40%
                ELSE RandomInt(5, 15)                        -- Cashier: 5-15%
            END
        -- Fixed amount discount (capped at 75% of item price)
        ELSE 
            CASE 
                WHEN s.UserId = 1 THEN RandomDecimal(5, MIN(si.Total * 0.75, 50))  -- Admin: up to 75% or $50
                WHEN s.UserId = 2 THEN RandomDecimal(5, MIN(si.Total * 0.5, 30))   -- Manager: up to 50% or $30
                ELSE RandomDecimal(2, MIN(si.Total * 0.25, 10))                   -- Cashier: up to 25% or $10
            END
    END AS DiscountValue,
    si.Total AS OriginalPrice,
    -- Calculate final price based on discount type
    CASE 
        -- Percentage discount
        WHEN RandomInt(1, 10) <= 8 THEN 
            si.Total * (1 - (
                CASE 
                    WHEN s.UserId = 1 THEN RandomInt(10, 75)
                    WHEN s.UserId = 2 THEN RandomInt(10, 40)
                    ELSE RandomInt(5, 15)
                END / 100.0
            ))
        -- Fixed amount discount
        ELSE 
            si.Total - (
                CASE 
                    WHEN s.UserId = 1 THEN RandomDecimal(5, MIN(si.Total * 0.75, 50))
                    WHEN s.UserId = 2 THEN RandomDecimal(5, MIN(si.Total * 0.5, 30))
                    ELSE RandomDecimal(2, MIN(si.Total * 0.25, 10))
                END
            )
    END AS FinalPrice,
    -- Random discount reason
    (SELECT Id FROM DiscountReasons ORDER BY RANDOM() LIMIT 1) AS ReasonId,
    -- Generate item-specific comments
    CASE RandomInt(1, 5)
        WHEN 1 THEN 'Display model'
        WHEN 2 THEN 'Last item in stock'
        WHEN 3 THEN 'Price match competitor'
        WHEN 4 THEN 'Packaging damaged'
        ELSE 'Customer loyalty discount'
    END AS Comment,
    s.Id AS SaleId,
    si.Id AS SaleItemId,
    s.UserId AS AppliedByUserId,
    s.SaleDate AS AppliedAt,
    1 AS IsActive
FROM Sales s
JOIN SaleItems si ON s.Id = si.SaleId
WHERE s.Id NOT IN (SELECT SaleId FROM Discounts WHERE SaleItemId IS NULL) -- Exclude sales with whole-sale discounts
AND RandomInt(1, 10) = 1;  -- Apply to ~10% of remaining items

-- Update sales with discounts
UPDATE Sales
SET 
    DiscountAmount = (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ),
    GrandTotal = Subtotal - (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ),
    AmountPaid = Subtotal - (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ) + Change -- Add back the change for Cash payments
WHERE Id IN (SELECT DISTINCT SaleId FROM Discounts);

-- Generate purchase orders for stock replenishment (2-3 per week)
INSERT INTO PurchaseOrders (OrderNumber, OrderDate, DueDate, SupplierId, Subtotal, TaxAmount, 
                           GrandTotal, Status, Notes, CreatedByUserId, PaymentMethod, PaymentReference, 
                           PaymentDate, CreatedAt, UpdatedAt)
WITH WeeklyPOs AS (
    SELECT 
        date_value,
        -- Create 2-3 POs per week
        ROW_NUMBER() OVER (PARTITION BY strftime('%W', date_value) ORDER BY date_value) AS PO_Number,
        -- Assign a supplier based on what needs to be ordered
        CASE 
            WHEN ROW_NUMBER() OVER (PARTITION BY strftime('%W', date_value) ORDER BY date_value) = 1 THEN 2 -- Groceries
            WHEN ROW_NUMBER() OVER (PARTITION BY strftime('%W', date_value) ORDER BY date_value) = 2 THEN 3 -- Clothing
            ELSE 1 -- Electronics
        END AS SupplierId
    FROM DatesInMonth
    WHERE strftime('%w', date_value) = '1' -- Create POs on Mondays
)
SELECT 
    'PO-' || strftime('%y%m%d', date_value) || '-' || PO_Number AS OrderNumber,
    datetime(date_value) AS OrderDate,
    datetime(date_value, '+3 days') AS DueDate,
    SupplierId,
    0 AS Subtotal, -- Will be calculated after adding items
    0 AS TaxAmount,
    0 AS GrandTotal,
    CASE 
        WHEN datetime('now') > datetime(date_value, '+3 days') THEN 'Received'
        WHEN datetime('now') > datetime(date_value) THEN 'In Transit'
        ELSE 'Pending'
    END AS Status,
    CASE 
        WHEN SupplierId = 1 THEN 'Regular electronics order'
        WHEN SupplierId = 2 THEN 'Weekly grocery restock'
        ELSE 'Clothing inventory replenishment'
    END AS Notes,
    -- Admin or Manager creates POs
    CASE WHEN RandomInt(1, 2) = 1 THEN 1 ELSE 2 END AS CreatedByUserId,
    CASE 
        WHEN RandomInt(1, 3) <= 2 THEN 'Bank Transfer'
        ELSE 'Credit Card'
    END AS PaymentMethod,
    CASE 
        WHEN RandomInt(1, 3) <= 2 THEN 'TRX-' || printf('%06d', RandomInt(100000, 999999))
        ELSE 'CC-AUTH-' || printf('%06d', RandomInt(100000, 999999))
    END AS PaymentReference,
    CASE 
        WHEN datetime('now') > datetime(date_value, '+1 day') THEN datetime(date_value, '+1 day')
        ELSE NULL
    END AS PaymentDate,
    datetime(date_value) AS CreatedAt,
    datetime(date_value, '+1 day') AS UpdatedAt
FROM WeeklyPOs
WHERE PO_Number <= 3; -- Limit to 3 POs per week

-- Add items to purchase orders
INSERT INTO PurchaseOrderItems (PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, 
                               BatchNumber, Location, Notes, ExpiryDate)
SELECT 
    po.Id AS PurchaseOrderId,
    p.Id AS ProductId,
    -- Order quantity based on product type and stock levels
    CASE 
        WHEN p.Id BETWEEN 1 AND 4 THEN RandomInt(5, 10)  -- Electronics
        WHEN p.Id BETWEEN 5 AND 8 THEN RandomInt(20, 50) -- Groceries
        ELSE RandomInt(10, 30)                          -- Clothing
    END AS Quantity,
    p.PurchasePrice AS UnitCost,
    p.SellingPrice AS SellingPrice,
    'BATCH-' || strftime('%y%m', po.OrderDate) || '-' || p.Id AS BatchNumber,
    CASE 
        WHEN p.Id BETWEEN 1 AND 4 THEN 'Electronics Section'
        WHEN p.Id BETWEEN 5 AND 8 THEN 'Grocery Department'
        ELSE 'Apparel Area'
    END AS Location,
    'Regular stock replenishment' AS Notes,
    -- Only set expiry for groceries
    CASE 
        WHEN p.Id BETWEEN 5 AND 8 THEN datetime(po.OrderDate, '+' || (30 + RandomInt(0, 60)) || ' days')
        ELSE NULL
    END AS ExpiryDate
FROM PurchaseOrders po
JOIN Products p ON (
    (po.SupplierId = 1 AND p.Id BETWEEN 1 AND 4) OR  -- Electronics supplier
    (po.SupplierId = 2 AND p.Id BETWEEN 5 AND 8) OR  -- Grocery supplier
    (po.SupplierId = 3 AND p.Id BETWEEN 9 AND 12)    -- Clothing supplier
)
-- Randomize which products get ordered (not all products every time)
WHERE RandomInt(1, 4) <= 3 -- 75% chance to include a product
ORDER BY po.OrderDate, p.Id;

-- Update purchase order totals
UPDATE PurchaseOrders
SET 
    Subtotal = (
        SELECT SUM(Quantity * UnitCost) 
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    ),
    TaxAmount = (
        SELECT SUM(Quantity * UnitCost) * 0.1 -- Assume 10% tax
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    ),
    GrandTotal = (
        SELECT SUM(Quantity * UnitCost) * 1.1 -- Subtotal + 10% tax
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    );

-- Generate inventory transactions for received purchase orders
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                  TotalPrice, ReferenceNumber, Notes, TransactionDate, UserId)
SELECT 
    poi.ProductId,
    'Purchase' AS TransactionType,
    poi.Quantity,
    poi.UnitCost AS UnitPrice,
    poi.Quantity * poi.UnitCost AS TotalPrice,
    po.OrderNumber AS ReferenceNumber,
    'Purchase order received' AS Notes,
    CASE 
        WHEN po.Status = 'Received' THEN datetime(po.DueDate)
        ELSE NULL
    END AS TransactionDate,
    po.CreatedByUserId AS UserId
FROM PurchaseOrderItems poi
JOIN PurchaseOrders po ON poi.PurchaseOrderId = po.Id
WHERE po.Status = 'Received';

-- Update product stock with received purchase orders
UPDATE Products
SET StockQuantity = StockQuantity + (
    SELECT COALESCE(SUM(it.Quantity), 0)
    FROM InventoryTransactions it
    WHERE it.ProductId = Products.Id AND it.TransactionType = 'Purchase'
);

-- Generate inventory transactions from sales (already triggered by our SaleItems)
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                  TotalPrice, ReferenceNumber, Notes, TransactionDate, UserId)
SELECT 
    si.ProductId,
    'Sale' AS TransactionType,
    -si.Quantity, -- Negative for sales
    si.UnitPrice,
    -si.Total AS TotalPrice, -- Negative for sales
    s.InvoiceNumber AS ReferenceNumber,
    'Sale transaction' AS Notes,
    s.SaleDate AS TransactionDate,
    s.UserId
FROM SaleItems si
JOIN Sales s ON si.SaleId = s.Id;

-- Update product stock based on sales
UPDATE Products
SET StockQuantity = StockQuantity + (
    SELECT COALESCE(SUM(it.Quantity), 0)
    FROM InventoryTransactions it
    WHERE it.ProductId = Products.Id AND it.TransactionType = 'Sale'
);

-- Generate cash transactions for each cash drawer
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
-- Opening balance entries
SELECT 
    Id AS CashDrawerId,
    'Opening Balance' AS Type,
    OpeningBalance AS Amount,
    OpenedAt AS Timestamp,
    'OPEN-' || Id AS Reference,
    'Daily opening' AS Reason,
    'Cash drawer opened for the day' AS Notes,
    OpenedById AS PerformedById
FROM CashDrawers;

-- Cash payments
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Sale Payment' AS Type,
    s.AmountPaid AS Amount,
    s.SaleDate AS Timestamp,
    s.InvoiceNumber AS Reference,
    'Cash sale' AS Reason,
    'Cash payment received' AS Notes,
    s.UserId AS PerformedById
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash';

-- Cash refunds (about
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Refund' AS Type,
    -s.GrandTotal AS Amount, -- Negative for refunds
    datetime(s.SaleDate, '+' || RandomInt(1, 3) || ' hours') AS Timestamp,
    'REF-' || s.InvoiceNumber AS Reference,
    CASE RandomInt(1, 3)
        WHEN 1 THEN 'Customer changed mind'
        WHEN 2 THEN 'Defective product'
        ELSE 'Wrong item purchased'
    END AS Reason,
    'Cash refund issued' AS Notes,
    2 AS PerformedById -- Manager handles refunds
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash'
AND RandomInt(1, 50) = 1; -- Only about 2% of cash sales get refunded

-- No sale cash drawer opens (a few per day)
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'No Sale' AS Type,
    0 AS Amount,
    datetime(cd.OpenedAt, '+' || (RandomInt(1, 9) * 3600 / 5) || ' seconds') AS Timestamp,
    'NOSALE-' || cd.Id || '-' || ROW_NUMBER() OVER (PARTITION BY cd.Id ORDER BY RANDOM()) AS Reference,
    CASE RandomInt(1, 3)
        WHEN 1 THEN 'Change needed'
        WHEN 2 THEN 'Mistake correction'
        ELSE 'Register check'
    END AS Reason,
    'No sale drawer open' AS Notes,
    CASE WHEN RandomInt(1, 10) <= 8 THEN 3 ELSE 2 END AS PerformedById -- Mostly cashier, sometimes manager
FROM CashDrawers cd
CROSS JOIN (SELECT 1 AS dummy FROM RandomHelper LIMIT RandomInt(1, 5)) -- 1-5 no sales per day
ORDER BY cd.Id, RANDOM();

-- Cash drops during the day (for busy days)
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Cash Drop' AS Type,
    -500.00 AS Amount, -- Negative for removing cash
    datetime(cd.OpenedAt, '+' || (RandomInt(4, 8) * 3600 / 5) || ' seconds') AS Timestamp,
    'DROP-' || cd.Id AS Reference,
    'Excess cash storage' AS Reason,
    'Excess cash moved to safe' AS Notes,
    2 AS PerformedById -- Manager handles cash drops
FROM CashDrawers cd
WHERE cd.ExpectedBalance > 1000.00; -- Only for drawers that collected a lot of cash

-- Closing balance entries
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    Id AS CashDrawerId,
    'Closing Balance' AS Type,
    -ActualBalance AS Amount, -- Negative as we're removing all cash
    ClosedAt AS Timestamp,
    'CLOSE-' || Id AS Reference,
    'Daily closing' AS Reason,
    CASE
        WHEN ABS(ExpectedBalance - ActualBalance) < 0.01 THEN 'Balanced perfectly'
        WHEN ActualBalance > ExpectedBalance THEN 'Over by $' || ROUND(ActualBalance - ExpectedBalance, 2)
        ELSE 'Short by $' || ROUND(ExpectedBalance - ActualBalance, 2)
    END AS Notes,
    ClosedById AS PerformedById
FROM CashDrawers;

-- Generate loyalty transactions for customers
-- Points earned from sales
INSERT INTO LoyaltyTransactions (CustomerId, TransactionType, Points, PointsBalance, SaleId, Description, CreatedAt)
SELECT 
    s.CustomerId,
    'Earn' AS TransactionType,
    -- Points calculation (simplified for this script)
    CAST(s.GrandTotal * (
        SELECT lp.PointsPerDollar * lt.PointsMultiplier
        FROM Customers c
        JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
        JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
        WHERE c.Id = s.CustomerId
    ) AS INTEGER) AS Points,
    0 AS PointsBalance, -- Will update later
    s.Id AS SaleId,
    'Points earned from purchase ' || s.InvoiceNumber AS Description,
    s.SaleDate AS CreatedAt
FROM Sales s
WHERE s.CustomerId IS NOT NULL;

-- Add some point redemptions (about 10% of customers redeem points)
INSERT INTO LoyaltyTransactions (CustomerId, TransactionType, Points, PointsBalance, SaleId, Description, CreatedAt)
SELECT 
    c.Id AS CustomerId,
    'Redeem' AS TransactionType,
    -- Redeem a portion of available points (100-500 points)
    -MIN(c.LoyaltyPoints, RandomInt(100, 500)) AS Points,
    0 AS PointsBalance, -- Will update later
    NULL AS SaleId, -- No associated sale for redemptions (could be for a discount)
    'Points redeemed for $' || (ABS(MIN(c.LoyaltyPoints, RandomInt(100, 500))) * 0.01) || ' discount' AS Description,
    datetime('now', '-' || RandomInt(1, 30) || ' days', '+' || RandomInt(1, 24) || ' hours') AS CreatedAt
FROM Customers c
WHERE c.LoyaltyPoints >= 100 -- Only if they have enough points
AND RandomInt(1, 10) = 1; -- 10% chance

-- Update loyalty points balance for all transactions (running total)
WITH UpdatedBalances AS (
    SELECT 
        lt.Id,
        lt.CustomerId,
        lt.Points,
        SUM(lt2.Points) AS NewBalance
    FROM LoyaltyTransactions lt
    JOIN LoyaltyTransactions lt2 ON lt.CustomerId = lt2.CustomerId
        AND lt2.CreatedAt <= lt.CreatedAt
    GROUP BY lt.Id
)
UPDATE LoyaltyTransactions
SET PointsBalance = (
    SELECT NewBalance 
    FROM UpdatedBalances 
    WHERE UpdatedBalances.Id = LoyaltyTransactions.Id
);

-- Update customer loyalty points based on transactions
UPDATE Customers
SET LoyaltyPoints = (
    SELECT COALESCE(SUM(Points), 0)
    FROM LoyaltyTransactions
    WHERE CustomerId = Customers.Id
);

-- Update loyalty tier based on points
UPDATE Customers
SET LoyaltyTierId = (
    SELECT lt.Id
    FROM LoyaltyTiers lt
    WHERE lt.LoyaltyProgramId = 1 -- We only have one loyalty program for now
    AND lt.MinimumPoints <= Customers.LoyaltyPoints
    ORDER BY lt.MinimumPoints DESC
    LIMIT 1
);

-- Add some user favorites
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
SELECT
    u.Id AS UserId,
    p.Id AS ProductId,
    datetime('now', '-' || RandomInt(1, 30) || ' days') AS CreatedAt
FROM Users u
CROSS JOIN Products p
WHERE RandomInt(1, 5) = 1 -- 20% chance for each user-product combination
AND NOT EXISTS (
    SELECT 1 FROM UserFavorites uf
    WHERE uf.UserId = u.Id AND uf.ProductId = p.Id
);

-- Clean up temporary objects
DROP TABLE IF EXISTS RandomHelper;
DROP TABLE IF EXISTS DatesInMonth;
DROP TABLE IF EXISTS SaleGenerator;
DROP TABLE IF EXISTS ProductWeights;

COMMIT; 