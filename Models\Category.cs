﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POSSystem.Models
{
    public class Category
    {
        public Category()
        {
            Name = string.Empty;
            Description = string.Empty;
            Subcategories = new List<Category>();
            Products = new List<Product>();
            IsActive = true;
        }

        public int Id { get; set; }
        
        [Required]
        public string Name { get; set; }
        
        public string Description { get; set; }
        
        public int? ParentCategoryId { get; set; }
        public bool IsActive { get; set; }
        public virtual Category? ParentCategory { get; set; }
        public virtual ICollection<Category> Subcategories { get; set; }
        public virtual ICollection<Product> Products { get; set; }
    }
}
