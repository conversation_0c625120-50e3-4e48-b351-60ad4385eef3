using System;

namespace POSSystem.Models.Dashboard
{
    /// <summary>
    /// Lightweight sales data model for dashboard charts
    /// Contains only essential fields to minimize memory usage and loading time
    /// </summary>
    public class DashboardSaleData
    {
        public DateTime SaleDate { get; set; }
        public decimal GrandTotal { get; set; }
        public int? CustomerId { get; set; }
    }

    /// <summary>
    /// Aggregated dashboard metrics model
    /// Pre-calculated metrics to avoid expensive calculations on the UI thread
    /// </summary>
    public class DashboardMetrics
    {
        public decimal TotalSales { get; set; }
        public int SalesCount { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        
        // Calculated properties
        public decimal DailyAverage => SalesCount > 0 ? TotalSales / Math.Max(1, (EndDate - StartDate).Days + 1) : 0;
        public string FormattedTotalSales => TotalSales.ToString("C");
        public string FormattedAverageOrderValue => AverageOrderValue.ToString("C");
    }

    /// <summary>
    /// Lightweight chart data point for trend charts
    /// </summary>
    public class ChartDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public string Label { get; set; } = string.Empty;
        public string FormattedValue => Value.ToString("C");
    }

    /// <summary>
    /// Aggregated data for dashboard charts
    /// Pre-processed to minimize UI thread work
    /// </summary>
    public class DashboardChartData
    {
        public List<ChartDataPoint> DataPoints { get; set; } = new List<ChartDataPoint>();
        public string[] Labels { get; set; } = Array.Empty<string>();
        public decimal[] Values { get; set; } = Array.Empty<decimal>();
        public string ChartTitle { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public bool HasData => DataPoints.Any();
    }
}
