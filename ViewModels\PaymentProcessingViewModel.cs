using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using POSSystem.Commands;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Printing;
using POSSystem.Services.Interfaces;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.ViewModels
{
    public class PaymentProcessingViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly SaleViewModel _parentViewModel;
        private readonly string _dialogIdentifier;
        private readonly DatabaseService _dbService;
        private IEnhancedReceiptPrintService _receiptPrintService; // ✅ PERFORMANCE FIX: Removed readonly for lazy initialization

        // ✅ PERFORMANCE FIX: Debouncing mechanism to reduce UI update frequency
        private readonly System.Timers.Timer _updateTimer;
        private bool _hasPendingUpdates = false;
        
        public SaleViewModel ParentViewModel => _parentViewModel;
        
        #region Properties
        
        // Payment values
        private decimal _amountDue;
        public decimal AmountDue
        {
            get => _amountDue;
            set
            {
                if (_amountDue != value)
                {
                    _amountDue = value;
                    OnPropertyChanged();
                    ValidatePayment();
                    CalculateChange();
                }
            }
        }
        
        private decimal _subtotal;
        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    UpdateAmountDue();
                }
            }
        }
        
        private decimal _discount;
        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    UpdateAmountDue();
                }
            }
        }
        
        private string _amountTendered;
        private bool _isUpdatingAmountTendered = false; // ✅ PERFORMANCE FIX: Prevent cascading updates

        public string AmountTendered
        {
            get => _amountTendered;
            set
            {
                if (_amountTendered != value && !_isUpdatingAmountTendered)
                {
                    _isUpdatingAmountTendered = true;
                    _amountTendered = value;
                    OnPropertyChanged();

                    // ✅ PERFORMANCE FIX: Batch calculations to reduce UI updates
                    BatchUpdateCalculations();
                    _isUpdatingAmountTendered = false;
                }
            }
        }
        
        private decimal _change;
        public decimal Change
        {
            get => _change;
            set
            {
                if (_change != value)
                {
                    _change = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ChangeTextColor));
                    OnPropertyChanged(nameof(IsChangeVisible));
                }
            }
        }
        
        public Brush ChangeTextColor => Change >= 0 ? Brushes.Green : Brushes.Red;
        
        private bool _isChangeVisible;
        public bool IsChangeVisible
        {
            get => _isChangeVisible;
            set
            {
                if (_isChangeVisible != value)
                {
                    _isChangeVisible = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ShouldShowChange));
                }
            }
        }
        
        public bool ShouldShowChange => IsChangeVisible && !IsUnpaidSelected;
        
        // Payment method selection
        private bool _isCashSelected = true;
        public bool IsCashSelected
        {
            get => _isCashSelected;
            set
            {
                if (_isCashSelected == value) return;
                _isCashSelected = value;
                if (value)
                {
                    SelectedPaymentMethod = "Cash";
                    _isCardSelected = false;
                    _isMobileSelected = false;
                    _isUnpaidSelected = false;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowAmountTendered));
                OnPropertyChanged(nameof(ShouldShowChange));
                
                // Validate and update UI when switching to cash
                if (value)
                {
                    ValidatePayment();
                    CalculateChange();
                    
                    // Force UI refresh
                    OnPropertyChanged(nameof(IsUnpaidSelected));
                }
            }
        }
        
        private bool _isCardSelected;
        public bool IsCardSelected
        {
            get => _isCardSelected;
            set
            {
                if (_isCardSelected == value) return;
                _isCardSelected = value;
                if (value)
                {
                    SelectedPaymentMethod = "Card";
                    _isCashSelected = false;
                    _isMobileSelected = false;
                    _isUnpaidSelected = false;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowAmountTendered));
                OnPropertyChanged(nameof(ShouldShowChange));
                
                // Validate and update UI when switching to card
                if (value)
                {
                    ValidatePayment();
                    CalculateChange();
                }
            }
        }
        
        private bool _isMobileSelected;
        public bool IsMobileSelected
        {
            get => _isMobileSelected;
            set
            {
                if (_isMobileSelected == value) return;
                _isMobileSelected = value;
                if (value)
                {
                    SelectedPaymentMethod = "Mobile";
                    _isCashSelected = false;
                    _isCardSelected = false;
                    _isUnpaidSelected = false;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowAmountTendered));
                OnPropertyChanged(nameof(ShouldShowChange));
                
                // Validate and update UI when switching to mobile
                if (value)
                {
                    ValidatePayment();
                    CalculateChange();
                }
            }
        }
        
        private bool _isUnpaidSelected;
        private bool _isUpdatingPaymentMethod = false; // ✅ PERFORMANCE FIX: Prevent cascading updates

        public bool IsUnpaidSelected
        {
            get => _isUnpaidSelected;
            set
            {
                if (_isUnpaidSelected == value || _isUpdatingPaymentMethod) return;

                _isUpdatingPaymentMethod = true;
                _isUnpaidSelected = value;

                if (value)
                {
                    SelectedPaymentMethod = "Unpaid";
                    _isCashSelected = false;
                    _isCardSelected = false;
                    _isMobileSelected = false;
                }

                // ✅ PERFORMANCE FIX: Batch all property notifications
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowAmountTendered));
                OnPropertyChanged(nameof(ShouldShowChange));
                OnPropertyChanged(nameof(IsCashSelected));

                // Batch calculations
                if (!value)
                {
                    BatchUpdateCalculations();
                }
                else
                {
                    ValidatePayment();
                }

                _isUpdatingPaymentMethod = false;
            }
        }
        
        public bool ShowAmountTendered => !IsUnpaidSelected;
        
        private string _selectedPaymentMethod = "Cash";
        public string SelectedPaymentMethod
        {
            get => _selectedPaymentMethod;
            set
            {
                if (_selectedPaymentMethod != value)
                {
                    _selectedPaymentMethod = value;
                    OnPropertyChanged();
                }
            }
        }
        
        // Due date for unpaid payment
        private DateTime? _dueDate = DateTime.Now.AddDays(30);
        public DateTime? DueDate
        {
            get => _dueDate;
            set
            {
                if (_dueDate != value)
                {
                    _dueDate = value;
                    OnPropertyChanged();
                    ValidatePayment();
                }
            }
        }

        // Minimum due date (today)
        public DateTime MinDueDate => DateTime.Today;
        
        // Customer information
        private string _customerName;
        public string CustomerName
        {
            get => _customerName ?? (_parentViewModel?.SelectedCustomer?.FullName ?? Application.Current.Resources["GuestCustomer"]?.ToString());
            set
            {
                if (_customerName == value) return;
                _customerName = value;
                OnPropertyChanged();
            }
        }
        
        private bool _hasLoyaltyCustomer;
        public bool HasLoyaltyCustomer
        {
            get => _hasLoyaltyCustomer;
            set
            {
                if (_hasLoyaltyCustomer != value)
                {
                    _hasLoyaltyCustomer = value;
                    OnPropertyChanged();
                }
            }
        }
        
        private decimal _loyaltyPointsEarned;
        public decimal LoyaltyPointsEarned
        {
            get => _loyaltyPointsEarned;
            set
            {
                if (_loyaltyPointsEarned != value)
                {
                    _loyaltyPointsEarned = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(LoyaltyPointsText));
                }
            }
        }

        // Formatted loyalty points text for display
        public string LoyaltyPointsText
        {
            get
            {
                if (_loyaltyPointsEarned <= 0) return "";

                // Get the localized "Points" text
                var pointsText = Application.Current?.FindResource("Points")?.ToString() ?? "Points";
                return $"{_loyaltyPointsEarned:N0} {pointsText}";
            }
        }
        
        // Validation and state tracking
        private bool _canProcessPayment;
        public bool CanProcessPayment
        {
            get => _canProcessPayment;
            set
            {
                if (_canProcessPayment != value)
                {
                    _canProcessPayment = value;
                    OnPropertyChanged();
                }
            }
        }
        
        private bool _paymentProcessed;
        public bool PaymentProcessed
        {
            get => _paymentProcessed;
            set
            {
                if (_paymentProcessed != value)
                {
                    _paymentProcessed = value;
                    OnPropertyChanged();
                }
            }
        }

        // Receipt printing properties
        private bool _printReceiptEnabled = true;
        public bool PrintReceiptEnabled
        {
            get => _printReceiptEnabled;
            set
            {
                if (_printReceiptEnabled != value)
                {
                    _printReceiptEnabled = value;
                    OnPropertyChanged();

                    // Save the setting to database when changed
                    _ = Task.Run(async () => await SavePrintReceiptSettingAsync(value));
                }
            }
        }

        private bool _showPrintDialog = false;
        public bool ShowPrintDialog
        {
            get => _showPrintDialog;
            set
            {
                if (_showPrintDialog != value)
                {
                    _showPrintDialog = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _saveAsPdf = false;
        public bool SaveAsPdf
        {
            get => _saveAsPdf;
            set
            {
                if (_saveAsPdf != value)
                {
                    _saveAsPdf = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands
        
        public ICommand ProcessPaymentCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand SetPredefinedAmountCommand { get; }
        public ICommand LookupCustomerCommand { get; }
        
        #endregion
        
        #region Constructor
        
        public PaymentProcessingViewModel(SaleViewModel parentViewModel, string dialogIdentifier = "SalesDialog")
        {
            _parentViewModel = parentViewModel;
            _dialogIdentifier = dialogIdentifier;

            // ✅ PERFORMANCE FIX: Initialize debouncing timer to reduce UI update frequency
            _updateTimer = new System.Timers.Timer(100); // 100ms debounce
            _updateTimer.Elapsed += OnUpdateTimerElapsed;
            _updateTimer.AutoReset = false;

            // ✅ PERFORMANCE FIX: Lazy initialize heavy services to prevent UI blocking
            _dbService = new DatabaseService();

            // Initialize values from parent view model first (lightweight operations)
            if (parentViewModel != null)
            {
                _subtotal = parentViewModel.Subtotal;
                // Get the calculated discount amount from the cart
                _discount = parentViewModel.ActiveCart?.DiscountAmount ?? 0;
                _amountDue = _subtotal - _discount;
                _amountTendered = _amountDue.ToString("N2");

                // Subscribe to parent view model's property changes
                parentViewModel.PropertyChanged += ParentViewModel_PropertyChanged;
                
                // Customer information
                if (parentViewModel.SelectedCustomer != null)
                {
                    _customerName = parentViewModel.SelectedCustomer.FullName;
                    _hasLoyaltyCustomer = parentViewModel.HasLoyaltyCustomer;
                    
                    // Calculate loyalty points
                    var loyaltyProgram = parentViewModel.GetActiveLoyaltyProgram();
                    if (loyaltyProgram != null)
                    {
                        LoyaltyPointsEarned = CalculateLoyaltyPoints(loyaltyProgram, _amountDue);
                    }
                }
            }
            
            // Initialize commands
            ProcessPaymentCommand = new RelayCommand(_ => ProcessPayment(), _ => CanProcessPayment);
            CancelCommand = new RelayCommand(_ => CancelPayment());
            SetPredefinedAmountCommand = new RelayCommand<string>(SetPredefinedAmount);
            LookupCustomerCommand = new RelayCommand(_ => LookupCustomer());

            // ✅ PERFORMANCE FIX: Defer heavy initialization to prevent UI blocking
            Task.Run(() =>
            {
                try
                {
                    // Initialize receipt print service in background
                    _receiptPrintService = new EnhancedReceiptPrintService(_dbService);

                    // Load print receipt setting from database
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        LoadPrintReceiptSetting();
                    }), System.Windows.Threading.DispatcherPriority.Background);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PAYMENT_VM] Error in background initialization: {ex.Message}");
                }
            });

            // Initialize UI state (lightweight operations only)
            UpdateAllProperties();
        }
        
        private void ParentViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SaleViewModel.Subtotal))
            {
                Subtotal = _parentViewModel.Subtotal;
            }
            else if (e.PropertyName == nameof(SaleViewModel.DiscountAmount))
            {
                // Get the calculated discount amount from the cart
                Discount = _parentViewModel.ActiveCart?.DiscountAmount ?? 0;
            }
        }
        
        #endregion
        
        #region Methods
        
        private void CalculateChange()
        {
            decimal newChange = 0;
            bool newIsChangeVisible = false;

            if (IsUnpaidSelected)
            {
                // For unpaid payment, we don't show change
                newChange = 0;
                newIsChangeVisible = false;
            }
            else if (decimal.TryParse(AmountTendered, out decimal tendered))
            {
                newChange = tendered - AmountDue;
                newIsChangeVisible = true; // Always show change for cash payments
            }
            else
            {
                newChange = 0;
                newIsChangeVisible = false;
            }

            // ✅ PERFORMANCE FIX: Only update properties if values actually changed
            if (_change != newChange)
            {
                _change = newChange;
                OnPropertyChanged(nameof(Change));
                OnPropertyChanged(nameof(ChangeTextColor));
            }

            if (_isChangeVisible != newIsChangeVisible)
            {
                _isChangeVisible = newIsChangeVisible;
                OnPropertyChanged(nameof(IsChangeVisible));
                OnPropertyChanged(nameof(ShouldShowChange));
            }
        }
        
        private void ValidatePayment()
        {
            if (IsUnpaidSelected)
            {
                // For unpaid payment, check if a customer is selected
                bool hasSelectedCustomer = _parentViewModel?.SelectedCustomer != null && 
                                         _parentViewModel.SelectedCustomer.Id != null &&
                                         _parentViewModel.SelectedCustomer.Id != -1; // -1 is often used for guest customer
                
                // Only allow unpaid sales if a customer is selected and due date is valid
                CanProcessPayment = hasSelectedCustomer && 
                                    DueDate.HasValue && 
                                    DueDate.Value > DateTime.Now;
            }
            else
            {
                // For other payment methods, need valid amount tendered
                if (decimal.TryParse(AmountTendered, out decimal tendered))
                {
                    CanProcessPayment = tendered >= AmountDue;
                }
                else
                {
                    // If no amount entered yet but we just switched to a paid method,
                    // don't enable the button until a valid amount is entered
                    CanProcessPayment = false;
                }
            }
            
            // Force property notification to update UI
            OnPropertyChanged(nameof(CanProcessPayment));
        }
        
        private decimal CalculateLoyaltyPoints(LoyaltyProgram program, decimal amount)
        {
            // Simple calculation based on program settings
            return Math.Floor(amount * program.PointsPerDollar);
        }
        
        private async void ProcessPayment()
        {
            try
            {
                if (IsUnpaidSelected)
                {
                    // Check for customer selection for unpaid transactions
                    bool hasSelectedCustomer = _parentViewModel?.SelectedCustomer != null && 
                                             _parentViewModel.SelectedCustomer.Id != null &&
                                             _parentViewModel.SelectedCustomer.Id != -1;
                                             
                    if (!hasSelectedCustomer)
                    {
                        MessageBox.Show(
                            Application.Current.FindResource("CustomerRequiredForUnpaid") as string ?? 
                            "You must select a customer for unpaid sales. Please select a customer before continuing.",
                            Application.Current.FindResource("CustomerRequired") as string ?? "Customer Required",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }
                
                    if (!DueDate.HasValue)
                    {
                        MessageBox.Show(
                            Application.Current.FindResource("DueDateRequired") as string ?? "Due date is required for unpaid sales.",
                            Application.Current.FindResource("InvalidInput") as string ?? "Invalid Input",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    if (await _parentViewModel.ProcessPayment(SelectedPaymentMethod, 0, DueDate))
                    {
                        PaymentProcessed = true;

                        // Handle receipt printing for unpaid sales if enabled
                        if (PrintReceiptEnabled)
                        {
                            await HandleReceiptPrintingAsync();
                        }

                        DialogHost.Close(_dialogIdentifier, true);
                    }
                }
                else if (decimal.TryParse(AmountTendered, out decimal amountTendered))
                {
                    if (amountTendered >= AmountDue)
                    {
                        if (await _parentViewModel.ProcessPayment(SelectedPaymentMethod, amountTendered))
                        {
                            PaymentProcessed = true;

                            // Handle receipt printing if enabled
                            if (PrintReceiptEnabled)
                            {
                                await HandleReceiptPrintingAsync();
                            }

                            DialogHost.Close(_dialogIdentifier, true);
                        }
                    }
                    else
                    {
                        MessageBox.Show(
                            Application.Current.FindResource("AmountTenderedMustBeGreater") as string ?? "Amount tendered must be greater than or equal to the total amount.",
                            Application.Current.FindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show(
                        Application.Current.FindResource("InvalidAmountFormat") as string ?? "Invalid amount format.",
                        Application.Current.FindResource("Error") as string ?? "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{Application.Current.FindResource("PaymentError") as string ?? "Error processing payment"}: {ex.Message}",
                    Application.Current.FindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
        
        private void CancelPayment()
        {
            DialogHost.Close(_dialogIdentifier, false);
        }
        
        private void SetPredefinedAmount(string amount)
        {
            if (decimal.TryParse(amount, out decimal value))
            {
                AmountTendered = value.ToString("N2");
            }
        }
        
        private async Task HandleReceiptPrintingAsync()
        {
            try
            {
                // Get the most recent sale (the one just processed)
                var recentSale = await GetMostRecentSaleAsync();
                if (recentSale == null)
                {
                    System.Diagnostics.Debug.WriteLine("[RECEIPT PRINT] Could not find recent sale for receipt printing");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT] Printing receipt for sale {recentSale.Id}, Invoice: {recentSale.InvoiceNumber}");

                // Print receipt with options
                bool printSuccess = await _receiptPrintService.PrintReceiptAsync(recentSale, ShowPrintDialog);

                if (printSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("[RECEIPT PRINT] Receipt printed successfully");

                    // Save as PDF if requested
                    if (SaveAsPdf)
                    {
                        var pdfPath = System.IO.Path.Combine(
                            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                            "POS Receipts",
                            $"Receipt_{recentSale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                        );

                        await _receiptPrintService.SaveReceiptAsPdfAsync(recentSale, pdfPath);
                        System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT] Receipt saved as PDF: {pdfPath}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[RECEIPT PRINT] Receipt printing failed");

                    // Show user-friendly message for print failure
                    MessageBox.Show(
                        "Receipt printing failed. The sale was completed successfully, but the receipt could not be printed.\n\nYou can reprint the receipt later from the sales history.",
                        "Print Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT] Error in HandleReceiptPrintingAsync: {ex.Message}");

                // Don't show error to user for receipt printing issues - sale was successful
                MessageBox.Show(
                    "Receipt printing encountered an issue, but your sale was completed successfully.\n\nYou can reprint the receipt later from the sales history.",
                    "Print Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        private async Task<Sale> GetMostRecentSaleAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await Task.FromResult(
                    context.Sales
                        .Include(s => s.Items)
                        .ThenInclude(i => i.Product)
                        .Include(s => s.Customer)
                        .OrderByDescending(s => s.SaleDate)
                        .FirstOrDefault()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT] Error getting recent sale: {ex.Message}");
                return null;
            }
        }

        private void LookupCustomer()
        {
            try
            {
                if (_parentViewModel == null) return;
                
                // Create a customer selection window
                // We need to use the Application.Current.Dispatcher to access UI from the ViewModel
                Application.Current.Dispatcher.Invoke(() => 
                {
                    // Create the window
                    var customerWindow = new POSSystem.Views.CustomerSelectionWindow();
                    
                    // Only set the owner if the MainWindow has been shown
                    if (Application.Current.MainWindow != null && 
                        Application.Current.MainWindow.IsLoaded && 
                        Application.Current.MainWindow.IsVisible)
                    {
                        customerWindow.Owner = Application.Current.MainWindow;
                    }
                    
                    // Show dialog and handle result
                    bool? result = customerWindow.ShowDialog();
                    if (result == true && customerWindow.SelectedCustomer != null)
                    {
                        // Set customer in parent ViewModel
                        _parentViewModel.SelectedCustomer = customerWindow.SelectedCustomer;
                        
                        // Update the CustomerName in this ViewModel
                        CustomerName = customerWindow.SelectedCustomer.FullName;
                        
                        // Update loyalty information in the ViewModel
                        if (_parentViewModel.HasLoyaltyCustomer)
                        {
                            HasLoyaltyCustomer = true;
                            
                            // Calculate loyalty points using the parent ViewModel's logic
                            LoyaltyPointsEarned = _parentViewModel.LoyaltyPointsEarned;
                        }
                        
                        // Revalidate payment options
                        ValidatePayment();
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error selecting customer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateAllProperties()
        {
            // Update all visibility-related properties to ensure consistent state
            OnPropertyChanged(nameof(IsCashSelected));
            OnPropertyChanged(nameof(IsUnpaidSelected));
            OnPropertyChanged(nameof(ShowAmountTendered));
            OnPropertyChanged(nameof(ShouldShowChange));
            OnPropertyChanged(nameof(IsChangeVisible));
            
            // Calculate and validate
            CalculateChange();
            ValidatePayment();
        }
        
        private void UpdateAmountDue()
        {
            // Calculate amount due based on subtotal and discount
            AmountDue = Subtotal - Discount;
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Batch calculations to reduce UI update frequency
        /// </summary>
        private void BatchUpdateCalculations()
        {
            // Perform all calculations in one batch to minimize UI updates
            CalculateChange();
            ValidatePayment();
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Debounced update handler to reduce UI update frequency
        /// </summary>
        private void OnUpdateTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            if (_hasPendingUpdates)
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    BatchUpdateCalculations();
                    _hasPendingUpdates = false;
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Schedule debounced update instead of immediate calculation
        /// </summary>
        private void ScheduleDebouncedUpdate()
        {
            _hasPendingUpdates = true;
            _updateTimer.Stop();
            _updateTimer.Start();
        }

        /// <summary>
        /// Load the print receipt setting from database
        /// </summary>
        private void LoadPrintReceiptSetting()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Handle lazy initialization of receipt print service
                if (_receiptPrintService == null)
                {
                    _printReceiptEnabled = true; // Default value
                    return;
                }

                var printSettings = _receiptPrintService.GetPrintSettings();
                if (printSettings != null)
                {
                    _printReceiptEnabled = printSettings.AutoPrintEnabled;
                    OnPropertyChanged(nameof(PrintReceiptEnabled));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PAYMENT DEBUG] Error loading print receipt setting: {ex.Message}");
                // Keep default value (true) if loading fails
            }
        }

        /// <summary>
        /// Save the print receipt setting to database
        /// </summary>
        private async Task SavePrintReceiptSettingAsync(bool enabled)
        {
            try
            {
                // ✅ PERFORMANCE FIX: Handle lazy initialization of receipt print service
                if (_receiptPrintService == null)
                {
                    return; // Service not ready yet
                }

                var printSettings = _receiptPrintService.GetPrintSettings();
                if (printSettings != null)
                {
                    printSettings.AutoPrintEnabled = enabled;
                    await _receiptPrintService.UpdatePrintSettingsAsync(printSettings);
                    System.Diagnostics.Debug.WriteLine($"[PAYMENT DEBUG] Print receipt setting saved: {enabled}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PAYMENT DEBUG] Error saving print receipt setting: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_parentViewModel != null)
            {
                _parentViewModel.PropertyChanged -= ParentViewModel_PropertyChanged;
            }

            // ✅ PERFORMANCE FIX: Dispose of timer to prevent memory leaks
            _updateTimer?.Stop();
            _updateTimer?.Dispose();

            GC.SuppressFinalize(this);
        }
        
        #endregion
        
        #region INotifyPropertyChanged
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        #endregion
    }
} 