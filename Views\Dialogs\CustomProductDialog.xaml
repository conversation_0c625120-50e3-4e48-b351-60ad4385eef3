<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.CustomProductDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="535.111" d:DesignWidth="450"
             Background="Transparent">
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MaxWidth="450"
                         Margin="16">
        <Grid>
            <!-- Header Section with Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="12,12,0,0"
                    Padding="24,16">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="BasketPlus" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="{DynamicResource CustomProduct}" 
                             FontSize="22"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled">
                <StackPanel Margin="24,20">
                    <!-- Product Name with Icon -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="ShoppingOutline" 
                                                   Width="20" 
                                                   Height="20"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource ProductName}" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     FontWeight="Medium"/>
                        </StackPanel>
                        
                        <TextBox x:Name="txtProductName"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource EnterProductName}"
                               materialDesign:TextFieldAssist.HasClearButton="True"
                               materialDesign:TextFieldAssist.PrefixText="📦"
                               FontSize="16"/>
                    </StackPanel>
                    
                    <!-- Price with Icon -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="20" 
                                                   Height="20"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource Price}" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     FontWeight="Medium"/>
                        </StackPanel>
                        
                        <TextBox x:Name="txtPrice"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource EnterPrice}"
                               materialDesign:TextFieldAssist.SuffixText="DA"
                               FontSize="16"/>
                    </StackPanel>
                    
                    <!-- Quantity with Stepper Control -->
                    <StackPanel Margin="0,0,0,24">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="Counter" 
                                                   Width="20" 
                                                   Height="20"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource Quantity}" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     FontWeight="Medium"/>
                        </StackPanel>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="txtQuantity"
                                   Grid.Column="0"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="{DynamicResource EnterQuantity}"
                                   Text="1"
                                   FontSize="16"/>
                            
                            <!-- Stepper Buttons -->
                            <StackPanel Grid.Column="1" 
                                      Orientation="Horizontal" 
                                      Margin="8,0,0,0"
                                      VerticalAlignment="Center">
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      Background="{DynamicResource PrimaryHueLightBrush}"
                                      Width="36"
                                      Height="36"
                                      Padding="0"
                                      Margin="0,0,8,0"
                                      Click="DecrementQuantity_Click">
                                    <materialDesign:PackIcon Kind="Minus" 
                                                           Width="20" 
                                                           Height="20"
                                                           Foreground="{DynamicResource MaterialDesignPaper}"/>
                                </Button>
                                
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      Background="{DynamicResource PrimaryHueLightBrush}"
                                      Width="36"
                                      Height="36"
                                      Padding="0"
                                      Click="IncrementQuantity_Click">
                                    <materialDesign:PackIcon Kind="Plus" 
                                                           Width="20" 
                                                           Height="20"
                                                           Foreground="{DynamicResource MaterialDesignPaper}"/>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                    
                    <!-- Recently Added Products Message -->
                    <TextBlock x:Name="txtProductAdded" 
                             Visibility="Collapsed"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"
                             TextAlignment="Center"
                             Margin="0,0,0,16"
                             FontWeight="Medium">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody1TextBlock}">
                                <Style.Triggers>
                                    <Trigger Property="Visibility" Value="Visible">
                                        <Trigger.EnterActions>
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                                   From="1.0" To="0.0" Duration="0:0:3"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </Trigger.EnterActions>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    
                    <!-- Button Panel with Material Design Styling -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="0,20,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Cancel Button -->
                            <Button x:Name="btnCancel" 
                                  Grid.Column="0"
                                  Content="{DynamicResource Cancel}"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Height="40"
                                  Margin="0,0,8,0"
                                  HorizontalAlignment="Stretch"
                                  Click="BtnCancel_Click"/>
                            
                            <!-- Add Button -->
                            <Button x:Name="btnAdd" 
                                  Grid.Column="1"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Background="{DynamicResource PrimaryHueMidBrush}"
                                  Foreground="{DynamicResource MaterialDesignPaper}"
                                  materialDesign:ButtonAssist.CornerRadius="6"
                                  Height="40"
                                  Margin="8,0,0,0"
                                  HorizontalAlignment="Stretch"
                                  Click="BtnAdd_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CartPlus" 
                                                           Width="18" 
                                                           Height="18"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="Add to Cart"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </materialDesign:Card>
</UserControl> 