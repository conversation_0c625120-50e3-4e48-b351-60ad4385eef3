-- Drop triggers first
DROP TRIGGER IF EXISTS update_loyalty_points_after_sale;
DROP TRIGGER IF EXISTS create_sale_history;

-- Disable foreign keys
PRAGMA foreign_keys = OFF;

-- Insert a simple sale
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                  TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  ('NOTRIGGER-INV-001', datetime('now'), 1, 1, 50.00, 0.00, 0.00, 50.00, 50.00, 0.00, 'Cash', 'Paid', 'Completed', 1);

-- Re-enable foreign keys
PRAGMA foreign_keys = ON; 