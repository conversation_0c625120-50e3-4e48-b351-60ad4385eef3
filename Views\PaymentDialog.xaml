<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.PaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource ProcessPaymentTitle}"
        Width="400"
        SizeToContent="Height"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <ResourceDictionary>
            <Style x:Key="PaymentOptionButton" TargetType="RadioButton" BasedOn="{StaticResource MaterialDesignChoiceChipPrimaryOutlineRadioButton}">
                <Setter Property="Margin" Value="0,0,8,0"/>
                <Setter Property="Height" Value="32"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <md:Card UniformCornerRadius="8" Background="White" md:ElevationAssist.Elevation="Dp4" Margin="16">
        <StackPanel Margin="24">
            <!-- Header -->
            <TextBlock Text="{DynamicResource ProcessPaymentTitle}"
                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                     Margin="0,0,0,24"/>

            <!-- Invoice Details -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Text="{DynamicResource InvoiceNumber}"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         Opacity="0.6"
                         Grid.Row="0" Grid.Column="0"
                         Margin="0,0,16,8"/>
                <TextBlock Text="{Binding InvoiceNumber}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Grid.Row="0" Grid.Column="1"
                         Margin="0,0,0,8"/>

                <TextBlock Text="{DynamicResource TotalAmount}"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         Opacity="0.6"
                         Grid.Row="1" Grid.Column="0"
                         Margin="0,0,16,8"/>
                <TextBlock Text="{Binding GrandTotal, StringFormat={}{0:N2} DA}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Grid.Row="1" Grid.Column="1"
                         Margin="0,0,0,8"/>

                <TextBlock Text="{DynamicResource Customer}"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         Opacity="0.6"
                         Grid.Row="2" Grid.Column="0"
                         Margin="0,0,16,0"/>
                <TextBlock Grid.Row="2" Grid.Column="1"
                         Style="{StaticResource MaterialDesignBody1TextBlock}">
                    <Run Text="{Binding Customer.FirstName, Mode=OneWay}"/>
                    <Run Text=" "/>
                    <Run Text="{Binding Customer.LastName, Mode=OneWay}"/>
                </TextBlock>
            </Grid>

            <!-- Payment Options -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <RadioButton Content="{DynamicResource FullPayment}"
                           Style="{StaticResource PaymentOptionButton}"
                           IsChecked="True"
                           x:Name="FullPaymentOption"/>
                <RadioButton Content="{DynamicResource PartialPayment}"
                           Style="{StaticResource PaymentOptionButton}"
                           x:Name="PartialPaymentOption"/>
            </StackPanel>

            <!-- Payment Amount Input -->
            <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     md:HintAssist.Hint="{DynamicResource PaymentAmount}"
                     Text="{Binding PaymentAmount, StringFormat={}{0:N2}}"
                     x:Name="PaymentAmountInput"
                     Margin="0,0,0,24"
                     IsEnabled="{Binding IsChecked, ElementName=PartialPaymentOption}"/>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="{DynamicResource Cancel}"
                        Margin="0,0,8,0"
                        Click="CancelButton_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Content="{DynamicResource ProcessPaymentAction}"
                        Click="ProcessButton_Click"/>
            </StackPanel>
        </StackPanel>
    </md:Card>
</Window> 