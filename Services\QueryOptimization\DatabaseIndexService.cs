using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using POSSystem.Data;
using System;
using System.IO;
using System.Threading.Tasks;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// Service for managing database indexes and query optimization
    /// </summary>
    public class DatabaseIndexService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<DatabaseIndexService> _logger;
        private readonly string _connectionString;

        public DatabaseIndexService(POSDbContext context, ILogger<DatabaseIndexService> logger = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;

            // Use default SQLite connection string for now
            _connectionString = "Data Source=pos_database.db";
        }

        /// <summary>
        /// Apply all performance indexes to the database
        /// </summary>
        public async Task ApplyPerformanceIndexesAsync()
        {
            try
            {
                _logger?.LogInformation("Starting database index optimization...");

                // Read the SQL script
                var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "Migrations", "AddPerformanceIndexes.sql");
                
                if (!File.Exists(scriptPath))
                {
                    _logger?.LogWarning("Index script not found at {ScriptPath}, creating indexes programmatically", scriptPath);
                    await CreateIndexesProgrammaticallyAsync();
                    return;
                }

                var script = await File.ReadAllTextAsync(scriptPath);
                
                // Split script into individual commands
                var commands = script.Split(new[] { ";\r\n", ";\n", ";" }, StringSplitOptions.RemoveEmptyEntries);

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var indexesCreated = 0;
                var indexesSkipped = 0;

                foreach (var command in commands)
                {
                    var trimmedCommand = command.Trim();
                    if (string.IsNullOrEmpty(trimmedCommand) || 
                        trimmedCommand.StartsWith("--") || 
                        trimmedCommand.StartsWith("/*"))
                        continue;

                    try
                    {
                        using var cmd = new SqliteCommand(trimmedCommand, connection);
                        await cmd.ExecuteNonQueryAsync();

                        if (trimmedCommand.Contains("CREATE INDEX"))
                        {
                            indexesCreated++;
                            _logger?.LogDebug("Created index: {Command}", trimmedCommand.Substring(0, Math.Min(100, trimmedCommand.Length)));
                        }
                    }
                    catch (SqliteException ex) when (ex.Message.Contains("already exists"))
                    {
                        indexesSkipped++;
                        _logger?.LogDebug("Index already exists, skipping: {Command}", trimmedCommand.Substring(0, Math.Min(50, trimmedCommand.Length)));
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error executing command: {Command}", trimmedCommand.Substring(0, Math.Min(100, trimmedCommand.Length)));
                    }
                }

                _logger?.LogInformation("Database index optimization completed. Created: {Created}, Skipped: {Skipped}", indexesCreated, indexesSkipped);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error applying performance indexes");
                throw;
            }
        }

        /// <summary>
        /// Create essential indexes programmatically if script file is not available
        /// </summary>
        private async Task CreateIndexesProgrammaticallyAsync()
        {
            var essentialIndexes = new[]
            {
                // Product search indexes
                "CREATE INDEX IF NOT EXISTS IX_Products_SKU ON Products(SKU)",
                "CREATE INDEX IF NOT EXISTS IX_Products_Barcode ON Products(Barcode)",
                "CREATE INDEX IF NOT EXISTS IX_Products_Name ON Products(Name)",
                "CREATE INDEX IF NOT EXISTS IX_Products_IsActive ON Products(IsActive)",
                "CREATE INDEX IF NOT EXISTS IX_Products_CategoryId ON Products(CategoryId)",
                
                // Sales indexes
                "CREATE INDEX IF NOT EXISTS IX_Sales_SaleDate ON Sales(SaleDate)",
                "CREATE INDEX IF NOT EXISTS IX_Sales_PaymentStatus ON Sales(PaymentStatus)",
                "CREATE INDEX IF NOT EXISTS IX_Sales_CustomerId ON Sales(CustomerId)",
                
                // Sale items indexes
                "CREATE INDEX IF NOT EXISTS IX_SaleItems_ProductId ON SaleItems(ProductId)",
                "CREATE INDEX IF NOT EXISTS IX_SaleItems_SaleId ON SaleItems(SaleId)",
                
                // Customer search indexes
                "CREATE INDEX IF NOT EXISTS IX_Customers_Phone ON Customers(Phone)",
                "CREATE INDEX IF NOT EXISTS IX_Customers_Email ON Customers(Email)",
                "CREATE INDEX IF NOT EXISTS IX_Customers_Name ON Customers(Name)",
                
                // Composite indexes for common patterns
                "CREATE INDEX IF NOT EXISTS IX_Products_Active_Category ON Products(IsActive, CategoryId)",
                "CREATE INDEX IF NOT EXISTS IX_Sales_Date_Status ON Sales(SaleDate, PaymentStatus)"
            };

            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            var created = 0;
            foreach (var indexSql in essentialIndexes)
            {
                try
                {
                    using var cmd = new SqliteCommand(indexSql, connection);
                    await cmd.ExecuteNonQueryAsync();
                    created++;
                }
                catch (SqliteException ex) when (ex.Message.Contains("already exists"))
                {
                    // Index already exists, continue
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error creating index: {IndexSql}", indexSql);
                }
            }

            _logger?.LogInformation("Created {Count} essential indexes programmatically", created);
        }

        /// <summary>
        /// Analyze database statistics for better query planning
        /// </summary>
        public async Task AnalyzeDatabaseAsync()
        {
            try
            {
                _logger?.LogInformation("Analyzing database statistics...");

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var cmd = new SqliteCommand("ANALYZE", connection);
                await cmd.ExecuteNonQueryAsync();

                _logger?.LogInformation("Database analysis completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error analyzing database");
                throw;
            }
        }

        /// <summary>
        /// Get database index information
        /// </summary>
        public async Task<string> GetIndexInformationAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var cmd = new SqliteCommand("SELECT name, sql FROM sqlite_master WHERE type = 'index' AND name LIKE 'IX_%' ORDER BY name", connection);
                using var reader = await cmd.ExecuteReaderAsync();

                var result = "Database Indexes:\n";
                var count = 0;

                while (await reader.ReadAsync())
                {
                    var name = reader.GetString(0); // Use ordinal instead of column name
                    var sql = reader.IsDBNull(1) ? "System Index" : reader.GetString(1);
                    result += $"{++count}. {name}\n   {sql}\n\n";
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting index information");
                return $"Error retrieving index information: {ex.Message}";
            }
        }

        /// <summary>
        /// Check if critical indexes exist
        /// </summary>
        public async Task<bool> AreIndexesOptimizedAsync()
        {
            try
            {
                var criticalIndexes = new[]
                {
                    "IX_Products_SKU",
                    "IX_Products_Barcode", 
                    "IX_Sales_SaleDate",
                    "IX_SaleItems_ProductId",
                    "IX_Customers_Phone"
                };

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                foreach (var indexName in criticalIndexes)
                {
                    using var cmd = new SqliteCommand($"SELECT name FROM sqlite_master WHERE type = 'index' AND name = '{indexName}'", connection);
                    var result = await cmd.ExecuteScalarAsync();
                    
                    if (result == null)
                    {
                        _logger?.LogWarning("Critical index {IndexName} is missing", indexName);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking index optimization status");
                return false;
            }
        }

        /// <summary>
        /// Optimize database file (VACUUM)
        /// </summary>
        public async Task OptimizeDatabaseFileAsync()
        {
            try
            {
                _logger?.LogInformation("Starting database file optimization (VACUUM)...");

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var cmd = new SqliteCommand("VACUUM", connection);
                await cmd.ExecuteNonQueryAsync();

                _logger?.LogInformation("Database file optimization completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error optimizing database file");
                throw;
            }
        }

        /// <summary>
        /// Get database performance statistics
        /// </summary>
        public async Task<DatabaseStats> GetDatabaseStatsAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var stats = new DatabaseStats();

                // Get table counts
                var tables = new[] { "Products", "Sales", "SaleItems", "Customers", "Categories" };
                foreach (var table in tables)
                {
                    using var cmd = new SqliteCommand($"SELECT COUNT(*) FROM {table}", connection);
                    var count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                    stats.TableCounts[table] = count;
                }

                // Get index count
                using var indexCmd = new SqliteCommand("SELECT COUNT(*) FROM sqlite_master WHERE type = 'index' AND name LIKE 'IX_%'", connection);
                stats.IndexCount = Convert.ToInt32(await indexCmd.ExecuteScalarAsync());

                // Get database file size
                var dbPath = connection.DataSource;
                if (File.Exists(dbPath))
                {
                    stats.DatabaseSizeMB = new FileInfo(dbPath).Length / (1024.0 * 1024.0);
                }

                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting database statistics");
                throw;
            }
        }
    }

    /// <summary>
    /// Database performance statistics
    /// </summary>
    public class DatabaseStats
    {
        public Dictionary<string, int> TableCounts { get; set; } = new Dictionary<string, int>();
        public int IndexCount { get; set; }
        public double DatabaseSizeMB { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        public override string ToString()
        {
            var result = $"Database Statistics (Generated: {GeneratedAt:yyyy-MM-dd HH:mm:ss})\n";
            result += $"Database Size: {DatabaseSizeMB:F2} MB\n";
            result += $"Indexes: {IndexCount}\n";
            result += "Table Counts:\n";
            
            foreach (var table in TableCounts)
            {
                result += $"  {table.Key}: {table.Value:N0}\n";
            }
            
            return result;
        }
    }
}
