using System;
using System.Collections.Generic;

namespace POSSystem.Models.Analytics
{
    /// <summary>
    /// Comprehensive bulk pricing analytics report.
    /// </summary>
    public class BulkPricingReport
    {
        public DateRange ReportPeriod { get; set; }
        public DateTime GeneratedAt { get; set; }

        // Overall Metrics
        public int TotalSales { get; set; }
        public int BulkPricingSales { get; set; }
        public decimal BulkPricingAdoptionRate { get; set; }

        // Financial Metrics
        public decimal TotalCustomerSavings { get; set; }
        public decimal AverageSavingsPerSale { get; set; }
        public decimal TotalBulkPricingRevenue { get; set; }
        public decimal BulkPricingRevenuePercentage { get; set; }

        // Trend Analysis
        public decimal AdoptionRateChange { get; set; }
        public decimal SavingsGrowth { get; set; }

        // Top Performers
        public List<ProductBulkPricingPerformance> TopPerformingProducts { get; set; } = new List<ProductBulkPricingPerformance>();
        public List<CustomerSavings> TopSavingCustomers { get; set; } = new List<CustomerSavings>();

        // Summary Properties
        public string AdoptionRateDisplay => $"{BulkPricingAdoptionRate:F1}%";
        public string TotalSavingsDisplay => TotalCustomerSavings.ToString("C2");
        public string RevenuePercentageDisplay => $"{BulkPricingRevenuePercentage:F1}%";
        
        public string TrendSummary
        {
            get
            {
                var adoptionTrend = AdoptionRateChange >= 0 ? "↑" : "↓";
                var savingsTrend = SavingsGrowth >= 0 ? "↑" : "↓";
                return $"Adoption {adoptionTrend} {Math.Abs(AdoptionRateChange):F1}%, Savings {savingsTrend} {Math.Abs(SavingsGrowth):C2}";
            }
        }
    }

    /// <summary>
    /// Performance metrics for a specific product's bulk pricing.
    /// </summary>
    public class ProductBulkPricingPerformance
    {
        public Product Product { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal CustomerSavings { get; set; }
        public int TimesOrdered { get; set; }
        public decimal AverageOrderSize { get; set; }

        // Calculated Properties
        public decimal SavingsPercentage => TotalRevenue > 0 ? (CustomerSavings / (TotalRevenue + CustomerSavings)) * 100 : 0;
        public decimal RevenuePerUnit => TotalQuantitySold > 0 ? TotalRevenue / TotalQuantitySold : 0;
        public decimal SavingsPerUnit => TotalQuantitySold > 0 ? CustomerSavings / TotalQuantitySold : 0;

        // Display Properties
        public string PerformanceSummary => $"{TotalQuantitySold:F0} units, {TotalRevenue:C2} revenue, {CustomerSavings:C2} customer savings";
        public string SavingsDisplay => $"{CustomerSavings:C2} ({SavingsPercentage:F1}%)";
    }

    /// <summary>
    /// Customer savings information.
    /// </summary>
    public class CustomerSavings
    {
        public int CustomerId { get; set; }
        public Customer Customer { get; set; }
        public decimal TotalSavings { get; set; }
        public int OrdersWithBulkPricing { get; set; }
        public decimal AverageSavingsPerOrder { get; set; }

        public string SavingsDisplay => TotalSavings.ToString("C2");
        public string CustomerDisplay => Customer?.Name ?? $"Customer #{CustomerId}";
    }

    /// <summary>
    /// Effectiveness analysis for a specific pricing tier.
    /// </summary>
    public class PriceTierEffectiveness
    {
        public ProductPriceTier PriceTier { get; set; }
        public int TimesUsed { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOrderSize { get; set; }
        public decimal CustomerSavings { get; set; }

        // Calculated Properties
        public decimal RevenuePerUse => TimesUsed > 0 ? TotalRevenue / TimesUsed : 0;
        public decimal SavingsPerUse => TimesUsed > 0 ? CustomerSavings / TimesUsed : 0;
        public decimal EffectivenessScore => (TimesUsed * 0.3m) + (TotalRevenue * 0.4m) + (CustomerSavings * 0.3m);

        // Display Properties
        public string TierDisplay => PriceTier?.GetDisplayText() ?? "Unknown Tier";
        public string EffectivenessSummary => $"Used {TimesUsed} times, {TotalRevenue:C2} revenue, {CustomerSavings:C2} savings";
        public string ProductName => PriceTier?.Product?.Name ?? "Unknown Product";
    }

    /// <summary>
    /// Trend data for bulk pricing adoption over time.
    /// </summary>
    public class BulkPricingTrendData
    {
        public DateRange Period { get; set; }
        public int TotalSales { get; set; }
        public int BulkPricingSales { get; set; }
        public decimal AdoptionRate { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal AverageSavingsPerSale { get; set; }

        // Display Properties
        public string PeriodDisplay => $"{Period.StartDate:MMM dd} - {Period.EndDate:MMM dd}";
        public string AdoptionRateDisplay => $"{AdoptionRate:F1}%";
        public string SavingsDisplay => TotalSavings.ToString("C2");
        public string TrendSummary => $"{BulkPricingSales}/{TotalSales} sales ({AdoptionRateDisplay}) - {SavingsDisplay} saved";
    }

    /// <summary>
    /// Date range for reporting periods.
    /// </summary>
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public TimeSpan Duration => EndDate - StartDate;
        public int DaysInRange => (int)Duration.TotalDays;
        
        public string DisplayText => $"{StartDate:MMM dd, yyyy} - {EndDate:MMM dd, yyyy}";
        public string ShortDisplayText => $"{StartDate:MMM dd} - {EndDate:MMM dd}";
    }

    /// <summary>
    /// Interval types for trend analysis.
    /// </summary>
    public enum TrendInterval
    {
        Daily,
        Weekly,
        Monthly
    }

    /// <summary>
    /// Bulk pricing insights and recommendations.
    /// </summary>
    public class BulkPricingInsights
    {
        public List<string> KeyFindings { get; set; } = new List<string>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public List<ProductOptimizationSuggestion> ProductSuggestions { get; set; } = new List<ProductOptimizationSuggestion>();
        
        public string InsightsSummary => $"{KeyFindings.Count} key findings, {Recommendations.Count} recommendations";
    }

    /// <summary>
    /// Optimization suggestion for a specific product's bulk pricing.
    /// </summary>
    public class ProductOptimizationSuggestion
    {
        public Product Product { get; set; }
        public string SuggestionType { get; set; }
        public string Description { get; set; }
        public decimal PotentialImpact { get; set; }
        public string RecommendedAction { get; set; }
        
        public string ImpactDisplay => PotentialImpact.ToString("C2");
        public string ProductName => Product?.Name ?? "Unknown Product";
    }

    /// <summary>
    /// Bulk pricing dashboard summary for quick overview.
    /// </summary>
    public class BulkPricingDashboard
    {
        public decimal TodaysSavings { get; set; }
        public decimal WeeksSavings { get; set; }
        public decimal MonthsSavings { get; set; }
        
        public int ActivePriceTiers { get; set; }
        public int ProductsWithBulkPricing { get; set; }
        
        public decimal TodaysAdoptionRate { get; set; }
        public decimal WeeksAdoptionRate { get; set; }
        
        public List<ProductBulkPricingPerformance> TopProducts { get; set; } = new List<ProductBulkPricingPerformance>();
        public List<PriceTierEffectiveness> TopTiers { get; set; } = new List<PriceTierEffectiveness>();
        
        // Display Properties
        public string TodaysSavingsDisplay => TodaysSavings.ToString("C2");
        public string WeeksSavingsDisplay => WeeksSavings.ToString("C2");
        public string MonthsSavingsDisplay => MonthsSavings.ToString("C2");
        
        public string TodaysAdoptionDisplay => $"{TodaysAdoptionRate:F1}%";
        public string WeeksAdoptionDisplay => $"{WeeksAdoptionRate:F1}%";
        
        public string QuickSummary => $"{ProductsWithBulkPricing} products, {ActivePriceTiers} tiers, {MonthsSavingsDisplay} monthly savings";
    }

    /// <summary>
    /// Bulk pricing comparison between periods.
    /// </summary>
    public class BulkPricingComparison
    {
        public BulkPricingReport CurrentPeriod { get; set; }
        public BulkPricingReport PreviousPeriod { get; set; }
        
        // Calculated Comparisons
        public decimal AdoptionRateChange => CurrentPeriod.BulkPricingAdoptionRate - PreviousPeriod.BulkPricingAdoptionRate;
        public decimal SavingsChange => CurrentPeriod.TotalCustomerSavings - PreviousPeriod.TotalCustomerSavings;
        public decimal RevenueChange => CurrentPeriod.TotalBulkPricingRevenue - PreviousPeriod.TotalBulkPricingRevenue;
        
        public decimal SavingsGrowthPercentage => PreviousPeriod.TotalCustomerSavings > 0 ? 
            (SavingsChange / PreviousPeriod.TotalCustomerSavings) * 100 : 0;
        
        public decimal RevenueGrowthPercentage => PreviousPeriod.TotalBulkPricingRevenue > 0 ? 
            (RevenueChange / PreviousPeriod.TotalBulkPricingRevenue) * 100 : 0;
        
        // Display Properties
        public string AdoptionTrend => AdoptionRateChange >= 0 ? $"↑ {AdoptionRateChange:F1}%" : $"↓ {Math.Abs(AdoptionRateChange):F1}%";
        public string SavingsTrend => SavingsChange >= 0 ? $"↑ {SavingsChange:C2}" : $"↓ {Math.Abs(SavingsChange):C2}";
        public string RevenueTrend => RevenueChange >= 0 ? $"↑ {RevenueChange:C2}" : $"↓ {Math.Abs(RevenueChange):C2}";
        
        public string ComparisonSummary => $"Adoption {AdoptionTrend}, Savings {SavingsTrend}, Revenue {RevenueTrend}";
    }
}
