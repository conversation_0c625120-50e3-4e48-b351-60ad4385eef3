using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Helpers;
using POSSystem.Models;
using POSSystem.Services.Performance;
using POSSystem.ViewModels;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// ✅ PERFORMANCE VALIDATION: Tests to ensure UI thread blocking is reduced to acceptable levels
    /// Target: <100ms for all operations, <50ms for critical POS transactions
    /// </summary>
    [TestClass]
    public class UIThreadBlockingValidationTests
    {
        private const int ACCEPTABLE_UI_BLOCK_MS = 100;
        private const int CRITICAL_TRANSACTION_BLOCK_MS = 50;
        private const int TEST_TIMEOUT_MS = 10000;

        [TestInitialize]
        public void Setup()
        {
            // Ensure we have a UI thread for testing
            if (Application.Current == null)
            {
                new Application();
            }
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task ProductsViewModel_LoadPagedProducts_ShouldNotBlockUIThread()
        {
            // Arrange
            var uiBlockDetected = false;
            var maxBlockDuration = 0.0;
            var blockingOperations = new List<double>();

            // Monitor UI thread blocking
            var timer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(10)
            };

            var lastCheck = DateTime.UtcNow;
            timer.Tick += (s, e) =>
            {
                var now = DateTime.UtcNow;
                var actualInterval = (now - lastCheck).TotalMilliseconds;
                lastCheck = now;

                if (actualInterval > 50) // More than 5x expected interval
                {
                    var blockDuration = actualInterval - 10;
                    blockingOperations.Add(blockDuration);
                    maxBlockDuration = Math.Max(maxBlockDuration, blockDuration);
                    
                    if (blockDuration > ACCEPTABLE_UI_BLOCK_MS)
                    {
                        uiBlockDetected = true;
                    }
                }
            };

            timer.Start();

            try
            {
                // Act - Simulate loading products multiple times
                var tasks = new List<Task>();
                for (int i = 0; i < 5; i++)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        // Simulate ProductsViewModel operations
                        await DatabasePerformanceHelper.ExecuteQueryAsync(async context =>
                        {
                            return await context.Products
                                .Include(p => p.Batches)
                                .Where(p => p.IsActive)
                                .Take(50)
                                .AsNoTracking()
                                .ToListAsync();
                        }, $"TestProductLoad_{i}");
                    }));
                }

                await Task.WhenAll(tasks);
                
                // Allow some time for final UI updates
                await Task.Delay(500);
            }
            finally
            {
                timer.Stop();
            }

            // Assert
            Assert.IsFalse(uiBlockDetected, 
                $"UI thread was blocked for {maxBlockDuration:F0}ms (max acceptable: {ACCEPTABLE_UI_BLOCK_MS}ms)");
            
            if (blockingOperations.Any())
            {
                var avgBlock = blockingOperations.Average();
                Debug.WriteLine($"UI blocking detected: {blockingOperations.Count} events, avg: {avgBlock:F0}ms, max: {maxBlockDuration:F0}ms");
            }
            
            Assert.IsTrue(maxBlockDuration < ACCEPTABLE_UI_BLOCK_MS, 
                $"Maximum UI block duration {maxBlockDuration:F0}ms exceeds acceptable limit of {ACCEPTABLE_UI_BLOCK_MS}ms");
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task Product_GetTotalStock_ShouldNotBlockUIThread()
        {
            // Arrange
            var products = new List<Product>();
            for (int i = 0; i < 100; i++)
            {
                products.Add(new Product
                {
                    Id = i + 1,
                    Name = $"Test Product {i}",
                    TrackBatches = i % 2 == 0, // Half with batch tracking
                    StockQuantity = 10
                });
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var stockCalculations = await Task.Run(() =>
            {
                return products.Select(p => p.GetTotalStock()).ToList();
            });

            stopwatch.Stop();

            // Assert
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < CRITICAL_TRANSACTION_BLOCK_MS,
                $"Stock calculations took {stopwatch.ElapsedMilliseconds}ms, exceeding critical limit of {CRITICAL_TRANSACTION_BLOCK_MS}ms");
            
            Assert.AreEqual(100, stockCalculations.Count);
            Debug.WriteLine($"✅ Stock calculations for 100 products completed in {stopwatch.ElapsedMilliseconds}ms");
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task BackgroundDataLoadingService_LoadDashboardData_ShouldNotBlockUIThread()
        {
            // Arrange
            var service = new BackgroundDataLoadingService();
            var uiResponsive = true;
            var responsivenessTasks = new List<Task>();

            // Start UI responsiveness monitoring
            for (int i = 0; i < 10; i++)
            {
                responsivenessTasks.Add(Task.Run(async () =>
                {
                    await Task.Delay(100);
                    // Simulate UI operations
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // This should execute quickly if UI thread is not blocked
                        var dummy = DateTime.Now.ToString();
                    }, DispatcherPriority.Normal);
                }));
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var loadTask = service.LoadDashboardDataAsync(
                DateTime.Today.AddDays(-30),
                DateTime.Today);

            // Wait for both data loading and UI responsiveness tests
            await Task.WhenAll(loadTask, Task.WhenAll(responsivenessTasks));
            
            stopwatch.Stop();

            // Assert
            Assert.IsTrue(uiResponsive, "UI became unresponsive during background data loading");
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, 
                $"Dashboard data loading took {stopwatch.ElapsedMilliseconds}ms, which may indicate performance issues");
            
            Debug.WriteLine($"✅ Dashboard data loading completed in {stopwatch.ElapsedMilliseconds}ms while maintaining UI responsiveness");
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task DatabasePerformanceHelper_ExecuteQuery_ShouldMeetPerformanceTargets()
        {
            // Arrange
            var operationTimes = new List<long>();

            // Act - Execute multiple database operations
            for (int i = 0; i < 10; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                
                await DatabasePerformanceHelper.ExecuteQueryAsync(async context =>
                {
                    return await context.Products
                        .Where(p => p.IsActive)
                        .Take(20)
                        .AsNoTracking()
                        .ToListAsync();
                }, $"PerformanceTest_{i}");
                
                stopwatch.Stop();
                operationTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Assert
            var avgTime = operationTimes.Average();
            var maxTime = operationTimes.Max();
            
            Assert.IsTrue(avgTime < 200, 
                $"Average database operation time {avgTime:F0}ms exceeds target of 200ms");
            
            Assert.IsTrue(maxTime < 500, 
                $"Maximum database operation time {maxTime}ms exceeds target of 500ms");
            
            Debug.WriteLine($"✅ Database operations: Avg={avgTime:F0}ms, Max={maxTime}ms, All within targets");
        }

        [TestMethod]
        public void UIPerformanceMonitor_ShouldDetectBlockingAccurately()
        {
            // Arrange
            var blockingDetected = false;
            var emergencyModeActivated = false;

            // Capture debug output
            Debug.Listeners.Clear();
            Debug.Listeners.Add(new TestTraceListener(message =>
            {
                if (message.Contains("UI THREAD BLOCKED"))
                {
                    blockingDetected = true;
                }
                if (message.Contains("EMERGENCY PERFORMANCE MODE ACTIVATED"))
                {
                    emergencyModeActivated = true;
                }
            }));

            // Act - Simulate UI thread blocking
            UIPerformanceMonitor.StartUIResponsivenessMonitoring();

            // Simulate a blocking operation
            Thread.Sleep(150); // This should trigger the monitor

            // Allow monitor to detect the block
            Thread.Sleep(200);

            // Assert
            Assert.IsTrue(blockingDetected, "UI performance monitor should detect blocking operations");
            Debug.WriteLine("✅ UI Performance Monitor correctly detected blocking operation");
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task EmergencyPerformanceFix_ShouldPreventSevereBlocking()
        {
            // Arrange
            var emergencyActivated = false;
            Debug.Listeners.Clear();
            Debug.Listeners.Add(new TestTraceListener(message =>
            {
                if (message.Contains("EMERGENCY PERFORMANCE MODE ACTIVATED"))
                {
                    emergencyActivated = true;
                }
            }));

            // Act - Simulate multiple critical blocks
            EmergencyPerformanceFix.CheckForEmergencyActivation(1500);
            EmergencyPerformanceFix.CheckForEmergencyActivation(2000);
            EmergencyPerformanceFix.CheckForEmergencyActivation(1200);

            // Allow time for emergency mode activation
            await Task.Delay(100);

            // Assert
            Assert.IsTrue(EmergencyPerformanceFix.IsEmergencyModeActive,
                "Emergency mode should be activated after multiple critical blocks");

            Assert.IsTrue(EmergencyPerformanceFix.CriticalBlockCount >= 3,
                "Critical block count should reflect the blocking events");

            Debug.WriteLine("✅ Emergency Performance Fix correctly activated emergency mode");

            // Cleanup
            EmergencyPerformanceFix.DeactivateEmergencyMode();
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task EmergencyPerformanceFix_ExecuteWithTimeout_ShouldPreventHanging()
        {
            // Arrange
            var timeoutOccurred = false;
            var operationCompleted = false;

            // Act
            try
            {
                await EmergencyPerformanceFix.ExecuteWithEmergencyTimeout(async () =>
                {
                    // Simulate a hanging operation
                    await Task.Delay(10000); // 10 seconds - should timeout
                    operationCompleted = true;
                    return "completed";
                }, 1000, "TestHangingOperation"); // 1 second timeout
            }
            catch (TimeoutException)
            {
                timeoutOccurred = true;
            }

            // Assert
            Assert.IsTrue(timeoutOccurred, "Operation should have timed out");
            Assert.IsFalse(operationCompleted, "Operation should not have completed");

            Debug.WriteLine("✅ Emergency timeout protection working correctly");
        }

        [TestMethod]
        [Timeout(TEST_TIMEOUT_MS)]
        public async Task PerformanceHelper_ReplaceCollectionContent_ShouldBeEfficient()
        {
            // Arrange
            var collection = new System.Collections.ObjectModel.ObservableCollection<Product>();
            var newItems = Enumerable.Range(1, 1000).Select(i => new Product
            {
                Id = i,
                Name = $"Product {i}",
                StockQuantity = i
            }).ToList();

            var stopwatch = Stopwatch.StartNew();

            // Act
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                PerformanceHelper.ReplaceCollectionContent(collection, newItems);
            });

            stopwatch.Stop();

            // Assert
            Assert.AreEqual(1000, collection.Count);
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < ACCEPTABLE_UI_BLOCK_MS,
                $"Collection update took {stopwatch.ElapsedMilliseconds}ms, exceeding limit of {ACCEPTABLE_UI_BLOCK_MS}ms");
            
            Debug.WriteLine($"✅ Collection update for 1000 items completed in {stopwatch.ElapsedMilliseconds}ms");
        }
    }

    public class TestTraceListener : TraceListener
    {
        private readonly Action<string> _onWrite;

        public TestTraceListener(Action<string> onWrite)
        {
            _onWrite = onWrite;
        }

        public override void Write(string message)
        {
            _onWrite?.Invoke(message);
        }

        public override void WriteLine(string message)
        {
            _onWrite?.Invoke(message);
        }
    }
}
