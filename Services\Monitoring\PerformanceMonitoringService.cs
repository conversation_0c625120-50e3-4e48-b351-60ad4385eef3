using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services.Monitoring
{
    /// <summary>
    /// Comprehensive service for monitoring application performance, detecting bottlenecks, and providing real-time alerts.
    /// Tracks operation execution times, memory usage, system resources, and performance trends.
    /// </summary>
    /// <remarks>
    /// <para>This service provides enterprise-grade performance monitoring capabilities:</para>
    /// <list type="bullet">
    /// <item><description>Real-time Performance Tracking: Monitors all critical operations with configurable thresholds</description></item>
    /// <item><description>Automatic Alerting: Generates alerts when operations exceed warning or critical thresholds</description></item>
    /// <item><description>Trend Analysis: Tracks performance degradation and improvement patterns over time</description></item>
    /// <item><description>Memory Monitoring: Detects potential memory leaks and high memory usage patterns</description></item>
    /// <item><description>System Metrics: Monitors CPU usage, thread count, and garbage collection activity</description></item>
    /// <item><description>Automated Reporting: Generates periodic performance reports with recommendations</description></item>
    /// </list>
    /// <para>The service runs background monitoring cycles and provides both real-time and historical performance data.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Track a database operation
    /// using var tracker = performanceMonitor.StartTracking("GetProducts", "Database");
    /// var products = await GetProductsAsync();
    /// tracker.AddMetadata("ProductCount", products.Count);
    /// // Automatically recorded when disposed
    ///
    /// // Get performance summary
    /// var summary = performanceMonitor.GetSummary();
    /// Console.WriteLine($"Average response time: {summary.AverageResponseTime}ms");
    /// </code>
    /// </example>
    public class PerformanceMonitoringService : IDisposable
    {
        private readonly ILogger<PerformanceMonitoringService> _logger;
        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
        private readonly ConcurrentQueue<PerformanceAlert> _alerts;
        private readonly Timer _reportingTimer;
        private readonly Timer _alertCheckTimer;
        private readonly object _lockObject = new object();

        // Performance thresholds
        private readonly Dictionary<string, PerformanceThreshold> _thresholds;

        public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger = null)
        {
            _logger = logger;
            _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
            _alerts = new ConcurrentQueue<PerformanceAlert>();

            _thresholds = InitializeThresholds();
            
            // Report metrics every 5 minutes
            _reportingTimer = new Timer(ReportMetrics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            // Check for alerts every 30 seconds
            _alertCheckTimer = new Timer(CheckAlerts, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        private Dictionary<string, PerformanceThreshold> InitializeThresholds()
        {
            return new Dictionary<string, PerformanceThreshold>
            {
                // Database operation thresholds
                { "Database.Query", new PerformanceThreshold { WarningMs = 1000, CriticalMs = 3000 } },
                { "Database.Insert", new PerformanceThreshold { WarningMs = 500, CriticalMs = 2000 } },
                { "Database.Update", new PerformanceThreshold { WarningMs = 500, CriticalMs = 2000 } },
                { "Database.Delete", new PerformanceThreshold { WarningMs = 300, CriticalMs = 1000 } },
                
                // Dashboard operation thresholds
                { "Dashboard.Load", new PerformanceThreshold { WarningMs = 500, CriticalMs = 2000 } },
                { "Dashboard.Refresh", new PerformanceThreshold { WarningMs = 300, CriticalMs = 1000 } },
                { "Dashboard.Chart", new PerformanceThreshold { WarningMs = 200, CriticalMs = 800 } },
                
                // Sales operation thresholds
                { "Sales.Process", new PerformanceThreshold { WarningMs = 1000, CriticalMs = 3000 } },
                { "Sales.Calculate", new PerformanceThreshold { WarningMs = 100, CriticalMs = 500 } },
                { "Sales.Save", new PerformanceThreshold { WarningMs = 500, CriticalMs = 2000 } },
                
                // Product operation thresholds
                { "Product.Search", new PerformanceThreshold { WarningMs = 300, CriticalMs = 1000 } },
                { "Product.Load", new PerformanceThreshold { WarningMs = 200, CriticalMs = 800 } },
                { "Product.Update", new PerformanceThreshold { WarningMs = 300, CriticalMs = 1000 } },
                
                // Memory thresholds (in MB)
                { "Memory.Usage", new PerformanceThreshold { WarningMs = 500, CriticalMs = 1000 } },
                { "Memory.GC", new PerformanceThreshold { WarningMs = 100, CriticalMs = 500 } }
            };
        }

        /// <summary>
        /// Starts monitoring a performance operation and returns a tracker for automatic timing and metadata collection.
        /// </summary>
        /// <param name="operationName">The name of the operation being monitored (e.g., "GetProducts", "ProcessSale")</param>
        /// <param name="category">Optional category for grouping operations (e.g., "Database", "Business", "UI")</param>
        /// <returns>A PerformanceTracker that automatically records timing when disposed</returns>
        /// <remarks>
        /// <para>Use this method with a using statement for automatic timing:</para>
        /// <code>
        /// using var tracker = monitor.StartTracking("DatabaseQuery", "Database");
        /// // Your operation here
        /// tracker.AddMetadata("RecordCount", results.Count);
        /// // Automatically recorded when disposed
        /// </code>
        /// <para>The tracker will automatically check against configured thresholds and generate alerts if exceeded.</para>
        /// </remarks>
        public PerformanceTracker StartTracking(string operationName, string category = null)
        {
            var tracker = new PerformanceTracker(operationName, category, this);
            return tracker;
        }

        /// <summary>
        /// Records a performance measurement for an operation with optional metadata and automatic threshold checking.
        /// </summary>
        /// <param name="operationName">The name of the operation that was measured</param>
        /// <param name="durationMs">The duration of the operation in milliseconds</param>
        /// <param name="category">Optional category for grouping operations (e.g., "Database", "Business", "UI")</param>
        /// <param name="metadata">Optional metadata dictionary with additional information about the operation</param>
        /// <remarks>
        /// <para>This method automatically:</para>
        /// <list type="bullet">
        /// <item><description>Updates performance statistics (min, max, average, recent average)</description></item>
        /// <item><description>Checks against configured thresholds and generates alerts if exceeded</description></item>
        /// <item><description>Logs performance information for diagnostic purposes</description></item>
        /// <item><description>Maintains recent measurement history for trend analysis</description></item>
        /// </list>
        /// <para>Prefer using StartTracking() for automatic timing instead of calling this method directly.</para>
        /// </remarks>
        public void RecordMetric(string operationName, long durationMs, string category = null, Dictionary<string, object> metadata = null)
        {
            var key = string.IsNullOrEmpty(category) ? operationName : $"{category}.{operationName}";
            
            _metrics.AddOrUpdate(key, 
                new PerformanceMetric(key, durationMs, metadata),
                (k, existing) => existing.AddMeasurement(durationMs, metadata));

            // Check if this measurement exceeds thresholds
            CheckThreshold(key, durationMs);

            _logger?.LogDebug("Performance metric recorded: {Operation} took {Duration}ms", key, durationMs);
        }

        /// <summary>
        /// Record memory usage
        /// </summary>
        public void RecordMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            var workingSetMB = process.WorkingSet64 / (1024 * 1024);
            var privateMB = process.PrivateMemorySize64 / (1024 * 1024);

            RecordMetric("Usage", workingSetMB, "Memory", new Dictionary<string, object>
            {
                { "WorkingSetMB", workingSetMB },
                { "PrivateMemoryMB", privateMB },
                { "GCTotalMemory", GC.GetTotalMemory(false) / (1024 * 1024) }
            });

            // Check memory thresholds
            CheckThreshold("Memory.Usage", workingSetMB);
        }

        /// <summary>
        /// Get current performance metrics
        /// </summary>
        public Dictionary<string, PerformanceMetric> GetMetrics()
        {
            return new Dictionary<string, PerformanceMetric>(_metrics);
        }

        /// <summary>
        /// Get performance summary
        /// </summary>
        public PerformanceSummary GetSummary()
        {
            var summary = new PerformanceSummary
            {
                GeneratedAt = DateTime.Now,
                TotalOperations = 0,
                AverageResponseTime = 0,
                SlowestOperations = new List<PerformanceMetric>(),
                RecentAlerts = new List<PerformanceAlert>()
            };

            foreach (var metric in _metrics.Values)
            {
                summary.TotalOperations += metric.Count;
                summary.AverageResponseTime += metric.AverageMs * metric.Count;
            }

            if (summary.TotalOperations > 0)
            {
                summary.AverageResponseTime /= summary.TotalOperations;
            }

            // Get slowest operations
            var sortedMetrics = _metrics.Values.OrderByDescending(m => m.AverageMs).Take(10);
            summary.SlowestOperations.AddRange(sortedMetrics);

            // Get recent alerts
            var recentAlerts = new List<PerformanceAlert>();
            while (_alerts.TryDequeue(out var alert) && recentAlerts.Count < 20)
            {
                if (alert.Timestamp > DateTime.Now.AddHours(-1)) // Last hour
                {
                    recentAlerts.Add(alert);
                }
            }
            summary.RecentAlerts = recentAlerts.OrderByDescending(a => a.Timestamp).ToList();

            return summary;
        }

        /// <summary>
        /// Clear all metrics
        /// </summary>
        public void ClearMetrics()
        {
            _metrics.Clear();
            _logger?.LogInformation("Performance metrics cleared");
        }

        private void CheckThreshold(string operationName, long durationMs)
        {
            if (_thresholds.TryGetValue(operationName, out var threshold))
            {
                AlertLevel? alertLevel = null;
                
                if (durationMs >= threshold.CriticalMs)
                {
                    alertLevel = AlertLevel.Critical;
                }
                else if (durationMs >= threshold.WarningMs)
                {
                    alertLevel = AlertLevel.Warning;
                }

                if (alertLevel.HasValue)
                {
                    var alert = new PerformanceAlert
                    {
                        OperationName = operationName,
                        DurationMs = durationMs,
                        ThresholdMs = alertLevel == AlertLevel.Critical ? threshold.CriticalMs : threshold.WarningMs,
                        Level = alertLevel.Value,
                        Timestamp = DateTime.Now,
                        Message = $"Operation '{operationName}' took {durationMs}ms (threshold: {(alertLevel == AlertLevel.Critical ? threshold.CriticalMs : threshold.WarningMs)}ms)"
                    };

                    _alerts.Enqueue(alert);
                    
                    _logger?.LogWarning("Performance alert: {Message}", alert.Message);
                }
            }
        }

        private void ReportMetrics(object state)
        {
            try
            {
                var summary = GetSummary();
                
                _logger?.LogInformation("Performance Report - Total Operations: {Total}, Average Response: {Average}ms, Alerts: {Alerts}",
                    summary.TotalOperations, summary.AverageResponseTime, summary.RecentAlerts.Count);

                if (summary.SlowestOperations.Any())
                {
                    _logger?.LogInformation("Slowest Operations:");
                    foreach (var op in summary.SlowestOperations.Take(5))
                    {
                        _logger?.LogInformation("  {Operation}: {Average}ms (count: {Count})", 
                            op.OperationName, op.AverageMs, op.Count);
                    }
                }

                // Record memory usage
                RecordMemoryUsage();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error generating performance report");
            }
        }

        private void CheckAlerts(object state)
        {
            try
            {
                // Check for patterns that might indicate performance issues
                CheckForPerformanceDegradation();
                CheckForMemoryLeaks();
                CheckForSlowOperations();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking performance alerts");
            }
        }

        private void CheckForPerformanceDegradation()
        {
            // Check if average response times are increasing
            foreach (var metric in _metrics.Values)
            {
                if (metric.Count >= 10 && metric.RecentAverageMs > metric.AverageMs * 1.5)
                {
                    var alert = new PerformanceAlert
                    {
                        OperationName = metric.OperationName,
                        Level = AlertLevel.Warning,
                        Timestamp = DateTime.Now,
                        Message = $"Performance degradation detected for '{metric.OperationName}': recent average {metric.RecentAverageMs}ms vs overall {metric.AverageMs}ms"
                    };
                    
                    _alerts.Enqueue(alert);
                    _logger?.LogWarning(alert.Message);
                }
            }
        }

        private void CheckForMemoryLeaks()
        {
            if (_metrics.TryGetValue("Memory.Usage", out var memoryMetric))
            {
                if (memoryMetric.Count >= 5 && memoryMetric.RecentAverageMs > memoryMetric.AverageMs * 1.3)
                {
                    var alert = new PerformanceAlert
                    {
                        OperationName = "Memory.Usage",
                        Level = AlertLevel.Warning,
                        Timestamp = DateTime.Now,
                        Message = $"Potential memory leak detected: memory usage trending upward from {memoryMetric.AverageMs}MB to {memoryMetric.RecentAverageMs}MB"
                    };
                    
                    _alerts.Enqueue(alert);
                    _logger?.LogWarning(alert.Message);
                }
            }
        }

        private void CheckForSlowOperations()
        {
            var slowOperations = _metrics.Values
                .Where(m => m.Count >= 5 && m.AverageMs > 2000) // Operations taking more than 2 seconds
                .OrderByDescending(m => m.AverageMs)
                .Take(3);

            foreach (var op in slowOperations)
            {
                var alert = new PerformanceAlert
                {
                    OperationName = op.OperationName,
                    Level = AlertLevel.Info,
                    Timestamp = DateTime.Now,
                    Message = $"Consistently slow operation detected: '{op.OperationName}' averages {op.AverageMs}ms over {op.Count} executions"
                };
                
                _alerts.Enqueue(alert);
            }
        }

        public void Dispose()
        {
            _reportingTimer?.Dispose();
            _alertCheckTimer?.Dispose();
        }
    }

    /// <summary>
    /// Performance tracking helper
    /// </summary>
    public class PerformanceTracker : IDisposable
    {
        private readonly string _operationName;
        private readonly string _category;
        private readonly PerformanceMonitoringService _service;
        private readonly Stopwatch _stopwatch;
        private readonly Dictionary<string, object> _metadata;

        public PerformanceTracker(string operationName, string category, PerformanceMonitoringService service)
        {
            _operationName = operationName;
            _category = category;
            _service = service;
            _stopwatch = Stopwatch.StartNew();
            _metadata = new Dictionary<string, object>();
        }

        public void AddMetadata(string key, object value)
        {
            _metadata[key] = value;
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            _service.RecordMetric(_operationName, _stopwatch.ElapsedMilliseconds, _category, _metadata);
        }
    }

    /// <summary>
    /// Performance metric data
    /// </summary>
    public class PerformanceMetric
    {
        private readonly Queue<long> _recentMeasurements = new Queue<long>();
        private readonly object _lock = new object();

        public string OperationName { get; }
        public long TotalMs { get; private set; }
        public int Count { get; private set; }
        public long MinMs { get; private set; } = long.MaxValue;
        public long MaxMs { get; private set; }
        public double AverageMs => Count > 0 ? (double)TotalMs / Count : 0;
        public double RecentAverageMs { get; private set; }
        public DateTime FirstRecorded { get; }
        public DateTime LastRecorded { get; private set; }
        public Dictionary<string, object> LastMetadata { get; private set; }

        public PerformanceMetric(string operationName, long durationMs, Dictionary<string, object> metadata = null)
        {
            OperationName = operationName;
            FirstRecorded = DateTime.Now;
            LastMetadata = metadata ?? new Dictionary<string, object>();
            AddMeasurement(durationMs, metadata);
        }

        public PerformanceMetric AddMeasurement(long durationMs, Dictionary<string, object> metadata = null)
        {
            lock (_lock)
            {
                TotalMs += durationMs;
                Count++;
                MinMs = Math.Min(MinMs, durationMs);
                MaxMs = Math.Max(MaxMs, durationMs);
                LastRecorded = DateTime.Now;
                LastMetadata = metadata ?? LastMetadata;

                // Keep track of recent measurements (last 10)
                _recentMeasurements.Enqueue(durationMs);
                if (_recentMeasurements.Count > 10)
                {
                    _recentMeasurements.Dequeue();
                }

                // Calculate recent average
                RecentAverageMs = _recentMeasurements.Average();
            }

            return this;
        }
    }

    /// <summary>
    /// Performance alert information
    /// </summary>
    public class PerformanceAlert
    {
        public string OperationName { get; set; }
        public long DurationMs { get; set; }
        public long ThresholdMs { get; set; }
        public AlertLevel Level { get; set; }
        public DateTime Timestamp { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Performance threshold configuration
    /// </summary>
    public class PerformanceThreshold
    {
        public long WarningMs { get; set; }
        public long CriticalMs { get; set; }
    }

    /// <summary>
    /// Performance summary report
    /// </summary>
    public class PerformanceSummary
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalOperations { get; set; }
        public double AverageResponseTime { get; set; }
        public List<PerformanceMetric> SlowestOperations { get; set; } = new List<PerformanceMetric>();
        public List<PerformanceAlert> RecentAlerts { get; set; } = new List<PerformanceAlert>();
    }

    /// <summary>
    /// Alert severity levels
    /// </summary>
    public enum AlertLevel
    {
        Info,
        Warning,
        Critical
    }
}
