# Weight-Based Product Testing Checklist

## Overview
This checklist covers comprehensive testing of the weight-based product functionality in the POS system.

## Pre-Testing Setup

### Database Migration
- [ ] Run the database migration script (`Migrations/AddWeightBasedProductSupport.sql`)
- [ ] Verify `IsWeightBased` column exists in Products table
- [ ] Verify `SaleItems.Quantity` is now `decimal(18,3)`
- [ ] Check that existing products have `IsWeightBased = 0` by default

### Sample Data
- [ ] Create test weight-based products (e.g., fruits, meat, bulk items)
- [ ] Create test unit-based products (e.g., bottles, boxes, individual items)
- [ ] Ensure products have appropriate units of measure (kg, lbs, g, etc.)

## Product Dialog Testing

### Weight-Based Product Creation
- [ ] Open Product Dialog
- [ ] Select "By Weight" option in Sales Method section
- [ ] Verify UI shows weight-based tooltips and hints
- [ ] Enter decimal quantity (e.g., 2.5)
- [ ] Select weight-based unit of measure (kg, lbs, g)
- [ ] Save product and verify `IsWeightBased = true` in database
- [ ] Verify quantity input accepts decimals for weight-based products

### Unit-Based Product Creation
- [ ] Open Product Dialog
- [ ] Select "By Units" option in Sales Method section
- [ ] Verify UI shows unit-based tooltips and hints
- [ ] Enter whole number quantity
- [ ] Select unit-based unit of measure (pcs, boxes, bottles)
- [ ] Save product and verify `IsWeightBased = false` in database
- [ ] Verify quantity input rounds decimals to whole numbers

### Product Editing
- [ ] Edit existing weight-based product
- [ ] Verify "By Weight" is selected correctly
- [ ] Change to "By Units" and save
- [ ] Verify database is updated correctly
- [ ] Edit existing unit-based product
- [ ] Change to "By Weight" and save
- [ ] Verify database is updated correctly

## Sales View Testing

### Adding Weight-Based Products to Cart
- [ ] Click on weight-based product in product grid
- [ ] Verify product is added with quantity 1.0
- [ ] Add same product again, verify quantity increases by 1.0
- [ ] Verify quantity displays with decimal precision (e.g., "2.0")
- [ ] Verify unit of measure is displayed correctly

### Adding Unit-Based Products to Cart
- [ ] Click on unit-based product in product grid
- [ ] Verify product is added with quantity 1
- [ ] Add same product again, verify quantity increases by 1
- [ ] Verify quantity displays as whole number (e.g., "2")

### Quantity Adjustment
- [ ] Use + button on weight-based product
- [ ] Verify quantity increases by 0.1
- [ ] Use - button on weight-based product
- [ ] Verify quantity decreases by 0.1
- [ ] Test minimum quantity (should not go below 0.1 for weight-based)
- [ ] Use + button on unit-based product
- [ ] Verify quantity increases by 1
- [ ] Use - button on unit-based product
- [ ] Verify quantity decreases by 1
- [ ] Test minimum quantity (should not go below 1 for unit-based)

### Keyboard Shortcuts
- [ ] Select weight-based cart item
- [ ] Press + key, verify quantity increases by 0.1
- [ ] Press - key, verify quantity decreases by 0.1
- [ ] Select unit-based cart item
- [ ] Press + key, verify quantity increases by 1
- [ ] Press - key, verify quantity decreases by 1

## Stock Management Testing

### Stock Validation
- [ ] Create weight-based product with limited stock (e.g., 5.0 kg)
- [ ] Try to add 3.5 kg to cart - should succeed
- [ ] Try to add another 2.0 kg - should show stock limit warning
- [ ] Verify error message shows correct units (kg, not items)
- [ ] Test partial quantity addition when stock is limited

### Stock Display
- [ ] Verify weight-based products show stock with decimal precision
- [ ] Verify unit-based products show stock as whole numbers
- [ ] Check stock status messages use appropriate units

## Cart and Checkout Testing

### Cart Calculations
- [ ] Add weight-based product with quantity 2.5
- [ ] Verify total calculation: 2.5 × unit price
- [ ] Add unit-based product with quantity 3
- [ ] Verify total calculation: 3 × unit price
- [ ] Verify cart subtotal includes both products correctly
- [ ] Test with discounts applied to weight-based products

### Cart Display
- [ ] Verify weight-based products show decimal quantities
- [ ] Verify unit-based products show whole number quantities
- [ ] Check that quantity formatting is consistent throughout UI
- [ ] Verify unit of measure is displayed where appropriate

### Checkout Process
- [ ] Complete sale with weight-based products
- [ ] Verify SaleItems table contains decimal quantities
- [ ] Check that receipt shows correct quantities and units
- [ ] Verify inventory is updated correctly

## Edge Cases and Error Handling

### Invalid Input Handling
- [ ] Try entering negative quantity for weight-based product
- [ ] Try entering zero quantity
- [ ] Try entering extremely large decimal values
- [ ] Test with very small decimal values (e.g., 0.001)

### Stock Edge Cases
- [ ] Test with zero stock weight-based product
- [ ] Test with fractional stock remaining (e.g., 0.3 kg left)
- [ ] Test stock validation with multiple cart items

### UI Edge Cases
- [ ] Test with very long product names
- [ ] Test with products having no unit of measure
- [ ] Test switching between weight-based and unit-based rapidly

## Performance Testing

### Large Quantities
- [ ] Test with large decimal quantities (e.g., 999.999)
- [ ] Test cart with many weight-based products
- [ ] Verify calculations remain accurate with large numbers

### Database Performance
- [ ] Test product search with mixed weight/unit products
- [ ] Verify decimal quantity queries perform well
- [ ] Check that indexes are being used effectively

## Regression Testing

### Existing Functionality
- [ ] Verify existing unit-based products still work correctly
- [ ] Test existing sales reports with new decimal quantities
- [ ] Verify inventory management still functions properly
- [ ] Check that existing integrations are not broken

### Data Integrity
- [ ] Verify existing sales data is preserved
- [ ] Check that product data migration was successful
- [ ] Ensure no data corruption occurred during migration

## User Experience Testing

### Intuitive Operation
- [ ] Test with non-technical users
- [ ] Verify weight-based vs unit-based distinction is clear
- [ ] Check that error messages are user-friendly
- [ ] Ensure quantity input is intuitive for both types

### Visual Feedback
- [ ] Verify clear visual distinction between product types
- [ ] Check that tooltips and hints are helpful
- [ ] Ensure quantity displays are easy to read

## Final Validation

### Complete Workflow Test
- [ ] Create weight-based product from scratch
- [ ] Add to cart with decimal quantity
- [ ] Adjust quantity using various methods
- [ ] Complete sale and verify all data is correct
- [ ] Check reports and inventory updates

### Documentation
- [ ] Update user manual with weight-based product instructions
- [ ] Document any configuration requirements
- [ ] Create training materials for staff

## Sign-off
- [ ] All tests passed
- [ ] No critical issues found
- [ ] Performance is acceptable
- [ ] User experience is satisfactory
- [ ] Ready for production deployment

**Tested by:** _______________  
**Date:** _______________  
**Version:** _______________
