using System;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Diagnostics;
using System.Security.Cryptography;

namespace POSLicensingSystem.Utilities
{
    public static class LicenseValidator
    {
        private const string BASE64_MARKER = "##POS2024##";
        private const string HEX_MARKER = "504F5353797374656D32303234"; // Hex for "POSSystem2024"
        private const string SECRET_KEY = "POS2024SecretKey";
        private const string ASSEMBLY_SEED = "POSSystem_v1.0";
        
        /// <summary>
        /// Validates a license key against the given business name and hardware ID.
        /// </summary>
        /// <param name="licenseKey">The license key to validate</param>
        /// <param name="businessName">The expected business name</param>
        /// <param name="hardwareId">The expected hardware ID</param>
        /// <param name="errorMessage">Output error message if validation fails</param>
        /// <returns>True if the license is valid, false otherwise</returns>
        public static bool ValidateLicense(string licenseKey, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("=== Starting license validation ===");
                Debug.WriteLine($"Business name: '{businessName}'");
                Debug.WriteLine($"Hardware ID: {hardwareId}");
                Debug.WriteLine($"License key: {licenseKey}");
                
                if (string.IsNullOrWhiteSpace(licenseKey))
                {
                    errorMessage = "License key cannot be empty";
                    Debug.WriteLine("License key is empty");
                    return false;
                }
                
                // First fix any potential split markers in the license key
                licenseKey = FixLicenseKeyFormat(licenseKey);
                Debug.WriteLine($"After format fixing: {licenseKey}");
                
                // Clean and check format (remove dashes and spaces)
                string cleanedKey = Regex.Replace(licenseKey, "[-\\s]", "");
                Debug.WriteLine($"Cleaned key: {cleanedKey}");
                
                // First check for Base64 format with marker (primary format we support)
                if (cleanedKey.Contains(BASE64_MARKER))
                {
                    Debug.WriteLine("Base64 marker found");
                    return ValidateBase64License(cleanedKey, businessName, hardwareId, out errorMessage);
                }
                
                // Check for hex format with marker
                if (cleanedKey.Contains(HEX_MARKER, StringComparison.OrdinalIgnoreCase))
                {
                    Debug.WriteLine("Hex marker found");
                    return ValidateHexLicense(cleanedKey, businessName, hardwareId, out errorMessage);
                }
                
                // Try to check if it's a Base64 license key without a marker
                try
                {
                    // Base64 characters are [A-Za-z0-9+/=]
                    // We'll try to validate the string as Base64 first
                    if (IsValidBase64(cleanedKey))
                    {
                        Debug.WriteLine("Key appears to be Base64 but missing marker - attempting direct Base64 validation");
                        return TryValidateDirectBase64(cleanedKey, businessName, hardwareId, out errorMessage);
                    }
                }
                catch
                {
                    // Not a valid Base64 string, continue with other checks
                }
                
                // Special case: If the key is simply a JSON object in hex format
                if (IsHexString(cleanedKey) && cleanedKey.StartsWith("7B") && cleanedKey.Contains("7D"))
                {
                    Debug.WriteLine("Detected JSON-like structure in hex format");
                    return TryLenientHexValidation(cleanedKey, businessName, hardwareId, out errorMessage);
                }
                
                // If we've reached this point, try the lenient validation as a last resort
                if (IsHexString(cleanedKey))
                {
                    Debug.WriteLine("Key is in hex format - attempting lenient validation");
                    return TryLenientHexValidation(cleanedKey, businessName, hardwareId, out errorMessage);
                }
                
                // If we can't identify the key format
                errorMessage = "License key format is invalid (contains invalid characters or missing marker)";
                Debug.WriteLine("License key is not in a recognized format");
                return false;
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating license: {ex.Message}";
                Debug.WriteLine($"Exception during validation: {ex}");
                return false;
            }
        }
        
        /// <summary>
        /// Checks if a string is valid Base64.
        /// </summary>
        private static bool IsValidBase64(string base64)
        {
            // Check if it's a valid Base64 string (valid characters and valid length)
            if (string.IsNullOrWhiteSpace(base64))
                return false;
            
            // Base64 length must be a multiple of 4
            if (base64.Length % 4 != 0)
                return false;
            
            // Check for valid Base64 characters
            return Regex.IsMatch(base64, "^[A-Za-z0-9+/]*={0,3}$");
        }
        
        /// <summary>
        /// Try to validate a Base64 string directly without a marker
        /// </summary>
        private static bool TryValidateDirectBase64(string cleanedKey, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                // Assume the last 32 chars are the signature
                if (cleanedKey.Length <= 32)
                {
                    errorMessage = "License key is too short";
                    return false;
                }
                
                string base64Part = cleanedKey.Substring(0, cleanedKey.Length - 32);
                string signaturePart = cleanedKey.Substring(cleanedKey.Length - 32);
                
                Debug.WriteLine($"Base64 part: {base64Part}, length: {base64Part.Length}");
                Debug.WriteLine($"Signature part: {signaturePart}, length: {signaturePart.Length}");
                
                // Try to decode the Base64 part
                byte[] jsonBytes;
                try
                {
                    jsonBytes = Convert.FromBase64String(base64Part);
                }
                catch (Exception ex)
                {
                    errorMessage = "Failed to decode Base64 data";
                    Debug.WriteLine($"Base64 decode error: {ex.Message}");
                    return false;
                }
                
                string jsonString = Encoding.UTF8.GetString(jsonBytes);
                Debug.WriteLine($"Decoded JSON: {jsonString}");
                
                // Validate the JSON and signature
                return ValidateLicenseData(jsonString, signaturePart, businessName, hardwareId, out errorMessage);
            }
            catch (Exception ex)
            {
                errorMessage = $"Error in direct Base64 validation: {ex.Message}";
                Debug.WriteLine($"Exception in direct Base64 validation: {ex}");
                return false;
            }
        }
        
        /// <summary>
        /// Attempt a more lenient validation of a hex string that might be a license key
        /// </summary>
        private static bool TryLenientHexValidation(string cleanedKey, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Attempting lenient hex validation (POSSystem format)");
                
                // Check minimum length requirements
                if (cleanedKey.Length <= 64)
                {
                    errorMessage = "License key is too short for hex format";
                    Debug.WriteLine("Key is too short for hex format");
                    return false;
                }
                
                // Separate data and signature parts (last 64 characters = 32 bytes SHA256 hash)
                string dataHex = cleanedKey.Substring(0, cleanedKey.Length - 64);
                string signatureHex = cleanedKey.Substring(cleanedKey.Length - 64);
                
                Debug.WriteLine($"Data hex part: {dataHex.Length} characters");
                Debug.WriteLine($"Signature hex part: {signatureHex.Length} characters");
                
                // Convert hex to bytes
                byte[] dataBytes = HexToBytes(dataHex);
                
                // Convert to string
                string dataString = Encoding.UTF8.GetString(dataBytes);
                Debug.WriteLine($"Decoded data: {dataString}");
                
                // POSSystem format appends a salt "POSSystem2024" to the end of data
                const string SALT = "POSSystem2024";
                
                // Check if the decoded string ends with the salt
                if (!dataString.EndsWith(SALT))
                {
                    Debug.WriteLine("Data does not end with expected salt");
                    errorMessage = "Invalid license key format (missing required salt)";
                    return false;
                }
                
                // Remove the salt to get the actual JSON data
                string jsonData = dataString.Substring(0, dataString.Length - SALT.Length);
                Debug.WriteLine($"JSON data: {jsonData}");
                
                // Parse the JSON data
                try
                {
                    using (JsonDocument document = JsonDocument.Parse(jsonData))
                    {
                        JsonElement root = document.RootElement;
                        Debug.WriteLine("Successfully parsed JSON data from hex format");
                        
                        // Verify business name
                        if (!root.TryGetProperty("BusinessName", out JsonElement businessNameElement))
                        {
                            errorMessage = "Invalid license key (missing business name)";
                            Debug.WriteLine("Missing BusinessName property");
                            return false;
                        }
                        
                        string licenseBusiness = businessNameElement.GetString() ?? string.Empty;
                        Debug.WriteLine($"License business name: '{licenseBusiness}', expected: '{businessName}'");
                        
                        if (!string.Equals(licenseBusiness, businessName, StringComparison.OrdinalIgnoreCase))
                        {
                            errorMessage = $"Business name mismatch. Expected '{businessName}', found '{licenseBusiness}'";
                            Debug.WriteLine("Business name mismatch");
                            return false;
                        }
                        
                        // Verify hardware ID
                        if (!root.TryGetProperty("HardwareId", out JsonElement hardwareIdElement))
                        {
                            errorMessage = "Invalid license key (missing hardware ID)";
                            Debug.WriteLine("Missing HardwareId property");
                            return false;
                        }
                        
                        string licenseHardwareId = hardwareIdElement.GetString() ?? string.Empty;
                        Debug.WriteLine($"License hardware ID: {licenseHardwareId}");
                        Debug.WriteLine($"Expected system ID: {hardwareId}");
                        
                        if (!string.Equals(licenseHardwareId, hardwareId, StringComparison.OrdinalIgnoreCase))
                        {
                            errorMessage = "Hardware ID mismatch";
                            Debug.WriteLine("Hardware ID mismatch");
                            return false;
                        }
                        
                        // Verify expiration date
                        if (!root.TryGetProperty("ExpirationDate", out JsonElement expirationElement))
                        {
                            errorMessage = "Invalid license key (missing expiration date)";
                            Debug.WriteLine("Missing ExpirationDate property");
                            return false;
                        }
                        
                        DateTime expirationDate;
                        if (expirationElement.ValueKind == JsonValueKind.String)
                        {
                            if (!DateTime.TryParse(expirationElement.GetString(), out expirationDate))
                            {
                                errorMessage = "Invalid expiration date format";
                                Debug.WriteLine("Invalid date format");
                                return false;
                            }
                        }
                        else
                        {
                            expirationDate = expirationElement.GetDateTime();
                        }
                        
                        if (expirationDate < DateTime.Now)
                        {
                            errorMessage = $"License expired on {expirationDate.ToShortDateString()}";
                            Debug.WriteLine("License expired");
                            return false;
                        }
                        
                        // Verify signature using POSSystem's approach
                        // We need to use a secret key similar to POSSystem's
                        string combinedData = $"{ASSEMBLY_SEED}:{SALT}";
                        byte[] keyBytes = SHA256.HashData(Encoding.UTF8.GetBytes(combinedData));
                        string secretKey = Convert.ToBase64String(keyBytes).Substring(0, 16) + "!" +
                                          Convert.ToBase64String(keyBytes).Substring(16, 8) + "#";
                        
                        byte[] signatureKeyBytes = Encoding.UTF8.GetBytes(secretKey);
                        
                        // Calculate HMAC of data+salt
                        using (var hmac = new HMACSHA256(signatureKeyBytes))
                        {
                            byte[] dataWithSaltBytes = Encoding.UTF8.GetBytes(dataString);
                            byte[] expectedSignature = hmac.ComputeHash(dataBytes);
                            
                            // Convert expected signature to hex
                            string expectedSignatureHex = BitConverter.ToString(expectedSignature).Replace("-", "");
                            
                            Debug.WriteLine($"Expected signature: {expectedSignatureHex}");
                            Debug.WriteLine($"Actual signature: {signatureHex}");
                            
                            if (!string.Equals(expectedSignatureHex, signatureHex, StringComparison.OrdinalIgnoreCase))
                            {
                                // Try alternate verification method if the first fails
                                // This is a fallback since we don't know exact POSSystem implementation
                                Debug.WriteLine("First signature verification failed, trying alternate method");
                                
                                // Try with just the JSON data (no salt)
                                byte[] jsonDataBytes = Encoding.UTF8.GetBytes(jsonData);
                                byte[] altExpectedSignature = hmac.ComputeHash(jsonDataBytes);
                                string altExpectedSignatureHex = BitConverter.ToString(altExpectedSignature).Replace("-", "");
                                
                                if (!string.Equals(altExpectedSignatureHex, signatureHex, StringComparison.OrdinalIgnoreCase))
                                {
                                    errorMessage = "License signature verification failed";
                                    Debug.WriteLine("Signature verification failed");
                                    return false;
                                }
                            }
                        }
                        
                        // All validations passed
                        Debug.WriteLine("POSSystem hex license validation successful");
                        return true;
                    }
                }
                catch (JsonException ex)
                {
                    errorMessage = $"Invalid license format: {ex.Message}";
                    Debug.WriteLine($"JSON parse error: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating hex license: {ex.Message}";
                Debug.WriteLine($"Exception in hex validation: {ex}");
                return false;
            }
        }
        
        /// <summary>
        /// Fixes potential issues with the license key format, especially split markers like ##P-OS202-4##
        /// </summary>
        public static string FixLicenseKeyFormat(string licenseKey)
        {
            if (string.IsNullOrEmpty(licenseKey))
                return licenseKey;
                
            // Check if the key already has the correct marker
            if (Regex.Replace(licenseKey, "[-\\s]", "").Contains(BASE64_MARKER))
                return licenseKey;
                
            // Look for split marker patterns
            if (licenseKey.Contains("##P") && licenseKey.Contains("4##"))
            {
                Debug.WriteLine("Detected split marker pattern, attempting to fix");
                
                // Extract all the characters between ##P and 4##, removing dashes
                Match match = Regex.Match(licenseKey, "##P(.+?)4##");
                if (match.Success)
                {
                    string middlePart = Regex.Replace(match.Groups[1].Value, "[-\\s]", "");
                    
                    // If the cleaned middle part is "OS202", we found our split marker
                    if (middlePart.Equals("OS202", StringComparison.OrdinalIgnoreCase))
                    {
                        string splitMarker = match.Value; // The split marker as it appears in the key
                        Debug.WriteLine($"Found split marker: {splitMarker}");
                        
                        // Replace the split marker with the correct one
                        return licenseKey.Replace(splitMarker, BASE64_MARKER);
                    }
                }
            }
            
            return licenseKey;
        }
        
        private static bool ValidateBase64License(string cleanedKey, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                // Find signature marker
                int markerIndex = cleanedKey.IndexOf(BASE64_MARKER);
                
                if (markerIndex < 0)
                {
                    errorMessage = "License key format is invalid (missing marker)";
                    Debug.WriteLine("Missing signature marker");
                    return false;
                }
                
                Debug.WriteLine($"Found signature marker at index {markerIndex}");
                
                // Extract Base64 data and signature
                string base64Data = cleanedKey.Substring(0, markerIndex);
                Debug.WriteLine($"Base64 data part length: {base64Data.Length} characters");
                
                string signaturePart = cleanedKey.Substring(markerIndex + BASE64_MARKER.Length);
                Debug.WriteLine($"Signature part length: {signaturePart.Length} characters");
                
                if (signaturePart.Length < 32)
                {
                    errorMessage = "License key format is invalid (signature too short)";
                    Debug.WriteLine("Signature part is too short");
                    return false;
                }
                
                // Decode Base64 to JSON
                byte[] jsonBytes;
                try
                {
                    jsonBytes = Convert.FromBase64String(base64Data);
                }
                catch (Exception ex)
                {
                    errorMessage = "License key format is invalid (Base64 decode failed)";
                    Debug.WriteLine($"Error decoding Base64: {ex.Message}");
                    return false;
                }
                
                string jsonString = Encoding.UTF8.GetString(jsonBytes);
                Debug.WriteLine($"Decoded JSON: {jsonString}");
                
                // Validate JSON and signature
                return ValidateLicenseData(jsonString, signaturePart, businessName, hardwareId, out errorMessage);
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating Base64 license: {ex.Message}";
                Debug.WriteLine($"Exception in Base64 validation: {ex}");
                return false;
            }
        }
        
        private static bool ValidateHexLicense(string cleanedKey, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Starting hex license validation");
                
                // Make sure we're working with uppercase for consistency
                cleanedKey = cleanedKey.ToUpperInvariant();
                
                // Find the marker (case insensitive)
                int markerIndex = cleanedKey.IndexOf(HEX_MARKER, StringComparison.OrdinalIgnoreCase);
                
                if (markerIndex < 0)
                {
                    errorMessage = "License key format is invalid (missing required marker)";
                    Debug.WriteLine("Missing required signature marker");
                    return false;
                }
                
                Debug.WriteLine($"Found signature marker at index {markerIndex}");
                
                // Extract hex data and signature
                string jsonHex = cleanedKey.Substring(0, markerIndex);
                Debug.WriteLine($"JSON hex part length: {jsonHex.Length} characters");
                
                string signaturePart = cleanedKey.Substring(markerIndex + HEX_MARKER.Length);
                Debug.WriteLine($"Signature hex part length: {signaturePart.Length} characters");
                
                if (signaturePart.Length < 32)
                {
                    errorMessage = "License key format is invalid (signature too short)";
                    Debug.WriteLine("Signature part is too short");
                    return false;
                }
                
                // Convert the hex JSON back to a string
                byte[] jsonBytes;
                try 
                {
                    jsonBytes = HexToBytes(jsonHex);
                }
                catch (Exception ex)
                {
                    errorMessage = "License key format is invalid (hex decode failed)";
                    Debug.WriteLine($"Error decoding hex: {ex.Message}");
                    return false;
                }
                
                string jsonString = Encoding.UTF8.GetString(jsonBytes);
                Debug.WriteLine($"Decoded JSON: {jsonString}");
                
                // Validate JSON and signature
                return ValidateLicenseData(jsonString, signaturePart, businessName, hardwareId, out errorMessage);
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating hex license: {ex.Message}";
                Debug.WriteLine($"Exception in hex validation: {ex}");
                return false;
            }
        }
        
        private static bool ValidateLicenseData(string jsonString, string signaturePart, string businessName, string hardwareId, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                // Parse the JSON license data
                using (JsonDocument document = JsonDocument.Parse(jsonString))
                {
                    JsonElement root = document.RootElement;
                    Debug.WriteLine("Successfully parsed JSON data");
                    
                    // Verify business name matches
                    if (!root.TryGetProperty("BusinessName", out JsonElement businessNameElement))
                    {
                        errorMessage = "Invalid license key (missing business name)";
                        Debug.WriteLine("Missing BusinessName property");
                        return false;
                    }
                    
                    string licenseBusiness = businessNameElement.GetString() ?? string.Empty;
                    Debug.WriteLine($"License business name: '{licenseBusiness}', expected: '{businessName}'");
                    
                    if (!string.Equals(licenseBusiness, businessName, StringComparison.OrdinalIgnoreCase))
                    {
                        errorMessage = $"Business name mismatch. Expected '{businessName}', found '{licenseBusiness}'";
                        Debug.WriteLine("Business name mismatch");
                        return false;
                    }
                    
                    // Verify hardware ID
                    if (!root.TryGetProperty("HardwareId", out JsonElement hardwareIdElement))
                    {
                        errorMessage = "Invalid license key (missing hardware ID)";
                        Debug.WriteLine("Missing HardwareId property");
                        return false;
                    }
                    
                    string licenseHardwareId = hardwareIdElement.GetString() ?? string.Empty;
                    Debug.WriteLine($"License hardware ID: {licenseHardwareId}");
                    Debug.WriteLine($"Expected system ID: {hardwareId}");
                    
                    if (!string.Equals(licenseHardwareId, hardwareId, StringComparison.OrdinalIgnoreCase))
                    {
                        errorMessage = "Hardware ID mismatch";
                        Debug.WriteLine("Hardware ID mismatch");
                        return false;
                    }
                    
                    // Verify signature
                    string calculatedSignature = CalculateSignature(jsonString);
                    Debug.WriteLine($"Calculated signature: {calculatedSignature}");
                    Debug.WriteLine($"License signature: {signaturePart}");
                    
                    if (!string.Equals(calculatedSignature, signaturePart, StringComparison.OrdinalIgnoreCase))
                    {
                        errorMessage = "Invalid license signature";
                        Debug.WriteLine("Invalid signature");
                        return false;
                    }
                    
                    // Check expiration date
                    if (root.TryGetProperty("ExpirationDate", out JsonElement expirationElement))
                    {
                        if (expirationElement.TryGetDateTime(out DateTime expirationDate))
                        {
                            Debug.WriteLine($"License expires on: {expirationDate}");
                            Debug.WriteLine($"Current date: {DateTime.Now}");
                            
                            if (expirationDate < DateTime.Now)
                            {
                                errorMessage = $"License expired on {expirationDate:d}";
                                Debug.WriteLine("License has expired");
                                return false;
                            }
                        }
                    }
                    
                    // All verifications passed
                    Debug.WriteLine("=== LICENSE VALIDATION SUCCESSFUL ===");
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating license data: {ex.Message}";
                Debug.WriteLine($"Exception in license data validation: {ex}");
                return false;
            }
        }
        
        private static bool IsHexString(string text)
        {
            // Check if string contains only hex characters (0-9, A-F)
            return Regex.IsMatch(text, "^[0-9A-Fa-f]+$");
        }
        
        private static byte[] HexToBytes(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return Array.Empty<byte>();
            
            if (hex.Length % 2 != 0)
                throw new ArgumentException("Hex string must have even length", nameof(hex));
            
            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            
            return bytes;
        }
        
        private static string CalculateSignature(string data)
        {
            // Create a hash of the license data with a secret key
            string dataToHash = data + SECRET_KEY;
            
            using (var sha = SHA256.Create())
            {
                byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(dataToHash));
                
                // Use the first 16 bytes of the hash as a signature
                var signature = new StringBuilder();
                for (int i = 0; i < 16; i++)
                {
                    signature.Append(hashBytes[i].ToString("X2"));
                }
                
                return signature.ToString();
            }
        }
    }
} 