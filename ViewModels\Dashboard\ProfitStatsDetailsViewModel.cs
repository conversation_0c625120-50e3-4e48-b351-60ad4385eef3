using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Windows;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.Views;

namespace POSSystem.ViewModels.Dashboard
{
    public class ProfitStatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private readonly string _statType;
        private bool _isLoading;
        private string _title;
        private string _subtitle;
        private decimal _totalProfit;
        private string _profitGrowth;
        private Brush _profitGrowthColor;
        private decimal _profitMargin;
        private string _marginGrowth;
        private Brush _marginGrowthColor;
        private decimal _averageProfit;
        private string _avgProfitGrowth;
        private Brush _avgProfitGrowthColor;
        private SeriesCollection _trendSeries;
        private string[] _trendLabels;
        private SeriesCollection _hourlyDistributionSeries;
        private string[] _hourlyLabels;
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;
        private ObservableCollection<Product> _products;
        private Product _selectedProduct;
        private bool _isProductFilterEnabled;
        private ObservableCollection<Category> _categories;
        private Category _selectedCategory;
        private bool _isCategoryFilterEnabled;
        private SeriesCollection _profitByDaysSeries;
        private string[] _profitByDaysLabels;
        private Dictionary<DayOfWeek, decimal> _profitByDayData;
        private ObservableCollection<TopProductItem> _topProducts;
        private Axis _hourlyAxisX;
        private Axis _hourlyAxisY;
        private Axis _daysAxisX;
        private Axis _daysAxisY;
        private bool _showNetProfit;
        private string _profitTypeText;
        private Dictionary<string, (decimal expenses, int salesCount)> _monthlyExpenseCache = new Dictionary<string, (decimal, int)>();
        private DateTime _lastCacheClear = DateTime.MinValue;
        private string _barcodeSearch;
        private ICommand _searchByBarcodeCommand;
        private ICommand _openProductSelectionCommand;

        public event PropertyChangedEventHandler PropertyChanged;

        public ProfitStatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService,
            string statType)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;
            _statType = statType;

            // Initialize properties
            Products = new ObservableCollection<Product>();
            Categories = new ObservableCollection<Category>();
            TopProducts = new ObservableCollection<TopProductItem>();
            IsProductFilterEnabled = false;
            IsCategoryFilterEnabled = false;
            ShowNetProfit = false;  // Initialize to false (Gross Profit)
            
            // Set initial title
            Title = Application.Current.TryFindResource("ProfitStatsTitle")?.ToString() ?? "Profit Statistics";
            UpdateProfitTypeText(); // Initialize the profit type text immediately

            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };
            SelectedTrendPeriod = TrendPeriods.First();

            // Load initial data
            _ = InitializeAsync();
        }

        private async Task InitializeAsync()
        {
            await Task.WhenAll(
                LoadProductsAsync(),
                LoadCategoriesAsync(),
                LoadDataAsync()
            );
        }

        public ObservableCollection<Product> Products
        {
            get => _products;
            set { _products = value; OnPropertyChanged(); }
        }

        public Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                System.Diagnostics.Debug.WriteLine($"SelectedProduct setter called with value: {value?.Name ?? "null"}");
                if (_selectedProduct != value)
                {
                    var oldProduct = _selectedProduct;
                    _selectedProduct = value;
                    OnPropertyChanged();

                    System.Diagnostics.Debug.WriteLine($"Product selection changed from {oldProduct?.Name ?? "null"} to {value?.Name ?? "null"}");

                    if (value != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Setting product filter for: {value.Name} (ID: {value.Id})");
                        _isProductFilterEnabled = true;  // Set directly to avoid recursion
                        OnPropertyChanged(nameof(IsProductFilterEnabled));
                        
                        // Disable category filter
                        _isCategoryFilterEnabled = false;
                        _selectedCategory = null;
                        OnPropertyChanged(nameof(IsCategoryFilterEnabled));
                        OnPropertyChanged(nameof(SelectedCategory));
                        
                        // Refresh data
                        _ = LoadDataAsync();
                    }
                }
            }
        }

        public bool IsProductFilterEnabled
        {
            get => _isProductFilterEnabled;
            set
            {
                System.Diagnostics.Debug.WriteLine($"IsProductFilterEnabled setter called with value: {value}");
                if (_isProductFilterEnabled != value)
                {
                    _isProductFilterEnabled = value;
                    OnPropertyChanged();

                    if (!value)
                    {
                        System.Diagnostics.Debug.WriteLine("Product filter disabled, clearing selection");
                        var hadProduct = _selectedProduct != null;
                        _selectedProduct = null;
                        OnPropertyChanged(nameof(SelectedProduct));
                        
                        if (hadProduct)
                        {
                            _ = LoadDataAsync();
                        }
                    }
                }
            }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set { _categories = value; OnPropertyChanged(); }
        }

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                _selectedCategory = value;
                OnPropertyChanged();
                if (value != null)
                {
                    IsCategoryFilterEnabled = true;
                    // Disable product filter when category is selected
                    IsProductFilterEnabled = false;
                    // Update products list to show only products in this category
                    UpdateProductsForCategory(value);
                }
                _ = LoadDataAsync();
            }
        }

        public bool IsCategoryFilterEnabled
        {
            get => _isCategoryFilterEnabled;
            set
            {
                _isCategoryFilterEnabled = value;
                OnPropertyChanged();
                if (!value)
                {
                    SelectedCategory = null;
                    // Reset products list to show all products
                    _ = LoadProductsAsync();
                }
                _ = LoadDataAsync();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public decimal TotalProfit
        {
            get => _totalProfit;
            set { _totalProfit = value; OnPropertyChanged(); }
        }

        public string ProfitGrowth
        {
            get => _profitGrowth;
            set { _profitGrowth = value; OnPropertyChanged(); }
        }

        public Brush ProfitGrowthColor
        {
            get => _profitGrowthColor;
            set { _profitGrowthColor = value; OnPropertyChanged(); }
        }

        public decimal ProfitMargin
        {
            get => _profitMargin;
            set { _profitMargin = value; OnPropertyChanged(); }
        }

        public string MarginGrowth
        {
            get => _marginGrowth;
            set { _marginGrowth = value; OnPropertyChanged(); }
        }

        public Brush MarginGrowthColor
        {
            get => _marginGrowthColor;
            set { _marginGrowthColor = value; OnPropertyChanged(); }
        }

        public decimal AverageProfit
        {
            get => _averageProfit;
            set { _averageProfit = value; OnPropertyChanged(); }
        }

        public string AvgProfitGrowth
        {
            get => _avgProfitGrowth;
            set { _avgProfitGrowth = value; OnPropertyChanged(); }
        }

        public Brush AvgProfitGrowthColor
        {
            get => _avgProfitGrowthColor;
            set { _avgProfitGrowthColor = value; OnPropertyChanged(); }
        }

        public SeriesCollection TrendSeries
        {
            get => _trendSeries;
            set { _trendSeries = value; OnPropertyChanged(); }
        }

        public string[] TrendLabels
        {
            get => _trendLabels;
            set { _trendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection HourlyDistributionSeries
        {
            get => _hourlyDistributionSeries;
            set { _hourlyDistributionSeries = value; OnPropertyChanged(); }
        }

        public string[] HourlyLabels
        {
            get => _hourlyLabels;
            set { _hourlyLabels = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set
            {
                _selectedTrendPeriod = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public SeriesCollection ProfitByDaysSeries
        {
            get => _profitByDaysSeries;
            set { _profitByDaysSeries = value; OnPropertyChanged(); }
        }

        public string[] ProfitByDaysLabels
        {
            get => _profitByDaysLabels;
            set { _profitByDaysLabels = value; OnPropertyChanged(); }
        }

        public Dictionary<DayOfWeek, decimal> ProfitByDayData
        {
            get => _profitByDayData;
            set { _profitByDayData = value; OnPropertyChanged(); }
        }

        public ObservableCollection<TopProductItem> TopProducts
        {
            get => _topProducts;
            set { _topProducts = value; OnPropertyChanged(); }
        }

        public Func<double, string> CurrencyFormatter => value => value.ToString("C0");

        public bool ShowNetProfit
        {
            get => _showNetProfit;
            set
            {
                if (_showNetProfit != value)
                {
                    _showNetProfit = value;
                    OnPropertyChanged();
                    UpdateProfitTypeText();
                    _ = LoadDataAsync();
                }
            }
        }

        public string ProfitTypeText
        {
            get => _profitTypeText;
            set { _profitTypeText = value; OnPropertyChanged(); }
        }

        public string BarcodeSearch
        {
            get => _barcodeSearch;
            set
            {
                if (_barcodeSearch != value)
                {
                    _barcodeSearch = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand SearchByBarcodeCommand => _searchByBarcodeCommand ??= new RelayCommand(async _ => await SearchByBarcode());
        public ICommand OpenProductSelectionCommand => _openProductSelectionCommand ??= new RelayCommand(_ => OpenProductSelection());

        private void UpdateProfitTypeText()
        {
            ProfitTypeText = ShowNetProfit ? 
                Application.Current.TryFindResource("NetProfit")?.ToString() ?? "Net Profit" :
                Application.Current.TryFindResource("GrossProfit")?.ToString() ?? "Gross Profit";
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Removed async keyword since no await operations are performed
        /// </summary>
        private void ClearExpenseCacheIfNeeded()
        {
            // Clear cache if it's older than 5 minutes
            if ((DateTime.Now - _lastCacheClear).TotalMinutes > 5)
            {
                _monthlyExpenseCache.Clear();
                _lastCacheClear = DateTime.Now;
            }
        }

        private async Task<(decimal expenses, int salesCount)> GetMonthlyExpensesAndSalesCount(DateTime monthStart)
        {
            var cacheKey = $"{monthStart:yyyy-MM}";
            
            ClearExpenseCacheIfNeeded();

            if (_monthlyExpenseCache.TryGetValue(cacheKey, out var cached))
            {
                return cached;
            }

            var monthEnd = monthStart.AddMonths(1).AddSeconds(-1);
            
            // ✅ CRITICAL FIX: Remove .Result calls to prevent UI thread blocking
            // Run these tasks in parallel
            var expensesTask = _dbService.GetBusinessExpensesForPeriodAsync(monthStart, monthEnd);
            var salesTask = Task.Run(async () =>
            {
                var sales = await Task.FromResult(_dbService.GetSalesByDateRange(monthStart, monthEnd));
                return sales.Count;
            });

            await Task.WhenAll(expensesTask, salesTask);

            var expenses = await expensesTask;
            var salesCount = await salesTask;

            decimal totalMonthExpenses = expenses.Sum(e =>
            {
                decimal amount = e.Amount;
                switch (e.Frequency)
                {
                    case ExpenseFrequency.Daily:
                        return amount * DateTime.DaysInMonth(monthStart.Year, monthStart.Month);
                    case ExpenseFrequency.Weekly:
                        return amount * 4; // Approximate 4 weeks per month
                    case ExpenseFrequency.Monthly:
                        return amount;
                    case ExpenseFrequency.Quarterly:
                        return amount / 3; // One third of quarterly expense
                    case ExpenseFrequency.Annually:
                        return amount / 12; // One twelfth of annual expense
                    default: // OneTime
                        if (e.Date >= monthStart && e.Date <= monthEnd)
                            return amount;
                        return 0;
                }
            });

            var result = (totalMonthExpenses, salesCount);
            _monthlyExpenseCache[cacheKey] = result;
            return result;
        }

        private async Task<decimal> CalculateProfitForSale(Sale sale, bool isNetProfit)
        {
            decimal profit = 0;
            
            // Calculate basic profit (revenue - cost of goods) using actual FIFO cost basis
            profit = sale.Items.Sum(i => i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice)));

            if (isNetProfit)
            {
                try
                {
                    var (periodStart, periodEnd) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                    
                    // For yearly view, use cached monthly expenses
                    if (SelectedTrendPeriod?.ResourceKey == "TimePeriod_ThisYear")
                    {
                        var monthStart = new DateTime(sale.SaleDate.Year, sale.SaleDate.Month, 1);
                        var monthData = await GetMonthlyExpensesAndSalesCount(monthStart);
                        
                        if (monthData.salesCount > 0)
                        {
                            profit -= monthData.expenses / monthData.salesCount;
                        }
                    }
                    else
                    {
                        // For other periods, use the original calculation
                        var expenses = await Task.Run(() => 
                            _dbService.GetBusinessExpensesForPeriodAsync(periodStart, periodEnd));

                        var periodSales = await Task.Run(() => 
                            _dbService.GetSalesByDateRange(periodStart, periodEnd));

                        if (periodSales.Count > 0)
                        {
                            decimal totalPeriodExpenses = expenses.Sum(e =>
                            {
                                decimal amount = e.Amount;
                                decimal daysBetween = (decimal)(periodEnd - periodStart).TotalDays;

                                switch (e.Frequency)
                                {
                                    case ExpenseFrequency.Daily:
                                        return amount * daysBetween;
                                    case ExpenseFrequency.Weekly:
                                        return amount * (daysBetween / 7.0m);
                                    case ExpenseFrequency.Monthly:
                                        return amount * (daysBetween / 30.0m);
                                    case ExpenseFrequency.Quarterly:
                                        return amount * (daysBetween / 90.0m);
                                    case ExpenseFrequency.Annually:
                                        return amount * (daysBetween / 365.0m);
                                    default: // OneTime
                                        if (e.Date >= periodStart && e.Date <= periodEnd)
                                            return amount;
                                        return 0;
                                }
                            });

                            profit -= totalPeriodExpenses / periodSales.Count;
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error calculating net profit: {ex.Message}");
                }
            }

            return profit;
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load data in parallel
                await Task.WhenAll(
                    LoadMetricsAsync(),
                    LoadTrendDataAsync(),
                    LoadHourlyDistributionAsync(),
                    LoadProfitByDaysAsync(),
                    LoadTopProductsAsync()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading profit stats details: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Loading products...");
                var products = await Task.Run(() => _dbService.GetAllProducts());
                System.Diagnostics.Debug.WriteLine($"Loaded {products.Count} products");
                
                // Store current selection
                var currentSelection = SelectedProduct;
                
                // Update products collection
                Products = new ObservableCollection<Product>(products);
                
                // Restore selection if product still exists in the new list
                if (currentSelection != null)
                {
                    var matchingProduct = products.FirstOrDefault(p => p.Id == currentSelection.Id);
                    if (matchingProduct != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Restoring product selection: {matchingProduct.Name}");
                        SelectedProduct = matchingProduct;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading products: {ex.Message}");
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await Task.Run(() => _dbService.GetAllCategories());
                Categories = new ObservableCollection<Category>(categories);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading categories: {ex.Message}");
            }
        }

        private async Task LoadMetricsAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var previousStartDate = startDate.AddDays(-(endDate - startDate).Days);
                var previousEndDate = startDate.AddSeconds(-1);

                System.Diagnostics.Debug.WriteLine($"Loading {(ShowNetProfit ? "net" : "gross")} profit metrics for period: {startDate:yyyy-MM-dd HH:mm:ss} to {endDate:yyyy-MM-dd HH:mm:ss}");

                // Get sales data
                var currentSales = await GetFilteredSalesAsync(startDate, endDate);
                var previousSales = await GetFilteredSalesAsync(previousStartDate, previousEndDate);

                // Calculate profits
                decimal currentTotalProfit = 0;
                decimal previousTotalProfit = 0;

                foreach (var sale in currentSales)
                {
                    currentTotalProfit += await CalculateProfitForSale(sale, ShowNetProfit);
                }

                foreach (var sale in previousSales)
                {
                    previousTotalProfit += await CalculateProfitForSale(sale, ShowNetProfit);
                }

                TotalProfit = currentTotalProfit;
                System.Diagnostics.Debug.WriteLine($"Calculated {(ShowNetProfit ? "net" : "gross")} profit: {TotalProfit:C2}");

                // Update other metrics
                decimal totalSales = currentSales.Sum(s => s.GrandTotal);
                ProfitMargin = totalSales > 0 ? (TotalProfit / totalSales) * 100 : 0;
                AverageProfit = currentSales.Count > 0 ? TotalProfit / currentSales.Count : 0;

                // Calculate growth indicators
                var profitGrowth = CalculateGrowthPercentage(previousTotalProfit, TotalProfit);
                var marginGrowth = CalculateGrowthPercentage(ProfitMargin, ProfitMargin);
                var avgProfitGrowth = CalculateGrowthPercentage(previousTotalProfit / (previousSales.Count == 0 ? 1 : previousSales.Count), AverageProfit);

                // Set growth indicators
                ProfitGrowth = FormatGrowthText(profitGrowth);
                ProfitGrowthColor = GetGrowthColor(profitGrowth);

                MarginGrowth = FormatGrowthText(marginGrowth);
                MarginGrowthColor = GetGrowthColor(marginGrowth);

                AvgProfitGrowth = FormatGrowthText(avgProfitGrowth);
                AvgProfitGrowthColor = GetGrowthColor(avgProfitGrowth);

                UpdateSubtitle();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadMetricsAsync: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }

        private async Task<List<Sale>> GetFilteredSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // ✅ CRITICAL N+1 FIX: Use async method to prevent UI thread blocking
                var sales = await _dbService.GetSalesByDateRangeAsync(startDate, endDate);
                System.Diagnostics.Debug.WriteLine($"Retrieved {sales.Count} sales from database (async optimized)");

                if (IsProductFilterEnabled && SelectedProduct != null)
                {
                    var productId = SelectedProduct.Id;
                    var productName = SelectedProduct.Name;
                    System.Diagnostics.Debug.WriteLine($"Filtering sales by product: {productName} (ID: {productId})");

                    var filteredSales = sales.Where(s =>
                        s.Items != null &&
                        s.Items.Any(i => i.ProductId == productId)
                    ).ToList();

                    System.Diagnostics.Debug.WriteLine($"Found {filteredSales.Count} sales containing product {productName}");

                    // Detailed verification of filtered data
                    foreach (var sale in filteredSales.Take(3))  // Log first 3 sales for verification
                    {
                        var matchingItems = sale.Items.Where(i => i.ProductId == productId).ToList();
                        System.Diagnostics.Debug.WriteLine($"Sale {sale.Id}: Found {matchingItems.Count} items of product {productName}");
                        foreach (var item in matchingItems)
                        {
                            System.Diagnostics.Debug.WriteLine($"  Item: Quantity={item.Quantity}, UnitPrice={item.UnitPrice:C2}");
                        }
                    }

                    return filteredSales;
                }
                else if (IsCategoryFilterEnabled && SelectedCategory != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Filtering by category: {SelectedCategory.Name}");
                    var categoryProducts = await Task.Run(() => _dbService.GetAllProducts()
                        .Where(p => p.CategoryId == SelectedCategory.Id)
                        .Select(p => p.Id)
                        .ToList());

                    sales = sales.Where(s =>
                        s.Items != null &&
                        s.Items.Any(i => categoryProducts.Contains(i.ProductId))
                    ).ToList();
                    System.Diagnostics.Debug.WriteLine($"After category filter: {sales.Count} sales");
                }

                return sales;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetFilteredSalesAsync: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return new List<Sale>();
            }
        }

        private void UpdateSubtitle()
        {
            var subtitle = Application.Current.TryFindResource("ProfitStatsFor")?.ToString() ?? "Profit Stats for";
            
            // Add profit type to subtitle
            subtitle = $"{(ShowNetProfit ? 
                Application.Current.TryFindResource("NetProfit")?.ToString() ?? "Net" : 
                Application.Current.TryFindResource("GrossProfit")?.ToString() ?? "Gross")} {subtitle}";
            
            if (IsProductFilterEnabled && SelectedProduct != null)
            {
                subtitle += $" {SelectedProduct.Name}";
                System.Diagnostics.Debug.WriteLine($"Updated subtitle for product: {SelectedProduct.Name}");
            }
            else if (IsCategoryFilterEnabled && SelectedCategory != null)
            {
                subtitle += $" {SelectedCategory.Name}";
                System.Diagnostics.Debug.WriteLine($"Updated subtitle for category: {SelectedCategory.Name}");
            }
            else
            {
                subtitle += $" {Application.Current.TryFindResource("AllProducts")?.ToString() ?? "all products"}";
                System.Diagnostics.Debug.WriteLine("Updated subtitle for all products");
            }
            Subtitle = subtitle;
        }

        private async Task LoadTrendDataAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var groupedSales = new List<DatabaseService.SalesTrendPoint>();
                var labels = new List<string>();

                if (SelectedTrendPeriod?.ResourceKey == "TimePeriod_Today")
                {
                    // Group by hour for today
                    groupedSales = sales
                        .GroupBy(s => s.SaleDate.Hour)
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = startDate.Date.AddHours(g.Key),
                            Value = g.Sum(s => s.Items.Sum(i =>
                                i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice))))
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("HH:00"));
                    }
                }
                else if (SelectedTrendPeriod?.ResourceKey == "TimePeriod_ThisYear")
                {
                    // Group by month for year view
                    groupedSales = sales
                        .GroupBy(s => new { s.SaleDate.Year, s.SaleDate.Month })
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Value = g.Sum(s => s.Items.Sum(i =>
                                i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice))))
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("MMM"));
                    }
                }
                else
                {
                    // Default to daily grouping
                    groupedSales = sales
                        .GroupBy(s => s.SaleDate.Date)
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = g.Key,
                            Value = g.Sum(s => s.Items.Sum(i =>
                                i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice))))
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("MMM dd"));
                    }
                }

                var values = new ChartValues<decimal>(groupedSales.Select(p => p.Value));

                TrendSeries = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "Profit",
                        Values = values,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8,
                        LineSmoothness = 0.3,
                        StrokeThickness = 2
                    }
                };

                TrendLabels = labels.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading trend data: {ex.Message}");
            }
        }

        private async Task LoadHourlyDistributionAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var hourlyData = Enumerable.Range(0, 24)
                    .ToDictionary(
                        hour => hour,
                        hour => 0m
                    );

                var profitByHour = sales
                    .GroupBy(s => s.SaleDate.Hour)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(s => s.Items.Sum(i =>
                            i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice))))
                    );

                foreach (var profit in profitByHour)
                {
                    hourlyData[profit.Key] = profit.Value;
                }

                var values = new ChartValues<decimal>();
                var labels = new List<string>();

                for (int hour = 0; hour < 24; hour++)
                {
                    values.Add(hourlyData[hour]);
                    labels.Add($"{hour:00}:00");
                }

                HourlyDistributionSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = Application.Current.TryFindResource("ProfitByHour")?.ToString() ?? "Profit by Hour",
                        Values = values,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"))
                    }
                };

                HourlyLabels = labels.ToArray();

                // Configure the axis for hourly distribution
                HourlyAxisX = new Axis
                {
                    Labels = labels.ToArray(),
                    Separator = new Separator
                    {
                        Step = 1,  // Show every label
                        IsEnabled = true
                    },
                    LabelsRotation = 45  // Rotate labels for better fit
                };

                HourlyAxisY = new Axis
                {
                    LabelFormatter = CurrencyFormatter,
                    MinValue = 0
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading hourly distribution: {ex.Message}");
            }
        }

        private async Task LoadProfitByDaysAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var allDays = Enum.GetValues(typeof(DayOfWeek))
                    .Cast<DayOfWeek>()
                    .ToDictionary(
                        day => day,
                        day => 0m
                    );

                var profitByDay = sales
                    .GroupBy(s => s.SaleDate.DayOfWeek)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(s => s.Items.Sum(i =>
                            i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice))))
                    );

                foreach (var profit in profitByDay)
                {
                    allDays[profit.Key] = profit.Value;
                }

                var values = new ChartValues<decimal>();
                var labels = new List<string>();

                // Ensure days are in correct order (Monday to Sunday)
                var orderedDays = allDays
                    .OrderBy(d => ((int)d.Key + 6) % 7) // Start with Monday
                    .ToList();

                foreach (var day in orderedDays)
                {
                    values.Add(day.Value);
                    // Get localized day name
                    var dayName = System.Globalization.CultureInfo.CurrentCulture.DateTimeFormat.GetDayName(day.Key);
                    labels.Add(dayName);
                }

                ProfitByDaysSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = Application.Current.TryFindResource("ProfitByDay")?.ToString() ?? "Profit by Day",
                        Values = values,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3"))
                    }
                };

                ProfitByDaysLabels = labels.ToArray();

                // Configure the axis for days
                DaysAxisX = new Axis
                {
                    Labels = labels.ToArray(),
                    Separator = new Separator
                    {
                        Step = 1,  // Show every label
                        IsEnabled = true
                    },
                    LabelsRotation = 45  // Rotate labels for better fit
                };

                DaysAxisY = new Axis
                {
                    LabelFormatter = CurrencyFormatter,
                    MinValue = 0
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading profit by days: {ex.Message}");
            }
        }

        private async Task LoadTopProductsAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var topProducts = sales
                    .SelectMany(s => s.Items)
                    .GroupBy(i => i.Product)
                    .Select(g => new TopProductItem
                    {
                        Product = g.Key,
                        TotalQuantity = g.Sum(i => i.Quantity), // ✅ WEIGHT-BASED FIX: Remove (int) cast to preserve decimal quantities
                        TotalSales = g.Sum(i => i.Quantity * i.UnitPrice),
                        TotalProfit = g.Sum(i => i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product.PurchasePrice)))
                    })
                    .OrderByDescending(p => p.TotalProfit)
                    .Take(5)
                    .ToList();

                TopProducts = new ObservableCollection<TopProductItem>(topProducts);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading top products: {ex.Message}");
            }
        }

        private (DateTime startDate, DateTime endDate) GetDateRangeFromPeriod(TrendPeriodItem period)
        {
            var now = DateTime.Now;
            var today = DateTime.Today;

            switch (period?.ResourceKey)
            {
                case "TimePeriod_Today":
                    return (today, now);

                case "TimePeriod_ThisWeek":
                    return (today.AddDays(-7), now);

                case "TimePeriod_ThisMonth":
                    return (new DateTime(today.Year, today.Month, 1), now);

                case "TimePeriod_ThisYear":
                    return (new DateTime(today.Year, 1, 1), now);

                default:
                    return (today, now);
            }
        }

        private decimal CalculateGrowthPercentage(decimal previous, decimal current)
        {
            if (previous == 0)
                return current > 0 ? 100 : 0;

            return ((current - previous) / Math.Abs(previous)) * 100;
        }

        private string FormatGrowthText(decimal growth)
        {
            var prefix = growth >= 0 ? "+" : "";
            return $"{prefix}{growth:F1}%";
        }

        private Brush GetGrowthColor(decimal growth)
        {
            if (growth > 0)
                return new SolidColorBrush(Colors.Green);
            if (growth < 0)
                return new SolidColorBrush(Colors.Red);
            return new SolidColorBrush(Colors.Gray);
        }

        private async void UpdateProductsForCategory(Category category)
        {
            try
            {
                var products = await Task.Run(() => _dbService.GetAllProducts()
                    .Where(p => p.CategoryId == category.Id)
                    .ToList());
                Products = new ObservableCollection<Product>(products);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating products for category: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public Axis HourlyAxisX
        {
            get => _hourlyAxisX;
            set { _hourlyAxisX = value; OnPropertyChanged(); }
        }

        public Axis HourlyAxisY
        {
            get => _hourlyAxisY;
            set { _hourlyAxisY = value; OnPropertyChanged(); }
        }

        public Axis DaysAxisX
        {
            get => _daysAxisX;
            set { _daysAxisX = value; OnPropertyChanged(); }
        }

        public Axis DaysAxisY
        {
            get => _daysAxisY;
            set { _daysAxisY = value; OnPropertyChanged(); }
        }

        private async Task SearchByBarcode()
        {
            if (string.IsNullOrWhiteSpace(BarcodeSearch)) return;

            try
            {
                var product = await Task.Run(() => _dbService.GetProductByBarcode(BarcodeSearch));
                if (product != null)
                {
                    SelectedProduct = product;
                    await LoadDataAsync();
                }
                else
                {
                    // Show snackbar message that product was not found
                    await DialogHost.Show(new SnackbarMessageDialog
                    {
                        Message = "Product not found"
                    }, "RootDialog");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error searching by barcode: {ex.Message}");
                await DialogHost.Show(new SnackbarMessageDialog
                {
                    Message = "Error searching product"
                }, "RootDialog");
            }
            finally
            {
                BarcodeSearch = string.Empty;
            }
        }

        private void OpenProductSelection()
        {
            var productSelectionWindow = new ProductSelectionWindow();
            
            // Set owner to the current window for proper modal behavior
            if (Application.Current.MainWindow != null)
            {
                productSelectionWindow.Owner = Application.Current.MainWindow;
            }

            if (productSelectionWindow.ShowDialog() == true)
            {
                var selectedProduct = productSelectionWindow.SelectedProduct;
                if (selectedProduct != null)
                {
                    SelectedProduct = selectedProduct;
                    IsProductFilterEnabled = true;
                    _ = LoadDataAsync();
                }
            }
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Func<object, bool> _canExecute;

        public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute(parameter);
        }

        public void Execute(object parameter)
        {
            _execute(parameter);
        }
    }
} 