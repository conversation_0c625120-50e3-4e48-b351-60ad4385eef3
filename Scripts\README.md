# POS System Test Data Scripts

This directory contains scripts to populate the POS system database with test data for development and testing purposes.

## Overview

The POS system now starts with **empty data** by default. This provides a clean slate for production use or custom data entry. When you need test data for development or demonstration purposes, you can use the scripts in this directory.

## Quick Start

### Option 1: Using PowerShell (Recommended)
```powershell
.\Scripts\PopulateTestData.ps1
```

### Option 2: Using Batch File
```cmd
.\Scripts\PopulateTestData.bat
```

### Option 3: Manual SQL Execution
If you have SQLite3 installed:
```bash
sqlite3 pos.db ".read Scripts/PopulateTestData.sql"
```

## Configuration Options

### Method 1: App.config Setting
Edit `App.config` and change the `CreateSampleData` setting:
```xml
<!-- Set to false for empty database, true for sample data -->
<add key="CreateSampleData" value="false" />
```

### Method 2: Control File
Create a file named `no_sample_data.txt` in the application directory to disable sample data creation.

### Method 3: Environment Variable
Set the environment variable `POS_NO_SAMPLE_DATA=true` to disable sample data.

## Test Data Contents

When you run the population script, it will add:

### Products (20 items)
- **Beverages**: Coca Cola, Pepsi, Orange Juice, Water, Energy Drinks
- **Snacks**: Potato Chips, Chocolate Bar, Peanuts, Cookies, Granola Bar
- **Electronics**: USB Cables, Phone Chargers, Bluetooth Earbuds, Power Banks, Phone Cases
- **Groceries**: Bread, Milk, Eggs, Bananas, Apples

### Customers (5 people)
- Complete contact information including addresses and phone numbers
- Realistic names and email addresses

### Suppliers (4 companies)
- TechWorld Electronics
- Fresh Foods Wholesale
- Global Beverages Inc
- Office Supplies Plus

### Sales Data (9 transactions)
- Recent sales from today, yesterday, and last week
- Realistic transaction amounts and payment methods
- Mix of cash and card payments
- Various customer associations

### Business Data
- **Expenses**: Rent, utilities, supplies, marketing, maintenance
- **Purchase Orders**: 3 orders with different suppliers
- **Purchase Order Items**: Detailed line items for each order

## Script Features

### Safety Features
- **Confirmation prompts** before clearing existing data
- **Data preservation** of system data (users, roles, settings)
- **Error handling** with clear error messages
- **Database detection** across common locations

### Flexibility
- **Custom database path** support
- **Force mode** to skip confirmations
- **Help documentation** built into scripts
- **Cross-platform** support (PowerShell works on Windows, Linux, macOS)

## Usage Examples

### Basic Usage
```powershell
# Populate with default database location
.\Scripts\PopulateTestData.ps1
```

### Custom Database Path
```powershell
# Specify custom database location
.\Scripts\PopulateTestData.ps1 -DatabasePath "C:\MyPOS\pos.db"
```

### Automated/Scripted Usage
```powershell
# Skip confirmation prompts (useful for automation)
.\Scripts\PopulateTestData.ps1 -Force
```

### Get Help
```powershell
# Show detailed help information
.\Scripts\PopulateTestData.ps1 -Help
```

## Development Workflow

### For Clean Production Setup
1. Keep `CreateSampleData=false` in App.config
2. Deploy application
3. Users start with empty database
4. Add real data as needed

### For Development/Testing
1. Set `CreateSampleData=true` in App.config, OR
2. Run population script after starting with empty data
3. Test features with realistic sample data
4. Re-run script anytime to reset test data

### For Demonstrations
1. Start with empty database
2. Run population script before demo
3. Show realistic business data
4. Reset between demos if needed

## Troubleshooting

### SQLite Not Found
- Download SQLite3 from https://www.sqlite.org/download.html
- Add to PATH or place in application directory
- Ensure executable permissions on Linux/macOS

### Database Not Found
- Check if application has been built and run at least once
- Verify database path in error message
- Use `-DatabasePath` parameter to specify location

### Permission Errors
- Ensure write access to database file
- Close POS application before running scripts
- Run as administrator if necessary

### Data Not Appearing
- Restart POS application after running script
- Check application logs for errors
- Verify script completed successfully

## File Structure

```
Scripts/
├── README.md                 # This documentation
├── PopulateTestData.sql      # Main SQL script with test data
├── PopulateTestData.ps1      # PowerShell execution script
└── PopulateTestData.bat      # Windows batch execution script
```

## Customization

You can modify `PopulateTestData.sql` to:
- Add more products or categories
- Include different customer demographics
- Create specific sales scenarios
- Add custom business expenses
- Modify supplier information

The SQL script is well-commented and organized by data type for easy customization.

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify SQLite3 installation
3. Ensure proper file permissions
4. Check application logs for detailed error messages
