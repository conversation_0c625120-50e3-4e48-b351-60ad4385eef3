<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CashDrawerTransactionsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="800" Height="600">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="{DynamicResource CashDrawerTransactions}"
                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                  Margin="0,0,0,16"/>

        <!-- Drawer Info -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock Text="{DynamicResource OpenedLabel}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,8,4"/>
            <TextBlock Grid.Column="1"
                      Text="{Binding OpenedAt, StringFormat={}{0:g}}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,16,4"/>

            <TextBlock Grid.Column="2"
                      Text="{DynamicResource OpeningBalanceLabel}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,8,4"/>
            <TextBlock Grid.Column="3"
                      Text="{Binding OpeningBalance, StringFormat={}{0:N2} DA}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,0,4"/>

            <TextBlock Grid.Row="1"
                      Text="{DynamicResource StatusLabel}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,8,0"/>
            <TextBlock Grid.Row="1" Grid.Column="1"
                      Text="{Binding Status, Converter={StaticResource DrawerStatusConverter}}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,16,0"/>

            <TextBlock Grid.Row="1" Grid.Column="2"
                      Text="{DynamicResource ExpectedBalanceLabel}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Margin="0,0,8,0"/>
            <TextBlock Grid.Row="1" Grid.Column="3"
                      Text="{Binding ExpectedBalance, StringFormat={}{0:N2} DA}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
        </Grid>

        <!-- Transactions -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding Transactions}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Style="{StaticResource MaterialDesignDataGrid}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="{DynamicResource TimeLabel}"
                                  Binding="{Binding Timestamp, StringFormat={}{0:g}}"
                                  Width="Auto"/>
                <DataGridTextColumn Header="{DynamicResource TypeLabel}"
                                  Binding="{Binding Type, Converter={StaticResource TransactionTypeConverter}, ConverterParameter=Type}"
                                  Width="Auto"/>
                <DataGridTextColumn Header="{DynamicResource AmountLabel}"
                                  Binding="{Binding Amount, StringFormat={}{0:N2} DA}"
                                  Width="Auto"/>
                <DataGridTextColumn Header="{DynamicResource ReasonLabel}"
                                  Binding="{Binding Reason, Converter={StaticResource TransactionTypeConverter}, ConverterParameter=Reason}"
                                  Width="*"/>
                <DataGridTextColumn Header="{DynamicResource ReferenceLabel}"
                                  Binding="{Binding Reference}"
                                  Width="Auto"/>
                <DataGridTextColumn Header="{DynamicResource PerformedByLabel}"
                                  Binding="{Binding PerformedBy.Username}"
                                  Width="Auto"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Close Button -->
        <Button Grid.Row="3"
                Style="{StaticResource MaterialDesignFlatButton}"
                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                HorizontalAlignment="Right"
                Margin="0,16,0,0"
                Content="{DynamicResource Close}"/>
    </Grid>
</UserControl> 