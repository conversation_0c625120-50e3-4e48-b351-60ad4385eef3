using System.Windows;
using System.Windows.Controls;

namespace POSSystem.Views
{
    public partial class OpenDrawerDialog : UserControl
    {
        public OpenDrawerDialog()
        {
            InitializeComponent();
        }

        private void OpenDrawer_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(OpeningBalanceTextBox.Text, out decimal openingBalance))
            {
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(openingBalance, null);
            }
            else
            {
                string message = (string)Application.Current.Resources["PleaseEnterValidOpeningBalance"];
                string title = (string)Application.Current.Resources["InvalidInput"];
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
} 