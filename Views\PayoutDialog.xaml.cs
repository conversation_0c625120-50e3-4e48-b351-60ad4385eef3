using System.Windows;
using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Models;

namespace POSSystem.Views
{
    public partial class PayoutDialog : UserControl
    {
        public PayoutDialog()
        {
            InitializeComponent();
        }

        private void AddPayout_Click(object sender, RoutedEventArgs e)
        {
            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
            {
                string message = (string)Application.Current.Resources["PleaseEnterValidAmount"];
                string title = (string)Application.Current.Resources["InvalidInput"];
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(ReasonTextBox.Text))
            {
                string message = (string)Application.Current.Resources["PleaseEnterReason"];
                string title = (string)Application.Current.Resources["MissingInformation"];
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var payoutInfo = new PayoutInfo
            {
                Amount = amount,
                Reason = ReasonTextBox.Text.Trim(),
                Reference = ReferenceTextBox.Text.Trim()
            };

            MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(payoutInfo, null);
        }
    }
} 