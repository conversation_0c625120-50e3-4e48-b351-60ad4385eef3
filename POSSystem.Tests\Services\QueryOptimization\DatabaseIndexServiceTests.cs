using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using POSSystem.Data;
using POSSystem.Services.QueryOptimization;
using Xunit;
using FluentAssertions;

namespace POSSystem.Tests.Services.QueryOptimization
{
    public class DatabaseIndexServiceTests : IDisposable
    {
        private readonly POSDbContext _context;
        private readonly Mock<ILogger<DatabaseIndexService>> _mockLogger;
        private readonly DatabaseIndexService _indexService;

        public DatabaseIndexServiceTests()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _mockLogger = new Mock<ILogger<DatabaseIndexService>>();
            _indexService = new DatabaseIndexService(_context, _mockLogger.Object);
        }

        #region Database Statistics Tests

        [Fact]
        public async Task GetDatabaseStatsAsync_ShouldReturnValidStatistics()
        {
            // Arrange - Add some test data
            await SeedTestData();

            // Act
            var stats = await _indexService.GetDatabaseStatsAsync();

            // Assert
            stats.Should().NotBeNull();
            stats.TableCounts.Should().NotBeEmpty();
            stats.TableCounts.Should().ContainKey("Products");
            stats.TableCounts.Should().ContainKey("Sales");
            stats.TableCounts.Should().ContainKey("Customers");
            stats.GeneratedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task GetDatabaseStatsAsync_ShouldReturnCorrectCounts()
        {
            // Arrange - Add specific test data
            await SeedTestData();

            // Act
            var stats = await _indexService.GetDatabaseStatsAsync();

            // Assert
            stats.TableCounts["Products"].Should().Be(2);
            stats.TableCounts["Customers"].Should().Be(2);
            stats.TableCounts["Categories"].Should().Be(1);
        }

        #endregion

        #region Index Information Tests

        [Fact]
        public async Task GetIndexInformationAsync_ShouldReturnIndexDetails()
        {
            // Act
            var indexInfo = await _indexService.GetIndexInformationAsync();

            // Assert
            indexInfo.Should().NotBeNull();
            indexInfo.Should().Contain("Database Indexes:");
        }

        [Fact]
        public async Task AreIndexesOptimizedAsync_ShouldReturnFalse_InitiallyForInMemoryDb()
        {
            // Note: In-memory database doesn't support custom indexes like SQLite
            // This test verifies the method handles missing indexes gracefully
            
            // Act
            var result = await _indexService.AreIndexesOptimizedAsync();

            // Assert
            // For in-memory database, this will likely return false since custom indexes aren't supported
            result.Should().BeFalse();
        }

        #endregion

        #region Database Statistics Model Tests

        [Fact]
        public void DatabaseStats_ShouldInitializeCorrectly()
        {
            // Act
            var stats = new DatabaseStats();

            // Assert
            stats.TableCounts.Should().NotBeNull();
            stats.TableCounts.Should().BeEmpty();
            stats.IndexCount.Should().Be(0);
            stats.DatabaseSizeMB.Should().Be(0);
            stats.GeneratedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public void DatabaseStats_ToString_ShouldFormatCorrectly()
        {
            // Arrange
            var stats = new DatabaseStats
            {
                DatabaseSizeMB = 15.75,
                IndexCount = 25,
                TableCounts = new Dictionary<string, int>
                {
                    { "Products", 100 },
                    { "Sales", 500 },
                    { "Customers", 50 }
                }
            };

            // Act
            var result = stats.ToString();

            // Assert
            result.Should().Contain("Database Size: 15.75 MB");
            result.Should().Contain("Indexes: 25");
            result.Should().Contain("Products: 100");
            result.Should().Contain("Sales: 500");
            result.Should().Contain("Customers: 50");
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task GetDatabaseStatsAsync_ShouldHandleEmptyDatabase()
        {
            // Act
            var stats = await _indexService.GetDatabaseStatsAsync();

            // Assert
            stats.Should().NotBeNull();
            stats.TableCounts.Should().NotBeNull();
            foreach (var count in stats.TableCounts.Values)
            {
                count.Should().BeGreaterOrEqualTo(0);
            }
        }

        [Fact]
        public async Task GetIndexInformationAsync_ShouldHandleNoCustomIndexes()
        {
            // Act
            var indexInfo = await _indexService.GetIndexInformationAsync();

            // Assert
            indexInfo.Should().NotBeNull();
            indexInfo.Should().Contain("Database Indexes:");
            // Should not throw exception even if no custom indexes exist
        }

        #endregion

        #region Performance Tests

        [Fact]
        public async Task GetDatabaseStatsAsync_ShouldCompleteQuickly()
        {
            // Arrange
            await SeedTestData();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var stats = await _indexService.GetDatabaseStatsAsync();

            // Assert
            stopwatch.Stop();
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second
            stats.Should().NotBeNull();
        }

        [Fact]
        public async Task AreIndexesOptimizedAsync_ShouldCompleteQuickly()
        {
            // Arrange
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = await _indexService.AreIndexesOptimizedAsync();

            // Assert
            stopwatch.Stop();
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(500); // Should complete within 0.5 seconds
        }

        #endregion

        #region Integration Tests

        [Fact]
        public async Task DatabaseIndexService_ShouldWorkWithRealDbContext()
        {
            // This test verifies that the service works correctly with a real DbContext
            // even if it's in-memory

            // Arrange
            await SeedTestData();

            // Act
            var stats = await _indexService.GetDatabaseStatsAsync();
            var indexInfo = await _indexService.GetIndexInformationAsync();
            var isOptimized = await _indexService.AreIndexesOptimizedAsync();

            // Assert
            stats.Should().NotBeNull();
            indexInfo.Should().NotBeNull();
            // isOptimized can be true or false, just verify it doesn't throw
        }

        #endregion

        #region Helper Methods

        private async Task SeedTestData()
        {
            var category = new POSSystem.Models.Category
            {
                Id = 1,
                Name = "Test Category",
                IsActive = true
            };

            var products = new List<POSSystem.Models.Product>
            {
                new POSSystem.Models.Product
                {
                    Id = 1,
                    Name = "Test Product 1",
                    SKU = "TEST001",
                    CategoryId = 1,
                    SellingPrice = 10.00m,
                    IsActive = true
                },
                new POSSystem.Models.Product
                {
                    Id = 2,
                    Name = "Test Product 2",
                    SKU = "TEST002",
                    CategoryId = 1,
                    SellingPrice = 20.00m,
                    IsActive = true
                }
            };

            var customers = new List<POSSystem.Models.Customer>
            {
                new POSSystem.Models.Customer
                {
                    Id = 1,
                    Name = "Test Customer 1",
                    Email = "<EMAIL>",
                    IsActive = true
                },
                new POSSystem.Models.Customer
                {
                    Id = 2,
                    Name = "Test Customer 2",
                    Email = "<EMAIL>",
                    IsActive = true
                }
            };

            _context.Categories.Add(category);
            _context.Products.AddRange(products);
            _context.Customers.AddRange(customers);
            await _context.SaveChangesAsync();
        }

        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
