using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Diagnostics;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Service responsible for chart generation and visualization.
    /// Extracted from DashboardViewModel to improve code organization and maintainability.
    /// </summary>
    public class ChartService : IChartService
    {
        private readonly IDashboardDataProvider _dataProvider;

        public ChartService(IDashboardDataProvider dataProvider)
        {
            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
        }

        /// <summary>
        /// Creates the main data series for a chart
        /// </summary>
        public Series CreateMainSeries(List<decimal> values, double dateSpan, string parameter = "sales")
        {
            if (values == null || !values.Any())
                return null;

            // Determine appropriate series type based on date span and chart type
            bool isBarChart = dateSpan >= 270 || values.Count <= 5;

            if (isBarChart)
            {
                return new ColumnSeries
                {
                    Values = new ChartValues<decimal>(values),
                    Title = Application.Current?.Resources["SalesData"]?.ToString() ?? "Sales",
                    Fill = GetBrushForParameter(parameter),
                    DataLabels = values.Count <= 10
                };
            }
            else
            {
                return new LineSeries
                {
                    Values = new ChartValues<decimal>(values),
                    Title = Application.Current?.Resources["SalesData"]?.ToString() ?? "Sales",
                    Stroke = GetBrushForParameter(parameter),
                    Fill = new SolidColorBrush(GetColorForParameter(parameter)) { Opacity = 0.2 },
                    PointGeometry = values.Count <= 14 ? DefaultGeometries.Circle : null,
                    PointGeometrySize = values.Count <= 14 ? 8 : 0,
                    LineSmoothness = 0.5,
                    DataLabels = values.Count <= 7
                };
            }
        }

        /// <summary>
        /// Calculates a trend line for a set of values using linear regression
        /// </summary>
        public Series CalculateTrendLine(List<decimal> values)
        {
            if (values == null || values.Count < 3)
                return null;

            try
            {
                // Calculate linear regression
                int n = values.Count;
                decimal sumX = 0;
                decimal sumY = 0;
                decimal sumXY = 0;
                decimal sumX2 = 0;

                for (int i = 0; i < n; i++)
                {
                    sumX += i;
                    sumY += values[i];
                    sumXY += i * values[i];
                    sumX2 += i * i;
                }

                decimal xMean = sumX / n;
                decimal yMean = sumY / n;
                decimal slope = (sumXY - (sumX * sumY / n)) / (sumX2 - (sumX * sumX / n));
                decimal intercept = yMean - (slope * xMean);

                // Generate trend line points
                var trendValues = new List<decimal>();
                for (int i = 0; i < n; i++)
                {
                    decimal y = (slope * i) + intercept;
                    trendValues.Add(Math.Max(0, y)); // Prevent negative values
                }

                return new LineSeries
                {
                    Values = new ChartValues<decimal>(trendValues),
                    Title = Application.Current?.Resources["TrendLine"]?.ToString() ?? "Trend",
                    Stroke = new SolidColorBrush(Colors.Red) { Opacity = 0.8 },
                    Fill = Brushes.Transparent,
                    PointGeometry = null,
                    LineSmoothness = 1,
                    DataLabels = false,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error calculating trend line: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Calculates a moving average for a set of values
        /// </summary>
        public List<decimal> CalculateMovingAverage(List<decimal> values, double dateSpan)
        {
            if (values == null || values.Count < 5)
                return null;

            try
            {
                // Determine window size based on date span and data points
                int windowSize = dateSpan <= 7 ? 3 : (dateSpan <= 30 ? 5 : 7);
                windowSize = Math.Min(windowSize, values.Count - 2); // Ensure we don't exceed data size

                var result = new List<decimal>();
                
                // Initial null values for periods where we can't calculate MA yet
                for (int i = 0; i < windowSize - 1; i++)
                {
                    result.Add(0); // Placeholder value, will be set to transparent in visualization
                }
                
                // Calculate moving average using sliding window
                for (int i = windowSize - 1; i < values.Count; i++)
                {
                    decimal sum = 0;
                    for (int j = 0; j < windowSize; j++)
                    {
                        sum += values[i - j];
                    }
                    result.Add(sum / windowSize);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error calculating moving average: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a chart series for the moving average
        /// </summary>
        public LineSeries CreateMovingAverageSeries(List<decimal> movingAverages)
        {
            if (movingAverages == null || !movingAverages.Any())
                return null;

            return new LineSeries
            {
                Values = new ChartValues<decimal>(movingAverages),
                Title = Application.Current?.Resources["MovingAverage"]?.ToString() ?? "Moving Average",
                Stroke = new SolidColorBrush(Colors.Blue) { Opacity = 0.5 },
                Fill = Brushes.Transparent,
                PointGeometry = null,
                LineSmoothness = 1,
                DataLabels = false
            };
        }

        /// <summary>
        /// Creates a comparison series showing previous period data
        /// </summary>
        public async Task<Series> CreateComparisonSeries(DateTime startDate, DateTime endDate)
        {
            // Calculate previous period
            double dateSpan = (endDate - startDate).TotalDays;
            var previousPeriodStart = startDate.AddDays(-dateSpan);
            var previousPeriodEnd = startDate.AddDays(-1);
            
            // Get previous period data
            var previousSales = await _dataProvider.GetSalesAsync(previousPeriodStart, previousPeriodEnd);
            
            // Convert to dashboard sales which have the Date property
            var dashboardSales = previousSales.ToDashboardSales();

            if (dashboardSales == null || !dashboardSales.Any())
                return null;

            // Aggregate data
            var previousData = AggregateData(dashboardSales, dateSpan);

            return new LineSeries
            {
                Values = new ChartValues<decimal>(previousData),
                Title = Application.Current?.Resources["PreviousPeriod"]?.ToString() ?? "Previous Period",
                Stroke = new SolidColorBrush(Colors.Gray) { Opacity = 0.5 },
                Fill = Brushes.Transparent,
                PointGeometry = null,
                LineSmoothness = 0,
                DataLabels = false,
                StrokeDashArray = new DoubleCollection { 4, 4 }
            };
        }

        /// <summary>
        /// Aggregates sales data for visualization
        /// </summary>
        private List<decimal> AggregateData(List<DashboardSale> sales, double dateSpan)
        {
            // This would contain the aggregation logic specific to the chart service
            // Simplified version
            if (sales == null || !sales.Any())
                return new List<decimal>();

            // Group sales by day and calculate total
            var groupedSales = sales
                .GroupBy(s => s.Date.Date)
                .Select(g => new { Date = g.Key, Total = g.Sum(s => s.GrandTotal) })
                .OrderBy(g => g.Date)
                .ToList();

            return groupedSales.Select(g => g.Total).ToList();
        }

        /// <summary>
        /// Gets the appropriate color for a parameter
        /// </summary>
        private Color GetColorForParameter(string parameter)
        {
            switch (parameter?.ToLower())
            {
                case "sales":
                case "revenue": return Colors.Green;
                case "profit": return Colors.Blue;
                case "margin": return Colors.Purple;
                case "items": return Colors.Orange;
                case "orders": return Colors.Teal;
                default: return Colors.Green;
            }
        }

        /// <summary>
        /// Gets the appropriate brush for a parameter
        /// </summary>
        private SolidColorBrush GetBrushForParameter(string parameter)
        {
            return new SolidColorBrush(GetColorForParameter(parameter));
        }
    }
} 