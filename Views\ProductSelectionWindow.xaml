<?xml version="1.0" encoding="utf-8" ?>
<Window x:Class="POSSystem.Views.ProductSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{DynamicResource SelectProduct}" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource AppWindowStyle}">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Search and Filter Section -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <TextBox x:Name="SearchBox"
                     Grid.Column="0"
                     Style="{StaticResource AppTextBoxStyle}"
                     Height="45"
                     Margin="0,0,10,0"
                     TextChanged="SearchBox_TextChanged"
                     md:HintAssist.Hint="{DynamicResource ProductSearchHint}"/>

            <ComboBox x:Name="CategoryFilter"
                      Grid.Column="1"
                      Style="{StaticResource AppComboBoxStyle}"
                      Height="45"
                      ItemsSource="{Binding Categories}"
                      DisplayMemberPath="Name"
                      SelectionChanged="CategoryFilter_SelectionChanged"
                      md:HintAssist.Hint="{DynamicResource FilterByCategory}"/>
        </Grid>

        <!-- Products List -->
        <Border Grid.Row="1" 
                Style="{StaticResource ContentCardStyle}">
            <DataGrid x:Name="ProductsGrid"
                      ItemsSource="{Binding FilteredProducts}"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      EnableRowVirtualization="True"
                      EnableColumnVirtualization="True"
                      VirtualizingPanel.IsVirtualizing="True"
                      VirtualizingPanel.VirtualizationMode="Recycling"
                      ScrollViewer.IsDeferredScrollingEnabled="True"
                      Style="{StaticResource AppDataGridStyle}"
                      MouseDoubleClick="ProductsGrid_MouseDoubleClick">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="{DynamicResource ProductName}" 
                                      Binding="{Binding Name}"
                                      Width="*"/>
                    <DataGridTextColumn Header="{DynamicResource Category}" 
                                      Binding="{Binding Category.Name}"
                                      Width="150"/>
                    <DataGridTextColumn Header="{DynamicResource PurchasePrice}" 
                                      Binding="{Binding PurchasePrice, StringFormat=C2}"
                                      Width="120"/>
                    <DataGridTextColumn Header="{DynamicResource InStock}"
                                      Binding="{Binding FormattedStock}"
                                      Width="80"/>
                    <DataGridTemplateColumn Width="Auto">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="{DynamicResource Select}"
                                        Click="SelectProduct_Click"
                                        Style="{StaticResource AppPrimaryButtonStyle}"
                                        Height="35"
                                        Width="80"
                                        Margin="4,2"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</Window> 