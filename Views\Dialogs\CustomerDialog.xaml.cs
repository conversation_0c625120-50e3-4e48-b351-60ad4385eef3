using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Markup; // Added for IAddChild
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Customers;
using System.Collections.Generic;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Linq;

namespace POSSystem.Views.Dialogs
{
    public partial class CustomerDialog : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly POSSystem.Services.Customers.CustomerService _customerService;
        private readonly ISettingsService _settingsService;
        private Customer _existingCustomer;
        private bool _isEditMode;
        private string _dialogIdentifier = "RootDialog";
        private BitmapImage _companyLogo;

        // Private fields for card flipping
        private bool _isShowingFrontCard = true;
        private Storyboard _cardFlipAnimation;
        
        // UI elements for the card flip functionality
        private Grid _cardPreviewContainer;
        private Border _frontCardPreview;
        private Border _backCardPreview;
        private TextBlock _btnFlipText;

        public Customer Result { get; private set; }

        public CustomerDialog(Customer customer = null, string dialogIdentifier = "RootDialog")
        {
            InitializeComponent();
            
            // Store the dialog identifier
            _dialogIdentifier = dialogIdentifier;
            
            // Get references to UI elements
            _cardPreviewContainer = (Grid)FindName("cardPreviewContainer");
            _frontCardPreview = (Border)FindName("frontCardPreview");
            _backCardPreview = (Border)FindName("backCardPreview");
            _btnFlipText = (TextBlock)FindName("btnFlipText");
            
            _dbService = new DatabaseService();
            _customerService = new CustomerService(_dbService);
            _settingsService = new SettingsService();
            
            // Initialize card flip animation
            InitializeCardFlipAnimation();
            
            // Load company logo
            LoadCompanyLogo();
            
            // If a customer is provided, we're in edit mode
            if (customer != null)
            {
                _existingCustomer = customer;
                _isEditMode = true;
                LoadCustomerData(customer);
                DialogTitle.Text = (string)Application.Current.Resources["EditCustomer"];
            }
            else
            {
                _isEditMode = false;
                DialogTitle.Text = (string)Application.Current.Resources["AddNewCustomer"];
                chkIsActive.IsChecked = true;
            }
            
            // Update card UI with logo
            UpdateCardWithLogo();
        }

        private void LoadCustomerData(Customer customer)
        {
            txtFirstName.Text = customer.FirstName;
            txtLastName.Text = customer.LastName;
            txtEmail.Text = customer.Email;
            txtPhone.Text = customer.Phone;
            txtAddress.Text = customer.Address;
            txtLoyaltyCode.Text = customer.LoyaltyCode;
            chkIsActive.IsChecked = customer.IsActive;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.Close(_dialogIdentifier);
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtFirstName.Text))
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["FirstNameRequired"],
                        (string)Application.Current.Resources["ValidationError"],
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var customer = _isEditMode ? _existingCustomer : new Customer();
                
                customer.FirstName = txtFirstName.Text.Trim();
                customer.LastName = txtLastName.Text.Trim();
                customer.Email = txtEmail.Text.Trim();
                customer.Phone = txtPhone.Text.Trim();
                customer.Address = txtAddress.Text.Trim();
                customer.LoyaltyCode = txtLoyaltyCode.Text.Trim();
                customer.IsActive = chkIsActive.IsChecked ?? true;

                if (_isEditMode)
                {
                    customer.UpdatedAt = DateTime.Now;
                    _dbService.UpdateCustomer(customer);
                }
                else
                {
                    customer.CreatedAt = DateTime.Now;
                    customer.UpdatedAt = DateTime.Now;
                    _dbService.AddCustomer(customer);
                }

                Result = customer;
                DialogHost.Close(_dialogIdentifier, customer);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    string.Format((string)Application.Current.Resources["ErrorSavingCustomer"], ex.Message),
                    (string)Application.Current.Resources["ErrorTitle"],
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateLoyaltyCode_Click(object sender, RoutedEventArgs e)
        {
            // Use the CustomerService to generate a unique loyalty code
            string code = _customerService.GenerateNewLoyaltyCode();
            
            // Add the LC- prefix if it doesn't already have it
            if (!code.StartsWith("LC-"))
            {
                code = $"LC-{code}";
            }
            
            txtLoyaltyCode.Text = code;
        }

        /// <summary>
        /// Prints a loyalty card for the customer
        /// </summary>
        private void PrintLoyaltyCard(Customer customer)
        {
            try
            {
                // Create a PrintDialog
                PrintDialog printDialog = new PrintDialog();
                
                if (printDialog.ShowDialog() == true)
                {
                    // Create the visual elements for the card
                    Grid cardContainer = new Grid
                    {
                        Width = 600, // Overall container width
                        Height = 400 // Overall container height
                    };
                    
                    // Create the card grid for front and back
                    Grid cardGrid = new Grid();
                    cardGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                    cardGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                    
                    // *** FRONT OF CARD ***
                    Border frontCard = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        BorderThickness = new Thickness(1),
                        CornerRadius = new CornerRadius(8),
                        Margin = new Thickness(20),
                        Width = 400,
                        Height = 160
                    };
                    
                    Grid frontGrid = new Grid();
                    
                    // Beige curved section on left
                    Border leftCurvedSection = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        HorizontalAlignment = HorizontalAlignment.Left,
                        Width = 100,
                        CornerRadius = new CornerRadius(8, 0, 70, 8)
                    };
                    
                    StackPanel logoPanel = new StackPanel
                    {
                        VerticalAlignment = VerticalAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    TextBlock logoText = new TextBlock
                    {
                        Text = "LOGO",
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    logoPanel.Children.Add(logoText);
                    leftCurvedSection.Child = logoPanel;
                    
                    // Content section on right
                    StackPanel contentPanel = new StackPanel
                    {
                        Margin = new Thickness(110, 16, 16, 16)
                    };
                    
                    // Customer name
                    TextBlock nameText = new TextBlock
                    {
                        Text = $"{customer.FirstName} {customer.LastName}",
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        Foreground = Brushes.White
                    };
                    
                    TextBlock memberText = new TextBlock
                    {
                        Text = "Loyalty Member",
                        FontSize = 12,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        Opacity = 0.9,
                        Margin = new Thickness(0, 0, 0, 16)
                    };
                    
                    // Loyalty code with icon
                    StackPanel codePanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    
                    var barcodeIcon = new System.Windows.Controls.Image
                    {
                        Width = 16,
                        Height = 16,
                        Source = new DrawingImage(
                            new GeometryDrawing(
                                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                                new Pen(Brushes.Transparent, 0),
                                Geometry.Parse("M0,0 L0,16 L2,16 L2,0 Z M5,0 L5,16 L6,16 L6,0 Z M9,0 L9,16 L11,16 L11,0 Z M12,0 L12,16 L14,16 L14,0 Z")
                            )
                        )
                    };
                    
                    TextBlock codeText = new TextBlock
                    {
                        Text = customer.LoyaltyCode,
                        Foreground = Brushes.White,
                        Margin = new Thickness(8, 0, 0, 0)
                    };
                    
                    codePanel.Children.Add(barcodeIcon);
                    codePanel.Children.Add(codeText);
                    
                    contentPanel.Children.Add(nameText);
                    contentPanel.Children.Add(memberText);
                    contentPanel.Children.Add(codePanel);
                    
                    frontGrid.Children.Add(leftCurvedSection);
                    frontGrid.Children.Add(contentPanel);
                    frontCard.Child = frontGrid;
                    
                    // *** BACK OF CARD ***
                    Border backCard = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        BorderThickness = new Thickness(1),
                        CornerRadius = new CornerRadius(8),
                        Margin = new Thickness(20),
                        Width = 400,
                        Height = 180
                    };
                    
                    Grid backGrid = new Grid();
                    
                    // Top section with store details
                    DockPanel topPanel = new DockPanel
                    {
                        VerticalAlignment = VerticalAlignment.Top,
                        Margin = new Thickness(16, 16, 16, 0)
                    };
                    
                    StackPanel storePanel = new StackPanel
                    {
                        Width = 80
                    };
                    DockPanel.SetDock(storePanel, Dock.Left);
                    
                    TextBlock storeLogoText = new TextBlock
                    {
                        Text = "LOGO",
                        FontSize = 14,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    TextBlock storeNameText = new TextBlock
                    {
                        Text = "YOUR STORE",
                        FontSize = 8,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        TextWrapping = TextWrapping.Wrap,
                        TextAlignment = TextAlignment.Center
                    };
                    
                    storePanel.Children.Add(storeLogoText);
                    storePanel.Children.Add(storeNameText);
                    
                    Rectangle divider = new Rectangle
                    {
                        Width = 1,
                        Height = 40,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        Margin = new Thickness(8, 0, 8, 0)
                    };
                    DockPanel.SetDock(divider, Dock.Left);
                    
                    StackPanel cardInfoPanel = new StackPanel();
                    
                    TextBlock cardTitleText = new TextBlock
                    {
                        Text = "LOYALTY CARD",
                        FontSize = 12,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3"))
                    };
                    
                    TextBlock cardCodeText = new TextBlock
                    {
                        Text = customer.LoyaltyCode,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.Medium,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    
                    cardInfoPanel.Children.Add(cardTitleText);
                    cardInfoPanel.Children.Add(cardCodeText);
                    
                    topPanel.Children.Add(storePanel);
                    topPanel.Children.Add(divider);
                    topPanel.Children.Add(cardInfoPanel);
                    
                    // Barcode visualization
                    Border barcodeSection = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        CornerRadius = new CornerRadius(0, 0, 8, 8),
                        VerticalAlignment = VerticalAlignment.Bottom,
                        Height = 100
                    };
                    
                    Grid barcodeGrid = new Grid
                    {
                        Margin = new Thickness(16),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    // Create barcode visualization
                    Canvas barcodeCanvas = new Canvas
                    {
                        Height = 44,
                        Width = 250,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Top
                    };
                    
                    // Generate barcode based on loyalty code
                    Random random = new Random(customer.LoyaltyCode.GetHashCode());
                    int totalBarcodeWidth = 250; // Match canvas width
                    int position = 0;
                    
                    // Calculate total width needed for the barcode elements
                    List<int> barWidths = new List<int>();
                    List<int> spaceWidths = new List<int>();
                    int totalWidth = 0;
                    
                    for (int i = 0; i < 40; i++)
                    {
                        int width = random.Next(1, 5);
                        barWidths.Add(width);
                        
                        int space = random.Next(1, 4);
                        spaceWidths.Add(space);
                        
                        totalWidth += width + space;
                    }
                    
                    // Calculate scale factor to fit within canvas
                    double scaleFactor = (double)totalBarcodeWidth / totalWidth;
                    
                    // Create the barcode with proper scaling
                    for (int i = 0; i < 40; i++)
                    {
                        int width = (int)(barWidths[i] * scaleFactor);
                        Rectangle bar = new Rectangle
                        {
                            Width = Math.Max(1, width), // Ensure minimum width of 1
                            Height = 44,
                            Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656"))
                        };
                        
                        Canvas.SetLeft(bar, position);
                        barcodeCanvas.Children.Add(bar);
                        
                        position += width + (int)(spaceWidths[i] * scaleFactor);
                    }
                    
                    TextBlock barcodeNumberText = new TextBlock
                    {
                        Text = customer.LoyaltyCode,
                        FontSize = 16,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Bottom,
                        TextAlignment = TextAlignment.Center
                    };
                    
                    TextBlock scanText = new TextBlock
                    {
                        Text = "Scan this barcode at checkout",
                        FontSize = 10,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#192656")),
                        Opacity = 0.8,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Bottom,
                        Margin = new Thickness(0, 0, 0, 18),
                        TextAlignment = TextAlignment.Center
                    };
                    
                    StackPanel barcodeTextPanel = new StackPanel
                    {
                        HorizontalAlignment = HorizontalAlignment.Center // Center all text elements
                    };
                    
                    barcodeTextPanel.Children.Add(barcodeNumberText);
                    barcodeTextPanel.Children.Add(scanText);
                    
                    barcodeGrid.Children.Add(barcodeCanvas);
                    barcodeGrid.Children.Add(barcodeTextPanel);
                    barcodeSection.Child = barcodeGrid;
                    
                    backGrid.Children.Add(topPanel);
                    backGrid.Children.Add(barcodeSection);
                    backCard.Child = backGrid;
                    
                    // Position the cards in the layout
                    Grid.SetRow(frontCard, 0);
                    Grid.SetRow(backCard, 1);
                    
                    cardGrid.Children.Add(frontCard);
                    cardGrid.Children.Add(backCard);
                    
                    cardContainer.Children.Add(cardGrid);
                    
                    // Create a FixedDocument for printing
                    FixedDocument document = new FixedDocument();
                    PageContent pageContent = new PageContent();
                    FixedPage fixedPage = new FixedPage();
                    
                    // Set the size of the fixed page (A4 in this case)
                    fixedPage.Width = 8.27 * 96; // A4 width in pixels
                    fixedPage.Height = 11.69 * 96; // A4 height in pixels
                    
                    // Center the card on the page with proper margins
                    cardContainer.HorizontalAlignment = HorizontalAlignment.Center;
                    cardContainer.VerticalAlignment = VerticalAlignment.Center;
                    cardContainer.Margin = new Thickness(20);
                    
                    // Add the card to the fixed page
                    fixedPage.Children.Add(cardContainer);
                    
                    ((IAddChild)pageContent).AddChild(fixedPage);
                    document.Pages.Add(pageContent);
                    
                    // Print the document
                    printDialog.PrintDocument(document.DocumentPaginator, "Loyalty Card");
                    
                    MessageBox.Show(
                        "Loyalty card has been sent to the printer.",
                        "Print Successful",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error printing loyalty card: {ex.Message}",
                    "Print Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Initializes the card flip animation storyboard
        /// </summary>
        private void InitializeCardFlipAnimation()
        {
            // Setup transform for card container
            TransformGroup transformGroup = new TransformGroup();
            transformGroup.Children.Add(new ScaleTransform());
            transformGroup.Children.Add(new SkewTransform());
            transformGroup.Children.Add(new RotateTransform());
            transformGroup.Children.Add(new TranslateTransform());
            _cardPreviewContainer.RenderTransform = transformGroup;
            
            // Set the transform origin to center for proper flipping
            _cardPreviewContainer.RenderTransformOrigin = new System.Windows.Point(0.5, 0.5);
            
            // Create the storyboard for card flipping
            _cardFlipAnimation = new Storyboard();
            
            // Create the timeline for the rotation
            var rotateAnimation = new DoubleAnimation
            {
                Duration = new Duration(TimeSpan.FromMilliseconds(500)),
                EasingFunction = new QuarticEase { EasingMode = EasingMode.EaseInOut }
            };
            
            Storyboard.SetTarget(rotateAnimation, _cardPreviewContainer);
            Storyboard.SetTargetProperty(rotateAnimation, new PropertyPath("(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"));
            
            _cardFlipAnimation.Children.Add(rotateAnimation);
            _cardFlipAnimation.Completed += CardFlipAnimation_Completed;
            
            // Ensure the front card is initially visible and the back card is hidden
            _frontCardPreview.Visibility = Visibility.Visible;
            _backCardPreview.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// Handle click on the flip card button
        /// </summary>
        private void FlipCard_Click(object sender, RoutedEventArgs e)
        {
            // Ensure _cardPreviewContainer has a transform group
            if (_cardPreviewContainer.RenderTransform == null || !(_cardPreviewContainer.RenderTransform is TransformGroup))
            {
                TransformGroup transformGroup = new TransformGroup();
                transformGroup.Children.Add(new ScaleTransform());
                transformGroup.Children.Add(new SkewTransform());
                transformGroup.Children.Add(new RotateTransform());
                transformGroup.Children.Add(new TranslateTransform());
                _cardPreviewContainer.RenderTransform = transformGroup;
            }
            
            // Setting from and to values for X scale transform 
            DoubleAnimation animation = (DoubleAnimation)_cardFlipAnimation.Children[0];
            animation.From = 1.0;
            animation.To = 0.0;
            
            // Begin the animation
            _cardFlipAnimation.Begin(this);
        }
        
        /// <summary>
        /// Handler for when the first half of the flip animation completes
        /// </summary>
        private void CardFlipAnimation_Completed(object sender, EventArgs e)
        {
            // Toggle which side is visible
            _isShowingFrontCard = !_isShowingFrontCard;
            
            // Update the visibility of the card sides
            _frontCardPreview.Visibility = _isShowingFrontCard ? Visibility.Visible : Visibility.Collapsed;
            _backCardPreview.Visibility = _isShowingFrontCard ? Visibility.Collapsed : Visibility.Visible;
            
            // Update the flip button text
            _btnFlipText.Text = _isShowingFrontCard ? "View Back" : "View Front";
            
            // Now animate back to normal scale
            TransformGroup transformGroup = (TransformGroup)_cardPreviewContainer.RenderTransform;
            ScaleTransform scaleTransform = (ScaleTransform)transformGroup.Children[0];
            
            DoubleAnimation animation = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = new Duration(TimeSpan.FromMilliseconds(500)),
                EasingFunction = new QuarticEase { EasingMode = EasingMode.EaseInOut }
            };
            
            scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, animation);
        }

        private void LoadCompanyLogo()
        {
            try
            {
                // Get the company logo from settings service
                string logoBase64 = _settingsService.GetCompanyLogo();
                if (!string.IsNullOrEmpty(logoBase64))
                {
                    // Convert Base64 string to BitmapImage
                    byte[] imageData = Convert.FromBase64String(logoBase64);
                    var imageSource = new BitmapImage();

                    using (var ms = new MemoryStream(imageData))
                    {
                        imageSource.BeginInit();
                        imageSource.StreamSource = ms;
                        imageSource.CacheOption = BitmapCacheOption.OnLoad;
                        imageSource.EndInit();
                    }

                    _companyLogo = imageSource;
                }
            }
            catch (Exception)
            {
                // If there's any error loading the logo, set to null
                _companyLogo = null;
            }
        }

        private void UpdateCardWithLogo()
        {
            // Get direct references to the named logo containers using FindName
            var frontLogoContainer = FindName("FrontLogoContainer") as StackPanel;
            var backLogoContainer = FindName("BackLogoContainer") as StackPanel;

            if (_companyLogo != null)
            {
                // If we have a logo, replace the "LOGO" text with an Image
                
                // Front card logo
                if (frontLogoContainer != null)
                {
                    frontLogoContainer.Children.Clear();
                    var frontImage = new Image
                    {
                        Source = _companyLogo,
                        Width = 80,
                        Height = 80,
                        Stretch = Stretch.Uniform
                    };
                    frontLogoContainer.Children.Add(frontImage);
                }
                
                // Back card logo
                if (backLogoContainer != null)
                {
                    backLogoContainer.Children.Clear();
                    var backImage = new Image
                    {
                        Source = _companyLogo,
                        Width = 60,
                        Height = 40,
                        Stretch = Stretch.Uniform,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 4)
                    };
                    backLogoContainer.Children.Add(backImage);
                    
                    // Keep the store name
                    var storeName = new TextBlock
                    {
                        Text = "YOUR STORE", 
                        FontSize = 8, 
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        TextWrapping = TextWrapping.Wrap,
                        TextAlignment = TextAlignment.Center
                    };
                    backLogoContainer.Children.Add(storeName);
                }
            }
        }
        
        /// <summary>
        /// Find a visual child element of a specified type and optionally matching a condition.
        /// </summary>
        private T FindVisualChild<T>(DependencyObject parent, Func<T, bool> condition = null) where T : DependencyObject
        {
            if (parent == null) return null;
            
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                
                if (child is T foundChild)
                {
                    if (condition == null || condition(foundChild))
                    {
                        return foundChild;
                    }
                }
                
                T childOfChild = FindVisualChild<T>(child, condition);
                if (childOfChild != null)
                {
                    return childOfChild;
                }
            }
            
            return null;
        }
    }
} 