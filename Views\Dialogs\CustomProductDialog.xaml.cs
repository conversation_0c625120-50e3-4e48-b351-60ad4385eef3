using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using System.Windows.Threading;

namespace POSSystem.Views.Dialogs
{
    public partial class CustomProductDialog : UserControl
    {
        public Product CustomProduct { get; private set; }
        public decimal Quantity { get; private set; }
        public event EventHandler<CustomProductEventArgs> ProductAdded;
        
        // Reference for the added product message TextBlock
        private TextBlock _addedMessageBlock;
        
        // Static counter for generating unique negative IDs for custom products
        private static int _customProductIdCounter = -1;
        
        public CustomProductDialog()
        {
            InitializeComponent();
            
            // Set default product name
            txtProductName.Text = "Custom Item";
            
            // Set initial focus to price field
            Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() => {
                txtPrice.Focus();
                txtPrice.SelectAll();
            }));
            
            // Add KeyDown event handler for price textbox
            txtPrice.KeyDown += TxtPrice_KeyDown;
            
            // Get reference to the added message TextBlock
            Loaded += (s, e) => {
                _addedMessageBlock = this.FindName("txtProductAdded") as TextBlock;
                
                // Ensure price textbox gets focus after everything is loaded
                Dispatcher.BeginInvoke(DispatcherPriority.Input, new Action(() => {
                    txtPrice.Focus();
                    txtPrice.SelectAll();
                }));
            };
        }
        
        private void TxtPrice_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                if (ValidateAndCreateProduct())
                {
                    DialogHost.Close("SalesDialog", true);
                }
            }
        }
        
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog without adding the product
            DialogHost.Close("SalesDialog", false);
        }
        
        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateAndCreateProduct())
            {
                // Close dialog with success result
                DialogHost.Close("SalesDialog", true);
            }
        }
        
        private bool ValidateAndCreateProduct()
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(txtProductName.Text))
            {
                MessageBox.Show("Please enter a product name", "Input Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtProductName.Focus();
                return false;
            }
            
            if (!decimal.TryParse(txtPrice.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("Please enter a valid price", "Input Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtPrice.Focus();
                return false;
            }
            
            // ✅ WEIGHT-BASED FIX: Use decimal.TryParse to support decimal quantities for weight-based products
            if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
            {
                MessageBox.Show("Please enter a valid quantity", "Input Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtQuantity.Focus();
                return false;
            }
            
            // Generate a unique negative ID for this custom product
            int uniqueId = _customProductIdCounter--;
            
            // Create the custom product with a unique ID and prefixed name
            string productName = txtProductName.Text.Trim();
            if (!productName.StartsWith("Custom: "))
            {
                productName = "Custom: " + productName;
            }
            
            // Create the custom product
            CustomProduct = new Product
            {
                Id = uniqueId, // Use a unique negative ID for each custom product
                Name = productName,
                SellingPrice = price,
                // Set additional properties
                SKU = "CUSTOM-" + Math.Abs(uniqueId),
                PurchasePrice = price * 0.7m, // Estimate purchase price as 70% of selling price
                StockQuantity = 1, // Custom products are always in stock (for calculation purposes)
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
            
            Quantity = quantity;
            
            return true;
        }
        
        private void ResetForm()
        {
            txtProductName.Text = "Custom Item";
            txtPrice.Clear();
            txtQuantity.Text = "1";
            txtPrice.Focus();
        }
        
        private void ShowAddedMessage(string productName)
        {
            if (_addedMessageBlock != null)
            {
                _addedMessageBlock.Text = $"Added: {productName}";
                _addedMessageBlock.Visibility = Visibility.Visible;
            }
            
            // Fade out after 3 seconds (managed by XAML animation)
        }

        private void IncrementQuantity_Click(object sender, RoutedEventArgs e)
        {
            // ✅ WEIGHT-BASED FIX: Support decimal quantities for weight-based products
            if (decimal.TryParse(txtQuantity.Text, out decimal currentQuantity))
            {
                // For custom products, increment by 1 (could be enhanced to support smaller increments for weight-based)
                txtQuantity.Text = (currentQuantity + 1).ToString("0.###");
            }
            else
            {
                txtQuantity.Text = "1";
            }
        }

        private void DecrementQuantity_Click(object sender, RoutedEventArgs e)
        {
            // ✅ WEIGHT-BASED FIX: Support decimal quantities for weight-based products
            if (decimal.TryParse(txtQuantity.Text, out decimal currentQuantity) && currentQuantity > 1)
            {
                txtQuantity.Text = (currentQuantity - 1).ToString("0.###");
            }
            else
            {
                txtQuantity.Text = "1";
            }
        }
    }
    
    public class CustomProductEventArgs : EventArgs
    {
        public Product Product { get; }
        public decimal Quantity { get; }

        public CustomProductEventArgs(Product product, decimal quantity)
        {
            Product = product;
            Quantity = quantity;
        }
    }
} 