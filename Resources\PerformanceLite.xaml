<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Performance Lite UI overrides: remove costly effects/animations and prefer low-cost rendering -->

    <!-- Disable product card hover animations -->
    <Storyboard x:Key="ProductCardHoverEnterAnimation"/>
    <Storyboard x:Key="ProductCardHoverExitAnimation"/>

    <!-- Prefer low-quality scaling for images to reduce GPU cost when resized -->
    <Style TargetType="Image">
        <Setter Property="RenderOptions.BitmapScalingMode" Value="LowQuality"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- Prefer device-pixel snapping and display text formatting globally -->
    <Style TargetType="TextBlock">
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- Card/container styles without DropShadowEffect -->
    <Style x:Key="ContentCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <Style x:Key="PrimaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <Style x:Key="SecondaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SecondaryGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <Style x:Key="LightCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource LightGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
    </Style>

    <Style x:Key="DarkCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource DarkGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <Style x:Key="DashboardSummaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="10"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- DataGrid style without shadow -->
    <Style x:Key="AppDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="md:DataGridAssist.CellPadding" Value="12 8 8 8" xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"/>
        <Setter Property="md:DataGridAssist.ColumnHeaderPadding" Value="12" xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"/>
        <Setter Property="RowBackground" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignBackground}"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="RowHeaderWidth" Value="0"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="ColumnHeaderStyle" Value="{StaticResource AppDataGridColumnHeaderStyle}"/>
    </Style>

    <!-- Primary buttons without shadow -->
    <Style x:Key="AppPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6" xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"/>
    </Style>

    <Style x:Key="AppSuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Background" Value="{StaticResource SuccessGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6" xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"/>
    </Style>

    <!-- Modern product card without shadow on hover -->
    <Style x:Key="ModernProductCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="FocusVisualStyle">
            <Setter.Value>
                <Style>
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <Border BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                        BorderThickness="2" CornerRadius="12"/> 
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FAFBFF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
                <!-- No DropShadowEffect in Performance Lite -->
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>

