using POSSystem.Models;
using POSSystem.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.ViewModels
{
    public class SuppliersViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService = new();
        private ObservableCollection<Supplier> _suppliers;
        private ObservableCollection<Supplier> _allSuppliers;



        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set
            {
                _suppliers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Supplier> AllSuppliers
        {
            get => _allSuppliers;
            set
            {
                _allSuppliers = value;
                OnPropertyChanged();
            }
        }

        public SuppliersViewModel()
        {
            LoadSuppliers();
        }

        public void LoadSuppliers()
        {
            var suppliers = _dbService.GetAllSuppliers();
            AllSuppliers = new ObservableCollection<Supplier>(suppliers);
            Suppliers = new ObservableCollection<Supplier>(suppliers);
        }

        public void AddSupplier(Supplier supplier)
        {
            _dbService.AddSupplier(supplier);
            LoadSuppliers();
        }

        public void UpdateSupplier(Supplier supplier)
        {
            _dbService.UpdateSupplier(supplier);
            LoadSuppliers();
        }

        public void DeleteSupplier(int id)
        {
            _dbService.DeleteSupplier(id);
            LoadSuppliers();
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }
}