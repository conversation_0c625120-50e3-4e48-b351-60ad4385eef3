# POS System

A comprehensive Point of Sale (POS) system built with WPF and .NET 8, featuring modern Material Design UI and extensive business management capabilities.

## Features

### 🛒 Sales Management
- **Real-time Sales Processing**: Fast and intuitive sales interface with barcode scanning support
- **Multiple Payment Methods**: Cash, card, and mobile payment processing
- **Cart Management**: Multiple cart support with item modifications and discounts
- **Receipt Printing**: Professional receipt generation and printing

### 📦 Inventory Management
- **Product Management**: Complete product catalog with categories, suppliers, and pricing
- **Batch Stock Tracking**: Track products by batch with expiration dates
- **Barcode Support**: Multiple barcode formats (EAN-13, Code 128, QR Code)
- **Low Stock Alerts**: Automated notifications for inventory management

### 👥 Customer Management
- **Customer Database**: Comprehensive customer information management
- **Loyalty Programs**: Points-based loyalty system with tier management
- **Customer Analytics**: Purchase history and behavior tracking

### 💰 Financial Management
- **Cash Drawer Management**: Opening/closing balances with reconciliation
- **Business Expense Tracking**: Categorized expense management
- **Sales Reporting**: Comprehensive sales analytics and reporting
- **Invoice Management**: Professional invoice generation and tracking

### 🎨 User Experience
- **Modern Material Design UI**: Clean, intuitive interface
- **Multi-language Support**: English, Arabic, and French localization
- **Theme Customization**: 11+ theme presets with dark/light mode
- **Responsive Design**: Optimized for various screen sizes

### 🔐 Security & Access Control
- **User Management**: Role-based access control system
- **Secure Authentication**: BCrypt password hashing
- **Permission Management**: Granular permission system
- **License Management**: Built-in licensing system

## Technical Stack

- **Framework**: .NET 8 with WPF
- **Database**: SQLite with Entity Framework Core
- **UI Framework**: MaterialDesignInXAML
- **Architecture**: MVVM pattern with proper separation of concerns
- **Authentication**: BCrypt for secure password hashing

## Getting Started

### Prerequisites
- .NET 8.0 Runtime
- Windows 10/11
- SQLite support

### Installation
1. Clone the repository
2. Open `POSSystem.sln` in Visual Studio 2022
3. Restore NuGet packages
4. Build and run the application

### First Run
- Default admin credentials: `admin` / `admin123`
- The application will create a SQLite database on first run
- Configure your business settings in the Settings panel

## Project Structure

```
POSSystem/
├── Models/              # Data models and entities
├── ViewModels/          # MVVM ViewModels
├── Views/               # WPF Views and UserControls
├── Services/            # Business logic and data services
├── Data/                # Entity Framework DbContext
├── Converters/          # WPF value converters
├── Resources/           # Localization and styling resources
├── Migrations/          # Database migrations
└── TestDatabase/        # Test data and performance testing
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the GitHub repository.