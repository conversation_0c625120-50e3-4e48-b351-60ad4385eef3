<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CashDrawerHistoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignBackground}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="{DynamicResource CashDrawerHistory}"
                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                  Grid.Row="0"
                  Margin="0,0,0,16"/>

        <Button Grid.Row="0"
                HorizontalAlignment="Right"
                Style="{StaticResource MaterialDesignFlatButton}"
                Click="BackToDrawer_Click">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="ArrowLeft" 
                                       Margin="0,0,8,0"/>
                <TextBlock Text="{DynamicResource BackToDrawer}"/>
            </StackPanel>
        </Button>

        <!-- Filters -->
        <DockPanel Grid.Row="1" LastChildFill="True" Margin="16,0">
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="Date Range:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <DatePicker x:Name="StartDate" SelectedDateChanged="DateFilter_Changed" Margin="0,0,8,0"/>
                <TextBlock Text="to" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <DatePicker x:Name="EndDate" SelectedDateChanged="DateFilter_Changed" Margin="0,0,16,0"/>
                <Button Content="Select Date Range" Click="SelectDateRange_Click" Style="{StaticResource MaterialDesignOutlinedButton}"/>
            </StackPanel>

            <Button Grid.Column="4"
                    Margin="16,0,0,0"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Click="ExportToExcel_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="FileExcel" 
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="{DynamicResource ExportToExcel}"/>
                </StackPanel>
            </Button>
        </DockPanel>

        <!-- Cash Drawers List -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding CashDrawers}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Style="{StaticResource MaterialDesignDataGrid}">
            <DataGrid.Resources>
                <Style TargetType="DataGridRow">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Status}" Value="Open">
                            <Setter Property="Background" Value="#E8F5E9"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.Resources>
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="{DynamicResource OpenedAt}"
                                  Binding="{Binding OpenedAt, StringFormat={}{0:g}}"
                                  Width="150"/>
                <DataGridTextColumn Header="{DynamicResource OpenedBy}"
                                  Binding="{Binding OpenedBy.Username}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource OpeningBalance}"
                                  Binding="{Binding OpeningBalance, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource Status}"
                                  Binding="{Binding Status}"
                                  Width="80"/>
                <DataGridTextColumn Header="{DynamicResource TotalSales}"
                                  Binding="{Binding TotalSales, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource Payouts}"
                                  Binding="{Binding TotalPayouts, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource ExpectedBalance}"
                                  Binding="{Binding ExpectedBalance, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource ActualBalance}"
                                  Binding="{Binding ActualBalance, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource Difference}"
                                  Binding="{Binding Difference, StringFormat={}{0:N2} DA}"
                                  Width="120"/>
                <DataGridTextColumn Header="{DynamicResource ClosedAt}"
                                  Binding="{Binding ClosedAt, StringFormat={}{0:g}}"
                                  Width="150"/>
                <DataGridTextColumn Header="{DynamicResource ClosedBy}"
                                  Binding="{Binding ClosedBy.Username}"
                                  Width="120"/>
                
                <DataGridTemplateColumn Width="Auto">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Click="ViewTransactions_Click"
                                    ToolTip="{DynamicResource ViewTransactions}">
                                <materialDesign:PackIcon Kind="ViewList"/>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl> 