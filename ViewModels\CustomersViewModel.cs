using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace POSSystem.ViewModels
{
    public class CustomersViewModel : INotifyPropertyChanged
    {
        private readonly RepositoryServiceAdapter _repositoryAdapter;
        private readonly DatabaseService _dbService; // Keep for fallback during migration
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Customer> _allCustomers;

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set
            {
                _customers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Customer> AllCustomers
        {
            get => _allCustomers;
            set
            {
                _allCustomers = value;
                OnPropertyChanged();
            }
        }

        // NEW: Proper dependency injection constructor
        public CustomersViewModel(RepositoryServiceAdapter repositoryAdapter, DatabaseService databaseService)
        {
            _repositoryAdapter = repositoryAdapter ?? throw new ArgumentNullException(nameof(repositoryAdapter));
            _dbService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _ = LoadCustomersAsync();
        }

        // LEGACY: Keep for backward compatibility during migration
        public CustomersViewModel() : this(
            (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter)) ??
                throw new InvalidOperationException("RepositoryServiceAdapter not available from DI"),
            (DatabaseService)App.ServiceProvider?.GetService(typeof(DatabaseService)) ??
                throw new InvalidOperationException("DatabaseService not available from DI"))
        {
            System.Diagnostics.Debug.WriteLine("[CUSTOMERS_VM] Using legacy constructor with DI services");
        }

        public async Task LoadCustomersAsync()
        {
            try
            {
                // ✅ Use repository adapter for better performance
                var customers = await _repositoryAdapter.GetCustomersAsync();
                AllCustomers = new ObservableCollection<Customer>(customers);
                Customers = new ObservableCollection<Customer>(customers);
                System.Diagnostics.Debug.WriteLine($"[CUSTOMERS_VM] Loaded {customers.Count} customers using repository adapter");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CUSTOMERS_VM] Error loading customers: {ex.Message}");
                // Fallback to direct database service
                LoadCustomersLegacy();
            }
        }

        // Legacy method for fallback
        public void LoadCustomersLegacy()
        {
            var customers = _dbService.GetAllCustomers();
            AllCustomers = new ObservableCollection<Customer>(customers);
            Customers = new ObservableCollection<Customer>(customers);
            System.Diagnostics.Debug.WriteLine($"[CUSTOMERS_VM] Loaded {customers.Count} customers using legacy method");
        }

        public async Task AddCustomerAsync(Customer customer)
        {
            _dbService.AddCustomer(customer);
            await LoadCustomersAsync();
        }

        public async Task UpdateCustomerAsync(Customer customer)
        {
            _dbService.UpdateCustomer(customer);
            await LoadCustomersAsync();
        }

        public async Task DeleteCustomerAsync(int id)
        {
            _dbService.DeleteCustomer(id);
            await LoadCustomersAsync();
        }

        // ✅ CRITICAL FIX: Remove .Wait() calls to prevent UI thread blocking
        // Legacy synchronous methods for backward compatibility - use async versions instead
        public void AddCustomer(Customer customer) => _ = AddCustomerAsync(customer);
        public void UpdateCustomer(Customer customer) => _ = UpdateCustomerAsync(customer);
        public void DeleteCustomer(int id) => _ = DeleteCustomerAsync(id);

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }
} 