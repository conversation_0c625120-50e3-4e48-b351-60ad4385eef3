using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;
using POSSystem.Services.Caching;
using POSSystem.Services.QueryOptimization;

namespace POSSystem.Services.BackgroundLoading
{
    /// <summary>
    /// Service for progressive data loading that loads essential data first,
    /// then loads additional data in the background without blocking the UI
    /// </summary>
    public class ProgressiveDataLoader
    {
        private readonly ICacheService _cache;
        private readonly OptimizedQueryService _queryService;
        private readonly ILogger<ProgressiveDataLoader> _logger;
        private readonly Dispatcher _dispatcher;

        public ProgressiveDataLoader(
            ICacheService cache,
            OptimizedQueryService queryService,
            ILogger<ProgressiveDataLoader> logger)
        {
            _cache = cache;
            _queryService = queryService;
            _logger = logger;
            _dispatcher = Dispatcher.CurrentDispatcher;
        }

        /// <summary>
        /// Load data progressively: essential data first, then additional data in background
        /// </summary>
        public async Task<ProgressiveLoadResult<T>> LoadProgressivelyAsync<T>(
            string cacheKey,
            Func<Task<T>> essentialDataLoader,
            Func<Task<T>> fullDataLoader,
            TimeSpan? cacheExpiry = null,
            Action<T> onEssentialDataLoaded = null,
            Action<T> onFullDataLoaded = null)
        {
            var result = new ProgressiveLoadResult<T>();

            try
            {
                // Step 1: Check cache first
                var cachedData = await _cache.GetAsync<T>(cacheKey);
                if (cachedData != null)
                {
                    _logger?.LogDebug("Data loaded from cache for key {CacheKey}", cacheKey);
                    result.Data = cachedData;
                    result.LoadedFromCache = true;
                    
                    // Notify immediately with cached data
                    await _dispatcher.InvokeAsync(() => onFullDataLoaded?.Invoke(cachedData));
                    return result;
                }

                // Step 2: Load essential data first (fast, minimal data)
                _logger?.LogDebug("Loading essential data for key {CacheKey}", cacheKey);
                var essentialData = await essentialDataLoader();
                result.Data = essentialData;
                result.EssentialDataLoaded = true;

                // Notify UI with essential data immediately
                await _dispatcher.InvokeAsync(() => onEssentialDataLoaded?.Invoke(essentialData));

                // Step 3: Load full data in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        _logger?.LogDebug("Loading full data in background for key {CacheKey}", cacheKey);
                        var fullData = await fullDataLoader();
                        
                        // Cache the full data
                        await _cache.SetAsync(cacheKey, fullData, cacheExpiry ?? TimeSpan.FromMinutes(15));
                        
                        // Update UI with full data
                        await _dispatcher.InvokeAsync(() => onFullDataLoaded?.Invoke(fullData));
                        
                        result.Data = fullData;
                        result.FullDataLoaded = true;
                        
                        _logger?.LogDebug("Full data loaded and cached for key {CacheKey}", cacheKey);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Failed to load full data in background for key {CacheKey}", cacheKey);
                        result.BackgroundLoadError = ex.Message;
                    }
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in progressive data loading for key {CacheKey}", cacheKey);
                result.Error = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Load data with pagination support and background preloading of next pages
        /// </summary>
        public async Task<PaginatedLoadResult<T>> LoadPaginatedAsync<T>(
            string baseCacheKey,
            int page,
            int pageSize,
            Func<int, int, Task<IEnumerable<T>>> dataLoader,
            Action<IEnumerable<T>> onDataLoaded = null,
            int preloadPages = 2)
        {
            var result = new PaginatedLoadResult<T>();

            try
            {
                // Load current page
                var cacheKey = $"{baseCacheKey}:page:{page}:size:{pageSize}";
                var currentPageData = await _cache.GetOrSetAsync(
                    cacheKey,
                    () => dataLoader(page, pageSize),
                    TimeSpan.FromMinutes(10)
                );

                result.Data = currentPageData;
                result.Page = page;
                result.PageSize = pageSize;

                // Notify UI immediately
                await _dispatcher.InvokeAsync(() => onDataLoaded?.Invoke(currentPageData));

                // Preload next pages in background
                _ = Task.Run(async () =>
                {
                    for (int i = 1; i <= preloadPages; i++)
                    {
                        try
                        {
                            var nextPage = page + i;
                            var nextPageCacheKey = $"{baseCacheKey}:page:{nextPage}:size:{pageSize}";
                            
                            // Only preload if not already cached
                            if (!await _cache.ExistsAsync(nextPageCacheKey))
                            {
                                _logger?.LogDebug("Preloading page {Page} in background", nextPage);
                                var nextPageData = await dataLoader(nextPage, pageSize);
                                await _cache.SetAsync(nextPageCacheKey, nextPageData, TimeSpan.FromMinutes(10));
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogWarning(ex, "Failed to preload page {Page}", page + i);
                        }
                    }
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in paginated data loading for page {Page}", page);
                result.Error = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Load data with smart refresh - checks if data is stale and refreshes if needed
        /// </summary>
        public async Task<SmartRefreshResult<T>> LoadWithSmartRefreshAsync<T>(
            string cacheKey,
            Func<Task<T>> dataLoader,
            TimeSpan maxAge,
            Action<T> onDataLoaded = null,
            bool forceRefresh = false)
        {
            var result = new SmartRefreshResult<T>();

            try
            {
                if (!forceRefresh)
                {
                    // Check if we have fresh cached data
                    var cachedData = await _cache.GetAsync<T>(cacheKey);
                    if (cachedData != null)
                    {
                        result.Data = cachedData;
                        result.LoadedFromCache = true;
                        
                        await _dispatcher.InvokeAsync(() => onDataLoaded?.Invoke(cachedData));
                        
                        // Check if data is still fresh enough
                        // This would require cache metadata to determine age
                        // For now, assume cached data is acceptable
                        return result;
                    }
                }

                // Load fresh data
                _logger?.LogDebug("Loading fresh data for key {CacheKey}", cacheKey);
                var freshData = await dataLoader();
                
                // Cache the fresh data
                await _cache.SetAsync(cacheKey, freshData, maxAge);
                
                result.Data = freshData;
                result.LoadedFreshData = true;
                
                await _dispatcher.InvokeAsync(() => onDataLoaded?.Invoke(freshData));
                
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in smart refresh for key {CacheKey}", cacheKey);
                result.Error = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Batch load multiple data sets concurrently
        /// </summary>
        public async Task<BatchLoadResult> LoadBatchAsync(params BatchLoadItem[] items)
        {
            var result = new BatchLoadResult();
            var tasks = new List<Task>();

            foreach (var item in items)
            {
                tasks.Add(LoadBatchItemAsync(item, result));
            }

            await Task.WhenAll(tasks);
            return result;
        }

        private async Task LoadBatchItemAsync(BatchLoadItem item, BatchLoadResult result)
        {
            try
            {
                var data = await _cache.GetOrSetAsync(
                    item.CacheKey,
                    item.DataLoader,
                    item.CacheExpiry ?? TimeSpan.FromMinutes(15)
                );

                await _dispatcher.InvokeAsync(() => item.OnDataLoaded?.Invoke(data));
                
                result.SuccessfulLoads.Add(item.CacheKey);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load batch item {CacheKey}", item.CacheKey);
                result.FailedLoads.Add(item.CacheKey, ex.Message);
            }
        }
    }

    #region Result Classes

    public class ProgressiveLoadResult<T>
    {
        public T Data { get; set; }
        public bool LoadedFromCache { get; set; }
        public bool EssentialDataLoaded { get; set; }
        public bool FullDataLoaded { get; set; }
        public string Error { get; set; }
        public string BackgroundLoadError { get; set; }
    }

    public class PaginatedLoadResult<T>
    {
        public IEnumerable<T> Data { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public string Error { get; set; }
    }

    public class SmartRefreshResult<T>
    {
        public T Data { get; set; }
        public bool LoadedFromCache { get; set; }
        public bool LoadedFreshData { get; set; }
        public string Error { get; set; }
    }

    public class BatchLoadResult
    {
        public List<string> SuccessfulLoads { get; set; } = new();
        public Dictionary<string, string> FailedLoads { get; set; } = new();
    }

    public class BatchLoadItem
    {
        public string CacheKey { get; set; }
        public Func<Task<object>> DataLoader { get; set; }
        public TimeSpan? CacheExpiry { get; set; }
        public Action<object> OnDataLoaded { get; set; }
    }

    #endregion
}
