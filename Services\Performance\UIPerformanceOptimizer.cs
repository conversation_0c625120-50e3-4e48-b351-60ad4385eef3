using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using System.Windows.Interop;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// ✅ CRITICAL PERFORMANCE FIX: Comprehensive UI performance optimizer to address low frame rates
    /// </summary>
    public class UIPerformanceOptimizer
    {
        private static readonly Lazy<UIPerformanceOptimizer> _instance = new Lazy<UIPerformanceOptimizer>(() => new UIPerformanceOptimizer());
        public static UIPerformanceOptimizer Instance => _instance.Value;

        private DispatcherTimer _optimizationTimer;
        private bool _isOptimizationActive = false;

        private UIPerformanceOptimizer()
        {
            InitializeOptimizations();
        }

        /// <summary>
        /// Initialize comprehensive UI performance optimizations
        /// </summary>
        private void InitializeOptimizations()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Set optimal rendering settings
                OptimizeRenderingSettings();

                // ✅ PERFORMANCE FIX: Reduce UI update frequency
                OptimizeDispatcherSettings();

                // ✅ PERFORMANCE FIX: Start periodic optimization
                StartPeriodicOptimization();

                Debug.WriteLine("✅ [UI-OPTIMIZER] UI Performance Optimizer initialized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error initializing: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Optimize WPF rendering settings for better frame rates
        /// </summary>
        private void OptimizeRenderingSettings()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Set optimal rendering settings
                if (RenderCapability.Tier >= 0x00020000)
                {
                    // Hardware acceleration available - use default settings
                    Debug.WriteLine("✅ [UI-OPTIMIZER] Hardware acceleration available");
                }
                else
                {
                    // Software rendering fallback
                    Debug.WriteLine("⚠️ [UI-OPTIMIZER] Using software rendering");
                }

                // ✅ PERFORMANCE FIX: Optimize composition target settings
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    // Set optimal frame rate for POS system (30 FPS is sufficient)
                    try
                    {
                        // Reduce rendering frequency for better performance
                        var timelineType = typeof(Timeline);
                        var desiredFrameRateProperty = timelineType.GetField("DesiredFrameRateProperty",
                            System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

                        if (desiredFrameRateProperty?.GetValue(null) is DependencyProperty frameRateProperty)
                        {
                            frameRateProperty.OverrideMetadata(timelineType,
                                new FrameworkPropertyMetadata { DefaultValue = 30 });
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[UI-OPTIMIZER] Could not set frame rate: {ex.Message}");
                    }

                }), DispatcherPriority.ApplicationIdle);

                Debug.WriteLine("✅ [UI-OPTIMIZER] Rendering settings optimized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error optimizing rendering: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Optimize dispatcher settings to reduce UI thread pressure
        /// </summary>
        private void OptimizeDispatcherSettings()
        {
            try
            {
                var dispatcher = Application.Current.Dispatcher;
                
                // ✅ PERFORMANCE FIX: Process dispatcher queue more efficiently
                dispatcher.BeginInvoke(new Action(() =>
                {
                    // Batch low-priority operations
                    dispatcher.Hooks.DispatcherInactive += OnDispatcherInactive;
                    
                }), DispatcherPriority.Background);

                Debug.WriteLine("✅ [UI-OPTIMIZER] Dispatcher settings optimized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error optimizing dispatcher: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Handle dispatcher inactive events to batch operations
        /// </summary>
        private void OnDispatcherInactive(object sender, EventArgs e)
        {
            try
            {
                if (_isOptimizationActive) return;

                _isOptimizationActive = true;

                // ✅ PERFORMANCE FIX: Perform low-priority optimizations when UI is idle
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // Clean up visual tree
                        OptimizeVisualTree();

                        // Force layout updates for pending elements
                        FlushPendingLayoutUpdates();
                    }
                    finally
                    {
                        _isOptimizationActive = false;
                    }
                }), DispatcherPriority.SystemIdle);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error in dispatcher inactive handler: {ex.Message}");
                _isOptimizationActive = false;
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Start periodic UI optimization
        /// </summary>
        private void StartPeriodicOptimization()
        {
            _optimizationTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromSeconds(30) // Every 30 seconds
            };

            _optimizationTimer.Tick += (s, e) =>
            {
                try
                {
                    PerformPeriodicOptimization();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [UI-OPTIMIZER] Error in periodic optimization: {ex.Message}");
                }
            };

            _optimizationTimer.Start();
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Perform periodic UI optimizations
        /// </summary>
        private void PerformPeriodicOptimization()
        {
            try
            {
                // Clean up unused visual resources
                CleanupUnusedResources();

                // Optimize bitmap caches
                OptimizeBitmapCaches();

                // Force garbage collection of UI elements
                ForceUIGarbageCollection();

                Debug.WriteLine("✅ [UI-OPTIMIZER] Periodic optimization completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error in periodic optimization: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Optimize visual tree for better rendering performance
        /// </summary>
        private void OptimizeVisualTree()
        {
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.IsLoaded && window.IsVisible)
                    {
                        // ✅ PERFORMANCE FIX: Optimize window rendering
                        OptimizeWindowRendering(window);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error optimizing visual tree: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Optimize individual window rendering
        /// </summary>
        private void OptimizeWindowRendering(Window window)
        {
            try
            {
                // ✅ PERFORMANCE FIX: Optimize bitmap cache settings
                if (window.CacheMode == null)
                {
                    window.CacheMode = new BitmapCache
                    {
                        RenderAtScale = 1.0,
                        SnapsToDevicePixels = true,
                        EnableClearType = false // Disable for better performance
                    };
                }

                // ✅ PERFORMANCE FIX: Optimize text rendering
                TextOptions.SetTextFormattingMode(window, TextFormattingMode.Ideal);
                TextOptions.SetTextRenderingMode(window, TextRenderingMode.Auto);
                TextOptions.SetTextHintingMode(window, TextHintingMode.Auto);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error optimizing window rendering: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Flush pending layout updates to prevent accumulation
        /// </summary>
        private void FlushPendingLayoutUpdates()
        {
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.IsLoaded && window.IsVisible)
                    {
                        window.UpdateLayout();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error flushing layout updates: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Clean up unused UI resources
        /// </summary>
        private void CleanupUnusedResources()
        {
            try
            {
                // Clear unused resource dictionaries
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.Resources.Count > 100) // Arbitrary threshold
                    {
                        // Clear non-essential resources
                        var keysToRemove = new System.Collections.Generic.List<object>();
                        foreach (var key in window.Resources.Keys)
                        {
                            if (window.Resources[key] is Brush || window.Resources[key] is Style)
                            {
                                // Keep essential resources
                                continue;
                            }
                            keysToRemove.Add(key);
                        }

                        foreach (var key in keysToRemove)
                        {
                            window.Resources.Remove(key);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error cleaning up resources: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Optimize bitmap caches for better memory usage
        /// </summary>
        private void OptimizeBitmapCaches()
        {
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.CacheMode is BitmapCache cache)
                    {
                        // Reset cache to free memory
                        window.CacheMode = null;
                        window.CacheMode = new BitmapCache
                        {
                            RenderAtScale = 1.0,
                            SnapsToDevicePixels = true,
                            EnableClearType = false
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error optimizing bitmap caches: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Force garbage collection of UI elements
        /// </summary>
        private void ForceUIGarbageCollection()
        {
            try
            {
                // Force collection of UI-related objects
                GC.Collect(0, GCCollectionMode.Optimized);
                GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error in UI garbage collection: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose of the optimizer
        /// </summary>
        public void Dispose()
        {
            try
            {
                _optimizationTimer?.Stop();
                _optimizationTimer = null;

                if (Application.Current?.Dispatcher != null)
                {
                    Application.Current.Dispatcher.Hooks.DispatcherInactive -= OnDispatcherInactive;
                }

                Debug.WriteLine("✅ [UI-OPTIMIZER] UI Performance Optimizer disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-OPTIMIZER] Error disposing: {ex.Message}");
            }
        }
    }
}
