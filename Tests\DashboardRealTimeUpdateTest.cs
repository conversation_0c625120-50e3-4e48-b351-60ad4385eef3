using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Services.RealTime;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using Moq;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test to verify that dashboard real-time updates are working correctly
    /// </summary>
    [TestClass]
    public class DashboardRealTimeUpdateTest
    {
        private Mock<IDatabaseService> _mockDatabaseService;
        private Mock<DashboardQueryService> _mockQueryService;
        private DashboardUpdateService _realTimeService;
        private RefactoredDashboardViewModel _dashboardViewModel;
        private Mock<IDashboardDataProvider> _mockDataProvider;

        [TestInitialize]
        public void Setup()
        {
            // Setup mocks
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockQueryService = new Mock<DashboardQueryService>();
            _mockDataProvider = new Mock<IDashboardDataProvider>();

            // Setup mock query service to return test metrics
            _mockQueryService.Setup(x => x.GetEssentialMetricsAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(new EssentialMetrics
                {
                    TotalSales = 1500.50m,
                    TransactionCount = 25,
                    UnpaidSalesCount = 3
                });

            // Create real-time service
            _realTimeService = new DashboardUpdateService(_mockDatabaseService.Object, _mockQueryService.Object);
        }

        [TestMethod]
        public async Task RealTimeUpdates_ShouldUpdateDashboardMetrics()
        {
            // Arrange
            var dataService = new Mock<DashboardDataService>(_mockDataProvider.Object);
            var chartService = new Mock<ChartService>(_mockDataProvider.Object);
            var metricsService = new Mock<MetricsCalculationService>();
            var parameterManager = new Mock<ChartParameterManager>();
            // Create dashboard view model with real-time service
            _dashboardViewModel = new RefactoredDashboardViewModel(
                _mockDataProvider.Object,
                dataService.Object,
                chartService.Object,
                metricsService.Object,
                parameterManager.Object,
                _realTimeService // This should connect the real-time updates
            );

            // Set initial values
            decimal initialSales = _dashboardViewModel.TodaySales;
            int initialUnpaidCount = _dashboardViewModel.UnpaidSalesCount;

            // Act - Force an update from the real-time service
            await _realTimeService.ForceUpdateAsync();

            // Wait a bit for the async update to complete
            await Task.Delay(100);

            // Assert - Check if the dashboard was updated
            // Note: This test verifies the connection is established
            // The actual values depend on the mock setup
            Assert.IsNotNull(_dashboardViewModel, "Dashboard view model should be created");
            
            // Verify that the real-time service is connected
            // We can't easily test the actual update without a full integration test
            // but we can verify the service is properly injected and events are subscribed
            Console.WriteLine($"Dashboard created with real-time service: {_realTimeService != null}");
            Console.WriteLine($"Initial sales: {initialSales}, Current sales: {_dashboardViewModel.TodaySales}");
            Console.WriteLine($"Initial unpaid: {initialUnpaidCount}, Current unpaid: {_dashboardViewModel.UnpaidSalesCount}");
        }

        [TestMethod]
        public void DashboardUpdateService_ShouldFireMetricsUpdatedEvent()
        {
            // Arrange
            bool eventFired = false;
            DashboardMetricsUpdatedEventArgs receivedArgs = null;

            _realTimeService.MetricsUpdated += (sender, args) =>
            {
                eventFired = true;
                receivedArgs = args;
            };

            // Act - Force an update
            Task.Run(async () => await _realTimeService.ForceUpdateAsync()).Wait();

            // Wait for the event
            Task.Delay(200).Wait();

            // Assert
            Assert.IsTrue(eventFired, "MetricsUpdated event should be fired");
            Assert.IsNotNull(receivedArgs, "Event args should not be null");
            Console.WriteLine($"Event fired with sales: {receivedArgs?.DaySales:C}");
        }

        [TestCleanup]
        public void Cleanup()
        {
            _realTimeService?.Dispose();
            _dashboardViewModel?.Dispose();
        }
    }

    /// <summary>
    /// Mock implementation of EssentialMetrics for testing
    /// </summary>
    public class EssentialMetrics
    {
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public int UnpaidSalesCount { get; set; }
    }
}
