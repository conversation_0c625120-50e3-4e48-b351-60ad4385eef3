using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// CORRECT implementation of Customer repository
    /// SAFE APPROACH: Works alongside existing DatabaseService
    /// </summary>
    public class CustomerRepository : ICustomerRepository
    {
        private readonly POSDbContext _context;
        private readonly ILogger<CustomerRepository> _logger;

        public CustomerRepository(POSDbContext context, ILogger<CustomerRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
        }

        public async Task<Customer> GetByIdAsync(int id)
        {
            try
            {
                return await _context.Customers
                    .Include(c => c.Sales)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting customer {CustomerId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetPagedAsync(int page, int pageSize)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.FirstName)
                    .ThenBy(c => c.LastName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting paged customers page {Page}, size {PageSize}", page, pageSize);
                throw;
            }
        }

        public async Task<Customer> GetByLoyaltyCodeAsync(string loyaltyCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(loyaltyCode))
                    return null;

                return await _context.Customers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.LoyaltyCode == loyaltyCode && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting customer by loyalty code {LoyaltyCode}", loyaltyCode);
                throw;
            }
        }

        public async Task<Customer> GetByEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return null;

                return await _context.Customers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.Email == email && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting customer by email {Email}", email);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> SearchAsync(string searchTerm, int maxResults = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<Customer>();

                return await _context.Customers
                    .Where(c => c.IsActive && 
                               (c.FirstName.Contains(searchTerm) ||
                                c.LastName.Contains(searchTerm) ||
                                c.Email.Contains(searchTerm) ||
                                c.Phone.Contains(searchTerm) ||
                                c.LoyaltyCode.Contains(searchTerm)))
                    .Take(maxResults)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error searching customers with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<Customer> CreateAsync(Customer customer)
        {
            try
            {
                customer.CreatedAt = DateTime.Now;
                customer.UpdatedAt = DateTime.Now;
                customer.IsActive = true;

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();
                
                return customer;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating customer {CustomerName}", $"{customer.FirstName} {customer.LastName}");
                throw;
            }
        }

        public async Task UpdateAsync(Customer customer)
        {
            try
            {
                var existingCustomer = await _context.Customers.FindAsync(customer.Id);
                if (existingCustomer == null)
                    throw new InvalidOperationException($"Customer with ID {customer.Id} not found");

                // Update properties
                existingCustomer.FirstName = customer.FirstName;
                existingCustomer.LastName = customer.LastName;
                existingCustomer.Email = customer.Email;
                existingCustomer.Phone = customer.Phone;
                existingCustomer.Address = customer.Address;
                existingCustomer.LoyaltyCode = customer.LoyaltyCode;
                existingCustomer.LoyaltyPoints = customer.LoyaltyPoints;
                existingCustomer.IsActive = customer.IsActive;
                existingCustomer.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating customer {CustomerId}", customer.Id);
                throw;
            }
        }

        public async Task DeleteAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer != null)
                {
                    _context.Customers.Remove(customer);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error deleting customer {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> SoftDeleteAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer != null)
                {
                    customer.IsActive = false;
                    customer.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error soft deleting customer {CustomerId}", id);
                throw;
            }
        }

        public async Task UpdateLoyaltyPointsAsync(int customerId, int points)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer != null)
                {
                    customer.LoyaltyPoints = points;
                    customer.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating loyalty points for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetTopLoyaltyCustomersAsync(int limit = 10)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderByDescending(c => c.LoyaltyPoints)
                    .Take(limit)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting top loyalty customers with limit {Limit}", limit);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.FirstName)
                    .ThenBy(c => c.LastName)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting active customers");
                throw;
            }
        }

        public async Task<int> GetTotalCountAsync()
        {
            try
            {
                return await _context.Customers.CountAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting total customer count");
                throw;
            }
        }

        public async Task<int> GetActiveCountAsync()
        {
            try
            {
                return await _context.Customers.CountAsync(c => c.IsActive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting active customer count");
                throw;
            }
        }

        public async Task<decimal> GetTotalCustomerValueAsync()
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .SumAsync(c => c.TotalSpent);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating total customer value");
                throw;
            }
        }

        public async Task<bool> ExistsByEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return false;

                return await _context.Customers.AnyAsync(c => c.Email == email);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if customer exists by email {Email}", email);
                throw;
            }
        }

        public async Task<bool> ExistsByLoyaltyCodeAsync(string loyaltyCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(loyaltyCode))
                    return false;

                return await _context.Customers.AnyAsync(c => c.LoyaltyCode == loyaltyCode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if customer exists by loyalty code {LoyaltyCode}", loyaltyCode);
                throw;
            }
        }

        public async Task<bool> ExistsByIdAsync(int id)
        {
            try
            {
                return await _context.Customers.AnyAsync(c => c.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if customer exists by ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer> GetByPhoneAsync(string phone)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phone))
                    return null;

                return await _context.Customers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.Phone == phone && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting customer by phone {Phone}", phone);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetAllAsync()
        {
            try
            {
                // ⚠️ WARNING: Use with caution - consider pagination
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.FirstName)
                    .ThenBy(c => c.LastName)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting all customers");
                throw;
            }
        }
    }
}
