﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class MakeCashTransactionReasonNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5323), new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5283), new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5324) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5337), new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5331), new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(5338) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 109, DateTimeKind.Local).AddTicks(4068));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(8343));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(8348));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 105, DateTimeKind.Local).AddTicks(8352));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8133));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8139));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8152));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8156));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8161));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8165));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8169));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 34, 16, 115, DateTimeKind.Local).AddTicks(8209));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 34, 16, 106, DateTimeKind.Local).AddTicks(6508), new DateTime(2025, 2, 5, 12, 34, 16, 106, DateTimeKind.Local).AddTicks(6517) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5212), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5183), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5213) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5221), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5219), new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(5221) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 53, DateTimeKind.Local).AddTicks(9452));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6821));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6825));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 51, DateTimeKind.Local).AddTicks(6828));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2588));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2593));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2602));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2606));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2610));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2614));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2617));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 27, 34, 56, DateTimeKind.Local).AddTicks(2621));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 27, 34, 52, DateTimeKind.Local).AddTicks(391), new DateTime(2025, 2, 5, 12, 27, 34, 52, DateTimeKind.Local).AddTicks(400) });
        }
    }
}
