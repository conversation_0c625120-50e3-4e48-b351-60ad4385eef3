<?xml version="1.0" encoding="utf-8"?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- Theme Settings -->
    <system:String x:Key="ThemeSettings">Theme Settings</system:String>
    <system:String x:Key="ThemeMode">Theme Mode</system:String>
    <system:String x:Key="DarkThemeToggle">Toggle Dark Theme</system:String>
    <system:String x:Key="ThemeColor">Theme Color</system:String>
    <system:String x:Key="SalesLayoutTheme">Sales Layout</system:String>
    <system:String x:Key="ChangeLayout">Change Layout</system:String>
    <system:String x:Key="LightMode">Light</system:String>
    <system:String x:Key="DarkMode">Dark</system:String>
    <system:String x:Key="ThemePresets">Theme Presets</system:String>
    <system:String x:Key="SelectTheme">Select Theme</system:String>
    <system:String x:Key="ThemePreview">Theme Preview</system:String>
    <system:String x:Key="SampleHeading">Sample Heading</system:String>
    <system:String x:Key="SampleText">This is a sample text to preview how your selected theme looks.</system:String>
    <system:String x:Key="SampleButton">Primary Button</system:String>
    <system:String x:Key="SampleOutlinedButton">Outlined Button</system:String>
    <system:String x:Key="SampleInput">Input Field</system:String>
    <system:String x:Key="PrimaryColor">Primary Color</system:String>
</ResourceDictionary> 