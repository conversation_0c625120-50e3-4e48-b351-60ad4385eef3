<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Layouts.SalesViewModern"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Layouts"
             xmlns:views="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource MaterialDesignBackground}">

    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:LengthToVisibilityConverter x:Key="LengthToVisibilityConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStockColorConverter x:Key="BooleanToStockColorConverter"/>
        <converters:TotalStockConverter x:Key="TotalStockConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <converters:StockDisplayConverter x:Key="StockDisplayConverter"/>
        
        <!-- Colors -->
        <SolidColorBrush x:Key="SearchBackgroundBrush" Color="#f5f7fa"/>
        <SolidColorBrush x:Key="PrimaryActionBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="PopularItemsBrush" Color="#673AB7"/>
        <SolidColorBrush x:Key="RecentSalesBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="DiscountBrush" Color="#E91E63"/>
        <SolidColorBrush x:Key="NeutralBrush" Color="#607D8B"/>
        <SolidColorBrush x:Key="FavoritesBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="PageBackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="CartBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="ProductCardBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="SectionBackgroundBrush" Color="#FFFFFF"/>
    </UserControl.Resources>

    <Grid Margin="16" Background="{StaticResource PageBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header with Search -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Background="{StaticResource HeaderBackgroundBrush}" UniformCornerRadius="12" materialDesign:ElevationAssist.Elevation="Dp1">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="{DynamicResource ProductSearchHint}"
                         materialDesign:TextFieldAssist.HasClearButton="True"
                         materialDesign:TextFieldAssist.PrefixText="🔍"
                         Height="56"
                         FontSize="16"
                         Margin="0,0,16,0"/>

                <Button Grid.Column="1"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Background="{StaticResource PrimaryActionBrush}"
                        Foreground="White"
                        Height="56"
                        Width="56"
                        Padding="4">
                    <materialDesign:PackIcon Kind="Plus" Width="28" Height="28"/>
                </Button>
            </Grid>
        </materialDesign:Card>
        
        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="240"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="380"/>
            </Grid.ColumnDefinitions>
            
            <!-- Left Sidebar - Categories and Filters -->
            <materialDesign:Card Grid.Column="0" Background="{StaticResource SectionBackgroundBrush}" UniformCornerRadius="12" Margin="0,0,16,0" materialDesign:ElevationAssist.Elevation="Dp1">
                <DockPanel Margin="16">
                    <TextBlock Text="{DynamicResource Categories}" 
                               FontSize="18" 
                               FontWeight="Medium" 
                               Margin="0,0,0,16" 
                               DockPanel.Dock="Top"/>
                                   
                    <StackPanel DockPanel.Dock="Top" Margin="0,0,0,16">
                        <Button Style="{StaticResource MaterialDesignFlatButton}" 
                                Margin="0,0,0,8" 
                                Height="40" 
                                HorizontalContentAlignment="Left"
                                Background="{StaticResource PopularItemsBrush}"
                                Foreground="White">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Popular}"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignFlatButton}" 
                                Margin="0,0,0,8" 
                                Height="40" 
                                HorizontalContentAlignment="Left"
                                Background="{StaticResource FavoritesBrush}"
                                Foreground="White">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Heart" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Favorites}"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignFlatButton}" 
                                Margin="0,0,0,8" 
                                Height="40" 
                                HorizontalContentAlignment="Left"
                                Background="{StaticResource NeutralBrush}"
                                Foreground="White">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FilterRemove" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource ClearFilter}"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                    
                    <ListBox BorderThickness="0" Background="Transparent">
                        <ListBoxItem>Category 1</ListBoxItem>
                        <ListBoxItem>Category 2</ListBoxItem>
                        <ListBoxItem>Category 3</ListBoxItem>
                    </ListBox>
                </DockPanel>
            </materialDesign:Card>
            
            <!-- Center - Products Grid -->
            <materialDesign:Card Grid.Column="1" Background="{StaticResource SectionBackgroundBrush}" UniformCornerRadius="12" Margin="0,0,16,0" materialDesign:ElevationAssist.Elevation="Dp1">
                <Grid Margin="16">
                    <!-- Modern Product Cards List -->
                    <ListBox BorderThickness="0" Background="Transparent">
                        <ListBox.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel />
                            </ItemsPanelTemplate>
                        </ListBox.ItemsPanel>
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <materialDesign:Card Width="220" 
                                                   Height="300"
                                                   Background="{StaticResource ProductCardBackgroundBrush}"
                                                   UniformCornerRadius="12"
                                                   Margin="8"
                                                   materialDesign:ElevationAssist.Elevation="Dp2">
                                    <!-- Modern Product Card Placeholder -->
                                    <Grid Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="160"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <!-- Image Placeholder -->
                                        <Border Grid.Row="0" Background="#F5F5F5" CornerRadius="8"/>
                                        
                                        <!-- Product Name -->
                                        <TextBlock Grid.Row="1" Text="Product Name" FontWeight="Medium" Margin="0,12,0,4"/>
                                        
                                        <!-- Price -->
                                        <TextBlock Grid.Row="2" Text="1,500.00 DA" FontWeight="Bold" Margin="0,0,0,12" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        
                                        <!-- Add Button -->
                                        <Button Grid.Row="3" Style="{StaticResource MaterialDesignFlatButton}" Background="{StaticResource PrimaryActionBrush}" Foreground="White">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20" Margin="0,0,8,0"/>
                                                <TextBlock Text="{DynamicResource AddToCart}"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </materialDesign:Card>
            
            <!-- Right - Cart -->
            <materialDesign:Card Grid.Column="2" Background="{StaticResource CartBackgroundBrush}" UniformCornerRadius="12" materialDesign:ElevationAssist.Elevation="Dp1">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Customer Bar -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <materialDesign:PackIcon Kind="Account" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Grid.Column="1" Text="Select Customer" VerticalAlignment="Center"/>
                        <Button Grid.Column="2" Style="{StaticResource MaterialDesignFlatButton}" Width="36" Height="36" Padding="4">
                            <materialDesign:PackIcon Kind="AccountPlus" Width="20" Height="20"/>
                        </Button>
                    </Grid>
                    
                    <!-- Cart Items -->
                    <DockPanel Grid.Row="1">
                        <TabControl DockPanel.Dock="Top" Style="{StaticResource MaterialDesignTabControl}" materialDesign:ColorZoneAssist.Mode="PrimaryLight" Height="48">
                            <TabItem Header="Cart 1"/>
                        </TabControl>
                        
                        <ListView BorderThickness="0" Background="Transparent">
                            <ListViewItem>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="Product Name" FontWeight="Medium"/>
                                        <TextBlock Text="1,500.00 DA" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="8,0">
                                        <Button Style="{StaticResource MaterialDesignIconButton}" Width="28" Height="28" Padding="0">
                                            <materialDesign:PackIcon Kind="Minus" Width="16" Height="16"/>
                                        </Button>
                                        <TextBlock Text="1" Width="24" TextAlignment="Center" VerticalAlignment="Center"/>
                                        <Button Style="{StaticResource MaterialDesignIconButton}" Width="28" Height="28" Padding="0">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                    
                                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" Width="28" Height="28" Padding="0">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </Grid>
                            </ListViewItem>
                        </ListView>
                    </DockPanel>
                    
                    <!-- Totals and Payment -->
                    <Grid Grid.Row="2" Margin="0,16,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Subtotal -->
                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="{DynamicResource Subtotal}" FontWeight="Medium"/>
                            <TextBlock Grid.Column="1" Text="1,500.00 DA" FontWeight="Medium"/>
                        </Grid>
                        
                        <!-- Grand Total -->
                        <Border Grid.Row="1" Background="#121212" CornerRadius="8" Padding="12,8" Margin="0,0,0,16">
                            <TextBlock Text="1,500.00 DA" Foreground="#00FF00" FontFamily="Consolas" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        
                        <!-- Pay Button -->
                        <Button Grid.Row="2" Style="{StaticResource MaterialDesignFlatButton}" Height="48" Background="{StaticResource PrimaryActionBrush}" Foreground="White">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CreditCard" Width="24" Height="24" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource ProcessPayment}" FontSize="16"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl> 