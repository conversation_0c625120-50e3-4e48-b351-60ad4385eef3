using System;
using System.Collections.Generic;
using System.Linq;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Services
{
    /// <summary>
    /// Helper class for testing and validating the permission system
    /// </summary>
    public class PermissionTestHelper
    {
        private readonly DatabaseService _dbService;
        private readonly UserPermissionsService _permissionsService;

        public PermissionTestHelper()
        {
            _dbService = new DatabaseService();
            _permissionsService = new UserPermissionsService(_dbService);
        }

        /// <summary>
        /// Tests all permission mappings for a specific user
        /// </summary>
        public void TestUserPermissions(int userId)
        {
            var user = _dbService.GetUserById(userId);
            if (user == null)
            {
                System.Diagnostics.Debug.WriteLine($"User with ID {userId} not found");
                return;
            }

            _permissionsService.Initialize(user);
            var permissions = _permissionsService.GetUserPermissions(userId);

            System.Diagnostics.Debug.WriteLine($"=== Permission Test for User: {user.Username} (Role: {user.UserRole?.Name}) ===");
            
            if (permissions == null)
            {
                System.Diagnostics.Debug.WriteLine("No custom permissions found - using role-based defaults");
                return;
            }

            // Test all permission categories
            TestSalesPermissions(permissions);
            TestProductPermissions(permissions);
            TestFinancialPermissions(permissions);
            TestCustomerSupplierPermissions(permissions);
            TestAdministrativePermissions(permissions);
        }

        private void TestSalesPermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine("--- Sales Permissions ---");
            TestPermission("sales.create", permissions.CanCreateSales);
            TestPermission("sales.void", permissions.CanVoidSales);
            TestPermission("sales.discount", permissions.CanApplyDiscount);
            TestPermission("sales.history", permissions.CanViewSalesHistory);
        }

        private void TestProductPermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine("--- Product Permissions ---");
            TestPermission("products.manage", permissions.CanManageProducts);
            TestPermission("categories.manage", permissions.CanManageCategories);
            TestPermission("inventory.view", permissions.CanViewInventory);
            TestPermission("inventory.adjust", permissions.CanAdjustInventory);
        }

        private void TestFinancialPermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine("--- Financial Permissions ---");
            TestPermission("expenses.manage", permissions.CanManageExpenses);
            TestPermission("cashdrawer.manage", permissions.CanManageCashDrawer);
            TestPermission("reports.view", permissions.CanViewReports);
            TestPermission("prices.manage", permissions.CanManagePrices);
        }

        private void TestCustomerSupplierPermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine("--- Customer & Supplier Permissions ---");
            TestPermission("customers.manage", permissions.CanManageCustomers);
            TestPermission("suppliers.manage", permissions.CanManageSuppliers);
        }

        private void TestAdministrativePermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine("--- Administrative Permissions ---");
            TestPermission("users.manage", permissions.CanManageUsers);
            TestPermission("roles.manage", permissions.CanManageRoles);
            TestPermission("settings.access", permissions.CanAccessSettings);
            TestPermission("logs.view", permissions.CanViewLogs);
        }

        private void TestPermission(string permissionKey, bool expectedValue)
        {
            var actualValue = _permissionsService.HasPermission(permissionKey);
            var status = actualValue == expectedValue ? "✓" : "✗";
            System.Diagnostics.Debug.WriteLine($"{status} {permissionKey}: Expected={expectedValue}, Actual={actualValue}");
        }

        /// <summary>
        /// Tests permission enforcement for common operations
        /// </summary>
        public void TestCommonOperations(int userId)
        {
            var user = _dbService.GetUserById(userId);
            if (user == null) return;

            _permissionsService.Initialize(user);

            System.Diagnostics.Debug.WriteLine($"=== Common Operations Test for User: {user.Username} ===");

            // Test common permission checks
            var operations = new Dictionary<string, string>
            {
                { "Create Sale", "sales.create" },
                { "Void Sale", "sales.void" },
                { "Apply Discount", "sales.discount" },
                { "Manage Products", "products.manage" },
                { "View Reports", "reports.view" },
                { "Manage Users", "users.manage" },
                { "Access Settings", "settings.access" }
            };

            foreach (var operation in operations)
            {
                var hasPermission = _permissionsService.HasPermission(operation.Value);
                var status = hasPermission ? "ALLOWED" : "DENIED";
                System.Diagnostics.Debug.WriteLine($"{status}: {operation.Key}");
            }
        }

        /// <summary>
        /// Validates that all users have proper permissions set up
        /// </summary>
        public void ValidateAllUsers()
        {
            System.Diagnostics.Debug.WriteLine("=== Validating All Users ===");
            
            var users = _dbService.GetAllUsers();
            foreach (var user in users.Where(u => u.IsActive))
            {
                var permissions = _permissionsService.GetUserPermissions(user.Id);
                if (permissions == null)
                {
                    System.Diagnostics.Debug.WriteLine($"WARNING: User {user.Username} has no permissions record");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✓ User {user.Username} has permissions configured");
                }
            }
        }

        /// <summary>
        /// Creates a test user with custom permissions for validation
        /// </summary>
        public User CreateTestUser(string username, int roleId, UserPermissions customPermissions)
        {
            var testUser = new User
            {
                Username = username,
                Password = "test123",
                FirstName = "Test",
                LastName = "User",
                Email = $"{username}@test.com",
                RoleId = roleId,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            try
            {
                _dbService.AddUser(testUser);
                
                if (customPermissions != null)
                {
                    customPermissions.UserId = testUser.Id;
                    _dbService.UpdateUserPermissions(customPermissions);
                }

                System.Diagnostics.Debug.WriteLine($"Test user '{username}' created successfully with ID: {testUser.Id}");
                return testUser;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create test user: {ex.Message}");
                return null;
            }
        }
    }
}
