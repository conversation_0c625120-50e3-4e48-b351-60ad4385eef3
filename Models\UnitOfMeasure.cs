using System;
using System.Collections.Generic;

namespace POSSystem.Models
{
    public class UnitOfMeasure
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Abbreviation { get; set; }
        public string Type { get; set; }  // Unit, Weight, Volume, Package
        public int? BaseUnitId { get; set; }
        public decimal? ConversionFactor { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual UnitOfMeasure BaseUnit { get; set; }
        public virtual ICollection<UnitOfMeasure> DerivedUnits { get; set; }
        public virtual ICollection<Product> Products { get; set; }

        public UnitOfMeasure()
        {
            DerivedUnits = new HashSet<UnitOfMeasure>();
            Products = new HashSet<Product>();
            IsActive = true;
            CreatedAt = DateTime.Now;
        }

        public decimal ConvertToBase(decimal quantity)
        {
            if (BaseUnitId == null || ConversionFactor == null)
                return quantity;
            
            return quantity * ConversionFactor.Value;
        }

        public decimal ConvertFromBase(decimal baseQuantity)
        {
            if (BaseUnitId == null || ConversionFactor == null)
                return baseQuantity;
            
            return baseQuantity / ConversionFactor.Value;
        }
    }
} 