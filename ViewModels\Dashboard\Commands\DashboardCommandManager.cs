using System;
using System.Windows.Input;
using POSSystem.Commands;
using POSSystem.ViewModels.Dashboard.Services;
using System.Threading.Tasks;

namespace POSSystem.ViewModels.Dashboard.Commands
{
    /// <summary>
    /// Centralized command manager for dashboard operations, providing separation of concerns
    /// and improved testability for dashboard command logic.
    /// </summary>
    /// <remarks>
    /// This class extracts command-related logic from the main DashboardViewModel to:
    /// - Reduce the complexity of the main ViewModel
    /// - Improve testability by isolating command logic
    /// - Provide better organization of related commands
    /// - Enable easier maintenance and extension of command functionality
    /// </remarks>
    public class DashboardCommandManager
    {
        private readonly Services.IDashboardDataService _dataService;
        private readonly Services.IDashboardChartService _chartService;
        private readonly Action _refreshUI;

        /// <summary>
        /// Initializes a new instance of the DashboardCommandManager.
        /// </summary>
        /// <param name="dataService">Service for dashboard data operations</param>
        /// <param name="chartService">Service for chart operations</param>
        /// <param name="refreshUI">Action to refresh the UI after command execution</param>
        public DashboardCommandManager(
            Services.IDashboardDataService dataService,
            Services.IDashboardChartService chartService,
            Action refreshUI)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _chartService = chartService ?? throw new ArgumentNullException(nameof(chartService));
            _refreshUI = refreshUI ?? throw new ArgumentNullException(nameof(refreshUI));

            InitializeCommands();
        }

        #region Command Properties

        /// <summary>
        /// Command to refresh all dashboard data and charts.
        /// </summary>
        public ICommand RefreshDashboardCommand { get; private set; }

        /// <summary>
        /// Command to change the selected time period for dashboard metrics.
        /// </summary>
        public ICommand ChangePeriodCommand { get; private set; }

        /// <summary>
        /// Command to export dashboard data to various formats.
        /// </summary>
        public ICommand ExportDataCommand { get; private set; }

        /// <summary>
        /// Command to toggle between different chart types (line, bar, etc.).
        /// </summary>
        public ICommand ToggleChartTypeCommand { get; private set; }

        /// <summary>
        /// Command to zoom into a specific chart area.
        /// </summary>
        public ICommand ZoomChartCommand { get; private set; }

        /// <summary>
        /// Command to reset chart zoom to default view.
        /// </summary>
        public ICommand ResetZoomCommand { get; private set; }

        /// <summary>
        /// Command to show detailed view for a specific metric.
        /// </summary>
        public ICommand ShowDetailViewCommand { get; private set; }

        /// <summary>
        /// Command to hide the detailed metric view.
        /// </summary>
        public ICommand HideDetailViewCommand { get; private set; }

        /// <summary>
        /// Command to apply custom date range filter.
        /// </summary>
        public ICommand ApplyDateRangeCommand { get; private set; }

        /// <summary>
        /// Command to clear all applied filters.
        /// </summary>
        public ICommand ClearFiltersCommand { get; private set; }

        #endregion

        #region Command Initialization

        /// <summary>
        /// Initializes all dashboard commands with their respective implementations.
        /// </summary>
        private void InitializeCommands()
        {
            RefreshDashboardCommand = new RelayCommand(
                parameter => ExecuteRefreshDashboard(),
                parameter => true);

            ChangePeriodCommand = new RelayCommand(
                parameter => ExecuteChangePeriod(parameter),
                parameter => parameter != null);

            ExportDataCommand = new RelayCommand(
                parameter => ExecuteExportData(parameter as string),
                parameter => parameter is string format && !string.IsNullOrEmpty(format));

            ToggleChartTypeCommand = new RelayCommand(
                parameter => ExecuteToggleChartType(parameter as string),
                parameter => parameter is string chartType && !string.IsNullOrEmpty(chartType));

            ZoomChartCommand = new RelayCommand(
                parameter => ExecuteZoomChart(parameter),
                parameter => parameter != null);

            ResetZoomCommand = new RelayCommand(
                parameter => ExecuteResetZoom(),
                parameter => true);

            ShowDetailViewCommand = new RelayCommand(
                parameter => ExecuteShowDetailView(parameter),
                parameter => parameter != null);

            HideDetailViewCommand = new RelayCommand(
                parameter => ExecuteHideDetailView(),
                parameter => true);

            ApplyDateRangeCommand = new RelayCommand(
                parameter => ExecuteApplyDateRange(parameter),
                parameter => parameter != null);

            ClearFiltersCommand = new RelayCommand(
                parameter => ExecuteClearFilters(),
                parameter => true);
        }

        #endregion

        #region Command Implementations

        /// <summary>
        /// Executes the refresh dashboard operation asynchronously.
        /// </summary>
        private async System.Threading.Tasks.Task ExecuteRefreshDashboardAsync()
        {
            try
            {
                await _dataService.RefreshAllDataAsync();
                await _chartService.RefreshAllChartsAsync();
                _refreshUI();
            }
            catch (Exception ex)
            {
                // Log error and show user-friendly message
                System.Diagnostics.Debug.WriteLine($"Error refreshing dashboard: {ex.Message}");
                // Could integrate with error handling service here
            }
        }

        /// <summary>
        /// Executes the refresh dashboard operation synchronously.
        /// </summary>
        private void ExecuteRefreshDashboard()
        {
            try
            {
                // For now, just refresh the UI - async operations would be handled elsewhere
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing dashboard: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the export data operation synchronously.
        /// </summary>
        private void ExecuteExportData(string format)
        {
            try
            {
                if (!string.IsNullOrEmpty(format))
                {
                    // For now, just log the export request
                    System.Diagnostics.Debug.WriteLine($"Export data requested in format: {format}");
                    _refreshUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting data: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the apply date range operation synchronously.
        /// </summary>
        private void ExecuteApplyDateRange(object parameter)
        {
            try
            {
                if (parameter is DateRange dateRange)
                {
                    // For now, just log the date range application
                    System.Diagnostics.Debug.WriteLine($"Apply date range: {dateRange.StartDate} to {dateRange.EndDate}");
                    _refreshUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying date range: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the clear filters operation synchronously.
        /// </summary>
        private void ExecuteClearFilters()
        {
            try
            {
                // For now, just log the clear filters request
                System.Diagnostics.Debug.WriteLine("Clear filters requested");
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing filters: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the change period operation.
        /// </summary>
        private void ExecuteChangePeriod(object parameter)
        {
            try
            {
                if (parameter is DashboardTimePeriod period)
                {
                    _dataService.SetTimePeriod(period);
                    _refreshUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error changing period: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the export data operation asynchronously.
        /// </summary>
        private async System.Threading.Tasks.Task ExecuteExportDataAsync(string format)
        {
            try
            {
                await _dataService.ExportDataAsync(format);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting data: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the toggle chart type operation.
        /// </summary>
        private void ExecuteToggleChartType(string chartType)
        {
            try
            {
                if (Enum.TryParse<Services.ChartType>(chartType, out var type))
                {
                    _chartService.SetChartType(type);
                    _refreshUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error toggling chart type: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the zoom chart operation.
        /// </summary>
        private void ExecuteZoomChart(object parameter)
        {
            try
            {
                _chartService.ZoomChart(parameter);
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error zooming chart: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the reset zoom operation.
        /// </summary>
        private void ExecuteResetZoom()
        {
            try
            {
                _chartService.ResetZoom();
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting zoom: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the show detail view operation.
        /// </summary>
        private void ExecuteShowDetailView(object parameter)
        {
            try
            {
                _dataService.ShowDetailView(parameter);
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing detail view: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the hide detail view operation.
        /// </summary>
        private void ExecuteHideDetailView()
        {
            try
            {
                _dataService.HideDetailView();
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error hiding detail view: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the apply date range operation asynchronously.
        /// </summary>
        private async System.Threading.Tasks.Task ExecuteApplyDateRangeAsync(object parameter)
        {
            try
            {
                if (parameter is DateRange dateRange)
                {
                    await _dataService.ApplyDateRangeAsync(dateRange);
                    _refreshUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying date range: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes the clear filters operation asynchronously.
        /// </summary>
        private async System.Threading.Tasks.Task ExecuteClearFiltersAsync()
        {
            try
            {
                await _dataService.ClearFiltersAsync();
                _refreshUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing filters: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents a date range for filtering dashboard data.
    /// </summary>
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// Represents different time periods for dashboard metrics.
    /// </summary>
    public enum DashboardTimePeriod
    {
        Today,
        Week,
        Month,
        Quarter,
        Year,
        Custom
    }
}
