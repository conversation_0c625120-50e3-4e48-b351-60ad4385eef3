using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using POSSystem.Services.Monitoring;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services.BackgroundServices
{
    /// <summary>
    /// Background service for continuous performance monitoring and alerting
    /// </summary>
    public class PerformanceMonitoringBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PerformanceMonitoringBackgroundService> _logger;

        public PerformanceMonitoringBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<PerformanceMonitoringBackgroundService> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Performance monitoring background service started");

            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    await PerformMonitoringCycle(stoppingToken);
                    
                    // Wait 30 seconds before next monitoring cycle
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Performance monitoring background service stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in performance monitoring background service");
            }
        }

        private async Task PerformMonitoringCycle(CancellationToken cancellationToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var performanceMonitor = scope.ServiceProvider.GetRequiredService<PerformanceMonitoringService>();
                var dashboardService = scope.ServiceProvider.GetRequiredService<PerformanceDashboardService>();

                // Record system metrics
                performanceMonitor.RecordMemoryUsage();

                // Get current performance data
                var dashboardData = dashboardService.GetDashboardData();
                
                // Check for critical performance issues
                await CheckCriticalPerformanceIssues(dashboardData, cancellationToken);

                // Log performance summary every 5 minutes
                if (DateTime.Now.Minute % 5 == 0 && DateTime.Now.Second < 30)
                {
                    await LogPerformanceSummary(performanceMonitor);
                }

                // Check for memory leaks every 2 minutes
                if (DateTime.Now.Minute % 2 == 0 && DateTime.Now.Second < 30)
                {
                    await CheckMemoryLeaks(dashboardData);
                }

                // Generate performance report every hour
                if (DateTime.Now.Minute == 0 && DateTime.Now.Second < 30)
                {
                    await GenerateHourlyPerformanceReport(dashboardService);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during performance monitoring cycle");
            }
        }

        private async Task CheckCriticalPerformanceIssues(PerformanceDashboardData data, CancellationToken cancellationToken)
        {
            try
            {
                // Check for critical alerts
                if (data.AlertsSummary?.CriticalAlerts > 0)
                {
                    _logger.LogWarning("Critical performance alerts detected: {Count} critical alerts", 
                        data.AlertsSummary.CriticalAlerts);

                    foreach (var alert in data.AlertsSummary.RecentAlerts)
                    {
                        if (alert.Level == AlertLevel.Critical)
                        {
                            _logger.LogCritical("CRITICAL PERFORMANCE ALERT: {Message}", alert.Message);
                        }
                    }
                }

                // Check system resource usage
                if (data.SystemMetrics != null)
                {
                    // Memory usage check - adjusted thresholds for POS application
                    if (data.SystemMetrics.MemoryUsageMB > 400) // More than 400MB (was 1GB)
                    {
                        _logger.LogWarning("High memory usage detected: {MemoryMB}MB",
                            data.SystemMetrics.MemoryUsageMB);
                    }
                    else if (data.SystemMetrics.MemoryUsageMB > 300) // More than 300MB
                    {
                        _logger.LogInformation("Elevated memory usage: {MemoryMB}MB",
                            data.SystemMetrics.MemoryUsageMB);
                    }

                    // CPU usage check (if available)
                    if (data.SystemMetrics.CpuUsagePercent > 80)
                    {
                        _logger.LogWarning("High CPU usage detected: {CpuPercent}%", 
                            data.SystemMetrics.CpuUsagePercent);
                    }

                    // Thread count check
                    if (data.SystemMetrics.ThreadCount > 100)
                    {
                        _logger.LogWarning("High thread count detected: {ThreadCount} threads", 
                            data.SystemMetrics.ThreadCount);
                    }
                }

                // Check database performance
                if (data.DatabaseMetrics?.AverageQueryTime > 2000) // More than 2 seconds
                {
                    _logger.LogWarning("Slow database performance detected: average query time {QueryTime}ms", 
                        data.DatabaseMetrics.AverageQueryTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking critical performance issues");
            }

            await Task.CompletedTask;
        }

        private async Task LogPerformanceSummary(PerformanceMonitoringService monitor)
        {
            try
            {
                var summary = monitor.GetSummary();
                
                _logger.LogInformation("Performance Summary - Operations: {Total}, Avg Response: {AvgMs}ms, Alerts: {Alerts}",
                    summary.TotalOperations, 
                    summary.AverageResponseTime,
                    summary.RecentAlerts.Count);

                if (summary.SlowestOperations.Any())
                {
                    var slowest = summary.SlowestOperations.First();
                    _logger.LogInformation("Slowest Operation: {Operation} - {AvgMs}ms average ({Count} executions)",
                        slowest.OperationName, slowest.AverageMs, slowest.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging performance summary");
            }

            await Task.CompletedTask;
        }

        private async Task CheckMemoryLeaks(PerformanceDashboardData data)
        {
            try
            {
                if (data.SystemMetrics != null)
                {
                    var memoryMB = data.SystemMetrics.MemoryUsageMB;
                    var gcMemoryMB = data.SystemMetrics.GCMemoryMB;
                    
                    // Check if there's a significant difference between working set and GC memory
                    var memoryDifference = memoryMB - gcMemoryMB;
                    
                    if (memoryDifference > 200) // More than 200MB difference
                    {
                        _logger.LogWarning("Potential memory leak detected - Working Set: {WorkingSet}MB, GC Memory: {GCMemory}MB, Difference: {Difference}MB",
                            memoryMB, gcMemoryMB, memoryDifference);
                    }

                    // Check GC collection frequency
                    var totalCollections = data.SystemMetrics.GCGen0Collections + 
                                         data.SystemMetrics.GCGen1Collections + 
                                         data.SystemMetrics.GCGen2Collections;
                    
                    if (totalCollections > 1000) // High GC activity
                    {
                        _logger.LogWarning("High GC activity detected - Gen0: {Gen0}, Gen1: {Gen1}, Gen2: {Gen2}",
                            data.SystemMetrics.GCGen0Collections,
                            data.SystemMetrics.GCGen1Collections,
                            data.SystemMetrics.GCGen2Collections);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking memory leaks");
            }

            await Task.CompletedTask;
        }

        private async Task GenerateHourlyPerformanceReport(PerformanceDashboardService dashboardService)
        {
            try
            {
                var trends = dashboardService.GetPerformanceTrends(TimeSpan.FromHours(1));
                var alertsSummary = dashboardService.GetAlertsSummary();
                var dbMetrics = dashboardService.GetDatabaseMetrics();

                _logger.LogInformation("=== HOURLY PERFORMANCE REPORT ===");
                _logger.LogInformation("Report Time: {Time}", DateTime.Now);
                
                // Performance trends
                _logger.LogInformation("Performance Trends (Last Hour):");
                foreach (var trend in trends.OperationTrends.Take(5))
                {
                    var trendSymbol = trend.TrendDirection switch
                    {
                        TrendDirection.Increasing => "↗️",
                        TrendDirection.Decreasing => "↘️",
                        _ => "→"
                    };
                    
                    _logger.LogInformation("  {Symbol} {Operation}: {Avg}ms avg, {Count} executions",
                        trendSymbol, trend.OperationName, trend.AverageResponseTime, trend.TotalExecutions);
                }

                // Alerts summary
                _logger.LogInformation("Alerts Summary: {Total} total ({Critical} critical, {Warning} warning)",
                    alertsSummary.TotalAlerts, alertsSummary.CriticalAlerts, alertsSummary.WarningAlerts);

                // Database performance
                _logger.LogInformation("Database Performance: {Total} queries, {Avg}ms average",
                    dbMetrics.TotalQueries, dbMetrics.AverageQueryTime);

                _logger.LogInformation("=== END PERFORMANCE REPORT ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating hourly performance report");
            }

            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Service for manual performance monitoring operations
    /// </summary>
    public class ManualPerformanceMonitoringService
    {
        private readonly PerformanceMonitoringService _monitor;
        private readonly PerformanceDashboardService _dashboard;
        private readonly ILogger<ManualPerformanceMonitoringService> _logger;

        public ManualPerformanceMonitoringService(
            PerformanceMonitoringService monitor,
            PerformanceDashboardService dashboard,
            ILogger<ManualPerformanceMonitoringService> logger = null)
        {
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
            _dashboard = dashboard ?? throw new ArgumentNullException(nameof(dashboard));
            _logger = logger;
        }

        /// <summary>
        /// Generate a comprehensive performance report
        /// </summary>
        public async Task<PerformanceReport> GeneratePerformanceReportAsync()
        {
            try
            {
                _logger?.LogInformation("Generating comprehensive performance report...");

                var report = new PerformanceReport
                {
                    GeneratedAt = DateTime.Now,
                    DashboardData = _dashboard.GetDashboardData(),
                    SystemMetrics = _dashboard.GetSystemMetrics(),
                    PerformanceTrends = _dashboard.GetPerformanceTrends(TimeSpan.FromHours(24)),
                    AlertsSummary = _dashboard.GetAlertsSummary(),
                    DatabaseMetrics = _dashboard.GetDatabaseMetrics(),
                    Recommendations = GenerateRecommendations(_dashboard.GetDashboardData())
                };

                _logger?.LogInformation("Performance report generated successfully");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error generating performance report");
                throw;
            }
        }

        /// <summary>
        /// Clear all performance metrics
        /// </summary>
        public void ClearMetrics()
        {
            _monitor.ClearMetrics();
            _logger?.LogInformation("Performance metrics cleared");
        }

        /// <summary>
        /// Force garbage collection and record memory freed
        /// </summary>
        public long ForceGarbageCollection()
        {
            var memoryFreed = PerformanceHelper.ForceGCAndGetMemoryFreed();
            _logger?.LogInformation("Forced garbage collection completed, {MemoryFreed}MB freed", memoryFreed);
            return memoryFreed;
        }

        private List<string> GenerateRecommendations(PerformanceDashboardData data)
        {
            var recommendations = new List<string>();

            // Memory recommendations
            if (data.SystemMetrics?.MemoryUsageMB > 500)
            {
                recommendations.Add("Consider optimizing memory usage - current usage is high");
            }

            // Database recommendations
            if (data.DatabaseMetrics?.AverageQueryTime > 1000)
            {
                recommendations.Add("Database queries are slow - consider adding indexes or optimizing queries");
            }

            // Alert recommendations
            if (data.AlertsSummary?.CriticalAlerts > 0)
            {
                recommendations.Add("Address critical performance alerts immediately");
            }

            // Performance trend recommendations
            if (data.TrendData?.OperationTrends?.Any(t => t.TrendDirection == TrendDirection.Increasing) == true)
            {
                recommendations.Add("Some operations are getting slower - investigate performance degradation");
            }

            return recommendations;
        }
    }

    /// <summary>
    /// Comprehensive performance report
    /// </summary>
    public class PerformanceReport
    {
        public DateTime GeneratedAt { get; set; }
        public PerformanceDashboardData DashboardData { get; set; }
        public SystemMetrics SystemMetrics { get; set; }
        public PerformanceTrends PerformanceTrends { get; set; }
        public AlertsSummary AlertsSummary { get; set; }
        public DatabasePerformanceMetrics DatabaseMetrics { get; set; }
        public List<string> Recommendations { get; set; } = new List<string>();
    }
}
