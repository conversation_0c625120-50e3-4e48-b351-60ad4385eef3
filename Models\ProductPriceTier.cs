using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    /// <summary>
    /// Represents a pricing tier for bulk/pack pricing functionality.
    /// Allows products to have multiple pricing levels based on quantity thresholds.
    /// </summary>
    public class ProductPriceTier
    {
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the Product this pricing tier belongs to
        /// </summary>
        [Required]
        public int ProductId { get; set; }

        /// <summary>
        /// Navigation property to the associated Product
        /// </summary>
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        /// <summary>
        /// Minimum quantity required to qualify for this pricing tier
        /// Supports decimal quantities for weight-based products
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,3)")]
        public decimal MinimumQuantity { get; set; }

        /// <summary>
        /// Maximum quantity for this tier (null means no upper limit)
        /// Used for creating quantity ranges like 5-9 units
        /// </summary>
        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaximumQuantity { get; set; }

        /// <summary>
        /// Unit price for this tier (price per individual item)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Total price for the minimum quantity (for pack pricing)
        /// If null, calculated as MinimumQuantity * UnitPrice
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PackPrice { get; set; }

        /// <summary>
        /// Display name for this tier (e.g., "5-Pack", "Bulk Rate", "Wholesale")
        /// </summary>
        [MaxLength(50)]
        public string TierName { get; set; }

        /// <summary>
        /// Description of the pricing tier for display purposes
        /// </summary>
        [MaxLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// Whether this pricing tier is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Priority order for displaying tiers (lower numbers shown first)
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// When this pricing tier was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// When this pricing tier was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Optional effective date for when this tier becomes active
        /// </summary>
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// Optional expiration date for when this tier becomes inactive
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Calculated property: Effective unit price for this tier
        /// Returns PackPrice / MinimumQuantity if PackPrice is set, otherwise UnitPrice
        /// </summary>
        [NotMapped]
        public decimal EffectiveUnitPrice
        {
            get
            {
                if (PackPrice.HasValue && MinimumQuantity > 0)
                {
                    return PackPrice.Value / MinimumQuantity;
                }
                return UnitPrice;
            }
        }

        /// <summary>
        /// Calculated property: Savings per unit compared to base price
        /// </summary>
        [NotMapped]
        public decimal SavingsPerUnit
        {
            get
            {
                if (Product?.SellingPrice > 0)
                {
                    return Product.SellingPrice - EffectiveUnitPrice;
                }
                return 0;
            }
        }

        /// <summary>
        /// Calculated property: Percentage savings compared to base price
        /// </summary>
        [NotMapped]
        public decimal SavingsPercentage
        {
            get
            {
                if (Product?.SellingPrice > 0)
                {
                    return (SavingsPerUnit / Product.SellingPrice) * 100;
                }
                return 0;
            }
        }

        /// <summary>
        /// Checks if this tier is currently valid based on effective and expiration dates
        /// </summary>
        [NotMapped]
        public bool IsCurrentlyValid
        {
            get
            {
                var now = DateTime.Now;
                return IsActive &&
                       (EffectiveDate == null || EffectiveDate <= now) &&
                       (ExpirationDate == null || ExpirationDate > now);
            }
        }

        /// <summary>
        /// Calculates the total price for a given quantity using this tier
        /// </summary>
        /// <param name="quantity">Quantity to calculate price for</param>
        /// <returns>Total price for the quantity</returns>
        public decimal CalculatePriceForQuantity(decimal quantity)
        {
            if (quantity < MinimumQuantity)
            {
                throw new ArgumentException($"Quantity {quantity} is below minimum {MinimumQuantity} for this tier");
            }

            if (MaximumQuantity.HasValue && quantity > MaximumQuantity.Value)
            {
                throw new ArgumentException($"Quantity {quantity} exceeds maximum {MaximumQuantity.Value} for this tier");
            }

            return quantity * EffectiveUnitPrice;
        }

        /// <summary>
        /// Checks if a given quantity qualifies for this pricing tier
        /// </summary>
        /// <param name="quantity">Quantity to check</param>
        /// <returns>True if quantity qualifies for this tier</returns>
        public bool QualifiesForTier(decimal quantity)
        {
            return IsCurrentlyValid &&
                   quantity >= MinimumQuantity &&
                   (MaximumQuantity == null || quantity <= MaximumQuantity.Value);
        }

        /// <summary>
        /// Returns a formatted display string for this tier
        /// </summary>
        /// <returns>Formatted tier description</returns>
        public string GetDisplayText()
        {
            if (!string.IsNullOrEmpty(TierName))
            {
                return TierName;
            }

            var quantityText = MaximumQuantity.HasValue
                ? $"{MinimumQuantity:0.###}-{MaximumQuantity.Value:0.###}"
                : $"{MinimumQuantity:0.###}+";

            var unitText = Product?.UnitOfMeasure?.Name ?? "units";
            
            return $"{quantityText} {unitText}";
        }

        /// <summary>
        /// Returns a formatted price display string for this tier
        /// </summary>
        /// <returns>Formatted price string</returns>
        public string GetPriceDisplayText()
        {
            if (PackPrice.HasValue)
            {
                return $"{PackPrice.Value:C2} ({EffectiveUnitPrice:C2} each)";
            }
            return $"{UnitPrice:C2} each";
        }
    }
}
