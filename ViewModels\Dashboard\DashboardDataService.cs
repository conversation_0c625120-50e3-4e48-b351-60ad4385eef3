using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Service responsible for dashboard data loading, caching, and aggregation.
    /// Extracted from DashboardViewModel to improve code organization and maintainability.
    /// </summary>
    public class DashboardDataService : IDashboardDataService
    {
        private readonly IDashboardDataProvider _dataProvider;
        private readonly ConcurrentDictionary<string, (object Data, DateTime Expiry)> _cache;
        private const int DEFAULT_CACHE_DURATION_MINUTES = 5;

        public DashboardDataService(IDashboardDataProvider dataProvider)
        {
            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
            _cache = new ConcurrentDictionary<string, (object Data, DateTime Expiry)>();
        }

        // ✅ CRITICAL FIX: Add request deduplication to prevent redundant database calls
        private static readonly Dictionary<string, Task<List<DashboardSale>>> _pendingRequests = new();
        private static readonly object _requestLock = new object();

        /// <summary>
        /// Gets sales data for a specified date range with request deduplication
        /// </summary>
        public async Task<List<DashboardSale>> GetSalesDataAsync(DateTime startDate, DateTime endDate, string cacheIdentifier = null)
        {
            // Include the cache identifier (if provided) to make the cache key unique per period selection
            string cacheKey = cacheIdentifier != null
                ? $"sales_{cacheIdentifier}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}"
                : $"sales_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";

            // ✅ PERFORMANCE FIX: Check if there's already a pending request for this data
            Task<List<DashboardSale>> existingTask = null;
            lock (_requestLock)
            {
                if (_pendingRequests.ContainsKey(cacheKey))
                {
                    existingTask = _pendingRequests[cacheKey];
                }
            }

            if (existingTask != null)
            {
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Reusing pending request for {cacheKey}");
                return await existingTask;
            }

            // Try to get from cache first
            var cachedData = GetFromCache<List<DashboardSale>>(cacheKey);
            if (cachedData != null)
            {
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Retrieved from cache: {cacheKey}");
                return cachedData;
            }

            // Create the database request task
            var requestTask = FetchSalesFromDatabaseAsync(startDate, endDate, cacheKey);

            // Add to pending requests
            lock (_requestLock)
            {
                _pendingRequests[cacheKey] = requestTask;
            }

            try
            {
                var result = await requestTask;
                return result;
            }
            finally
            {
                // Remove from pending requests when done
                lock (_requestLock)
                {
                    _pendingRequests.Remove(cacheKey);
                }
            }
        }

        /// <summary>
        /// Internal method to fetch sales from database
        /// </summary>
        private async Task<List<DashboardSale>> FetchSalesFromDatabaseAsync(DateTime startDate, DateTime endDate, string cacheKey)
        {
            try
            {
                // Get from database if not in cache
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Fetching sales data from database: {startDate:yyyy-MM-dd HH:mm:ss} to {endDate:yyyy-MM-dd HH:mm:ss}");
                var sales = await _dataProvider.GetSalesAsync(startDate, endDate);
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Retrieved {sales?.Count ?? 0} raw sales from database");

                // Convert to dashboard sales
                var dashboardSales = sales.ToDashboardSales();
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Converted to {dashboardSales?.Count ?? 0} dashboard sales");

                // If no data was found, return empty list instead of generating sample data
                if (dashboardSales == null || !dashboardSales.Any())
                {
                    Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: No sales data found for period. Returning empty list.");
                    // Cache empty result to prevent repeated database calls
                    AddToCache(cacheKey, new List<DashboardSale>(), TimeSpan.FromMinutes(1));
                    return new List<DashboardSale>();
                }

                // Log some sample data for debugging
                foreach (var sale in dashboardSales.Take(3))
                {
                    Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Sample sale - Date: {sale.Date:yyyy-MM-dd HH:mm:ss}, Amount: {sale.GrandTotal:N2}");
                }

                // Cache the results
                int cacheDuration = DetermineCacheDuration(endDate);
                AddToCache(cacheKey, dashboardSales, TimeSpan.FromMinutes(cacheDuration));
                Debug.WriteLine($"DashboardDataService.GetSalesDataAsync: Cached {dashboardSales.Count} sales with duration {cacheDuration} minutes");

                return dashboardSales;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error fetching sales data: {ex.Message}");
                // Cache empty result to prevent repeated failed calls
                AddToCache(cacheKey, new List<DashboardSale>(), TimeSpan.FromMinutes(1));
                return new List<DashboardSale>();
            }
        }

        /// <summary>
        /// Gets sales aggregation data for a specified time period
        /// </summary>
        public async Task<List<SaleAggregation>> GetSalesAggregationAsync(DateTime startDate, DateTime endDate, string metricType = "sales", string cacheIdentifier = null)
        {
            // Include the cache identifier (if provided) to make the cache key unique per period selection
            string cacheKey = cacheIdentifier != null 
                ? $"aggregation_{cacheIdentifier}_{metricType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}"
                : $"aggregation_{metricType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Try to get from cache first
            var cachedData = GetFromCache<List<SaleAggregation>>(cacheKey);
            if (cachedData != null)
            {
                return cachedData;
            }

            try
            {
                // Get base sales data - pass through the cache identifier
                var sales = await GetSalesDataAsync(startDate, endDate, cacheIdentifier);
                
                if (sales == null || !sales.Any())
                {
                    return new List<SaleAggregation>();
                }

                // Perform the aggregation
                var aggregations = new List<SaleAggregation>();
                
                // Determine appropriate grouping based on date range
                TimeSpan span = endDate - startDate;
                
                if (span.TotalDays <= 2)
                {
                    // Hourly aggregation for 1-2 days
                    aggregations = sales
                        .GroupBy(s => new DateTime(s.Date.Year, s.Date.Month, s.Date.Day, s.Date.Hour, 0, 0))
                        .Select(g => new SaleAggregation
                        {
                            Date = g.Key,
                            TotalSales = g.Sum(s => s.GrandTotal),
                            TotalProfit = g.Sum(s => s.Profit),
                            TotalItems = g.Sum(s => s.TotalItems),
                            OrderCount = g.Count()
                        })
                        .OrderBy(s => s.Date)
                        .ToList();
                }
                else if (span.TotalDays <= 31)
                {
                    // Daily aggregation for up to a month
                    aggregations = sales
                        .GroupBy(s => s.Date.Date)
                        .Select(g => new SaleAggregation
                        {
                            Date = g.Key,
                            TotalSales = g.Sum(s => s.GrandTotal),
                            TotalProfit = g.Sum(s => s.Profit),
                            TotalItems = g.Sum(s => s.TotalItems),
                            OrderCount = g.Count()
                        })
                        .OrderBy(s => s.Date)
                        .ToList();
                }
                else if (span.TotalDays <= 90)
                {
                    // Weekly aggregation for up to 3 months
                    aggregations = sales
                        .GroupBy(s => {
                            var date = s.Date.Date;
                            return date.AddDays(-(int)date.DayOfWeek);
                        })
                        .Select(g => new SaleAggregation
                        {
                            Date = g.Key,
                            TotalSales = g.Sum(s => s.GrandTotal),
                            TotalProfit = g.Sum(s => s.Profit),
                            TotalItems = g.Sum(s => s.TotalItems),
                            OrderCount = g.Count()
                        })
                        .OrderBy(s => s.Date)
                        .ToList();
                }
                else
                {
                    // Monthly aggregation for longer periods
                    aggregations = sales
                        .GroupBy(s => new DateTime(s.Date.Year, s.Date.Month, 1))
                        .Select(g => new SaleAggregation
                        {
                            Date = g.Key,
                            TotalSales = g.Sum(s => s.GrandTotal),
                            TotalProfit = g.Sum(s => s.Profit),
                            TotalItems = g.Sum(s => s.TotalItems),
                            OrderCount = g.Count()
                        })
                        .OrderBy(s => s.Date)
                        .ToList();
                }
                
                // Add to cache
                int cacheDuration = DetermineCacheDuration(endDate);
                AddToCache(cacheKey, aggregations, TimeSpan.FromMinutes(cacheDuration));
                Debug.WriteLine($"Cached {aggregations.Count} aggregations with duration {cacheDuration} minutes");
                
                return aggregations;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating sales aggregation: {ex.Message}");
                return new List<SaleAggregation>();
            }
        }

        /// <summary>
        /// Extract specific metric values from sale aggregations
        /// </summary>
        public (List<decimal> values, List<string> labels) ExtractMetricData(
            List<SaleAggregation> aggregations, 
            string metricType,
            Func<DateTime, string> labelFormatter)
        {
            if (aggregations == null || !aggregations.Any())
            {
                return (new List<decimal>(), new List<string>());
            }
            
            var values = new List<decimal>();
            var labels = new List<string>();
            
            foreach (var agg in aggregations)
            {
                // Extract metric value based on type
                decimal value = 0;
                switch (metricType?.ToLower())
                {
                    case "sales":
                        value = agg.TotalSales;
                        break;
                    case "profit":
                        value = agg.TotalProfit;
                        break;
                    case "margin":
                        value = agg.TotalSales > 0 ? (agg.TotalProfit / agg.TotalSales) * 100 : 0;
                        break;
                    case "items":
                        value = agg.TotalItems;
                        break;
                    case "orders":
                        value = agg.OrderCount;
                        break;
                    default:
                        value = agg.TotalSales;
                        break;
                }
                
                values.Add(value);
                labels.Add(labelFormatter(agg.Date));
            }
            
            return (values, labels);
        }

        /// <summary>
        /// Gets the count of sales in a date range
        /// </summary>
        public async Task<int> GetSalesCountAsync(DateTime startDate, DateTime endDate)
        {
            string cacheKey = $"sales_count_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Try to get from cache first
            var cachedCount = GetIntFromCache(cacheKey);
            if (cachedCount.HasValue)
            {
                Debug.WriteLine($"Retrieved sales count from cache: {cacheKey}");
                return cachedCount.Value;
            }
            
            // Get data from provider
            var sales = await _dataProvider.GetSalesAsync(startDate, endDate);
            int count = sales?.Count ?? 0;
            
            // Cache the result
            int cacheDuration = DetermineCacheDuration(endDate);
            AddIntToCache(cacheKey, count, TimeSpan.FromMinutes(cacheDuration));
            
            return count;
        }

        /// <summary>
        /// Gets the total sales in a date range
        /// </summary>
        public async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            string cacheKey = $"sales_total_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Try to get from cache first
            var cachedTotal = GetDecimalFromCache(cacheKey);
            if (cachedTotal.HasValue)
            {
                Debug.WriteLine($"Retrieved sales total from cache: {cacheKey}");
                return cachedTotal.Value;
            }
            
            // Get data from provider
            var sales = await _dataProvider.GetSalesAsync(startDate, endDate);
            decimal total = sales?.Sum(s => s.GrandTotal) ?? 0;
            
            // Cache the result
            int cacheDuration = DetermineCacheDuration(endDate);
            AddDecimalToCache(cacheKey, total, TimeSpan.FromMinutes(cacheDuration));
            
            return total;
        }

        /// <summary>
        /// Clear the cache completely
        /// </summary>
        public void ClearCache()
        {
            _cache.Clear();
            Debug.WriteLine("Dashboard data cache cleared");
        }

        /// <summary>
        /// Force clears all cached data and ensures fresh data on next request
        /// </summary>
        public void ForceClearCache()
        {
            _cache.Clear();

            // Also clear any static caches or memory references
            GC.Collect(); // Force garbage collection to clear any lingering references
            GC.WaitForPendingFinalizers();

            Debug.WriteLine("DashboardDataService: Force cache clear completed");
        }

        /// <summary>
        /// Clean up expired cache entries
        /// </summary>
        public void CleanupCache()
        {
            var expiredKeys = _cache
                .Where(kv => kv.Value.Expiry < DateTime.Now)
                .Select(kv => kv.Key)
                .ToList();
                
            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }
            
            if (expiredKeys.Any())
            {
                Debug.WriteLine($"Removed {expiredKeys.Count} expired cache entries");
            }
        }
        
        /// <summary>
        /// Generates sample sales data for visualization when no database data is available
        /// </summary>
        private List<DashboardSale> GenerateSampleSalesData(DateTime startDate, DateTime endDate)
        {
            var random = new Random();
            var result = new List<DashboardSale>();
            
            // Generate one sale per day with random values
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                // 2-4 sales per day
                int salesPerDay = random.Next(2, 5);
                
                for (int i = 0; i < salesPerDay; i++)
                {
                    // Randomize values for sample data
                    int items = random.Next(1, 10);
                    decimal pricePerItem = random.Next(50, 500) + (decimal)Math.Round(random.NextDouble(), 2);
                    decimal total = items * pricePerItem;
                    decimal profit = total * (decimal)(0.3 + random.NextDouble() * 0.3); // 30-60% profit
                    
                    // Create a random time of day
                    int hour = random.Next(8, 20); // Business hours
                    int minute = random.Next(0, 60);
                    var saleDateTime = new DateTime(
                        date.Year, date.Month, date.Day, 
                        hour, minute, 0
                    );
                    
                    result.Add(new DashboardSale
                    {
                        Id = 1000 + result.Count,
                        Date = saleDateTime,
                        GrandTotal = total,
                        Profit = profit,
                        TotalItems = items
                    });
                }
            }
            
            Debug.WriteLine($"Generated {result.Count} sample sales for demonstration");
            return result;
        }

        /// <summary>
        /// Determine an appropriate cache duration based on data freshness
        /// </summary>
        private int DetermineCacheDuration(DateTime endDate)
        {
            // More dynamic caching based on date range
            var now = DateTime.Now;
            var dayDiff = (now - endDate).TotalDays;
            
            if (endDate.Date == now.Date)
            {
                // Today's data - short cache
                return 1;
            }
            else if (dayDiff <= 7)
            {
                // Recent data - medium cache
                return 5;
            }
            else
            {
                // Historical data - longer cache
                return 20;
            }
        }

        /// <summary>
        /// Gets data from the cache if available and not expired
        /// </summary>
        private T GetFromCache<T>(string key)
        {
            if (_cache.TryGetValue(key, out var cachedItem) && cachedItem.Expiry > DateTime.Now)
            {
                return (T)cachedItem.Data;
            }
            return default;
        }

        /// <summary>
        /// Adds data to the cache with expiration
        /// </summary>
        private void AddToCache<T>(string key, T data, TimeSpan customCacheDuration)
        {
            _cache[key] = (data, DateTime.Now.Add(customCacheDuration));
        }

        // Add specialized methods for value types if needed
        private int? GetIntFromCache(string key)
        {
            if (_cache.TryGetValue(key, out var cachedItem) && cachedItem.Expiry > DateTime.Now)
            {
                return (int)cachedItem.Data;
            }
            return null;
        }

        private void AddIntToCache(string key, int data, TimeSpan customCacheDuration)
        {
            _cache[key] = (data, DateTime.Now.Add(customCacheDuration));
        }

        private decimal? GetDecimalFromCache(string key)
        {
            if (_cache.TryGetValue(key, out var cachedItem) && cachedItem.Expiry > DateTime.Now)
            {
                return (decimal)cachedItem.Data;
            }
            return null;
        }

        private void AddDecimalToCache(string key, decimal data, TimeSpan customCacheDuration)
        {
            _cache[key] = (data, DateTime.Now.Add(customCacheDuration));
        }

        public async Task<List<BusinessExpense>> GetBusinessExpensesAsync(DateTime startDate, DateTime endDate)
        {
            string cacheKey = $"expenses_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            var cachedData = GetFromCache<List<BusinessExpense>>(cacheKey);
            if (cachedData != null)
            {
                return cachedData;
            }

            var expenses = await _dataProvider.GetBusinessExpensesAsync(startDate, endDate);
            AddToCache(cacheKey, expenses, TimeSpan.FromMinutes(DEFAULT_CACHE_DURATION_MINUTES));
            return expenses;
        }

        public List<Sale> GetUnpaidSales()
        {
            string cacheKey = "unpaid_sales";
            var cachedData = GetFromCache<List<Sale>>(cacheKey);
            if (cachedData != null)
            {
                return cachedData;
            }

            var sales = _dataProvider.GetUnpaidSales();
            AddToCache(cacheKey, sales, TimeSpan.FromMinutes(5)); // Short cache duration for unpaid sales
            return sales;
        }

        public List<Product> GetExpiringProducts(int daysThreshold)
        {
            string cacheKey = $"expiring_products_{daysThreshold}";
            var cachedData = GetFromCache<List<Product>>(cacheKey);
            if (cachedData != null)
            {
                return cachedData;
            }

            var products = _dataProvider.GetExpiringProducts(daysThreshold);
            AddToCache(cacheKey, products, TimeSpan.FromMinutes(30)); // Longer cache for expiring products
            return products;
        }

        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            string cacheKey = "low_stock_products";
            var cachedData = GetFromCache<List<Product>>(cacheKey);
            if (cachedData != null)
            {
                return cachedData;
            }

            var products = await _dataProvider.GetLowStockProductsAsync();
            AddToCache(cacheKey, products, TimeSpan.FromMinutes(15)); // Medium cache duration
            return products;
        }
    }
} 