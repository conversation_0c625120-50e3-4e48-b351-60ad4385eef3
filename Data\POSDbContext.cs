using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Models.Printing;
using System.IO;
using System.Diagnostics;
using System;
using POSSystem.Services;

namespace POSSystem.Data
{
    public class POSDbContext : DbContext
    {
        public POSDbContext(DbContextOptions<POSDbContext> options) : base(options)
        {
        }

        public POSDbContext()
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<ProductPrice> ProductPrices { get; set; }
        public DbSet<ProductPriceTier> ProductPriceTiers { get; set; }
        public DbSet<LoyaltyProgram> LoyaltyPrograms { get; set; }
        public DbSet<LoyaltyTier> LoyaltyTiers { get; set; }
        public DbSet<LoyaltyTransaction> LoyaltyTransactions { get; set; }
        public DbSet<SaleHistory> SaleHistory { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<ProductBarcode> ProductBarcodes { get; set; }
        public DbSet<ProductAlert> ProductAlerts { get; set; }
        public DbSet<UnitOfMeasure> UnitsOfMeasure { get; set; }
        public DbSet<BatchStock> BatchStock { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<DiscountType> DiscountTypes { get; set; }
        public DbSet<DiscountReason> DiscountReasons { get; set; }
        public DbSet<DiscountPermission> DiscountPermissions { get; set; }
        public DbSet<Discount> Discounts { get; set; }
        public DbSet<CashDrawer> CashDrawers { get; set; }
        public DbSet<CashTransaction> CashTransactions { get; set; }
        public DbSet<BusinessExpense> BusinessExpenses { get; set; }
        public DbSet<UserFavorite> UserFavorites { get; set; }
        public DbSet<UserPermissions> UserPermissions { get; set; }

        // Invoice System DbSets (Two-Tier)
        public DbSet<Invoice> Invoice { get; set; } // Changed from Invoices to Invoice for consistency
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<InvoicePayment> InvoicePayments { get; set; }
        public DbSet<DraftInvoiceNotification> DraftInvoiceNotifications { get; set; }
        public DbSet<DraftInvoiceSettings> DraftInvoiceSettings { get; set; }

        // Receipt Printing System DbSets
        public DbSet<ReceiptTemplate> ReceiptTemplates { get; set; }
        public DbSet<PrinterConfiguration> PrinterConfigurations { get; set; }
        public DbSet<ReceiptPrintSettings> ReceiptPrintSettings { get; set; }
        public DbSet<ReceiptPrintJob> ReceiptPrintJobs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // ✅ OPTIMIZED: Enhanced indexes for product search and dashboard performance
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Name);

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Description);

            // ✅ NEW: Critical index for dashboard stock queries
            modelBuilder.Entity<Product>()
                .HasIndex(p => new { p.IsActive, p.StockQuantity })
                .HasDatabaseName("IX_Products_IsActive_StockQuantity");

            // Add index for product barcodes
            modelBuilder.Entity<ProductBarcode>()
                .HasIndex(pb => pb.Barcode);

            // Create optimized composite indexes for search
            modelBuilder.Entity<Product>()
                .HasIndex(p => new { p.Name, p.SKU })
                .IsDescending(false, false)
                .HasFilter(null);

            modelBuilder.Entity<Product>()
                .HasIndex(p => new { p.SKU, p.Description })
                .IsDescending(false, false)
                .HasFilter(null);

            // ✅ CRITICAL: Dashboard performance indexes for Sales table
            modelBuilder.Entity<Sale>()
                .HasIndex(s => s.SaleDate)
                .HasDatabaseName("IX_Sales_SaleDate");

            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.SaleDate, s.Status })
                .HasDatabaseName("IX_Sales_SaleDate_Status");

            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.SaleDate, s.GrandTotal })
                .HasDatabaseName("IX_Sales_SaleDate_GrandTotal");

            // ✅ NEW: Index for payment status queries
            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.PaymentStatus, s.SaleDate })
                .HasDatabaseName("IX_Sales_PaymentStatus_SaleDate");

            // ✅ NEW: Index for customer sales analysis
            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.CustomerId, s.SaleDate })
                .HasDatabaseName("IX_Sales_CustomerId_SaleDate");

            // ✅ NEW: Index for user performance tracking
            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.UserId, s.SaleDate })
                .HasDatabaseName("IX_Sales_UserId_SaleDate");

            // ✅ NEW: Critical index for SaleItems dashboard queries
            modelBuilder.Entity<SaleItem>()
                .HasIndex(si => new { si.ProductId, si.SaleId })
                .HasDatabaseName("IX_SaleItems_ProductId_SaleId");

            // ✅ NEW: Index for inventory transactions
            modelBuilder.Entity<InventoryTransaction>()
                .HasIndex(it => new { it.ProductId, it.TransactionDate })
                .HasDatabaseName("IX_InventoryTransactions_ProductId_Date");

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.ToTable("Customers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();
                entity.Property(e => e.FirstName).HasColumnName("FirstName").IsRequired();
                entity.Property(e => e.LastName).HasColumnName("LastName").IsRequired();
                entity.Property(e => e.Email).HasColumnName("Email");
                entity.Property(e => e.Phone).HasColumnName("Phone");
                entity.Property(e => e.Address).HasColumnName("Address");
                entity.Property(e => e.IsActive).HasColumnName("IsActive").HasConversion<int>();
                entity.Property(e => e.LoyaltyCode).HasColumnName("LoyaltyCode");
                entity.Property(e => e.LoyaltyPoints).HasColumnName("LoyaltyPoints").HasDefaultValue(0);
                entity.Property(e => e.LastVisit).HasColumnName("LastVisit");
                entity.Property(e => e.TotalVisits).HasColumnName("TotalVisits").HasDefaultValue(0);
                entity.Property(e => e.TotalSpent).HasColumnName("TotalSpent").HasDefaultValue(0);
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
                entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");

                // Add sample data
                entity.HasData(
                    new Customer
                    {
                        Id = 1,
                        FirstName = "John",
                        LastName = "Doe",
                        Email = "<EMAIL>",
                        Phone = "************",
                        Address = "123 Main St",
                        IsActive = true,
                        LoyaltyCode = "JD12345",
                        LoyaltyPoints = 100,
                        LastVisit = DateTime.Now,
                        TotalVisits = 5,
                        TotalSpent = 500.00m,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new Customer
                    {
                        Id = 2,
                        FirstName = "Jane",
                        LastName = "Smith",
                        Email = "<EMAIL>",
                        Phone = "************",
                        Address = "456 Oak Ave",
                        IsActive = true,
                        LoyaltyCode = "JS67890",
                        LoyaltyPoints = 250,
                        LastVisit = DateTime.Now,
                        TotalVisits = 8,
                        TotalSpent = 800.00m,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                );
            });

            // Configure Role entity
            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("Roles");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.Property(e => e.IsActive).HasConversion<int>();

                // Add default roles
                entity.HasData(
                    new Role { Id = 1, Name = "Admin", Description = "System Administrator", IsActive = true, CreatedAt = DateTime.Now },
                    new Role { Id = 2, Name = "Manager", Description = "Store Manager", IsActive = true, CreatedAt = DateTime.Now },
                    new Role { Id = 3, Name = "Cashier", Description = "Store Cashier", IsActive = true, CreatedAt = DateTime.Now }
                );
            });

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("Users");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired();
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Password).IsRequired();
                entity.Property(e => e.FirstName).IsRequired();
                entity.Property(e => e.LastName).IsRequired();
                entity.Property(e => e.Email).IsRequired(false);  // Make email optional
                entity.Property(e => e.Phone).IsRequired(false);  // Make phone optional
                entity.Property(e => e.IsActive).HasConversion<int>();
                entity.Property(e => e.PhotoPath).HasDefaultValue("default-user.png");

                // Configure Role relationship
                entity.HasOne(u => u.UserRole)
                    .WithMany(r => r.Users)
                    .HasForeignKey(u => u.RoleId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Add default admin user
                entity.HasData(new User
                {
                    Id = 1,
                    Username = "admin",
                    Password = "admin123", // Note: In production, this should be hashed
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    Phone = "************",
                    RoleId = 1, // Admin role
                    IsActive = true,
                    PhotoPath = "default-user.png",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });
            });

            // Configure Sale entity
            modelBuilder.Entity<Sale>(entity =>
            {
                entity.ToTable("Sales");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired();
                entity.Property(e => e.SaleDate).IsRequired();
                entity.Property(e => e.DueDate);  // Allow null for paid sales
                entity.Property(e => e.Status).IsRequired();
                entity.Property(e => e.PaymentStatus).IsRequired();
                entity.Property(e => e.PaymentMethod).IsRequired();
                entity.HasOne(e => e.Customer)
                    .WithMany(c => c.Sales)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(s => s.User)
                      .WithMany(u => u.Sales)
                      .HasForeignKey(s => s.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.Property(e => e.Subtotal).HasPrecision(18, 2);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2);
                entity.Property(e => e.TaxAmount).HasPrecision(18, 2);
                entity.Property(e => e.GrandTotal).HasPrecision(18, 2);
                entity.Property(e => e.AmountPaid).HasPrecision(18, 2);
                entity.Property(e => e.Change).HasPrecision(18, 2);
            });

            // Configure Product entity
            modelBuilder.Entity<Product>(entity =>
            {
                entity.ToTable("Products");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.SKU).IsRequired(false);
                entity.Property(e => e.Description).IsRequired(false);
                entity.Property(e => e.PurchasePrice).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.SellingPrice).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.ImageData).IsRequired(false);

                // Configure ProductType enum
                entity.Property(e => e.Type)
                    .HasConversion<int>()
                    .HasDefaultValue(ProductType.Product)
                    .IsRequired();

                // Configure IsWeightBased property
                entity.Property(e => e.IsWeightBased)
                    .HasDefaultValue(false)
                    .IsRequired();

                // Configure unified decimal stock quantity for all products
                entity.Property(e => e.StockQuantity)
                    .HasPrecision(18, 3)
                    .HasDefaultValue(0m)
                    .IsRequired();
            });

            // Configure ProductPriceTier entity
            modelBuilder.Entity<ProductPriceTier>(entity =>
            {
                entity.ToTable("ProductPriceTiers");
                entity.HasKey(e => e.Id);

                // Configure required properties
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.MinimumQuantity).HasPrecision(18, 3).IsRequired();
                entity.Property(e => e.MaximumQuantity).HasPrecision(18, 3);
                entity.Property(e => e.UnitPrice).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.PackPrice).HasPrecision(18, 2);
                entity.Property(e => e.TierName).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.Property(e => e.IsActive).HasDefaultValue(true).IsRequired();
                entity.Property(e => e.DisplayOrder).HasDefaultValue(0).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.EffectiveDate);
                entity.Property(e => e.ExpirationDate);

                // Configure relationship with Product
                entity.HasOne(pt => pt.Product)
                    .WithMany(p => p.PriceTiers)
                    .HasForeignKey(pt => pt.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Create indexes for performance
                entity.HasIndex(pt => new { pt.ProductId, pt.MinimumQuantity })
                    .HasDatabaseName("IX_ProductPriceTiers_ProductId_MinimumQuantity");

                entity.HasIndex(pt => new { pt.ProductId, pt.IsActive, pt.EffectiveDate, pt.ExpirationDate })
                    .HasDatabaseName("IX_ProductPriceTiers_ProductId_Active_Dates");

                entity.HasIndex(pt => new { pt.IsActive, pt.EffectiveDate, pt.ExpirationDate })
                    .HasDatabaseName("IX_ProductPriceTiers_Active_Dates");
            });

            // Configure SaleItem entity
            modelBuilder.Entity<SaleItem>(entity =>
            {
                entity.ToTable("SaleItems");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasPrecision(18, 3).IsRequired(); // Support decimal quantities with 3 decimal places
                entity.Property(e => e.UnitPrice).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.Total).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.ActualCostBasis).HasPrecision(18, 2).HasDefaultValue(0m).IsRequired(); // FIFO cost tracking
            });

            // Configure relationships
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Products)
                .HasForeignKey(p => p.SupplierId);

            modelBuilder.Entity<SaleItem>()
                .HasOne(si => si.Sale)
                .WithMany(s => s.Items)
                .HasForeignKey(si => si.SaleId);

            modelBuilder.Entity<SaleItem>()
                .HasOne(si => si.Product)
                .WithMany(p => p.Sales)
                .HasForeignKey(si => si.ProductId);



            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Sale)
                .WithMany(s => s.Payments)
                .HasForeignKey(p => p.SaleId);

            // Configure LoyaltyProgram
            modelBuilder.Entity<LoyaltyProgram>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.PointsPerDollar).HasPrecision(18, 2);
                entity.Property(e => e.MonetaryValuePerPoint).HasPrecision(18, 2);
                entity.HasMany(e => e.Tiers)
                      .WithOne(e => e.Program)
                      .HasForeignKey(e => e.LoyaltyProgramId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Add default loyalty program
                entity.HasData(
                    new LoyaltyProgram
                    {
                        Id = 1,
                        Name = "Standard Rewards",
                        Description = "Standard customer loyalty program",
                        PointsPerDollar = 1.0m,
                        MonetaryValuePerPoint = 0.01m,
                        ExpiryMonths = 12,
                        MinimumPointsRedemption = 100,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    }
                );
            });

            // Configure LoyaltyTier
            modelBuilder.Entity<LoyaltyTier>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.MinimumPoints).HasPrecision(18, 2);
                entity.Property(e => e.PointsMultiplier).HasPrecision(18, 2);

                // Add default loyalty tiers
                entity.HasData(
                    new LoyaltyTier
                    {
                        Id = 1,
                        LoyaltyProgramId = 1,
                        Name = "Bronze",
                        MinimumPoints = 0,
                        PointsMultiplier = 1.0m,
                        Benefits = "Basic rewards earning"
                    },
                    new LoyaltyTier
                    {
                        Id = 2,
                        LoyaltyProgramId = 1,
                        Name = "Silver",
                        MinimumPoints = 1000,
                        PointsMultiplier = 1.25m,
                        Benefits = "25% bonus points earning"
                    },
                    new LoyaltyTier
                    {
                        Id = 3,
                        LoyaltyProgramId = 1,
                        Name = "Gold",
                        MinimumPoints = 5000,
                        PointsMultiplier = 1.5m,
                        Benefits = "50% bonus points earning"
                    }
                );
            });

            // Configure LoyaltyTransaction
            modelBuilder.Entity<LoyaltyTransaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Points).HasPrecision(18, 2);
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.LoyaltyTransactions)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SaleHistory>(entity =>
            {
                entity.ToTable("SaleHistory");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Action).IsRequired();
                entity.Property(e => e.ActionDate).IsRequired();
                entity.HasOne(e => e.Sale)
                    .WithMany()
                    .HasForeignKey(e => e.SaleId)
                    .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Supplier entity
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.ToTable("Suppliers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasConversion<int>().HasDefaultValue(true);
                entity.Property(e => e.ProductCount).HasDefaultValue(0);
            });

            // Configure PurchaseOrder entity
            modelBuilder.Entity<PurchaseOrder>(entity =>
            {
                entity.ToTable("PurchaseOrders");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired();
                entity.Property(e => e.OrderDate).IsRequired();
                entity.Property(e => e.Status).IsRequired().HasDefaultValue("Pending");
                entity.Property(e => e.Subtotal).HasColumnType("decimal(18,2)").HasDefaultValue(0m);
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0m);
                entity.Property(e => e.GrandTotal).HasColumnType("decimal(18,2)").HasDefaultValue(0m);

                entity.HasOne(e => e.Supplier)
                      .WithMany(s => s.PurchaseOrders)
                      .HasForeignKey(e => e.SupplierId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure PurchaseOrderItem entity
            modelBuilder.Entity<PurchaseOrderItem>(entity =>
            {
                entity.ToTable("PurchaseOrderItems");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasPrecision(18, 3).IsRequired(); // Support decimal quantities with 3 decimal places
                entity.Property(e => e.UnitCost).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.Location).IsRequired(false); // Make Location nullable
                entity.HasOne(e => e.PurchaseOrder)
                      .WithMany(p => p.Items)
                      .HasForeignKey(e => e.PurchaseOrderId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.Product)
                      .WithMany()
                      .HasForeignKey(e => e.ProductId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<ProductBarcode>(entity =>
            {
                entity.HasIndex(e => e.Barcode).IsUnique();

                entity.HasOne(e => e.Product)
                      .WithMany(p => p.Barcodes)
                      .HasForeignKey(e => e.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure UnitOfMeasure entity
            modelBuilder.Entity<UnitOfMeasure>(entity =>
            {
                entity.ToTable("UnitsOfMeasure");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Abbreviation).IsRequired();
                entity.Property(e => e.Type).IsRequired();
                entity.Property(e => e.ConversionFactor).HasPrecision(18, 6);
                entity.Property(e => e.IsActive).HasConversion<int>();
                entity.Property(e => e.CreatedAt).IsRequired();

                // Configure self-referencing relationship for base units
                entity.HasOne(e => e.BaseUnit)
                      .WithMany(e => e.DerivedUnits)
                      .HasForeignKey(e => e.BaseUnitId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Configure relationship with Products
                entity.HasMany(e => e.Products)
                      .WithOne(e => e.UnitOfMeasure)
                      .HasForeignKey(e => e.UnitOfMeasureId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Add default units of measure
                entity.HasData(
                    new UnitOfMeasure
                    {
                        Id = 1,
                        Name = "Piece",
                        Abbreviation = "pc",
                        Type = "Unit",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 2,
                        Name = "Kilogram",
                        Abbreviation = "kg",
                        Type = "Weight",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 3,
                        Name = "Gram",
                        Abbreviation = "g",
                        Type = "Weight",
                        BaseUnitId = 2,
                        ConversionFactor = 0.001m,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 4,
                        Name = "Liter",
                        Abbreviation = "L",
                        Type = "Volume",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 5,
                        Name = "Milliliter",
                        Abbreviation = "mL",
                        Type = "Volume",
                        BaseUnitId = 4,
                        ConversionFactor = 0.001m,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 6,
                        Name = "Box",
                        Abbreviation = "box",
                        Type = "Package",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 7,
                        Name = "Carton",
                        Abbreviation = "ctn",
                        Type = "Package",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    },
                    new UnitOfMeasure
                    {
                        Id = 8,
                        Name = "Dozen",
                        Abbreviation = "dz",
                        Type = "Unit",
                        BaseUnitId = 1,
                        ConversionFactor = 12m,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    }
                );
            });

            modelBuilder.Entity<Product>()
                .Property(p => p.TrackBatches)
                .HasDefaultValue(false);

            modelBuilder.Entity<BatchStock>(entity =>
            {
                entity.ToTable("BatchStock");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                    .ValueGeneratedOnAdd()
                    .Metadata.SetBeforeSaveBehavior(Microsoft.EntityFrameworkCore.Metadata.PropertySaveBehavior.Ignore);

                // Configure unified decimal quantity for all batch stock
                entity.Property(e => e.Quantity)
                    .HasPrecision(18, 3)
                    .HasDefaultValue(0m)
                    .IsRequired();

                entity.HasOne(e => e.Product)
                      .WithMany(p => p.Batches)
                      .HasForeignKey(e => e.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure selling price precision
                entity.Property(e => e.SellingPrice).HasPrecision(18, 2);

                // Add index on BatchNumber and ProductId combination
                entity.HasIndex(e => new { e.BatchNumber, e.ProductId })
                      .IsUnique();
            });

            // Configure DiscountPermission
            modelBuilder.Entity<DiscountPermission>(entity =>
            {
                entity.ToTable("DiscountPermissions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.MaxPercentage).HasPrecision(5, 2);
                entity.Property(e => e.MaxFixedAmount).HasPrecision(10, 2);
                entity.Property(e => e.MinPricePercentage).HasPrecision(5, 2);
                entity.Property(e => e.IsActive).HasConversion<int>();

                // Configure Role relationship
                entity.HasOne(dp => dp.Role)
                    .WithMany(r => r.DiscountPermissions)
                    .HasForeignKey(dp => dp.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Configure DiscountType relationship
                entity.HasOne(dp => dp.DiscountType)
                    .WithMany(dt => dt.Permissions)
                    .HasForeignKey(dp => dp.DiscountTypeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Discount
            modelBuilder.Entity<Discount>()
                .HasOne(d => d.Sale)
                .WithMany()
                .HasForeignKey(d => d.SaleId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Discount>()
                .HasOne(d => d.SaleItem)
                .WithMany()
                .HasForeignKey(d => d.SaleItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Discount>()
                .HasOne(d => d.DiscountType)
                .WithMany(dt => dt.Discounts)
                .HasForeignKey(d => d.DiscountTypeId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Discount>()
                .HasOne(d => d.Reason)
                .WithMany(dr => dr.Discounts)
                .HasForeignKey(d => d.ReasonId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Discount>()
                .HasOne(d => d.AppliedByUser)
                .WithMany()
                .HasForeignKey(d => d.AppliedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Discount>()
                .HasOne(d => d.ApprovedByUser)
                .WithMany()
                .HasForeignKey(d => d.ApprovedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed Discount Types
            modelBuilder.Entity<DiscountType>().HasData(
                new DiscountType { Id = 1, Name = "Percentage", Description = "Percentage off the original price" },
                new DiscountType { Id = 2, Name = "Fixed Amount", Description = "Fixed amount off the original price" },
                new DiscountType { Id = 3, Name = "Price Override", Description = "Override with a specific price" }
            );

            // Seed Discount Reasons
            modelBuilder.Entity<DiscountReason>().HasData(
                new DiscountReason { Id = 1, Code = "MANAGER", Description = "Manager Special", IsActive = true },
                new DiscountReason { Id = 2, Code = "DAMAGED", Description = "Damaged Item", IsActive = true },
                new DiscountReason { Id = 3, Code = "PRICEMATCH", Description = "Price Match", IsActive = true },
                new DiscountReason { Id = 4, Code = "CUSTOMER", Description = "Customer Satisfaction", IsActive = true },
                new DiscountReason { Id = 5, Code = "PROMO", Description = "Promotion", IsActive = true },
                new DiscountReason { Id = 6, Code = "BULK", Description = "Bulk Purchase", IsActive = true },
                new DiscountReason { Id = 7, Code = "LOYALTY", Description = "Loyalty Discount", IsActive = true }
            );

            // Configure decimal precision
            modelBuilder.Entity<Discount>()
                .Property(d => d.DiscountValue)
                .HasColumnType("DECIMAL(10,2)");

            modelBuilder.Entity<Discount>()
                .Property(d => d.OriginalPrice)
                .HasColumnType("DECIMAL(10,2)");

            modelBuilder.Entity<Discount>()
                .Property(d => d.FinalPrice)
                .HasColumnType("DECIMAL(10,2)");

            // Configure CashDrawer entity
            modelBuilder.Entity<CashDrawer>(entity =>
            {
                entity.ToTable("CashDrawers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OpeningBalance).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.CurrentBalance).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.ExpectedBalance).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.ActualBalance).HasPrecision(18, 2);
                entity.Property(e => e.Difference).HasPrecision(18, 2);
                entity.Property(e => e.Status).IsRequired();
                entity.Property(e => e.Notes).IsRequired(false);  // Making Notes nullable

                // Ignore properties not stored in database
                entity.Ignore(e => e.ClosingBalance); // ActualBalance serves this purpose
                entity.Ignore(e => e.IsActive); // Status field serves this purpose

                // Configure relationships with nullable ClosedById
                entity.HasOne(d => d.OpenedBy)
                    .WithMany()
                    .HasForeignKey(d => d.OpenedById)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);  // Make OpenedById nullable to match model

                entity.HasOne(d => d.ClosedBy)
                    .WithMany()
                    .HasForeignKey(d => d.ClosedById)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);  // This makes ClosedById nullable
            });

            // Configure CashTransaction entity
            modelBuilder.Entity<CashTransaction>(entity =>
            {
                entity.ToTable("CashTransactions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).IsRequired();
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Reason).IsRequired(false);  // Making Reason nullable
                entity.Property(e => e.Reference).HasMaxLength(50).IsRequired(false);
                entity.Property(e => e.Notes).HasMaxLength(500).IsRequired(false);
                entity.Property(e => e.Timestamp).IsRequired();

                // Configure relationship with CashDrawer
                entity.HasOne<CashDrawer>()
                    .WithMany(d => d.Transactions)
                    .HasForeignKey(t => t.CashDrawerId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with User (PerformedBy)
                entity.HasOne(t => t.PerformedBy)
                    .WithMany()
                    .HasForeignKey("PerformedById")
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure ProductAlert entity
            modelBuilder.Entity<ProductAlert>(entity =>
            {
                entity.ToTable("ProductAlerts");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AlertType).HasMaxLength(50);
                entity.Property(e => e.Message).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.IsRead).HasDefaultValue(false);
                entity.Property(e => e.ReferenceId);
                entity.Property(e => e.ReferenceType).HasMaxLength(50);

                entity.HasOne(e => e.Product)
                    .WithMany()
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure BusinessExpense entity
            modelBuilder.Entity<BusinessExpense>(entity =>
            {
                entity.ToTable("BusinessExpenses");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).IsRequired();
                entity.Property(e => e.Amount).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.Date).IsRequired();
                entity.Property(e => e.Category).IsRequired();
                entity.Property(e => e.Frequency).IsRequired();
                entity.Property(e => e.Notes).IsRequired(false); // Make Notes nullable
                entity.Property(e => e.NextDueDate).IsRequired(false);

                // Configure relationships
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.CashDrawer)
                    .WithMany()
                    .HasForeignKey(e => e.CashDrawerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure UserFavorite entity
            modelBuilder.Entity<UserFavorite>(entity =>
            {
                entity.ToTable("UserFavorites");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CreatedAt).IsRequired();

                // Configure relationships
                entity.HasOne(e => e.User)
                    .WithMany(u => u.Favorites)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Product)
                    .WithMany(p => p.FavoritedBy)
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Create a unique index for UserId and ProductId combination
                entity.HasIndex(e => new { e.UserId, e.ProductId }).IsUnique();
            });

            // Configure UserPermissions entity
            modelBuilder.Entity<UserPermissions>(entity =>
            {
                entity.ToTable("UserPermissions");
                entity.HasKey(e => e.Id);

                // Configure relationship with User
                entity.HasOne(e => e.User)
                      .WithOne(u => u.Permissions)
                      .HasForeignKey<UserPermissions>(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure boolean properties to be stored as integers
                entity.Property(e => e.CanCreateSales).HasConversion<int>();
                entity.Property(e => e.CanVoidSales).HasConversion<int>();
                entity.Property(e => e.CanApplyDiscount).HasConversion<int>();
                entity.Property(e => e.CanViewSalesHistory).HasConversion<int>();
                entity.Property(e => e.CanManageProducts).HasConversion<int>();
                entity.Property(e => e.CanManageCategories).HasConversion<int>();
                entity.Property(e => e.CanViewInventory).HasConversion<int>();
                entity.Property(e => e.CanAdjustInventory).HasConversion<int>();
                entity.Property(e => e.CanManageExpenses).HasConversion<int>();
                entity.Property(e => e.CanManageCashDrawer).HasConversion<int>();
                entity.Property(e => e.CanViewReports).HasConversion<int>();
                entity.Property(e => e.CanManagePrices).HasConversion<int>();
                entity.Property(e => e.CanManageCustomers).HasConversion<int>();
                entity.Property(e => e.CanManageSuppliers).HasConversion<int>();
                entity.Property(e => e.CanManageUsers).HasConversion<int>();
                entity.Property(e => e.CanManageRoles).HasConversion<int>();
                entity.Property(e => e.CanAccessSettings).HasConversion<int>();
                entity.Property(e => e.CanViewLogs).HasConversion<int>();

                // Invoice permissions (Two-Tier System)
                entity.Property(e => e.CanCreateFullInvoices).HasConversion<int>();
                entity.Property(e => e.CanCreateDraftInvoices).HasConversion<int>();
                entity.Property(e => e.CanCompleteInvoiceDrafts).HasConversion<int>();
                entity.Property(e => e.CanViewPendingDrafts).HasConversion<int>();
                entity.Property(e => e.CanModifyInvoicePricing).HasConversion<int>();
                entity.Property(e => e.CanSetPaymentTerms).HasConversion<int>();
                entity.Property(e => e.CanSelectCustomersForInvoices).HasConversion<int>();
                entity.Property(e => e.CanDeleteDraftInvoices).HasConversion<int>();
                entity.Property(e => e.CanRejectDraftInvoices).HasConversion<int>();
                entity.Property(e => e.CanManageInvoiceSettings).HasConversion<int>();
            });

            // Configure Invoice entity (Two-Tier System)
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.ToTable("Invoice");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(20); // Sales or Purchase
                entity.Property(e => e.IssueDate).IsRequired();
                entity.Property(e => e.DueDate).IsRequired();
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20).HasDefaultValue("Draft");
                entity.Property(e => e.PaymentTerms).HasMaxLength(100);
                entity.Property(e => e.Reference).HasMaxLength(100);
                entity.Property(e => e.Notes);
                entity.Property(e => e.Subtotal).HasPrecision(18, 2).HasDefaultValue(0);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2).HasDefaultValue(0);
                entity.Property(e => e.TaxAmount).HasPrecision(18, 2).HasDefaultValue(0);
                entity.Property(e => e.GrandTotal).HasPrecision(18, 2).HasDefaultValue(0);

                // Two-tier system properties
                entity.Property(e => e.CreatedByUserId).IsRequired();
                entity.Property(e => e.CompletedByUserId).IsRequired(false);
                entity.Property(e => e.DraftCreatedAt).IsRequired();
                entity.Property(e => e.AdminCompletedAt).IsRequired(false);
                entity.Property(e => e.RequiresAdminCompletion).HasConversion<int>().HasDefaultValue(false);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();

                // Configure relationships
                entity.HasOne(e => e.Customer)
                    .WithMany()
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false); // Allow null for purchase invoices

                entity.HasOne(e => e.Supplier)
                    .WithMany()
                    .HasForeignKey(e => e.SupplierId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false); // Allow null for sales invoices

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired();

                entity.HasOne(e => e.CompletedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CompletedByUserId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);

                // Indexes for two-tier system performance
                entity.HasIndex(e => e.Status)
                    .HasDatabaseName("IX_Invoice_Status");
                entity.HasIndex(e => e.RequiresAdminCompletion)
                    .HasDatabaseName("IX_Invoice_RequiresAdminCompletion");
                entity.HasIndex(e => e.CreatedByUserId)
                    .HasDatabaseName("IX_Invoice_CreatedByUserId");
                entity.HasIndex(e => e.CompletedByUserId)
                    .HasDatabaseName("IX_Invoice_CompletedByUserId");
                entity.HasIndex(e => e.DraftCreatedAt)
                    .HasDatabaseName("IX_Invoice_DraftCreatedAt");
                entity.HasIndex(e => new { e.Type, e.Status })
                    .HasDatabaseName("IX_Invoice_Type_Status");
                entity.HasIndex(e => new { e.Status, e.RequiresAdminCompletion })
                    .HasDatabaseName("IX_Invoice_Status_RequiresAdminCompletion");
            });

            // Configure InvoiceItem entity
            modelBuilder.Entity<InvoiceItem>(entity =>
            {
                entity.ToTable("InvoiceItem");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasPrecision(18, 3).IsRequired(); // Support decimal quantities with 3 decimal places
                entity.Property(e => e.UnitPrice).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.SellingPrice).HasPrecision(18, 2);
                entity.Property(e => e.Total).HasPrecision(18, 2).IsRequired();

                // Configure relationships
                entity.HasOne(e => e.Invoice)
                    .WithMany(i => i.Items)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                entity.HasOne(e => e.Product)
                    .WithMany()
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired();

                // Ignore CustomProperties as it's not a database column but a Dictionary
                entity.Ignore(e => e.CustomProperties);

                // Add BatchNumber as a regular string property
                entity.Property(e => e.BatchNumber).IsRequired(false);
            });

            // Configure InvoicePayment entity
            modelBuilder.Entity<InvoicePayment>(entity =>
            {
                entity.ToTable("InvoicePayment");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.PaymentDate).IsRequired();
                entity.Property(e => e.Amount).HasPrecision(18, 2).IsRequired();
                entity.Property(e => e.PaymentMethod).IsRequired();

                // Configure relationships
                entity.HasOne(e => e.Invoice)
                    .WithMany(i => i.Payments)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();
            });

            // Configure DraftInvoiceNotification entity (Two-Tier System)
            modelBuilder.Entity<DraftInvoiceNotification>(entity =>
            {
                entity.ToTable("DraftInvoiceNotifications");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.NotificationType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Message).HasMaxLength(500);
                entity.Property(e => e.IsRead).HasConversion<int>().HasDefaultValue(false);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.ReadAt).IsRequired(false);

                // Foreign key relationships
                entity.HasOne(e => e.Invoice)
                    .WithMany(i => i.Notifications)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired();

                // Indexes for performance
                entity.HasIndex(e => new { e.IsRead, e.CreatedAt })
                    .HasDatabaseName("IX_DraftNotifications_IsRead_CreatedAt");
                entity.HasIndex(e => e.InvoiceId)
                    .HasDatabaseName("IX_DraftNotifications_InvoiceId");
                entity.HasIndex(e => e.CreatedByUserId)
                    .HasDatabaseName("IX_DraftNotifications_CreatedByUserId");
                entity.HasIndex(e => e.NotificationType)
                    .HasDatabaseName("IX_DraftNotifications_NotificationType");
            });

            // Configure DraftInvoiceSettings entity (Two-Tier System)
            modelBuilder.Entity<DraftInvoiceSettings>(entity =>
            {
                entity.ToTable("DraftInvoiceSettings");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ExpirationDays).IsRequired().HasDefaultValue(7);
                entity.Property(e => e.AutoNotifyAdmins).HasConversion<int>().HasDefaultValue(true);
                entity.Property(e => e.AllowNonAdminCustomerSelection).HasConversion<int>().HasDefaultValue(true);
                entity.Property(e => e.RequireReasonForDraft).HasConversion<int>().HasDefaultValue(false);
                entity.Property(e => e.MaxDraftItemsPerInvoice).IsRequired().HasDefaultValue(50);
                entity.Property(e => e.EnableEmailNotifications).HasConversion<int>().HasDefaultValue(false);
                entity.Property(e => e.DraftCreatedEmailTemplate).HasMaxLength(1000);
                entity.Property(e => e.DraftCompletedEmailTemplate).HasMaxLength(1000);
                entity.Property(e => e.AutoArchiveExpiredDrafts).HasConversion<int>().HasDefaultValue(true);
                entity.Property(e => e.ArchiveRetentionDays).IsRequired().HasDefaultValue(90);
                entity.Property(e => e.RequireApprovalForHighValue).HasConversion<int>().HasDefaultValue(false);
                entity.Property(e => e.HighValueThreshold).HasPrecision(18, 2).HasDefaultValue(1000.00m);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
            });

            // Configure Category entity
            modelBuilder.Entity<Category>(entity =>
            {
                entity.ToTable("Categories");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Description).IsRequired(false);
                entity.HasMany(e => e.Products)
                      .WithOne(p => p.Category)
                      .HasForeignKey(p => p.CategoryId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Receipt Printing entities
            modelBuilder.Entity<ReceiptTemplate>(entity =>
            {
                entity.ToTable("ReceiptTemplates");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.TemplateType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FooterText).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.ModifiedDate).IsRequired();
            });

            modelBuilder.Entity<PrinterConfiguration>(entity =>
            {
                entity.ToTable("PrinterConfigurations");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PrinterName).HasMaxLength(200);
                entity.Property(e => e.PrinterType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PaperSize).HasMaxLength(50);
                entity.Property(e => e.PrintQuality).HasMaxLength(50);
                entity.Property(e => e.ConnectionSettings).HasMaxLength(1000);

                // Foreign key to ReceiptTemplate
                entity.HasOne(e => e.ReceiptTemplate)
                    .WithMany()
                    .HasForeignKey(e => e.ReceiptTemplateId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<ReceiptPrintSettings>(entity =>
            {
                entity.ToTable("ReceiptPrintSettings");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.PdfBackupPath).HasMaxLength(500);

                // Foreign key relationships
                entity.HasOne(e => e.DefaultPrinterConfig)
                    .WithMany()
                    .HasForeignKey(e => e.DefaultPrinterConfigId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.DefaultReceiptTemplate)
                    .WithMany()
                    .HasForeignKey(e => e.DefaultReceiptTemplateId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<ReceiptPrintJob>(entity =>
            {
                entity.ToTable("ReceiptPrintJobs");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ErrorMessage).HasMaxLength(1000);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.StartedAt);
                entity.Property(e => e.CompletedAt);

                // Foreign key relationships
                entity.HasOne(e => e.Sale)
                    .WithMany()
                    .HasForeignKey(e => e.SaleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.PrinterConfig)
                    .WithMany()
                    .HasForeignKey(e => e.PrinterConfigId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ReceiptTemplate)
                    .WithMany()
                    .HasForeignKey(e => e.ReceiptTemplateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                try
                {
                    // Default configuration if none is provided
                    string dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");

                    // ✅ ENHANCED: Ensure directory exists and handle file access
                    var dbDirectory = Path.GetDirectoryName(dbPath);
                    if (!Directory.Exists(dbDirectory))
                    {
                        Directory.CreateDirectory(dbDirectory);
                    }

                    // ✅ ENHANCED: Use simple connection string to avoid encryption issues
                    var connectionString = $"Data Source={dbPath};Cache=Shared;";
                    optionsBuilder.UseSqlite(connectionString);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Database configuration error: {ex.Message}");
                    // Fallback to in-memory database if file access fails
                    optionsBuilder.UseSqlite("Data Source=:memory:");
                }
            }

            // ✅ CRITICAL FIX: Configure SQLite with performance optimizations
            optionsBuilder
                .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
                .ConfigureWarnings(warnings =>
                {
                    warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
                    warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.MultipleCollectionIncludeWarning);
                });

#if DEBUG
            // ✅ SUPPRESS EF CORE SQL QUERY LOGGING: Completely disable SQL command logging in debug mode
            // Only log critical errors, not regular database commands
            optionsBuilder.LogTo(message =>
                {
                    // Only log actual errors, not normal SQL commands
                    if (message.Contains("Exception") || message.Contains("Failed") || message.Contains("Critical"))
                    {
                        System.Diagnostics.Debug.WriteLine($"[EF_ERROR] {message}");
                    }
                },
                new[] { DbLoggerCategory.Database.Command.Name },
                Microsoft.Extensions.Logging.LogLevel.Error);

            // Enable detailed errors in debug mode for actual errors only
            optionsBuilder.EnableDetailedErrors();
#endif
        }

        /// <summary>
        /// Optimizes a SQLite connection with performance-enhancing pragmas
        /// </summary>
        public static void OptimizeSqliteConnection(Microsoft.Data.Sqlite.SqliteConnection connection)
        {
            if (connection.State != System.Data.ConnectionState.Open)
            {
                connection.Open();
            }

            // Use Write-Ahead Logging for better concurrency and performance
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA journal_mode = WAL;";
                cmd.ExecuteNonQuery();
            }

            // Set synchronous mode to NORMAL for better performance while maintaining data integrity
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA synchronous = NORMAL;";
                cmd.ExecuteNonQuery();
            }

            // Increase cache size for better performance
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA cache_size = 10000;";
                cmd.ExecuteNonQuery();
            }

            // Use memory for temporary storage
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA temp_store = MEMORY;";
                cmd.ExecuteNonQuery();
            }

            // Enable mmap to improve read performance
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA mmap_size = 30000000000;";
                cmd.ExecuteNonQuery();
            }

            // Set busy timeout to avoid connection contention issues
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA busy_timeout = 5000;";
                cmd.ExecuteNonQuery();
            }

            // Enable foreign keys (safety feature that doesn't impact performance much)
            using (var cmd = connection.CreateCommand())
            {
                cmd.CommandText = "PRAGMA foreign_keys = ON;";
                cmd.ExecuteNonQuery();
            }

            // Fix BatchStock table schema
            FixBatchStockSchema(connection);

            // Migrate to unified decimal stock system
            MigrateToUnifiedDecimalStock(connection);
        }

        public static void FixBatchStockSchema(Microsoft.Data.Sqlite.SqliteConnection connection)
        {
            try
            {
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    connection.Open();
                }

                // First, detect existing columns to avoid dropping important data like SellingPrice
                bool hasSellingPrice = false;
                using (var infoCmd = connection.CreateCommand())
                {
                    infoCmd.CommandText = "PRAGMA table_info(BatchStock)";
                    using (var rdr = infoCmd.ExecuteReader())
                    {
                        while (rdr.Read())
                        {
                            var columnName = rdr.GetString(1);
                            if (string.Equals(columnName, "SellingPrice", StringComparison.OrdinalIgnoreCase))
                            {
                                hasSellingPrice = true;
                            }
                        }
                    }
                }

                // If the table already has SellingPrice, skip the destructive rebuild to preserve data
                if (hasSellingPrice)
                {
                    System.Diagnostics.Debug.WriteLine("[DB_SCHEMA] BatchStock schema already includes SellingPrice - skipping rebuild to preserve data");
                    return;
                }

                // Create a new table with correct auto-increment and include SellingPrice column
                using (var cmd = connection.CreateCommand())
                {
                    cmd.CommandText = @"
                        BEGIN TRANSACTION;

                        -- Create new table with correct auto-increment (including SellingPrice)
                        CREATE TABLE IF NOT EXISTS BatchStock_new (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            BatchNumber TEXT NOT NULL,
                            Quantity INTEGER NOT NULL,
                            QuantityDecimal DECIMAL(18,3) NOT NULL DEFAULT 0,
                            ManufactureDate TEXT,
                            ExpiryDate TEXT,
                            CreatedAt TEXT NOT NULL,
                            ProductId INTEGER NOT NULL,
                            PurchasePrice DECIMAL(18,2),
                            SellingPrice DECIMAL(18,2) NOT NULL DEFAULT 0,
                            Location TEXT,
                            Notes TEXT,
                            FOREIGN KEY(ProductId) REFERENCES Products(Id) ON DELETE CASCADE
                        );

                        -- Copy existing data (SellingPrice will default to 0 for old rows)
                        INSERT OR IGNORE INTO BatchStock_new
                            (BatchNumber, Quantity, QuantityDecimal, ManufactureDate, ExpiryDate, CreatedAt,
                             ProductId, PurchasePrice, SellingPrice, Location, Notes)
                        SELECT
                            BatchNumber, Quantity, CAST(Quantity AS DECIMAL(18,3)), ManufactureDate, ExpiryDate, CreatedAt,
                            ProductId, PurchasePrice, 0 as SellingPrice, Location, Notes
                        FROM BatchStock;

                        -- Drop old table and rename new one
                        DROP TABLE IF EXISTS BatchStock;
                        ALTER TABLE BatchStock_new RENAME TO BatchStock;

                        -- Create unique index
                        CREATE UNIQUE INDEX IF NOT EXISTS idx_batch_product
                        ON BatchStock(BatchNumber, ProductId);

                        COMMIT;";

                    cmd.ExecuteNonQuery();
                    System.Diagnostics.Debug.WriteLine("[DB_SCHEMA] BatchStock table schema updated successfully (with SellingPrice)");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DB_SCHEMA] Error fixing BatchStock schema: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[DB_SCHEMA] Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public static void MigrateToUnifiedDecimalStock(Microsoft.Data.Sqlite.SqliteConnection connection)
        {
            try
            {
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    connection.Open();
                }

                System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] Starting unified decimal stock migration...");

                // Step 1: Check current Products table schema
                using (var cmd = connection.CreateCommand())
                {
                    cmd.CommandText = "PRAGMA table_info(Products);";
                    using (var reader = cmd.ExecuteReader())
                    {
                        bool hasStockQuantityDecimal = false;
                        bool stockQuantityIsDecimal = false;

                        while (reader.Read())
                        {
                            string columnName = reader.GetString(1);
                            string columnType = reader.GetString(2);

                            if (columnName == "StockQuantityDecimal")
                            {
                                hasStockQuantityDecimal = true;
                            }
                            else if (columnName == "StockQuantity")
                            {
                                stockQuantityIsDecimal = columnType.ToUpper().Contains("DECIMAL");
                            }
                        }

                        reader.Close();

                        // Step 2: Migrate Products table to unified decimal stock
                        if (!stockQuantityIsDecimal)
                        {
                            System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] Converting Products.StockQuantity to decimal...");

                            // Create new table with decimal StockQuantity
                            using (var migrationCmd = connection.CreateCommand())
                            {
                                migrationCmd.CommandText = @"
                                    -- Create new Products table with decimal StockQuantity
                                    CREATE TABLE IF NOT EXISTS Products_new (
                                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        Name TEXT NOT NULL,
                                        SKU TEXT,
                                        Description TEXT,
                                        PurchasePrice DECIMAL(18,2) NOT NULL,
                                        SellingPrice DECIMAL(18,2) NOT NULL,
                                        StockQuantity DECIMAL(18,3) NOT NULL DEFAULT 0,
                                        MinimumStock INTEGER NOT NULL DEFAULT 0,
                                        ReorderPoint INTEGER NOT NULL DEFAULT 0,
                                        CategoryId INTEGER,
                                        SupplierId INTEGER,
                                        UnitOfMeasureId INTEGER,
                                        IsActive INTEGER NOT NULL DEFAULT 1,
                                        IsFavorited INTEGER NOT NULL DEFAULT 0,
                                        Type INTEGER NOT NULL DEFAULT 0,
                                        HasExpiry INTEGER NOT NULL DEFAULT 0,
                                        ExpiryDate TEXT,
                                        TrackBatches INTEGER NOT NULL DEFAULT 0,
                                        IsWeightBased INTEGER NOT NULL DEFAULT 0,
                                        ImageData TEXT,
                                        Barcode TEXT,
                                        CreatedAt TEXT NOT NULL,
                                        UpdatedAt TEXT NOT NULL,
                                        FOREIGN KEY(CategoryId) REFERENCES Categories(Id),
                                        FOREIGN KEY(SupplierId) REFERENCES Suppliers(Id),
                                        FOREIGN KEY(UnitOfMeasureId) REFERENCES UnitsOfMeasure(Id)
                                    );";
                                migrationCmd.ExecuteNonQuery();

                                // Copy data, converting StockQuantity to decimal and using StockQuantityDecimal if available
                                if (hasStockQuantityDecimal)
                                {
                                    migrationCmd.CommandText = @"
                                        INSERT INTO Products_new
                                        SELECT Id, Name, SKU, Description, PurchasePrice, SellingPrice,
                                               COALESCE(StockQuantityDecimal, CAST(StockQuantity AS DECIMAL(18,3))) as StockQuantity,
                                               MinimumStock, ReorderPoint, CategoryId, SupplierId, UnitOfMeasureId,
                                               IsActive, IsFavorited, Type, HasExpiry, ExpiryDate, TrackBatches,
                                               IsWeightBased, ImageData, Barcode, CreatedAt, UpdatedAt
                                        FROM Products;";
                                }
                                else
                                {
                                    migrationCmd.CommandText = @"
                                        INSERT INTO Products_new
                                        SELECT Id, Name, SKU, Description, PurchasePrice, SellingPrice,
                                               CAST(StockQuantity AS DECIMAL(18,3)) as StockQuantity,
                                               MinimumStock, ReorderPoint, CategoryId, SupplierId, UnitOfMeasureId,
                                               IsActive, IsFavorited, Type, HasExpiry, ExpiryDate, TrackBatches,
                                               IsWeightBased, ImageData, Barcode, CreatedAt, UpdatedAt
                                        FROM Products;";
                                }
                                migrationCmd.ExecuteNonQuery();

                                // Replace old table
                                migrationCmd.CommandText = "DROP TABLE Products;";
                                migrationCmd.ExecuteNonQuery();

                                migrationCmd.CommandText = "ALTER TABLE Products_new RENAME TO Products;";
                                migrationCmd.ExecuteNonQuery();

                                System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] Products table migrated to unified decimal stock");
                            }
                        }
                    }
                }

                // Step 3: Migrate BatchStock table to unified decimal quantity
                using (var cmd = connection.CreateCommand())
                {
                    cmd.CommandText = "PRAGMA table_info(BatchStock);";
                    using (var reader = cmd.ExecuteReader())
                    {
                        bool quantityIsDecimal = false;

                        while (reader.Read())
                        {
                            string columnName = reader.GetString(1);
                            string columnType = reader.GetString(2);

                            if (columnName == "Quantity")
                            {
                                quantityIsDecimal = columnType.ToUpper().Contains("DECIMAL");
                                break;
                            }
                        }

                        reader.Close();

                        if (!quantityIsDecimal)
                        {
                            System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] Converting BatchStock.Quantity to decimal...");

                            using (var migrationCmd = connection.CreateCommand())
                            {
                                // Create new BatchStock table with decimal Quantity
                                migrationCmd.CommandText = @"
                                    CREATE TABLE IF NOT EXISTS BatchStock_new (
                                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        ProductId INTEGER NOT NULL,
                                        BatchNumber TEXT NOT NULL,
                                        Quantity DECIMAL(18,3) NOT NULL DEFAULT 0,
                                        ManufactureDate TEXT,
                                        ExpiryDate TEXT,
                                        PurchasePrice DECIMAL(18,2),
                                        Location TEXT,
                                        Notes TEXT,
                                        CreatedAt TEXT NOT NULL,
                                        FOREIGN KEY(ProductId) REFERENCES Products(Id) ON DELETE CASCADE
                                    );";
                                migrationCmd.ExecuteNonQuery();

                                // Copy data, converting Quantity to decimal and using QuantityDecimal if available
                                migrationCmd.CommandText = @"
                                    INSERT INTO BatchStock_new
                                    SELECT Id, ProductId, BatchNumber,
                                           COALESCE(QuantityDecimal, CAST(Quantity AS DECIMAL(18,3))) as Quantity,
                                           ManufactureDate, ExpiryDate, PurchasePrice, Location, Notes, CreatedAt
                                    FROM BatchStock;";
                                migrationCmd.ExecuteNonQuery();

                                // Replace old table
                                migrationCmd.CommandText = "DROP TABLE BatchStock;";
                                migrationCmd.ExecuteNonQuery();

                                migrationCmd.CommandText = "ALTER TABLE BatchStock_new RENAME TO BatchStock;";
                                migrationCmd.ExecuteNonQuery();

                                // Recreate indexes
                                migrationCmd.CommandText = "CREATE INDEX IF NOT EXISTS IX_BatchStock_ProductId ON BatchStock(ProductId);";
                                migrationCmd.ExecuteNonQuery();

                                System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] BatchStock table migrated to unified decimal quantity");
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("[DB_MIGRATION] Unified decimal stock migration completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DB_MIGRATION] Error during unified decimal stock migration: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[DB_MIGRATION] Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }
    }
}