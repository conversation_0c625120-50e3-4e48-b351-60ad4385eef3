using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Views
{
    public class CloseDrawerResult
    {
        public decimal ActualBalance { get; set; }
        public string Notes { get; set; }
    }

    public partial class CloseDrawerDialog : UserControl
    {
        private readonly CashDrawer _drawer;
        private decimal _expectedBalance;

        public CloseDrawerDialog(CashDrawer drawer)
        {
            InitializeComponent();
            _drawer = drawer;
            _expectedBalance = drawer.ExpectedBalance;
            ExpectedBalanceText.Text = _expectedBalance.ToString("C2");
        }

        private void ActualBalance_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(ActualBalanceTextBox.Text, out decimal actualBalance))
            {
                decimal difference = actualBalance - _expectedBalance;
                DifferenceText.Text = difference.ToString("C2");
            }
            else
            {
                DifferenceText.Text = "Invalid amount";
            }
        }

        private void CloseDrawer_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(ActualBalanceTextBox.Text, out decimal actualBalance))
            {
                var result = new CloseDrawerResult
                {
                    ActualBalance = actualBalance,
                    Notes = NotesTextBox.Text
                };
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(result, null);
            }
            else
            {
                MessageBox.Show("Please enter a valid actual balance.", "Invalid Input", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
} 