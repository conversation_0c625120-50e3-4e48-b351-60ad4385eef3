using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ProductManagement;
using Xunit;
using FluentAssertions;

namespace POSSystem.Tests.Services.ProductManagement
{
    public class ProductManagementServiceTests : IDisposable
    {
        private readonly POSDbContext _context;
        private readonly Mock<ILogger<ProductManagementService>> _mockLogger;
        private readonly ProductManagementService _productService;

        public ProductManagementServiceTests()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _mockLogger = new Mock<ILogger<ProductManagementService>>();
            _productService = new ProductManagementService(_context, _mockLogger.Object);

            // Seed test data
            SeedTestData();
        }

        private void SeedTestData()
        {
            var categories = new List<Category>
            {
                new Category { Id = 1, Name = "Electronics", IsActive = true },
                new Category { Id = 2, Name = "Clothing", IsActive = true },
                new Category { Id = 3, Name = "Books", IsActive = false }
            };

            var products = new List<Product>
            {
                new Product 
                { 
                    Id = 1, 
                    Name = "Laptop", 
                    SKU = "ELEC001", 
                    Barcode = "123456789",
                    CategoryId = 1, 
                    SellingPrice = 999.99m, 
                    PurchasePrice = 750.00m,
                    StockQuantity = 10,
                    ReorderPoint = 5,
                    IsActive = true 
                },
                new Product 
                { 
                    Id = 2, 
                    Name = "T-Shirt", 
                    SKU = "CLOTH001", 
                    Barcode = "987654321",
                    CategoryId = 2, 
                    SellingPrice = 29.99m, 
                    PurchasePrice = 15.00m,
                    StockQuantity = 50,
                    ReorderPoint = 10,
                    IsActive = true 
                },
                new Product 
                { 
                    Id = 3, 
                    Name = "Inactive Product", 
                    SKU = "INACT001", 
                    CategoryId = 1, 
                    SellingPrice = 10.00m,
                    IsActive = false 
                }
            };

            _context.Categories.AddRange(categories);
            _context.Products.AddRange(products);
            _context.SaveChanges();
        }

        #region Get Products Tests

        [Fact]
        public async Task GetAllProductsAsync_ShouldReturnAllActiveProducts()
        {
            // Act
            var result = await _productService.GetAllProductsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // Only active products
            result.All(p => p.IsActive).Should().BeTrue();
        }

        [Fact]
        public async Task GetProductByIdAsync_ShouldReturnCorrectProduct()
        {
            // Act
            var result = await _productService.GetProductByIdAsync(1);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(1);
            result.Name.Should().Be("Laptop");
            result.SKU.Should().Be("ELEC001");
        }

        [Fact]
        public async Task GetProductByIdAsync_ShouldReturnNull_WhenProductNotFound()
        {
            // Act
            var result = await _productService.GetProductByIdAsync(999);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetProductBySKUAsync_ShouldReturnCorrectProduct()
        {
            // Act
            var result = await _productService.GetProductBySKUAsync("ELEC001");

            // Assert
            result.Should().NotBeNull();
            result.SKU.Should().Be("ELEC001");
            result.Name.Should().Be("Laptop");
        }

        [Fact]
        public async Task GetProductByBarcodeAsync_ShouldReturnCorrectProduct()
        {
            // Act
            var result = await _productService.GetProductByBarcodeAsync("123456789");

            // Assert
            result.Should().NotBeNull();
            result.Barcode.Should().Be("123456789");
            result.Name.Should().Be("Laptop");
        }

        #endregion

        #region Search Tests

        [Fact]
        public async Task SearchProductsAsync_ShouldReturnMatchingProducts_ByName()
        {
            // Act
            var result = await _productService.SearchProductsAsync("Laptop");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Name.Should().Be("Laptop");
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldReturnMatchingProducts_BySKU()
        {
            // Act
            var result = await _productService.SearchProductsAsync("CLOTH001");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().SKU.Should().Be("CLOTH001");
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldReturnMatchingProducts_ByBarcode()
        {
            // Act
            var result = await _productService.SearchProductsAsync("987654321");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Barcode.Should().Be("987654321");
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldReturnEmpty_WhenNoMatches()
        {
            // Act
            var result = await _productService.SearchProductsAsync("NonExistentProduct");

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldBeCaseInsensitive()
        {
            // Act
            var result = await _productService.SearchProductsAsync("laptop");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Name.Should().Be("Laptop");
        }

        #endregion

        #region Add Product Tests

        [Fact]
        public async Task AddProductAsync_ShouldAddProductSuccessfully()
        {
            // Arrange
            var newProduct = new Product
            {
                Name = "New Product",
                SKU = "NEW001",
                Barcode = "111222333",
                CategoryId = 1,
                SellingPrice = 49.99m,
                PurchasePrice = 30.00m,
                StockQuantity = 20,
                IsActive = true
            };

            // Act
            var result = await _productService.AddProductAsync(newProduct);

            // Assert
            result.Should().BeGreaterThan(0);
            
            var addedProduct = await _context.Products.FindAsync(result);
            addedProduct.Should().NotBeNull();
            addedProduct.Name.Should().Be("New Product");
            addedProduct.SKU.Should().Be("NEW001");
        }

        [Fact]
        public async Task AddProductAsync_ShouldThrowException_WhenSKUAlreadyExists()
        {
            // Arrange
            var duplicateProduct = new Product
            {
                Name = "Duplicate SKU Product",
                SKU = "ELEC001", // This SKU already exists
                CategoryId = 1,
                SellingPrice = 100.00m,
                IsActive = true
            };

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                _productService.AddProductAsync(duplicateProduct));
        }

        [Fact]
        public async Task AddProductAsync_ShouldThrowException_WhenBarcodeAlreadyExists()
        {
            // Arrange
            var duplicateProduct = new Product
            {
                Name = "Duplicate Barcode Product",
                SKU = "DUP001",
                Barcode = "123456789", // This barcode already exists
                CategoryId = 1,
                SellingPrice = 100.00m,
                IsActive = true
            };

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                _productService.AddProductAsync(duplicateProduct));
        }

        #endregion

        #region Update Product Tests

        [Fact]
        public async Task UpdateProductAsync_ShouldUpdateProductSuccessfully()
        {
            // Arrange
            var product = await _context.Products.FindAsync(1);
            product.Name = "Updated Laptop";
            product.SellingPrice = 1099.99m;

            // Act
            var result = await _productService.UpdateProductAsync(product);

            // Assert
            result.Should().BeTrue();
            
            var updatedProduct = await _context.Products.FindAsync(1);
            updatedProduct.Name.Should().Be("Updated Laptop");
            updatedProduct.SellingPrice.Should().Be(1099.99m);
        }

        [Fact]
        public async Task UpdateProductAsync_ShouldReturnFalse_WhenProductNotFound()
        {
            // Arrange
            var nonExistentProduct = new Product
            {
                Id = 999,
                Name = "Non-existent Product",
                SKU = "NONE001",
                IsActive = true
            };

            // Act
            var result = await _productService.UpdateProductAsync(nonExistentProduct);

            // Assert
            result.Should().BeFalse();
        }

        #endregion

        #region Delete Product Tests

        [Fact]
        public async Task DeleteProductAsync_ShouldMarkProductAsInactive()
        {
            // Act
            var result = await _productService.DeleteProductAsync(1);

            // Assert
            result.Should().BeTrue();
            
            var deletedProduct = await _context.Products.FindAsync(1);
            deletedProduct.Should().NotBeNull();
            deletedProduct.IsActive.Should().BeFalse();
        }

        [Fact]
        public async Task DeleteProductAsync_ShouldReturnFalse_WhenProductNotFound()
        {
            // Act
            var result = await _productService.DeleteProductAsync(999);

            // Assert
            result.Should().BeFalse();
        }

        #endregion

        #region Stock Management Tests

        [Fact]
        public async Task GetLowStockProductsAsync_ShouldReturnProductsBelowReorderPoint()
        {
            // Arrange - Update a product to have low stock
            var product = await _context.Products.FindAsync(1);
            product.StockQuantity = 3; // Below reorder point of 5
            await _context.SaveChangesAsync();

            // Act
            var result = await _productService.GetLowStockProductsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Id.Should().Be(1);
            result.First().StockQuantity.Should().BeLessThan(result.First().ReorderPoint);
        }

        [Fact]
        public async Task UpdateStockAsync_ShouldUpdateProductStock()
        {
            // Act
            var result = await _productService.UpdateStockAsync(1, 25);

            // Assert
            result.Should().BeTrue();
            
            var updatedProduct = await _context.Products.FindAsync(1);
            updatedProduct.StockQuantity.Should().Be(25);
        }

        [Fact]
        public async Task UpdateStockAsync_ShouldReturnFalse_WhenProductNotFound()
        {
            // Act
            var result = await _productService.UpdateStockAsync(999, 10);

            // Assert
            result.Should().BeFalse();
        }

        #endregion

        #region Category Tests

        [Fact]
        public async Task GetProductsByCategoryAsync_ShouldReturnProductsInCategory()
        {
            // Act
            var result = await _productService.GetProductsByCategoryAsync(1);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1); // Only active products in category 1
            result.All(p => p.CategoryId == 1 && p.IsActive).Should().BeTrue();
        }

        [Fact]
        public async Task GetProductsByCategoryAsync_ShouldReturnEmpty_WhenCategoryHasNoActiveProducts()
        {
            // Act
            var result = await _productService.GetProductsByCategoryAsync(3); // Inactive category

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        #endregion

        #region Validation Tests

        [Fact]
        public async Task AddProductAsync_ShouldThrowException_WhenProductIsNull()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => 
                _productService.AddProductAsync(null));
        }

        [Fact]
        public async Task AddProductAsync_ShouldThrowException_WhenSKUIsEmpty()
        {
            // Arrange
            var invalidProduct = new Product
            {
                Name = "Invalid Product",
                SKU = "", // Empty SKU
                CategoryId = 1,
                SellingPrice = 10.00m,
                IsActive = true
            };

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _productService.AddProductAsync(invalidProduct));
        }

        [Fact]
        public async Task AddProductAsync_ShouldThrowException_WhenSellingPriceIsNegative()
        {
            // Arrange
            var invalidProduct = new Product
            {
                Name = "Invalid Product",
                SKU = "INV001",
                CategoryId = 1,
                SellingPrice = -10.00m, // Negative price
                IsActive = true
            };

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _productService.AddProductAsync(invalidProduct));
        }

        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
