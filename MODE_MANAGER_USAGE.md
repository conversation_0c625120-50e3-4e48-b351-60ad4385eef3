# POS System Mode Manager - Quick Start Guide

## What is this tool for?
This tool helps you easily switch your POS System between two modes:
- **Development Mode**: For testing and development (bypasses license activation)
- **Production Mode**: For normal business operation (requires proper licensing)

## How to use it

### Starting the tool
1. Find `pos_mode_manager.bat` in your POS System folder
2. Right-click it and select "Run as Administrator"
3. You'll see a menu with options and the current mode clearly displayed

### Switching modes

#### To enable Development Mode:
1. Press `1` at the main menu
2. Confirm with `Y` when prompted
3. Wait for the process to complete
4. Restart your POS application

#### To enable Production Mode:
1. Press `2` at the main menu
2. Confirm with `Y` when prompted
3. Wait for the process to complete
4. Restart your POS application

### Other useful features
- **Check System Status** (option 3): Scan your system to see how it's configured
- **View Mode History** (option 4): See a log of when modes were changed and by whom
- **Repair Mode Configuration** (option 5): Fix any issues with mode settings

## Important warnings
- Only use Development Mode for testing purposes
- Always switch back to Production Mode for normal business operations
- You must restart the POS System after changing modes
- The tool requires administrator access to work properly

## Visual indicators
- The tool shows your current mode with a colored banner
- When in Development Mode, a special desktop shortcut is created
- The shortcut will be removed when you switch back to Production Mode

If you need additional help, please consult the technical README or contact your system administrator. 