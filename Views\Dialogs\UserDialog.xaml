<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.UserDialog"
             x:Name="UserDialogControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:controls="clr-namespace:POSSystem.Views.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
    </UserControl.Resources>
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MaxWidth="800"
                         Margin="16">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="12,12,0,0"
                    Padding="16,12">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountPlus" 
                                           Width="24" 
                                           Height="24" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,8,0"/>
                    <TextBlock x:Name="DialogTitle" 
                             Text="{DynamicResource AddUser}" 
                             FontSize="18"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Content -->
            <Grid Grid.Row="1" Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Side - Basic Info -->
                <ScrollViewer Grid.Column="0" 
                            VerticalScrollBarVisibility="Auto" 
                            Margin="0,0,16,0">
                    <StackPanel>
                        <!-- Photo -->
                        <Grid HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Border Width="100" 
                                    Height="100"
                                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                                    BorderThickness="1"
                                    CornerRadius="50">
                                <Image x:Name="imgUserPhoto" 
                                       Stretch="UniformToFill"
                                       Source="/Resources/Images/default-user.png"/>
                            </Border>

                            <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0" 
                                      HorizontalAlignment="Center">
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                        Click="BtnUploadPhoto_Click"
                                        ToolTip="{DynamicResource UploadPhoto}"
                                        Width="36" Height="36">
                                    <materialDesign:PackIcon Kind="Upload"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                        Click="BtnRemovePhoto_Click"
                                        ToolTip="{DynamicResource RemovePhoto}"
                                        Width="36" Height="36"
                                        Foreground="Red">
                                    <materialDesign:PackIcon Kind="Delete"/>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- User Info Fields -->
                        <TextBox x:Name="txtUsername"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Username}"
                               Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                               Margin="0,0,0,8"/>
                        
                        <PasswordBox x:Name="passwordBox"
                                   Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                   materialDesign:HintAssist.Hint="{DynamicResource Password}"
                                   Margin="0,0,0,8"/>

                        <TextBox x:Name="txtFirstName"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource FirstName}"
                               Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged}"
                               Margin="0,0,0,8"/>
                        
                        <TextBox x:Name="txtLastName"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource LastName}"
                               Text="{Binding LastName, UpdateSourceTrigger=PropertyChanged}"
                               Margin="0,0,0,8"/>

                        <TextBox x:Name="txtEmail"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Email}"
                               Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                               Margin="0,0,0,8"/>
                        
                        <TextBox x:Name="txtPhone"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Phone}"
                               Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                               Margin="0,0,0,8"/>

                        <ComboBox x:Name="cmbRole"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 materialDesign:HintAssist.Hint="{DynamicResource Role}"
                                 ItemsSource="{Binding Roles}"
                                 SelectedItem="{Binding SelectedRole}"
                                 DisplayMemberPath="Name"
                                 Margin="0,0,0,8"/>

                        <CheckBox x:Name="chkIsActive"
                                 Style="{StaticResource MaterialDesignCheckBox}"
                                 Content="{DynamicResource UserIsActive}"
                                 IsChecked="{Binding IsActive}"
                                 Margin="0,8,0,0"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Right Side - Permissions -->
                <Border Grid.Column="1" 
                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="1,0,0,0"
                        Padding="16,0,0,0">
                    <controls:UserPermissionsControl x:Name="permissionsControl"/>
                </Border>
            </Grid>

            <!-- Button Panel -->
            <Border Grid.Row="2" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,1,0,0"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0"
                              Text="{DynamicResource RequiredFieldsNote}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.6"
                              VerticalAlignment="Center"/>
                    
                    <Button x:Name="btnCancel" 
                            Grid.Column="1"
                            Content="{DynamicResource Cancel}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="120"
                            Height="36"
                            Margin="0,0,8,0"
                            Click="BtnCancel_Click"/>
                    
                    <Button x:Name="btnSave" 
                            Grid.Column="2"
                            Content="{DynamicResource AddUser}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Width="120"
                            Height="36"
                            Click="BtnSave_Click"/>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:Card>
</UserControl> 