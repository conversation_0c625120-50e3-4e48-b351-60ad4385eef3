using System;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class BusinessExpense
    {
        public int Id { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public ExpenseCategory Category { get; set; }

        [Required]
        public ExpenseFrequency Frequency { get; set; }

        public DateTime? NextDueDate { get; set; }

        public string Notes { get; set; }

        public int? CashDrawerId { get; set; }
        public virtual CashDrawer CashDrawer { get; set; }

        public int UserId { get; set; }
        public virtual User User { get; set; }
    }

    public enum ExpenseCategory
    {
        Salary,
        Loan,
        Utilities,
        Rent,
        Insurance,
        Maintenance,
        Supplies,
        Marketing,
        Other
    }

    public enum ExpenseFrequency
    {
        OneTime,
        Daily,
        Weekly,
        Monthly,
        Quarterly,
        Annually
    }
} 