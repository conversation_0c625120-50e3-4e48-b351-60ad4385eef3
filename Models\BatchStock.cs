using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    public class BatchStock
    {
        [Key]
        public int Id { get; set; }
        
        public int ProductId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string BatchNumber { get; set; }
        
        /// <summary>
        /// Unified decimal quantity for all batch stock
        /// Supports both weight-based (decimal) and unit-based (whole number) products
        /// </summary>
        public decimal Quantity { get; set; }
        
        [Required]
        public DateTime ManufactureDate { get; set; }
        
        public DateTime? ExpiryDate { get; set; }

        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// Selling price captured at the time the batch was created (from the invoice)
        /// </summary>
        public decimal SellingPrice { get; set; }

        public string? Location { get; set; }

        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
        
        [NotMapped]
        public string DisplayName { get; set; }
    }
} 