﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{

    /// <inheritdoc />
    public partial class MakeBarcodeNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
                        migrationBuilder.AlterColumn<string>(
                      name: "Barcode",
              table: "Products",
              nullable: true, // Change to true
              oldClrType: typeof(string),
              oldType: "TEXT");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }

  
        }
}
