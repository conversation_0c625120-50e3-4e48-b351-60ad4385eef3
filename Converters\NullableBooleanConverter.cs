using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class NullableBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // If null, return false
            if (value == null)
                return false;
            
            // If it's an int, return true if it's greater than 0
            if (value is int count)
                return count > 0;
            
            // If it's a decimal, return true if it's greater than 0
            if (value is decimal decimalValue)
                return decimalValue > 0;
                
            // If it's a collection, check if it has items
            if (value is System.Collections.ICollection collection)
                return collection.Count > 0;
                
            // Default case - check if value isn't null or empty
            return !string.IsNullOrEmpty(value.ToString());
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 