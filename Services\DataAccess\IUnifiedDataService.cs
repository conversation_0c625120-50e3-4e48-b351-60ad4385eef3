using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.DataAccess
{
    /// <summary>
    /// Unified data access interface that standardizes all data operations
    /// This replaces the mixed pattern of DatabaseService, Repositories, and Management Services
    /// </summary>
    public interface IUnifiedDataService
    {
        // ===== PRODUCT OPERATIONS (High Traffic) =====
        Task<List<Product>> GetProductsAsync(int page = 1, int pageSize = 50);
        Task<List<Product>> GetAllProductsAsync();
        Task<Product> GetProductByIdAsync(int id);
        Task<Product> GetProductByBarcodeAsync(string barcode);
        Task<List<Product>> SearchProductsAsync(string searchTerm, int maxResults = 50);
        Task<List<Product>> GetLowStockProductsAsync();
        Task<List<Product>> GetExpiringProductsAsync(int daysThreshold = 30);
        Task<int> AddProductAsync(Product product);
        Task<bool> UpdateProductAsync(Product product);
        Task<bool> DeleteProductAsync(int id);
        Task<bool> UpdateProductStockAsync(int productId, decimal newQuantity);

        // ===== SALES OPERATIONS (High Traffic) =====
        Task<List<Sale>> GetSalesAsync(DateTime startDate, DateTime endDate);
        Task<List<Sale>> GetRecentSalesAsync(int limit = 50);
        Task<Sale> GetSaleByIdAsync(int id);
        Task<List<Sale>> GetUnpaidSalesAsync();
        Task<int> SaveSaleAsync(Sale sale);
        Task<bool> UpdateSaleAsync(Sale sale);
        Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetSalesTotalAsync(DateTime startDate, DateTime endDate);

        // ===== CUSTOMER OPERATIONS (Medium Traffic) =====
        Task<List<Customer>> GetCustomersAsync();
        Task<Customer> GetCustomerByIdAsync(int id);
        Task<List<Customer>> SearchCustomersAsync(string searchTerm);
        Task<int> AddCustomerAsync(Customer customer);
        Task<bool> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<List<Sale>> GetCustomerSalesHistoryAsync(int customerId, DateTime? startDate = null, DateTime? endDate = null);

        // ===== USER OPERATIONS (Medium Traffic) =====
        Task<List<User>> GetUsersAsync();
        Task<User> GetUserByIdAsync(int id);
        Task<User> AuthenticateUserAsync(string username, string password);
        Task<int> AddUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);

        // ===== DASHBOARD OPERATIONS (High Traffic) =====
        Task<List<Product>> GetTopSellingProductsAsync(int count = 10);
        Task<List<Customer>> GetTopCustomersAsync(int count = 10);
        Task<decimal> GetTotalRevenueAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetTotalProfitAsync(DateTime startDate, DateTime endDate);
        Task<int> GetTotalCustomersAsync();
        Task<int> GetTotalProductsAsync();

        // ===== INVENTORY OPERATIONS (Medium Traffic) =====
        Task<List<Category>> GetCategoriesAsync();
        Task<int> AddCategoryAsync(Category category);
        Task<bool> UpdateCategoryAsync(Category category);
        Task<bool> DeleteCategoryAsync(int id);

        // ===== STATISTICS OPERATIONS (Low Traffic) =====
        Task<Dictionary<string, decimal>> GetSalesByPaymentMethodAsync(DateTime startDate, DateTime endDate);
        Task<List<Sale>> GetSalesByPaymentMethodAsync(string paymentMethod, DateTime startDate, DateTime endDate);
        Task<decimal> GetAverageTransactionValueAsync(DateTime startDate, DateTime endDate);

        // ===== BULK PRICING OPERATIONS (Medium Traffic) =====
        Task<List<ProductPriceTier>> GetProductPriceTiersAsync(int productId);
        Task<List<ProductPriceTier>> GetAllActivePriceTiersAsync();
        Task<ProductPriceTier> GetPriceTierByIdAsync(int id);
        Task<int> AddPriceTierAsync(ProductPriceTier priceTier);
        Task<bool> UpdatePriceTierAsync(ProductPriceTier priceTier);
        Task<bool> DeletePriceTierAsync(int id);
        Task<bool> DeleteProductPriceTiersAsync(int productId);
        Task<List<Product>> GetProductsWithBulkPricingAsync();
        Task<BulkPricingResult> CalculateBestPricingAsync(int productId, decimal quantity);
        Task<List<QuantitySuggestion>> GetQuantitySuggestionsAsync(int productId, decimal currentQuantity);

        // ===== UTILITY OPERATIONS =====
        Task<bool> TestConnectionAsync();
        Task<string> GetDatabaseInfoAsync();
    }
}
