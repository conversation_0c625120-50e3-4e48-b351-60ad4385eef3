<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.LocalizedThreeOptionDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <materialDesign:Card Padding="24" MinWidth="350" MaxWidth="550">
        <StackPanel>
            <!-- Icon and Title Row -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16" HorizontalAlignment="Left">
                <materialDesign:PackIcon x:Name="DialogIcon" 
                                       Kind="Information" 
                                       Width="24" 
                                       Height="24" 
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                <TextBlock x:Name="TitleTextBlock"
                          Text="Title"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            
            <!-- Message Content -->
            <TextBlock x:Name="MessageTextBlock"
                      Text="Message"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      TextWrapping="Wrap"
                      Margin="0,0,0,24"/>
            
            <!-- Button Panel -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                <Button x:Name="FirstOptionButton"
                        Content="Option 1"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Margin="0,0,8,0"
                        MinWidth="100"
                        Click="FirstOptionButton_Click"/>
                
                <Button x:Name="SecondOptionButton"
                        Content="Option 2"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Margin="0,0,8,0"
                        MinWidth="100"
                        Click="SecondOptionButton_Click"/>
                
                <Button x:Name="CancelButton"
                        Content="Cancel"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        MinWidth="80"
                        IsCancel="True"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
