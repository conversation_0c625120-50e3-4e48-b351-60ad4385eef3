using System;
using System.IO;
using Microsoft.Data.Sqlite;

class Program
{
    static void Main(string[] args)
    {
        try
        {
            Console.WriteLine("Making Location column in PurchaseOrderItems table nullable...");
            
            // Get the database path
            string dbPath;
            
            // Check if a database path was provided as a command-line argument
            if (args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
            {
                dbPath = args[0];
                Console.WriteLine($"Using provided database path: {dbPath}");
            }
            else
            {
                dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
                Console.WriteLine($"Using default database path: {dbPath}");
            }
            
            // Check if the file exists
            if (!File.Exists(dbPath))
            {
                Console.WriteLine($"Error: Database file not found at {dbPath}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return;
            }
            
            // Connect to the database
            string connectionString = $"Data Source={dbPath}";
            using (var connection = new SqliteConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Database connection opened successfully.");
                
                // Check if the table exists
                using (var checkTableCommand = connection.CreateCommand())
                {
                    checkTableCommand.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='PurchaseOrderItems';";
                    var result = checkTableCommand.ExecuteScalar();
                    
                    if (result == null)
                    {
                        Console.WriteLine("Error: PurchaseOrderItems table does not exist in the database.");
                        Console.WriteLine("Press any key to exit...");
                        Console.ReadKey();
                        return;
                    }
                }
                
                // Turn off foreign key constraints temporarily
                using (var pragmaCommand = connection.CreateCommand())
                {
                    pragmaCommand.CommandText = "PRAGMA foreign_keys=off;";
                    pragmaCommand.ExecuteNonQuery();
                }
                
                // Begin transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Create a temporary table with the desired schema
                        using (var createTempTableCommand = connection.CreateCommand())
                        {
                            createTempTableCommand.CommandText = @"
                                CREATE TABLE PurchaseOrderItems_temp (
                                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    PurchaseOrderId INTEGER NOT NULL,
                                    ProductId INTEGER NOT NULL,
                                    Quantity INTEGER NOT NULL,
                                    UnitCost DECIMAL(18,2) NOT NULL,
                                    SellingPrice DECIMAL(18,2) NOT NULL,
                                    BatchNumber TEXT,
                                    Location TEXT, -- No NOT NULL constraint
                                    Notes TEXT,
                                    ExpiryDate TEXT,
                                    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id) ON DELETE CASCADE,
                                    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE RESTRICT
                                );";
                            createTempTableCommand.ExecuteNonQuery();
                            Console.WriteLine("Created temporary table.");
                        }
                        
                        // Copy data from the original table to the temporary table
                        using (var copyDataCommand = connection.CreateCommand())
                        {
                            copyDataCommand.CommandText = "INSERT INTO PurchaseOrderItems_temp SELECT * FROM PurchaseOrderItems;";
                            copyDataCommand.ExecuteNonQuery();
                            Console.WriteLine("Copied data to temporary table.");
                        }
                        
                        // Drop the original table
                        using (var dropTableCommand = connection.CreateCommand())
                        {
                            dropTableCommand.CommandText = "DROP TABLE PurchaseOrderItems;";
                            dropTableCommand.ExecuteNonQuery();
                            Console.WriteLine("Dropped original table.");
                        }
                        
                        // Rename the temporary table to the original table name
                        using (var renameTableCommand = connection.CreateCommand())
                        {
                            renameTableCommand.CommandText = "ALTER TABLE PurchaseOrderItems_temp RENAME TO PurchaseOrderItems;";
                            renameTableCommand.ExecuteNonQuery();
                            Console.WriteLine("Renamed temporary table to original name.");
                        }
                        
                        // Recreate any indexes
                        using (var createIndexCommand = connection.CreateCommand())
                        {
                            createIndexCommand.CommandText = @"
                                CREATE INDEX IF NOT EXISTS idx_purchase_order_items_order ON PurchaseOrderItems(PurchaseOrderId);
                                CREATE INDEX IF NOT EXISTS idx_purchase_order_items_product ON PurchaseOrderItems(ProductId);";
                            createIndexCommand.ExecuteNonQuery();
                            Console.WriteLine("Recreated indexes.");
                        }
                        
                        // Commit the transaction
                        transaction.Commit();
                        Console.WriteLine("Transaction committed.");
                    }
                    catch (Exception ex)
                    {
                        // Roll back the transaction if there's an error
                        transaction.Rollback();
                        Console.WriteLine($"Error occurred, transaction rolled back: {ex.Message}");
                        throw;
                    }
                }
                
                // Turn foreign key constraints back on
                using (var pragmaCommand = connection.CreateCommand())
                {
                    pragmaCommand.CommandText = "PRAGMA foreign_keys=on;";
                    pragmaCommand.ExecuteNonQuery();
                }
                
                Console.WriteLine("Location column in PurchaseOrderItems table is now nullable.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
} 