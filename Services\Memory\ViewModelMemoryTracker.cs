using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using POSSystem.ViewModels;

namespace POSSystem.Services.Memory
{
    /// <summary>
    /// ✅ CRITICAL MEMORY OPTIMIZATION: Tracks ViewModels to detect and prevent memory leaks
    /// </summary>
    public class ViewModelMemoryTracker : IDisposable
    {
        private readonly ConcurrentDictionary<WeakReference, ViewModelInfo> _trackedViewModels;
        private readonly Timer _monitoringTimer;
        private readonly object _lockObject = new object();
        private bool _disposed;

        private class ViewModelInfo
        {
            public string TypeName { get; set; }
            public DateTime CreatedAt { get; set; }
            public string CreationContext { get; set; }
            public bool WasDisposed { get; set; }
        }

        public ViewModelMemoryTracker()
        {
            _trackedViewModels = new ConcurrentDictionary<WeakReference, ViewModelInfo>();
            
            // Monitor every 30 seconds
            _monitoringTimer = new Timer(MonitorViewModels, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            Debug.WriteLine("✅ [VM-MEMORY-TRACKER] ViewModel Memory Tracker initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Track a ViewModel for memory leak detection
        /// </summary>
        public void TrackViewModel(ViewModelBase viewModel, string context = null)
        {
            if (viewModel == null) return;

            try
            {
                var weakRef = new WeakReference(viewModel);
                var info = new ViewModelInfo
                {
                    TypeName = viewModel.GetType().Name,
                    CreatedAt = DateTime.Now,
                    CreationContext = context ?? "Unknown",
                    WasDisposed = false
                };

                _trackedViewModels.TryAdd(weakRef, info);

                // Subscribe to disposal event if available
                if (viewModel is IDisposable disposable)
                {
                    // We can't directly subscribe to disposal, but we can track it
                    viewModel.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == "IsDisposed" && s is ViewModelBase vm)
                        {
                            MarkAsDisposed(vm);
                        }
                    };
                }

                Debug.WriteLine($"[VM-TRACKER] Tracking {info.TypeName} from {info.CreationContext}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VM-TRACKER] Error tracking ViewModel: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Mark a ViewModel as disposed
        /// </summary>
        private void MarkAsDisposed(ViewModelBase viewModel)
        {
            try
            {
                var entry = _trackedViewModels.FirstOrDefault(kvp => 
                    kvp.Key.IsAlive && ReferenceEquals(kvp.Key.Target, viewModel));

                if (entry.Key != null)
                {
                    entry.Value.WasDisposed = true;
                    Debug.WriteLine($"[VM-TRACKER] Marked {entry.Value.TypeName} as disposed");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VM-TRACKER] Error marking as disposed: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Monitor ViewModels for memory leaks
        /// </summary>
        private void MonitorViewModels(object state)
        {
            try
            {
                lock (_lockObject)
                {
                    var now = DateTime.Now;
                    var leakedViewModels = new List<ViewModelInfo>();
                    var deadReferences = new List<WeakReference>();

                    foreach (var kvp in _trackedViewModels)
                    {
                        var weakRef = kvp.Key;
                        var info = kvp.Value;

                        if (!weakRef.IsAlive)
                        {
                            // ViewModel was garbage collected - this is good
                            deadReferences.Add(weakRef);
                            
                            if (!info.WasDisposed)
                            {
                                Debug.WriteLine($"⚠️ [VM-LEAK-WARNING] {info.TypeName} was GC'd without being disposed (created: {info.CreationContext})");
                            }
                        }
                        else
                        {
                            // ViewModel is still alive - check if it's been alive too long
                            var age = now - info.CreatedAt;
                            
                            if (age.TotalMinutes > 30 && !info.WasDisposed)
                            {
                                leakedViewModels.Add(info);
                            }
                        }
                    }

                    // Clean up dead references
                    foreach (var deadRef in deadReferences)
                    {
                        _trackedViewModels.TryRemove(deadRef, out _);
                    }

                    // Report potential leaks
                    if (leakedViewModels.Count > 0)
                    {
                        Debug.WriteLine($"🚨 [VM-MEMORY-LEAK] Detected {leakedViewModels.Count} potential ViewModel memory leaks:");
                        foreach (var leaked in leakedViewModels)
                        {
                            var age = now - leaked.CreatedAt;
                            Debug.WriteLine($"   - {leaked.TypeName} (Age: {age.TotalMinutes:F1} min, Context: {leaked.CreationContext})");
                        }
                    }

                    // Summary
                    var aliveCount = _trackedViewModels.Count(kvp => kvp.Key.IsAlive);
                    Debug.WriteLine($"[VM-TRACKER] Summary: {aliveCount} alive, {deadReferences.Count} cleaned up, {leakedViewModels.Count} potential leaks");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VM-TRACKER] Monitoring error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Force cleanup of all tracked ViewModels
        /// </summary>
        public async Task ForceCleanupAsync()
        {
            try
            {
                Debug.WriteLine("[VM-TRACKER] Starting forced cleanup of ViewModels");

                var viewModelsToDispose = new List<ViewModelBase>();

                lock (_lockObject)
                {
                    foreach (var kvp in _trackedViewModels)
                    {
                        if (kvp.Key.IsAlive && kvp.Key.Target is ViewModelBase vm && !kvp.Value.WasDisposed)
                        {
                            viewModelsToDispose.Add(vm);
                        }
                    }
                }

                // Dispose ViewModels on UI thread if needed
                if (viewModelsToDispose.Count > 0)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        foreach (var vm in viewModelsToDispose)
                        {
                            try
                            {
                                if (vm is IDisposable disposable)
                                {
                                    disposable.Dispose();
                                    Debug.WriteLine($"[VM-TRACKER] Force disposed {vm.GetType().Name}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"❌ [VM-TRACKER] Error disposing {vm.GetType().Name}: {ex.Message}");
                            }
                        }
                    });
                }

                Debug.WriteLine($"[VM-TRACKER] Forced cleanup completed. Disposed {viewModelsToDispose.Count} ViewModels");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VM-TRACKER] Force cleanup error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get memory tracking statistics
        /// </summary>
        public (int TotalTracked, int Alive, int PotentialLeaks) GetStatistics()
        {
            lock (_lockObject)
            {
                var now = DateTime.Now;
                var alive = 0;
                var potentialLeaks = 0;

                foreach (var kvp in _trackedViewModels)
                {
                    if (kvp.Key.IsAlive)
                    {
                        alive++;
                        var age = now - kvp.Value.CreatedAt;
                        if (age.TotalMinutes > 30 && !kvp.Value.WasDisposed)
                        {
                            potentialLeaks++;
                        }
                    }
                }

                return (_trackedViewModels.Count, alive, potentialLeaks);
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get detailed report of tracked ViewModels
        /// </summary>
        public string GetDetailedReport()
        {
            lock (_lockObject)
            {
                var report = new System.Text.StringBuilder();
                var now = DateTime.Now;

                report.AppendLine("=== ViewModel Memory Tracking Report ===");
                report.AppendLine($"Generated: {now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                var aliveViewModels = _trackedViewModels
                    .Where(kvp => kvp.Key.IsAlive)
                    .OrderByDescending(kvp => now - kvp.Value.CreatedAt)
                    .ToList();

                report.AppendLine($"Alive ViewModels: {aliveViewModels.Count}");
                report.AppendLine();

                foreach (var kvp in aliveViewModels)
                {
                    var info = kvp.Value;
                    var age = now - info.CreatedAt;
                    var status = info.WasDisposed ? "Disposed" : "Active";
                    var warning = age.TotalMinutes > 30 && !info.WasDisposed ? " ⚠️ POTENTIAL LEAK" : "";

                    report.AppendLine($"- {info.TypeName} | Age: {age.TotalMinutes:F1}min | Status: {status} | Context: {info.CreationContext}{warning}");
                }

                return report.ToString();
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _monitoringTimer?.Dispose();
                
                lock (_lockObject)
                {
                    _trackedViewModels.Clear();
                }

                Debug.WriteLine("✅ [VM-MEMORY-TRACKER] ViewModel Memory Tracker disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VM-MEMORY-TRACKER] Disposal error: {ex.Message}");
            }

            _disposed = true;
        }
    }
}
