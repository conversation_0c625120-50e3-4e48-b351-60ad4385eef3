using POSSystem.Models;

namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for authentication services
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// Gets the currently authenticated user
        /// </summary>
        User CurrentUser { get; }

        /// <summary>
        /// Attempts to log in a user with the provided credentials
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>True if login successful, false otherwise</returns>
        bool Login(string username, string password);

        /// <summary>
        /// Logs out the current user
        /// </summary>
        void Logout();

        /// <summary>
        /// Checks if the current user has a specific permission
        /// </summary>
        /// <param name="permission">Permission to check</param>
        /// <returns>True if user has permission, false otherwise</returns>
        bool HasPermission(string permission);

        /// <summary>
        /// Checks if the current user is in a specific role
        /// </summary>
        /// <param name="role">Role to check</param>
        /// <returns>True if user is in role, false otherwise</returns>
        bool IsInRole(string role);
    }
}
