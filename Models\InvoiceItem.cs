using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.CompilerServices;

namespace POSSystem.Models
{
    /// <summary>
    /// Represents an item within an invoice in the two-tier invoice system
    /// </summary>
    public class InvoiceItem : INotifyPropertyChanged
    {
        // Backing fields for properties that need change notification
        private decimal _quantity;
        private decimal _unitPrice;
        private decimal _sellingPrice;
        private decimal _total;
        private string _productName;

        public InvoiceItem()
        {
            CustomProperties = new Dictionary<string, object>();
            CreatedAt = DateTime.Now;
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Invoice")]
        public int InvoiceId { get; set; }

        [Required]
        [ForeignKey("Product")]
        public int ProductId { get; set; }

        /// <summary>
        /// Store product name for historical accuracy (in case product is deleted/renamed)
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Quantity of the product (supports decimal for weight-based products)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(QuantityDisplay));
                    // Auto-calculate total when quantity changes
                    var newTotal = _quantity * _unitPrice;
                    if (_total != newTotal)
                    {
                        _total = newTotal;
                        OnPropertyChanged(nameof(Total));
                        OnPropertyChanged(nameof(TotalDisplay));
                    }
                }
            }
        }

        /// <summary>
        /// Unit price at the time of invoice creation
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                if (_unitPrice != value)
                {
                    _unitPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(UnitPriceDisplay));
                    // Auto-calculate total when unit price changes
                    var newTotal = _quantity * _unitPrice;
                    if (_total != newTotal)
                    {
                        _total = newTotal;
                        OnPropertyChanged(nameof(Total));
                        OnPropertyChanged(nameof(TotalDisplay));
                    }
                }
            }
        }

        /// <summary>
        /// Selling price (for compatibility with existing code)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal SellingPrice
        {
            get => _sellingPrice;
            set
            {
                if (_sellingPrice != value)
                {
                    _sellingPrice = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Total amount for this line item (Quantity * UnitPrice)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalDisplay));
                }
            }
        }

        /// <summary>
        /// Batch number for inventory tracking
        /// </summary>
        [MaxLength(50)]
        public string BatchNumber { get; set; }

        /// <summary>
        /// Additional custom properties stored as JSON
        /// </summary>
        [NotMapped]
        public Dictionary<string, object> CustomProperties { get; set; }

        /// <summary>
        /// Timestamp when the item was added to the invoice
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public virtual Invoice Invoice { get; set; }
        public virtual Product Product { get; set; }

        // Computed properties for UI
        [NotMapped]
        public string QuantityDisplay => Product?.IsWeightBased == true ?
            $"{Quantity:0.###} {Product.Unit ?? "kg"}" :
            $"{Quantity:0.##}";

        [NotMapped]
        public string TotalDisplay => Total.ToString("C");

        [NotMapped]
        public string UnitPriceDisplay => UnitPrice.ToString("C");

        // Helper methods
        public void CalculateTotal()
        {
            // Use the backing field to avoid triggering property change notification recursively
            var newTotal = Quantity * UnitPrice;
            if (_total != newTotal)
            {
                _total = newTotal;
                OnPropertyChanged(nameof(Total));
                OnPropertyChanged(nameof(TotalDisplay));
            }
        }

        public void UpdateFromProduct(Product product)
        {
            if (product != null)
            {
                ProductName = product.Name;
                UnitPrice = product.SellingPrice;
                SellingPrice = product.SellingPrice;
                CalculateTotal();
            }
        }

        public static InvoiceItem CreateFromProduct(Product product, decimal quantity)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            var item = new InvoiceItem
            {
                ProductId = product.Id,
                ProductName = product.Name,
                Quantity = quantity,
                UnitPrice = product.SellingPrice,
                SellingPrice = product.SellingPrice
            };

            item.CalculateTotal();
            return item;
        }

        public bool IsValid(out string errorMessage)
        {
            errorMessage = string.Empty;

            if (Quantity <= 0)
            {
                errorMessage = "Quantity must be greater than 0.";
                return false;
            }

            if (UnitPrice < 0)
            {
                errorMessage = "Unit price cannot be negative.";
                return false;
            }

            if (string.IsNullOrWhiteSpace(ProductName))
            {
                errorMessage = "Product name is required.";
                return false;
            }

            return true;
        }

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}