<Window x:Class="POSLicensingSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POSLicensingSystem.Views"
        xmlns:vm="clr-namespace:POSLicensingSystem.ViewModels"
        mc:Ignorable="d"
        Title="POS Licensing System" Height="450" Width="800"
        WindowStartupLocation="CenterScreen">
    
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>
    
    <Window.Resources>
        <!-- Button Styles -->
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="0,5" />
            <Setter Property="Padding" Value="20,10" />
            <Setter Property="Background" Value="#3498db" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalContentAlignment" Value="Left" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                             VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980b9" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1f6aa5" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        
        <!-- Menu Bar -->
        <Menu Grid.Row="0">
            <MenuItem Header="File">
                <MenuItem Header="Exit" Command="{Binding ExitCommand}" />
            </MenuItem>
            <MenuItem Header="Tools">
                <MenuItem Header="Test License System" Command="{Binding OpenTestCommand}" />
            </MenuItem>
            <MenuItem Header="Help">
                <MenuItem Header="About" Command="{Binding AboutCommand}" />
            </MenuItem>
        </Menu>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="POS Licensing System" FontSize="24" FontWeight="Bold" Margin="0,0,0,10" />
                <TextBlock Text="Generate and activate license keys for the POS System" FontSize="14" Foreground="#777" />
            </StackPanel>
            
            <!-- Main Options -->
            <StackPanel Grid.Row="1" Width="300" HorizontalAlignment="Left">
                <Button Content="Generate License Key" 
                        Style="{StaticResource MenuButtonStyle}"
                        Command="{Binding OpenGeneratorCommand}" />
                        
                <Button Content="Activate License" 
                        Style="{StaticResource MenuButtonStyle}"
                        Command="{Binding OpenActivationCommand}" />
                
                <Button Content="Test License System" 
                        Style="{StaticResource MenuButtonStyle}"
                        Command="{Binding OpenTestCommand}" />
            </StackPanel>
            
            <!-- Footer -->
            <TextBlock Grid.Row="2" 
                       Text="© 2024 POS System. All rights reserved." 
                       HorizontalAlignment="Center" 
                       Foreground="#999" 
                       Margin="0,20,0,0" />
        </Grid>
    </Grid>
</Window> 