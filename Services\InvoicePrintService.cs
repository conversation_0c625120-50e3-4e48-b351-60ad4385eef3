using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Globalization;
using System.Windows.Markup;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Helpers;
using POSSystem.Services.Interfaces;
using System.Windows.Data;
using System.Printing;
using System.Windows.Media.Imaging;
using System.Windows.Xps;
using System.Text;

namespace POSSystem.Services
{
    /// <summary>
    /// Service for printing invoices with multilingual support
    /// </summary>
    public class InvoicePrintService : IInvoicePrintService
    {
        private readonly DatabaseService _dbService;
        private readonly SettingsService _settingsService;
        
        // Company information (will be loaded from settings)
        private string _companyName;
        private string _companyAddress;
        private string _companyPhone;
        private string _companyEmail;
        private string _companyWebsite;
        private string _companyTaxId;
        private string _logoPath;
        
        private CompanyInfo _companyInfo;
        
        private readonly double _pageWidth = 8.5 * 96; // 8.5 inches in device units (96 DPI)
        private readonly double _contentWidth;
        private readonly double _leftMargin = 48; // 0.5 inch margins
        private readonly double _rightMargin = 48;
        
        // Constructor
        public InvoicePrintService(DatabaseService dbService = null)
        {
            _dbService = dbService ?? new DatabaseService();
            _settingsService = new SettingsService();
            
            // Load company information from settings
            LoadCompanySettings();
            LoadCompanyInfo();
            _contentWidth = _pageWidth - _leftMargin - _rightMargin;
        }
        
        private void LoadCompanySettings()
        {
            _companyName = _settingsService.GetSetting("CompanyName") ?? "Your Company Name";
            _companyAddress = _settingsService.GetSetting("CompanyAddress") ?? "Company Address";
            _companyPhone = _settingsService.GetSetting("CompanyPhone") ?? "Phone Number";
            _companyEmail = _settingsService.GetSetting("CompanyEmail") ?? "<EMAIL>";
            _companyWebsite = _settingsService.GetSetting("CompanyWebsite") ?? "www.example.com";
            _companyTaxId = _settingsService.GetSetting("CompanyTaxId") ?? "Tax ID";
            _logoPath = _settingsService.GetSetting("CompanyLogoPath") ?? "";
        }
        
        /// <summary>
        /// Loads company information from database or settings
        /// </summary>
        private void LoadCompanyInfo()
        {
            try
            {
                // Get company info from settings instead of database
                _companyInfo = new CompanyInfo
                {
                    Name = _companyName,
                    Address = _companyAddress,
                    Phone = _companyPhone,
                    Email = _companyEmail,
                    Website = _companyWebsite,
                    TaxId = _companyTaxId
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading company information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// Prints an invoice with multilingual support
        /// </summary>
        /// <param name="invoice">The invoice to print</param>
        /// <returns>True if printing was successful, false otherwise</returns>
        public bool PrintInvoice(Invoice invoice)
        {
            try
            {
                // Get the flow direction based on the current language
                var flowDirection = IsRightToLeftLanguage() ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
                
                // Create the invoice content
                var document = CreateInvoiceContent(invoice, flowDirection);
                
                // Set up the document properties for A4 page
                document.FontSize = 10; // Smaller font for better readability
                document.PagePadding = new Thickness(10, 10, 10, 10); // Minimal margins for maximum content area
                document.ColumnWidth = double.NaN; // Auto column width
                document.IsOptimalParagraphEnabled = true; // Optimize paragraph layout
                document.IsColumnWidthFlexible = true; // Allow columns to adjust width
                
                // Set A4 page dimensions (A4 = 8.27 × 11.69 inches)
                double a4Width = 8.27 * 96; // 8.27 inches in device units (96 DPI)
                double a4Height = 11.69 * 96; // 11.69 inches in device units
                document.PageWidth = a4Width;
                document.PageHeight = a4Height;
                document.MaxPageWidth = a4Width;
                document.MinPageWidth = a4Width;
                
                // Create a PrintDialog
                var printDialog = new PrintDialog();
                
                // Configure print settings
                printDialog.PageRangeSelection = PageRangeSelection.AllPages;
                printDialog.UserPageRangeEnabled = false;
                
                // Set default printer options
                var capabilities = printDialog.PrintQueue.GetPrintCapabilities();
                var pageMediaSizeWidth = capabilities.PageImageableArea?.ExtentWidth;
                var pageMediaSizeHeight = capabilities.PageImageableArea?.ExtentHeight;
                
                // Use A4 if available
                bool foundA4 = false;
                foreach (var pageSize in printDialog.PrintQueue.GetPrintCapabilities().PageMediaSizeCapability)
                {
                    if (pageSize.PageMediaSizeName == PageMediaSizeName.ISOA4)
                    {
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                        printDialog.PrintTicket.PageOrientation = PageOrientation.Portrait;
                        foundA4 = true;
                        break;
                    }
                }
                
                // If A4 is not available, use default
                if (!foundA4 && pageMediaSizeWidth.HasValue && pageMediaSizeHeight.HasValue)
                {
                    printDialog.PrintTicket.PageMediaSize = new PageMediaSize(
                        pageMediaSizeWidth.Value,
                        pageMediaSizeHeight.Value);
                }
                
                // Center print on page and set PageDuplex to ensure proper printing
                printDialog.PrintTicket.PageBorderless = PageBorderless.None;
                printDialog.PrintTicket.PageMediaType = PageMediaType.Plain;
                printDialog.PrintTicket.PageScalingFactor = 100; // Ensure no scaling is used
                
                // Show the print dialog and print if the user confirms
                if (printDialog.ShowDialog() == true)
                {
                    // Print the document with specified printer options
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                    
                    // Use exact A4 dimensions for the paginator instead of the printer's reported printable area
                    // This ensures the document uses the full A4 page width regardless of printer margins
                    paginator.PageSize = new Size(a4Width, a4Height);
                    
                    printDialog.PrintDocument(paginator, $"Invoice #{invoice.InvoiceNumber}");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing invoice: {ex.Message}", "Print Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
        
        /// <summary>
        /// Creates a FlowDocument with the invoice content
        /// </summary>
        private FlowDocument CreateInvoiceContent(Invoice invoice, FlowDirection flowDirection)
        {
            // Define precise A4 measurements
            double a4Width = 8.27 * 96; // 8.27 inches = 210mm at 96 DPI
            double a4Height = 11.69 * 96; // 11.69 inches = 297mm at 96 DPI
            
            var document = new FlowDocument
            {
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = 10,
                PageWidth = a4Width,
                PageHeight = a4Height,
                PagePadding = new Thickness(10, 10, 10, 10), // Minimal margins for maximum content area
                ColumnGap = 0,
                TextAlignment = TextAlignment.Left,
                Background = Brushes.White,
                LineHeight = 1.0, // Tighter line spacing
                IsColumnWidthFlexible = true, // Allow content to expand to full width
                IsOptimalParagraphEnabled = true, // Better paragraph layout
                MaxPageWidth = a4Width, // Maximum width is A4 width
                MinPageWidth = a4Width,
                ColumnWidth = a4Width - 20 // Page width minus margins
            };
            
            // Set the correct flow direction based on current language
            document.FlowDirection = flowDirection;

            // Add the company header section
            document.Blocks.Add(CreateHeaderSection(invoice));

            // Add invoice details section
            document.Blocks.Add(CreateInvoiceDetailsSection(invoice));
            
            // Add customer section 
            document.Blocks.Add(CreateCustomerSection(invoice));

            // Add items section (table)
            document.Blocks.Add(CreateItemsTableSection(invoice));

            // Add totals section
            document.Blocks.Add(CreateTotalsSection(invoice));

            // Add notes section if there are notes
            if (!string.IsNullOrEmpty(invoice.Notes))
            {
                document.Blocks.Add(CreateNotesSection(invoice));
            }

            // Add footer
            document.Blocks.Add(CreateFooterSection());

            return document;
        }
        
        /// <summary>
        /// Creates the header section with company details
        /// </summary>
        private Section CreateHeaderSection(Invoice invoice)
        {
            var section = new Section() { 
                Margin = new Thickness(0, 0, 0, 8),
                TextAlignment = TextAlignment.Center // Center the entire section
            };
            
            // Create company name - centered
            var companyNamePara = new Paragraph(new Run(_companyName)) { 
                FontSize = 18, 
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 4)
            };
            section.Blocks.Add(companyNamePara);
            
            // Create company address - centered
            var companyAddressPara = new Paragraph(new Run(_companyAddress)) { 
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 2)
            };
            section.Blocks.Add(companyAddressPara);
            
            // Combine contact info
            var contactInfo = new StringBuilder();
            
            if (!string.IsNullOrEmpty(_companyPhone))
            {
                contactInfo.Append($"{GetResource("Phone") ?? "Phone"}: {_companyPhone}");
            }
            
            if (!string.IsNullOrEmpty(_companyEmail))
            {
                if (contactInfo.Length > 0) contactInfo.Append(" | ");
                contactInfo.Append($"{GetResource("Email") ?? "Email"}: {_companyEmail}");
            }
            
            if (contactInfo.Length > 0)
            {
                var contactPara = new Paragraph(new Run(contactInfo.ToString())) { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 2)
                };
                section.Blocks.Add(contactPara);
            }
            
            // Website
            if (!string.IsNullOrEmpty(_companyWebsite))
            {
                var websitePara = new Paragraph(new Run(_companyWebsite)) { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 2)
                };
                section.Blocks.Add(websitePara);
            }
            
            // Tax ID
            if (!string.IsNullOrEmpty(_companyTaxId))
            {
                var taxPara = new Paragraph(new Run($"{GetResource("TaxId") ?? "Tax ID"}: {_companyTaxId}")) { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 2)
                };
                section.Blocks.Add(taxPara);
            }
            
            // Add a divider after company info
            section.Blocks.Add(new Paragraph(new Run(" ")) { 
                BorderThickness = new Thickness(0, 0, 0, 1),
                BorderBrush = Brushes.Black,
                Margin = new Thickness(0, 4, 0, 4)
            });
            
            // Add invoice title
            var titleParagraph = new Paragraph(new Run($"{GetResource("Invoice") ?? "INVOICE"} #{invoice.InvoiceNumber}")) {
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 4, 0, 8)
            };
            section.Blocks.Add(titleParagraph);
            
            return section;
        }

        /// <summary>
        /// Creates the invoice details section with customer/supplier and invoice info
        /// </summary>
        private Section CreateInvoiceDetailsSection(Invoice invoice)
        {
            var section = new Section() { 
                Margin = new Thickness(0, 4, 0, 4)
            };
            
            // Create a table for invoice details with two columns - one for details and one for payment
            var detailsTable = new Table() { 
                CellSpacing = 0
            };
            
            // Set columns to use relative sizing (star) to fill the full width
            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            
            // Add row group
            var detailsRowGroup = new TableRowGroup();
            detailsTable.RowGroups.Add(detailsRowGroup);
            
            // Create a row
            var detailsRow = new TableRow();
            detailsRowGroup.Rows.Add(detailsRow);
            
            // Left column - Invoice details
            var leftCell = new TableCell() { Padding = new Thickness(0, 0, 8, 0) };
            
            // Create invoice details table
            var invoiceDetailsTable = new Table() { 
                CellSpacing = 0, 
                BorderThickness = new Thickness(0.5),
                BorderBrush = Brushes.Gray
            };
            
            // Add two columns to invoice details table with percentage-based widths 
            invoiceDetailsTable.Columns.Add(new TableColumn { Width = new GridLength(40, GridUnitType.Star) }); // 40% for label
            invoiceDetailsTable.Columns.Add(new TableColumn { Width = new GridLength(60, GridUnitType.Star) }); // 60% for value
            
            // Add row group for invoice details
            var invoiceDetailsRowGroup = new TableRowGroup();
            invoiceDetailsTable.RowGroups.Add(invoiceDetailsRowGroup);
            
            // Standard cell padding
            var cellPadding = new Thickness(2);
            
            // Create row for each invoice detail
            
            // Invoice number
            var invoiceNumberRow = new TableRow();
            var invoiceNumberLabelCell = new TableCell(new Paragraph(new Run(GetResource("InvoiceNumber") ?? "Invoice #")) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding, 
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            };
            
            var invoiceNumberValueCell = new TableCell(new Paragraph(new Run(invoice.InvoiceNumber)) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            invoiceNumberRow.Cells.Add(invoiceNumberLabelCell);
            invoiceNumberRow.Cells.Add(invoiceNumberValueCell);
            invoiceDetailsRowGroup.Rows.Add(invoiceNumberRow);
            
            // Date
            var dateRow = new TableRow();
            var dateLabelCell = new TableCell(new Paragraph(new Run(GetResource("Date") ?? "Date")) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding, 
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            };
            
            var dateValueCell = new TableCell(new Paragraph(new Run(invoice.IssueDate.ToString("d", CultureInfo.CurrentCulture))) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            dateRow.Cells.Add(dateLabelCell);
            dateRow.Cells.Add(dateValueCell);
            invoiceDetailsRowGroup.Rows.Add(dateRow);
            
            // Due Date
            var dueDateRow = new TableRow();
            var dueDateLabelCell = new TableCell(new Paragraph(new Run(GetResource("DueDate") ?? "Due Date")) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding, 
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            };
            
            var dueDateValueCell = new TableCell(new Paragraph(new Run(invoice.DueDate.ToString("d", CultureInfo.CurrentCulture))) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            dueDateRow.Cells.Add(dueDateLabelCell);
            dueDateRow.Cells.Add(dueDateValueCell);
            invoiceDetailsRowGroup.Rows.Add(dueDateRow);
            
            // Status
                var statusRow = new TableRow();
            var statusLabelCell = new TableCell(new Paragraph(new Run(GetResource("Status") ?? "Status")) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding, 
                BorderThickness = new Thickness(0, 0, 0.5, 0),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            };
            
            var status = GetLocalizedStatus(invoice.Status);
            var statusRun = new Run(status);
            
            // Color status based on value
                if (invoice.Status.Equals("Paid", StringComparison.OrdinalIgnoreCase))
                {
                statusRun.Foreground = Brushes.Green;
                statusRun.FontWeight = FontWeights.Bold;
                }
            else if (invoice.Status.Equals("Unpaid", StringComparison.OrdinalIgnoreCase) || 
                    invoice.Status.Equals("Overdue", StringComparison.OrdinalIgnoreCase))
                {
                statusRun.Foreground = Brushes.Red;
                statusRun.FontWeight = FontWeights.Bold;
                }
                else if (invoice.Status.Equals("Partially Paid", StringComparison.OrdinalIgnoreCase))
                {
                statusRun.Foreground = Brushes.Orange;
                statusRun.FontWeight = FontWeights.Bold;
            }
            
            var statusValueCell = new TableCell(new Paragraph(statusRun) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0),
                BorderBrush = Brushes.Gray
            };
            
            statusRow.Cells.Add(statusLabelCell);
            statusRow.Cells.Add(statusValueCell);
            invoiceDetailsRowGroup.Rows.Add(statusRow);
            
            // Add the invoice details table to the left cell
            leftCell.Blocks.Add(invoiceDetailsTable);
            
            // Right column - Payment terms
            var rightCell = new TableCell() { Padding = new Thickness(8, 0, 0, 0) };
            
            // Create payment terms table
            var paymentTermsTable = new Table() { 
                CellSpacing = 0, 
                BorderThickness = new Thickness(0.5),
                BorderBrush = Brushes.Gray
            };
            
            // Add two columns to payment terms table with percentage-based widths
            paymentTermsTable.Columns.Add(new TableColumn { Width = new GridLength(40, GridUnitType.Star) }); // 40% for label
            paymentTermsTable.Columns.Add(new TableColumn { Width = new GridLength(60, GridUnitType.Star) }); // 60% for value
            
            // Add row group for payment terms
            var paymentTermsRowGroup = new TableRowGroup();
            paymentTermsTable.RowGroups.Add(paymentTermsRowGroup);
            
            // Payment Terms
            var paymentTermsRow = new TableRow();
            var paymentTermsLabelCell = new TableCell(new Paragraph(new Run(GetResource("PaymentTerms") ?? "Payment Terms")) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding, 
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            };
            
            var localizedTerms = GetLocalizedPaymentTerms(invoice.PaymentTerms);
            var paymentTermsValueCell = new TableCell(new Paragraph(new Run(localizedTerms)) { 
                Margin = new Thickness(0)
            }) { 
                Padding = cellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            paymentTermsRow.Cells.Add(paymentTermsLabelCell);
            paymentTermsRow.Cells.Add(paymentTermsValueCell);
            paymentTermsRowGroup.Rows.Add(paymentTermsRow);
            
            // Add the payment terms table to the right cell
            rightCell.Blocks.Add(paymentTermsTable);
            
            // Add cells to row
            detailsRow.Cells.Add(leftCell);
            detailsRow.Cells.Add(rightCell);
            
            // Add the main table to the section
            section.Blocks.Add(detailsTable);
            
            return section;
        }

        /// <summary>
        /// Creates the customer section with customer details
        /// </summary>
        private Section CreateCustomerSection(Invoice invoice)
        {
            var section = new Section() { 
                Margin = new Thickness(0, 4, 0, 8)
            };
            
            // Create a table for customer information
            var customerTable = new Table() { 
                CellSpacing = 0,
                BorderThickness = new Thickness(0.5),
                BorderBrush = Brushes.Gray
            };
            
            // Define columns with equal width
            customerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            customerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            
            // Add row group
            var customerRowGroup = new TableRowGroup();
            customerTable.RowGroups.Add(customerRowGroup);
            
            // Header row
            var headerRow = new TableRow { Background = Brushes.LightGray };
            customerRowGroup.Rows.Add(headerRow);
            
            var billToHeaderCell = new TableCell(new Paragraph(new Run(GetResource("BillTo") ?? "BILL TO")) { 
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0) 
            }) { 
                Padding = new Thickness(4), 
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            var shipToHeaderCell = new TableCell(new Paragraph(new Run(GetResource("ShipTo") ?? "SHIP TO")) { 
                    FontWeight = FontWeights.Bold,
                Margin = new Thickness(0) 
            }) { 
                Padding = new Thickness(4), 
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            };
            
            headerRow.Cells.Add(billToHeaderCell);
            headerRow.Cells.Add(shipToHeaderCell);
            
            // Content row
            var contentRow = new TableRow();
            customerRowGroup.Rows.Add(contentRow);
            
            var billToContentCell = new TableCell() { 
                Padding = new Thickness(4), 
                BorderThickness = new Thickness(0, 0, 0.5, 0),
                BorderBrush = Brushes.Gray
            };
            
            var shipToContentCell = new TableCell() { 
                Padding = new Thickness(4), 
                BorderThickness = new Thickness(0, 0, 0, 0),
                BorderBrush = Brushes.Gray
            };
            
            // Determine correct customer/supplier information based on invoice type
            string name = "";
            string address = "";
            string phone = "";
            string email = "";
            
            if (invoice.Type.Equals("Sales", StringComparison.OrdinalIgnoreCase) && invoice.Customer != null)
            {
                name = invoice.Customer.Name;
                address = invoice.Customer.Address;
                phone = invoice.Customer.Phone;
                email = invoice.Customer.Email;
            }
            else if (invoice.Type.Equals("Purchase", StringComparison.OrdinalIgnoreCase) && invoice.Supplier != null)
            {
                name = invoice.Supplier.Name;
                address = invoice.Supplier.Address;
                phone = invoice.Supplier.Phone;
                email = invoice.Supplier.Email;
            }
            
            // Add customer name
            var nameParaLeft = new Paragraph(new Run(name) { FontWeight = FontWeights.Bold }) { Margin = new Thickness(0, 0, 0, 2) };
            billToContentCell.Blocks.Add(nameParaLeft);
            
            // Add address if available
            if (!string.IsNullOrWhiteSpace(address))
            {
                var addressParaLeft = new Paragraph(new Run(address)) { Margin = new Thickness(0, 0, 0, 2) };
                billToContentCell.Blocks.Add(addressParaLeft);
            }
            
            // Add phone if available
            if (!string.IsNullOrWhiteSpace(phone))
            {
                var phoneParaLeft = new Paragraph(new Run($"{GetResource("Phone") ?? "Phone"}: {phone}")) { Margin = new Thickness(0, 0, 0, 2) };
                billToContentCell.Blocks.Add(phoneParaLeft);
            }
            
            // Add email if available
            if (!string.IsNullOrWhiteSpace(email))
            {
                var emailParaLeft = new Paragraph(new Run($"{GetResource("Email") ?? "Email"}: {email}")) { Margin = new Thickness(0, 0, 0, 0) };
                billToContentCell.Blocks.Add(emailParaLeft);
            }
            
            // Mirror the information for Ship To column if shipping address is not provided
            var nameParaRight = new Paragraph(new Run(name) { FontWeight = FontWeights.Bold }) { Margin = new Thickness(0, 0, 0, 2) };
            shipToContentCell.Blocks.Add(nameParaRight);
            
            if (!string.IsNullOrWhiteSpace(address))
            {
                var addressParaRight = new Paragraph(new Run(address)) { Margin = new Thickness(0, 0, 0, 2) };
                shipToContentCell.Blocks.Add(addressParaRight);
            }
            
            if (!string.IsNullOrWhiteSpace(phone))
            {
                var phoneParaRight = new Paragraph(new Run($"{GetResource("Phone") ?? "Phone"}: {phone}")) { Margin = new Thickness(0, 0, 0, 2) };
                shipToContentCell.Blocks.Add(phoneParaRight);
            }
            
            if (!string.IsNullOrWhiteSpace(email))
            {
                var emailParaRight = new Paragraph(new Run($"{GetResource("Email") ?? "Email"}: {email}")) { Margin = new Thickness(0, 0, 0, 0) };
                shipToContentCell.Blocks.Add(emailParaRight);
            }
            
            contentRow.Cells.Add(billToContentCell);
            contentRow.Cells.Add(shipToContentCell);
            
            // Add the customer table to the section
            section.Blocks.Add(customerTable);
            
            return section;
        }
        
        /// <summary>
        /// Creates the items table section with the list of products
        /// </summary>
        private Section CreateItemsTableSection(Invoice invoice)
        {
            var section = new Section() { 
                Margin = new Thickness(0, 4, 0, 4),
                TextAlignment = TextAlignment.Left
            };
            
            // Section title
            var titleParagraph = new Paragraph(new Run(GetResource("InvoiceItems") ?? "INVOICE ITEMS")) {
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Left,
                Margin = new Thickness(0, 4, 0, 4)
            };
            section.Blocks.Add(titleParagraph);
            
            // Check if there are items
            if (invoice.Items == null || invoice.Items.Count == 0)
            {
                var noItemsParagraph = new Paragraph(new Run(GetResource("NoItemsInInvoice") ?? "No items in this invoice")) {
                    FontStyle = FontStyles.Italic,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 4, 0, 4)
                };
                section.Blocks.Add(noItemsParagraph);
                return section;
            }
            
            // Create table for items with clear, visible structure
            var itemsTable = new Table() {
                CellSpacing = 0,
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(0.5)
            };
            
            // Define columns with appropriate proportional widths - sum to 100%
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(5, GridUnitType.Star) }); // #
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(40, GridUnitType.Star) }); // Product
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(10, GridUnitType.Star) }); // Quantity
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(15, GridUnitType.Star) }); // Unit Price
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(15, GridUnitType.Star) }); // Expiry Date
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(15, GridUnitType.Star) }); // Total
                
            // Set table properties for better visibility
            itemsTable.CellSpacing = 0;
            itemsTable.BorderThickness = new Thickness(0.5);
            itemsTable.BorderBrush = Brushes.Black;
            
            // Create header row group
            var headerRowGroup = new TableRowGroup();
            itemsTable.RowGroups.Add(headerRowGroup);
                
            // Create header row with darker background
            var headerRow = new TableRow { Background = Brushes.LightGray };
            headerRowGroup.Rows.Add(headerRow);
            
            // Add header cells with smaller padding to save space
            var headerCellPadding = new Thickness(2);
            
            var numHeaderCell = new TableCell(new Paragraph(new Run("#"))
            { 
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5),
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(numHeaderCell);
            
            var prodHeaderCell = new TableCell(new Paragraph(new Run(GetResource("Product") ?? "Product"))
            { 
                TextAlignment = TextAlignment.Left,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5), 
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(prodHeaderCell);
            
            var qtyHeaderCell = new TableCell(new Paragraph(new Run(GetResource("Quantity") ?? "Quantity"))
            { 
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5), 
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(qtyHeaderCell);
            
            var priceHeaderCell = new TableCell(new Paragraph(new Run(GetResource("UnitPrice") ?? "Unit Price"))
            { 
                TextAlignment = TextAlignment.Right,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5), 
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(priceHeaderCell);
            
            var expiryHeaderCell = new TableCell(new Paragraph(new Run(GetResource("ExpiryDate") ?? "Expiry Date"))
            { 
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5), 
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(expiryHeaderCell);
            
            var totalHeaderCell = new TableCell(new Paragraph(new Run(GetResource("Total") ?? "Total"))
            { 
                TextAlignment = TextAlignment.Right,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0)
            }) 
            { 
                BorderThickness = new Thickness(0.5), 
                BorderBrush = Brushes.Gray,
                Padding = headerCellPadding 
            };
            headerRow.Cells.Add(totalHeaderCell);
            
            // Create items row group
            var itemsRowGroup = new TableRowGroup();
            itemsTable.RowGroups.Add(itemsRowGroup);
            
            // Set smaller cell padding for item rows
            var itemCellPadding = new Thickness(2);
            
            // Add rows for each item with clear borders
            int itemIndex = 1;
            foreach (var item in invoice.Items)
            {
                var itemRow = new TableRow 
                { 
                    Background = (itemIndex % 2 == 0) ? Brushes.WhiteSmoke : Brushes.White // Alternating row colors
                };
                itemsRowGroup.Rows.Add(itemRow);
                
                // Item number
                var numCell = new TableCell(new Paragraph(new Run(itemIndex.ToString()))
                { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(numCell);
                
                // Product name
                var prodCell = new TableCell(new Paragraph(new Run(item.Product?.Name ?? "Unknown Product"))
                { 
                    TextAlignment = TextAlignment.Left,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(prodCell);
                
                // Quantity
                var qtyCell = new TableCell(new Paragraph(new Run(item.Quantity.ToString()))
                { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(qtyCell);
                
                // Unit Price
                var priceCell = new TableCell(new Paragraph(new Run(FormatCurrency(item.UnitPrice)))
                { 
                    TextAlignment = TextAlignment.Right,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(priceCell);
                
                // Expiry Date
                var expiryDate = item.Product?.ExpiryDate.HasValue == true ? 
                    item.Product.ExpiryDate.Value.ToString("d", CultureInfo.CurrentCulture) : "-";
                    
                var expiryCell = new TableCell(new Paragraph(new Run(expiryDate))
                { 
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(expiryCell);
            
                // Total
                var totalCell = new TableCell(new Paragraph(new Run(FormatCurrency(item.Total)))
                { 
                    TextAlignment = TextAlignment.Right,
                    Margin = new Thickness(0)
                }) 
                { 
                    BorderThickness = new Thickness(0.5),
                    BorderBrush = Brushes.Gray,
                    Padding = itemCellPadding
                };
                itemRow.Cells.Add(totalCell);
                
                itemIndex++;
            }
            
            // Add the table to the section
            section.Blocks.Add(itemsTable);
            
            return section;
        }
        
        /// <summary>
        /// Creates the totals section with subtotal, tax, discount and grand total
        /// </summary>
        private Section CreateTotalsSection(Invoice invoice)
        {
            var section = new Section() { 
                Margin = new Thickness(0, 4, 0, 4),
                TextAlignment = TextAlignment.Right // Right align the totals section
            };
            
            // Create a table for the totals that uses full width
            var totalsTable = new Table() {
                CellSpacing = 0
            };
            
            // Set up columns - using 3 columns with a spacer column
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(60, GridUnitType.Star) }); // Empty spacer column
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(20, GridUnitType.Star) }); // Label column
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(20, GridUnitType.Star) }); // Value column
            
            // Add row group
            var totalsRowGroup = new TableRowGroup();
            totalsTable.RowGroups.Add(totalsRowGroup);
            
            // Standard cell padding for all totals rows
            var totalsCellPadding = new Thickness(2);
            
            // Subtotal row
            var subtotalRow = new TableRow();
            
            // Empty spacer cell
            subtotalRow.Cells.Add(new TableCell());
            
            subtotalRow.Cells.Add(new TableCell(new Paragraph(new Run(GetResource("Subtotal") ?? "Subtotal"))) 
            { 
                TextAlignment = TextAlignment.Right,
                Padding = totalsCellPadding,
                BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                BorderBrush = Brushes.Gray
            });
            
            subtotalRow.Cells.Add(new TableCell(new Paragraph(new Run(FormatCurrency(invoice.Subtotal)))) 
            { 
                TextAlignment = TextAlignment.Right,
                Padding = totalsCellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0.5),
                BorderBrush = Brushes.Gray
            });
            
            totalsRowGroup.Rows.Add(subtotalRow);
            
            // Discount row - only if there's a discount
            if (invoice.DiscountAmount > 0)
            {
                var discountRow = new TableRow();
                
                // Empty spacer cell
                discountRow.Cells.Add(new TableCell());
                
                discountRow.Cells.Add(new TableCell(new Paragraph(new Run(GetResource("Discount") ?? "Discount"))) 
                { 
                    TextAlignment = TextAlignment.Right,
                    Padding = totalsCellPadding,
                    BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                    BorderBrush = Brushes.Gray
                });
                
                discountRow.Cells.Add(new TableCell(new Paragraph(new Run("-" + FormatCurrency(invoice.DiscountAmount)))) 
                { 
                    TextAlignment = TextAlignment.Right,
                    Padding = totalsCellPadding,
                    BorderThickness = new Thickness(0, 0, 0, 0.5),
                    BorderBrush = Brushes.Gray
                });
                
                totalsRowGroup.Rows.Add(discountRow);
            }
            
            // Tax row - only if there's tax
            if (invoice.TaxAmount > 0)
            {
                var taxRow = new TableRow();
                
                // Empty spacer cell
                taxRow.Cells.Add(new TableCell());
                
                taxRow.Cells.Add(new TableCell(new Paragraph(new Run(GetResource("Tax") ?? "Tax"))) 
                { 
                    TextAlignment = TextAlignment.Right,
                    Padding = totalsCellPadding,
                    BorderThickness = new Thickness(0, 0, 0.5, 0.5),
                    BorderBrush = Brushes.Gray
                });
                
                taxRow.Cells.Add(new TableCell(new Paragraph(new Run(FormatCurrency(invoice.TaxAmount)))) 
                { 
                    TextAlignment = TextAlignment.Right,
                    Padding = totalsCellPadding,
                    BorderThickness = new Thickness(0, 0, 0, 0.5),
                    BorderBrush = Brushes.Gray
                });
                
                totalsRowGroup.Rows.Add(taxRow);
            }
            
            // Grand total row with emphasis
            var grandTotalRow = new TableRow();
            
            // Empty spacer cell
            grandTotalRow.Cells.Add(new TableCell());
            
            grandTotalRow.Cells.Add(new TableCell(new Paragraph(new Run(GetResource("GrandTotal") ?? "Grand Total") 
                { FontWeight = FontWeights.Bold })) 
            { 
                TextAlignment = TextAlignment.Right,
                Padding = totalsCellPadding,
                BorderThickness = new Thickness(0, 0, 0.5, 0),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            });
            
            grandTotalRow.Cells.Add(new TableCell(new Paragraph(new Run(FormatCurrency(invoice.GrandTotal)) 
                { FontWeight = FontWeights.Bold })) 
            { 
                TextAlignment = TextAlignment.Right,
                Padding = totalsCellPadding,
                BorderThickness = new Thickness(0, 0, 0, 0),
                BorderBrush = Brushes.Gray,
                Background = Brushes.WhiteSmoke
            });
            
            totalsRowGroup.Rows.Add(grandTotalRow);
            
            // Add the table to the section
            section.Blocks.Add(totalsTable);
            
            return section;
        }
        
        /// <summary>
        /// Creates the notes section if invoice has notes
        /// </summary>
        private Section CreateNotesSection(Invoice invoice)
        {
            var section = new Section() 
            { 
                Margin = new Thickness(0, 8, 0, 8),
                TextAlignment = TextAlignment.Left
            };
            
            // Only add notes section if there are notes
            if (!string.IsNullOrEmpty(invoice.Notes))
            {
                // Notes title
                var titleParagraph = new Paragraph(new Run(GetResource("Notes") ?? "Notes"))
                {
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 20, 0, 5)
                };
                section.Blocks.Add(titleParagraph);
                
                // Notes content (remove TextWrapping property)
                var notesParagraph = new Paragraph(new Run(invoice.Notes))
                {
                    FontStyle = FontStyles.Italic
                };
                section.Blocks.Add(notesParagraph);
            }
            
            return section;
        }
        
        /// <summary>
        /// Creates the footer section
        /// </summary>
        private Section CreateFooterSection()
        {
            var section = new Section() 
            { 
                Margin = new Thickness(0, 4, 0, 0),
                TextAlignment = TextAlignment.Left
            };
            
            // Add a divider before the footer
            section.Blocks.Add(new Paragraph(new Run(" ")) { 
                BorderThickness = new Thickness(0, 0.5, 0, 0),
                BorderBrush = Brushes.Gray,
                Margin = new Thickness(0, 4, 0, 4)
            });
            
            // Thank you message
            var thankYouParagraph = new Paragraph(new Run(GetResource("ThankYou") ?? "Thank you for your business!"))
            {
                FontStyle = FontStyles.Italic,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 2, 0, 0)
            };
            section.Blocks.Add(thankYouParagraph);
            
            // Print date/time in small text
            var printDateParagraph = new Paragraph(new Run($"Printed on {DateTime.Now.ToString("g", CultureInfo.CurrentCulture)}"))
            {
                FontSize = 7,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 4, 0, 0)
            };
            section.Blocks.Add(printDateParagraph);
            
            return section;
        }
        
        /// <summary>
        /// Formats a decimal value as currency
        /// </summary>
        private string FormatCurrency(decimal value)
        {
            return value.ToString("C", CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// Gets a resource string from the application resources
        /// </summary>
        private string GetResource(string key)
        {
            try
            {
                return Application.Current.Resources[key] as string;
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// Determines if the current language is right-to-left
        /// </summary>
        private bool IsRightToLeftLanguage()
        {
            try
            {
                // Check for RTL languages like Arabic, Hebrew, etc.
                return CultureInfo.CurrentUICulture.TextInfo.IsRightToLeft;
            }
            catch
            {
                return false;
            }
        }

        private string FormatDate(DateTime date)
        {
            return date.ToString("d", CultureInfo.CurrentCulture);
        }

        private string GetLocalizedStatus(string status)
        {
            // Try to get localized version of the status
            string resourceKey = "Status" + status?.Replace(" ", "");
            string localizedStatus = GetResource(resourceKey);
            
            // If no localized version is found, return the original status
            return localizedStatus ?? status;
        }

        private string GetLocalizedPaymentTerms(string terms)
        {
            // Try to get localized version of payment terms
            string resourceKey = terms?.Replace(" ", "");
            string localizedTerms = GetResource(resourceKey);
            
            // If no localized version is found, return the original terms
            return localizedTerms ?? terms;
        }

        // Interface implementation methods
        void IInvoicePrintService.PrintInvoice(Invoice invoice)
        {
            PrintInvoice(invoice);
        }

        public async Task PrintInvoiceAsync(Invoice invoice)
        {
            await Task.Run(() => PrintInvoice(invoice));
        }

        public void PreviewInvoice(Invoice invoice)
        {
            // Simple preview implementation - in a real scenario, this would show a print preview
            PrintInvoice(invoice);
        }
    }
    
    /// <summary>
    /// Company information class used for invoice printing
    /// </summary>
    public class CompanyInfo
    {
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Website { get; set; }
        public string TaxId { get; set; }
        public byte[] Logo { get; set; }
    }

    // Extension methods for interface compliance
    public static class InvoicePrintServiceExtensions
    {
        public static async Task PrintInvoiceAsync(this InvoicePrintService service, Invoice invoice)
        {
            await Task.Run(() => service.PrintInvoice(invoice));
        }

        public static void PreviewInvoice(this InvoicePrintService service, Invoice invoice)
        {
            // Simple preview implementation
            service.PrintInvoice(invoice);
        }
    }
}