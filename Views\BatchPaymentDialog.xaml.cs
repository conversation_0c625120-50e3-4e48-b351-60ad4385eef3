using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Data;
using System.ComponentModel;
using System.Linq;
using POSSystem.Models;
using System.Windows.Documents;
using System.Windows.Xps.Packaging;
using System.IO;
using System.Windows.Xps;

namespace POSSystem.Views
{
    public partial class BatchPaymentDialog : Window
    {
        private readonly Action<Sale> _showSaleDetails;
        private readonly CollectionViewSource _salesViewSource;
        public decimal PaymentAmount { get; private set; }
        public string SelectedPaymentMethod { get; private set; }
        public decimal TotalAmount { get; }
        public string CustomerName { get; }
        public ObservableCollection<Sale> Sales { get; }
        private bool IsPartialPayment => PartialPaymentToggle.IsChecked ?? false;

        public BatchPaymentDialog(string customerName, ObservableCollection<Sale> sales, decimal totalAmount, Action<Sale> showSaleDetails)
        {
            InitializeComponent();
            CustomerName = customerName;
            Sales = sales;
            TotalAmount = totalAmount;
            PaymentAmount = totalAmount;
            _showSaleDetails = showSaleDetails;

            // Initialize CollectionViewSource for filtering and sorting
            _salesViewSource = new CollectionViewSource { Source = Sales };
            _salesViewSource.Filter += SalesViewSource_Filter;

            // Set the DataGrid's ItemsSource to the view
            SalesDataGrid.ItemsSource = _salesViewSource.View;

            DataContext = this;

            // Set default payment method
            PaymentMethodComboBox.SelectedIndex = 0;

            // Validate payment amount on text change
            PaymentAmountInput.TextChanged += (s, e) => ValidatePaymentAmount();

            // Setup search box
            SearchBox.TextChanged += (s, e) => _salesViewSource.View.Refresh();

            // Setup sort combo box
            SortComboBox.SelectionChanged += SortComboBox_SelectionChanged;

            // Add entrance animation
            this.Loaded += (s, e) => AnimateDialogEntrance();

            // Add responsive behavior
            this.SizeChanged += OnWindowSizeChanged;

            // Set initial focus
            this.Loaded += (s, e) => SearchBox.Focus();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Ensure the dialog fits within the screen bounds
            EnsureDialogFitsScreen();
        }

        private void EnsureDialogFitsScreen()
        {
            // Get the working area of the screen
            var workingArea = SystemParameters.WorkArea;

            // Calculate maximum available size (90% of screen)
            var maxWidth = workingArea.Width * 0.9;
            var maxHeight = workingArea.Height * 0.9;

            // Adjust window size if it exceeds screen bounds
            if (this.ActualWidth > maxWidth)
            {
                this.Width = maxWidth;
                this.SizeToContent = SizeToContent.Height;
            }

            if (this.ActualHeight > maxHeight)
            {
                this.Height = maxHeight;
                this.SizeToContent = SizeToContent.Manual;
            }

            // Ensure the window is centered
            this.Left = (workingArea.Width - this.ActualWidth) / 2 + workingArea.Left;
            this.Top = (workingArea.Height - this.ActualHeight) / 2 + workingArea.Top;

            // Ensure the window doesn't go off-screen
            if (this.Left < workingArea.Left)
                this.Left = workingArea.Left;
            if (this.Top < workingArea.Top)
                this.Top = workingArea.Top;
            if (this.Left + this.ActualWidth > workingArea.Right)
                this.Left = workingArea.Right - this.ActualWidth;
            if (this.Top + this.ActualHeight > workingArea.Bottom)
                this.Top = workingArea.Bottom - this.ActualHeight;
        }

        private void AnimateDialogEntrance()
        {
            // Find the main card to animate
            var mainCard = this.FindName("MainDialogCard") as FrameworkElement;
            if (mainCard == null) return;

            // Set initial state
            mainCard.Opacity = 0;
            mainCard.RenderTransform = new ScaleTransform(0.9, 0.9);
            mainCard.RenderTransformOrigin = new Point(0.5, 0.5);

            // Create animation storyboard
            var storyboard = new System.Windows.Media.Animation.Storyboard();

            // Scale animation
            var scaleX = new System.Windows.Media.Animation.DoubleAnimation(0.9, 1, TimeSpan.FromMilliseconds(300));
            var scaleY = new System.Windows.Media.Animation.DoubleAnimation(0.9, 1, TimeSpan.FromMilliseconds(300));
            scaleX.EasingFunction = new System.Windows.Media.Animation.CubicEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut };
            scaleY.EasingFunction = new System.Windows.Media.Animation.CubicEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut };

            // Opacity animation
            var opacity = new System.Windows.Media.Animation.DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            opacity.EasingFunction = new System.Windows.Media.Animation.CubicEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut };

            // Set animation targets
            System.Windows.Media.Animation.Storyboard.SetTarget(scaleX, mainCard);
            System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleX, new PropertyPath("RenderTransform.ScaleX"));
            System.Windows.Media.Animation.Storyboard.SetTarget(scaleY, mainCard);
            System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleY, new PropertyPath("RenderTransform.ScaleY"));
            System.Windows.Media.Animation.Storyboard.SetTarget(opacity, mainCard);
            System.Windows.Media.Animation.Storyboard.SetTargetProperty(opacity, new PropertyPath("Opacity"));

            storyboard.Children.Add(scaleX);
            storyboard.Children.Add(scaleY);
            storyboard.Children.Add(opacity);
            storyboard.Begin();
        }

        private void OnWindowSizeChanged(object sender, SizeChangedEventArgs e)
        {
            // Adjust layout for smaller screens
            if (e.NewSize.Width < 1100)
            {
                // Switch to more compact layout
                if (PaymentControlsGrid != null)
                {
                    PaymentControlsGrid.ColumnDefinitions.Clear();
                    PaymentControlsGrid.RowDefinitions.Clear();

                    PaymentControlsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                    PaymentControlsGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(16) });
                    PaymentControlsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                    // Move payment method to second row
                    var paymentMethodCard = PaymentControlsGrid.Children.OfType<FrameworkElement>()
                        .FirstOrDefault(x => Grid.GetColumn(x) == 2);
                    if (paymentMethodCard != null)
                    {
                        Grid.SetColumn(paymentMethodCard, 0);
                        Grid.SetRow(paymentMethodCard, 2);
                    }
                }
            }
            else
            {
                // Restore horizontal layout
                if (PaymentControlsGrid != null)
                {
                    PaymentControlsGrid.RowDefinitions.Clear();
                    PaymentControlsGrid.ColumnDefinitions.Clear();

                    PaymentControlsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) });
                    PaymentControlsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
                    PaymentControlsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                    // Restore payment method to third column
                    var paymentMethodCard = PaymentControlsGrid.Children.OfType<FrameworkElement>()
                        .FirstOrDefault(x => Grid.GetRow(x) == 2);
                    if (paymentMethodCard != null)
                    {
                        Grid.SetRow(paymentMethodCard, 0);
                        Grid.SetColumn(paymentMethodCard, 2);
                    }
                }
            }
        }

        private void Window_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // Handle keyboard shortcuts
            switch (e.Key)
            {
                case System.Windows.Input.Key.Escape:
                    CancelButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    break;

                case System.Windows.Input.Key.Enter:
                    if (e.KeyboardDevice.Modifiers == System.Windows.Input.ModifierKeys.Control)
                    {
                        ProcessButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                    }
                    break;

                case System.Windows.Input.Key.F:
                    if (e.KeyboardDevice.Modifiers == System.Windows.Input.ModifierKeys.Control)
                    {
                        SearchBox.Focus();
                        SearchBox.SelectAll();
                        e.Handled = true;
                    }
                    break;

                case System.Windows.Input.Key.P:
                    if (e.KeyboardDevice.Modifiers == System.Windows.Input.ModifierKeys.Control)
                    {
                        PrintAllSaleDetails_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                    }
                    break;
            }
        }

        private void SalesViewSource_Filter(object sender, FilterEventArgs e)
        {
            if (e.Item is Sale sale && !string.IsNullOrWhiteSpace(SearchBox.Text))
            {
                string searchText = SearchBox.Text.ToLower();
                e.Accepted = sale.InvoiceNumber.ToLower().Contains(searchText) ||
                           (sale.Customer?.FirstName?.ToLower().Contains(searchText) ?? false) ||
                           (sale.Customer?.LastName?.ToLower().Contains(searchText) ?? false);
            }
        }

        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string sortBy = selectedItem.Content.ToString();
                var view = _salesViewSource.View;

                view.SortDescriptions.Clear();
                
                if (sortBy == Application.Current.TryFindResource("Date") as string)
                {
                    view.SortDescriptions.Add(new SortDescription("SaleDate", ListSortDirection.Descending));
                }
                else if (sortBy == Application.Current.TryFindResource("Amount") as string)
                {
                    view.SortDescriptions.Add(new SortDescription("GrandTotal", ListSortDirection.Descending));
                }
                else if (sortBy == Application.Current.TryFindResource("DueDate") as string)
                {
                    view.SortDescriptions.Add(new SortDescription("DueDate", ListSortDirection.Ascending));
                }
            }
        }

        private async void ShowInvoiceDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Sale sale)
            {
                try
                {
                    // Show enhanced sale details dialog
                    var dialog = new POSSystem.Views.Dialogs.SaleDetailsDialog(sale);

                    // Use the BatchPaymentDialog's own DialogHost
                    await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, "BatchPaymentDialog");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Error viewing sale details: {ex.Message}",
                        "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private void CloseDetails_Click(object sender, RoutedEventArgs e)
        {
            saleDetailsPopup.IsOpen = false;
        }

        private void ValidatePaymentAmount()
        {
            if (decimal.TryParse(PaymentAmountInput.Text, out decimal amount))
            {
                if (amount > TotalAmount)
                {
                    ShowError(Application.Current.TryFindResource("PaymentAmountExceedsTotal") as string ?? 
                        "Payment amount cannot exceed the total amount.");
                    PaymentAmountInput.Text = TotalAmount.ToString("N2");
                    return;
                }
                
                if (amount <= 0)
                {
                    ShowError(Application.Current.TryFindResource("PaymentAmountMustBePositive") as string ?? 
                        "Payment amount must be greater than zero.");
                    PaymentAmountInput.Text = TotalAmount.ToString("N2");
                    return;
                }

                // Update remaining amount display
                if (IsPartialPayment)
                {
                    decimal remaining = TotalAmount - amount;
                    RemainingAmountValue.Text = $"{remaining:N2} DA";
                    
                    // Visual feedback on remaining amount
                    if (remaining == 0)
                    {
                        RemainingAmountValue.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")); // Green
                    }
                    else
                    {
                        RemainingAmountValue.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D32F2F")); // Red
                    }
                }
            }
        }

        private void ShowError(string message)
        {
            MessageBox.Show(
                message,
                Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }

        private void PartialPaymentToggle_CheckedChanged(object sender, RoutedEventArgs e)
        {
            if (IsPartialPayment)
            {
                RemainingAmountBorder.Visibility = Visibility.Visible;
                PaymentAmountInput.Text = "0.00";
                PaymentAmountInput.SelectAll();
                PaymentAmountInput.Focus();

                // Animate the remaining amount display
                var storyboard = new System.Windows.Media.Animation.Storyboard();
                var fadeIn = new System.Windows.Media.Animation.DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
                System.Windows.Media.Animation.Storyboard.SetTarget(fadeIn, RemainingAmountBorder);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(fadeIn, new PropertyPath("Opacity"));
                storyboard.Children.Add(fadeIn);
                storyboard.Begin();
            }
            else
            {
                RemainingAmountBorder.Visibility = Visibility.Collapsed;
                PaymentAmountInput.Text = TotalAmount.ToString("N2");
            }
            ValidatePaymentAmount();
        }

        private void ProcessButton_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(PaymentAmountInput.Text, out decimal amount))
            {
                if (amount <= 0 || amount > TotalAmount)
                {
                    ShowError(Application.Current.TryFindResource("InvalidPaymentAmount") as string ?? 
                        "Please enter a valid payment amount.");
                    return;
                }

                PaymentAmount = amount;
                SelectedPaymentMethod = (PaymentMethodComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "Cash";
                DialogResult = true;
                Close();
            }
            else
            {
                ShowError(Application.Current.TryFindResource("PleaseEnterValidAmount") as string ?? 
                    "Please enter a valid amount.");
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void PrintSaleDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Sale sale)
            {
                try
                {
                    var printDialog = new PrintDialog();
                    if (printDialog.ShowDialog() == true)
                    {
                        // Create a FlowDocument for printing
                        var document = new FlowDocument()
                        {
                            PagePadding = new Thickness(50),
                            FontFamily = new FontFamily("Traditional Arabic, Segoe UI"),
                            FlowDirection = FlowDirection.RightToLeft,
                            Background = Brushes.White,
                            ColumnWidth = printDialog.PrintableAreaWidth,
                            FontSize = 14
                        };

                        // Add company header section
                        var headerSection = new Section()
                        {
                            BorderBrush = Brushes.LightGray,
                            BorderThickness = new Thickness(0, 0, 0, 1),
                            Padding = new Thickness(0, 0, 0, 20),
                            Margin = new Thickness(0, 0, 0, 30)
                        };

                        // Add title
                        var titleRun = new Run($"{CustomerName} - {Application.Current.TryFindResource("UnpaidInvoices")}")
                        {
                            FontSize = 28,
                            FontWeight = FontWeights.Bold,
                            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3"))
                        };
                        var title = new Paragraph(titleRun)
                        {
                            TextAlignment = TextAlignment.Center,
                            Margin = new Thickness(0, 0, 0, 10)
                        };
                        headerSection.Blocks.Add(title);

                        // Add sale info in a modern card-like layout
                        var infoSection = new Section()
                        {
                            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5")),
                            Padding = new Thickness(20),
                            Margin = new Thickness(0, 0, 0, 30),
                            BorderThickness = new Thickness(1),
                            BorderBrush = Brushes.LightGray
                        };

                        var infoTable = new Table() { CellSpacing = 10 };
                        infoTable.Columns.Add(new TableColumn { Width = new GridLength(150) }); // Label
                        infoTable.Columns.Add(new TableColumn { Width = new GridLength(200) }); // Value

                        void AddInfoRow(string label, string value)
                        {
                            var row = new TableRow();
                            
                            // Label cell (first column)
                            row.Cells.Add(new TableCell(new Paragraph(new Run($"{label}:"))
                            {
                                TextAlignment = TextAlignment.Right,
                                FontWeight = FontWeights.SemiBold,
                                Foreground = Brushes.Gray,
                                FontSize = 14
                            }));
                            
                            // Value cell (second column)
                            row.Cells.Add(new TableCell(new Paragraph(new Run(value))
                            {
                                TextAlignment = TextAlignment.Right,
                                FontWeight = FontWeights.Medium,
                                FontSize = 14
                            }));
                            
                            infoTable.RowGroups[0].Rows.Add(row);
                        }

                        infoTable.RowGroups.Add(new TableRowGroup());
                        AddInfoRow(Application.Current.TryFindResource("SaleDate") as string, sale.SaleDate.ToString("dd/MM/yyyy HH:mm"));
                        AddInfoRow(Application.Current.TryFindResource("UnpaidSalesDueDate") as string, sale.DueDate?.ToString("dd/MM/yyyy") ?? "-");
                        AddInfoRow(Application.Current.TryFindResource("Customer") as string, $"{sale.Customer?.FirstName} {sale.Customer?.LastName}");

                        infoSection.Blocks.Add(infoTable);
                        document.Blocks.Add(infoSection);

                        // Items section header
                        var itemsHeader = new Paragraph(new Run(Application.Current.TryFindResource("SaleItems") as string))
                        {
                            FontSize = 18,
                            FontWeight = FontWeights.Bold,
                            Margin = new Thickness(0, 0, 0, 15),
                            TextAlignment = TextAlignment.Center,
                            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3"))
                        };
                        document.Blocks.Add(itemsHeader);

                        // Create items table with modern styling
                        var itemsTable = new Table()
                        {
                            CellSpacing = 0,
                            BorderBrush = Brushes.LightGray,
                            BorderThickness = new Thickness(1)
                        };

                        // Define columns with fixed widths (RTL order)
                        itemsTable.Columns.Add(new TableColumn { Width = new GridLength(260) }); // Product
                        itemsTable.Columns.Add(new TableColumn { Width = new GridLength(80) });  // Quantity
                        itemsTable.Columns.Add(new TableColumn { Width = new GridLength(120) }); // Unit Price
                        itemsTable.Columns.Add(new TableColumn { Width = new GridLength(120) }); // Total

                        // Add header row with background
                        var headerRow = new TableRow()
                        {
                            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"))
                        };

                        void AddHeaderCell(string text)
                        {
                            headerRow.Cells.Add(new TableCell(new Paragraph(new Run(Application.Current.TryFindResource(text) as string))
                            {
                                FontWeight = FontWeights.SemiBold,
                                TextAlignment = TextAlignment.Right,
                                Padding = new Thickness(8),
                                Foreground = Brushes.Gray,
                                FontSize = 14
                            }));
                        }

                        // Add headers in RTL order
                        AddHeaderCell("Product");
                        AddHeaderCell("Quantity");
                        AddHeaderCell("UnitPrice");
                        AddHeaderCell("Total");

                        itemsTable.RowGroups.Add(new TableRowGroup());
                        itemsTable.RowGroups[0].Rows.Add(headerRow);

                        // Add item rows with alternating background
                        bool isAlternate = false;
                        foreach (var item in sale.Items)
                        {
                            var row = new TableRow()
                            {
                                Background = isAlternate ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FAFAFA")) : Brushes.White
                            };

                            void AddItemCell(string text, TextAlignment alignment = TextAlignment.Right, bool isNumber = false)
                            {
                                var cell = new TableCell(new Paragraph(new Run(text))
                                {
                                    TextAlignment = alignment,
                                    Padding = new Thickness(8),
                                    FontWeight = isNumber ? FontWeights.Medium : FontWeights.Normal,
                                    FontSize = 14
                                });
                                row.Cells.Add(cell);
                            }

                            // Add cells in RTL order
                            AddItemCell(item.Product.Name, TextAlignment.Right);
                            AddItemCell(item.Quantity.ToString(), TextAlignment.Center, true);
                            AddItemCell($"{item.UnitPrice:N2} دج", TextAlignment.Right, true);
                            AddItemCell($"{item.Total:N2} دج", TextAlignment.Right, true);

                            itemsTable.RowGroups[0].Rows.Add(row);
                            isAlternate = !isAlternate;
                        }

                        document.Blocks.Add(itemsTable);

                        // Add totals section
                        var totalsGrid = new Grid()
                        {
                            Width = 300,
                            HorizontalAlignment = HorizontalAlignment.Right,
                            Margin = new Thickness(0, 20, 50, 0)
                        };

                        var totalsStackPanel = new StackPanel();
                        totalsGrid.Children.Add(totalsStackPanel);

                        void AddTotalRow(string label, decimal value, bool isGrandTotal = false)
                        {
                            var grid = new Grid();
                            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(150) }); // Label
                            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(150) }); // Value

                            var labelBlock = new TextBlock
                            {
                                Text = $"{label}:",
                                TextAlignment = TextAlignment.Right,
                                FontWeight = isGrandTotal ? FontWeights.Bold : FontWeights.Normal,
                                Foreground = isGrandTotal ? Brushes.Black : Brushes.Gray,
                                FontSize = isGrandTotal ? 16 : 14,
                                Margin = new Thickness(0, 5, 0, 5)
                            };
                            Grid.SetColumn(labelBlock, 0);

                            var valueBlock = new TextBlock
                            {
                                Text = $"{value:N2} دج",
                                TextAlignment = TextAlignment.Right,
                                FontWeight = isGrandTotal ? FontWeights.Bold : FontWeights.Medium,
                                Foreground = isGrandTotal ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")) : Brushes.Black,
                                FontSize = isGrandTotal ? 16 : 14,
                                Margin = new Thickness(0, 5, 0, 5)
                            };
                            Grid.SetColumn(valueBlock, 1);

                            grid.Children.Add(labelBlock);
                            grid.Children.Add(valueBlock);

                            if (isGrandTotal)
                            {
                                var border = new Border
                                {
                                    BorderBrush = Brushes.LightGray,
                                    BorderThickness = new Thickness(0, 1, 0, 0),
                                    Padding = new Thickness(0, 15, 0, 0),
                                    Margin = new Thickness(0, 15, 0, 5),
                                    Child = grid
                                };
                                totalsStackPanel.Children.Add(border);
                            }
                            else
                            {
                                totalsStackPanel.Children.Add(grid);
                            }
                        }

                        AddTotalRow(Application.Current.TryFindResource("Subtotal") as string, sale.Subtotal);
                        AddTotalRow(Application.Current.TryFindResource("Discount") as string, sale.DiscountAmount);
                        AddTotalRow(Application.Current.TryFindResource("Tax") as string, sale.TaxAmount);
                        AddTotalRow(Application.Current.TryFindResource("GrandTotal") as string, sale.GrandTotal, true);

                        document.Blocks.Add(new BlockUIContainer(totalsGrid));

                        // Add footer
                        var footer = new Paragraph()
                        {
                            TextAlignment = TextAlignment.Center,
                            Margin = new Thickness(0, 30, 0, 0),
                            Foreground = Brushes.Gray
                        };
                        footer.Inlines.Add(new Run("شكراً لتعاملكم معنا") 
                        { 
                            FontStyle = FontStyles.Italic,
                            FontSize = 16,
                            FontFamily = new FontFamily("Traditional Arabic")
                        });
                        document.Blocks.Add(footer);

                        // Print the document
                        printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "Sale Details");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Error printing sale details: {ex.Message}",
                        Application.Current.TryFindResource("Error") as string ?? "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private void PrintAllSaleDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // Create enhanced professional document
                    var document = CreateProfessionalUnpaidInvoicesDocument();

                    // Print the document
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "Unpaid Invoices Report");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error printing sale details: {ex.Message}",
                    Application.Current.TryFindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Creates a professional FlowDocument for unpaid invoices with enhanced formatting and multi-language support
        /// </summary>
        private FlowDocument CreateProfessionalUnpaidInvoicesDocument()
        {
            // Determine current language and flow direction
            var currentLanguage = Application.Current.TryFindResource("CurrentLanguage") as string ?? "en";
            var isRtl = currentLanguage == "ar";
            var flowDirection = isRtl ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;

            // A4 dimensions in device units (96 DPI)
            const double a4Width = 8.27 * 96;  // 8.27 inches
            const double a4Height = 11.69 * 96; // 11.69 inches

            // Create document with professional settings
            var document = new FlowDocument()
            {
                PageWidth = a4Width,
                PageHeight = a4Height,
                PagePadding = new Thickness(48, 48, 48, 48), // 0.5 inch margins
                FontFamily = new FontFamily(isRtl ? "Traditional Arabic, Segoe UI" : "Segoe UI, Traditional Arabic"),
                FlowDirection = flowDirection,
                Background = Brushes.White,
                ColumnWidth = a4Width - 96, // Full width minus margins
                FontSize = 12,
                LineHeight = 1.2,
                IsOptimalParagraphEnabled = true,
                TextAlignment = isRtl ? TextAlignment.Right : TextAlignment.Left
            };

            // Add document sections
            document.Blocks.Add(CreateProfessionalHeader());
            document.Blocks.Add(CreateDocumentTitle());
            document.Blocks.Add(CreateSummarySection());

            // Add each invoice with enhanced formatting
            foreach (var sale in Sales)
            {
                document.Blocks.Add(CreateInvoiceSection(sale));

                // Add page break before next invoice if not the last one
                if (sale != Sales.Last())
                {
                    document.Blocks.Add(new Paragraph(new Run(""))
                    {
                        BreakPageBefore = true,
                        Margin = new Thickness(0)
                    });
                }
            }

            document.Blocks.Add(CreateProfessionalFooter());

            return document;
        }

        /// <summary>
        /// Creates a professional company header with branding
        /// </summary>
        private Section CreateProfessionalHeader()
        {
            var settingsService = new POSSystem.Services.SettingsService();
            var companyName = settingsService.GetSetting("CompanyName") ?? "Your Company Name";
            var companyAddress = settingsService.GetSetting("CompanyAddress") ?? "";
            var companyPhone = settingsService.GetSetting("CompanyPhone") ?? "";
            var companyEmail = settingsService.GetSetting("CompanyEmail") ?? "";
            var companyWebsite = settingsService.GetSetting("CompanyWebsite") ?? "";

            var headerSection = new Section()
            {
                Margin = new Thickness(0, 0, 0, 24),
                BorderBrush = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                BorderThickness = new Thickness(0, 0, 0, 2),
                Padding = new Thickness(0, 0, 0, 16)
            };

            // Company name with enhanced styling
            var companyNamePara = new Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            };
            companyNamePara.Inlines.Add(new Run(companyName.ToUpper())
            {
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
            });
            headerSection.Blocks.Add(companyNamePara);

            // Contact information in a structured layout
            if (!string.IsNullOrEmpty(companyAddress) || !string.IsNullOrEmpty(companyPhone) ||
                !string.IsNullOrEmpty(companyEmail) || !string.IsNullOrEmpty(companyWebsite))
            {
                var contactTable = new Table()
                {
                    CellSpacing = 0,
                    Margin = new Thickness(0, 8, 0, 0)
                };

                contactTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
                contactTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

                var contactRowGroup = new TableRowGroup();
                contactTable.RowGroups.Add(contactRowGroup);

                var contactRow = new TableRow();
                contactRowGroup.Rows.Add(contactRow);

                // Left cell - Address and Phone
                var leftCell = new TableCell()
                {
                    Padding = new Thickness(0, 0, 8, 0)
                };

                if (!string.IsNullOrEmpty(companyAddress))
                {
                    leftCell.Blocks.Add(new Paragraph(new Run(companyAddress))
                    {
                        FontSize = 10,
                        Foreground = Brushes.DarkGray,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 2)
                    });
                }

                if (!string.IsNullOrEmpty(companyPhone))
                {
                    leftCell.Blocks.Add(new Paragraph(new Run($"📞 {companyPhone}"))
                    {
                        FontSize = 10,
                        Foreground = Brushes.DarkGray,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 2)
                    });
                }

                // Right cell - Email and Website
                var rightCell = new TableCell()
                {
                    Padding = new Thickness(8, 0, 0, 0)
                };

                if (!string.IsNullOrEmpty(companyEmail))
                {
                    rightCell.Blocks.Add(new Paragraph(new Run($"✉ {companyEmail}"))
                    {
                        FontSize = 10,
                        Foreground = Brushes.DarkGray,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 2)
                    });
                }

                if (!string.IsNullOrEmpty(companyWebsite))
                {
                    rightCell.Blocks.Add(new Paragraph(new Run($"🌐 {companyWebsite}"))
                    {
                        FontSize = 10,
                        Foreground = Brushes.DarkGray,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 2)
                    });
                }

                contactRow.Cells.Add(leftCell);
                contactRow.Cells.Add(rightCell);
                headerSection.Blocks.Add(contactTable);
            }

            return headerSection;

        }

        /// <summary>
        /// Creates the document title section
        /// </summary>
        private Section CreateDocumentTitle()
        {
            var titleSection = new Section()
            {
                Margin = new Thickness(0, 16, 0, 24)
            };

            var titlePara = new Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0)
            };

            titlePara.Inlines.Add(new Run(Application.Current.TryFindResource("UnpaidInvoices") as string ?? "Unpaid Invoices")
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(68, 68, 68))
            });

            titleSection.Blocks.Add(titlePara);

            // Add customer name if available
            if (!string.IsNullOrEmpty(CustomerName))
            {
                var customerPara = new Paragraph()
                {
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 8, 0, 0)
                };

                customerPara.Inlines.Add(new Run($"{Application.Current.TryFindResource("Customer") as string ?? "Customer"}: ")
                {
                    FontSize = 14,
                    FontWeight = FontWeights.Medium,
                    Foreground = Brushes.Gray
                });

                customerPara.Inlines.Add(new Run(CustomerName)
                {
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                });

                titleSection.Blocks.Add(customerPara);
            }

            return titleSection;
        }

        /// <summary>
        /// Creates the summary section with totals and statistics
        /// </summary>
        private Section CreateSummarySection()
        {
            var summarySection = new Section()
            {
                Margin = new Thickness(0, 0, 0, 32),
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Padding = new Thickness(16),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1)
            };

            // Summary title
            var summaryTitle = new Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 12)
            };
            summaryTitle.Inlines.Add(new Run(Application.Current.TryFindResource("Summary") as string ?? "Summary")
            {
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(68, 68, 68))
            });
            summarySection.Blocks.Add(summaryTitle);

            // Create summary table
            var summaryTable = new Table()
            {
                CellSpacing = 0,
                Margin = new Thickness(0)
            };

            summaryTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            summaryTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            summaryTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var summaryRowGroup = new TableRowGroup();
            summaryTable.RowGroups.Add(summaryRowGroup);

            var summaryRow = new TableRow();
            summaryRowGroup.Rows.Add(summaryRow);

            // Total invoices
            var totalInvoicesCell = new TableCell()
            {
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };
            totalInvoicesCell.Blocks.Add(new Paragraph(new Run(Sales.Count.ToString())
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
            })
            {
                Margin = new Thickness(0, 0, 0, 4)
            });
            totalInvoicesCell.Blocks.Add(new Paragraph(new Run(Application.Current.TryFindResource("TotalInvoices") as string ?? "Total Invoices")
            {
                FontSize = 10,
                Foreground = Brushes.Gray
            })
            {
                Margin = new Thickness(0)
            });

            // Total amount
            var totalAmountCell = new TableCell()
            {
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };
            totalAmountCell.Blocks.Add(new Paragraph(new Run($"{TotalAmount:N2} DA")
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(220, 53, 69))
            })
            {
                Margin = new Thickness(0, 0, 0, 4)
            });
            totalAmountCell.Blocks.Add(new Paragraph(new Run(Application.Current.TryFindResource("TotalAmount") as string ?? "Total Amount")
            {
                FontSize = 10,
                Foreground = Brushes.Gray
            })
            {
                Margin = new Thickness(0)
            });

            // Report date
            var reportDateCell = new TableCell()
            {
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };
            reportDateCell.Blocks.Add(new Paragraph(new Run(DateTime.Now.ToString("dd/MM/yyyy"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69))
            })
            {
                Margin = new Thickness(0, 0, 0, 4)
            });
            reportDateCell.Blocks.Add(new Paragraph(new Run(Application.Current.TryFindResource("ReportDate") as string ?? "Report Date")
            {
                FontSize = 10,
                Foreground = Brushes.Gray
            })
            {
                Margin = new Thickness(0)
            });

            summaryRow.Cells.Add(totalInvoicesCell);
            summaryRow.Cells.Add(totalAmountCell);
            summaryRow.Cells.Add(reportDateCell);

            summarySection.Blocks.Add(summaryTable);
            return summarySection;

        }

        /// <summary>
        /// Creates a professional invoice section with enhanced formatting
        /// </summary>
        private Section CreateInvoiceSection(POSSystem.Models.Sale sale)
        {
            var invoiceSection = new Section()
            {
                Margin = new Thickness(0, 0, 0, 32),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                Padding = new Thickness(0)
            };

            // Invoice header with gradient background
            var headerSection = new Section()
            {
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                Padding = new Thickness(16, 12, 16, 12),
                Margin = new Thickness(0)
            };

            var headerTable = new Table()
            {
                CellSpacing = 0,
                Margin = new Thickness(0)
            };

            headerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            headerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var headerRowGroup = new TableRowGroup();
            headerTable.RowGroups.Add(headerRowGroup);

            var headerRow = new TableRow();
            headerRowGroup.Rows.Add(headerRow);

            // Left cell - Invoice number
            var leftHeaderCell = new TableCell()
            {
                Padding = new Thickness(0)
            };
            leftHeaderCell.Blocks.Add(new Paragraph(new Run($"{Application.Current.TryFindResource("InvoiceNumber") as string ?? "Invoice"} #{sale.InvoiceNumber}")
            {
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Left
            });

            // Right cell - Status and amount
            var rightHeaderCell = new TableCell()
            {
                Padding = new Thickness(0)
            };
            rightHeaderCell.Blocks.Add(new Paragraph(new Run($"{sale.RemainingAmount:N2} DA")
            {
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Right
            });

            headerRow.Cells.Add(leftHeaderCell);
            headerRow.Cells.Add(rightHeaderCell);
            headerSection.Blocks.Add(headerTable);
            invoiceSection.Blocks.Add(headerSection);

            // Invoice details section
            var detailsSection = new Section()
            {
                Padding = new Thickness(16),
                Margin = new Thickness(0)
            };

            // Create details table
            var detailsTable = new Table()
            {
                CellSpacing = 0,
                Margin = new Thickness(0, 0, 0, 16)
            };

            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var detailsRowGroup = new TableRowGroup();
            detailsTable.RowGroups.Add(detailsRowGroup);

            // Sale date row
            var saleDateRow = new TableRow();
            detailsRowGroup.Rows.Add(saleDateRow);

            saleDateRow.Cells.Add(new TableCell(new Paragraph(new Run($"{Application.Current.TryFindResource("SaleDate") as string ?? "Sale Date"}:")
            {
                FontWeight = FontWeights.Medium,
                Foreground = Brushes.Gray
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Left
            })
            {
                Padding = new Thickness(0, 4, 8, 4)
            });

            saleDateRow.Cells.Add(new TableCell(new Paragraph(new Run(sale.SaleDate.ToString("dd/MM/yyyy HH:mm"))
            {
                FontWeight = FontWeights.Medium
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Right
            })
            {
                Padding = new Thickness(8, 4, 0, 4)
            });

            // Due date row
            var dueDateRow = new TableRow();
            detailsRowGroup.Rows.Add(dueDateRow);

            dueDateRow.Cells.Add(new TableCell(new Paragraph(new Run($"{Application.Current.TryFindResource("DueDate") as string ?? "Due Date"}:")
            {
                FontWeight = FontWeights.Medium,
                Foreground = Brushes.Gray
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Left
            })
            {
                Padding = new Thickness(0, 4, 8, 4)
            });

            var dueDateText = sale.DueDate?.ToString("dd/MM/yyyy") ?? "-";
            var dueDateColor = sale.DueDate.HasValue && sale.DueDate < DateTime.Now ?
                new SolidColorBrush(Color.FromRgb(220, 53, 69)) : Brushes.Black;

            dueDateRow.Cells.Add(new TableCell(new Paragraph(new Run(dueDateText)
            {
                FontWeight = FontWeights.Medium,
                Foreground = dueDateColor
            })
            {
                Margin = new Thickness(0),
                TextAlignment = TextAlignment.Right
            })
            {
                Padding = new Thickness(8, 4, 0, 4)
            });

            detailsSection.Blocks.Add(detailsTable);

            // Items table
            detailsSection.Blocks.Add(CreateItemsTable(sale));

            // Totals section
            detailsSection.Blocks.Add(CreateTotalsTable(sale));

            invoiceSection.Blocks.Add(detailsSection);
            return invoiceSection;

        }

        /// <summary>
        /// Creates a professional items table with enhanced styling
        /// </summary>
        private Table CreateItemsTable(POSSystem.Models.Sale sale)
        {
            var itemsTable = new Table()
            {
                CellSpacing = 0,
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 16)
            };

            // Define columns with proper proportions
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(3, GridUnitType.Star) }); // Product
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) }); // Quantity
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) }); // Unit Price
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) }); // Total

            var itemsRowGroup = new TableRowGroup();
            itemsTable.RowGroups.Add(itemsRowGroup);

            // Header row with professional styling
            var headerRow = new TableRow()
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            void AddHeaderCell(string text, TextAlignment alignment = TextAlignment.Center)
            {
                var cell = new TableCell(new Paragraph(new Run(Application.Current.TryFindResource(text) as string ?? text)
                {
                    FontWeight = FontWeights.SemiBold,
                    FontSize = 11,
                    Foreground = new SolidColorBrush(Color.FromRgb(68, 68, 68))
                })
                {
                    TextAlignment = alignment,
                    Margin = new Thickness(0)
                })
                {
                    Padding = new Thickness(12, 8, 12, 8),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                    BorderThickness = new Thickness(0, 0, 1, 1)
                };
                headerRow.Cells.Add(cell);
            }

            AddHeaderCell("Product", TextAlignment.Left);
            AddHeaderCell("Quantity");
            AddHeaderCell("UnitPrice");
            AddHeaderCell("Total");
            itemsRowGroup.Rows.Add(headerRow);

            // Add items with alternating row colors
            bool isAlternateRow = false;
            foreach (var item in sale.Items)
            {
                var row = new TableRow()
                {
                    Background = isAlternateRow ?
                        new SolidColorBrush(Color.FromRgb(252, 253, 254)) :
                        Brushes.White
                };

                void AddItemCell(string text, TextAlignment alignment = TextAlignment.Center, FontWeight fontWeight = default)
                {
                    var cell = new TableCell(new Paragraph(new Run(text)
                    {
                        FontWeight = fontWeight == default ? FontWeights.Normal : fontWeight,
                        FontSize = 10
                    })
                    {
                        TextAlignment = alignment,
                        Margin = new Thickness(0)
                    })
                    {
                        Padding = new Thickness(12, 8, 12, 8),
                        BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                        BorderThickness = new Thickness(0, 0, 1, 1)
                    };
                    row.Cells.Add(cell);
                }

                AddItemCell(item.Product.Name, TextAlignment.Left, FontWeights.Medium);
                AddItemCell(item.Quantity.ToString("N2"));
                AddItemCell($"{item.UnitPrice:N2} DA");
                AddItemCell($"{item.Total:N2} DA", TextAlignment.Right, FontWeights.Medium);

                itemsRowGroup.Rows.Add(row);
                isAlternateRow = !isAlternateRow;
            }

            return itemsTable;
        }

        /// <summary>
        /// Creates a professional totals table
        /// </summary>
        private Table CreateTotalsTable(POSSystem.Models.Sale sale)
        {
            var totalsTable = new Table()
            {
                CellSpacing = 0,
                Margin = new Thickness(0)
            };

            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var totalsRowGroup = new TableRowGroup();
            totalsTable.RowGroups.Add(totalsRowGroup);

            void AddTotalRow(string label, decimal value, bool isHighlight = false, bool isGrandTotal = false)
            {
                var row = new TableRow();

                var labelCell = new TableCell(new Paragraph(new Run($"{label}:")
                {
                    FontWeight = isGrandTotal ? FontWeights.Bold : FontWeights.Medium,
                    FontSize = isGrandTotal ? 12 : 11,
                    Foreground = isGrandTotal ? Brushes.Black : Brushes.Gray
                })
                {
                    TextAlignment = TextAlignment.Right,
                    Margin = new Thickness(0)
                })
                {
                    Padding = new Thickness(8, 4, 12, 4)
                };

                var valueColor = isGrandTotal ? new SolidColorBrush(Color.FromRgb(33, 150, 243)) :
                               isHighlight ? new SolidColorBrush(Color.FromRgb(220, 53, 69)) : Brushes.Black;

                var valueCell = new TableCell(new Paragraph(new Run($"{value:N2} DA")
                {
                    FontWeight = isGrandTotal ? FontWeights.Bold : FontWeights.Medium,
                    FontSize = isGrandTotal ? 12 : 11,
                    Foreground = valueColor
                })
                {
                    TextAlignment = TextAlignment.Right,
                    Margin = new Thickness(0)
                })
                {
                    Padding = new Thickness(12, 4, 8, 4)
                };

                if (isGrandTotal)
                {
                    row.Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));
                    labelCell.BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230));
                    labelCell.BorderThickness = new Thickness(0, 1, 0, 0);
                    valueCell.BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230));
                    valueCell.BorderThickness = new Thickness(0, 1, 0, 0);
                }

                row.Cells.Add(labelCell);
                row.Cells.Add(valueCell);
                totalsRowGroup.Rows.Add(row);
            }

            AddTotalRow(Application.Current.TryFindResource("Subtotal") as string ?? "Subtotal", sale.Subtotal);
            if (sale.DiscountAmount > 0)
                AddTotalRow(Application.Current.TryFindResource("Discount") as string ?? "Discount", sale.DiscountAmount);
            if (sale.TaxAmount > 0)
                AddTotalRow(Application.Current.TryFindResource("Tax") as string ?? "Tax", sale.TaxAmount);
            AddTotalRow(Application.Current.TryFindResource("GrandTotal") as string ?? "Grand Total", sale.GrandTotal, false, true);
            AddTotalRow(Application.Current.TryFindResource("RemainingAmount") as string ?? "Remaining Amount", sale.RemainingAmount, true, true);

            return totalsTable;
        }

        /// <summary>
        /// Creates a professional footer with multi-language support
        /// </summary>
        private Section CreateProfessionalFooter()
        {
            var footerSection = new Section()
            {
                Margin = new Thickness(0, 32, 0, 0),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(0, 1, 0, 0),
                Padding = new Thickness(0, 16, 0, 0)
            };

            // Thank you message in multiple languages
            var thankYouPara = new Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            };

            // Arabic thank you message
            thankYouPara.Inlines.Add(new Run("شكراً لتعاملكم معنا")
            {
                FontFamily = new FontFamily("Traditional Arabic"),
                FontSize = 14,
                FontStyle = FontStyles.Italic,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
            });

            thankYouPara.Inlines.Add(new Run(" • "));

            // French thank you message
            thankYouPara.Inlines.Add(new Run("Merci pour votre confiance")
            {
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = 14,
                FontStyle = FontStyles.Italic,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
            });

            footerSection.Blocks.Add(thankYouPara);

            // Report generation info
            var reportInfoPara = new Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0)
            };

            reportInfoPara.Inlines.Add(new Run($"{Application.Current.TryFindResource("GeneratedOn") as string ?? "Generated on"}: {DateTime.Now:dd/MM/yyyy HH:mm}")
            {
                FontSize = 9,
                Foreground = Brushes.Gray
            });

            footerSection.Blocks.Add(reportInfoPara);

            return footerSection;
        }
    }
} 