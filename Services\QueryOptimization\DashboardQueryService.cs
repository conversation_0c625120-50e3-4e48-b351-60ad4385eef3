using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using System.Diagnostics;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// ✅ NEW: Specialized query service for dashboard operations with maximum performance
    /// Uses optimized queries, proper indexing, and minimal data loading for dashboard metrics
    /// </summary>
    public class DashboardQueryService
    {
        private readonly POSDbContext _context;

        public DashboardQueryService(POSDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get essential sales metrics with minimal database load
        /// </summary>
        public async Task<DashboardMetrics> GetEssentialMetricsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Use optimized query with SQLite-compatible aggregations
                var sales = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .ToListAsync();

                // Perform aggregations on the client side to avoid SQLite decimal issues
                var metrics = sales.Any() ? new DashboardMetrics
                {
                    TotalSales = sales.Sum(s => s.GrandTotal),
                    TransactionCount = sales.Count(),
                    AverageTransactionValue = sales.Average(s => s.GrandTotal),
                    TotalProfit = sales.Sum(s => s.GrandTotal - s.Subtotal + s.DiscountAmount), // Estimated profit
                    PaidSalesTotal = sales.Where(s => s.PaymentStatus == "Paid").Sum(s => s.GrandTotal),
                    UnpaidSalesTotal = sales.Where(s => s.PaymentStatus == "Unpaid").Sum(s => s.GrandTotal),
                    UnpaidSalesCount = sales.Count(s => s.PaymentStatus == "Unpaid")
                } : new DashboardMetrics();

                stopwatch.Stop();
                Debug.WriteLine($"DashboardQueryService.GetEssentialMetricsAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                return metrics ?? new DashboardMetrics();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetEssentialMetricsAsync: {ex.Message}");
                return new DashboardMetrics();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get sales trend data using indexed queries
        /// </summary>
        public async Task<List<SalesTrendData>> GetSalesTrendAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                var trendData = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate && s.Status == "Completed")
                    .GroupBy(s => s.SaleDate.Date)
                    .Select(g => new SalesTrendData
                    {
                        Date = g.Key,
                        // SQLite decimal aggregate workaround: cast to double then back to decimal
                        TotalSales = (decimal)g.Sum(s => (double)s.GrandTotal),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = (decimal)g.Average(s => (double)s.GrandTotal)
                    })
                    .OrderBy(t => t.Date)
                    .ToListAsync();

                stopwatch.Stop();
                Debug.WriteLine($"DashboardQueryService.GetSalesTrendAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                return trendData;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSalesTrendAsync: {ex.Message}");
                return new List<SalesTrendData>();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get top performing products using indexed queries
        /// </summary>
        public async Task<List<ProductPerformanceData>> GetTopProductsAsync(DateTime startDate, DateTime endDate, int limit = 10)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                var topProducts = await _context.SaleItems
                    .AsNoTracking()
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && 
                                si.Sale.SaleDate <= endDate && 
                                si.Sale.Status == "Completed")
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new ProductPerformanceData
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.Name,
                        TotalQuantitySold = g.Sum(si => si.Quantity), // ✅ WEIGHT-BASED FIX: Remove (int) cast to preserve decimal quantities
                        TotalRevenue = g.Sum(si => si.Total),
                        TransactionCount = g.Count()
                    })
                    .OrderByDescending(p => p.TotalRevenue)
                    .Take(limit)
                    .ToListAsync();

                stopwatch.Stop();
                Debug.WriteLine($"DashboardQueryService.GetTopProductsAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                return topProducts;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetTopProductsAsync: {ex.Message}");
                return new List<ProductPerformanceData>();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get low stock products using indexed queries
        /// </summary>
        public async Task<List<LowStockProduct>> GetLowStockProductsAsync()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                var lowStockProducts = await _context.Products
                    .AsNoTracking()
                    .Where(p => p.IsActive && p.StockQuantity <= (p.ReorderPoint > 0 ? p.ReorderPoint : 10))
                    .Select(p => new LowStockProduct
                    {
                        Id = p.Id,
                        Name = p.Name,
                        SKU = p.SKU,
                        CurrentStock = p.StockQuantity,
                        ReorderPoint = p.ReorderPoint > 0 ? p.ReorderPoint : 10
                    })
                    .OrderBy(p => p.CurrentStock)
                    .ToListAsync();

                stopwatch.Stop();
                Debug.WriteLine($"DashboardQueryService.GetLowStockProductsAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                return lowStockProducts;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetLowStockProductsAsync: {ex.Message}");
                return new List<LowStockProduct>();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZED: Get hourly sales distribution for analytics
        /// </summary>
        public async Task<Dictionary<int, decimal>> GetHourlySalesDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                var hourlyData = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate && s.Status == "Completed")
                    .GroupBy(s => s.SaleDate.Hour)
                    .Select(g => new { Hour = g.Key, Total = (decimal)g.Sum(s => (double)s.GrandTotal) })
                    .ToListAsync();

                var result = hourlyData.ToDictionary(x => x.Hour, x => x.Total);

                stopwatch.Stop();
                Debug.WriteLine($"DashboardQueryService.GetHourlySalesDistributionAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetHourlySalesDistributionAsync: {ex.Message}");
                return new Dictionary<int, decimal>();
            }
        }
    }

    #region Data Transfer Objects

    public class DashboardMetrics
    {
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal PaidSalesTotal { get; set; }
        public decimal UnpaidSalesTotal { get; set; }
        public int UnpaidSalesCount { get; set; }
    }

    public class SalesTrendData
    {
        public DateTime Date { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    public class ProductPerformanceData
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal TotalQuantitySold { get; set; } // ✅ WEIGHT-BASED FIX: Changed from int to decimal to support weight-based products
        public decimal TotalRevenue { get; set; }
        public int TransactionCount { get; set; }
    }

    public class LowStockProduct
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string SKU { get; set; }
        public decimal CurrentStock { get; set; }
        public int ReorderPoint { get; set; }
    }

    #endregion
}
