using System;
using System.Linq;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test utility to debug permission enforcement issues step by step
    /// </summary>
    public class PermissionDebuggingTest
    {
        private readonly DatabaseService _dbService;
        private readonly AuthenticationService _authService;

        public PermissionDebuggingTest()
        {
            _dbService = new DatabaseService();
            _authService = new AuthenticationService(_dbService);
        }

        /// <summary>
        /// Creates a test user with specific custom permissions and tests the complete flow
        /// </summary>
        public void TestCompletePermissionFlow()
        {
            Console.WriteLine("=== COMPLETE PERMISSION FLOW TEST ===");
            Console.WriteLine();

            try
            {
                // Step 1: Create test user with custom permissions
                var testUser = CreateTestUserWithCustomPermissions();
                if (testUser == null)
                {
                    Console.WriteLine("❌ FAILED: Could not create test user");
                    return;
                }

                Console.WriteLine($"✅ Test user created: {testUser.Username} (ID: {testUser.Id})");
                Console.WriteLine();

                // Step 1.5: Test immediate retrieval after creation
                Console.WriteLine("--- STEP 1.5: Test Immediate Permission Retrieval ---");
                var immediatePermissions = _dbService.GetUserPermissions(testUser.Id);
                if (immediatePermissions != null)
                {
                    Console.WriteLine("✅ Permissions retrieved immediately after creation:");
                    Console.WriteLine($"  - CanManageUsers: {immediatePermissions.CanManageUsers} (expected: False)");
                    Console.WriteLine($"  - CanAccessSettings: {immediatePermissions.CanAccessSettings} (expected: False)");
                    Console.WriteLine($"  - CanManageProducts: {immediatePermissions.CanManageProducts} (expected: True)");
                    Console.WriteLine($"  - CanViewReports: {immediatePermissions.CanViewReports} (expected: True)");

                    // Check if permissions are correct
                    bool permissionsCorrect =
                        !immediatePermissions.CanManageUsers &&
                        !immediatePermissions.CanAccessSettings &&
                        immediatePermissions.CanManageProducts &&
                        immediatePermissions.CanViewReports;

                    if (permissionsCorrect)
                    {
                        Console.WriteLine("✅ Custom permissions are saved correctly!");
                    }
                    else
                    {
                        Console.WriteLine("❌ Custom permissions are NOT saved correctly!");
                        Console.WriteLine("This indicates the permission saving process is failing.");
                    }
                }
                else
                {
                    Console.WriteLine("❌ FAILED: Could not retrieve permissions immediately after creation");
                    return;
                }
                Console.WriteLine();

                // Step 2: Verify permissions were saved correctly
                Console.WriteLine("--- STEP 2: Verify Permissions Saved ---");
                var savedPermissions = _dbService.GetUserPermissions(testUser.Id);
                if (savedPermissions == null)
                {
                    Console.WriteLine("❌ FAILED: No custom permissions found in database");
                    return;
                }

                Console.WriteLine("✅ Custom permissions found in database:");
                Console.WriteLine($"  - CanManageUsers: {savedPermissions.CanManageUsers}");
                Console.WriteLine($"  - CanAccessSettings: {savedPermissions.CanAccessSettings}");
                Console.WriteLine($"  - CanManageProducts: {savedPermissions.CanManageProducts}");
                Console.WriteLine($"  - CanViewReports: {savedPermissions.CanViewReports}");
                Console.WriteLine();

                // Step 3: Test authentication and login
                Console.WriteLine("--- STEP 3: Test Authentication ---");
                bool loginResult = _authService.Login("permissiontest", "test123");
                if (!loginResult)
                {
                    Console.WriteLine("❌ FAILED: Could not login with test user");
                    return;
                }

                Console.WriteLine("✅ Login successful");
                Console.WriteLine($"Current user: {_authService.CurrentUser?.Username}");
                Console.WriteLine();

                // Step 4: Test individual permission checks
                Console.WriteLine("--- STEP 4: Test Permission Checks ---");
                TestPermissionCheck("users.manage", false); // Should be FALSE (unchecked)
                TestPermissionCheck("settings.access", false); // Should be FALSE (unchecked)
                TestPermissionCheck("products.manage", true); // Should be TRUE (checked)
                TestPermissionCheck("reports.view", true); // Should be TRUE (checked)
                Console.WriteLine();

                // Step 5: Test UserPermissionsService directly
                Console.WriteLine("--- STEP 5: Test UserPermissionsService Directly ---");
                var permissionsService = new UserPermissionsService(_dbService);
                permissionsService.Initialize(_authService.CurrentUser);

                Console.WriteLine("Direct UserPermissionsService tests:");
                TestDirectPermissionCheck(permissionsService, "users.manage", false);
                TestDirectPermissionCheck(permissionsService, "settings.access", false);
                TestDirectPermissionCheck(permissionsService, "products.manage", true);
                TestDirectPermissionCheck(permissionsService, "reports.view", true);
                Console.WriteLine();

                // Step 6: Test permission loading
                Console.WriteLine("--- STEP 6: Test Permission Loading ---");
                var loadedPermissions = permissionsService.GetUserPermissions(testUser.Id);
                if (loadedPermissions != null)
                {
                    Console.WriteLine("✅ Permissions loaded by UserPermissionsService:");
                    Console.WriteLine($"  - CanManageUsers: {loadedPermissions.CanManageUsers}");
                    Console.WriteLine($"  - CanAccessSettings: {loadedPermissions.CanAccessSettings}");
                    Console.WriteLine($"  - CanManageProducts: {loadedPermissions.CanManageProducts}");
                    Console.WriteLine($"  - CanViewReports: {loadedPermissions.CanViewReports}");
                }
                else
                {
                    Console.WriteLine("❌ FAILED: UserPermissionsService could not load permissions");
                }

                Console.WriteLine();
                Console.WriteLine("=== TEST COMPLETE ===");

                // Cleanup
                _authService.Logout();
                CleanupTestUser();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private User CreateTestUserWithCustomPermissions()
        {
            try
            {
                Console.WriteLine("Creating test user with custom permissions...");

                // Get Admin role
                var roles = _dbService.GetAllRoles();
                var adminRole = roles.FirstOrDefault(r => r.Name == "Admin");
                if (adminRole == null)
                {
                    Console.WriteLine("ERROR: Admin role not found!");
                    return null;
                }

                Console.WriteLine($"Using Admin role (ID: {adminRole.Id})");

                // Create test user - EXACTLY like UserDialog does
                var testUser = new User
                {
                    Username = "permissiontest",
                    Password = "test123", // This will be hashed by AddUser
                    FirstName = "Permission",
                    LastName = "Test",
                    Email = "<EMAIL>",
                    RoleId = adminRole.Id,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                Console.WriteLine("Step 1: Adding user (this creates default permissions)...");

                // Add user (this creates default permissions) - EXACTLY like UserDialog line 450
                _dbService.AddUser(testUser);

                Console.WriteLine($"User added with ID: {testUser.Id}");

                // Check what default permissions were created
                var defaultPermissions = _dbService.GetUserPermissions(testUser.Id);
                if (defaultPermissions != null)
                {
                    Console.WriteLine("Default permissions created by AddUser:");
                    Console.WriteLine($"  - CanManageUsers: {defaultPermissions.CanManageUsers}");
                    Console.WriteLine($"  - CanAccessSettings: {defaultPermissions.CanAccessSettings}");
                    Console.WriteLine($"  - CanManageProducts: {defaultPermissions.CanManageProducts}");
                    Console.WriteLine($"  - CanViewReports: {defaultPermissions.CanViewReports}");
                }

                Console.WriteLine("Step 2: Creating custom permissions object...");

                // Create custom permissions (different from Admin defaults) - EXACTLY like UserDialog line 441
                var customPermissions = new UserPermissions
                {
                    UserId = testUser.Id,
                    CanCreateSales = true,
                    CanVoidSales = true,
                    CanApplyDiscount = true,
                    CanViewSalesHistory = true,
                    CanManageProducts = true,        // TRUE - should have access
                    CanManageCategories = true,
                    CanViewInventory = true,
                    CanAdjustInventory = true,
                    CanManageExpenses = true,
                    CanManageCashDrawer = true,
                    CanViewReports = true,           // TRUE - should have access
                    CanManagePrices = true,
                    CanManageCustomers = true,
                    CanManageSuppliers = true,
                    CanManageUsers = false,          // FALSE - should NOT have access
                    CanManageRoles = false,
                    CanAccessSettings = false,       // FALSE - should NOT have access
                    CanViewLogs = false,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                Console.WriteLine("Custom permissions to save:");
                Console.WriteLine($"  - CanManageUsers: {customPermissions.CanManageUsers}");
                Console.WriteLine($"  - CanAccessSettings: {customPermissions.CanAccessSettings}");
                Console.WriteLine($"  - CanManageProducts: {customPermissions.CanManageProducts}");
                Console.WriteLine($"  - CanViewReports: {customPermissions.CanViewReports}");

                Console.WriteLine("Step 3: Updating with custom permissions...");

                // Update with custom permissions - EXACTLY like UserDialog line 460
                _dbService.UpdateUserPermissions(customPermissions);

                Console.WriteLine("Custom permissions update completed");

                return testUser;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to create test user: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        private void TestPermissionCheck(string permission, bool expectedResult)
        {
            try
            {
                bool result = _authService.HasPermission(permission);
                string status = result == expectedResult ? "✅ PASS" : "❌ FAIL";
                Console.WriteLine($"{status}: {permission} = {result} (expected: {expectedResult})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {permission} threw exception: {ex.Message}");
            }
        }

        private void TestDirectPermissionCheck(UserPermissionsService service, string permission, bool expectedResult)
        {
            try
            {
                bool result = service.HasPermission(permission);
                string status = result == expectedResult ? "✅ PASS" : "❌ FAIL";
                Console.WriteLine($"{status}: {permission} = {result} (expected: {expectedResult})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {permission} threw exception: {ex.Message}");
            }
        }

        private void CleanupTestUser()
        {
            try
            {
                var users = _dbService.GetAllUsers();
                var testUser = users.FirstOrDefault(u => u.Username == "permissiontest");
                if (testUser != null)
                {
                    _dbService.DeleteUser(testUser.Id);
                    Console.WriteLine("Test user cleaned up successfully");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to cleanup test user: {ex.Message}");
            }
        }
    }
}
