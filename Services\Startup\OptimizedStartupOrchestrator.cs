using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services.Startup
{
    /// <summary>
    /// ✅ CRITICAL STARTUP OPTIMIZATION: Orchestrates optimized application startup with parallel initialization, lazy loading, and progressive UI
    /// </summary>
    public class OptimizedStartupOrchestrator : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<OptimizedStartupOrchestrator> _logger;
        private readonly StartupPerformanceMonitor _performanceMonitor;
        private readonly ParallelServiceInitializer _serviceInitializer;
        private readonly LazyLoadingManager _lazyLoadingManager;
        private readonly ProgressiveUILoader _progressiveUILoader;
        private volatile bool _disposed;
        private readonly object _disposeLock = new object();

        public OptimizedStartupOrchestrator(IServiceProvider serviceProvider, ILogger<OptimizedStartupOrchestrator> logger = null)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger;

            try
            {
                Debug.WriteLine("[STARTUP-ORCHESTRATOR] Initializing startup optimization services...");

                // Initialize startup optimization services with error handling
                _performanceMonitor = _serviceProvider.GetService<StartupPerformanceMonitor>();
                Debug.WriteLine($"[STARTUP-ORCHESTRATOR] StartupPerformanceMonitor: {(_performanceMonitor != null ? "✅" : "❌")}");

                _serviceInitializer = _serviceProvider.GetService<ParallelServiceInitializer>();
                Debug.WriteLine($"[STARTUP-ORCHESTRATOR] ParallelServiceInitializer: {(_serviceInitializer != null ? "✅" : "❌")}");

                _lazyLoadingManager = _serviceProvider.GetService<LazyLoadingManager>();
                Debug.WriteLine($"[STARTUP-ORCHESTRATOR] LazyLoadingManager: {(_lazyLoadingManager != null ? "✅" : "❌")}");

                _progressiveUILoader = _serviceProvider.GetService<ProgressiveUILoader>();
                Debug.WriteLine($"[STARTUP-ORCHESTRATOR] ProgressiveUILoader: {(_progressiveUILoader != null ? "✅" : "❌")}");

                Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] Optimized Startup Orchestrator initialized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Error initializing services: {ex.Message}");
                _logger?.LogError(ex, "Error initializing startup orchestrator services");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Execute optimized application startup sequence
        /// </summary>
        public async Task ExecuteOptimizedStartupAsync(CancellationToken cancellationToken = default)
        {
            // Check if already disposed
            if (_disposed)
            {
                Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Cannot execute startup - orchestrator is disposed");
                throw new ObjectDisposedException(nameof(OptimizedStartupOrchestrator));
            }

            // Additional safety check for application shutdown
            try
            {
                // Test if service provider is still accessible
                var testService = _serviceProvider.GetService<object>();
            }
            catch (ObjectDisposedException)
            {
                Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Service provider is disposed - aborting startup");
                return; // Don't throw during shutdown
            }

            using var overallTracker = _performanceMonitor?.TrackStartupPhase("OptimizedStartup", "Orchestrator");

            try
            {
                Debug.WriteLine("🚀 [STARTUP-ORCHESTRATOR] Beginning optimized application startup");

                // Phase 1: Show splash screen and begin progressive loading (if available)
                if (_progressiveUILoader != null && !_disposed)
                {
                    Debug.WriteLine("[STARTUP-ORCHESTRATOR] Executing progressive UI loading...");
                    await _progressiveUILoader.ExecuteStandardLoadingSequenceAsync(_serviceProvider, cancellationToken);
                }
                else
                {
                    Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] ProgressiveUILoader not available, skipping splash screen");
                }

                // Check if disposed during splash screen
                if (_disposed)
                {
                    Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Orchestrator disposed during splash screen");
                    return;
                }

                // Phase 2: Initialize services in parallel (if available)
                if (_serviceInitializer != null && !_disposed)
                {
                    Debug.WriteLine("[STARTUP-ORCHESTRATOR] Executing parallel service initialization...");
                    await InitializeServicesInParallelAsync(cancellationToken);
                }
                else
                {
                    Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] ParallelServiceInitializer not available, skipping parallel initialization");
                }

                // Check if disposed during service initialization
                if (_disposed)
                {
                    Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Orchestrator disposed during service initialization");
                    return;
                }

                // Phase 3: Validate startup completion
                await ValidateStartupCompletionAsync(cancellationToken);

                // Phase 4: Complete startup and generate performance report
                CompleteStartup();

                Debug.WriteLine("🎉 [STARTUP-ORCHESTRATOR] Optimized application startup completed successfully");
            }
            catch (ObjectDisposedException)
            {
                Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Service provider disposed during startup");
                // Don't throw - this is expected during application shutdown
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] Startup cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Critical error during startup: {ex.Message}");
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Stack trace: {ex.StackTrace}");
                _logger?.LogError(ex, "Critical error during optimized startup");
                throw new StartupException($"Failed to complete optimized startup: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Initialize services in parallel during splash screen display
        /// </summary>
        private async Task InitializeServicesInParallelAsync(CancellationToken cancellationToken)
        {
            using var parallelTracker = _performanceMonitor?.TrackStartupPhase("ParallelServiceInit", "Services");

            try
            {
                Debug.WriteLine("[STARTUP-ORCHESTRATOR] Starting parallel service initialization");

                if (_serviceInitializer == null)
                {
                    Debug.WriteLine("⚠️ [STARTUP-ORCHESTRATOR] ParallelServiceInitializer is null, skipping parallel initialization");
                    return;
                }

                // Initialize services in parallel
                await _serviceInitializer.InitializeServicesAsync(cancellationToken);

                // Get initialization results
                var results = _serviceInitializer.GetInitializationResults();
                var successCount = 0;
                var failureCount = 0;

                foreach (var result in results.Values)
                {
                    if (result.IsSuccessful)
                    {
                        successCount++;
                        Debug.WriteLine($"✅ [STARTUP-ORCHESTRATOR] Service {result.ServiceName} initialized in {result.ExecutionTimeMs}ms");
                    }
                    else
                    {
                        failureCount++;
                        Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Service {result.ServiceName} failed: {result.ErrorMessage}");
                    }
                }

                Debug.WriteLine($"[STARTUP-ORCHESTRATOR] Service initialization completed: {successCount} successful, {failureCount} failed");

                if (failureCount > 0)
                {
                    _logger?.LogWarning("Some services failed to initialize during startup: {FailureCount} failures", failureCount);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Error during parallel service initialization: {ex.Message}");
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Stack trace: {ex.StackTrace}");

                // Don't throw - service initialization failures shouldn't block startup completely
                _logger?.LogError(ex, "Error during parallel service initialization");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Validate that startup completed successfully
        /// </summary>
        private async Task ValidateStartupCompletionAsync(CancellationToken cancellationToken)
        {
            using var validationTracker = _performanceMonitor?.TrackStartupPhase("StartupValidation", "Validation");
            
            try
            {
                Debug.WriteLine("[STARTUP-ORCHESTRATOR] Validating startup completion");

                // Validate critical services are available
                await ValidateCriticalServicesAsync(cancellationToken);

                // Validate database connectivity
                await ValidateDatabaseConnectivityAsync(cancellationToken);

                // Validate UI readiness
                await ValidateUIReadinessAsync(cancellationToken);

                Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] Startup validation completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Startup validation failed: {ex.Message}");
                throw new StartupValidationException("Startup validation failed", ex);
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Validate critical services are available
        /// </summary>
        private async Task ValidateCriticalServicesAsync(CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                if (_disposed)
                {
                    Debug.WriteLine("⚠️ [STARTUP-VALIDATION] Cannot validate services - orchestrator is disposed");
                    return;
                }

                var criticalServices = new[]
                {
                    typeof(IDatabaseService),
                    typeof(IAuthenticationService),
                    typeof(IAlertService)
                };

                foreach (var serviceType in criticalServices)
                {
                    if (_disposed) return; // Check disposal between services

                    try
                    {
                        var service = _serviceProvider.GetService(serviceType);
                        if (service == null)
                        {
                            throw new StartupValidationException($"Critical service {serviceType.Name} is not available");
                        }

                        Debug.WriteLine($"✅ [STARTUP-VALIDATION] Critical service {serviceType.Name} is available");
                    }
                    catch (ObjectDisposedException)
                    {
                        Debug.WriteLine($"⚠️ [STARTUP-VALIDATION] Service provider disposed while validating {serviceType.Name}");
                        return; // Don't throw - this is expected during shutdown
                    }
                }
            }, cancellationToken);
        }

        /// <summary>
        /// ✅ INTERNAL: Validate database connectivity
        /// </summary>
        private async Task ValidateDatabaseConnectivityAsync(CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                if (_disposed)
                {
                    Debug.WriteLine("⚠️ [STARTUP-VALIDATION] Cannot validate database - orchestrator is disposed");
                    return;
                }

                try
                {
                    var dbService = _serviceProvider.GetRequiredService<IDatabaseService>();

                    // Test database connectivity by getting user count
                    var users = dbService.GetAllUsers();
                    Debug.WriteLine($"✅ [STARTUP-VALIDATION] Database connectivity verified: {users.Count} users found");
                }
                catch (ObjectDisposedException)
                {
                    Debug.WriteLine("⚠️ [STARTUP-VALIDATION] Service provider disposed during database validation");
                    return; // Don't throw - this is expected during shutdown
                }
                catch (Exception ex)
                {
                    throw new StartupValidationException($"Database connectivity validation failed: {ex.Message}", ex);
                }
            }, cancellationToken);
        }

        /// <summary>
        /// ✅ INTERNAL: Validate UI readiness
        /// </summary>
        private async Task ValidateUIReadinessAsync(CancellationToken cancellationToken)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                try
                {
                    // Validate that the main application is ready
                    if (Application.Current == null)
                    {
                        throw new StartupValidationException("Application.Current is null");
                    }

                    // Validate dispatcher is available
                    if (Application.Current.Dispatcher == null)
                    {
                        throw new StartupValidationException("Application dispatcher is null");
                    }

                    Debug.WriteLine("✅ [STARTUP-VALIDATION] UI readiness validated");
                }
                catch (Exception ex)
                {
                    throw new StartupValidationException($"UI readiness validation failed: {ex.Message}", ex);
                }
            }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);
        }

        /// <summary>
        /// ✅ CRITICAL: Complete startup and generate performance report
        /// </summary>
        private void CompleteStartup()
        {
            try
            {
                // Complete performance monitoring
                _performanceMonitor?.CompleteStartup();

                // Get final statistics
                var startupStats = _performanceMonitor?.GetStartupStatistics();
                var lazyLoadingStats = _lazyLoadingManager?.GetStatistics();

                if (startupStats != null)
                {
                    Debug.WriteLine($"🎉 [STARTUP-ORCHESTRATOR] Startup completed in {startupStats.TotalStartupTimeMs}ms");
                    Debug.WriteLine($"   Total phases: {startupStats.TotalPhases}");
                    Debug.WriteLine($"   Successful phases: {startupStats.SuccessfulPhases}");
                    Debug.WriteLine($"   Average phase time: {startupStats.AveragePhaseTimeMs:F1}ms");
                }

                if (lazyLoadingStats != null)
                {
                    Debug.WriteLine($"   Lazy loading: {lazyLoadingStats.LoadedComponents}/{lazyLoadingStats.TotalComponents} components loaded");
                }

                // Log final metrics
                _logger?.LogInformation("Optimized startup completed successfully in {TotalTime}ms with {PhaseCount} phases", 
                    startupStats?.TotalStartupTimeMs ?? 0, startupStats?.TotalPhases ?? 0);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Error completing startup: {ex.Message}");
                _logger?.LogError(ex, "Error completing startup");
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get comprehensive startup report
        /// </summary>
        public string GetStartupReport()
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== Optimized Startup Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // Performance monitoring report
            if (_performanceMonitor != null)
            {
                report.AppendLine(_performanceMonitor.GenerateStartupReport());
                report.AppendLine();
            }

            // Service initialization report
            if (_serviceInitializer != null)
            {
                var results = _serviceInitializer.GetInitializationResults();
                report.AppendLine("=== Service Initialization Results ===");
                foreach (var result in results.Values)
                {
                    var status = result.IsSuccessful ? "✅" : "❌";
                    report.AppendLine($"{status} {result.ServiceName}: {result.ExecutionTimeMs}ms");
                    if (!result.IsSuccessful && !string.IsNullOrEmpty(result.ErrorMessage))
                    {
                        report.AppendLine($"   Error: {result.ErrorMessage}");
                    }
                }
                report.AppendLine();
            }

            // Lazy loading report
            if (_lazyLoadingManager != null)
            {
                var stats = _lazyLoadingManager.GetStatistics();
                report.AppendLine("=== Lazy Loading Statistics ===");
                report.AppendLine($"Total components: {stats.TotalComponents}");
                report.AppendLine($"Loaded components: {stats.LoadedComponents}");
                report.AppendLine($"Failed components: {stats.FailedComponents}");
                report.AppendLine($"Average load time: {stats.AverageLoadTimeMs:F1}ms");
            }

            return report.ToString();
        }

        public void Dispose()
        {
            lock (_disposeLock)
            {
                if (_disposed) return;

                try
                {
                    Debug.WriteLine("[STARTUP-ORCHESTRATOR] Starting disposal...");

                    // Mark as disposed first to prevent new operations
                    _disposed = true;

                    // Dispose services in reverse order of initialization
                    try
                    {
                        _progressiveUILoader?.Dispose();
                        Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] ProgressiveUILoader disposed");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ [STARTUP-ORCHESTRATOR] Error disposing ProgressiveUILoader: {ex.Message}");
                    }

                    try
                    {
                        _lazyLoadingManager?.Dispose();
                        Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] LazyLoadingManager disposed");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ [STARTUP-ORCHESTRATOR] Error disposing LazyLoadingManager: {ex.Message}");
                    }

                    try
                    {
                        _serviceInitializer?.Dispose();
                        Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] ParallelServiceInitializer disposed");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ [STARTUP-ORCHESTRATOR] Error disposing ParallelServiceInitializer: {ex.Message}");
                    }

                    try
                    {
                        _performanceMonitor?.Dispose();
                        Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] StartupPerformanceMonitor disposed");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ [STARTUP-ORCHESTRATOR] Error disposing StartupPerformanceMonitor: {ex.Message}");
                    }

                    Debug.WriteLine("✅ [STARTUP-ORCHESTRATOR] Optimized Startup Orchestrator disposed successfully");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [STARTUP-ORCHESTRATOR] Error during disposal: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// Custom exceptions for startup orchestration
    /// </summary>
    public class StartupException : Exception
    {
        public StartupException(string message) : base(message) { }
        public StartupException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class StartupValidationException : Exception
    {
        public StartupValidationException(string message) : base(message) { }
        public StartupValidationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
