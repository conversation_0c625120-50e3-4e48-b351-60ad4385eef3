-- Fix Product 483: Add better searchable content
-- Run this in SQLite DB Browser to make the product more searchable

-- Update the product with better search content
UPDATE Products 
SET 
    Name = 'Test Weight Product',
    SKU = 'WEIGHT-TEST-001',
    Description = 'Test weight-based product for debugging',
    UpdatedAt = datetime('now')
WHERE Id = 483;

-- Add a barcode for testing (if ProductBarcodes table exists)
INSERT OR IGNORE INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description, CreatedAt)
SELECT 
    483 as ProductId,
    '1234567890123' as Barcode,
    1 as IsPrimary,
    'Test barcode for weight product' as Description,
    datetime('now') as CreatedAt
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='ProductBarcodes')
  AND NOT EXISTS (SELECT 1 FROM ProductBarcodes WHERE ProductId = 483);

-- Verify the changes
SELECT 'Updated product details:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    Description,
    IsWeightBased,
    IsActive,
    StockQuantity
FROM Products 
WHERE Id = 483;

-- Check if barcode was added
SELECT 'Barcode information:' as Info;
SELECT pb.Barcode, pb.IsPrimary
FROM ProductBarcodes pb
WHERE pb.ProductId = 483;
