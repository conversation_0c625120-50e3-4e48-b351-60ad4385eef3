using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.Models;

namespace POSSystem.Converters
{
    /// <summary>
    /// Converter to format stock display with proper decimal handling for weight-based products
    /// and unit display for both weight-based and unit-based products
    /// </summary>
    public class StockDisplayConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Product product)
            {
                // ✅ FIX: Use StockQuantity directly since it's already calculated correctly during loading
                // This avoids additional database queries that GetTotalStockDecimal() would make
                var stockDecimal = product.StockQuantity;
                var stockDisplay = stockDecimal % 1 == 0 ? stockDecimal.ToString("F0") : stockDecimal.ToString("N3");

                // ✅ PERFORMANCE FIX: Removed debug logging that was called on every UI update
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[STOCK-DISPLAY-CONVERTER] Product {product.Id} ({product.Name}): StockQuantity = {stockDecimal}, Display = {stockDisplay}");
                #endif

                // Add unit of measure if available
                string unit = product.UnitOfMeasure?.Abbreviation;
                if (!string.IsNullOrEmpty(unit))
                {
                    return $"{stockDisplay} {unit}";
                }

                // For products without specific unit of measure, add appropriate suffix
                if (product.IsWeightBased)
                {
                    return stockDisplay; // Weight-based products typically have their own units
                }
                else
                {
                    return $"{stockDisplay} units"; // Unit-based products get "units" suffix
                }
            }
            return "0 units";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
