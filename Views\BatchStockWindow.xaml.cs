using System.Windows;
using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;
using System.Linq;
using System;

namespace POSSystem.Views
{
    public partial class BatchStockWindow : Window
    {
        private readonly Product _product;
        private readonly DatabaseService _dbService;
        private readonly BatchStockViewModel _viewModel;
        
        public BatchStockWindow(Product product, DatabaseService dbService)
        {
            InitializeComponent();
            _product = product;
            _dbService = dbService;
            
            // Create and set up the BatchStockViewModel
            _viewModel = new BatchStockViewModel(product, dbService);
            DataContext = _viewModel;
            
            // Set the window title with product name
            this.Title = $"Batch Stock - {product.Name}";
            
            // Log for debugging
            System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Opening BatchStockWindow for '{product.Name}' (ID: {product.Id}). Current StockQuantity={product.StockQuantity}");
            
            // Refresh batch data
            _viewModel.LoadBatches();
            
            // Update the UI with batch statistics
            UpdateBatchStatistics();
        }
        
        private void UpdateBatchStatistics()
        {
            // Log the batch statistics
            System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] BatchStockWindow - TotalStock: {_viewModel.TotalStock}, BatchCount: {_viewModel.Batches?.Count ?? 0}");
            
            // Update product with the correct total stock
            if (_product != null && _product.TrackBatches)
            {
                // Update the product's stock quantity to include batch quantities
                var totalStock = _viewModel.TotalStock + _product.StockQuantity;
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Total Product Stock: {totalStock} (General stock: {_product.StockQuantity} + Batch stock: {_viewModel.TotalStock})");
            }
        }

        private void AddBatch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addBatchDialog = new AddBatchDialog(_product);
                if (addBatchDialog.ShowDialog() == true)
                {
                    _viewModel.AddBatch(addBatchDialog.BatchStock);
                    System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Added new batch: {addBatchDialog.BatchStock.BatchNumber}, Quantity: {addBatchDialog.BatchStock.Quantity}");
                    
                    // Update statistics after adding a batch
                    UpdateBatchStatistics();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error adding batch: {ex.Message}");
                MessageBox.Show($"Error adding batch: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditBatch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = (Button)sender;
                var batch = (BatchStock)button.DataContext;
                
                var editDialog = new AddBatchDialog(_product)
                {
                    Owner = this,
                    Title = "Edit Batch"
                };
                
                // Pre-fill the dialog with batch data
                editDialog.txtBatchNumber.Text = batch.BatchNumber;
                editDialog.txtQuantity.Text = batch.Quantity.ToString();
                editDialog.txtPurchasePrice.Text = batch.PurchasePrice.ToString();
                editDialog.txtSellingPrice.Text = batch.SellingPrice.ToString();
                editDialog.dpManufactureDate.SelectedDate = batch.ManufactureDate;
                editDialog.dpExpiryDate.SelectedDate = batch.ExpiryDate;
                editDialog.txtLocation.Text = batch.Location;
                
                if (editDialog.ShowDialog() == true)
                {
                    // Update the batch with new values
                    _dbService.UpdateBatch(batch.Id, editDialog.BatchStock);
                    System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Updated batch: {batch.BatchNumber}, New Quantity: {editDialog.BatchStock.Quantity}");
                    
                    // Refresh the batch list and statistics
                    _viewModel.LoadBatches();
                    UpdateBatchStatistics();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error editing batch: {ex.Message}");
                MessageBox.Show($"Error editing batch: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void AddStock_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = (Button)sender;
                var batch = (BatchStock)button.DataContext;
                
                var dialog = new InputDialog("Add Stock", "Enter quantity to add:")
                {
                    Owner = this
                };
                
                if (dialog.ShowDialog() == true && decimal.TryParse(dialog.Value, out decimal quantity))
                {
                    _dbService.AddStockToBatch(batch.Id, quantity);
                    System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Added {quantity} to batch {batch.BatchNumber}");

                    // Refresh the batch list and statistics
                    _viewModel.LoadBatches();
                    UpdateBatchStatistics();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error adding stock to batch: {ex.Message}");
                MessageBox.Show($"Error adding stock to batch: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 