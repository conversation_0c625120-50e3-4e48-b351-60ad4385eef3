-- Check if BatchStock table exists and create if not
CREATE TABLE IF NOT EXISTS "BatchStock" (
    "Id" INTEGER NOT NULL,
    "ProductId" INTEGER NOT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Quantity" DECIMAL(18,3) NOT NULL DEFAULT 0,
    "ManufactureDate" TEXT NOT NULL,
    "ExpiryDate" TEXT,
    "PurchasePrice" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "SellingPrice" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "Location" TEXT,
    "Notes" TEXT,
    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "PK_BatchStock" PRIMARY KEY("Id" AUTOINCREMENT),
    CONSTRAINT "FK_BatchStock_Products_ProductId" FOREIGN KEY("ProductId")
        REFERENCES "Products"("Id") ON DELETE CASCADE
);

-- Verify indexes
CREATE INDEX IF NOT EXISTS "IX_BatchStock_ProductId" ON "BatchStock"("ProductId"); 