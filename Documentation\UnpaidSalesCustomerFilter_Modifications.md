# Unpaid Sales Customer Filter Modifications

## Overview
Modified the `UnpaidSalesStatsDetailsDialog` to support filtering unpaid invoices by a specific customer while maintaining backward compatibility with the existing functionality that shows all unpaid invoices.

## Files Modified

### 1. Views/Dialogs/UnpaidSalesStatsDetailsDialog.xaml.cs
**Changes:**
- Added new constructor that accepts a `Customer` parameter for filtering
- Maintains existing constructor for backward compatibility
- Added `using POSSystem.Models;` for Customer model access

**New Constructor:**
```csharp
public UnpaidSalesStatsDetailsDialog(RefactoredDashboardViewModel dashboardViewModel, Customer customer)
```

### 2. ViewModels/Dashboard/UnpaidSalesStatsDetailsViewModel.cs
**Changes:**
- Added `_filterCustomer` field to store the customer filter
- Modified constructor to accept optional `Customer filterCustomer` parameter
- Updated title and subtitle to show customer name when filtering
- Added customer filtering logic in `LoadDataAsync()` method
- Added new properties for customer information display

**New Properties:**
- `IsCustomerFiltered`: Boolean indicating if filtering by customer
- `CustomerName`: Display name of the filtered customer
- `CustomerInfo`: Additional customer information (phone, email)

**Filtering Logic:**
```csharp
var unpaidSales = _filterCustomer != null 
    ? allUnpaidSales.Where(s => s.CustomerId == _filterCustomer.Id).ToList()
    : allUnpaidSales;
```

### 3. Views/Dialogs/UnpaidSalesStatsDetailsDialog.xaml
**Changes:**
- Added customer information card that displays when filtering by customer
- Card shows customer name, phone, and email
- Card is only visible when `IsCustomerFiltered` is true
- Reorganized filters section to accommodate customer info

**New UI Elements:**
- Customer info card with icon and customer details
- Conditional visibility based on customer filtering
- Improved layout structure

## Usage Examples

### Show All Unpaid Sales (Original Behavior)
```csharp
var dialog = new UnpaidSalesStatsDetailsDialog(dashboardViewModel);
await DialogHost.Show(dialog, "RootDialog");
```

### Show Unpaid Sales for Specific Customer
```csharp
var dialog = new UnpaidSalesStatsDetailsDialog(dashboardViewModel, customer);
await DialogHost.Show(dialog, "RootDialog");
```

### Using Extension Methods (from example file)
```csharp
// Show for specific customer
await dashboardViewModel.ShowUnpaidSalesForCustomerAsync(customer);

// Show all unpaid sales
await dashboardViewModel.ShowAllUnpaidSalesAsync();
```

## Integration Points

### Customer Selection
The application already has customer selection mechanisms:
- `CustomerSelectionWindow` - Standalone window for customer selection
- Customer selection dialogs in sales views
- Customer context menus

### Potential Integration Locations
1. **Customer List Context Menu**: Add "View Unpaid Sales" option
2. **Customer Details View**: Add button to view unpaid sales
3. **Dashboard**: Add customer-specific unpaid sales widget
4. **Reports Menu**: Add customer unpaid sales report option

## Features Preserved

### Existing Functionality
- All original charts and metrics calculations
- Time period filtering
- Export capabilities
- Sale detail viewing
- Loading states and error handling

### UI Design
- Material Design styling maintained
- Responsive layout preserved
- Accessibility features intact
- Consistent with application theme

## Benefits

### For Users
- **Focused View**: See unpaid invoices for specific customers
- **Better Customer Management**: Quickly identify customer payment issues
- **Contextual Information**: Customer details displayed prominently
- **Seamless Integration**: Works with existing customer selection flows

### For Developers
- **Backward Compatibility**: Existing code continues to work
- **Clean Architecture**: Optional parameter pattern
- **Reusable Components**: Leverages existing customer selection UI
- **Extensible Design**: Easy to add more customer-specific features

## Testing Recommendations

1. **Functional Testing**
   - Test with customer filter (should show only that customer's unpaid sales)
   - Test without customer filter (should show all unpaid sales)
   - Test with customer who has no unpaid sales
   - Test customer info display accuracy

2. **UI Testing**
   - Verify customer card visibility logic
   - Test responsive layout with customer info
   - Validate Material Design consistency
   - Check accessibility features

3. **Integration Testing**
   - Test customer selection flow
   - Verify dialog opening from different contexts
   - Test error handling scenarios

## Future Enhancements

1. **Customer Payment History**: Add payment history section
2. **Quick Payment**: Allow payment processing directly from dialog
3. **Customer Communication**: Add email/SMS reminder features
4. **Bulk Operations**: Select multiple invoices for batch processing
5. **Customer Analytics**: Add customer-specific payment behavior insights
