using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using POSSystem.Services.Caching;
using POSSystem.Services.QueryOptimization;

namespace POSSystem.Services.BackgroundLoading
{
    /// <summary>
    /// Background service that preloads frequently accessed data to improve UI responsiveness
    /// Runs on a separate thread to avoid blocking the UI
    /// </summary>
    public class BackgroundDataLoader : BackgroundService
    {
        private readonly ICacheService _cache;
        private readonly OptimizedQueryService _queryService;
        private readonly ILogger<BackgroundDataLoader> _logger;
        private readonly Timer _refreshTimer;

        // Configuration
        private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(10); // Refresh every 10 minutes
        private readonly TimeSpan _startupDelay = TimeSpan.FromSeconds(30); // Wait 30 seconds after startup

        public BackgroundDataLoader(
            ICacheService cache,
            OptimizedQueryService queryService,
            ILogger<BackgroundDataLoader> logger)
        {
            _cache = cache;
            _queryService = queryService;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger?.LogInformation("Background data loader started");

            // Wait for startup delay
            await Task.Delay(_startupDelay, stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await LoadCriticalDataAsync(stoppingToken);
                    await Task.Delay(_refreshInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error in background data loading cycle");
                    // Wait a bit before retrying
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }

            _logger?.LogInformation("Background data loader stopped");
        }

        /// <summary>
        /// Load critical data that's frequently accessed by the UI
        /// </summary>
        private async Task LoadCriticalDataAsync(CancellationToken cancellationToken)
        {
            _logger?.LogDebug("Starting background data loading cycle");

            var tasks = new List<Task>
            {
                LoadDashboardDataAsync(cancellationToken),
                LoadReferenceDataAsync(cancellationToken),
                LoadProductStatisticsAsync(cancellationToken),
                LoadLowStockAlertsAsync(cancellationToken)
            };

            await Task.WhenAll(tasks);

            _logger?.LogDebug("Background data loading cycle completed");
        }

        /// <summary>
        /// Preload dashboard data for today
        /// </summary>
        private async Task LoadDashboardDataAsync(CancellationToken cancellationToken)
        {
            try
            {
                var today = DateTime.Today;
                var endOfDay = today.AddDays(1).AddTicks(-1);

                // Check if data is already cached
                var cacheKey = $"dashboard:data:{today:yyyy-MM-dd}";
                var cached = await _cache.GetAsync<DashboardData>(cacheKey);

                if (cached == null)
                {
                    _logger?.LogDebug("Loading dashboard data for {Date}", today);
                    var dashboardData = await _queryService.GetDashboardDataAsync(today, endOfDay);
                    
                    // Cache for 5 minutes (dashboard data changes frequently)
                    await _cache.SetAsync(cacheKey, dashboardData, TimeSpan.FromMinutes(5));
                    
                    _logger?.LogDebug("Dashboard data loaded and cached");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load dashboard data in background");
            }
        }

        /// <summary>
        /// Preload reference data (categories, units, etc.)
        /// </summary>
        private async Task LoadReferenceDataAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Check if reference data is already cached
                var categoriesCached = await _cache.GetFromLevel<object>(CacheLevel.Reference, "categories");
                var unitsCached = await _cache.GetFromLevel<object>(CacheLevel.Reference, "units");

                if (categoriesCached == null)
                {
                    _logger?.LogDebug("Loading categories reference data");
                    // This would load categories - implementation depends on your repository
                    // For now, we'll just mark it as loaded
                    await _cache.SetToLevel(CacheLevel.Reference, "categories", new { loaded = true });
                }

                if (unitsCached == null)
                {
                    _logger?.LogDebug("Loading units reference data");
                    // This would load units - implementation depends on your repository
                    await _cache.SetToLevel(CacheLevel.Reference, "units", new { loaded = true });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load reference data in background");
            }
        }

        /// <summary>
        /// Preload product statistics
        /// </summary>
        private async Task LoadProductStatisticsAsync(CancellationToken cancellationToken)
        {
            try
            {
                var cacheKey = "products:statistics";
                var cached = await _cache.GetAsync<ProductStatisticsSummary>(cacheKey);

                if (cached == null)
                {
                    _logger?.LogDebug("Loading product statistics");
                    var stats = await _queryService.GetProductStatisticsAsync();
                    
                    // Cache for 10 minutes
                    await _cache.SetAsync(cacheKey, stats, TimeSpan.FromMinutes(10));
                    
                    _logger?.LogDebug("Product statistics loaded and cached");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load product statistics in background");
            }
        }

        /// <summary>
        /// Preload low stock alerts
        /// </summary>
        private async Task LoadLowStockAlertsAsync(CancellationToken cancellationToken)
        {
            try
            {
                var cacheKey = "products:lowstock:alerts";
                var cached = await _cache.GetAsync<IEnumerable<LowStockAlert>>(cacheKey);

                if (cached == null)
                {
                    _logger?.LogDebug("Loading low stock alerts");
                    var alerts = await _queryService.GetLowStockProductsMinimalAsync();
                    
                    // Cache for 5 minutes (inventory changes frequently)
                    await _cache.SetAsync(cacheKey, alerts, TimeSpan.FromMinutes(5));
                    
                    _logger?.LogDebug("Low stock alerts loaded and cached");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load low stock alerts in background");
            }
        }

        /// <summary>
        /// Force refresh of all cached data
        /// </summary>
        public async Task ForceRefreshAsync()
        {
            _logger?.LogInformation("Force refresh requested");
            
            try
            {
                // Clear relevant cache entries
                await _cache.RemoveAsync("dashboard:data:" + DateTime.Today.ToString("yyyy-MM-dd"));
                await _cache.RemoveAsync("products:statistics");
                await _cache.RemoveAsync("products:lowstock:alerts");

                // Reload data
                await LoadCriticalDataAsync(CancellationToken.None);
                
                _logger?.LogInformation("Force refresh completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during force refresh");
                throw;
            }
        }

        /// <summary>
        /// Get cache status for monitoring
        /// </summary>
        public async Task<BackgroundLoaderStatus> GetStatusAsync()
        {
            try
            {
                var cacheInfo = await _cache.GetCacheInfoAsync();
                
                return new BackgroundLoaderStatus
                {
                    IsRunning = true,
                    LastRefresh = DateTime.Now, // This would be tracked in a real implementation
                    NextRefresh = DateTime.Now.Add(_refreshInterval),
                    CacheHitRatio = (double)(cacheInfo.ContainsKey("HitRatio") ? cacheInfo["HitRatio"] : 0),
                    TotalCachedItems = (int)(cacheInfo.ContainsKey("TotalKeys") ? cacheInfo["TotalKeys"] : 0)
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting background loader status");
                return new BackgroundLoaderStatus
                {
                    IsRunning = false,
                    Error = ex.Message
                };
            }
        }

        public override void Dispose()
        {
            _refreshTimer?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// Status information for the background data loader
    /// </summary>
    public class BackgroundLoaderStatus
    {
        public bool IsRunning { get; set; }
        public DateTime LastRefresh { get; set; }
        public DateTime NextRefresh { get; set; }
        public double CacheHitRatio { get; set; }
        public int TotalCachedItems { get; set; }
        public string Error { get; set; }
    }
}
