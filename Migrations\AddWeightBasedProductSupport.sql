-- Migration: Add Weight-Based Product Support
-- Description: Adds support for weight-based products with decimal quantities
-- Date: 2025-07-16

-- =====================================================
-- Step 1: Add IsWeightBased column to Products table
-- =====================================================

-- Check if the column already exists before adding it
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
BEGIN
    ALTER TABLE Products 
    ADD IsWeightBased BIT NOT NULL DEFAULT 0;
    
    PRINT 'Added IsWeightBased column to Products table';
END
ELSE
BEGIN
    PRINT 'IsWeightBased column already exists in Products table';
END

-- =====================================================
-- Step 2: Update SaleItems table to support decimal quantities
-- =====================================================

-- Check if the Quantity column is already decimal
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity' 
           AND DATA_TYPE = 'int')
BEGIN
    -- Backup existing data before modification
    PRINT 'Updating SaleItems.Quantity from int to decimal(18,3)';
    
    -- Update the column type to decimal with 3 decimal places
    ALTER TABLE SaleItems 
    ALTER COLUMN Quantity DECIMAL(18,3) NOT NULL;
    
    PRINT 'Successfully updated SaleItems.Quantity to decimal(18,3)';
END
ELSE
BEGIN
    PRINT 'SaleItems.Quantity is already decimal or does not exist';
END

-- =====================================================
-- Step 3: Create indexes for performance
-- =====================================================

-- Index for weight-based product queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Products_IsWeightBased')
BEGIN
    CREATE INDEX IX_Products_IsWeightBased ON Products (IsWeightBased);
    PRINT 'Created index IX_Products_IsWeightBased';
END

-- =====================================================
-- Step 4: Update existing data (optional)
-- =====================================================

-- Set default values for existing products
-- All existing products will be unit-based by default
UPDATE Products 
SET IsWeightBased = 0 
WHERE IsWeightBased IS NULL;

PRINT 'Set all existing products to unit-based (IsWeightBased = 0)';

-- =====================================================
-- Step 5: Add sample weight-based products (optional)
-- =====================================================

-- You can uncomment and modify this section to add sample weight-based products
/*
-- Example: Add some weight-based products
INSERT INTO Products (Name, SKU, Description, PurchasePrice, SellingPrice, CategoryId, UnitOfMeasureId, IsWeightBased, StockQuantity, MinimumStock, IsActive, CreatedAt, UpdatedAt, Type)
VALUES 
    ('Apples', 'APPLE-001', 'Fresh red apples sold by weight', 2.50, 4.99, 1, 2, 1, 1000, 50, 1, GETDATE(), GETDATE(), 0),
    ('Ground Beef', 'BEEF-001', 'Fresh ground beef sold by weight', 8.99, 12.99, 1, 2, 1, 500, 25, 1, GETDATE(), GETDATE(), 0),
    ('Rice', 'RICE-001', 'Premium basmati rice sold by weight', 3.99, 6.99, 1, 2, 1, 2000, 100, 1, GETDATE(), GETDATE(), 0);

PRINT 'Added sample weight-based products';
*/

-- =====================================================
-- Step 6: Verification queries
-- =====================================================

-- Verify the changes
SELECT 
    'Products with IsWeightBased column' as TableInfo,
    COUNT(*) as TotalProducts,
    SUM(CASE WHEN IsWeightBased = 1 THEN 1 ELSE 0 END) as WeightBasedProducts,
    SUM(CASE WHEN IsWeightBased = 0 THEN 1 ELSE 0 END) as UnitBasedProducts
FROM Products;

-- Check SaleItems column type
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';

PRINT 'Migration completed successfully!';
PRINT 'Weight-based product support has been added to the database.';
