using POSSystem.Models;
using System;
using System.Linq;
using System.Collections.Generic;

namespace POSSystem.Services.Customers
{
    public class CustomerService
    {
        private readonly DatabaseService _dbService;

        public CustomerService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public Customer GetCustomerByCode(string code)
        {
            return _dbService.GetAllCustomers().FirstOrDefault(c => c.LoyaltyCode == code);
        }

        public decimal CalculateLoyaltyPoints(decimal purchaseAmount, Customer customer, List<SaleItem> items)
        {
            var loyaltyProgram = _dbService.GetActiveLoyaltyProgram();
            if (loyaltyProgram == null || customer == null) return 0;

            var customerTier = loyaltyProgram.Tiers
                .OrderByDescending(t => t.MinimumPoints)
                .FirstOrDefault(t => customer.LoyaltyPoints >= t.MinimumPoints);

            decimal pointMultiplier = customerTier?.PointsMultiplier ?? 1;

            // Calculate points based on purchase amount (using points per dollar)
            return Math.Floor(purchaseAmount * loyaltyProgram.PointsPerDollar * pointMultiplier);
        }

        public void AddLoyaltyTransaction(Customer customer, decimal points, string description)
        {
            if (customer == null || customer.Id <= 0)
            {
                throw new ArgumentException("Invalid customer. Cannot add loyalty points.");
            }

            var transaction = new LoyaltyTransaction
            {
                CustomerId = customer.Id,
                Points = points,
                TransactionDate = DateTime.Now,
                Description = description
            };

            // Save the loyalty transaction
            _dbService.SaveLoyaltyTransaction(transaction);
            
            // Update customer's total points
            customer.LoyaltyPoints += points;
            _dbService.UpdateCustomer(customer);

            // Update customer's tier if needed
            var loyaltyProgram = _dbService.GetActiveLoyaltyProgram();
            if (loyaltyProgram != null)
            {
                var newTier = loyaltyProgram.Tiers
                    .OrderByDescending(t => t.MinimumPoints)
                    .FirstOrDefault(t => customer.LoyaltyPoints >= t.MinimumPoints);

                if (newTier != null && (customer.LoyaltyTier == null || customer.LoyaltyTier.Id != newTier.Id))
                {
                    customer.LoyaltyTier = newTier;
                    _dbService.UpdateCustomer(customer);
                }
            }
        }

        public string GenerateNewLoyaltyCode()
        {
            Random random = new Random();
            string code;
            do
            {
                code = random.Next(10000000, 99999999).ToString();
            } while (_dbService.GetAllCustomers().Any(c => c.LoyaltyCode == code));

            return code;
        }
    }
} 