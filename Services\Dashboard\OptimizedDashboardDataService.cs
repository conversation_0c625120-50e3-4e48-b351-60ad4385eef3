using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models.Dashboard;
using POSSystem.Services;

namespace POSSystem.Services.Dashboard
{
    /// <summary>
    /// Optimized dashboard data service that provides lightweight data loading
    /// specifically designed for dashboard performance
    /// </summary>
    public class OptimizedDashboardDataService
    {
        private readonly DatabaseService _databaseService;
        private readonly Dictionary<string, object> _cache = new();
        private readonly Dictionary<string, DateTime> _cacheExpiry = new();

        public OptimizedDashboardDataService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// Get lightweight sales data for dashboard charts
        /// Uses optimized query without expensive joins
        /// </summary>
        public async Task<List<DashboardSaleData>> GetLightweightSalesDataAsync(DateTime startDate, DateTime endDate)
        {
            var cacheKey = $"lightweight_sales_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Check cache first
            if (TryGetFromCache<List<DashboardSaleData>>(cacheKey, out var cachedData))
            {
                Debug.WriteLine($"OptimizedDashboardDataService: Returning {cachedData.Count} sales from cache");
                return cachedData;
            }

            // Load from database using optimized method
            var stopwatch = Stopwatch.StartNew();
            var salesData = await _databaseService.GetDashboardSalesDataAsync(startDate, endDate);
            stopwatch.Stop();

            Debug.WriteLine($"OptimizedDashboardDataService: Loaded {salesData.Count} lightweight sales in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            var cacheDuration = GetCacheDuration(endDate);
            AddToCache(cacheKey, salesData, cacheDuration);

            return salesData;
        }

        /// <summary>
        /// Get pre-aggregated dashboard metrics
        /// Much faster than loading full sales and calculating on UI thread
        /// </summary>
        public async Task<DashboardMetrics> GetDashboardMetricsAsync(DateTime startDate, DateTime endDate)
        {
            var cacheKey = $"dashboard_metrics_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Check cache first
            if (TryGetFromCache<DashboardMetrics>(cacheKey, out var cachedMetrics))
            {
                Debug.WriteLine("OptimizedDashboardDataService: Returning metrics from cache");
                return cachedMetrics;
            }

            // Load from database using optimized aggregation
            var stopwatch = Stopwatch.StartNew();
            var metrics = await _databaseService.GetDashboardMetricsAsync(startDate, endDate);
            stopwatch.Stop();

            Debug.WriteLine($"OptimizedDashboardDataService: Loaded metrics in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            var cacheDuration = GetCacheDuration(endDate);
            AddToCache(cacheKey, metrics, cacheDuration);

            return metrics;
        }

        /// <summary>
        /// Get chart data optimized for dashboard display
        /// Pre-aggregates data to reduce UI thread work
        /// </summary>
        public async Task<DashboardChartData> GetChartDataAsync(DateTime startDate, DateTime endDate, string chartType = "sales")
        {
            var cacheKey = $"chart_data_{chartType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            
            // Check cache first
            if (TryGetFromCache<DashboardChartData>(cacheKey, out var cachedChartData))
            {
                Debug.WriteLine($"OptimizedDashboardDataService: Returning {chartType} chart data from cache");
                return cachedChartData;
            }

            var stopwatch = Stopwatch.StartNew();
            
            // Get lightweight sales data
            var salesData = await GetLightweightSalesDataAsync(startDate, endDate);
            
            // Aggregate data based on date range
            var chartData = AggregateChartData(salesData, startDate, endDate, chartType);
            
            stopwatch.Stop();
            Debug.WriteLine($"OptimizedDashboardDataService: Generated {chartType} chart data with {chartData.DataPoints.Count} points in {stopwatch.ElapsedMilliseconds}ms");

            // Cache the result
            var cacheDuration = GetCacheDuration(endDate);
            AddToCache(cacheKey, chartData, cacheDuration);

            return chartData;
        }

        /// <summary>
        /// Aggregate sales data into chart-friendly format
        /// </summary>
        private DashboardChartData AggregateChartData(List<DashboardSaleData> salesData, DateTime startDate, DateTime endDate, string chartType)
        {
            var dateSpan = (endDate - startDate).TotalDays;
            var dataPoints = new List<ChartDataPoint>();
            var labels = new List<string>();

            if (!salesData.Any())
            {
                return new DashboardChartData
                {
                    ChartTitle = $"No {chartType} data available",
                    Period = $"{startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
                };
            }

            // Determine aggregation level based on date span
            if (dateSpan <= 1) // Hourly for today
            {
                var hourlyData = salesData
                    .GroupBy(s => s.SaleDate.Hour)
                    .Select(g => new ChartDataPoint
                    {
                        Date = startDate.Date.AddHours(g.Key),
                        Value = g.Sum(s => s.GrandTotal),
                        Label = $"{g.Key:D2}:00"
                    })
                    .OrderBy(p => p.Date)
                    .ToList();

                dataPoints = hourlyData;
            }
            else if (dateSpan <= 31) // Daily for up to a month
            {
                var dailyData = salesData
                    .GroupBy(s => s.SaleDate.Date)
                    .Select(g => new ChartDataPoint
                    {
                        Date = g.Key,
                        Value = g.Sum(s => s.GrandTotal),
                        Label = g.Key.ToString("MM/dd")
                    })
                    .OrderBy(p => p.Date)
                    .ToList();

                dataPoints = dailyData;
            }
            else // Monthly for longer periods
            {
                var monthlyData = salesData
                    .GroupBy(s => new { s.SaleDate.Year, s.SaleDate.Month })
                    .Select(g => new ChartDataPoint
                    {
                        Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Value = g.Sum(s => s.GrandTotal),
                        Label = new DateTime(g.Key.Year, g.Key.Month, 1).ToString("MM/yyyy")
                    })
                    .OrderBy(p => p.Date)
                    .ToList();

                dataPoints = monthlyData;
            }

            return new DashboardChartData
            {
                DataPoints = dataPoints,
                Labels = dataPoints.Select(p => p.Label).ToArray(),
                Values = dataPoints.Select(p => p.Value).ToArray(),
                ChartTitle = $"{chartType.ToUpper()} Trend",
                Period = $"{startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            };
        }

        /// <summary>
        /// Clear expired cache entries
        /// </summary>
        public void ClearExpiredCache()
        {
            var now = DateTime.Now;
            var expiredKeys = _cacheExpiry.Where(kvp => kvp.Value < now).Select(kvp => kvp.Key).ToList();
            
            foreach (var key in expiredKeys)
            {
                _cache.Remove(key);
                _cacheExpiry.Remove(key);
            }

            if (expiredKeys.Any())
            {
                Debug.WriteLine($"OptimizedDashboardDataService: Cleared {expiredKeys.Count} expired cache entries");
            }
        }

        private bool TryGetFromCache<T>(string key, out T value)
        {
            value = default(T);
            
            if (!_cache.ContainsKey(key) || !_cacheExpiry.ContainsKey(key))
                return false;

            if (_cacheExpiry[key] < DateTime.Now)
            {
                _cache.Remove(key);
                _cacheExpiry.Remove(key);
                return false;
            }

            if (_cache[key] is T cachedValue)
            {
                value = cachedValue;
                return true;
            }

            return false;
        }

        private void AddToCache<T>(string key, T value, int durationMinutes)
        {
            _cache[key] = value;
            _cacheExpiry[key] = DateTime.Now.AddMinutes(durationMinutes);
        }

        private int GetCacheDuration(DateTime endDate)
        {
            // Recent data expires faster than historical data
            if (endDate.Date >= DateTime.Today)
                return 5; // 5 minutes for today's data
            else if (endDate.Date >= DateTime.Today.AddDays(-7))
                return 30; // 30 minutes for this week's data
            else
                return 120; // 2 hours for older data
        }
    }
}
