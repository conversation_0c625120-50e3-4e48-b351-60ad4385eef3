using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class EnumToResourceConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return null;

            // Get the enum value as string
            string resourceKey = value.ToString();

            // Try to find the resource
            var resource = Application.Current.TryFindResource(resourceKey);
            
            // Return the localized string if found, otherwise return the enum value
            return resource ?? resourceKey;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 