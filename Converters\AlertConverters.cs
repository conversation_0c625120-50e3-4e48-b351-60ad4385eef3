using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    public class AlertTypeToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string alertType = value?.ToString();
            return alertType switch
            {
                "Warning" => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                "Error" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                "OutOfStock" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                "LowStock" => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                "Expiry" => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                "OverdueSale" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                "OverduePurchase" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                _ => new SolidColorBrush(Color.FromRgb(33, 150, 243))
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class AlertTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string alertType = value?.ToString();
            return alertType switch
            {
                "Warning" => PackIconKind.Warning,
                "Error" => PackIconKind.Error,
                "OutOfStock" => PackIconKind.CartOff,
                "LowStock" => PackIconKind.CartRemove,
                "Expiry" => PackIconKind.Calendar,
                "OverdueSale" => PackIconKind.CurrencyUsd,
                "OverduePurchase" => PackIconKind.Store,
                _ => PackIconKind.Information
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 