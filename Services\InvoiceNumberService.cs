using System;
using System.Diagnostics;
using Microsoft.Data.Sqlite;
using System.IO;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    /// <summary>
    /// Service to handle invoice number generation without using INSTR function
    /// </summary>
    public class InvoiceNumberService : IInvoiceNumberService
    {
        /// <summary>
        /// Gets the database connection string
        /// </summary>
        private string GetConnectionString()
        {
            try
            {
                // Simple approach: Use the default database path
                // This matches what DatabaseService.GetDatabasePath() does
                var settingsService = new SettingsService();
                string dbPath = settingsService.GetSetting("DatabaseLocation");
                
                if (string.IsNullOrEmpty(dbPath))
                {
                    // Use default location in app directory
                    dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
                }
                
                // Remove any connection string prefix if it was accidentally stored that way
                if (dbPath.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase))
                {
                    dbPath = dbPath.Substring("Data Source=".Length);
                }
                
                // Ensure the directory exists
                string dbDirectory = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(dbDirectory) && !Directory.Exists(dbDirectory))
                {
                    Directory.CreateDirectory(dbDirectory);
                }
                
                Debug.WriteLine($"InvoiceNumberService using database path: {dbPath}");
                return $"Data Source={dbPath}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting connection string: {ex.Message}");
                // Last resort fallback
                return $"Data Source={Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db")}";
            }
        }
        
        /// <summary>
        /// Gets the next invoice number without using the INSTR function
        /// </summary>
        public string GetNextInvoiceNumber()
        {
            return GetNextInvoiceNumberInternal().ToString();
        }

        public string GenerateInvoiceNumber(DateTime date)
        {
            return $"INV-{date:yyyyMMdd}-{GetNextInvoiceNumberInternal()}";
        }

        public void ResetInvoiceCounter()
        {
            // Implementation for resetting counter if needed
            // This could reset a counter in the database
        }

        private int GetNextInvoiceNumberInternal()
        {
            string connectionString = GetConnectionString();
            using (var connection = new SqliteConnection(connectionString))
            {
                connection.Open();
                
                // Get today's date formatted as YYYYMMDD
                string today = DateTime.Now.ToString("yyyyMMdd");
                string prefix = $"INV-{today}-%";
                
                try
                {
                    // Use a simpler query that doesn't rely on INSTR function
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT InvoiceNumber 
                            FROM Invoice 
                            WHERE InvoiceNumber LIKE @prefix
                            ORDER BY Id DESC
                            LIMIT 1";
                        
                        command.Parameters.AddWithValue("@prefix", prefix);
                        
                        var result = command.ExecuteScalar();
                        
                        // No invoices for today yet
                        if (result == null || result == DBNull.Value)
                        {
                            return 1;
                        }
                        
                        // Parse the invoice number
                        string invoiceNumber = result.ToString();
                        string[] parts = invoiceNumber.Split('-');
                        
                        // Should have format INV-YYYYMMDD-NUMBER
                        if (parts.Length >= 3 && int.TryParse(parts[2], out int currentNumber))
                        {
                            return currentNumber + 1;
                        }
                        
                        // Fallback to 1 if parsing fails
                        return 1;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error getting next invoice number: {ex.Message}");
                    
                    // Alternative approach using COUNT
                    try
                    {
                        using (var command = connection.CreateCommand())
                        {
                            command.CommandText = "SELECT COUNT(*) + 1 FROM Invoice WHERE InvoiceNumber LIKE @prefix";
                            command.Parameters.AddWithValue("@prefix", prefix);
                            
                            var count = command.ExecuteScalar();
                            return Convert.ToInt32(count);
                        }
                    }
                    catch (Exception fallbackEx)
                    {
                        Debug.WriteLine($"Error in fallback invoice number method: {fallbackEx.Message}");
                        // Final fallback to a random number to avoid blocking the sale
                        return new Random().Next(1000, 9999);
                    }
                }
            }
        }
    }
}
