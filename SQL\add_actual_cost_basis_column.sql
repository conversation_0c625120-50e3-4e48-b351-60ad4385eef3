-- Add ActualCostBasis column to SaleItems table for FIFO cost tracking
-- This column stores the actual cost basis used for each sale item based on FIFO allocation

-- Check if the column already exists before adding it
PRAGMA table_info(SaleItems);

-- Add the ActualCostBasis column if it doesn't exist
-- SQLite doesn't support IF NOT EXISTS for ALTER TABLE, so we'll use a more complex approach

-- First, create a temporary table with the new structure
CREATE TABLE IF NOT EXISTS SaleItems_temp (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    Total DECIMAL(18,2) NOT NULL,
    ActualCostBasis DECIMAL(18,2) NOT NULL DEFAULT 0,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Copy existing data to the temporary table
INSERT INTO SaleItems_temp (Id, SaleId, ProductId, Quantity, UnitPrice, Total, ActualCostBasis)
SELECT Id, SaleId, ProductId, Quantity, UnitPrice, Total, 0 as ActualCostBasis
FROM SaleItems;

-- Drop the original table
DROP TABLE SaleItems;

-- Rename the temporary table to the original name
ALTER TABLE SaleItems_temp RENAME TO SaleItems;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS IX_SaleItems_SaleId ON SaleItems(SaleId);
CREATE INDEX IF NOT EXISTS IX_SaleItems_ProductId ON SaleItems(ProductId);

-- Update existing records to populate ActualCostBasis with product purchase prices
-- This provides a reasonable fallback for existing data
UPDATE SaleItems 
SET ActualCostBasis = (
    SELECT COALESCE(p.PurchasePrice, 0)
    FROM Products p 
    WHERE p.Id = SaleItems.ProductId
)
WHERE ActualCostBasis = 0;
