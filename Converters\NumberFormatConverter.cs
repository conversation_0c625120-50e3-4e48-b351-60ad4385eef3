using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class NumberFormatConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return string.Empty;

            // Create a number format that always uses Hindu-Arabic numerals
            var numberFormat = (NumberFormatInfo)culture.NumberFormat.Clone();
            numberFormat.DigitSubstitution = DigitShapes.None;
            numberFormat.NativeDigits = new string[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };

            string format = parameter as string ?? "N0";
            
            if (value is decimal decimalValue)
                return decimalValue.ToString(format, numberFormat);
            if (value is double doubleValue)
                return doubleValue.ToString(format, numberFormat);
            if (value is int intValue)
                return intValue.ToString(format, numberFormat);
            if (value is long longValue)
                return longValue.ToString(format, numberFormat);

            return value.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 