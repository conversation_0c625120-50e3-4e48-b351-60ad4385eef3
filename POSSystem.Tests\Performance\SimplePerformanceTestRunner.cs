using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.DataAccess;
using POSSystem.Services.Monitoring;
using POSSystem.Services.Logging;
using POSSystem.Models;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Simple Performance Test Runner
    /// 
    /// A simplified version that bypasses the MockDatabaseService issues
    /// and focuses on running the core performance tests with real services.
    /// </summary>
    public class SimplePerformanceTestRunner
    {
        private readonly string _sessionId;
        private readonly string _logPath;
        private readonly IServiceProvider _serviceProvider;
        private readonly List<string> _testResults;

        public SimplePerformanceTestRunner()
        {
            _sessionId = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            _testResults = new List<string>();
            
            // Set up logging
            var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceTestLogs");
            Directory.CreateDirectory(logsDirectory);
            _logPath = Path.Combine(logsDirectory, $"simple_performance_test_{_sessionId}.log");

            // Configure services
            _serviceProvider = ConfigureServices();
            
            LogMessage("=".PadRight(80, '='));
            LogMessage($"SIMPLE PERFORMANCE TEST RUNNER STARTED - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            LogMessage($"Session ID: {_sessionId}");
            LogMessage($"Log Path: {_logPath}");
            LogMessage("=".PadRight(80, '='));
        }

        private IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add in-memory database
            services.AddDbContext<POSDbContext>(options =>
            {
                options.UseInMemoryDatabase(databaseName: $"PerformanceTest_{_sessionId}");
                options.EnableSensitiveDataLogging();
            });

            // Add core services
            services.AddScoped<DatabaseService>();
            services.AddScoped<IProductManagementService, ProductManagementService>();
            services.AddScoped<ISalesManagementService, SalesManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagementService>();
            services.AddScoped<IInventoryManagementService, InventoryManagementService>();
            services.AddScoped<UnifiedDataService>();

            // Add performance monitoring (if available)
            try
            {
                services.AddSingleton<PerformanceMonitoringService>();
                services.AddSingleton<IEnhancedLoggingService, EnhancedLoggingService>();
            }
            catch
            {
                // If these services aren't available, continue without them
                LogMessage("Warning: Performance monitoring services not available");
            }

            return services.BuildServiceProvider();
        }

        public async Task RunPerformanceTestsAsync()
        {
            try
            {
                LogMessage("Starting performance test execution...");

                // Set up test data
                await SetupTestDataAsync();

                // Run basic performance tests
                await RunProductLookupTestsAsync();
                await RunDatabasePerformanceTestsAsync();
                await RunMemoryUsageTestsAsync();

                // Generate summary
                GenerateTestSummary();

                LogMessage("Performance tests completed successfully!");
            }
            catch (Exception ex)
            {
                LogMessage($"Error during performance tests: {ex.Message}");
                LogMessage($"Stack trace: {ex.StackTrace}");
            }
        }

        private async Task SetupTestDataAsync()
        {
            LogMessage("Setting up test data...");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<POSDbContext>();

                // Clear existing data
                context.Database.EnsureDeleted();
                context.Database.EnsureCreated();

                // Add test categories
                var categories = new List<Category>
                {
                    new Category { Name = "Electronics", Description = "Electronic devices", IsActive = true },
                    new Category { Name = "Clothing", Description = "Apparel items", IsActive = true },
                    new Category { Name = "Food", Description = "Food items", IsActive = true }
                };
                context.Categories.AddRange(categories);
                await context.SaveChangesAsync();

                // Add test products
                var products = new List<Product>();
                for (int i = 1; i <= 1000; i++)
                {
                    products.Add(new Product
                    {
                        Name = $"Test Product {i:D4}",
                        SKU = $"TEST{i:D6}",
                        Description = $"Performance test product {i}",
                        SellingPrice = 10.00m + (i % 100),
                        PurchasePrice = 5.00m + (i % 50),
                        StockQuantity = 100 + (i % 200),
                        CategoryId = categories[i % categories.Count].Id,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    });
                }
                context.Products.AddRange(products);
                await context.SaveChangesAsync();

                // Add test customers
                var customers = new List<Customer>();
                for (int i = 1; i <= 500; i++)
                {
                    customers.Add(new Customer
                    {
                        Name = $"Test Customer {i:D4}",
                        Email = $"test{i}@example.com",
                        Phone = $"555-{i:D4}",
                        Address = $"{i} Test Street",
                        City = "Test City",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    });
                }
                context.Customers.AddRange(customers);
                await context.SaveChangesAsync();

                stopwatch.Stop();
                LogMessage($"Test data setup completed in {stopwatch.ElapsedMilliseconds}ms");
                LogMessage($"Created: {categories.Count} categories, {products.Count} products, {customers.Count} customers");
            }
            catch (Exception ex)
            {
                LogMessage($"Error setting up test data: {ex.Message}");
                throw;
            }
        }

        private async Task RunProductLookupTestsAsync()
        {
            LogMessage("Running product lookup performance tests...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var productService = scope.ServiceProvider.GetRequiredService<IProductManagementService>();

                // Test 1: Product lookup by ID
                var stopwatch = Stopwatch.StartNew();
                for (int i = 1; i <= 100; i++)
                {
                    var product = await productService.GetProductByIdAsync(i);
                }
                stopwatch.Stop();
                var avgLookupTime = stopwatch.ElapsedMilliseconds / 100.0;
                LogMessage($"Product lookup by ID: Average {avgLookupTime:F2}ms per lookup");
                _testResults.Add($"Product ID Lookup: {avgLookupTime:F2}ms avg");

                // Test 2: Product search
                stopwatch.Restart();
                var searchResults = await productService.SearchProductsAsync("Test", 1, 50);
                stopwatch.Stop();
                LogMessage($"Product search: {stopwatch.ElapsedMilliseconds}ms for {searchResults?.Count() ?? 0} results");
                _testResults.Add($"Product Search: {stopwatch.ElapsedMilliseconds}ms");

                // Test 3: Get all products (paginated)
                stopwatch.Restart();
                var allProducts = await productService.GetProductsAsync(1, 100);
                stopwatch.Stop();
                LogMessage($"Get products (paginated): {stopwatch.ElapsedMilliseconds}ms for {allProducts?.Count() ?? 0} products");
                _testResults.Add($"Product Pagination: {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                LogMessage($"Error in product lookup tests: {ex.Message}");
                _testResults.Add($"Product Lookup Tests: FAILED - {ex.Message}");
            }
        }

        private async Task RunDatabasePerformanceTestsAsync()
        {
            LogMessage("Running database performance tests...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<POSDbContext>();

                // Test 1: Simple query performance
                var stopwatch = Stopwatch.StartNew();
                var productCount = await context.Products.CountAsync();
                stopwatch.Stop();
                LogMessage($"Product count query: {stopwatch.ElapsedMilliseconds}ms (Count: {productCount})");
                _testResults.Add($"Count Query: {stopwatch.ElapsedMilliseconds}ms");

                // Test 2: Complex query with joins
                stopwatch.Restart();
                var productsWithCategories = await context.Products
                    .Include(p => p.Category)
                    .Take(100)
                    .ToListAsync();
                stopwatch.Stop();
                LogMessage($"Products with categories query: {stopwatch.ElapsedMilliseconds}ms for {productsWithCategories.Count} products");
                _testResults.Add($"Join Query: {stopwatch.ElapsedMilliseconds}ms");

                // Test 3: Bulk insert performance
                stopwatch.Restart();
                var testSales = new List<Sale>();
                for (int i = 1; i <= 100; i++)
                {
                    testSales.Add(new Sale
                    {
                        CustomerId = (i % 500) + 1,
                        SaleDate = DateTime.Now,
                        Status = "Completed",
                        PaymentStatus = "Paid",
                        Subtotal = 100.00m,
                        TaxAmount = 10.00m,
                        GrandTotal = 110.00m,
                        CreatedAt = DateTime.Now
                    });
                }
                context.Sales.AddRange(testSales);
                await context.SaveChangesAsync();
                stopwatch.Stop();
                LogMessage($"Bulk insert (100 sales): {stopwatch.ElapsedMilliseconds}ms");
                _testResults.Add($"Bulk Insert: {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                LogMessage($"Error in database performance tests: {ex.Message}");
                _testResults.Add($"Database Tests: FAILED - {ex.Message}");
            }
        }

        private async Task RunMemoryUsageTestsAsync()
        {
            LogMessage("Running memory usage tests...");

            try
            {
                // Force garbage collection before test
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var initialMemory = GC.GetTotalMemory(false);
                LogMessage($"Initial memory usage: {initialMemory / (1024 * 1024):F2} MB");

                // Perform memory-intensive operations
                using var scope = _serviceProvider.CreateScope();
                var productService = scope.ServiceProvider.GetRequiredService<IProductManagementService>();

                for (int i = 0; i < 10; i++)
                {
                    var products = await productService.GetProductsAsync(1, 100);
                    await Task.Delay(100); // Small delay
                }

                var finalMemory = GC.GetTotalMemory(false);
                var memoryUsed = (finalMemory - initialMemory) / (1024 * 1024);
                LogMessage($"Final memory usage: {finalMemory / (1024 * 1024):F2} MB");
                LogMessage($"Memory used during test: {memoryUsed:F2} MB");
                _testResults.Add($"Memory Usage: {memoryUsed:F2} MB");

                // Force cleanup
                GC.Collect();
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogMessage($"Error in memory usage tests: {ex.Message}");
                _testResults.Add($"Memory Tests: FAILED - {ex.Message}");
            }
        }

        private void GenerateTestSummary()
        {
            LogMessage("=".PadRight(80, '='));
            LogMessage("PERFORMANCE TEST SUMMARY");
            LogMessage("=".PadRight(80, '='));

            foreach (var result in _testResults)
            {
                LogMessage($"✓ {result}");
            }

            LogMessage("=".PadRight(80, '='));
            LogMessage($"Total tests completed: {_testResults.Count}");
            LogMessage($"Session completed at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            LogMessage($"Full log available at: {_logPath}");
            LogMessage("=".PadRight(80, '='));
        }

        private void LogMessage(string message)
        {
            var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} {message}";
            Console.WriteLine(logEntry);
            
            try
            {
                File.AppendAllText(_logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // If file logging fails, continue with console output
            }
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
