using Microsoft.Extensions.Logging;
using Moq;
using POSSystem.Models;
using POSSystem.Services.DataAccess;
using POSSystem.Services.Interfaces;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.UserManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.QueryOptimization;
using Xunit;
using FluentAssertions;

namespace POSSystem.Tests.Services.DataAccess
{
    public class UnifiedDataServiceTests
    {
        private readonly Mock<IProductManagementService> _mockProductService;
        private readonly Mock<ISalesManagementService> _mockSalesService;
        private readonly Mock<ICustomerManagementService> _mockCustomerService;
        private readonly Mock<IUserManagementService> _mockUserService;
        private readonly Mock<IInventoryManagementService> _mockInventoryService;
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<OptimizedQueryService> _mockOptimizedQueries;
        private readonly Mock<ILogger<UnifiedDataService>> _mockLogger;
        private readonly UnifiedDataService _unifiedDataService;

        public UnifiedDataServiceTests()
        {
            _mockProductService = new Mock<IProductManagementService>();
            _mockSalesService = new Mock<ISalesManagementService>();
            _mockCustomerService = new Mock<ICustomerManagementService>();
            _mockUserService = new Mock<IUserManagementService>();
            _mockInventoryService = new Mock<IInventoryManagementService>();
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockOptimizedQueries = new Mock<OptimizedQueryService>();
            _mockLogger = new Mock<ILogger<UnifiedDataService>>();

            _unifiedDataService = new UnifiedDataService(
                _mockProductService.Object,
                _mockSalesService.Object,
                _mockCustomerService.Object,
                _mockUserService.Object,
                _mockInventoryService.Object,
                _mockDatabaseService.Object,
                _mockOptimizedQueries.Object,
                _mockLogger.Object
            );
        }

        #region Product Tests

        [Fact]
        public async Task GetProductsAsync_ShouldUseOptimizedQueries_WhenAvailable()
        {
            // Arrange
            var expectedProducts = new List<Product>
            {
                new Product { Id = 1, Name = "Test Product 1", SKU = "TEST001" },
                new Product { Id = 2, Name = "Test Product 2", SKU = "TEST002" }
            };

            _mockOptimizedQueries.Setup(x => x.GetProductsWithCategoriesOptimizedAsync(
                It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(expectedProducts);

            // Act
            var result = await _unifiedDataService.GetProductsAsync(1, 50);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(expectedProducts);
            
            _mockOptimizedQueries.Verify(x => x.GetProductsWithCategoriesOptimizedAsync(
                null, null, 1, 50), Times.Once);
        }

        [Fact]
        public async Task GetProductsAsync_ShouldFallbackToManagementService_WhenOptimizedQueriesFail()
        {
            // Arrange
            var expectedProducts = new List<Product>
            {
                new Product { Id = 1, Name = "Test Product 1", SKU = "TEST001" }
            };

            _mockOptimizedQueries.Setup(x => x.GetProductsWithCategoriesOptimizedAsync(
                It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(new Exception("Optimized query failed"));

            _mockProductService.Setup(x => x.GetAllProductsAsync())
                .ReturnsAsync(expectedProducts);

            // Act
            var result = await _unifiedDataService.GetProductsAsync(1, 50);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            
            _mockProductService.Verify(x => x.GetAllProductsAsync(), Times.Once);
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldUseOptimizedQueries_WhenAvailable()
        {
            // Arrange
            var searchTerm = "test";
            var expectedProducts = new List<Product>
            {
                new Product { Id = 1, Name = "Test Product", SKU = "TEST001" }
            };

            _mockOptimizedQueries.Setup(x => x.GetProductsWithCategoriesOptimizedAsync(
                null, searchTerm, 1, 50))
                .ReturnsAsync(expectedProducts);

            // Act
            var result = await _unifiedDataService.SearchProductsAsync(searchTerm, 50);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Name.Should().Contain("Test");
            
            _mockOptimizedQueries.Verify(x => x.GetProductsWithCategoriesOptimizedAsync(
                null, searchTerm, 1, 50), Times.Once);
        }

        [Fact]
        public async Task AddProductAsync_ShouldReturnProductId_WhenSuccessful()
        {
            // Arrange
            var product = new Product { Name = "New Product", SKU = "NEW001", SellingPrice = 10.99m };
            var expectedId = 123;

            _mockProductService.Setup(x => x.AddProductAsync(product))
                .ReturnsAsync(expectedId);

            // Act
            var result = await _unifiedDataService.AddProductAsync(product);

            // Assert
            result.Should().Be(expectedId);
            _mockProductService.Verify(x => x.AddProductAsync(product), Times.Once);
        }

        [Fact]
        public async Task AddProductAsync_ShouldFallbackToDatabaseService_WhenManagementServiceFails()
        {
            // Arrange
            var product = new Product { Id = 123, Name = "New Product", SKU = "NEW001" };

            _mockProductService.Setup(x => x.AddProductAsync(product))
                .ThrowsAsync(new Exception("Management service failed"));

            _mockDatabaseService.Setup(x => x.AddProduct(product))
                .Verifiable();

            // Act
            var result = await _unifiedDataService.AddProductAsync(product);

            // Assert
            result.Should().Be(123); // Should return the product ID
            _mockDatabaseService.Verify(x => x.AddProduct(product), Times.Once);
        }

        #endregion

        #region Sales Tests

        [Fact]
        public async Task GetSalesAsync_ShouldUseOptimizedQueries_WhenAvailable()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-7);
            var endDate = DateTime.Today;
            var expectedSales = new List<Sale>
            {
                new Sale { Id = 1, SaleDate = DateTime.Today, GrandTotal = 100.00m },
                new Sale { Id = 2, SaleDate = DateTime.Today.AddDays(-1), GrandTotal = 150.00m }
            };

            _mockOptimizedQueries.Setup(x => x.GetSalesWithItemsOptimizedAsync(startDate, endDate, null))
                .ReturnsAsync(expectedSales);

            // Act
            var result = await _unifiedDataService.GetSalesAsync(startDate, endDate);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Sum(s => s.GrandTotal).Should().Be(250.00m);
            
            _mockOptimizedQueries.Verify(x => x.GetSalesWithItemsOptimizedAsync(startDate, endDate, null), Times.Once);
        }

        [Fact]
        public async Task AddSaleAsync_ShouldReturnSaleId_WhenSuccessful()
        {
            // Arrange
            var sale = new Sale 
            { 
                SaleDate = DateTime.Now, 
                GrandTotal = 99.99m,
                Items = new List<SaleItem>
                {
                    new SaleItem { ProductId = 1, Quantity = 2, UnitPrice = 49.995m }
                }
            };
            var expectedId = 456;

            _mockSalesService.Setup(x => x.AddSaleAsync(sale))
                .ReturnsAsync(expectedId);

            // Act
            var result = await _unifiedDataService.AddSaleAsync(sale);

            // Assert
            result.Should().Be(expectedId);
            _mockSalesService.Verify(x => x.AddSaleAsync(sale), Times.Once);
        }

        [Fact]
        public async Task GetCustomerSalesHistoryAsync_ShouldUseOptimizedQueries_WhenAvailable()
        {
            // Arrange
            var customerId = 123;
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Today;
            var expectedSales = new List<Sale>
            {
                new Sale { Id = 1, CustomerId = customerId, SaleDate = DateTime.Today, GrandTotal = 75.00m }
            };

            _mockOptimizedQueries.Setup(x => x.GetCustomerSalesHistoryOptimizedAsync(customerId, startDate, endDate))
                .ReturnsAsync(expectedSales);

            // Act
            var result = await _unifiedDataService.GetCustomerSalesHistoryAsync(customerId, startDate, endDate);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().CustomerId.Should().Be(customerId);
            
            _mockOptimizedQueries.Verify(x => x.GetCustomerSalesHistoryOptimizedAsync(customerId, startDate, endDate), Times.Once);
        }

        #endregion

        #region Customer Tests

        [Fact]
        public async Task AddCustomerAsync_ShouldReturnCustomerId_WhenSuccessful()
        {
            // Arrange
            var customer = new Customer { Name = "John Doe", Email = "<EMAIL>", Phone = "************" };
            var expectedId = 789;

            _mockCustomerService.Setup(x => x.AddCustomerAsync(customer))
                .ReturnsAsync(expectedId);

            // Act
            var result = await _unifiedDataService.AddCustomerAsync(customer);

            // Assert
            result.Should().Be(expectedId);
            _mockCustomerService.Verify(x => x.AddCustomerAsync(customer), Times.Once);
        }

        [Fact]
        public async Task SearchCustomersAsync_ShouldReturnMatchingCustomers()
        {
            // Arrange
            var searchTerm = "john";
            var expectedCustomers = new List<Customer>
            {
                new Customer { Id = 1, Name = "John Doe", Email = "<EMAIL>" },
                new Customer { Id = 2, Name = "Johnny Smith", Email = "<EMAIL>" }
            };

            _mockCustomerService.Setup(x => x.SearchCustomersAsync(searchTerm))
                .ReturnsAsync(expectedCustomers);

            // Act
            var result = await _unifiedDataService.SearchCustomersAsync(searchTerm);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(c => c.Name.ToLower().Contains("john")).Should().BeTrue();
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task GetProductsAsync_ShouldLogWarning_WhenOptimizedQueriesFail()
        {
            // Arrange
            _mockOptimizedQueries.Setup(x => x.GetProductsWithCategoriesOptimizedAsync(
                It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            _mockProductService.Setup(x => x.GetAllProductsAsync())
                .ReturnsAsync(new List<Product>());

            // Act
            var result = await _unifiedDataService.GetProductsAsync(1, 50);

            // Assert
            result.Should().NotBeNull();
            
            // Verify that warning was logged (this would require more sophisticated logging verification)
            _mockProductService.Verify(x => x.GetAllProductsAsync(), Times.Once);
        }

        [Fact]
        public async Task AddProductAsync_ShouldHandleNullProduct_Gracefully()
        {
            // Arrange
            Product nullProduct = null;

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => 
                _unifiedDataService.AddProductAsync(nullProduct));
        }

        #endregion
    }
}
