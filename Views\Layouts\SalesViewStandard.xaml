<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Layouts.SalesViewStandard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Layouts"
             xmlns:views="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280">
    
    <!-- This layout simply embeds the original SalesView to maintain full functionality -->
    <views:SalesView x:Name="originalSalesView"/>
    
</UserControl> 