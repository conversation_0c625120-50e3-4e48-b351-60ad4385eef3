<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.ExpensesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             Background="{DynamicResource MaterialDesignBackground}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header with Statistics -->
            <materialDesign:Card Grid.Row="0" Margin="16,16,16,8">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title and Subtitle -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="{DynamicResource BusinessExpenses}"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Margin="0,0,0,4"/>
                        <TextBlock Text="{DynamicResource ManageBusinessExpenses}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" 
                              Orientation="Horizontal"
                              VerticalAlignment="Center">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                Command="{Binding RefreshCommand}"
                                ToolTip="{DynamicResource Refresh}">
                            <materialDesign:PackIcon Kind="Refresh" 
                                                   Height="24" 
                                                   Width="24"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Command="{Binding AddExpenseCommand}"
                                Margin="8,0,0,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource AddExpense}"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Expenses List -->
            <materialDesign:Card Grid.Row="1" Margin="16,8,16,16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Search and Filter Bar -->
                    <Grid Grid.Row="0" 
                          Margin="16,16,16,8"
                          Height="40">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Search Box -->
                        <TextBox Grid.Column="0"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource SearchExpenses}"
                                materialDesign:TextFieldAssist.HasClearButton="True"
                                materialDesign:TextFieldAssist.PrefixText="🔍"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Margin="0,0,16,0"
                                Height="40"
                                VerticalAlignment="Center"/>

                        <!-- Filter Button -->
                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Height="40">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Filter" 
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Filter}"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- Expenses DataGrid -->
                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding BusinessExpenses}"
                             SelectedItem="{Binding SelectedExpense}"
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             Margin="16,8,16,16"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"
                             GridLinesVisibility="All"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             CanUserSortColumns="True"
                             CanUserResizeColumns="True"
                             SelectionMode="Single"
                             SelectionUnit="FullRow"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.ScrollUnit="Pixel">
                        <DataGrid.Resources>
                            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                                           Color="{DynamicResource Primary200}"/>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="{DynamicResource Date}"
                                              Binding="{Binding Date, StringFormat=\{0:d\}}"
                                              Width="Auto"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            <DataGridTextColumn Header="{DynamicResource Description}"
                                              Binding="{Binding Description}"
                                              Width="*"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            <DataGridTextColumn Header="{DynamicResource Amount}"
                                              Binding="{Binding Amount, StringFormat={}{0:N2} DA}"
                                              Width="Auto"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            <DataGridTextColumn Header="{DynamicResource Category}"
                                              Binding="{Binding Category}"
                                              Width="Auto"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            <DataGridTemplateColumn Header="{DynamicResource Actions}"
                                                  Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                                    Command="{Binding DataContext.EditExpenseCommand, 
                                                             RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip="{DynamicResource Edit}">
                                                <materialDesign:PackIcon Kind="Pencil"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                                    Command="{Binding DataContext.DeleteExpenseCommand, 
                                                             RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    Foreground="Red"
                                                    ToolTip="{DynamicResource Delete}">
                                                <materialDesign:PackIcon Kind="Delete"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                <Setter Property="Height" Value="48"/>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Loading Overlay -->
            <Grid Grid.Row="0" 
                  Grid.RowSpan="2" 
                  Background="#80000000"
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="48"
                           Height="48"/>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</UserControl> 