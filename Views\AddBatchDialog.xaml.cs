using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Models;

namespace POSSystem.Views
{
    public partial class AddBatchDialog : Window
    {
        private readonly Product _product;
        public BatchStock BatchStock { get; private set; }

        public AddBatchDialog(Product product)
        {
            InitializeComponent();
            _product = product;
            txtPurchasePrice.Text = product.PurchasePrice.ToString();
            txtSellingPrice.Text = product.SellingPrice.ToString();
            dpManufactureDate.SelectedDate = DateTime.Today;
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                BatchStock = new BatchStock
                {
                    ProductId = _product.Id,
                    BatchNumber = txtBatchNumber.Text,
                    Quantity = decimal.Parse(txtQuantity.Text),
                    PurchasePrice = decimal.Parse(txtPurchasePrice.Text),
                    SellingPrice = decimal.TryParse(txtSellingPrice.Text, out var sp) ? sp : 0m,
                    ManufactureDate = dpManufactureDate.SelectedDate ?? DateTime.Today,
                    ExpiryDate = dpExpiryDate.SelectedDate,
                    Location = txtLocation.Text
                };
                
                DialogResult = true;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtBatchNumber.Text))
            {
                MessageBox.Show("Please enter a batch number", "Validation Error");
                return false;
            }

            if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
            {
                MessageBox.Show("Please enter a valid quantity", "Validation Error");
                return false;
            }

            if (!decimal.TryParse(txtPurchasePrice.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("Please enter a valid purchase price", "Validation Error");
                return false;
            }

            return true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }

        private void DecimalValidation_PreviewTextInput(object sender, System.Windows.Input.TextCompositionEventArgs e)
        {
            var textBox = sender as TextBox;
            var fullText = textBox.Text.Insert(textBox.SelectionStart, e.Text);
            e.Handled = !decimal.TryParse(fullText, out _);
        }
    }
}