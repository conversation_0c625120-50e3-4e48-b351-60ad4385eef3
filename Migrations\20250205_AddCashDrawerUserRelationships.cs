using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class AddCashDrawerUserRelationships : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add foreign key columns
            migrationBuilder.AddColumn<int>(
                name: "OpenedById",
                table: "CashDrawers",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ClosedById",
                table: "CashDrawers",
                type: "INTEGER",
                nullable: true);

            // Add foreign key constraints
            migrationBuilder.CreateIndex(
                name: "IX_CashDrawers_OpenedById",
                table: "CashDrawers",
                column: "OpenedById");

            migrationBuilder.CreateIndex(
                name: "IX_CashDrawers_ClosedById",
                table: "CashDrawers",
                column: "ClosedById");

            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers",
                column: "OpenedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers",
                column: "ClosedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove foreign key constraints
            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers");

            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers");

            migrationBuilder.DropIndex(
                name: "IX_CashDrawers_OpenedById",
                table: "CashDrawers");

            migrationBuilder.DropIndex(
                name: "IX_CashDrawers_ClosedById",
                table: "CashDrawers");

            // Remove foreign key columns
            migrationBuilder.DropColumn(
                name: "OpenedById",
                table: "CashDrawers");

            migrationBuilder.DropColumn(
                name: "ClosedById",
                table: "CashDrawers");
        }
    }
} 