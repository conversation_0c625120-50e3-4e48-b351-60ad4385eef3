using System;
using System.Collections.Generic;
using System.Linq;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services.InventoryManagement
{
    public class StockService : IStockService
    {
        private readonly IDatabaseService _db;

        public StockService(IDatabaseService db)
        {
            _db = db;
        }

        public void IncreaseStock(int productId, decimal quantity, string reason = null, string batchNumber = null)
        {
            IncreaseStock(productId, quantity, reason, batchNumber, null, null, null, null, null);
        }

        public void IncreaseStock(int productId, decimal quantity, string reason = null, string batchNumber = null,
            decimal? purchasePrice = null, DateTime? expiryDate = null, DateTime? manufactureDate = null,
            string location = null, string notes = null, decimal? sellingPrice = null)
        {
            if (quantity <= 0) return;

            var product = _db.GetProductById(productId);
            if (product == null) throw new InvalidOperationException($"Product {productId} not found");

            if (product.TrackBatches || product.HasExpiry)
            {
                // If a batchNumber is provided, try to add to that batch; otherwise create a new batch
                var batches = _db.GetBatchesForProduct(productId) ?? new List<BatchStock>();
                BatchStock target = null;
                if (!string.IsNullOrWhiteSpace(batchNumber))
                {
                    target = batches.FirstOrDefault(b => b.BatchNumber == batchNumber);
                }

                if (target == null)
                {
                    var effectiveSellingPrice = (sellingPrice.HasValue && sellingPrice.Value > 0m)
                        ? sellingPrice.Value
                        : (product?.SellingPrice ?? 0m);

                    target = new BatchStock
                    {
                        ProductId = productId,
                        BatchNumber = batchNumber ?? $"AUTO-{DateTime.Now:yyyyMMddHHmmss}",
                        Quantity = quantity,
                        PurchasePrice = purchasePrice ?? 0m,
                        SellingPrice = effectiveSellingPrice,
                        ExpiryDate = expiryDate,
                        ManufactureDate = manufactureDate ?? DateTime.Now,
                        Location = location ?? "",
                        Notes = notes ?? "",
                        CreatedAt = DateTime.Now
                    };
                    _db.AddBatchStock(target);
                }
                else
                {
                    _db.AddStockToBatch(target.Id, quantity);
                }

                // Totals are synced inside AddBatchStock/AddStockToBatch
            }
            else
            {
                // Non-batch: use absolute update or delta method
                // We can reuse the delta-based raw SQL method via concrete DatabaseService if available
                if (_db is DatabaseService concrete)
                {
                    concrete.UpdateProductStock(productId, quantity);
                }
                else
                {
                    var p = _db.GetProductById(productId);
                    _db.UpdateProductStock(productId, p.StockQuantity + quantity, reason ?? "Purchase Increase");
                }
            }


        }

        // Force creation of a brand-new batch entry regardless of existing batch numbers (used by purchase invoices)
        public void IncreaseStockNewBatch(int productId, decimal quantity, string reason = null, string batchNumber = null,
            decimal? purchasePrice = null, DateTime? expiryDate = null, DateTime? manufactureDate = null,
            string location = null, string notes = null, decimal? sellingPrice = null)
        {
            if (quantity <= 0) return;

            var product = _db.GetProductById(productId);
            if (product == null) throw new InvalidOperationException($"Product {productId} not found");

            // Always create a fresh batch entry
            var effectiveSellingPrice = (sellingPrice.HasValue && sellingPrice.Value > 0m)
                ? sellingPrice.Value
                : (product?.SellingPrice ?? 0m);

            var newBatch = new BatchStock
            {
                ProductId = productId,
                BatchNumber = string.IsNullOrWhiteSpace(batchNumber) ? $"AUTO-{DateTime.Now:yyyyMMddHHmmss}" : batchNumber,
                Quantity = quantity,
                PurchasePrice = purchasePrice ?? 0m,
                SellingPrice = effectiveSellingPrice,
                ExpiryDate = expiryDate,
                ManufactureDate = manufactureDate ?? DateTime.Now,
                Location = location ?? string.Empty,
                Notes = notes ?? string.Empty,
                CreatedAt = DateTime.Now
            };

            _db.AddBatchStock(newBatch);

            // Sync totals via AddBatchStock; then notify
            NotifyStockChanged(productId);
        }

        public void DecreaseStock(int productId, decimal quantity, string reason = null)
        {
            if (quantity <= 0) return;

            var product = _db.GetProductById(productId);
            if (product == null) throw new InvalidOperationException($"Product {productId} not found");

            if (product.TrackBatches)
            {
                // Use concrete helper to handle FEFO/FIFO and syncing
                if (_db is DatabaseService concrete)
                {
                    concrete.UpdateProductStock(productId, -quantity);
                }
                else
                {
                    // Fallback: subtract from denormalized, then rely on separate sync if needed
                    var p = _db.GetProductById(productId);
                    _db.UpdateProductStock(productId, p.StockQuantity - quantity, reason ?? "Sales Decrease");
                }
            }
            else
            {
                if (_db is DatabaseService concrete)
                {
                    concrete.UpdateProductStock(productId, -quantity);
                }
                else
                {
                    var p = _db.GetProductById(productId);
                    _db.UpdateProductStock(productId, p.StockQuantity - quantity, reason ?? "Sales Decrease");
                }
            }

            NotifyStockChanged(productId);
        }

        public void SetStock(int productId, decimal newQuantity, string reason = null)
        {
            _db.UpdateProductStock(productId, newQuantity, reason ?? "SetStock");
            NotifyStockChanged(productId);
        }

        public void NotifyStockChanged(int productId)
        {
            try
            {
                var prod = _db.GetProductById(productId);
                if (prod == null) return;
                var finalQty = prod.TrackBatches ? prod.GetTotalStockDecimal() : prod.StockQuantity;
                ViewModels.SaleViewModel.NotifyProductStockChanged(productId, finalQty, this);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[StockService] Notify error: {ex.Message}");
            }
        }

        public void IncreaseStockBulk(IEnumerable<(int productId, decimal qty)> items)
        {
            foreach (var (productId, qty) in items)
            {
                IncreaseStock(productId, qty, null, null);
            }
        }

        public void DecreaseStockBulk(IEnumerable<(int productId, decimal qty)> items)
        {
            foreach (var (productId, qty) in items)
            {
                DecreaseStock(productId, qty, null);
            }
        }
    }
}

