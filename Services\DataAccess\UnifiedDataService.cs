using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.UserManagement;
using POSSystem.Services.Interfaces;
using POSSystem.Services.QueryOptimization;
using POSSystem.Services.Monitoring;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.DataAccess
{
    /// <summary>
    /// Unified data access service that standardizes all data operations across the POS system.
    /// Provides a consistent API that automatically routes to appropriate management services
    /// with fallback to DatabaseService for backward compatibility.
    /// </summary>
    /// <remarks>
    /// <para>This service acts as a unified facade over multiple data access patterns:</para>
    /// <list type="bullet">
    /// <item><description>Primary: Uses specialized management services (ProductManagementService, SalesManagementService, etc.)</description></item>
    /// <item><description>Fallback: Falls back to DatabaseService for operations not yet migrated</description></item>
    /// <item><description>Optimization: Leverages OptimizedQueryService for high-performance queries when available</description></item>
    /// <item><description>Monitoring: Integrates with PerformanceMonitoringService for operation tracking</description></item>
    /// </list>
    /// <para>The service ensures consistent error handling, logging, and performance monitoring
    /// across all data operations while maintaining backward compatibility with existing code.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Get products with automatic optimization and monitoring
    /// var products = await unifiedService.GetProductsAsync(page: 1, pageSize: 50);
    ///
    /// // Search with fallback handling
    /// var searchResults = await unifiedService.SearchProductsAsync("laptop", maxResults: 20);
    /// </code>
    /// </example>
    public class UnifiedDataService : IUnifiedDataService
    {
        private readonly IProductManagementService _productService;
        private readonly ISalesManagementService _salesService;
        private readonly ICustomerManagementService _customerService;
        private readonly IUserManagementService _userService;
        private readonly IInventoryManagementService _inventoryService;
        private readonly IDatabaseService _databaseService; // Fallback for operations not yet migrated
        private readonly OptimizedQueryService _optimizedQueries; // For high-performance queries
        private readonly PerformanceMonitoringService _performanceMonitor; // For performance tracking
        private readonly ILogger<UnifiedDataService> _logger;

        /// <summary>
        /// Initializes a new instance of the UnifiedDataService with required dependencies.
        /// </summary>
        /// <param name="productService">Service for product-related operations</param>
        /// <param name="salesService">Service for sales-related operations</param>
        /// <param name="customerService">Service for customer-related operations</param>
        /// <param name="userService">Service for user-related operations</param>
        /// <param name="inventoryService">Service for inventory-related operations</param>
        /// <param name="databaseService">Fallback service for operations not yet migrated to management services</param>
        /// <param name="optimizedQueries">Optional service for high-performance query optimization</param>
        /// <param name="performanceMonitor">Optional service for performance monitoring and alerting</param>
        /// <param name="logger">Optional logger for diagnostic information</param>
        /// <exception cref="ArgumentNullException">Thrown when any required service parameter is null</exception>
        public UnifiedDataService(
            IProductManagementService productService,
            ISalesManagementService salesService,
            ICustomerManagementService customerService,
            IUserManagementService userService,
            IInventoryManagementService inventoryService,
            IDatabaseService databaseService,
            OptimizedQueryService optimizedQueries = null,
            PerformanceMonitoringService performanceMonitor = null,
            ILogger<UnifiedDataService> logger = null)
        {
            _productService = productService ?? throw new ArgumentNullException(nameof(productService));
            _salesService = salesService ?? throw new ArgumentNullException(nameof(salesService));
            _customerService = customerService ?? throw new ArgumentNullException(nameof(customerService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _inventoryService = inventoryService ?? throw new ArgumentNullException(nameof(inventoryService));
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _optimizedQueries = optimizedQueries; // Optional - can be null
            _performanceMonitor = performanceMonitor; // Optional - can be null
            _logger = logger;
        }

        // ===== PRODUCT OPERATIONS =====
        /// <summary>
        /// Retrieves a paginated list of active products with automatic optimization and performance monitoring.
        /// </summary>
        /// <param name="page">The page number to retrieve (1-based indexing)</param>
        /// <param name="pageSize">The number of products per page (default: 50, maximum recommended: 100)</param>
        /// <returns>A list of products for the specified page, or empty list if no products found</returns>
        /// <remarks>
        /// <para>This method uses a multi-tier approach for optimal performance:</para>
        /// <list type="number">
        /// <item><description>First attempts to use OptimizedQueryService for best performance with category includes</description></item>
        /// <item><description>Falls back to ProductManagementService if optimization is unavailable</description></item>
        /// <item><description>Finally falls back to DatabaseService for backward compatibility</description></item>
        /// </list>
        /// <para>All operations are automatically monitored for performance tracking and alerting.</para>
        /// </remarks>
        /// <example>
        /// <code>
        /// // Get first page of products
        /// var products = await unifiedService.GetProductsAsync();
        ///
        /// // Get specific page with custom page size
        /// var products = await unifiedService.GetProductsAsync(page: 2, pageSize: 25);
        /// </code>
        /// </example>
        public async Task<List<Product>> GetProductsAsync(int page = 1, int pageSize = 50)
        {
            return await _performanceMonitor?.ExecuteWithMonitoringAsync("GetProducts", async () =>
            {
                try
                {
                    // Use optimized query service if available (fixes N+1 problems)
                    if (_optimizedQueries != null)
                    {
                        return await _optimizedQueries.GetProductsWithCategoriesOptimizedAsync(
                            categoryId: null,
                            searchTerm: null,
                            page: page,
                            pageSize: pageSize);
                    }

                    // Fallback to management service
                    var allProducts = await _productService.GetAllProductsAsync();
                    return allProducts.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Optimized query failed, falling back to DatabaseService");
                    // Fallback to DatabaseService
                    var allProducts = _databaseService.GetAllProducts();
                    return allProducts.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                }
            }, "DataAccess") ?? await GetProductsWithoutMonitoringAsync(page, pageSize);
        }

        private async Task<List<Product>> GetProductsWithoutMonitoringAsync(int page, int pageSize)
        {
            try
            {
                if (_optimizedQueries != null)
                {
                    return await _optimizedQueries.GetProductsWithCategoriesOptimizedAsync(
                        categoryId: null, searchTerm: null, page: page, pageSize: pageSize);
                }
                var allProducts = await _productService.GetAllProductsAsync();
                return allProducts.Skip((page - 1) * pageSize).Take(pageSize).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Optimized query failed, falling back to DatabaseService");
                var allProducts = _databaseService.GetAllProducts();
                return allProducts.Skip((page - 1) * pageSize).Take(pageSize).ToList();
            }
        }

        public async Task<List<Product>> GetAllProductsAsync()
        {
            try
            {
                return await _productService.GetAllProductsAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllProducts();
            }
        }

        public async Task<Product> GetProductByIdAsync(int id)
        {
            try
            {
                return await _productService.GetProductByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return _databaseService.GetProductById(id);
            }
        }

        public async Task<Product> GetProductByBarcodeAsync(string barcode)
        {
            try
            {
                return await _productService.GetProductByBarcodeAsync(barcode);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return _databaseService.GetProductByBarcode(barcode);
            }
        }

        public async Task<List<Product>> SearchProductsAsync(string searchTerm, int maxResults = 50)
        {
            try
            {
                // Use optimized query service if available (uses indexes for better performance)
                if (_optimizedQueries != null)
                {
                    return await _optimizedQueries.GetProductsWithCategoriesOptimizedAsync(
                        categoryId: null,
                        searchTerm: searchTerm,
                        page: 1,
                        pageSize: maxResults);
                }

                // Fallback to management service
                var results = await _productService.SearchProductsAsync(searchTerm);
                return results.Take(maxResults).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Optimized search failed, falling back to DatabaseService");
                var results = await _databaseService.SearchProductsAsync(searchTerm);
                return results.Take(maxResults).ToList();
            }
        }

        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            try
            {
                return await _productService.GetLowStockProductsAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return await _databaseService.GetLowStockProductsAsync();
            }
        }

        public async Task<List<Product>> GetExpiringProductsAsync(int daysThreshold = 30)
        {
            try
            {
                return await _productService.GetExpiringProductsAsync(daysThreshold);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return _databaseService.GetExpiringProducts(daysThreshold);
            }
        }

        public async Task<int> AddProductAsync(Product product)
        {
            try
            {
                return await _productService.AddProductAsync(product);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                _databaseService.AddProduct(product);
                return product.Id; // DatabaseService.AddProduct is void, return the product ID
            }
        }

        public async Task<bool> UpdateProductAsync(Product product)
        {
            try
            {
                return await _productService.UpdateProductAsync(product);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateProduct(product);
                return true; // DatabaseService.UpdateProduct is void, assume success
            }
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                return await _productService.DeleteProductAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                _databaseService.DeleteProduct(id);
                return true; // DatabaseService.DeleteProduct is void, assume success
            }
        }

        public async Task<bool> UpdateProductStockAsync(int productId, decimal newQuantity)
        {
            try
            {
                return await _inventoryService.UpdateProductStockWithReasonAsync(productId, newQuantity, "Stock Update");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "InventoryManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateProductStock(productId, newQuantity, "Stock Update");
                return true; // DatabaseService.UpdateProductStock is void, assume success
            }
        }

        // ===== SALES OPERATIONS =====
        public async Task<List<Sale>> GetSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // Use optimized query service if available (fixes N+1 problems with sale items)
                if (_optimizedQueries != null)
                {
                    return await _optimizedQueries.GetSalesWithItemsOptimizedAsync(startDate, endDate);
                }

                // Fallback to management service
                return await _salesService.GetSalesForPeriodAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Optimized sales query failed, falling back to DatabaseService");
                return await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
            }
        }

        public async Task<List<Sale>> GetRecentSalesAsync(int limit = 50)
        {
            try
            {
                return await _salesService.GetRecentSalesAsync(limit);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                return _databaseService.GetRecentSales(limit);
            }
        }

        public async Task<Sale> GetSaleByIdAsync(int id)
        {
            try
            {
                return await _salesService.GetSaleByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                return _databaseService.GetSaleById(id);
            }
        }

        public async Task<List<Sale>> GetUnpaidSalesAsync()
        {
            try
            {
                return await _salesService.GetUnpaidSalesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                return _databaseService.GetUnpaidSales();
            }
        }

        public async Task<int> SaveSaleAsync(Sale sale)
        {
            try
            {
                return await _salesService.SaveSaleAsync(sale);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                _databaseService.AddSale(sale); // Use AddSale instead of SaveSale
                return sale.Id; // DatabaseService.AddSale is void, return the sale ID
            }
        }

        public async Task<bool> UpdateSaleAsync(Sale sale)
        {
            try
            {
                return await _salesService.UpdateSaleAsync(sale);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateSale(sale);
                return true; // DatabaseService.UpdateSale is void, assume success
            }
        }

        public async Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _salesService.GetSalesCountAndTotalAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                return await _databaseService.GetSalesCountAndTotalAsync(startDate, endDate);
            }
        }

        public async Task<decimal> GetSalesTotalAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // SalesManagementService.GetSalesTotalAsync only takes single date, use fallback calculation
                var sales = await _salesService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Sum(s => s.GrandTotal);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have GetSalesTotalAsync with date range, use fallback calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Sum(s => s.GrandTotal);
            }
        }

        // ===== CUSTOMER OPERATIONS =====
        public async Task<List<Customer>> GetCustomersAsync()
        {
            try
            {
                return await _customerService.GetAllCustomersAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllCustomers();
            }
        }

        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            try
            {
                return await _customerService.GetCustomerByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                return _databaseService.GetCustomerById(id);
            }
        }

        public async Task<List<Customer>> SearchCustomersAsync(string searchTerm)
        {
            try
            {
                return await _customerService.SearchCustomersAsync(searchTerm);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have SearchCustomers, use GetAllCustomers and filter
                var allCustomers = _databaseService.GetAllCustomers();
                return allCustomers.Where(c => c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                              c.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                              c.Phone.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
            }
        }

        public async Task<int> AddCustomerAsync(Customer customer)
        {
            try
            {
                return await _customerService.AddCustomerAsync(customer);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                _databaseService.AddCustomer(customer);
                return customer.Id; // DatabaseService.AddCustomer is void, return the customer ID
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                return await _customerService.UpdateCustomerAsync(customer);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateCustomer(customer);
                return true; // DatabaseService.UpdateCustomer is void, assume success
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                return await _customerService.DeleteCustomerAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                _databaseService.DeleteCustomer(id);
                return true; // DatabaseService.DeleteCustomer is void, assume success
            }
        }

        public async Task<List<Sale>> GetCustomerSalesHistoryAsync(int customerId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // Use optimized query service if available (fixes N+1 problems)
                if (_optimizedQueries != null)
                {
                    return await _optimizedQueries.GetCustomerSalesHistoryOptimizedAsync(customerId, startDate, endDate);
                }

                // Fallback to DatabaseService
                var allSales = await _databaseService.GetSalesForPeriodAsync(
                    startDate ?? DateTime.MinValue,
                    endDate ?? DateTime.MaxValue);

                return allSales.Where(s => s.CustomerId == customerId).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get customer sales history for customer {CustomerId}", customerId);
                return new List<Sale>();
            }
        }

        // ===== USER OPERATIONS =====
        public async Task<List<User>> GetUsersAsync()
        {
            try
            {
                return await _userService.GetAllUsersAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllUsers();
            }
        }

        public async Task<User> GetUserByIdAsync(int id)
        {
            try
            {
                return await _userService.GetUserByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                return _databaseService.GetUserById(id);
            }
        }

        public async Task<User> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                return await _userService.AuthenticateUserAsync(username, password);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                return _databaseService.AuthenticateUser(username, password);
            }
        }

        public async Task<int> AddUserAsync(User user)
        {
            try
            {
                // UserManagementService.AddUserAsync requires a password parameter
                return await _userService.AddUserAsync(user, user.Password ?? "defaultpassword");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                _databaseService.AddUser(user);
                return user.Id; // DatabaseService.AddUser is void, return the user ID
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                return await _userService.UpdateUserAsync(user);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateUser(user);
                return true; // DatabaseService.UpdateUser is void, assume success
            }
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            try
            {
                return await _userService.DeleteUserAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "UserManagementService failed, falling back to DatabaseService");
                _databaseService.DeleteUser(id);
                return true; // DatabaseService.DeleteUser is void, assume success
            }
        }

        // ===== DASHBOARD OPERATIONS =====
        public async Task<List<Product>> GetTopSellingProductsAsync(int count = 10)
        {
            try
            {
                // Use DatabaseService for complex analytics until we have dedicated analytics service
                var topProducts = _databaseService.GetTopSellingProducts(count);
                return topProducts.Select(tp => tp.Product).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get top selling products");
                return new List<Product>();
            }
        }

        public async Task<List<Customer>> GetTopCustomersAsync(int count = 10)
        {
            try
            {
                // DatabaseService doesn't have GetTopCustomers, use fallback
                var allCustomers = _databaseService.GetAllCustomers();
                return allCustomers.Take(count).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get top customers");
                return new List<Customer>();
            }
        }

        public async Task<decimal> GetTotalRevenueAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // SalesManagementService.GetSalesTotalAsync only takes single date, use fallback calculation
                var sales = await _salesService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Sum(s => s.GrandTotal);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have GetSalesTotalAsync with date range, use fallback calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Sum(s => s.GrandTotal);
            }
        }

        public async Task<decimal> GetTotalProfitAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // DatabaseService doesn't have GetTotalProfitAsync, use FIFO-based calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Sum(s => s.Items.Sum(i =>
                    i.Quantity * (i.UnitPrice - (i.ActualCostBasis > 0 ? i.ActualCostBasis : i.Product?.PurchasePrice ?? 0))));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get total profit");
                return 0m;
            }
        }

        public async Task<int> GetTotalCustomersAsync()
        {
            try
            {
                var customers = await _customerService.GetAllCustomersAsync();
                return customers.Count;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "CustomerManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllCustomers().Count;
            }
        }

        public async Task<int> GetTotalProductsAsync()
        {
            try
            {
                var products = await _productService.GetAllProductsAsync();
                return products.Count;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ProductManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllProducts().Count;
            }
        }

        // ===== INVENTORY OPERATIONS =====
        public async Task<List<Category>> GetCategoriesAsync()
        {
            try
            {
                return await _inventoryService.GetAllCategoriesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "InventoryManagementService failed, falling back to DatabaseService");
                return _databaseService.GetAllCategories();
            }
        }

        public async Task<int> AddCategoryAsync(Category category)
        {
            try
            {
                return await _inventoryService.AddCategoryAsync(category);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "InventoryManagementService failed, falling back to DatabaseService");
                _databaseService.AddCategory(category);
                return category.Id; // DatabaseService.AddCategory is void, return the category ID
            }
        }

        public async Task<bool> UpdateCategoryAsync(Category category)
        {
            try
            {
                return await _inventoryService.UpdateCategoryAsync(category);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "InventoryManagementService failed, falling back to DatabaseService");
                _databaseService.UpdateCategory(category);
                return true; // DatabaseService.UpdateCategory is void, assume success
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                return await _inventoryService.DeleteCategoryAsync(id);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "InventoryManagementService failed, falling back to DatabaseService");
                _databaseService.DeleteCategory(id);
                return true; // DatabaseService.DeleteCategory is void, assume success
            }
        }

        // ===== STATISTICS OPERATIONS =====
        public async Task<Dictionary<string, decimal>> GetSalesByPaymentMethodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _salesService.GetPaymentMethodDistributionAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have GetSalesByPaymentMethod, use fallback calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.GroupBy(s => s.PaymentMethod)
                           .ToDictionary(g => g.Key, g => g.Sum(s => s.GrandTotal));
            }
        }

        public async Task<List<Sale>> GetSalesByPaymentMethodAsync(string paymentMethod, DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _salesService.GetSalesByPaymentMethodAsync(paymentMethod, startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have GetSalesByPaymentMethod, use fallback calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Where(s => s.PaymentMethod == paymentMethod).ToList();
            }
        }

        public async Task<decimal> GetAverageTransactionValueAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _salesService.GetAverageTransactionValueAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "SalesManagementService failed, falling back to DatabaseService");
                // DatabaseService doesn't have GetAverageTransactionValue, use fallback calculation
                var sales = await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
                return sales.Any() ? sales.Average(s => s.GrandTotal) : 0m;
            }
        }

        // ===== BULK PRICING OPERATIONS =====
        public async Task<List<ProductPriceTier>> GetProductPriceTiersAsync(int productId)
        {
            try
            {
                using var context = new POSDbContext();
                return await context.ProductPriceTiers
                    .AsNoTracking()
                    .Where(pt => pt.ProductId == productId)
                    .OrderBy(pt => pt.MinimumQuantity)
                    .ThenBy(pt => pt.DisplayOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get product price tiers for product {ProductId}", productId);
                return new List<ProductPriceTier>();
            }
        }

        public async Task<List<ProductPriceTier>> GetAllActivePriceTiersAsync()
        {
            try
            {
                using var context = new POSDbContext();
                var now = DateTime.Now;
                return await context.ProductPriceTiers
                    .AsNoTracking()
                    .Include(pt => pt.Product)
                    .Where(pt => pt.IsActive &&
                                (pt.EffectiveDate == null || pt.EffectiveDate <= now) &&
                                (pt.ExpirationDate == null || pt.ExpirationDate > now))
                    .OrderBy(pt => pt.ProductId)
                    .ThenBy(pt => pt.MinimumQuantity)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get all active price tiers");
                return new List<ProductPriceTier>();
            }
        }

        public async Task<ProductPriceTier> GetPriceTierByIdAsync(int id)
        {
            try
            {
                using var context = new POSDbContext();
                return await context.ProductPriceTiers
                    .AsNoTracking()
                    .Include(pt => pt.Product)
                    .FirstOrDefaultAsync(pt => pt.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get price tier {Id}", id);
                return null;
            }
        }

        public async Task<int> AddPriceTierAsync(ProductPriceTier priceTier)
        {
            try
            {
                using var context = new POSDbContext();
                priceTier.CreatedAt = DateTime.Now;
                priceTier.UpdatedAt = DateTime.Now;

                context.ProductPriceTiers.Add(priceTier);
                await context.SaveChangesAsync();

                _logger?.LogInformation("Added price tier {Id} for product {ProductId}", priceTier.Id, priceTier.ProductId);
                return priceTier.Id;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to add price tier for product {ProductId}", priceTier.ProductId);
                return 0;
            }
        }

        public async Task<bool> UpdatePriceTierAsync(ProductPriceTier priceTier)
        {
            try
            {
                using var context = new POSDbContext();
                priceTier.UpdatedAt = DateTime.Now;

                context.ProductPriceTiers.Update(priceTier);
                await context.SaveChangesAsync();

                _logger?.LogInformation("Updated price tier {Id}", priceTier.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to update price tier {Id}", priceTier.Id);
                return false;
            }
        }

        public async Task<bool> DeletePriceTierAsync(int id)
        {
            try
            {
                using var context = new POSDbContext();
                var priceTier = await context.ProductPriceTiers.FindAsync(id);

                if (priceTier != null)
                {
                    context.ProductPriceTiers.Remove(priceTier);
                    await context.SaveChangesAsync();

                    _logger?.LogInformation("Deleted price tier {Id}", id);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to delete price tier {Id}", id);
                return false;
            }
        }

        public async Task<bool> DeleteProductPriceTiersAsync(int productId)
        {
            try
            {
                using var context = new POSDbContext();
                var priceTiers = await context.ProductPriceTiers
                    .Where(pt => pt.ProductId == productId)
                    .ToListAsync();

                if (priceTiers.Any())
                {
                    context.ProductPriceTiers.RemoveRange(priceTiers);
                    await context.SaveChangesAsync();

                    _logger?.LogInformation("Deleted {Count} price tiers for product {ProductId}", priceTiers.Count, productId);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to delete price tiers for product {ProductId}", productId);
                return false;
            }
        }

        public async Task<List<Product>> GetProductsWithBulkPricingAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.PriceTiers.Where(pt => pt.IsActive))
                    .Where(p => p.IsActive && p.PriceTiers.Any(pt => pt.IsActive))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get products with bulk pricing");
                return new List<Product>();
            }
        }

        public async Task<BulkPricingResult> CalculateBestPricingAsync(int productId, decimal quantity)
        {
            try
            {
                var product = await GetProductByIdAsync(productId);
                if (product == null)
                {
                    return new BulkPricingResult
                    {
                        Product = null,
                        RequestedQuantity = quantity,
                        RegularUnitPrice = 0,
                        RegularTotalPrice = 0,
                        EffectiveUnitPrice = 0,
                        TotalPrice = 0,
                        TotalSavings = 0,
                        SavingsPercentage = 0
                    };
                }

                // Load pricing tiers for the product
                var priceTiers = await GetProductPriceTiersAsync(productId);
                product.PriceTiers = priceTiers;

                var bulkPricingService = new BulkPricingService();
                return bulkPricingService.CalculateBestPricing(product, quantity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to calculate best pricing for product {ProductId} with quantity {Quantity}", productId, quantity);
                return new BulkPricingResult
                {
                    Product = null,
                    RequestedQuantity = quantity,
                    RegularUnitPrice = 0,
                    RegularTotalPrice = 0,
                    EffectiveUnitPrice = 0,
                    TotalPrice = 0,
                    TotalSavings = 0,
                    SavingsPercentage = 0
                };
            }
        }

        public async Task<List<QuantitySuggestion>> GetQuantitySuggestionsAsync(int productId, decimal currentQuantity)
        {
            try
            {
                var product = await GetProductByIdAsync(productId);
                if (product == null)
                {
                    return new List<QuantitySuggestion>();
                }

                // Load pricing tiers for the product
                var priceTiers = await GetProductPriceTiersAsync(productId);
                product.PriceTiers = priceTiers;

                var bulkPricingService = new BulkPricingService();
                return bulkPricingService.GetQuantitySuggestions(product, currentQuantity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get quantity suggestions for product {ProductId} with quantity {Quantity}", productId, currentQuantity);
                return new List<QuantitySuggestion>();
            }
        }

        // ===== UTILITY OPERATIONS =====
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                // DatabaseService doesn't have TestConnectionAsync, use simple test
                var products = _databaseService.GetAllProducts();
                return true; // If we can get products, connection is working
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Database connection test failed");
                return false;
            }
        }

        public async Task<string> GetDatabaseInfoAsync()
        {
            try
            {
                // DatabaseService doesn't have GetDatabaseInfo, return basic info
                return await Task.FromResult("Database connection active");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get database info");
                return "Database info unavailable";
            }
        }
    }
}
