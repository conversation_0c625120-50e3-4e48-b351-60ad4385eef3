using System;
using System.Collections;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class EmptyCollectionToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Check if the value is a collection
            if (value is ICollection collection)
            {
                bool isEmpty = collection.Count == 0;
                
                // By default, return Visible for empty collections
                // If parameter is "inverse", reverse the logic
                bool showWhenEmpty = true;
                if (parameter is string param && param == "inverse")
                {
                    showWhenEmpty = false;
                }
                
                return (isEmpty == showWhenEmpty) ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // If not a collection, default to Collapsed
            return Visibility.Collapsed;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 