using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using POSSystem.ViewModels;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// Orchestrates comprehensive performance testing of SaleViewGrid
    /// </summary>
    public class PerformanceTestRunner
    {
        private readonly SaleViewGridPerformanceAnalyzer _analyzer;
        private readonly string _reportDirectory;

        public PerformanceTestRunner()
        {
            _analyzer = new SaleViewGridPerformanceAnalyzer();
            _reportDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceReports");
            Directory.CreateDirectory(_reportDirectory);
        }

        /// <summary>
        /// Run comprehensive performance analysis
        /// </summary>
        public async Task<PerformanceAnalysisReport> RunPerformanceAnalysisAsync(
            Views.Layouts.SalesViewGrid gridView, 
            SaleViewModel viewModel)
        {
            Debug.WriteLine("[PERFORMANCE] Starting comprehensive performance analysis...");
            
            try
            {
                // Run the analysis
                var report = await _analyzer.AnalyzePerformanceAsync(gridView, viewModel);
                
                // Generate recommendations
                GenerateRecommendations(report);
                
                // Save detailed report
                await SaveDetailedReportAsync(report);
                
                // Log summary
                LogPerformanceSummary(report);
                
                return report;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERFORMANCE] Analysis failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Generate performance recommendations based on test results
        /// </summary>
        private void GenerateRecommendations(PerformanceAnalysisReport report)
        {
            // Load time recommendations
            if (report.LoadTimeTests.Count > 0)
            {
                var avgLoadTime = report.LoadTimeTests.Average(t => t.TotalLoadTime);
                if (avgLoadTime > 1000)
                {
                    report.Recommendations.Add("Consider implementing progressive loading for large product catalogs");
                    report.Warnings.Add($"Average load time is {avgLoadTime:F0}ms, which may impact user experience");
                }
                else if (avgLoadTime > 500)
                {
                    report.Recommendations.Add("Load times are acceptable but could benefit from further optimization");
                }
            }

            // Memory recommendations
            if (report.MemoryUsageTest != null)
            {
                if (report.MemoryUsageTest.HasMemoryLeak)
                {
                    report.Warnings.Add($"Potential memory leak detected: {report.MemoryUsageTest.MemoryLeakIndicator:F1}MB growth");
                    report.Recommendations.Add("Review event subscriptions and ensure proper disposal of resources");
                }
                
                var memoryIncrease = report.MemoryUsageTest.PeakMemory - report.MemoryUsageTest.BaselineMemory;
                if (memoryIncrease > 100)
                {
                    report.Recommendations.Add("Consider implementing object pooling for product items to reduce memory allocation");
                }
            }

            // Grid layout recommendations
            var suboptimalLayouts = report.GridLayoutTests.Count(t => !t.IsOptimal);
            if (suboptimalLayouts > 0)
            {
                report.Warnings.Add($"{suboptimalLayouts} grid layout scenarios are not optimal");
                report.Recommendations.Add("Fine-tune dynamic column calculation algorithm for better spacing");
            }

            // Virtualization recommendations
            if (report.VirtualizationTest != null && !report.VirtualizationTest.IsVirtualizationEffective)
            {
                report.Warnings.Add("Virtualization is not working effectively");
                report.Recommendations.Add("Verify UniformGrid virtualization settings and container recycling");
            }

            // UI responsiveness recommendations
            if (report.UIResponsivenessTest != null && !report.UIResponsivenessTest.IsResponsive)
            {
                report.Recommendations.Add("Consider debouncing grid updates and using background threading for calculations");
            }

            // Overall performance recommendations
            var score = report.CalculateOverallScore();
            if (score < 80)
            {
                report.Recommendations.Add("Overall performance needs improvement - prioritize addressing warnings above");
            }
            else if (score >= 90)
            {
                report.Recommendations.Add("Excellent performance! Consider this implementation as a best practice template");
            }
        }

        /// <summary>
        /// Save detailed performance report to file
        /// </summary>
        private async Task SaveDetailedReportAsync(PerformanceAnalysisReport report)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var filename = $"SaleViewGrid_Performance_Report_{timestamp}.txt";
            var filepath = Path.Combine(_reportDirectory, filename);

            var reportContent = GenerateDetailedReportContent(report);
            await File.WriteAllTextAsync(filepath, reportContent);
            
            Debug.WriteLine($"[PERFORMANCE] Detailed report saved to: {filepath}");
        }

        /// <summary>
        /// Generate detailed report content
        /// </summary>
        private string GenerateDetailedReportContent(PerformanceAnalysisReport report)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("═══════════════════════════════════════════════════════════════");
            sb.AppendLine("              SALEVIEWGRID PERFORMANCE ANALYSIS REPORT");
            sb.AppendLine("═══════════════════════════════════════════════════════════════");
            sb.AppendLine($"Test Date: {report.TestStartTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"Duration: {report.TotalTestDuration.TotalSeconds:F2} seconds");
            sb.AppendLine($"Grid Type: {report.GridViewType}");
            sb.AppendLine($"Overall Score: {report.CalculateOverallScore():F1}/100 (Grade: {report.GetPerformanceGrade()})");
            sb.AppendLine();

            // Baseline Metrics
            if (report.BaselineMetrics != null)
            {
                sb.AppendLine("BASELINE METRICS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine($"Initial Memory Usage: {report.BaselineMetrics.InitialMemoryUsage:F1} MB");
                sb.AppendLine($"Initial Product Count: {report.BaselineMetrics.InitialProductCount}");
                sb.AppendLine($"Current Columns: {report.BaselineMetrics.CurrentColumns}");
                sb.AppendLine($"Grid Dimensions: {report.BaselineMetrics.GridWidth:F0} x {report.BaselineMetrics.GridHeight:F0}");
                sb.AppendLine($"UI Thread Response: {report.BaselineMetrics.UIThreadResponseTime} ms");
                sb.AppendLine();
            }

            // Load Time Tests
            if (report.LoadTimeTests.Count > 0)
            {
                sb.AppendLine("LOAD TIME PERFORMANCE");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine("Products | Total Time | Collection | Memory Inc | Rating");
                sb.AppendLine("---------|------------|------------|------------|--------");
                foreach (var test in report.LoadTimeTests)
                {
                    sb.AppendLine($"{test.ProductCount,8} | {test.TotalLoadTime,9} ms | {test.CollectionUpdateTime,9} ms | {test.MemoryIncrease,9:F1} MB | {test.PerformanceRating}");
                }
                sb.AppendLine();
            }

            // Grid Layout Tests
            if (report.GridLayoutTests.Count > 0)
            {
                sb.AppendLine("GRID LAYOUT OPTIMIZATION");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine("Scenario        | Products | Columns | Spacing | Calc Time | Quality");
                sb.AppendLine("----------------|----------|---------|---------|-----------|--------");
                foreach (var test in report.GridLayoutTests)
                {
                    sb.AppendLine($"{test.Scenario,-15} | {test.ProductCount,8} | {test.ActualColumns,7} | {test.SpacingRatio,6:F2}x | {test.CalculationTime,8} ms | {test.SpacingQuality}");
                }
                sb.AppendLine();
            }

            // Memory Usage
            if (report.MemoryUsageTest != null)
            {
                sb.AppendLine("MEMORY USAGE ANALYSIS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine($"Baseline Memory: {report.MemoryUsageTest.BaselineMemory:F1} MB");
                sb.AppendLine($"Peak Memory: {report.MemoryUsageTest.PeakMemory:F1} MB");
                sb.AppendLine($"Post-Cleanup Memory: {report.MemoryUsageTest.PostCleanupMemory:F1} MB");
                sb.AppendLine($"Memory Leak Indicator: {report.MemoryUsageTest.MemoryLeakIndicator:F1} MB ({report.MemoryUsageTest.MemoryEfficiency})");
                sb.AppendLine();
            }

            // UI Responsiveness
            if (report.UIResponsivenessTest != null)
            {
                sb.AppendLine("UI RESPONSIVENESS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine($"Resize Response: {report.UIResponsivenessTest.ResizeResponseTime} ms");
                sb.AppendLine($"Scroll Performance: {report.UIResponsivenessTest.ScrollPerformance} ms");
                sb.AppendLine($"Overall Rating: {report.UIResponsivenessTest.ResponsivenessRating}");
                sb.AppendLine();
            }

            // Virtualization
            if (report.VirtualizationTest != null)
            {
                sb.AppendLine("VIRTUALIZATION EFFECTIVENESS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine($"Total Items: {report.VirtualizationTest.TotalItems:N0}");
                sb.AppendLine($"Realized Containers: {report.VirtualizationTest.RealizedContainers}");
                sb.AppendLine($"Virtualization Ratio: {report.VirtualizationTest.VirtualizationRatio:P2}");
                sb.AppendLine($"Effectiveness: {report.VirtualizationTest.VirtualizationEfficiency}");
                sb.AppendLine();
            }

            // Memory Leak Test
            if (report.MemoryLeakTest != null)
            {
                sb.AppendLine("MEMORY LEAK DETECTION");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                sb.AppendLine($"Initial Memory: {report.MemoryLeakTest.InitialMemory:F1} MB");
                sb.AppendLine($"Final Memory: {report.MemoryLeakTest.FinalMemory:F1} MB");
                sb.AppendLine($"Memory Growth: {report.MemoryLeakTest.MemoryGrowth:F1} MB");
                sb.AppendLine($"Leak Risk: {report.MemoryLeakTest.LeakRisk}");
                sb.AppendLine();
            }

            // Warnings
            if (report.Warnings.Count > 0)
            {
                sb.AppendLine("⚠️  WARNINGS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                foreach (var warning in report.Warnings)
                {
                    sb.AppendLine($"• {warning}");
                }
                sb.AppendLine();
            }

            // Recommendations
            if (report.Recommendations.Count > 0)
            {
                sb.AppendLine("💡 RECOMMENDATIONS");
                sb.AppendLine("─────────────────────────────────────────────────────────────");
                foreach (var recommendation in report.Recommendations)
                {
                    sb.AppendLine($"• {recommendation}");
                }
                sb.AppendLine();
            }

            sb.AppendLine("═══════════════════════════════════════════════════════════════");
            sb.AppendLine("                        END OF REPORT");
            sb.AppendLine("═══════════════════════════════════════════════════════════════");

            return sb.ToString();
        }

        /// <summary>
        /// Log performance summary to debug output
        /// </summary>
        private void LogPerformanceSummary(PerformanceAnalysisReport report)
        {
            Debug.WriteLine("═══════════════════════════════════════════════════════════════");
            Debug.WriteLine("              PERFORMANCE ANALYSIS SUMMARY");
            Debug.WriteLine("═══════════════════════════════════════════════════════════════");
            Debug.WriteLine($"Overall Score: {report.CalculateOverallScore():F1}/100 (Grade: {report.GetPerformanceGrade()})");
            
            if (report.LoadTimeTests.Count > 0)
            {
                var avgLoadTime = report.LoadTimeTests.Average(t => t.TotalLoadTime);
                Debug.WriteLine($"Average Load Time: {avgLoadTime:F0}ms");
            }

            if (report.MemoryUsageTest != null)
            {
                Debug.WriteLine($"Memory Efficiency: {report.MemoryUsageTest.MemoryEfficiency}");
            }

            if (report.VirtualizationTest != null)
            {
                Debug.WriteLine($"Virtualization: {report.VirtualizationTest.VirtualizationEfficiency} ({report.VirtualizationTest.VirtualizationRatio:P1} realized)");
            }

            Debug.WriteLine($"Warnings: {report.Warnings.Count}");
            Debug.WriteLine($"Recommendations: {report.Recommendations.Count}");
            Debug.WriteLine("═══════════════════════════════════════════════════════════════");
        }
    }
}
