using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Views.Dialogs
{
    public partial class ProductDetailsDialog : UserControl, INotifyPropertyChanged
    {
        private readonly SaleViewModel _viewModel;
        private readonly DatabaseService _dbService;
        private bool? _canCreateInvoices;
        
        // Properties for binding
        public Product Product { get; private set; }
        
        // Computed properties for the UI
        public string CategoryName => Product?.Category?.Name ?? "Uncategorized";
        
        public string PrimaryBarcode
        {
            get
            {
                if (Product?.Barcodes == null || !Product.Barcodes.Any())
                    return "N/A";
                    
                var barcode = Product.Barcodes.FirstOrDefault();
                return barcode?.Barcode ?? "N/A";
            }
        }
        
        public string PriceFormatted => $"{Product?.SellingPrice:N2} DA";
        
        public string StockText
        {
            get
            {
                if (Product == null) return "0 units";

                // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                var stockDecimal = Product.StockQuantity;
                var stockDisplay = Product.FormatStockQuantity(stockDecimal);

                // Add unit of measure if available
                string unit = Product.UnitOfMeasure?.Abbreviation;
                if (!string.IsNullOrEmpty(unit))
                {
                    return $"{stockDisplay} {unit}";
                }

                // For weight-based products, don't add "units" suffix
                if (Product.IsWeightBased)
                {
                    return stockDisplay;
                }
                else
                {
                    // For unit-based products, add "units" suffix if no specific unit
                    return $"{stockDisplay} units";
                }
            }
        }
        
        public string StockStatusText
        {
            get
            {
                if (Product == null) return "Unknown";
                
                if (Product.IsOutOfStock)
                    return "Out of Stock";
                else if (Product.IsLowStock)
                    return "Low Stock";
                else
                    return "In Stock";
            }
        }
        
        public PackIconKind StockStatusIcon
        {
            get
            {
                if (Product == null) return PackIconKind.HelpCircleOutline;
                
                if (Product.IsOutOfStock)
                    return PackIconKind.AlertCircle;
                else if (Product.IsLowStock)
                    return PackIconKind.AlertOutline;
                else
                    return PackIconKind.CheckCircleOutline;
            }
        }
        
        public Brush StockStatusColor
        {
            get
            {
                if (Product == null) return Brushes.Gray;
                
                if (Product.IsOutOfStock)
                    return new SolidColorBrush(Color.FromRgb(0xF4, 0x43, 0x36)); // Red
                else if (Product.IsLowStock)
                    return new SolidColorBrush(Color.FromRgb(0xFF, 0xA5, 0x00)); // Orange
                else
                    return new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)); // Green
            }
        }
        
        public Brush StockStatusBackground
        {
            get
            {
                if (Product == null) return new SolidColorBrush(Color.FromArgb(20, 0, 0, 0));
                
                if (Product.IsOutOfStock)
                    return new SolidColorBrush(Color.FromArgb(20, 0xF4, 0x43, 0x36)); // Red with alpha
                else if (Product.IsLowStock)
                    return new SolidColorBrush(Color.FromArgb(20, 0xFF, 0xA5, 0x00)); // Orange with alpha
                else
                    return new SolidColorBrush(Color.FromArgb(20, 0x4C, 0xAF, 0x50)); // Green with alpha
            }
        }
        
        public bool HasDescription => !string.IsNullOrEmpty(Product?.Description);

        public bool CanAddToCart => Product != null && !Product.IsOutOfStock;

        public bool CanCreateInvoices
        {
            get
            {
                if (_canCreateInvoices.HasValue)
                    return _canCreateInvoices.Value;

                try
                {
                    // Use the established pattern: Get DatabaseService first, then create UserPermissionsService
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                    if (permissionsService != null)
                    {
                        // TEMPORARY: Grant permissions when no user is logged in (for testing only)
                        // TODO: Remove this block when proper user authentication is implemented
                        if (permissionsService.CurrentUser == null)
                        {
                            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                            #if DEBUG && VERBOSE_LOGGING
                            System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] No user logged in, granting permissions for testing");
                            #endif
                            _canCreateInvoices = true;
                            return true;
                        }

                        bool canCreate = permissionsService.CanCreateDraftInvoices() || permissionsService.CanCreateFullInvoices();
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] CanCreateInvoices: {canCreate} for user: {permissionsService.CurrentUser?.Username}");

                        _canCreateInvoices = canCreate;
                        return canCreate;
                    }
                    else
                    {
                        _canCreateInvoices = false;
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Error checking invoice permissions: {ex.Message}");
                    _canCreateInvoices = false;
                    return false;
                }
            }
        }

        public ProductDetailsDialog(Product product, SaleViewModel viewModel)
        {
            InitializeComponent();
            
            Product = product;
            _viewModel = viewModel;
            _dbService = new DatabaseService();
            
            // Set DataContext to this for bindings
            DataContext = this;
            
            // Subscribe to stock changes
            SaleViewModel.ProductStockChanged += OnProductStockChanged;
            
            // Unsubscribe when control is unloaded
            this.Unloaded += (s, e) =>
            {
                SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            };

            // Initialize permissions
            RefreshPermissions();
        }

        private void RefreshPermissions()
        {
            _canCreateInvoices = null; // Clear cache
            OnPropertyChanged(nameof(CanCreateInvoices));
        }
        
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.Close("SalesDialog");
        }


        
        private void AddToCartButton_Click(object sender, RoutedEventArgs e)
        {
            if (Product != null && !Product.IsOutOfStock)
            {
                // ✅ BARCODE SCANNER FIX: Use AddToCartCommand for consistency with product cards
                if (_viewModel.AddToCartCommand.CanExecute(Product))
                {
                    _viewModel.AddToCartCommand.Execute(Product);
                }
                DialogHost.Close("SalesDialog");
            }
        }

        private async void CreateInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Product != null && sender is FrameworkElement element && element.Tag is Product product)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] CreateInvoiceButton_Click called for product: {product.Name}");

                    // Get required services using the established pattern
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Services created - dbService: {dbService != null}, permissionsService: {permissionsService != null}");

                    if (permissionsService == null || dbService == null)
                    {
                        System.Windows.MessageBox.Show("Required services not available. Please restart the application.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    // Check permissions (with temporary override for testing)
                    bool canCreateInvoices = permissionsService.CanCreateDraftInvoices() || permissionsService.CanCreateFullInvoices();
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Initial permission check: {canCreateInvoices}, CurrentUser: {permissionsService.CurrentUser?.Username ?? "NULL"}");

                    // TEMPORARY: For testing purposes, allow invoice creation even without login
                    // TODO: Remove this after testing and ensure proper user authentication
                    if (!canCreateInvoices && permissionsService.CurrentUser == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] No user logged in, enabling test mode for invoice creation");
                        canCreateInvoices = true; // Enable for testing
                    }

                    if (!canCreateInvoices)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Permission denied, showing access denied message");
                        System.Windows.MessageBox.Show("You don't have permission to create invoices.", "Access Denied",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Permission granted, proceeding with invoice creation");
                    // Create and show confirmation dialog
                    var dbServiceConcrete = dbService as POSSystem.Services.DatabaseService
                        ?? App.ServiceProvider?.GetService(typeof(POSSystem.Services.DatabaseService)) as POSSystem.Services.DatabaseService;
                    var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbServiceConcrete);
                    var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                    // Close current dialog first
                    DialogHost.Close("SalesDialog");

                    // Show invoice confirmation dialog
                    var result = await DialogHost.Show(confirmationDialog, "SalesDialog");

                    if (result is POSSystem.ViewModels.ProductToInvoiceResult invoiceResult && invoiceResult.Confirmed)
                    {
                        if (invoiceResult.CreateFullInvoice)
                        {
                            // Admin user - create full invoice directly
                            await CreateFullInvoiceFromProduct(invoiceResult);
                        }
                        else
                        {
                            // Non-admin user - create draft invoice
                            await CreateDraftInvoiceFromProduct(invoiceResult);
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Product-to-invoice workflow completed for: {product.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Error in CreateInvoiceButton_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error creating invoice from product: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task CreateFullInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                // Implementation would go here - for now, show success message
                System.Windows.MessageBox.Show($"Full invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})",
                    "Invoice Created", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Error creating full invoice: {ex.Message}");
                throw;
            }
        }

        private async System.Threading.Tasks.Task CreateDraftInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                // Implementation would go here - for now, show success message
                System.Windows.MessageBox.Show($"Draft invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})",
                    "Draft Invoice Created", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DETAILS] Error creating draft invoice: {ex.Message}");
                throw;
            }
        }

        private void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            if (Product != null && Product.Id == e.ProductId)
            {
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Product.StockQuantity = e.NewStockQuantity;
                    
                    // Notify all relevant property changes
                    DataContext = null;  // Force full refresh
                    DataContext = this;
                    
                    // Notify individual properties
                    OnPropertyChanged(nameof(Product));
                    OnPropertyChanged(nameof(StockText));
                    OnPropertyChanged(nameof(StockStatusText));
                    OnPropertyChanged(nameof(StockStatusIcon));
                    OnPropertyChanged(nameof(StockStatusColor));
                    OnPropertyChanged(nameof(StockStatusBackground));
                });
            }
        }

        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 