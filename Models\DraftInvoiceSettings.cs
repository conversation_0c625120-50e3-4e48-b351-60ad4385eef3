using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    /// <summary>
    /// Configuration settings for the two-tier draft invoice system
    /// </summary>
    public class DraftInvoiceSettings
    {
        public DraftInvoiceSettings()
        {
            // Set default values
            ExpirationDays = 7;
            AutoNotifyAdmins = true;
            AllowNonAdminCustomerSelection = true;
            RequireReasonForDraft = false;
            MaxDraftItemsPerInvoice = 50;
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Number of days after which draft invoices expire
        /// </summary>
        [Range(1, 365)]
        public int ExpirationDays { get; set; }

        /// <summary>
        /// Whether to automatically notify admins when new drafts are created
        /// </summary>
        public bool AutoNotifyAdmins { get; set; }

        /// <summary>
        /// Whether non-admin users can select customers when creating draft invoices
        /// </summary>
        public bool AllowNonAdminCustomerSelection { get; set; }

        /// <summary>
        /// Whether to require a reason when creating draft invoices
        /// </summary>
        public bool RequireReasonForDraft { get; set; }

        /// <summary>
        /// Maximum number of items allowed per draft invoice
        /// </summary>
        [Range(1, 1000)]
        public int MaxDraftItemsPerInvoice { get; set; }

        /// <summary>
        /// Whether to send email notifications (future feature)
        /// </summary>
        public bool EnableEmailNotifications { get; set; }

        /// <summary>
        /// Email template for draft creation notifications (future feature)
        /// </summary>
        [MaxLength(1000)]
        public string DraftCreatedEmailTemplate { get; set; }

        /// <summary>
        /// Email template for draft completion notifications (future feature)
        /// </summary>
        [MaxLength(1000)]
        public string DraftCompletedEmailTemplate { get; set; }

        /// <summary>
        /// Whether to automatically archive expired drafts
        /// </summary>
        public bool AutoArchiveExpiredDrafts { get; set; }

        /// <summary>
        /// Number of days to keep archived drafts before permanent deletion
        /// </summary>
        [Range(1, 3650)] // Up to 10 years
        public int ArchiveRetentionDays { get; set; }

        /// <summary>
        /// Whether to require admin approval for high-value draft invoices
        /// </summary>
        public bool RequireApprovalForHighValue { get; set; }

        /// <summary>
        /// Threshold amount above which admin approval is required
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal HighValueThreshold { get; set; }

        // Timestamps
        [Required]
        public DateTime CreatedAt { get; set; }

        [Required]
        public DateTime UpdatedAt { get; set; }

        // Computed properties for UI
        [NotMapped]
        public string ExpirationDisplayText => ExpirationDays == 1 ? "1 day" : $"{ExpirationDays} days";

        [NotMapped]
        public string MaxItemsDisplayText => MaxDraftItemsPerInvoice == 1 ? "1 item" : $"{MaxDraftItemsPerInvoice} items";

        [NotMapped]
        public string HighValueThresholdDisplayText => RequireApprovalForHighValue ? 
            $"Above {HighValueThreshold:C}" : "Disabled";

        // Helper methods
        public bool IsDraftExpired(DateTime draftCreatedAt)
        {
            return DateTime.Now.Subtract(draftCreatedAt).TotalDays > ExpirationDays;
        }

        public DateTime GetExpirationDate(DateTime draftCreatedAt)
        {
            return draftCreatedAt.AddDays(ExpirationDays);
        }

        public bool RequiresHighValueApproval(decimal invoiceAmount)
        {
            return RequireApprovalForHighValue && invoiceAmount > HighValueThreshold;
        }

        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.Now;
        }

        // Static method to get default settings
        public static DraftInvoiceSettings GetDefaultSettings()
        {
            return new DraftInvoiceSettings
            {
                ExpirationDays = 7,
                AutoNotifyAdmins = true,
                AllowNonAdminCustomerSelection = true,
                RequireReasonForDraft = false,
                MaxDraftItemsPerInvoice = 50,
                EnableEmailNotifications = false,
                AutoArchiveExpiredDrafts = true,
                ArchiveRetentionDays = 90,
                RequireApprovalForHighValue = false,
                HighValueThreshold = 1000.00m
            };
        }

        // Validation method
        public bool IsValid(out string errorMessage)
        {
            errorMessage = string.Empty;

            if (ExpirationDays < 1 || ExpirationDays > 365)
            {
                errorMessage = "Expiration days must be between 1 and 365.";
                return false;
            }

            if (MaxDraftItemsPerInvoice < 1 || MaxDraftItemsPerInvoice > 1000)
            {
                errorMessage = "Maximum draft items must be between 1 and 1000.";
                return false;
            }

            if (ArchiveRetentionDays < 1 || ArchiveRetentionDays > 3650)
            {
                errorMessage = "Archive retention days must be between 1 and 3650.";
                return false;
            }

            if (RequireApprovalForHighValue && HighValueThreshold <= 0)
            {
                errorMessage = "High value threshold must be greater than 0 when approval is required.";
                return false;
            }

            return true;
        }
    }
}
