using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.ViewModels
{
    public class DiscountPermissionsViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private ObservableCollection<Role> _roles;
        private ObservableCollection<DiscountType> _discountTypes;
        private ObservableCollection<DiscountPermissionViewModel> _permissions;
        private Role _selectedRole;
        private bool _isLoading;

        public DiscountPermissionsViewModel(DatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            
            SaveCommand = new RelayCommand(async _ => await SavePermissionsAsync());
            
            // Initialize collections
            Roles = new ObservableCollection<Role>();
            DiscountTypes = new ObservableCollection<DiscountType>();
            Permissions = new ObservableCollection<DiscountPermissionViewModel>();
            
            LoadData();
        }

        public ObservableCollection<Role> Roles
        {
            get => _roles;
            set
            {
                _roles = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<DiscountType> DiscountTypes
        {
            get => _discountTypes;
            set
            {
                _discountTypes = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<DiscountPermissionViewModel> Permissions
        {
            get => _permissions;
            set
            {
                _permissions = value;
                OnPropertyChanged();
            }
        }

        public Role SelectedRole
        {
            get => _selectedRole;
            set
            {
                _selectedRole = value;
                OnPropertyChanged();
                if (_selectedRole != null)
                {
                    LoadPermissionsForRole(_selectedRole.Id);
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public ICommand SaveCommand { get; }

        private async void LoadData()
        {
            IsLoading = true;
            try
            {
                // Load roles
                var roles = _dbService.GetAllRoles();
                Roles.Clear();
                foreach (var role in roles)
                {
                    Roles.Add(role);
                }

                // Load discount types
                using (var context = new POSDbContext())
                {
                    var discountTypes = await context.DiscountTypes.ToListAsync();
                    DiscountTypes.Clear();
                    foreach (var type in discountTypes)
                    {
                        DiscountTypes.Add(type);
                    }
                }

                // Select the first role if available
                if (Roles.Any())
                {
                    SelectedRole = Roles.First();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading discount permission data: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"Error loading discount permission data: {ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void LoadPermissionsForRole(int roleId)
        {
            IsLoading = true;
            try
            {
                using (var context = new POSDbContext())
                {
                    // Get existing permissions for this role
                    var existingPermissions = context.DiscountPermissions
                        .Include(dp => dp.DiscountType)
                        .Where(dp => dp.RoleId == roleId)
                        .ToList();

                    Permissions.Clear();

                    // For each discount type, add a permission model
                    foreach (var discountType in DiscountTypes)
                    {
                        // Look for an existing permission for this type
                        var existingPermission = existingPermissions
                            .FirstOrDefault(p => p.DiscountTypeId == discountType.Id);

                        // If no permission exists, create a default one
                        if (existingPermission == null)
                        {
                            existingPermission = new DiscountPermission
                            {
                                RoleId = roleId,
                                DiscountTypeId = discountType.Id,
                                MaxPercentage = 20,
                                MaxFixedAmount = 100,
                                MinPricePercentage = 70,
                                RequiresApproval = true,
                                ApprovalThreshold = 50,
                                IsActive = false,
                                CreatedAt = DateTime.Now,
                                DiscountType = discountType
                            };
                        }

                        Permissions.Add(new DiscountPermissionViewModel(existingPermission));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading permissions for role: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"Error loading permissions for role: {ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SavePermissionsAsync()
        {
            if (SelectedRole == null)
            {
                System.Windows.MessageBox.Show(
                    "Please select a role before saving permissions.",
                    "No Role Selected",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return;
            }

            IsLoading = true;
            try
            {
                using (var context = new POSDbContext())
                {
                    // Get existing permissions for this role
                    var existingPermissions = await context.DiscountPermissions
                        .Where(dp => dp.RoleId == SelectedRole.Id)
                        .ToListAsync();

                    // For each permission in our view model
                    foreach (var permissionVM in Permissions)
                    {
                        var permission = permissionVM.ToModel();

                        // Find existing permission or add new one
                        var existingPermission = existingPermissions
                            .FirstOrDefault(p => p.DiscountTypeId == permission.DiscountTypeId);

                        if (existingPermission != null)
                        {
                            // Update existing
                            existingPermission.MaxPercentage = permission.MaxPercentage;
                            existingPermission.MaxFixedAmount = permission.MaxFixedAmount;
                            existingPermission.MinPricePercentage = permission.MinPricePercentage;
                            existingPermission.RequiresApproval = permission.RequiresApproval;
                            existingPermission.ApprovalThreshold = permission.ApprovalThreshold;
                            existingPermission.IsActive = permission.IsActive;
                            existingPermission.UpdatedAt = DateTime.Now;
                        }
                        else
                        {
                            // Add new
                            permission.RoleId = SelectedRole.Id;
                            permission.CreatedAt = DateTime.Now;
                            context.DiscountPermissions.Add(permission);
                        }
                    }

                    await context.SaveChangesAsync();
                    System.Windows.MessageBox.Show(
                        "Discount permissions saved successfully.",
                        "Success",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"Error saving discount permissions: {ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Error saving permissions: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }

    public class DiscountPermissionViewModel : INotifyPropertyChanged
    {
        private int _id;
        private int _roleId;
        private int _discountTypeId;
        private decimal _maxPercentage;
        private decimal _maxFixedAmount;
        private decimal _minPricePercentage;
        private bool _requiresApproval;
        private decimal _approvalThreshold;
        private bool _isActive;
        private string _discountTypeName;

        public DiscountPermissionViewModel(DiscountPermission model)
        {
            Id = model.Id;
            RoleId = model.RoleId;
            DiscountTypeId = model.DiscountTypeId;
            MaxPercentage = model.MaxPercentage ?? 0;
            MaxFixedAmount = model.MaxFixedAmount ?? 0;
            MinPricePercentage = model.MinPricePercentage ?? 0;
            RequiresApproval = model.RequiresApproval;
            ApprovalThreshold = model.ApprovalThreshold ?? 0;
            IsActive = model.IsActive;
            DiscountTypeName = GetLocalizedDiscountTypeName(model.DiscountType?.Name ?? $"Discount Type {model.DiscountTypeId}");
        }

        private string GetLocalizedDiscountTypeName(string originalName)
        {
            try
            {
                // Get the localized string from resources
                switch (originalName)
                {
                    case "Percentage":
                        return Application.Current.FindResource("Percentage") as string ?? originalName;
                    case "Fixed Amount":
                        return Application.Current.FindResource("FixedAmount") as string ?? originalName;
                    case "Price Override":
                        return Application.Current.FindResource("PriceOverride") as string ?? originalName;
                    default:
                        return originalName;
                }
            }
            catch
            {
                // Fallback to original name if resource lookup fails
                return originalName;
            }
        }

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public int RoleId
        {
            get => _roleId;
            set
            {
                _roleId = value;
                OnPropertyChanged();
            }
        }

        public int DiscountTypeId
        {
            get => _discountTypeId;
            set
            {
                _discountTypeId = value;
                OnPropertyChanged();
            }
        }

        public decimal MaxPercentage
        {
            get => _maxPercentage;
            set
            {
                if (value >= 0 && value <= 100)
                {
                    _maxPercentage = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal MaxFixedAmount
        {
            get => _maxFixedAmount;
            set
            {
                if (value >= 0)
                {
                    _maxFixedAmount = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal MinPricePercentage
        {
            get => _minPricePercentage;
            set
            {
                if (value >= 0 && value <= 100)
                {
                    _minPricePercentage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequiresApproval
        {
            get => _requiresApproval;
            set
            {
                _requiresApproval = value;
                OnPropertyChanged();
            }
        }

        public decimal ApprovalThreshold
        {
            get => _approvalThreshold;
            set
            {
                if (value >= 0)
                {
                    _approvalThreshold = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged();
            }
        }

        public string DiscountTypeName
        {
            get => _discountTypeName;
            set
            {
                _discountTypeName = value;
                OnPropertyChanged();
            }
        }

        public DiscountPermission ToModel()
        {
            return new DiscountPermission
            {
                Id = Id,
                RoleId = RoleId,
                DiscountTypeId = DiscountTypeId,
                MaxPercentage = MaxPercentage,
                MaxFixedAmount = MaxFixedAmount,
                MinPricePercentage = MinPricePercentage,
                RequiresApproval = RequiresApproval,
                ApprovalThreshold = ApprovalThreshold,
                IsActive = IsActive
            };
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }
} 