using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                // By default, true = Visible, false = Collapsed
                bool isInverted = false;
                
                // If parameter is "invert", flip the logic
                if (parameter is string param && param == "invert")
                {
                    isInverted = true;
                }
                
                return (boolValue != isInverted) ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Collapsed;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                // By default, Visible = true, Collapsed = false
                bool isInverted = false;
                
                // If parameter is "invert", flip the logic
                if (parameter is string param && param == "invert")
                {
                    isInverted = true;
                }
                
                return (visibility == Visibility.Visible) != isInverted;
            }
            
            return false;
        }
    }
} 