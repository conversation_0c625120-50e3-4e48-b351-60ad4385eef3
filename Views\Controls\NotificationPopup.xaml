<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Controls.NotificationPopup"
            xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
            xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
            mc:Ignorable="d"
            d:DesignHeight="450" d:DesignWidth="350"
            Background="Transparent">

    <UserControl.Resources>
        <!-- Show Animation -->
        <Storyboard x:Key="ShowPopup">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1"
                           Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                           From="-20" To="0"
                           Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Hide Animation -->
        <Storyboard x:Key="HidePopup">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0"
                           Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                           From="0" To="-20"
                           Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <Popup x:Name="PART_Popup"
               AllowsTransparency="True"
               Placement="Bottom"
               StaysOpen="False">
            <Grid x:Name="PART_Container"
                  RenderTransformOrigin="0.5,0.5">
                <Grid.RenderTransform>
                    <TranslateTransform/>
                </Grid.RenderTransform>
                
                <Border Background="{DynamicResource MaterialDesignPaper}"
                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Width="350"
                        Effect="{DynamicResource MaterialDesignElevationShadow2}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Grid Grid.Row="0" 
                              Background="{DynamicResource MaterialDesignBackground}" 
                              Height="48">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="{DynamicResource Notifications}"
                                     Margin="16,0,0,0"
                                     VerticalAlignment="Center"
                                     FontWeight="SemiBold"
                                     FontSize="16"
                                     Foreground="{DynamicResource MaterialDesignBody}"/>
                            
                            <Button Grid.Column="1"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Height="32"
                                    Margin="8,0"
                                    Click="MarkAllAsRead_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CheckAll"
                                                           Width="16"
                                                           Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="{DynamicResource MarkAllAsRead}"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Notifications List -->
                        <ScrollViewer Grid.Row="1"
                                     VerticalScrollBarVisibility="Auto"
                                     MaxHeight="400">
                            <ItemsControl ItemsSource="{Binding}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{DynamicResource MaterialDesignPaper}"
                                                BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                BorderThickness="0,0,0,1"
                                                Padding="16">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Notification Icon -->
                                                <Border Width="40"
                                                        Height="40"
                                                        CornerRadius="20"
                                                        Background="{Binding IconBackground}">
                                                    <materialDesign:PackIcon Kind="{Binding IconKind}"
                                                                           Width="20"
                                                                           Height="20"
                                                                           Foreground="White"/>
                                                </Border>

                                                <!-- Notification Content -->
                                                <StackPanel Grid.Column="1" 
                                                          Margin="12,0,0,0">
                                                    <TextBlock Text="{Binding Title}"
                                                             FontWeight="SemiBold"
                                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                                    <TextBlock Text="{Binding Message}"
                                                             TextWrapping="Wrap"
                                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                             Margin="0,4,0,0"/>
                                                    <TextBlock Text="{Binding TimeAgo}"
                                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                             FontSize="11"
                                                             Margin="0,4,0,0"/>
                                                </StackPanel>

                                                <!-- Action Button -->
                                                <Button Grid.Column="2"
                                                        Style="{StaticResource MaterialDesignFlatButton}"
                                                        Height="32"
                                                        Margin="8,0,0,0"
                                                        Command="{Binding ActionCommand}">
                                                    <materialDesign:PackIcon Kind="ArrowRight"/>
                                                </Button>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Footer -->
                        <Button Grid.Row="2"
                                Style="{StaticResource MaterialDesignFlatButton}"
                                Height="48"
                                Click="ViewAll_Click">
                            <TextBlock Text="{DynamicResource ViewAllNotifications}"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Button>
                    </Grid>
                </Border>
            </Grid>
        </Popup>
    </Grid>
</UserControl> 