using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.ViewModels;
using POSSystem.Services;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.MemoryValidation
{
    /// <summary>
    /// ✅ NEW: Memory usage validation tests for dashboard optimization
    /// Validates memory leaks, disposal patterns, and memory efficiency
    /// </summary>
    [TestClass]
    public class DashboardMemoryTests
    {
        private POSDbContext _context;
        private DatabaseService _databaseService;

        [TestInitialize]
        public void Setup()
        {
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _databaseService = new DatabaseService(_context);
            
            SeedTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _context?.Dispose();
        }

        /// <summary>
        /// ✅ MEMORY TEST: Dashboard ViewModel disposal prevents memory leaks
        /// </summary>
        [TestMethod]
        public void DashboardViewModel_Disposal_PreventsMemoryLeaks()
        {
            // Arrange
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var initialMemory = GC.GetTotalMemory(false);
            var viewModels = new List<WeakReference>();

            // Act - Create and dispose multiple ViewModels
            for (int i = 0; i < 10; i++)
            {
                var viewModel = new DashboardViewModel(_databaseService);
                viewModels.Add(new WeakReference(viewModel));
                
                // Simulate usage
                _ = viewModel.LoadDashboardDataAsync();
                
                // Dispose properly
                viewModel.Dispose();
                viewModel = null;
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(false);
            var memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024); // Convert to MB

            // Assert
            Assert.IsTrue(memoryIncrease < 50, 
                $"Memory increased by {memoryIncrease}MB after creating/disposing 10 ViewModels, expected < 50MB");

            // Check that ViewModels were actually garbage collected
            var aliveViewModels = viewModels.Count(wr => wr.IsAlive);
            Assert.IsTrue(aliveViewModels <= 2, 
                $"{aliveViewModels} ViewModels still alive after disposal, expected <= 2");

            Debug.WriteLine($"✅ Memory test: {memoryIncrease}MB increase, {aliveViewModels}/10 ViewModels still alive");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Chart series disposal prevents memory leaks
        /// </summary>
        [TestMethod]
        public async Task ChartSeries_Disposal_PreventsMemoryLeaks()
        {
            // Arrange
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var initialMemory = GC.GetTotalMemory(false);

            // Act - Create and dispose ViewModels with chart data
            for (int i = 0; i < 5; i++)
            {
                using var viewModel = new DashboardViewModel(_databaseService);
                
                // Load chart data
                await viewModel.LoadDashboardDataAsync();
                await viewModel.LoadSalesTrendDataAsync();
                await viewModel.LoadProductPerformanceAsync();
                
                // Charts should be automatically disposed when ViewModel is disposed
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(false);
            var memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024);

            // Assert
            Assert.IsTrue(memoryIncrease < 30, 
                $"Memory increased by {memoryIncrease}MB after chart operations, expected < 30MB");

            Debug.WriteLine($"✅ Chart memory test: {memoryIncrease}MB increase");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Cache management prevents unbounded growth
        /// </summary>
        [TestMethod]
        public async Task Cache_Management_PreventsUnboundedGrowth()
        {
            // Arrange
            using var viewModel = new DashboardViewModel(_databaseService);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var initialMemory = GC.GetTotalMemory(false);

            // Act - Perform many operations that use caching
            for (int i = 0; i < 20; i++)
            {
                var startDate = DateTime.Today.AddDays(-i * 7);
                var endDate = DateTime.Today.AddDays(-(i * 7) + 6);
                
                viewModel.StartDate = startDate;
                viewModel.EndDate = endDate;
                
                await viewModel.LoadDashboardDataAsync();
                
                // Small delay to allow cache operations
                await Task.Delay(10);
            }

            var finalMemory = GC.GetTotalMemory(false);
            var memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024);

            // Assert
            Assert.IsTrue(memoryIncrease < 100, 
                $"Memory increased by {memoryIncrease}MB with extensive caching, expected < 100MB");

            Debug.WriteLine($"✅ Cache memory test: {memoryIncrease}MB increase after 20 cache operations");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Collection cleanup prevents memory leaks
        /// </summary>
        [TestMethod]
        public async Task Collections_Cleanup_PreventsMemoryLeaks()
        {
            // Arrange
            var memorySnapshots = new List<long>();
            
            // Act - Create, use, and dispose ViewModels while monitoring memory
            for (int iteration = 0; iteration < 5; iteration++)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var beforeMemory = GC.GetTotalMemory(false);

                using var viewModel = new DashboardViewModel(_databaseService);
                
                // Load data into collections
                await viewModel.LoadDashboardDataAsync();
                await viewModel.LoadProductPerformanceAsync();
                
                // Simulate user interactions that populate collections
                for (int i = 0; i < 10; i++)
                {
                    viewModel.StartDate = DateTime.Today.AddDays(-i * 5);
                    viewModel.EndDate = DateTime.Today.AddDays(-i * 5 + 4);
                    await viewModel.LoadDashboardDataAsync();
                }

                // ViewModel disposal should clean up all collections
                
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var afterMemory = GC.GetTotalMemory(false);
                memorySnapshots.Add(afterMemory - beforeMemory);
            }

            // Assert - Memory usage should not grow significantly between iterations
            var firstIteration = memorySnapshots[0];
            var lastIteration = memorySnapshots[^1];
            var memoryGrowth = (lastIteration - firstIteration) / (1024.0 * 1024.0);

            Assert.IsTrue(memoryGrowth < 20, 
                $"Memory grew by {memoryGrowth:F1}MB across iterations, expected < 20MB");

            Debug.WriteLine($"✅ Collection cleanup test: Memory growth {memoryGrowth:F1}MB across 5 iterations");
            Debug.WriteLine($"   Memory per iteration: {string.Join(", ", memorySnapshots.Select(m => $"{m / (1024 * 1024)}MB"))}");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Event handler unsubscription prevents memory leaks
        /// </summary>
        [TestMethod]
        public void EventHandlers_Unsubscription_PreventsMemoryLeaks()
        {
            // Arrange
            var viewModelReferences = new List<WeakReference>();
            var serviceReferences = new List<WeakReference>();

            // Act - Create ViewModels that subscribe to service events
            for (int i = 0; i < 10; i++)
            {
                var service = new DatabaseService(_context);
                var viewModel = new DashboardViewModel(service);
                
                viewModelReferences.Add(new WeakReference(viewModel));
                serviceReferences.Add(new WeakReference(service));
                
                // Dispose ViewModel (should unsubscribe from events)
                viewModel.Dispose();
                
                // Clear references
                viewModel = null;
                service = null;
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            // Assert - Most objects should be garbage collected
            var aliveViewModels = viewModelReferences.Count(wr => wr.IsAlive);
            var aliveServices = serviceReferences.Count(wr => wr.IsAlive);

            Assert.IsTrue(aliveViewModels <= 2, 
                $"{aliveViewModels} ViewModels still alive, expected <= 2 (event handlers may prevent GC)");
            
            Assert.IsTrue(aliveServices <= 3, 
                $"{aliveServices} Services still alive, expected <= 3");

            Debug.WriteLine($"✅ Event handler test: {aliveViewModels}/10 ViewModels alive, {aliveServices}/10 Services alive");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Large dataset operations don't cause memory spikes
        /// </summary>
        [TestMethod]
        public async Task LargeDataset_Operations_NoMemorySpikes()
        {
            // Arrange
            SeedLargeDataset();
            var memoryReadings = new List<long>();
            
            using var viewModel = new DashboardViewModel(_databaseService);

            // Act - Monitor memory during large dataset operations
            for (int i = 0; i < 10; i++)
            {
                var beforeOperation = GC.GetTotalMemory(false);
                
                // Perform memory-intensive operation
                viewModel.StartDate = DateTime.Today.AddDays(-365);
                viewModel.EndDate = DateTime.Now;
                await viewModel.LoadDashboardDataAsync();
                
                var afterOperation = GC.GetTotalMemory(false);
                memoryReadings.Add(afterOperation - beforeOperation);
                
                // Clear cache to reset for next iteration
                viewModel.ClearCache();
                
                // Force garbage collection between operations
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }

            // Assert - Memory usage should be consistent
            var averageMemoryUsage = memoryReadings.Average() / (1024 * 1024); // MB
            var maxMemoryUsage = memoryReadings.Max() / (1024 * 1024); // MB
            var memoryVariance = memoryReadings.Max() - memoryReadings.Min();

            Assert.IsTrue(averageMemoryUsage < 150, 
                $"Average memory usage {averageMemoryUsage:F1}MB, expected < 150MB");
            
            Assert.IsTrue(maxMemoryUsage < 200, 
                $"Max memory usage {maxMemoryUsage:F1}MB, expected < 200MB");

            Debug.WriteLine($"✅ Large dataset memory test: Avg {averageMemoryUsage:F1}MB, Max {maxMemoryUsage:F1}MB");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Memory usage monitoring and alerts
        /// </summary>
        [TestMethod]
        public async Task MemoryMonitoring_DetectsMemoryIssues()
        {
            // Arrange
            var memoryAlerts = new List<string>();
            using var viewModel = new DashboardViewModel(_databaseService);

            // Monitor memory usage
            var initialMemory = GC.GetTotalMemory(false);

            // Act - Perform operations that might cause memory issues
            for (int i = 0; i < 5; i++)
            {
                await viewModel.LoadDashboardDataAsync();
                await viewModel.LoadProductPerformanceAsync();
                
                var currentMemory = GC.GetTotalMemory(false);
                var memoryUsage = (currentMemory - initialMemory) / (1024 * 1024);
                
                if (memoryUsage > 100) // Alert threshold
                {
                    memoryAlerts.Add($"High memory usage detected: {memoryUsage}MB at iteration {i}");
                }
            }

            // Assert
            Assert.IsTrue(memoryAlerts.Count <= 2, 
                $"Too many memory alerts: {memoryAlerts.Count}, expected <= 2");

            if (memoryAlerts.Count > 0)
            {
                Debug.WriteLine($"⚠️ Memory alerts: {string.Join("; ", memoryAlerts)}");
            }
            else
            {
                Debug.WriteLine("✅ Memory monitoring: No memory alerts detected");
            }
        }

        /// <summary>
        /// ✅ HELPER: Seed test data
        /// </summary>
        private void SeedTestData()
        {
            // Add basic test data
            for (int i = 1; i <= 50; i++)
            {
                _context.Products.Add(new Models.Product
                {
                    Id = i,
                    Name = $"Product {i}",
                    SKU = $"SKU{i:D3}",
                    SellingPrice = 10 + i,
                    PurchasePrice = 5 + i,
                    StockQuantity = 100 - i,
                    IsActive = true
                });
            }

            var random = new Random(42);
            for (int i = 1; i <= 200; i++)
            {
                _context.Sales.Add(new Models.Sale
                {
                    Id = i,
                    SaleDate = DateTime.Today.AddDays(-random.Next(0, 30)),
                    GrandTotal = 50 + random.Next(0, 100),
                    Subtotal = 45 + random.Next(0, 90),
                    Status = "Completed",
                    PaymentStatus = "Paid"
                });
            }

            _context.SaveChanges();
        }

        /// <summary>
        /// ✅ HELPER: Seed large dataset for memory testing
        /// </summary>
        private void SeedLargeDataset()
        {
            var random = new Random(42);
            
            // Add more products
            for (int i = 51; i <= 500; i++)
            {
                _context.Products.Add(new Models.Product
                {
                    Id = i,
                    Name = $"Product {i}",
                    SKU = $"SKU{i:D3}",
                    SellingPrice = 10 + (i % 100),
                    PurchasePrice = 5 + (i % 50),
                    StockQuantity = 100 - (i % 30),
                    IsActive = true
                });
            }

            // Add more sales
            for (int i = 201; i <= 2000; i++)
            {
                _context.Sales.Add(new Models.Sale
                {
                    Id = i,
                    SaleDate = DateTime.Today.AddDays(-random.Next(0, 365)),
                    GrandTotal = 25 + random.Next(0, 200),
                    Subtotal = 20 + random.Next(0, 180),
                    Status = "Completed",
                    PaymentStatus = random.Next(0, 10) > 8 ? "Unpaid" : "Paid"
                });
            }

            _context.SaveChanges();
        }
    }
}
