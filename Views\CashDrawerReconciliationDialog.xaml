<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CashDrawerReconciliationDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="600">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Cash Drawer Reconciliation"
                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                  Margin="0,0,0,16"/>

        <!-- Summary Section -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="Opening Balance:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="0" Grid.Column="0"/>
            <TextBlock x:Name="OpeningBalanceText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="0" Grid.Column="1"
                      FontWeight="Bold"/>

            <TextBlock Text="Total Sales:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="1" Grid.Column="0"
                      Margin="0,8"/>
            <TextBlock x:Name="TotalSalesText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="1" Grid.Column="1"
                      Margin="0,8"
                      FontWeight="Bold"/>

            <TextBlock Text="Total Payouts:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="2" Grid.Column="0"/>
            <TextBlock x:Name="TotalPayoutsText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="2" Grid.Column="1"
                      FontWeight="Bold"/>

            <TextBlock Text="Expected Balance:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="3" Grid.Column="0"
                      Margin="0,8"/>
            <TextBlock x:Name="ExpectedBalanceText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="3" Grid.Column="1"
                      Margin="0,8"
                      FontWeight="Bold"/>
        </Grid>

        <!-- Cash Count Section -->
        <GroupBox Grid.Row="2"
                  Header="Cash Count"
                  Style="{StaticResource MaterialDesignGroupBox}"
                  Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="100"/>
                </Grid.ColumnDefinitions>

                <!-- Headers -->
                <TextBlock Text="Denomination"
                          Grid.Row="0" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          FontWeight="Bold"/>
                <TextBlock Text="Count"
                          Grid.Row="0" Grid.Column="1"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          FontWeight="Bold"/>
                <TextBlock Text="Total"
                          Grid.Row="0" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          FontWeight="Bold"/>

                <!-- Bills -->
                <TextBlock Text="2000 DA Bills"
                          Grid.Row="1" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Hundreds"
                         Grid.Row="1" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="HundredsTotal"
                          Grid.Row="1" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="1000 DA Bills"
                          Grid.Row="2" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Fifties"
                         Grid.Row="2" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="FiftiesTotal"
                          Grid.Row="2" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="500 DA Bills"
                          Grid.Row="3" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Twenties"
                         Grid.Row="3" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="TwentiesTotal"
                          Grid.Row="3" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="200 DA Bills"
                          Grid.Row="4" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Tens"
                         Grid.Row="4" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="TensTotal"
                          Grid.Row="4" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="100 DA Bills"
                          Grid.Row="5" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Fives"
                         Grid.Row="5" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="FivesTotal"
                          Grid.Row="5" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="50 DA Bills"
                          Grid.Row="6" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                <TextBox x:Name="Ones"
                         Grid.Row="6" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="OnesTotal"
                          Grid.Row="6" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                <TextBlock Text="Coins"
                          Grid.Row="7" Grid.Column="0"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                <TextBox x:Name="Coins"
                         Grid.Row="7" Grid.Column="1"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="DenominationCount_TextChanged"/>
                <TextBlock x:Name="CoinsTotal"
                          Grid.Row="7" Grid.Column="2"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
            </Grid>
        </GroupBox>

        <!-- Results Section -->
        <Grid Grid.Row="3" Margin="0,0,0,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="Actual Count:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="0" Grid.Column="0"
                      FontWeight="Bold"/>
            <TextBlock x:Name="ActualCountText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="0" Grid.Column="1"
                      FontWeight="Bold"/>

            <TextBlock Text="Difference:"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="1" Grid.Column="0"
                      Margin="0,8"
                      FontWeight="Bold"/>
            <TextBlock x:Name="DifferenceText"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      Grid.Row="1" Grid.Column="1"
                      Margin="0,8"
                      FontWeight="Bold"/>
        </Grid>

        <!-- Notes -->
        <TextBox x:Name="NotesTextBox"
                 Grid.Row="4"
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 materialDesign:HintAssist.Hint="Notes"
                 TextWrapping="Wrap"
                 Height="80"
                 Margin="0,0,0,16"/>

        <!-- Buttons -->
        <StackPanel Grid.Row="5"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="{x:Null}"
                    Content="CANCEL"
                    Margin="0,0,8,0"/>
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    Click="SaveReconciliation_Click"
                    Content="SAVE"/>
        </StackPanel>
    </Grid>
</UserControl> 