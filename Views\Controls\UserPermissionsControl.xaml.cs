using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Views.Controls
{
    public partial class UserPermissionsControl : UserControl
    {
        public UserPermissionsViewModel ViewModel { get; private set; }

        public event Action PermissionChanged;
        private bool _suppressPermissionChangeEvents = false;

        public UserPermissionsControl()
        {
            InitializeComponent();
            ViewModel = new UserPermissionsViewModel();
            DataContext = ViewModel;

            // Wire up checkbox events after the control is loaded
            Loaded += UserPermissionsControl_Loaded;
        }

        private void UserPermissionsControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Find all checkboxes and subscribe to their events
            WireUpCheckboxEvents(this);
        }

        private void WireUpCheckboxEvents(DependencyObject parent)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is CheckBox checkbox)
                {
                    checkbox.Checked += OnCheckboxChanged;
                    checkbox.Unchecked += OnCheckboxChanged;
                }
                else
                {
                    WireUpCheckboxEvents(child);
                }
            }
        }

        private void OnCheckboxChanged(object sender, RoutedEventArgs e)
        {
            if (!_suppressPermissionChangeEvents)
            {
                PermissionChanged?.Invoke();
            }
        }

        public void LoadPermissions(UserPermissions permissions)
        {
            // Suppress events during loading to prevent triggering Custom role switch
            _suppressPermissionChangeEvents = true;

            try
            {
                System.Diagnostics.Debug.WriteLine("[USERPERMISSIONSCONTROL] LoadPermissions() called");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] Input permissions: {permissions?.GetHashCode()}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] Input CanManageUsers: {permissions?.CanManageUsers}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] Input CanAccessSettings: {permissions?.CanAccessSettings}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] Current ViewModel.Permissions: {ViewModel.Permissions?.GetHashCode()}");

                if (permissions == null)
                {
                    permissions = new UserPermissions
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                }
                ViewModel.LoadPermissions(permissions);

                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] After LoadPermissions - ViewModel.Permissions: {ViewModel.Permissions?.GetHashCode()}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] After LoadPermissions - CanManageUsers: {ViewModel.Permissions?.CanManageUsers}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONSCONTROL] After LoadPermissions - CanAccessSettings: {ViewModel.Permissions?.CanAccessSettings}");
            }
            finally
            {
                _suppressPermissionChangeEvents = false;
            }
        }

        public UserPermissions GetPermissions()
        {
            var permissions = ViewModel.Permissions;

            // Debug: Log what we're returning
            var logMessage = "[USERPERMISSIONSCONTROL] GetPermissions() called";
            System.Diagnostics.Debug.WriteLine(logMessage);
            LogToFile(logMessage);

            var logMessage2 = $"[USERPERMISSIONSCONTROL] ViewModel.Permissions object: {permissions?.GetHashCode()}";
            System.Diagnostics.Debug.WriteLine(logMessage2);
            LogToFile(logMessage2);

            var logMessage3 = $"[USERPERMISSIONSCONTROL] CanManageUsers: {permissions?.CanManageUsers}";
            System.Diagnostics.Debug.WriteLine(logMessage3);
            LogToFile(logMessage3);

            var logMessage4 = $"[USERPERMISSIONSCONTROL] CanAccessSettings: {permissions?.CanAccessSettings}";
            System.Diagnostics.Debug.WriteLine(logMessage4);
            LogToFile(logMessage4);

            var logMessage5 = $"[USERPERMISSIONSCONTROL] CanManageProducts: {permissions?.CanManageProducts}";
            System.Diagnostics.Debug.WriteLine(logMessage5);
            LogToFile(logMessage5);

            var logMessage6 = $"[USERPERMISSIONSCONTROL] CanViewReports: {permissions?.CanViewReports}";
            System.Diagnostics.Debug.WriteLine(logMessage6);
            LogToFile(logMessage6);

            permissions.CreatedAt = permissions.CreatedAt == default ? DateTime.Now : permissions.CreatedAt;
            permissions.UpdatedAt = DateTime.Now;
            return permissions;
        }

        private static void LogToFile(string message)
        {
            try
            {
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "permissions_debug.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }
}