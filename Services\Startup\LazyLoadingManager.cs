using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.Startup
{
    /// <summary>
    /// ✅ CRITICAL STARTUP OPTIMIZATION: Lazy loading manager for heavy components to improve startup performance
    /// </summary>
    public class LazyLoadingManager : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<LazyLoadingManager> _logger;
        private readonly StartupPerformanceMonitor _performanceMonitor;
        private readonly ConcurrentDictionary<string, LazyComponent> _lazyComponents;
        private readonly ConcurrentDictionary<string, object> _loadedComponents;
        private readonly SemaphoreSlim _loadingSemaphore;
        private bool _disposed;

        // Configuration
        private const int MAX_CONCURRENT_LOADS = 2;
        private const int COMPONENT_LOAD_TIMEOUT_MS = 15000; // 15 seconds

        public LazyLoadingManager(IServiceProvider serviceProvider,
            StartupPerformanceMonitor performanceMonitor = null,
            ILogger<LazyLoadingManager> logger = null)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _performanceMonitor = performanceMonitor;
            _logger = logger;
            _lazyComponents = new ConcurrentDictionary<string, LazyComponent>();
            _loadedComponents = new ConcurrentDictionary<string, object>();
            _loadingSemaphore = new SemaphoreSlim(MAX_CONCURRENT_LOADS, MAX_CONCURRENT_LOADS);

            RegisterLazyComponents();
            Debug.WriteLine("✅ [LAZY-LOADING] Lazy Loading Manager initialized");
        }

        /// <summary>
        /// ✅ INTERNAL: Register components for lazy loading
        /// </summary>
        private void RegisterLazyComponents()
        {
            // Dashboard ViewModels (heavy data loading)
            RegisterLazyComponent("DashboardViewModel", LoadDashboardViewModelAsync, LazyLoadPriority.High);
            RegisterLazyComponent("SalesHistoryViewModel", LoadSalesHistoryViewModelAsync, LazyLoadPriority.Medium);
            RegisterLazyComponent("InventoryViewModel", LoadInventoryViewModelAsync, LazyLoadPriority.Medium);
            RegisterLazyComponent("ReportsViewModel", LoadReportsViewModelAsync, LazyLoadPriority.Low);

            // Heavy UI Components
            RegisterLazyComponent("ProductCatalogView", LoadProductCatalogViewAsync, LazyLoadPriority.High);
            RegisterLazyComponent("CustomerManagementView", LoadCustomerManagementViewAsync, LazyLoadPriority.Low);
            RegisterLazyComponent("SettingsView", LoadSettingsViewAsync, LazyLoadPriority.Low);

            // Background Services
            RegisterLazyComponent("ReportingService", LoadReportingServiceAsync, LazyLoadPriority.Low);
            RegisterLazyComponent("BackupService", LoadBackupServiceAsync, LazyLoadPriority.Low);
            RegisterLazyComponent("UpdateService", LoadUpdateServiceAsync, LazyLoadPriority.Low);

            Debug.WriteLine($"[LAZY-LOADING] Registered {_lazyComponents.Count} components for lazy loading");
        }

        /// <summary>
        /// ✅ INTERNAL: Register a component for lazy loading
        /// </summary>
        private void RegisterLazyComponent(string componentName, Func<CancellationToken, Task<object>> loadFunc, LazyLoadPriority priority)
        {
            var component = new LazyComponent
            {
                Name = componentName,
                LoadFunction = loadFunc,
                Priority = priority,
                RegistrationTime = DateTime.Now
            };

            _lazyComponents.TryAdd(componentName, component);
        }

        /// <summary>
        /// ✅ CRITICAL: Get a component, loading it if necessary
        /// </summary>
        public async Task<T> GetComponentAsync<T>(string componentName, CancellationToken cancellationToken = default) where T : class
        {
            // Check if already loaded
            if (_loadedComponents.TryGetValue(componentName, out var loadedComponent))
            {
                return loadedComponent as T;
            }

            // Check if component is registered for lazy loading
            if (!_lazyComponents.TryGetValue(componentName, out var lazyComponent))
            {
                Debug.WriteLine($"⚠️ [LAZY-LOADING] Component {componentName} not registered for lazy loading");
                return null;
            }

            // Load the component
            return await LoadComponentAsync<T>(lazyComponent, cancellationToken);
        }

        /// <summary>
        /// ✅ CRITICAL: Load a component with performance tracking
        /// </summary>
        private async Task<T> LoadComponentAsync<T>(LazyComponent component, CancellationToken cancellationToken) where T : class
        {
            await _loadingSemaphore.WaitAsync(cancellationToken);

            try
            {
                // Double-check if component was loaded while waiting
                if (_loadedComponents.TryGetValue(component.Name, out var existingComponent))
                {
                    return existingComponent as T;
                }

                using var componentTracker = _performanceMonitor?.TrackStartupPhase($"LazyLoad_{component.Name}", "LazyLoading");
                
                Debug.WriteLine($"[LAZY-LOADING] Loading component: {component.Name} (Priority: {component.Priority})");

                var stopwatch = Stopwatch.StartNew();

                try
                {
                    // Create timeout cancellation token
                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    timeoutCts.CancelAfter(COMPONENT_LOAD_TIMEOUT_MS);

                    // Load the component
                    var loadedComponent = await component.LoadFunction(timeoutCts.Token);
                    
                    stopwatch.Stop();
                    component.LoadTime = stopwatch.ElapsedMilliseconds;
                    component.IsLoaded = true;
                    component.LoadedTime = DateTime.Now;

                    // Cache the loaded component
                    _loadedComponents.TryAdd(component.Name, loadedComponent);

                    Debug.WriteLine($"✅ [LAZY-LOADING] Component {component.Name} loaded in {component.LoadTime}ms");

                    return loadedComponent as T;
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    Debug.WriteLine($"⚠️ [LAZY-LOADING] Component {component.Name} loading cancelled");
                    throw;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    component.LoadTime = stopwatch.ElapsedMilliseconds;
                    component.LoadError = ex.Message;

                    Debug.WriteLine($"❌ [LAZY-LOADING] Component {component.Name} failed to load after {component.LoadTime}ms: {ex.Message}");
                    _logger?.LogError(ex, "Failed to load lazy component {ComponentName}", component.Name);
                    
                    throw new ComponentLoadException($"Failed to load component {component.Name}: {ex.Message}", ex);
                }
            }
            finally
            {
                _loadingSemaphore.Release();
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Preload high-priority components in background
        /// </summary>
        public async Task PreloadHighPriorityComponentsAsync(CancellationToken cancellationToken = default)
        {
            using var preloadTracker = _performanceMonitor?.TrackStartupPhase("PreloadHighPriorityComponents", "LazyLoading");
            
            var highPriorityComponents = _lazyComponents.Values
                .Where(c => c.Priority == LazyLoadPriority.High && !c.IsLoaded)
                .ToList();

            if (highPriorityComponents.Count == 0)
            {
                Debug.WriteLine("[LAZY-LOADING] No high-priority components to preload");
                return;
            }

            Debug.WriteLine($"[LAZY-LOADING] Preloading {highPriorityComponents.Count} high-priority components");

            var preloadTasks = highPriorityComponents.Select(async component =>
            {
                try
                {
                    await LoadComponentAsync<object>(component, cancellationToken);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"⚠️ [LAZY-LOADING] Failed to preload {component.Name}: {ex.Message}");
                    // Don't throw - preloading failures shouldn't block startup
                }
            });

            await Task.WhenAll(preloadTasks);
            Debug.WriteLine("[LAZY-LOADING] High-priority component preloading completed");
        }

        /// <summary>
        /// ✅ COMPONENT LOADERS: Individual component loading methods
        /// </summary>
        private async Task<object> LoadDashboardViewModelAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Try to get DashboardViewModel - it may not exist yet
                var viewModel = _serviceProvider.GetService<POSSystem.ViewModels.DashboardViewModel>();
                Debug.WriteLine("DashboardViewModel loaded");
                return viewModel ?? new object(); // Return placeholder if not available
            }, cancellationToken);
        }

        private async Task<object> LoadSalesHistoryViewModelAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Try to get SalesHistoryViewModel - it may not exist yet
                var viewModel = _serviceProvider.GetService<POSSystem.ViewModels.SalesHistoryViewModel>();
                Debug.WriteLine("SalesHistoryViewModel loaded");
                return viewModel ?? new object(); // Return placeholder if not available
            }, cancellationToken);
        }

        private async Task<object> LoadInventoryViewModelAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Use ProductsViewModel as it handles inventory functionality
                var viewModel = _serviceProvider.GetService<POSSystem.ViewModels.ProductsViewModel>();
                Debug.WriteLine("ProductsViewModel (Inventory) loaded");
                return viewModel ?? new object(); // Return placeholder if not available
            }, cancellationToken);
        }

        private async Task<object> LoadReportsViewModelAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Reports ViewModel would be loaded here
                Debug.WriteLine("ReportsViewModel loaded");
                return new object(); // Placeholder
            }, cancellationToken);
        }

        private async Task<object> LoadProductCatalogViewAsync(CancellationToken cancellationToken)
        {
            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                // Product catalog view would be loaded here
                Debug.WriteLine("ProductCatalogView loaded");
                return new object(); // Placeholder
            }, DispatcherPriority.Background, cancellationToken);
        }

        private async Task<object> LoadCustomerManagementViewAsync(CancellationToken cancellationToken)
        {
            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                // Customer management view would be loaded here
                Debug.WriteLine("CustomerManagementView loaded");
                return new object(); // Placeholder
            }, DispatcherPriority.Background, cancellationToken);
        }

        private async Task<object> LoadSettingsViewAsync(CancellationToken cancellationToken)
        {
            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                // Settings view would be loaded here
                Debug.WriteLine("SettingsView loaded");
                return new object(); // Placeholder
            }, DispatcherPriority.Background, cancellationToken);
        }

        private async Task<object> LoadReportingServiceAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Reporting service would be loaded here
                Debug.WriteLine("ReportingService loaded");
                return new object(); // Placeholder
            }, cancellationToken);
        }

        private async Task<object> LoadBackupServiceAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Backup service would be loaded here
                Debug.WriteLine("BackupService loaded");
                return new object(); // Placeholder
            }, cancellationToken);
        }

        private async Task<object> LoadUpdateServiceAsync(CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                // Update service would be loaded here
                Debug.WriteLine("UpdateService loaded");
                return new object(); // Placeholder
            }, cancellationToken);
        }

        /// <summary>
        /// ✅ PUBLIC API: Get loading statistics
        /// </summary>
        public LazyLoadingStatistics GetStatistics()
        {
            var components = _lazyComponents.Values.ToList();
            return new LazyLoadingStatistics
            {
                TotalComponents = components.Count,
                LoadedComponents = components.Count(c => c.IsLoaded),
                FailedComponents = components.Count(c => !string.IsNullOrEmpty(c.LoadError)),
                HighPriorityComponents = components.Count(c => c.Priority == LazyLoadPriority.High),
                AverageLoadTimeMs = components.Where(c => c.IsLoaded).Any() 
                    ? components.Where(c => c.IsLoaded).Average(c => c.LoadTime) 
                    : 0
            };
        }

        /// <summary>
        /// ✅ PUBLIC API: Get detailed component status
        /// </summary>
        public List<ComponentStatus> GetComponentStatuses()
        {
            return _lazyComponents.Values.Select(c => new ComponentStatus
            {
                Name = c.Name,
                Priority = c.Priority,
                IsLoaded = c.IsLoaded,
                LoadTimeMs = c.LoadTime,
                ErrorMessage = c.LoadError,
                RegistrationTime = c.RegistrationTime,
                LoadedTime = c.LoadedTime
            }).ToList();
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _loadingSemaphore?.Dispose();
                _loadedComponents.Clear();
                _lazyComponents.Clear();

                Debug.WriteLine("✅ [LAZY-LOADING] Lazy Loading Manager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [LAZY-LOADING] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Data structures for lazy loading
    /// </summary>
    public class LazyComponent
    {
        public string Name { get; set; }
        public Func<CancellationToken, Task<object>> LoadFunction { get; set; }
        public LazyLoadPriority Priority { get; set; }
        public DateTime RegistrationTime { get; set; }
        public bool IsLoaded { get; set; }
        public long LoadTime { get; set; }
        public DateTime? LoadedTime { get; set; }
        public string LoadError { get; set; }
    }

    public class LazyLoadingStatistics
    {
        public int TotalComponents { get; set; }
        public int LoadedComponents { get; set; }
        public int FailedComponents { get; set; }
        public int HighPriorityComponents { get; set; }
        public double AverageLoadTimeMs { get; set; }
    }

    public class ComponentStatus
    {
        public string Name { get; set; }
        public LazyLoadPriority Priority { get; set; }
        public bool IsLoaded { get; set; }
        public long LoadTimeMs { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime RegistrationTime { get; set; }
        public DateTime? LoadedTime { get; set; }
    }

    public enum LazyLoadPriority
    {
        Low,
        Medium,
        High
    }

    public class ComponentLoadException : Exception
    {
        public ComponentLoadException(string message) : base(message) { }
        public ComponentLoadException(string message, Exception innerException) : base(message, innerException) { }
    }
}
