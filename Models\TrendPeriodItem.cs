using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace POSSystem.Models
{
    public class TrendPeriodItem : INotifyPropertyChanged
    {
        private string _resourceKey;
        private string _displayName;

        public string ResourceKey
        {
            get => _resourceKey;
            set
            {
                if (_resourceKey != value)
                {
                    _resourceKey = value;
                    OnPropertyChanged();
                    UpdateDisplayName();
                }
            }
        }

        public string DisplayName
        {
            get => _displayName;
            private set
            {
                if (_displayName != value)
                {
                    _displayName = value;
                    OnPropertyChanged();
                }
            }
        }

        private void UpdateDisplayName()
        {
            if (Application.Current != null && !string.IsNullOrEmpty(ResourceKey))
            {
                DisplayName = Application.Current.TryFindResource(ResourceKey)?.ToString() ?? ResourceKey;
            }
            else
            {
                DisplayName = ResourceKey;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 