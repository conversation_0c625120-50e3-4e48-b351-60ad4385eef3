using System;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using POSSystem.Services.Interfaces;
using POSSystem.Services.QueryOptimization;
using System.ComponentModel;

namespace POSSystem.Services.RealTime
{
    /// <summary>
    /// ✅ NEW: Real-time dashboard update service for live metrics
    /// Provides real-time updates to dashboard metrics without full page refresh
    /// </summary>
    public class DashboardUpdateService : INotifyPropertyChanged, IDisposable
    {
        private readonly IDatabaseService _databaseService;
        private readonly DashboardQueryService _queryService;
        private readonly Timer _updateTimer;
        private readonly object _lockObject = new object();
        
        private bool _isUpdating = false;
        private bool _disposed = false;
        private DateTime _lastUpdateTime = DateTime.MinValue;

        // Update intervals
        private static readonly TimeSpan UPDATE_INTERVAL = TimeSpan.FromSeconds(30); // Update every 30 seconds
        private static readonly TimeSpan FAST_UPDATE_INTERVAL = TimeSpan.FromSeconds(10); // Fast updates during business hours

        // Current metrics
        private decimal _currentDaySales = 0;
        private int _currentDayTransactions = 0;
        private decimal _currentHourSales = 0;
        private int _pendingTransactions = 0;
        private bool _isBusinessHours = false;

        public event PropertyChangedEventHandler PropertyChanged;
        public event EventHandler<DashboardMetricsUpdatedEventArgs> MetricsUpdated;

        public DashboardUpdateService(IDatabaseService databaseService, DashboardQueryService queryService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));

            // Determine update interval based on business hours
            var interval = CheckIsBusinessHours() ? FAST_UPDATE_INTERVAL : UPDATE_INTERVAL;
            _updateTimer = new Timer(OnUpdateTimer, null, TimeSpan.Zero, interval);

            Debug.WriteLine($"DashboardUpdateService initialized with {interval.TotalSeconds}s interval");
        }

        #region Properties

        public decimal CurrentDaySales
        {
            get => _currentDaySales;
            private set
            {
                if (_currentDaySales != value)
                {
                    _currentDaySales = value;
                    OnPropertyChanged(nameof(CurrentDaySales));
                }
            }
        }

        public int CurrentDayTransactions
        {
            get => _currentDayTransactions;
            private set
            {
                if (_currentDayTransactions != value)
                {
                    _currentDayTransactions = value;
                    OnPropertyChanged(nameof(CurrentDayTransactions));
                }
            }
        }

        public decimal CurrentHourSales
        {
            get => _currentHourSales;
            private set
            {
                if (_currentHourSales != value)
                {
                    _currentHourSales = value;
                    OnPropertyChanged(nameof(CurrentHourSales));
                }
            }
        }

        public int PendingTransactions
        {
            get => _pendingTransactions;
            private set
            {
                if (_pendingTransactions != value)
                {
                    _pendingTransactions = value;
                    OnPropertyChanged(nameof(PendingTransactions));
                }
            }
        }

        public DateTime LastUpdateTime => _lastUpdateTime;

        public bool IsBusinessHours
        {
            get => _isBusinessHours;
            private set
            {
                if (_isBusinessHours != value)
                {
                    _isBusinessHours = value;
                    OnPropertyChanged(nameof(IsBusinessHours));
                    
                    // Adjust timer interval based on business hours
                    AdjustUpdateInterval();
                }
            }
        }

        #endregion

        /// <summary>
        /// ✅ CORE: Timer callback for periodic updates
        /// </summary>
        private async void OnUpdateTimer(object state)
        {
            try
            {
                await UpdateMetricsAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardUpdateService timer error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CORE: Update dashboard metrics in real-time
        /// </summary>
        public async Task UpdateMetricsAsync()
        {
            lock (_lockObject)
            {
                if (_isUpdating || _disposed)
                    return;
                _isUpdating = true;
            }

            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                var now = DateTime.Now;
                var todayStart = DateTime.Today;
                var currentHourStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);

                // ✅ UPDATE 1: Current day metrics
                var todayMetrics = await _queryService.GetEssentialMetricsAsync(todayStart, now);
                CurrentDaySales = todayMetrics.TotalSales;
                CurrentDayTransactions = todayMetrics.TransactionCount;

                // ✅ UPDATE 2: Current hour sales
                var hourMetrics = await _queryService.GetEssentialMetricsAsync(currentHourStart, now);
                CurrentHourSales = hourMetrics.TotalSales;

                // ✅ UPDATE 3: Pending transactions (unpaid sales)
                PendingTransactions = todayMetrics.UnpaidSalesCount;

                // ✅ UPDATE 4: Business hours status
                IsBusinessHours = CheckIsBusinessHours();

                _lastUpdateTime = DateTime.Now;
                stopwatch.Stop();

                // Notify subscribers
                MetricsUpdated?.Invoke(this, new DashboardMetricsUpdatedEventArgs
                {
                    DaySales = CurrentDaySales,
                    DayTransactions = CurrentDayTransactions,
                    HourSales = CurrentHourSales,
                    PendingTransactions = PendingTransactions,
                    UpdateTime = _lastUpdateTime,
                    UpdateDuration = stopwatch.Elapsed
                });

                Debug.WriteLine($"DashboardUpdateService: Metrics updated in {stopwatch.ElapsedMilliseconds}ms - Day: {CurrentDaySales:C}, Hour: {CurrentHourSales:C}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating dashboard metrics: {ex.Message}");
            }
            finally
            {
                lock (_lockObject)
                {
                    _isUpdating = false;
                }
            }
        }

        /// <summary>
        /// ✅ HELPER: Determine if current time is during business hours
        /// </summary>
        private bool CheckIsBusinessHours()
        {
            var now = DateTime.Now;
            var hour = now.Hour;
            var dayOfWeek = now.DayOfWeek;

            // Business hours: Monday-Saturday 8 AM to 8 PM
            return dayOfWeek != DayOfWeek.Sunday && hour >= 8 && hour < 20;
        }

        /// <summary>
        /// ✅ HELPER: Adjust timer interval based on business hours
        /// </summary>
        private void AdjustUpdateInterval()
        {
            try
            {
                var interval = IsBusinessHours ? FAST_UPDATE_INTERVAL : UPDATE_INTERVAL;
                _updateTimer?.Change(TimeSpan.Zero, interval);
                Debug.WriteLine($"DashboardUpdateService: Update interval changed to {interval.TotalSeconds}s (Business hours: {IsBusinessHours})");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adjusting update interval: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PUBLIC: Force immediate update
        /// </summary>
        public async Task ForceUpdateAsync()
        {
            try
            {
                Debug.WriteLine("DashboardUpdateService: Force update requested");
                await UpdateMetricsAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in force update: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PUBLIC: Start real-time updates
        /// </summary>
        public void StartUpdates()
        {
            if (!_disposed)
            {
                var interval = IsBusinessHours ? FAST_UPDATE_INTERVAL : UPDATE_INTERVAL;
                _updateTimer?.Change(TimeSpan.Zero, interval);
                Debug.WriteLine("DashboardUpdateService: Real-time updates started");
            }
        }

        /// <summary>
        /// ✅ PUBLIC: Stop real-time updates
        /// </summary>
        public void StopUpdates()
        {
            _updateTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            Debug.WriteLine("DashboardUpdateService: Real-time updates stopped");
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _updateTimer?.Dispose();
                _disposed = true;
                Debug.WriteLine("DashboardUpdateService disposed");
            }
        }
    }

    /// <summary>
    /// ✅ EVENT ARGS: Dashboard metrics updated event arguments
    /// </summary>
    public class DashboardMetricsUpdatedEventArgs : EventArgs
    {
        public decimal DaySales { get; set; }
        public int DayTransactions { get; set; }
        public decimal HourSales { get; set; }
        public int PendingTransactions { get; set; }
        public DateTime UpdateTime { get; set; }
        public TimeSpan UpdateDuration { get; set; }
    }
}
