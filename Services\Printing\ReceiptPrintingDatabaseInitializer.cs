using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models.Printing;

namespace POSSystem.Services.Printing
{
    /// <summary>
    /// Initializes the database tables and default data for receipt printing functionality
    /// </summary>
    public class ReceiptPrintingDatabaseInitializer
    {
        private readonly POSDbContext _context;
        private readonly ILogger<ReceiptPrintingDatabaseInitializer> _logger;

        public ReceiptPrintingDatabaseInitializer(POSDbContext context, ILogger<ReceiptPrintingDatabaseInitializer> logger = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
        }

        // Constructor without logger for simpler usage
        public ReceiptPrintingDatabaseInitializer(POSDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = null;
        }

        /// <summary>
        /// Initialize receipt printing tables and default data
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                LogInfo("Starting receipt printing database initialization");

                // Check if database is accessible
                if (!_context.Database.CanConnect())
                {
                    LogError("Cannot connect to database");
                    return false;
                }

                // Create tables if they don't exist (this would normally be done via migrations)
                await EnsureTablesExistAsync();

                // Create default data
                await CreateDefaultDataAsync();

                LogInfo("Receipt printing database initialization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error initializing receipt printing database: {ex.Message}", ex);
                return false;
            }
        }

        private async Task EnsureTablesExistAsync()
        {
            try
            {
                // This is a simplified approach - in production, use proper migrations
                // For now, we'll just ensure the context can create the tables
                await _context.Database.EnsureCreatedAsync();
                LogInfo("Database tables ensured");
            }
            catch (Exception ex)
            {
                LogError($"Error ensuring tables exist: {ex.Message}", ex);
                throw;
            }
        }

        private async Task CreateDefaultDataAsync()
        {
            try
            {
                // Create default receipt template if none exists
                if (!await _context.Set<ReceiptTemplate>().AnyAsync())
                {
                    var defaultTemplate = new ReceiptTemplate
                    {
                        Name = "Default Receipt Template",
                        Description = "Standard receipt template with all basic information",
                        TemplateType = "Standard",
                        PaperWidth = 48,
                        FontSize = 12,
                        IncludeLogo = true,
                        IncludeCompanyInfo = true,
                        IncludeCustomerInfo = true,
                        IncludeItemDetails = true,
                        IncludePaymentInfo = true,
                        IncludeBarcode = false,
                        FooterText = "Thank you for your business!",
                        IsDefault = true,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        ModifiedDate = DateTime.Now
                    };

                    _context.Set<ReceiptTemplate>().Add(defaultTemplate);
                    LogInfo("Created default receipt template");
                }

                // Create default printer configuration if none exists
                if (!await _context.Set<PrinterConfiguration>().AnyAsync())
                {
                    var defaultPrinter = new PrinterConfiguration
                    {
                        Name = "Default Printer",
                        PrinterType = "Standard",
                        PaperSize = "A4",
                        PrintQuality = "Normal",
                        Copies = 1,
                        IsDefault = true,
                        IsActive = true
                    };

                    _context.Set<PrinterConfiguration>().Add(defaultPrinter);
                    LogInfo("Created default printer configuration");
                }

                // Create default print settings if none exists
                if (!await _context.Set<ReceiptPrintSettings>().AnyAsync())
                {
                    var defaultPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "POS Receipts"
                    );

                    // Ensure the directory exists
                    Directory.CreateDirectory(defaultPath);

                    var defaultSettings = new ReceiptPrintSettings
                    {
                        AutoPrintEnabled = true,
                        ShowPrintDialog = false,
                        SaveAsPdfBackup = false,
                        PdfBackupPath = defaultPath,
                        EnablePrintPreview = true,
                        PrintTimeoutSeconds = 30,
                        RetryFailedPrints = true,
                        MaxRetryAttempts = 3
                    };

                    _context.Set<ReceiptPrintSettings>().Add(defaultSettings);
                    LogInfo($"Created default print settings with PDF path: {defaultPath}");
                }

                // Save all changes
                await _context.SaveChangesAsync();
                LogInfo("Default receipt printing data created successfully");
            }
            catch (Exception ex)
            {
                LogError($"Error creating default data: {ex.Message}", ex);
                throw;
            }
        }

        private void LogInfo(string message)
        {
            _logger?.LogInformation(message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT DB INIT] {message}");
        }

        private void LogError(string message, Exception ex = null)
        {
            _logger?.LogError(ex, message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT DB INIT ERROR] {message}");
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT DB INIT ERROR] Exception: {ex}");
            }
        }
    }
}
