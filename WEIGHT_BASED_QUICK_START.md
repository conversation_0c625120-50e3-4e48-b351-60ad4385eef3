# Weight-Based Products Quick Start Guide

## 🚀 Getting Started

### 1. Database Setup
```sql
-- Run this in SQL Server Management Studio
-- Execute the file: Migrations/AddWeightBasedProductSupport.sql
```

### 2. Build and Run
```bash
# Build your solution
dotnet build

# Run the application
dotnet run
```

## 🧪 Quick Testing Steps

### Test 1: Create a Weight-Based Product
1. Open your POS application
2. Go to Products → Add Product
3. In the **Sales Method** section, select **"By Weight"**
4. Fill in product details:
   - Name: "Fresh Apples"
   - Category: Select appropriate category
   - Unit of Measure: Select "Kilogram" or "Pound"
   - Quantity: 100 (represents 100 kg in stock)
   - Purchase Price: 2.50
   - Selling Price: 4.99
5. Click Save
6. ✅ **Expected**: Product saves successfully with weight-based flag

### Test 2: Create a Unit-Based Product
1. Add another product
2. In the **Sales Method** section, select **"By Units"**
3. Fill in product details:
   - Name: "Water Bottles"
   - Unit of Measure: Select "Piece"
   - Quantity: 50
   - Selling Price: 1.99
4. Click Save
5. ✅ **Expected**: Product saves as unit-based

### Test 3: Add Weight-Based Product to Cart
1. Go to Sales view
2. Click on your "Fresh Apples" product
3. ✅ **Expected**: Product added with quantity "1.0"
4. Click the + button next to quantity
5. ✅ **Expected**: Quantity increases to "1.1"
6. Click + several more times
7. ✅ **Expected**: Quantity shows decimals (1.2, 1.3, etc.)

### Test 4: Add Unit-Based Product to Cart
1. Click on your "Water Bottles" product
2. ✅ **Expected**: Product added with quantity "1"
3. Click the + button
4. ✅ **Expected**: Quantity increases to "2" (whole numbers only)

### Test 5: Verify Calculations
1. With "Fresh Apples" at quantity 2.5:
   - ✅ **Expected**: Total = 2.5 × 4.99 = $12.48
2. With "Water Bottles" at quantity 3:
   - ✅ **Expected**: Total = 3 × 1.99 = $5.97
3. Cart subtotal should be $18.45

## 🔍 What to Look For

### ✅ **Correct Behavior**
- Weight-based products show decimal quantities (2.5, 1.75, etc.)
- Unit-based products show whole numbers (1, 2, 3, etc.)
- + button increases weight products by 0.1, unit products by 1
- - button decreases weight products by 0.1, unit products by 1
- Calculations are accurate for decimal quantities
- Stock validation works with decimal quantities

### ❌ **Issues to Watch For**
- Decimal quantities showing for unit-based products
- Whole numbers forced for weight-based products
- Incorrect calculations with decimal quantities
- UI not updating properly when changing quantities
- Error messages when adding weight-based products

## 🐛 Troubleshooting

### Issue: "IsWeightBased column doesn't exist"
**Solution**: Run the database migration script first

### Issue: Quantities not showing decimals
**Solution**: Check that the QuantityDisplayConverter is properly referenced in XAML

### Issue: Compilation errors
**Solution**: Rebuild the solution and check for missing references

### Issue: Products not saving with weight-based flag
**Solution**: Verify the ProductDialog event handlers are working correctly

## 📞 Need Help?

If you encounter any issues:

1. **Check the console output** for error messages
2. **Verify database migration** ran successfully
3. **Check the diagnostics** for compilation errors
4. **Review the testing checklist** for comprehensive testing steps

## 🎯 Success Criteria

You'll know everything is working when:
- ✅ Weight-based products can be created and saved
- ✅ Decimal quantities work in the cart (2.5, 1.75, etc.)
- ✅ Unit-based products still work with whole numbers
- ✅ Calculations are accurate for both product types
- ✅ UI shows appropriate formatting for each type
- ✅ Stock validation works correctly

## 📈 Next Steps

Once basic functionality is confirmed:
1. Run the comprehensive test suite (`Tests/WeightBasedProductTests.cs`)
2. Follow the detailed testing checklist (`Tests/WeightBasedProductTestingChecklist.md`)
3. Test with real-world scenarios and data
4. Train users on the new functionality

---

**Happy Testing! 🎉**
