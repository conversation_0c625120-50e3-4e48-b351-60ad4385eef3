using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for database operations
    /// </summary>
    public interface IDatabaseService : IDisposable
    {
        // User Management
        User AuthenticateUser(string username, string password);
        List<User> GetAllUsers();
        void AddUser(User user);
        void UpdateUser(User user);
        void DeleteUser(int id);
        User GetUserById(int id);

        // Product Management
        List<Product> GetAllProducts();
        Task<List<Product>> GetProductsAsync(int pageSize, int offset, int? categoryId = null, string searchText = null, CancellationToken cancellationToken = default);
        Product GetProductById(int id);
        Task<Product> GetProductByIdAsync(int id, CancellationToken cancellationToken = default);
        Product GetProductByBarcode(string barcode);
        Task<Product> GetProductByBarcodeAsync(string barcode, CancellationToken cancellationToken = default);
        Task<List<Product>> GetAllProductsWithFullDetailsAsync(CancellationToken cancellationToken = default);
        void AddProduct(Product product);
        void UpdateProduct(Product product);
        void DeleteProduct(int id);
        List<Product> SearchProducts(string searchTerm);
        List<Product> GetLowStockProducts();
        Task<List<Product>> GetLowStockProductsAsync();
        List<Product> GetExpiringProducts(int daysAhead);

        // Category Management
        List<Category> GetAllCategories();
        void AddCategory(Category category);
        void UpdateCategory(Category category);
        void DeleteCategory(int id);
        Category GetCategoryById(int id);

        // Customer Management
        List<Customer> GetAllCustomers();
        void AddCustomer(Customer customer);
        void UpdateCustomer(Customer customer);
        void DeleteCustomer(int id);
        Customer GetCustomerById(int id);
        Customer GetCustomerByLoyaltyCode(string loyaltyCode);

        // Sales Management
        List<Sale> GetAllSales();
        Task<List<Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate);
        void AddSale(Sale sale);
        void UpdateSale(Sale sale);
        void DeleteSale(int id);
        Sale GetSaleById(int id);
        List<Sale> GetUnpaidSales();
        decimal GetSalesTotal(DateTime date);
        List<Sale> GetRecentSales(int limit);

        // ✅ NEW: Optimized methods for dashboard performance
        Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate);
        Task<SalesMetrics> GetSalesMetricsAsync(DateTime startDate, DateTime endDate);
        Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

        // Supplier Management
        List<Supplier> GetAllSuppliers();
        void AddSupplier(Supplier supplier);
        void UpdateSupplier(Supplier supplier);
        void DeleteSupplier(int id);
        Supplier GetSupplierById(int id);

        // Inventory Management
        void AddInventoryTransaction(InventoryTransaction transaction);
        List<InventoryTransaction> GetInventoryTransactions(int? productId = null);
        void UpdateProductStock(int productId, decimal newQuantity, string reason);

        // Batch Stock Management
        void AddBatchStock(BatchStock batch);
        List<BatchStock> GetBatchesForProduct(int productId);
        void AddStockToBatch(int batchId, decimal quantity);
        void UpdateBatch(int batchId, BatchStock updatedBatch);

        // Cash Drawer Management
        List<CashDrawer> GetAllCashDrawers();
        CashDrawer GetActiveCashDrawer();
        void AddCashDrawer(CashDrawer cashDrawer);
        void UpdateCashDrawer(CashDrawer cashDrawer);

        // Business Expenses
        List<BusinessExpense> GetAllBusinessExpenses();
        void AddBusinessExpense(BusinessExpense expense);
        void UpdateBusinessExpense(BusinessExpense expense);
        void DeleteBusinessExpense(int id);

        // Purchase Orders
        List<PurchaseOrder> GetAllPurchaseOrders();
        List<PurchaseOrder> GetUnpaidPurchaseOrders();
        void AddPurchaseOrder(PurchaseOrder purchaseOrder);
        void UpdatePurchaseOrder(PurchaseOrder purchaseOrder);

        // Invoice Management
        void EnsureInvoiceTablesExist();
        List<Invoice> GetInvoices(string type = null, string status = null, int? customerId = null, int? supplierId = null);
        Invoice GetInvoiceById(int invoiceId);
        int GetNextInvoiceNumber();
        int CreateInvoice(Invoice invoice);
        bool UpdateInvoice(Invoice invoice);
        bool DeleteInvoice(int invoiceId);

        // Loyalty Program
        List<LoyaltyProgram> GetAllLoyaltyPrograms();
        void AddLoyaltyProgram(LoyaltyProgram program);
        void UpdateLoyaltyProgram(LoyaltyProgram program);
        LoyaltyProgram GetActiveLoyaltyProgram();
        void SaveLoyaltyTransaction(LoyaltyTransaction transaction);

        // Discounts
        List<DiscountType> GetAllDiscountTypes();
        List<DiscountReason> GetAllDiscountReasons();

        // Reports and Analytics
        List<SalesTrend> GetSalesTrends(DateTime startDate, DateTime endDate);
        List<TopProductItem> GetTopSellingProducts(int count = 10);

        // Database Maintenance
        void MigratePasswords();
        void InitializeDefaultRoles();
        void BackupDatabase(string backupPath);
        void RestoreDatabase(string backupPath);

        // Events
        event EventHandler CategoryUpdated;

        // Additional methods
        User GetDefaultUser();
        Task<User> GetDefaultUserAsync(); // ✅ PERFORMANCE: Async version
        Task<Customer> GetCustomerByLoyaltyCodeAsync(string loyaltyCode); // ✅ PERFORMANCE: Async version
        Task<List<Product>> SearchProductsAsync(string searchTerm); // ✅ PERFORMANCE: Async version
        Task<List<CashDrawer>> GetAllCashDrawersAsync(); // ✅ PERFORMANCE: Async version
        Task<CashDrawer> GetActiveCashDrawerAsync(); // ✅ PERFORMANCE: Async version
        object Context { get; }
        Task<List<Product>> GetUserFavoritesAsync(int userId);

        /// <summary>
        /// Clears test data from the database to show real business data
        /// </summary>
        Task ClearTestDataAsync();

        /// <summary>
        /// ✅ PERFORMANCE OPTIMIZATION: Get customer spending summary without loading full sales entities
        /// </summary>
        Task<Dictionary<int, (decimal TotalSpent, int TransactionCount)>> GetCustomerSpendingSummaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// ✅ PERFORMANCE OPTIMIZATION: Get user performance metrics without loading full sales entities
        /// </summary>
        Task<Dictionary<int, dynamic>> GetUserPerformanceMetricsAsync(DateTime startDate, DateTime endDate);
    }
}
