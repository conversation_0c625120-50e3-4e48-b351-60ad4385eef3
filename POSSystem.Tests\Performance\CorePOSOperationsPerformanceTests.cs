using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;
using POSSystem.Data;
using POSSystem.Services;
using POSSystem.Services.Monitoring;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Models;
using POSSystem.ViewModels;
using FluentAssertions;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Core POS Operations Performance Tests
    /// 
    /// Tests the performance of key POS operations including:
    /// - Product scanning and lookup
    /// - Cart management operations
    /// - Payment processing
    /// - Sales completion
    /// - Dashboard loading and data visualization
    /// </summary>
    public class CorePOSOperationsPerformanceTests : IClassFixture<ComprehensivePerformanceTestSuite>
    {
        private readonly ComprehensivePerformanceTestSuite _testSuite;
        private readonly ITestOutputHelper _output;

        public CorePOSOperationsPerformanceTests(ComprehensivePerformanceTestSuite testSuite, ITestOutputHelper output)
        {
            _testSuite = testSuite;
            _output = output;
        }

        [Fact]
        public async Task ProductScanningPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 5000);
            var productService = _testSuite._serviceProvider.GetRequiredService<IProductManagementService>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            var targetResponseTime = TimeSpan.FromMilliseconds(100); // Target: under 100ms per scan

            _testSuite.LogPerformanceMessage("Starting Product Scanning Performance Test");

            // Act & Assert - Test various product lookup scenarios
            await TestProductLookupBySKU(productService, performanceMonitor, testResults, targetResponseTime);
            await TestProductLookupByBarcode(productService, performanceMonitor, testResults, targetResponseTime);
            await TestProductSearchByName(productService, performanceMonitor, testResults, targetResponseTime);
            await TestBulkProductRetrieval(productService, performanceMonitor, testResults, targetResponseTime);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            if (failedTests.Any())
            {
                var failureDetails = string.Join("\n", failedTests.Select(t => $"- {t.TestName}: {t.ErrorMessage}"));
                _testSuite.LogPerformanceMessage($"Product scanning performance test failures:\n{failureDetails}");
            }

            failedTests.Should().BeEmpty("All product scanning operations should meet performance targets");
            
            _testSuite.LogPerformanceMessage($"Product Scanning Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestProductLookupBySKU(IProductManagementService productService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results, TimeSpan targetTime)
        {
            var testName = "ProductLookupBySKU";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ProductScanning");
                
                // Test looking up products by SKU (simulate barcode scanning)
                for (int i = 1; i <= 100; i++)
                {
                    var sku = $"SKU{i:D6}";
                    var product = await productService.GetProductBySKUAsync(sku);
                    
                    if (product == null && i <= 50) // First 50 should exist
                    {
                        throw new Exception($"Product with SKU {sku} not found");
                    }
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 100.0);
                
                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("TotalLookups", 100);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["TotalLookups"] = 100,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per lookup (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestProductLookupByBarcode(IProductManagementService productService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results, TimeSpan targetTime)
        {
            var testName = "ProductLookupByBarcode";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ProductScanning");

                // Test barcode lookup performance
                for (int i = 1; i <= 50; i++)
                {
                    var barcode = $"123456789{i:D3}";
                    var product = await productService.GetProductByBarcodeAsync(barcode);
                    // Note: These barcodes may not exist, but we're testing lookup performance
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 50.0);

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("TotalLookups", 50);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["TotalLookups"] = 50,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per lookup (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestProductSearchByName(IProductManagementService productService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results, TimeSpan targetTime)
        {
            var testName = "ProductSearchByName";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ProductScanning");

                // Test product search performance
                var searchTerms = new[] { "Test", "Product", "001", "Electronics", "Food" };
                
                foreach (var term in searchTerms)
                {
                    var products = await productService.SearchProductsAsync(term, 1, 20);
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / (double)searchTerms.Length);

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("TotalSearches", searchTerms.Length);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= TimeSpan.FromMilliseconds(200), // More lenient for search
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["TotalSearches"] = searchTerms.Length,
                        ["TargetTime"] = 200
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per search (Target: 200ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestBulkProductRetrieval(IProductManagementService productService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results, TimeSpan targetTime)
        {
            var testName = "BulkProductRetrieval";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ProductScanning");

                // Test bulk product retrieval (like loading product grid)
                var products = await productService.GetProductsAsync(1, 100);

                stopwatch.Stop();

                tracker.AddMetadata("ResponseTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ProductsRetrieved", products?.Count() ?? 0);

                var targetBulkTime = TimeSpan.FromMilliseconds(500); // 500ms for 100 products
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetBulkTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ResponseTime"] = stopwatch.ElapsedMilliseconds,
                        ["ProductsRetrieved"] = products?.Count() ?? 0,
                        ["TargetTime"] = targetBulkTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms for {products?.Count() ?? 0} products (Target: {targetBulkTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        [Fact]
        public async Task CartManagementPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 1000);
            var salesService = _testSuite._serviceProvider.GetRequiredService<ISalesManagementService>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            _testSuite.LogPerformanceMessage("Starting Cart Management Performance Test");

            // Act & Assert - Test cart operations
            await TestCartItemAddition(salesService, performanceMonitor, testResults);
            await TestCartItemRemoval(salesService, performanceMonitor, testResults);
            await TestCartCalculations(salesService, performanceMonitor, testResults);
            await TestLargeCartOperations(salesService, performanceMonitor, testResults);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            failedTests.Should().BeEmpty("All cart management operations should meet performance targets");

            _testSuite.LogPerformanceMessage($"Cart Management Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestCartItemAddition(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "CartItemAddition";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "CartManagement");

                // Simulate adding items to cart rapidly
                var cart = new Cart { Id = 1, CreatedAt = DateTime.Now };
                
                for (int i = 1; i <= 50; i++)
                {
                    var cartItem = new CartItem
                    {
                        CartId = cart.Id,
                        ProductId = i,
                        Quantity = 1,
                        UnitPrice = 10.00m
                    };
                    
                    // Simulate cart item addition processing
                    await Task.Delay(1); // Minimal delay to simulate processing
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 50.0);
                var targetTime = TimeSpan.FromMilliseconds(10); // 10ms per item addition

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("ItemsAdded", 50);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["ItemsAdded"] = 50,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per item (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestCartItemRemoval(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "CartItemRemoval";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "CartManagement");

                // Simulate removing items from cart
                for (int i = 1; i <= 25; i++)
                {
                    // Simulate cart item removal processing
                    await Task.Delay(1);
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 25.0);
                var targetTime = TimeSpan.FromMilliseconds(10);

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("ItemsRemoved", 25);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["ItemsRemoved"] = 25,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per removal (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestCartCalculations(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "CartCalculations";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "CartManagement");

                // Simulate cart total calculations
                for (int i = 1; i <= 100; i++)
                {
                    // Simulate complex cart calculations (subtotal, tax, discounts, etc.)
                    var subtotal = i * 10.50m;
                    var tax = subtotal * 0.1m;
                    var discount = subtotal * 0.05m;
                    var total = subtotal + tax - discount;
                    
                    await Task.Delay(1);
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 100.0);
                var targetTime = TimeSpan.FromMilliseconds(5);

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("CalculationsPerformed", 100);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["CalculationsPerformed"] = 100,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per calculation (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestLargeCartOperations(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "LargeCartOperations";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "CartManagement");

                // Test performance with large cart (100 items)
                var cart = new Cart { Id = 2, CreatedAt = DateTime.Now };
                var cartItems = new List<CartItem>();

                for (int i = 1; i <= 100; i++)
                {
                    cartItems.Add(new CartItem
                    {
                        CartId = cart.Id,
                        ProductId = i,
                        Quantity = i % 5 + 1,
                        UnitPrice = 10.00m + (i % 10)
                    });
                }

                // Simulate processing large cart
                var subtotal = cartItems.Sum(item => item.Quantity * item.UnitPrice);
                var tax = subtotal * 0.1m;
                var total = subtotal + tax;

                stopwatch.Stop();
                var targetTime = TimeSpan.FromMilliseconds(100); // 100ms for large cart

                tracker.AddMetadata("ResponseTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("CartSize", cartItems.Count);
                tracker.AddMetadata("CartTotal", (double)total);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ResponseTime"] = stopwatch.ElapsedMilliseconds,
                        ["CartSize"] = cartItems.Count,
                        ["CartTotal"] = (double)total,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms for {cartItems.Count} items (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        [Fact]
        public async Task PaymentProcessingPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 500, customerCount: 100);
            var salesService = _testSuite._serviceProvider.GetRequiredService<ISalesManagementService>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            _testSuite.LogPerformanceMessage("Starting Payment Processing Performance Test");

            // Act & Assert - Test payment operations
            await TestCashPaymentProcessing(salesService, performanceMonitor, testResults);
            await TestCardPaymentProcessing(salesService, performanceMonitor, testResults);
            await TestSplitPaymentProcessing(salesService, performanceMonitor, testResults);
            await TestPaymentValidation(salesService, performanceMonitor, testResults);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            failedTests.Should().BeEmpty("All payment processing operations should meet performance targets");

            _testSuite.LogPerformanceMessage($"Payment Processing Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestCashPaymentProcessing(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "CashPaymentProcessing";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "PaymentProcessing");

                // Simulate cash payment processing
                for (int i = 1; i <= 50; i++)
                {
                    var payment = new Payment
                    {
                        Id = i,
                        SaleId = i,
                        PaymentMethod = "Cash",
                        Amount = 50.00m + (i % 10),
                        PaymentDate = DateTime.Now,
                        Status = "Completed"
                    };

                    // Simulate payment processing logic
                    await Task.Delay(2); // Simulate processing time
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 50.0);
                var targetTime = TimeSpan.FromMilliseconds(50); // 50ms per cash payment

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("PaymentsProcessed", 50);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["PaymentsProcessed"] = 50,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per payment (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestCardPaymentProcessing(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "CardPaymentProcessing";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "PaymentProcessing");

                // Simulate card payment processing (typically slower than cash)
                for (int i = 1; i <= 30; i++)
                {
                    var payment = new Payment
                    {
                        Id = i + 50,
                        SaleId = i + 50,
                        PaymentMethod = "Credit Card",
                        Amount = 75.00m + (i % 15),
                        PaymentDate = DateTime.Now,
                        Status = "Completed"
                    };

                    // Simulate card processing (authorization, etc.)
                    await Task.Delay(5); // Simulate longer processing time for cards
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 30.0);
                var targetTime = TimeSpan.FromMilliseconds(100); // 100ms per card payment

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("PaymentsProcessed", 30);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["PaymentsProcessed"] = 30,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per payment (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestSplitPaymentProcessing(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "SplitPaymentProcessing";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "PaymentProcessing");

                // Simulate split payment processing
                for (int i = 1; i <= 20; i++)
                {
                    var totalAmount = 100.00m + (i % 20);
                    var cashAmount = totalAmount * 0.6m;
                    var cardAmount = totalAmount * 0.4m;

                    var payments = new List<Payment>
                    {
                        new Payment
                        {
                            Id = i * 2 + 100,
                            SaleId = i + 100,
                            PaymentMethod = "Cash",
                            Amount = cashAmount,
                            PaymentDate = DateTime.Now,
                            Status = "Completed"
                        },
                        new Payment
                        {
                            Id = i * 2 + 101,
                            SaleId = i + 100,
                            PaymentMethod = "Credit Card",
                            Amount = cardAmount,
                            PaymentDate = DateTime.Now,
                            Status = "Completed"
                        }
                    };

                    // Simulate split payment processing
                    await Task.Delay(8); // Simulate processing multiple payments
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 20.0);
                var targetTime = TimeSpan.FromMilliseconds(150); // 150ms per split payment

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("SplitPaymentsProcessed", 20);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["SplitPaymentsProcessed"] = 20,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per split payment (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestPaymentValidation(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "PaymentValidation";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "PaymentProcessing");

                // Simulate payment validation
                for (int i = 1; i <= 100; i++)
                {
                    var payment = new Payment
                    {
                        Id = i + 200,
                        SaleId = i + 200,
                        PaymentMethod = i % 2 == 0 ? "Cash" : "Credit Card",
                        Amount = 25.00m + (i % 25),
                        PaymentDate = DateTime.Now,
                        Status = "Pending"
                    };

                    // Simulate validation logic
                    var isValid = payment.Amount > 0 && !string.IsNullOrEmpty(payment.PaymentMethod);
                    if (isValid)
                    {
                        payment.Status = "Completed";
                    }

                    await Task.Delay(1); // Minimal processing time for validation
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 100.0);
                var targetTime = TimeSpan.FromMilliseconds(10); // 10ms per validation

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("ValidationsPerformed", 100);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["ValidationsPerformed"] = 100,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per validation (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        [Fact]
        public async Task SalesCompletionPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 500, customerCount: 100);
            var salesService = _testSuite._serviceProvider.GetRequiredService<ISalesManagementService>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            _testSuite.LogPerformanceMessage("Starting Sales Completion Performance Test");

            // Act & Assert - Test sales completion operations
            await TestSaleFinalization(salesService, performanceMonitor, testResults);
            await TestReceiptGeneration(salesService, performanceMonitor, testResults);
            await TestInventoryUpdate(salesService, performanceMonitor, testResults);
            await TestSalesReporting(salesService, performanceMonitor, testResults);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            failedTests.Should().BeEmpty("All sales completion operations should meet performance targets");

            _testSuite.LogPerformanceMessage($"Sales Completion Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestSaleFinalization(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "SaleFinalization";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "SalesCompletion");

                // Simulate sale finalization process
                for (int i = 1; i <= 50; i++)
                {
                    var sale = new Sale
                    {
                        Id = i + 300,
                        CustomerId = (i % 100) + 1,
                        SaleDate = DateTime.Now,
                        Status = "Pending",
                        PaymentStatus = "Paid",
                        Subtotal = 100.00m + (i % 50),
                        TaxAmount = 10.00m + (i % 5),
                        GrandTotal = 110.00m + (i % 55)
                    };

                    // Simulate finalization logic
                    sale.Status = "Completed";
                    sale.CreatedAt = DateTime.Now;

                    await Task.Delay(3); // Simulate processing time
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 50.0);
                var targetTime = TimeSpan.FromMilliseconds(100); // 100ms per sale finalization

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("SalesFinalized", 50);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["SalesFinalized"] = 50,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per sale (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestReceiptGeneration(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "ReceiptGeneration";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "SalesCompletion");

                // Simulate receipt generation
                for (int i = 1; i <= 30; i++)
                {
                    var receiptData = new
                    {
                        SaleId = i + 350,
                        CustomerName = $"Customer {i}",
                        Items = Enumerable.Range(1, i % 5 + 1).Select(j => new
                        {
                            ProductName = $"Product {j}",
                            Quantity = j,
                            UnitPrice = 10.00m + j,
                            Total = j * (10.00m + j)
                        }).ToList(),
                        Subtotal = 50.00m + (i % 25),
                        Tax = 5.00m + (i % 3),
                        Total = 55.00m + (i % 28)
                    };

                    // Simulate receipt formatting and generation
                    var receiptText = $"Receipt for Sale {receiptData.SaleId}\n" +
                                    $"Customer: {receiptData.CustomerName}\n" +
                                    $"Items: {receiptData.Items.Count}\n" +
                                    $"Total: ${receiptData.Total:F2}";

                    await Task.Delay(5); // Simulate receipt generation time
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 30.0);
                var targetTime = TimeSpan.FromMilliseconds(50); // 50ms per receipt

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("ReceiptsGenerated", 30);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["ReceiptsGenerated"] = 30,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per receipt (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestInventoryUpdate(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "InventoryUpdate";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "SalesCompletion");

                // Simulate inventory updates after sales
                for (int i = 1; i <= 100; i++)
                {
                    var inventoryUpdate = new
                    {
                        ProductId = (i % 500) + 1,
                        QuantitySold = i % 5 + 1,
                        PreviousStock = 100 + (i % 50),
                        NewStock = 100 + (i % 50) - (i % 5 + 1)
                    };

                    // Simulate inventory update logic
                    await Task.Delay(1); // Simulate update processing
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 100.0);
                var targetTime = TimeSpan.FromMilliseconds(20); // 20ms per inventory update

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("InventoryUpdates", 100);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["InventoryUpdates"] = 100,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per update (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestSalesReporting(ISalesManagementService salesService, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "SalesReporting";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "SalesCompletion");

                // Simulate sales reporting updates
                for (int i = 1; i <= 20; i++)
                {
                    var reportData = new
                    {
                        Date = DateTime.Now.Date,
                        TotalSales = 1000.00m + (i * 50),
                        TransactionCount = 50 + (i * 2),
                        AverageTransaction = (1000.00m + (i * 50)) / (50 + (i * 2))
                    };

                    // Simulate report generation/update
                    await Task.Delay(10); // Simulate report processing
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 20.0);
                var targetTime = TimeSpan.FromMilliseconds(100); // 100ms per report update

                tracker.AddMetadata("AverageResponseTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("ReportsGenerated", 20);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageResponseTime"] = avgTime.TotalMilliseconds,
                        ["ReportsGenerated"] = 20,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per report (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }
    }
}
