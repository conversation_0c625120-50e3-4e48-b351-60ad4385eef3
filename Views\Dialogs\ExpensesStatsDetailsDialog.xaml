<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.ExpensesStatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d"
             MinWidth="640" MinHeight="480"
             Background="{DynamicResource MaterialDesignPaper}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid Margin="24"
              MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
              MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource ExpensesStats}"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       VerticalAlignment="Center"/>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Click="CloseButton_Click"
                    ToolTip="{DynamicResource Close}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Period Filter -->
            <TextBlock Grid.Column="0" 
                       Text="{DynamicResource Period}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       VerticalAlignment="Center"
                       Margin="0,0,8,0"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding TrendPeriods}"
                      SelectedItem="{Binding SelectedTrendPeriod}"
                      DisplayMemberPath="DisplayName"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,16,0"/>

            <!-- Frequency Filter -->
            <TextBlock Grid.Column="2" 
                      Text="{DynamicResource Frequency}"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      VerticalAlignment="Center"
                      Margin="0,0,8,0"/>
            <ComboBox Grid.Column="3"
                     ItemsSource="{Binding ExpenseFrequencies}"
                     SelectedItem="{Binding SelectedFrequency}"
                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                     Margin="0,0,16,0"/>
        </Grid>

        <!-- Subtitle -->
        <TextBlock Grid.Row="2" 
                   Text="{Binding Subtitle}"
                   Style="{StaticResource MaterialDesignBody1TextBlock}"
                   Opacity="0.6"
                   Margin="0,0,0,8"/>

        <!-- Main Content -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metrics Cards -->
            <UniformGrid Rows="1" Margin="0,0,0,16">
                <!-- Total Expenses Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource TotalExpenses}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="TotalExpenses"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding ExpensesGrowth}"
                                 Foreground="{Binding ExpensesGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Average Monthly Expenses Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="ChartLineVariant" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource AverageMonthlyExpenses}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="AverageMonthlyExpenses"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding MonthlyAverageGrowth}"
                                 Foreground="{Binding MonthlyAverageGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Recurring Expenses Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Repeat" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource RecurringExpenses}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="RecurringExpenses"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding RecurringExpensesPercentage, StringFormat={}{0:N1}%}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Padding="8">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Expenses Trend -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpensesTrend}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpensesTrend}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding TrendSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding TrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Expenses by Category -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpensesByCategory}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpensesByCategory}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:PieChart Grid.Row="1"
                                        Series="{Binding CategoryDistributionSeries}"
                                        LegendLocation="Right"
                                        InnerRadius="40">
                                <lvc:PieChart.ChartLegend>
                                    <lvc:DefaultLegend BulletSize="15"/>
                                </lvc:PieChart.ChartLegend>
                            </lvc:PieChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Expenses List -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="FormatListBulleted"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpensesList}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="FormatListBulleted"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpensesList}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <DataGrid Grid.Row="1"
                                    ItemsSource="{Binding Expenses}"
                                    AutoGenerateColumns="False"
                                    IsReadOnly="True"
                                    Style="{StaticResource MaterialDesignDataGrid}"
                                    Height="400"
                                    HorizontalScrollBarVisibility="Auto"
                                    VerticalScrollBarVisibility="Auto">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Description}"
                                                      Binding="{Binding Description}"
                                                      Width="300"
                                                      MaxWidth="300"/>
                                    <DataGridTextColumn Header="{DynamicResource Amount}"
                                                      Width="120"
                                                      MaxWidth="120">
                                        <DataGridTextColumn.Binding>
                                            <MultiBinding StringFormat="{}{0:N2} {1}">
                                                <Binding Path="Amount"/>
                                                <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                            </MultiBinding>
                                        </DataGridTextColumn.Binding>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="{DynamicResource Date}"
                                                      Binding="{Binding Date, StringFormat=d}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                    <DataGridTextColumn Header="{DynamicResource Frequency}"
                                                      Binding="{Binding Frequency}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4"
              Background="{DynamicResource MaterialDesignPaper}"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                      VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="24"
                           Height="24"/>
                <TextBlock Text="{DynamicResource LoadingData}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Margin="0,8,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>