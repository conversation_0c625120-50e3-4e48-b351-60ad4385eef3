using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    /// <summary>
    /// Represents notifications for draft invoice events in the two-tier invoice system
    /// </summary>
    public class DraftInvoiceNotification
    {
        public DraftInvoiceNotification()
        {
            CreatedAt = DateTime.Now;
            IsRead = false;
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Invoice")]
        public int InvoiceId { get; set; }

        [Required]
        [ForeignKey("CreatedByUser")]
        public int CreatedByUserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string NotificationType { get; set; } // DRAFT_CREATED, DRAFT_COMPLETED, DRAFT_EXPIRED, DRAFT_REJECTED

        public bool IsRead { get; set; }

        [MaxLength(500)]
        public string Message { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }

        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual Invoice Invoice { get; set; }
        public virtual User CreatedByUser { get; set; }

        // Computed properties for UI
        [NotMapped]
        public string NotificationTypeDisplay => NotificationType switch
        {
            "DRAFT_CREATED" => "Draft Created",
            "DRAFT_COMPLETED" => "Draft Completed",
            "DRAFT_EXPIRED" => "Draft Expired",
            "DRAFT_REJECTED" => "Draft Rejected",
            _ => NotificationType
        };

        [NotMapped]
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - CreatedAt;
                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} minutes ago";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} hours ago";
                if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays} days ago";
                return CreatedAt.ToString("MMM dd, yyyy");
            }
        }

        [NotMapped]
        public string IconKind => NotificationType switch
        {
            "DRAFT_CREATED" => "FileDocumentPlus",
            "DRAFT_COMPLETED" => "CheckCircle",
            "DRAFT_EXPIRED" => "ClockAlert",
            "DRAFT_REJECTED" => "CloseCircle",
            _ => "Information"
        };

        [NotMapped]
        public string NotificationColor => NotificationType switch
        {
            "DRAFT_CREATED" => "#2196F3", // Blue
            "DRAFT_COMPLETED" => "#4CAF50", // Green
            "DRAFT_EXPIRED" => "#FF9800", // Orange
            "DRAFT_REJECTED" => "#F44336", // Red
            _ => "#757575" // Gray
        };

        // Helper methods
        public void MarkAsRead()
        {
            IsRead = true;
            ReadAt = DateTime.Now;
        }

        public static DraftInvoiceNotification CreateDraftCreatedNotification(int invoiceId, int createdByUserId, string invoiceNumber)
        {
            return new DraftInvoiceNotification
            {
                InvoiceId = invoiceId,
                CreatedByUserId = createdByUserId,
                NotificationType = "DRAFT_CREATED",
                Message = $"New draft invoice {invoiceNumber} created and requires admin completion"
            };
        }

        public static DraftInvoiceNotification CreateDraftCompletedNotification(int invoiceId, int createdByUserId, string invoiceNumber, string completedByUserName)
        {
            return new DraftInvoiceNotification
            {
                InvoiceId = invoiceId,
                CreatedByUserId = createdByUserId,
                NotificationType = "DRAFT_COMPLETED",
                Message = $"Draft invoice {invoiceNumber} has been completed by {completedByUserName}"
            };
        }

        public static DraftInvoiceNotification CreateDraftExpiredNotification(int invoiceId, int createdByUserId, string invoiceNumber)
        {
            return new DraftInvoiceNotification
            {
                InvoiceId = invoiceId,
                CreatedByUserId = createdByUserId,
                NotificationType = "DRAFT_EXPIRED",
                Message = $"Draft invoice {invoiceNumber} has expired and requires attention"
            };
        }

        public static DraftInvoiceNotification CreateDraftRejectedNotification(int invoiceId, int createdByUserId, string invoiceNumber, string reason)
        {
            return new DraftInvoiceNotification
            {
                InvoiceId = invoiceId,
                CreatedByUserId = createdByUserId,
                NotificationType = "DRAFT_REJECTED",
                Message = $"Draft invoice {invoiceNumber} has been rejected. Reason: {reason}"
            };
        }
    }

    /// <summary>
    /// Enum for notification types to ensure type safety
    /// </summary>
    public enum DraftInvoiceNotificationType
    {
        DraftCreated,
        DraftCompleted,
        DraftExpired,
        DraftRejected
    }
}
