using System;
using System.Collections.Generic;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class SupplierContract
    {
        public int Id { get; set; }
        public int SupplierId { get; set; }
        public string ContractNumber { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string PaymentTerms { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal DiscountTerms { get; set; }
        public string DeliveryTerms { get; set; }
        public string Status { get; set; }
        public virtual Supplier Supplier { get; set; }
        public virtual ICollection<ContractItem> Items { get; set; }
    }
} 