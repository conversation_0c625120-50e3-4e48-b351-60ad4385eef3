using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using System.Diagnostics;

namespace POSSystem.Utilities
{
    /// <summary>
    /// ✅ DEBUG FILE READER: Utility to easily read and analyze debug log files
    /// </summary>
    public static class DebugFileReader
    {
        private static readonly string LogsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");

        /// <summary>
        /// Gets the latest debug log content
        /// </summary>
        public static string GetLatestDebugLog()
        {
            try
            {
                var latestFile = Path.Combine(LogsDirectory, "latest_debug.log");
                if (File.Exists(latestFile))
                {
                    return File.ReadAllText(latestFile);
                }

                // Fallback to most recent timestamped file
                var debugFiles = Directory.GetFiles(LogsDirectory, "debug_*.log")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .FirstOrDefault();

                return debugFiles != null ? File.ReadAllText(debugFiles) : "No debug log found.";
            }
            catch (Exception ex)
            {
                return $"Error reading debug log: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets the latest performance log content
        /// </summary>
        public static string GetLatestPerformanceLog()
        {
            try
            {
                var latestFile = Path.Combine(LogsDirectory, "latest_performance.log");
                if (File.Exists(latestFile))
                {
                    return File.ReadAllText(latestFile);
                }

                // Fallback to most recent timestamped file
                var perfFiles = Directory.GetFiles(LogsDirectory, "performance_*.log")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .FirstOrDefault();

                return perfFiles != null ? File.ReadAllText(perfFiles) : "No performance log found.";
            }
            catch (Exception ex)
            {
                return $"Error reading performance log: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets performance-related entries from the latest debug log
        /// </summary>
        public static string GetPerformanceEntries()
        {
            try
            {
                var debugLog = GetLatestDebugLog();
                var lines = debugLog.Split('\n');
                
                var performanceLines = lines.Where(line => 
                    line.Contains("UI THREAD BLOCKED") ||
                    line.Contains("UpdateProductChartsAsync") ||
                    line.Contains("UpdateCategoryChartsAsync") ||
                    line.Contains("LoadDashboardDataAsync") ||
                    line.Contains("LoadAlertsDataAsync") ||
                    line.Contains("performance") ||
                    line.Contains("ms") ||
                    line.Contains("PERFORMANCE") ||
                    line.Contains("async") ||
                    line.Contains("background")
                ).ToList();

                return string.Join("\n", performanceLines);
            }
            catch (Exception ex)
            {
                return $"Error extracting performance entries: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets the last N lines from the latest debug log
        /// </summary>
        public static string GetLatestDebugLogTail(int lineCount = 50)
        {
            try
            {
                var debugLog = GetLatestDebugLog();
                var lines = debugLog.Split('\n');
                
                var tailLines = lines.Skip(Math.Max(0, lines.Length - lineCount)).ToArray();
                return string.Join("\n", tailLines);
            }
            catch (Exception ex)
            {
                return $"Error reading debug log tail: {ex.Message}";
            }
        }

        /// <summary>
        /// Opens the logs directory in Windows Explorer
        /// </summary>
        public static void OpenLogsDirectory()
        {
            try
            {
                if (Directory.Exists(LogsDirectory))
                {
                    Process.Start("explorer.exe", LogsDirectory);
                }
                else
                {
                    Debug.WriteLine($"Logs directory does not exist: {LogsDirectory}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening logs directory: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a summary of all available log files
        /// </summary>
        public static string GetLogFilesSummary()
        {
            try
            {
                if (!Directory.Exists(LogsDirectory))
                {
                    return "Logs directory does not exist.";
                }

                var sb = new StringBuilder();
                sb.AppendLine("=== DEBUG LOG FILES SUMMARY ===");
                sb.AppendLine($"Logs Directory: {LogsDirectory}");
                sb.AppendLine();

                var debugFiles = Directory.GetFiles(LogsDirectory, "debug_*.log")
                    .OrderByDescending(f => File.GetCreationTime(f));

                var performanceFiles = Directory.GetFiles(LogsDirectory, "performance_*.log")
                    .OrderByDescending(f => File.GetCreationTime(f));

                sb.AppendLine("Debug Files:");
                foreach (var file in debugFiles.Take(5)) // Show last 5 files
                {
                    var info = new FileInfo(file);
                    sb.AppendLine($"  {Path.GetFileName(file)} - {info.CreationTime:yyyy-MM-dd HH:mm:ss} ({info.Length / 1024:N0} KB)");
                }

                sb.AppendLine();
                sb.AppendLine("Performance Files:");
                foreach (var file in performanceFiles.Take(5)) // Show last 5 files
                {
                    var info = new FileInfo(file);
                    sb.AppendLine($"  {Path.GetFileName(file)} - {info.CreationTime:yyyy-MM-dd HH:mm:ss} ({info.Length / 1024:N0} KB)");
                }

                // Check for latest files
                var latestDebug = Path.Combine(LogsDirectory, "latest_debug.log");
                var latestPerformance = Path.Combine(LogsDirectory, "latest_performance.log");

                sb.AppendLine();
                sb.AppendLine("Quick Access Files:");
                sb.AppendLine($"  latest_debug.log: {(File.Exists(latestDebug) ? "Available" : "Not found")}");
                sb.AppendLine($"  latest_performance.log: {(File.Exists(latestPerformance) ? "Available" : "Not found")}");

                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"Error getting log files summary: {ex.Message}";
            }
        }

        /// <summary>
        /// Searches for specific text in the latest debug log
        /// </summary>
        public static string SearchDebugLog(string searchTerm, int contextLines = 2)
        {
            try
            {
                var debugLog = GetLatestDebugLog();
                var lines = debugLog.Split('\n');
                var results = new List<string>();

                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                    {
                        // Add context lines before
                        int startIndex = Math.Max(0, i - contextLines);
                        int endIndex = Math.Min(lines.Length - 1, i + contextLines);

                        results.Add($"=== Match at line {i + 1} ===");
                        for (int j = startIndex; j <= endIndex; j++)
                        {
                            string prefix = j == i ? ">>> " : "    ";
                            results.Add($"{prefix}{lines[j]}");
                        }
                        results.Add("");
                    }
                }

                return results.Count > 0 
                    ? string.Join("\n", results) 
                    : $"No matches found for '{searchTerm}'";
            }
            catch (Exception ex)
            {
                return $"Error searching debug log: {ex.Message}";
            }
        }
    }
}
