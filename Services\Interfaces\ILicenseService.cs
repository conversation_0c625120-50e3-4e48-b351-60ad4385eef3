namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for license management services
    /// </summary>
    public interface ILicenseService
    {
        /// <summary>
        /// Validates the current license
        /// </summary>
        /// <returns>True if license is valid, false otherwise</returns>
        bool ValidateLicense();

        /// <summary>
        /// Activates a license with the provided key
        /// </summary>
        /// <param name="licenseKey">License key to activate</param>
        /// <returns>True if activation successful, false otherwise</returns>
        bool ActivateLicense(string licenseKey);

        /// <summary>
        /// Deactivates the current license
        /// </summary>
        void DeactivateLicense();

        /// <summary>
        /// Gets license information
        /// </summary>
        /// <returns>License information string</returns>
        string GetLicenseInfo();

        /// <summary>
        /// Checks if license is expired
        /// </summary>
        /// <returns>True if license is expired, false otherwise</returns>
        bool IsLicenseExpired();

        /// <summary>
        /// Cleans security files
        /// </summary>
        void CleanSecurityFiles();

        /// <summary>
        /// Shuts down the license service
        /// </summary>
        void Shutdown();
    }
}
