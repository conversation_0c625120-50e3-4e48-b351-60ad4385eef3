-- Make PaymentReference nullable in PurchaseOrders table
PRAGMA foreign_keys=off;
BEGIN TRANSACTION;

CREATE TABLE PurchaseOrders_new (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OrderNumber TEXT NOT NULL,
    OrderDate TEXT NOT NULL,
    DueDate TEXT NOT NULL,
    SupplierId INTEGER NOT NULL,
    Status TEXT NOT NULL,
    PaymentMethod TEXT NOT NULL,
    PaymentReference TEXT,  -- Changed to nullable
    PaymentDate TEXT,
    CreatedAt TEXT NOT NULL,
    CreatedByUserId INTEGER NOT NULL,
    UpdatedAt TEXT,
    Notes TEXT,
    Subtotal DECIMAL(18,2) NOT NULL,
    TaxAmount DECIMAL(18,2) NOT NULL,
    GrandTotal DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
);

INSERT INTO PurchaseOrders_new SELECT * FROM PurchaseOrders;
DROP TABLE PurchaseOrders;
ALTER TABLE PurchaseOrders_new RENAME TO PurchaseOrders;

COMMIT;
PRAGMA foreign_keys=on; 