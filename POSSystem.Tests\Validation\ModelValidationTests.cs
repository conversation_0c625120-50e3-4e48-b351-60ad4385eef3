using POSSystem.Models;
using Xunit;
using FluentAssertions;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Tests.Validation
{
    public class ModelValidationTests
    {
        #region Product Validation Tests

        [Fact]
        public void Product_ShouldBeValid_WithValidData()
        {
            // Arrange
            var product = new Product
            {
                Name = "Valid Product",
                SKU = "VALID001",
                CategoryId = 1,
                SellingPrice = 10.00m,
                PurchasePrice = 5.00m,
                StockQuantity = 100,
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(product);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void Product_ShouldBeInvalid_WithInvalidName(string invalidName)
        {
            // Arrange
            var product = new Product
            {
                Name = invalidName,
                SKU = "VALID001",
                CategoryId = 1,
                SellingPrice = 10.00m,
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(product);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Name"));
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void Product_ShouldBeInvalid_WithInvalidSKU(string invalidSKU)
        {
            // Arrange
            var product = new Product
            {
                Name = "Valid Product",
                SKU = invalidSKU,
                CategoryId = 1,
                SellingPrice = 10.00m,
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(product);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("SKU"));
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(-10.50)]
        public void Product_ShouldBeInvalid_WithNegativeSellingPrice(decimal invalidPrice)
        {
            // Arrange
            var product = new Product
            {
                Name = "Valid Product",
                SKU = "VALID001",
                CategoryId = 1,
                SellingPrice = invalidPrice,
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(product);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("SellingPrice"));
        }

        #endregion

        #region Customer Validation Tests

        [Fact]
        public void Customer_ShouldBeValid_WithValidData()
        {
            // Arrange
            var customer = new Customer
            {
                Name = "John Doe",
                Email = "<EMAIL>",
                Phone = "************",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(customer);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void Customer_ShouldBeInvalid_WithInvalidName(string invalidName)
        {
            // Arrange
            var customer = new Customer
            {
                Name = invalidName,
                Email = "<EMAIL>",
                Phone = "************",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(customer);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Name"));
        }

        [Theory]
        [InlineData("invalid-email")]
        [InlineData("@example.com")]
        [InlineData("user@")]
        [InlineData("user.example.com")]
        public void Customer_ShouldBeInvalid_WithInvalidEmail(string invalidEmail)
        {
            // Arrange
            var customer = new Customer
            {
                Name = "John Doe",
                Email = invalidEmail,
                Phone = "************",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(customer);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Email"));
        }

        #endregion

        #region Sale Validation Tests

        [Fact]
        public void Sale_ShouldBeValid_WithValidData()
        {
            // Arrange
            var sale = new Sale
            {
                SaleDate = DateTime.Now,
                CustomerId = 1,
                UserId = 1,
                Subtotal = 100.00m,
                TaxAmount = 8.00m,
                GrandTotal = 108.00m,
                PaymentMethod = "Cash",
                PaymentStatus = "Paid"
            };

            // Act
            var validationResults = ValidateModel(sale);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(-100.50)]
        public void Sale_ShouldBeInvalid_WithNegativeSubtotal(decimal invalidSubtotal)
        {
            // Arrange
            var sale = new Sale
            {
                SaleDate = DateTime.Now,
                CustomerId = 1,
                UserId = 1,
                Subtotal = invalidSubtotal,
                TaxAmount = 0,
                GrandTotal = invalidSubtotal,
                PaymentMethod = "Cash",
                PaymentStatus = "Paid"
            };

            // Act
            var validationResults = ValidateModel(sale);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Subtotal"));
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(-50.25)]
        public void Sale_ShouldBeInvalid_WithNegativeGrandTotal(decimal invalidGrandTotal)
        {
            // Arrange
            var sale = new Sale
            {
                SaleDate = DateTime.Now,
                CustomerId = 1,
                UserId = 1,
                Subtotal = 100.00m,
                TaxAmount = 8.00m,
                GrandTotal = invalidGrandTotal,
                PaymentMethod = "Cash",
                PaymentStatus = "Paid"
            };

            // Act
            var validationResults = ValidateModel(sale);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("GrandTotal"));
        }

        #endregion

        #region SaleItem Validation Tests

        [Fact]
        public void SaleItem_ShouldBeValid_WithValidData()
        {
            // Arrange
            var saleItem = new SaleItem
            {
                SaleId = 1,
                ProductId = 1,
                Quantity = 2,
                UnitPrice = 25.50m,
                Total = 51.00m
            };

            // Act
            var validationResults = ValidateModel(saleItem);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(-5.5)]
        public void SaleItem_ShouldBeInvalid_WithInvalidQuantity(decimal invalidQuantity)
        {
            // Arrange
            var saleItem = new SaleItem
            {
                SaleId = 1,
                ProductId = 1,
                Quantity = invalidQuantity,
                UnitPrice = 25.50m,
                Total = 51.00m
            };

            // Act
            var validationResults = ValidateModel(saleItem);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Quantity"));
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(-25.50)]
        public void SaleItem_ShouldBeInvalid_WithNegativeUnitPrice(decimal invalidUnitPrice)
        {
            // Arrange
            var saleItem = new SaleItem
            {
                SaleId = 1,
                ProductId = 1,
                Quantity = 2,
                UnitPrice = invalidUnitPrice,
                Total = 51.00m
            };

            // Act
            var validationResults = ValidateModel(saleItem);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("UnitPrice"));
        }

        #endregion

        #region Category Validation Tests

        [Fact]
        public void Category_ShouldBeValid_WithValidData()
        {
            // Arrange
            var category = new Category
            {
                Name = "Electronics",
                Description = "Electronic devices and accessories",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(category);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void Category_ShouldBeInvalid_WithInvalidName(string invalidName)
        {
            // Arrange
            var category = new Category
            {
                Name = invalidName,
                Description = "Valid description",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(category);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Name"));
        }

        #endregion

        #region User Validation Tests

        [Fact]
        public void User_ShouldBeValid_WithValidData()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void User_ShouldBeInvalid_WithInvalidUsername(string invalidUsername)
        {
            // Arrange
            var user = new User
            {
                Username = invalidUsername,
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Username"));
        }

        #endregion

        #region Helper Methods

        private static List<ValidationResult> ValidateModel(object model)
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(model);
            Validator.TryValidateObject(model, validationContext, validationResults, true);
            return validationResults;
        }

        #endregion
    }
}
