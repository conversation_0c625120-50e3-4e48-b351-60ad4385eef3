using System;
using System.Collections.Generic;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// Lightweight DTOs for optimized database queries
    /// These classes contain only the essential data needed for specific operations
    /// </summary>

    #region Product DTOs

    /// <summary>
    /// Minimal product data for list displays (70% less memory than full Product entity)
    /// </summary>
    public class ProductListItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string SKU { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal StockQuantity { get; set; }
        public string CategoryName { get; set; }
        public bool HasLowStock { get; set; }
        public string PrimaryBarcode { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// Optimized search result with match type information
    /// </summary>
    public class ProductSearchResult
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string SKU { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal StockQuantity { get; set; }
        public string CategoryName { get; set; }
        public string PrimaryBarcode { get; set; }
        public string MatchType { get; set; } // "Name", "SKU", "Barcode"
    }

    /// <summary>
    /// Aggregated product statistics (single query instead of loading all products)
    /// </summary>
    public class ProductStatisticsSummary
    {
        public int TotalProducts { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal AverageSellingPrice { get; set; }
        public double LowStockPercentage => TotalProducts > 0 ? (double)LowStockCount / TotalProducts * 100 : 0;
        public double OutOfStockPercentage => TotalProducts > 0 ? (double)OutOfStockCount / TotalProducts * 100 : 0;
    }

    /// <summary>
    /// Minimal data for low stock alerts
    /// </summary>
    public class LowStockAlert
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string SKU { get; set; }
        public decimal CurrentStock { get; set; }
        public int ReorderPoint { get; set; }
        public string CategoryName { get; set; }
        public decimal StockDeficit => Math.Max(0m, ReorderPoint - CurrentStock);
    }

    #endregion

    #region Sales DTOs

    /// <summary>
    /// Aggregated sales summary for dashboard (single query)
    /// </summary>
    public class SalesSummary
    {
        public decimal TotalSales { get; set; }
        public int SalesCount { get; set; }
        public decimal AverageSaleAmount { get; set; }
        public decimal PaidSalesTotal { get; set; }
        public decimal UnpaidSalesTotal { get; set; }
        public int UnpaidSalesCount { get; set; }
        public double PaymentRate => TotalSales > 0 ? (double)PaidSalesTotal / (double)TotalSales * 100 : 0;
    }

    /// <summary>
    /// Top selling product data with aggregated metrics
    /// </summary>
    public class TopSellingProduct
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal TotalQuantitySold { get; set; } // ✅ WEIGHT-BASED FIX: Changed from int to decimal to support weight-based products
        public decimal TotalRevenue { get; set; }
        public int SalesCount { get; set; }
        public decimal AverageQuantityPerSale => SalesCount > 0 ? TotalQuantitySold / SalesCount : 0; // ✅ WEIGHT-BASED FIX: Remove cast since TotalQuantitySold is now decimal
        public decimal AverageRevenuePerSale => SalesCount > 0 ? TotalRevenue / SalesCount : 0;
    }

    /// <summary>
    /// ✅ DASHBOARD OPTIMIZATION: Top selling product with category info (eliminates N+1 category lookups)
    /// </summary>
    public class TopSellingProductWithCategory
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalProfit { get; set; }
        public int SalesCount { get; set; }
        public decimal ProfitMargin => TotalRevenue > 0 ? (TotalProfit / TotalRevenue) * 100 : 0;
        public decimal AverageQuantityPerSale => SalesCount > 0 ? TotalQuantitySold / SalesCount : 0;
        public decimal AverageRevenuePerSale => SalesCount > 0 ? TotalRevenue / SalesCount : 0;
    }

    /// <summary>
    /// ✅ OPTIMIZATION: Category performance data with aggregated metrics (eliminates N+1 queries)
    /// </summary>
    public class CategoryPerformanceData
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public int ProductCount { get; set; }
        public int SalesCount { get; set; }
        public decimal AverageRevenuePerProduct => ProductCount > 0 ? TotalRevenue / ProductCount : 0;
        public decimal AverageQuantityPerProduct => ProductCount > 0 ? TotalQuantitySold / ProductCount : 0;
        public decimal AverageRevenuePerSale => SalesCount > 0 ? TotalRevenue / SalesCount : 0;
    }

    /// <summary>
    /// Product sales analytics with comprehensive metrics
    /// </summary>
    public class ProductSalesAnalytics
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public int TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalTransactions { get; set; }
        public decimal AverageQuantityPerTransaction { get; set; }
        public decimal AverageRevenuePerTransaction { get; set; }
        public decimal RevenuePercentage { get; set; }
        public int Rank { get; set; }
    }

    #endregion

    #region Dashboard DTOs

    /// <summary>
    /// Complete dashboard data loaded in optimized batch operation
    /// </summary>
    public class DashboardData
    {
        public SalesSummary SalesSummary { get; set; }
        public ProductStatisticsSummary ProductStatistics { get; set; }
        public IEnumerable<TopSellingProduct> TopSellingProducts { get; set; }
        public IEnumerable<LowStockAlert> LowStockProducts { get; set; }
        public DateTime GeneratedAt { get; set; }
    }

    #endregion

    #region Customer DTOs

    /// <summary>
    /// Minimal customer data for dropdowns and lists
    /// </summary>
    public class CustomerListItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public decimal TotalPurchases { get; set; }
        public DateTime LastPurchaseDate { get; set; }
    }

    /// <summary>
    /// Customer statistics for dashboard
    /// </summary>
    public class CustomerStatistics
    {
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; } // Customers with purchases in last 30 days
        public int NewCustomersThisMonth { get; set; }
        public decimal AverageCustomerValue { get; set; }
        public decimal TopCustomerValue { get; set; }
    }

    #endregion

    #region Inventory DTOs

    /// <summary>
    /// Inventory movement summary
    /// </summary>
    public class InventoryMovementSummary
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public int TotalIn { get; set; }
        public int TotalOut { get; set; }
        public int NetMovement { get; set; }
        public DateTime LastMovementDate { get; set; }
    }

    /// <summary>
    /// Expiring products alert data
    /// </summary>
    public class ExpiringProductAlert
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string SKU { get; set; }
        public DateTime ExpiryDate { get; set; }
        public decimal StockQuantity { get; set; }
        public int DaysUntilExpiry { get; set; }
        public decimal PotentialLoss { get; set; } // StockQuantity * PurchasePrice
    }

    #endregion

    #region Financial DTOs

    /// <summary>
    /// Financial summary for dashboard
    /// </summary>
    public class FinancialSummary
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal ProfitMargin => TotalRevenue > 0 ? GrossProfit / TotalRevenue * 100 : 0;
        public decimal AverageTransactionValue { get; set; }
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// Payment method breakdown
    /// </summary>
    public class PaymentMethodSummary
    {
        public string PaymentMethod { get; set; }
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public double Percentage { get; set; }
    }

    #endregion

    #region Performance Metrics

    /// <summary>
    /// Query performance metrics for monitoring
    /// </summary>
    public class QueryPerformanceMetrics
    {
        public string QueryName { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public int RecordsReturned { get; set; }
        public DateTime ExecutedAt { get; set; }
        public bool UsedCache { get; set; }
    }

    #endregion
}
