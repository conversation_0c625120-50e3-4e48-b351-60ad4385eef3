using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.ErrorHandling
{
    /// <summary>
    /// Represents the severity level of an error.
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// Low severity - minor issues that don't significantly impact functionality.
        /// </summary>
        Low = 1,

        /// <summary>
        /// Medium severity - issues that may impact some functionality but are recoverable.
        /// </summary>
        Medium = 2,

        /// <summary>
        /// High severity - significant issues that impact major functionality.
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical severity - severe issues that may cause system instability or data loss.
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// Represents the result of error handling operations.
    /// </summary>
    public class ErrorResult
    {
        /// <summary>
        /// Gets or sets the unique identifier for this error.
        /// </summary>
        public string ErrorId { get; set; }

        /// <summary>
        /// Gets or sets the severity level of the error.
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// Gets or sets the user-friendly error message.
        /// </summary>
        public string UserMessage { get; set; }

        /// <summary>
        /// Gets or sets whether the error is recoverable.
        /// </summary>
        public bool IsRecoverable { get; set; }

        /// <summary>
        /// Gets or sets the suggested action for the user.
        /// </summary>
        public string SuggestedAction { get; set; }

        /// <summary>
        /// Gets or sets additional metadata about the error.
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Represents detailed information about an error.
    /// </summary>
    public class ErrorDetails
    {
        /// <summary>
        /// Gets or sets the unique identifier for this error.
        /// </summary>
        public string ErrorId { get; set; }

        /// <summary>
        /// Gets or sets the exception that caused the error.
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// Gets or sets the context in which the error occurred.
        /// </summary>
        public string Context { get; set; }

        /// <summary>
        /// Gets or sets the severity level of the error.
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the error occurred.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the user-friendly error message.
        /// </summary>
        public string UserMessage { get; set; }

        /// <summary>
        /// Gets or sets the stack trace of the error.
        /// </summary>
        public string StackTrace { get; set; }

        /// <summary>
        /// Gets or sets the inner exception information.
        /// </summary>
        public string InnerException { get; set; }

        /// <summary>
        /// Gets or sets additional metadata about the error.
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Represents the result of an error recovery attempt.
    /// </summary>
    public class RecoveryResult
    {
        /// <summary>
        /// Gets or sets whether the recovery was successful.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets a message describing the recovery result.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets any data returned by the recovery operation.
        /// </summary>
        public object Data { get; set; }

        /// <summary>
        /// Gets or sets the number of retry attempts made.
        /// </summary>
        public int RetryAttempts { get; set; }

        /// <summary>
        /// Gets or sets the time taken for the recovery operation.
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Represents error statistics for monitoring and analysis.
    /// </summary>
    public class ErrorStatistics
    {
        /// <summary>
        /// Gets or sets the time range for these statistics.
        /// </summary>
        public TimeSpan TimeRange { get; set; }

        /// <summary>
        /// Gets or sets the total number of errors in the time range.
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// Gets or sets the number of critical errors.
        /// </summary>
        public int CriticalErrors { get; set; }

        /// <summary>
        /// Gets or sets the number of high severity errors.
        /// </summary>
        public int HighSeverityErrors { get; set; }

        /// <summary>
        /// Gets or sets the number of medium severity errors.
        /// </summary>
        public int MediumSeverityErrors { get; set; }

        /// <summary>
        /// Gets or sets the number of low severity errors.
        /// </summary>
        public int LowSeverityErrors { get; set; }

        /// <summary>
        /// Gets or sets the most common error types and their frequencies.
        /// </summary>
        public List<string> MostCommonErrors { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets error trends over time.
        /// </summary>
        public Dictionary<DateTime, int> ErrorTrends { get; set; } = new Dictionary<DateTime, int>();

        /// <summary>
        /// Gets the error rate per hour.
        /// </summary>
        public double ErrorRatePerHour => TimeRange.TotalHours > 0 ? TotalErrors / TimeRange.TotalHours : 0;

        /// <summary>
        /// Gets the percentage of critical errors.
        /// </summary>
        public double CriticalErrorPercentage => TotalErrors > 0 ? (double)CriticalErrors / TotalErrors * 100 : 0;
    }

    /// <summary>
    /// Interface for error recovery strategies.
    /// </summary>
    public interface IRecoveryStrategy
    {
        /// <summary>
        /// Executes the recovery strategy for the specified exception.
        /// </summary>
        /// <param name="exception">The exception to recover from</param>
        /// <param name="context">Additional context information</param>
        /// <returns>A RecoveryResult indicating the outcome</returns>
        Task<RecoveryResult> ExecuteAsync(Exception exception, string context);
    }

    /// <summary>
    /// Recovery strategy that attempts to retry the failed operation.
    /// </summary>
    public class RetryRecoveryStrategy : IRecoveryStrategy
    {
        private readonly int _maxRetries;
        private readonly TimeSpan _retryDelay;

        /// <summary>
        /// Initializes a new instance of the RetryRecoveryStrategy.
        /// </summary>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="retryDelay">Delay between retry attempts</param>
        public RetryRecoveryStrategy(int maxRetries = 3, TimeSpan? retryDelay = null)
        {
            _maxRetries = maxRetries;
            _retryDelay = retryDelay ?? TimeSpan.FromSeconds(1);
        }

        /// <summary>
        /// Executes the retry recovery strategy.
        /// </summary>
        public async Task<RecoveryResult> ExecuteAsync(Exception exception, string context)
        {
            var startTime = DateTime.Now;
            
            for (int attempt = 1; attempt <= _maxRetries; attempt++)
            {
                try
                {
                    await Task.Delay(_retryDelay);
                    
                    // In a real implementation, this would retry the original operation
                    // For now, we'll simulate a successful retry
                    if (attempt >= 2) // Simulate success on second attempt
                    {
                        return new RecoveryResult
                        {
                            Success = true,
                            Message = $"Operation succeeded after {attempt} attempts",
                            RetryAttempts = attempt,
                            Duration = DateTime.Now - startTime
                        };
                    }
                }
                catch (Exception retryException)
                {
                    if (attempt == _maxRetries)
                    {
                        return new RecoveryResult
                        {
                            Success = false,
                            Message = $"All {_maxRetries} retry attempts failed. Last error: {retryException.Message}",
                            RetryAttempts = attempt,
                            Duration = DateTime.Now - startTime
                        };
                    }
                }
            }

            return new RecoveryResult
            {
                Success = false,
                Message = $"All {_maxRetries} retry attempts failed",
                RetryAttempts = _maxRetries,
                Duration = DateTime.Now - startTime
            };
        }
    }

    /// <summary>
    /// Recovery strategy for database-related errors.
    /// </summary>
    public class DatabaseRecoveryStrategy : IRecoveryStrategy
    {
        /// <summary>
        /// Executes the database recovery strategy.
        /// </summary>
        public async Task<RecoveryResult> ExecuteAsync(Exception exception, string context)
        {
            var startTime = DateTime.Now;

            try
            {
                // In a real implementation, this would:
                // 1. Check database connectivity
                // 2. Attempt to reconnect if needed
                // 3. Retry the failed operation
                // 4. Fall back to cached data if available

                await Task.Delay(500); // Simulate recovery time

                return new RecoveryResult
                {
                    Success = true,
                    Message = "Database connection restored and operation completed successfully",
                    Duration = DateTime.Now - startTime
                };
            }
            catch (Exception recoveryException)
            {
                return new RecoveryResult
                {
                    Success = false,
                    Message = $"Database recovery failed: {recoveryException.Message}",
                    Duration = DateTime.Now - startTime
                };
            }
        }
    }
}
