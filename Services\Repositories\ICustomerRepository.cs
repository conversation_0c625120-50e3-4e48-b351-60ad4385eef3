using POSSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// Repository interface for Customer operations
    /// SAFE APPROACH: Works alongside existing DatabaseService
    /// </summary>
    public interface ICustomerRepository
    {
        // Core CRUD operations
        Task<Customer> GetByIdAsync(int id);
        Task<IEnumerable<Customer>> GetAllAsync();
        Task<IEnumerable<Customer>> GetPagedAsync(int page, int pageSize);
        Task<Customer> CreateAsync(Customer customer);
        Task UpdateAsync(Customer customer);
        Task DeleteAsync(int id);
        Task<bool> SoftDeleteAsync(int id);

        // Business queries
        Task<Customer> GetByLoyaltyCodeAsync(string loyaltyCode);
        Task<Customer> GetByEmailAsync(string email);
        Task<Customer> GetByPhoneAsync(string phone);
        Task<IEnumerable<Customer>> SearchAsync(string searchTerm, int maxResults = 50);

        // Loyalty operations
        Task UpdateLoyaltyPointsAsync(int customerId, int points);
        Task<IEnumerable<Customer>> GetTopLoyaltyCustomersAsync(int limit = 10);
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();

        // Statistics
        Task<int> GetTotalCountAsync();
        Task<int> GetActiveCountAsync();
        Task<decimal> GetTotalCustomerValueAsync();

        // Validation
        Task<bool> ExistsByEmailAsync(string email);
        Task<bool> ExistsByLoyaltyCodeAsync(string loyaltyCode);
        Task<bool> ExistsByIdAsync(int id);
    }
}
