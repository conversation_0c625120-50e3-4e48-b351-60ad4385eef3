-- SQLite Migration: Add Weight-Based Product Support
-- Description: Adds support for weight-based products with decimal quantities
-- Date: 2025-07-16

-- Note: SQLite has limited ALTER TABLE support, so we use a different approach

-- =====================================================
-- Step 1: Add IsWeightBased column to Products table
-- =====================================================

-- Check if column already exists
-- If this query returns results, the column already exists
-- SELECT name FROM pragma_table_info('Products') WHERE name = 'IsWeightBased';

-- Add the IsWeightBased column (SQLite will ignore if it already exists in newer versions)
ALTER TABLE Products ADD COLUMN IsWeightBased INTEGER DEFAULT 0;

-- =====================================================
-- Step 2: Update SaleItems table to support decimal quantities
-- =====================================================

-- SQLite approach: Create new table with correct schema, copy data, replace old table

-- Create new SaleItems table with decimal quantity support
CREATE TABLE IF NOT EXISTS SaleItems_New (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity REAL NOT NULL DEFAULT 1.0,  -- Changed from INTEGER to REAL for decimal support
    UnitPrice REAL NOT NULL,
    TotalPrice REAL NOT NULL,
    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id),
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Copy existing data from old table (if it exists)
INSERT OR IGNORE INTO SaleItems_New (Id, SaleId, ProductId, Quantity, UnitPrice, TotalPrice, CreatedAt, UpdatedAt)
SELECT Id, SaleId, ProductId, CAST(Quantity AS REAL), UnitPrice, TotalPrice, CreatedAt, UpdatedAt
FROM SaleItems
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='SaleItems');

-- Drop old table and rename new one
DROP TABLE IF EXISTS SaleItems;
ALTER TABLE SaleItems_New RENAME TO SaleItems;

-- =====================================================
-- Step 3: Update CartItems table to support decimal quantities
-- =====================================================

-- Create new CartItems table with decimal quantity support
CREATE TABLE IF NOT EXISTS CartItems_New (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CartId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity REAL NOT NULL DEFAULT 1.0,  -- Changed from INTEGER to REAL for decimal support
    UnitPrice REAL NOT NULL,
    TotalPrice REAL NOT NULL,
    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (CartId) REFERENCES Carts(Id),
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Copy existing data from old table (if it exists)
INSERT OR IGNORE INTO CartItems_New (Id, CartId, ProductId, Quantity, UnitPrice, TotalPrice, CreatedAt, UpdatedAt)
SELECT Id, CartId, ProductId, CAST(Quantity AS REAL), UnitPrice, TotalPrice, CreatedAt, UpdatedAt
FROM CartItems
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='CartItems');

-- Drop old table and rename new one
DROP TABLE IF EXISTS CartItems;
ALTER TABLE CartItems_New RENAME TO CartItems;

-- =====================================================
-- Step 4: Create sample weight-based products for testing
-- =====================================================

-- Insert sample weight-based products (only if Products table exists and has the new column)
INSERT OR IGNORE INTO Products (Name, SKU, Description, PurchasePrice, SellingPrice, CategoryId, IsWeightBased, StockQuantity, MinimumStock, IsActive, CreatedAt, UpdatedAt, Type)
SELECT 
    'Fresh Apples' as Name,
    'APPLE-001' as SKU,
    'Fresh red apples sold by weight' as Description,
    2.50 as PurchasePrice,
    4.99 as SellingPrice,
    1 as CategoryId,
    1 as IsWeightBased,
    100 as StockQuantity,
    10 as MinimumStock,
    1 as IsActive,
    datetime('now') as CreatedAt,
    datetime('now') as UpdatedAt,
    0 as Type
WHERE EXISTS (SELECT 1 FROM pragma_table_info('Products') WHERE name = 'IsWeightBased')
  AND NOT EXISTS (SELECT 1 FROM Products WHERE SKU = 'APPLE-001');

INSERT OR IGNORE INTO Products (Name, SKU, Description, PurchasePrice, SellingPrice, CategoryId, IsWeightBased, StockQuantity, MinimumStock, IsActive, CreatedAt, UpdatedAt, Type)
SELECT 
    'Ground Beef' as Name,
    'BEEF-001' as SKU,
    'Fresh ground beef sold by weight' as Description,
    8.99 as PurchasePrice,
    12.99 as SellingPrice,
    1 as CategoryId,
    1 as IsWeightBased,
    50 as StockQuantity,
    5 as MinimumStock,
    1 as IsActive,
    datetime('now') as CreatedAt,
    datetime('now') as UpdatedAt,
    0 as Type
WHERE EXISTS (SELECT 1 FROM pragma_table_info('Products') WHERE name = 'IsWeightBased')
  AND NOT EXISTS (SELECT 1 FROM Products WHERE SKU = 'BEEF-001');

INSERT OR IGNORE INTO Products (Name, SKU, Description, PurchasePrice, SellingPrice, CategoryId, IsWeightBased, StockQuantity, MinimumStock, IsActive, CreatedAt, UpdatedAt, Type)
SELECT 
    'Premium Rice' as Name,
    'RICE-001' as SKU,
    'Premium basmati rice sold by weight' as Description,
    3.99 as PurchasePrice,
    6.99 as SellingPrice,
    1 as CategoryId,
    1 as IsWeightBased,
    200 as StockQuantity,
    20 as MinimumStock,
    1 as IsActive,
    datetime('now') as CreatedAt,
    datetime('now') as UpdatedAt,
    0 as Type
WHERE EXISTS (SELECT 1 FROM pragma_table_info('Products') WHERE name = 'IsWeightBased')
  AND NOT EXISTS (SELECT 1 FROM Products WHERE SKU = 'RICE-001');

-- =====================================================
-- Step 5: Add sample barcodes for testing
-- =====================================================

-- Add barcodes for the sample weight-based products (if ProductBarcodes table exists)
INSERT OR IGNORE INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description, CreatedAt)
SELECT 
    p.Id as ProductId,
    '1111111111111' as Barcode,
    1 as IsPrimary,
    'Primary barcode for ' || p.Name as Description,
    datetime('now') as CreatedAt
FROM Products p
WHERE p.SKU = 'APPLE-001'
  AND EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='ProductBarcodes')
  AND NOT EXISTS (SELECT 1 FROM ProductBarcodes WHERE Barcode = '1111111111111');

INSERT OR IGNORE INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description, CreatedAt)
SELECT 
    p.Id as ProductId,
    '2222222222222' as Barcode,
    1 as IsPrimary,
    'Primary barcode for ' || p.Name as Description,
    datetime('now') as CreatedAt
FROM Products p
WHERE p.SKU = 'BEEF-001'
  AND EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='ProductBarcodes')
  AND NOT EXISTS (SELECT 1 FROM ProductBarcodes WHERE Barcode = '2222222222222');

INSERT OR IGNORE INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description, CreatedAt)
SELECT 
    p.Id as ProductId,
    '3333333333333' as Barcode,
    1 as IsPrimary,
    'Primary barcode for ' || p.Name as Description,
    datetime('now') as CreatedAt
FROM Products p
WHERE p.SKU = 'RICE-001'
  AND EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='ProductBarcodes')
  AND NOT EXISTS (SELECT 1 FROM ProductBarcodes WHERE Barcode = '3333333333333');

-- =====================================================
-- Verification: Show results
-- =====================================================

-- Show that the migration completed
SELECT 'Migration completed successfully!' as Result;

-- Show the new weight-based products
SELECT 'Sample weight-based products created:' as Info;
SELECT Id, Name, SKU, IsWeightBased, StockQuantity 
FROM Products 
WHERE IsWeightBased = 1;

-- Show products with barcodes
SELECT 'Products with barcodes:' as Info;
SELECT p.Name, pb.Barcode, p.IsWeightBased
FROM Products p
INNER JOIN ProductBarcodes pb ON p.Id = pb.ProductId
WHERE p.IsWeightBased = 1;
