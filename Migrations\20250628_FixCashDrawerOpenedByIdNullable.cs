using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class FixCashDrawerOpenedByIdNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop existing foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers");

            // Re-create foreign key with nullable reference (removing IsRequired)
            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers",
                column: "OpenedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers");

            // Re-create foreign key with required reference
            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_OpenedById",
                table: "CashDrawers",
                column: "OpenedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
