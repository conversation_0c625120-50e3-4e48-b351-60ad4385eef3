using POSSystem.Models;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    /// <summary>
    /// Adapter pattern to gradually migrate from DatabaseService to Repository pattern
    /// SAFE APPROACH: Allows incremental migration without breaking existing functionality
    /// ✅ FIX: Implements IDisposable for proper resource cleanup
    /// </summary>
    public class RepositoryServiceAdapter : IDisposable
    {
        private readonly IDatabaseService _databaseService;
        private readonly IProductRepository _productRepository;
        private readonly ISaleRepository _saleRepository;
        private readonly ICustomerRepository _customerRepository;

        // ✅ FIX: Add semaphore to prevent concurrent database operations
        private readonly SemaphoreSlim _databaseSemaphore = new SemaphoreSlim(1, 1);

        public RepositoryServiceAdapter(
            IDatabaseService databaseService,
            IProductRepository productRepository,
            ISaleRepository saleRepository,
            ICustomerRepository customerRepository)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _productRepository = productRepository;
            _saleRepository = saleRepository;
            _customerRepository = customerRepository;
        }

        /// <summary>
        /// Gets products using the new repository (better performance)
        /// Falls back to DatabaseService if repository fails
        /// </summary>
        public async Task<List<Product>> GetProductsAsync(bool useRepository = true)
        {
            if (useRepository && _productRepository != null)
            {
                try
                {
                    var products = await _productRepository.GetAllAsync();
                    return products.ToList();
                }
                catch (Exception ex)
                {
                    // Log error and fall back to DatabaseService
                    System.Diagnostics.Debug.WriteLine($"Repository failed, falling back to DatabaseService: {ex.Message}");
                }
            }

            // Fallback to existing DatabaseService
            return _databaseService.GetAllProducts();
        }

        /// <summary>
        /// Gets paged products using repository (much better performance)
        /// </summary>
        public async Task<List<Product>> GetProductsPagedAsync(int page, int pageSize)
        {
            if (_productRepository != null)
            {
                try
                {
                    var products = await _productRepository.GetPagedAsync(page, pageSize);
                    return products.ToList();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository paging failed: {ex.Message}");
                }
            }

            // Fallback: Get all and manually page (not ideal but safe)
            var allProducts = _databaseService.GetAllProducts();
            return allProducts.Skip((page - 1) * pageSize).Take(pageSize).ToList();
        }

        /// <summary>
        /// Search products using repository (better performance with barcode support)
        /// </summary>
        public async Task<List<Product>> SearchProductsAsync(string searchTerm, bool useRepository = true)
        {
            if (useRepository && _productRepository != null && !string.IsNullOrWhiteSpace(searchTerm))
            {
                try
                {
                    var products = await _productRepository.SearchAsync(searchTerm, 50);
                    return products.ToList();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository search failed: {ex.Message}");
                }
            }

            // Fallback to DatabaseService search
            return _databaseService.SearchProducts(searchTerm);
        }

        /// <summary>
        /// Gets low stock products using repository (better performance)
        /// </summary>
        public async Task<List<Product>> GetLowStockProductsAsync(bool useRepository = true)
        {
            if (useRepository && _productRepository != null)
            {
                try
                {
                    var products = await _productRepository.GetLowStockAsync();
                    return products.ToList();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository low stock query failed: {ex.Message}");
                }
            }

            // Fallback to DatabaseService
            return _databaseService.GetLowStockProducts();
        }

        /// <summary>
        /// Gets sales using repository (better performance)
        /// </summary>
        public async Task<List<Sale>> GetSalesAsync(DateTime startDate, DateTime endDate, bool useRepository = true)
        {
            if (useRepository && _saleRepository != null)
            {
                try
                {
                    var sales = await _saleRepository.GetByDateRangeAsync(startDate, endDate);
                    return sales.ToList();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository sales query failed: {ex.Message}");
                }
            }

            // Fallback to DatabaseService
            return await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
        }

        /// <summary>
        /// Gets customers using repository (better performance)
        /// </summary>
        public async Task<List<Customer>> GetCustomersAsync(bool useRepository = true)
        {
            if (useRepository && _customerRepository != null)
            {
                try
                {
                    var customers = await _customerRepository.GetAllAsync();
                    return customers.ToList();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository customers query failed: {ex.Message}");
                }
            }

            // Fallback to DatabaseService
            return _databaseService.GetAllCustomers();
        }

        /// <summary>
        /// Gets customer by loyalty code using repository (better performance)
        /// </summary>
        public async Task<Customer> GetCustomerByLoyaltyCodeAsync(string loyaltyCode, bool useRepository = true)
        {
            if (useRepository && _customerRepository != null && !string.IsNullOrWhiteSpace(loyaltyCode))
            {
                try
                {
                    return await _customerRepository.GetByLoyaltyCodeAsync(loyaltyCode);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository loyalty code query failed: {ex.Message}");
                }
            }

            // ✅ PERFORMANCE FIX: Use async version to prevent UI thread blocking
            return await _databaseService.GetCustomerByLoyaltyCodeAsync(loyaltyCode);
        }

        /// <summary>
        /// Gets product statistics using repository (much better performance)
        /// ✅ FIX: Added semaphore protection to prevent concurrent DbContext access
        /// </summary>
        public async Task<(int total, int lowStock, decimal inventoryValue)> GetProductStatisticsAsync(bool useRepository = true)
        {
            if (useRepository && _productRepository != null)
            {
                // ✅ FIX: Use semaphore to prevent concurrent database operations
                await _databaseSemaphore.WaitAsync().ConfigureAwait(false);
                try
                {
                    // ✅ FIX: Execute statistics queries sequentially to avoid DbContext conflicts
                    var total = await _productRepository.GetTotalCountAsync().ConfigureAwait(false);
                    var lowStock = await _productRepository.GetLowStockCountAsync().ConfigureAwait(false);
                    var inventoryValue = await _productRepository.GetTotalInventoryValueAsync().ConfigureAwait(false);

                    return (total, lowStock, inventoryValue);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Repository statistics query failed: {ex.Message}");
                }
                finally
                {
                    _databaseSemaphore.Release();
                }
            }

            // Fallback to DatabaseService (slower)
            var allProducts = _databaseService.GetAllProducts();
            var fallbackTotal = allProducts.Count;
            var fallbackLowStock = allProducts.Count(p => p.StockQuantity <= p.ReorderPoint);
            var fallbackInventoryValue = allProducts.Sum(p => p.StockQuantity * p.PurchasePrice);

            return (fallbackTotal, fallbackLowStock, fallbackInventoryValue);
        }

        /// <summary>
        /// Configuration method to enable/disable repository usage globally
        /// </summary>
        public static bool UseRepositories { get; set; } = true;

        /// <summary>
        /// Helper method to check if repositories are available and enabled
        /// </summary>
        private bool ShouldUseRepository(bool useRepository)
        {
            return UseRepositories && useRepository;
        }

        /// <summary>
        /// ✅ FIX: Implement IDisposable to properly clean up semaphore
        /// </summary>
        public void Dispose()
        {
            _databaseSemaphore?.Dispose();
        }
    }
}
