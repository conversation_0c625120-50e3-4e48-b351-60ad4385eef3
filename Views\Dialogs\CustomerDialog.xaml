<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.CustomerDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="750"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
    </UserControl.Resources>
    
    <materialDesign:Card Background="#FFFFFF"
                         UniformCornerRadius="16"
                         materialDesign:ElevationAssist.Elevation="Dp6"
                         MaxWidth="800"
                         Margin="16">
        <Grid>
            <!-- Header Section with Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{StaticResource PrimaryGradientBrush}" 
                    CornerRadius="16,16,0,0"
                    Padding="24,18">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountPlus" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,12,0"/>
                    <TextBlock x:Name="DialogTitle" 
                             Text="{DynamicResource AddNewCustomer}" 
                             FontSize="22"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section - Two-column layout -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled">
                <Grid Margin="24,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Left Column - Customer Information -->
                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,16,0">
                        <!-- First Name -->
                        <TextBox x:Name="txtFirstName"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource FirstName}"
                               Margin="0,0,0,16"/>
                        
                        <!-- Last Name -->
                        <TextBox x:Name="txtLastName"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource LastName}"
                               Margin="0,0,0,16"/>
                        
                        <!-- Email -->
                        <TextBox x:Name="txtEmail"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Email}"
                               Margin="0,0,0,16"/>
                        
                        <!-- Phone -->
                        <TextBox x:Name="txtPhone"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Phone}"
                               Margin="0,0,0,16"/>
                        
                        <!-- Address -->
                        <TextBox x:Name="txtAddress"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{DynamicResource Address}"
                               Height="80"
                               TextWrapping="Wrap"
                               AcceptsReturn="True"
                               Margin="0,0,0,16"/>
                        
                        <!-- Loyalty Code with Generate Button -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="txtLoyaltyCode"
                                   Grid.Column="0"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="{DynamicResource LoyaltyCode}"/>
                            <Button Content="{DynamicResource Generate}"
                                    Grid.Column="1"
                                    Margin="10,0,0,0"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Click="GenerateLoyaltyCode_Click"/>
                        </Grid>
                        
                        <!-- Is Active Checkbox -->
                        <CheckBox x:Name="chkIsActive"
                                 Style="{StaticResource MaterialDesignCheckBox}"
                                 Content="{DynamicResource CustomerIsActive}"
                                 IsChecked="True"
                                 Margin="0,0,0,16"/>
                    </StackPanel>
                    
                    <!-- Right Column - Loyalty Card Preview -->
                    <Grid Grid.Column="1" Grid.Row="0" Width="280" VerticalAlignment="Top">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="Loyalty Card Preview" 
                                         FontWeight="Medium" 
                                         Grid.Column="0"
                                         Margin="0,0,0,8"/>
                                
                                <!-- Flip button -->
                                <Button x:Name="btnFlipCard"
                                        Grid.Column="1"
                                        Style="{StaticResource AppSecondaryButtonStyle}"
                                        Height="36"
                                        Padding="8,0"
                                        Click="FlipCard_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FlipHorizontal" 
                                                               Width="16" 
                                                               Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,4,0"/>
                                        <TextBlock x:Name="btnFlipText" Text="View Back" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Card container with 3D transform capabilities -->
                            <Grid x:Name="cardPreviewContainer" Height="180" 
                                 Margin="0,0,0,16">
                                <!-- This Viewport3D provides 3D rotation capabilities -->
                                <Viewport3D x:Name="viewport3D" IsHitTestVisible="False"/>
                                
                                <!-- Front of card (shown by default) -->
                                <Border x:Name="frontCardPreview" 
                                        Style="{StaticResource LoyaltyCardStyle}"
                                        Height="160"
                                        Visibility="Visible">
                                    <Grid>
                                        <!-- Beige curved section on left -->
                                        <Border Background="#E1D0B3" 
                                                HorizontalAlignment="Left"
                                                Width="100"
                                                CornerRadius="8,0,70,8">
                                            <StackPanel x:Name="FrontLogoContainer"
                                                      VerticalAlignment="Center" 
                                                      HorizontalAlignment="Center">
                                                <TextBlock Text="LOGO" 
                                                         FontSize="18" 
                                                         FontWeight="Bold" 
                                                         Foreground="#192656"
                                                         HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                        
                                        <!-- Content section on right -->
                                        <StackPanel Margin="110,16,16,16">
                                            <!-- Customer name -->
                                            <TextBlock FontSize="18" 
                                                      FontWeight="Bold"
                                                      Foreground="White">
                                                <TextBlock.Text>
                                                    <MultiBinding StringFormat="{}{0} {1}">
                                                        <Binding ElementName="txtFirstName" Path="Text" />
                                                        <Binding ElementName="txtLastName" Path="Text" />
                                                    </MultiBinding>
                                                </TextBlock.Text>
                                            </TextBlock>
                                            
                                            <TextBlock Text="Loyalty Member" 
                                                      Foreground="#E1D0B3" 
                                                      FontSize="12"
                                                      Opacity="0.9"
                                                      Margin="0,0,0,16"/>
                                            
                                            <!-- Loyalty code with icon -->
                                            <StackPanel Orientation="Horizontal" 
                                                       Margin="0,4,0,0">
                                                <materialDesign:PackIcon Kind="Barcode" 
                                                                        Width="16" 
                                                                        Height="16"
                                                                        Foreground="#E1D0B3"/>
                                                <TextBlock Text="{Binding ElementName=txtLoyaltyCode, Path=Text, FallbackValue='00000000'}" 
                                                         Foreground="White" 
                                                         Margin="8,0,0,0"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                                
                                <!-- Back of card (hidden by default) -->
                                <Border x:Name="backCardPreview"
                                        Style="{StaticResource LoyaltyCardStyle}"
                                        Height="180"
                                        Visibility="Collapsed">
                                    <Grid>
                                        <!-- Top section with store details -->
                                        <DockPanel VerticalAlignment="Top"
                                                  Margin="16,16,16,0">
                                            <StackPanel x:Name="BackLogoContainer"
                                                      DockPanel.Dock="Left" 
                                                      Width="80">
                                            <TextBlock Text="LOGO" 
                                                     FontSize="14" 
                                                     FontWeight="Bold" 
                                                     Foreground="#E1D0B3"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="YOUR STORE" 
                                                     FontSize="8" 
                                                     Foreground="#E1D0B3"
                                                     HorizontalAlignment="Center"
                                                     TextWrapping="Wrap"
                                                     TextAlignment="Center"/>
                                        </StackPanel>
                                        
                                            <Rectangle DockPanel.Dock="Left" 
                                                     Width="1" 
                                                     Height="40" 
                                                 Fill="#E1D0B3" 
                                                     Margin="8,0"/>
                                            
                                            <StackPanel>
                                                <TextBlock Text="LOYALTY CARD" 
                                                         FontSize="12" 
                                                         FontWeight="Bold" 
                                                         Foreground="#E1D0B3"/>
                                                <TextBlock Text="{Binding ElementName=txtLoyaltyCode, Path=Text, FallbackValue='00000000'}" 
                                                         Foreground="White"
                                                         FontWeight="Medium" 
                                                         Margin="0,4,0,0"/>
                                            </StackPanel>
                                        </DockPanel>
                                        
                                        <!-- Barcode visualization -->
                                        <Border Background="#E1D0B3" 
                                                CornerRadius="0,0,8,8"
                                                VerticalAlignment="Bottom"
                                                Height="100">
                                            <Grid Margin="16,10,16,16">
                                                <!-- Simulated barcode -->
                                                <Canvas Height="44" VerticalAlignment="Top" HorizontalAlignment="Center" Width="248">
                                                    <Rectangle Canvas.Left="0" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="4" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="7" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="12" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="16" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="22" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="25" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="30" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="35" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="41" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="45" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="50" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="53" Width="5" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="60" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="64" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="67" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="72" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="76" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="82" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="85" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="90" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="95" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="101" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="105" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="110" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="113" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="117" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="123" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="126" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="130" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="134" Width="4" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="140" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="145" Width="3" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="150" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="153" Width="5" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="160" Width="2" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="164" Width="1" Height="44" Fill="#192656" />
                                                    <Rectangle Canvas.Left="167" Width="2" Height="44" Fill="#192656" />
                                                </Canvas>
                                                
                                                <!-- Loyalty code display -->
                                                <TextBlock Text="{Binding ElementName=txtLoyaltyCode, Path=Text, FallbackValue='00000000'}" 
                                                         FontSize="16"
                                                         FontWeight="Bold"
                                                         Foreground="#192656"
                                                         HorizontalAlignment="Center"
                                                         VerticalAlignment="Bottom"/>
                                                
                                                <TextBlock Text="Scan this barcode at checkout" 
                                                         FontSize="10"
                                                         Foreground="#192656"
                                                         Opacity="0.8"
                                                         HorizontalAlignment="Center"
                                                         VerticalAlignment="Bottom"
                                                         Margin="0,0,0,18"/>
                                            </Grid>
                                        </Border>
                                    </Grid>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Button Panel - Spans both columns -->
                    <Border Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="0,1,0,0"
                           Padding="0,20,0,0"
                           Margin="0,16,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Cancel Button -->
                            <Button x:Name="btnCancel" 
                                   Grid.Column="0"
                                   Content="{DynamicResource Cancel}"
                                   Style="{StaticResource AppSecondaryButtonStyle}"
                                   Height="44"
                                   Margin="0,0,8,0"
                                   HorizontalAlignment="Stretch"
                                   Click="BtnCancel_Click"/>
                            
                            <!-- Save Button -->
                            <Button x:Name="btnSave" 
                                   Grid.Column="1"
                                   Content="{DynamicResource Add}"
                                   Style="{StaticResource AppPrimaryButtonStyle}"
                                   Height="44"
                                   Margin="8,0,0,0"
                                   HorizontalAlignment="Stretch"
                                   Click="BtnSave_Click"/>
                        </Grid>
                    </Border>
                </Grid>
            </ScrollViewer>
        </Grid>
    </materialDesign:Card>
</UserControl> 