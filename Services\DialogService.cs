using System;
using System.Threading.Tasks;
using System.Windows;
using MaterialDesignThemes.Wpf;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    /// <summary>
    /// Central service for managing dialogs throughout the application.
    /// Ensures consistent dialog behavior across different views and layouts.
    /// </summary>
    public class DialogService : IDialogService
    {
        private const string MAIN_DIALOG_IDENTIFIER = "MainSalesDialog";

        // Public constructor for dependency injection
        public DialogService() { }
        
        /// <summary>
        /// Shows a dialog using the main application DialogHost
        /// </summary>
        /// <param name="content">The content to display in the dialog</param>
        /// <returns>The dialog result</returns>
        public async Task<object> ShowDialog(object content)
        {
            try
            {
                // Check if a dialog is already open to prevent conflicts
                if (DialogHost.IsDialogOpen(MAIN_DIALOG_IDENTIFIER))
                {
                    System.Diagnostics.Debug.WriteLine($"Dialog already open on {MAIN_DIALOG_IDENTIFIER}, cannot show new dialog");
                    return null;
                }

                return await DialogHost.Show(content, MAIN_DIALOG_IDENTIFIER);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Multiple viable DialogHosts"))
            {
                // Fallback to localized message box if there are DialogHost conflicts
                var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Dialog Error";
                var errorMessage = Application.Current.FindResource("DialogMultipleHostsError") as string ?? "Multiple dialog hosts detected. Please report this issue to the development team.";

                _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync(errorMessage, errorTitle));
                return null;
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected in DialogService: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
                return null;
            }
            catch (Exception ex)
            {
                // Log and show an error message for any other exceptions
                MessageBox.Show(
                    $"Error showing dialog: {ex.Message}",
                    "Dialog Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return null;
            }
        }
        
        /// <summary>
        /// Closes the currently open dialog
        /// </summary>
        public void CloseDialog()
        {
            try
            {
                DialogHost.Close(MAIN_DIALOG_IDENTIFIER);
            }
            catch (Exception ex)
            {
                // Log error but don't show message box to avoid dialog cascade
                Console.WriteLine($"Error closing dialog: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Closes the dialog with a specific result
        /// </summary>
        /// <param name="result">Result to return from the dialog</param>
        public void CloseDialogWithResult(object result)
        {
            try
            {
                DialogHost.Close(MAIN_DIALOG_IDENTIFIER, result);
            }
            catch (Exception ex)
            {
                // Log error but don't show message box to avoid dialog cascade
                Console.WriteLine($"Error closing dialog with result: {ex.Message}");
            }
        }

        // Interface implementation methods
        public async void ShowMessage(string message, string title = "Information")
        {
            await POSSystem.Helpers.LocalizedMessageBox.ShowInfoAsync(message, title);
        }

        public async void ShowError(string message, string title = "Error")
        {
            await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync(message, title);
        }

        public async void ShowWarning(string message, string title = "Warning")
        {
            await POSSystem.Helpers.LocalizedMessageBox.ShowWarningAsync(message, title);
        }

        public async Task<bool> ShowConfirmationAsync(string message, string title = "Confirm")
        {
            return await POSSystem.Helpers.LocalizedMessageBox.ShowConfirmationAsync(message, title);
        }

        public bool ShowConfirmation(string message, string title = "Confirm")
        {
            // Synchronous wrapper for backward compatibility
            return ShowConfirmationAsync(message, title).GetAwaiter().GetResult();
        }

        public string ShowInput(string message, string title = "Input", string defaultValue = "")
        {
            // Simple implementation using InputBox
            try
            {
                return Microsoft.VisualBasic.Interaction.InputBox(message, title, defaultValue);
            }
            catch
            {
                // Fallback if VB.NET InputBox is not available
                return defaultValue;
            }
        }

        public async Task ShowMessageAsync(string message, string title = "Information")
        {
            await Task.Run(() => ShowMessage(message, title));
        }
    }
}