using System;
using System.Collections.Generic;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Simple service locator for dependency injection
    /// This is a basic implementation for the POS system
    /// </summary>
    public class ServiceLocator
    {
        private static ServiceLocator _current;
        private readonly Dictionary<Type, object> _services;
        private readonly Dictionary<Type, Func<object>> _serviceFactories;

        private ServiceLocator()
        {
            _services = new Dictionary<Type, object>();
            _serviceFactories = new Dictionary<Type, Func<object>>();
        }

        /// <summary>
        /// Gets the current service locator instance
        /// </summary>
        public static ServiceLocator Current
        {
            get
            {
                if (_current == null)
                {
                    _current = new ServiceLocator();
                }
                return _current;
            }
        }

        /// <summary>
        /// Registers a singleton service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="instance">The service instance</param>
        public void RegisterInstance<T>(T instance) where T : class
        {
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            _services[typeof(T)] = instance;
        }

        /// <summary>
        /// Registers a service factory
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="factory">The factory function</param>
        public void RegisterFactory<T>(Func<T> factory) where T : class
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            _serviceFactories[typeof(T)] = () => factory();
        }

        /// <summary>
        /// Registers a transient service type
        /// </summary>
        /// <typeparam name="TInterface">The interface type</typeparam>
        /// <typeparam name="TImplementation">The implementation type</typeparam>
        public void RegisterTransient<TInterface, TImplementation>()
            where TInterface : class
            where TImplementation : class, TInterface, new()
        {
            _serviceFactories[typeof(TInterface)] = () => new TImplementation();
        }

        /// <summary>
        /// Gets a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        public T GetInstance<T>() where T : class
        {
            var type = typeof(T);

            // Check for registered instance first
            if (_services.TryGetValue(type, out var instance))
            {
                return (T)instance;
            }

            // Check for registered factory
            if (_serviceFactories.TryGetValue(type, out var factory))
            {
                return (T)factory();
            }

            // Try to create instance if it has a parameterless constructor
            try
            {
                return Activator.CreateInstance<T>();
            }
            catch
            {
                throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered and cannot be created.");
            }
        }

        /// <summary>
        /// Gets a service instance (non-generic version)
        /// </summary>
        /// <param name="serviceType">The service type</param>
        /// <returns>The service instance</returns>
        public object GetInstance(Type serviceType)
        {
            if (serviceType == null)
                throw new ArgumentNullException(nameof(serviceType));

            // Check for registered instance first
            if (_services.TryGetValue(serviceType, out var instance))
            {
                return instance;
            }

            // Check for registered factory
            if (_serviceFactories.TryGetValue(serviceType, out var factory))
            {
                return factory();
            }

            // Try to create instance if it has a parameterless constructor
            try
            {
                return Activator.CreateInstance(serviceType);
            }
            catch
            {
                throw new InvalidOperationException($"Service of type {serviceType.Name} is not registered and cannot be created.");
            }
        }

        /// <summary>
        /// Checks if a service is registered
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>True if the service is registered</returns>
        public bool IsRegistered<T>() where T : class
        {
            var type = typeof(T);
            return _services.ContainsKey(type) || _serviceFactories.ContainsKey(type);
        }

        /// <summary>
        /// Clears all registered services (useful for testing)
        /// </summary>
        public void Clear()
        {
            _services.Clear();
            _serviceFactories.Clear();
        }

        /// <summary>
        /// Resets the service locator to a new instance
        /// </summary>
        public static void Reset()
        {
            _current = new ServiceLocator();
        }

        /// <summary>
        /// Initializes the service locator with common POS system services
        /// This should be called during application startup
        /// </summary>
        public static void InitializePOSServices()
        {
            var locator = Current;

            try
            {
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] Starting POS services initialization...");

                // Check if services are already registered
                if (locator.IsRegistered<POSSystem.Services.DatabaseService>())
                {
                    System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] Services already initialized, skipping...");
                    return;
                }

                // Register core services
                var dbService = new POSSystem.Services.DatabaseService();
                locator.RegisterInstance(dbService);
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] DatabaseService registered");

                var authService = new POSSystem.Services.AuthenticationService(dbService);
                locator.RegisterInstance(authService);
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] AuthenticationService registered");

                var permissionsService = new POSSystem.Services.UserPermissionsService(dbService);
                locator.RegisterInstance(permissionsService);
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] UserPermissionsService registered");

                var notificationService = new POSSystem.Services.DraftInvoiceNotificationService(dbService, authService);
                locator.RegisterInstance(notificationService);
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] DraftInvoiceNotificationService registered");

                var draftInvoiceService = new POSSystem.Services.DraftInvoiceService(dbService, notificationService, permissionsService);
                locator.RegisterInstance(draftInvoiceService);
                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] DraftInvoiceService registered");

                System.Diagnostics.Debug.WriteLine("[SERVICE_LOCATOR] POS services initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SERVICE_LOCATOR] Error initializing POS services: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[SERVICE_LOCATOR] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
