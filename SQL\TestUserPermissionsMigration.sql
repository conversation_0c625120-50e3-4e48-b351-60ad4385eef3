-- =====================================================
-- Test UserPermissions Migration Script
-- =====================================================
-- This script tests if the UserPermissions table has the required invoice columns
-- and provides diagnostic information about the current state

-- Display current UserPermissions table structure
SELECT '=== CURRENT USERPERMISSIONS TABLE STRUCTURE ===' as Info;
PRAGMA table_info(UserPermissions);

-- Check if invoice permission columns exist
SELECT '=== CHECKING FOR INVOICE PERMISSION COLUMNS ===' as Info;

-- Test for each required column
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanCreateFullInvoices'
        ) THEN '✓ CanCreateFullInvoices column exists'
        ELSE '✗ CanCreateFullInvoices column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanCreateDraftInvoices'
        ) THEN '✓ CanCreateDraftInvoices column exists'
        ELSE '✗ CanCreateDraftInvoices column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanCompleteInvoiceDrafts'
        ) THEN '✓ CanCompleteInvoiceDrafts column exists'
        ELSE '✗ CanCompleteInvoiceDrafts column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanViewPendingDrafts'
        ) THEN '✓ CanViewPendingDrafts column exists'
        ELSE '✗ CanViewPendingDrafts column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanModifyInvoicePricing'
        ) THEN '✓ CanModifyInvoicePricing column exists'
        ELSE '✗ CanModifyInvoicePricing column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanSetPaymentTerms'
        ) THEN '✓ CanSetPaymentTerms column exists'
        ELSE '✗ CanSetPaymentTerms column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanSelectCustomersForInvoices'
        ) THEN '✓ CanSelectCustomersForInvoices column exists'
        ELSE '✗ CanSelectCustomersForInvoices column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanDeleteDraftInvoices'
        ) THEN '✓ CanDeleteDraftInvoices column exists'
        ELSE '✗ CanDeleteDraftInvoices column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanRejectDraftInvoices'
        ) THEN '✓ CanRejectDraftInvoices column exists'
        ELSE '✗ CanRejectDraftInvoices column MISSING'
    END as ColumnCheck;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pragma_table_info('UserPermissions') 
            WHERE name = 'CanManageInvoiceSettings'
        ) THEN '✓ CanManageInvoiceSettings column exists'
        ELSE '✗ CanManageInvoiceSettings column MISSING'
    END as ColumnCheck;

-- Show current user permissions data (if any exists)
SELECT '=== CURRENT USER PERMISSIONS DATA ===' as Info;
SELECT COUNT(*) as TotalUserPermissions FROM UserPermissions;

-- Show users and their roles
SELECT '=== USERS AND ROLES ===' as Info;
SELECT u.Id, u.Username, ur.Name as Role
FROM Users u
LEFT JOIN UserRoles ur ON u.UserRoleId = ur.Id
ORDER BY u.Id;

-- Show sample permissions (if columns exist)
SELECT '=== SAMPLE PERMISSIONS (if columns exist) ===' as Info;
-- This query will fail if columns don't exist, which is expected
-- SELECT u.Username, ur.Name as Role, 
--        up.CanCreateFullInvoices, up.CanCreateDraftInvoices, up.CanCompleteInvoiceDrafts
-- FROM Users u
-- LEFT JOIN UserRoles ur ON u.UserRoleId = ur.Id
-- LEFT JOIN UserPermissions up ON u.Id = up.UserId
-- LIMIT 5;
