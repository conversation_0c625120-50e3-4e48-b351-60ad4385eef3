using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class FavoriteService : IFavoriteService
    {
        private readonly POSDbContext _context;

        public FavoriteService(POSDbContext context)
        {
            _context = context;
        }

        public async Task<bool> AddFavoriteAsync(int userId, int productId)
        {
            try
            {
                var exists = await _context.UserFavorites
                    .AnyAsync(uf => uf.UserId == userId && uf.ProductId == productId);

                if (exists)
                    return false;

                var favorite = new UserFavorite
                {
                    UserId = userId,
                    ProductId = productId,
                    CreatedAt = DateTime.Now
                };

                _context.UserFavorites.Add(favorite);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> RemoveFavoriteAsync(int userId, int productId)
        {
            try
            {
                var favorite = await _context.UserFavorites
                    .FirstOrDefaultAsync(uf => uf.UserId == userId && uf.ProductId == productId);

                if (favorite == null)
                    return false;

                _context.UserFavorites.Remove(favorite);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<Product>> GetUserFavoritesAsync(int userId)
        {
            return await _context.UserFavorites
                .Where(uf => uf.UserId == userId)
                .Include(uf => uf.Product)
                .ThenInclude(p => p.PriceTiers.Where(pt => pt.IsActive)) // ✅ BULK PRICING FIX: Include pricing tiers
                .Include(uf => uf.Product.Category)
                .Include(uf => uf.Product.Barcodes)
                .Select(uf => uf.Product)
                .ToListAsync();
        }

        public async Task<bool> IsFavoriteAsync(int userId, int productId)
        {
            return await _context.UserFavorites
                .AnyAsync(uf => uf.UserId == userId && uf.ProductId == productId);
        }

        // Interface implementation methods (synchronous versions)
        public List<UserFavorite> GetUserFavorites(int userId)
        {
            return _context.UserFavorites
                .Where(uf => uf.UserId == userId)
                .ToList();
        }

        public void AddFavorite(int userId, int productId)
        {
            AddFavoriteAsync(userId, productId).Wait();
        }

        public void RemoveFavorite(int userId, int productId)
        {
            RemoveFavoriteAsync(userId, productId).Wait();
        }

        public bool IsFavorite(int userId, int productId)
        {
            return IsFavoriteAsync(userId, productId).Result;
        }
    }
}