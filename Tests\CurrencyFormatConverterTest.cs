using System;
using System.Globalization;
using System.Windows;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Converters;

namespace POSSystem.Tests
{
    [TestClass]
    public class CurrencyFormatConverterTest
    {
        private CurrencyFormatConverter _converter;

        [TestInitialize]
        public void Setup()
        {
            _converter = new CurrencyFormatConverter();
            
            // Initialize a mock Application.Current.Resources for testing
            if (Application.Current == null)
            {
                // Create a minimal WPF application for testing
                var app = new Application();
                app.Resources = new ResourceDictionary();
            }
        }

        [TestMethod]
        public void Convert_WithEnglishCurrencyFormat_ReturnsCorrectFormat()
        {
            // Arrange
            decimal testAmount = 123.45m;
            Application.Current.Resources["CurrencyFormat"] = "{0:N2} DA";
            Application.Current.Resources["CurrencySymbol"] = "DA";

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("123.45 DA", result);
        }

        [TestMethod]
        public void Convert_WithArabicCurrencyFormat_ReturnsCorrectFormat()
        {
            // Arrange
            decimal testAmount = 123.45m;
            Application.Current.Resources["CurrencyFormat"] = "{0:N2} د.ج";
            Application.Current.Resources["CurrencySymbol"] = "د.ج";

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("123.45 د.ج", result);
        }

        [TestMethod]
        public void Convert_WithFrenchCurrencyFormat_ReturnsCorrectFormat()
        {
            // Arrange
            decimal testAmount = 123.45m;
            Application.Current.Resources["CurrencyFormat"] = "{0:N2} DA";
            Application.Current.Resources["CurrencySymbol"] = "DA";

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("123.45 DA", result);
        }

        [TestMethod]
        public void Convert_WithNoCurrencyFormat_UsesFallback()
        {
            // Arrange
            decimal testAmount = 123.45m;
            Application.Current.Resources.Remove("CurrencyFormat");
            Application.Current.Resources["CurrencySymbol"] = "DA";

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("123.45 DA", result);
        }

        [TestMethod]
        public void Convert_WithNoCurrencySymbol_UsesDefaultDA()
        {
            // Arrange
            decimal testAmount = 123.45m;
            Application.Current.Resources.Remove("CurrencyFormat");
            Application.Current.Resources.Remove("CurrencySymbol");

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("123.45 DA", result);
        }

        [TestMethod]
        public void Convert_WithNullValue_ReturnsZeroWithCurrency()
        {
            // Arrange
            Application.Current.Resources["CurrencySymbol"] = "DA";

            // Act
            var result = _converter.Convert(null, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("0.00 DA", result);
        }

        [TestMethod]
        public void Convert_WithZeroValue_ReturnsZeroWithCurrency()
        {
            // Arrange
            decimal testAmount = 0m;
            Application.Current.Resources["CurrencyFormat"] = "{0:N2} DA";

            // Act
            var result = _converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("0.00 DA", result);
        }

        [TestMethod]
        public void ConvertBack_WithValidString_ReturnsDecimal()
        {
            // Arrange
            string testValue = "123.45 DA";

            // Act
            var result = _converter.ConvertBack(testValue, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(123.45m, result);
        }

        [TestMethod]
        public void ConvertBack_WithArabicString_ReturnsDecimal()
        {
            // Arrange
            string testValue = "123.45 د.ج";

            // Act
            var result = _converter.ConvertBack(testValue, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(123.45m, result);
        }

        [TestMethod]
        public void ConvertBack_WithInvalidString_ReturnsZero()
        {
            // Arrange
            string testValue = "invalid";

            // Act
            var result = _converter.ConvertBack(testValue, typeof(decimal), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(0.00m, result);
        }
    }
}
