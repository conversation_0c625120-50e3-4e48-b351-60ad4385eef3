<?xml version="1.0" encoding="utf-8"?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <!-- Common -->
    <system:String x:Key="AppName">Point of Sale System</system:String>
    <system:String x:Key="Loading">Loading...</system:String>
    <system:String x:Key="Save">Save</system:String>
    <system:String x:Key="Cancel">Cancel</system:String>
    <system:String x:Key="Delete">Delete</system:String>
    <system:String x:Key="Edit">Edit</system:String>
    <system:String x:Key="Add">Add</system:String>
    <system:String x:Key="Search">Search</system:String>
    <system:String x:Key="Clear">Clear</system:String>
    <system:String x:Key="Close">Close</system:String>
    <system:String x:Key="Confirm">Confirm</system:String>
    <system:String x:Key="Print">Print</system:String>
    <system:String x:Key="Export">Export</system:String>
    <system:String x:Key="Import">Import</system:String>
    <system:String x:Key="Refresh">Refresh</system:String>
    <system:String x:Key="Filter">Filter</system:String>

    <!-- Missing Navigation Resources -->
    <system:String x:Key="CustomProduct">Custom Product</system:String>
    <system:String x:Key="SaleNavigation">Sale Navigation</system:String>
    <system:String x:Key="PreviousSale">Previous Sale</system:String>
    <system:String x:Key="NextSale">Next Sale</system:String>
    <system:String x:Key="SaveChanges">Save Changes</system:String>
    <system:String x:Key="ExitEditMode">Exit Edit Mode</system:String>
    <system:String x:Key="Payment">Payment</system:String>
    <system:String x:Key="Sort">Sort</system:String>
    <system:String x:Key="Details">Details</system:String>
    <system:String x:Key="Status">Status</system:String>
    <system:String x:Key="Value">Value</system:String>
    <system:String x:Key="TodaysSales">Today's Sales</system:String>
    <system:String x:Key="BusinessOverview">Business Overview</system:String>
    <system:String x:Key="NoData">No Data</system:String>
    <system:String x:Key="NoCustomerSelected">No Customer Selected</system:String>
    <system:String x:Key="NoCustomerSelectedHint">Select a customer or continue as guest</system:String>
    <system:String x:Key="GuestCustomer">Guest Customer</system:String>
    <system:String x:Key="CustomerRequiredMessage">A customer must be selected for unpaid sales</system:String>
    
    <!-- Roles -->
    <system:String x:Key="RoleAdministrator">Administrator</system:String>
    <system:String x:Key="RoleManager">Manager</system:String>
    <system:String x:Key="RoleCashier">Cashier</system:String>
    <system:String x:Key="RoleInventory">Inventory Clerk</system:String>
    
    <!-- Stock Status -->
    <system:String x:Key="AllStock">All Stock</system:String>
    <system:String x:Key="LowStock">Low Stock</system:String>
    <system:String x:Key="OutOfStock">Out of Stock</system:String>
    <system:String x:Key="Actions">Actions</system:String>
    <system:String x:Key="LoginButton">Login</system:String>
    <system:String x:Key="CustomerLabel">Customer</system:String>
    <system:String x:Key="StartDate">Start Date</system:String>
    <system:String x:Key="EndDate">End Date</system:String>
    <system:String x:Key="TotalSales">Total Sales</system:String>
    <system:String x:Key="TotalPurchases">Total Purchases</system:String>
    <system:String x:Key="NetProfit">Net Profit</system:String>
    <system:String x:Key="NumberOfTransactions">Total Transactions</system:String>
    <system:String x:Key="Select">Select</system:String>
    <system:String x:Key="DA">DA</system:String>
    
    <!-- Keyboard Shortcuts -->
    <system:String x:Key="KeyboardShortcuts">Keyboard Shortcuts</system:String>
    <system:String x:Key="ProductDetails">View Product Details</system:String>
    <system:String x:Key="ProcessPayment">Process Payment</system:String>
    <system:String x:Key="RedeemPoints">Redeem Loyalty Points</system:String>
    <system:String x:Key="CartDiscount">Cart Discount</system:String>
    <system:String x:Key="ItemDiscount">Item Discount</system:String>
    <system:String x:Key="SetQuantity">Set Item Quantity</system:String>
    <system:String x:Key="LookupCustomer">Lookup Customer</system:String>
    <system:String x:Key="NewCart">New Cart</system:String>
    <system:String x:Key="IncreaseQuantity">Increase Quantity</system:String>
    <system:String x:Key="DecreaseQuantity">Decrease Quantity</system:String>
    <system:String x:Key="RemoveItem">Remove Item</system:String>
    
    <!-- Navigation -->
    <system:String x:Key="Dashboard">Dashboard</system:String>
    <system:String x:Key="Sales">Sales</system:String>
    <system:String x:Key="Purchase">Purchase</system:String>
    <system:String x:Key="Products">Products</system:String>
    <system:String x:Key="Categories">Categories</system:String>
    <system:String x:Key="Customers">Customers</system:String>
    <system:String x:Key="Suppliers">Suppliers</system:String>
    <system:String x:Key="Users">Users</system:String>
    <system:String x:Key="Reports">Reports</system:String>
    <system:String x:Key="Settings">Settings</system:String>
    <system:String x:Key="PurchaseOrders">Purchase Orders</system:String>
    <system:String x:Key="Inventory">Inventory</system:String>
    <system:String x:Key="Logout">Logout</system:String>
    
    <!-- Products View -->
    <system:String x:Key="TotalProducts">Total Products</system:String>
    <system:String x:Key="InStock">In Stock</system:String>
    <system:String x:Key="InventoryValue">Inventory Value</system:String>
    <system:String x:Key="InventoryCost">Inventory Cost</system:String>
    <system:String x:Key="AddProduct">Add Product</system:String>
    <system:String x:Key="EditProduct">Edit Product</system:String>
    <system:String x:Key="UpdateProduct">Update Product</system:String>
    <system:String x:Key="DeleteProduct">Delete Product</system:String>
    <system:String x:Key="ImportProducts">Import Products</system:String>
    <system:String x:Key="ExportProducts">Export Products</system:String>
    <system:String x:Key="SelectProductGeneral">Select Product</system:String>
    <system:String x:Key="SelectProductForStats">Select Product for Statistics</system:String>
    <system:String x:Key="ProductName">Product Name</system:String>
    <system:String x:Key="ProductCode">Product Code</system:String>
    <system:String x:Key="Barcode">Barcode</system:String>
    <system:String x:Key="Category">Category</system:String>
    <system:String x:Key="Price">Price</system:String>
    <system:String x:Key="Cost">Cost</system:String>
    <system:String x:Key="StockQuantity">Stock Quantity</system:String>
    <system:String x:Key="MinimumStock">Minimum Stock</system:String>
    <system:String x:Key="Supplier">Supplier</system:String>
    <system:String x:Key="Active">Active</system:String>
    <system:String x:Key="Inactive">Inactive</system:String>
    <system:String x:Key="LastUpdated">Last Updated</system:String>
    <system:String x:Key="PrintBarcodes">Print Barcodes</system:String>
    <system:String x:Key="PurchasePrice">Purchase Price</system:String>
    <system:String x:Key="SellingPrice">Selling Price</system:String>
    <system:String x:Key="ReorderPoint">Reorder Point</system:String>
    <system:String x:Key="UnitOfMeasure">Unit of Measure</system:String>
    <system:String x:Key="DeleteConfirmation">Are you sure you want to delete?</system:String>
    <system:String x:Key="CurrentStock">Current Stock</system:String>
    <system:String x:Key="Description">Description</system:String>
    <system:String x:Key="ManageBarcodes">Manage Barcodes</system:String>
    <system:String x:Key="UploadProductImage">Upload Product Image</system:String>
    <system:String x:Key="RemoveProductImage">Remove Product Image</system:String>
    <system:String x:Key="UploadImage">Upload Image</system:String>
    <system:String x:Key="NoImage">No Image</system:String>
    <system:String x:Key="ProductIsActive">Product is Active</system:String>
    <system:String x:Key="ProductHasExpiry">Product Has Expiry Date</system:String>
    <system:String x:Key="ExpiryDate">Expiry Date</system:String>
    <system:String x:Key="TrackBatches">Track Batches</system:String>
    
    <!-- Units of Measure -->
    <system:String x:Key="UnitOfMeasure_Piece">Piece</system:String>
    <system:String x:Key="UnitOfMeasure_Box">Box</system:String>
    <system:String x:Key="UnitOfMeasure_Pack">Pack</system:String>
    <system:String x:Key="UnitOfMeasure_Kilogram">Kilogram</system:String>
    <system:String x:Key="UnitOfMeasure_Gram">Gram</system:String>
    <system:String x:Key="UnitOfMeasure_Liter">Liter</system:String>
    <system:String x:Key="UnitOfMeasure_Milliliter">Milliliter</system:String>
    <system:String x:Key="UnitOfMeasure_Meter">Meter</system:String>
    <system:String x:Key="UnitOfMeasure_Centimeter">Centimeter</system:String>
    <system:String x:Key="UnitOfMeasure_Dozen">Dozen</system:String>
    <system:String x:Key="UnitOfMeasure_Bottle">Bottle</system:String>
    <system:String x:Key="UnitOfMeasure_Can">Can</system:String>
    <system:String x:Key="UnitOfMeasure_Carton">Carton</system:String>
    <system:String x:Key="loyaltySearch">Search by loyalty card</system:String>
    
    <!-- Messages -->
    <system:String x:Key="SaveSuccessful">Changes saved successfully</system:String>
    <system:String x:Key="SaveError">Error saving changes</system:String>
    <system:String x:Key="DeleteSuccess">Item deleted successfully</system:String>
    <system:String x:Key="DeleteError">Error deleting item</system:String>
    <system:String x:Key="ValidationError">Please fill in all required fields</system:String>
    <system:String x:Key="NoResults">No results found</system:String>
    <system:String x:Key="LoadingError">Error loading data</system:String>
    <system:String x:Key="RestartMessage">Some changes require restarting the application. Would you like to restart now?</system:String>
    <system:String x:Key="RestartTitle">Restart Required</system:String>
    <system:String x:Key="ErrorTitle">Error</system:String>
    <system:String x:Key="WarningTitle">Warning</system:String>
    <system:String x:Key="InfoTitle">Information</system:String>
    <system:String x:Key="SuccessTitle">Success</system:String>
    <system:String x:Key="ConfirmTitle">Confirm</system:String>
    <system:String x:Key="ErrorLoadingData">Error loading data: {0}</system:String>
    <system:String x:Key="ErrorSavingData">Error saving data: {0}</system:String>
    <system:String x:Key="ErrorProcessingRequest">Error processing request: {0}</system:String>
    <system:String x:Key="InvalidInputMessage">Invalid input. Please check your entries.</system:String>
    <system:String x:Key="UnsavedChanges">You have unsaved changes. Do you want to save them before continuing?</system:String>
    <system:String x:Key="UnsavedChangesTitle">Unsaved Changes</system:String>
    <system:String x:Key="SelectionRequired">Please make a selection to continue.</system:String>
    <system:String x:Key="OperationSuccessful">Operation completed successfully.</system:String>
    <system:String x:Key="OperationFailed">Operation failed. Please try again.</system:String>
    <system:String x:Key="ConfirmDelete">Are you sure you want to delete this item? This action cannot be undone.</system:String>
    <system:String x:Key="ConfirmCancel">Are you sure you want to cancel? Any unsaved changes will be lost.</system:String>
    <system:String x:Key="PercentageExceeds100">Discount percentage cannot exceed 100%</system:String>
    <system:String x:Key="DiscountExceedsPrice">Discount amount cannot exceed the original price</system:String>
    <system:String x:Key="InvalidDiscount">The discount cannot be applied with the specified parameters</system:String>
    <system:String x:Key="RequiresApproval">This discount requires manager approval</system:String>
    <system:String x:Key="FillRequiredFields">Please fill in all required fields</system:String>
    
    <!-- Settings -->
    <system:String x:Key="LanguageSettings">Language</system:String>
    <system:String x:Key="SelectLanguage">Select Display Language</system:String>
    <system:String x:Key="RestartRequired">Changes will take effect after restart</system:String>
    <system:String x:Key="RegionalSettings">Regional</system:String>
    <system:String x:Key="DateFormat">Date Format</system:String>
    <system:String x:Key="CurrencySymbol">DA</system:String>
    <system:String x:Key="CurrencyFormat">{0:N2} DA</system:String>
    <system:String x:Key="ThemeSettings">Theme</system:String>
    <system:String x:Key="ColorTheme">Color Theme</system:String>
    <system:String x:Key="SalesLayoutTheme">Sales Layout Theme</system:String>
    <system:String x:Key="ThemeDescription">Choose your preferred layout for the sales screen</system:String>
    <system:String x:Key="StandardLayout">Standard Layout</system:String>
    <system:String x:Key="CompactLayout">Compact Layout</system:String>
    <system:String x:Key="ModernLayout">Modern Layout</system:String>
    <system:String x:Key="GridLayout">Grid Layout</system:String>
    <system:String x:Key="ChangeLayout">Change Layout</system:String>
    <system:String x:Key="SelectLayout">Select Sales Layout Theme</system:String>
    <system:String x:Key="LayoutChangeRestart">The layout change will take effect after restarting the application.</system:String>
    <system:String x:Key="RestartNow">Would you like to restart now?</system:String>
    <system:String x:Key="ThemeChanged">Theme Changed</system:String>
    <system:String x:Key="ApplyChanges">Apply Changes</system:String>
    <system:String x:Key="CompanySettings">Company</system:String>
    <system:String x:Key="LoyaltyProgramSettings">Loyalty Program</system:String>
    <system:String x:Key="Language">Language</system:String>
    <system:String x:Key="Currency">Currency</system:String>
    <system:String x:Key="Theme">Theme</system:String>
    <system:String x:Key="ReceiptFooter">Receipt Footer</system:String>
    <system:String x:Key="LanguageChangeRestart">The language change will take effect after restarting the application.</system:String>
    <system:String x:Key="DatabaseSettings">Database</system:String>
    <system:String x:Key="DatabaseLocation">Database Location</system:String>
    <system:String x:Key="CurrentDatabaseLocation">Current Database Location</system:String>
    <system:String x:Key="Change">Change</system:String>
    <system:String x:Key="DatabaseLocationDescription">Select where to store the database file. The application will need to restart after changing the location.</system:String>
    
    <!-- Search and Filters -->
    <system:String x:Key="FilterByCategory">Filter by Category</system:String>
    <system:String x:Key="ProductSearchHint">Search products by name or barcode...</system:String>
    <system:String x:Key="SearchPlaceholder">Type to search...</system:String>
    <system:String x:Key="SortBy">Sort by</system:String>
    <system:String x:Key="ClearFilter">Clear Filter</system:String>
    <system:String x:Key="NoProductsFound">No Products Found</system:String>
    <system:String x:Key="NoProductsFoundHint">No products match the current filter. Try selecting a different category or clearing the search.</system:String>

    <!-- Pricing Tier Dialog -->
    <system:String x:Key="AddPricingTier">Add Pricing Tier</system:String>
    <system:String x:Key="EditPricingTier">Edit Pricing Tier</system:String>
    <system:String x:Key="QuantityRange">Quantity Range</system:String>
    <system:String x:Key="To">to</system:String>
    <system:String x:Key="PricingConfiguration">Pricing Configuration</system:String>
    <system:String x:Key="UnitPricing">Unit Pricing</system:String>
    <system:String x:Key="PackPricing">Pack Pricing</system:String>
    <system:String x:Key="TierDetails">Tier Details</system:String>
    <system:String x:Key="Options">Options</system:String>

    <!-- Pricing Tier Dialog Hints -->
    <system:String x:Key="MinimumQuantityHint">Minimum Quantity</system:String>
    <system:String x:Key="MaximumQuantityHint">Maximum Quantity (Optional)</system:String>
    <system:String x:Key="UnitPriceHint">Unit Price</system:String>
    <system:String x:Key="PackPriceOptionalHint">Pack Price (Optional)</system:String>
    <system:String x:Key="PackPriceRequiredHint">Pack Price (Required for pack pricing)</system:String>
    <system:String x:Key="TierNameHint">Tier Name (e.g., '5-Pack', 'Bulk Rate')</system:String>
    <system:String x:Key="DescriptionOptionalHint">Description (Optional)</system:String>
    <system:String x:Key="EffectiveDateHint">Effective Date (Optional)</system:String>
    <system:String x:Key="ExpirationDateHint">Expiration Date (Optional)</system:String>

    <!-- Product To Invoice Confirmation Dialog -->
    <system:String x:Key="CreateInvoice">Create Invoice</system:String>
    <system:String x:Key="CreateStockReservation">Create Stock Reservation</system:String>
    <system:String x:Key="QuantityHint">Quantity</system:String>
    <system:String x:Key="CustomerOptionalHint">Customer (Optional)</system:String>
    <system:String x:Key="UnknownProduct">Unknown Product</system:String>
    <system:String x:Key="NoCategory">No Category</system:String>
    <system:String x:Key="FullInvoicePermissionMessage">You can create a complete invoice with full control over pricing and payment terms.</system:String>
    <system:String x:Key="StockReservationPermissionMessage">This will create a stock reservation that adds inventory and requires admin completion. The reserved stock will be immediately available for sales, and an administrator will complete the final invoice processing.</system:String>

    <!-- Pending Drafts Panel -->
    <system:String x:Key="PendingDraftInvoices">Pending Draft Invoices</system:String>
    <system:String x:Key="NoPendingDraftInvoices">No pending draft invoices</system:String>
    <system:String x:Key="AllDraftInvoicesProcessed">All draft invoices have been processed</system:String>
    <system:String x:Key="Estimated">Estimated</system:String>
    <system:String x:Key="CompleteDraft">Complete Draft</system:String>
    <system:String x:Key="ViewAll">View All</system:String>

    <!-- Settings View -->
    <system:String x:Key="LanguageAndRegional">Language &amp; Regional</system:String>
    <system:String x:Key="DiscountPermissions">Discount Permissions</system:String>
    <system:String x:Key="ReceiptPrinting">Receipt Printing</system:String>
    <system:String x:Key="LanguageAndRegionalSettings">Language &amp; Regional Settings</system:String>
    <system:String x:Key="LanguageAndRegionalDescription">Configure language, date formats, currency, and regional preferences for your POS system</system:String>
    <system:String x:Key="LanguageConfiguration">Language Configuration</system:String>
    <system:String x:Key="SelectDisplayLanguageDescription">Select the display language for the application interface</system:String>
    <system:String x:Key="RegionalConfiguration">Regional Configuration</system:String>
    <system:String x:Key="RegionalConfigurationDescription">Configure date formats, currency, and number formatting</system:String>
    <system:String x:Key="FormatPreview">Format Preview</system:String>
    <system:String x:Key="FormatPreviewDescription">See how dates, numbers, and currency will be displayed</system:String>
     <system:String x:Key="CurrencyFormatLabel">Currency Format:</system:String>
    <system:String x:Key="NumberFormat">Number Format:</system:String>
    <system:String x:Key="ThemeAndAppearanceSettings">Theme &amp; Appearance Settings</system:String>
    <system:String x:Key="ThemeAndAppearanceDescription">Customize the visual appearance of your POS system with themes, colors, and layout options</system:String>
    <system:String x:Key="ThemeMode">Theme Mode</system:String>
    <system:String x:Key="ThemePresets">Theme Presets</system:String>
    <system:String x:Key="ThemePresetsDescription">Choose from predefined theme combinations</system:String>
    <system:String x:Key="ColorCustomization">Color Customization</system:String>
    <system:String x:Key="ColorCustomizationDescription">Customize the primary color scheme of your application</system:String>
    <system:String x:Key="CustomColor">Custom Color</system:String>
    <system:String x:Key="QuickColorPresets">Quick Color Presets</system:String>
    <system:String x:Key="SalesInterfaceLayout">Sales Interface Layout</system:String>
    <system:String x:Key="SalesInterfaceLayoutDescription">Choose the layout style for your sales interface</system:String>
    <system:String x:Key="LivePreview">Live Preview</system:String>
    <system:String x:Key="LivePreviewDescription">See how your theme changes will look</system:String>
    <system:String x:Key="SamplePOSInterface">Sample POS Interface</system:String>
    <system:String x:Key="ProductNameSample">Product Name: Sample Item</system:String>
    <system:String x:Key="PriceSample">Price: $19.99</system:String>
    <system:String x:Key="AddToCart">Add to Cart</system:String>
    <system:String x:Key="Available">Available:</system:String>
    <system:String x:Key="PrimaryThemeColor">Primary Theme Color</system:String>
    <system:String x:Key="CurrentThemeInfo">Current Theme Info</system:String>
    <system:String x:Key="Mode">Mode:</system:String>
    <system:String x:Key="Preset">Preset:</system:String>
    <system:String x:Key="Layout">Layout:</system:String>

    <!-- Regional Configuration -->
     <system:String x:Key="DateFormatLabel">Date Format:</system:String>
    <system:String x:Key="NumberFormatLabel">Number Format:</system:String>

    <!-- Theme Settings -->
     <system:String x:Key="ThemeModeLabel">Theme Mode</system:String>
    <system:String x:Key="ThemePresetsLabel">Theme Presets</system:String>
    <system:String x:Key="ColorCustomizationLabel">Color Customization</system:String>
    <system:String x:Key="CustomColorLabel">Custom Color</system:String>
    <system:String x:Key="QuickColorPresetsLabel">Quick Color Presets</system:String>
     <system:String x:Key="SampleProductName">Product Name: Sample Item</system:String>
    <system:String x:Key="SamplePrice">Price: $19.99</system:String>
     <system:String x:Key="SearchProductsHint">Search products...</system:String>
    <system:String x:Key="PrimaryThemeColorLabel">Primary Theme Color</system:String>
    <system:String x:Key="CurrentThemeInfoLabel">Current Theme Info</system:String>
    <system:String x:Key="ModeLabel">Mode:</system:String>
    <system:String x:Key="PresetLabel">Preset:</system:String>
    <system:String x:Key="LayoutLabel">Layout:</system:String>

    <!-- Company Settings -->
    <system:String x:Key="CompanyInformation">Company Information</system:String>
    <system:String x:Key="CompanyInformationDescription">Configure your business details, contact information, and branding for receipts and invoices</system:String>
    <system:String x:Key="BasicInformation">Basic Information</system:String>
    <system:String x:Key="AddressInformation">Address Information</system:String>
    <system:String x:Key="CompanyLogo">Company Logo</system:String>
    <system:String x:Key="CompanyLogoDescription">Upload your company logo for receipts and invoices</system:String>
    <system:String x:Key="NoLogo">No Logo</system:String>
    <system:String x:Key="UploadLogo">Upload Logo</system:String>
    <system:String x:Key="RemoveLogo">Remove Logo</system:String>

    <!-- Database Settings -->
    <system:String x:Key="DatabaseManagement">Database Management</system:String>
    <system:String x:Key="DatabaseManagementDescription">Manage database location, create backups, and restore data to ensure your business information is secure</system:String>
    <system:String x:Key="DatabaseLocationLabel">Database Location</system:String>
    <system:String x:Key="BackupAndRestore">Backup &amp; Restore</system:String>
    <system:String x:Key="BackupAndRestoreDescription">Create backups of your data and restore from previous backups</system:String>
    <system:String x:Key="CreateBackup">Create Backup</system:String>
    <system:String x:Key="CreateBackupDescription">Export your current database to a backup file</system:String>
    <system:String x:Key="RestoreBackup">Restore Backup</system:String>
    <system:String x:Key="RestoreBackupDescription">Import data from a previously created backup file</system:String>
    <system:String x:Key="ImportantInformation">Important Information</system:String>

    <!-- Receipt Printing Settings -->
    <system:String x:Key="ReceiptPrintingSettingsLabel">Receipt Printing Settings</system:String>
    <system:String x:Key="GeneralSettingsLabel">General Settings</system:String>
    <system:String x:Key="AutoPrintReceiptsLabel">Automatically print receipts after sale completion</system:String>
    <system:String x:Key="ShowPrintDialogLabel">Show print dialog before printing</system:String>
    <system:String x:Key="SaveReceiptsAsPDFLabel">Save receipts as PDF backup</system:String>
    <system:String x:Key="EnablePrintPreviewLabel">Enable print preview functionality</system:String>
    <system:String x:Key="PDFBackupDirectoryLabel">PDF Backup Directory</system:String>
    <system:String x:Key="Browse">Browse</system:String>
    <system:String x:Key="PrinterConfigurationLabel">Printer Configuration</system:String>
    <system:String x:Key="DefaultPrinterLabel">Default Printer</system:String>
    <system:String x:Key="PrinterTypeLabel">Printer Type</system:String>
    <system:String x:Key="PaperSizeLabel">Paper Size</system:String>
    <system:String x:Key="StandardPrinter">Standard Printer</system:String>
    <system:String x:Key="ThermalPrinter">Thermal Printer</system:String>
    <system:String x:Key="PDFExport">PDF Export</system:String>
    <system:String x:Key="A4">A4</system:String>
    <system:String x:Key="Letter">Letter</system:String>
    <system:String x:Key="Thermal80mm">Thermal 80mm</system:String>
    <system:String x:Key="Thermal58mm">Thermal 58mm</system:String>
    <system:String x:Key="ReceiptTemplateLabel">Receipt Template</system:String>
    <system:String x:Key="IncludeCompanyLogo">Include company logo</system:String>
    <system:String x:Key="IncludeCompanyInformation">Include company information</system:String>
    <system:String x:Key="IncludeCustomerInformation">Include customer information</system:String>
    <system:String x:Key="IncludeItemDetails">Include item details</system:String>
    <system:String x:Key="IncludePaymentInformation">Include payment information</system:String>
    <system:String x:Key="FontSizeLabel">Font Size</system:String>
    <system:String x:Key="FooterTextLabel">Footer Text</system:String>
    <system:String x:Key="TestPrintingLabel">Test Printing</system:String>
    <system:String x:Key="TestPrintingDescription">Test your receipt printing configuration with sample data</system:String>
    <system:String x:Key="TestPrint">Test Print</system:String>
    <system:String x:Key="PreviewTest">Preview Test</system:String>
    <system:String x:Key="SaveTestPDF">Save Test PDF</system:String>
    <system:String x:Key="TestConfiguration">Test Configuration</system:String>

    <!-- Discount Permissions -->
    <system:String x:Key="DiscountPermissionsManagement">Discount Permissions Management</system:String>
    <system:String x:Key="DiscountPermissionsManagementDescription">Configure discount limits and approval requirements for different user roles</system:String>
    <system:String x:Key="SelectUserRole">Select User Role</system:String>
    <system:String x:Key="DiscountTypePermissions">Discount Type Permissions</system:String>
    <system:String x:Key="LoadingDiscountPermissions">Loading discount permissions...</system:String>
    <system:String x:Key="ChooseRoleToConfigureHint">Choose a role to configure</system:String>
    <system:String x:Key="SelectRoleDescription">Select a user role to view and modify its discount permissions. Each role can have different limits for various discount types.</system:String>

    <!-- Discount Types -->
    <system:String x:Key="DiscountType">Discount Type</system:String>
    <system:String x:Key="Percentage">Percentage</system:String>
    <system:String x:Key="FixedAmount">Fixed Amount</system:String>
    <system:String x:Key="PriceOverride">Price Override</system:String>

    <!-- DataGrid Headers -->
     <system:String x:Key="MaxPercentage">Max %</system:String>
    <system:String x:Key="MaxAmount">Max Amount</system:String>
    <system:String x:Key="MinPricePercentage">Min Price %</system:String>
    <system:String x:Key="Approval">Approval</system:String>
    <system:String x:Key="Threshold">Threshold</system:String>

    <!-- Tooltips -->
    <system:String x:Key="EnableDisableDiscountTypeTooltip">Enable/Disable this discount type</system:String>
    <system:String x:Key="MaxPercentageTooltip">Maximum percentage discount allowed</system:String>
    <system:String x:Key="MaxFixedAmountTooltip">Maximum fixed amount discount allowed</system:String>
    <system:String x:Key="MinPricePercentageTooltip">Minimum price percentage after discount</system:String>
    <system:String x:Key="RequireApprovalTooltip">Require manager approval for this discount</system:String>
    <system:String x:Key="ApprovalThresholdTooltip">Amount threshold that requires approval</system:String>

    <!-- Configuration Guide -->
    <system:String x:Key="ConfigurationGuide">Configuration Guide:</system:String>
    <system:String x:Key="MaxPercentageGuide">Max %: Maximum percentage discount this role can apply</system:String>
    <system:String x:Key="MaxAmountGuide">Max Amount: Maximum fixed amount discount this role can apply</system:String>
    <system:String x:Key="MinPriceGuide">Min Price %: Minimum percentage of original price that must remain</system:String>
    <system:String x:Key="ApprovalGuide">Approval: Whether discounts above threshold require manager approval</system:String>
    <system:String x:Key="ThresholdGuide">Threshold: Amount above which approval is required</system:String>

    <!-- Loyalty Program -->
    <system:String x:Key="LoyaltyPrograms">Loyalty Programs</system:String>
    <system:String x:Key="ProgramTiers">Program Tiers</system:String>
    <system:String x:Key="PointsPerDA">Points per DA: </system:String>
    <system:String x:Key="ValuePerPointDA"> | Value per point: DA</system:String>
    <system:String x:Key="Separator"> | </system:String>
    <system:String x:Key="MinPoints">Min Points: </system:String>
    <system:String x:Key="Multiplier"> | Multiplier: </system:String>

    <!-- Program Form Fields -->
    <system:String x:Key="ProgramNameHint">Program Name</system:String>
    <system:String x:Key="DescriptionHint">Description</system:String>
    <system:String x:Key="PointsPerDollarHint">Points per Dollar</system:String>
    <system:String x:Key="ValuePerPointHint">Value per Point (DA)</system:String>
    <system:String x:Key="PointsExpiryHint">Points Expiry (months)</system:String>
    <system:String x:Key="MinimumPointsRedemptionHint">Minimum Points for Redemption</system:String>
    <system:String x:Key="ProgramActive">Program Active</system:String>

    <!-- Program Buttons -->
    <system:String x:Key="SaveProgram">Save Program</system:String>
    <system:String x:Key="AddNewProgram">Add New Program</system:String>
    <system:String x:Key="EditProgram">Edit Program</system:String>
    <system:String x:Key="DeactivateProgram">Deactivate Program</system:String>
    <system:String x:Key="RemoveProgram">Remove Program</system:String>

    <!-- Tier Form Fields -->
     <system:String x:Key="MinimumPointsRequiredHint">Minimum Points Required</system:String>
    <system:String x:Key="PointsMultiplierHint">Points Multiplier</system:String>
    <system:String x:Key="BenefitsDescriptionHint">Benefits Description</system:String>

    <!-- Tier Buttons -->
    <system:String x:Key="SaveTier">Save Tier</system:String>
    <system:String x:Key="AddNewTier">Add New Tier</system:String>

    <!-- Dialogs -->
    <system:String x:Key="ConfirmDialog">Confirmation</system:String>
    <system:String x:Key="ErrorDialog">Error</system:String>
    <system:String x:Key="WarningDialog">Warning</system:String>
    <system:String x:Key="InfoDialog">Information</system:String>
    <system:String x:Key="ProcessingRequest">Processing request...</system:String>
    <system:String x:Key="PleaseWait">Please wait...</system:String>
    
    <!-- Sales View -->
    <system:String x:Key="Cart">Cart</system:String>
    <system:String x:Key="CreateNewCart">New Cart</system:String>
    <system:String x:Key="CloseCart">Close Cart</system:String>
    <system:String x:Key="EmptyCart">Empty Cart</system:String>
     <system:String x:Key="RemoveFromCart">Remove from Cart</system:String>
    <system:String x:Key="Quantity">Quantity</system:String>
    <system:String x:Key="Total">Total</system:String>
    <system:String x:Key="Subtotal">Subtotal</system:String>
    <system:String x:Key="Discount">Discount</system:String>
    <system:String x:Key="Tax">Tax</system:String>
    <system:String x:Key="GrandTotal">Grand Total</system:String>
    <system:String x:Key="OrderSummary">Order Summary</system:String>
    
    <!-- Discount Dialog -->
    <system:String x:Key="ApplyDiscount">Apply Discount</system:String>
    <system:String x:Key="ApplyItemDiscount">Apply Item Discount</system:String>
    <system:String x:Key="ApplyCartDiscount">Apply Cart Discount</system:String>
     <system:String x:Key="DiscountValue">Discount Value</system:String>
    <system:String x:Key="DiscountReason">Reason</system:String>
    <system:String x:Key="DiscountComment">Comment (Optional)</system:String>
    <system:String x:Key="Apply">Apply</system:String>
    <system:String x:Key="InvalidDiscountPermission">This discount cannot be applied. Please check your permissions or discount limits.</system:String>
    
    <!-- Discount Types -->
    <system:String x:Key="DiscountTypePercentage">Percentage</system:String>
    <system:String x:Key="DiscountTypeFixedAmount">Fixed Amount</system:String>
    <system:String x:Key="DiscountTypePriceOverride">Price Override</system:String>
     <system:String x:Key="Reason">Reason</system:String>
    
    <!-- Loyalty Points -->
    <system:String x:Key="LoyaltyPointsRedemption">Redeem Loyalty Points</system:String>
    <system:String x:Key="AvailablePoints">Available Points</system:String>
    <system:String x:Key="ValuePerPoint">Value per Point</system:String>
    <system:String x:Key="PointsToRedeem">Points to Redeem</system:String>
    <system:String x:Key="DiscountValueLabel">Discount Value</system:String>
    <system:String x:Key="RedeemPointsButton">Redeem Points</system:String>
    <system:String x:Key="MinimumPointsRequired">Minimum Points Required</system:String>
    
    <!-- Discount Reasons -->
    <system:String x:Key="DiscountReasonMANAGER">Manager Special</system:String>
    <system:String x:Key="DiscountReasonDAMAGED">Damaged Item</system:String>
    <system:String x:Key="DiscountReasonPRICEMATCH">Price Match</system:String>
    <system:String x:Key="DiscountReasonCUSTOMER">Customer Satisfaction</system:String>
    <system:String x:Key="DiscountReasonPROMO">Promotion</system:String>
    <system:String x:Key="DiscountReasonBULK">Bulk Purchase</system:String>
    <system:String x:Key="DiscountReasonLOYALTY">Loyalty Discount</system:String>
    <system:String x:Key="DiscountReasonOVERRIDE">Price Override</system:String>
    
    <!-- Payment and Processing -->
    <system:String x:Key="PaymentTitle">Payment</system:String>
    <system:String x:Key="PaymentDialog">Payment Dialog</system:String>
    <system:String x:Key="PaymentMethod">Payment Method</system:String>
    <system:String x:Key="ProcessingPayment">Processing Payment</system:String>
    <system:String x:Key="PaymentDue">Payment Due</system:String>
    <system:String x:Key="PendingPayment">Pending Payment</system:String>
    <system:String x:Key="AmountTendered">Amount Tendered</system:String>
    <system:String x:Key="InsufficientAmount">Insufficient amount</system:String>
    <system:String x:Key="ProcessPaymentButton">Process Payment</system:String>
    <system:String x:Key="InvalidAmount">Invalid Amount</system:String>
    <system:String x:Key="AmountTenderedMustBeGreater">Amount tendered must be greater than or equal to the total amount.</system:String>
    <system:String x:Key="PleaseEnterValidAmount">Please enter a valid amount.</system:String>
    <system:String x:Key="TotalAmountDue">Total Amount Due</system:String>
    <system:String x:Key="RemainingAmount">Remaining Amount</system:String>
    <system:String x:Key="PaymentConfiguration">Payment Configuration</system:String>
    <system:String x:Key="ReadyToProcessPaymentFor">Ready to process payment for</system:String>
    <system:String x:Key="EnablePartialPaymentDescription">Enable to enter a custom payment amount less than the total</system:String>

    <!-- Categories View -->
    <system:String x:Key="PaymentStatusCompleted">Completed</system:String>
    <system:String x:Key="PaymentStatusVoided">Voided</system:String>
    <system:String x:Key="PaymentStatusRefunded">Refunded</system:String>
    <system:String x:Key="PaymentStatusPartiallyRefunded">Partially Refunded</system:String>
    <system:String x:Key="PaymentStatusPending">Pending</system:String>
    
    <system:String x:Key="CategoryName">Category Name</system:String>
    <system:String x:Key="CategoryDescription">Description</system:String>
    <system:String x:Key="ParentCategory">Parent Category</system:String>
    <system:String x:Key="AddCategory">Add Category</system:String>
    <system:String x:Key="EditCategory">Edit Category</system:String>
    <system:String x:Key="DeleteCategory">Delete Category</system:String>
    <system:String x:Key="CategoryIcon">Category Icon</system:String>
    <system:String x:Key="CategoryColor">Category Color</system:String>
    <system:String x:Key="CategoryProducts">Products in Category</system:String>
    <system:String x:Key="NoParentCategory">No Parent Category</system:String>
    <system:String x:Key="SelectParentCategory">Select Parent Category</system:String>
    <system:String x:Key="CategoryDetails">Category Details</system:String>
    <system:String x:Key="SubCategories">Sub Categories</system:String>
    <system:String x:Key="CategoryStatus">Status</system:String>
    <system:String x:Key="CategoryCreatedAt">Created At</system:String>
    <system:String x:Key="CategoryUpdatedAt">Last Updated</system:String>
    <system:String x:Key="CategoryTotalProducts">Total Products</system:String>
    <system:String x:Key="CategoryValue">Category Value</system:String>
    <system:String x:Key="UpdateCategory">Update Category</system:String>
    <system:String x:Key="TotalCategories">Total Categories</system:String>
    <system:String x:Key="AllCategories">All Categories</system:String>
    <system:String x:Key="ProductFilters">Product Filters</system:String>
    <system:String x:Key="Favorites">Favorites</system:String>
    <system:String x:Key="AddToFavorites">Add to Favorites</system:String>
    <system:String x:Key="Popular">Popular</system:String>
    
    <!-- Purchase Orders View -->
    <system:String x:Key="PurchaseOrdersManagement">Purchase Orders Management</system:String>
    <system:String x:Key="NewPurchaseOrder">New Purchase Order</system:String>
    <system:String x:Key="UnpaidOrders">Unpaid Orders</system:String>
    <system:String x:Key="AmountDue">Amount Due</system:String>
    <system:String x:Key="DueThisWeek">Due This Week</system:String>
    <system:String x:Key="MonthlyOrders">Monthly Orders</system:String>
    <system:String x:Key="AllOrders">All Orders</system:String>
    <system:String x:Key="Paid">Paid</system:String>
    <system:String x:Key="PurchaseOrderPendingPayment">Pending Payment</system:String>
    <system:String x:Key="PurchaseOrderPaymentDue">Payment Due</system:String>
    <system:String x:Key="Overdue">Overdue</system:String>
    <system:String x:Key="Cancelled">Cancelled</system:String>
    <system:String x:Key="PurchaseOrdersList">Purchase Orders List</system:String>
    <system:String x:Key="OrderNumber">Order #</system:String>
    <system:String x:Key="OrderDate">Order Date</system:String>
    <system:String x:Key="DueDate">Due Date</system:String>
    <system:String x:Key="TotalAmount">Total Amount</system:String>
    
    <!-- Customers View -->
    <system:String x:Key="CustomerStatistics">Customer Statistics</system:String>
    <system:String x:Key="TotalCustomers">Total Customers</system:String>
    <system:String x:Key="ActiveCustomers">Active Customers</system:String>
    <system:String x:Key="TotalRevenue">Total Revenue</system:String>
    <system:String x:Key="SearchCustomersHint">Search customers by name, email, or phone...</system:String>
    <system:String x:Key="AllCustomers">All Customers</system:String>
    <system:String x:Key="ActiveOnly">Active Only</system:String>
    <system:String x:Key="ID">ID</system:String>
    <system:String x:Key="FirstName">First Name</system:String>
    <system:String x:Key="LastName">Last Name</system:String>
    <system:String x:Key="Email">Email</system:String>
    <system:String x:Key="Phone">Phone</system:String>
    <system:String x:Key="LoyaltyPoints">Loyalty Points</system:String>
    <system:String x:Key="TotalSpent">Total Spent</system:String>
    <system:String x:Key="AddNewCustomer">Add New Customer</system:String>
    <system:String x:Key="Address">Address</system:String>
    <system:String x:Key="LoyaltyCode">Loyalty Code</system:String>
    <system:String x:Key="Generate">Generate</system:String>
    <system:String x:Key="CustomerIsActive">Customer is Active</system:String>

    <!-- Report Column Headers -->
    <system:String x:Key="CustomerName">Customer Name</system:String>
    <system:String x:Key="TransactionCount">Transaction Count</system:String>
    <system:String x:Key="AverageSpent">Average Spent</system:String>
    <system:String x:Key="LastPurchaseDate">Last Purchase Date</system:String>
    <system:String x:Key="SupplierName">Supplier Name</system:String>
    <system:String x:Key="OrderCount">Order Count</system:String>
    <system:String x:Key="AverageOrderValue">Average Order Value</system:String>
    <system:String x:Key="LastOrderDate">Last Order Date</system:String>

    <!-- Users View -->
    <system:String x:Key="SearchUsersHint">Search users by username, name, or email...</system:String>
    <system:String x:Key="Username">Username</system:String>
    <system:String x:Key="Password">Password</system:String>
    <system:String x:Key="FullName">Full Name</system:String>
    <system:String x:Key="Role">Role</system:String>
    <system:String x:Key="ModifyUser">Edit User</system:String>
    <system:String x:Key="AddUser">Add User</system:String>
    <system:String x:Key="AddNewUser">Add New User</system:String>
    <system:String x:Key="UserIsActive">User is Active</system:String>
    <system:String x:Key="Update">Update</system:String>
    <system:String x:Key="UserStatistics">User Statistics</system:String>
    <system:String x:Key="TotalUsers">Total Users</system:String>
    <system:String x:Key="ActiveUsers">Active Users</system:String>
    
    <!-- Suppliers View -->
    <system:String x:Key="SupplierStatistics">Supplier Statistics</system:String>
    <system:String x:Key="TotalSuppliers">Total Suppliers</system:String>
    <system:String x:Key="ActiveSuppliers">Active Suppliers</system:String>
    <system:String x:Key="SearchSuppliersHint">Search suppliers by name, contact person, or email...</system:String>
    <system:String x:Key="AllSuppliers">All Suppliers</system:String>
    <system:String x:Key="CompanyName">Company Name</system:String>
    <system:String x:Key="ContactPerson">Contact Person</system:String>
    <system:String x:Key="Website">Website</system:String>
    <system:String x:Key="Notes">Notes</system:String>
    <system:String x:Key="AddNewSupplier">Add New Supplier</system:String>
    <system:String x:Key="EditSupplier">Edit Supplier</system:String>
    <system:String x:Key="AddSupplier">Add Supplier</system:String>
    <system:String x:Key="SupplierIsActive">Supplier is Active</system:String>
    
    <!-- Cash Drawer Management -->
    <system:String x:Key="CashDrawerManagement">Cash Drawer Management</system:String>
    <system:String x:Key="OpenDrawer">Open Drawer</system:String>
    <system:String x:Key="CloseDrawer">Close Drawer</system:String>
    <system:String x:Key="ViewHistory">View History</system:String>
    <system:String x:Key="BusinessExpenses">Business Expenses</system:String>
    <system:String x:Key="Transactions">Transactions</system:String>
    <system:String x:Key="AddPayout">Add Payout</system:String>
    <system:String x:Key="Time">Time</system:String>
    <system:String x:Key="Type">Type</system:String>
    <system:String x:Key="Amount">Amount</system:String>
    <system:String x:Key="Reference">Reference</system:String>
    <system:String x:Key="PerformedBy">Performed By</system:String>
    <system:String x:Key="OpeningInfo">Opening Info</system:String>
    <system:String x:Key="OpenedBy">Opened by</system:String>
    <system:String x:Key="OpenedAt">Opened at</system:String>
    <system:String x:Key="OpeningBalance">Opening Balance</system:String>
    <system:String x:Key="CurrentTotals">Current Totals</system:String>
    <system:String x:Key="CashSales">Cash Sales</system:String>
    <system:String x:Key="Payouts">Payouts</system:String>
    <system:String x:Key="ExpectedBalance">Expected Balance</system:String>
    <system:String x:Key="ActualBalance">Actual Balance</system:String>
    <system:String x:Key="EnterActualBalance">Enter actual balance...</system:String>
    <system:String x:Key="Difference">Difference: {0}</system:String>
    
    <!-- Unpaid Sales Management -->
    <system:String x:Key="UnpaidSalesManagement">Unpaid Sales Management</system:String>
    <system:String x:Key="ManageUnpaidTransactionsDescription">Manage and process unpaid transactions</system:String>
    <system:String x:Key="TotalUnpaidAmount">Total Unpaid Amount</system:String>
    <system:String x:Key="OverdueSalesAlert">Overdue Sales Alert</system:String>
    <system:String x:Key="SalesPastDue">Sales Past Due</system:String>
    <system:String x:Key="UnpaidSalesList">Unpaid Sales List</system:String>
    <system:String x:Key="SearchByInvoiceOrCustomer">Search by invoice or customer</system:String>
    <system:String x:Key="FilterByStatus">Filter by status</system:String>
    <system:String x:Key="All">All</system:String>
    <system:String x:Key="DueToday">Due Today</system:String>
    <system:String x:Key="InvoiceNumber">Invoice #</system:String>
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="Customer">Customer</system:String>
    <system:String x:Key="SaleDetails">Sale Details</system:String>
    <system:String x:Key="SaleItems">Sale Items</system:String>
    <system:String x:Key="Product">Product</system:String>
    <system:String x:Key="UnitPrice">Unit Price</system:String>
    <system:String x:Key="PrintSaleDetails">Print Sale Details</system:String>
    <system:String x:Key="SaleIsOverdue">This sale is overdue!</system:String>
    <system:String x:Key="SaleDate">Sale Date</system:String>
    
    <!-- Reports -->
    <system:String x:Key="ReportsManagement">Reports Management</system:String>
    <system:String x:Key="ReportType">Report Type</system:String>
    <system:String x:Key="ExportToExcel">Export to Excel</system:String>
    <system:String x:Key="LoadingReport">Loading report data...</system:String>
    <system:String x:Key="TotalTransactions">Total Transactions</system:String>
    <system:String x:Key="SalesSummary">Sales Summary</system:String>
    <system:String x:Key="PurchaseOrdersSummary">Purchase Orders Summary</system:String>
    <system:String x:Key="InventoryStatus">Inventory Status</system:String>
    <system:String x:Key="TopProducts">Top Products</system:String>
    <system:String x:Key="CustomerActivity">Customer Activity</system:String>
    <system:String x:Key="SupplierActivity">Supplier Activity</system:String>
    
    <!-- Report Types -->
    <system:String x:Key="SalesReport">Sales Report</system:String>
    <system:String x:Key="PurchasesReport">Purchases Report</system:String>
    <system:String x:Key="InventoryReport">Inventory Report</system:String>
    <system:String x:Key="CustomerReport">Customer Report</system:String>
    <system:String x:Key="SupplierReport">Supplier Report</system:String>
    <system:String x:Key="ProfitReport">Profit Report</system:String>
    <system:String x:Key="TaxReport">Tax Report</system:String>
    <system:String x:Key="PaymentMethodReport">Payment Method Report</system:String>
    
    <!-- Sales History -->
    <system:String x:Key="SalesHistory">Sales History</system:String>
    <system:String x:Key="Items">Items</system:String>
    <system:String x:Key="ViewDetails">View Details</system:String>
    <system:String x:Key="Period">Period</system:String>
    <system:String x:Key="PeriodFilter">Period Filter</system:String>
    <system:String x:Key="SalesHistorySearch">Search by invoice number, customer, or status...</system:String>
    <system:String x:Key="SalesList">List of Sales</system:String>

    <!-- Time Periods -->
    <system:String x:Key="TimePeriod_Today">Today</system:String>
    <system:String x:Key="TimePeriod_Yesterday">Yesterday</system:String>
    <system:String x:Key="TimePeriod_Week">Week</system:String>
    <system:String x:Key="TimePeriod_Month">Month</system:String>
    <system:String x:Key="TimePeriod_Year">Year</system:String>
    <system:String x:Key="TimePeriod_Quarter">Quarter</system:String>
    <system:String x:Key="TimePeriod_Custom">Custom</system:String>
    <system:String x:Key="TimePeriod_Last7Days">Last 7 Days</system:String>
    <system:String x:Key="TimePeriod_Last30Days">Last 30 Days</system:String>
    <system:String x:Key="TimePeriod_Last90Days">Last 90 Days</system:String>
    <system:String x:Key="TimePeriod_ThisMonth">This Month</system:String>
    <system:String x:Key="TimePeriod_LastMonth">Last Month</system:String>
    <system:String x:Key="TimePeriod_ThisYear">This Year</system:String>
    <system:String x:Key="TimePeriod_ThisWeek">This Week</system:String>
    
    <!-- Void/Refund -->
    <system:String x:Key="VoidRefund">Void/Refund</system:String>
    <system:String x:Key="VoidRefundSale">Void/Refund Sale</system:String>
    <system:String x:Key="VoidRefundConfirmation">Are you sure you want to void/refund sale with invoice number</system:String>
    <system:String x:Key="VoidRefundConfirmationPrefix">Are you sure you want to void/refund sale with invoice number </system:String>
    <system:String x:Key="VoidRefundConfirmationSuffix">?</system:String>
    <system:String x:Key="VoidRefundReason">Select Reason</system:String>
    <system:String x:Key="SelectReason">Select Reason</system:String>
    <system:String x:Key="AdditionalComments">Additional Comments</system:String>
    <system:String x:Key="VoidSale">Void Sale</system:String>
    <system:String x:Key="FullRefund">Full Refund</system:String>
    <system:String x:Key="PartialRefund">Partial Refund</system:String>
    <system:String x:Key="PleaseProvideReason">Please provide a reason for this action.</system:String>
    <system:String x:Key="RefundAmount">Refund Amount</system:String>
    <system:String x:Key="InvalidRefundAmount">Invalid refund amount. Amount must be greater than 0 and less than or equal to the total sale amount.</system:String>
    <system:String x:Key="SaleVoided">Sale has been voided successfully.</system:String>
    <system:String x:Key="SaleRefunded">Sale has been refunded successfully.</system:String>
    <system:String x:Key="AlreadyVoidedOrRefunded">This sale has already been voided or refunded.</system:String>
    
    <!-- Dashboard -->
    <system:String x:Key="DashboardOverview">Dashboard Overview</system:String>
    <system:String x:Key="LoadingDashboardData">Loading dashboard data...</system:String>

    <!-- Quick Stats -->
    <system:String x:Key="VsPrevious">vs previous</system:String>
    <system:String x:Key="GrossProfit">Gross Profit</system:String>
    <system:String x:Key="ProfitMargin">Margin</system:String>
    <system:String x:Key="CustomMetrics">Custom Metrics</system:String>

    <!-- Stock Management -->
    <system:String x:Key="StockExpiry">Stock Expiry</system:String>
    <system:String x:Key="ProductsExpiringSoon">products expiring soon</system:String>
    <system:String x:Key="ProductsExpired">products expired</system:String>
    <system:String x:Key="LowStockAlert">Low Stock</system:String>
    <system:String x:Key="ItemsLowStock">items low on stock</system:String>
    <system:String x:Key="ItemsOutOfStock">items out of stock</system:String>

    <!-- Orders -->
    <system:String x:Key="OverdueOrders">Overdue Orders</system:String>
    <system:String x:Key="OverdueOrdersCount">overdue orders</system:String>
    <system:String x:Key="TotalOverdueValue">Total value: {0}</system:String>

    <!-- Charts -->
    <system:String x:Key="TrendAnalysis">Trend Analysis</system:String>
    <system:String x:Key="LineChart">Line Chart</system:String>
    <system:String x:Key="BarChart">Bar Chart</system:String>
    <system:String x:Key="ResetZoom">Reset Zoom</system:String>
    <system:String x:Key="Hour">Hour</system:String>

    <!-- Product Performance -->
    <system:String x:Key="ProductPerformance">Product Performance</system:String>
    <system:String x:Key="Revenue">Revenue</system:String>
    <system:String x:Key="Profit">Profit</system:String>
    <system:String x:Key="Margin">Margin</system:String>
    <system:String x:Key="ItemsSold">Items Sold</system:String>

    <!-- Category Performance -->
    <system:String x:Key="CategoryPerformance">Category Performance</system:String>

    <!-- Customer Insights -->
    <system:String x:Key="CustomerDemographics">Customer Demographics</system:String>
    <system:String x:Key="TopCustomers">Top Customers</system:String>
    <system:String x:Key="NumberOfCustomers">Number of Customers</system:String>
    <system:String x:Key="Orders">Orders</system:String>
    <system:String x:Key="AvgOrderValue">Average Order Value</system:String>
    <system:String x:Key="ItemsPurchased">Items Purchased</system:String>

    <!-- User Performance -->
    <system:String x:Key="UserPerformance">User Performance</system:String>
    <system:String x:Key="SalesPerformance">Sales Performance</system:String>
    <system:String x:Key="CustomersServed">Customers Served</system:String>
    <system:String x:Key="ConversionRate">Conversion Rate</system:String>
    <system:String x:Key="SalesAmount">Sales Amount</system:String>
    <system:String x:Key="OfTarget">% of target</system:String>

    <!-- Barcode Printing Window -->
    <system:String x:Key="BarcodePrintingSearchHint">Search products...</system:String>
    <system:String x:Key="PrimaryBarcode">Primary Barcode</system:String>
    <system:String x:Key="PrintSettings">Print Settings</system:String>
    <system:String x:Key="BarcodeType">Barcode Type</system:String>
    <system:String x:Key="BarcodesPerPage">Barcodes Per Page</system:String>
    <system:String x:Key="LabelSize">Label Size</system:String>
    <system:String x:Key="LabelSizeSmall">Small (30x20mm)</system:String>
    <system:String x:Key="LabelSizeMedium">Medium (50x30mm)</system:String>
    <system:String x:Key="LabelSizeLarge">Large (70x40mm)</system:String>
    <system:String x:Key="CopiesPerBarcode">Copies per barcode</system:String>
    <system:String x:Key="IncludePrice">Include Price</system:String>
    <system:String x:Key="IncludeProductName">Include Product Name</system:String>
    <system:String x:Key="Preview">Preview</system:String>

    <!-- Barcode Management Window -->
    <system:String x:Key="BarcodeManagement">Barcode Management</system:String>
    <system:String x:Key="AddBarcode">Add Barcode</system:String>
    <system:String x:Key="RemoveBarcode">Remove Barcode</system:String>
    <system:String x:Key="SetAsPrimary">Set as Primary</system:String>
    <system:String x:Key="BarcodeValue">Barcode Value</system:String>
    <system:String x:Key="BarcodeDescription">Description (Optional)</system:String>
    <system:String x:Key="IsPrimaryBarcode">Is Primary Barcode</system:String>
    <system:String x:Key="CurrentBarcodes">Current Barcodes</system:String>
    <system:String x:Key="NoBarcodes">No barcodes assigned to this product</system:String>
    <system:String x:Key="BarcodeExists">Barcode already exists</system:String>
    <system:String x:Key="InvalidBarcode">Invalid barcode format</system:String>
    <system:String x:Key="BarcodeRequired">Barcode value is required</system:String>
    <system:String x:Key="ConfirmRemoveBarcode">Are you sure you want to remove this barcode?</system:String>
    <system:String x:Key="Primary">Primary</system:String>

    <!-- Login -->
    <system:String x:Key="LoginErrorTitle">Login Error</system:String>
    <system:String x:Key="LoginError">Invalid username or password</system:String>
    <system:String x:Key="Login">Login</system:String>
    <system:String x:Key="RememberMe">Remember Me</system:String>
    <system:String x:Key="ForgotPassword">Forgot Password?</system:String>
    <system:String x:Key="LoginTitle">Welcome Back</system:String>
    <system:String x:Key="LoginSubtitle">Please sign in to continue</system:String>
    <system:String x:Key="LoginProcessing">Logging in...</system:String>
    <system:String x:Key="LoginSuccess">Login successful</system:String>
    <system:String x:Key="LoginFailed">Login failed</system:String>
    <system:String x:Key="AccountLocked">Account is locked. Please contact administrator.</system:String>
    <system:String x:Key="SessionExpired">Your session has expired. Please login again.</system:String>

    <!-- Dashboard Metrics -->
    <system:String x:Key="AverageTransaction">Average Transaction</system:String>
    <system:String x:Key="CustomerCount">Customer Count</system:String>
    <system:String x:Key="RevenuePerCustomer">Revenue per Customer</system:String>

    <!-- Dashboard Card Titles - Sales -->
    <system:String x:Key="SalesCard_TodaysSales">Today's Sales</system:String>
    <system:String x:Key="SalesCard_YesterdaysSales">Yesterday's Sales</system:String>
    <system:String x:Key="SalesCard_ThisWeeksSales">This Week's Sales</system:String>
    <system:String x:Key="SalesCard_ThisMonthsSales">This Month's Sales</system:String>
    <system:String x:Key="SalesCard_ThisYearsSales">This Year's Sales</system:String>
    <system:String x:Key="SalesCard_Sales">Sales</system:String>

    <!-- Dashboard Card Titles - Profit -->
    <system:String x:Key="ProfitCard_TodaysProfit">Today's Profit</system:String>
    <system:String x:Key="ProfitCard_YesterdaysProfit">Yesterday's Profit</system:String>
    <system:String x:Key="ProfitCard_ThisWeeksProfit">This Week's Profit</system:String>
    <system:String x:Key="ProfitCard_ThisMonthsProfit">This Month's Profit</system:String>
    <system:String x:Key="ProfitCard_ThisYearsProfit">This Year's Profit</system:String>
    <system:String x:Key="ProfitCard_GrossProfit">Gross Profit</system:String>

    <!-- User Roles -->
    <system:String x:Key="RoleAdmin">Administrator</system:String>

    <!-- Cash Drawer -->
    <system:String x:Key="AddCashPayout">Add Cash Payout</system:String>
    <system:String x:Key="ReferenceOptional">Reference (Optional)</system:String>
    <system:String x:Key="Open">Open</system:String>
    <system:String x:Key="DrawerOpen">Open</system:String>
    <system:String x:Key="DrawerClosed">Closed</system:String>
    <system:String x:Key="CashIn">Cash In</system:String>
    <system:String x:Key="CashOut">Cash Out</system:String>
    <system:String x:Key="Payout">Payout</system:String>
    <system:String x:Key="CashSale">Cash Sale</system:String>

    <!-- New strings for barcode lookup popup -->
    <system:String x:Key="ProductFoundInDatabase">Product found in products database</system:String>
    <system:String x:Key="ProductFoundDetails">Name: {0}
Description: {1}
Purchase Price: {2}
Selling Price: {3}</system:String>
    <system:String x:Key="AutofillProductDetails">Would you like to auto-fill the product details?</system:String>

    <!-- Common Dialog Messages -->
    <system:String x:Key="DialogConfirmTitle">Confirm</system:String>
    <system:String x:Key="DialogErrorTitle">Error</system:String>
    <system:String x:Key="DialogWarningTitle">Warning</system:String>
    <system:String x:Key="DialogInfoTitle">Information</system:String>
    <system:String x:Key="DialogSuccessTitle">Success</system:String>
    <system:String x:Key="DialogConfirmLogout">Are you sure you want to logout?</system:String>
    <system:String x:Key="DialogConfirmDeleteUser">Are you sure you want to delete user {0}?</system:String>
    <system:String x:Key="DialogConfirmClearOrder">Are you sure you want to clear the current order?</system:String>
    <system:String x:Key="DialogConfirmDeleteExpense">Are you sure you want to delete the expense '{0}'?</system:String>
    <system:String x:Key="DialogErrorDuringLogout">Error during logout: {0}</system:String>
    <system:String x:Key="DialogErrorUserDeletion">Error deleting user: {0}</system:String>
    <system:String x:Key="DialogSuccessUserDeleted">User deleted successfully!</system:String>
    <system:String x:Key="DialogErrorStartingApplication">Error starting application: {0}</system:String>
    <system:String x:Key="DialogMultipleHostsError">Multiple dialog hosts detected. Please report this issue to the development team.</system:String>
    <system:String x:Key="DialogCashDrawerError">Error checking cash drawer status: {0}</system:String>
    <system:String x:Key="DialogUserSwitchError">Error during user switch: {0}</system:String>

    <!-- Time Ago Formatting -->
    <system:String x:Key="TimeAgo_DaysAgo">{0} days ago</system:String>
    <system:String x:Key="TimeAgo_HoursAgo">{0} hours ago</system:String>
    <system:String x:Key="TimeAgo_MinutesAgo">{0} minutes ago</system:String>
    <system:String x:Key="TimeAgo_JustNow">Just now</system:String>

    <!-- Dialog Messages for PurchaseView -->
    <system:String x:Key="DialogSelectProduct">Please select a product first.</system:String>
    <system:String x:Key="DialogEnterValidUnitCost">Please enter a valid unit cost.</system:String>
    <system:String x:Key="DialogEnterValidQuantity">Please enter a valid quantity.</system:String>
    <system:String x:Key="DialogSelectSupplier">Please select a supplier.</system:String>
    <system:String x:Key="DialogAddItemToOrder">Please add at least one item to the order.</system:String>
    <system:String x:Key="DialogPurchaseOrderSaved">Purchase order saved successfully!</system:String>
    <system:String x:Key="DialogErrorSavingPurchaseOrder">Error saving purchase order: {0}</system:String>

    <!-- Common Dialog Titles -->
    <system:String x:Key="DialogValidationErrorTitle">Validation Error</system:String>

    <!-- Dialog Messages for ProductsView -->
    <system:String x:Key="DialogProductNameRequired">Product name is required.</system:String>
    <system:String x:Key="DialogSelectCategory">Please select a category.</system:String>
    <system:String x:Key="DialogSelectUnitOfMeasure">Please select a unit of measure.</system:String>
    <system:String x:Key="DialogProductSavedSuccess">Product saved successfully!</system:String>
    <system:String x:Key="DialogErrorSavingProduct">Error saving product: {0}</system:String>
    <system:String x:Key="DialogProductDeletedSuccess">Product deleted successfully!</system:String>
    <system:String x:Key="DialogErrorDeletingProduct">Error deleting product: {0}</system:String>
    <system:String x:Key="DialogConfirmDeleteProduct">Are you sure you want to delete this product?</system:String>
    
    <!-- Expense Dialog Strings -->
    <system:String x:Key="DialogExpenseDeletedSuccess">Expense deleted successfully!</system:String>
    <system:String x:Key="DialogErrorDeletingExpense">Error deleting expense: {0}</system:String>

    <!-- Notifications -->
    <system:String x:Key="AllNotifications">All Notifications</system:String>
    <system:String x:Key="ShowingNotifications">Showing {0} of {1} notifications</system:String>
    <system:String x:Key="MarkAllAsRead">Mark All as Read</system:String>
    <system:String x:Key="Notifications">Notifications</system:String>
    <system:String x:Key="LoadingMoreNotifications">Loading more notifications...</system:String>
    <system:String x:Key="NoNotifications">No notifications to display</system:String>
    <system:String x:Key="MarkAsRead">Mark as Read</system:String>
    <system:String x:Key="ViewAllNotifications">View All Notifications</system:String>
    
    <!-- Notification Titles -->
    <system:String x:Key="ProductExpiredTitle">Product Expired</system:String>
    <system:String x:Key="LowStockTitle">Low Stock Alert</system:String>
    <system:String x:Key="NotificationTitle_OutOfStock">Out of Stock</system:String>
    <system:String x:Key="NotificationTitle_OverdueSale">Overdue Sale Payment</system:String>
    <system:String x:Key="NotificationTitle_OverduePurchase">Overdue Purchase Payment</system:String>

    <!-- Notification Messages -->
    <system:String x:Key="ProductExpiredNotification">{0} has expired {1} days ago</system:String>
    <system:String x:Key="ExpiryDaysFormat">{0} days</system:String>
    <system:String x:Key="ProductExpiryUrgentNotification">{0} will expire in {1} days</system:String>
    <system:String x:Key="ProductExpiryWarningNotification">{0} warning: will expire in {1} days</system:String>
    <system:String x:Key="OutOfStockNotification">{0} is out of stock</system:String>
    <system:String x:Key="LowStockNotification">{0} low stock ({1} units left, minimum: {2})</system:String>
    <system:String x:Key="RemainingStockFormat">{0} units remaining</system:String>
    <system:String x:Key="MinimumStockFormat">Minimum: {0}</system:String>
    <system:String x:Key="OverdueSaleNotification">Invoice #{0} for customer {1} - {2} days overdue (Amount: {3})</system:String>
    <system:String x:Key="OverduePurchaseNotification">Purchase order #{0} for supplier {1} - {2} days overdue (Amount: {3})</system:String>

    <!-- Time Ago Formats -->
   
    <system:String x:Key="TimeAgo_DateFormat">MM/dd/yyyy</system:String>

    <!-- Purchase Order Details -->
    <system:String x:Key="PurchaseOrderDetails">Purchase Order Details</system:String>
    <system:String x:Key="OrderNumberLabel">Order #:</system:String>
    <system:String x:Key="DateLabel">Date:</system:String>
    <system:String x:Key="SupplierLabel">Supplier:</system:String>
    <system:String x:Key="StatusLabel">Status:</system:String>
    
    <!-- Cash Drawer Transactions -->
    <system:String x:Key="CashDrawerTransactions">Cash Drawer Transactions</system:String>
    <system:String x:Key="OpenedLabel">Opened:</system:String>
    <system:String x:Key="OpeningBalanceLabel">Opening Balance:</system:String>
    <system:String x:Key="ExpectedBalanceLabel">Expected Balance:</system:String>
    <system:String x:Key="TimeLabel">Time</system:String>
    <system:String x:Key="TypeLabel">Type</system:String>
    <system:String x:Key="AmountLabel">Amount</system:String>
    <system:String x:Key="ReasonLabel">Reason</system:String>
    <system:String x:Key="ReferenceLabel">Reference</system:String>
    <system:String x:Key="PerformedByLabel">Performed By</system:String>

    <!-- Common Buttons and Dialog Actions -->
    <system:String x:Key="OK">OK</system:String>
    <system:String x:Key="Yes">Yes</system:String>
    <system:String x:Key="No">No</system:String>
 
    <!-- Receipt Printing Settings -->
    <system:String x:Key="ReceiptPrintingSettings">Receipt Printing Settings</system:String>
    <system:String x:Key="ReceiptPrintingDescription">Configure receipt templates, printer settings, and printing behavior for your POS system</system:String>
    <system:String x:Key="GeneralSettings">General Settings</system:String>
    <system:String x:Key="AutoPrintReceipts">Automatically print receipts after sale completion</system:String>
    <system:String x:Key="ShowPrintDialog">Show print dialog before printing</system:String>
    <system:String x:Key="SaveReceiptsAsPDF">Save receipts as PDF backup</system:String>
    <system:String x:Key="EnablePrintPreview">Enable print preview functionality</system:String>
    <system:String x:Key="PDFBackupDirectory">PDF Backup Directory</system:String>
    <system:String x:Key="PrinterConfiguration">Printer Configuration</system:String>
    <system:String x:Key="DefaultPrinter">Default Printer</system:String>
    <system:String x:Key="PrinterType">Printer Type</system:String>
     <system:String x:Key="PaperSize">Paper Size</system:String>
     <system:String x:Key="ReceiptTemplate">Receipt Template</system:String>
     <system:String x:Key="FontSize">Font Size</system:String>
    <system:String x:Key="FooterText">Footer Text</system:String>
    <system:String x:Key="ThankYouMessage">Thank you for your business!</system:String>
    <system:String x:Key="TestPrinting">Test Printing</system:String>
     
    <!-- Cart Selection Dialog -->
    <system:String x:Key="SelectCartToRecall">Select Cart to Recall</system:String>
    <system:String x:Key="RecallCart">Recall Cart</system:String>
    <system:String x:Key="DialogSelectCart">Please select a cart to recall.</system:String>
    
    <!-- Product Selection Dialog -->
    <system:String x:Key="DialogSelectProductFirst">Please select a product first.</system:String>
    
    <!-- Input Validation Messages -->
    <system:String x:Key="InvalidInput">Invalid Input</system:String>
    <system:String x:Key="PleaseEnterValidOpeningBalance">Please enter a valid opening balance.</system:String>
    <system:String x:Key="PleaseEnterReason">Please enter a reason for the payout.</system:String>
    <system:String x:Key="MissingInformation">Missing Information</system:String>

    <!-- Message Box Strings -->
    <system:String x:Key="ErrorGeneratingPreview">Error generating preview: {0}</system:String>
    <system:String x:Key="PreviewError">Preview Error</system:String>
    <system:String x:Key="PleaseSelectProduct">Please select at least one product.</system:String>
    <system:String x:Key="NoSelection">No Selection</system:String>
    <system:String x:Key="ProductHasNoBarcode">Selected product has no barcode.</system:String>
    <system:String x:Key="NoBarcode">No Barcode</system:String>
    <system:String x:Key="ErrorGeneratingBarcodePreview">Error generating barcode preview: {0}</system:String>
    <system:String x:Key="PrintJobSentSuccessfully">Print job sent successfully!</system:String>
    <system:String x:Key="ErrorPrintingBarcodes">Error printing barcodes: {0}</system:String>
    <system:String x:Key="PleaseEnterValidQuantity">Please enter a valid quantity</system:String>
    <system:String x:Key="PleaseEnterValidUnitCost">Please enter a valid unit cost</system:String>
    <system:String x:Key="PleaseSelectCustomer">Please select a customer.</system:String>
    <system:String x:Key="ProductNameRequired">Product name is required.</system:String>
    <system:String x:Key="SellingPriceMustBeGreaterThanZero">Selling price must be greater than zero.</system:String>
    <system:String x:Key="PurchasePriceMustBeGreaterThanZero">Purchase price must be greater than zero.</system:String>
    <system:String x:Key="PleaseSelectCategoryValidation">Please select a category.</system:String>
    <system:String x:Key="FirstNameRequired">First name is required.</system:String>
    <system:String x:Key="CustomerSavedSuccessfully">Customer saved successfully!</system:String>
    <system:String x:Key="ErrorSavingCustomer">Error saving customer: {0}</system:String>
    <system:String x:Key="CustomerDeletedSuccessfully">Customer deleted successfully!</system:String>
    <system:String x:Key="ErrorDeletingCustomer">Error deleting customer: {0}</system:String>
    <system:String x:Key="ErrorInitializingView">Error initializing {0} view: {1}</system:String>
    <system:String x:Key="ErrorLoadingCashDrawer">Error loading cash drawer: {0}</system:String>
    <system:String x:Key="ErrorCheckingCashDrawerStatus">Error checking cash drawer status: {0}</system:String>
    <system:String x:Key="PleaseSelectPaymentMethod">Please select a payment method.</system:String>
    <system:String x:Key="ProductNotFoundWithBarcode">Product not found with this barcode.</system:String>
    <system:String x:Key="NotFound">Not Found</system:String>
    <system:String x:Key="ErrorSelectingProduct">Error selecting product: {0}</system:String>
    <system:String x:Key="ErrorSearchingProduct">Error searching product: {0}</system:String>
    <system:String x:Key="StartDateCannotBeAfterEndDate">Start date cannot be after end date</system:String>
    <system:String x:Key="InvalidDateRange">Invalid Date Range</system:String>
    <system:String x:Key="EnterValid13DigitBarcode">Please enter a valid 13-digit barcode</system:String>
    <system:String x:Key="ProductExistsWithBarcode">A product with this barcode already exists in your local database:\nName: {0}</system:String>
    <system:String x:Key="NoProductFoundWithBarcode">No product found with this barcode in any database</system:String>
    <system:String x:Key="ErrorLoadingImage">Error loading image: {0}</system:String>
    <system:String x:Key="PurchaseOrderSavedSuccessfully">Purchase order saved successfully!</system:String>

    <!-- Unpaid Sales -->
    <system:String x:Key="UnpaidSales">Unpaid Sales</system:String>
    <system:String x:Key="UnpaidSalesText">unpaid sales</system:String>
    <system:String x:Key="OverdueSalesText">overdue sales</system:String>
    <system:String x:Key="Location">Location</system:String>

    <!-- Purchase Order Specific -->
    <system:String x:Key="PurchaseOrderNumber">Purchase Order #</system:String>
    <system:String x:Key="OrderStatus">Order Status</system:String>
    <system:String x:Key="SelectSupplier">Select Supplier</system:String>
    <system:String x:Key="Stock">Stock</system:String>
    <system:String x:Key="OrderNotes">Order Notes</system:String>
    <system:String x:Key="AddItem">Add Item</system:String>
    <system:String x:Key="SaveOrder">Save Order</system:String>
    <system:String x:Key="CancelOrder">Cancel</system:String>
    <system:String x:Key="UnitCost">Unit Cost</system:String>
    <system:String x:Key="StatusPaid">Paid</system:String>
    <system:String x:Key="StatusUnpaid">Unpaid</system:String>
    <system:String x:Key="StatusApproved">Approved</system:String>
    <system:String x:Key="StatusReceived">Received</system:String>
    <system:String x:Key="StatusCancelled">Cancelled</system:String>
    <system:String x:Key="Unpaid">Unpaid</system:String>

    <!-- Database Settings -->
    <system:String x:Key="DatabaseBackupRestore">Backup and Restore</system:String>
    <system:String x:Key="BackupDatabase">Backup Database</system:String>
    <system:String x:Key="RestoreDatabase">Restore Database</system:String>
    <system:String x:Key="BackupDescription">Create a backup copy of your database or restore from a previous backup. Make sure to backup your data regularly.</system:String>
    <system:String x:Key="BackupSuccess">Database backup created successfully at: {0}</system:String>
    <system:String x:Key="RestoreSuccess">Database restored successfully. The application will now restart.</system:String>
    <system:String x:Key="BackupError">Error creating backup: {0}</system:String>
    <system:String x:Key="RestoreError">Error restoring database: {0}</system:String>
    <system:String x:Key="BackupInProgress">Creating database backup...</system:String>
    <system:String x:Key="RestoreInProgress">Restoring database...</system:String>
    <system:String x:Key="ConfirmRestore">Are you sure you want to restore the database? This will overwrite your current data and the application will restart.</system:String>

    <!-- Permissions -->
    <system:String x:Key="SalesPermissions">Sales Permissions</system:String>
    <system:String x:Key="CreateSales">Create Sales</system:String>
    <system:String x:Key="VoidSales">Void Sales</system:String>
    <system:String x:Key="ApplyDiscounts">Apply Discounts</system:String>
    <system:String x:Key="ViewSalesHistory">View Sales History</system:String>
    
    <system:String x:Key="ProductPermissions">Product Permissions</system:String>
    <system:String x:Key="ManageProducts">Manage Products</system:String>
    <system:String x:Key="ManageCategories">Manage Categories</system:String>
    <system:String x:Key="ViewInventory">View Inventory</system:String>
    <system:String x:Key="AdjustInventory">Adjust Inventory</system:String>
    
    <system:String x:Key="FinancialPermissions">Financial Permissions</system:String>
    <system:String x:Key="ManageExpenses">Manage Expenses</system:String>
    <system:String x:Key="ManageCashDrawer">Manage Cash Drawer</system:String>
    <system:String x:Key="ViewReports">View Reports</system:String>
    <system:String x:Key="ManagePrices">Manage Prices</system:String>
    
    <system:String x:Key="CustomerSupplierPermissions">Customer &amp; Supplier Permissions</system:String>
    <system:String x:Key="ManageCustomers">Manage Customers</system:String>
    <system:String x:Key="ManageSuppliers">Manage Suppliers</system:String>
    
    <system:String x:Key="AdministrativePermissions">Administrative Permissions</system:String>
    <system:String x:Key="ManageUsers">Manage Users</system:String>
    <system:String x:Key="ManageRoles">Manage Roles</system:String>
    <system:String x:Key="AccessSettings">Access Settings</system:String>
    <system:String x:Key="ViewLogs">View Logs</system:String>
    
    <system:String x:Key="SelectAll">Select All</system:String>
    <system:String x:Key="DeselectAll">Deselect All</system:String>

    <!-- Payment Processing -->
    <system:String x:Key="Cash">Cash</system:String>
    <system:String x:Key="EnterAmount">Enter amount</system:String>
    <system:String x:Key="SelectDueDate">Select due date</system:String>

    <!-- New chart view strings -->
    <system:String x:Key="Charts">Charts</system:String>
    <system:String x:Key="ChartsView">Charts View</system:String>
    <system:String x:Key="TableView">Table View</system:String>
    <system:String x:Key="ProductCount">Product Count</system:String>
    <system:String x:Key="DetailedCategoryStatistics">Detailed Category Statistics</system:String>
    <system:String x:Key="SearchCategories">Search Categories</system:String>
    <system:String x:Key="ExportData">Export Data</system:String>

    <!-- New string for current date -->
    <system:String x:Key="Current date">Current date</system:String>

    <!-- New string for date range -->
    <system:String x:Key="Date range">Date range</system:String>

    <!-- Invoice Dialog View -->
    <system:String x:Key="SaveInvoice">Save Invoice</system:String>
    <system:String x:Key="InvoiceDetails">INVOICE DETAILS</system:String>
    <system:String x:Key="InvoiceType">Invoice Type</system:String>
    <system:String x:Key="PaymentTerms">Payment Terms</system:String>
    <system:String x:Key="DueOnReceipt">Due on Receipt</system:String>
    <system:String x:Key="Net15">Net 15</system:String>
    <system:String x:Key="Net30">Net 30</system:String>
    <system:String x:Key="Net60">Net 60</system:String>
    <system:String x:Key="PartyInformation">PARTY INFORMATION</system:String>
    <system:String x:Key="IssueDate">Issue Date</system:String>
    <system:String x:Key="SelectCustomer">Select Customer</system:String>
    <system:String x:Key="FinancialInformation">FINANCIAL INFORMATION</system:String>
    <system:String x:Key="PaymentInformation">PAYMENT INFORMATION</system:String>
    <system:String x:Key="PaymentDate">Payment Date</system:String>
    <system:String x:Key="Method">Method</system:String>
    <system:String x:Key="ReferenceNumber">Reference Number</system:String>
    <system:String x:Key="BalanceDue">Balance Due</system:String>
    <system:String x:Key="InvoiceNotesHint">Invoice notes, terms, conditions, etc.</system:String>
    <system:String x:Key="InvoiceItems">INVOICE ITEMS</system:String>
    <system:String x:Key="ScanBarcodeHint">Scan or Enter Barcode</system:String>
    <system:String x:Key="BarcodeToggleTooltip">Toggle between barcode and product name search</system:String>
    <system:String x:Key="SearchByBarcode">Search by barcode</system:String>
    <system:String x:Key="RemainingStock">Remaining Stock</system:String>
    <system:String x:Key="SellingPriceTooltip">Price the item will be sold for</system:String>
    <system:String x:Key="NoItemsAddedYet">No Items Added Yet</system:String>
    <system:String x:Key="AddItemsMessage">Add at least one product to save this invoice.</system:String>
    <system:String x:Key="UseFormMessage">Use the form above to add items to your invoice.</system:String>
    <system:String x:Key="Draft">Draft</system:String>
    <system:String x:Key="Issued">Issued</system:String>
    <system:String x:Key="SelectDate">Select date</system:String>
    <system:String x:Key="Expiry">Expiry</system:String>

    <!-- Invoice View -->
    <system:String x:Key="InvoiceManagement">Invoice Management</system:String>
    <system:String x:Key="CreateNewInvoice">Create a new invoice</system:String>
    <system:String x:Key="SearchInvoices">Search invoices...</system:String>
    <system:String x:Key="RefreshInvoiceList">Refresh invoice list</system:String>
    <system:String x:Key="Invoices">Invoices</system:String>
    <system:String x:Key="TotalInvoices">Total Invoices:</system:String>
    <system:String x:Key="SelectStatus">Select status</system:String>
    <system:String x:Key="SelectType">Select type</system:String>
    
    <!-- Add new resource string for FromTo -->
    <system:String x:Key="FromTo">From/To</system:String>

    <!-- Add new string for "Invoice" -->
    <system:String x:Key="Invoice">Invoice</system:String>

    <!-- Stats Details Dialog -->
    <system:String x:Key="Analysis">Analysis</system:String>
    <system:String x:Key="DetailedMetricsAndTrends">Detailed Metrics and Trends</system:String>
    <system:String x:Key="AnalysisFor">Analysis for</system:String>
    <system:String x:Key="TimePeriod">Time Period</system:String>
    <system:String x:Key="FilterByCategoryStats">Filter by Category</system:String>
    <system:String x:Key="FilterByProduct">Filter by Product</system:String>
    <system:String x:Key="SelectCategory">Select Category</system:String>
    <system:String x:Key="SalesTrend">Sales Trend</system:String>
    <system:String x:Key="SalesByHour">Sales by Hour</system:String>
    <system:String x:Key="SalesByDayOfWeek">Sales by Day of Week</system:String>

    <!-- Tab Labels for Statistics Dialogs -->
    <system:String x:Key="HourlyDistribution">Hourly Distribution</system:String>
    <system:String x:Key="DailyDistribution">Daily Distribution</system:String>
    <system:String x:Key="SalesByDays">Sales by Days</system:String>
    <system:String x:Key="TopSellingProducts">Top Selling Products</system:String>
    <system:String x:Key="ProfitTrend">Profit Trend</system:String>
    <system:String x:Key="ProfitByDays">Profit by Days</system:String>
    <system:String x:Key="TopProfitableProducts">Top Profitable Products</system:String>
    <system:String x:Key="ExpiryTrend">Expiry Trend</system:String>
    <system:String x:Key="CategoryDistribution">Category Distribution</system:String>
    <system:String x:Key="ExpiryByDays">Expiry by Days</system:String>
    <system:String x:Key="ExpiringProducts">Expiring Products</system:String>
    <system:String x:Key="ExpensesTrend">Expenses Trend</system:String>
    <system:String x:Key="ExpensesByCategory">Expenses by Category</system:String>
    <system:String x:Key="ExpensesList">Expenses List</system:String>
    <system:String x:Key="UnpaidTrends">Unpaid Trends</system:String>
    <system:String x:Key="AgeDistribution">Age Distribution</system:String>
    <system:String x:Key="AmountRanges">Amount Ranges</system:String>

    <!-- Low Stock Dialog Tab Labels -->
    <system:String x:Key="StockTrends">Stock Trends</system:String>
    <system:String x:Key="StockLevels">Stock Levels</system:String>
    <system:String x:Key="ProductsNeedingAttention">Products Needing Attention</system:String>
    <system:String x:Key="TotalInventoryValue">Total Inventory Value</system:String>
    <system:String x:Key="RestockValue">Restock Value</system:String>

    <!-- Enhanced Sales Detail Dialog -->
    <system:String x:Key="TransactionInformation">Transaction Information</system:String>
    <system:String x:Key="FinancialSummary">Financial Summary</system:String>
    <system:String x:Key="ReprintReceipt">Reprint Receipt</system:String>
    <system:String x:Key="PreviewReceipt">Preview Receipt</system:String>
    <system:String x:Key="SaveAsPDF">Save as PDF</system:String>
    <system:String x:Key="ReprintReceiptTooltip">Print a copy of this receipt</system:String>
    <system:String x:Key="PreviewReceiptTooltip">Preview receipt before printing</system:String>
    <system:String x:Key="SaveAsPDFTooltip">Save receipt as PDF file</system:String>

    <!-- Missing Dialog and View Translations -->
     <system:String x:Key="PayAll">Pay All</system:String>
    <system:String x:Key="PartialPayment">Partial Payment</system:String>
    <system:String x:Key="PaymentAmount">Payment Amount</system:String>
    <system:String x:Key="BatchPayment">Batch Payment</system:String>
    <system:String x:Key="StockLevel">Stock Level</system:String>
    <system:String x:Key="WeightBased">Weight-Based</system:String>
    <system:String x:Key="ProductConfiguration">Product Configuration</system:String>
    <system:String x:Key="EstimatedTotal">Estimated Total</system:String>
    <system:String x:Key="Completed">Completed</system:String>
    <system:String x:Key="PrintReceipt">Print Receipt</system:String>

    <!-- Batch Stock Management -->
    <system:String x:Key="ManageBatchStock">Manage Batch Stock</system:String>
    <system:String x:Key="BatchStockManagement">Batch Stock Management</system:String>
    <system:String x:Key="AddNewBatch">Add New Batch</system:String>
    <system:String x:Key="EditBatch">Edit Batch</system:String>
    <system:String x:Key="AddStock">Add Stock</system:String>
    <system:String x:Key="BatchNumber">Batch Number</system:String>
    <system:String x:Key="ManufacturingDate">Manufacturing Date</system:String>
    <system:String x:Key="AverageCost">Average Cost</system:String>
    <system:String x:Key="ExpiringSoon">Expiring Soon</system:String>
    <system:String x:Key="TotalStock">Total Stock</system:String>
    <system:String x:Key="Created">Created</system:String>
    <system:String x:Key="BatchStatistics">Batch Statistics</system:String>
    <system:String x:Key="BatchList">Batch List</system:String>
    <system:String x:Key="BatchDetails">Batch Details</system:String>

    <!-- Product Dialog Specific -->
    <system:String x:Key="Weight">Weight</system:String>
    <system:String x:Key="SalesMethod">Sales Method</system:String>
    <system:String x:Key="Units">Units</system:String>
    <system:String x:Key="Service">Service</system:String>
     <system:String x:Key="PricingStock">Pricing &amp; Stock</system:String>
    <system:String x:Key="BulkPackPricing">Bulk/Pack Pricing</system:String>
    <system:String x:Key="EnableBulkPricing">Enable bulk pricing</system:String>
    <system:String x:Key="ConfigureQuantityBasedPricing">Configure quantity-based pricing tiers to offer bulk discounts</system:String>

    <!-- Payment Processing View -->
    <system:String x:Key="ReceiptOptions">Receipt Options</system:String>
    <system:String x:Key="AutoPrintReceipt">Auto-print receipt</system:String>
     <system:String x:Key="SaveAsPDFBackup">Save as PDF backup</system:String>
    <system:String x:Key="AutomaticallyPrintReceipt">Automatically print receipt after payment completion</system:String>
    <system:String x:Key="ShowPrinterSelectionDialog">Show printer selection dialog before printing</system:String>
    <system:String x:Key="SaveReceiptAsPDFBackup">Save receipt as PDF backup</system:String>

    <!-- Weight Based Product Dialog -->
     <system:String x:Key="PricePerUnit">Price per unit</system:String>
     <system:String x:Key="EnterWeight">Enter Weight</system:String>
    <system:String x:Key="HowMuchSpend">How much would you like to spend?</system:String>
    <system:String x:Key="EnterDollarAmount">Enter the dollar amount and we'll calculate the weight for you.</system:String>
    <system:String x:Key="HowMuchWeight">How much weight/quantity do you need?</system:String>
    <system:String x:Key="EnterWeightCalculateCost">Enter the weight or quantity and we'll calculate the total cost.</system:String>
    <system:String x:Key="CalculationSummary">Calculation Summary</system:String>
    <system:String x:Key="SelectTabEnterValue">Select a tab and enter a value to see the calculation</system:String>
    <system:String x:Key="TotalCost">Total Cost</system:String>
    <system:String x:Key="TotalWeight">Total Weight</system:String>

    <!-- Sales History View -->
    <system:String x:Key="ShowingItemsOf">Showing {0} of {1} items</system:String>
    <system:String x:Key="ReprintReceiptFor">Reprint receipt for Invoice #{0}</system:String>
    <system:String x:Key="ReprintReceiptDialog">Reprint Receipt</system:String>
    <system:String x:Key="ReprintReceiptMessage">Reprint receipt for Invoice #{0}?</system:String>
    <system:String x:Key="ClickYesPrintDirectly">Click Yes to print directly, No to show print dialog, or Cancel to abort.</system:String>
    <system:String x:Key="ReprintReceiptForInvoice">Reprint receipt for Invoice #{0}</system:String>
    <system:String x:Key="ReprintReceiptFullMessage">Reprint receipt for Invoice #{0}?

Click Yes to print directly, No to show print dialog, or Cancel to abort.</system:String>
    <system:String x:Key="PrintingStatus">Printing...</system:String>
    <system:String x:Key="ReceiptPrintingFailed">Receipt printing failed or was cancelled.</system:String>
    <system:String x:Key="PrintResult">Print Result</system:String>

    <!-- Product To Invoice Confirmation Dialog -->
    <system:String x:Key="InvoiceInformation">Invoice Information</system:String>

    <!-- Out of Stock Dialog Messages -->
    <system:String x:Key="OutOfStockCreateInvoiceTitle">Out of Stock - Create Invoice?</system:String>
    <system:String x:Key="OutOfStockCreateInvoiceMessage">This product is out of stock (0 items available).

Would you like to create an invoice for this product instead?</system:String>
    <system:String x:Key="OutOfStockTitle">Out of Stock</system:String>
    <system:String x:Key="OutOfStockMessage">This product is out of stock!</system:String>
    <system:String x:Key="OutOfStockWithQuantityMessage">This product is out of stock (0 {0} available).</system:String>
    <system:String x:Key="StockLimitMessage">Cannot add that quantity. Only {0} {1} available in stock.</system:String>
    <system:String x:Key="StockLimitTitle">Stock Limit</system:String>

    <!-- Insufficient Stock Dialog Messages -->
    <system:String x:Key="InsufficientStockTitle">Insufficient Stock</system:String>
    <system:String x:Key="InsufficientStockWithInvoiceMessage">Only {0} {1} available in stock.

Add available stock to cart?</system:String>

    <!-- Localized Button Text -->
    <system:String x:Key="ButtonYes">Yes</system:String>
    <system:String x:Key="ButtonNo">No</system:String>
    <system:String x:Key="ButtonCancel">Cancel</system:String>
    <system:String x:Key="ButtonOK">OK</system:String>
</ResourceDictionary>
