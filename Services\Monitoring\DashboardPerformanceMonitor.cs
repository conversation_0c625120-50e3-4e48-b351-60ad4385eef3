using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Services.Monitoring
{
    /// <summary>
    /// ✅ NEW: Dashboard performance monitoring service
    /// Tracks loading times, memory usage, and performance metrics for optimization
    /// </summary>
    public class DashboardPerformanceMonitor : INotifyPropertyChanged, IDisposable
    {
        private readonly List<DashboardPerformanceMetric> _metrics = new();
        private readonly object _lockObject = new object();
        private readonly string _logFilePath;
        private readonly PerformanceCounter _memoryCounter;
        private readonly PerformanceCounter _cpuCounter;
        
        private bool _disposed = false;
        private DateTime _sessionStartTime = DateTime.Now;

        // Current session metrics
        private double _averageLoadTime = 0;
        private double _maxLoadTime = 0;
        private double _minLoadTime = double.MaxValue;
        private int _totalOperations = 0;
        private long _currentMemoryUsage = 0;
        private double _currentCpuUsage = 0;

        public event PropertyChangedEventHandler PropertyChanged;
        public event EventHandler<DashboardPerformanceAlertEventArgs> PerformanceAlert;

        public DashboardPerformanceMonitor()
        {
            _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "dashboard_performance.json");
            Directory.CreateDirectory(Path.GetDirectoryName(_logFilePath));

            // Initialize performance counters
            try
            {
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _cpuCounter.NextValue(); // First call returns 0, so we call it once
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing performance counters: {ex.Message}");
            }

            Debug.WriteLine("DashboardPerformanceMonitor initialized");
        }

        #region Properties

        public double AverageLoadTime
        {
            get => _averageLoadTime;
            private set
            {
                if (Math.Abs(_averageLoadTime - value) > 0.001)
                {
                    _averageLoadTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public double MaxLoadTime
        {
            get => _maxLoadTime;
            private set
            {
                if (Math.Abs(_maxLoadTime - value) > 0.001)
                {
                    _maxLoadTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public double MinLoadTime
        {
            get => _minLoadTime == double.MaxValue ? 0 : _minLoadTime;
            private set
            {
                if (Math.Abs(_minLoadTime - value) > 0.001)
                {
                    _minLoadTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public int TotalOperations
        {
            get => _totalOperations;
            private set
            {
                if (_totalOperations != value)
                {
                    _totalOperations = value;
                    OnPropertyChanged();
                }
            }
        }

        public long CurrentMemoryUsage
        {
            get => _currentMemoryUsage;
            private set
            {
                if (_currentMemoryUsage != value)
                {
                    _currentMemoryUsage = value;
                    OnPropertyChanged();
                }
            }
        }

        public double CurrentCpuUsage
        {
            get => _currentCpuUsage;
            private set
            {
                if (Math.Abs(_currentCpuUsage - value) > 0.1)
                {
                    _currentCpuUsage = value;
                    OnPropertyChanged();
                }
            }
        }

        public TimeSpan SessionDuration => DateTime.Now - _sessionStartTime;

        #endregion

        /// <summary>
        /// ✅ CORE: Start monitoring a dashboard operation
        /// </summary>
        public DashboardPerformanceTracker StartOperation(string operationName, string category = "Dashboard")
        {
            return new DashboardPerformanceTracker(this, operationName, category);
        }

        /// <summary>
        /// ✅ CORE: Record a completed operation
        /// </summary>
        internal void RecordOperation(string operationName, string category, TimeSpan duration, Dictionary<string, object> additionalData = null)
        {
            lock (_lockObject)
            {
                try
                {
                    var metric = new DashboardPerformanceMetric
                    {
                        OperationName = operationName,
                        Category = category,
                        Duration = duration,
                        Timestamp = DateTime.Now,
                        MemoryUsage = GetCurrentMemoryUsage(),
                        CpuUsage = GetCurrentCpuUsage(),
                        AdditionalData = additionalData ?? new Dictionary<string, object>()
                    };

                    _metrics.Add(metric);
                    UpdateAggregateMetrics();

                    // Check for performance alerts
                    CheckPerformanceAlerts(metric);

                    Debug.WriteLine($"DashboardPerformanceMonitor: {operationName} completed in {duration.TotalMilliseconds:F1}ms");

                    // Log to file periodically
                    if (_metrics.Count % 10 == 0)
                    {
                        _ = Task.Run(() => SaveMetricsToFileAsync());
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error recording operation: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// ✅ ANALYSIS: Get performance summary for a specific period
        /// </summary>
        public DashboardPerformanceSummary GetPerformanceSummary(TimeSpan? period = null)
        {
            lock (_lockObject)
            {
                try
                {
                    var cutoff = period.HasValue ? DateTime.Now - period.Value : DateTime.MinValue;
                    var relevantMetrics = _metrics.Where(m => m.Timestamp >= cutoff).ToList();

                    if (!relevantMetrics.Any())
                    {
                        return new DashboardPerformanceSummary();
                    }

                    var loadTimes = relevantMetrics.Select(m => m.Duration.TotalMilliseconds).ToList();

                    return new DashboardPerformanceSummary
                    {
                        TotalOperations = relevantMetrics.Count,
                        AverageLoadTime = loadTimes.Average(),
                        MinLoadTime = loadTimes.Min(),
                        MaxLoadTime = loadTimes.Max(),
                        MedianLoadTime = GetMedian(loadTimes),
                        P95LoadTime = GetPercentile(loadTimes, 95),
                        P99LoadTime = GetPercentile(loadTimes, 99),
                        AverageMemoryUsage = relevantMetrics.Average(m => m.MemoryUsage),
                        AverageCpuUsage = relevantMetrics.Average(m => m.CpuUsage),
                        OperationsByCategory = relevantMetrics.GroupBy(m => m.Category)
                            .ToDictionary(g => g.Key, g => g.Count()),
                        Period = period ?? SessionDuration
                    };
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error getting performance summary: {ex.Message}");
                    return new DashboardPerformanceSummary();
                }
            }
        }

        /// <summary>
        /// ✅ ANALYSIS: Get slowest operations
        /// </summary>
        public List<DashboardPerformanceMetric> GetSlowestOperations(int count = 10)
        {
            lock (_lockObject)
            {
                return _metrics
                    .OrderByDescending(m => m.Duration.TotalMilliseconds)
                    .Take(count)
                    .ToList();
            }
        }

        /// <summary>
        /// ✅ HELPER: Update aggregate metrics
        /// </summary>
        private void UpdateAggregateMetrics()
        {
            if (_metrics.Count == 0) return;

            var loadTimes = _metrics.Select(m => m.Duration.TotalMilliseconds).ToList();
            
            AverageLoadTime = loadTimes.Average();
            MaxLoadTime = loadTimes.Max();
            MinLoadTime = loadTimes.Min();
            TotalOperations = _metrics.Count;
            CurrentMemoryUsage = GetCurrentMemoryUsage();
            CurrentCpuUsage = GetCurrentCpuUsage();
        }

        /// <summary>
        /// ✅ HELPER: Check for performance alerts
        /// </summary>
        private void CheckPerformanceAlerts(DashboardPerformanceMetric metric)
        {
            try
            {
                var alerts = new List<string>();

                // Check for slow operations (> 2 seconds)
                if (metric.Duration.TotalSeconds > 2)
                {
                    alerts.Add($"Slow operation: {metric.OperationName} took {metric.Duration.TotalSeconds:F1}s");
                }

                // Check for high memory usage (> 1GB)
                if (metric.MemoryUsage > 1024)
                {
                    alerts.Add($"High memory usage: {metric.MemoryUsage}MB during {metric.OperationName}");
                }

                // Check for high CPU usage (> 80%)
                if (metric.CpuUsage > 80)
                {
                    alerts.Add($"High CPU usage: {metric.CpuUsage:F1}% during {metric.OperationName}");
                }

                if (alerts.Count > 0)
                {
                    PerformanceAlert?.Invoke(this, new DashboardPerformanceAlertEventArgs
                    {
                        Metric = metric,
                        Alerts = alerts
                    });
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking performance alerts: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ HELPER: Get current memory usage
        /// </summary>
        private long GetCurrentMemoryUsage()
        {
            try
            {
                return GC.GetTotalMemory(false) / (1024 * 1024); // Convert to MB
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// ✅ HELPER: Get current CPU usage
        /// </summary>
        private double GetCurrentCpuUsage()
        {
            try
            {
                return _cpuCounter?.NextValue() ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// ✅ HELPER: Calculate median value
        /// </summary>
        private double GetMedian(List<double> values)
        {
            var sorted = values.OrderBy(x => x).ToList();
            var mid = sorted.Count / 2;
            return sorted.Count % 2 == 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
        }

        /// <summary>
        /// ✅ HELPER: Calculate percentile value
        /// </summary>
        private double GetPercentile(List<double> values, int percentile)
        {
            var sorted = values.OrderBy(x => x).ToList();
            var index = (int)Math.Ceiling(percentile / 100.0 * sorted.Count) - 1;
            return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
        }

        /// <summary>
        /// ✅ PERSISTENCE: Save metrics to file
        /// </summary>
        private async Task SaveMetricsToFileAsync()
        {
            try
            {
                var summary = GetPerformanceSummary();
                var json = JsonSerializer.Serialize(summary, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_logFilePath, json);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving metrics to file: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _memoryCounter?.Dispose();
                _cpuCounter?.Dispose();
                _ = SaveMetricsToFileAsync();
                _disposed = true;
                Debug.WriteLine("DashboardPerformanceMonitor disposed");
            }
        }
    }

    /// <summary>
    /// ✅ TRACKER: Performance tracking helper
    /// </summary>
    public class DashboardPerformanceTracker : IDisposable
    {
        private readonly DashboardPerformanceMonitor _monitor;
        private readonly string _operationName;
        private readonly string _category;
        private readonly Stopwatch _stopwatch;
        private readonly Dictionary<string, object> _additionalData = new();

        internal DashboardPerformanceTracker(DashboardPerformanceMonitor monitor, string operationName, string category)
        {
            _monitor = monitor;
            _operationName = operationName;
            _category = category;
            _stopwatch = Stopwatch.StartNew();
        }

        public void AddData(string key, object value)
        {
            _additionalData[key] = value;
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            _monitor.RecordOperation(_operationName, _category, _stopwatch.Elapsed, _additionalData);
        }
    }

    /// <summary>
    /// ✅ METRIC: Performance metric data
    /// </summary>
    public class DashboardPerformanceMetric
    {
        public string OperationName { get; set; }
        public string Category { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; }
        public long MemoryUsage { get; set; }
        public double CpuUsage { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    /// <summary>
    /// ✅ SUMMARY: Performance summary data
    /// </summary>
    public class DashboardPerformanceSummary
    {
        public int TotalOperations { get; set; }
        public double AverageLoadTime { get; set; }
        public double MinLoadTime { get; set; }
        public double MaxLoadTime { get; set; }
        public double MedianLoadTime { get; set; }
        public double P95LoadTime { get; set; }
        public double P99LoadTime { get; set; }
        public double AverageMemoryUsage { get; set; }
        public double AverageCpuUsage { get; set; }
        public Dictionary<string, int> OperationsByCategory { get; set; } = new();
        public TimeSpan Period { get; set; }
    }

    /// <summary>
    /// ✅ ALERT: Dashboard performance alert event arguments
    /// </summary>
    public class DashboardPerformanceAlertEventArgs : EventArgs
    {
        public DashboardPerformanceMetric Metric { get; set; }
        public List<string> Alerts { get; set; } = new();
    }
}
