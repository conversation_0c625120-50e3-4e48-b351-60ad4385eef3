using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Linq;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using System.Configuration;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Views
{
    public partial class LoginWindow : Window
    {
        private readonly AuthenticationService _authService;
        private readonly ISettingsService _settingsService;
        private bool _isPasswordRevealed = false;
        private string _currentPassword = string.Empty;
        private readonly DatabaseService _dbService;
        private bool _isSwitchUserMode;

        public LoginWindow()
        {
            InitializeComponent();
            _dbService = new DatabaseService();
            // Use the singleton AuthenticationService from the service provider
            _authService = App.ServiceProvider?.GetService<IAuthenticationService>() as AuthenticationService ?? new AuthenticationService(_dbService);
            _settingsService = new SettingsService();
            
            // Check if this is a switch user scenario based on owner window
            _isSwitchUserMode = Owner != null;
            
            if (_isSwitchUserMode)
            {
                // Clear any saved credentials in switch user mode
                txtUsername.Clear();
                txtPassword.Clear();
                chkRememberMe.IsChecked = false;
                
                // Change the login button text and window title for switch user mode
                Title = FindResource("SwitchUserTitle")?.ToString() ?? "Switch User";
                btnLogin.Content = FindResource("SwitchUserButton")?.ToString() ?? "Switch User";
                
                // Don't load saved credentials in switch user mode
                txtUsername.Focus();
            }
            else
            {
                LoadSavedCredentials();
            }
            
            // Add KeyDown event handler for password box
            txtPassword.KeyDown += TxtPassword_KeyDown;
            txtPassword.PasswordChanged += TxtPassword_PasswordChanged;
        }

        private void TxtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnLogin_Click(sender, new RoutedEventArgs());
            }
        }

        private void TxtPassword_PasswordChanged(object sender, RoutedEventArgs e)
        {
            _currentPassword = txtPassword.Password;
        }

        private void LoadSavedCredentials()
        {
            try
            {
                var savedUsername = _settingsService.GetSetting("RememberedUsername");
                var rememberMe = _settingsService.GetSetting("RememberMe");

                if (!string.IsNullOrEmpty(savedUsername) && rememberMe == "true")
                {
                    txtUsername.Text = savedUsername;
                    chkRememberMe.IsChecked = true;
                    txtPassword.Focus();
                }
                else
                {
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user as this is not critical
                Console.WriteLine($"Error loading saved credentials: {ex.Message}");
                txtUsername.Focus();
            }
        }

        private void SaveCredentials()
        {
            try
            {
                if (chkRememberMe.IsChecked == true)
                {
                    _settingsService.SaveSetting("RememberedUsername", txtUsername.Text);
                    _settingsService.SaveSetting("RememberMe", "true");
                }
                else
                {
                    _settingsService.SaveSetting("RememberedUsername", "");
                    _settingsService.SaveSetting("RememberMe", "false");
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user as this is not critical
                Console.WriteLine($"Error saving credentials: {ex.Message}");
            }
        }

        private async void btnLogin_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                btnLogin.IsEnabled = false;
                txtError.Visibility = Visibility.Collapsed;

                var username = txtUsername.Text;
                var password = txtPassword.Password;

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    txtError.Text = FindResource("UsernameRequired").ToString();
                    txtError.Visibility = Visibility.Visible;
                    return;
                }

                bool loginSuccess = await Task.Run(() => _authService.Login(username, password));

                if (loginSuccess)
                {
                    if (_isSwitchUserMode)
                    {
                        // In switch user mode, we just return success
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        // Normal login flow - save credentials only in normal mode
                        SaveCredentials();
                        var mainWindow = new MainWindow(_authService);
                        mainWindow.Show();
                        Close();
                    }
                }
                else
                {
                    txtError.Text = FindResource("LoginError").ToString();
                    txtError.Visibility = Visibility.Visible;
                    txtPassword.Password = "";
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                txtError.Text = FindResource("LoginErrorTitle").ToString();
                txtError.Visibility = Visibility.Visible;
                Console.WriteLine($"Login error: {ex.Message}");
            }
            finally
            {
                btnLogin.IsEnabled = true;
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            if (_isSwitchUserMode)
            {
                DialogResult = false;
            }
            Close();
        }

        private void btnForgotPassword_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show(
                FindResource("ResetPasswordInstructions").ToString(),
                FindResource("ResetPasswordTitle").ToString(),
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void btnPasswordReveal_Click(object sender, RoutedEventArgs e)
        {
            _isPasswordRevealed = !_isPasswordRevealed;
            
            if (_isPasswordRevealed)
            {
                passwordRevealIcon.Kind = PackIconKind.EyeOffOutline;
                string currentPassword = txtPassword.Password;
                txtPassword.Visibility = Visibility.Hidden;
                
                // Create temporary textbox to show password
                TextBox tempTextBox = new TextBox
                {
                    Text = currentPassword,
                    Style = (Style)FindResource("TempPasswordTextBox"),
                    Name = "tempPasswordBox"
                };
                
                Grid.SetColumn(tempTextBox, Grid.GetColumn(txtPassword));
                var parent = (Grid)txtPassword.Parent;
                
                // Remove any existing temporary textbox
                var existingTempBox = parent.Children.OfType<TextBox>().FirstOrDefault(x => x.Name == "tempPasswordBox");
                if (existingTempBox != null)
                {
                    parent.Children.Remove(existingTempBox);
                }
                
                parent.Children.Insert(0, tempTextBox);
            }
            else
            {
                passwordRevealIcon.Kind = PackIconKind.EyeOutline;
                txtPassword.Visibility = Visibility.Visible;
                
                // Remove temporary textbox
                var parent = (Grid)txtPassword.Parent;
                var textBox = parent.Children.OfType<TextBox>().FirstOrDefault(x => x.Name == "tempPasswordBox");
                if (textBox != null)
                {
                    txtPassword.Password = textBox.Text;
                    parent.Children.Remove(textBox);
                }
            }
        }
    }
} 