<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.BusinessExpenseDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:POSSystem.Models"
             mc:Ignorable="d"
             MinWidth="640" MinHeight="480">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid x:Name="ContentGrid" Margin="24"
              MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
              MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="{DynamicResource BusinessExpenses}" 
                          Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                
                <Button Grid.Column="1"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}">
                    <materialDesign:PackIcon Kind="Close" />
                </Button>
            </Grid>

            <!-- Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <materialDesign:Card Grid.Column="0" 
                                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Margin="16">
                    <StackPanel Margin="16" HorizontalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                    Value="0"
                                    IsIndeterminate="True" />
                        <TextBlock Text="{DynamicResource LoadingExpenses}"
                                  HorizontalAlignment="Center"
                                  Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Expenses List -->
                <DataGrid Grid.Column="0"
                          ItemsSource="{Binding BusinessExpenses}"
                          SelectedItem="{Binding SelectedExpense}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          IsReadOnly="True"
                          HorizontalAlignment="Stretch"
                          ColumnWidth="*"
                          MinColumnWidth="80"
                          MaxColumnWidth="240"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto"
                          MaxWidth="{Binding ElementName=ContentGrid, Path=ActualWidth}"
                          GridLinesVisibility="All"
                          AlternatingRowBackground="#F5F5F5">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="{DynamicResource Description}"
                                          Binding="{Binding Description}"
                                          Width="2*"
                                          MinWidth="160"
                                          ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                        <DataGridTextColumn Header="{DynamicResource Amount}"
                                          Binding="{Binding Amount, StringFormat=C}"
                                          Width="*"
                                          MinWidth="90"
                                          ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                        <DataGridTextColumn Header="{DynamicResource Category}"
                                          Binding="{Binding Category}"
                                          Width="*"
                                          MinWidth="100"
                                          ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                        <DataGridTextColumn Header="{DynamicResource Frequency}"
                                          Binding="{Binding Frequency}"
                                          Width="*"
                                          MinWidth="100"
                                          ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                        <DataGridTextColumn Header="{DynamicResource NextDue}"
                                          Binding="{Binding NextDueDate, StringFormat=d}"
                                          Width="*"
                                          MinWidth="110"
                                          ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                    </DataGrid.Columns>
                </DataGrid>


            </Grid>

            <!-- Footer -->
            <StackPanel Grid.Row="2"
                        Orientation="Horizontal"
                        HorizontalAlignment="Right"
                        Margin="0,16,0,0">
                <Button Content="{DynamicResource Add}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding AddExpenseCommand}"
                        Margin="0,0,8,0"/>
                <Button Content="{DynamicResource Edit}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding EditExpenseCommand}"
                        Margin="0,0,8,0"/>
                <Button Content="{DynamicResource Delete}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding DeleteExpenseCommand}"
                        Margin="0,0,16,0"/>
                <Button Content="{DynamicResource Close}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
            </StackPanel>
            </Grid>
        </ScrollViewer>
</UserControl> 