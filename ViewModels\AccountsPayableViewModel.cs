using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.ViewModels
{
    public class AccountsPayableViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private ObservableCollection<PurchaseOrder> _unpaidPurchaseOrders;
        private ObservableCollection<Supplier> _suppliers;
        private int _unpaidPurchaseOrdersCount;
        private decimal _totalAmountDue;
        private int _dueThisWeek;
        private DateTime? _startDate;
        private DateTime? _endDate;
        private string _selectedStatus;
        private Supplier _selectedSupplier;

        public AccountsPayableViewModel()
        {
            _dbService = new DatabaseService();
            UnpaidPurchaseOrders = new ObservableCollection<PurchaseOrder>();
            Suppliers = new ObservableCollection<Supplier>();
            LoadData();
        }

        public ObservableCollection<PurchaseOrder> UnpaidPurchaseOrders
        {
            get => _unpaidPurchaseOrders;
            set { _unpaidPurchaseOrders = value; OnPropertyChanged(); }
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set { _suppliers = value; OnPropertyChanged(); }
        }

        public int UnpaidPurchaseOrdersCount
        {
            get => _unpaidPurchaseOrdersCount;
            set { _unpaidPurchaseOrdersCount = value; OnPropertyChanged(); }
        }

        public decimal TotalAmountDue
        {
            get => _totalAmountDue;
            set { _totalAmountDue = value; OnPropertyChanged(); }
        }

        public int DueThisWeek
        {
            get => _dueThisWeek;
            set { _dueThisWeek = value; OnPropertyChanged(); }
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set { _startDate = value; OnPropertyChanged(); ApplyFilters(); }
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set { _endDate = value; OnPropertyChanged(); ApplyFilters(); }
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set { _selectedStatus = value; OnPropertyChanged(); ApplyFilters(); }
        }

        public Supplier SelectedSupplier
        {
            get => _selectedSupplier;
            set { _selectedSupplier = value; OnPropertyChanged(); ApplyFilters(); }
        }

        public async void LoadData()
        {
            try
            {
                // Initialize empty collections first
                UnpaidPurchaseOrders = new ObservableCollection<PurchaseOrder>();
                Suppliers = new ObservableCollection<Supplier>();
                UnpaidPurchaseOrdersCount = 0;
                TotalAmountDue = 0;
                DueThisWeek = 0;

                // Load data
                var suppliers = _dbService.GetAllSuppliers();
                var unpaidPurchaseOrders = _dbService.GetUnpaidPurchaseOrders();

                // Update collections
                Suppliers = new ObservableCollection<Supplier>(suppliers ?? new List<Supplier>());
                UnpaidPurchaseOrders = new ObservableCollection<PurchaseOrder>(unpaidPurchaseOrders ?? new List<PurchaseOrder>());

                // Update metrics
                UpdateMetrics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading accounts payable data: {ex.Message}", 
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateMetrics()
        {
            UnpaidPurchaseOrdersCount = UnpaidPurchaseOrders.Count;
            TotalAmountDue = UnpaidPurchaseOrders.Sum(p => p.GrandTotal);
            
            var today = DateTime.Now.Date;
            var nextWeek = today.AddDays(7);
            DueThisWeek = UnpaidPurchaseOrders.Count(p => p.DueDate <= nextWeek && p.DueDate >= today);
            
            // Update status for overdue orders
            foreach (var order in UnpaidPurchaseOrders)
            {
                if (order.DueDate < today && order.Status != "Overdue")
                {
                    order.Status = "Overdue";
                    _dbService.UpdatePurchaseOrder(order);
                }
            }
        }

        private void ApplyFilters()
        {
            var filteredOrders = _dbService.GetUnpaidPurchaseOrders();
            var today = DateTime.Now.Date;

            // Apply status filter
            if (!string.IsNullOrEmpty(SelectedStatus))
            {
                switch (SelectedStatus)
                {
                    case "Pending Payment":
                        filteredOrders = filteredOrders.Where(p => p.Status == "Pending").ToList();
                        break;
                    case "Payment Due":
                        filteredOrders = filteredOrders.Where(p => p.DueDate == today).ToList();
                        break;
                    case "Overdue":
                        filteredOrders = filteredOrders.Where(p => p.DueDate < today).ToList();
                        break;
                    case "Processing Payment":
                        filteredOrders = filteredOrders.Where(p => p.Status == "Processing").ToList();
                        break;
                    // "All Unpaid" shows everything, so no filter needed
                }
            }

            // Apply supplier filter
            if (SelectedSupplier != null)
            {
                filteredOrders = filteredOrders.Where(p => p.SupplierId == SelectedSupplier.Id).ToList();
            }

            // Apply date range filter
            if (StartDate.HasValue)
            {
                filteredOrders = filteredOrders.Where(p => p.OrderDate.Date >= StartDate.Value.Date).ToList();
            }
            if (EndDate.HasValue)
            {
                filteredOrders = filteredOrders.Where(p => p.OrderDate.Date <= EndDate.Value.Date).ToList();
            }

            UnpaidPurchaseOrders = new ObservableCollection<PurchaseOrder>(filteredOrders);
            UpdateMetrics();
        }

        public bool ProcessPayment(PurchaseOrder order, string paymentMethod, string referenceNumber, string notes)
        {
            try
            {
                // Update order status
                order.Status = "Paid";
                order.PaymentMethod = paymentMethod;
                order.PaymentReference = referenceNumber;
                order.Notes = notes;
                order.PaymentDate = DateTime.Now;
                
                _dbService.UpdatePurchaseOrder(order);

                // Remove from the list and update metrics
                UnpaidPurchaseOrders.Remove(order);
                UpdateMetrics();

                MessageBox.Show("Payment processed successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing payment: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 