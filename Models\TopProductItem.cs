using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Models
{
    public class TopProductItem : INotifyPropertyChanged
    {
        private Product _product;
        private decimal _totalQuantity; // ✅ WEIGHT-BASED FIX: Changed from int to decimal to support weight-based products
        private decimal _totalSales;
        private decimal _totalProfit;

        public Product Product
        {
            get => _product;
            set
            {
                if (_product != value)
                {
                    _product = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalQuantity // ✅ WEIGHT-BASED FIX: Changed from int to decimal to support weight-based products
        {
            get => _totalQuantity;
            set
            {
                if (_totalQuantity != value)
                {
                    _totalQuantity = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set
            {
                if (_totalSales != value)
                {
                    _totalSales = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalProfit
        {
            get => _totalProfit;
            set
            {
                if (_totalProfit != value)
                {
                    _totalProfit = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal ProfitMargin => TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;

        public string FormattedTotalSales => $"{TotalSales:N2}";
        public string FormattedTotalProfit => $"{TotalProfit:N2}";
        public string FormattedProfitMargin => $"{ProfitMargin:N1}%";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 