using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using POSSystem.Utilities;

namespace POSSystem.Views
{
    /// <summary>
    /// ✅ DEBUG LOG VIEWER: Interactive window to view and analyze debug logs
    /// </summary>
    public partial class DebugLogViewer : Window
    {
        private readonly string _defaultSearchText = "Search logs...";

        public DebugLogViewer()
        {
            InitializeComponent();
            LoadInitialData();
        }

        private void LoadInitialData()
        {
            try
            {
                StatusText.Text = "Loading debug logs...";
                
                // Load debug log
                var debugLog = DebugFileReader.GetLatestDebugLog();
                DebugLogTextBox.Text = debugLog;
                
                // Load performance log
                var performanceLog = DebugFileReader.GetLatestPerformanceLog();
                PerformanceLogTextBox.Text = performanceLog;
                
                // Load summary
                var summary = DebugFileReader.GetLogFilesSummary();
                SummaryTextBox.Text = summary;
                
                // Update status
                var lineCount = debugLog.Split('\n').Length;
                StatusText.Text = "Ready";
                LogInfoText.Text = $"Debug log: {lineCount:N0} lines";
                
                // Scroll to bottom of debug log
                DebugLogTextBox.ScrollToEnd();
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error loading logs: {ex.Message}";
                DebugLogTextBox.Text = $"Error loading debug log: {ex.Message}";
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadInitialData();
            StatusText.Text = "Logs refreshed";
        }

        private void OpenLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DebugFileReader.OpenLogsDirectory();
                StatusText.Text = "Opened logs directory";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error opening logs directory: {ex.Message}";
            }
        }

        private void PerformanceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "Extracting performance entries...";
                
                var performanceEntries = DebugFileReader.GetPerformanceEntries();
                DebugLogTextBox.Text = performanceEntries;
                
                var lineCount = performanceEntries.Split('\n').Length;
                StatusText.Text = "Performance entries loaded";
                LogInfoText.Text = $"Performance entries: {lineCount:N0} lines";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error extracting performance entries: {ex.Message}";
            }
        }

        private void TailButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "Loading last 50 lines...";
                
                var tail = DebugFileReader.GetLatestDebugLogTail(50);
                DebugLogTextBox.Text = tail;
                
                StatusText.Text = "Last 50 lines loaded";
                LogInfoText.Text = "Showing last 50 lines";
                
                // Scroll to bottom
                DebugLogTextBox.ScrollToEnd();
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error loading tail: {ex.Message}";
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var searchTerm = SearchBox.Text;
                if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm == _defaultSearchText)
                {
                    StatusText.Text = "Please enter a search term";
                    return;
                }

                StatusText.Text = $"Searching for '{searchTerm}'...";
                
                var searchResults = DebugFileReader.SearchDebugLog(searchTerm, 3);
                SearchResultsTextBox.Text = searchResults;
                
                // Switch to search results tab
                var tabControl = FindParent<TabControl>(SearchResultsTextBox);
                if (tabControl != null)
                {
                    tabControl.SelectedIndex = 2; // Search Results tab
                }
                
                var matchCount = searchResults.Split(new[] { "=== Match at line" }, StringSplitOptions.None).Length - 1;
                StatusText.Text = $"Search completed";
                LogInfoText.Text = $"Found {matchCount} matches for '{searchTerm}'";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error searching: {ex.Message}";
            }
        }

        private void SearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchBox.Text == _defaultSearchText)
            {
                SearchBox.Text = "";
                SearchBox.Foreground = Brushes.Black;
            }
        }

        private void SearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchBox.Text))
            {
                SearchBox.Text = _defaultSearchText;
                SearchBox.Foreground = Brushes.Gray;
            }
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            // Allow Ctrl+F to focus search box
            if (e.Key == System.Windows.Input.Key.F && 
                (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
            {
                SearchBox.Focus();
                SearchBox.SelectAll();
                e.Handled = true;
            }
            // Allow F5 to refresh
            else if (e.Key == System.Windows.Input.Key.F5)
            {
                RefreshButton_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }
            // Allow Enter in search box to search
            else if (e.Key == System.Windows.Input.Key.Enter && SearchBox.IsFocused)
            {
                SearchButton_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }

            base.OnKeyDown(e);
        }

        /// <summary>
        /// Helper method to find parent control of specific type
        /// </summary>
        private static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parentObject = System.Windows.Media.VisualTreeHelper.GetParent(child);
            if (parentObject == null) return null;

            if (parentObject is T parent)
                return parent;

            return FindParent<T>(parentObject);
        }
    }
}
