using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.Caching
{
    /// <summary>
    /// Advanced caching service interface with intelligent cache management
    /// Supports multiple cache levels, TTL, dependencies, and cache warming
    /// </summary>
    public interface ICacheService
    {
        // Basic cache operations
        Task<T> GetAsync<T>(string key);
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null);
        Task RemoveAsync(string key);
        Task ClearAsync();
        Task<bool> ExistsAsync(string key);

        // Advanced operations
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null);
        Task SetWithDependencyAsync<T>(string key, T value, string[] dependencies, TimeSpan? expiry = null);
        Task InvalidateDependenciesAsync(string dependency);

        // Cache warming and preloading
        Task WarmCacheAsync();
        Task PreloadReferenceDataAsync();

        // Cache statistics and monitoring
        CacheStatistics GetStatistics();
        Task<Dictionary<string, object>> GetCacheInfoAsync();

        // Cache levels for different data types
        Task<T> GetFromLevel<T>(CacheLevel level, string key);
        Task SetToLevel<T>(CacheLevel level, string key, T value, TimeSpan? expiry = null);
    }

    /// <summary>
    /// Cache levels for different types of data with different TTL and strategies
    /// </summary>
    public enum CacheLevel
    {
        /// <summary>Reference data that rarely changes (categories, units, settings)</summary>
        Reference = 1,
        
        /// <summary>Business data that changes moderately (products, customers)</summary>
        Business = 2,
        
        /// <summary>Transactional data that changes frequently (sales, inventory)</summary>
        Transactional = 3,
        
        /// <summary>Temporary data for UI performance (search results, statistics)</summary>
        Temporary = 4
    }

    /// <summary>
    /// Cache statistics for monitoring and optimization
    /// </summary>
    public class CacheStatistics
    {
        public int TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public int HitCount { get; set; }
        public int MissCount { get; set; }
        public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
        public int TotalRequests => HitCount + MissCount;
        public DateTime LastCleared { get; set; }
        public Dictionary<CacheLevel, int> KeysByLevel { get; set; } = new();
    }
}
