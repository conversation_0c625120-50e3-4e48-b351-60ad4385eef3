-- Create UserPermissions table
CREATE TABLE IF NOT EXISTS UserPermissions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId INTEGER NOT NULL,
    CanCreateSales INTEGER DEFAULT 0,
    CanVoidSales INTEGER DEFAULT 0,
    CanApplyDiscount INTEGER DEFAULT 0,
    CanViewSalesHistory INTEGER DEFAULT 0,
    CanManageProducts INTEGER DEFAULT 0,
    CanManageCategories INTEGER DEFAULT 0,
    CanViewInventory INTEGER DEFAULT 0,
    CanAdjustInventory INTEGER DEFAULT 0,
    CanManageExpenses INTEGER DEFAULT 0,
    CanManageCashDrawer INTEGER DEFAULT 0,
    CanViewReports INTEGER DEFAULT 0,
    CanManagePrices INTEGER DEFAULT 0,
    CanManageCustomers INTEGER DEFAULT 0,
    CanManageSuppliers INTEGER DEFAULT 0,
    CanManageUsers INTEGER DEFAULT 0,
    CanManageRoles INTEGER DEFAULT 0,
    CanAccessSettings INTEGER DEFAULT 0,
    <PERSON><PERSON>iewLogs INTEGER DEFAULT 0,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
    UpdatedAt TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
); 