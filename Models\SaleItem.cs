﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POSSystem.Models
{
    public class SaleItem
    {
        public int Id { get; set; }
        public int SaleId { get; set; }
        public int ProductId { get; set; }

        /// <summary>
        /// Quantity of the product sold.
        /// For weight-based products, this can be a decimal value (e.g., 2.5 kg).
        /// For unit-based products, this should be a whole number (e.g., 3 pieces).
        /// </summary>
        public decimal Quantity { get; set; }

        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }

        /// <summary>
        /// The actual cost basis used for this sale item, determined by FIFO allocation.
        /// For batch-tracked products, this is the weighted average cost of the specific batches used.
        /// For non-batch products, this is the product's purchase price at time of sale.
        /// </summary>
        public decimal ActualCostBasis { get; set; }

        public virtual Sale Sale { get; set; }
        public virtual Product Product { get; set; }
    }
}
