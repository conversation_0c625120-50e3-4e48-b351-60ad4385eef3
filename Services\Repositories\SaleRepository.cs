using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// CORRECT implementation of Sale repository
    /// SAFE APPROACH: Works alongside existing DatabaseService
    /// </summary>
    public class SaleRepository : ISaleRepository
    {
        private readonly POSDbContext _context;
        private readonly ILogger<SaleRepository> _logger;

        public SaleRepository(POSDbContext context, ILogger<SaleRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
        }

        public async Task<Sale> GetByIdAsync(int id)
        {
            try
            {
                return await _context.Sales
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Payments)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sale {SaleId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetPagedAsync(int page, int pageSize)
        {
            try
            {
                return await _context.Sales
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.SaleDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting paged sales page {Page}, size {PageSize}", page, pageSize);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales by date range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime date)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.SaleDate.Date == date.Date)
                    .SumAsync(s => s.GrandTotal);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting total sales for date {Date}", date);
                throw;
            }
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .SumAsync(s => s.GrandTotal);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting total sales for range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<Sale> CreateAsync(Sale sale)
        {
            try
            {
                sale.SaleDate = DateTime.Now;
                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();
                return sale;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating sale");
                throw;
            }
        }

        public async Task UpdateAsync(Sale sale)
        {
            try
            {
                _context.Sales.Update(sale);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating sale {SaleId}", sale.Id);
                throw;
            }
        }

        public async Task DeleteAsync(int id)
        {
            try
            {
                var sale = await _context.Sales.FindAsync(id);
                if (sale != null)
                {
                    _context.Sales.Remove(sale);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error deleting sale {SaleId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetRecentAsync(int limit = 10)
        {
            try
            {
                return await _context.Sales
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.SaleDate)
                    .Take(limit)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting recent sales with limit {Limit}", limit);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetUnpaidSalesAsync()
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.PaymentStatus != "Paid")
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting unpaid sales");
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetByCustomerAsync(int customerId)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.CustomerId == customerId)
                    .Include(s => s.Items)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetByUserAsync(int userId)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.UserId == userId)
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales for user {UserId}", userId);
                throw;
            }
        }

        public async Task<int> GetSalesCountAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Sales
                    .CountAsync(s => s.SaleDate >= startDate && s.SaleDate <= endDate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales count for range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<Dictionary<string, decimal>> GetSalesByPaymentMethodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .GroupBy(s => s.PaymentMethod)
                    .Select(g => new { Method = g.Key, Total = g.Sum(s => s.GrandTotal) })
                    .ToDictionaryAsync(x => x.Method, x => x.Total);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales by payment method for range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetTopSalesAsync(int limit = 10)
        {
            try
            {
                return await _context.Sales
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.GrandTotal)
                    .Take(limit)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting top sales with limit {Limit}", limit);
                throw;
            }
        }

        public async Task<bool> HasSalesForDateAsync(DateTime date)
        {
            try
            {
                return await _context.Sales.AnyAsync(s => s.SaleDate.Date == date.Date);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if sales exist for date {Date}", date);
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetAllAsync()
        {
            try
            {
                // ⚠️ WARNING: Use with caution - consider pagination
                return await _context.Sales
                    .Include(s => s.Customer)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting all sales");
                throw;
            }
        }

        public async Task<IEnumerable<Sale>> GetByDateAsync(DateTime date)
        {
            try
            {
                return await _context.Sales
                    .Where(s => s.SaleDate.Date == date.Date)
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                    .OrderByDescending(s => s.SaleDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting sales for date {Date}", date);
                throw;
            }
        }
    }
}
