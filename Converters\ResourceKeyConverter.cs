using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class ResourceKeyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string resourceKey)
            {
                // Try with TimePeriod_ prefix for period values
                if (resourceKey.StartsWith("TimePeriod_"))
                {
                    var periodTranslation = Application.Current.TryFindResource(resourceKey);
                    return periodTranslation ?? resourceKey;
                }
                
                // Try with DiscountType prefix for discount types
                var prefixedKey = $"DiscountType{resourceKey.Replace(" ", "")}";
                var translation = Application.Current.TryFindResource(prefixedKey);
                
                // If not found, try without prefix
                if (translation == null)
                {
                    translation = Application.Current.TryFindResource(resourceKey);
                }
                
                return translation ?? resourceKey;
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 