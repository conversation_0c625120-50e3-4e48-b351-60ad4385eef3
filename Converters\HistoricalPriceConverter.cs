using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Converter that calculates the correct historical unit price from stored Total and Quantity.
    /// This ensures that the displayed unit price is consistent with the actual total that was charged,
    /// rather than potentially incorrect stored UnitPrice values.
    /// </summary>
    public class HistoricalPriceConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (values.Length >= 3 && 
                    values[0] is decimal total && 
                    values[1] is decimal quantity &&
                    values[2] is decimal storedUnitPrice)
                {
                    // If quantity is zero, return the stored unit price to avoid division by zero
                    if (quantity == 0)
                    {
                        return storedUnitPrice;
                    }

                    // Calculate the actual unit price from the stored total
                    decimal calculatedUnitPrice = total / quantity;

                    // If the calculated price matches the stored price (within a small tolerance),
                    // use the stored price to avoid rounding display issues
                    decimal tolerance = 0.001m;
                    if (Math.Abs(calculatedUnitPrice - storedUnitPrice) <= tolerance)
                    {
                        return storedUnitPrice;
                    }

                    // Otherwise, use the calculated price to ensure consistency with the total
                    return calculatedUnitPrice;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[HISTORICAL-PRICE-CONVERTER] Error: {ex.Message}");
            }

            // Fallback to stored unit price if conversion fails
            if (values.Length >= 3 && values[2] is decimal fallbackPrice)
            {
                return fallbackPrice;
            }

            return 0m;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ConvertBack is not supported for HistoricalPriceConverter");
        }
    }
}
