using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface for dashboard data loading, caching, and aggregation service.
    /// </summary>
    public interface IDashboardDataService
    {
        /// <summary>
        /// Gets sales data for the specified date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <param name="cacheIdentifier">Optional identifier to create unique cache key for different period selections</param>
        /// <returns>List of dashboard sales</returns>
        Task<List<DashboardSale>> GetSalesDataAsync(DateTime startDate, DateTime endDate, string cacheIdentifier = null);
        
        /// <summary>
        /// Gets sales aggregation data for the specified date range and metric type
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <param name="metricType">The metric type (sales, profit, etc.)</param>
        /// <param name="cacheIdentifier">Optional identifier to create unique cache key for different period selections</param>
        /// <returns>List of sales aggregations</returns>
        Task<List<SaleAggregation>> GetSalesAggregationAsync(DateTime startDate, DateTime endDate, string metricType = "sales", string cacheIdentifier = null);
        
        /// <summary>
        /// Extracts metric data from aggregations for chart visualization
        /// </summary>
        /// <param name="aggregations">The sales aggregations</param>
        /// <param name="metricType">The metric type</param>
        /// <param name="labelFormatter">Function to format date labels</param>
        /// <returns>Tuple of values and labels</returns>
        (List<decimal> values, List<string> labels) ExtractMetricData(
            List<SaleAggregation> aggregations, 
            string metricType,
            Func<DateTime, string> labelFormatter);
        
        /// <summary>
        /// Gets the count of sales in a date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>Count of sales in the date range</returns>
        Task<int> GetSalesCountAsync(DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Clears the data cache
        /// </summary>
        void ClearCache();
        
        /// <summary>
        /// Removes expired cache entries
        /// </summary>
        void CleanupCache();
    }
    
    /// <summary>
    /// Represents aggregated sales data for a specific time period
    /// </summary>
    public class SaleAggregation
    {
        /// <summary>
        /// Date of the aggregation
        /// </summary>
        public DateTime Date { get; set; }
        
        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSales { get; set; }
        
        /// <summary>
        /// Total profit amount
        /// </summary>
        public decimal TotalProfit { get; set; }
        
        /// <summary>
        /// Total number of items sold
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// Total number of orders
        /// </summary>
        public int OrderCount { get; set; }
    }
} 