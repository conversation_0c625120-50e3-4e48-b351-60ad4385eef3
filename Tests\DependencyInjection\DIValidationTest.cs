using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Tests.DependencyInjection
{
    /// <summary>
    /// Comprehensive test to validate dependency injection configuration and identify issues.
    /// This test ensures all services are properly registered and can be resolved.
    /// </summary>
    public class DIValidationTest
    {
        /// <summary>
        /// Validates the entire DI configuration and reports issues.
        /// </summary>
        /// <returns>Validation result with detailed information</returns>
        public static async Task<DIValidationResult> ValidateDIConfigurationAsync()
        {
            var result = new DIValidationResult();
            
            try
            {
                Console.WriteLine("=== Dependency Injection Validation Test ===");
                Console.WriteLine();

                // Create service provider
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                result.ServiceProvider = serviceProvider;

                // Test 1: Core Service Resolution
                Console.WriteLine("1. Testing Core Service Resolution...");
                await TestCoreServices(serviceProvider, result);

                // Test 2: ViewModel Resolution
                Console.WriteLine();
                Console.WriteLine("2. Testing ViewModel Resolution...");
                await TestViewModels(serviceProvider, result);

                // Test 3: Interface Implementations
                Console.WriteLine();
                Console.WriteLine("3. Testing Interface Implementations...");
                await TestInterfaces(serviceProvider, result);

                // Test 4: Service Lifetimes
                Console.WriteLine();
                Console.WriteLine("4. Testing Service Lifetimes...");
                await TestServiceLifetimes(serviceProvider, result);

                // Test 5: Circular Dependencies
                Console.WriteLine();
                Console.WriteLine("5. Testing for Circular Dependencies...");
                await TestCircularDependencies(serviceProvider, result);

                // Test 6: Service Locator Anti-patterns
                Console.WriteLine();
                Console.WriteLine("6. Analyzing Service Locator Anti-patterns...");
                await AnalyzeServiceLocatorPatterns(serviceProvider, result);

                // Generate final report
                GenerateFinalReport(result);

                return result;
            }
            catch (Exception ex)
            {
                result.ValidationError = ex.Message;
                result.IsValid = false;
                Console.WriteLine($"❌ DI Validation failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Tests core service resolution.
        /// </summary>
        private static async Task TestCoreServices(IServiceProvider serviceProvider, DIValidationResult result)
        {
            var coreServices = new[]
            {
                typeof(IDatabaseService),
                typeof(IUserManagementService),
                typeof(IProductManagementService),
                typeof(ISalesManagementService),
                typeof(ICustomerManagementService),
                typeof(IInventoryManagementService)
            };

            foreach (var serviceType in coreServices)
            {
                try
                {
                    var service = serviceProvider.GetService(serviceType);
                    if (service != null)
                    {
                        result.ResolvedServices.Add(serviceType.Name);
                        Console.WriteLine($"   ✓ {serviceType.Name} - Resolved successfully");
                    }
                    else
                    {
                        result.FailedServices.Add(serviceType.Name);
                        Console.WriteLine($"   ❌ {serviceType.Name} - Failed to resolve (returned null)");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedServices.Add(serviceType.Name);
                    result.ServiceErrors.Add($"{serviceType.Name}: {ex.Message}");
                    Console.WriteLine($"   ❌ {serviceType.Name} - Exception: {ex.Message}");
                }
            }

            await Task.Delay(10); // Simulate async operation
        }

        /// <summary>
        /// Tests ViewModel resolution.
        /// </summary>
        private static async Task TestViewModels(IServiceProvider serviceProvider, DIValidationResult result)
        {
            var viewModelTypes = new[]
            {
                typeof(ProductsViewModel),
                typeof(SaleViewModel),
                typeof(DashboardViewModel),
                typeof(CustomersViewModel),
                typeof(SalesHistoryViewModel)
            };

            foreach (var viewModelType in viewModelTypes)
            {
                try
                {
                    var viewModel = serviceProvider.GetService(viewModelType);
                    if (viewModel != null)
                    {
                        result.ResolvedViewModels.Add(viewModelType.Name);
                        Console.WriteLine($"   ✓ {viewModelType.Name} - Resolved successfully");
                    }
                    else
                    {
                        result.FailedViewModels.Add(viewModelType.Name);
                        Console.WriteLine($"   ⚠️ {viewModelType.Name} - Not registered (this may be intentional)");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedViewModels.Add(viewModelType.Name);
                    result.ViewModelErrors.Add($"{viewModelType.Name}: {ex.Message}");
                    Console.WriteLine($"   ❌ {viewModelType.Name} - Exception: {ex.Message}");
                }
            }

            await Task.Delay(10);
        }

        /// <summary>
        /// Tests interface implementations.
        /// </summary>
        private static async Task TestInterfaces(IServiceProvider serviceProvider, DIValidationResult result)
        {
            var interfaceTests = new Dictionary<Type, Type>
            {
                { typeof(IDatabaseService), typeof(DatabaseService) },
                { typeof(IAlertService), null }, // Should be registered
                { typeof(IProductLookupService), null }, // Should be registered
                { typeof(IDiscountService), typeof(DiscountService) }
            };

            foreach (var test in interfaceTests)
            {
                try
                {
                    var service = serviceProvider.GetService(test.Key);
                    if (service != null)
                    {
                        var actualType = service.GetType();
                        if (test.Value == null || actualType == test.Value || test.Value.IsAssignableFrom(actualType))
                        {
                            result.ValidInterfaces.Add(test.Key.Name);
                            Console.WriteLine($"   ✓ {test.Key.Name} -> {actualType.Name}");
                        }
                        else
                        {
                            result.InvalidInterfaces.Add($"{test.Key.Name} -> Expected: {test.Value.Name}, Actual: {actualType.Name}");
                            Console.WriteLine($"   ⚠️ {test.Key.Name} -> Expected: {test.Value.Name}, Actual: {actualType.Name}");
                        }
                    }
                    else
                    {
                        result.InvalidInterfaces.Add($"{test.Key.Name} -> Not registered");
                        Console.WriteLine($"   ❌ {test.Key.Name} -> Not registered");
                    }
                }
                catch (Exception ex)
                {
                    result.InvalidInterfaces.Add($"{test.Key.Name} -> Exception: {ex.Message}");
                    Console.WriteLine($"   ❌ {test.Key.Name} -> Exception: {ex.Message}");
                }
            }

            await Task.Delay(10);
        }

        /// <summary>
        /// Tests service lifetimes.
        /// </summary>
        private static async Task TestServiceLifetimes(IServiceProvider serviceProvider, DIValidationResult result)
        {
            // Test singleton services
            try
            {
                var settings1 = serviceProvider.GetService<ISettingsService>();
                var settings2 = serviceProvider.GetService<ISettingsService>();
                
                if (settings1 != null && settings2 != null)
                {
                    if (ReferenceEquals(settings1, settings2))
                    {
                        result.CorrectLifetimes.Add("ISettingsService - Singleton");
                        Console.WriteLine("   ✓ ISettingsService - Correctly registered as Singleton");
                    }
                    else
                    {
                        result.IncorrectLifetimes.Add("ISettingsService - Should be Singleton but creates new instances");
                        Console.WriteLine("   ⚠️ ISettingsService - Should be Singleton but creates new instances");
                    }
                }
            }
            catch (Exception ex)
            {
                result.LifetimeErrors.Add($"ISettingsService: {ex.Message}");
                Console.WriteLine($"   ❌ ISettingsService - Error: {ex.Message}");
            }

            // Test scoped services
            using (var scope1 = serviceProvider.CreateScope())
            using (var scope2 = serviceProvider.CreateScope())
            {
                try
                {
                    var db1a = scope1.ServiceProvider.GetService<IDatabaseService>();
                    var db1b = scope1.ServiceProvider.GetService<IDatabaseService>();
                    var db2 = scope2.ServiceProvider.GetService<IDatabaseService>();

                    if (db1a != null && db1b != null && db2 != null)
                    {
                        if (ReferenceEquals(db1a, db1b) && !ReferenceEquals(db1a, db2))
                        {
                            result.CorrectLifetimes.Add("IDatabaseService - Scoped");
                            Console.WriteLine("   ✓ IDatabaseService - Correctly registered as Scoped");
                        }
                        else
                        {
                            result.IncorrectLifetimes.Add("IDatabaseService - Lifetime behavior incorrect");
                            Console.WriteLine("   ⚠️ IDatabaseService - Lifetime behavior incorrect");
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.LifetimeErrors.Add($"IDatabaseService: {ex.Message}");
                    Console.WriteLine($"   ❌ IDatabaseService - Error: {ex.Message}");
                }
            }

            await Task.Delay(10);
        }

        /// <summary>
        /// Tests for circular dependencies.
        /// </summary>
        private static async Task TestCircularDependencies(IServiceProvider serviceProvider, DIValidationResult result)
        {
            var potentialCircularServices = new[]
            {
                typeof(IDatabaseService),
                typeof(IProductManagementService),
                typeof(ISalesManagementService)
            };

            foreach (var serviceType in potentialCircularServices)
            {
                try
                {
                    using var scope = serviceProvider.CreateScope();
                    var service = scope.ServiceProvider.GetService(serviceType);
                    
                    if (service != null)
                    {
                        result.NoDependencyIssues.Add(serviceType.Name);
                        Console.WriteLine($"   ✓ {serviceType.Name} - No circular dependency detected");
                    }
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("circular"))
                {
                    result.CircularDependencies.Add(serviceType.Name);
                    Console.WriteLine($"   ❌ {serviceType.Name} - Circular dependency detected: {ex.Message}");
                }
                catch (Exception ex)
                {
                    result.DependencyErrors.Add($"{serviceType.Name}: {ex.Message}");
                    Console.WriteLine($"   ⚠️ {serviceType.Name} - Dependency error: {ex.Message}");
                }
            }

            await Task.Delay(10);
        }

        /// <summary>
        /// Analyzes service locator anti-patterns.
        /// </summary>
        private static async Task AnalyzeServiceLocatorPatterns(IServiceProvider serviceProvider, DIValidationResult result)
        {
            // Use the DICleanupService to analyze patterns
            try
            {
                var cleanupService = new POSSystem.Services.DependencyInjection.DICleanupService(serviceProvider);
                var analysisResult = cleanupService.AnalyzeDIConfiguration();

                result.ServiceLocatorIssues = analysisResult.ServiceLocatorIssues.Count;
                result.LifetimeIssues = analysisResult.LifetimeIssues.Count;
                result.MissingRegistrations = analysisResult.MissingRegistrations.Count;
                result.DIRecommendations = analysisResult.Recommendations;

                Console.WriteLine($"   Service Locator Issues: {result.ServiceLocatorIssues}");
                Console.WriteLine($"   Lifetime Issues: {result.LifetimeIssues}");
                Console.WriteLine($"   Missing Registrations: {result.MissingRegistrations}");
                Console.WriteLine($"   Total DI Issues: {analysisResult.TotalIssueCount}");
            }
            catch (Exception ex)
            {
                result.AnalysisErrors.Add($"DI Analysis failed: {ex.Message}");
                Console.WriteLine($"   ❌ DI Analysis failed: {ex.Message}");
            }

            await Task.Delay(10);
        }

        /// <summary>
        /// Generates the final validation report.
        /// </summary>
        private static void GenerateFinalReport(DIValidationResult result)
        {
            Console.WriteLine();
            Console.WriteLine("=== DI Validation Summary ===");
            Console.WriteLine($"✓ Resolved Services: {result.ResolvedServices.Count}");
            Console.WriteLine($"❌ Failed Services: {result.FailedServices.Count}");
            Console.WriteLine($"✓ Resolved ViewModels: {result.ResolvedViewModels.Count}");
            Console.WriteLine($"❌ Failed ViewModels: {result.FailedViewModels.Count}");
            Console.WriteLine($"✓ Valid Interfaces: {result.ValidInterfaces.Count}");
            Console.WriteLine($"❌ Invalid Interfaces: {result.InvalidInterfaces.Count}");
            Console.WriteLine($"✓ Correct Lifetimes: {result.CorrectLifetimes.Count}");
            Console.WriteLine($"⚠️ Incorrect Lifetimes: {result.IncorrectLifetimes.Count}");
            Console.WriteLine($"❌ Circular Dependencies: {result.CircularDependencies.Count}");
            Console.WriteLine($"⚠️ Service Locator Issues: {result.ServiceLocatorIssues}");

            // Determine overall validation status
            result.IsValid = result.FailedServices.Count == 0 && 
                           result.ServiceErrors.Count == 0 && 
                           result.CircularDependencies.Count == 0;

            Console.WriteLine();
            if (result.IsValid)
            {
                Console.WriteLine("🎉 DI Configuration is VALID!");
            }
            else
            {
                Console.WriteLine("⚠️ DI Configuration has issues that should be addressed.");
            }

            // Show recommendations if any
            if (result.DIRecommendations.Any())
            {
                Console.WriteLine();
                Console.WriteLine("📋 Recommendations:");
                foreach (var recommendation in result.DIRecommendations.Take(5))
                {
                    Console.WriteLine($"   • {recommendation}");
                }
            }
        }
    }

    /// <summary>
    /// Result of DI validation test.
    /// </summary>
    public class DIValidationResult
    {
        public bool IsValid { get; set; } = true;
        public string ValidationError { get; set; }
        public IServiceProvider ServiceProvider { get; set; }

        // Service resolution results
        public List<string> ResolvedServices { get; set; } = new List<string>();
        public List<string> FailedServices { get; set; } = new List<string>();
        public List<string> ServiceErrors { get; set; } = new List<string>();

        // ViewModel resolution results
        public List<string> ResolvedViewModels { get; set; } = new List<string>();
        public List<string> FailedViewModels { get; set; } = new List<string>();
        public List<string> ViewModelErrors { get; set; } = new List<string>();

        // Interface implementation results
        public List<string> ValidInterfaces { get; set; } = new List<string>();
        public List<string> InvalidInterfaces { get; set; } = new List<string>();

        // Lifetime validation results
        public List<string> CorrectLifetimes { get; set; } = new List<string>();
        public List<string> IncorrectLifetimes { get; set; } = new List<string>();
        public List<string> LifetimeErrors { get; set; } = new List<string>();

        // Dependency validation results
        public List<string> NoDependencyIssues { get; set; } = new List<string>();
        public List<string> CircularDependencies { get; set; } = new List<string>();
        public List<string> DependencyErrors { get; set; } = new List<string>();

        // DI pattern analysis results
        public int ServiceLocatorIssues { get; set; }
        public int LifetimeIssues { get; set; }
        public int MissingRegistrations { get; set; }
        public List<string> DIRecommendations { get; set; } = new List<string>();
        public List<string> AnalysisErrors { get; set; } = new List<string>();
    }
}
