using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Printing;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Windows;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Collections.Generic;

namespace POSSystem.ViewModels
{
    public class SalesHistoryViewModel : ViewModelBase
    {
        // Static event that can be raised when a new sale is created
        public static event EventHandler SaleCreated;

        // Method to trigger the event from other parts of the application
        public static void NotifyNewSale()
        {
            SaleCreated?.Invoke(null, EventArgs.Empty);
        }

        private DatabaseService _dbService;
        private IEnhancedReceiptPrintService _receiptPrintService;
        private ObservableCollection<Sale> _sales;
        private List<Sale> _allSales;
        private string _searchText;
        private bool _isCustomPeriod;
        private DateTime _currentStartDate;
        private DateTime _currentEndDate;
        private Sale _selectedSale;
        private bool _isLoading;
        private int _currentPage = 1;
        private const int PageSize = 50;
        private int _totalItems;
        private string _statusMessage;
        private CancellationTokenSource _cancellationTokenSource;

        public SalesHistoryViewModel()
        {
            // ✅ CRITICAL FIX: Move heavy initialization to background to prevent UI blocking
            System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] SalesHistoryViewModel constructor started");

            // ✅ CRITICAL MEMORY FIX: Initialize cancellation token for proper cleanup
            _cancellationTokenSource = new CancellationTokenSource();

            // Initialize collections immediately for UI binding
            Sales = new ObservableCollection<Sale>();
            BindingOperations.EnableCollectionSynchronization(Sales, new object());

            // Set initial date range to Today (matching ComboBox default selection)
            _currentStartDate = DateTime.Today;
            _currentEndDate = DateTime.Today.AddDays(1).AddSeconds(-1);

            // ✅ CRITICAL FIX: Initialize services and load data in background
            _ = Task.Run(async () =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] Starting background initialization");

                    // Initialize services in background
                    _dbService = (DatabaseService)(App.ServiceProvider?.GetService(typeof(DatabaseService))) ?? new DatabaseService();
                    _receiptPrintService = new EnhancedReceiptPrintService(_dbService);

                    // Load initial data in background
                    await LoadSalesAsync(_currentStartDate, _currentEndDate);

                    System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] Background initialization completed");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Background initialization error: {ex.Message}");

                    // Update UI on main thread with safe status message
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        StatusMessage = "Error loading sales data";
                        IsLoading = false;

                        // ✅ CRITICAL FIX: Ensure Sales collection is never null
                        if (Sales == null)
                        {
                            Sales = new ObservableCollection<Sale>();
                            BindingOperations.EnableCollectionSynchronization(Sales, new object());
                        }
                    });
                }
            });

            // ✅ CRITICAL MEMORY FIX: Subscribe to the SaleCreated event with proper handler reference
            SaleCreated += OnSaleCreated;

            System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] SalesHistoryViewModel constructor completed");
        }

        public ObservableCollection<Sale> Sales
        {
            get => _sales;
            set { _sales = value; OnPropertyChanged(); }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set { _statusMessage = value; OnPropertyChanged(); }
        }

        public Sale SelectedSale
        {
            get => _selectedSale;
            set 
            { 
                _selectedSale = value; 
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set 
            { 
                if (_searchText != value)
                {
                    _searchText = value;
                    _currentPage = 1; // Reset to first page when searching
                    FilterSalesAsync().ConfigureAwait(false);
                    OnPropertyChanged();
                }
            }
        }

        public bool IsCustomPeriod
        {
            get => _isCustomPeriod;
            set { _isCustomPeriod = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// Current start date for filtering (for testing purposes)
        /// </summary>
        public DateTime CurrentStartDate => _currentStartDate;

        /// <summary>
        /// Current end date for filtering (for testing purposes)
        /// </summary>
        public DateTime CurrentEndDate => _currentEndDate;

        /// <summary>
        /// Refreshes the sales data based on current filter settings.
        /// This method can be called when a new sale is created to immediately update the view.
        /// </summary>
        public void RefreshSales()
        {
            _currentPage = 1;
            _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
        }

        /// <summary>
        /// Initializes the filter to match the default ComboBox selection (Today)
        /// This ensures synchronization between UI and ViewModel on startup
        /// </summary>
        public void InitializeDefaultFilter()
        {
            // Set to Today filter to match ComboBox SelectedIndex="0"
            var todayText = Application.Current.TryFindResource("TimePeriod_Today") as string ?? "Today";
            UpdateSalesFilter(todayText);
        }

        public void UpdateSalesFilter(string period)
        {
            DateTime startDate, endDate;

            // Get localized resource strings for comparison
            var todayText = Application.Current.TryFindResource("TimePeriod_Today") as string ?? "Today";
            var yesterdayText = Application.Current.TryFindResource("TimePeriod_Yesterday") as string ?? "Yesterday";
            var last7DaysText = Application.Current.TryFindResource("TimePeriod_Last7Days") as string ?? "Last 7 Days";
            var last30DaysText = Application.Current.TryFindResource("TimePeriod_Last30Days") as string ?? "Last 30 Days";
            var customText = Application.Current.TryFindResource("TimePeriod_Custom") as string ?? "Custom";

            // Match against localized strings
            if (period == todayText)
            {
                startDate = DateTime.Today;
                endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
            }
            else if (period == yesterdayText)
            {
                startDate = DateTime.Today.AddDays(-1);
                endDate = DateTime.Today.AddSeconds(-1);
            }
            else if (period == last7DaysText)
            {
                startDate = DateTime.Today.AddDays(-7);
                endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
            }
            else if (period == last30DaysText)
            {
                startDate = DateTime.Today.AddDays(-30);
                endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
            }
            else if (period == customText)
            {
                IsCustomPeriod = true;
                return;
            }
            else
            {
                // Fallback to English strings for backward compatibility
                switch (period)
                {
                    case "Today":
                        startDate = DateTime.Today;
                        endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                        break;
                    case "Yesterday":
                        startDate = DateTime.Today.AddDays(-1);
                        endDate = DateTime.Today.AddSeconds(-1);
                        break;
                    case "Last 7 Days":
                        startDate = DateTime.Today.AddDays(-7);
                        endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                        break;
                    case "Last 30 Days":
                        startDate = DateTime.Today.AddDays(-30);
                        endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                        break;
                    case "Custom":
                        IsCustomPeriod = true;
                        return;
                    default:
                        return;
                }
            }

            IsCustomPeriod = false;
            _currentStartDate = startDate;
            _currentEndDate = endDate;
            _currentPage = 1;
            _ = LoadSalesAsync(startDate, endDate);
        }

        public void UpdateCustomDateRange(DateTime? start, DateTime? end)
        {
            if (start.HasValue && end.HasValue)
            {
                _currentStartDate = start.Value;
                _currentEndDate = end.Value.AddDays(1).AddSeconds(-1);
                _currentPage = 1;
                _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
            }
        }

        public async Task LoadMoreItems()
        {
            if (IsLoading) return;

            _currentPage++;
            await LoadPageAsync(_currentPage);
        }

        /// <summary>
        /// ✅ PERFORMANCE OPTIMIZATION: Preload additional pages in background for smoother scrolling
        /// </summary>
        private async Task PreloadNextPageAsync()
        {
            if (IsLoading || _currentPage * PageSize >= _totalItems) return;

            try
            {
                var nextPage = _currentPage + 1;
                var skip = (nextPage - 1) * PageSize;

                // Load next page in background without showing loading indicator
                var sales = await Task.Run(() =>
                    _dbService.GetSalesByDateRangePaged(_currentStartDate, _currentEndDate, skip, PageSize));

                // Add to _allSales for search functionality, but don't add to UI yet
                _allSales.AddRange(sales);

                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Preloaded page {nextPage} with {sales.Count} sales");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Error preloading next page: {ex.Message}");
            }
        }

        private async Task LoadSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15))) // Increased timeout for optimized loading
                {
                    // Set loading state immediately
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        IsLoading = true;
                        StatusMessage = Application.Current.TryFindResource("LoadingData") as string ?? "Loading sales data...";

                        // Ensure Sales collection is initialized
                        if (Sales == null)
                        {
                            Sales = new ObservableCollection<Sale>();
                            BindingOperations.EnableCollectionSynchronization(Sales, new object());
                        }

                        // Clear existing data immediately
                        Sales.Clear();
                    });

                    System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] Starting optimized sales data load");

                    // ✅ PERFORMANCE OPTIMIZATION: Load count and first page data in parallel
                    var countTask = Task.Run(() => _dbService.GetSalesCount(startDate, endDate), cts.Token);
                    var firstPageTask = Task.Run(() => _dbService.GetSalesByDateRangePaged(startDate, endDate, 0, PageSize), cts.Token);

                    // Wait for both operations to complete
                    await Task.WhenAll(countTask, firstPageTask);

                    _totalItems = await countTask;
                    var firstPageSales = await firstPageTask;

                    // ✅ PERFORMANCE OPTIMIZATION: Single UI update with all data
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _allSales = new List<Sale>(firstPageSales);

                        foreach (var sale in firstPageSales)
                        {
                            Sales.Add(sale);
                        }

                        _currentPage = 1;
                        UpdateStatusMessage();
                    }, System.Windows.Threading.DispatcherPriority.Normal);

                    System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Optimized sales data loaded: {_totalItems} total items, {firstPageSales.Count} loaded");
                }
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("[SALES-HISTORY] Sales loading timed out");
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "Loading timed out. Please try again.";
                    IsLoading = false;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Error loading sales: {ex.Message}");
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "Error loading sales data";
                    IsLoading = false;
                });
            }
            finally
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoading = false;
                });
            }
        }

        private async Task LoadPageAsync(int page, CancellationToken cancellationToken = default)
        {
            try
            {
                // ✅ PERFORMANCE OPTIMIZATION: Only show loading for additional pages, not first page
                if (page > 1)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = true);
                }

                var skip = (page - 1) * PageSize;

                // Load sales data in background with cancellation support
                var sales = await Task.Run(() =>
                    _dbService.GetSalesByDateRangePaged(_currentStartDate, _currentEndDate, skip, PageSize), cancellationToken);

                // ✅ PERFORMANCE OPTIMIZATION: Batch UI updates and reduce dispatcher calls
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Add to collections in batch
                    foreach (var sale in sales)
                    {
                        Sales.Add(sale);
                        _allSales.Add(sale);
                    }

                    _currentPage = page;

                    // Only update status if we're loading additional pages
                    if (page > 1)
                    {
                        UpdateStatusMessage();
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal);

                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Loaded page {page} with {sales.Count} sales");
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Page {page} loading was cancelled");
                throw; // Re-throw to be handled by caller
            }
            finally
            {
                // Only update loading state for additional pages
                if (page > 1)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        IsLoading = false;
                    });
                }
            }
        }

        private async Task FilterSalesAsync()
        {
            try
            {
                // ✅ PERFORMANCE OPTIMIZATION: Quick return for empty search without loading indicator
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Sales.Clear();
                        foreach (var sale in _allSales)
                        {
                            Sales.Add(sale);
                        }
                        UpdateStatusMessage();
                    });
                    return;
                }

                // Show loading only for complex searches
                IsLoading = true;
                var searchTerm = SearchText.ToLower();

                // ✅ PERFORMANCE OPTIMIZATION: Perform filtering in background with optimized LINQ
                var filteredSales = await Task.Run(() =>
                {
                    return _allSales
                        .Where(s =>
                            (s.InvoiceNumber?.ToLower().Contains(searchTerm) == true) ||
                            (s.Customer != null && $"{s.Customer.FirstName} {s.Customer.LastName}".ToLower().Contains(searchTerm)) ||
                            (s.PaymentMethod?.ToLower().Contains(searchTerm) == true) ||
                            (s.Status?.ToLower().Contains(searchTerm) == true)
                        ).ToList();
                });

                // ✅ PERFORMANCE OPTIMIZATION: Single UI update with all filtered results
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Sales.Clear();
                    foreach (var sale in filteredSales)
                    {
                        Sales.Add(sale);
                    }
                    UpdateStatusMessage();
                }, System.Windows.Threading.DispatcherPriority.Normal);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateStatusMessage()
        {
            try
            {
                // ✅ CRITICAL FIX: Add null checking for resource string to prevent ArgumentNullException
                var formatString = Application.Current.TryFindResource("ShowingItemsOf") as string;

                // Provide fallback if resource is not found
                if (string.IsNullOrEmpty(formatString))
                {
                    formatString = "Showing {0} of {1} items";
                }

                StatusMessage = string.Format(formatString, Sales?.Count ?? 0, _totalItems);

                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Status updated: {StatusMessage}");
            }
            catch (Exception ex)
            {
                // Fallback to simple status message if formatting fails
                StatusMessage = $"Showing {Sales?.Count ?? 0} of {_totalItems} items";
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY] Error updating status message: {ex.Message}");
            }
        }

        public bool VoidSale(Sale sale, string reason)
        {
            try
            {
                if (sale == null)
                    throw new ArgumentNullException(nameof(sale));

                if (string.IsNullOrWhiteSpace(reason))
                    throw new ArgumentException("A reason must be provided for voiding the sale.");

                if (sale.Status == "Voided")
                    throw new InvalidOperationException("This sale has already been voided.");

                // Create a sale history record
                var history = new SaleHistory
                {
                    SaleId = sale.Id,
                    Action = "Void",
                    Reason = reason,
                    UserId = 1, // TODO: Get from logged in user
                    ActionDate = DateTime.Now,
                    AdjustmentAmount = sale.GrandTotal
                };

                // Update sale status
                sale.Status = "Voided";
                
                // Restore product quantities
                foreach (var item in sale.Items)
                {
                    var product = item.Product;
                    product.StockQuantity += (int)Math.Ceiling(item.Quantity); // Round up for stock restoration
                    _dbService.UpdateProduct(product);
                }

                // Save changes
                _dbService.UpdateSale(sale);
                _dbService.SaveSaleHistory(history);

                // Refresh the sales list
                _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error voiding sale: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public bool RefundSale(Sale sale, decimal amount, string reason)
        {
            try
            {
                if (sale == null)
                    throw new ArgumentNullException(nameof(sale));

                if (string.IsNullOrWhiteSpace(reason))
                    throw new ArgumentException("A reason must be provided for the refund.");

                if (sale.Status == "Refunded")
                    throw new InvalidOperationException("This sale has already been refunded.");

                if (amount <= 0 || amount > sale.GrandTotal)
                    throw new ArgumentException("Invalid refund amount.");

                // Create a sale history record
                var history = new SaleHistory
                {
                    SaleId = sale.Id,
                    Action = amount == sale.GrandTotal ? "Full Refund" : "Partial Refund",
                    Reason = reason,
                    UserId = 1, // TODO: Get from logged in user
                    ActionDate = DateTime.Now,
                    AdjustmentAmount = amount
                };

                // Update sale status
                sale.Status = amount == sale.GrandTotal ? "Refunded" : "Partially Refunded";
                
                // Save changes
                _dbService.UpdateSale(sale);
                _dbService.SaveSaleHistory(history);

                // Refresh the sales list
                _ = LoadSalesAsync(_currentStartDate, _currentEndDate);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing refund: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// Reprint receipt for a sale
        /// </summary>
        public async Task<bool> ReprintReceiptAsync(Sale sale, bool showDialog = true)
        {
            try
            {
                if (sale == null)
                {
                    MessageBox.Show("No sale selected for receipt printing.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"[RECEIPT REPRINT] Reprinting receipt for sale {sale.Id}, Invoice: {sale.InvoiceNumber}");

                // Print receipt with dialog option
                bool success = await _receiptPrintService.PrintReceiptAsync(sale, showDialog);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("[RECEIPT REPRINT] Receipt reprinted successfully");

                    MessageBox.Show(
                        $"Receipt for invoice #{sale.InvoiceNumber} has been sent to the printer.",
                        "Receipt Printed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[RECEIPT REPRINT] Receipt reprinting failed or was cancelled");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT REPRINT] Error reprinting receipt: {ex.Message}");

                MessageBox.Show(
                    $"Error reprinting receipt: {ex.Message}",
                    "Print Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return false;
            }
        }

        /// <summary>
        /// Preview receipt for a sale
        /// </summary>
        public async Task<bool> PreviewReceiptAsync(Sale sale)
        {
            try
            {
                if (sale == null)
                {
                    MessageBox.Show("No sale selected for receipt preview.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"[RECEIPT PREVIEW] Previewing receipt for sale {sale.Id}, Invoice: {sale.InvoiceNumber}");

                return await _receiptPrintService.PreviewReceiptAsync(sale);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PREVIEW] Error previewing receipt: {ex.Message}");

                MessageBox.Show(
                    $"Error previewing receipt: {ex.Message}",
                    "Preview Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return false;
            }
        }

        /// <summary>
        /// Save receipt as PDF for a sale
        /// </summary>
        public async Task<bool> SaveReceiptAsPdfAsync(Sale sale)
        {
            try
            {
                if (sale == null)
                {
                    MessageBox.Show("No sale selected for PDF export.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // Show save file dialog
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "Save Receipt as PDF",
                    Filter = "PDF files (*.pdf)|*.pdf|XPS files (*.xps)|*.xps|All files (*.*)|*.*",
                    DefaultExt = "pdf",
                    FileName = $"Receipt_{sale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF] Saving receipt as PDF for sale {sale.Id}, Invoice: {sale.InvoiceNumber}");

                    bool success = await _receiptPrintService.SaveReceiptAsPdfAsync(sale, saveFileDialog.FileName);

                    if (success)
                    {
                        System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF] Receipt saved successfully: {saveFileDialog.FileName}");

                        MessageBox.Show(
                            $"Receipt saved successfully to:\n{saveFileDialog.FileName}",
                            "Receipt Saved",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }

                    return success;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF] Error saving receipt as PDF: {ex.Message}");

                MessageBox.Show(
                    $"Error saving receipt as PDF: {ex.Message}",
                    "Save Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return false;
            }
        }



        /// <summary>
        /// ✅ CRITICAL MEMORY FIX: Proper disposal pattern to prevent memory leaks
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // ✅ CRITICAL: Unsubscribe from static event to prevent memory leaks
                // Note: We need to store the handler reference to properly unsubscribe
                SaleCreated -= OnSaleCreated;

                // Clear collections to release references
                Sales?.Clear();

                // Cancel any ongoing operations
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }

            base.Dispose(disposing);
        }

        /// <summary>
        /// ✅ CRITICAL: Proper event handler for static event subscription
        /// </summary>
        private void OnSaleCreated(object sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(RefreshSales);
        }
    }
} 