using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace POSSystem.Converters
{
    public class DueDateColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Handle DateTime values (due dates)
            if (value is DateTime dueDate)
            {
                DateTime today = DateTime.Today;
                
                // If due date has passed
                if (dueDate.Date < today)
                {
                    return new SolidColorBrush(Colors.Red);
                }
                
                // If due date is within the next 7 days
                if ((dueDate.Date - today).TotalDays <= 7)
                {
                    return new SolidColorBrush(Colors.Orange);
                }
                
                // If due date is within the next 14 days
                if ((dueDate.Date - today).TotalDays <= 14)
                {
                    return new SolidColorBrush(Colors.DarkGoldenrod);
                }
                
                // Default - due date is far away
                return new SolidColorBrush(Colors.Black);
            }
            
            // Handle decimal values (balance due)
            if (value is decimal balance)
            {
                if (balance <= 0)
                {
                    // Fully paid
                    return new SolidColorBrush(Colors.ForestGreen);
                }
                else
                {
                    // Still has balance due
                    return new SolidColorBrush(Colors.Red);
                }
            }
            
            // Default color if value is not a date or decimal
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 