<UserControl x:Class="POSSystem.Views.Dialogs.ProductNotFoundChoiceDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="500" Height="350">
    
    <materialDesign:Card UniformCornerRadius="8" 
                         materialDesign:ElevationAssist.Elevation="Dp8"
                         Background="{DynamicResource MaterialDesignPaper}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    CornerRadius="8,8,0,0">
                <StackPanel Orientation="Horizontal" 
                           Margin="24,16">
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Width="24" 
                                           Height="24"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="Product Not Found" 
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             FontWeight="Medium"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section -->
            <StackPanel Grid.Row="1" Margin="24,20">
                <!-- Barcode Display -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="Barcode:" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Margin="0,0,0,4"/>
                    <TextBox x:Name="txtBarcode"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           IsReadOnly="True"
                           FontSize="16"
                           FontWeight="Medium"
                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>
                
                <!-- Message -->
                <TextBlock Text="This barcode was not found in the main product database. What would you like to do?"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         TextWrapping="Wrap"
                         TextAlignment="Center"
                         Margin="0,0,0,24"/>
                
                <!-- Options -->
                <StackPanel>
                    <!-- Option 1: Search External Database -->
                    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                       materialDesign:ElevationAssist.Elevation="Dp2"
                                       UniformCornerRadius="8"
                                       Cursor="Hand"
                                       MouseLeftButtonUp="SearchExternal_Click"
                                       Margin="0,0,0,12">
                        <Border Padding="16,12">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="DatabaseSearch" 
                                                       Width="20" 
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       Margin="0,0,12,0"/>
                                <StackPanel>
                                    <TextBlock Text="Search External Database" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="Look for this product in the external products database and import it"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </materialDesign:Card>
                    
                    <!-- Option 2: Create New Product -->
                    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                       materialDesign:ElevationAssist.Elevation="Dp2"
                                       UniformCornerRadius="8"
                                       Cursor="Hand"
                                       MouseLeftButtonUp="CreateNew_Click">
                        <Border Padding="16,12">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       Width="20" 
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                       Margin="0,0,12,0"/>
                                <StackPanel>
                                    <TextBlock Text="Create New Product" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="Create a completely new product with this barcode"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </materialDesign:Card>
                </StackPanel>
            </StackPanel>
            
            <!-- Footer -->
            <Border Grid.Row="2" 
                    BorderThickness="0,1,0,0"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    Padding="24,16">
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Right">
                    <Button x:Name="btnCancel"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Content="Cancel"
                          Margin="0,0,8,0"
                          Click="BtnCancel_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </materialDesign:Card>
</UserControl>
