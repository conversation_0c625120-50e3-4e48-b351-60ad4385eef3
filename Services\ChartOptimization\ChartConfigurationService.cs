using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace POSSystem.Services.ChartOptimization
{
    /// <summary>
    /// ✅ PRIORITY 4 OPTIMIZATION: Chart configuration service for managing performance settings
    /// </summary>
    public static class ChartConfigurationService
    {
        // Default performance thresholds
        private static readonly Dictionary<string, int> DefaultThresholds = new Dictionary<string, int>
        {
            ["SmallDataset"] = 20,
            ["MediumDataset"] = 50,
            ["LargeDataset"] = 100,
            ["VeryLargeDataset"] = 200,
            ["MaxChartPoints"] = 500,
            ["MaxPieSlices"] = 15,
            ["MaxColumnSeries"] = 50
        };

        // Performance settings cache
        private static Dictionary<string, object> _settingsCache = new Dictionary<string, object>();
        private static DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(10);

        /// <summary>
        /// ✅ OPTIMIZATION: Get performance threshold for dataset size
        /// </summary>
        public static int GetPerformanceThreshold(string thresholdType)
        {
            RefreshCacheIfNeeded();
            
            var key = $"Chart.{thresholdType}Threshold";
            
            // Try to get from app config first
            if (_settingsCache.ContainsKey(key) && _settingsCache[key] is int cachedValue)
            {
                return cachedValue;
            }
            
            // Try to read from configuration
            var configValue = ConfigurationManager.AppSettings[key];
            if (int.TryParse(configValue, out int parsedValue) && parsedValue > 0)
            {
                _settingsCache[key] = parsedValue;
                return parsedValue;
            }
            
            // Fall back to default
            if (DefaultThresholds.ContainsKey(thresholdType))
            {
                var defaultValue = DefaultThresholds[thresholdType];
                _settingsCache[key] = defaultValue;
                return defaultValue;
            }
            
            return 50; // Ultimate fallback
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Get chart performance configuration
        /// </summary>
        public static ChartPerformanceConfig GetPerformanceConfig(int dataPointCount)
        {
            var smallThreshold = GetPerformanceThreshold("SmallDataset");
            var mediumThreshold = GetPerformanceThreshold("MediumDataset");
            var largeThreshold = GetPerformanceThreshold("LargeDataset");
            var veryLargeThreshold = GetPerformanceThreshold("VeryLargeDataset");
            
            return new ChartPerformanceConfig
            {
                DataPointCount = dataPointCount,
                EnableDataLabels = dataPointCount <= smallThreshold,
                EnablePoints = dataPointCount <= smallThreshold,
                EnableSmoothing = dataPointCount <= mediumThreshold,
                EnableAnimations = dataPointCount <= mediumThreshold,
                EnableTooltips = dataPointCount <= largeThreshold,
                EnableStroke = dataPointCount <= largeThreshold,
                ShouldSampleData = dataPointCount > veryLargeThreshold,
                MaxDisplayPoints = GetPerformanceThreshold("MaxChartPoints"),
                OptimalColumnWidth = CalculateOptimalColumnWidth(dataPointCount),
                OptimalPointSize = CalculateOptimalPointSize(dataPointCount),
                OptimalStrokeThickness = CalculateOptimalStrokeThickness(dataPointCount),
                OptimalSmoothness = CalculateOptimalSmoothness(dataPointCount)
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Get pie chart specific configuration
        /// </summary>
        public static PieChartPerformanceConfig GetPieChartConfig(int sliceCount)
        {
            var maxSlices = GetPerformanceThreshold("MaxPieSlices");
            
            return new PieChartPerformanceConfig
            {
                SliceCount = sliceCount,
                EnableDataLabels = sliceCount <= 10,
                EnableDetailedLabels = sliceCount <= 8,
                EnableStroke = sliceCount <= 12,
                ShouldGroupSmallSlices = sliceCount > maxSlices,
                MaxDisplaySlices = maxSlices,
                MinSlicePercentage = sliceCount > 10 ? 2.0 : 1.0, // Group slices smaller than this percentage
                OptimalStrokeThickness = sliceCount <= 8 ? 2 : (sliceCount <= 12 ? 1 : 0)
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Check if chart should use high performance mode
        /// </summary>
        public static bool ShouldUseHighPerformanceMode(int dataPointCount)
        {
            return dataPointCount > GetPerformanceThreshold("LargeDataset");
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Get recommended update frequency based on data size
        /// </summary>
        public static TimeSpan GetRecommendedUpdateFrequency(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= 20 => TimeSpan.FromSeconds(1),   // Real-time updates for small datasets
                <= 50 => TimeSpan.FromSeconds(2),   // Fast updates for medium datasets
                <= 100 => TimeSpan.FromSeconds(5),  // Moderate updates for large datasets
                <= 200 => TimeSpan.FromSeconds(10), // Slow updates for very large datasets
                _ => TimeSpan.FromSeconds(30)       // Very slow updates for massive datasets
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Get memory usage estimate for chart configuration
        /// </summary>
        public static ChartMemoryEstimate EstimateMemoryUsage(ChartPerformanceConfig config)
        {
            var baseMemoryPerPoint = 50; // bytes per data point
            var labelMemoryPerPoint = config.EnableDataLabels ? 100 : 0; // bytes per label
            var pointMemoryPerPoint = config.EnablePoints ? 25 : 0; // bytes per point geometry
            var strokeMemoryPerPoint = config.EnableStroke ? 15 : 0; // bytes per stroke
            
            var totalMemoryPerPoint = baseMemoryPerPoint + labelMemoryPerPoint + pointMemoryPerPoint + strokeMemoryPerPoint;
            var estimatedTotalMemory = config.DataPointCount * totalMemoryPerPoint;
            
            return new ChartMemoryEstimate
            {
                EstimatedMemoryBytes = estimatedTotalMemory,
                EstimatedMemoryMB = estimatedTotalMemory / (1024.0 * 1024.0),
                MemoryLevel = estimatedTotalMemory switch
                {
                    < 1024 * 1024 => MemoryUsageLevel.Low,      // < 1MB
                    < 5 * 1024 * 1024 => MemoryUsageLevel.Medium,  // < 5MB
                    < 10 * 1024 * 1024 => MemoryUsageLevel.High,   // < 10MB
                    _ => MemoryUsageLevel.Critical                   // > 10MB
                }
            };
        }

        #region Private Helper Methods

        private static void RefreshCacheIfNeeded()
        {
            if (DateTime.Now - _lastCacheUpdate > CacheExpiry)
            {
                _settingsCache.Clear();
                _lastCacheUpdate = DateTime.Now;
            }
        }

        private static int CalculateOptimalColumnWidth(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= 20 => 50,
                <= 50 => 35,
                <= 100 => 25,
                <= 200 => 15,
                _ => 10
            };
        }

        private static int CalculateOptimalPointSize(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= 20 => 8,
                <= 50 => 6,
                <= 100 => 4,
                _ => 0
            };
        }

        private static int CalculateOptimalStrokeThickness(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= 50 => 3,
                <= 100 => 2,
                _ => 1
            };
        }

        private static double CalculateOptimalSmoothness(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= 20 => 0.7,
                <= 50 => 0.5,
                <= 100 => 0.3,
                _ => 0
            };
        }

        #endregion
    }

    /// <summary>
    /// Chart performance configuration data
    /// </summary>
    public class ChartPerformanceConfig
    {
        public int DataPointCount { get; set; }
        public bool EnableDataLabels { get; set; }
        public bool EnablePoints { get; set; }
        public bool EnableSmoothing { get; set; }
        public bool EnableAnimations { get; set; }
        public bool EnableTooltips { get; set; }
        public bool EnableStroke { get; set; }
        public bool ShouldSampleData { get; set; }
        public int MaxDisplayPoints { get; set; }
        public int OptimalColumnWidth { get; set; }
        public int OptimalPointSize { get; set; }
        public int OptimalStrokeThickness { get; set; }
        public double OptimalSmoothness { get; set; }
    }

    /// <summary>
    /// Pie chart specific performance configuration
    /// </summary>
    public class PieChartPerformanceConfig
    {
        public int SliceCount { get; set; }
        public bool EnableDataLabels { get; set; }
        public bool EnableDetailedLabels { get; set; }
        public bool EnableStroke { get; set; }
        public bool ShouldGroupSmallSlices { get; set; }
        public int MaxDisplaySlices { get; set; }
        public double MinSlicePercentage { get; set; }
        public int OptimalStrokeThickness { get; set; }
    }

    /// <summary>
    /// Chart memory usage estimate
    /// </summary>
    public class ChartMemoryEstimate
    {
        public long EstimatedMemoryBytes { get; set; }
        public double EstimatedMemoryMB { get; set; }
        public MemoryUsageLevel MemoryLevel { get; set; }
    }

    /// <summary>
    /// Memory usage levels
    /// </summary>
    public enum MemoryUsageLevel
    {
        Low,
        Medium,
        High,
        Critical
    }
}
