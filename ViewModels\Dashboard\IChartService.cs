using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface for the chart service that handles chart creation and visualization.
    /// </summary>
    public interface IChartService
    {
        /// <summary>
        /// Creates the main data series for a chart
        /// </summary>
        /// <param name="values">The values to display in the chart</param>
        /// <param name="dateSpan">The span of days covered by the data</param>
        /// <param name="parameter">The chart parameter (sales, profit, etc.)</param>
        /// <returns>The created chart series</returns>
        Series CreateMainSeries(List<decimal> values, double dateSpan, string parameter = "sales");
        
        /// <summary>
        /// Calculates a trend line for a set of values using linear regression
        /// </summary>
        /// <param name="values">The values to use for trend calculation</param>
        /// <returns>The created trend line series</returns>
        Series CalculateTrendLine(List<decimal> values);
        
        /// <summary>
        /// Calculates a moving average for a set of values
        /// </summary>
        /// <param name="values">The source values</param>
        /// <param name="dateSpan">The span of days covered by the data</param>
        /// <returns>The calculated moving average values</returns>
        List<decimal> CalculateMovingAverage(List<decimal> values, double dateSpan);
        
        /// <summary>
        /// Creates a chart series for the moving average
        /// </summary>
        /// <param name="movingAverages">The moving average values</param>
        /// <returns>The created moving average series</returns>
        LineSeries CreateMovingAverageSeries(List<decimal> movingAverages);
        
        /// <summary>
        /// Creates a comparison series showing previous period data
        /// </summary>
        /// <param name="startDate">The start date of the current period</param>
        /// <param name="endDate">The end date of the current period</param>
        /// <returns>The created comparison series</returns>
        Task<Series> CreateComparisonSeries(DateTime startDate, DateTime endDate);
    }
} 