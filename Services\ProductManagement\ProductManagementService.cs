using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ErrorHandling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.ProductManagement
{
    /// <summary>
    /// Specialized service for comprehensive product management operations in the POS system.
    /// Provides CRUD operations, search functionality, and business logic for product entities.
    /// </summary>
    /// <remarks>
    /// <para>This service was extracted from the monolithic DatabaseService to improve:</para>
    /// <list type="bullet">
    /// <item><description>Maintainability: Focused responsibility for product operations</description></item>
    /// <item><description>Testability: Isolated business logic for unit testing</description></item>
    /// <item><description>Performance: Optimized queries specific to product operations</description></item>
    /// <item><description>Error Handling: Consistent error handling patterns with detailed logging</description></item>
    /// </list>
    /// <para>All operations include automatic error handling, logging, and performance optimization.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Get all products with categories
    /// var products = await productService.GetAllProductsAsync();
    ///
    /// // Search products by name or SKU
    /// var searchResults = await productService.SearchProductsAsync("laptop");
    ///
    /// // Add new product with validation
    /// var productId = await productService.AddProductAsync(newProduct);
    /// </code>
    /// </example>
    public class ProductManagementService : IProductManagementService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<ProductManagementService> _logger;
        private readonly IErrorHandlingService _errorHandler;
        private readonly string _connectionString;

        /// <summary>
        /// Initializes a new instance of the ProductManagementService with required dependencies.
        /// </summary>
        /// <param name="context">Entity Framework database context for data access</param>
        /// <param name="logger">Optional logger for diagnostic and error information</param>
        /// <param name="errorHandler">Optional service for consistent error handling and user-friendly messages</param>
        /// <exception cref="ArgumentNullException">Thrown when context parameter is null</exception>
        public ProductManagementService(
            POSDbContext context,
            ILogger<ProductManagementService> logger = null,
            IErrorHandlingService errorHandler = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _errorHandler = errorHandler;
            _connectionString = context.Database.GetConnectionString();
        }

        /// <summary>
        /// Retrieves all active products from the database with their associated category information.
        /// </summary>
        /// <returns>
        /// A list of all products ordered by name, including category details.
        /// Returns an empty list if no products are found or if an error occurs.
        /// </returns>
        /// <remarks>
        /// <para>This method uses Entity Framework's AsNoTracking() for optimal read performance
        /// and includes the Category navigation property to avoid N+1 query problems.</para>
        /// <para>All database errors are handled gracefully with logging and return an empty list.</para>
        /// </remarks>
        /// <example>
        /// <code>
        /// var allProducts = await productService.GetAllProductsAsync();
        /// foreach (var product in allProducts)
        /// {
        ///     Console.WriteLine($"{product.Name} - {product.Category?.Name}");
        /// }
        /// </code>
        /// </example>
        public async Task<List<Product>> GetAllProductsAsync()
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .OrderBy(p => p.Name)
                    .ToListAsync();
            }, "Get All Products", new List<Product>());

            return result ?? new List<Product>();
        }

        /// <summary>
        /// Get all products with full details including barcodes and batches
        /// </summary>
        public async Task<List<Product>> GetAllProductsWithFullDetailsAsync()
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .OrderBy(p => p.Name)
                    .ToListAsync();
            }, "Get All Products With Full Details", new List<Product>());

            return result ?? new List<Product>();
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        public async Task<Product> GetProductByIdAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Product ID must be greater than 0", nameof(id));

                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .FirstOrDefaultAsync(p => p.Id == id);
            }, "Get Product By ID", null);

            return result;
        }

        /// <summary>
        /// Get product by barcode
        /// </summary>
        public async Task<Product> GetProductByBarcodeAsync(string barcode)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(barcode))
                    return null;

                // First try to find by primary barcode
                var product = await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .FirstOrDefaultAsync(p => p.Barcode == barcode);

                if (product != null)
                    return product;

                // Then try to find by additional barcodes
                var productBarcode = await _context.ProductBarcodes
                    .AsNoTracking()
                    .Include(pb => pb.Product)
                    .ThenInclude(p => p.Category)
                    .FirstOrDefaultAsync(pb => pb.Barcode == barcode);

                return productBarcode?.Product;
            }, "Get Product By Barcode", null);

            return result;
        }

        /// <summary>
        /// Add new product
        /// </summary>
        public async Task<int> AddProductAsync(Product product)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateProduct(product);

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully added product {ProductId} - {ProductName}", product.Id, product.Name);
                return product.Id;
            }, "Add Product", 0);

            return result;
        }

        /// <summary>
        /// Update existing product
        /// </summary>
        public async Task<bool> UpdateProductAsync(Product product)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateProduct(product);

                if (product.Id <= 0)
                    throw new ArgumentException("Product ID must be greater than 0 for updates", nameof(product));

                var existingProduct = await _context.Products.FindAsync(product.Id);
                if (existingProduct == null)
                    throw new InvalidOperationException($"Product with ID {product.Id} not found");

                // Update properties
                existingProduct.Name = product.Name;
                existingProduct.SKU = product.SKU;
                existingProduct.Description = product.Description;
                existingProduct.PurchasePrice = product.PurchasePrice;
                existingProduct.SellingPrice = product.SellingPrice;
                existingProduct.StockQuantity = product.StockQuantity;
                existingProduct.MinimumStock = product.MinimumStock;
                existingProduct.ReorderPoint = product.ReorderPoint;
                existingProduct.IsActive = product.IsActive;
                existingProduct.ExpiryDate = product.ExpiryDate;
                existingProduct.TrackBatches = product.TrackBatches;
                existingProduct.ImageData = product.ImageData;
                existingProduct.UnitOfMeasureId = product.UnitOfMeasureId;
                existingProduct.CategoryId = product.CategoryId;
                existingProduct.LoyaltyPoints = product.LoyaltyPoints;
                existingProduct.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated product {ProductId} - {ProductName}", product.Id, product.Name);
                return true;
            }, "Update Product", false);

            return result;
        }

        /// <summary>
        /// Delete product
        /// </summary>
        public async Task<bool> DeleteProductAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Product ID must be greater than 0", nameof(id));

                var product = await _context.Products.FindAsync(id);
                if (product == null)
                    throw new InvalidOperationException($"Product with ID {id} not found");

                // Check if product is used in any sales
                var hasTransactions = await _context.SaleItems.AnyAsync(si => si.ProductId == id);
                if (hasTransactions)
                {
                    // Soft delete - mark as inactive instead of hard delete
                    product.IsActive = false;
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Soft deleted product {ProductId} - {ProductName} (marked as inactive)", id, product.Name);
                }
                else
                {
                    // Hard delete if no transactions
                    _context.Products.Remove(product);
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Hard deleted product {ProductId} - {ProductName}", id, product.Name);
                }

                return true;
            }, "Delete Product", false);

            return result;
        }

        /// <summary>
        /// Update product stock quantity
        /// </summary>
        public async Task<bool> UpdateProductStockAsync(int productId, decimal quantity)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (productId <= 0)
                    throw new ArgumentException("Product ID must be greater than 0", nameof(productId));

                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                    throw new InvalidOperationException($"Product with ID {productId} not found");

                product.StockQuantity = quantity;
                product.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated stock for product {ProductId}, new quantity: {Quantity}", productId, quantity);
                return true;
            }, "Update Product Stock", false);

            return result;
        }

        /// <summary>
        /// Check if product exists by barcode
        /// </summary>
        public async Task<bool> ProductExistsAsync(string barcode)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(barcode))
                    return false;

                // Check primary barcode
                var existsByPrimary = await _context.Products.AnyAsync(p => p.Barcode == barcode);
                if (existsByPrimary)
                    return true;

                // Check additional barcodes
                return await _context.ProductBarcodes.AnyAsync(pb => pb.Barcode == barcode);
            }, "Check Product Exists", false);

            return result;
        }

        /// <summary>
        /// Search products by name, SKU, or barcode
        /// </summary>
        public async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return new List<Product>();

                var term = searchTerm.ToLower();

                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .Where(p => p.Name.ToLower().Contains(term) ||
                               p.SKU.ToLower().Contains(term) ||
                               p.Barcode.ToLower().Contains(term))
                    .OrderBy(p => p.Name)
                    .ToListAsync();
            }, "Search Products", new List<Product>());

            return result ?? new List<Product>();
        }

        /// <summary>
        /// Get low stock products
        /// </summary>
        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Batches) // ✅ PERFORMANCE FIX: Include batch data
                    .Where(p => p.IsActive && p.Type != ProductType.Service && p.StockQuantity <= p.ReorderPoint)
                    .OrderBy(p => p.StockQuantity)
                    .ToListAsync();
            }, "Get Low Stock Products", new List<Product>());

            return result ?? new List<Product>();
        }

        /// <summary>
        /// Get expiring products
        /// </summary>
        public async Task<List<Product>> GetExpiringProductsAsync(int daysThreshold = 30)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                var thresholdDate = DateTime.Now.AddDays(daysThreshold);

                return await _context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Where(p => p.IsActive &&
                               p.ExpiryDate.HasValue &&
                               p.ExpiryDate.Value <= thresholdDate)
                    .OrderBy(p => p.ExpiryDate)
                    .ToListAsync();
            }, "Get Expiring Products", new List<Product>());

            return result ?? new List<Product>();
        }

        /// <summary>
        /// Validate product data
        /// </summary>
        private void ValidateProduct(Product product)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product), "Product cannot be null");

            string itemType = product.Type == ProductType.Service ? "Service" : "Product";

            if (string.IsNullOrWhiteSpace(product.Name))
                throw new ArgumentException($"{itemType} name cannot be empty", nameof(product));

            if (product.SellingPrice < 0)
                throw new ArgumentException($"{itemType} selling price cannot be negative", nameof(product));

            if (product.PurchasePrice < 0)
                throw new ArgumentException($"{itemType} purchase price cannot be negative", nameof(product));

            // Stock quantity validation only applies to products, not services
            if (product.Type != ProductType.Service && product.StockQuantity < 0)
                throw new ArgumentException("Stock quantity cannot be negative", nameof(product));
        }
    }
}
