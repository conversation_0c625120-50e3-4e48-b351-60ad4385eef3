﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddCreatedAtToSales : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Products_Suppliers_SupplierId",
                table: "Products");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "Sales",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AlterColumn<int>(
                name: "ReorderPoint",
                table: "Products",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "LoyaltyPoints",
                table: "Products",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SaleId1",
                table: "Discounts",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SaleItemId1",
                table: "Discounts",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5847), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5818), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5848) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5853), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5852), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5854) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 182, DateTimeKind.Local).AddTicks(8170));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(6981));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(6994));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(7000));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6933));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6938));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6950));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6953));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6958));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6962));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6965));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(7008));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 181, DateTimeKind.Local).AddTicks(1392), new DateTime(2025, 6, 11, 21, 41, 43, 181, DateTimeKind.Local).AddTicks(1399) });

            migrationBuilder.CreateIndex(
                name: "IX_Discounts_SaleId1",
                table: "Discounts",
                column: "SaleId1");

            migrationBuilder.CreateIndex(
                name: "IX_Discounts_SaleItemId1",
                table: "Discounts",
                column: "SaleItemId1");

            migrationBuilder.CreateIndex(
                name: "IX_BatchStock_BatchNumber_ProductId",
                table: "BatchStock",
                columns: new[] { "BatchNumber", "ProductId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Discounts_SaleItems_SaleItemId1",
                table: "Discounts",
                column: "SaleItemId1",
                principalTable: "SaleItems",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Discounts_Sales_SaleId1",
                table: "Discounts",
                column: "SaleId1",
                principalTable: "Sales",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Products_Suppliers_SupplierId",
                table: "Products",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Discounts_SaleItems_SaleItemId1",
                table: "Discounts");

            migrationBuilder.DropForeignKey(
                name: "FK_Discounts_Sales_SaleId1",
                table: "Discounts");

            migrationBuilder.DropForeignKey(
                name: "FK_Products_Suppliers_SupplierId",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Discounts_SaleId1",
                table: "Discounts");

            migrationBuilder.DropIndex(
                name: "IX_Discounts_SaleItemId1",
                table: "Discounts");

            migrationBuilder.DropIndex(
                name: "IX_BatchStock_BatchNumber_ProductId",
                table: "BatchStock");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "Sales");

            migrationBuilder.DropColumn(
                name: "SaleId1",
                table: "Discounts");

            migrationBuilder.DropColumn(
                name: "SaleItemId1",
                table: "Discounts");

            migrationBuilder.AlterColumn<int>(
                name: "ReorderPoint",
                table: "Products",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<decimal>(
                name: "LoyaltyPoints",
                table: "Products",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5544), new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5499), new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5546) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5559), new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5555), new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(5560) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 694, DateTimeKind.Local).AddTicks(3989));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(8269));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(8275));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 688, DateTimeKind.Local).AddTicks(8295));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1249));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1257));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1274));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1280));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1288));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1294));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1301));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 10, 21, 37, 54, 699, DateTimeKind.Local).AddTicks(1387));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 10, 21, 37, 54, 689, DateTimeKind.Local).AddTicks(8832), new DateTime(2025, 6, 10, 21, 37, 54, 689, DateTimeKind.Local).AddTicks(8846) });

            migrationBuilder.AddForeignKey(
                name: "FK_Products_Suppliers_SupplierId",
                table: "Products",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
