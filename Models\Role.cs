using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Windows;

namespace POSSystem.Models
{
    public class Role
    {
        public Role()
        {
            Users = new HashSet<User>();
            DiscountPermissions = new HashSet<DiscountPermission>();
            IsActive = true;
            CreatedAt = DateTime.Now;
        }

        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Name { get; set; }
        
        [MaxLength(200)]
        public string Description { get; set; }
        
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual ICollection<User> Users { get; set; }
        public virtual ICollection<DiscountPermission> DiscountPermissions { get; set; }

        public string DisplayName => Name;
    }
} 