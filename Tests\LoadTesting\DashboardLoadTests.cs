using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Services;
using POSSystem.Services.QueryOptimization;
using POSSystem.Data;
using POSSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.LoadTesting
{
    /// <summary>
    /// ✅ NEW: Load testing for dashboard with large datasets
    /// Tests performance with realistic data volumes (1000+ products, 10000+ sales)
    /// </summary>
    [TestClass]
    public class DashboardLoadTests
    {
        private POSDbContext _context;
        private DatabaseService _databaseService;
        private DashboardQueryService _queryService;

        // Test data volumes
        private const int LARGE_PRODUCT_COUNT = 2000;
        private const int LARGE_SALES_COUNT = 15000;
        private const int LARGE_CUSTOMER_COUNT = 500;

        [TestInitialize]
        public void Setup()
        {
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _databaseService = new DatabaseService(_context);
            _queryService = new DashboardQueryService(_context);

            Debug.WriteLine("Setting up large dataset for load testing...");
            SeedLargeDataset();
            Debug.WriteLine($"Load test setup complete: {LARGE_PRODUCT_COUNT} products, {LARGE_SALES_COUNT} sales");
        }

        [TestCleanup]
        public void Cleanup()
        {
            _context?.Dispose();
        }

        /// <summary>
        /// ✅ LOAD TEST: Dashboard with 15,000 sales records
        /// </summary>
        [TestMethod]
        public async Task LoadTest_15000Sales_PerformanceAcceptable()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-365); // Full year of data
            var endDate = DateTime.Now;
            var stopwatch = new Stopwatch();

            // Act & Assert - Essential Metrics
            stopwatch.Start();
            var metrics = await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            stopwatch.Stop();
            
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000, 
                $"Essential metrics with 15K sales took {stopwatch.ElapsedMilliseconds}ms, expected < 1000ms");
            Assert.IsTrue(metrics.TotalSales > 0);
            
            Debug.WriteLine($"✅ Essential metrics (15K sales): {stopwatch.ElapsedMilliseconds}ms");

            // Act & Assert - Sales Trend
            stopwatch.Restart();
            var trendData = await _queryService.GetSalesTrendAsync(startDate, endDate);
            stopwatch.Stop();
            
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000, 
                $"Sales trend with 15K sales took {stopwatch.ElapsedMilliseconds}ms, expected < 2000ms");
            Assert.IsTrue(trendData.Count > 0);
            
            Debug.WriteLine($"✅ Sales trend (15K sales): {stopwatch.ElapsedMilliseconds}ms");

            // Act & Assert - Top Products
            stopwatch.Restart();
            var topProducts = await _queryService.GetTopProductsAsync(startDate, endDate, 50);
            stopwatch.Stop();
            
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1500, 
                $"Top products with 15K sales took {stopwatch.ElapsedMilliseconds}ms, expected < 1500ms");
            Assert.IsTrue(topProducts.Count > 0);
            
            Debug.WriteLine($"✅ Top products (15K sales): {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ LOAD TEST: Dashboard with 2,000 products
        /// </summary>
        [TestMethod]
        public async Task LoadTest_2000Products_PerformanceAcceptable()
        {
            // Arrange
            var stopwatch = new Stopwatch();

            // Act & Assert - Low Stock Products
            stopwatch.Start();
            var lowStockProducts = await _queryService.GetLowStockProductsAsync();
            stopwatch.Stop();
            
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 800, 
                $"Low stock check with 2K products took {stopwatch.ElapsedMilliseconds}ms, expected < 800ms");
            
            Debug.WriteLine($"✅ Low stock products (2K products): {stopwatch.ElapsedMilliseconds}ms, found {lowStockProducts.Count} low stock items");

            // Act & Assert - Product Performance Analysis
            var startDate = DateTime.Today.AddDays(-90);
            var endDate = DateTime.Now;
            
            stopwatch.Restart();
            var productPerformance = await _queryService.GetTopProductsAsync(startDate, endDate, 100);
            stopwatch.Stop();
            
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000, 
                $"Product performance analysis took {stopwatch.ElapsedMilliseconds}ms, expected < 2000ms");
            
            Debug.WriteLine($"✅ Product performance (2K products): {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ STRESS TEST: Concurrent dashboard operations
        /// </summary>
        [TestMethod]
        public async Task StressTest_ConcurrentDashboardOperations()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Now;
            var concurrentOperations = 10;
            var stopwatch = new Stopwatch();

            // Act
            stopwatch.Start();
            var tasks = new List<Task>();
            
            for (int i = 0; i < concurrentOperations; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    await _queryService.GetEssentialMetricsAsync(startDate, endDate);
                    await _queryService.GetSalesTrendAsync(startDate, endDate);
                    await _queryService.GetTopProductsAsync(startDate, endDate, 25);
                }));
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Assert
            var averageTimePerOperation = stopwatch.ElapsedMilliseconds / concurrentOperations;
            Assert.IsTrue(averageTimePerOperation < 3000, 
                $"Concurrent operations averaged {averageTimePerOperation}ms each, expected < 3000ms");
            
            Debug.WriteLine($"✅ Stress test: {concurrentOperations} concurrent operations completed in {stopwatch.ElapsedMilliseconds}ms (avg: {averageTimePerOperation}ms each)");
        }

        /// <summary>
        /// ✅ MEMORY TEST: Large dataset memory usage
        /// </summary>
        [TestMethod]
        public async Task MemoryTest_LargeDataset_MemoryUsageAcceptable()
        {
            // Arrange
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var initialMemory = GC.GetTotalMemory(false);
            var startDate = DateTime.Today.AddDays(-365);
            var endDate = DateTime.Now;

            // Act - Perform memory-intensive operations
            var metrics = await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            var trendData = await _queryService.GetSalesTrendAsync(startDate, endDate);
            var topProducts = await _queryService.GetTopProductsAsync(startDate, endDate, 100);
            var lowStockProducts = await _queryService.GetLowStockProductsAsync();
            var hourlyDistribution = await _queryService.GetHourlySalesDistributionAsync(startDate, endDate);

            var finalMemory = GC.GetTotalMemory(false);
            var memoryUsed = (finalMemory - initialMemory) / (1024 * 1024); // Convert to MB

            // Assert
            Assert.IsTrue(memoryUsed < 200, 
                $"Large dataset operations used {memoryUsed}MB, expected < 200MB");
            
            Debug.WriteLine($"✅ Memory test: Large dataset operations used {memoryUsed}MB");
        }

        /// <summary>
        /// ✅ SCALABILITY TEST: Performance with different data sizes
        /// </summary>
        [TestMethod]
        public async Task ScalabilityTest_PerformanceScalesLinearly()
        {
            // Test with different date ranges to simulate different data sizes
            var testCases = new[]
            {
                new { Days = 7, ExpectedMaxTime = 300 },
                new { Days = 30, ExpectedMaxTime = 600 },
                new { Days = 90, ExpectedMaxTime = 1000 },
                new { Days = 365, ExpectedMaxTime = 1500 }
            };

            var results = new List<ScalabilityResult>();

            foreach (var testCase in testCases)
            {
                var startDate = DateTime.Today.AddDays(-testCase.Days);
                var endDate = DateTime.Now;
                
                var stopwatch = Stopwatch.StartNew();
                var metrics = await _queryService.GetEssentialMetricsAsync(startDate, endDate);
                stopwatch.Stop();

                var result = new ScalabilityResult
                {
                    DataSize = testCase.Days,
                    ActualTime = stopwatch.ElapsedMilliseconds,
                    ExpectedMaxTime = testCase.ExpectedMaxTime,
                    RecordCount = (int)metrics.TransactionCount
                };

                results.Add(result);

                Assert.IsTrue(result.ActualTime <= result.ExpectedMaxTime, 
                    $"{testCase.Days} days of data took {result.ActualTime}ms, expected <= {result.ExpectedMaxTime}ms");
                
                Debug.WriteLine($"✅ {testCase.Days} days ({result.RecordCount} records): {result.ActualTime}ms");
            }

            // Check that performance scales reasonably
            var performanceRatio = (double)results.Last().ActualTime / results.First().ActualTime;
            var dataRatio = (double)results.Last().RecordCount / results.First().RecordCount;
            
            Assert.IsTrue(performanceRatio < dataRatio * 2, 
                $"Performance degradation ({performanceRatio:F2}x) is too high compared to data increase ({dataRatio:F2}x)");
            
            Debug.WriteLine($"✅ Scalability: Performance ratio {performanceRatio:F2}x for data ratio {dataRatio:F2}x");
        }

        /// <summary>
        /// ✅ ENDURANCE TEST: Repeated operations over time
        /// </summary>
        [TestMethod]
        public async Task EnduranceTest_RepeatedOperations_NoPerformanceDegradation()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Now;
            var iterations = 50;
            var times = new List<long>();

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await _queryService.GetEssentialMetricsAsync(startDate, endDate);
                stopwatch.Stop();
                times.Add(stopwatch.ElapsedMilliseconds);

                // Small delay to simulate real usage
                await Task.Delay(10);
            }

            // Assert
            var firstHalf = times.Take(iterations / 2).Average();
            var secondHalf = times.Skip(iterations / 2).Average();
            var degradationPercent = ((secondHalf - firstHalf) / firstHalf) * 100;

            Assert.IsTrue(degradationPercent < 20, 
                $"Performance degraded by {degradationPercent:F1}% over {iterations} iterations");
            
            var averageTime = times.Average();
            var maxTime = times.Max();
            var minTime = times.Min();
            
            Debug.WriteLine($"✅ Endurance test: {iterations} iterations, avg: {averageTime:F1}ms, min: {minTime}ms, max: {maxTime}ms, degradation: {degradationPercent:F1}%");
        }

        /// <summary>
        /// ✅ HELPER: Seed large realistic dataset
        /// </summary>
        private void SeedLargeDataset()
        {
            var random = new Random(42); // Fixed seed for consistent tests
            var categories = new[] { "Electronics", "Clothing", "Food", "Books", "Home", "Sports", "Beauty", "Toys" };
            var customerNames = GenerateCustomerNames();

            // Add categories
            for (int i = 0; i < categories.Length; i++)
            {
                _context.Categories.Add(new Category
                {
                    Id = i + 1,
                    Name = categories[i],
                    Description = $"{categories[i]} category"
                });
            }

            // Add customers
            for (int i = 1; i <= LARGE_CUSTOMER_COUNT; i++)
            {
                _context.Customers.Add(new Customer
                {
                    Id = i,
                    Name = customerNames[i % customerNames.Length],
                    Email = $"customer{i}@test.com",
                    Phone = $"555-{i:D4}",
                    Address = $"{i} Test Street"
                });
            }

            // Add products
            for (int i = 1; i <= LARGE_PRODUCT_COUNT; i++)
            {
                var categoryId = (i % categories.Length) + 1;
                var basePrice = 10 + (i % 100);
                
                _context.Products.Add(new Product
                {
                    Id = i,
                    Name = $"Product {i}",
                    SKU = $"SKU{i:D4}",
                    Description = $"Description for product {i}",
                    CategoryId = categoryId,
                    SellingPrice = basePrice,
                    PurchasePrice = basePrice * 0.6m,
                    StockQuantity = random.Next(0, 200),
                    ReorderPoint = 10 + (i % 20),
                    IsActive = true,
                    ExpiryDate = i % 10 == 0 ? DateTime.Today.AddDays(random.Next(1, 365)) : null
                });
            }

            // Add sales with realistic patterns
            for (int i = 1; i <= LARGE_SALES_COUNT; i++)
            {
                var daysAgo = random.Next(0, 365);
                var saleDate = DateTime.Today.AddDays(-daysAgo);
                
                // More sales on weekdays and recent dates
                var isWeekend = saleDate.DayOfWeek == DayOfWeek.Saturday || saleDate.DayOfWeek == DayOfWeek.Sunday;
                var isRecent = daysAgo < 30;
                
                if (isWeekend && random.Next(0, 3) == 0) continue; // Skip some weekend sales
                if (!isRecent && random.Next(0, 2) == 0) continue; // Skip some older sales

                var customerId = random.Next(1, LARGE_CUSTOMER_COUNT + 1);
                var itemCount = random.Next(1, 6);
                var subtotal = 0m;

                var sale = new Sale
                {
                    Id = i,
                    SaleDate = saleDate,
                    CustomerId = customerId,
                    Status = "Completed",
                    PaymentStatus = random.Next(0, 20) == 0 ? "Unpaid" : "Paid", // 5% unpaid
                    Items = new List<SaleItem>()
                };

                // Add sale items
                for (int j = 0; j < itemCount; j++)
                {
                    var productId = random.Next(1, LARGE_PRODUCT_COUNT + 1);
                    var quantity = random.Next(1, 4);
                    var unitPrice = 10 + (productId % 100);
                    var total = quantity * unitPrice;

                    sale.Items.Add(new SaleItem
                    {
                        ProductId = productId,
                        Quantity = quantity,
                        UnitPrice = unitPrice,
                        Total = total
                    });

                    subtotal += total;
                }

                sale.Subtotal = subtotal;
                sale.DiscountAmount = subtotal > 100 ? subtotal * 0.05m : 0; // 5% discount on large orders
                sale.GrandTotal = subtotal - sale.DiscountAmount;

                _context.Sales.Add(sale);
            }

            _context.SaveChanges();
        }

        /// <summary>
        /// ✅ HELPER: Generate realistic customer names
        /// </summary>
        private string[] GenerateCustomerNames()
        {
            return new[]
            {
                "John Smith", "Jane Doe", "Mike Johnson", "Sarah Wilson", "David Brown",
                "Lisa Davis", "Chris Miller", "Amy Taylor", "Tom Anderson", "Emily White",
                "Mark Thompson", "Jessica Garcia", "Ryan Martinez", "Ashley Rodriguez",
                "Kevin Lewis", "Michelle Lee", "Brian Walker", "Nicole Hall", "Jason Allen",
                "Stephanie Young", "Daniel King", "Rachel Wright", "Matthew Lopez",
                "Lauren Hill", "Andrew Scott", "Megan Green", "Joshua Adams", "Kimberly Baker"
            };
        }
    }

    /// <summary>
    /// ✅ HELPER: Scalability test result
    /// </summary>
    public class ScalabilityResult
    {
        public int DataSize { get; set; }
        public long ActualTime { get; set; }
        public long ExpectedMaxTime { get; set; }
        public int RecordCount { get; set; }
    }
}
