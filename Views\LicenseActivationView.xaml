<Window x:Class="POSSystem.Views.LicenseActivationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="License Activation" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="POS System License Activation" 
                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,20"/>
            
            <TextBlock Text="Please follow these steps to activate your software:"
                     TextWrapping="Wrap"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,20"/>
        </StackPanel>

        <StackPanel Grid.Row="1" Margin="0,10">
            <!-- Step 1: System ID -->
            <GroupBox Header="Step 1: Generate System ID" 
                      Style="{StaticResource MaterialDesignGroupBox}"
                      Margin="0,0,0,20">
                <StackPanel Margin="0,10">
                    <TextBox Text="{Binding SystemId, Mode=OneWay}"
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="Your System ID"
                             Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="Generate System ID"
                                Command="{Binding GenerateSystemIdCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"/>
                        
                        <Button Content="Copy System ID"
                                Command="{Binding CopySystemIdCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- Step 2: License Activation -->
            <GroupBox Header="Step 2: Enter License Information" 
                      Style="{StaticResource MaterialDesignGroupBox}">
                <StackPanel Margin="0,10">
                    <TextBox Text="{Binding BusinessName, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="Business Name"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding LicenseKey, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="License Key"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <TextBlock Text="{Binding ErrorMessage}"
                      Foreground="Red"
                      TextWrapping="Wrap"
                      Margin="0,10"
                      Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </StackPanel>

        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            <Button Content="Activate"
                    Command="{Binding ActivateCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Margin="0,0,10,0"/>
            
            <Button Content="Exit"
                    Command="{Binding ExitCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"/>
        </StackPanel>
    </Grid>
</Window> 