-- <PERSON><PERSON>t to create external ProductsDB database for testing
-- This database serves as an external product catalog for importing products

-- Create Products table
CREATE TABLE IF NOT EXISTS Products (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    Brand TEXT,
    Categories TEXT,
    PurchasePrice DECIMAL(10,2) DEFAULT 0.00,
    SellingPrice DECIMAL(10,2) DEFAULT 0.00,
    ImageData TEXT,
    IsActive INTEGER DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create ProductBarcodes table
CREATE TABLE IF NOT EXISTS ProductBarcodes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    Barcode TEXT NOT NULL,
    IsPrimary INTEGER DEFAULT 0,
    Description TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ProductId) REFERENCES Products(Id),
    UNIQUE(Barcode)
);

-- Insert sample products for testing
INSERT INTO Products (Name, Description, Brand, Categories, PurchasePrice, SellingPrice, IsActive) VALUES
('Coca-Cola 500ml', 'Refreshing cola drink in 500ml bottle', 'Coca-Cola', 'Beverages', 0.75, 1.50, 1),
('Pepsi 500ml', 'Cola soft drink in 500ml bottle', 'PepsiCo', 'Beverages', 0.70, 1.45, 1),
('Lay''s Classic Chips', 'Original flavor potato chips', 'Lay''s', 'Snacks', 1.20, 2.99, 1),
('Oreo Cookies', 'Chocolate sandwich cookies', 'Nabisco', 'Snacks', 2.50, 4.99, 1),
('Milk 1L', 'Fresh whole milk 1 liter', 'Local Dairy', 'Dairy', 1.80, 3.49, 1),
('Bread Loaf', 'White sandwich bread', 'Wonder Bread', 'Bakery', 1.00, 2.29, 1),
('Bananas 1kg', 'Fresh bananas per kilogram', 'Fresh Produce', 'Fruits', 1.50, 2.99, 1),
('Apples 1kg', 'Red delicious apples per kilogram', 'Fresh Produce', 'Fruits', 2.00, 3.99, 1),
('Chicken Breast 1kg', 'Fresh chicken breast meat', 'Poultry Farm', 'Meat', 8.00, 12.99, 1),
('Ground Beef 500g', 'Fresh ground beef 500 grams', 'Butcher Shop', 'Meat', 6.00, 9.99, 1);

-- Insert sample barcodes for the products
INSERT INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description) VALUES
(1, '1234567890123', 1, 'Coca-Cola 500ml primary barcode'),
(2, '1234567890124', 1, 'Pepsi 500ml primary barcode'),
(3, '1234567890125', 1, 'Lay''s Classic Chips primary barcode'),
(4, '1234567890126', 1, 'Oreo Cookies primary barcode'),
(5, '1234567890127', 1, 'Milk 1L primary barcode'),
(6, '1234567890128', 1, 'Bread Loaf primary barcode'),
(7, '1234567890129', 1, 'Bananas 1kg primary barcode'),
(8, '1234567890130', 1, 'Apples 1kg primary barcode'),
(9, '1234567890131', 1, 'Chicken Breast 1kg primary barcode'),
(10, '1234567890132', 1, 'Ground Beef 500g primary barcode');

-- Add some additional barcodes for testing multiple barcodes per product
INSERT INTO ProductBarcodes (ProductId, Barcode, IsPrimary, Description) VALUES
(1, '9876543210123', 0, 'Coca-Cola 500ml alternative barcode'),
(2, '9876543210124', 0, 'Pepsi 500ml alternative barcode'),
(3, '9876543210125', 0, 'Lay''s Classic Chips alternative barcode');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_name ON Products(Name);
CREATE INDEX IF NOT EXISTS idx_products_active ON Products(IsActive);
CREATE INDEX IF NOT EXISTS idx_barcodes_barcode ON ProductBarcodes(Barcode);
CREATE INDEX IF NOT EXISTS idx_barcodes_product ON ProductBarcodes(ProductId);
