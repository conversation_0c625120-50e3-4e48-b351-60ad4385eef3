using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <summary>
    /// Migration to add critical database indexes for dashboard performance optimization
    /// These indexes will significantly improve dashboard loading times by optimizing
    /// the most frequently used queries for sales data, product statistics, and analytics.
    /// </summary>
    public partial class AddDashboardPerformanceIndexes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // ✅ CRITICAL: Sales table indexes for dashboard performance
            migrationBuilder.CreateIndex(
                name: "IX_Sales_SaleDate",
                table: "Sales",
                column: "SaleDate");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_SaleDate_Status",
                table: "Sales",
                columns: new[] { "SaleDate", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_Sales_SaleDate_GrandTotal",
                table: "Sales",
                columns: new[] { "SaleDate", "GrandTotal" });

            migrationBuilder.CreateIndex(
                name: "IX_Sales_PaymentStatus_SaleDate",
                table: "Sales",
                columns: new[] { "PaymentStatus", "SaleDate" });

            migrationBuilder.CreateIndex(
                name: "IX_Sales_CustomerId_SaleDate",
                table: "Sales",
                columns: new[] { "CustomerId", "SaleDate" });

            migrationBuilder.CreateIndex(
                name: "IX_Sales_UserId_SaleDate",
                table: "Sales",
                columns: new[] { "UserId", "SaleDate" });

            // ✅ CRITICAL: Product table indexes for inventory and dashboard queries
            migrationBuilder.CreateIndex(
                name: "IX_Products_IsActive_StockQuantity",
                table: "Products",
                columns: new[] { "IsActive", "StockQuantity" });

            // ✅ CRITICAL: SaleItems table indexes for product performance analysis
            migrationBuilder.CreateIndex(
                name: "IX_SaleItems_ProductId_SaleId",
                table: "SaleItems",
                columns: new[] { "ProductId", "SaleId" });

            // ✅ NEW: InventoryTransactions table indexes for stock tracking
            migrationBuilder.CreateIndex(
                name: "IX_InventoryTransactions_ProductId_Date",
                table: "InventoryTransactions",
                columns: new[] { "ProductId", "TransactionDate" });

            // ✅ PERFORMANCE: Add covering indexes for common dashboard queries
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS IX_Sales_Dashboard_Coverage 
                ON Sales (SaleDate, Status, PaymentStatus) 
                INCLUDE (GrandTotal, Subtotal, DiscountAmount, CustomerId, UserId);
            ");

            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS IX_Products_Dashboard_Coverage 
                ON Products (IsActive, CategoryId) 
                INCLUDE (Name, StockQuantity, SellingPrice, PurchasePrice);
            ");

            // ✅ ANALYTICS: Indexes for business intelligence queries
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS IX_Sales_Analytics_Hourly 
                ON Sales (strftime('%Y-%m-%d %H', SaleDate), Status) 
                INCLUDE (GrandTotal);
            ");

            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS IX_Sales_Analytics_Daily 
                ON Sales (date(SaleDate), Status) 
                INCLUDE (GrandTotal, CustomerId);
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop all the indexes created in Up method
            migrationBuilder.DropIndex(
                name: "IX_Sales_SaleDate",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Sales_SaleDate_Status",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Sales_SaleDate_GrandTotal",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Sales_PaymentStatus_SaleDate",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Sales_CustomerId_SaleDate",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Sales_UserId_SaleDate",
                table: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_Products_IsActive_StockQuantity",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_SaleItems_ProductId_SaleId",
                table: "SaleItems");

            migrationBuilder.DropIndex(
                name: "IX_InventoryTransactions_ProductId_Date",
                table: "InventoryTransactions");

            // Drop custom SQL indexes
            migrationBuilder.Sql("DROP INDEX IF EXISTS IX_Sales_Dashboard_Coverage;");
            migrationBuilder.Sql("DROP INDEX IF EXISTS IX_Products_Dashboard_Coverage;");
            migrationBuilder.Sql("DROP INDEX IF EXISTS IX_Sales_Analytics_Hourly;");
            migrationBuilder.Sql("DROP INDEX IF EXISTS IX_Sales_Analytics_Daily;");
        }
    }
}
