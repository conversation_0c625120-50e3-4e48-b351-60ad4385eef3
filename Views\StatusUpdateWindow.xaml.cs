using System.Windows;
using System.Windows.Controls;

namespace POSSystem.Views
{
    public partial class StatusUpdateWindow : Window
    {
        public string SelectedStatus { get; private set; }

        public StatusUpdateWindow(string currentStatus)
        {
            InitializeComponent();
            
            // Set the current status in the ComboBox
            foreach (ComboBoxItem item in cboStatus.Items)
            {
                if (item.Content.ToString() == currentStatus)
                {
                    cboStatus.SelectedItem = item;
                    break;
                }
            }
        }

        private void Update_Click(object sender, RoutedEventArgs e)
        {
            if (cboStatus.SelectedItem == null)
            {
                MessageBox.Show("Please select a status.", "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            SelectedStatus = ((ComboBoxItem)cboStatus.SelectedItem).Content.ToString();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
} 