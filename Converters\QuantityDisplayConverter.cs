using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.Models;

namespace POSSystem.Converters
{
    /// <summary>
    /// Converter to format quantity display based on whether the product is weight-based or unit-based
    /// </summary>
    public class QuantityDisplayConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CartItem cartItem)
            {
                // ✅ PERFORMANCE FIX: Removed debug logging that was called on every UI update
                if (cartItem.Product?.IsWeightBased == true)
                {
                    // For weight-based products, show up to 3 decimal places with proper formatting
                    return cartItem.Quantity.ToString("N3");
                }
                else
                {
                    // For unit-based products, show as whole number
                    return cartItem.Quantity.ToString("F0");
                }
            }
            
            if (value is decimal quantity)
            {
                // If we only have quantity without product context, show with decimals
                return quantity.ToString("N3");
            }
            
            return value?.ToString() ?? "0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (decimal.TryParse(value?.ToString(), out decimal result))
            {
                return result;
            }
            return 0m;
        }
    }

    /// <summary>
    /// Converter to format quantity with unit of measure for display
    /// </summary>
    public class QuantityWithUnitConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CartItem cartItem)
            {
                string quantityStr;
                if (cartItem.Product?.IsWeightBased == true)
                {
                    // For weight-based products, show up to 3 decimal places with proper formatting
                    quantityStr = cartItem.Quantity.ToString("N3");
                }
                else
                {
                    // For unit-based products, show as whole number
                    quantityStr = cartItem.Quantity.ToString("F0");
                }

                // Add unit of measure if available
                string unit = cartItem.Product?.UnitOfMeasure?.Abbreviation;
                if (!string.IsNullOrEmpty(unit))
                {
                    return $"{quantityStr} {unit}";
                }
                
                return quantityStr;
            }
            
            return value?.ToString() ?? "0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
