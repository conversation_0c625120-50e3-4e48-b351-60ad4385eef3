using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    public class CashDrawer : INotifyPropertyChanged
    {
        private int _id;
        private decimal _openingBalance;
        private decimal _closingBalance;
        private decimal _currentBalance;
        private decimal _expectedBalance;
        private decimal _actualBalance;
        private decimal _difference;
        private bool _isActive;
        private DateTime _openedAt;
        private DateTime? _closedAt;
        private User _openedBy;
        private User _closedBy;
        private string _status;
        private string? _notes;
        private ObservableCollection<CashTransaction> _transactions;
        private int? _openedById;
        private int? _closedById;

        public CashDrawer()
        {
            Transactions = new ObservableCollection<CashTransaction>();
            Status = "Closed";
            OpenedAt = DateTime.Now;
        }

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public decimal OpeningBalance
        {
            get => _openingBalance;
            set
            {
                _openingBalance = value;
                OnPropertyChanged();
                CalculateBalances();
            }
        }

        public decimal ClosingBalance
        {
            get => _closingBalance;
            set
            {
                _closingBalance = value;
                OnPropertyChanged();
            }
        }

        public decimal CurrentBalance
        {
            get => _currentBalance;
            private set
            {
                _currentBalance = value;
                OnPropertyChanged();
            }
        }

        public decimal ExpectedBalance
        {
            get
            {
                decimal transactionTotal = 0;
                foreach (var transaction in Transactions)
                {
                    if (transaction.Type == "Sale" || transaction.Type == "In")
                        transactionTotal += transaction.Amount;
                    else if (transaction.Type == "Out")
                        transactionTotal -= transaction.Amount;
                }
                return OpeningBalance + transactionTotal;
            }
        }

        public decimal ActualBalance
        {
            get => _actualBalance;
            set
            {
                _actualBalance = value;
                OnPropertyChanged();
                CalculateDifference();
            }
        }

        public decimal Difference
        {
            get => _difference;
            private set
            {
                _difference = value;
                OnPropertyChanged();
            }
        }

        public DateTime OpenedAt
        {
            get => _openedAt;
            set
            {
                _openedAt = value;
                OnPropertyChanged();
            }
        }

        public DateTime? ClosedAt
        {
            get => _closedAt;
            set
            {
                _closedAt = value;
                OnPropertyChanged();
            }
        }

        [ForeignKey("OpenedBy")]
        public int? OpenedById
        {
            get => _openedById;
            set
            {
                _openedById = value;
                OnPropertyChanged();
            }
        }

        [ForeignKey("ClosedBy")]
        public int? ClosedById
        {
            get => _closedById;
            set
            {
                _closedById = value;
                OnPropertyChanged();
            }
        }

        public virtual User OpenedBy
        {
            get => _openedBy;
            set
            {
                _openedBy = value;
                OpenedById = value?.Id;
                OnPropertyChanged();
            }
        }

        public virtual User ClosedBy
        {
            get => _closedBy;
            set
            {
                _closedBy = value;
                ClosedById = value?.Id;
                OnPropertyChanged();
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public string? Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged();
            }
        }

        public virtual ObservableCollection<CashTransaction> Transactions
        {
            get => _transactions;
            set
            {
                _transactions = value;
                OnPropertyChanged();
            }
        }

        public void AddTransaction(CashTransaction transaction)
        {
            Transactions.Add(transaction);
            CalculateBalances();
        }

        public void OpenDrawer(User user, decimal openingBalance)
        {
            OpenedBy = user;
            OpeningBalance = openingBalance;
            OpenedAt = DateTime.Now;
            Status = "Open";
            CalculateBalances();
        }

        public void CloseDrawer(User user, decimal actualBalance, string notes = "")
        {
            ClosedBy = user;
            ClosedAt = DateTime.Now;
            ActualBalance = actualBalance;
            Status = "Closed";
            Notes = notes;
            CalculateBalances();
        }

        private void CalculateBalances()
        {
            decimal transactionTotal = 0;
            foreach (var transaction in Transactions)
            {
                if (transaction.Type == "In")
                    transactionTotal += transaction.Amount;
                else if (transaction.Type == "Out")
                    transactionTotal -= transaction.Amount;
            }

            CurrentBalance = OpeningBalance + transactionTotal;
        }

        private void CalculateDifference()
        {
            Difference = ActualBalance - ExpectedBalance;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 