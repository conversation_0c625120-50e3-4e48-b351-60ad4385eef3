using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for creating and managing draft invoices
    /// </summary>
    public class DraftInvoiceDto
    {
        public DraftInvoiceDto()
        {
            Items = new List<DraftInvoiceItemDto>();
            Type = "Sales";
            IssueDate = DateTime.Now;
            DueDate = DateTime.Now.AddDays(30);
            PaymentTerms = "Net 30";
        }

        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; }

        [Required]
        [MaxLength(20)]
        public string Type { get; set; }

        [Required]
        public DateTime IssueDate { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        public int? CustomerId { get; set; }
        public string CustomerName { get; set; }

        public int? SupplierId { get; set; }
        public string SupplierName { get; set; }

        public decimal Subtotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal GrandTotal { get; set; }

        [MaxLength(100)]
        public string PaymentTerms { get; set; }

        [MaxLength(100)]
        public string Reference { get; set; }

        public string Notes { get; set; }

        // Draft-specific properties
        [Required]
        public int CreatedByUserId { get; set; }
        public string CreatedByUserName { get; set; }

        public int? CompletedByUserId { get; set; }
        public string CompletedByUserName { get; set; }

        [Required]
        public DateTime DraftCreatedAt { get; set; }

        public DateTime? AdminCompletedAt { get; set; }

        public bool RequiresAdminCompletion { get; set; }

        public string Status { get; set; }

        // Items collection
        public List<DraftInvoiceItemDto> Items { get; set; }

        // Computed properties
        public int ItemCount => Items?.Count ?? 0;
        public int DaysPending => RequiresAdminCompletion ? (DateTime.Now - DraftCreatedAt).Days : 0;
        public bool IsOverdue => DaysPending > 7;
        public string StatusDisplay => RequiresAdminCompletion ? "Pending Admin Completion" : Status;

        // Helper methods
        public void CalculateTotals()
        {
            if (Items != null && Items.Count > 0)
            {
                Subtotal = 0;
                foreach (var item in Items)
                {
                    item.CalculateTotal();
                    Subtotal += item.Total;
                }
                GrandTotal = Subtotal - DiscountAmount + TaxAmount;
            }
            else
            {
                Subtotal = 0;
                GrandTotal = 0;
            }
        }

        public bool IsValid(out List<string> errors)
        {
            errors = new List<string>();

            if (string.IsNullOrWhiteSpace(InvoiceNumber))
                errors.Add("Invoice number is required.");

            if (string.IsNullOrWhiteSpace(Type))
                errors.Add("Invoice type is required.");

            if (CreatedByUserId <= 0)
                errors.Add("Created by user is required.");

            if (Items == null || Items.Count == 0)
                errors.Add("At least one item is required.");

            if (Items != null)
            {
                for (int i = 0; i < Items.Count; i++)
                {
                    if (!Items[i].IsValid(out string itemError))
                        errors.Add($"Item {i + 1}: {itemError}");
                }
            }

            return errors.Count == 0;
        }
    }

    /// <summary>
    /// Data Transfer Object for draft invoice items
    /// </summary>
    public class DraftInvoiceItemDto
    {
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; }

        [Required]
        [Range(0.001, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
        public decimal Quantity { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Unit price cannot be negative")]
        public decimal UnitPrice { get; set; }

        public decimal Total { get; set; }

        [MaxLength(50)]
        public string BatchNumber { get; set; }

        // Product information for display
        public string ProductSku { get; set; }
        public string ProductDescription { get; set; }
        public string ProductCategory { get; set; }
        public bool IsWeightBased { get; set; }
        public string Unit { get; set; }

        // Computed properties
        public string QuantityDisplay => IsWeightBased ? 
            $"{Quantity:0.###} {Unit ?? "kg"}" : 
            $"{Quantity:0.##}";

        public string TotalDisplay => Total.ToString("C");
        public string UnitPriceDisplay => UnitPrice.ToString("C");

        // Helper methods
        public void CalculateTotal()
        {
            Total = Quantity * UnitPrice;
        }

        public bool IsValid(out string errorMessage)
        {
            errorMessage = string.Empty;

            if (ProductId <= 0)
            {
                errorMessage = "Product is required.";
                return false;
            }

            if (string.IsNullOrWhiteSpace(ProductName))
            {
                errorMessage = "Product name is required.";
                return false;
            }

            if (Quantity <= 0)
            {
                errorMessage = "Quantity must be greater than 0.";
                return false;
            }

            if (UnitPrice < 0)
            {
                errorMessage = "Unit price cannot be negative.";
                return false;
            }

            return true;
        }

        public static DraftInvoiceItemDto FromProduct(Product product, decimal quantity)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            var dto = new DraftInvoiceItemDto
            {
                ProductId = product.Id,
                ProductName = product.Name,
                ProductSku = product.SKU,
                ProductDescription = product.Description,
                ProductCategory = product.Category?.Name,
                Quantity = quantity,
                UnitPrice = product.SellingPrice,
                IsWeightBased = product.IsWeightBased,
                Unit = product.Unit
            };

            dto.CalculateTotal();
            return dto;
        }
    }

    /// <summary>
    /// Result object for draft invoice operations
    /// </summary>
    public class DraftInvoiceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int? InvoiceId { get; set; }
        public List<string> Errors { get; set; } = new List<string>();

        public static DraftInvoiceResult CreateSuccess(int invoiceId, string message = "Draft invoice created successfully")
        {
            return new DraftInvoiceResult
            {
                Success = true,
                InvoiceId = invoiceId,
                Message = message
            };
        }

        public static DraftInvoiceResult CreateError(string message, List<string> errors = null)
        {
            return new DraftInvoiceResult
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>()
            };
        }
    }
}
