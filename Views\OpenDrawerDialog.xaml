<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.OpenDrawerDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="300">
    <StackPanel Margin="16">
        <TextBlock Text="{DynamicResource OpenDrawer}"
                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                 Margin="0,0,0,16"/>

        <TextBox x:Name="OpeningBalanceTextBox"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                materialDesign:HintAssist.Hint="{DynamicResource OpeningBalance}"
                Margin="0,0,0,16"/>

        <StackPanel Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="{x:Null}"
                    Content="{DynamicResource Cancel}"
                    Margin="0,0,8,0"/>
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    Click="OpenDrawer_Click"
                    Content="{DynamicResource Open}"/>
        </StackPanel>
    </StackPanel>
</UserControl> 