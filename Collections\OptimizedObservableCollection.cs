using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.Collections
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: High-performance ObservableCollection with batching, virtualization support, and optimized notifications
    /// </summary>
    public class OptimizedObservableCollection<T> : ObservableCollection<T>
    {
        private bool _suppressNotifications;
        private bool _batchUpdateInProgress;
        private readonly List<NotifyCollectionChangedEventArgs> _batchedEvents = new List<NotifyCollectionChangedEventArgs>();
        private readonly DispatcherTimer _batchTimer;
        private readonly object _lockObject = new object();
        
        // Performance tracking
        private int _totalOperations;
        private int _batchedOperations;
        private readonly Stopwatch _performanceStopwatch = new Stopwatch();

        public OptimizedObservableCollection() : base()
        {
            // Timer for delayed batch notifications
            _batchTimer = new DispatcherTimer(DispatcherPriority.DataBind)
            {
                Interval = TimeSpan.FromMilliseconds(50) // 50ms delay for batching
            };
            _batchTimer.Tick += BatchTimer_Tick;
        }

        public OptimizedObservableCollection(IEnumerable<T> collection) : base()
        {
            _batchTimer = new DispatcherTimer(DispatcherPriority.DataBind)
            {
                Interval = TimeSpan.FromMilliseconds(50)
            };
            _batchTimer.Tick += BatchTimer_Tick;

            AddRange(collection);
        }

        /// <summary>
        /// ✅ CRITICAL: Add multiple items efficiently with batched notifications
        /// </summary>
        public void AddRange(IEnumerable<T> items)
        {
            if (items == null) return;

            var itemList = items.ToList();
            if (itemList.Count == 0) return;

            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                foreach (var item in itemList)
                {
                    Items.Add(item);
                }

                // Fire a single reset notification for better performance
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;
            _batchedOperations += itemList.Count;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] AddRange: {itemList.Count} items added in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Remove multiple items efficiently with batched notifications
        /// </summary>
        public void RemoveRange(IEnumerable<T> items)
        {
            if (items == null) return;

            var itemList = items.ToList();
            if (itemList.Count == 0) return;

            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                foreach (var item in itemList)
                {
                    Items.Remove(item);
                }

                // Fire a single reset notification for better performance
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] RemoveRange: {itemList.Count} items removed in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Replace all items efficiently with minimal notifications
        /// </summary>
        public void ReplaceAll(IEnumerable<T> newItems)
        {
            if (newItems == null) return;

            var newItemList = newItems.ToList();
            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                Items.Clear();
                foreach (var item in newItemList)
                {
                    Items.Add(item);
                }

                // Fire a single reset notification
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] ReplaceAll: {newItemList.Count} items replaced in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Update items in place without triggering collection change notifications
        /// </summary>
        public void UpdateItemsInPlace(Func<T, T> updateFunction)
        {
            if (updateFunction == null) return;

            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                for (int i = 0; i < Items.Count; i++)
                {
                    Items[i] = updateFunction(Items[i]);
                }

                // Fire property change notifications for indexer
                OnPropertyChanged(new PropertyChangedEventArgs("Item[]"));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] UpdateItemsInPlace: {Items.Count} items updated in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Begin batch update mode to suppress notifications
        /// </summary>
        public IDisposable BeginBatchUpdate()
        {
            return new BatchUpdateScope(this);
        }

        /// <summary>
        /// ✅ CRITICAL: Sort collection in place with minimal notifications
        /// </summary>
        public void Sort(IComparer<T> comparer = null)
        {
            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                var sortedItems = comparer != null 
                    ? Items.OrderBy(x => x, comparer).ToList()
                    : Items.OrderBy(x => x).ToList();

                Items.Clear();
                foreach (var item in sortedItems)
                {
                    Items.Add(item);
                }

                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] Sort: {Items.Count} items sorted in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Filter collection in place
        /// </summary>
        public void Filter(Func<T, bool> predicate)
        {
            if (predicate == null) return;

            _performanceStopwatch.Restart();

            using (new BatchUpdateScope(this))
            {
                var filteredItems = Items.Where(predicate).ToList();
                
                Items.Clear();
                foreach (var item in filteredItems)
                {
                    Items.Add(item);
                }

                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
            }

            _performanceStopwatch.Stop();
            _totalOperations++;

            Debug.WriteLine($"[OPTIMIZED-COLLECTION] Filter: {Items.Count} items after filtering in {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ MONITORING: Get performance statistics
        /// </summary>
        public CollectionPerformanceStats GetPerformanceStats()
        {
            return new CollectionPerformanceStats
            {
                TotalOperations = _totalOperations,
                BatchedOperations = _batchedOperations,
                CurrentItemCount = Count,
                IsBatchUpdateActive = _batchUpdateInProgress,
                SuppressNotifications = _suppressNotifications
            };
        }

        /// <summary>
        /// ✅ INTERNAL: Override to support notification suppression
        /// </summary>
        protected override void OnCollectionChanged(NotifyCollectionChangedEventArgs e)
        {
            if (_suppressNotifications) return;

            if (_batchUpdateInProgress)
            {
                lock (_lockObject)
                {
                    _batchedEvents.Add(e);
                }
                
                // Start or restart the batch timer
                _batchTimer.Stop();
                _batchTimer.Start();
                return;
            }

            // Ensure we're on the UI thread
            if (Application.Current?.Dispatcher != null && !Application.Current.Dispatcher.CheckAccess())
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(() => base.OnCollectionChanged(e)), DispatcherPriority.DataBind);
            }
            else
            {
                base.OnCollectionChanged(e);
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Override to support notification suppression
        /// </summary>
        protected override void OnPropertyChanged(PropertyChangedEventArgs e)
        {
            if (_suppressNotifications) return;

            // Ensure we're on the UI thread
            if (Application.Current?.Dispatcher != null && !Application.Current.Dispatcher.CheckAccess())
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(() => base.OnPropertyChanged(e)), DispatcherPriority.DataBind);
            }
            else
            {
                base.OnPropertyChanged(e);
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Handle batch timer tick
        /// </summary>
        private void BatchTimer_Tick(object sender, EventArgs e)
        {
            _batchTimer.Stop();

            List<NotifyCollectionChangedEventArgs> eventsToFire;
            lock (_lockObject)
            {
                eventsToFire = new List<NotifyCollectionChangedEventArgs>(_batchedEvents);
                _batchedEvents.Clear();
            }

            // Fire batched events
            if (eventsToFire.Count > 0)
            {
                // For multiple events, just fire a reset for better performance
                if (eventsToFire.Count > 1)
                {
                    base.OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
                }
                else
                {
                    base.OnCollectionChanged(eventsToFire[0]);
                }
            }
        }

        /// <summary>
        /// Batch update scope for automatic notification management
        /// </summary>
        private class BatchUpdateScope : IDisposable
        {
            private readonly OptimizedObservableCollection<T> _collection;
            private readonly bool _wasAlreadyInBatch;

            public BatchUpdateScope(OptimizedObservableCollection<T> collection)
            {
                _collection = collection;
                _wasAlreadyInBatch = _collection._batchUpdateInProgress;
                
                if (!_wasAlreadyInBatch)
                {
                    _collection._batchUpdateInProgress = true;
                }
            }

            public void Dispose()
            {
                if (!_wasAlreadyInBatch)
                {
                    _collection._batchUpdateInProgress = false;
                    
                    // Fire any pending batched events
                    if (_collection._batchedEvents.Count > 0)
                    {
                        _collection._batchTimer.Stop();
                        _collection.BatchTimer_Tick(null, null);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Performance statistics for optimized collections
    /// </summary>
    public class CollectionPerformanceStats
    {
        public int TotalOperations { get; set; }
        public int BatchedOperations { get; set; }
        public int CurrentItemCount { get; set; }
        public bool IsBatchUpdateActive { get; set; }
        public bool SuppressNotifications { get; set; }
    }
}
