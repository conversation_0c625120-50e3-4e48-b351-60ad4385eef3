using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Threading;
using POSSystem.Services.UI;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: Advanced UI optimizer that applies comprehensive performance improvements to WPF controls
    /// </summary>
    public static class AdvancedUIOptimizer
    {
        private static UIRenderingPerformanceMonitor _performanceMonitor;
        private static readonly Dictionary<Type, Action<FrameworkElement>> _optimizationStrategies;

        static AdvancedUIOptimizer()
        {
            _optimizationStrategies = new Dictionary<Type, Action<FrameworkElement>>
            {
                { typeof(DataGrid), OptimizeDataGrid },
                { typeof(ListView), OptimizeListView },
                { typeof(ListBox), OptimizeListBox },
                { typeof(ItemsControl), OptimizeItemsControl },
                { typeof(TreeView), OptimizeTreeView },
                { typeof(ComboBox), OptimizeComboBox },
                { typeof(TextBox), OptimizeTextBox },
                { typeof(ScrollViewer), OptimizeScrollViewer }
            };
        }

        /// <summary>
        /// ✅ CRITICAL: Initialize the UI optimizer with performance monitoring
        /// </summary>
        public static void Initialize(UIRenderingPerformanceMonitor performanceMonitor)
        {
            _performanceMonitor = performanceMonitor;
            Debug.WriteLine("✅ [ADVANCED-UI-OPTIMIZER] Advanced UI Optimizer initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Apply comprehensive optimizations to a UI element and all its children
        /// </summary>
        public static void OptimizeUIElement(FrameworkElement element)
        {
            if (element == null) return;

            using (_performanceMonitor?.TrackUIOperation($"OptimizeUIElement_{element.GetType().Name}", "Optimization"))
            {
                try
                {
                    // Apply specific optimizations based on element type
                    ApplySpecificOptimizations(element);

                    // Apply general optimizations
                    ApplyGeneralOptimizations(element);

                    // Apply virtualization optimizations
                    ApplyVirtualizationOptimizations(element);

                    // Apply rendering optimizations
                    ApplyRenderingOptimizations(element);

                    // Track element for performance monitoring
                    _performanceMonitor?.TrackElementRendering(element);

                    // Recursively optimize children
                    OptimizeChildren(element);

                    Debug.WriteLine($"[ADVANCED-UI-OPTIMIZER] ✅ Optimized {element.GetType().Name}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [ADVANCED-UI-OPTIMIZER] Error optimizing {element.GetType().Name}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Apply optimizations to an entire window
        /// </summary>
        public static void OptimizeWindow(Window window)
        {
            if (window == null) return;

            using (_performanceMonitor?.TrackUIOperation($"OptimizeWindow_{window.GetType().Name}", "WindowOptimization"))
            {
                try
                {
                    // Window-level optimizations
                    window.UseLayoutRounding = true;
                    window.SnapsToDevicePixels = true;
                    
                    // Optimize rendering
                    RenderOptions.SetBitmapScalingMode(window, BitmapScalingMode.HighQuality);
                    RenderOptions.SetClearTypeHint(window, ClearTypeHint.Enabled);

                    // Apply optimizations to window content
                    if (window.Content is FrameworkElement content)
                    {
                        OptimizeUIElement(content);
                    }

                    Debug.WriteLine($"[ADVANCED-UI-OPTIMIZER] ✅ Optimized window {window.GetType().Name}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [ADVANCED-UI-OPTIMIZER] Error optimizing window: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Apply specific optimizations based on element type
        /// </summary>
        private static void ApplySpecificOptimizations(FrameworkElement element)
        {
            var elementType = element.GetType();
            
            // Find the most specific optimization strategy
            var strategy = _optimizationStrategies
                .Where(kvp => kvp.Key.IsAssignableFrom(elementType))
                .OrderByDescending(kvp => GetTypeHierarchyDepth(kvp.Key, elementType))
                .FirstOrDefault();

            strategy.Value?.Invoke(element);
        }

        /// <summary>
        /// ✅ INTERNAL: Get type hierarchy depth for strategy selection
        /// </summary>
        private static int GetTypeHierarchyDepth(Type baseType, Type derivedType)
        {
            int depth = 0;
            var currentType = derivedType;
            
            while (currentType != null && currentType != baseType)
            {
                depth++;
                currentType = currentType.BaseType;
            }
            
            return currentType == baseType ? depth : -1;
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize DataGrid controls
        /// </summary>
        private static void OptimizeDataGrid(FrameworkElement element)
        {
            if (element is DataGrid dataGrid)
            {
                // Enable virtualization
                dataGrid.EnableRowVirtualization = true;
                dataGrid.EnableColumnVirtualization = true;
                VirtualizingPanel.SetVirtualizationMode(dataGrid, VirtualizationMode.Recycling);
                VirtualizingPanel.SetIsVirtualizing(dataGrid, true);
                VirtualizingPanel.SetIsVirtualizingWhenGrouping(dataGrid, true);
                
                // Optimize scrolling
                VirtualizingPanel.SetCacheLength(dataGrid, new VirtualizationCacheLength(5, 5));
                VirtualizingPanel.SetCacheLengthUnit(dataGrid, VirtualizationCacheLengthUnit.Page);
                VirtualizingPanel.SetScrollUnit(dataGrid, ScrollUnit.Pixel);
                
                // Optimize rendering
                dataGrid.UseLayoutRounding = true;
                dataGrid.SnapsToDevicePixels = true;
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ DataGrid optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize ListView controls
        /// </summary>
        private static void OptimizeListView(FrameworkElement element)
        {
            if (element is ListView listView)
            {
                // Enable virtualization
                VirtualizingPanel.SetIsVirtualizing(listView, true);
                VirtualizingPanel.SetVirtualizationMode(listView, VirtualizationMode.Recycling);
                VirtualizingPanel.SetCacheLength(listView, new VirtualizationCacheLength(3, 3));
                VirtualizingPanel.SetCacheLengthUnit(listView, VirtualizationCacheLengthUnit.Page);
                VirtualizingPanel.SetScrollUnit(listView, ScrollUnit.Pixel);
                
                // Optimize scrolling
                ScrollViewer.SetCanContentScroll(listView, true);
                ScrollViewer.SetIsDeferredScrollingEnabled(listView, true);
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ ListView optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize ListBox controls
        /// </summary>
        private static void OptimizeListBox(FrameworkElement element)
        {
            if (element is ListBox listBox)
            {
                // Enable virtualization
                VirtualizingPanel.SetIsVirtualizing(listBox, true);
                VirtualizingPanel.SetVirtualizationMode(listBox, VirtualizationMode.Recycling);
                VirtualizingPanel.SetCacheLength(listBox, new VirtualizationCacheLength(2, 2));
                VirtualizingPanel.SetCacheLengthUnit(listBox, VirtualizationCacheLengthUnit.Page);
                
                // Optimize scrolling
                ScrollViewer.SetCanContentScroll(listBox, true);
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ ListBox optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize ItemsControl
        /// </summary>
        private static void OptimizeItemsControl(FrameworkElement element)
        {
            if (element is ItemsControl itemsControl && !(element is Selector))
            {
                // Enable virtualization if possible
                VirtualizingPanel.SetIsVirtualizing(itemsControl, true);
                VirtualizingPanel.SetVirtualizationMode(itemsControl, VirtualizationMode.Recycling);
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ ItemsControl optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize TreeView controls
        /// </summary>
        private static void OptimizeTreeView(FrameworkElement element)
        {
            if (element is TreeView treeView)
            {
                // Enable virtualization
                VirtualizingPanel.SetIsVirtualizing(treeView, true);
                VirtualizingPanel.SetVirtualizationMode(treeView, VirtualizationMode.Recycling);
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ TreeView optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize ComboBox controls
        /// </summary>
        private static void OptimizeComboBox(FrameworkElement element)
        {
            if (element is ComboBox comboBox)
            {
                // Enable virtualization for dropdown
                VirtualizingPanel.SetIsVirtualizing(comboBox, true);
                VirtualizingPanel.SetVirtualizationMode(comboBox, VirtualizationMode.Recycling);
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ ComboBox optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize TextBox controls
        /// </summary>
        private static void OptimizeTextBox(FrameworkElement element)
        {
            if (element is TextBox textBox)
            {
                // Optimize text rendering
                textBox.UseLayoutRounding = true;
                textBox.SnapsToDevicePixels = true;
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ TextBox optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize ScrollViewer controls
        /// </summary>
        private static void OptimizeScrollViewer(FrameworkElement element)
        {
            if (element is ScrollViewer scrollViewer)
            {
                // Optimize scrolling performance
                scrollViewer.CanContentScroll = true;
                scrollViewer.IsDeferredScrollingEnabled = true;
                
                Debug.WriteLine("[ADVANCED-UI-OPTIMIZER] ✅ ScrollViewer optimized");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Apply general optimizations to any FrameworkElement
        /// </summary>
        private static void ApplyGeneralOptimizations(FrameworkElement element)
        {
            // Layout optimizations
            element.UseLayoutRounding = true;
            element.SnapsToDevicePixels = true;
            
            // Binding optimizations
            OptimizeDataBindings(element);
        }

        /// <summary>
        /// ✅ CRITICAL: Apply virtualization optimizations
        /// </summary>
        private static void ApplyVirtualizationOptimizations(FrameworkElement element)
        {
            // Apply virtualization to panels
            if (element is Panel panel)
            {
                VirtualizingPanel.SetIsVirtualizing(panel, true);
                VirtualizingPanel.SetVirtualizationMode(panel, VirtualizationMode.Recycling);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Apply rendering optimizations
        /// </summary>
        private static void ApplyRenderingOptimizations(FrameworkElement element)
        {
            // Rendering optimizations
            RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
            RenderOptions.SetClearTypeHint(element, ClearTypeHint.Enabled);
            RenderOptions.SetEdgeMode(element, EdgeMode.Aliased);
            
            // Apply bitmap caching for complex elements (but not for frequently changing ones)
            if (ShouldApplyBitmapCache(element))
            {
                element.CacheMode = new BitmapCache
                {
                    EnableClearType = true,
                    SnapsToDevicePixels = true,
                    RenderAtScale = 1.0
                };
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Determine if bitmap caching should be applied
        /// </summary>
        private static bool ShouldApplyBitmapCache(FrameworkElement element)
        {
            // Don't cache frequently changing elements
            return !(element is TextBox) && 
                   !(element is ComboBox) && 
                   !(element is Slider) &&
                   !(element is ProgressBar);
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize data bindings for better performance
        /// </summary>
        private static void OptimizeDataBindings(FrameworkElement element)
        {
            try
            {
                var bindingExpressions = BindingOperations.GetSourceUpdatingBindings(element);
                foreach (BindingExpressionBase bindingExpression in bindingExpressions)
                {
                    if (bindingExpression is BindingExpression binding)
                    {
                        // Optimize binding if possible
                        // Note: This is a placeholder for potential binding optimizations
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [ADVANCED-UI-OPTIMIZER] Error optimizing bindings: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Recursively optimize child elements
        /// </summary>
        private static void OptimizeChildren(FrameworkElement element)
        {
            try
            {
                var childCount = VisualTreeHelper.GetChildrenCount(element);
                for (int i = 0; i < childCount; i++)
                {
                    if (VisualTreeHelper.GetChild(element, i) is FrameworkElement child)
                    {
                        OptimizeUIElement(child);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [ADVANCED-UI-OPTIMIZER] Error optimizing children: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Apply optimizations to a specific control type
        /// </summary>
        public static void OptimizeControlType<T>(T control) where T : FrameworkElement
        {
            if (control == null) return;

            using (_performanceMonitor?.TrackUIOperation($"OptimizeControlType_{typeof(T).Name}", "TypeOptimization"))
            {
                OptimizeUIElement(control);
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get optimization statistics
        /// </summary>
        public static string GetOptimizationReport()
        {
            if (_performanceMonitor == null)
                return "Performance monitor not initialized";

            return _performanceMonitor.GetDetailedRenderingReport();
        }
    }
}
