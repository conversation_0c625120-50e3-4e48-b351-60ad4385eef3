using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using POSSystem.Models;

namespace POSSystem.Views
{
    public partial class CartSelectionDialog : Window
    {
        public Cart SelectedCart { get; private set; }
        public ObservableCollection<Cart> HeldCarts { get; }

        public CartSelectionDialog(List<Cart> heldCarts)
        {
            InitializeComponent();
            HeldCarts = new ObservableCollection<Cart>(heldCarts);
            DataContext = this;
        }

        private void CartList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var listView = sender as ListView;
            if (listView?.SelectedItem is Cart selectedCart)
            {
                SelectedCart = selectedCart;
            }
        }

        private void RecallCart_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedCart != null)
            {
                DialogResult = true;
            }
            else
            {
                string message = (string)Application.Current.Resources["DialogSelectCart"];
                string title = (string)Application.Current.Resources["SelectCartToRecall"];
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
} 