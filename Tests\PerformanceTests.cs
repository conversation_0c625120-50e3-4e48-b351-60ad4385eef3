using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Performance;
using POSSystem.Helpers;
using POSSystem.Models;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Tests
{
    /// <summary>
    /// ✅ PERFORMANCE VALIDATION: Tests to validate critical performance improvements
    /// </summary>
    public static class PerformanceTests
    {
        /// <summary>
        /// ✅ CRITICAL TEST: Validate database async operations don't block UI thread
        /// </summary>
        public static async Task<bool> TestDatabaseAsyncOperations()
        {
            try
            {
                Debug.WriteLine("=== TESTING DATABASE ASYNC OPERATIONS ===");
                
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as IDatabaseService;
                if (dbService == null)
                {
                    Debug.WriteLine("❌ DatabaseService not available");
                    return false;
                }

                var asyncDbService = App.ServiceProvider?.GetService(typeof(AsyncDatabaseService)) as AsyncDatabaseService;
                if (asyncDbService == null)
                {
                    Debug.WriteLine("❌ AsyncDatabaseService not available");
                    return false;
                }

                // Test 1: Product by barcode async
                var stopwatch = Stopwatch.StartNew();
                var product = await asyncDbService.GetProductByBarcodeAsync("TEST123");
                stopwatch.Stop();
                
                Debug.WriteLine($"✅ GetProductByBarcodeAsync completed in {stopwatch.ElapsedMilliseconds}ms");
                
                if (stopwatch.ElapsedMilliseconds > 2000)
                {
                    Debug.WriteLine($"⚠️ Slow operation: GetProductByBarcodeAsync took {stopwatch.ElapsedMilliseconds}ms");
                }

                // Test 2: Product by ID async
                stopwatch.Restart();
                var productById = await asyncDbService.GetProductByIdAsync(1);
                stopwatch.Stop();
                
                Debug.WriteLine($"✅ GetProductByIdAsync completed in {stopwatch.ElapsedMilliseconds}ms");

                // Test 3: UI thread check
                bool isOnUIThread = UIThreadProtection.IsOnUIThread();
                Debug.WriteLine($"✅ UI Thread Check: {UIThreadProtection.GetThreadInfo()}");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Database async test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ CRITICAL TEST: Validate UI thread protection works correctly
        /// </summary>
        public static async Task<bool> TestUIThreadProtection()
        {
            try
            {
                Debug.WriteLine("=== TESTING UI THREAD PROTECTION ===");

                // Test 1: Background operation execution
                var result = await UIThreadProtection.ExecuteOnBackgroundAsync(async () =>
                {
                    Debug.WriteLine($"Background operation running on: {UIThreadProtection.GetThreadInfo()}");
                    await Task.Delay(100); // Simulate work
                    return "Background operation completed";
                }, "Test Background Operation");

                Debug.WriteLine($"✅ Background operation result: {result}");

                // Test 2: UI update from background
                await UIThreadProtection.UpdateUIAsync(() =>
                {
                    Debug.WriteLine($"UI update running on: {UIThreadProtection.GetThreadInfo()}");
                });

                Debug.WriteLine("✅ UI update completed successfully");

                // Test 3: Batch UI update
                await UIThreadProtection.BatchUpdateUIAsync(() =>
                {
                    Debug.WriteLine($"Batch UI update running on: {UIThreadProtection.GetThreadInfo()}");
                }, "Test Batch Update");

                Debug.WriteLine("✅ Batch UI update completed successfully");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ UI thread protection test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ CRITICAL TEST: Validate product loading performance
        /// </summary>
        public static async Task<bool> TestProductLoadingPerformance()
        {
            try
            {
                Debug.WriteLine("=== TESTING PRODUCT LOADING PERFORMANCE ===");

                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as IDatabaseService;
                if (dbService == null)
                {
                    Debug.WriteLine("❌ DatabaseService not available");
                    return false;
                }

                // Test 1: Paged product loading
                var stopwatch = Stopwatch.StartNew();
                var products = await dbService.GetProductsAsync(20, 0);
                stopwatch.Stop();

                Debug.WriteLine($"✅ Paged product loading: {products.Count} products in {stopwatch.ElapsedMilliseconds}ms");

                if (stopwatch.ElapsedMilliseconds > 1000)
                {
                    Debug.WriteLine($"⚠️ Slow product loading: {stopwatch.ElapsedMilliseconds}ms for {products.Count} products");
                }

                // Test 2: Memory usage check
                var beforeMemory = GC.GetTotalMemory(false);
                
                // Load products multiple times to test memory usage
                for (int i = 0; i < 5; i++)
                {
                    var testProducts = await dbService.GetProductsAsync(10, i * 10);
                    testProducts = null; // Release reference
                }

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var afterMemory = GC.GetTotalMemory(false);
                var memoryDiff = afterMemory - beforeMemory;

                Debug.WriteLine($"✅ Memory usage test: {memoryDiff / 1024}KB difference after loading products");

                if (memoryDiff > 10 * 1024 * 1024) // 10MB
                {
                    Debug.WriteLine($"⚠️ High memory usage: {memoryDiff / 1024 / 1024}MB increase");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Product loading performance test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ CRITICAL TEST: Run all performance tests
        /// </summary>
        public static async Task<bool> RunAllPerformanceTests()
        {
            try
            {
                Debug.WriteLine("🚀 STARTING COMPREHENSIVE PERFORMANCE TESTS");
                var overallStopwatch = Stopwatch.StartNew();

                var results = new List<bool>();

                // Test 1: Database async operations
                results.Add(await TestDatabaseAsyncOperations());

                // Test 2: UI thread protection
                results.Add(await TestUIThreadProtection());

                // Test 3: Product loading performance
                results.Add(await TestProductLoadingPerformance());

                overallStopwatch.Stop();

                var passedTests = results.Count(r => r);
                var totalTests = results.Count;

                Debug.WriteLine($"📊 PERFORMANCE TEST RESULTS:");
                Debug.WriteLine($"   Passed: {passedTests}/{totalTests}");
                Debug.WriteLine($"   Total Time: {overallStopwatch.ElapsedMilliseconds}ms");

                if (passedTests == totalTests)
                {
                    Debug.WriteLine("✅ ALL PERFORMANCE TESTS PASSED");
                    return true;
                }
                else
                {
                    Debug.WriteLine($"❌ {totalTests - passedTests} PERFORMANCE TESTS FAILED");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Performance test suite failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ UTILITY: Quick performance validation for startup
        /// </summary>
        public static async Task<bool> QuickPerformanceValidation()
        {
            try
            {
                Debug.WriteLine("⚡ QUICK PERFORMANCE VALIDATION");

                // Quick UI thread check
                var isUIThread = UIThreadProtection.IsOnUIThread();
                Debug.WriteLine($"UI Thread: {isUIThread}");

                // Quick database availability check
                var dbService = App.ServiceProvider?.GetService<IDatabaseService>();
                var asyncDbService = App.ServiceProvider?.GetService<AsyncDatabaseService>();

                Debug.WriteLine($"DatabaseService: {(dbService != null ? "✅" : "❌")}");
                Debug.WriteLine($"AsyncDatabaseService: {(asyncDbService != null ? "✅" : "❌")}");

                // Test async method availability
                if (dbService != null)
                {
                    try
                    {
                        // Test that the new async methods are available
                        var hasAsyncMethods = dbService.GetType().GetMethod("GetProductByIdAsync") != null;
                        Debug.WriteLine($"Async Methods Available: {(hasAsyncMethods ? "✅" : "❌")}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Method check error: {ex.Message}");
                    }
                }

                return dbService != null && asyncDbService != null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Quick validation failed: {ex.Message}");
                return false;
            }
        }
    }
}
