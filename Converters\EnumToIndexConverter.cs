using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.ViewModels;

namespace POSSystem.Converters
{
    public class EnumToIndexConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ChartType chartType)
            {
                return (int)chartType;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int index)
            {
                return (ChartType)index;
            }
            return ChartType.Line;
        }
    }
} 