﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class MakeBusinessExpenseNotesNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "BusinessExpenses",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1886), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1850), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1887) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1897), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1894), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1898) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 464, DateTimeKind.Local).AddTicks(6687));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3644));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3650));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3655));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1439));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1445));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1458));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1464));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1470));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1475));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1480));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1486));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(8236), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(8247) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "BusinessExpenses",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6378), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6343), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6379) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6387), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6385), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6388) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 62, DateTimeKind.Local).AddTicks(1066));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7728));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7733));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7737));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6960));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6965));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6974));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6978));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6985));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6989));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6994));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(7001));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 60, DateTimeKind.Local).AddTicks(1650), new DateTime(2025, 2, 5, 15, 19, 56, 60, DateTimeKind.Local).AddTicks(1655) });
        }
    }
}
