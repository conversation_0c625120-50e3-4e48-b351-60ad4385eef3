using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Services.Migration;
using Microsoft.Extensions.Logging;

namespace POSSystem.Views.Dialogs
{
    public partial class DatabaseMigrationDialog : UserControl
    {
        private readonly DatabaseMigrationService _migrationService;
        private bool _migrationInProgress = false;

        public DatabaseMigrationDialog()
        {
            InitializeComponent();
            _migrationService = new DatabaseMigrationService();
            Loaded += OnLoaded;
        }

        private async void OnLoaded(object sender, RoutedEventArgs e)
        {
            await CheckDatabaseStatus();
        }

        private async Task CheckDatabaseStatus()
        {
            try
            {
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var legacyDbPath = Path.Combine(baseDirectory, "pos2.db");
                var currentDbPath = Path.Combine(baseDirectory, "pos.db");

                // Check legacy database
                if (File.Exists(legacyDbPath))
                {
                    txtLegacyStatus.Text = "✅ Found";
                    txtLegacyStatus.Foreground = System.Windows.Media.Brushes.Green;
                    
                    var legacyProductCount = await GetProductCount(legacyDbPath);
                    txtLegacyProductCount.Text = $"{legacyProductCount} products found";
                }
                else
                {
                    txtLegacyStatus.Text = "❌ Not found";
                    txtLegacyStatus.Foreground = System.Windows.Media.Brushes.Red;
                    txtLegacyProductCount.Text = "Database file missing";
                    btnStartMigration.IsEnabled = false;
                }

                // Check current database
                if (File.Exists(currentDbPath))
                {
                    txtCurrentStatus.Text = "✅ Found";
                    txtCurrentStatus.Foreground = System.Windows.Media.Brushes.Green;
                    
                    var currentProductCount = await GetProductCount(currentDbPath);
                    txtCurrentProductCount.Text = $"{currentProductCount} products currently";
                }
                else
                {
                    txtCurrentStatus.Text = "❌ Not found";
                    txtCurrentStatus.Foreground = System.Windows.Media.Brushes.Red;
                    txtCurrentProductCount.Text = "Database file missing";
                    btnStartMigration.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                txtLegacyStatus.Text = "❌ Error checking databases";
                txtLegacyStatus.Foreground = System.Windows.Media.Brushes.Red;
                txtLegacyProductCount.Text = ex.Message;
                btnStartMigration.IsEnabled = false;
            }
        }

        private async Task<int> GetProductCount(string dbPath)
        {
            try
            {
                using var connection = new System.Data.SQLite.SQLiteConnection($"Data Source={dbPath};Version=3;");
                await connection.OpenAsync();
                
                var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM Products WHERE IsActive = 1 OR IsActive IS NULL";
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
            catch
            {
                return 0;
            }
        }

        private async void StartMigration_Click(object sender, RoutedEventArgs e)
        {
            if (_migrationInProgress) return;

            var result = MessageBox.Show(
                "This will migrate product data from pos2.db to pos.db.\n\n" +
                "⚠️ IMPORTANT: A backup will be created, but please ensure you have your own backup before proceeding.\n\n" +
                "Do you want to continue?",
                "Confirm Migration",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            _migrationInProgress = true;
            btnStartMigration.IsEnabled = false;
            btnClose.IsEnabled = false;
            progressCard.Visibility = Visibility.Visible;

            try
            {
                await PerformMigration();
            }
            finally
            {
                _migrationInProgress = false;
                btnStartMigration.IsEnabled = true;
                btnClose.IsEnabled = true;
            }
        }

        private async Task PerformMigration()
        {
            var progress = new Progress<string>(message =>
            {
                Dispatcher.Invoke(() =>
                {
                    txtProgressStatus.Text = message;
                    txtProgressLog.Text += $"[{DateTime.Now:HH:mm:ss}] {message}\n";
                    scrollProgress.ScrollToEnd();
                });
            });

            try
            {
                ((IProgress<string>)progress).Report("Starting database migration...");
                progressBar.IsIndeterminate = true;

                var migrationResult = await _migrationService.MigrateProductDataAsync();

                progressBar.IsIndeterminate = false;
                progressBar.Value = 100;

                if (migrationResult.IsSuccess)
                {
                    ((IProgress<string>)progress).Report("✅ Migration completed successfully!");
                    
                    var summary = migrationResult.GetSummary();
                    ((IProgress<string>)progress).Report("\n" + summary);

                    MessageBox.Show(
                        $"Migration completed successfully!\n\n" +
                        $"Products migrated: {migrationResult.MigratedProductCount}\n" +
                        $"Categories migrated: {migrationResult.MigratedCategoryCount}\n" +
                        $"Products skipped: {migrationResult.SkippedProductCount}\n\n" +
                        $"Backup created at:\n{migrationResult.BackupPath}",
                        "Migration Successful",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    // Refresh database status
                    await CheckDatabaseStatus();
                }
                else
                {
                    ((IProgress<string>)progress).Report("❌ Migration failed!");
                    ((IProgress<string>)progress).Report($"Error: {migrationResult.Message}");

                    if (migrationResult.Errors.Count > 0)
                    {
                        foreach (var error in migrationResult.Errors)
                        {
                            ((IProgress<string>)progress).Report($"  - {error}");
                        }
                    }

                    MessageBox.Show(
                        $"Migration failed!\n\n{migrationResult.Message}\n\n" +
                        "Please check the log for details.",
                        "Migration Failed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                ((IProgress<string>)progress).Report($"❌ Unexpected error: {ex.Message}");
                
                MessageBox.Show(
                    $"An unexpected error occurred during migration:\n\n{ex.Message}",
                    "Migration Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (_migrationInProgress)
            {
                var result = MessageBox.Show(
                    "Migration is in progress. Are you sure you want to close?",
                    "Confirm Close",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;
            }

            DialogHost.Close("MainDialog");
        }
    }
}
