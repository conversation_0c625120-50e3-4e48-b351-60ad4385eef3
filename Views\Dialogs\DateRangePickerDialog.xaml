<Window x:Class="POSSystem.Views.Dialogs.DateRangePickerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
        mc:Ignorable="d"
        Title="Select Date Range" 
        Height="220" 
        Width="400"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style TargetType="DatePicker">
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
        </Style>
        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="0,10,0,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        <Style TargetType="Button">
            <Setter Property="Width" Value="80"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Margin" Value="10,0"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Start Date -->
        <TextBlock Grid.Row="0" Text="Start Date:"/>
        <DatePicker Grid.Row="1" x:Name="StartDatePicker" 
                   SelectedDateChanged="StartDatePicker_SelectedDateChanged"/>
        
        <!-- End Date -->
        <TextBlock Grid.Row="2" Text="End Date:"/>
        <DatePicker Grid.Row="3" x:Name="EndDatePicker"/>
        
        <!-- Button Panel -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="Apply" Click="ApplyButton_Click" IsDefault="True"/>
            <Button Content="Cancel" Click="CancelButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window> 