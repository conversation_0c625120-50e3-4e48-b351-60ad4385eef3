using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// Service to initialize and verify the two-tier invoice system database setup
    /// </summary>
    public class TwoTierInvoiceSystemInitializer
    {
        private readonly DatabaseService _dbService;
        private readonly string _connectionString;

        public TwoTierInvoiceSystemInitializer(DatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _connectionString = _dbService.GetConnectionString();
        }

        /// <summary>
        /// Initializes the two-tier invoice system by running the migration script
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Starting two-tier invoice system initialization...");

                // Check if migration is needed
                if (await IsMigrationNeededAsync())
                {
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Migration needed, executing migration script...");
                    await ExecuteMigrationScriptAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Migration not needed, tables already exist.");
                }

                // Verify the setup
                var isValid = await VerifySetupAsync();
                if (isValid)
                {
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Two-tier invoice system initialized successfully!");
                    await EnsureDefaultSettingsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Setup verification failed!");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error during initialization: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if the migration is needed by verifying if the new tables exist
        /// </summary>
        private async Task<bool> IsMigrationNeededAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Check if DraftInvoiceNotifications table exists
                using var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT COUNT(*) 
                    FROM sqlite_master 
                    WHERE type='table' AND name='DraftInvoiceNotifications';";

                var result = await command.ExecuteScalarAsync();
                var tableExists = Convert.ToInt32(result) > 0;

                return !tableExists; // Migration needed if table doesn't exist
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error checking migration status: {ex.Message}");
                return true; // Assume migration is needed if we can't check
            }
        }

        /// <summary>
        /// Executes the two-tier invoice system migration script
        /// </summary>
        private async Task ExecuteMigrationScriptAsync()
        {
            try
            {
                var migrationScriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SQL", "TwoTierInvoiceSystem_Migration.sql");
                
                if (!File.Exists(migrationScriptPath))
                {
                    throw new FileNotFoundException($"Migration script not found at: {migrationScriptPath}");
                }

                var migrationScript = await File.ReadAllTextAsync(migrationScriptPath);
                
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Split the script into individual commands (simple approach)
                var commands = migrationScript.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var commandText in commands)
                {
                    var trimmedCommand = commandText.Trim();
                    if (string.IsNullOrEmpty(trimmedCommand) || trimmedCommand.StartsWith("--"))
                        continue;

                    try
                    {
                        using var command = connection.CreateCommand();
                        command.CommandText = trimmedCommand;
                        await command.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error executing command: {trimmedCommand.Substring(0, Math.Min(50, trimmedCommand.Length))}... Error: {ex.Message}");
                        // Continue with other commands
                    }
                }

                System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Migration script executed successfully.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error executing migration script: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Verifies that all required tables and columns exist
        /// </summary>
        private async Task<bool> VerifySetupAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Check required tables
                var requiredTables = new[]
                {
                    "Invoice",
                    "InvoiceItems",
                    "InvoicePayments",
                    "DraftInvoiceNotifications",
                    "DraftInvoiceSettings"
                };

                foreach (var tableName in requiredTables)
                {
                    using var command = connection.CreateCommand();
                    command.CommandText = @"
                        SELECT COUNT(*) 
                        FROM sqlite_master 
                        WHERE type='table' AND name=@tableName;";
                    command.Parameters.AddWithValue("@tableName", tableName);

                    var result = await command.ExecuteScalarAsync();
                    var tableExists = Convert.ToInt32(result) > 0;

                    if (!tableExists)
                    {
                        System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Required table '{tableName}' not found!");
                        return false;
                    }
                }

                // Check if Invoice table has the new two-tier columns
                var requiredInvoiceColumns = new[]
                {
                    "CreatedByUserId",
                    "CompletedByUserId",
                    "DraftCreatedAt",
                    "AdminCompletedAt",
                    "RequiresAdminCompletion"
                };

                foreach (var columnName in requiredInvoiceColumns)
                {
                    using var command = connection.CreateCommand();
                    command.CommandText = @"
                        SELECT COUNT(*) 
                        FROM pragma_table_info('Invoice') 
                        WHERE name=@columnName;";
                    command.Parameters.AddWithValue("@columnName", columnName);

                    var result = await command.ExecuteScalarAsync();
                    var columnExists = Convert.ToInt32(result) > 0;

                    if (!columnExists)
                    {
                        System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Required column 'Invoice.{columnName}' not found!");
                        return false;
                    }
                }

                System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] All required tables and columns verified successfully.");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error during setup verification: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ensures default settings exist in the database
        /// </summary>
        private async Task EnsureDefaultSettingsAsync()
        {
            try
            {
                using var context = new POSDbContext();
                
                var existingSettings = await context.DraftInvoiceSettings.FirstOrDefaultAsync();
                if (existingSettings == null)
                {
                    var defaultSettings = DraftInvoiceSettings.GetDefaultSettings();
                    context.DraftInvoiceSettings.Add(defaultSettings);
                    await context.SaveChangesAsync();
                    
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Default settings created successfully.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[TWO_TIER_INIT] Settings already exist, skipping default creation.");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error ensuring default settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current status of the two-tier invoice system
        /// </summary>
        public async Task<TwoTierSystemStatus> GetSystemStatusAsync()
        {
            try
            {
                var status = new TwoTierSystemStatus();
                
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Check pending drafts count
                using var pendingCommand = connection.CreateCommand();
                pendingCommand.CommandText = @"
                    SELECT COUNT(*) 
                    FROM Invoice 
                    WHERE RequiresAdminCompletion = 1 AND Status = 'Draft';";
                
                var pendingResult = await pendingCommand.ExecuteScalarAsync();
                status.PendingDraftsCount = Convert.ToInt32(pendingResult);

                // Check total drafts count
                using var totalCommand = connection.CreateCommand();
                totalCommand.CommandText = @"
                    SELECT COUNT(*) 
                    FROM Invoice 
                    WHERE RequiresAdminCompletion = 1;";
                
                var totalResult = await totalCommand.ExecuteScalarAsync();
                status.TotalDraftsCount = Convert.ToInt32(totalResult);

                // Check notifications count
                using var notificationCommand = connection.CreateCommand();
                notificationCommand.CommandText = @"
                    SELECT COUNT(*) 
                    FROM DraftInvoiceNotifications 
                    WHERE IsRead = 0;";
                
                var notificationResult = await notificationCommand.ExecuteScalarAsync();
                status.UnreadNotificationsCount = Convert.ToInt32(notificationResult);

                status.IsInitialized = true;
                return status;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[TWO_TIER_INIT] Error getting system status: {ex.Message}");
                return new TwoTierSystemStatus { IsInitialized = false };
            }
        }
    }

    /// <summary>
    /// Status information for the two-tier invoice system
    /// </summary>
    public class TwoTierSystemStatus
    {
        public bool IsInitialized { get; set; }
        public int PendingDraftsCount { get; set; }
        public int TotalDraftsCount { get; set; }
        public int UnreadNotificationsCount { get; set; }
        
        public string StatusMessage => IsInitialized 
            ? $"System ready - {PendingDraftsCount} pending drafts, {UnreadNotificationsCount} unread notifications"
            : "System not initialized";
    }
}
