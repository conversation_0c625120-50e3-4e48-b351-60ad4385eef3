using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Views;
using MaterialDesignThemes.Wpf;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem;
using System.Windows;

namespace POSSystem.ViewModels
{
    public class BusinessExpenseViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private readonly CashDrawerService _cashDrawerService;
        private BusinessExpense _selectedExpense;
        private ObservableCollection<BusinessExpense> _businessExpenses;
        private bool _isLoading;

        public BusinessExpenseViewModel()
        {
            _dbService = new DatabaseService();
            _cashDrawerService = new CashDrawerService(_dbService);
            _businessExpenses = new ObservableCollection<BusinessExpense>();
            InitializeCommands();
            LoadExpensesAsync().ConfigureAwait(false);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<BusinessExpense> BusinessExpenses
        {
            get => _businessExpenses;
            set
            {
                if (_businessExpenses != value)
                {
                    _businessExpenses = value;
                    OnPropertyChanged();
                }
            }
        }

        public BusinessExpense SelectedExpense
        {
            get => _selectedExpense;
            set
            {
                _selectedExpense = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public ICommand AddExpenseCommand { get; private set; }
        public ICommand EditExpenseCommand { get; private set; }
        public ICommand DeleteExpenseCommand { get; private set; }
        public ICommand CloseCommand { get; private set; }

        private void InitializeCommands()
        {
            AddExpenseCommand = new RelayCommand(ExecuteAddExpense);
            EditExpenseCommand = new RelayCommand(ExecuteEditExpense, CanExecuteEditExpense);
            DeleteExpenseCommand = new RelayCommand(ExecuteDeleteExpense, CanExecuteDeleteExpense);
            CloseCommand = new RelayCommand(ExecuteClose);
        }

        private async Task LoadExpensesAsync()
        {
            try
            {
                IsLoading = true;
                var expenses = await _cashDrawerService.GetBusinessExpenses();
                
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _businessExpenses.Clear();
                    if (expenses != null)
                    {
                        foreach (var expense in expenses)
                        {
                            _businessExpenses.Add(expense);
                        }
                    }
                    OnPropertyChanged(nameof(BusinessExpenses));
                });
            }
            catch (Exception ex)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageBox.Show($"Error loading expenses: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void ExecuteAddExpense(object parameter)
        {
            try
            {
                var expense = new BusinessExpense
                {
                    Date = DateTime.Now,
                    UserId = _dbService.GetDefaultUser().Id,
                    Category = ExpenseCategory.Other,  // Default category
                    Frequency = ExpenseFrequency.OneTime,  // Default frequency
                    Amount = 0,  // Will be set by user
                    Description = "",  // Empty string by default
                    Notes = ""  // Empty string by default
                };

                var dialog = new BusinessExpenseEditDialog(expense);
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    try
                    {
                        await _cashDrawerService.AddBusinessExpense(expense);
                        BusinessExpenses.Add(expense);
                    }
                    catch (Exception ex)
                    {
                        var innerMessage = ex.InnerException?.Message ?? ex.Message;
                        MessageBox.Show($"Failed to save expense: {innerMessage}", "Database Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding expense: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ExecuteEditExpense(object parameter)
        {
            if (SelectedExpense == null) return;

            try
            {
                // Create a copy of the expense for editing
                var expenseCopy = new BusinessExpense
                {
                    Id = SelectedExpense.Id,
                    Description = SelectedExpense.Description,
                    Amount = SelectedExpense.Amount,
                    Date = SelectedExpense.Date,
                    Category = SelectedExpense.Category,
                    Frequency = SelectedExpense.Frequency,
                    NextDueDate = SelectedExpense.NextDueDate,
                    Notes = SelectedExpense.Notes ?? "",
                    UserId = SelectedExpense.UserId,
                    CashDrawerId = SelectedExpense.CashDrawerId
                };

                var dialog = new BusinessExpenseEditDialog(expenseCopy);
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    try
                    {
                        // Update the original expense with new values
                        SelectedExpense.Description = expenseCopy.Description;
                        SelectedExpense.Amount = expenseCopy.Amount;
                        SelectedExpense.Date = expenseCopy.Date;
                        SelectedExpense.Category = expenseCopy.Category;
                        SelectedExpense.Frequency = expenseCopy.Frequency;
                        SelectedExpense.NextDueDate = expenseCopy.NextDueDate;
                        SelectedExpense.Notes = expenseCopy.Notes;

                        await _cashDrawerService.UpdateBusinessExpense(SelectedExpense);
                        await LoadExpensesAsync(); // Reload to refresh the list
                    }
                    catch (Exception ex)
                    {
                        var innerMessage = ex.InnerException?.Message ?? ex.Message;
                        MessageBox.Show($"Failed to update expense: {innerMessage}", "Database Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error editing expense: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ExecuteDeleteExpense(object parameter)
        {
            if (SelectedExpense == null) return;

            var confirmTemplate = FindResourceString("DialogConfirmDeleteExpense") ?? "Are you sure you want to delete the expense '{0}'?";
            var confirmMessage = string.Format(confirmTemplate, SelectedExpense.Description);
            
            var confirmTitle = FindResourceString("DialogConfirmTitle") ?? "Confirm Delete";
            
            var result = MessageBox.Show(
                confirmMessage,
                confirmTitle,
                MessageBoxButton.YesNo,
                MessageBoxImage.Question) == MessageBoxResult.Yes;

            if (result)
            {
                try
                {
                    await _cashDrawerService.DeleteBusinessExpense(SelectedExpense.Id);
                    BusinessExpenses.Remove(SelectedExpense);
                    
                    var successMessage = FindResourceString("DialogExpenseDeletedSuccess") ?? "Expense deleted successfully!";
                    var successTitle = FindResourceString("DialogSuccessTitle") ?? "Success";
                    MessageBox.Show(successMessage, successTitle, MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    var errorTemplate = FindResourceString("DialogErrorDeletingExpense") ?? "Error deleting expense: {0}";
                    var errorMessage = string.Format(errorTemplate, ex.Message);
                    var errorTitle = FindResourceString("DialogErrorTitle") ?? "Error";
                    MessageBox.Show(errorMessage, errorTitle, MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ExecuteClose(object parameter)
        {
            try
            {
                if (DialogHost.IsDialogOpen("BusinessExpenseDialog"))
                {
                    DialogHost.Close("BusinessExpenseDialog");
                }
                else if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    DialogHost.Close("RootDialog");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error closing dialog: {ex.Message}");
            }
        }

        private bool CanExecuteEditExpense(object parameter)
        {
            return SelectedExpense != null;
        }

        private bool CanExecuteDeleteExpense(object parameter)
        {
            return SelectedExpense != null;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private string FindResourceString(string key)
        {
            try
            {
                return Application.Current.FindResource(key) as string;
            }
            catch
            {
                return null;
            }
        }
    }
} 