<?xml version="1.0" encoding="utf-8" ?>
<Window x:Class="POSSystem.Views.BusinessExpenseEditDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:models="clr-namespace:POSSystem.Models"
        mc:Ignorable="d"
        Title="{Binding IsNewExpense, Converter={StaticResource BoolToStringConverter}, ConverterParameter=AddNewExpense:EditExpense}"
        Height="500" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="{Binding IsNewExpense, Converter={StaticResource BoolToStringConverter}, ConverterParameter=AddNewExpense:EditExpense}"
                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                   Margin="0,0,0,16"/>

        <!-- Form Fields -->
        <StackPanel Grid.Row="1" Margin="0,8">
            <TextBox Text="{Binding Expense.Description}"
                     materialDesign:HintAssist.Hint="{DynamicResource Description}"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     Margin="0,8"/>

            <TextBox Text="{Binding Expense.Amount}"
                     materialDesign:HintAssist.Hint="{DynamicResource Amount}"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     Margin="0,8"/>

            <ComboBox ItemsSource="{Binding Source={StaticResource ExpenseCategoryEnum}}"
                      SelectedItem="{Binding Expense.Category}"
                      materialDesign:HintAssist.Hint="{DynamicResource Category}"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,8">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Converter={StaticResource EnumToResourceConverter}}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

            <ComboBox ItemsSource="{Binding Source={StaticResource ExpenseFrequencyEnum}}"
                      SelectedItem="{Binding Expense.Frequency}"
                      materialDesign:HintAssist.Hint="{DynamicResource Frequency}"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,8">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Converter={StaticResource EnumToResourceConverter}}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

            <DatePicker SelectedDate="{Binding Expense.NextDueDate}"
                       materialDesign:HintAssist.Hint="{DynamicResource NextDue}"
                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                       Margin="0,8"/>

            <TextBox Text="{Binding Expense.Notes}"
                     materialDesign:HintAssist.Hint="{DynamicResource Notes}"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalContentAlignment="Top"
                     Height="80"
                     Margin="0,8"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,16,0,0">
            <Button Content="{DynamicResource Cancel}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Command="{Binding CancelCommand}"
                    Margin="0,0,8,0"/>
            <Button Content="{DynamicResource Save}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Command="{Binding SaveCommand}"/>
        </StackPanel>
    </Grid>
</Window> 