using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Services;
using POSSystem.Services.Printing;
using POSSystem.Services.Interfaces;
using POSSystem.Models;
using System.Windows;
using Microsoft.Win32;

namespace POSSystem.Views
{
    public partial class SettingsView : UserControl
    {
        private SettingsViewModel ViewModel => (SettingsViewModel)DataContext;
        private readonly DatabaseService _dbService;
        private readonly IEnhancedReceiptPrintService _receiptPrintService;

        public SettingsView()
        {
            InitializeComponent();
            _dbService = new DatabaseService();
            _receiptPrintService = new EnhancedReceiptPrintService(_dbService);
            Loaded += SettingsView_Loaded;
        }

        private void SettingsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (loyaltyProgramView != null)
                {
                    loyaltyProgramView.DataContext = new LoyaltyProgramViewModel(_dbService);
                }

                if (discountPermissionsView != null)
                {
                    discountPermissionsView.DataContext = new DiscountPermissionsViewModel(_dbService);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing settings views: {ex.Message}");
                MessageBox.Show($"Error initializing settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnNavButtonClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                try
                {
                    // Reset all buttons to default
                    btnLanguageRegional.Tag = null;
                    btnTheme.Tag = null;
                    btnCompany.Tag = null;
                    btnLoyalty.Tag = null;

                    // Reset discount permissions button if found
                    var discountButton = FindName("btnDiscountPermissions") as Button;
                    if (discountButton != null)
                    {
                        discountButton.Tag = null;
                    }

                    btnDatabase.Tag = null;

                    // Reset receipt printing button if found
                    var receiptButton = FindName("btnReceiptPrinting") as Button;
                    if (receiptButton != null)
                    {
                        receiptButton.Tag = null;
                    }

                    // Set the clicked button as selected
                    button.Tag = "Selected";

                    // Navigate to the appropriate tab
                    switch (button.Name)
                    {
                        case "btnLanguageRegional":
                            SettingsTabs.SelectedItem = LanguageRegionalTab;
                            break;
                        case "btnTheme":
                            SettingsTabs.SelectedItem = ThemeTab;
                            break;
                        case "btnCompany":
                            SettingsTabs.SelectedItem = CompanyTab;
                            break;
                        case "btnLoyalty":
                            SettingsTabs.SelectedItem = LoyaltyTab;
                            break;
                        case "btnDiscountPermissions":
                            var discountTab = FindName("DiscountPermissionsTab") as TabItem;
                            if (discountTab != null)
                            {
                                SettingsTabs.SelectedItem = discountTab;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("DiscountPermissionsTab not found");
                            }
                            break;
                        case "btnDatabase":
                            SettingsTabs.SelectedItem = DatabaseTab;
                            break;
                        case "btnReceiptPrinting":
                            var receiptTab = FindName("ReceiptPrintingTab") as TabItem;
                            if (receiptTab != null)
                            {
                                SettingsTabs.SelectedItem = receiptTab;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("ReceiptPrintingTab not found");
                            }
                            break;
                        default:
                            System.Diagnostics.Debug.WriteLine($"Unknown button: {button.Name}");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in navigation: {ex.Message}");
                    MessageBox.Show($"Navigation error: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #region Receipt Printing Event Handlers

        private void BrowsePdfPath_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "Select PDF backup directory",
                    ShowNewFolderButton = true
                };

                if (!string.IsNullOrEmpty(txtPdfBackupPath.Text))
                {
                    folderDialog.SelectedPath = txtPdfBackupPath.Text;
                }

                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    txtPdfBackupPath.Text = folderDialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error selecting folder: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TestPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "Printing...";

                // Create a test sale for printing
                var testSale = CreateTestSale();

                bool success = await _receiptPrintService.PrintReceiptAsync(testSale, true);

                if (success)
                {
                    MessageBox.Show("Test receipt sent to printer successfully!",
                        "Test Print", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("Test printing failed or was cancelled.",
                        "Test Print", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during test print: {ex.Message}", "Test Print Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "Test Print";
            }
        }

        private async void PreviewTest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "Loading...";

                // Create a test sale for preview
                var testSale = CreateTestSale();

                await _receiptPrintService.PreviewReceiptAsync(testSale);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during test preview: {ex.Message}", "Test Preview Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "Preview Test";
            }
        }

        private async void SaveTestPdf_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "Saving...";

                // Use the test service for better error handling
                var testService = new Services.Printing.ReceiptPrintingTestService(_receiptPrintService);
                bool success = await testService.TestPdfExportAsync();

                if (!success)
                {
                    MessageBox.Show(
                        "PDF export test failed. Please check the debug output for detailed error information.",
                        "Test Failed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during PDF test: {ex.Message}", "Test Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "Save Test PDF";
            }
        }

        private async void TestConfiguration_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "Testing...";

                // Test system configuration
                var testService = new Services.Printing.ReceiptPrintingTestService(_receiptPrintService);
                await testService.TestSystemConfigurationAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during configuration test: {ex.Message}", "Test Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "Test Configuration";
            }
        }

        private Sale CreateTestSale()
        {
            // Create a sample sale for testing
            var testSale = new Sale
            {
                Id = 999999,
                InvoiceNumber = "TEST-001",
                SaleDate = DateTime.Now,
                PaymentMethod = "Cash",
                PaymentStatus = "Paid",
                Status = "Completed",
                Subtotal = 150.00m,
                DiscountAmount = 15.00m,
                TaxAmount = 13.50m,
                GrandTotal = 148.50m,
                AmountPaid = 150.00m,
                Items = new List<SaleItem>
                {
                    new SaleItem
                    {
                        Id = 1,
                        ProductId = 1,
                        Product = new Product { Name = "Test Product 1", Barcode = "*********" },
                        Quantity = 2,
                        UnitPrice = 50.00m,
                        Total = 100.00m
                    },
                    new SaleItem
                    {
                        Id = 2,
                        ProductId = 2,
                        Product = new Product { Name = "Test Product 2", Barcode = "*********" },
                        Quantity = 1,
                        UnitPrice = 50.00m,
                        Total = 50.00m
                    }
                },
                Customer = new Customer
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Customer",
                    Phone = "************",
                    Email = "<EMAIL>"
                }
            };

            return testSale;
        }

        #endregion
    }
}