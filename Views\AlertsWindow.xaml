<?xml version="1.0" encoding="utf-8" ?>
<Window x:Class="POSSystem.Views.AlertsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POSSystem.Views"
        xmlns:converters="clr-namespace:POSSystem.Converters"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{DynamicResource AllNotifications}" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>
        <converters:AlertTypeToColorConverter x:Key="AlertTypeToColorConverter"/>
        <converters:AlertTypeToIconConverter x:Key="AlertTypeToIconConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource AllNotifications}" 
                     FontSize="24" 
                     FontWeight="SemiBold"/>

            <TextBlock Grid.Column="1" 
                      x:Name="NotificationCounter"
                      Text="{DynamicResource ShowingNotifications}"
                      VerticalAlignment="Center"
                      Margin="12,0"
                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>

            <Button Grid.Column="2"
                    Click="MarkAllAsRead_Click"
                    Style="{StaticResource MaterialDesignFlatButton}">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckAll" 
                                                   Width="20" 
                                                   Height="20"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource MarkAllAsRead}"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Grid>

        <!-- Notifications List -->
        <ListView x:Name="AlertsList" 
                  Grid.Row="1"
                  Style="{StaticResource MaterialDesignListView}"
                  ItemContainerStyle="{StaticResource MaterialDesignListBoxItem}"
                  VirtualizingStackPanel.IsVirtualizing="True"
                  VirtualizingStackPanel.VirtualizationMode="Recycling"
                  ScrollViewer.IsDeferredScrollingEnabled="True"
                  VirtualizingPanel.ScrollUnit="Pixel"
                  VirtualizingPanel.CacheLength="2"
                  VirtualizingPanel.CacheLengthUnit="Page">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Alert Icon -->
                        <Border Width="40" 
                                Height="40" 
                                CornerRadius="20"
                                Background="{Binding AlertType, Converter={StaticResource AlertTypeToColorConverter}}">
                            <materialDesign:PackIcon Kind="{Binding AlertType, Converter={StaticResource AlertTypeToIconConverter}}"
                                                   Width="24"
                                                   Height="24"
                                                   Foreground="White"/>
                        </Border>

                        <!-- Alert Content -->
                        <StackPanel Grid.Column="1" 
                                  Margin="16,0">
                            <TextBlock Text="{Binding Message}"
                                     TextWrapping="Wrap"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                            <TextBlock Text="{Binding CreatedAt, StringFormat='{}{0:g}'}"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Mark as Read Button -->
                        <Button Grid.Column="2"
                                Style="{StaticResource MaterialDesignFlatButton}"
                                Click="MarkAsRead_Click"
                                Margin="8,0"
                                ToolTip="{DynamicResource MarkAsRead}">
                            <materialDesign:PackIcon Kind="CheckCircleOutline"/>
                        </Button>
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Loading Indicator -->
        <Grid x:Name="LoadingIndicator" 
              Grid.Row="1" 
              VerticalAlignment="Bottom" 
              HorizontalAlignment="Center"
              Margin="0,0,0,16"
              Visibility="Collapsed">
            <StackPanel Orientation="Horizontal">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="20"
                           Height="20"
                           Margin="0,0,8,0"/>
                <TextBlock Text="{DynamicResource LoadingMoreNotifications}"
                         VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 