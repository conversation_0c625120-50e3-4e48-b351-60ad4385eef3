using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.SalesManagement
{
    /// <summary>
    /// Interface for sales management operations
    /// </summary>
    public interface ISalesManagementService
    {
        /// <summary>
        /// Save a new sale
        /// </summary>
        Task<int> SaveSaleAsync(Sale sale);

        /// <summary>
        /// Get sale by ID
        /// </summary>
        Task<Sale> GetSaleByIdAsync(int id);

        /// <summary>
        /// Get sales for a date range
        /// </summary>
        Task<List<Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get recent sales with limit
        /// </summary>
        Task<List<Sale>> GetRecentSalesAsync(int limit = 50);

        /// <summary>
        /// Get sales total for a specific date
        /// </summary>
        Task<decimal> GetSalesTotalAsync(DateTime date);

        /// <summary>
        /// Get sales count and total for a period
        /// </summary>
        Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get unpaid sales
        /// </summary>
        Task<List<Sale>> GetUnpaidSalesAsync();

        /// <summary>
        /// Update sale
        /// </summary>
        Task<bool> UpdateSaleAsync(Sale sale);

        /// <summary>
        /// Delete sale
        /// </summary>
        Task<bool> DeleteSaleAsync(int id);

        /// <summary>
        /// Get sales by payment method
        /// </summary>
        Task<List<Sale>> GetSalesByPaymentMethodAsync(string paymentMethod, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get average transaction value for a period
        /// </summary>
        Task<decimal> GetAverageTransactionValueAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get payment method distribution for a period
        /// </summary>
        Task<Dictionary<string, decimal>> GetPaymentMethodDistributionAsync(DateTime startDate, DateTime endDate);
    }
}
