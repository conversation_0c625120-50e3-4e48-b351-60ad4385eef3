using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// A proxy class to resolve ambiguity between multiple DiscountType definitions.
    /// This can be used in the DiscountService to provide clear, unambiguous access to DiscountType properties.
    /// </summary>
    public class DiscountTypeProxy
    {
        private readonly DiscountType _discountType;
        
        public DiscountTypeProxy(DiscountType discountType)
        {
            _discountType = discountType;
        }
        
        public int Id => _discountType.Id;
        public string Name => _discountType.Name;
        public string Description => _discountType.Description;
        public string InternalName => _discountType.InternalName;
        public bool IsPercentage => InternalName == "Percentage";
        
        // Add any other properties or methods you need to access from DiscountType
        
        // Static conversion methods for convenience
        public static DiscountTypeProxy FromDiscountType(DiscountType discountType)
        {
            return new DiscountTypeProxy(discountType);
        }
        
        public DiscountType ToDiscountType()
        {
            return _discountType;
        }
    }
} 