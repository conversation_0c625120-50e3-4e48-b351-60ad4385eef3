using System;
using System.Diagnostics;
using System.Windows;
using POSSystem.Models;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ PERFORMANCE OPTIMIZATION: Helper to control debug logging for performance troubleshooting
    /// </summary>
    public static class PerformanceDebugHelper
    {
        private static bool _isDebugModeEnabled = false;
        private static DateTime _debugModeStartTime;
        private static int _debugSessionDurationMinutes = 5;

        // ✅ PERFORMANCE FIX: Control various debug logging systems
        private static bool _cartDebugEnabled = false;
        private static bool _salesViewDebugEnabled = false;

        /// <summary>
        /// Enable debug logging for performance troubleshooting (temporary)
        /// </summary>
        public static void EnableDebugMode(int durationMinutes = 5)
        {
            _isDebugModeEnabled = true;
            _debugModeStartTime = DateTime.Now;
            _debugSessionDurationMinutes = durationMinutes;
            
            // Enable various debug logging systems
            CartItem.SetDebugLogging(true);
            _cartDebugEnabled = true;
            _salesViewDebugEnabled = true;

            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Debug mode enabled for {durationMinutes} minutes");
            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Cart calculation logging: ENABLED");
            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Cart debug logging: ENABLED");
            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Sales view debug logging: ENABLED");
            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Debug mode will auto-disable at: {_debugModeStartTime.AddMinutes(durationMinutes):HH:mm:ss}");
        }

        /// <summary>
        /// Disable debug logging to restore performance
        /// </summary>
        public static void DisableDebugMode()
        {
            _isDebugModeEnabled = false;
            
            // Disable various debug logging systems
            CartItem.SetDebugLogging(false);
            _cartDebugEnabled = false;
            _salesViewDebugEnabled = false;

            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Debug mode disabled");
            Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] All debug logging: DISABLED");
        }

        /// <summary>
        /// Check if debug mode should auto-expire
        /// </summary>
        public static void CheckDebugModeExpiry()
        {
            if (_isDebugModeEnabled && DateTime.Now > _debugModeStartTime.AddMinutes(_debugSessionDurationMinutes))
            {
                Debug.WriteLine($"🔧 [PERFORMANCE-DEBUG] Debug mode auto-expired after {_debugSessionDurationMinutes} minutes");
                DisableDebugMode();
            }
        }

        /// <summary>
        /// Get current debug mode status
        /// </summary>
        public static bool IsDebugModeEnabled => _isDebugModeEnabled;

        /// <summary>
        /// Check if cart debug logging is enabled
        /// </summary>
        public static bool IsCartDebugEnabled => _cartDebugEnabled;

        /// <summary>
        /// Write cart debug message only if cart debug logging is enabled
        /// </summary>
        public static void WriteCartDebug(string message)
        {
            #if DEBUG
            if (_cartDebugEnabled)
            {
                Debug.WriteLine(message);
            }
            #endif
        }

        /// <summary>
        /// Check if sales view debug logging is enabled
        /// </summary>
        public static bool IsSalesViewDebugEnabled => _salesViewDebugEnabled;

        /// <summary>
        /// Get remaining debug time in minutes
        /// </summary>
        public static int RemainingDebugTimeMinutes
        {
            get
            {
                if (!_isDebugModeEnabled) return 0;
                var elapsed = DateTime.Now - _debugModeStartTime;
                var remaining = _debugSessionDurationMinutes - (int)elapsed.TotalMinutes;
                return Math.Max(0, remaining);
            }
        }

        /// <summary>
        /// Show performance debug control dialog
        /// </summary>
        public static void ShowDebugControlDialog()
        {
            var message = _isDebugModeEnabled 
                ? $"Debug mode is ENABLED\nRemaining time: {RemainingDebugTimeMinutes} minutes\n\nClick OK to disable debug mode now."
                : "Debug mode is DISABLED\n\nClick OK to enable debug mode for 5 minutes.";

            var result = MessageBox.Show(message, "Performance Debug Control", 
                MessageBoxButton.OKCancel, MessageBoxImage.Information);

            if (result == MessageBoxResult.OK)
            {
                if (_isDebugModeEnabled)
                {
                    DisableDebugMode();
                }
                else
                {
                    EnableDebugMode(5);
                }
            }
        }

        /// <summary>
        /// Quick performance test to measure frame rate improvement
        /// </summary>
        public static void RunQuickPerformanceTest()
        {
            Debug.WriteLine("🧪 [PERFORMANCE-TEST] Starting quick performance test...");
            
            var startTime = DateTime.Now;
            var testDuration = TimeSpan.FromSeconds(10);
            var frameCount = 0;
            
            // Simple frame counting test
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(16) // ~60 FPS
            };
            
            timer.Tick += (s, e) =>
            {
                frameCount++;
                if (DateTime.Now - startTime >= testDuration)
                {
                    timer.Stop();
                    var actualFPS = frameCount / testDuration.TotalSeconds;
                    
                    Debug.WriteLine($"🧪 [PERFORMANCE-TEST] Test completed:");
                    Debug.WriteLine($"🧪 [PERFORMANCE-TEST] Duration: {testDuration.TotalSeconds:F1}s");
                    Debug.WriteLine($"🧪 [PERFORMANCE-TEST] Frame count: {frameCount}");
                    Debug.WriteLine($"🧪 [PERFORMANCE-TEST] Average FPS: {actualFPS:F1}");
                    
                    var status = actualFPS >= 50 ? "EXCELLENT" : 
                                actualFPS >= 30 ? "GOOD" : 
                                actualFPS >= 20 ? "ACCEPTABLE" : "POOR";
                    
                    Debug.WriteLine($"🧪 [PERFORMANCE-TEST] Performance status: {status}");
                    
                    MessageBox.Show($"Performance Test Results:\n\n" +
                                  $"Average FPS: {actualFPS:F1}\n" +
                                  $"Status: {status}\n\n" +
                                  $"Target: 30+ FPS for smooth operation",
                                  "Performance Test Complete", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };
            
            timer.Start();
        }
    }
}
