using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using POSSystem.ViewModels;

namespace POSSystem.Converters
{
    public class DateRangeToResourceConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string key)
            {
                var localizedString = Application.Current.TryFindResource(key);
                return localizedString ?? key;
            }
            else if (value is TimePeriod timePeriod)
            {
                var resourceKey = timePeriod.DisplayName;
                var localizedString = Application.Current.TryFindResource(resourceKey);
                return localizedString ?? timePeriod.DisplayName;
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 