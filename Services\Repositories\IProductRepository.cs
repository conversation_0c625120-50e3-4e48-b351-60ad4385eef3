using POSSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// Repository interface for Product operations
    /// This demonstrates the CORRECT way to handle database operations
    /// SAFE APPROACH: This interface works alongside existing DatabaseService
    /// </summary>
    public interface IProductRepository
    {
        // Async operations for better performance
        Task<Product> GetByIdAsync(int id);
        Task<IEnumerable<Product>> GetAllAsync();
        Task<IEnumerable<Product>> GetPagedAsync(int page, int pageSize);
        Task<IEnumerable<Product>> SearchAsync(string searchTerm, int maxResults = 50);
        Task<IEnumerable<Product>> GetLowStockAsync();
        Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId);
        Task<IEnumerable<Product>> GetExpiringProductsAsync(int daysThreshold = 30);

        // CRUD operations
        Task<Product> CreateAsync(Product product);
        Task UpdateAsync(Product product);
        Task DeleteAsync(int id);
        Task<bool> SoftDeleteAsync(int id); // Safer than hard delete

        // Stock management
        Task UpdateStockAsync(int productId, int newQuantity);
        Task<bool> ExistsByBarcodeAsync(string barcode);
        Task<bool> ExistsByIdAsync(int id);
        Task<Product> GetByBarcodeAsync(string barcode);

        // Batch operations for performance
        Task<IEnumerable<Product>> CreateBatchAsync(IEnumerable<Product> products);
        Task UpdateBatchAsync(IEnumerable<Product> products);

        // Statistics (replacing heavy DatabaseService queries)
        Task<int> GetTotalCountAsync();
        Task<int> GetLowStockCountAsync();
        Task<decimal> GetTotalInventoryValueAsync();
    }
}
