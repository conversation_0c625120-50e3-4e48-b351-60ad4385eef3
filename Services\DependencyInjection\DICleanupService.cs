using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace POSSystem.Services.DependencyInjection
{
    /// <summary>
    /// Service to identify and help fix dependency injection anti-patterns and issues.
    /// This service provides analysis and recommendations for proper DI usage.
    /// </summary>
    public class DICleanupService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DICleanupService> _logger;

        public DICleanupService(IServiceProvider serviceProvider, ILogger<DICleanupService> logger = null)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger;
        }

        /// <summary>
        /// Analyzes the current DI configuration and identifies issues.
        /// </summary>
        /// <returns>Analysis result with identified issues and recommendations</returns>
        public DIAnalysisResult AnalyzeDIConfiguration()
        {
            var result = new DIAnalysisResult();

            try
            {
                _logger?.LogInformation("Starting DI configuration analysis...");

                // Check for service locator anti-patterns
                result.ServiceLocatorIssues = IdentifyServiceLocatorPatterns();

                // Check for mixed service lifetimes
                result.LifetimeIssues = IdentifyLifetimeIssues();

                // Check for missing service registrations
                result.MissingRegistrations = IdentifyMissingRegistrations();

                // Check for circular dependencies
                result.CircularDependencies = IdentifyCircularDependencies();

                // Check for improper constructor patterns
                result.ConstructorIssues = IdentifyConstructorIssues();

                // Generate recommendations
                result.Recommendations = GenerateRecommendations(result);

                _logger?.LogInformation("DI analysis completed. Found {IssueCount} issues", 
                    result.TotalIssueCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during DI analysis");
                result.AnalysisError = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Identifies service locator anti-patterns in the codebase.
        /// </summary>
        private List<DIIssue> IdentifyServiceLocatorPatterns()
        {
            var issues = new List<DIIssue>();

            // Known service locator patterns to look for
            var serviceLocatorPatterns = new[]
            {
                "App.ServiceProvider?.GetService",
                "ServiceConfiguration.CreateServiceProvider()",
                "serviceProvider.GetService",
                "new DatabaseService()",
                "new SimpleAlertService()",
                "new ProductLookupService("
            };

            foreach (var pattern in serviceLocatorPatterns)
            {
                issues.Add(new DIIssue
                {
                    Type = DIIssueType.ServiceLocatorAntiPattern,
                    Description = $"Service locator pattern detected: {pattern}",
                    Severity = DIIssueSeverity.High,
                    Location = "Multiple files (requires code analysis)",
                    Recommendation = GetServiceLocatorRecommendation(pattern)
                });
            }

            return issues;
        }

        /// <summary>
        /// Identifies mixed service lifetime issues.
        /// </summary>
        private List<DIIssue> IdentifyLifetimeIssues()
        {
            var issues = new List<DIIssue>();

            // Check for common lifetime mismatches
            issues.Add(new DIIssue
            {
                Type = DIIssueType.IncorrectLifetime,
                Description = "DatabaseService registered as Scoped but used as Singleton in some places",
                Severity = DIIssueSeverity.Medium,
                Location = "ServiceConfiguration.cs, Various Views",
                Recommendation = "Ensure DatabaseService is consistently used as Scoped service"
            });

            issues.Add(new DIIssue
            {
                Type = DIIssueType.IncorrectLifetime,
                Description = "ViewModels should be Transient but some are created manually",
                Severity = DIIssueSeverity.Medium,
                Location = "Various Views and Dialogs",
                Recommendation = "Register all ViewModels as Transient and inject them properly"
            });

            return issues;
        }

        /// <summary>
        /// Identifies missing service registrations.
        /// </summary>
        private List<DIIssue> IdentifyMissingRegistrations()
        {
            var issues = new List<DIIssue>();

            // Services that should be registered but might not be
            var expectedServices = new[]
            {
                typeof(POSSystem.Services.Interfaces.IProductLookupService),
                typeof(POSSystem.ViewModels.ProductsViewModel),
                typeof(POSSystem.ViewModels.SaleViewModel),
                typeof(POSSystem.ViewModels.DashboardViewModel)
            };

            foreach (var serviceType in expectedServices)
            {
                try
                {
                    var service = _serviceProvider.GetService(serviceType);
                    if (service == null)
                    {
                        issues.Add(new DIIssue
                        {
                            Type = DIIssueType.MissingRegistration,
                            Description = $"Service {serviceType.Name} is not registered in DI container",
                            Severity = DIIssueSeverity.High,
                            Location = "ServiceConfiguration.cs",
                            Recommendation = $"Register {serviceType.Name} with appropriate lifetime in ServiceConfiguration"
                        });
                    }
                }
                catch (Exception)
                {
                    // Service resolution failed - likely missing registration
                    issues.Add(new DIIssue
                    {
                        Type = DIIssueType.MissingRegistration,
                        Description = $"Service {serviceType.Name} cannot be resolved from DI container",
                        Severity = DIIssueSeverity.High,
                        Location = "ServiceConfiguration.cs",
                        Recommendation = $"Register {serviceType.Name} and its dependencies in ServiceConfiguration"
                    });
                }
            }

            return issues;
        }

        /// <summary>
        /// Identifies potential circular dependencies.
        /// </summary>
        private List<DIIssue> IdentifyCircularDependencies()
        {
            var issues = new List<DIIssue>();

            // This would require more sophisticated analysis
            // For now, we'll identify known potential circular dependencies
            issues.Add(new DIIssue
            {
                Type = DIIssueType.CircularDependency,
                Description = "Potential circular dependency between DatabaseService and ViewModels",
                Severity = DIIssueSeverity.Medium,
                Location = "DatabaseService, ViewModels",
                Recommendation = "Use interfaces and consider breaking circular dependencies with events or mediator pattern"
            });

            return issues;
        }

        /// <summary>
        /// Identifies constructor issues that prevent proper DI.
        /// </summary>
        private List<DIIssue> IdentifyConstructorIssues()
        {
            var issues = new List<DIIssue>();

            issues.Add(new DIIssue
            {
                Type = DIIssueType.ConstructorIssue,
                Description = "Many classes have parameterless constructors that create dependencies manually",
                Severity = DIIssueSeverity.High,
                Location = "ViewModels, Services, Dialogs",
                Recommendation = "Remove parameterless constructors and use proper dependency injection"
            });

            issues.Add(new DIIssue
            {
                Type = DIIssueType.ConstructorIssue,
                Description = "Some constructors have fallback logic that creates services manually",
                Severity = DIIssueSeverity.Medium,
                Location = "Various classes",
                Recommendation = "Remove fallback service creation and ensure proper DI registration"
            });

            return issues;
        }

        /// <summary>
        /// Generates recommendations based on identified issues.
        /// </summary>
        private List<string> GenerateRecommendations(DIAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.ServiceLocatorIssues.Any())
            {
                recommendations.Add("1. Replace service locator patterns with constructor injection");
                recommendations.Add("2. Register all services in ServiceConfiguration.cs");
                recommendations.Add("3. Use interfaces for service contracts");
            }

            if (result.LifetimeIssues.Any())
            {
                recommendations.Add("4. Review and standardize service lifetimes");
                recommendations.Add("5. Use Scoped for business services, Singleton for stateless services, Transient for ViewModels");
            }

            if (result.MissingRegistrations.Any())
            {
                recommendations.Add("6. Register all missing services in DI container");
                recommendations.Add("7. Create interfaces for services that don't have them");
            }

            if (result.ConstructorIssues.Any())
            {
                recommendations.Add("8. Remove parameterless constructors from services and ViewModels");
                recommendations.Add("9. Use constructor injection for all dependencies");
                recommendations.Add("10. Remove manual service creation fallbacks");
            }

            recommendations.Add("11. Use DIValidationTest to verify service registration");
            recommendations.Add("12. Implement proper disposal patterns for services");

            return recommendations;
        }

        /// <summary>
        /// Gets specific recommendation for a service locator pattern.
        /// </summary>
        private string GetServiceLocatorRecommendation(string pattern)
        {
            return pattern switch
            {
                "App.ServiceProvider?.GetService" => "Inject service through constructor instead of using static service locator",
                "ServiceConfiguration.CreateServiceProvider()" => "Use existing service provider from DI container",
                "new DatabaseService()" => "Inject IDatabaseService through constructor",
                "new SimpleAlertService()" => "Inject IAlertService through constructor",
                "new ProductLookupService(" => "Inject IProductLookupService through constructor",
                _ => "Replace with proper constructor injection"
            };
        }
    }

    /// <summary>
    /// Result of DI configuration analysis.
    /// </summary>
    public class DIAnalysisResult
    {
        public List<DIIssue> ServiceLocatorIssues { get; set; } = new List<DIIssue>();
        public List<DIIssue> LifetimeIssues { get; set; } = new List<DIIssue>();
        public List<DIIssue> MissingRegistrations { get; set; } = new List<DIIssue>();
        public List<DIIssue> CircularDependencies { get; set; } = new List<DIIssue>();
        public List<DIIssue> ConstructorIssues { get; set; } = new List<DIIssue>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public string AnalysisError { get; set; }

        public int TotalIssueCount => ServiceLocatorIssues.Count + LifetimeIssues.Count + 
                                     MissingRegistrations.Count + CircularDependencies.Count + 
                                     ConstructorIssues.Count;
    }

    /// <summary>
    /// Represents a specific DI issue.
    /// </summary>
    public class DIIssue
    {
        public DIIssueType Type { get; set; }
        public string Description { get; set; }
        public DIIssueSeverity Severity { get; set; }
        public string Location { get; set; }
        public string Recommendation { get; set; }
    }

    /// <summary>
    /// Types of DI issues.
    /// </summary>
    public enum DIIssueType
    {
        ServiceLocatorAntiPattern,
        IncorrectLifetime,
        MissingRegistration,
        CircularDependency,
        ConstructorIssue
    }

    /// <summary>
    /// Severity levels for DI issues.
    /// </summary>
    public enum DIIssueSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
