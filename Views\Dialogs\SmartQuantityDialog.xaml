<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.SmartQuantityDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="450"
             Background="Transparent">
    
    <UserControl.Resources>
        <!-- Professional Dialog Styles -->
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="1" BlurRadius="4"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuggestionCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#000000" Opacity="0.15" ShadowDepth="2" BlurRadius="6"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
    </UserControl.Resources>
    
    <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
            UniformCornerRadius="12"
            md:ElevationAssist.Elevation="Dp6"
            MaxWidth="420"
            Margin="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    CornerRadius="12,12,0,0"
                    Padding="24,16">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF3F51B5" Offset="0"/>
                        <GradientStop Color="#FF303F9F" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,8">
                        <md:PackIcon Kind="Calculator"
                                    Width="24" Height="24"
                                    VerticalAlignment="Center"
                                    Foreground="{DynamicResource MaterialDesignPaper}"
                                    Margin="0,0,12,0"/>
                        <TextBlock Text="Smart Quantity Selection"
                                 FontSize="18"
                                 FontWeight="SemiBold"
                                 Foreground="{DynamicResource MaterialDesignPaper}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock x:Name="ProductNameText"
                             Text="Product Name"
                             FontSize="14"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             HorizontalAlignment="Center"
                             Opacity="0.9"/>
                </StackPanel>
            </Border>
            
            <!-- Content -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="0,0,8,0">
                <Grid Margin="24,20,24,24">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Current Selection -->
                    <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="Enter Quantity" 
                                     FontWeight="SemiBold"
                                     FontSize="14"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                     Margin="0,0,0,12"/>
                            
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="txtQuantity"
                                       Grid.Column="0"
                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                       md:HintAssist.Hint="Quantity"
                                       Text="{Binding SelectedQuantity, StringFormat=F3}"
                                       PreviewTextInput="DecimalValidation_PreviewTextInput"
                                       TextChanged="Quantity_TextChanged"
                                       FontSize="16"
                                       Margin="0,0,12,0"/>

                                <TextBlock Grid.Column="1"
                                         x:Name="UnitText"
                                         Text="units"
                                         VerticalAlignment="Center"
                                         FontSize="14"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Current Pricing -->
                    <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Current Pricing" 
                                         FontWeight="SemiBold"
                                         FontSize="14"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock x:Name="CurrentPricingText"
                                         Text="$1.00 each"
                                         FontSize="13"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         Margin="0,4,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                <TextBlock x:Name="CurrentTotalText"
                                         Text="$10.00"
                                         FontSize="18"
                                         FontWeight="SemiBold"
                                         HorizontalAlignment="Right"/>
                                <TextBlock x:Name="CurrentSavingsText"
                                         Text=""
                                         FontSize="12"
                                         Foreground="#4CAF50"
                                         HorizontalAlignment="Right"
                                         Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Bulk Pricing Suggestions -->
                    <Border Grid.Row="2" Style="{StaticResource SectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="Better Pricing Available" 
                                     FontWeight="SemiBold"
                                     FontSize="14"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                     Margin="0,0,0,12"/>
                            
                            <ScrollViewer Grid.Row="1" 
                                        MaxHeight="200"
                                        VerticalScrollBarVisibility="Auto"
                                        HorizontalScrollBarVisibility="Disabled">
                                <ItemsControl x:Name="SuggestionsPanel">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource SuggestionCardStyle}"
                                                  MouseLeftButtonUp="Suggestion_Click"
                                                  Tag="{Binding}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <StackPanel Grid.Column="0">
                                                        <TextBlock Text="{Binding TierName}" 
                                                                 FontWeight="Medium"
                                                                 FontSize="13"/>
                                                        <TextBlock Text="{Binding Description}" 
                                                                 FontSize="11"
                                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                 Margin="0,2,0,0"/>
                                                    </StackPanel>
                                                    
                                                    <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                                        <TextBlock Text="{Binding TotalSavings, StringFormat=Save {0:C2}}" 
                                                                 FontSize="12"
                                                                 FontWeight="Medium"
                                                                 Foreground="#4CAF50"
                                                                 HorizontalAlignment="Right"/>
                                                        <TextBlock Text="{Binding SavingsPercentage, StringFormat=({0:F1}% off)}" 
                                                                 FontSize="10"
                                                                 Foreground="#4CAF50"
                                                                 HorizontalAlignment="Right"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </Border>

                    <!-- Buttons -->
                    <Grid Grid.Row="3" Margin="0,16,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button x:Name="btnCancel"
                              Grid.Column="0"
                              Content="Cancel"
                              Style="{StaticResource SecondaryButtonStyle}"
                              Margin="0,0,8,0"
                              Click="Cancel_Click"/>

                        <Button x:Name="btnAddToCart"
                              Grid.Column="1"
                              Content="Add to Cart"
                              Style="{StaticResource PrimaryButtonStyle}"
                              Margin="8,0,0,0"
                              Click="AddToCart_Click"/>
                    </Grid>
                </Grid>
            </ScrollViewer>
        </Grid>
    </md:Card>
</UserControl>
