using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;

namespace POSSystem.Views.Dialogs
{
    public partial class StatsDetailsDialog : UserControl
    {
        private readonly StatsDetailsViewModel _viewModel;
        
        public StatsDetailsDialog(
            RefactoredDashboardViewModel dashboardViewModel,
            string statType)
        {
            InitializeComponent();
            var dbService = new DatabaseService();
            _viewModel = new StatsDetailsViewModel(dashboardViewModel, dbService, statType);
            DataContext = _viewModel;
            
            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }
        
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Close the dialog
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    DialogHost.Close("RootDialog");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error closing dialog: {ex.Message}");
            }
        }
    }
} 