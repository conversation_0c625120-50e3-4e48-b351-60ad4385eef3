using System;
using System.Reflection;
using Microsoft.Data.Sqlite;
using System.Diagnostics;

namespace POSSystem.Services
{
    /// <summary>
    /// Extension methods for database operations
    /// </summary>
    public static class DatabaseExtensions
    {
        /// <summary>
        /// Create an optimized SQLite connection with pooling enabled
        /// </summary>
        public static SqliteConnection CreateOptimizedConnection(string dbPath)
        {
            // Ensure connection string starts with "Data Source="
            string connectionString = !dbPath.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase)
                ? $"Data Source={dbPath}"
                : dbPath;
            
            // Add connection pooling and other optimizations
            if (!connectionString.Contains("Pooling="))
            {
                connectionString += ";Pooling=True";
            }
            
            return new SqliteConnection(connectionString);
        }
        
        /// <summary>
        /// Creates a command with sanitized SQL text to fix common typos
        /// </summary>
        public static SqliteCommand CreateSanitizedCommand(this SqliteConnection connection)
        {
            var command = connection.CreateCommand();
            
            // Add event handler to sanitize command text before execution
            // Since SqliteCommand doesn't have a direct event for this, we'll use a wrapper
            return new SanitizingSqliteCommand(command);
        }
        
        /// <summary>
        /// Wrapper for SqliteCommand that sanitizes SQL queries
        /// </summary>
        private class SanitizingSqliteCommand : SqliteCommand
        {
            private readonly SqliteCommand _innerCommand;
            private string _commandText;
            
            public SanitizingSqliteCommand(SqliteCommand innerCommand)
            {
                _innerCommand = innerCommand;
                Connection = innerCommand.Connection;
                Transaction = innerCommand.Transaction;
                CommandTimeout = innerCommand.CommandTimeout;
                DesignTimeVisible = innerCommand.DesignTimeVisible;
                UpdatedRowSource = innerCommand.UpdatedRowSource;
            }
            
            public override string CommandText
            {
                get => _commandText;
                set
                {
                    // Sanitize the SQL query
                    _commandText = SqlQuerySanitizer.SanitizeQuery(value);
                    _innerCommand.CommandText = _commandText;
                }
            }
            
            public override int ExecuteNonQuery()
            {
                EnsureCommandTextSanitized();
                return _innerCommand.ExecuteNonQuery();
            }
            
            public override object ExecuteScalar()
            {
                EnsureCommandTextSanitized();
                return _innerCommand.ExecuteScalar();
            }
            
            protected override System.Data.Common.DbDataReader ExecuteDbDataReader(System.Data.CommandBehavior behavior)
            {
                EnsureCommandTextSanitized();
                return _innerCommand.ExecuteReader(behavior);
            }
            
            private void EnsureCommandTextSanitized()
            {
                // Double-check that command text is sanitized before execution
                if (_innerCommand.CommandText != _commandText || _innerCommand.CommandText == null)
                {
                    _innerCommand.CommandText = SqlQuerySanitizer.SanitizeQuery(_innerCommand.CommandText);
                    _commandText = _innerCommand.CommandText;
                }
            }
        }
    }
}
