using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services.Memory
{
    /// <summary>
    /// ✅ CRITICAL MEMORY OPTIMIZATION: Advanced memory manager for preventing leaks and optimizing resource usage
    /// </summary>
    public class AdvancedMemoryManager : IDisposable
    {
        private readonly ILogger<AdvancedMemoryManager> _logger;
        private readonly Timer _memoryMonitorTimer;
        private readonly Timer _cleanupTimer;
        private readonly Dictionary<WeakReference, string> _trackedObjects;
        private readonly object _lockObject = new object();
        private long _lastMemoryUsage;
        private int _cleanupCycles;
        private bool _disposed;

	        // Lightweight activity ring buffer for correlating memory spikes
	        private const int ActivityBufferSize = 32;
	        private readonly string[] _recentActivities = new string[ActivityBufferSize];
	        private int _activityIndex = 0;
	        private readonly object _activityLock = new object();


        // ✅ PERFORMANCE FIX: Adjusted memory thresholds and intervals to reduce performance impact
        private const long MEMORY_WARNING_THRESHOLD_MB = 600; // Increased threshold
        private const long MEMORY_CRITICAL_THRESHOLD_MB = 900; // Increased threshold
        private const int CLEANUP_INTERVAL_MS = 120000; // 2 minutes (reduced frequency)
        private const int MONITOR_INTERVAL_MS = 30000; // 30 seconds (reduced frequency)

        public AdvancedMemoryManager(ILogger<AdvancedMemoryManager> logger = null)
        {
            _logger = logger;
            _trackedObjects = new Dictionary<WeakReference, string>();

            // Start memory monitoring
            _memoryMonitorTimer = new Timer(MonitorMemoryUsage, null, MONITOR_INTERVAL_MS, MONITOR_INTERVAL_MS);
            _cleanupTimer = new Timer(PerformScheduledCleanup, null, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS);

            Debug.WriteLine("✅ [MEMORY-MANAGER] Advanced Memory Manager initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Monitor memory usage and trigger cleanup when needed
        /// </summary>
        public void MarkActivity(string label)
        {
            if (string.IsNullOrWhiteSpace(label)) return;
            var timestamped = $"{DateTime.Now:HH:mm:ss} {label}";
            lock (_activityLock)
            {
                _recentActivities[_activityIndex % ActivityBufferSize] = timestamped;
                _activityIndex++;
            }
        }




        private void DumpRecentActivities(bool force = false)
        {
            // Only print if explicitly forced or there are recent entries
            string[] snapshot;
            lock (_activityLock)
            {
                snapshot = _recentActivities.ToArray();
            }
            if (!force && snapshot.All(string.IsNullOrEmpty)) return;

            Debug.WriteLine("[MEMORY-MONITOR] Recent activities:");
            foreach (var s in snapshot)
            {
                if (!string.IsNullOrEmpty(s)) Debug.WriteLine($"  • {s}");
            }
        }
        private void LogMemoryBreakdown(long currentMB)
        {
            try
            {
                var proc = Process.GetCurrentProcess();
                var ws = proc.WorkingSet64 / (1024 * 1024);
                var pws = proc.PrivateMemorySize64 / (1024 * 1024);
                var pm = proc.PagedMemorySize64 / (1024 * 1024);
                var npm = proc.NonpagedSystemMemorySize64 / (1024 * 1024);
                Debug.WriteLine($"[MEMORY-BREAKDOWN] WS={ws}MB, Private={pws}MB, Paged={pm}MB, NonPaged={npm}MB");
            }
            catch { }
        }



        /*
	        private void DumpRecentActivitiesIfSpike(long memoryDelta)
	        {
	            if (Math.Abs(memoryDelta) < 100) return;
	            string[] snapshot;
	            lock (_activityLock)
	            {

        public void MarkActivity(string label)
        {
            if (string.IsNullOrWhiteSpace(label)) return;
            var timestamped = $"{DateTime.Now:HH:mm:ss} {label}";
            lock (_activityLock)
            {
                _recentActivities[_activityIndex % ActivityBufferSize] = timestamped;
                _activityIndex++;
            }

        }

	                snapshot = _recentActivities.ToArray();
	            }
	            Debug.WriteLine("[MEMORY-MONITOR] Recent activities around spike:");
	            foreach (var s in snapshot)
	            {
	                if (!string.IsNullOrEmpty(s)) Debug.WriteLine($"  • {s}");
	            }

        */

        private void MonitorMemoryUsage(object state)
        {
            try
            {
                var currentMemory = GetCurrentMemoryUsageMB();
                var memoryDelta = currentMemory - _lastMemoryUsage;

                Debug.WriteLine($"[MEMORY-MONITOR] Current: {currentMemory}MB, Delta: {memoryDelta:+#;-#;0}MB");
                // Dump recent activities to correlate spike cause and log breakdown
                DumpRecentActivities(force: true);
                LogMemoryBreakdown(currentMemory);


                if (currentMemory > MEMORY_CRITICAL_THRESHOLD_MB)
                {
                    Debug.WriteLine($"🚨 [MEMORY-CRITICAL] Memory usage critical: {currentMemory}MB");

                    DumpRecentActivities(force: true);

                    _ = Task.Run(() => PerformEmergencyCleanup());
                }
                else if (currentMemory > MEMORY_WARNING_THRESHOLD_MB)
                {
                    // Also dump on warnings to catch gradual growth
                    DumpRecentActivities(force: true);
                    LogMemoryBreakdown(currentMemory);

                    Debug.WriteLine($"⚠️ [MEMORY-WARNING] Memory usage high: {currentMemory}MB");
                    _ = Task.Run(() => PerformStandardCleanup());
                }

                _lastMemoryUsage = currentMemory;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [MEMORY-MONITOR] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Perform scheduled cleanup to prevent memory accumulation
        /// </summary>
        private void PerformScheduledCleanup(object state)
        {
            try
            {
                _cleanupCycles++;
                // ✅ PERFORMANCE FIX: Reduce debug output frequency
                if (_cleanupCycles % 5 == 0) // Only log every 5th cleanup
                {
                    Debug.WriteLine($"[MEMORY-CLEANUP] Starting scheduled cleanup cycle #{_cleanupCycles}");
                }

                var beforeMemory = GetCurrentMemoryUsageMB();

                // Clean up tracked objects
                CleanupTrackedObjects();

                // Clean up bitmap caches
                CleanupBitmapCaches();

                // Force garbage collection every 5 cycles
                if (_cleanupCycles % 5 == 0)
                {
                    ForceGarbageCollection();
                }

                var afterMemory = GetCurrentMemoryUsageMB();
                var freed = beforeMemory - afterMemory;

                // ✅ PERFORMANCE FIX: Only log significant memory cleanup
                if (freed > 10 || _cleanupCycles % 5 == 0) // Only log if significant cleanup or every 5th cycle
                {
                    Debug.WriteLine($"[MEMORY-CLEANUP] Cycle #{_cleanupCycles} completed. Freed: {freed}MB");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [MEMORY-CLEANUP] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Emergency cleanup when memory usage is critical
        /// </summary>
        private async Task PerformEmergencyCleanup()
        {
            try
            {
                Debug.WriteLine("🚨 [EMERGENCY-CLEANUP] Starting emergency memory cleanup");

                var beforeMemory = GetCurrentMemoryUsageMB();

                // Aggressive cleanup
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    CleanupBitmapCaches();
                    ClearImageCaches();
                    CleanupUnusedResources();
                });

                // Force multiple GC cycles
                for (int i = 0; i < 3; i++)
                {
                    GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true);
                    GC.WaitForPendingFinalizers();
                    await Task.Delay(100);
                }

                var afterMemory = GetCurrentMemoryUsageMB();
                var freed = beforeMemory - afterMemory;

                Debug.WriteLine($"🚨 [EMERGENCY-CLEANUP] Completed. Freed: {freed}MB");

                if (freed < 50) // If we didn't free much memory, log a warning
                {
                    Debug.WriteLine("⚠️ [EMERGENCY-CLEANUP] Low memory recovery - potential memory leak detected");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [EMERGENCY-CLEANUP] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Standard cleanup routine
        /// </summary>
        private async Task PerformStandardCleanup()
        {
            try
            {
                Debug.WriteLine("[STANDARD-CLEANUP] Starting standard memory cleanup");

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    CleanupTrackedObjects();
                    CleanupBitmapCaches();
                });

                GC.Collect(0, GCCollectionMode.Optimized);

                Debug.WriteLine("[STANDARD-CLEANUP] Completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STANDARD-CLEANUP] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Clean up bitmap caches to free memory
        /// </summary>
        private void CleanupBitmapCaches()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Ensure bitmap cleanup runs on UI thread to prevent threading errors
                if (Application.Current?.Dispatcher != null)
                {
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        // Already on UI thread
                        var mainWindow = Application.Current?.MainWindow;
                        if (mainWindow != null)
                        {
                            CleanupBitmapCachesRecursive(mainWindow);
                        }
                    }
                    else
                    {
                        // Marshal to UI thread
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                var mainWindow = Application.Current?.MainWindow;
                                if (mainWindow != null)
                                {
                                    CleanupBitmapCachesRecursive(mainWindow);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"❌ [BITMAP-CLEANUP-UI] Error: {ex.Message}");
                            }
                        }), System.Windows.Threading.DispatcherPriority.Background);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [BITMAP-CLEANUP] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Recursively clean up bitmap caches in visual tree
        /// </summary>
        private void CleanupBitmapCachesRecursive(DependencyObject parent)
        {
            if (parent == null) return;

            try
            {
                // Clear bitmap cache if it exists
                if (parent is UIElement element && element.CacheMode is BitmapCache)
                {
                    element.CacheMode = null;
                }

                // Recursively process children
                int childCount = VisualTreeHelper.GetChildrenCount(parent);
                for (int i = 0; i < childCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    CleanupBitmapCachesRecursive(child);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [BITMAP-CLEANUP-RECURSIVE] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Clear image caches and reset BitmapImage objects
        /// </summary>
        private void ClearImageCaches()
        {
            try
            {
                // Clear WPF image cache
                var field = typeof(BitmapImage).GetField("s_cache",
                    System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);

                if (field?.GetValue(null) is System.Collections.IDictionary cache)
                {
                    cache.Clear();
                    Debug.WriteLine("[IMAGE-CACHE] Cleared BitmapImage cache");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [IMAGE-CACHE] Error clearing image cache: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Clean up unused application resources
        /// </summary>
        private void CleanupUnusedResources()
        {
            try
            {
                // Clean up resource dictionaries that are no longer needed
                var app = Application.Current;
                if (app?.Resources?.MergedDictionaries != null)
                {
                    var toRemove = app.Resources.MergedDictionaries
                        .Where(rd => rd != null && rd.Count == 0)
                        .ToList();

                    foreach (var emptyDict in toRemove)
                    {
                        app.Resources.MergedDictionaries.Remove(emptyDict);
                    }

                    if (toRemove.Count > 0)
                    {
                        Debug.WriteLine($"[RESOURCE-CLEANUP] Removed {toRemove.Count} empty resource dictionaries");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [RESOURCE-CLEANUP] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Clean up tracked objects that are no longer alive
        /// </summary>
        private void CleanupTrackedObjects()
        {
            lock (_lockObject)
            {
                try
                {
                    var deadReferences = _trackedObjects.Keys
                        .Where(wr => !wr.IsAlive)
                        .ToList();

                    foreach (var deadRef in deadReferences)
                    {
                        _trackedObjects.Remove(deadRef);
                    }

                    if (deadReferences.Count > 0)
                    {
                        Debug.WriteLine($"[TRACKED-OBJECTS] Cleaned up {deadReferences.Count} dead references");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [TRACKED-OBJECTS] Error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Force garbage collection with proper timing
        /// </summary>
        private void ForceGarbageCollection()
        {
            try
            {
                var beforeMemory = GetCurrentMemoryUsageMB();

                GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true);
                GC.WaitForPendingFinalizers();
                GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true);

                var afterMemory = GetCurrentMemoryUsageMB();
                var freed = beforeMemory - afterMemory;

                Debug.WriteLine($"[FORCE-GC] Forced garbage collection. Freed: {freed}MB");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [FORCE-GC] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ UTILITY: Get current memory usage in MB
        /// </summary>
        private long GetCurrentMemoryUsageMB()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return process.WorkingSet64 / (1024 * 1024);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Track an object for memory monitoring
        /// </summary>
        public void TrackObject(object obj, string description)
        {
            if (obj == null) return;

            lock (_lockObject)
            {
                try
                {
                    var weakRef = new WeakReference(obj);
                    _trackedObjects[weakRef] = description;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ [TRACK-OBJECT] Error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get memory usage statistics
        /// </summary>
        public (long CurrentMB, int TrackedObjects, int CleanupCycles) GetMemoryStats()
        {
            lock (_lockObject)
            {
                return (GetCurrentMemoryUsageMB(), _trackedObjects.Count, _cleanupCycles);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _memoryMonitorTimer?.Dispose();
                _cleanupTimer?.Dispose();

                lock (_lockObject)
                {
                    _trackedObjects.Clear();
                }

                Debug.WriteLine("✅ [MEMORY-MANAGER] Advanced Memory Manager disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [MEMORY-MANAGER] Disposal error: {ex.Message}");
            }

            _disposed = true;
        }
    }
}
