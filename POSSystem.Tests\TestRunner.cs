using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using System.Diagnostics;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test runner for executing and reporting on unit tests
    /// </summary>
    public class TestRunner
    {
        private readonly ILogger<TestRunner> _logger;
        private readonly ServiceProvider _serviceProvider;

        public TestRunner()
        {
            _serviceProvider = TestConfiguration.CreateTestServiceProvider();
            _logger = _serviceProvider.GetRequiredService<ILogger<TestRunner>>();
        }

        /// <summary>
        /// Run all unit tests and return results
        /// </summary>
        public async Task<TestResults> RunAllTestsAsync()
        {
            var results = new TestResults();
            var stopwatch = Stopwatch.StartNew();

            _logger.LogInformation("Starting comprehensive unit test execution...");

            try
            {
                // Run different test categories
                await RunDataAccessTests(results);
                await RunBusinessLogicTests(results);
                await RunValidationTests(results);
                await RunPerformanceTests(results);

                stopwatch.Stop();
                results.TotalExecutionTime = stopwatch.Elapsed;
                results.Success = results.FailedTests.Count == 0;

                _logger.LogInformation("Unit test execution completed in {Duration}ms. " +
                    "Passed: {Passed}, Failed: {Failed}, Total: {Total}",
                    results.TotalExecutionTime.TotalMilliseconds,
                    results.PassedTests.Count,
                    results.FailedTests.Count,
                    results.TotalTests);

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during test execution");
                results.Success = false;
                results.TotalExecutionTime = stopwatch.Elapsed;
                results.FailedTests.Add(new TestResult
                {
                    TestName = "TestRunner.RunAllTestsAsync",
                    ErrorMessage = ex.Message,
                    Duration = stopwatch.Elapsed
                });
                return results;
            }
        }

        private async Task RunDataAccessTests(TestResults results)
        {
            _logger.LogInformation("Running data access tests...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<POSDbContext>();
                await TestConfiguration.SeedTestDataAsync(context);

                // Test UnifiedDataService
                await RunUnifiedDataServiceTests(scope, results);

                // Test ProductManagementService
                await RunProductManagementTests(scope, results);

                _logger.LogInformation("Data access tests completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in data access tests");
                results.FailedTests.Add(new TestResult
                {
                    TestName = "DataAccessTests",
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task RunUnifiedDataServiceTests(IServiceScope scope, TestResults results)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var unifiedService = scope.ServiceProvider.GetRequiredService<UnifiedDataService>();

                // Test GetProductsAsync
                var products = await unifiedService.GetProductsAsync(1, 10);
                if (products != null && products.Any())
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "UnifiedDataService.GetProductsAsync",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "UnifiedDataService.GetProductsAsync",
                        ErrorMessage = "No products returned",
                        Duration = stopwatch.Elapsed
                    });
                }

                stopwatch.Restart();

                // Test SearchProductsAsync
                var searchResults = await unifiedService.SearchProductsAsync("Laptop", 10);
                if (searchResults != null)
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "UnifiedDataService.SearchProductsAsync",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "UnifiedDataService.SearchProductsAsync",
                        ErrorMessage = "Search returned null",
                        Duration = stopwatch.Elapsed
                    });
                }
            }
            catch (Exception ex)
            {
                results.FailedTests.Add(new TestResult
                {
                    TestName = "UnifiedDataService Tests",
                    ErrorMessage = ex.Message,
                    Duration = stopwatch.Elapsed
                });
            }
        }

        private async Task RunProductManagementTests(IServiceScope scope, TestResults results)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var productService = scope.ServiceProvider.GetRequiredService<ProductManagementService>();

                // Test GetAllProductsAsync
                var products = await productService.GetAllProductsAsync();
                if (products != null && products.Any())
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "ProductManagementService.GetAllProductsAsync",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "ProductManagementService.GetAllProductsAsync",
                        ErrorMessage = "No products returned",
                        Duration = stopwatch.Elapsed
                    });
                }

                stopwatch.Restart();

                // Test AddProductAsync
                var newProduct = new POSSystem.Models.Product
                {
                    Name = "Test Product",
                    SKU = "TEST001",
                    CategoryId = 1,
                    SellingPrice = 10.00m,
                    IsActive = true
                };

                var productId = await productService.AddProductAsync(newProduct);
                if (productId > 0)
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "ProductManagementService.AddProductAsync",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "ProductManagementService.AddProductAsync",
                        ErrorMessage = "Product ID not returned",
                        Duration = stopwatch.Elapsed
                    });
                }
            }
            catch (Exception ex)
            {
                results.FailedTests.Add(new TestResult
                {
                    TestName = "ProductManagementService Tests",
                    ErrorMessage = ex.Message,
                    Duration = stopwatch.Elapsed
                });
            }
        }

        private async Task RunBusinessLogicTests(TestResults results)
        {
            _logger.LogInformation("Running business logic tests...");

            try
            {
                // Test sales calculations
                RunSalesCalculationTests(results);

                _logger.LogInformation("Business logic tests completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in business logic tests");
                results.FailedTests.Add(new TestResult
                {
                    TestName = "BusinessLogicTests",
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }

            await Task.CompletedTask;
        }

        private void RunSalesCalculationTests(TestResults results)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Test subtotal calculation
                var sale = new POSSystem.Models.Sale
                {
                    Items = new List<POSSystem.Models.SaleItem>
                    {
                        new POSSystem.Models.SaleItem { Quantity = 2, UnitPrice = 10.50m },
                        new POSSystem.Models.SaleItem { Quantity = 1, UnitPrice = 25.99m }
                    }
                };

                var subtotal = sale.Items.Sum(item => item.Quantity * item.UnitPrice);
                var expectedSubtotal = 46.99m; // (2*10.50) + (1*25.99)

                if (subtotal == expectedSubtotal)
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "SalesCalculation.SubtotalCalculation",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "SalesCalculation.SubtotalCalculation",
                        ErrorMessage = $"Expected {expectedSubtotal}, got {subtotal}",
                        Duration = stopwatch.Elapsed
                    });
                }

                stopwatch.Restart();

                // Test tax calculation
                var taxRate = 0.08m;
                var taxAmount = subtotal * taxRate;
                var expectedTax = 3.7592m;

                if (Math.Abs(taxAmount - expectedTax) < 0.01m)
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "SalesCalculation.TaxCalculation",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "SalesCalculation.TaxCalculation",
                        ErrorMessage = $"Expected {expectedTax}, got {taxAmount}",
                        Duration = stopwatch.Elapsed
                    });
                }
            }
            catch (Exception ex)
            {
                results.FailedTests.Add(new TestResult
                {
                    TestName = "SalesCalculation Tests",
                    ErrorMessage = ex.Message,
                    Duration = stopwatch.Elapsed
                });
            }
        }

        private async Task RunValidationTests(TestResults results)
        {
            _logger.LogInformation("Running validation tests...");

            try
            {
                // Test model validation
                RunModelValidationTests(results);

                _logger.LogInformation("Validation tests completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in validation tests");
                results.FailedTests.Add(new TestResult
                {
                    TestName = "ValidationTests",
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }

            await Task.CompletedTask;
        }

        private void RunModelValidationTests(TestResults results)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Test valid product
                var validProduct = new POSSystem.Models.Product
                {
                    Name = "Valid Product",
                    SKU = "VALID001",
                    CategoryId = 1,
                    SellingPrice = 10.00m,
                    IsActive = true
                };

                // In a real scenario, you would use validation attributes
                // For this test, we'll just check basic properties
                if (!string.IsNullOrWhiteSpace(validProduct.Name) && 
                    !string.IsNullOrWhiteSpace(validProduct.SKU) &&
                    validProduct.SellingPrice >= 0)
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "ModelValidation.ValidProduct",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "ModelValidation.ValidProduct",
                        ErrorMessage = "Valid product failed validation",
                        Duration = stopwatch.Elapsed
                    });
                }
            }
            catch (Exception ex)
            {
                results.FailedTests.Add(new TestResult
                {
                    TestName = "ModelValidation Tests",
                    ErrorMessage = ex.Message,
                    Duration = stopwatch.Elapsed
                });
            }
        }

        private async Task RunPerformanceTests(TestResults results)
        {
            _logger.LogInformation("Running performance tests...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<POSDbContext>();
                await TestConfiguration.SeedTestDataAsync(context);

                var unifiedService = scope.ServiceProvider.GetRequiredService<UnifiedDataService>();

                // Test query performance
                var stopwatch = Stopwatch.StartNew();
                var products = await unifiedService.GetProductsAsync(1, 100);
                stopwatch.Stop();

                if (stopwatch.ElapsedMilliseconds < 1000) // Should complete within 1 second
                {
                    results.PassedTests.Add(new TestResult
                    {
                        TestName = "Performance.GetProductsAsync",
                        Duration = stopwatch.Elapsed
                    });
                }
                else
                {
                    results.FailedTests.Add(new TestResult
                    {
                        TestName = "Performance.GetProductsAsync",
                        ErrorMessage = $"Query took {stopwatch.ElapsedMilliseconds}ms, expected < 1000ms",
                        Duration = stopwatch.Elapsed
                    });
                }

                _logger.LogInformation("Performance tests completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in performance tests");
                results.FailedTests.Add(new TestResult
                {
                    TestName = "PerformanceTests",
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }

    /// <summary>
    /// Test execution results
    /// </summary>
    public class TestResults
    {
        public List<TestResult> PassedTests { get; set; } = new();
        public List<TestResult> FailedTests { get; set; } = new();
        public TimeSpan TotalExecutionTime { get; set; }
        public bool Success { get; set; }
        public int TotalTests => PassedTests.Count + FailedTests.Count;

        public override string ToString()
        {
            return $"Test Results: {PassedTests.Count} passed, {FailedTests.Count} failed, " +
                   $"{TotalTests} total. Execution time: {TotalExecutionTime.TotalMilliseconds:F2}ms";
        }
    }

    /// <summary>
    /// Individual test result
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public string ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Passed => string.IsNullOrEmpty(ErrorMessage);
    }
}
