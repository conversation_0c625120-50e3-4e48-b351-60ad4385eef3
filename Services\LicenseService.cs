using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Management;
using System.Linq;
using System.Text.Json;
using System.Windows;
using System.Reflection;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class LicenseService : ILicenseService
    {
        // Base directory paths
        private readonly string _baseDirectory;
        private readonly string _appDataDirectory;
        
        // Subfolder where license files will be stored
        private const string LICENSE_FOLDER_NAME = "AppData";
        
        // Embedded seed values - fragments distributed in the code
        private static readonly byte[] _seedPart1 = { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64 };
        private static readonly byte[] _seedPart2 = { 0x76, 0x65, 0x64, 0x65, 0x76, 0x20, 0x31, 0x32 };
        
        // Assembly information used for key generation
        private const string ASSEMBLY_SEED = "POSSystem_v1.0";
        
        // Salt for key derivation - still needs to match between POS and License Generator
        private const string KEY_DERIVATION_SALT = "POSSystem2024";
        
        // Constants for license generation/validation
        private const int KEY_ITERATION_COUNT = 10000; // Higher iteration count increases security
        private const int KEY_SIZE_BYTES = 32; // For AES-256
        
        // Time tracking constants
        private const string TIME_TRACKING_SUFFIX = "time"; // Suffix for time tracking file
        private const int ALLOWED_TIME_DRIFT_MINUTES = 60; // Allow 1 hour of clock drift
        private const int MAX_USAGE_WITHOUT_ADVANCE = 30; // Maximum uses without time advancing
        
        // Anti-tampering constants
        private const string INTEGRITY_FILE_SUFFIX = "integrity"; // Suffix for integrity file
        private const int INTEGRITY_CHECK_INTERVAL_MS = 30000; // Check every 30 seconds
        
        // Flag to track if tampering was detected
        private bool _tamperingDetected = false;
        private readonly object _tamperLock = new object();
        private CancellationTokenSource _tamperDetectionCts;
        
        // P/Invoke declarations for anti-debugging
        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsDebuggerPresent();
        
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, [MarshalAs(UnmanagedType.Bool)] ref bool isDebuggerPresent);
        
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetCurrentProcess();
        
        // Add a field for development mode
        private bool _isDevelopmentMode = false;
        
        // Check if development mode is enabled
        private bool CheckDevelopmentMode()
        {
            try
            {
                Debug.WriteLine("=== LICENSESERVICE DEVELOPMENT MODE DETECTION ===");

                // STEP 1: Check for force-disable mechanisms first (highest priority)

                // Check 1: Check for dev_mode_disabled file (highest priority)
                string devModeDisabledPath = Path.Combine(_baseDirectory, "dev_mode_disabled");
                Debug.WriteLine($"Checking for dev_mode_disabled file at: {devModeDisabledPath}");
                if (File.Exists(devModeDisabledPath))
                {
                    Debug.WriteLine("Development mode disabled: dev_mode_disabled file exists");
                    return false;
                }

                // Check 2: Check for App.config setting (config override)
                try
                {
                    string devModeDisabledSetting = System.Configuration.ConfigurationManager.AppSettings["DevModeDisabled"];
                    Debug.WriteLine($"App.config DevModeDisabled setting: '{devModeDisabledSetting}'");
                    if (!string.IsNullOrEmpty(devModeDisabledSetting) &&
                        (devModeDisabledSetting.ToLower() == "true" || devModeDisabledSetting == "1"))
                    {
                        Debug.WriteLine("Development mode disabled: DevModeDisabled app setting is true");
                        return false;
                    }
                }
                catch (Exception configEx)
                {
                    Debug.WriteLine($"Error reading App.config: {configEx.Message}");
                }

                // STEP 2: Now check for enabling mechanisms
                Debug.WriteLine("No disable mechanisms found - checking for enable mechanisms...");
                bool isDev = false;

                // Method 1: Check for a development mode file in the application directory
                string devModeFilePath = Path.Combine(_baseDirectory, "dev_mode.txt");
                Debug.WriteLine($"Checking for dev_mode.txt file at: {devModeFilePath}");
                if (File.Exists(devModeFilePath))
                {
                    string content = File.ReadAllText(devModeFilePath).Trim();
                    Debug.WriteLine($"dev_mode.txt file exists with content: '{content}'");
                    if (content.ToLower() == "true")
                    {
                        isDev = true;
                        Debug.WriteLine("Development mode enabled via dev_mode.txt");
                    }
                }

                // Method 2: Check for an environment variable
                string envVar = Environment.GetEnvironmentVariable("POS_DEVELOPMENT_MODE");
                Debug.WriteLine($"Environment variable POS_DEVELOPMENT_MODE: '{envVar}'");
                if (!string.IsNullOrEmpty(envVar) && (envVar.ToLower() == "true" || envVar == "1"))
                {
                    isDev = true;
                    Debug.WriteLine("Development mode enabled via environment variable");
                }

                // Method 3: Check debug compilation
                #if DEBUG
                isDev = true;
                Debug.WriteLine("DEBUG build detected - development mode enabled");
                #else
                Debug.WriteLine("RELEASE build detected");
                #endif

                Debug.WriteLine($"Final development mode result: {isDev}");

                // If in development mode, handle cleanup of integrity files
                if (isDev)
                {
                    // Reset integrity file on application startup in dev mode
                    Task.Run(() => ResetIntegrityFileIfNeeded());
                }

                Debug.WriteLine("=== END LICENSESERVICE DEVELOPMENT MODE DETECTION ===");
                return isDev;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CheckDevelopmentMode: {ex.Message}");
                return false;
            }
        }
        
        // Reset the integrity file to prevent checksum mismatches in development
        private void ResetIntegrityFileIfNeeded()
        {
            try 
            {
                // Get the integrity file path first (before possibly deleting it)
                string integrityFilePath = GetIntegrityFilePath();
                
                // Check if file exists and try to delete it
                if (File.Exists(integrityFilePath))
                {
                    Debug.WriteLine("Development mode: Removing outdated integrity file");
                    File.Delete(integrityFilePath);
                }
                
                // Calculate and save new integrity data
                string hash = CalculateAssemblyHash();
                DateTime now = DateTime.UtcNow;
                SaveIntegrityData(hash, now);
                
                Debug.WriteLine("Development mode: Created fresh integrity file");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting integrity file: {ex.Message}");
            }
        }
        
        // Constructor to initialize base paths
        public LicenseService()
        {
            _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _appDataDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "POSSystem");
                
            // Create the app data directory if it doesn't exist
            EnsureDirectoriesExist();
            
            // Check if running in development mode
            _isDevelopmentMode = CheckDevelopmentMode();
            
            // Initialize anti-tampering
            InitializeAntiTampering();
            
            // Initialize license watermarking
            InitializeLicenseWatermarking();
            
            // Initialize runtime memory protection if not in development mode
            if (!_isDevelopmentMode)
            {
                InitializeRuntimeMemoryProtection();
            }
            
            Debug.WriteLine($"License service initialized. Development mode: {_isDevelopmentMode}");
        }
        
        // Variables for memory protection
        private CancellationTokenSource _memoryProtectionCts;
        private LicenseInfo _inMemoryLicenseSnapshot;
        private readonly object _memoryProtectionLock = new object();
        
        // Initialize memory protection
        private void InitializeRuntimeMemoryProtection()
        {
            try
            {
                _memoryProtectionCts = new CancellationTokenSource();
                
                // Create initial snapshot of license data for comparison
                UpdateLicenseSnapshot();
                
                // Start the background task
                Task.Run(() => RunPeriodicMemoryChecks(_memoryProtectionCts.Token));
                
                Debug.WriteLine("Runtime memory protection initialized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing memory protection: {ex.Message}");
            }
        }
        
        // Update the in-memory snapshot of license data
        private void UpdateLicenseSnapshot()
        {
            try
            {
                if (!File.Exists(GetLicenseFilePath()))
                    return;
                    
                lock (_memoryProtectionLock)
                {
                    // Load license from disk
                    var license = LoadLicense();
                    if (license == null)
                        return;
                        
                    // Create a deep copy to isolate it from any memory modifications
                    var json = JsonSerializer.Serialize(license);
                    _inMemoryLicenseSnapshot = JsonSerializer.Deserialize<LicenseInfo>(json);
                    
                    // Calculate a checksum of the license data for later verification
                    string licenseHash = CalculateLicenseDataHash(license);
                    StoreMethodHash("MemoryLicenseHash", licenseHash);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating license snapshot: {ex.Message}");
            }
        }
        
        // Calculate a hash of license data
        private string CalculateLicenseDataHash(LicenseInfo license)
        {
            if (license == null)
                return string.Empty;
                
            try
            {
                var dataToHash = new StringBuilder();
                dataToHash.Append(license.BusinessName ?? string.Empty);
                dataToHash.Append("|");
                dataToHash.Append(license.LicenseKey ?? string.Empty);
                dataToHash.Append("|");
                dataToHash.Append(license.ExpirationDate.Ticks);
                dataToHash.Append("|");
                dataToHash.Append(license.Type.ToString());
                dataToHash.Append("|");
                dataToHash.Append(license.TerminalCount);
                dataToHash.Append("|");
                dataToHash.Append(license.HardwareId ?? string.Empty);
                
                using (var sha = SHA256.Create())
                {
                    byte[] hash = sha.ComputeHash(Encoding.UTF8.GetBytes(dataToHash.ToString()));
                    return Convert.ToBase64String(hash);
                }
            }
            catch
            {
                return string.Empty;
            }
        }
        
        // Run periodic memory checks
        private async Task RunPeriodicMemoryChecks(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Random delay between checks (1-30 seconds) to make it less predictable
                    int delayMs = new Random().Next(1000, 30000);
                    await Task.Delay(delayMs, cancellationToken);
                    
                    // Verify license data integrity
                    if (!VerifyLicenseMemoryIntegrity())
                    {
                        // If license data in memory has been tampered with, take action
                        await TamperingDetectedInMemory();
                    }
                }
                catch (TaskCanceledException)
                {
                    // Task was canceled, exit gracefully
                    break;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error in periodic memory check: {ex.Message}");
                    // Wait before retrying
                    await Task.Delay(5000, cancellationToken);
                }
            }
        }
        
        // Verify license data hasn't been tampered with in memory
        private bool VerifyLicenseMemoryIntegrity()
        {
            try
            {
                // Skip check if license doesn't exist or we're in development mode
                if (_isDevelopmentMode || !File.Exists(GetLicenseFilePath()))
                    return true;
                    
                lock (_memoryProtectionLock)
                {
                    // If we don't have a snapshot, create one
                    if (_inMemoryLicenseSnapshot == null)
                    {
                        UpdateLicenseSnapshot();
                        return true;
                    }
                    
                    // Load current license from disk for comparison
                    var license = LoadLicense();
                    if (license == null)
                        return false;
                        
                    // Check for signs of tampering
                    
                    // 1. Compare expiration date - this is a common target for hackers
                    if (license.ExpirationDate.Date != _inMemoryLicenseSnapshot.ExpirationDate.Date)
                    {
                        Debug.WriteLine("Memory tampering detected: Expiration date modified");
                        return false;
                    }
                    
                    // 2. Compare license type - hackers might upgrade license tier
                    if (license.Type != _inMemoryLicenseSnapshot.Type)
                    {
                        Debug.WriteLine("Memory tampering detected: License type modified");
                        return false;
                    }
                    
                    // 3. Compare terminal count - hackers might increase allowed terminals
                    if (license.TerminalCount != _inMemoryLicenseSnapshot.TerminalCount)
                    {
                        Debug.WriteLine("Memory tampering detected: Terminal count modified");
                        return false;
                    }
                    
                    // 4. Verify license key and business name haven't changed
                    if (license.LicenseKey != _inMemoryLicenseSnapshot.LicenseKey ||
                        license.BusinessName != _inMemoryLicenseSnapshot.BusinessName)
                    {
                        Debug.WriteLine("Memory tampering detected: License key or business name modified");
                        return false;
                    }
                    
                    // 5. Compare license hash
                    string storedHash = GetStoredMethodHash("MemoryLicenseHash");
                    string currentHash = CalculateLicenseDataHash(license);
                    
                    if (!string.IsNullOrEmpty(storedHash) && !string.IsNullOrEmpty(currentHash) && 
                        storedHash != currentHash)
                    {
                        Debug.WriteLine("Memory tampering detected: License data hash mismatch");
                        return false;
                    }
                    
                    // 6. Check license data consistency
                    if (!ValidateLicenseKeyFormat(license.LicenseKey))
                    {
                        Debug.WriteLine("Memory tampering detected: License key validation failed");
                        return false;
                    }
                    
                    // Periodically update our snapshot (every ~10 checks based on random)
                    if (new Random().Next(10) == 0)
                    {
                        UpdateLicenseSnapshot();
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in license memory integrity check: {ex.Message}");
                return false;
            }
        }
        
        // Handle in-memory tampering detection
        private async Task TamperingDetectedInMemory()
        {
            try
            {
                Debug.WriteLine("Memory tampering detected - taking action");
                
                // 1. Update tamper status
                SetTamperingDetected("License memory tampering");
                
                // 2. Reset license data from disk
                UpdateLicenseSnapshot();
                
                // 3. Randomly choose additional counter-measures to make bypass harder
                var rand = new Random();
                int action = rand.Next(3);
                
                switch (action)
                {
                    case 0:
                        // Delay response to make it harder to trace
                        await Task.Delay(rand.Next(1000, 5000));
                        break;
                        
                    case 1:
                        // Force garbage collection to clear memory
                        GC.Collect(2, GCCollectionMode.Forced);
                        break;
                        
                    case 2:
                        // Re-validate license
                        ValidateLicense();
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling memory tampering: {ex.Message}");
            }
        }
        
        // Initialize anti-tampering protections
        private void InitializeAntiTampering()
        {
            try
            {
                // Create an initial integrity file if not exists
                string integrityFile = GetIntegrityFilePath();
                if (!File.Exists(integrityFile))
                {
                    SaveIntegrityData(CalculateAssemblyHash(), DateTime.UtcNow);
                }
                
                // Start background integrity checks
                _tamperDetectionCts = new CancellationTokenSource();
                Task.Run(() => RunPeriodicIntegrityChecks(_tamperDetectionCts.Token));
                
                // Perform an initial tampering check at startup
                CheckForTampering();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing anti-tampering: {ex.Message}");
            }
        }
        
        // Get integrity file path
        private string GetIntegrityFilePath()
        {
            string fileNameBase = GetFileNameFromHardware(INTEGRITY_FILE_SUFFIX);
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.dat");
        }
        
        // Calculate hash of the application assembly for integrity verification
        private string CalculateAssemblyHash()
        {
            try
            {
                // Get the main assembly
                var assembly = Assembly.GetExecutingAssembly();
                string assemblyPath = assembly.Location;
                
                // Calculate SHA-256 hash of the assembly file
                using (var sha = SHA256.Create())
                using (var stream = new FileStream(assemblyPath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    byte[] hash = sha.ComputeHash(stream);
                    return Convert.ToBase64String(hash);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating assembly hash: {ex.Message}");
                return string.Empty;
            }
        }
        
        // Save integrity data
        private void SaveIntegrityData(string assemblyHash, DateTime timestamp)
        {
            try
            {
                var data = new 
                {
                    AssemblyHash = assemblyHash,
                    Timestamp = timestamp,
                    IntegrityChecksum = CalculateIntegrityChecksum(assemblyHash, timestamp)
                };
                
                var json = JsonSerializer.Serialize(data);
                var encrypted = EncryptString(json);
                File.WriteAllText(GetIntegrityFilePath(), encrypted);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving integrity data: {ex.Message}");
            }
        }
        
        // Calculate integrity checksum
        private string CalculateIntegrityChecksum(string assemblyHash, DateTime timestamp)
        {
            string input = $"{assemblyHash}|{timestamp.Ticks}|{KEY_DERIVATION_SALT}";
            using (var hmac = new HMACSHA256(GetEncryptionKey()))
            {
                byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToBase64String(hash);
            }
        }
        
        // Load integrity data
        private (string AssemblyHash, DateTime Timestamp, string Checksum) LoadIntegrityData()
        {
            try
            {
                string filePath = GetIntegrityFilePath();
                
                if (!File.Exists(filePath))
                {
                    string hash = CalculateAssemblyHash();
                    DateTime now = DateTime.UtcNow;
                    SaveIntegrityData(hash, now);
                    return (hash, now, CalculateIntegrityChecksum(hash, now));
                }
                
                var encrypted = File.ReadAllText(filePath);
                var json = DecryptString(encrypted);
                
                var data = JsonSerializer.Deserialize<JsonElement>(json);
                
                string assemblyHash = data.GetProperty("AssemblyHash").GetString();
                DateTime timestamp = data.GetProperty("Timestamp").GetDateTime();
                string checksum = data.GetProperty("IntegrityChecksum").GetString();
                
                return (assemblyHash, timestamp, checksum);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading integrity data: {ex.Message}");
                return (string.Empty, DateTime.UtcNow, string.Empty);
            }
        }
        
        // Check for tampering
        private bool CheckForTampering()
        {
            // If tampering was already detected, no need to check again
            if (_tamperingDetected)
                return true;
                
            try
            {
                // Check 1: Debugger detection
                if (IsDebuggerDetected())
                {
                    SetTamperingDetected("Debugger detected");
                    return true;
                }
                
                // Check 2: Assembly integrity
                var (storedHash, timestamp, storedChecksum) = LoadIntegrityData();
                
                // Verify checksum first to detect integrity file tampering
                string expectedChecksum = CalculateIntegrityChecksum(storedHash, timestamp);
                if (storedChecksum != expectedChecksum)
                {
                    SetTamperingDetected("Integrity file checksum mismatch");
                    return true;
                }
                
                // Calculate current assembly hash and compare
                string currentHash = CalculateAssemblyHash();
                if (storedHash != currentHash)
                {
                    SetTamperingDetected("Assembly file modified");
                    return true;
                }
                
                // Check 3: Verify runtime code integrity
                if (!VerifyRuntimeCodeIntegrity())
                {
                    SetTamperingDetected("Runtime code integrity check failed");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in tampering check: {ex.Message}");
                return false; // Don't flag as tampered if we can't perform the check
            }
        }
        
        // Set the tampering detected flag and take appropriate actions
        private void SetTamperingDetected(string reason)
        {
            lock (_tamperLock)
            {
                if (!_tamperingDetected)
                {
                    _tamperingDetected = true;
                    System.Diagnostics.Debug.WriteLine($"TAMPERING DETECTED: {reason}");
                    
                    // Security response - you can customize this based on your needs
                    // Options include: corrupting license data, shutting down, or silent reporting
                    
                    // Let's handle tampering gracefully but effectively
                    Task.Run(() => 
                    {
                        // Delay the response slightly to make it less obvious
                        Thread.Sleep(2000 + new Random().Next(2000));
                        
                        // Report tampering through event
                        TamperingResponseAction(reason);
                    });
                }
            }
        }
        
        // Periodic integrity check task
        private async Task RunPeriodicIntegrityChecks(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Delay with random variation to make it less predictable
                    int delayMs = INTEGRITY_CHECK_INTERVAL_MS + new Random().Next(-5000, 5000);
                    await Task.Delay(Math.Max(5000, delayMs), cancellationToken);
                    
                    // Run the check
                    CheckForTampering();
                }
                catch (TaskCanceledException)
                {
                    // Task was canceled, exit gracefully
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in periodic integrity check: {ex.Message}");
                    // Wait a bit and continue
                    await Task.Delay(5000, cancellationToken);
                }
            }
        }
        
        // Actions to take when tampering is detected
        private void TamperingResponseAction(string reason)
        {
            try
            {
                // If in development mode, just log the tampering but don't take action
                if (_isDevelopmentMode)
                {
                    // Show informative message in development mode
                    var dispatcherDev = Application.Current?.Dispatcher;
                    if (dispatcherDev != null)
                    {
                        dispatcherDev.BeginInvoke(() =>
                        {
                            MessageBox.Show(
                                $"DEVELOPMENT MODE: Tampering detection ({reason}) was bypassed. In production, this would prevent the application from running.",
                                "Security Alert - DEV MODE",
                                MessageBoxButton.OK,
                                MessageBoxImage.Warning);
                        });
                    }
                    
                    Debug.WriteLine($"[DEV MODE] Tampering detected: {reason}");
                    return;
                }

                // In production mode, take real action
                // Approach 1: Corrupt the license in a way that's not immediately obvious
                var license = LoadLicense();
                if (license != null)
                {
                    // Subtly modify the license to invalidate it
                    // This is more effective than deleting it outright
                    license.ExpirationDate = DateTime.UtcNow.AddDays(-1); // Expired yesterday
                    SaveLicense(license);
                }
                
                // Approach 2: For a more user-friendly approach, you could show a warning
                var dispatcher = Application.Current?.Dispatcher;
                if (dispatcher != null)
                {
                    dispatcher.BeginInvoke(() =>
                    {
                        MessageBox.Show(
                            "Application integrity issue detected. Please reinstall the application.",
                            "Security Alert",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    });
                }
                
                // Log the tampering event
                System.Diagnostics.Debug.WriteLine($"Tampering response executed: {reason}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in tampering response: {ex.Message}");
            }
        }
        
        // Check for debugger (both managed and native)
        private bool IsDebuggerDetected()
        {
            try
            {
                // If development mode is enabled, bypass debugger detection
                if (_isDevelopmentMode)
                    return false;

                // ---- Basic debugger detection (existing code) ----
                
                // Check for attached managed debugger
                if (Debugger.IsAttached)
                    return true;
                
                // Check for native debugger using Windows API
                if (IsDebuggerPresent())
                    return true;
                
                // Check for remote debugger
                bool isRemoteDebuggerPresent = false;
                if (CheckRemoteDebuggerPresent(GetCurrentProcess(), ref isRemoteDebuggerPresent) && isRemoteDebuggerPresent)
                    return true;
                
                // ---- Advanced detection techniques ----
                
                // Timing-based detection - code will run significantly slower under a debugger
                if (DetectDebuggerByTiming())
                    return true;
                
                // Memory signature detection - look for breakpoints and debugger structures
                if (DetectDebuggerByMemorySignatures())
                    return true;
                
                // Process window detection - many debuggers create specific window classes
                if (DetectDebuggerByWindowClasses())
                    return true;
                
                // Parent process detection - debuggers often run as parent processes
                if (DetectDebuggerByParentProcess())
                    return true;
                
                // ---- Environment check (existing code) ----
                
                // Check debugger behaviors and common debugging tools
                if (Environment.GetEnvironmentVariable("COMPLUS_ZapDisable") == "1" ||
                    Environment.GetEnvironmentVariable("COR_ENABLE_PROFILING") == "1")
                    return true;
                
                // ---- Process detection (existing code with enhancements) ----
                
                // Enhanced process detection with more comprehensive list
                var debuggerProcessNames = new[] { 
                    "dnspy", "ilspy", "ida", "ida64", "ollydbg", "x32dbg", "x64dbg", 
                    "windbg", "cheatengine", "de4dot", "ghidra", "hxd", "dotpeek",
                    "procmon", "scylla", "pesieve", "process hacker"
                };
                
                var runningProcesses = Process.GetProcesses()
                    .Select(p => p.ProcessName.ToLower())
                    .ToList();
                
                foreach (var debuggerName in debuggerProcessNames)
                {
                    if (runningProcesses.Any(p => p == debuggerName || p == debuggerName + ".vshost"))
                        return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in debugger detection: {ex.Message}");
                // If we can't perform the check, assume no debugger to avoid false positives
                return false;
            }
        }

        // Timing-based debugger detection
        private bool DetectDebuggerByTiming()
        {
            try
            {
                // Baseline operations that should execute very quickly
                var sw = new Stopwatch();
                
                // First calibrate timing on a simple operation to establish baseline
                sw.Start();
                for (int i = 0; i < 1000; i++)
                {
                    var x = i * i;
                }
                sw.Stop();
                long baselineTime = sw.ElapsedTicks;
                
                // Now time a similar operation with anti-debug checks in between
                // Debuggers will significantly slow this down due to breakpoints on checks
                sw.Restart();
                for (int i = 0; i < 1000; i++)
                {
                    // Insert operations that debuggers might break on
                    if (i % 100 == 0)
                    {
                        if (IsDebuggerPresent()) { /* Just a check to trigger breakpoints */ }
                        if (Debugger.IsAttached) { /* Just a check to trigger breakpoints */ }
                    }
                    var x = i * i;
                }
                sw.Stop();
                long testTime = sw.ElapsedTicks;
                
                // Calculate the ratio - if running under a debugger, the test will be
                // significantly slower (ratio will be higher)
                double ratio = (double)testTime / baselineTime;
                
                // A very high ratio indicates debugging - calibrate threshold based on testing
                // Normal operation should have a ratio close to 1-2
                if (ratio > 10)
                {
                    Debug.WriteLine($"Timing anomaly detected. Ratio: {ratio}");
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        // Memory signature-based debugger detection
        private bool DetectDebuggerByMemorySignatures()
        {
            try
            {
                // Get the current process
                var process = Process.GetCurrentProcess();
                
                // Look for INT3 breakpoint instructions in memory around key methods
                // This approach requires p/invoke and unsafe code to inspect memory
                // Simplified implementation - a full implementation would use native code
                
                // As a simple detection, check if the first 10 bytes of our license validation 
                // method have been modified from expected values
                // This would require pre-computing these values at startup
                
                // For demonstration, we'll just check for unexpectedly high open handles
                // Debuggers often attach many handles to the process
                if (process.HandleCount > 1000)  // Threshold needs calibration in real app
                {
                    Debug.WriteLine($"High handle count detected: {process.HandleCount}");
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        // Detect debugger by window classes they create
        private bool DetectDebuggerByWindowClasses()
        {
            try
            {
                // Many debuggers create specific window classes
                // This requires p/invoke to enumerate windows
                
                // For a simple implementation, we can check if certain known debugger windows exist
                // Full implementation would use FindWindow API and check window class names
                
                // Simplified version - check process window titles
                foreach (var proc in Process.GetProcesses())
                {
                    try
                    {
                        string title = proc.MainWindowTitle.ToLower();
                        if (string.IsNullOrEmpty(title))
                            continue;
                            
                        // Check for common debugger window titles
                        if (title.Contains("x64dbg") || title.Contains("ollydbg") || 
                            title.Contains("ida") || title.Contains("dnspy") ||
                            title.Contains("immunity debugger") || title.Contains("cheat engine"))
                        {
                            Debug.WriteLine($"Debugger window detected: {title}");
                            return true;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        // Detect debugger by checking parent process
        private bool DetectDebuggerByParentProcess()
        {
            try
            {
                // Debuggers often run as parent processes of the debugged application
                // Full implementation would use NtQueryInformationProcess to get parent PID
                
                // Simplified version - check for suspiciously high process ID difference
                // Between our process and previous processes
                int currentPid = Process.GetCurrentProcess().Id;
                
                // Get all processes and their IDs
                var allPids = Process.GetProcesses().Select(p => p.Id).OrderBy(id => id).ToList();
                
                // Find our process index in the list
                int ourIndex = allPids.IndexOf(currentPid);
                if (ourIndex > 0 && ourIndex < allPids.Count - 1)
                {
                    // Check for large gaps in PIDs which might indicate a debugger
                    int prevPid = allPids[ourIndex - 1];
                    int nextPid = allPids[ourIndex + 1];
                    
                    // If there's a very large gap after our process ID,
                    // it might indicate our process was launched by a debugger
                    if (currentPid - prevPid > 10000 || nextPid - currentPid > 10000)
                    {
                        Debug.WriteLine($"Suspicious process ID gap detected. Current: {currentPid}, Prev: {prevPid}, Next: {nextPid}");
                        return true;
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        // Verify the integrity of runtime code
        private bool VerifyRuntimeCodeIntegrity()
        {
            try
            {
                // Create a verification network for critical methods
                var integrityChecks = new Dictionary<string, Func<bool>>
                {
                    // Check license validation method
                    { "LicenseValidation", ValidateLicenseMethod },
                    
                    // Check encryption method
                    { "Encryption", ValidateEncryptionMethod },
                    
                    // Check hardware ID method
                    { "HardwareID", ValidateHardwareIdMethod },
                    
                    // Check integrity check method itself (recursive protection)
                    { "IntegrityChecker", ValidateIntegrityCheckerMethod },
                    
                    // Check anti-debug methods
                    { "AntiDebug", ValidateAntiDebugMethod }
                };
                
                // Randomly shuffle the order of checks to make it harder to bypass all
                var checkOrder = integrityChecks.Keys.OrderBy(k => Guid.NewGuid()).ToList();
                
                int validChecks = 0;
                int failedChecks = 0;
                
                // Execute checks in random order
                foreach (var checkName in checkOrder)
                {
                    // Add some random timing variance to make it harder to trace execution
                    if (new Random().Next(100) > 50)
                    {
                        Thread.Sleep(new Random().Next(5, 20));
                    }
                    
                    // Run the check
                    bool result = integrityChecks[checkName]();
                    
                    // Update counters
                    if (result) 
                        validChecks++;
                    else 
                        failedChecks++;
                    
                    // Early exit on failure with some randomization to prevent simple pattern matching
                    if (!result && new Random().Next(100) > 30)
                    {
                        Debug.WriteLine($"Integrity check failed: {checkName}");
                        return false;
                    }
                }
                
                // Create a non-obvious return logic to increase reverse engineering difficulty
                if (failedChecks > 0)
                {
                    Debug.WriteLine($"Runtime integrity compromised. Failed checks: {failedChecks}");
                    return false;
                }
                
                // Verify we actually ran all checks (protect against bypass that skips loop)
                if (validChecks != integrityChecks.Count)
                {
                    Debug.WriteLine($"Integrity checking incomplete. Only ran {validChecks} of {integrityChecks.Count} checks.");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying runtime code: {ex.Message}");
                return false;
            }
        }
        
        // Validate the license validation method hasn't been tampered with
        private bool ValidateLicenseMethod()
        {
            // Create a signature of the validation logic
            var methodSignature = new StringBuilder();
            
            try
            {
                // Add signature components in a way that's hard to spoof
                methodSignature.Append(typeof(LicenseService).Assembly.FullName);
                methodSignature.Append("|");
                
                // Add a check on the ValidateLicense method properties
                var methodInfo = typeof(LicenseService).GetMethod("ValidateLicense", 
                    BindingFlags.Public | BindingFlags.Instance);
                
                if (methodInfo != null)
                {
                    methodSignature.Append(methodInfo.MetadataToken);
                    methodSignature.Append("|");
                    methodSignature.Append(methodInfo.Attributes.ToString());
                    
                    // Check method implementation - more reliable than method metadata
                    // This creates a signature based on the IL code of the method
                    string methodBodyCheck = CreateMethodBodySignature(methodInfo);
                    methodSignature.Append("|");
                    methodSignature.Append(methodBodyCheck);
                }
                
                // Calculate hash of the signature
                using (var sha = SHA256.Create())
                {
                    byte[] hash = sha.ComputeHash(Encoding.UTF8.GetBytes(methodSignature.ToString()));
                    string calculatedHash = Convert.ToBase64String(hash);
                    
                    // Store expected hash at first run, use it for validation in subsequent runs
                    string hashKey = "ValidateLicense_Hash";
                    string expectedHash = GetStoredMethodHash(hashKey);
                    
                    if (string.IsNullOrEmpty(expectedHash))
                    {
                        // First run, store the hash
                        StoreMethodHash(hashKey, calculatedHash);
                        return true;
                    }
                    
                    // Compare with expected hash
                    return calculatedHash == expectedHash;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateLicenseMethod: {ex.Message}");
                return false;
            }
        }
        
        // Validate the encryption method hasn't been tampered with
        private bool ValidateEncryptionMethod()
        {
            try
            {
                // Perform a known calculation with our encryption methods and verify the result
                string testInput = "ValidationTest_" + KEY_DERIVATION_SALT;
                string encrypted = EncryptString(testInput);
                string decrypted = DecryptString(encrypted);
                
                // If decryption works correctly, our encryption methods should be intact
                if (decrypted != testInput)
                {
                    Debug.WriteLine("Encryption method validation failed");
                    return false;
                }
                
                // Also validate the method signature
                var methodInfo = typeof(LicenseService).GetMethod("EncryptString", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                    
                if (methodInfo != null)
                {
                    string methodBodyCheck = CreateMethodBodySignature(methodInfo);
                    string hashKey = "EncryptString_Hash";
                    string expectedHash = GetStoredMethodHash(hashKey);
                    
                    if (string.IsNullOrEmpty(expectedHash))
                    {
                        // First run, store the hash
                        StoreMethodHash(hashKey, methodBodyCheck);
                        return true;
                    }
                    
                    // Compare with expected hash
                    return methodBodyCheck == expectedHash;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating encryption method: {ex.Message}");
                return false;
            }
        }
        
        // Validate the hardware ID method hasn't been tampered with
        private bool ValidateHardwareIdMethod()
        {
            try
            {
                // Check method signature of GetHardwareId
                var methodInfo = typeof(LicenseService).GetMethod("GetHardwareId", 
                    BindingFlags.Public | BindingFlags.Instance);
                    
                if (methodInfo != null)
                {
                    string methodBodyCheck = CreateMethodBodySignature(methodInfo);
                    string hashKey = "GetHardwareId_Hash";
                    string expectedHash = GetStoredMethodHash(hashKey);
                    
                    if (string.IsNullOrEmpty(expectedHash))
                    {
                        // First run, store the hash
                        StoreMethodHash(hashKey, methodBodyCheck);
                    }
                    else if (methodBodyCheck != expectedHash)
                    {
                        Debug.WriteLine("Hardware ID method validation failed");
                        return false;
                    }
                }
                
                // Also perform behavioral validation:
                // GetHardwareId should be deterministic on the same hardware
                string id1 = GetHardwareId();
                string id2 = GetHardwareId();
                
                if (id1 != id2 || string.IsNullOrEmpty(id1))
                {
                    Debug.WriteLine("Hardware ID behaves inconsistently");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating hardware ID method: {ex.Message}");
                return false;
            }
        }
        
        // Validate integrity checker itself
        private bool ValidateIntegrityCheckerMethod()
        {
            try
            {
                // Check method signature of VerifyRuntimeCodeIntegrity
                var methodInfo = typeof(LicenseService).GetMethod("VerifyRuntimeCodeIntegrity", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                    
                if (methodInfo != null)
                {
                    string methodBodyCheck = CreateMethodBodySignature(methodInfo);
                    string hashKey = "VerifyIntegrity_Hash";
                    string expectedHash = GetStoredMethodHash(hashKey);
                    
                    if (string.IsNullOrEmpty(expectedHash))
                    {
                        // First run, store the hash
                        StoreMethodHash(hashKey, methodBodyCheck);
                    }
                    else if (methodBodyCheck != expectedHash)
                    {
                        Debug.WriteLine("Integrity checker method validation failed");
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating integrity checker: {ex.Message}");
                return false;
            }
        }
        
        // Validate anti-debug methods
        private bool ValidateAntiDebugMethod()
        {
            try
            {
                // Check method signature of IsDebuggerDetected
                var methodInfo = typeof(LicenseService).GetMethod("IsDebuggerDetected", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                    
                if (methodInfo != null)
                {
                    string methodBodyCheck = CreateMethodBodySignature(methodInfo);
                    string hashKey = "AntiDebug_Hash";
                    string expectedHash = GetStoredMethodHash(hashKey);
                    
                    if (string.IsNullOrEmpty(expectedHash))
                    {
                        // First run, store the hash
                        StoreMethodHash(hashKey, methodBodyCheck);
                    }
                    else if (methodBodyCheck != expectedHash)
                    {
                        Debug.WriteLine("Anti-debug method validation failed");
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating anti-debug method: {ex.Message}");
                return false;
            }
        }
        
        // Create a signature for a method's body
        private string CreateMethodBodySignature(MethodInfo method)
        {
            try
            {
                // Get method body
                var body = method.GetMethodBody();
                if (body == null)
                    return string.Empty;
                
                // Get IL bytes
                byte[] il = body.GetILAsByteArray();
                if (il == null || il.Length == 0)
                    return string.Empty;
                
                // Create a hash of the IL
                using (var sha = SHA256.Create())
                {
                    byte[] hash = sha.ComputeHash(il);
                    return Convert.ToBase64String(hash);
                }
            }
            catch
            {
                return string.Empty;
            }
        }
        
        // Store method hash
        private void StoreMethodHash(string key, string hash)
        {
            try
            {
                // We need to store the hash in a secure location
                // Options include:
                // 1. In-memory storage (lost on restart)
                // 2. Encrypted file storage
                // 3. Registry storage
                
                // For this implementation, we'll use encrypted storage
                // in a special file within the license directory
                
                var hashStorage = LoadMethodHashStorage();
                hashStorage[key] = hash;
                SaveMethodHashStorage(hashStorage);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error storing method hash: {ex.Message}");
            }
        }
        
        // Get stored method hash
        private string GetStoredMethodHash(string key)
        {
            try
            {
                var hashStorage = LoadMethodHashStorage();
                return hashStorage.TryGetValue(key, out string hash) ? hash : string.Empty;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error retrieving method hash: {ex.Message}");
                return string.Empty;
            }
        }
        
        // Get hash storage path
        private string GetHashStorageFilePath()
        {
            string fileNameBase = GetFileNameFromHardware("secmethods");
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.dat");
        }
        
        // Load method hash storage
        private Dictionary<string, string> LoadMethodHashStorage()
        {
            try
            {
                string filePath = GetHashStorageFilePath();
                
                if (!File.Exists(filePath))
                    return new Dictionary<string, string>();
                    
                var encrypted = File.ReadAllText(filePath);
                var json = DecryptString(encrypted);
                
                return JsonSerializer.Deserialize<Dictionary<string, string>>(json) 
                    ?? new Dictionary<string, string>();
            }
            catch
            {
                return new Dictionary<string, string>();
            }
        }
        
        // Save method hash storage
        private void SaveMethodHashStorage(Dictionary<string, string> hashStorage)
        {
            try
            {
                var json = JsonSerializer.Serialize(hashStorage);
                var encrypted = EncryptString(json);
                File.WriteAllText(GetHashStorageFilePath(), encrypted);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving method hash storage: {ex.Message}");
            }
        }

        // Ensure all required directories exist
        private void EnsureDirectoriesExist()
        {
            try
            {
                if (!Directory.Exists(_appDataDirectory))
                    Directory.CreateDirectory(_appDataDirectory);
                    
                var licenseDir = GetLicenseDirectory();
                if (!Directory.Exists(licenseDir))
                    Directory.CreateDirectory(licenseDir);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating directories: {ex.Message}");
            }
        }
        
        // Get the license file path
        private string GetLicenseFilePath()
        {
            // Create a deterministic but non-obvious name based on hardware and install location
            string fileNameBase = GetFileNameFromHardware("license");
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.bin");
        }
        
        // Get the activation file path
        private string GetActivationFilePath()
        {
            // Create a deterministic but non-obvious name based on hardware and install location
            string fileNameBase = GetFileNameFromHardware("activation");
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.cfg");
        }
        
        // Get the time tracking file path
        private string GetTimeTrackingFilePath()
        {
            // Create a deterministic but non-obvious name based on hardware and install location
            string fileNameBase = GetFileNameFromHardware(TIME_TRACKING_SUFFIX);
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.dat");
        }
        
        // Get the directory where license files will be stored
        private string GetLicenseDirectory()
        {
            // Create a deterministic folder name based on a hash of some stable system properties
            string folderHash = GetStableSystemHash();
            
            // Use the first 8 characters of the hash for the directory name
            string dirName = $"{LICENSE_FOLDER_NAME}_{folderHash.Substring(0, 8)}";
            
            // Alternate between appdata and executable directory based on hardware
            // This makes it harder to find both files as they'll be in different locations
            if (folderHash[0] % 2 == 0)
                return Path.Combine(_appDataDirectory, dirName);
            else
                return Path.Combine(_baseDirectory, dirName);
        }
        
        // Generate a file name from hardware information
        private string GetFileNameFromHardware(string fileType)
        {
            try
            {
                // Get stable system identifier
                string sysId = GetStableSystemHash();
                
                // Mix with file type to create different names for license and activation
                using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(fileType + KEY_DERIVATION_SALT)))
                {
                    byte[] nameHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(sysId));
                    
                    // Create a Base64 string and make it filesystem safe
                    return Convert.ToBase64String(nameHash)
                        .Replace("/", "_")
                        .Replace("+", "-")
                        .Replace("=", "")
                        .Substring(0, 16); // Use only first 16 chars
                }
            }
            catch
            {
                // Fallback with a less random but still variable name
                return $"data_{fileType}_{Environment.MachineName.GetHashCode():X8}";
            }
        }
        
        // Generate a stable system hash that doesn't change between runs
        private string GetStableSystemHash()
        {
            try
            {
                // Use information that doesn't change between reboots
                string processorId = GetProcessorId();
                string biosSerialNumber = GetBiosInfo().Split('|')[1];
                string osInfo = GetOsInfo();
                
                // Create a stable identifier string
                string stableSystemId = $"{processorId}|{biosSerialNumber}|{osInfo}|{ASSEMBLY_SEED}";
                
                // Hash it for consistency
                using (var sha = SHA256.Create())
                {
                    byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(stableSystemId));
                    return Convert.ToBase64String(hashBytes)
                        .Replace("/", "_")
                        .Replace("+", "-")
                        .Replace("=", "");
                }
            }
            catch
            {
                // Fallback to a less secure but still functional identifier
                return Convert.ToBase64String(
                    Encoding.UTF8.GetBytes($"{Environment.MachineName}|{Environment.UserDomainName}")
                ).Replace("/", "_").Replace("+", "-").Replace("=", "");
            }
        }

        public class LicenseInfo
        {
            public string BusinessName { get; set; }
            public string LicenseKey { get; set; }
            public DateTime ExpirationDate { get; set; }
            public LicenseType Type { get; set; }
            public int TerminalCount { get; set; }
            public string HardwareId { get; set; }
            public DateTime LicenseCreationDate { get; set; } = DateTime.UtcNow; // Added for time validation
            public int MaxAllowedUsages { get; set; } = 0; // 0 means unlimited, otherwise imposes a limit
        }

        public enum LicenseType
        {
            Basic,
            Standard,
            Professional,
            Enterprise
        }

        // Get the encryption key through key derivation
        private byte[] GetEncryptionKey()
        {
            try
            {
                // Combine seed parts with assembly information
                byte[] combinedSeed = CombineSeedParts();
                
                // Use a static portion of system information that doesn't change between installs
                // This provides a stable factor for key derivation that's unique to the application
                string systemFactor = GetSystemFactor();
                
                // Derive a key using PBKDF2
                using (var deriveBytes = new Rfc2898DeriveBytes(
                    combinedSeed, 
                    Encoding.UTF8.GetBytes(systemFactor + KEY_DERIVATION_SALT),
                    KEY_ITERATION_COUNT, 
                    HashAlgorithmName.SHA256))
                {
                    return deriveBytes.GetBytes(KEY_SIZE_BYTES);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deriving encryption key: {ex.Message}");
                
                // Fallback to a safer approach if key derivation fails
                return GetFallbackEncryptionKey();
            }
        }
        
        // Combine the seed parts in a way that's hard to statically analyze
        private byte[] CombineSeedParts()
        {
            byte[] combined = new byte[_seedPart1.Length + _seedPart2.Length + ASSEMBLY_SEED.Length];
            int offset = 0;
            
            // Interleave the parts in a non-obvious pattern
            for (int i = 0; i < _seedPart1.Length; i++)
            {
                combined[offset++] = _seedPart1[i];
                if (i < _seedPart2.Length)
                    combined[offset++] = _seedPart2[i];
            }
            
            // Add assembly information
            byte[] assemblySeed = Encoding.UTF8.GetBytes(ASSEMBLY_SEED);
            for (int i = 0; i < assemblySeed.Length; i++)
            {
                combined[offset++] = assemblySeed[i];
            }
            
            // One more transformation to make the pattern less obvious
            for (int i = 0; i < combined.Length / 2; i++)
            {
                byte temp = combined[i];
                combined[i] = combined[combined.Length - i - 1];
                combined[combined.Length - i - 1] = temp;
            }
            
            return combined;
        }
        
        // Get system factor for key derivation that's consistent across the application lifetime
        private string GetSystemFactor()
        {
            try
            {
                // Get OS and processor architecture information from the system
                string osVersion = Environment.OSVersion.Version.ToString();
                string is64BitOS = Environment.Is64BitOperatingSystem.ToString();
                string machineName = Environment.MachineName;
                // Use a hash of machineName to make it harder to manipulate directly
                string machineHash = Convert.ToBase64String(
                    SHA256.HashData(Encoding.UTF8.GetBytes(machineName))
                ).Substring(0, 8);
                
                // Get assembly info
                var assembly = Assembly.GetExecutingAssembly();
                string assemblyGuid = assembly.ManifestModule.ModuleVersionId.ToString().Substring(0, 8);
                
                // Combine factors
                return $"{osVersion}|{is64BitOS}|{machineHash}|{assemblyGuid}";
            }
            catch
            {
                return ASSEMBLY_SEED; // Minimal fallback
            }
        }
        
        // Fallback encryption key - used only if derivation fails
        private byte[] GetFallbackEncryptionKey()
        {
            // Use a PBKDF2 derivation with just the ASSEMBLY_SEED and KEY_DERIVATION_SALT
            using (var deriveBytes = new Rfc2898DeriveBytes(
                ASSEMBLY_SEED,
                Encoding.UTF8.GetBytes(KEY_DERIVATION_SALT),
                5000, 
                HashAlgorithmName.SHA256))
            {
                return deriveBytes.GetBytes(KEY_SIZE_BYTES);
            }
        }
        
        // Derive the secret key used for license validation
        private string GetSecretKey()
        {
            try
            {
                // Combine the assembly info with the license data salt
                string combinedData = $"{ASSEMBLY_SEED}:{KEY_DERIVATION_SALT}";
                
                // Create a SHA256 hash of the combined data
                byte[] hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(combinedData));
                
                // Convert to base64 and take first 24 characters
                string base64Hash = Convert.ToBase64String(hashBytes);
                
                // Make it more complex with a fixed pattern
                return $"{base64Hash.Substring(0, 16)}!{base64Hash.Substring(16, 8)}#";
            }
            catch
            {
                // Fallback with reduced security
                return "POSSystem_SecretKey!2024#";
            }
        }

        public string GetHardwareId()
        {
            try
            {
                // Get individual component hashes instead of a single combined hash
                var components = new Dictionary<string, string>
                {
                    { "CPU", GetComponentHash(GetProcessorId()) },
                    { "Motherboard", GetComponentHash(GetMotherboardInfo()) },
                    { "BIOS", GetComponentHash(GetBiosInfo()) },
                    { "Disk", GetComponentHash(GetDiskDriveInfo()) },
                    { "MAC", GetComponentHash(GetMacAddress()) },
                    { "OS", GetComponentHash(GetOsInfo()) }
                };

                // Create a combined string with labeled components
                // Format: CPU:hash|Motherboard:hash|BIOS:hash|Disk:hash|MAC:hash|OS:hash
                string combinedHash = string.Join("|", components.Select(c => $"{c.Key}:{c.Value}"));
                
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(combinedHash)).TrimEnd('=');
            }
            catch (Exception)
            {
                // Fallback to basic system info if WMI fails
                var fallbackInfo = $"{Environment.ProcessorCount}|{Environment.MachineName}|{Environment.OSVersion}";
                return Convert.ToBase64String(Encoding.UTF8.GetBytes("FALLBACK:" + fallbackInfo)).TrimEnd('=');
            }
        }
        
        // Get hash for a single component
        private string GetComponentHash(string componentInfo)
        {
            if (string.IsNullOrEmpty(componentInfo))
                return string.Empty;
                
            using (var sha = SHA256.Create())
            {
                var hash = sha.ComputeHash(Encoding.UTF8.GetBytes(componentInfo));
                return Convert.ToBase64String(hash).Substring(0, 10); // First 10 chars is enough for component comparison
            }
        }

        // Hardware information gathering methods
        private string GetProcessorId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            return obj["ProcessorId"]?.ToString() ?? "";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetMotherboardInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Product, Manufacturer FROM Win32_BaseBoard"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var product = obj["Product"]?.ToString() ?? "";
                            var manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                            return $"{manufacturer}|{product}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetBiosInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Version FROM Win32_BIOS"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var version = obj["Version"]?.ToString() ?? "";
                            return $"{version}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetDiskDriveInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Model FROM Win32_DiskDrive"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var model = obj["Model"]?.ToString() ?? "";
                            return $"{model}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetMacAddress()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE PhysicalAdapter=True"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var mac = obj["MACAddress"]?.ToString() ?? "";
                            if (!string.IsNullOrEmpty(mac))
                                return mac;
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetOsInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Version FROM Win32_OperatingSystem"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var version = obj["Version"]?.ToString() ?? "";
                            return $"{version}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        // Validate hardware ID with fuzzy matching (allowing some components to change)
        private bool ValidateHardwareId(string storedHardwareId, string currentHardwareId)
        {
            try
            {
                // If in development mode, be more permissive
                if (_isDevelopmentMode)
                {
                    Debug.WriteLine("Development mode: Using relaxed hardware validation");
                    return true;
                }
                
                // If exact match, return true immediately
                if (storedHardwareId == currentHardwareId)
                    return true;
                
                // Decode the Base64 encoded hardware IDs
                string storedComponents = Encoding.UTF8.GetString(Convert.FromBase64String(storedHardwareId));
                string currentComponents = Encoding.UTF8.GetString(Convert.FromBase64String(currentHardwareId));
                
                // Handle fallback case
                if (storedComponents.StartsWith("FALLBACK:") || currentComponents.StartsWith("FALLBACK:"))
                {
                    return storedComponents == currentComponents;
                }
                
                // Parse components into dictionaries
                var storedDict = ParseHardwareComponents(storedComponents);
                var currentDict = ParseHardwareComponents(currentComponents);
                
                // Count matching components
                int matchCount = 0;
                int totalComponents = 0;
                
                foreach (var component in storedDict)
                {
                    totalComponents++;
                    
                    // If component exists in both and values match
                    if (currentDict.TryGetValue(component.Key, out string currentValue) && 
                        component.Value == currentValue)
                    {
                        matchCount++;
                    }
                }
                
                if (totalComponents == 0)
                    return false;
                
                // Calculate match percentage
                double matchPercentage = (double)matchCount / totalComponents * 100;
                
                // Get required match percentage based on license type
                double requiredPercentage = GetRequiredHardwareMatchPercentage();
                
                Debug.WriteLine($"Hardware ID match: {matchPercentage}% (required: {requiredPercentage}%)");
                Debug.WriteLine($"Matched {matchCount} of {totalComponents} components");
                
                // Return true if match percentage exceeds required threshold
                return matchPercentage >= requiredPercentage;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating hardware ID: {ex.Message}");
                // On error, fall back to exact string comparison
                return storedHardwareId == currentHardwareId;
            }
        }
        
        // Parse hardware component string into dictionary
        private Dictionary<string, string> ParseHardwareComponents(string componentsString)
        {
            var result = new Dictionary<string, string>();
            
            // Split by component separator
            string[] components = componentsString.Split('|');
            
            foreach (string component in components)
            {
                // Split component name and value
                string[] parts = component.Split(':');
                if (parts.Length == 2)
                {
                    result[parts[0]] = parts[1];
                }
            }
            
            return result;
        }
        
        // Get required hardware match percentage based on license type
        private double GetRequiredHardwareMatchPercentage()
        {
            try
            {
                // Get license type from current license
                var license = LoadLicense();
                if (license == null)
                    return 100; // If no valid license, require perfect match
                
                // Different threshold based on license tier
                switch (license.Type)
                {
                    case LicenseType.Basic:
                        return 90; // Basic license requires 90% match (5/6 components)
                    case LicenseType.Standard:
                        return 80; // Standard license requires 80% match (4-5/6 components)
                    case LicenseType.Professional:
                        return 66; // Professional license requires 66% match (4/6 components)
                    case LicenseType.Enterprise:
                        return 50; // Enterprise license allows half the components to change
                    default:
                        return 90;
                }
            }
            catch
            {
                return 90; // Default to strict matching on error
            }
        }

        /// <summary>
        /// Validates the license including time integrity
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool ValidateLicense()
        {
            try
            {
                // If in development mode, bypass security checks
                if (_isDevelopmentMode)
                {
                    Debug.WriteLine("Development mode: Bypassing tampering checks");
                    // In dev mode, only check license existence
                    goto LicenseValidation;
                }
                
                // Initialize watermarking if needed
                if (_licenseWatermarks == null || _licenseWatermarks.Count == 0)
                {
                    InitializeLicenseWatermarking();
                }
                
                // Check for tampering
                if (CheckForTampering())
                {
                    Debug.WriteLine("Tampering detected - license validation failed");
                    return false;
                }
                
                // Verify license watermarks are intact
                if (!VerifyLicenseWatermarks())
                {
                    // Quietly detect tampering instead of immediately failing
                    // This makes it harder to locate the check
                    SetTamperingDetected("License watermark verification failed");
                }
                
                // Check if running in a VM (optional - can be enabled based on license type)
                if (IsRunningInVirtualMachine() && DenyVirtualMachines())
                {
                    Debug.WriteLine("Virtual environment detected - license validation failed");
                    return false;
                }
                
                // Verify system time integrity
                if (!VerifyTimeIntegrity())
                {
                    Debug.WriteLine("Time integrity check failed - license validation failed");
                    return false;
                }

                // Normal license validation follows
            LicenseValidation:
                string licenseFile = GetLicenseFilePath();
                string activationFile = GetActivationFilePath();
                
                if (!File.Exists(licenseFile) || !File.Exists(activationFile))
                    return false;

                var license = LoadLicense();
                if (license == null)
                    return false;

                // Check expiration with additional clock manipulation checks
                if (!IsExpirationDateValid(license))
                    return false;

                // Verify hardware ID using fuzzy matching
                var currentHardwareId = GetHardwareId();
                if (!ValidateHardwareId(license.HardwareId, currentHardwareId))
                {
                    // Handle hardware changes gracefully
                    HandleHardwareChanges(license, currentHardwareId);
                    return false;
                }

                // Verify license key format and checksum
                return ValidateLicenseKeyFormat(license.LicenseKey);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"License validation error: {ex.Message}");
                return false;
            }
        }
        
        // Handle hardware changes when validation fails
        private void HandleHardwareChanges(LicenseInfo license, string currentHardwareId)
        {
            Debug.WriteLine("Hardware ID validation failed");
            Debug.WriteLine($"Stored: {license.HardwareId}");
            Debug.WriteLine($"Current: {currentHardwareId}");
            
            try
            {
                // Log the comparison details for diagnostics
                LogHardwareComparison(license.HardwareId, currentHardwareId);
                
                // Show user-friendly message about hardware changes on the UI thread
                var dispatcher = Application.Current?.Dispatcher;
                if (dispatcher != null)
                {
                    dispatcher.BeginInvoke(() =>
                    {
                        ShowHardwareChangeMessage(license);
                    });
                }
                else
                {
                    // Fallback if dispatcher is not available
                    ShowHardwareChangeMessage(license);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling hardware changes: {ex.Message}");
            }
        }
        
        // Log detailed hardware comparison for diagnostics
        private void LogHardwareComparison(string storedHardwareId, string currentHardwareId)
        {
            try
            {
                // Decode the Base64 encoded hardware IDs
                string storedComponents = Encoding.UTF8.GetString(Convert.FromBase64String(storedHardwareId));
                string currentComponents = Encoding.UTF8.GetString(Convert.FromBase64String(currentHardwareId));
                
                // Parse components into dictionaries
                var storedDict = ParseHardwareComponents(storedComponents);
                var currentDict = ParseHardwareComponents(currentComponents);
                
                Debug.WriteLine("Hardware comparison details:");
                
                // Log each component comparison
                foreach (var component in storedDict)
                {
                    string currentValue = currentDict.TryGetValue(component.Key, out string value) ? value : "Missing";
                    bool matches = component.Value == currentValue;
                    
                    Debug.WriteLine($"Component: {component.Key}");
                    Debug.WriteLine($"  Stored: {component.Value}");
                    Debug.WriteLine($"  Current: {currentValue}");
                    Debug.WriteLine($"  Match: {matches}");
                }
                
                // Check for additional components in current hardware
                foreach (var component in currentDict)
                {
                    if (!storedDict.ContainsKey(component.Key))
                    {
                        Debug.WriteLine($"New component: {component.Key} = {component.Value}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error logging hardware comparison: {ex.Message}");
            }
        }
        
        // Show message to user about hardware changes
        private void ShowHardwareChangeMessage(LicenseInfo license)
        {
            try
            {
                var message = 
                    "Your system hardware has changed significantly since the license was activated.\n\n" +
                    "This could be due to:\n" +
                    "• Hardware upgrades or replacements\n" +
                    "• Moving the software to a different computer\n" +
                    "• Operating system reinstallation\n\n" +
                    $"Your license is registered to: {license.BusinessName}\n\n" +
                    "Would you like to attempt to update your hardware ID automatically? " +
                    "This will work for most hardware upgrades but requires your original license key.";
                
                var result = MessageBox.Show(
                    message,
                    "Hardware Change Detected",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // Prompt for license key to verify
                    AttemptHardwareIdUpdate(license);
                }
                else
                {
                    // Show contact support message
                    ShowContactSupportMessage(license);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing hardware change message: {ex.Message}");
                
                // Fall back to contact support message on error
                ShowContactSupportMessage(license);
            }
        }
        
        // Store hardware update history in a protected file
        private const string HARDWARE_UPDATE_HISTORY_SUFFIX = "hwupdate";
        
        // Show contact support message
        private void ShowContactSupportMessage(LicenseInfo license)
        {
            var contactMessage = 
                "To continue using the application, you need to:\n" +
                "1. Contact customer support to reactivate your license\n" +
                "2. Provide your current hardware ID when requested\n" +
                $"3. Include your business name: {license.BusinessName}\n\n" +
                $"Your current hardware ID: {GetHardwareId()}";
                
            MessageBox.Show(
                contactMessage,
                "Contact Support",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        
        // Get hardware update history file path
        private string GetHardwareUpdateHistoryFilePath()
        {
            string fileNameBase = GetFileNameFromHardware(HARDWARE_UPDATE_HISTORY_SUFFIX);
            return Path.Combine(GetLicenseDirectory(), $"{fileNameBase}.dat");
        }
        
        // Log hardware update attempt with details
        private void LogHardwareUpdateAttempt(LicenseInfo license, string status, string details = "")
        {
            try
            {
                // Create update record
                var updateRecord = new 
                {
                    Timestamp = DateTime.UtcNow,
                    BusinessName = license.BusinessName,
                    LicenseKey = license.LicenseKey.Substring(0, 8) + "****", // Partial key for privacy
                    OldHardwareId = license.HardwareId,
                    NewHardwareId = GetHardwareId(),
                    Status = status,
                    Details = details,
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                };
                
                // Load existing history if available
                var historyList = LoadHardwareUpdateHistory();
                
                // Add new record
                historyList.Add(updateRecord);
                
                // Keep only last 10 records
                while (historyList.Count > 10)
                    historyList.RemoveAt(0);
                
                // Save updated history
                SaveHardwareUpdateHistory(historyList);
                
                // Also log to debug output
                Debug.WriteLine($"Hardware update attempt: {status} - {details}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error logging hardware update attempt: {ex.Message}");
            }
        }
        
        // Load hardware update history
        private List<object> LoadHardwareUpdateHistory()
        {
            try
            {
                string filePath = GetHardwareUpdateHistoryFilePath();
                
                if (!File.Exists(filePath))
                    return new List<object>();
                
                var encrypted = File.ReadAllText(filePath);
                var json = DecryptString(encrypted);
                
                return JsonSerializer.Deserialize<List<object>>(json) ?? new List<object>();
            }
            catch
            {
                return new List<object>();
            }
        }
        
        // Save hardware update history
        private void SaveHardwareUpdateHistory(List<object> history)
        {
            try
            {
                var json = JsonSerializer.Serialize(history);
                var encrypted = EncryptString(json);
                File.WriteAllText(GetHardwareUpdateHistoryFilePath(), encrypted);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving hardware update history: {ex.Message}");
            }
        }
        
        // Get the time of the last hardware update for this license
        private DateTime GetLastHardwareUpdateTime(string licenseKey)
        {
            try
            {
                var history = LoadHardwareUpdateHistory();
                
                // Convert to JsonElement to allow dynamic parsing
                var historyJson = JsonSerializer.Serialize(history);
                var historyArray = JsonSerializer.Deserialize<JsonElement>(historyJson);
                
                // Find most recent successful update with matching license key prefix
                DateTime lastUpdate = DateTime.MinValue;
                
                if (historyArray.ValueKind == JsonValueKind.Array)
                {
                    string keyPrefix = licenseKey.Substring(0, 8);
                    
                    foreach (var item in historyArray.EnumerateArray())
                    {
                        if (item.TryGetProperty("Status", out JsonElement statusElement) &&
                            statusElement.GetString() == "Approved" &&
                            item.TryGetProperty("LicenseKey", out JsonElement licenseElement) &&
                            licenseElement.GetString()?.StartsWith(keyPrefix) == true &&
                            item.TryGetProperty("Timestamp", out JsonElement timestampElement))
                        {
                            DateTime updateTime = timestampElement.GetDateTime();
                            if (updateTime > lastUpdate)
                                lastUpdate = updateTime;
                        }
                    }
                }
                
                return lastUpdate;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }
        
        // Verify additional business information to reduce social engineering attacks
        private bool VerifyBusinessInformation(LicenseInfo license, string userInput)
        {
            try
            {
                // Count how many pieces of information match
                int matchCount = 0;
                
                // 1. Verify license key (already verified in calling method)
                matchCount++;
                
                // 2. Verify business name (case insensitive)
                if (!string.IsNullOrEmpty(userInput))
                {
                    string cleanedInput = userInput.Trim();
                    if (cleanedInput.Equals(license.BusinessName, StringComparison.OrdinalIgnoreCase))
                        matchCount++;
                }
                
                // Return true if enough verification data matches
                return matchCount >= 2;
            }
            catch
            {
                return false;
            }
        }
        
        // Verify if hardware update is allowed (based on license tier and update history)
        private (bool Allowed, string Reason) ValidateHardwareUpdateEligibility(LicenseInfo license)
        {
            try
            {
                // Check if this license has had recent hardware updates
                var lastUpdate = GetLastHardwareUpdateTime(license.LicenseKey);
                
                // Get allowed update frequency based on license tier
                int cooldownDays = GetHardwareUpdateCooldownDays(license.Type);
                
                // Check if update is within cooldown period
                if (lastUpdate != DateTime.MinValue && 
                    DateTime.UtcNow - lastUpdate < TimeSpan.FromDays(cooldownDays))
                {
                    TimeSpan timeSinceUpdate = DateTime.UtcNow - lastUpdate;
                    int daysRemaining = cooldownDays - (int)timeSinceUpdate.TotalDays;
                    
                    return (false, $"Too recent since last update. Please wait {daysRemaining} more days or contact support.");
                }
                
                return (true, "Eligible for hardware update");
            }
            catch (Exception ex)
            {
                return (false, $"Error checking eligibility: {ex.Message}");
            }
        }
        
        // Get allowed update frequency based on license tier
        private int GetHardwareUpdateCooldownDays(LicenseType licenseType)
        {
            switch (licenseType)
            {
                case LicenseType.Enterprise:
                    return 7;  // Enterprise licenses can update weekly
                case LicenseType.Professional:
                    return 14; // Professional licenses can update bi-weekly
                case LicenseType.Standard:
                    return 30; // Standard licenses can update monthly
                case LicenseType.Basic:
                default:
                    return 60; // Basic licenses can update every 60 days
            }
        }
        
        // Attempt to update hardware ID
        private void AttemptHardwareIdUpdate(LicenseInfo license)
        {
            try
            {
                // Check if hardware update is allowed for this license
                var (isEligible, reason) = ValidateHardwareUpdateEligibility(license);
                
                if (!isEligible)
                {
                    LogHardwareUpdateAttempt(license, "Denied - " + reason, reason);
                    
                    MessageBox.Show(
                        $"Hardware ID update is not allowed: {reason}\n\nIf this is urgent, please contact customer support.",
                        "Update Not Allowed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    
                    // Show contact support message
                    ShowContactSupportMessage(license);
                    return;
                }
                
                // Create a dialog to verify license information
                var dialog = new Window
                {
                    Title = "Verify License Information",
                    Width = 450,
                    Height = 250,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    ResizeMode = ResizeMode.NoResize
                };
                
                var mainPanel = new System.Windows.Controls.StackPanel
                {
                    Margin = new Thickness(10)
                };
                
                mainPanel.Children.Add(new System.Windows.Controls.Label
                {
                    Content = "Please enter your license information to verify ownership:",
                    Margin = new Thickness(0, 10, 0, 10)
                });
                
                // License key input
                mainPanel.Children.Add(new System.Windows.Controls.Label
                {
                    Content = "License Key:",
                    Margin = new Thickness(0, 5, 0, 0)
                });
                
                var licenseKeyInput = new System.Windows.Controls.TextBox
                {
                    Width = 400,
                    Height = 25,
                    Text = license.LicenseKey  // Pre-fill with current key for convenience
                };
                mainPanel.Children.Add(licenseKeyInput);
                
                // Business name input
                mainPanel.Children.Add(new System.Windows.Controls.Label
                {
                    Content = "Business Name (as registered with license):",
                    Margin = new Thickness(0, 5, 0, 0)
                });
                
                var businessNameInput = new System.Windows.Controls.TextBox
                {
                    Width = 400,
                    Height = 25
                };
                mainPanel.Children.Add(businessNameInput);
                
                // Buttons panel
                var buttonsPanel = new System.Windows.Controls.StackPanel
                {
                    Orientation = System.Windows.Controls.Orientation.Horizontal,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Right,
                    Margin = new Thickness(0, 20, 0, 0)
                };
                
                var okButton = new System.Windows.Controls.Button
                {
                    Content = "Update Hardware ID",
                    Width = 120,
                    Margin = new Thickness(0, 0, 10, 0),
                    IsDefault = true
                };
                
                var cancelButton = new System.Windows.Controls.Button
                {
                    Content = "Cancel",
                    Width = 80,
                    IsCancel = true
                };
                
                buttonsPanel.Children.Add(okButton);
                buttonsPanel.Children.Add(cancelButton);
                mainPanel.Children.Add(buttonsPanel);
                
                dialog.Content = mainPanel;
                
                bool? result = false;
                
                okButton.Click += (s, e) =>
                {
                    dialog.DialogResult = true;
                    dialog.Close();
                };
                
                result = dialog.ShowDialog();
                
                if (result == true)
                {
                    // Verify the license key matches
                    if (!string.IsNullOrEmpty(licenseKeyInput.Text) && 
                        licenseKeyInput.Text.Trim() == license.LicenseKey)
                    {
                        // Additional business verification
                        if (VerifyBusinessInformation(license, businessNameInput.Text))
                        {
                            // Update the hardware ID and save
                            UpdateHardwareId(license);
                        }
                        else
                        {
                            LogHardwareUpdateAttempt(license, "Denied - verification failed", 
                                "Business name verification failed");
                            
                            MessageBox.Show(
                                "The business name you entered does not match our records.",
                                "Verification Failed",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                            
                            // Show contact support message
                            ShowContactSupportMessage(license);
                        }
                    }
                    else
                    {
                        LogHardwareUpdateAttempt(license, "Denied - license key verification failed", 
                            "Incorrect license key entered");
                        
                        MessageBox.Show(
                            "The license key you entered does not match your current license.",
                            "License Verification Failed",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                        
                        // Show contact support message
                        ShowContactSupportMessage(license);
                    }
                }
                else
                {
                    LogHardwareUpdateAttempt(license, "Canceled", "User canceled the verification dialog");
                    
                    // User canceled
                    ShowContactSupportMessage(license);
                }
            }
            catch (Exception ex)
            {
                LogHardwareUpdateAttempt(license, "Error", ex.Message);
                
                Debug.WriteLine($"Error in hardware ID update attempt: {ex.Message}");
                MessageBox.Show(
                    "An error occurred while trying to update your hardware ID. Please contact customer support.",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                
                // Show contact support message
                ShowContactSupportMessage(license);
            }
        }
        
        // Update hardware ID and save
        private void UpdateHardwareId(LicenseInfo license)
        {
            try
            {
                // Get the current hardware ID before updating
                string oldHardwareId = license.HardwareId;
                string currentHardwareId = GetHardwareId();
                
                // Update the hardware ID
                license.HardwareId = currentHardwareId;
                
                // Save the updated license
                SaveLicense(license);
                
                // Get license expiration
                string expirationDate = license.ExpirationDate.ToString("yyyy-MM-dd");
                
                // Also update the activation file
                SaveActivation(currentHardwareId, license.LicenseKey);
                
                // Log successful hardware update
                LogHardwareUpdateAttempt(license, "Approved", 
                    $"Updated hardware ID from {oldHardwareId.Substring(0, 12)}... to {currentHardwareId.Substring(0, 12)}...");
                
                MessageBox.Show(
                    $"Hardware ID updated successfully!\n\n" +
                    $"Business Name: {license.BusinessName}\n" +
                    $"License Type: {license.Type}\n" +
                    $"Expiration Date: {expirationDate}\n\n" +
                    "The application will now restart to apply the changes.",
                    "Hardware ID Updated",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
                
                // Restart the application
                RestartApplication();
            }
            catch (Exception ex)
            {
                LogHardwareUpdateAttempt(license, "Error", ex.Message);
                
                Debug.WriteLine($"Error updating hardware ID: {ex.Message}");
                MessageBox.Show(
                    "An error occurred while updating your hardware ID. Please contact customer support.",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
        
        // Restart the application
        private void RestartApplication()
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = Application.ResourceAssembly.Location,
                    UseShellExecute = true
                };
                System.Diagnostics.Process.Start(startInfo);

                // Shutdown the current instance
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error restarting application: {ex.Message}");
                MessageBox.Show(
                    "The application needs to be restarted manually to apply the changes.",
                    "Restart Required",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }
        
        // Check if the license expiration date is valid considering possible clock manipulation
        private bool IsExpirationDateValid(LicenseInfo license)
        {
            // Primary check: current time against expiration
            if (DateTime.UtcNow > license.ExpirationDate.ToUniversalTime())
            {
                Debug.WriteLine("License expired based on current time");
                return false;
            }
            
            try
            {
                // Secondary check: if license creation date is in the future, clock was manipulated
                if (license.LicenseCreationDate.ToUniversalTime() > DateTime.UtcNow.AddDays(1))
                {
                    Debug.WriteLine("License creation date is in the future - clock was rolled back");
                    return false;
                }
                
                // Tertiary check: usage tracking for time-limited licenses
                var timeData = LoadTimeTrackingData();
                
                // If this is an expired license that was somehow reactivated, the usage count
                // should still be advancing, but the time might have been manipulated
                if (license.MaxAllowedUsages > 0 && timeData.UsageCounter > license.MaxAllowedUsages)
                {
                    Debug.WriteLine("License exceeded maximum allowed usages");
                    return false;
                }
                
                // Check time coherence: is license expiration date too far from license creation + allowed term?
                // This is a check for if someone tried to modify the expiration date directly
                TimeSpan timeSinceCreation = DateTime.UtcNow - license.LicenseCreationDate.ToUniversalTime();
                TimeSpan expirationSpan = license.ExpirationDate.ToUniversalTime() - license.LicenseCreationDate.ToUniversalTime();
                
                // If the license has been active for longer than its supposed full term
                if (timeSinceCreation > expirationSpan && timeSinceCreation.TotalDays > 30)
                {
                    Debug.WriteLine("License term inconsistency detected");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in expiration validation: {ex.Message}");
                // Fall back to simple check if something goes wrong
                return DateTime.UtcNow <= license.ExpirationDate.ToUniversalTime();
            }
        }

        /// <summary>
        /// Determines whether the application should deny running in virtual machines
        /// based on the license type.
        /// </summary>
        /// <returns>True if VMs should be denied, false if allowed</returns>
        private bool DenyVirtualMachines()
        {
            try
            {
                // Read license to determine the tier
                var licenseData = DecryptLicenseData();
                if (licenseData == null) return true; // No valid license, deny VMs
                
                // Get license tier from license data
                string tier = licenseData.ContainsKey("Tier") ? licenseData["Tier"] : "Basic";
                
                // Allow VMs only for higher-tier licenses
                return tier != "Enterprise" && tier != "Professional";
            }
            catch
            {
                // On any error, default to deny for security
                return true;
            }
        }
        
        /// <summary>
        /// Detects if the application is running inside a virtual machine or sandbox environment
        /// </summary>
        /// <returns>True if VM detected, false otherwise</returns>
        private bool IsRunningInVirtualMachine()
        {
            try
            {
                // Check for common VM indicators
                
                // 1. Check for VM-specific services
                if (CheckVMServices())
                    return true;
                    
                // 2. Check system model and manufacturer
                if (CheckVMHardware())
                    return true;
                    
                // 3. Check for VM-specific registry keys
                if (CheckVMRegistry())
                    return true;
                    
                // 4. Check for VM-specific processes
                if (CheckVMProcesses())
                    return true;
                    
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in VM detection: {ex.Message}");
                return false; // On error, assume not a VM to prevent false positives
            }
        }
        
        private bool CheckVMServices()
        {
            try
            {
                string[] vmServices = new string[] 
                {
                    "vmicheartbeat", "vmicvss", "vmicshutdown", "vmicexchange", 
                    "vmware tools", "vboxservice", "vboxtray"
                };
                
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Service"))
                {
                    foreach (var service in searcher.Get())
                    {
                        string serviceName = service["Name"]?.ToString()?.ToLower() ?? "";
                        if (vmServices.Any(vm => serviceName.Contains(vm)))
                        {
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        private bool CheckVMHardware()
        {
            try
            {
                string[] vmManufacturers = new string[] 
                { 
                    "vmware", "virtualbox", "kvm", "qemu", "xen", "innotek", "microsoft corporation", "parallels"
                };
                
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (var item in searcher.Get())
                    {
                        string manufacturer = item["Manufacturer"]?.ToString()?.ToLower() ?? "";
                        string model = item["Model"]?.ToString()?.ToLower() ?? "";
                        
                        if (vmManufacturers.Any(vm => manufacturer.Contains(vm) || model.Contains(vm)))
                        {
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        private bool CheckVMRegistry()
        {
            try
            {
                // Check for common VM registry keys
                string[] registryKeys = new string[]
                {
                    @"SYSTEM\ControlSet001\Services\VBoxGuest",
                    @"SYSTEM\ControlSet001\Services\VBoxMouse",
                    @"SYSTEM\ControlSet001\Services\VBoxService",
                    @"SYSTEM\ControlSet001\Services\VBoxSF",
                    @"SYSTEM\ControlSet001\Services\vmci",
                    @"SYSTEM\ControlSet001\Services\vmhgfs",
                    @"SYSTEM\ControlSet001\Services\vmmouse",
                    @"SYSTEM\ControlSet001\Services\vmx86",
                    @"SYSTEM\ControlSet001\Services\vmxnet",
                    @"SOFTWARE\VMware, Inc.\VMware Tools"
                };
                
                using (var baseKey = Microsoft.Win32.Registry.LocalMachine)
                {
                    foreach (var keyPath in registryKeys)
                    {
                        var key = baseKey.OpenSubKey(keyPath, false);
                        if (key != null)
                        {
                            key.Close();
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        private bool CheckVMProcesses()
        {
            try
            {
                string[] vmProcesses = new string[]
                {
                    "vmtoolsd.exe", "vmwaretray.exe", "vmwareuser.exe",
                    "vboxtray.exe", "vboxservice.exe", "xenservice.exe"
                };
                
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        if (vmProcesses.Any(vm => process.ProcessName.ToLower().Contains(vm.Replace(".exe", ""))))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        // Ignore errors for individual processes
                        continue;
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        public bool ActivateLicense(string licenseKey, string businessName)
        {
            try
            {
                Debug.WriteLine($"Attempting to activate license for {businessName}");
                Debug.WriteLine($"Hardware ID: {GetHardwareId()}");

                if (!ValidateLicenseKeyFormat(licenseKey))
                {
                    Debug.WriteLine("License key format validation failed");
                    return false;
                }

                var hardwareId = GetHardwareId();
                Debug.WriteLine($"Current Hardware ID: {hardwareId}");

                var info = DecodeLicenseInfo(licenseKey);
                Debug.WriteLine($"Decoded License Info:");
                Debug.WriteLine($"Business Name: {info.BusinessName}");
                Debug.WriteLine($"System ID: {info.HardwareId}");
                Debug.WriteLine($"Type: {info.Type}");
                Debug.WriteLine($"Expiration: {info.ExpirationDate}");

                // Verify business name and hardware ID
                if (info.BusinessName != businessName)
                {
                    Debug.WriteLine($"Business name mismatch. Expected: {businessName}, Got: {info.BusinessName}");
                    return false;
                }

                if (info.HardwareId != hardwareId)
                {
                    Debug.WriteLine($"Hardware ID mismatch. Expected: {hardwareId}, Got: {info.HardwareId}");
                    return false;
                }

                // Verify expiration
                if (info.ExpirationDate < DateTime.Now)
                {
                    Debug.WriteLine($"License expired on {info.ExpirationDate}");
                    return false;
                }

                // For new licenses, initialize the creation date and track usage info
                var license = new LicenseInfo
                {
                    BusinessName = businessName,
                    LicenseKey = licenseKey,
                    ExpirationDate = info.ExpirationDate,
                    Type = info.Type,
                    TerminalCount = info.TerminalCount,
                    HardwareId = hardwareId,
                    LicenseCreationDate = DateTime.UtcNow, // Add creation date
                    // Set MaxAllowedUsages based on license type
                    MaxAllowedUsages = DetermineMaxUsagesForLicenseType(info.Type)
                };

                SaveLicense(license);

                SaveActivation(hardwareId, licenseKey);
                Debug.WriteLine("License activation successful");
                
                // Initialize time tracking with the new license activation
                var timeData = LoadTimeTrackingData();
                timeData.LastValidSystemTime = DateTime.UtcNow;
                timeData.LastRunTime = DateTime.UtcNow;
                SaveTimeTrackingData(timeData);

                // Show success message and handle window closure
                var dispatcher = Application.Current?.Dispatcher;
                if (dispatcher != null)
                {
                    dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            var result = MessageBox.Show(
                                "License activated successfully. The application needs to restart to apply the changes. Click OK to restart now.",
                                "Activation Successful",
                                MessageBoxButton.OKCancel,
                                MessageBoxImage.Information);

                            if (result == MessageBoxResult.OK)
                            {
                                // Find and close the activation window if it exists
                                if (Application.Current != null)
                                {
                                    var activationWindow = Application.Current.Windows.OfType<Window>()
                                        .FirstOrDefault(w => w.GetType().Name == "LicenseActivationView");
                                    if (activationWindow != null)
                                    {
                                        activationWindow.Close();
                                    }
                                }

                                // Start new instance before closing current
                                try
                                {
                                    var startInfo = new System.Diagnostics.ProcessStartInfo
                                    {
                                        FileName = Application.ResourceAssembly.Location,
                                        UseShellExecute = true
                                    };
                                    System.Diagnostics.Process.Start(startInfo);

                                    // Shutdown the current instance
                                    if (Application.Current != null)
                                    {
                                        Application.Current.Shutdown();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.WriteLine($"Error restarting application: {ex.Message}");
                                    MessageBox.Show(
                                        "The application needs to be restarted manually to apply the changes.",
                                        "Restart Required",
                                        MessageBoxButton.OK,
                                        MessageBoxImage.Information);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error in license activation UI handling: {ex.Message}");
                            MessageBox.Show(
                                "License was activated but there was an error completing the process. Please restart the application manually.",
                                "Restart Required",
                                MessageBoxButton.OK,
                                MessageBoxImage.Warning);
                        }
                    });
                }
                else
                {
                    Debug.WriteLine("Could not access UI dispatcher - license activated but UI update skipped");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"License activation failed: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }
        
        // Determine maximum usages based on license type, used for additional time validation
        private int DetermineMaxUsagesForLicenseType(LicenseType type)
        {
            switch (type)
            {
                case LicenseType.Basic:
                    return 500; // After 500 uses, license needs to be revalidated
                case LicenseType.Standard:
                    return 1000;
                case LicenseType.Professional:
                    return 5000;
                case LicenseType.Enterprise:
                    return 0; // Unlimited for enterprise
                default:
                    return 500;
            }
        }

        private void SaveLicense(LicenseInfo license)
        {
            var json = JsonSerializer.Serialize(license);
            var encrypted = EncryptString(json);
            File.WriteAllText(GetLicenseFilePath(), encrypted);
        }

        private void SaveActivation(string hardwareId, string licenseKey)
        {
            var activation = new { HardwareId = hardwareId, LicenseKey = licenseKey };
            var json = JsonSerializer.Serialize(activation);
            var encrypted = EncryptString(json);
            File.WriteAllText(GetActivationFilePath(), encrypted);
        }

        private LicenseInfo LoadLicense()
        {
            try
            {
                var path = GetLicenseFilePath();
                if (!File.Exists(path))
                {
                    Debug.WriteLine($"License file not found at: {path}");
                    return null;
                }

                var encrypted = File.ReadAllText(path);
                var json = DecryptString(encrypted);
                return JsonSerializer.Deserialize<LicenseInfo>(json);
            }
            catch (FileNotFoundException)
            {
                // Gracefully handle missing file
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading license: {ex.Message}");
                return null;
            }
        }

        private string EncryptString(string text)
        {
            using (var aes = Aes.Create())
            {
                aes.Key = GetEncryptionKey(); // Use the derived key
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                {
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(text);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        private string DecryptString(string cipherText)
        {
            var fullCipher = Convert.FromBase64String(cipherText);

            using (var aes = Aes.Create())
            {
                var iv = new byte[16];
                var cipher = new byte[fullCipher.Length - 16];

                Buffer.BlockCopy(fullCipher, 0, iv, 0, iv.Length);
                Buffer.BlockCopy(fullCipher, iv.Length, cipher, 0, cipher.Length);

                aes.Key = GetEncryptionKey(); // Use the derived key
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipher))
                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (var srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        private bool ValidateLicenseKeyFormat(string licenseKey)
        {
            try
            {
                if (string.IsNullOrEmpty(licenseKey))
                {
                    Debug.WriteLine("License key is empty");
                    return false;
                }

                // Remove hyphens
                string hexKey = licenseKey.Replace("-", "");

                // Check if it's a valid hex string
                if (!System.Text.RegularExpressions.Regex.IsMatch(hexKey, @"^[0-9A-Fa-f]+$"))
                {
                    Debug.WriteLine("License key contains invalid characters");
                    return false;
                }

                // Convert hex string back to bytes
                byte[] allBytes = new byte[hexKey.Length / 2];
                for (int i = 0; i < allBytes.Length; i++)
                {
                    allBytes[i] = Convert.ToByte(hexKey.Substring(i * 2, 2), 16);
                }

                // Separate data and hash
                if (allBytes.Length <= 32)
                    return false;

                byte[] originalData = new byte[allBytes.Length - 32];
                byte[] originalHash = new byte[32];
                Buffer.BlockCopy(allBytes, 0, originalData, 0, originalData.Length);
                Buffer.BlockCopy(allBytes, originalData.Length, originalHash, 0, 32);

                // Compute hash of original data
                byte[] keyBytes = Encoding.UTF8.GetBytes(GetSecretKey()); // Use derived secret key
                using (var hmac = new HMACSHA256(keyBytes))
                {
                    byte[] computedHash = hmac.ComputeHash(originalData);
                    return computedHash.SequenceEqual(originalHash);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"License key validation failed: {ex.Message}");
                return false;
            }
        }

        private DateTime DecodeExpirationDate(string licenseKey)
        {
            try
            {
                var info = DecodeLicenseInfo(licenseKey);
                return info.ExpirationDate;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        private LicenseType DecodeLicenseType(string licenseKey)
        {
            try
            {
                var info = DecodeLicenseInfo(licenseKey);
                return info.Type;
            }
            catch
            {
                return LicenseType.Basic;
            }
        }

        private int DecodeTerminalCount(string licenseKey)
        {
            try
            {
                var info = DecodeLicenseInfo(licenseKey);
                return info.TerminalCount;
            }
            catch
            {
                return 1;
            }
        }

        private LicenseInfo DecodeLicenseInfo(string licenseKey)
        {
            try
            {
                Debug.WriteLine($"Decoding license key: {licenseKey}");

                // Remove hyphens
                string hexKey = licenseKey.Replace("-", "");

                // Convert hex string back to bytes
                byte[] allBytes = new byte[hexKey.Length / 2];
                for (int i = 0; i < allBytes.Length; i++)
                {
                    allBytes[i] = Convert.ToByte(hexKey.Substring(i * 2, 2), 16);
                }

                // Extract the original data (excluding the hash)
                byte[] originalData = new byte[allBytes.Length - 32];
                Buffer.BlockCopy(allBytes, 0, originalData, 0, originalData.Length);

                // Convert bytes to string
                string fullString = Encoding.UTF8.GetString(originalData);
                Debug.WriteLine($"Decoded full string: {fullString}");

                // Remove the salt from the end
                string jsonString = fullString.Substring(0, fullString.Length - KEY_DERIVATION_SALT.Length);
                Debug.WriteLine($"Extracted JSON: {jsonString}");

                // Deserialize the JSON string
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                var licenseInfo = JsonSerializer.Deserialize<LicenseInfo>(jsonString, options);

                Debug.WriteLine($"Decoded license info - Business: {licenseInfo.BusinessName}, Type: {licenseInfo.Type}, HardwareId: {licenseInfo.HardwareId}");

                return licenseInfo;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DecodeLicenseInfo: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw new Exception($"Error decoding license key: {ex.Message}");
            }
        }

        public void RemoveActivation()
        {
            try
            {
                string licenseFile = GetLicenseFilePath();
                string activationFile = GetActivationFilePath();
                
                if (File.Exists(licenseFile))
                {
                    File.Delete(licenseFile);
                }
                if (File.Exists(activationFile))
                {
                    File.Delete(activationFile);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing activation: {ex.Message}");
            }
        }

        // Time tracking data structure
        private class TimeTrackingData
        {
            public DateTime LastRunTime { get; set; } = DateTime.UtcNow;
            public DateTime FirstRunTime { get; set; } = DateTime.UtcNow;
            public List<DateTime> RecentRuns { get; set; } = new List<DateTime>();
            public int UsageCounter { get; set; } = 0;
            public DateTime LastValidSystemTime { get; set; } = DateTime.UtcNow;
            public Dictionary<string, DateTime> ReferenceFileTimes { get; set; } = new Dictionary<string, DateTime>();
        }
        
        // Save time tracking data
        private void SaveTimeTrackingData(TimeTrackingData data)
        {
            try
            {
                var json = JsonSerializer.Serialize(data);
                var encrypted = EncryptString(json);
                File.WriteAllText(GetTimeTrackingFilePath(), encrypted);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving time tracking data: {ex.Message}");
            }
        }
        
        // Load time tracking data
        private TimeTrackingData LoadTimeTrackingData()
        {
            try
            {
                string filePath = GetTimeTrackingFilePath();
                
                if (!File.Exists(filePath))
                {
                    var newData = InitializeTimeTrackingData();
                    SaveTimeTrackingData(newData);
                    return newData;
                }
                
                var encrypted = File.ReadAllText(filePath);
                var json = DecryptString(encrypted);
                return JsonSerializer.Deserialize<TimeTrackingData>(json);
            }
            catch
            {
                // If loading fails, create a new time tracking file
                var newData = InitializeTimeTrackingData();
                SaveTimeTrackingData(newData);
                return newData;
            }
        }
        
        // Initialize new time tracking data
        private TimeTrackingData InitializeTimeTrackingData()
        {
            var data = new TimeTrackingData
            {
                LastRunTime = DateTime.UtcNow,
                FirstRunTime = DateTime.UtcNow,
                RecentRuns = new List<DateTime> { DateTime.UtcNow },
                UsageCounter = 1,
                LastValidSystemTime = DateTime.UtcNow,
                ReferenceFileTimes = GetReferenceFileTimes()
            };
            return data;
        }
        
        // Get a dictionary of reference file timestamps
        private Dictionary<string, DateTime> GetReferenceFileTimes()
        {
            var referenceTimes = new Dictionary<string, DateTime>();
            try
            {
                // Use system files that shouldn't be modified
                string[] referenceFiles = {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "system32\\kernel32.dll"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "system32\\ntdll.dll"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "system32\\shell32.dll"),
                    Assembly.GetExecutingAssembly().Location
                };
                
                foreach (string file in referenceFiles)
                {
                    if (File.Exists(file))
                    {
                        referenceTimes[file] = File.GetLastWriteTimeUtc(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting reference file times: {ex.Message}");
            }
            return referenceTimes;
        }
        
        // Update time tracking data with current run
        private void UpdateTimeTrackingData(TimeTrackingData data)
        {
            // Increment usage counter
            data.UsageCounter++;
            
            // Add current time to recent runs and keep only the last 10
            data.RecentRuns.Add(DateTime.UtcNow);
            while (data.RecentRuns.Count > 10)
            {
                data.RecentRuns.RemoveAt(0);
            }
            
            // Update last run time if the current time seems valid
            if (IsCurrentTimeValid(data))
            {
                data.LastRunTime = DateTime.UtcNow;
                data.LastValidSystemTime = DateTime.UtcNow;
            }
            
            // Update reference file times - only those that exist and haven't been tampered with
            var currentReferenceTimes = GetReferenceFileTimes();
            foreach (var item in currentReferenceTimes)
            {
                // Only update if file already exists in our tracking and time hasn't decreased
                if (data.ReferenceFileTimes.ContainsKey(item.Key) && 
                    item.Value >= data.ReferenceFileTimes[item.Key])
                {
                    data.ReferenceFileTimes[item.Key] = item.Value;
                }
            }
            
            // Save updated data
            SaveTimeTrackingData(data);
        }
        
        // Verify if the current system time is valid
        private bool IsCurrentTimeValid(TimeTrackingData data)
        {
            DateTime now = DateTime.UtcNow;
            
            // Check 1: Is the current time less than the last run time (clock moved backwards)?
            if (now < data.LastRunTime.AddMinutes(-ALLOWED_TIME_DRIFT_MINUTES))
            {
                Debug.WriteLine($"Clock manipulation detected: Current time {now} is before last run time {data.LastRunTime}");
                return false;
            }
            
            // Check 2: Is the current time reasonably advanced from the first run time?
            if (data.UsageCounter > MAX_USAGE_WITHOUT_ADVANCE && now < data.FirstRunTime.AddDays(1))
            {
                Debug.WriteLine("High usage count without significant time advancement");
                return false;
            }
            
            // Check 3: Verify reference file timestamps haven't been tampered with
            foreach (var item in data.ReferenceFileTimes)
            {
                if (File.Exists(item.Key))
                {
                    DateTime currentTime = File.GetLastWriteTimeUtc(item.Key);
                    if (currentTime < item.Value.AddMinutes(-ALLOWED_TIME_DRIFT_MINUTES))
                    {
                        Debug.WriteLine($"File timestamp manipulation detected for {item.Key}");
                        return false;
                    }
                }
            }
            
            // Check 4: Time progression compared to recent runs
            if (data.RecentRuns.Count >= 3)
            {
                // If all recent timestamps are extremely close despite being separate runs,
                // this might indicate that the clock has been frozen
                bool allSimilar = true;
                DateTime referenceTime = data.RecentRuns[0];
                
                for (int i = 1; i < data.RecentRuns.Count; i++)
                {
                    TimeSpan diff = data.RecentRuns[i] - referenceTime;
                    if (Math.Abs(diff.TotalMinutes) > 5) // If any differ by more than 5 minutes
                    {
                        allSimilar = false;
                        break;
                    }
                }
                
                if (allSimilar && data.RecentRuns.Count >= 5)
                {
                    Debug.WriteLine("Clock appears to be frozen - multiple runs with nearly identical times");
                    return false;
                }
            }
            
            return true;
        }
        
        // Verify license with clock manipulation protection
        private bool VerifyTimeIntegrity()
        {
            try
            {
                var data = LoadTimeTrackingData();
                
                // Check if current time is valid
                bool isTimeValid = IsCurrentTimeValid(data);
                
                // Update time tracking data regardless of validation result
                // This helps track attempted manipulations
                UpdateTimeTrackingData(data);
                
                return isTimeValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying time integrity: {ex.Message}");
                return true; // In case of error, default to allowing access
            }
        }

        /// <summary>
        /// Properly shuts down the license service, canceling any background tasks.
        /// </summary>
        public void Shutdown()
        {
            try
            {
                // Cancel the background integrity check task
                if (_tamperDetectionCts != null && !_tamperDetectionCts.IsCancellationRequested)
                {
                    _tamperDetectionCts.Cancel();
                    _tamperDetectionCts.Dispose();
                    _tamperDetectionCts = null;
                }
                
                // Cancel memory protection task
                if (_memoryProtectionCts != null && !_memoryProtectionCts.IsCancellationRequested)
                {
                    _memoryProtectionCts.Cancel();
                    _memoryProtectionCts.Dispose();
                    _memoryProtectionCts = null;
                }
                
                // Clear license watermarks
                if (_licenseWatermarks != null)
                {
                    _licenseWatermarks.Clear();
                    _licenseWatermarks = null;
                }
                
                // Perform any final integrity checks or data saving if needed
                try
                {
                    SaveIntegrityData(CalculateAssemblyHash(), DateTime.UtcNow);
                    SaveTimeTrackingData(LoadTimeTrackingData());
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error during final data saving: {ex.Message}");
                }
                
                Debug.WriteLine("License service shutdown completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error during license service shutdown: {ex.Message}");
            }
        }

        /// <summary>
        /// Decrypts license data from the license file to get additional parameters
        /// </summary>
        /// <returns>Dictionary containing license parameters or null if unable to decrypt</returns>
        private Dictionary<string, string> DecryptLicenseData()
        {
            try
            {
                string licenseFile = GetLicenseFilePath();
                
                if (!File.Exists(licenseFile))
                    return null;
                
                var license = LoadLicense();
                if (license == null)
                    return null;
                
                // Basic parameters from the license object
                var result = new Dictionary<string, string>
                {
                    { "BusinessName", license.BusinessName },
                    { "LicenseKey", license.LicenseKey },
                    { "HardwareId", license.HardwareId },
                    { "ExpirationDate", license.ExpirationDate.ToString("yyyy-MM-dd") }
                };
                
                // Add LicenseCreationDate if available
                if (license.LicenseCreationDate > DateTime.MinValue)
                {
                    result["ActivationDate"] = license.LicenseCreationDate.ToString("yyyy-MM-dd");
                }
                
                // Try to extract additional parameters from the license key
                try
                {
                    var info = DecodeLicenseInfo(license.LicenseKey);
                    if (info != null)
                    {
                        // Convert enum to string if Type is an enum
                        result["Tier"] = info.Type.ToString();
                        
                        // Set max allowed usages based on license tier
                        string tierLower = result["Tier"].ToLower();
                        
                        if (tierLower == "basic")
                            result["MaxAllowedUsages"] = "500";
                        else if (tierLower == "standard")
                            result["MaxAllowedUsages"] = "1000";
                        else if (tierLower == "professional" || tierLower == "pro")
                            result["MaxAllowedUsages"] = "5000";
                        else if (tierLower == "enterprise")
                            result["MaxAllowedUsages"] = "unlimited";
                        else
                            result["MaxAllowedUsages"] = "100"; // Default for unknown types
                    }
                }
                catch
                {
                    // If we can't decode, use defaults
                    result["Tier"] = "Basic";
                    result["MaxAllowedUsages"] = "100";
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error decrypting license data: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Cleans all security-related files for development/testing purposes.
        /// Should only be used in development mode to reset security state.
        /// </summary>
        public void CleanSecurityFiles()
        {
            // Only allow this in development mode
            if (!_isDevelopmentMode)
            {
                Debug.WriteLine("Security file cleanup attempted in production mode - operation denied");
                return;
            }
            
            try
            {
                // Get directory info
                string licenseDir = GetLicenseDirectory();
                if (!Directory.Exists(licenseDir))
                    return;
                
                Debug.WriteLine($"Cleaning security files in: {licenseDir}");
                
                // Clean integrity files
                string integrityFilePath = GetIntegrityFilePath();
                if (File.Exists(integrityFilePath))
                {
                    File.Delete(integrityFilePath);
                    Debug.WriteLine($"Deleted integrity file: {integrityFilePath}");
                }
                
                // Clean time tracking files
                string timeFilePath = GetTimeTrackingFilePath();
                if (File.Exists(timeFilePath))
                {
                    File.Delete(timeFilePath);
                    Debug.WriteLine($"Deleted time tracking file: {timeFilePath}");
                }
                
                // Re-initialize integrity data
                string hash = CalculateAssemblyHash();
                DateTime now = DateTime.UtcNow;
                SaveIntegrityData(hash, now);
                
                // Re-initialize time tracking
                var timeData = InitializeTimeTrackingData();
                SaveTimeTrackingData(timeData);
                
                Debug.WriteLine("Security files cleaned and re-initialized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error cleaning security files: {ex.Message}");
            }
        }

        // Initialize the license data watermarking
        private Dictionary<string, byte[]> _licenseWatermarks;
        private readonly string[] _watermarkKeys = { "sig_a", "sig_b", "sig_c", "sig_d", "sig_e" };
        
        // Initialize license watermarking
        private void InitializeLicenseWatermarking()
        {
            try
            {
                _licenseWatermarks = new Dictionary<string, byte[]>();
                
                // Skip if in development mode or no license exists
                if (_isDevelopmentMode || !File.Exists(GetLicenseFilePath()))
                    return;
                
                // Load license
                var license = LoadLicense();
                if (license == null)
                    return;
                    
                // Create watermark fragments from license data
                CreateLicenseWatermarks(license);
                
                Debug.WriteLine("License watermarking initialized");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing license watermarking: {ex.Message}");
            }
        }
        
        // Create watermark fragments from license
        private void CreateLicenseWatermarks(LicenseInfo license)
        {
            try
            {
                if (license == null)
                    return;
                    
                // Clear existing watermarks
                _licenseWatermarks.Clear();
                
                // Create a hash of the entire license
                string licenseJson = JsonSerializer.Serialize(license);
                byte[] licenseBytes = Encoding.UTF8.GetBytes(licenseJson);
                
                using (var sha = SHA256.Create())
                {
                    byte[] fullHash = sha.ComputeHash(licenseBytes);
                    
                    // Split the hash into segments and distribute
                    int segmentSize = fullHash.Length / _watermarkKeys.Length;
                    
                    for (int i = 0; i < _watermarkKeys.Length; i++)
                    {
                        // Extract segment
                        byte[] segment = new byte[segmentSize];
                        Array.Copy(fullHash, i * segmentSize, segment, 0, segmentSize);
                        
                        // Mix with some unique factor to make each watermark different
                        byte[] mixed = MixBytes(segment, Encoding.UTF8.GetBytes(_watermarkKeys[i]));
                        
                        // Store watermark
                        _licenseWatermarks[_watermarkKeys[i]] = mixed;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating license watermarks: {ex.Message}");
            }
        }
        
        // Mix two byte arrays to create a unique derivative
        private byte[] MixBytes(byte[] a, byte[] b)
        {
            if (a == null || b == null || a.Length == 0)
                return a;
                
            byte[] result = new byte[a.Length];
            for (int i = 0; i < a.Length; i++)
            {
                result[i] = (byte)(a[i] ^ b[i % b.Length]);
            }
            return result;
        }
        
        // Get watermark by key
        private byte[] GetWatermark(string key)
        {
            if (_licenseWatermarks == null || !_licenseWatermarks.ContainsKey(key))
                return null;
                
            return _licenseWatermarks[key];
        }
        
        // Verify watermarks are intact
        private bool VerifyLicenseWatermarks()
        {
            try
            {
                // Skip in development mode
                if (_isDevelopmentMode)
                    return true;
                
                // If watermarks aren't initialized, do so now
                if (_licenseWatermarks == null || _licenseWatermarks.Count == 0)
                {
                    InitializeLicenseWatermarking();
                    
                    // First-time initialization always passes
                    return true;
                }
                
                // Load license to recalculate watermarks
                var license = LoadLicense();
                if (license == null)
                    return false;
                
                // Create temporary watermarks for verification
                var tempWatermarks = new Dictionary<string, byte[]>();
                string licenseJson = JsonSerializer.Serialize(license);
                byte[] licenseBytes = Encoding.UTF8.GetBytes(licenseJson);
                
                using (var sha = SHA256.Create())
                {
                    byte[] fullHash = sha.ComputeHash(licenseBytes);
                    int segmentSize = fullHash.Length / _watermarkKeys.Length;
                    
                    for (int i = 0; i < _watermarkKeys.Length; i++)
                    {
                        byte[] segment = new byte[segmentSize];
                        Array.Copy(fullHash, i * segmentSize, segment, 0, segmentSize);
                        tempWatermarks[_watermarkKeys[i]] = MixBytes(segment, Encoding.UTF8.GetBytes(_watermarkKeys[i]));
                    }
                }
                
                // Compare watermarks
                foreach (var key in _watermarkKeys)
                {
                    byte[] original = _licenseWatermarks[key];
                    byte[] recomputed = tempWatermarks[key];
                    
                    if (!CompareBytes(original, recomputed))
                    {
                        Debug.WriteLine($"License watermark verification failed for key: {key}");
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying license watermarks: {ex.Message}");
                return false;
            }
        }
        
        // Compare two byte arrays
        private bool CompareBytes(byte[] a, byte[] b)
        {
            if (a == null || b == null)
                return a == b;
                
            if (a.Length != b.Length)
                return false;
                
            for (int i = 0; i < a.Length; i++)
            {
                if (a[i] != b[i])
                    return false;
            }
            
            return true;
        }

        // Interface implementation methods
        public bool ActivateLicense(string licenseKey)
        {
            return ActivateLicense(licenseKey, "Default Business");
        }

        public void DeactivateLicense()
        {
            try
            {
                // Clear license files
                CleanSecurityFiles();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error deactivating license: {ex.Message}");
            }
        }

        public string GetLicenseInfo()
        {
            try
            {
                if (ValidateLicense())
                {
                    return "License is valid and active";
                }
                else
                {
                    return "License is invalid or expired";
                }
            }
            catch (Exception ex)
            {
                return $"Error getting license info: {ex.Message}";
            }
        }

        public bool IsLicenseExpired()
        {
            try
            {
                return !ValidateLicense();
            }
            catch (Exception)
            {
                return true; // Assume expired if we can't validate
            }
        }
    }
}