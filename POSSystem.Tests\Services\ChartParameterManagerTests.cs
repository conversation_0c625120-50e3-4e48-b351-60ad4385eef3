using System;
using Xunit;
using FluentAssertions;
using POSSystem.ViewModels.Dashboard;
using POSSystem.ViewModels;

namespace POSSystem.Tests.Services
{
    /// <summary>
    /// Tests for ChartParameterManager to ensure proper date range calculations
    /// </summary>
    public class ChartParameterManagerTests
    {
        private readonly ChartParameterManager _chartParameterManager;

        public ChartParameterManagerTests()
        {
            _chartParameterManager = new ChartParameterManager();
        }

        [Fact]
        public void GetDateRange_WithMonthPeriodAndIsCurrentMonthTrue_ShouldReturnCurrentMonthRange()
        {
            // Arrange
            var period = new TimePeriod
            {
                Type = TimePeriodType.Month,
                IsCurrentMonth = true,
                DisplayName = "TimePeriod_Month"
            };

            var now = DateTime.Now;
            var expectedStart = new DateTime(now.Year, now.Month, 1);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }

        [Fact]
        public void GetDateRange_WithMonthPeriodAndIsLastMonthTrue_ShouldReturnLastMonthRange()
        {
            // Arrange
            var period = new TimePeriod
            {
                Type = TimePeriodType.Month,
                IsLastMonth = true,
                DisplayName = "TimePeriod_Month"
            };

            var now = DateTime.Now;
            var today = now.Date;
            var expectedStart = new DateTime(today.Year, today.Month, 1).AddMonths(-1);
            var expectedEnd = new DateTime(today.Year, today.Month, 1).AddSeconds(-1);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().Be(expectedEnd);
        }

        [Fact]
        public void GetDateRange_WithMonthPeriodAndNoDaysOrFlags_ShouldDefaultToCurrentMonth()
        {
            // Arrange - This simulates the bug scenario where Month period has no specific configuration
            var period = new TimePeriod
            {
                Type = TimePeriodType.Month,
                IsCurrentMonth = false,
                IsLastMonth = false,
                Days = 0,
                DisplayName = "TimePeriod_Month"
            };

            var now = DateTime.Now;
            var expectedStart = new DateTime(now.Year, now.Month, 1);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }

        [Fact]
        public void GetDateRange_WithMonthPeriodAndDaysSet_ShouldUseDaysRange()
        {
            // Arrange
            var period = new TimePeriod
            {
                Type = TimePeriodType.Month,
                IsCurrentMonth = false,
                IsLastMonth = false,
                Days = 30,
                DisplayName = "TimePeriod_Month"
            };

            var now = DateTime.Now;
            var today = now.Date;
            var expectedStart = today.AddDays(-30);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }

        [Fact]
        public void GetDateRange_WithTodayPeriod_ShouldReturnTodayRange()
        {
            // Arrange
            var period = new TimePeriod
            {
                Type = TimePeriodType.Today,
                DisplayName = "TimePeriod_Today"
            };

            var now = DateTime.Now;
            var today = now.Date;

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(today);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }

        [Fact]
        public void GetDateRange_WithWeekPeriod_ShouldReturnLast7DaysRange()
        {
            // Arrange
            var period = new TimePeriod
            {
                Type = TimePeriodType.Week,
                DisplayName = "TimePeriod_Week"
            };

            var now = DateTime.Now;
            var today = now.Date;
            var expectedStart = today.AddDays(-7);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }

        [Fact]
        public void GetDateRange_WithNullPeriod_ShouldReturnLast30DaysRange()
        {
            // Arrange
            TimePeriod period = null;

            var now = DateTime.Now;
            var today = now.Date;
            var expectedStart = today.AddDays(-30);

            // Act
            var (start, end) = _chartParameterManager.GetDateRange(period);

            // Assert
            start.Should().Be(expectedStart);
            end.Should().BeCloseTo(now, TimeSpan.FromSeconds(1)); // Allow small time difference
        }
    }
}
