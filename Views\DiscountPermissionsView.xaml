<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.DiscountPermissionsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>

            <!-- Custom Styles -->
            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="Margin" Value="0,0,0,12"/>
            </Style>

            <Style x:Key="DescriptionTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="LineHeight" Value="16"/>
            </Style>

            <Style x:Key="CardStyle" TargetType="Border">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
            </Style>

            <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="Padding" Value="8,12"/>
                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                <Setter Property="BorderThickness" Value="0,0,1,1"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
            </Style>

            <Style x:Key="CompactTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Height" Value="32"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- Loading Overlay -->
        <Border Background="{DynamicResource MaterialDesignPaper}"
                Opacity="0.9"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                Panel.ZIndex="999">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,16"/>
                <TextBlock Text="{DynamicResource LoadingDiscountPermissions}"
                          Style="{StaticResource DescriptionTextStyle}"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
            <StackPanel Margin="0">
                <!-- Header Section -->
                <Border Style="{StaticResource CardStyle}" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:PackIcon Kind="ShieldAccount"
                                               Width="28"
                                               Height="28"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,16,0"/>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="{DynamicResource DiscountPermissionsManagement}"
                                      Style="{StaticResource SectionHeaderStyle}"
                                      FontSize="18"/>
                            <TextBlock Text="{DynamicResource DiscountPermissionsManagementDescription}"
                                      Style="{StaticResource DescriptionTextStyle}"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Role Selection Card -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource SelectUserRole}"
                                  Style="{StaticResource SectionHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="300"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0"
                                     ItemsSource="{Binding Roles}"
                                     SelectedItem="{Binding SelectedRole}"
                                     DisplayMemberPath="Name"
                                     materialDesign:HintAssist.Hint="{DynamicResource ChooseRoleToConfigureHint}"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Background="{DynamicResource MaterialDesignPaper}"
                                     BorderBrush="{DynamicResource MaterialDesignDivider}"/>

                            <TextBlock Grid.Column="1"
                                      Text="{DynamicResource SelectRoleDescription}"
                                      Style="{StaticResource DescriptionTextStyle}"
                                      VerticalAlignment="Center"
                                      Margin="16,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </Border>
                <!-- Permissions Configuration Card -->
                <Border Style="{StaticResource CardStyle}"
                        Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <StackPanel>
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="20"
                                                   Height="20"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,12,0"/>

                            <TextBlock Grid.Column="1"
                                      Text="{DynamicResource DiscountTypePermissions}"
                                      Style="{StaticResource SectionHeaderStyle}"
                                      VerticalAlignment="Center"/>

                            <Button Grid.Column="2"
                                   Command="{Binding SaveCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   materialDesign:ButtonAssist.CornerRadius="4"
                                   Padding="16,8">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ContentSave"
                                                               Width="16"
                                                               Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="{DynamicResource SaveChanges}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </Grid>

                        <!-- Enhanced DataGrid -->
                        <DataGrid ItemsSource="{Binding Permissions}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 CanUserReorderColumns="False"
                                 CanUserResizeRows="False"
                                 CanUserSortColumns="True"
                                 HeadersVisibility="Column"
                                 BorderThickness="1"
                                 BorderBrush="{DynamicResource MaterialDesignDivider}"
                                 Background="{DynamicResource MaterialDesignPaper}"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 SelectionMode="Single"
                                 GridLinesVisibility="Horizontal"
                                 HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                 RowHeight="48"
                                 ColumnHeaderHeight="40"
                                 materialDesign:DataGridAssist.CellPadding="8,4"
                                 materialDesign:DataGridAssist.ColumnHeaderPadding="8,8">

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource DataGridHeaderStyle}"/>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.Columns>
                                <!-- Discount Type Name with Icon -->
                                <DataGridTemplateColumn Header="{DynamicResource DiscountType}" Width="180" IsReadOnly="True">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Percent"
                                                                       Width="16"
                                                                       Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding DiscountTypeName}"
                                                          VerticalAlignment="Center"
                                                          FontWeight="Medium"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Active Toggle with Label -->
                                <DataGridTemplateColumn Header="{DynamicResource Active}" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ToggleButton IsChecked="{Binding IsActive, UpdateSourceTrigger=PropertyChanged}"
                                                         Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                         VerticalAlignment="Center"
                                                         HorizontalAlignment="Center"
                                                         ToolTip="{DynamicResource EnableDisableDiscountTypeTooltip}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Max Percentage -->
                                <DataGridTemplateColumn Header="{DynamicResource MaxPercentage}" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding MaxPercentage, UpdateSourceTrigger=PropertyChanged, StringFormat=N1}"
                                                    Style="{StaticResource CompactTextBoxStyle}"
                                                    materialDesign:HintAssist.Hint="0.00"
                                                    ToolTip="{DynamicResource MaxPercentageTooltip}"
                                                    HorizontalAlignment="Stretch"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Max Fixed Amount -->
                                <DataGridTemplateColumn Header="{DynamicResource MaxAmount}" Width="110">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding MaxFixedAmount, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                    Style="{StaticResource CompactTextBoxStyle}"
                                                    materialDesign:HintAssist.Hint="0.00"
                                                    ToolTip="{DynamicResource MaxFixedAmountTooltip}"
                                                    HorizontalAlignment="Stretch"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Min Price Percentage -->
                                <DataGridTemplateColumn Header="{DynamicResource MinPricePercentage}" Width="110">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding MinPricePercentage, UpdateSourceTrigger=PropertyChanged, StringFormat=N1}"
                                                    Style="{StaticResource CompactTextBoxStyle}"
                                                    materialDesign:HintAssist.Hint="0.00"
                                                    ToolTip="{DynamicResource MinPricePercentageTooltip}"
                                                    HorizontalAlignment="Stretch"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Requires Approval -->
                                <DataGridTemplateColumn Header="{DynamicResource Approval}" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ToggleButton IsChecked="{Binding RequiresApproval, UpdateSourceTrigger=PropertyChanged}"
                                                         Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                         VerticalAlignment="Center"
                                                         HorizontalAlignment="Center"
                                                         ToolTip="{DynamicResource RequireApprovalTooltip}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Approval Threshold -->
                                <DataGridTemplateColumn Header="{DynamicResource Threshold}" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding ApprovalThreshold, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                    Style="{StaticResource CompactTextBoxStyle}"
                                                    materialDesign:HintAssist.Hint="0.00"
                                                    ToolTip="{DynamicResource ApprovalThresholdTooltip}"
                                                    IsEnabled="{Binding RequiresApproval}"
                                                    HorizontalAlignment="Stretch"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- Help Text -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,16,0,0">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource ConfigurationGuide}"
                                          FontWeight="SemiBold"
                                          Foreground="{DynamicResource MaterialDesignBody}"
                                          Margin="0,0,0,8"/>
                                <TextBlock Style="{StaticResource DescriptionTextStyle}">
                                    <Run Text="• "/>
                                    <Run Text="{DynamicResource MaxPercentageGuide}"/>
                                    <LineBreak/>
                                    <Run Text="• "/>
                                    <Run Text="{DynamicResource MaxAmountGuide}"/>
                                    <LineBreak/>
                                    <Run Text="• "/>
                                    <Run Text="{DynamicResource MinPriceGuide}"/>
                                    <LineBreak/>
                                    <Run Text="• "/>
                                    <Run Text="{DynamicResource ApprovalGuide}"/>
                                    <LineBreak/>
                                    <Run Text="• "/>
                                    <Run Text="{DynamicResource ThresholdGuide}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>