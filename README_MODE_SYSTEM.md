# POS System Mode Manager

## Overview
This new Mode Manager provides a comprehensive solution for switching between development and production modes in your POS System. It replaces the previous batch files with a single, user-friendly interface that handles all mode-related operations.

## Features

### 1. Visual Mode Indicator
- Clearly shows the current system mode with colored, highlighted indicators
- Makes it immediately obvious if you're in development or production mode

### 2. Complete Mode Management
- **Enable Development Mode**: Bypasses license activation for testing
- **Enable Production Mode**: Enforces license activation for normal operation
- **Check System Status**: Comprehensive diagnostics of all mode-related settings
- **View Mode History**: Tracks all mode changes with timestamps and usernames
- **Repair Mode Configuration**: Fixes inconsistent settings and ensures proper configuration

### 3. Enhanced Security
- Requires administrator privileges for proper operation
- Creates a hidden log file to track all mode changes
- Creates special desktop shortcuts for development mode to prevent accidental use

### 4. Multi-Layer Approach
The system manages all possible ways development mode can be enabled/disabled:
- `dev_mode.txt` file (content true/false)
- `dev_mode_disabled` blocker file
- POS_DEVELOPMENT_MODE environment variable
- Registry keys in HKCU\Software\POSSystem
- App.config settings

## How to Use

1. **Run as Administrator**: Right-click `pos_mode_manager.bat` and select "Run as Administrator"
2. **Select Operation**: Choose from the main menu options based on your needs
3. **Follow Prompts**: The system will guide you through any additional steps

## Important Notes

- **Development Mode** should only be used for testing or development purposes
- **Production Mode** is the normal operation mode that requires license activation
- Always restart the application after changing modes
- If you have a **DEBUG build** of the application, it may override these settings - contact your developer for a RELEASE build

## Troubleshooting

If you encounter issues with mode switching:
1. Use the "Check System Status" option to diagnose the current configuration
2. Use the "Repair Mode Configuration" option to reset all settings to a consistent state
3. If problems persist, consult your system administrator or developer

## Technical Details

The Mode Manager modifies the following items:
- `dev_mode.txt` file in the application directory
- `dev_mode_disabled` file in the application directory
- POS_DEVELOPMENT_MODE environment variable
- Registry keys in HKCU\Software\POSSystem
- DevModeDisabled setting in App.config (if present)
- Creates special desktop shortcuts when in development mode

These changes ensure that the application correctly respects the development mode settings in both `App.xaml.cs` and `LicenseService.cs`. 