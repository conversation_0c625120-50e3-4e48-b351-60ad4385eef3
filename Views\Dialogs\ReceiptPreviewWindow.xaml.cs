using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using Microsoft.Win32;
using POSSystem.Models;
using POSSystem.Services.Printing;
using POSSystem.Services.Interfaces;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Receipt preview window with print and export functionality
    /// </summary>
    public partial class ReceiptPreviewWindow : Window
    {
        private readonly Sale _sale;
        private readonly IEnhancedReceiptPrintService _receiptPrintService;
        private FlowDocument _receiptDocument;
        private double _currentZoom = 100;

        public ReceiptPreviewWindow(Sale sale, IEnhancedReceiptPrintService receiptPrintService = null)
        {
            InitializeComponent();
            
            _sale = sale ?? throw new ArgumentNullException(nameof(sale));
            _receiptPrintService = receiptPrintService ?? new EnhancedReceiptPrintService();
            
            LoadReceiptPreview();
        }

        private async void LoadReceiptPreview()
        {
            try
            {
                // Update header with invoice number
                InvoiceNumberText.Text = $"Invoice #: {_sale.InvoiceNumber}";
                
                // Get default template and create document
                var templates = await _receiptPrintService.GetReceiptTemplatesAsync();
                var defaultTemplate = templates.FirstOrDefault(t => t.IsDefault) ?? templates.FirstOrDefault();
                
                if (defaultTemplate == null)
                {
                    MessageBox.Show("No receipt template found. Please configure receipt templates in settings.", 
                        "Template Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    Close();
                    return;
                }

                // Create receipt document using the enhanced service's internal method
                // Since we can't access the private method directly, we'll create our own document
                _receiptDocument = await CreateReceiptDocumentAsync(_sale, defaultTemplate);
                
                // Convert to XPS for preview
                var xpsDocument = ConvertFlowDocumentToXps(_receiptDocument);
                ReceiptDocumentViewer.Document = xpsDocument.GetFixedDocumentSequence();
                
                // Set initial zoom
                ReceiptDocumentViewer.Zoom = _currentZoom;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading receipt preview: {ex.Message}", 
                    "Preview Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
            }
        }

        private async Task<FlowDocument> CreateReceiptDocumentAsync(Sale sale, Models.Printing.ReceiptTemplate template)
        {
            return await Task.Run(() =>
            {
                var document = new FlowDocument();
                document.PageWidth = template.PaperWidth * 8; // Convert to pixels
                document.FontSize = template.FontSize;
                document.FontFamily = new System.Windows.Media.FontFamily("Consolas, Courier New");
                document.PagePadding = new Thickness(10);
                
                // Add company header if enabled
                if (template.IncludeCompanyInfo)
                {
                    AddCompanyHeader(document);
                }
                
                // Add receipt header
                AddReceiptHeader(document, sale);
                
                // Add customer info if enabled
                if (template.IncludeCustomerInfo && sale.Customer != null)
                {
                    AddCustomerInfo(document, sale.Customer);
                }
                
                // Add items if enabled
                if (template.IncludeItemDetails)
                {
                    AddItemDetails(document, sale);
                }
                
                // Add totals
                AddTotals(document, sale);
                
                // Add payment info if enabled
                if (template.IncludePaymentInfo)
                {
                    AddPaymentInfo(document, sale);
                }
                
                // Add footer
                if (!string.IsNullOrEmpty(template.FooterText))
                {
                    AddFooter(document, template.FooterText);
                }
                
                return document;
            });
        }

        private XpsDocument ConvertFlowDocumentToXps(FlowDocument flowDocument)
        {
            var tempFileName = Path.GetTempFileName();
            var xpsDocument = new XpsDocument(tempFileName, FileAccess.ReadWrite);
            
            var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
            xpsWriter.Write(((IDocumentPaginatorSource)flowDocument).DocumentPaginator);
            
            return xpsDocument;
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private async void Print_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintButton.IsEnabled = false;
                PrintButton.Content = "Printing...";

                bool success = await _receiptPrintService.PrintReceiptAsync(_sale, true);
                
                if (success)
                {
                    MessageBox.Show("Receipt sent to printer successfully.", 
                        "Print Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing receipt: {ex.Message}", 
                    "Print Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                PrintButton.IsEnabled = true;
                PrintButton.Content = "Print";
            }
        }

        private async void SaveAsPdf_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "Save Receipt as PDF",
                    Filter = "PDF files (*.pdf)|*.pdf|XPS files (*.xps)|*.xps|All files (*.*)|*.*",
                    DefaultExt = "pdf",
                    FileName = $"Receipt_{_sale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    SaveAsPdfButton.IsEnabled = false;
                    SaveAsPdfButton.Content = "Saving...";

                    bool success = await _receiptPrintService.SaveReceiptAsPdfAsync(_sale, saveFileDialog.FileName);
                    
                    if (success)
                    {
                        MessageBox.Show($"Receipt saved successfully to:\n{saveFileDialog.FileName}", 
                            "Save Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving receipt: {ex.Message}", 
                    "Save Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveAsPdfButton.IsEnabled = true;
                SaveAsPdfButton.Content = "Save as PDF";
            }
        }

        private void ZoomIn_Click(object sender, RoutedEventArgs e)
        {
            _currentZoom = Math.Min(_currentZoom + 25, 500);
            ReceiptDocumentViewer.Zoom = _currentZoom;
        }

        private void ZoomOut_Click(object sender, RoutedEventArgs e)
        {
            _currentZoom = Math.Max(_currentZoom - 25, 25);
            ReceiptDocumentViewer.Zoom = _currentZoom;
        }

        private void FitToWidth_Click(object sender, RoutedEventArgs e)
        {
            ReceiptDocumentViewer.FitToWidth();
        }

        // Helper methods for document creation (simplified versions)
        private void AddCompanyHeader(FlowDocument document)
        {
            var headerParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };
            
            headerParagraph.Inlines.Add(new Run("Your Business Name") { FontWeight = FontWeights.Bold, FontSize = 16 });
            headerParagraph.Inlines.Add(new LineBreak());
            headerParagraph.Inlines.Add(new Run("Business Address"));
            headerParagraph.Inlines.Add(new LineBreak());
            headerParagraph.Inlines.Add(new Run("Phone Number"));
            
            document.Blocks.Add(headerParagraph);
            document.Blocks.Add(new Paragraph(new Run(new string('-', 48))) { TextAlignment = TextAlignment.Center });
        }

        private void AddReceiptHeader(FlowDocument document, Sale sale)
        {
            var headerParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 5, 0, 10)
            };
            
            headerParagraph.Inlines.Add(new Run("RECEIPT") { FontWeight = FontWeights.Bold, FontSize = 14 });
            headerParagraph.Inlines.Add(new LineBreak());
            headerParagraph.Inlines.Add(new Run($"Invoice #: {sale.InvoiceNumber}"));
            headerParagraph.Inlines.Add(new LineBreak());
            headerParagraph.Inlines.Add(new Run($"Date: {sale.SaleDate:yyyy-MM-dd HH:mm}"));
            
            document.Blocks.Add(headerParagraph);
        }

        private void AddCustomerInfo(FlowDocument document, Customer customer)
        {
            var customerParagraph = new Paragraph
            {
                Margin = new Thickness(0, 5, 0, 5)
            };
            
            customerParagraph.Inlines.Add(new Run("Customer: ") { FontWeight = FontWeights.Bold });
            customerParagraph.Inlines.Add(new Run(customer.FullName));
            if (!string.IsNullOrEmpty(customer.Phone))
            {
                customerParagraph.Inlines.Add(new LineBreak());
                customerParagraph.Inlines.Add(new Run($"Phone: {customer.Phone}"));
            }
            
            document.Blocks.Add(customerParagraph);
            document.Blocks.Add(new Paragraph(new Run(new string('-', 48))) { TextAlignment = TextAlignment.Center });
        }

        private void AddItemDetails(FlowDocument document, Sale sale)
        {
            foreach (var item in sale.Items)
            {
                var itemParagraph = new Paragraph { Margin = new Thickness(0, 2, 0, 2) };
                itemParagraph.Inlines.Add(new Run($"{item.Product?.Name ?? "Unknown Item"}"));
                itemParagraph.Inlines.Add(new LineBreak());
                itemParagraph.Inlines.Add(new Run($"  {item.Quantity} x {item.UnitPrice:N2} = {item.Total:N2} DA"));
                document.Blocks.Add(itemParagraph);
            }
            
            document.Blocks.Add(new Paragraph(new Run(new string('-', 48))) { TextAlignment = TextAlignment.Center });
        }

        private void AddTotals(FlowDocument document, Sale sale)
        {
            var totalsParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 5, 0, 5)
            };
            
            totalsParagraph.Inlines.Add(new Run($"Subtotal: {sale.Subtotal:N2} DA"));
            if (sale.DiscountAmount > 0)
            {
                totalsParagraph.Inlines.Add(new LineBreak());
                totalsParagraph.Inlines.Add(new Run($"Discount: -{sale.DiscountAmount:N2} DA"));
            }
            if (sale.TaxAmount > 0)
            {
                totalsParagraph.Inlines.Add(new LineBreak());
                totalsParagraph.Inlines.Add(new Run($"Tax: {sale.TaxAmount:N2} DA"));
            }
            totalsParagraph.Inlines.Add(new LineBreak());
            totalsParagraph.Inlines.Add(new Run($"TOTAL: {sale.GrandTotal:N2} DA") { FontWeight = FontWeights.Bold, FontSize = 14 });
            
            document.Blocks.Add(totalsParagraph);
        }

        private void AddPaymentInfo(FlowDocument document, Sale sale)
        {
            var paymentParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 5, 0, 5)
            };
            
            paymentParagraph.Inlines.Add(new Run($"Payment Method: {sale.PaymentMethod}"));
            paymentParagraph.Inlines.Add(new LineBreak());
            paymentParagraph.Inlines.Add(new Run($"Amount Paid: {sale.AmountPaid:N2} DA"));
            
            if (sale.PaymentMethod == "Cash" && sale.AmountPaid > sale.GrandTotal)
            {
                var change = sale.AmountPaid - sale.GrandTotal;
                paymentParagraph.Inlines.Add(new LineBreak());
                paymentParagraph.Inlines.Add(new Run($"Change: {change:N2} DA"));
            }
            
            document.Blocks.Add(paymentParagraph);
        }

        private void AddFooter(FlowDocument document, string footerText)
        {
            document.Blocks.Add(new Paragraph(new Run(new string('-', 48))) { TextAlignment = TextAlignment.Center });
            
            var footerParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 10, 0, 0)
            };
            
            footerParagraph.Inlines.Add(new Run(footerText));
            footerParagraph.Inlines.Add(new LineBreak());
            footerParagraph.Inlines.Add(new Run("Thank you for your business!"));
            
            document.Blocks.Add(footerParagraph);
        }
    }
}
