<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Layouts.SalesViewCompact"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Layouts"
             xmlns:views="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource MaterialDesignBackground}">

    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:LengthToVisibilityConverter x:Key="LengthToVisibilityConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStockColorConverter x:Key="BooleanToStockColorConverter"/>
        <converters:TotalStockConverter x:Key="TotalStockConverter"/>
        <converters:StockDisplayConverter x:Key="StockDisplayConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        
        <!-- Colors -->
        <SolidColorBrush x:Key="SearchBackgroundBrush" Color="#f5f7fa"/>
        <SolidColorBrush x:Key="PrimaryActionBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="PopularItemsBrush" Color="#673AB7"/>
        <SolidColorBrush x:Key="RecentSalesBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="DiscountBrush" Color="#E91E63"/>
        <SolidColorBrush x:Key="NeutralBrush" Color="#607D8B"/>
        <SolidColorBrush x:Key="FavoritesBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="PageBackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="CartBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="ProductCardBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="SectionBackgroundBrush" Color="#FFFFFF"/>
    </UserControl.Resources>

    <!-- DialogHost for localized dialogs -->
    <materialDesign:DialogHost Identifier="SalesDialog">
        <Grid Margin="8" Background="{StaticResource PageBackgroundBrush}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="350"/>
        </Grid.ColumnDefinitions>

        <!-- Left Side - Products -->
        <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" UniformCornerRadius="8" Background="{StaticResource SectionBackgroundBrush}" materialDesign:ElevationAssist.Elevation="Dp1">
            <Grid Margin="8">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search and Category Filter -->
                <Grid Grid.Row="0" Margin="0,0,0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtSearch"
                            Grid.Column="0"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            materialDesign:HintAssist.Hint="{DynamicResource ProductSearchHint}"
                            materialDesign:TextFieldAssist.HasClearButton="True"
                            materialDesign:TextFieldAssist.PrefixText="🔍"
                            Margin="0,0,8,0"
                            Height="40"/>

                    <ComboBox Grid.Column="1"
                             x:Name="categoryFilter"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"
                             materialDesign:HintAssist.Hint="{DynamicResource Category}"
                             DisplayMemberPath="Name"
                             Width="150"
                             Height="40"
                             Margin="0,0,8,0"/>

                    <Button Grid.Column="2"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="40">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource Add}"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- Quick Filters -->
                <WrapPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,8">
                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                            Height="32"
                            Background="{StaticResource PopularItemsBrush}"
                            Foreground="White"
                            Margin="0,0,4,0"
                            Padding="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TrendingUp" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource Popular}"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                            Height="32"
                            Background="{StaticResource FavoritesBrush}"
                            Foreground="White"
                            Margin="0,0,4,0"
                            Padding="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Heart" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource Favorites}"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                            Height="32"
                            Background="{StaticResource NeutralBrush}"
                            Foreground="White"
                            Margin="0,0,4,0"
                            Padding="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource ClearFilter}"/>
                        </StackPanel>
                    </Button>
                </WrapPanel>

                <!-- Products List -->
                <ScrollViewer x:Name="productsScrollViewer" Grid.Row="2"
                              HorizontalScrollBarVisibility="Disabled"
                              VerticalScrollBarVisibility="Auto"
                              Background="Transparent"
                              ScrollChanged="ProductsScrollViewer_ScrollChanged">
                    <ItemsControl ItemsSource="{Binding FilteredProducts}"
                                  x:Name="productsList">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <!-- Compact Product Card -->
                                <materialDesign:Card Width="150"
                                                  Height="180"
                                                  Background="{StaticResource ProductCardBackgroundBrush}"
                                                  UniformCornerRadius="8"
                                                  Margin="4"
                                                  materialDesign:ElevationAssist.Elevation="Dp1"
                                                  MouseDown="Product_MouseDown">
                                    <Grid Margin="8">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="80"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- Image -->
                                        <Border Grid.Row="0" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1" CornerRadius="4">
                                            <Image Source="{Binding ImageData, Converter={StaticResource Base64ToImageConverter}}" Stretch="Uniform"/>
                                        </Border>

                                        <!-- Name -->
                                        <TextBlock Grid.Row="1" Text="{Binding Name}" TextTrimming="CharacterEllipsis" Margin="0,4,0,2"/>

                                        <!-- Price -->
                                        <TextBlock Grid.Row="2" Text="{Binding SellingPrice, StringFormat={}{0:N2} DA}" FontWeight="Bold" Margin="0,0,0,4"/>

                                        <!-- Add Button -->
                                        <Button Grid.Row="3" Style="{StaticResource MaterialDesignFlatButton}" Height="24" FontSize="11"
                                                Command="{Binding DataContext.AddToCartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="CartPlus" Width="12" Height="12" Margin="0,0,4,0"/>
                                                <TextBlock Text="{DynamicResource Add}"/>
                                            </StackPanel>
                                        </Button>

                                        <!-- Enhanced Out-of-Stock Overlay with Reservation Option -->
                                        <Border Grid.RowSpan="4"
                                                CornerRadius="8"
                                                Panel.ZIndex="3">
                                            <Border.Background>
                                                <!-- Semi-transparent overlay to show product info underneath -->
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1" Opacity="0.85">
                                                    <GradientStop Color="#1F2937" Offset="0.0"/>
                                                    <GradientStop Color="#111827" Offset="1.0"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="*"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Product name and price visible when out of stock -->
                                                <StackPanel Grid.Row="0" Margin="8,8,8,4">
                                                    <TextBlock Text="{Binding Name}"
                                                               Foreground="White"
                                                               FontWeight="SemiBold"
                                                               FontSize="11"
                                                               TextAlignment="Center"
                                                               TextWrapping="Wrap"
                                                               MaxHeight="30"
                                                               TextTrimming="CharacterEllipsis"/>
                                                    <TextBlock Text="{Binding SellingPrice, StringFormat={}{0:N2} DA}"
                                                               Foreground="#E5E7EB"
                                                               FontWeight="Medium"
                                                               FontSize="10"
                                                               TextAlignment="Center"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>

                                                <!-- Out of stock indicator and reservation button -->
                                                <StackPanel Grid.Row="2"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Bottom"
                                                            Margin="8,4,8,8">
                                                    <materialDesign:PackIcon Kind="AlertCircleOutline"
                                                                             Width="18" Height="18"
                                                                             Foreground="#EF4444"
                                                                             HorizontalAlignment="Center"
                                                                             Margin="0,0,0,4"/>
                                                    <TextBlock Text="{DynamicResource OutOfStock}"
                                                               Foreground="#EF4444"
                                                               FontWeight="Bold"
                                                               HorizontalAlignment="Center"
                                                               FontSize="9"
                                                               Margin="0,0,0,8"/>

                                                    <!-- Reserve button for creating reservation invoice -->
                                                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Background="#3B82F6"
                                                            BorderBrush="#2563EB"
                                                            Foreground="White"
                                                            FontSize="8"
                                                            Height="20"
                                                            MinWidth="50"
                                                            Padding="8,2"
                                                            Click="CreateReservationInvoice_Click"
                                                            Tag="{Binding}"
                                                            ToolTip="Create a reservation invoice for this out-of-stock product"
                                                            Visibility="{Binding DataContext.CanCreateInvoices, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <StackPanel Orientation="Horizontal">
                                                            <materialDesign:PackIcon Kind="CalendarClock"
                                                                                     Width="8" Height="8"
                                                                                     VerticalAlignment="Center"
                                                                                     Margin="0,0,3,0"/>
                                                            <TextBlock Text="Reserve"
                                                                       VerticalAlignment="Center"
                                                                       FontSize="8"
                                                                       FontWeight="Medium"/>
                                                        </StackPanel>
                                                    </Button>
                                                </StackPanel>
                                            </Grid>
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsOutOfStock}" Value="True">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                        </Border>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>

        <!-- Right Side - Cart -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Customer Info -->
            <materialDesign:Card Grid.Row="0" Background="{StaticResource HeaderBackgroundBrush}" UniformCornerRadius="8" Margin="0,0,0,8" Padding="8,4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="0" Style="{StaticResource MaterialDesignIconButton}" Width="28" Height="28" Padding="0">
                        <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16"/>
                    </Button>

                    <TextBlock Grid.Column="1" Text="Select Customer" VerticalAlignment="Center" Margin="8,0"/>

                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" Width="28" Height="28" Padding="0">
                        <materialDesign:PackIcon Kind="Tag" Width="16" Height="16"/>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Cart -->
            <materialDesign:Card Grid.Row="1" Background="{StaticResource CartBackgroundBrush}" UniformCornerRadius="8" Margin="0,0,0,8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Cart Tabs -->
                    <TabControl Grid.Row="0" Style="{StaticResource MaterialDesignTabControl}" Height="40"/>

                    <!-- Cart Items -->
                    <ListView Grid.Row="1" BorderThickness="0" Background="Transparent"
                              ItemsSource="{Binding CurrentCart.Items}">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="{Binding Product.Name}" FontWeight="Medium" TextTrimming="CharacterEllipsis"/>
                                        <TextBlock Text="{Binding Total, StringFormat={}{0:N2} DA}" FontSize="11"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="8,0">
                                        <Button Style="{StaticResource MaterialDesignIconButton}" Width="20" Height="20" Padding="0"
                                                Click="DecreaseQuantity_Click">
                                            <materialDesign:PackIcon Kind="Minus" Width="12" Height="12"/>
                                        </Button>
                                        <TextBlock Text="{Binding Quantity}" Width="20" TextAlignment="Center" VerticalAlignment="Center"/>
                                        <Button Style="{StaticResource MaterialDesignIconButton}" Width="20" Height="20" Padding="0"
                                                Click="IncreaseQuantity_Click">
                                            <materialDesign:PackIcon Kind="Plus" Width="12" Height="12"/>
                                        </Button>
                                    </StackPanel>

                                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" Width="20" Height="20" Padding="0"
                                            Click="RemoveItem_Click">
                                        <materialDesign:PackIcon Kind="Delete" Width="12" Height="12"/>
                                    </Button>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </materialDesign:Card>

            <!-- Totals and Payment -->
            <materialDesign:Card Grid.Row="2" Background="{StaticResource SectionBackgroundBrush}" UniformCornerRadius="8">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Subtotal -->
                    <Grid Grid.Row="0" Margin="0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="{DynamicResource Subtotal}" FontWeight="Medium"/>
                        <TextBlock Grid.Column="1" Text="{Binding CurrentCart.Subtotal, StringFormat={}{0:N2} DA}" FontWeight="Medium"/>
                    </Grid>

                    <!-- Grand Total -->
                    <Border Grid.Row="1" Background="#121212" CornerRadius="4" Padding="8,4" Margin="0,0,0,8">
                        <TextBlock Text="{Binding CurrentCart.GrandTotal, StringFormat={}{0:N2} DA}"
                                   Foreground="#00FF00"
                                   FontFamily="Consolas"
                                   FontSize="24"
                                   HorizontalAlignment="Center"
                                   TextOptions.TextFormattingMode="Display"
                                   TextOptions.TextRenderingMode="ClearType"
                                   TextOptions.TextHintingMode="Fixed"
                                   UseLayoutRounding="True"
                                   SnapsToDevicePixels="True"/>
                    </Border>

                    <!-- Pay Button -->
                    <Button Grid.Row="2" Style="{StaticResource MaterialDesignFlatButton}" Height="40" Background="{StaticResource PrimaryActionBrush}" Foreground="White"
                            Click="ProcessPayment_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CreditCard" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource Pay}"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
    </materialDesign:DialogHost>
</UserControl>