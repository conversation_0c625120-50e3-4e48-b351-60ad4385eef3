using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Views.Dialogs;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views
{
    public partial class CategoriesView : UserControl
    {
        private CategoriesViewModel ViewModel => (CategoriesViewModel)DataContext;
        private string currentSearchText = "";
        private System.Timers.Timer _searchTimer;
        private const int SEARCH_DELAY_MS = 300;

        public CategoriesView()
        {
            InitializeComponent();
            DataContext = new CategoriesViewModel();

            // Initialize search timer
            _searchTimer = new System.Timers.Timer(SEARCH_DELAY_MS);
            _searchTimer.Elapsed += async (s, e) => await Application.Current.Dispatcher.InvokeAsync(ApplyFilters);
            _searchTimer.AutoReset = false;

            // Subscribe to category updates
            CategoriesViewModel.CategoryChanged += OnCategoryChanged;

            // Unsubscribe and dispose VM when control is unloaded
            Unloaded += (s, e) => {
                CategoriesViewModel.CategoryChanged -= OnCategoryChanged;
                try { (DataContext as CategoriesViewModel)?.Dispose(); } catch {}
            };
        }

        private void OnCategoryChanged(object sender, CategoryUpdateEventArgs e)
        {
            // Only refresh the filters, don't reload categories as that will be handled by the ViewModel
            Dispatcher.InvokeAsync(() =>
            {
                ApplyFilters();
            });
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            currentSearchText = txtSearch.Text.Trim().ToLower();
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void SearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                _searchTimer.Stop();
                ApplyFilters();
            }
        }

        private void ApplyFilters()
        {
            var filteredCategories = ViewModel.AllCategories.AsEnumerable();

            if (!string.IsNullOrEmpty(currentSearchText))
            {
                filteredCategories = filteredCategories.Where(c =>
                    c.Name.ToLower().Contains(currentSearchText) ||
                    (c.Description?.ToLower().Contains(currentSearchText) ?? false));
            }

            ViewModel.Categories = new ObservableCollection<Category>(filteredCategories);
        }

        private async void AddNewCategory_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CategoryDialog();
            var result = await DialogHost.Show(dialog, "RootDialog");
            
            if (result is Category newCategory)
            {
                // The category has already been added to the database by the dialog
                // Just refresh the category list in the view model
                await ViewModel.LoadCategoriesAsync();
            }
        }

        private async void EditCategory_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var category = (Category)button.DataContext;
            
            var dialog = new CategoryDialog(category);
            var result = await DialogHost.Show(dialog, "RootDialog");
            
            if (result is Category updatedCategory)
            {
                // The category has already been updated in the database by the dialog
                // Just refresh the category list in the view model
                await ViewModel.LoadCategoriesAsync();
            }
        }

        private async void DeleteCategory_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var category = (Category)button.DataContext;

            if (category.Id == 1)
            {
                MessageBox.Show(
                    (string)Application.Current.Resources["CannotDeleteDefaultCategory"], 
                    (string)Application.Current.Resources["WarningTitle"], 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                (string)Application.Current.Resources["ConfirmDeleteCategory"],
                (string)Application.Current.Resources["ConfirmDeleteTitle"],
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                await ViewModel.DeleteCategory(category.Id);
            }
        }
    }
} 