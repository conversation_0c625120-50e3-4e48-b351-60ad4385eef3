using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Services.UI;
using POSSystem.Services.Performance;
using POSSystem.ViewModels;
using POSSystem.Models;
using POSSystem.Views;
using System.Windows.Controls.Primitives;
using POSSystem.Commands;
using System.Linq;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.ObjectModel;
using System.Windows.Data;
using System.Windows.Media.Animation;
using System.Diagnostics;
using POSSystem.Views.Dialogs;
using POSSystem.Data;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Windows.Threading;
using System.ComponentModel;
using System.Threading;


namespace POSSystem.Views.Layouts
{
    /// <summary>
    /// Interaction logic for SalesViewGrid.xaml
    /// </summary>
    public partial class SalesViewGrid : UserControl
    {
        private ISettingsService _settingsService;
        private SaleViewModel ViewModel => (SaleViewModel)DataContext;
        // ✅ CRITICAL FIX: Lazy-load DatabaseService to prevent constructor blocking
        private DatabaseService _dbService;

        // Timer for debouncing search input
        private System.Timers.Timer _searchTimer;
        private const int SEARCH_DELAY_MS = 350; // slightly higher debounce to reduce churn


            // Cancellation for search/filter operations
            private CancellationTokenSource _searchCancellation;

        // ✅ GRID LAYOUT: Constants for responsive grid calculation
        private const double PRODUCT_CARD_WIDTH = 142; // 130 + 12 margin (6+6)
        private const double PRODUCT_CARD_HEIGHT = 182; // 170 + 12 margin (6+6)
        private const double MIN_COLUMNS = 2;
        private const double MAX_COLUMNS = 12;

        // ✅ CRITICAL FIX: Lazy-load DatabaseService property
        private DatabaseService DbService => _dbService ??= new DatabaseService();

        // ✅ FIX: Add flag to prevent duplicate product selection events
        private bool _isProcessingProductSelection = false;
        private readonly object _productSelectionLock = new object();

        public SalesViewGrid()
        {
            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] SalesViewGrid constructor called");

            // ✅ CRITICAL FIX: Minimize constructor work to prevent UI blocking
            InitializeComponent();

            // Defer heavy initialization to Loaded event
            Loaded += SalesViewGrid_Loaded;
            Unloaded += OnUnloaded;

            // ✅ GRID LAYOUT: Subscribe to size changes for responsive grid
            SizeChanged += OnSizeChanged;

            // ✅ PRODUCT REFRESH FIX: Subscribe to DataContext changes to handle event subscriptions
            DataContextChanged += SalesViewGrid_DataContextChanged;

            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] SalesViewGrid constructor completed");
        }

        /// <summary>
        /// ✅ PRODUCT REFRESH FIX: Handle DataContext changes to properly subscribe to events
        /// </summary>
        private void SalesViewGrid_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] DataContext changed from {e.OldValue?.GetType()?.Name} to {e.NewValue?.GetType()?.Name}");

            // Unsubscribe from previous DataContext's static events (defensive)
            if (e.OldValue is SaleViewModel)
            {
                ViewModels.SaleViewModel.SaleCompleted -= OnSaleCompleted;
                ViewModels.SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            }

            if (e.NewValue is SaleViewModel)
            {
                // Subscribe to sale completed event for automatic product list updates
                ViewModels.SaleViewModel.SaleCompleted += OnSaleCompleted;

                // Subscribe to individual product stock changes for real-time updates
                ViewModels.SaleViewModel.ProductStockChanged += OnProductStockChanged;

                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Event subscriptions completed for SaleViewModel");
            }
        }

        private void SalesViewGrid_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] SalesViewGrid_Loaded called");

            // ✅ CRITICAL FIX: Initialize heavy objects in background to prevent UI blocking
            _ = Task.Run(async () =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Starting background initialization");

                    // Initialize services in background
                    _settingsService = new SettingsService();

                    // Initialize search timer
                    _searchTimer = new System.Timers.Timer(SEARCH_DELAY_MS);
                    _searchTimer.Elapsed += OnSearchTimerElapsed;
                    _searchTimer.AutoReset = false;

                    // ✅ CRITICAL FIX: Initialize ViewModel data in background if needed
                    bool needsInitialLoad = await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        try
                        {
                            return ViewModel != null && (ViewModel.FilteredProducts == null || ViewModel.FilteredProducts.Count == 0);
                        }
                        catch
                        {
                            return false;
                        }
                    });

                    if (needsInitialLoad)
                    {
                        System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Starting background product loading");

                        // Add timeout protection for product loading
                        using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(8)))
                        {
                            try
                            {
                                await ViewModel.RefreshProducts();
                                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Background product loading completed");
                            }
                            catch (OperationCanceledException)
                            {
                                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Product loading timed out after 8 seconds");
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Background initialization completed");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Background initialization error: {ex.Message}");
                }
            });

            // Keep lightweight UI operations on UI thread
            this.Focusable = true;
            this.Focus();

            // Subscribe to keyboard events for debugging
            this.PreviewKeyDown += SalesViewGrid_PreviewKeyDown;
            this.KeyDown += SalesViewGrid_KeyDown;

            // Subscribe to ViewModel events
            if (ViewModel != null)
            {
                ViewModel.FocusSearchBoxRequested += OnFocusSearchBoxRequested;
                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Subscribed to ViewModel events");
            }

            // ✅ GRID LAYOUT: Initialize grid columns after layout is complete
            this.Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateGridColumns();
                // Hook internal ListView ScrollViewer for infinite scrolling (after template applied)
                var productsListView = this.FindName("productsListView") as ListView;
                if (productsListView != null)
                {
                    var internalSV = FindVisualChild<ScrollViewer>(productsListView);
                    if (internalSV != null)
                    {
                        internalSV.ScrollChanged += ProductsScrollViewer_ScrollChanged;
                    }
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);

            // ✅ PERFORMANCE FIX: Preload common dialogs for better first-time performance
            DialogCacheService.Instance.PreloadCommonDialogs();

            // ✅ CRITICAL PERFORMANCE FIX: Apply UI optimizations to this control
            ApplyPerformanceOptimizations();


            if (DataContext is SaleViewModel vm)
            {
                // set up batch boundary event subscription when loaded
                vm.BatchBoundaryCrossed -= ViewModel_BatchBoundaryCrossed;
                vm.BatchBoundaryCrossed += ViewModel_BatchBoundaryCrossed;
            }

            // ✅ PERFORMANCE: Bitmap cache toggled via XAML DialogHost events (see SalesDialog_DialogOpened/Closing)
            // Handlers defined in XAML for DialogHost
            // These simply toggle bitmap caching on the root content grid
            // to reduce rendering overhead while the dialog overlay is visible.




            // ✅ PERFORMANCE FIX: Preload common dialogs for better first-time performance
            DialogCacheService.Instance.PreloadCommonDialogs();

            // ✅ GRID LAYOUT: Subscribe to product collection changes
            SubscribeToProductChanges();

            // ✅ PERFORMANCE FIX: Conditional debug logging to prevent frame rate drops

#if DEBUG
            if (POSSystem.Helpers.PerformanceDebugHelper.IsSalesViewDebugEnabled)
            {
                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Keyboard event handlers attached");
            }
#endif
        }

        private void ViewModel_BatchBoundaryCrossed(object sender, SaleViewModel.BatchBoundaryEventArgs e)
        {
            try
            {
                // Use lightweight toast via DialogHost with small message
                var message = $"{e.Product.Name}: moved to batch {e.BatchNumber} at {e.NewBatchUnitPrice:N2} DA";
                if (e.ExpiryDate.HasValue)
                {
                    message += $" (exp: {e.ExpiryDate:yyyy-MM-dd})";
                }

                // Build a simple inline dialog content
                var card = new MaterialDesignThemes.Wpf.Card
                {
                    Padding = new Thickness(8),
                    Background = (Brush)Application.Current.Resources["MaterialDesignCardBackground"],
                    Content = new TextBlock
                    {
                        Text = message,
                        Margin = new Thickness(4, 0, 4, 0)
                    }
                };

                // Auto-close quickly (non-blocking). We don't await to avoid UI pause.
                _ = MaterialDesignThemes.Wpf.DialogHost.Show(card, "SalesDialog");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH_NOTICE] Error showing batch boundary toast: {ex.Message}");
            }
        }

        private void SalesDialog_DialogOpened(object sender, DialogOpenedEventArgs e)
        {
            try
            {
                if (this.Content is Grid rootGrid)
                {
                    rootGrid.CacheMode = new BitmapCache();
                }
            }
            catch { }
        }

        private void SalesDialog_DialogClosing(object sender, DialogClosingEventArgs e)
        {
            try
            {
                if (this.Content is Grid rootGrid)
                {
                    rootGrid.CacheMode = null;
                }
            }
            catch { }
        }

        private void OnSearchTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            // ✅ CRITICAL FIX: Implement proper search functionality with background processing
            _ = Task.Run(async () =>
            {
                try
                {
                    await Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        if (ViewModel != null)
                        {
                            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Search timer elapsed - triggering search");

                            // Get current search text from the search box
                            var searchBox = FindSearchTextBox();
                            if (searchBox != null && !string.IsNullOrWhiteSpace(searchBox.Text))
                            {
                                // Update ViewModel search text and trigger search
                                ViewModel.SearchText = searchBox.Text;

                                // Trigger search in background to prevent UI blocking
                                _ = Task.Run(async () =>
                                {
                                    try
                                    {
                                        _searchCancellation?.Cancel();
                                        _searchCancellation = new CancellationTokenSource();
                                        await ViewModel.SearchProducts(_searchCancellation.Token);
                                        System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Background search completed");
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Search error: {ex.Message}");
                                    }
                                });
                            }
                        }
                    }, System.Windows.Threading.DispatcherPriority.Background);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Search timer error: {ex.Message}");
                }
            });
        }

        // ✅ CRITICAL FIX: Helper method to find search text box
        private TextBox FindSearchTextBox()
        {
            try
            {
                return this.FindName("txtSearch") as TextBox ??
                       FindVisualChild<TextBox>(this, "txtSearch");
            }
            catch
            {
                return null;
            }
        }

        // ✅ CRITICAL FIX: Helper method to find visual children
        private T FindVisualChild<T>(DependencyObject parent, string name = null) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T typedChild && (name == null || (child as FrameworkElement)?.Name == name))
                {
                    return typedChild;
                }

                var result = FindVisualChild<T>(child, name);
                if (result != null)
                    return result;
            }
            return null;
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // ✅ CRITICAL FIX: Optimized search with debouncing and performance monitoring
            try
            {
                if (_searchTimer != null && sender is TextBox textBox)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Search text changed: '{textBox.Text}'");

                    // Reset timer on each text change (debouncing)
                    _searchTimer.Stop();

                    // Only start timer if there's actual text or if clearing search
                    if (!string.IsNullOrWhiteSpace(textBox.Text) || string.IsNullOrEmpty(textBox.Text))
                    {
                        _searchTimer.Start();
                    }

                    // ✅ PERFORMANCE FIX: Immediate clear for empty search
                    if (string.IsNullOrEmpty(textBox.Text) && ViewModel != null)
                    {
                        // Clear search immediately without waiting for timer
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await Application.Current.Dispatcher.InvokeAsync(() =>
                                {
                                    ViewModel.SearchText = string.Empty;
                                }, System.Windows.Threading.DispatcherPriority.Background);

                                // Trigger refresh in background
                                await ViewModel.RefreshProducts();
                                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Search cleared and products refreshed");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Clear search error: {ex.Message}");
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] SearchBox_TextChanged error: {ex.Message}");
            }
        }

        private void ProductGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // ✅ CRITICAL FIX: Optimized product selection handling with duplicate prevention
            try
            {
                if (sender is ListView listView && listView.SelectedItem is Product selectedProduct)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product selected: {selectedProduct.Name}");

                    // ✅ FIX: Prevent duplicate execution when both SelectionChanged and Click events fire
                    lock (_productSelectionLock)
                    {
                        if (_isProcessingProductSelection)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Skipping duplicate product selection for: {selectedProduct.Name}");
                            listView.SelectedItem = null;
                            return;
                        }
                        _isProcessingProductSelection = true;
                    }

                    // Handle product selection in background to prevent UI blocking
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                ViewModel?.AddToCartCommand?.Execute(selectedProduct);
                            }, System.Windows.Threading.DispatcherPriority.Normal);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product selection error: {ex.Message}");
                        }
                        finally
                        {
                            // Reset the flag after a short delay to allow for the next selection
                            await Task.Delay(100);
                            lock (_productSelectionLock)
                            {
                                _isProcessingProductSelection = false;
                            }
                        }
                    });

                    // Clear selection to allow re-selecting the same product
                    listView.SelectedItem = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] ProductGrid_SelectionChanged error: {ex.Message}");
                // Ensure flag is reset even if an error occurs
                lock (_productSelectionLock)
                {
                    _isProcessingProductSelection = false;
                }
            }
        }

        private async void ProductCard_Click(object sender, MouseButtonEventArgs e)
        {
            // ✅ GRID LAYOUT FIX: Handle product card clicks for ItemsControl with duplicate prevention
            try
            {
                var border = sender as Border;
                if (border?.DataContext is Product selectedProduct)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product card clicked: {selectedProduct.Name}");

                    // ✅ FIX: Prevent duplicate execution when both SelectionChanged and Click events fire
                    lock (_productSelectionLock)
                    {
                        if (_isProcessingProductSelection)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Skipping duplicate product card click for: {selectedProduct.Name}");
                            return;
                        }
                        _isProcessingProductSelection = true;
                    }

                    // ✅ OUT-OF-STOCK FIX: Check if product is out of stock and handle with reservation workflow
                    if (selectedProduct.Id >= 0 && selectedProduct.Type != Models.ProductType.Service && selectedProduct.StockQuantity <= 0m)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product {selectedProduct.Name} is out of stock, showing confirmation prompt");

                        // Show confirmation prompt for out-of-stock products
                        var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                        var message = Application.Current.FindResource("OutOfStockReservationMessage") as string ?? "This product is currently out of stock (0 items available). Would you like to create a reservation invoice for this product instead?";

                        var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                            POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                            POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                        if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] User confirmed reservation creation for {selectedProduct.Name}");
                            // User wants to create a reservation - open the reservation dialog
                            await CreateInvoiceFromProduct_ClickInternal(selectedProduct);
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] User declined reservation creation for {selectedProduct.Name}");
                            // User declined - do nothing, return to normal browsing
                        }

                        // Reset the flag and return early
                        lock (_productSelectionLock)
                        {
                            _isProcessingProductSelection = false;
                        }
                        return;
                    }

                    // Handle normal product selection in background to prevent UI blocking
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                ViewModel?.AddToCartCommand?.Execute(selectedProduct);
                            }, System.Windows.Threading.DispatcherPriority.Normal);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product card selection error: {ex.Message}");
                        }
                        finally
                        {
                            // Reset the flag after a short delay to allow for the next selection
                            await Task.Delay(100);
                            lock (_productSelectionLock)
                            {
                                _isProcessingProductSelection = false;
                            }
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] ProductCard_Click error: {ex.Message}");
                // Ensure flag is reset even if an error occurs
                lock (_productSelectionLock)
                {
                    _isProcessingProductSelection = false;
                }
            }
        }

        /// <summary>
        /// ✅ ACCESSIBILITY: Handle keyboard navigation for product cards with duplicate prevention
        /// </summary>
        private void ProductCard_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.Key == Key.Enter || e.Key == Key.Space)
                {
                    var border = sender as Border;
                    if (border?.DataContext is Product selectedProduct)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product card selected via keyboard: {selectedProduct.Name}");

                        // ✅ FIX: Prevent duplicate execution when multiple events fire
                        lock (_productSelectionLock)
                        {
                            if (_isProcessingProductSelection)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Skipping duplicate product keyboard selection for: {selectedProduct.Name}");
                                e.Handled = true;
                                return;
                            }
                            _isProcessingProductSelection = true;
                        }

                        // Handle product selection in background to prevent UI blocking
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await Application.Current.Dispatcher.InvokeAsync(() =>
                                {
                                    ViewModel?.AddToCartCommand?.Execute(selectedProduct);
                                }, System.Windows.Threading.DispatcherPriority.Normal);
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product card keyboard selection error: {ex.Message}");
                            }
                            finally
                            {
                                // Reset the flag after a short delay to allow for the next selection
                                await Task.Delay(100);
                                lock (_productSelectionLock)
                                {
                                    _isProcessingProductSelection = false;
                                }
                            }
                        });
                    }
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] ProductCard_KeyDown error: {ex.Message}");
                // Ensure flag is reset even if an error occurs
                lock (_productSelectionLock)
                {
                    _isProcessingProductSelection = false;
                }
            }
        }

        // ✅ CRITICAL FIX: Optimized scroll handling for infinite loading
        private void ProductsScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            try
            {
                if (sender is ScrollViewer scrollViewer && ViewModel != null)
                {
                    // Check if we're near the bottom (within 100 pixels)
                    var isNearBottom = scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight - 100;

                    if (isNearBottom && !ViewModel.IsLoading)
                    {
                        System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Near bottom - loading more products");

                        // ✅ FIX: Call LoadMoreProducts directly - it's already async and handles threading properly
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                // Ensure LoadMoreProducts is called safely and UI updates happen on UI thread
                                await ViewModel.LoadMoreProducts();
                                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] More products loaded");
                            }
                            catch (InvalidOperationException invEx) when (invEx.Message.Contains("different thread owns it"))
                            {
                                // Retry on UI thread if cross-thread access occurs
                                await Application.Current.Dispatcher.InvokeAsync(async () => await ViewModel.LoadMoreProducts());
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Load more products error: {ex.Message}");
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Scroll changed error: {ex.Message}");
            }
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is Category selectedCategory)
            {
                // Filter products by category
            }
        }

        private void RefreshProducts_Click(object sender, RoutedEventArgs e)
        {
            // Refresh product list
            ViewModel?.RefreshProductsCommand?.Execute(null);
        }

        /// <summary>
        /// ✅ PRODUCT REFRESH FIX: Handle sale completed event to automatically refresh product list
        /// </summary>
        private void OnSaleCompleted(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] *** OnSaleCompleted called! Sender: {sender?.GetType()?.Name}, ViewModel: {ViewModel?.GetType()?.Name}");

            // Refresh the product list to show updated stock quantities
            if (ViewModel != null)
            {
                _ = Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Sale completed - refreshing product list");
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Current FilteredProducts count: {ViewModel.FilteredProducts?.Count ?? 0}");
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] ShowingFavorites: {ViewModel.ShowingFavorites}, ShowingPopularItems: {ViewModel.ShowingPopularItems}");

                        // Invalidate any search caches to avoid stale results on next operations
                        ViewModel.InvalidateSearchCaches();

                        // Check what type of products are currently being shown
                        if (ViewModel.ShowingFavorites)
                        {
                            await ViewModel.RefreshProducts(); // This will refresh favorites
                        }
                        else if (ViewModel.ShowingPopularItems)
                        {
                            await ViewModel.RefreshProducts(); // This will refresh popular items
                        }
                        else
                        {
                            // For regular product view, we need to reload the current products
                            // Reset the pagination and reload
                            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Refreshing regular product view");
                            ViewModel.ResetProductList();
                            await ViewModel.LoadMoreProducts();
                        }

                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product list refreshed after sale completion. New count: {ViewModel.FilteredProducts?.Count ?? 0}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error refreshing products after sale: {ex.Message}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Background);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] ViewModel is null - cannot refresh products");
            }
        }

        /// <summary>
        /// ✅ PRODUCT REFRESH FIX: Handle individual product stock changes for real-time updates
        /// </summary>
        private void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] *** OnProductStockChanged called! ProductId: {e.ProductId}, NewStock: {e.NewStockQuantity}");

            // Update the specific product's stock quantity in the FilteredProducts collection
            if (ViewModel?.FilteredProducts != null)
            {
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        var product = ViewModel.FilteredProducts.FirstOrDefault(p => p.Id == e.ProductId);
                        if (product != null)
                        {
                            var oldStock = product.StockQuantity;
                            product.StockQuantity = e.NewStockQuantity;
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Updated stock for product {product.Name}: {oldStock} -> {e.NewStockQuantity}");

                            // Also invalidate search caches so subsequent searches don't resurrect old stock
                            ViewModel.InvalidateSearchCaches();
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Product with ID {e.ProductId} not found in FilteredProducts - triggering targeted refresh");
                            try
                            {
                                // Targeted refresh: attempt to refresh this product in collections
                                _ = ViewModel?.RefreshSpecificProductByIdSafe(e.ProductId);
                            }
                            catch (Exception refreshEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error triggering targeted product refresh: {refreshEx.Message}");
                            }
                        }

                        // ✅ FIX: Update the info card - check if the selected cart item's product needs updating
                        if (ViewModel.SelectedCartItem?.Product?.Id == e.ProductId)
                        {
                            var oldInfoCardStock = ViewModel.SelectedCartItem.Product.StockQuantity;
                            ViewModel.SelectedCartItem.Product.StockQuantity = e.NewStockQuantity;
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Updated info card stock for product {ViewModel.SelectedCartItem.Product.Name}: {oldInfoCardStock} -> {e.NewStockQuantity}");

                            // Force property change notification for the info card UI
                            // Use reflection to trigger property change notification since OnPropertyChanged is protected
                            var propertyChangedMethod = typeof(SaleViewModel).GetMethod("OnPropertyChanged",
                                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                            propertyChangedMethod?.Invoke(ViewModel, new object[] { nameof(ViewModel.SelectedCartItem) });
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error updating individual product stock: {ex.Message}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] ViewModel or FilteredProducts is null - cannot update individual product stock");
            }
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            // Clean up timer
            _searchTimer?.Stop();
            _searchTimer?.Dispose();

            // Unsubscribe from keyboard events
            this.PreviewKeyDown -= SalesViewGrid_PreviewKeyDown;
            this.KeyDown -= SalesViewGrid_KeyDown;

            // Unsubscribe from ViewModel events
            if (ViewModel != null)
            {
                ViewModel.FocusSearchBoxRequested -= OnFocusSearchBoxRequested;
            }

            // ✅ GRID LAYOUT: Unsubscribe from size changes
            SizeChanged -= OnSizeChanged;

            // ✅ GRID LAYOUT: Unsubscribe from product collection changes
            UnsubscribeFromProductChanges();

            // ✅ PRODUCT REFRESH FIX: Unsubscribe from DataContext changes
            DataContextChanged -= SalesViewGrid_DataContextChanged;

            // ✅ PRODUCT REFRESH FIX: Unsubscribe from sale completed event to prevent memory leaks
            ViewModels.SaleViewModel.SaleCompleted -= OnSaleCompleted;

            // ✅ PRODUCT REFRESH FIX: Unsubscribe from product stock changed event to prevent memory leaks
            ViewModels.SaleViewModel.ProductStockChanged -= OnProductStockChanged;
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Handle size changes to update grid columns responsively
        /// </summary>
        private void OnSizeChanged(object sender, SizeChangedEventArgs e)
        {
            UpdateGridColumns();
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Calculate optimal layout for WrapPanel based on available width
        /// </summary>
        private void UpdateGridColumns()
        {
            try
            {
                // Find the products ListView
                var productsListView = FindName("productsListView") as ListView;
                if (productsListView == null) return;

                // Get the available width (subtract scrollbar width)
                var availableWidth = productsListView.ActualWidth - 20; // Account for scrollbar
                if (availableWidth <= 0) return;

                // Calculate how many cards can fit in the available width
                var possibleColumns = (int)Math.Floor(availableWidth / PRODUCT_CARD_WIDTH);
                var optimalColumns = Math.Max((int)MIN_COLUMNS, Math.Min((int)MAX_COLUMNS, possibleColumns));

                // Find the VirtualizingWrapPanel in the ItemsPanel
                var wrapPanel = FindVisualChild<POSSystem.Controls.VirtualizingWrapPanel>(productsListView);
                if (wrapPanel != null)
                {
                    // WrapPanel automatically wraps based on available space and item size
                    // We just need to ensure the ListView has the correct width
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] WrapPanel layout updated (width: {availableWidth:F0}px, estimated columns: {optimalColumns})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error updating grid layout: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Calculate estimated columns for WrapPanel (for debugging/monitoring)
        /// </summary>
        private int CalculateEstimatedColumns(double availableWidth)
        {
            // Calculate how many cards can fit in the available width
            var estimatedColumns = (int)Math.Floor(availableWidth / PRODUCT_CARD_WIDTH);
            return Math.Max((int)MIN_COLUMNS, Math.Min((int)MAX_COLUMNS, estimatedColumns));
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Get current product count from ViewModel
        /// </summary>
        private int GetCurrentProductCount()
        {
            try
            {
                if (ViewModel?.FilteredProducts != null)
                {
                    return ViewModel.FilteredProducts.Count;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Helper method to find visual children
        /// </summary>
        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private void OnFocusSearchBoxRequested(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] FocusSearchBoxRequested event received");
            FocusSearchBox();
        }

        private void SalesViewGrid_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[KEYBOARD] PreviewKeyDown: {e.Key} (Modifiers: {e.KeyboardDevice.Modifiers})");

            // Handle global shortcuts that should work regardless of focus
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel == null) return;

            // Check for specific key combinations
            bool handled = false;

            switch (e.Key)
            {
                case Key.F3:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] F3 pressed - executing SearchCommand");
                    if (viewModel.SearchCommand?.CanExecute(null) == true)
                    {
                        viewModel.SearchCommand.Execute(null);
                        FocusSearchBox(); // Also focus the search box
                        handled = true;
                    }
                    break;

                case Key.F4:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] F4 pressed - executing ProcessPaymentCommand");
                    if (viewModel.ProcessPaymentCommand?.CanExecute(null) == true)
                    {
                        viewModel.ProcessPaymentCommand.Execute(null);
                        // Also trigger the payment dialog
                        _ = ShowPaymentDialog();
                        handled = true;
                    }
                    break;

                case Key.F5:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] F5 pressed - executing RedeemPointsCommand");
                    if (viewModel.RedeemPointsCommand?.CanExecute(null) == true)
                    {
                        viewModel.RedeemPointsCommand.Execute(null);
                        handled = true;
                    }
                    break;

                case Key.F when e.KeyboardDevice.Modifiers == ModifierKeys.Control:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Ctrl+F pressed - executing SearchCommand");
                    if (viewModel.SearchCommand?.CanExecute(null) == true)
                    {
                        viewModel.SearchCommand.Execute(null);
                        FocusSearchBox(); // Also focus the search box
                        handled = true;
                    }
                    break;

                case Key.N when e.KeyboardDevice.Modifiers == ModifierKeys.Control:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Ctrl+N pressed - executing CreateNewCartCommand");
                    if (viewModel.CreateNewCartCommand?.CanExecute(null) == true)
                    {
                        viewModel.CreateNewCartCommand.Execute(null);
                        handled = true;
                    }
                    break;

                case Key.Enter when e.KeyboardDevice.Modifiers == ModifierKeys.Control:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Ctrl+Enter pressed - executing ProcessPaymentCommand");
                    if (viewModel.ProcessPaymentCommand?.CanExecute(null) == true)
                    {
                        viewModel.ProcessPaymentCommand.Execute(null);
                        // Also trigger the payment dialog
                        _ = ShowPaymentDialog();
                        handled = true;
                    }
                    break;

                case Key.Escape:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Escape pressed - executing ClearSearchCommand");
                    if (viewModel.ClearSearchCommand?.CanExecute(null) == true)
                    {
                        viewModel.ClearSearchCommand.Execute(null);
                        handled = true;
                    }
                    break;
            }

            if (handled)
            {
                e.Handled = true;
                System.Diagnostics.Debug.WriteLine($"[KEYBOARD] Key {e.Key} handled successfully");
            }
        }

        private void SalesViewGrid_KeyDown(object sender, KeyEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[KEYBOARD] KeyDown: {e.Key} (Handled: {e.Handled})");

            // Handle cart-specific shortcuts only if we have focus and a selected item
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel == null || e.Handled) return;

            bool handled = false;

            switch (e.Key)
            {
                case Key.Delete:
                    if (viewModel.SelectedCartItem != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[KEYBOARD] Delete pressed - removing item {viewModel.SelectedCartItem.Product.Id}");
                        if (viewModel.RemoveFromCartCommand?.CanExecute(viewModel.SelectedCartItem.Product.Id) == true)
                        {
                            viewModel.RemoveFromCartCommand.Execute(viewModel.SelectedCartItem.Product.Id);
                            handled = true;
                        }
                    }
                    break;

                case Key.OemPlus:
                case Key.Add:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Plus key pressed - executing IncreaseQuantityCommand");
                    if (viewModel.IncreaseQuantityCommand?.CanExecute(null) == true)
                    {
                        viewModel.IncreaseQuantityCommand.Execute(null);
                        handled = true;
                    }
                    break;

                case Key.OemMinus:
                case Key.Subtract:
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Minus key pressed - executing DecreaseQuantityCommand");
                    if (viewModel.DecreaseQuantityCommand?.CanExecute(null) == true)
                    {
                        viewModel.DecreaseQuantityCommand.Execute(null);
                        handled = true;
                    }
                    break;
            }

            if (handled)
            {
                e.Handled = true;
                System.Diagnostics.Debug.WriteLine($"[KEYBOARD] Key {e.Key} handled successfully in KeyDown");
            }
        }

        // Event handlers that are referenced in XAML
        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            SearchBox_TextChanged(sender, e);
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            // Handle search key events
            if (e.Key == Key.Enter)
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null && sender is TextBox textBox)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Search Enter pressed: {textBox.Text}");
                    // Try to find product by barcode first, then by name
                    var searchText = textBox.Text?.Trim();
                    if (!string.IsNullOrEmpty(searchText))
                    {
                        // Check if it's a barcode (numeric) and try to add directly to cart
                        if (searchText.All(char.IsDigit) && searchText.Length >= 8)
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Processing barcode: {searchText}");
                            // Use async method to search both main and external databases
                            ProcessBarcodeAsync(viewModel, searchText, textBox);
                        }
                        else
                        {
                            // Regular search - just focus search box (SearchCommand calls FocusSearchBox)
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Regular search: {searchText}");
                            if (viewModel.SearchCommand?.CanExecute(null) == true)
                            {
                                viewModel.SearchCommand.Execute(null);
                            }
                        }
                    }
                }
            }
        }

        private void TxtLoyaltyCode_KeyDown(object sender, KeyEventArgs e)
        {
            // Handle loyalty code key events
            if (e.Key == Key.Enter)
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null && sender is TextBox textBox)
                {
                    var loyaltyCode = textBox.Text?.Trim();

                    if (!string.IsNullOrEmpty(loyaltyCode))
                    {
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Loyalty code Enter pressed: {loyaltyCode}");

                        // Use the existing SearchLoyaltyCard method
                        viewModel.SearchLoyaltyCard(loyaltyCode);

                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] Loyalty search completed via Enter key");
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a loyalty card number or phone number.", "Validation Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }
                }
            }
        }

        private void TxtInvoiceNumber_KeyDown(object sender, KeyEventArgs e)
        {
            // Handle invoice number key events
        }

        public async Task ShowPaymentDialog()
        {
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel?.CurrentCart == null) return;

            try
            {
                // ✅ PERFORMANCE FIX: Create dialog components in background to prevent UI blocking
                PaymentProcessingView paymentProcessingView = null;
                PaymentProcessingViewModel paymentProcessingViewModel = null;

                await Task.Run(() =>
                {
                    // Create ViewModel in background thread
                    paymentProcessingViewModel = new PaymentProcessingViewModel(viewModel, "SalesDialog");
                });

                // Create View on UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    paymentProcessingView = new PaymentProcessingView
                    {
                        DataContext = paymentProcessingViewModel
                    };
                }, System.Windows.Threading.DispatcherPriority.Normal);

                // Show the PaymentProcessingView in the dialog
                var result = await DialogHost.Show(paymentProcessingView, "SalesDialog");

                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[PAYMENT DEBUG] Payment dialog result: {result}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PAYMENT DEBUG] Error showing payment dialog: {ex.Message}");
                System.Windows.MessageBox.Show($"Error opening payment dialog: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private Card CreateOrderSummary(SaleViewModel viewModel)
        {
            var summaryCard = new Card
            {
                Margin = new Thickness(0, 8, 0, 8),
                Padding = new Thickness(16)
            };

            var summaryPanel = new StackPanel();

            // Items
            foreach (var item in viewModel.CurrentCart.Items)
            {
                var itemPanel = new Grid();
                itemPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                itemPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

                var itemName = new TextBlock
                {
                    Text = $"{item.Product.Name} x{item.Quantity}",
                    Style = (Style)Application.Current.Resources["MaterialDesignBody2TextBlock"]
                };
                Grid.SetColumn(itemName, 0);

                var itemTotal = new TextBlock
                {
                    Text = item.Total.ToString("C2"),
                    Style = (Style)Application.Current.Resources["MaterialDesignBody2TextBlock"],
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetColumn(itemTotal, 1);

                itemPanel.Children.Add(itemName);
                itemPanel.Children.Add(itemTotal);
                summaryPanel.Children.Add(itemPanel);
            }

            // Total
            var totalPanel = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            totalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            totalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var totalLabel = new TextBlock
            {
                Text = "Total:",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                FontWeight = FontWeights.Bold
            };
            Grid.SetColumn(totalLabel, 0);

            var totalValue = new TextBlock
            {
                Text = viewModel.CurrentCart.GrandTotal.ToString("C2"),
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetColumn(totalValue, 1);

            totalPanel.Children.Add(totalLabel);
            totalPanel.Children.Add(totalValue);
            summaryPanel.Children.Add(totalPanel);

            summaryCard.Content = summaryPanel;
            return summaryCard;
        }

        private async void SetQuantity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                CartItem cartItem = null;

                // Try to get cart item from sender's tag first, then from SelectedCartItem
                if (sender is FrameworkElement element && element.Tag is CartItem tagCartItem)
                {
                    cartItem = tagCartItem;
                }
                else if (viewModel?.SelectedCartItem != null)
                {
                    cartItem = viewModel.SelectedCartItem;
                }

                if (cartItem == null) return;

                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] SetQuantity_Click called for item: {cartItem.Product.Name}");

                var content = new StackPanel { Margin = new Thickness(16) };

                // Add title
                content.Children.Add(new TextBlock
                {
                    Text = "Set Quantity",
                    Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                    Margin = new Thickness(0, 0, 0, 16)
                });

                // Add quantity input
                var txtNewQuantity = new TextBox
                {
                    Margin = new Thickness(0, 8, 0, 16),
                    Text = cartItem.Quantity.ToString(),
                    Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"]
                };
                txtNewQuantity.SetValue(HintAssist.HintProperty, "Enter new quantity");

                content.Children.Add(txtNewQuantity);

                // Add buttons
                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 16, 0, 0)
                };

                var cancelButton = new Button
                {
                    Content = "Cancel",
                    Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                    Margin = new Thickness(0, 0, 8, 0)
                };

                var confirmButton = new Button
                {
                    Content = "Confirm",
                    Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"]
                };

                void processQuantityChange()
                {
                    // ✅ WEIGHT-BASED FIX: Use decimal.TryParse to support decimal quantities for weight-based products
                    if (decimal.TryParse(txtNewQuantity.Text, out decimal newQuantity) && newQuantity > 0)
                    {
                        // Check stock for regular products (not custom products or services)
                        if (cartItem.Product.Id != -1 &&
                            cartItem.Product.Type != ProductType.Service &&
                            cartItem.Product.GetTotalStockDecimal() < newQuantity)
                        {
                            // ✅ FIX: Use standardized stock limit message format
                            var availableStock = cartItem.Product.GetTotalStockDecimal();
                            var stockDisplay = cartItem.Product.IsWeightBased ?
                                availableStock.ToString("0.###") :
                                availableStock.ToString("F0");

                            string unit = cartItem.Product.IsWeightBased ?
                                (cartItem.Product.UnitOfMeasure?.Abbreviation ?? "units") : "items";

                            var title = Application.Current.FindResource("StockLimitTitle") as string ?? "Stock Limit";
                            var message = $"Cannot add that quantity. Only {stockDisplay} {unit} available in stock.";

                            System.Windows.MessageBox.Show(message, title,
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                            return;
                        }

                        cartItem.Quantity = newQuantity;
                        viewModel?.CalculateTotals();
                        DialogHost.Close("SalesDialog", true);
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Quantity updated to: {newQuantity}");
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a valid quantity!", "Invalid Quantity",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }
                }

                // Add keyboard shortcut
                txtNewQuantity.KeyDown += (s, args) =>
                {
                    if (args.Key == Key.Enter)
                    {
                        processQuantityChange();
                        args.Handled = true;
                    }
                };

                cancelButton.Click += (s, args) => DialogHost.Close("SalesDialog", false);
                confirmButton.Click += (s, args) => processQuantityChange();

                buttonPanel.Children.Add(cancelButton);
                buttonPanel.Children.Add(confirmButton);
                content.Children.Add(buttonPanel);

                // Show dialog with focus on the quantity input
                var dialogOpenedEventHandler = new DialogOpenedEventHandler((s, args) =>
                {
                    txtNewQuantity.Focus();
                    txtNewQuantity.SelectAll();
                });

                await DialogHost.Show(content, "SalesDialog", dialogOpenedEventHandler);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in SetQuantity_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error setting quantity: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void SearchLoyaltyCode_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] SearchLoyaltyCode_Click called");

                    // Get the loyalty code from the text field
                    var loyaltyCode = txtLoyaltyCode?.Text?.Trim();

                    if (!string.IsNullOrEmpty(loyaltyCode))
                    {
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Searching for loyalty code: {loyaltyCode}");

                        // Use the existing SearchLoyaltyCard method
                        viewModel.SearchLoyaltyCard(loyaltyCode);

                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] Loyalty search completed");
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a loyalty card number or phone number.", "Validation Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        txtLoyaltyCode?.Focus();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in SearchLoyaltyCode_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error searching loyalty code: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }



        private void SaveChanges_Click(object sender, RoutedEventArgs e)
        {
            // Save changes
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null && viewModel.IsEditMode)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] SaveChanges_Click called");

                try
                {
                    // Call the save command
                    if (viewModel.SaveModifiedSaleCommand?.CanExecute(null) == true)
                    {
                        viewModel.SaveModifiedSaleCommand.Execute(null);
                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] Save changes command executed");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] Save command cannot execute or is null");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error saving changes: {ex.Message}");
                    System.Windows.MessageBox.Show($"Error saving changes: {ex.Message}", "Save Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private void RemoveItem_Click(object sender, RoutedEventArgs e)
        {
            // Remove item
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null && sender is FrameworkElement element && element.Tag is CartItem cartItem)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] RemoveItem_Click called for product: {cartItem.Product.Name}");
                if (viewModel.RemoveFromCartCommand?.CanExecute(cartItem.Product.Id) == true)
                {
                    viewModel.RemoveFromCartCommand.Execute(cartItem.Product.Id);
                }
            }
        }

        private async void RedeemPoints_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel?.SelectedCustomer == null || !viewModel.HasLoyaltyCustomer)
                {
                    System.Windows.MessageBox.Show("Please select a customer first.", "No Customer Selected",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                if (!viewModel.CanRedeemPoints)
                {
                    System.Windows.MessageBox.Show("Customer doesn't have enough points to redeem.", "Insufficient Points",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] RedeemPoints_Click called");
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Customer: {viewModel.SelectedCustomer.FullName}");
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Available Points: {viewModel.CustomerLoyaltyPoints}");

                await ShowPointsRedemptionDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in RedeemPoints_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error redeeming points: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task ShowPointsRedemptionDialog()
        {
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel?.SelectedCustomer == null) return;

            var content = new StackPanel { Margin = new Thickness(16) };

            // Title
            content.Children.Add(new TextBlock
            {
                Text = "Redeem Loyalty Points",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            });

            // Customer info
            content.Children.Add(new TextBlock
            {
                Text = $"Customer: {viewModel.SelectedCustomer.FullName}",
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = $"Available Points: {viewModel.CustomerLoyaltyPoints:N0}",
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            });

            // Points to redeem input
            var txtPointsToRedeem = new TextBox
            {
                Margin = new Thickness(0, 8, 0, 8),
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"]
            };
            txtPointsToRedeem.SetValue(HintAssist.HintProperty, "Points to redeem");

            content.Children.Add(txtPointsToRedeem);

            // Discount amount display
            var txtDiscountAmount = new TextBlock
            {
                Text = "Discount Amount: $0.00",
                Style = (Style)Application.Current.Resources["MaterialDesignBody2TextBlock"],
                Margin = new Thickness(0, 8, 0, 16),
                Foreground = new SolidColorBrush(Colors.Green)
            };
            content.Children.Add(txtDiscountAmount);

            // Update discount amount when points change
            txtPointsToRedeem.TextChanged += (s, e) =>
            {
                if (int.TryParse(txtPointsToRedeem.Text, out int points) && points > 0)
                {
                    var loyaltyProgram = viewModel.GetActiveLoyaltyProgram();
                    if (loyaltyProgram != null)
                    {
                        decimal discountAmount = points * loyaltyProgram.MonetaryValuePerPoint;
                        txtDiscountAmount.Text = $"Discount Amount: {discountAmount:C2}";
                    }
                    else
                    {
                        txtDiscountAmount.Text = "Discount Amount: $0.00";
                    }
                }
                else
                {
                    txtDiscountAmount.Text = "Discount Amount: $0.00";
                }
            };

            // Buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 16, 0, 0)
            };

            var cancelButton = new Button
            {
                Content = "Cancel",
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Margin = new Thickness(0, 0, 8, 0)
            };

            var redeemButton = new Button
            {
                Content = "Redeem",
                Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"]
            };

            redeemButton.Click += (s, e) =>
            {
                try
                {
                    if (int.TryParse(txtPointsToRedeem.Text, out int pointsToRedeem) && pointsToRedeem > 0)
                    {
                        if (pointsToRedeem <= viewModel.CustomerLoyaltyPoints)
                        {
                            var loyaltyProgram = viewModel.GetActiveLoyaltyProgram();
                            if (loyaltyProgram == null)
                            {
                                System.Windows.MessageBox.Show("Loyalty program is not configured.", "Configuration Error",
                                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                                return;
                            }

                            // Calculate discount value
                            decimal discountValue = pointsToRedeem * loyaltyProgram.MonetaryValuePerPoint;
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Calculated discount value: {discountValue}");

                            if (discountValue > viewModel.CurrentCart.GrandTotal)
                            {
                                System.Windows.MessageBox.Show($"The discount value ({discountValue:C2}) cannot exceed the total amount ({viewModel.CurrentCart.GrandTotal:C2}).",
                                    "Invalid Points",
                                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                                return;
                            }

                            // Store the points and discount value for redemption after payment
                            viewModel.CurrentCart.PendingLoyaltyPoints = pointsToRedeem;
                            viewModel.CurrentCart.PendingLoyaltyDiscount = discountValue;

                            // Apply the discount to the cart (this adds it to the Discounts collection)
                            viewModel.ApplyLoyaltyDiscount(discountValue);

                            // Clear the pending discount to avoid double counting since we applied it immediately
                            viewModel.CurrentCart.PendingLoyaltyDiscount = 0;

                            // Force update the cart totals to reflect the change
                            viewModel.CurrentCart.UpdateTotals();

                            DialogHost.Close("SalesDialog", true);
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Redeemed {pointsToRedeem} points for ${discountValue:N2} discount");
                        }
                        else
                        {
                            System.Windows.MessageBox.Show("Cannot redeem more points than available.", "Invalid Amount",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        }
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a valid number of points to redeem.", "Invalid Input",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in redeem button click handler - {ex}");
                    System.Windows.MessageBox.Show($"An error occurred while redeeming points: {ex.Message}", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            };

            cancelButton.Click += (s, e) => DialogHost.Close("SalesDialog", false);

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(redeemButton);
            content.Children.Add(buttonPanel);

            await DialogHost.Show(content, "SalesDialog");
        }





        private async void ProductGridDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show product details
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null && sender is FrameworkElement element && element.Tag is Product product)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] ProductGridDetails_Click called for product: {product.Name}");

                    // ✅ PERFORMANCE FIX: Create dialog in background to prevent UI blocking
                    ProductDetailsDialog productDetailsDialog = null;

                    await Task.Run(() =>
                    {
                        // ✅ PERFORMANCE FIX: Use cached dialog to prevent recreation overhead
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            productDetailsDialog = DialogCacheService.Instance.GetOrCreateProductDetailsDialog(product, viewModel);
                        }, System.Windows.Threading.DispatcherPriority.Background);
                    });

                    // Show dialog
                    await DialogHost.Show(productDetailsDialog, "SalesDialog");

                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Product details dialog shown for: {product.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ProductGridDetails_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error showing product details: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void CreateInvoiceFromProduct_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // ✅ CRITICAL FIX: Prevent event bubbling to avoid Add to Cart conflict
                e.Handled = true;

                if (sender is FrameworkElement element && element.Tag is Product product)
                {
                    await CreateInvoiceFromProduct_ClickInternal(product);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error in CreateInvoiceFromProduct_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error creating invoice from product: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task CreateInvoiceFromProduct_ClickInternal(Product product)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null && product != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[INVOICE] CreateInvoiceFromProduct_ClickInternal called for product: {product.Name}");

                    // Get required services using established pattern
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                    if (permissionsService == null || dbService == null)
                    {
                        System.Windows.MessageBox.Show("Invoice services are not available. Please restart the application.",
                                                      "Service Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    // Create and show confirmation dialog (let dialog handle permission checks)
                    var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                    var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                    var result = await DialogHost.Show(confirmationDialog, "SalesDialog");

                    if (result is POSSystem.ViewModels.ProductToInvoiceResult invoiceResult && invoiceResult.Confirmed)
                    {
                        // Only create stock reservation, don't open draft invoice dialog
                        await CreateStockReservationOnly(invoiceResult);
                    }

                    System.Diagnostics.Debug.WriteLine($"[INVOICE] Product-to-invoice workflow completed for: {product.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error in CreateInvoiceFromProduct_ClickInternal: {ex.Message}");
                System.Windows.MessageBox.Show($"Error creating invoice from product: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void ProcessPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] ProcessPayment_Click called");
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] HasCartItems: {viewModel.HasCartItems}");
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Cart items count: {viewModel.CurrentCart?.Items?.Count ?? 0}");

                    if (viewModel.HasCartItems)
                    {
                        await ShowPaymentDialog();
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Payment dialog shown");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] No items in cart");
                        System.Windows.MessageBox.Show("Cart is empty. Please add items before processing payment.",
                            "Empty Cart", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ProcessPayment_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error processing payment: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void PreviousSale_Click(object sender, RoutedEventArgs e)
        {
            // Previous sale
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] PreviousSale_Click called");
                if (viewModel.PreviousSaleCommand?.CanExecute(null) == true)
                {
                    viewModel.PreviousSaleCommand.Execute(null);
                }
            }
        }

        private void PopularItems_Click(object sender, RoutedEventArgs e)
        {
            // Show popular items
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] PopularItems_Click called");
                _ = viewModel.LoadPopularProducts();
            }
        }

        private void NextSale_Click(object sender, RoutedEventArgs e)
        {
            // Next sale
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] NextSale_Click called");
                if (viewModel.NextSaleCommand?.CanExecute(null) == true)
                {
                    viewModel.NextSaleCommand.Execute(null);
                }
            }
        }

        private void NewCart_Click(object sender, RoutedEventArgs e)
        {
            // New cart
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] NewCart_Click called");
                viewModel.CreateNewCart();
            }
        }

        private async void LookupCustomer_Click(object sender, RoutedEventArgs e)
        {
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] LookupCustomer_Click called");
                await ShowCustomerSelectionDialog();
            }
        }

        private async Task ShowCustomerSelectionDialog()
        {
            try
            {
                // Create a customer selection window
                var customerWindow = new Views.CustomerSelectionWindow();

                // Only set the owner if the MainWindow has been shown
                if (System.Windows.Application.Current.MainWindow != null &&
                    System.Windows.Application.Current.MainWindow.IsLoaded &&
                    System.Windows.Application.Current.MainWindow.IsVisible)
                {
                    customerWindow.Owner = System.Windows.Application.Current.MainWindow;
                }

                // Show dialog and handle result
                bool? result = customerWindow.ShowDialog();
                if (result == true && customerWindow.SelectedCustomer != null)
                {
                    var viewModel = this.DataContext as SaleViewModel;
                    if (viewModel != null)
                    {
                        // Set customer in ViewModel
                        viewModel.SelectedCustomer = customerWindow.SelectedCustomer;
                        viewModel.UpdateLoyaltyInfo();

                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Customer selected: {customerWindow.SelectedCustomer.FullName}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Customer selection cancelled");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ShowCustomerSelectionDialog: {ex.Message}");
                System.Windows.MessageBox.Show($"Error opening customer selection: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void IncreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                CartItem cartItem = null;

                // Try to get cart item from sender's tag first, then from SelectedCartItem
                if (sender is FrameworkElement element && element.Tag is CartItem tagCartItem)
                {
                    cartItem = tagCartItem;
                }
                else if (viewModel?.SelectedCartItem != null)
                {
                    cartItem = viewModel.SelectedCartItem;
                }

                if (cartItem == null || viewModel == null) return;

                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] IncreaseQuantity_Click called for item: {cartItem.Product.Name}");

                // Delegate quantity increase to ViewModel so it can respect FIFO batches and update price per batch
                decimal increment = cartItem.Product.IsWeightBased ? 0.1m : 1m;
                viewModel.IncreaseCartItemQuantityRespectingBatches(cartItem, increment);
                System.Diagnostics.Debug.WriteLine($"[CART_INCREASE] Increased with batch-aware logic - New Quantity: {cartItem.Quantity}, UnitPrice: {cartItem.UnitPrice}, Total: {cartItem.Total}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in IncreaseQuantity_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error increasing quantity: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        public void FocusSearchBox()
        {
            // Focus search box
            try
            {
                // Find the search textbox and focus it
                var searchBox = this.FindName("txtSearch") as TextBox;
                if (searchBox != null)
                {
                    searchBox.Focus();
                    searchBox.SelectAll();
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Search box focused via FocusSearchBox method");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[KEYBOARD] Search box not found in FocusSearchBox method");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[KEYBOARD] Error focusing search box: {ex.Message}");
            }
        }

        private void Favorites_Click(object sender, RoutedEventArgs e)
        {
            // Show favorites
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Favorites_Click called");
                if (viewModel.ShowFavoritesCommand?.CanExecute(null) == true)
                {
                    viewModel.ShowFavoritesCommand.Execute(null);
                }
            }
        }

        private void ExitEditMode_Click(object sender, RoutedEventArgs e)
        {
            // Exit edit mode
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] ExitEditMode_Click called");
                if (viewModel.ExitEditModeCommand?.CanExecute(null) == true)
                {
                    viewModel.ExitEditModeCommand.Execute(null);
                }
            }
        }

        private void DecreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                CartItem cartItem = null;

                // Try to get cart item from sender's tag first, then from SelectedCartItem
                if (sender is FrameworkElement element && element.Tag is CartItem tagCartItem)
                {
                    cartItem = tagCartItem;
                }
                else if (viewModel?.SelectedCartItem != null)
                {
                    cartItem = viewModel.SelectedCartItem;
                }

                if (cartItem == null || viewModel == null) return;

                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] DecreaseQuantity_Click called for item: {cartItem.Product.Name}");

                // For weight-based products, decrease by 0.1, for unit-based products, decrease by 1
                decimal decrement = cartItem.Product.IsWeightBased ? 0.1m : 1m;
                decimal newQuantity = cartItem.Quantity - decrement;

                // Minimum quantity check - for weight-based products, minimum is 0.1, for unit-based, minimum is 1
                decimal minimumQuantity = cartItem.Product.IsWeightBased ? 0.1m : 1m;

                if (newQuantity >= minimumQuantity)
                {
                    cartItem.Quantity = newQuantity;
                    viewModel.CalculateTotals();
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Quantity decreased to: {cartItem.Quantity}");
                }
                else
                {
                    // Remove item if quantity would become below minimum
                    if (viewModel.RemoveFromCartCommand?.CanExecute(cartItem.Product.Id) == true)
                    {
                        viewModel.RemoveFromCartCommand.Execute(cartItem.Product.Id);
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Item removed from cart: {cartItem.Product.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in DecreaseQuantity_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error decreasing quantity: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void CustomProduct_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] CustomProduct_Click called");
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Opening custom product dialog");

                    await ShowCustomProductDialog();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in CustomProduct_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error creating custom product: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task ShowCustomProductDialog()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Create dialog in background to prevent UI blocking
                CustomProductDialog customProductDialog = null;

                await Task.Run(() =>
                {
                    // ✅ PERFORMANCE FIX: Use cached dialog to prevent recreation overhead
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        customProductDialog = DialogCacheService.Instance.GetOrCreateCustomProductDialog();
                    }, System.Windows.Threading.DispatcherPriority.Background);
                });

                var result = await DialogHost.Show(customProductDialog, "SalesDialog");

                // Check if the dialog was closed with a successful result
                if (result is bool success && success)
                {
                    // Get the custom product and quantity from the dialog
                    var customProduct = customProductDialog.CustomProduct;
                    var quantity = customProductDialog.Quantity;

                    if (customProduct != null)
                    {
                        var viewModel = this.DataContext as SaleViewModel;
                        if (viewModel != null)
                        {
                            viewModel.AddToCart(customProduct, quantity);
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Custom product added: {customProduct.Name} x{quantity}");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Custom product dialog was cancelled");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ShowCustomProductDialog: {ex.Message}");
                System.Windows.MessageBox.Show($"Error adding custom product: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void CloseCart_Click(object sender, RoutedEventArgs e)
        {
            // Close cart
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null && sender is FrameworkElement element && element.Tag is Cart cart)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] CloseCart_Click called for cart: {cart.Name}");
                viewModel.CloseCart(cart);
            }
        }

        private void CloseActiveCart_Click(object sender, RoutedEventArgs e)
        {
            // Close the currently active cart
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null && viewModel.ActiveCart != null)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] CloseActiveCart_Click called for active cart: {viewModel.ActiveCart.Name}");
                viewModel.CloseCart(viewModel.ActiveCart);
            }
        }

        private void ClearCustomer_Click(object sender, RoutedEventArgs e)
        {
            // Clear customer
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[CART DEBUG] ClearCustomer_Click called");
                viewModel.SelectedCustomer = null;
                viewModel.HasLoyaltyCustomer = false;
            }
        }

        private void CategoriesList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            CategoryFilter_SelectionChanged(sender, e);
        }

        private void Cart_Click(object sender, RoutedEventArgs e)
        {
            // Handle cart tab selection
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null && sender is FrameworkElement element && element.DataContext is Cart cart)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Cart_Click called for cart: {cart.Name}");
                viewModel.SwitchToCart(cart);
            }
        }

        private void CartItems_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Cart items selection changed
        }

        private async void BarcodeSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] BarcodeSearch_Click called");
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Opening barcode search dialog");

                    await ShowBarcodeSearchDialog();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in BarcodeSearch_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error opening barcode search: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task ShowBarcodeSearchDialog()
        {
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel == null) return;

            try
            {
                // Create the barcode search dialog
                var barcodeDialog = new BarcodeSearchDialog(viewModel);

                // Show the dialog and wait for result
                var result = await DialogHost.Show(barcodeDialog, "SalesDialog");

                // If a product was found and returned
                if (result is Product foundProduct)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Product found via barcode search: {foundProduct.Name}");

                    // ✅ BARCODE SCANNER FIX: Get fresh product data from the same source as product cards
                    // This ensures the barcode scanner uses the same complete product data with accurate stock information
                    var freshProduct = viewModel.FilteredProducts?.FirstOrDefault(p => p.Id == foundProduct.Id);
                    if (freshProduct != null)
                    {
                        foundProduct = freshProduct; // Use the fresh product data with complete batch information
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Using fresh product data for: {foundProduct.Name}");
                    }

                    // ✅ BARCODE SCANNER FIX: Use AddToCartCommand for consistency with product cards
                    if (viewModel.AddToCartCommand.CanExecute(foundProduct))
                    {
                        viewModel.AddToCartCommand.Execute(foundProduct);
                    }

                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Product added to cart from barcode search");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Barcode search dialog closed without selecting product");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ShowBarcodeSearchDialog: {ex.Message}");
                System.Windows.MessageBox.Show($"Error in barcode search: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void ApplyItemDiscount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Apply item discount
                var viewModel = this.DataContext as SaleViewModel;
                CartItem cartItem = null;

                // Try to get cart item from sender's tag first, then from SelectedCartItem
                if (sender is FrameworkElement element && element.Tag is CartItem tagCartItem)
                {
                    cartItem = tagCartItem;
                }
                else if (viewModel?.SelectedCartItem != null)
                {
                    cartItem = viewModel.SelectedCartItem;
                }

                if (cartItem == null || viewModel == null) return;

                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] ApplyItemDiscount_Click called for item: {cartItem.Product.Name}");

                // Check if CurrentUser is available
                if (viewModel.CurrentUser == null)
                {
                    System.Windows.MessageBox.Show("No user is currently logged in.", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                // Get the current user ID and reload from the current context to avoid detached entity issues
                var currentUserId = viewModel.CurrentUser.Id;
                var discountService = viewModel.DiscountService as DiscountService;

                // Reload user from the current DbContext to avoid LINQ evaluation issues
                User currentUser;
                using (var context = new POSSystem.Data.POSDbContext())
                {
                    currentUser = context.Users.Include(u => u.UserRole).FirstOrDefault(u => u.Id == currentUserId);
                }

                if (currentUser == null)
                {
                    System.Windows.MessageBox.Show("Unable to load current user information.", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                // Create and show DiscountDialog for item discount
                var dialog = new DiscountDialog(
                    currentUser,
                    cartItem.Total,
                    false, // isCartWide = false for item discount
                    discountService);

                var result = await DialogHost.Show(dialog, "SalesDialog");
                if (result is Discount discount)
                {
                    // Additional null checks before applying discount
                    if (cartItem == null)
                    {
                        System.Windows.MessageBox.Show("No cart item is selected.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    if (viewModel.DiscountService == null)
                    {
                        System.Windows.MessageBox.Show("Discount service is not available.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    if (discount == null)
                    {
                        System.Windows.MessageBox.Show("Invalid discount information.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Applying item discount - Item: {cartItem.Product?.Name}, Discount: {discount.DiscountValue}");

                    viewModel.DiscountService.ApplyItemDiscount(cartItem, discount);
                    viewModel.CalculateTotals();
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Item discount applied: {discount.DiscountValue}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[CART DEBUG] Item discount dialog was cancelled");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ApplyItemDiscount_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error applying item discount: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void ApplyCartDiscount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Apply cart discount
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel != null && viewModel.HasCartItems)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] ApplyCartDiscount_Click called");
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] viewModel.CurrentUser: {viewModel.CurrentUser?.Username ?? "NULL"}");
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] viewModel.CurrentUser.Id: {viewModel.CurrentUser?.Id ?? -1}");

                    // Check if CurrentUser is available
                    if (viewModel.CurrentUser == null)
                    {
                        System.Diagnostics.Debug.WriteLine("[CART DEBUG] CurrentUser is null - showing error message");
                        System.Windows.MessageBox.Show("No user is currently logged in.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    // Get the current user ID and reload from the current context to avoid detached entity issues
                    var currentUserId = viewModel.CurrentUser.Id;
                    var discountService = viewModel.DiscountService as DiscountService;

                    // Reload user from the current DbContext to avoid LINQ evaluation issues
                    User currentUser;
                    using (var context = new POSSystem.Data.POSDbContext())
                    {
                        currentUser = context.Users.Include(u => u.UserRole).FirstOrDefault(u => u.Id == currentUserId);
                    }

                    if (currentUser == null)
                    {
                        System.Windows.MessageBox.Show("Unable to load current user information.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    // Additional null checks before creating dialog
                    if (viewModel.CurrentCart == null)
                    {
                        System.Windows.MessageBox.Show("No cart is available.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    if (discountService == null)
                    {
                        System.Windows.MessageBox.Show("Discount service is not available.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Creating cart discount dialog - Cart subtotal: {viewModel.CurrentCart.Subtotal}");

                    // Create and show DiscountDialog for cart discount
                    var dialog = new DiscountDialog(
                        currentUser,
                        viewModel.CurrentCart.Subtotal,
                        true, // isCartWide = true for cart discount
                        discountService);

                    var result = await DialogHost.Show(dialog, "SalesDialog");
                    if (result is Discount discount)
                    {
                        // Additional null checks before applying discount
                        if (viewModel.CurrentCart == null)
                        {
                            System.Windows.MessageBox.Show("No cart is available.", "Error",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                            return;
                        }

                        if (viewModel.DiscountService == null)
                        {
                            System.Windows.MessageBox.Show("Discount service is not available.", "Error",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                            return;
                        }

                        if (discount == null)
                        {
                            System.Windows.MessageBox.Show("Invalid discount information.", "Error",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                            return;
                        }

                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Applying cart discount - Cart items: {viewModel.CurrentCart.Items?.Count}, Discount: {discount.DiscountValue}");

                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] About to call ApplyCartWideDiscount...");
                            viewModel.DiscountService.ApplyCartWideDiscount(viewModel.CurrentCart, discount);
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] ApplyCartWideDiscount completed successfully");

                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] About to call CalculateTotals...");
                            viewModel.CalculateTotals();
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] CalculateTotals completed successfully");

                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Cart discount applied: {discount.DiscountValue}");
                        }
                        catch (Exception innerEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Inner exception in cart discount application: {innerEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Inner exception stack trace: {innerEx.StackTrace}");
                            throw new Exception($"Error in cart discount application: {innerEx.Message}", innerEx);
                        }
                    }
                    else
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Cart discount dialog was cancelled");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Error in ApplyCartDiscount_Click: {ex.Message}");
                System.Windows.MessageBox.Show($"Error applying cart discount: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task ShowProductNotFoundChoiceDialog(string barcode, TextBox textBox)
        {
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel == null)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] SaleViewModel is null in ShowProductNotFoundChoiceDialog");
                return;
            }

            try
            {

                // Validate barcode format
                if (string.IsNullOrWhiteSpace(barcode))
                {
                    MessageBox.Show("Invalid barcode format.", "Invalid Barcode",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Additional barcode validation - check if it's reasonable length and format
                if (barcode.Length < 4 || barcode.Length > 20)
                {
                    var lengthCheckResult = MessageBox.Show(
                        $"The barcode '{barcode}' has an unusual length ({barcode.Length} characters). " +
                        "Most barcodes are between 8-13 characters. Do you want to continue?",
                        "Unusual Barcode Length",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (lengthCheckResult == MessageBoxResult.No)
                    {
                        textBox.Text = "";
                        return;
                    }
                }

                // Create the product not found choice dialog
                var choiceDialog = new Views.Dialogs.ProductNotFoundChoiceDialog(barcode);

                // Show the dialog and wait for result
                var result = await DialogHost.Show(choiceDialog, "SalesDialog");

                if (result is Views.Dialogs.ProductNotFoundChoiceDialog dialogResult)
                {
                    if (dialogResult.SearchExternalDatabase)
                    {
                        // User chose to search external database
                        await SearchExternalDatabaseWithManualEntry(barcode, textBox, viewModel);
                    }
                    else if (dialogResult.CreateNewProduct)
                    {
                        // User chose to create new product
                        await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                    }
                }

                // Clear the search box
                textBox.Text = "";
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost"))
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] DialogHost error: {ex.Message}");
                // Fallback to simple MessageBox if DialogHost is not available
                var fallbackResult = MessageBox.Show(
                    $"Product with barcode '{barcode}' was not found.\n\nWould you like to create a new product with this barcode?",
                    "Product Not Found",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (fallbackResult == MessageBoxResult.Yes)
                {
                    await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                }
                textBox.Text = "";
            }
            catch (Exception ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Error in ShowProductNotFoundChoiceDialog: {ex.Message}");
                MessageBox.Show($"Error showing product options: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                textBox.Text = "";
            }
        }

        private async Task SearchExternalDatabaseWithManualEntry(string barcode, TextBox textBox, SaleViewModel viewModel)
        {
            try
            {
                // Check if external database file exists
                if (!System.IO.File.Exists("productsDB.db"))
                {
                    var dbNotFoundResult = MessageBox.Show(
                        "External products database (productsDB.db) not found.\n\nWould you like to create a new product instead?",
                        "External Database Not Found",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (dbNotFoundResult == MessageBoxResult.Yes)
                    {
                        await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                    }
                    return;
                }

                // Initialize external product lookup service
                var alertService = new SimpleAlertServiceImpl();
                var productLookupService = new ProductLookupService(alertService);

                // Try to search external database with the barcode
                var externalProduct = await productLookupService.LookupProductByBarcodeAsync(barcode);

                if (externalProduct != null)
                {
                    // Product found in external database - show import dialog
                    var importDialog = new Views.Dialogs.ExternalProductImportDialog(externalProduct, barcode);
                    var result = await ShowExternalProductImportDialog(importDialog);

                    if (result is Views.Dialogs.ExternalProductImportDialog importDialogResult && importDialogResult.OpenAddProductDialog)
                    {
                        // Open the Add Product dialog with pre-filled data
                        var addedProduct = await OpenAddProductDialogWithExternalData(externalProduct, barcode, viewModel);
                        if (addedProduct != null)
                        {
                            // Add the product to cart
                            if (viewModel.AddToCartCommand.CanExecute(addedProduct))
                            {
                                viewModel.AddToCartCommand.Execute(addedProduct);
                            }
                        }
                    }
                }
                else
                {
                    // Not found in external database either - offer to create new product
                    var result = MessageBox.Show(
                        $"Product with barcode '{barcode}' was not found in the external database either.\n\nWould you like to create a new product with this barcode?",
                        "Product Not Found",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                    }
                }
            }
            catch (System.Data.SQLite.SQLiteException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] SQLite error accessing external database: {ex.Message}");
                var dbErrorResult = MessageBox.Show(
                    $"Error accessing external database: {ex.Message}\n\nWould you like to create a new product instead?",
                    "Database Error",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);

                if (dbErrorResult == MessageBoxResult.Yes)
                {
                    await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                }
            }
            catch (System.IO.IOException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] IO error accessing external database: {ex.Message}");
                MessageBox.Show(
                    $"Cannot access external database file: {ex.Message}\n\nPlease check if the file is in use by another application.",
                    "File Access Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            catch (TimeoutException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Timeout searching external database: {ex.Message}");
                var timeoutResult = MessageBox.Show(
                    "External database search timed out. This may be due to a large database or network issues.\n\nWould you like to create a new product instead?",
                    "Search Timeout",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (timeoutResult == MessageBoxResult.Yes)
                {
                    await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                }
            }
            catch (Exception ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Error searching external database: {ex.Message}");
                var generalErrorResult = MessageBox.Show(
                    $"Error searching external database: {ex.Message}\n\nWould you like to create a new product instead?",
                    "Error",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);

                if (generalErrorResult == MessageBoxResult.Yes)
                {
                    await CreateNewProductWithBarcode(barcode, textBox, viewModel);
                }
            }
        }

        private async Task CreateNewProductWithBarcode(string barcode, TextBox textBox, SaleViewModel viewModel)
        {
            try
            {
                // Validate that we have the necessary services
                if (App.ServiceProvider == null)
                {
                    throw new InvalidOperationException("Service provider not initialized. Please restart the application.");
                }

                // Check if barcode already exists in the main database
                var existingProduct = viewModel.GetProductByBarcode(barcode);
                if (existingProduct != null)
                {
                    var existingProductResult = MessageBox.Show(
                        $"A product with barcode '{barcode}' already exists: {existingProduct.Name}\n\nWould you like to add it to the cart instead?",
                        "Barcode Already Exists",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Information);

                    if (existingProductResult == MessageBoxResult.Yes && viewModel.AddToCartCommand.CanExecute(existingProduct))
                    {
                        viewModel.AddToCartCommand.Execute(existingProduct);
                    }
                    return;
                }

                // Create a ProductsViewModel instance (we need this for the ProductDialog)
                var repositoryAdapter = (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter));
                if (repositoryAdapter == null)
                {
                    throw new InvalidOperationException("RepositoryServiceAdapter not available from DI. Please check service configuration.");
                }

                var databaseService = viewModel.DatabaseService as DatabaseService;
                if (databaseService == null)
                {
                    throw new InvalidOperationException("DatabaseService not available or is not of expected type.");
                }

                var productsViewModel = new ProductsViewModel(repositoryAdapter, new SimpleAlertServiceImpl(), databaseService);
                await productsViewModel.LoadInitialData();

                // Create the ProductDialog for new product creation
                var productDialog = new Views.Dialogs.ProductDialog(productsViewModel, "SalesDialog");

                // Set the barcode for the new product
                productDialog.SetBarcodeFromExternal(barcode);

                // Show the dialog
                var result = await ShowProductDialog(productDialog);

                if (result is Product savedProduct)
                {
                    // Product was successfully created
                    MessageBox.Show($"Product '{savedProduct.Name}' created successfully!", "Product Created",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // Add the product to cart
                    if (viewModel.AddToCartCommand.CanExecute(savedProduct))
                    {
                        viewModel.AddToCartCommand.Execute(savedProduct);
                    }
                }
            }
            catch (InvalidOperationException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Service configuration error: {ex.Message}");
                MessageBox.Show($"Service configuration error: {ex.Message}\n\nPlease restart the application and try again.",
                    "Configuration Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (System.Data.Common.DbException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Database error creating product: {ex.Message}");
                MessageBox.Show($"Database error while creating product: {ex.Message}\n\nPlease check database connectivity and try again.",
                    "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (UnauthorizedAccessException ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Access denied creating product: {ex.Message}");
                MessageBox.Show("You don't have permission to create new products.\n\nPlease contact your administrator.",
                    "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Error creating new product: {ex.Message}");
                MessageBox.Show($"Error creating new product: {ex.Message}\n\nPlease try again or contact support if the problem persists.",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task<object> ShowExternalProductImportDialog(Views.Dialogs.ExternalProductImportDialog importDialog)
        {
            // Try different DialogHost identifiers in order of preference
            string[] dialogIdentifiers = { "SalesDialog", "MainSalesDialog", "MainDialog" };

            foreach (string identifier in dialogIdentifiers)
            {
                try
                {
                    return await DialogHost.Show(importDialog, identifier);
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                {
                    // This DialogHost identifier doesn't exist, try the next one
                    continue;
                }
            }

            // If all identifiers failed, throw the last exception
            throw new InvalidOperationException("No suitable DialogHost found for showing external product import dialog");
        }

        private async Task<Product> OpenAddProductDialogWithExternalData(Product externalProduct, string barcode, SaleViewModel viewModel)
        {
            try
            {
                // Create a ProductsViewModel instance (we need this for the ProductDialog)
                var repositoryAdapter = (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter)) ??
                    throw new InvalidOperationException("RepositoryServiceAdapter not available from DI");
                var databaseService = viewModel.DatabaseService as DatabaseService ??
                    throw new InvalidOperationException("DatabaseService not available");
                var productsViewModel = new ProductsViewModel(repositoryAdapter, new SimpleAlertServiceImpl(), databaseService);
                await productsViewModel.LoadInitialData();

                // Create the ProductDialog with pre-filled external data
                var productDialog = new Views.Dialogs.ProductDialog(productsViewModel, "SalesDialog");

                // Pre-fill the dialog with external product data
                productDialog.AutoFillExternalProductInformation(externalProduct);

                // Set the barcode (this should be locked to prevent conflicts)
                productDialog.SetBarcodeFromExternal(barcode);

                // Show the dialog
                var result = await ShowProductDialog(productDialog);

                if (result is Product savedProduct)
                {
                    return savedProduct;
                }
            }
            catch (Exception ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Error opening add product dialog: {ex.Message}");
                MessageBox.Show($"Error opening product dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return null;
        }

        private async Task<object> ShowProductDialog(Views.Dialogs.ProductDialog productDialog)
        {
            // Try different DialogHost identifiers in order of preference
            string[] dialogIdentifiers = { "SalesDialog", "MainSalesDialog", "MainDialog" };

            foreach (string identifier in dialogIdentifiers)
            {
                try
                {
                    return await DialogHost.Show(productDialog, identifier);
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                {
                    // This DialogHost identifier doesn't exist, try the next one
                    continue;
                }
            }

            // If all identifiers failed, throw the last exception
            throw new InvalidOperationException("No suitable DialogHost found for showing product dialog");
        }

        private void AllCategories_Click(object sender, RoutedEventArgs e)
        {
            // All categories
            var viewModel = this.DataContext as SaleViewModel;
            if (viewModel != null)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] AllCategories_Click called");
                // Clear category filter and reset product loading state
                viewModel.ResetProductList();
                viewModel.SelectedCategory = null;
                viewModel.ShowingPopularItems = false;
                viewModel.ShowingFavorites = false;
                _ = viewModel.LoadMoreProducts();
            }
        }

        private async void ProcessBarcodeAsync(SaleViewModel viewModel, string barcode, TextBox textBox)
        {
            try
            {
                // Validate input parameters
                if (viewModel == null)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] SaleViewModel is null in ProcessBarcodeAsync");
                    return;
                }

                if (string.IsNullOrWhiteSpace(barcode))
                {
                    MessageBox.Show("Please enter a valid barcode.", "Invalid Barcode",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Trim and validate barcode
                barcode = barcode.Trim();

                // Check for obviously invalid barcodes
                if (barcode.Contains(" ") || barcode.Any(c => !char.IsLetterOrDigit(c) && c != '-' && c != '_'))
                {
                    var result = MessageBox.Show(
                        $"The barcode '{barcode}' contains invalid characters. Barcodes typically contain only numbers, letters, hyphens, and underscores.\n\nDo you want to continue anyway?",
                        "Invalid Barcode Format",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                    {
                        textBox.Text = "";
                        return;
                    }
                }

                try
                {
                    // First try main database, then external if not found
                    var product = await viewModel.GetProductByBarcodeWithExternalLookupAsync(barcode);

                    if (product != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Found product by barcode: {product.Name}");

                        // ✅ BARCODE SCANNER FIX: Get fresh product data from the same source as product cards
                        var freshProduct = viewModel.FilteredProducts?.FirstOrDefault(p => p.Id == product.Id);
                        if (freshProduct != null)
                        {
                            product = freshProduct; // Use the fresh product data with complete batch information
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Using fresh product data for: {product.Name}, Stock: {product.StockQuantity}");
                        }
                        else
                        {
                            if (product.TrackBatches)
                            {
                                System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Product {product.Name} not in FilteredProducts, refreshing batch data for accurate stock");
                                // GetProductByBarcodeWithExternalLookupAsync already loads batch data, so this is just a safety check
                            }
                            System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Using barcode lookup product data for: {product.Name}, Stock: {product.StockQuantity}");
                        }

                        // ✅ Use AddToCartCommand for consistency
                        if (viewModel.AddToCartCommand.CanExecute(product))
                        {
                            viewModel.AddToCartCommand.Execute(product);
                        }

                        // Clear the search box after successful barcode scan
                        textBox.Text = "";
                    }
                    else
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] No product found for barcode: {barcode}");
                        // Show choice dialog for adding product from external DB or creating new
                        await ShowProductNotFoundChoiceDialog(barcode, textBox);
                    }
                }
                catch (OperationCanceledException)
                {
                    // ✅ User cancelled the external product dialog → exit silently
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] External product dialog cancelled for barcode: {barcode}");
                    textBox.Text = "";
                    return;
                }
            }
            catch (Exception ex)
            {
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Error processing barcode: {ex.Message}");
                MessageBox.Show($"Error processing barcode: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Subscribe to product collection changes to update grid columns
        /// </summary>
        private void SubscribeToProductChanges()
        {
            try
            {
                if (ViewModel?.FilteredProducts != null)
                {
                    ViewModel.FilteredProducts.CollectionChanged += OnProductCollectionChanged;
                    System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Subscribed to FilteredProducts collection changes");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error subscribing to product changes: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Unsubscribe from product collection changes
        /// </summary>
        private void UnsubscribeFromProductChanges()
        {
            try
            {
                if (ViewModel?.FilteredProducts != null)
                {
                    ViewModel.FilteredProducts.CollectionChanged -= OnProductCollectionChanged;
                    System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Unsubscribed from FilteredProducts collection changes");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error unsubscribing from product changes: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ GRID LAYOUT: Handle product collection changes to update grid layout
        /// </summary>
        private void OnProductCollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            try
            {
                // Delay the update slightly to allow the UI to process the collection change
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateGridColumns();
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Error handling product collection change: {ex.Message}");
            }
        }

        /// <summary>
        /// 🔍 PERFORMANCE: Run comprehensive performance analysis
        /// </summary>
        public async Task<Services.Performance.PerformanceAnalysisReport> RunPerformanceAnalysisAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[SALESVIEWGRID] Starting performance analysis...");

                var testRunner = new Services.Performance.PerformanceTestRunner();
                var report = await testRunner.RunPerformanceAnalysisAsync(this, ViewModel);

                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Performance analysis completed. Score: {report.CalculateOverallScore():F1}/100");
                return report;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Performance analysis failed: {ex.Message}");
                throw;
            }
        }

        private async Task CreateFullInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult invoiceResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating full invoice for product: {invoiceResult.Product.Name}");

                // Use modern DI container instead of ServiceLocator
                var draftInvoiceService = App.ServiceProvider?.GetService<DraftInvoiceService>();
                var authService = App.ServiceProvider?.GetService<IAuthenticationService>() as AuthenticationService;
                var permissionsService = App.ServiceProvider?.GetService<IUserPermissionsService>() as UserPermissionsService;
                var dbService = App.ServiceProvider?.GetService<IDatabaseService>() as DatabaseService;
                var stockService = App.ServiceProvider?.GetService<POSSystem.Services.InventoryManagement.IStockService>();

                if (draftInvoiceService == null || authService == null || permissionsService == null || dbService == null || stockService == null)
                {
                    throw new InvalidOperationException("Required services are not available");
                }

                // IMPORTANT: Add reserved stock to product before creating the full invoice
                await AddReservedStockToProduct(invoiceResult.Product, invoiceResult.Quantity, stockService, dbService, authService);

                // Create draft invoice ViewModel with the product pre-filled
                var draftViewModel = new POSSystem.ViewModels.DraftInvoiceViewModel(draftInvoiceService, permissionsService, authService, dbService);

                // Pre-fill the product and customer
                draftViewModel.SelectedCustomer = invoiceResult.Customer;
                draftViewModel.SelectedProduct = invoiceResult.Product;
                draftViewModel.ProductQuantity = invoiceResult.Quantity;

                // Add the product automatically
                draftViewModel.AddProductCommand.Execute(null);

                // Show the draft invoice dialog
                var draftDialog = new POSSystem.Views.Dialogs.DraftInvoiceDialog(draftViewModel);
                var result = await DialogHost.Show(draftDialog, "SalesDialog");

                // Refresh the product display to show updated stock
                if (result != null)
                {
                    await RefreshProductDisplay(invoiceResult.Product.Id);
                }

                System.Diagnostics.Debug.WriteLine($"[INVOICE] Full invoice creation completed for: {invoiceResult.Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating full invoice: {ex.Message}");
                MessageBox.Show($"Error creating invoice from product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateDraftInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult invoiceResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating draft invoice for product: {invoiceResult.Product.Name}");

                // Use modern DI container instead of ServiceLocator
                var draftInvoiceService = App.ServiceProvider?.GetService<DraftInvoiceService>();
                var authService = App.ServiceProvider?.GetService<IAuthenticationService>() as AuthenticationService;
                var permissionsService = App.ServiceProvider?.GetService<IUserPermissionsService>() as UserPermissionsService;
                var dbService = App.ServiceProvider?.GetService<IDatabaseService>() as DatabaseService;
                var stockService = App.ServiceProvider?.GetService<POSSystem.Services.InventoryManagement.IStockService>();

                if (draftInvoiceService == null || authService == null || permissionsService == null || dbService == null || stockService == null)
                {
                    throw new InvalidOperationException("Required services are not available");
                }

                // IMPORTANT: Add reserved stock to product before creating the draft invoice
                await AddReservedStockToProduct(invoiceResult.Product, invoiceResult.Quantity, stockService, dbService, authService);

                // Create draft invoice ViewModel with the product pre-filled
                var draftViewModel = new POSSystem.ViewModels.DraftInvoiceViewModel(draftInvoiceService, permissionsService, authService, dbService);

                // Pre-fill the product and customer
                draftViewModel.SelectedCustomer = invoiceResult.Customer;
                draftViewModel.SelectedProduct = invoiceResult.Product;
                draftViewModel.ProductQuantity = invoiceResult.Quantity;

                // Add the product automatically
                draftViewModel.AddProductCommand.Execute(null);

                // Show the draft invoice dialog
                var draftDialog = new POSSystem.Views.Dialogs.DraftInvoiceDialog(draftViewModel);
                var result = await DialogHost.Show(draftDialog, "SalesDialog");

                // Refresh the product display to show updated stock
                if (result != null)
                {
                    await RefreshProductDisplay(invoiceResult.Product.Id);
                }

                System.Diagnostics.Debug.WriteLine($"[INVOICE] Draft invoice creation completed for: {invoiceResult.Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating draft invoice: {ex.Message}");
                MessageBox.Show($"Error creating invoice from product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Creates stock reservation only without opening draft invoice dialog
        /// </summary>
        private async Task CreateStockReservationOnly(POSSystem.ViewModels.ProductToInvoiceResult invoiceResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Creating stock reservation for product: {invoiceResult.Product.Name}");

                // Use modern DI container instead of ServiceLocator
                var authService = App.ServiceProvider?.GetService(typeof(IAuthenticationService)) as AuthenticationService;
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as DatabaseService;
                var stockService = App.ServiceProvider?.GetService(typeof(POSSystem.Services.InventoryManagement.IStockService)) as POSSystem.Services.InventoryManagement.IStockService;

                if (authService == null || dbService == null || stockService == null)
                {
                    throw new InvalidOperationException("Required services are not available");
                }

                // Add reserved stock to product
                await AddReservedStockToProduct(invoiceResult.Product, invoiceResult.Quantity, stockService, dbService, authService);

                // Refresh the product display to show updated stock
                await RefreshProductDisplay(invoiceResult.Product.Id);

                // Show success message
                MessageBox.Show($"Stock reservation created successfully!\n\nProduct: {invoiceResult.Product.Name}\nQuantity: {invoiceResult.Quantity}\n\nThe product now has stock available for sale.",
                    "Stock Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Stock reservation completed for: {invoiceResult.Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error creating stock reservation: {ex.Message}");
                MessageBox.Show($"Error creating stock reservation: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Adds reserved stock to a product and creates inventory batch if needed
        /// </summary>
        private async Task AddReservedStockToProduct(Product product, decimal quantity,
            POSSystem.Services.InventoryManagement.IStockService stockService,
            DatabaseService dbService, AuthenticationService authService)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Adding {quantity} reserved stock to product {product.Name} (ID: {product.Id})");

                var currentUser = authService.CurrentUser;
                if (currentUser == null)
                {
                    throw new InvalidOperationException("User not authenticated");
                }

                // Get the most recent batch information for batch-tracked products
                BatchStock mostRecentBatch = null;
                if (product.TrackBatches)
                {
                    var batches = dbService.GetBatchesForProduct(product.Id);
                    mostRecentBatch = batches?.OrderByDescending(b => b.CreatedAt).FirstOrDefault();
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Product tracks batches. Found {batches?.Count ?? 0} existing batches");
                }

                // Create new batch entry for the reserved stock
                if (product.TrackBatches)
                {
                    var reservationBatch = new BatchStock
                    {
                        ProductId = product.Id,
                        BatchNumber = $"RESERVE-{DateTime.Now:yyyyMMddHHmmss}",
                        Quantity = quantity,
                        ManufactureDate = DateTime.Now,
                        ExpiryDate = mostRecentBatch?.ExpiryDate, // Use expiry from most recent batch if available
                        PurchasePrice = mostRecentBatch?.PurchasePrice ?? 0m, // Use purchase price from most recent batch
                        SellingPrice = mostRecentBatch?.SellingPrice ?? product.SellingPrice,
                        Location = "Reserved Stock",
                        Notes = $"Stock reserved for out-of-stock product. Based on batch: {mostRecentBatch?.BatchNumber ?? "N/A"}",
                        CreatedAt = DateTime.Now
                    };

                    dbService.AddBatchStock(reservationBatch);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created reservation batch: {reservationBatch.BatchNumber}");
                }
                else
                {
                    // For non-batch tracked products, use the stock service to increase stock
                    stockService.IncreaseStock(product.Id, quantity, $"Stock reservation for out-of-stock product", null);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Increased stock for non-batch product by {quantity}");
                }

                // Create inventory transaction for tracking
                var transaction = new InventoryTransaction
                {
                    ProductId = product.Id,
                    TransactionType = "Stock Reservation",
                    Quantity = (int)Math.Ceiling(quantity), // Convert decimal to int, rounding up
                    TransactionDate = DateTime.Now,
                    UserId = currentUser.Id,
                    Notes = $"Stock reserved for out-of-stock product: {product.Name}. Reserved stock added via reserve invoice workflow."
                };

                dbService.AddInventoryTransaction(transaction);
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created inventory transaction for tracking");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error adding reserved stock: {ex.Message}");
                throw new Exception($"Failed to add reserved stock: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Refreshes the product display to show updated stock information
        /// </summary>
        private async Task RefreshProductDisplay(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Refreshing display for product ID: {productId}");

                // Get the updated product from database
                var dbService = App.ServiceProvider?.GetService<IDatabaseService>() as DatabaseService;
                if (dbService == null) return;

                var updatedProduct = dbService.GetProductById(productId);
                if (updatedProduct == null) return;

                // Update the product in the ViewModel's collection if it exists
                var viewModel = DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    // Find and update the product in the collections
                    var productInAllProducts = viewModel.AllProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInAllProducts != null)
                    {
                        // Update stock quantity and other properties
                        productInAllProducts.StockQuantity = updatedProduct.StockQuantity;
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Updated product stock to: {updatedProduct.StockQuantity}");
                    }

                    var productInFilteredProducts = viewModel.FilteredProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInFilteredProducts != null)
                    {
                        productInFilteredProducts.StockQuantity = updatedProduct.StockQuantity;
                    }

                    // Trigger UI refresh by refreshing the view model's data
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // Call the view model's refresh method if available
                        if (viewModel.RefreshProductsCommand?.CanExecute(null) == true)
                        {
                            viewModel.RefreshProductsCommand.Execute(null);
                        }
                        else
                        {
                            // Alternative: Force a UI refresh by invalidating commands
                            CommandManager.InvalidateRequerySuggested();
                        }
                    });
                }

                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Product display refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Error refreshing product display: {ex.Message}");
            }
        }

        #region Performance Optimizations

        /// <summary>
        /// ✅ CRITICAL PERFORMANCE FIX: Apply specific optimizations to SalesViewGrid
        /// </summary>
        private void ApplyPerformanceOptimizations()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Optimize rendering settings for this control
                System.Windows.Media.RenderOptions.SetBitmapScalingMode(this, System.Windows.Media.BitmapScalingMode.LowQuality);
                System.Windows.Media.RenderOptions.SetCachingHint(this, System.Windows.Media.CachingHint.Cache);

                // ✅ PERFORMANCE FIX: Optimize text rendering
                System.Windows.Media.TextOptions.SetTextFormattingMode(this, System.Windows.Media.TextFormattingMode.Ideal);
                System.Windows.Media.TextOptions.SetTextRenderingMode(this, System.Windows.Media.TextRenderingMode.Auto);

                // ✅ PERFORMANCE FIX: Enable bitmap caching for better performance
                this.CacheMode = new System.Windows.Media.BitmapCache
                {
                    RenderAtScale = 1.0,
                    SnapsToDevicePixels = true,
                    EnableClearType = false // Disable for better performance
                };

                // ✅ PERFORMANCE FIX: Reduce layout update frequency
                this.SetValue(System.Windows.FrameworkElement.UseLayoutRoundingProperty, true);

                System.Diagnostics.Debug.WriteLine("✅ [SALESVIEWGRID] Performance optimizations applied");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ [SALESVIEWGRID] Error applying performance optimizations: {ex.Message}");
            }
        }

        #endregion

        #region Performance Testing

        /// <summary>
        /// Run dialog performance analysis to validate optimizations
        /// </summary>
        public async Task<DialogPerformanceReport> RunDialogPerformanceTestAsync()
        {
            try
            {
                var viewModel = this.DataContext as SaleViewModel;
                if (viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("[DIALOG_PERF] No ViewModel available for testing");
                    return null;
                }

                var analyzer = new DialogPerformanceAnalyzer();
                var report = await analyzer.AnalyzeDialogPerformanceAsync(viewModel);

                // Log summary results
                System.Diagnostics.Debug.WriteLine($"[DIALOG_PERF] Performance Test Results:");
                System.Diagnostics.Debug.WriteLine($"  - Average Creation Time: {report.AverageDialogCreationTime:F2}ms");
                System.Diagnostics.Debug.WriteLine($"  - Cache Performance Improvement: {report.CachingTest?.PerformanceImprovement:F1}%");
                System.Diagnostics.Debug.WriteLine($"  - Test Success: {report.IsSuccessful}");

                return report;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG_PERF] Error running performance test: {ex.Message}");
                return null;
            }
        }

        #endregion

        // Simple implementation of IAlertService for external product lookup
        private class SimpleAlertServiceImpl : IAlertService
        {
            public void CheckExpiringProducts() { }
            public void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product") { }
            public List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1) => new List<ProductAlert>();
            public List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1) => new List<ProductAlert>();
            public int GetTotalAlertsCount() => 0;
            public List<ProductAlert> GetUnreadAlerts() => new List<ProductAlert>();
            public List<ProductAlert> GetUnreadAlertsBasic() => new List<ProductAlert>();
            public int GetUnreadAlertsCount() => 0;
            public void MarkAlertAsRead(int alertId) { }
            public void MarkAllAlertsAsRead() { }
            public void ShowError(string message) { }
            public void ClearAlertCache() { }
        }
    }
}
