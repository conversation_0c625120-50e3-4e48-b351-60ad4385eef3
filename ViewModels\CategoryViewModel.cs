using System;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Collections.ObjectModel;
using POSSystem.Services;
using POSSystem.Models;
using POSSystem.ViewModels.Commands;

namespace POSSystem.ViewModels
{
    public class CategoryViewModel : ViewModelBase
    {
        private readonly IDbService _dbService;
        private Category _newCategory;
        private bool _isEditMode;
        private ObservableCollection<Category> _categories;
        private ICommand _saveCategoryCommand;

        public ICommand SaveCategoryCommand => _saveCategoryCommand ??= new RelayCommand(_ => SaveCategory());

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged();
            }
        }

        public Category NewCategory
        {
            get => _newCategory;
            set
            {
                _newCategory = value;
                OnPropertyChanged();
            }
        }

        public CategoryViewModel(IDbService dbService)
        {
            _dbService = dbService;
            Categories = new ObservableCollection<Category>();
            NewCategory = new Category();
            IsEditMode = false;
            LoadCategories();
        }

        private void LoadCategories()
        {
            var categories = _dbService.GetAllCategories();
            Categories = new ObservableCollection<Category>(categories);
            RefreshSaleViewCategories();
        }

        private void CancelEdit()
        {
            NewCategory = new Category();
            IsEditMode = false;
        }

        private async void RefreshSaleViewCategories()
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.DataContext is SaleViewModel saleViewModel)
                    {
                        await saleViewModel.RefreshCategories();
                    }
                }
            });
        }

        private void SaveCategory()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NewCategory.Name))
                {
                    MessageBox.Show("Category name is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (IsEditMode)
                {
                    _dbService.UpdateCategory(NewCategory);
                }
                else
                {
                    _dbService.AddCategory(NewCategory);
                }

                LoadCategories();
                CancelEdit();
                MessageBox.Show("Category saved successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving category: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 