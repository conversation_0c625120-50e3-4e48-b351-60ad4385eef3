using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Services.Repositories;
using POSSystem.Data; // ✅ FIX: Add using for POSDbContext

namespace POSSystem.Services.Caching
{
    /// <summary>
    /// Cached repository service that wraps existing repositories with intelligent caching
    /// Provides high-performance data access with automatic cache invalidation
    /// </summary>
    public class CachedRepositoryService
    {
        private readonly ICacheService _cache;
        private readonly IProductRepository _productRepository;
        private readonly ICustomerRepository _customerRepository;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<CachedRepositoryService> _logger;

        // Cache key patterns
        private const string PRODUCTS_ALL_KEY = "products:all";
        private const string PRODUCTS_ACTIVE_KEY = "products:active";
        private const string PRODUCTS_LOW_STOCK_KEY = "products:lowstock";
        private const string PRODUCTS_EXPIRING_KEY = "products:expiring:{0}";
        private const string PRODUCT_BY_ID_KEY = "product:id:{0}";
        private const string PRODUCT_BY_BARCODE_KEY = "product:barcode:{0}";
        private const string PRODUCT_STATS_KEY = "products:statistics";
        
        private const string CATEGORIES_ALL_KEY = "categories:all";
        private const string UNITS_ALL_KEY = "units:all";
        
        private const string CUSTOMERS_ALL_KEY = "customers:all";
        private const string CUSTOMERS_ACTIVE_KEY = "customers:active";
        private const string CUSTOMER_BY_ID_KEY = "customer:id:{0}";
        private const string CUSTOMER_BY_PHONE_KEY = "customer:phone:{0}";
        private const string CUSTOMER_BY_EMAIL_KEY = "customer:email:{0}";

        public CachedRepositoryService(
            ICacheService cache,
            DatabaseService databaseService,
            IProductRepository productRepository = null,
            ICustomerRepository customerRepository = null,
            ILogger<CachedRepositoryService> logger = null)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _productRepository = productRepository; // Can be null
            _customerRepository = customerRepository; // Can be null
            _logger = logger;
        }

        #region Product Operations with Caching

        /// <summary>
        /// Get all products with intelligent caching
        /// </summary>
        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            if (_productRepository == null)
            {
                _logger?.LogWarning("ProductRepository not available, returning empty collection");
                return new List<Product>();
            }

            return await _cache.GetOrSetAsync(
                PRODUCTS_ALL_KEY,
                async () => await _productRepository.GetAllAsync(),
                TimeSpan.FromMinutes(30) // Business data cache level
            );
        }

        /// <summary>
        /// Get product by ID with caching
        /// </summary>
        public async Task<Product> GetProductByIdAsync(int id)
        {
            var key = string.Format(PRODUCT_BY_ID_KEY, id);
            return await _cache.GetOrSetAsync(
                key,
                async () => await _productRepository.GetByIdAsync(id),
                TimeSpan.FromMinutes(15)
            );
        }

        /// <summary>
        /// Get product by barcode with caching
        /// </summary>
        public async Task<Product> GetProductByBarcodeAsync(string barcode)
        {
            var key = string.Format(PRODUCT_BY_BARCODE_KEY, barcode);
            return await _cache.GetOrSetAsync(
                key,
                async () => await _productRepository.GetByBarcodeAsync(barcode),
                TimeSpan.FromMinutes(10)
            );
        }

        /// <summary>
        /// Get low stock products with caching
        /// </summary>
        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _cache.GetOrSetAsync(
                PRODUCTS_LOW_STOCK_KEY,
                async () => await _productRepository.GetLowStockAsync(),
                TimeSpan.FromMinutes(5) // Transactional data - shorter cache
            );
        }

        /// <summary>
        /// Get expiring products with caching
        /// </summary>
        public async Task<IEnumerable<Product>> GetExpiringProductsAsync(int daysThreshold = 30)
        {
            var key = string.Format(PRODUCTS_EXPIRING_KEY, daysThreshold);
            return await _cache.GetOrSetAsync(
                key,
                async () => await _productRepository.GetExpiringProductsAsync(daysThreshold),
                TimeSpan.FromMinutes(10)
            );
        }

        /// <summary>
        /// Get product statistics with caching
        /// </summary>
        public async Task<(int total, int lowStock, decimal inventoryValue)> GetProductStatisticsAsync()
        {
            if (_productRepository == null)
            {
                _logger?.LogWarning("ProductRepository not available, returning default statistics");
                return (0, 0, 0);
            }

            var cachedStats = await _cache.GetOrSetAsync(
                PRODUCT_STATS_KEY,
                async () =>
                {
                    var total = await _productRepository.GetTotalCountAsync();
                    var lowStock = await _productRepository.GetLowStockCountAsync();
                    var inventoryValue = await _productRepository.GetTotalInventoryValueAsync();
                    return new ProductStatistics { Total = total, LowStock = lowStock, InventoryValue = inventoryValue };
                },
                TimeSpan.FromMinutes(5)
            );

            return (cachedStats.Total, cachedStats.LowStock, cachedStats.InventoryValue);
        }

        /// <summary>
        /// Search products with temporary caching
        /// </summary>
        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm, int maxResults = 50)
        {
            var key = $"products:search:{searchTerm}:{maxResults}";
            return await _cache.GetOrSetAsync(
                key,
                async () => await _productRepository.SearchAsync(searchTerm, maxResults),
                TimeSpan.FromMinutes(2) // Temporary cache for search results
            );
        }

        #endregion

        #region Customer Operations with Caching

        /// <summary>
        /// Get all active customers with caching
        /// </summary>
        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            return await _cache.GetOrSetAsync(
                CUSTOMERS_ACTIVE_KEY,
                async () => await _customerRepository.GetActiveCustomersAsync(),
                TimeSpan.FromMinutes(20)
            );
        }

        /// <summary>
        /// Get customer by ID with caching
        /// </summary>
        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            var key = string.Format(CUSTOMER_BY_ID_KEY, id);
            return await _cache.GetOrSetAsync(
                key,
                async () => await _customerRepository.GetByIdAsync(id),
                TimeSpan.FromMinutes(15)
            );
        }

        /// <summary>
        /// Get customer by phone with caching
        /// </summary>
        public async Task<Customer> GetCustomerByPhoneAsync(string phone)
        {
            var key = string.Format(CUSTOMER_BY_PHONE_KEY, phone);
            return await _cache.GetOrSetAsync(
                key,
                async () => await _customerRepository.GetByPhoneAsync(phone),
                TimeSpan.FromMinutes(10)
            );
        }

        /// <summary>
        /// Search customers with temporary caching
        /// </summary>
        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm, int maxResults = 50)
        {
            var key = $"customers:search:{searchTerm}:{maxResults}";
            return await _cache.GetOrSetAsync(
                key,
                async () => await _customerRepository.SearchAsync(searchTerm, maxResults),
                TimeSpan.FromMinutes(2)
            );
        }

        #endregion

        #region Reference Data Caching

        /// <summary>
        /// Get all categories with long-term caching (reference data)
        /// </summary>
        public async Task<IEnumerable<Category>> GetCategoriesAsync()
        {
            return await _cache.GetFromLevel<IEnumerable<Category>>(
                CacheLevel.Reference, 
                "categories"
            ) ?? await LoadAndCacheCategories();
        }

        /// <summary>
        /// Get all units of measure with long-term caching (reference data)
        /// </summary>
        public async Task<IEnumerable<UnitOfMeasure>> GetUnitsOfMeasureAsync()
        {
            return await _cache.GetFromLevel<IEnumerable<UnitOfMeasure>>(
                CacheLevel.Reference, 
                "units"
            ) ?? await LoadAndCacheUnits();
        }

        private async Task<IEnumerable<Category>> LoadAndCacheCategories()
        {
            try
            {
                // ✅ FIX: Use new context to avoid threading issues
                using var context = new POSDbContext();
                var categories = await context.Categories
                    .AsNoTracking()
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                await _cache.SetToLevel(CacheLevel.Reference, "categories", categories);
                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading categories for cache");
                return new List<Category>();
            }
        }

        private async Task<IEnumerable<UnitOfMeasure>> LoadAndCacheUnits()
        {
            try
            {
                // Load units of measure from database using the injected DatabaseService
                var units = await _databaseService.Context.UnitsOfMeasure
                    .AsNoTracking()
                    .OrderBy(u => u.Name)
                    .ToListAsync();

                await _cache.SetToLevel(CacheLevel.Reference, "units", units);
                return units;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading units of measure for cache");
                return new List<UnitOfMeasure>();
            }
        }

        #endregion

        #region Cache Invalidation

        /// <summary>
        /// Invalidate product-related cache when products are modified
        /// </summary>
        public async Task InvalidateProductCacheAsync(int? productId = null)
        {
            await _cache.RemoveAsync(PRODUCTS_ALL_KEY);
            await _cache.RemoveAsync(PRODUCTS_ACTIVE_KEY);
            await _cache.RemoveAsync(PRODUCTS_LOW_STOCK_KEY);
            await _cache.RemoveAsync(PRODUCT_STATS_KEY);
            
            if (productId.HasValue)
            {
                var key = string.Format(PRODUCT_BY_ID_KEY, productId.Value);
                await _cache.RemoveAsync(key);
            }

            _logger?.LogDebug("Invalidated product cache for product {ProductId}", productId);
        }

        /// <summary>
        /// Invalidate customer-related cache when customers are modified
        /// </summary>
        public async Task InvalidateCustomerCacheAsync(int? customerId = null)
        {
            await _cache.RemoveAsync(CUSTOMERS_ALL_KEY);
            await _cache.RemoveAsync(CUSTOMERS_ACTIVE_KEY);
            
            if (customerId.HasValue)
            {
                var key = string.Format(CUSTOMER_BY_ID_KEY, customerId.Value);
                await _cache.RemoveAsync(key);
            }

            _logger?.LogDebug("Invalidated customer cache for customer {CustomerId}", customerId);
        }

        /// <summary>
        /// Invalidate reference data cache (categories, units)
        /// </summary>
        public async Task InvalidateReferenceDataCacheAsync()
        {
            await _cache.RemoveAsync(CATEGORIES_ALL_KEY);
            await _cache.RemoveAsync(UNITS_ALL_KEY);

            _logger?.LogDebug("Invalidated reference data cache");
        }

        /// <summary>
        /// Clear all cached data - synchronous version for compatibility
        /// </summary>
        public void ClearCache()
        {
            try
            {
                // Use async method but wait for completion
                Task.Run(async () => await ClearCacheAsync()).Wait();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error clearing cache synchronously");
            }
        }

        /// <summary>
        /// Clear all cached data - asynchronous version
        /// </summary>
        public async Task ClearCacheAsync()
        {
            try
            {
                await _cache.ClearAsync();
                _logger?.LogInformation("All cached data cleared");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error clearing cache");
                throw;
            }
        }

        #endregion
    }

    /// <summary>
    /// Wrapper class for product statistics to enable caching of tuple values
    /// </summary>
    public class ProductStatistics
    {
        public int Total { get; set; }
        public int LowStock { get; set; }
        public decimal InventoryValue { get; set; }
    }
}
