using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Data;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Windows.Threading;
using System.Threading;

namespace POSSystem.ViewModels
{
    public class CategoriesViewModel : INotifyPropertyChanged, IDisposable
    {
        public static event EventHandler<CategoryUpdateEventArgs> CategoryChanged;

        private readonly DatabaseService _dbService = new();
        private ObservableCollection<Category> _categories;
        private ObservableCollection<Category> _allCategories;
        private int _totalCategories;
        private int _totalProducts;
        private decimal _averageProductsPerCategory;
        private bool _isLoading;
        private readonly Dispatcher _dispatcher;
        private readonly SemaphoreSlim _loadLock = new SemaphoreSlim(1, 1);

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public int TotalCategories
        {
            get => _totalCategories;
            set
            {
                _totalCategories = value;
                OnPropertyChanged();
            }
        }

        public int TotalProducts
        {
            get => _totalProducts;
            set
            {
                _totalProducts = value;
                OnPropertyChanged();
            }
        }

        public decimal AverageProductsPerCategory
        {
            get => _averageProductsPerCategory;
            set
            {
                _averageProductsPerCategory = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Category> AllCategories
        {
            get => _allCategories;
            set
            {
                _allCategories = value;
                OnPropertyChanged();
            }
        }

        public CategoriesViewModel()
        {
            _dispatcher = Application.Current.Dispatcher;
            Categories = new ObservableCollection<Category>();
            AllCategories = new ObservableCollection<Category>();
            _ = LoadCategoriesAsync();
        }

        public async Task LoadCategoriesAsync()
        {
            await _loadLock.WaitAsync();
            IsLoading = true;
            try
            {
                var categories = await _dbService.GetAllCategoriesAsync();
                AllCategories = new ObservableCollection<Category>(categories);
                Categories = new ObservableCollection<Category>(categories);

                // Update statistics
                TotalCategories = categories.Count;
                TotalProducts = categories.Sum(c => c.Products.Count);
                AverageProductsPerCategory = TotalCategories > 0 ? (decimal)TotalProducts / TotalCategories : 0;

                await RefreshSaleViewCategories();
            }
            catch (Exception ex)
            {
                await _dispatcher.InvokeAsync(() =>
                {
                    var errorMessage = Application.Current.Resources["ErrorLoadingCategories"] as string ?? "Error loading categories: {0}";
                    var errorTitle = Application.Current.Resources["ErrorTitle"] as string ?? "Error";

                    MessageBox.Show(
                        string.Format(errorMessage, ex.Message),
                        errorTitle,
                        MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
                _loadLock.Release();
            }
        }

        private async Task RefreshSaleViewCategories()
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.DataContext is SaleViewModel saleViewModel)
                    {
                        await saleViewModel.RefreshCategories();
                    }
                }
            });
        }

        public async Task AddCategory(Category category)
        {
            try
            {
                _dbService.AddCategory(category);
                await LoadCategoriesAsync();
                // Notify all listeners that a category was added
                CategoryChanged?.Invoke(this, new CategoryUpdateEventArgs
                {
                    UpdateType = CategoryUpdateType.Added,
                    Category = category
                });
            }
            catch (Exception ex)
            {
                var errorMessage = Application.Current.Resources["ErrorAddingCategory"] as string ?? "Error adding category: {0}";
                var errorTitle = Application.Current.Resources["ErrorTitle"] as string ?? "Error";

                MessageBox.Show(
                    string.Format(errorMessage, ex.Message),
                    errorTitle,
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public async Task UpdateCategory(Category category)
        {
            try
            {
                _dbService.UpdateCategory(category);
                await LoadCategoriesAsync();
                // Notify all listeners that a category was updated
                CategoryChanged?.Invoke(this, new CategoryUpdateEventArgs
                {
                    UpdateType = CategoryUpdateType.Updated,
                    Category = category
                });
            }
            catch (Exception ex)
            {
                var errorMessage = Application.Current.Resources["ErrorUpdatingCategory"] as string ?? "Error updating category: {0}";
                var errorTitle = Application.Current.Resources["ErrorTitle"] as string ?? "Error";

                MessageBox.Show(
                    string.Format(errorMessage, ex.Message),
                    errorTitle,
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public async Task DeleteCategory(int id)
        {
            try
            {
                var category = _dbService.GetCategory(id);
                _dbService.DeleteCategory(id);
                await LoadCategoriesAsync();
                // Notify all listeners that a category was deleted
                CategoryChanged?.Invoke(this, new CategoryUpdateEventArgs
                {
                    UpdateType = CategoryUpdateType.Deleted,
                    Category = category
                });
            }
            catch (Exception ex)
            {
                var errorMessage = Application.Current.Resources["ErrorDeletingCategory"] as string ?? "Error deleting category: {0}";
                var errorTitle = Application.Current.Resources["ErrorTitle"] as string ?? "Error";

                MessageBox.Show(
                    string.Format(errorMessage, ex.Message),
                    errorTitle,
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Notifies all listeners that a category was added externally
        /// </summary>
        public static void NotifyCategoryAdded(Category category)
        {
            CategoryChanged?.Invoke(null, new CategoryUpdateEventArgs
            {
                UpdateType = CategoryUpdateType.Added,
                Category = category
            });
        }

        #region IDisposable Implementation
        private bool _disposed;
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            try
            {
                _loadLock?.Dispose();
                (_dbService as IDisposable)?.Dispose();
            }
            catch { }
        }
        #endregion


        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }

    public enum CategoryUpdateType
    {
        Added,
        Updated,
        Deleted
    }

    public class CategoryUpdateEventArgs : EventArgs
    {
        public CategoryUpdateType UpdateType { get; set; }
        public Category Category { get; set; }
    }
}