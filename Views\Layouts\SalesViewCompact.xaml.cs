using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using System.Linq;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using System.Media;
using System.Diagnostics;
using POSSystem.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Views.Layouts
{
    public partial class SalesViewCompact : UserControl
    {
        private readonly ISettingsService _settingsService;
        private readonly IDialogService _dialogService;
        private SaleViewModel ViewModel => (SaleViewModel)DataContext;

        public SalesViewCompact()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            _dialogService = App.ServiceProvider?.GetService<IDialogService>() ?? new DialogService();

            // Initialize data context if not set
            if (DataContext == null)
            {
                DataContext = new SaleViewModel();
            }

            this.Loaded += SalesViewCompact_Loaded;
            // Subscribe to DataContext changes for event wiring
            this.DataContextChanged += OnDataContextChanged;
            this.Unloaded += OnUnloaded;
        }

        private void SalesViewCompact_Loaded(object sender, RoutedEventArgs e)
        {
            // Initialize theme selection button
            CreateThemeSelectionButton();

            // Set up category filter
            if (categoryFilter != null && ViewModel != null)
            {
                categoryFilter.ItemsSource = ViewModel.Categories;
                categoryFilter.SelectionChanged += CategoryFilter_SelectionChanged;
            }

            // Set up search box with optimizations
            if (txtSearch != null)
            {
                txtSearch.TextChanged += TxtSearch_TextChanged;
                txtSearch.KeyDown += TxtSearch_KeyDown;

                // Apply barcode scanning optimizations
                SearchHelper.OptimizeForBarcodeScanning(txtSearch);
            }

            // Initialize products if needed
            if (ViewModel != null && ViewModel.FilteredProducts.Count == 0)
            {
                _ = ViewModel.RefreshProducts();
            }
        }

        private void CreateThemeSelectionButton()
        {
            // Create a button to open theme selection
            var themeButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignIconButton"),
                ToolTip = Application.Current.FindResource("ChangeLayout") as string,
                Margin = new Thickness(8),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Width = 36,
                Height = 36
            };

            themeButton.Content = new PackIcon
            {
                Kind = PackIconKind.ViewDashboard,
                Width = 24,
                Height = 24
            };

            themeButton.Click += ThemeButton_Click;

            // Add to the main grid - using safe casting
            if (this.Content is Grid mainGrid)
            {
                Grid.SetColumn(themeButton, 0);
                mainGrid.Children.Add(themeButton);
            }
        }

        private async void ThemeButton_Click(object sender, RoutedEventArgs e)
        {
            // For DialogHost compatibility, inform user about theme selection
            MessageBox.Show(
                "To avoid DialogHost conflicts, theme selection is now controlled from the parent container." +
                "\n\nPlease use the theme button in the top-right corner of the screen.",
                "Layout Information",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private async Task ShowThemeSelectionDialog()
        {
            var view = new StackPanel { Margin = new Thickness(16) };

            // Create title
            var title = new TextBlock
            {
                Text = Application.Current.FindResource("SelectLayout") as string,
                Style = (Style)FindResource("MaterialDesignHeadline6TextBlock"),
                Margin = new Thickness(0, 0, 0, 16)
            };
            view.Children.Add(title);

            // Current layout theme
            var currentTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

            // Create layout options
            var themes = new[]
            {
                new { Code = "Standard", Name = Application.Current.FindResource("StandardLayout") as string, Description = "Default sales layout with product cards and cart" },
                new { Code = "Compact", Name = Application.Current.FindResource("CompactLayout") as string, Description = "Compact layout with smaller product cards and simplified cart" },
                new { Code = "Modern", Name = Application.Current.FindResource("ModernLayout") as string, Description = "Modern layout with large product images and enhanced visual elements" },
                new { Code = "Grid", Name = Application.Current.FindResource("GridLayout") as string, Description = "Grid-based layout with tabular product listing and detailed information" }
            };

            var themeWrapPanel = new WrapPanel();
            foreach (var theme in themes)
            {
                var themeCard = new Card
                {
                    Width = 120,
                    Height = 140,
                    Margin = new Thickness(8),
                    Padding = new Thickness(8),
                    Tag = theme.Code,
                    Background = theme.Code == currentTheme
                        ? (System.Windows.Media.Brush)FindResource("PrimaryHueLightBrush")
                        : System.Windows.Media.Brushes.White
                };

                var themePanel = new StackPanel();
                themePanel.Children.Add(new PackIcon
                {
                    Kind = theme.Code switch
                    {
                        "Standard" => PackIconKind.ViewAgenda,
                        "Compact" => PackIconKind.ViewCompactOutline,
                        "Modern" => PackIconKind.ViewCarousel,
                        "Grid" => PackIconKind.ViewGridOutline,
                        _ => PackIconKind.ViewAgenda
                    },
                    Width = 32,
                    Height = 32,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 8, 0, 8)
                });

                themePanel.Children.Add(new TextBlock
                {
                    Text = theme.Name,
                    FontWeight = FontWeights.Medium,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 4, 0, 4)
                });

                themePanel.Children.Add(new TextBlock
                {
                    Text = theme.Description,
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Center,
                    FontSize = 11,
                    Opacity = 0.8,
                    Margin = new Thickness(0, 2, 0, 0)
                });

                themeCard.Content = themePanel;

                var themeBorder = new Border();
                themeBorder.Child = themeCard;
                themeBorder.MouseDown += ThemeCard_MouseDown;

                themeWrapPanel.Children.Add(themeBorder);
            }

            view.Children.Add(themeWrapPanel);

            var result = await DialogHost.Show(view, "SalesDialog");
        }

        private void ThemeCard_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border &&
                border.Child is Card card &&
                card.Tag is string themeCode)
            {
                // Save the selected theme
                _settingsService.SaveSetting("SalesLayoutTheme", themeCode);

                // Ask user if they want to restart
                var result = MessageBox.Show(
                    (Application.Current.FindResource("LayoutChangeRestart") as string) + " " +
                    (Application.Current.FindResource("RestartNow") as string),
                    (Application.Current.FindResource("ThemeChanged") as string),
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Restart the application
                    System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    Application.Current.Shutdown();
                }

                // Close the dialog
                DialogHost.Close("SalesDialog");
            }
        }

        // Event handlers for cart items
        private void IncreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is CartItem item)
            {
                item.Quantity++;
                ViewModel.CalculateTotals();
            }
        }

        private void DecreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is CartItem item)
            {
                if (item.Quantity > 1)
                {
                    item.Quantity--;
                    ViewModel.CalculateTotals();
                }
                else
                {
                    ViewModel.RemoveFromCart(item.Product.Id);
                }
            }
        }

        private void RemoveItem_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is CartItem item)
            {
                ViewModel.RemoveFromCart(item.Product.Id);
            }
        }

        private async void Product_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && element.DataContext is Models.Product product && ViewModel != null)
            {
                // Check if product has enough stock before adding to cart (skip for services)
                if (product.Id >= 0 && product.Type != Models.ProductType.Service && product.StockQuantity <= 0m)
                {
                    System.Diagnostics.Debug.WriteLine($"[OutOfStock] Product {product.Name} is out of stock, showing confirmation prompt");

                    // Show confirmation prompt for out-of-stock products
                    var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                    var message = $"This product is currently out of stock (0 items available).\n\nWould you like to create a reservation invoice for this product instead?";

                    var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                    if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User confirmed reservation creation for {product.Name}");
                        // User wants to create a reservation - open the reservation dialog
                        await CreateReservationInvoice_ClickInternal(product);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User declined reservation creation for {product.Name}");
                        // User declined - do nothing, return to normal browsing
                    }
                    return;
                }

                // ✅ WEIGHT-BASED PRODUCT FIX: Use AddToCartCommand for consistent behavior
                if (ViewModel.AddToCartCommand.CanExecute(product))
                {
                    ViewModel.AddToCartCommand.Execute(product);
                }
            }
        }

        private async Task CreateInvoiceFromOutOfStockProduct(Models.Product product)
        {
            try
            {
                // Get required services
                var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                if (permissionsService == null || dbService == null)
                {
                    MessageBox.Show("Required services not available.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Create and show confirmation dialog
                var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                // Show invoice confirmation dialog
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(confirmationDialog, "SalesDialog");

                if (confirmationDialog.DialogResult?.Confirmed == true)
                {
                    var invoiceResult = confirmationDialog.DialogResult;

                    if (invoiceResult.CreateFullInvoice)
                    {
                        // Admin user - create full invoice directly
                        MessageBox.Show($"Full invoice creation for {invoiceResult.Product.Name} would be implemented here.",
                            "Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        // Non-admin user - create draft invoice
                        MessageBox.Show($"Draft invoice creation for {invoiceResult.Product.Name} would be implemented here.",
                            "Draft Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error in CreateInvoiceFromOutOfStockProduct: {ex.Message}");
                MessageBox.Show($"Error creating invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is SaleViewModel)
            {
                SaleViewModel.SaleCompleted -= OnSaleCompleted;
                SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            }
            if (e.NewValue is SaleViewModel)
            {
                SaleViewModel.SaleCompleted += OnSaleCompleted;
                SaleViewModel.ProductStockChanged += OnProductStockChanged;
            }
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            SaleViewModel.SaleCompleted -= OnSaleCompleted;
            SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            this.DataContextChanged -= OnDataContextChanged;
            this.Unloaded -= OnUnloaded;
        }

        private async void OnSaleCompleted(object sender, System.EventArgs e)
        {
            try
            {
                await ViewModel.RefreshProducts();
            }
            catch { }
        }

        private void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            var product = ViewModel?.FilteredProducts?.FirstOrDefault(p => p.Id == e.ProductId);
            if (product != null)
            {
                product.StockQuantity = e.NewStockQuantity;
            }
        }

        private void ProcessPayment_Click(object sender, RoutedEventArgs e)
        {
            if (ViewModel?.CurrentCart?.Items.Count > 0)
            {
                // Show payment dialog using the DialogService
                ShowPaymentDialog();
            }
            else
            {
                MessageBox.Show(
                    Application.Current.FindResource("CartEmptyMessage") as string ?? "Your cart is empty.",
                    Application.Current.FindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        private async void ShowPaymentDialog()
        {
            // Create the payment dialog content
            var grid = new Grid { Margin = new Thickness(16) };
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Title
            var title = new TextBlock
            {
                Text = Application.Current.FindResource("Payment") as string ?? "Payment",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            };
            Grid.SetRow(title, 0);
            grid.Children.Add(title);

            // Payment methods
            var paymentMethodsPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            Grid.SetRow(paymentMethodsPanel, 1);

            var paymentMethodLabel = new TextBlock
            {
                Text = Application.Current.FindResource("PaymentMethod") as string ?? "Payment Method",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            paymentMethodsPanel.Children.Add(paymentMethodLabel);

            var paymentMethodsWrap = new WrapPanel();
            var selectedPaymentMethod = "Cash"; // Default

            var methods = new[] { "Cash", "Card", "Mobile", "Unpaid" };
            foreach (var method in methods)
            {
                var chip = new MaterialDesignThemes.Wpf.Chip
                {
                    Content = method,
                    Margin = new Thickness(0, 0, 8, 8)
                };

                if (method == "Cash")
                {
                    chip.Background = (System.Windows.Media.Brush)Application.Current.Resources["PrimaryHueLightBrush"];
                    chip.Foreground = System.Windows.Media.Brushes.Black;
                }

                chip.Click += (s, e) =>
                {
                    // Reset all chips
                    foreach (var c in paymentMethodsWrap.Children.OfType<MaterialDesignThemes.Wpf.Chip>())
                    {
                        c.Background = System.Windows.Media.Brushes.LightGray;
                        c.Foreground = System.Windows.Media.Brushes.Black;
                    }

                    // Highlight selected
                    chip.Background = (System.Windows.Media.Brush)Application.Current.Resources["PrimaryHueLightBrush"];
                    chip.Foreground = System.Windows.Media.Brushes.Black;

                    selectedPaymentMethod = method;
                };

                paymentMethodsWrap.Children.Add(chip);
            }

            paymentMethodsPanel.Children.Add(paymentMethodsWrap);
            grid.Children.Add(paymentMethodsPanel);

            // Amount fields
            var amountPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            Grid.SetRow(amountPanel, 2);

            var amountTenderedLabel = new TextBlock
            {
                Text = Application.Current.FindResource("AmountTendered") as string ?? "Amount Tendered",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            amountPanel.Children.Add(amountTenderedLabel);

            var txtAmountTendered = new TextBox
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                Width = 200,
                Text = ViewModel.GrandTotal.ToString("F2"),
                HorizontalAlignment = HorizontalAlignment.Left
            };
            amountPanel.Children.Add(txtAmountTendered);

            var changePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 8, 0, 0)
            };

            var changeLabel = new TextBlock
            {
                Text = (Application.Current.FindResource("Change") as string ?? "Change") + ":",
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var txtChange = new TextBlock
            {
                Text = "0.00",
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = System.Windows.Media.Brushes.Green
            };

            changePanel.Children.Add(changeLabel);
            changePanel.Children.Add(txtChange);
            amountPanel.Children.Add(changePanel);

            txtAmountTendered.TextChanged += (s, e) =>
            {
                if (decimal.TryParse(txtAmountTendered.Text, out decimal tendered))
                {
                    decimal change = tendered - ViewModel.GrandTotal;
                    txtChange.Text = change >= 0 ? change.ToString("F2") :
                        (Application.Current.FindResource("InsufficientAmount") as string ?? "Insufficient amount");
                    txtChange.Foreground = change >= 0 ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;
                }
                else
                {
                    txtChange.Text = string.Empty;
                }
            };

            grid.Children.Add(amountPanel);

            // Due date for unpaid
            var datePicker = new DatePicker
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedDatePicker"],
                Width = 200,
                SelectedDate = DateTime.Now.AddDays(30),
                Visibility = Visibility.Collapsed,
                Margin = new Thickness(0, 16, 0, 0)
            };
            Grid.SetRow(datePicker, 3);
            grid.Children.Add(datePicker);

            // Buttons
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 16, 0, 0)
            };
            Grid.SetRow(buttonsPanel, 4);

            var cancelButton = new Button
            {
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Content = Application.Current.FindResource("Cancel") as string ?? "Cancel",
                Margin = new Thickness(0, 0, 8, 0)
            };
            cancelButton.Click += (s, e) =>
            {
                _dialogService.CloseDialog();
            };

            var processButton = new Button
            {
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Content = Application.Current.FindResource("ProcessPayment") as string ?? "Process Payment"
            };
            processButton.Click += async (s, e) =>
            {
                if (selectedPaymentMethod == "Unpaid")
                {
                    if (datePicker.SelectedDate == null)
                    {
                        MessageBox.Show(
                            Application.Current.FindResource("DueDateRequired") as string ?? "Due date is required for unpaid sales.",
                            Application.Current.FindResource("InvalidInput") as string ?? "Invalid Input",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    if (await ViewModel.ProcessPayment(selectedPaymentMethod, 0, datePicker.SelectedDate))
                    {
                        _dialogService.CloseDialogWithResult(true);
                    }
                }
                else if (decimal.TryParse(txtAmountTendered.Text, out decimal amountTendered))
                {
                    if (amountTendered >= ViewModel.GrandTotal)
                    {
                        if (await ViewModel.ProcessPayment(selectedPaymentMethod, amountTendered))
                        {
                            _dialogService.CloseDialogWithResult(true);
                        }
                    }
                    else
                    {
                        MessageBox.Show(
                            Application.Current.FindResource("AmountTenderedMustBeGreater") as string ??
                                "Amount tendered must be greater than or equal to the total amount.",
                            Application.Current.FindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show(
                        Application.Current.FindResource("InvalidAmountFormat") as string ?? "Invalid amount format.",
                        Application.Current.FindResource("Error") as string ?? "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            };

            buttonsPanel.Children.Add(cancelButton);
            buttonsPanel.Children.Add(processButton);
            grid.Children.Add(buttonsPanel);

            // Show dialog using DialogService
            await _dialogService.ShowDialog(grid);
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel != null && sender is ComboBox comboBox && comboBox.SelectedItem is Models.Category category)
            {
                ViewModel.SelectedCategory = category;
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (ViewModel != null && sender is TextBox textBox)
            {
                string searchText = textBox.Text.Trim();
                ViewModel.FilterProducts(searchText);
            }
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && sender is TextBox textBox)
            {
                e.Handled = true;
                string searchText = textBox.Text?.Trim() ?? "";

                // If the search text is a barcode, add the product to the cart
                if (searchText.Length >= 8 && searchText.All(char.IsDigit))
                {
                    // Show processing feedback immediately
                    ShowBarcodeProcessingFeedback(textBox, true);

                    // Use caching and async pattern to improve performance
                    Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        try
                        {
                            // Look up product by barcode with external database support
                            var product = await ViewModel.GetProductByBarcodeWithExternalLookupAsync(searchText);

                            if (product != null)
                            {
                                // ✅ WEIGHT-BASED PRODUCT FIX: Use same logic as AddToCartCommand for consistency
                                bool success = false;
                                if (product.IsWeightBased)
                                {
                                    // For weight-based products, execute the AddToCartCommand which shows the weight dialog
                                    if (ViewModel.AddToCartCommand.CanExecute(product))
                                    {
                                        ViewModel.AddToCartCommand.Execute(product);
                                        success = true; // Assume success since dialog handles the interaction
                                    }
                                }
                                else
                                {
                                    // For unit-based products, use AddToCartAndScroll for proper UI feedback
                                    success = AddToCartAndScroll(product, 1);
                                }

                                if (success)
                                {
                                    PlayBarcodeSound(true);
                                    textBox.Clear(); // Clear the search box after successful addition
                                }
                                else
                                {
                                    PlayBarcodeSound(false);
                                }
                            }
                            else
                            {
                                PlayBarcodeSound(false);
                                MessageBox.Show("Product not found with this barcode", "Product Not Found", MessageBoxButton.OK, MessageBoxImage.Warning);
                            }
                        }
                        finally
                        {
                            // Ensure we always reset the feedback state
                            ShowBarcodeProcessingFeedback(textBox, false);
                            // Always return focus to the search box for next scan
                            textBox.Focus();
                        }
                    }, DispatcherPriority.Normal);

                    return;
                }

                // For non-barcode searches, use the normal search functionality
                if (!string.IsNullOrEmpty(searchText))
                {
                    ViewModel?.FilterProducts(searchText, true);
                }
            }
        }

        private void ShowBarcodeProcessingFeedback(TextBox textBox, bool isProcessing)
        {
            if (isProcessing)
            {
                // Change the visual state to show we're processing the barcode
                textBox.IsEnabled = false;
                textBox.Background = new SolidColorBrush(Colors.LightYellow);
            }
            else
            {
                // Return to normal state
                textBox.IsEnabled = true;
                textBox.Background = null; // Return to default background
            }
        }

        private void PlayBarcodeSound(bool isSuccess)
        {
            try
            {
                if (isSuccess)
                {
                    SystemSounds.Asterisk.Play();
                }
                else
                {
                    SystemSounds.Exclamation.Play();
                }
            }
            catch
            {
                // Silently fail if sounds can't be played
            }
        }

        private void ProductsScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            var scrollViewer = sender as ScrollViewer;
            if (scrollViewer != null &&
                ViewModel != null &&
                scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight * 0.8 &&
                !ViewModel.IsLoading)
            {
                // Load more products
                _ = ViewModel.LoadMoreProducts();
            }
        }

        internal bool AddToCartAndScroll(Product product, int quantity = 1)
        {
            // Ensure cart exists
            if (ViewModel.CurrentCart == null)
            {
                ViewModel.CreateNewCart();
            }

            if (product == null)
            {
                return false;
            }

            // Add product to cart
            bool success = ViewModel.AddToCart(product, quantity);

            if (!success)
            {
                return false;
            }

            // Use Invoke instead of BeginInvoke to ensure operation completes before further UI updates
            Application.Current.Dispatcher.Invoke(new Action(() =>
            {
                try
                {
                    // First, ensure the cart item is selected to maintain visibility in preview card
                    var addedItem = ViewModel.CurrentCart.Items.LastOrDefault(i => i.Product.Id == product.Id);
                    if (addedItem != null)
                    {
                        ViewModel.SelectedCartItem = addedItem;
                    }

                    // Schedule additional scroll attempts with with decreasing priorities
                    for (int delay = 50; delay <= 300; delay += 100)
                    {
                        var timer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(delay) };
                        timer.Tick += (s, e) =>
                        {
                            // Ensure selection is still maintained
                            if (addedItem != null)
                            {
                                ViewModel.SelectedCartItem = addedItem;
                            }
                            timer.Stop();
                        };
                        timer.Start();
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error in AddToCartAndScroll: {ex.Message}");
                }
            }), DispatcherPriority.Normal);

            return true;
        }

        private async void CreateReservationInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Prevent event bubbling to avoid conflicts
                e.Handled = true;

                if (sender is FrameworkElement element && element.Tag is Models.Product product)
                {
                    await CreateReservationInvoice_ClickInternal(product);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error in CreateReservationInvoice_Click: {ex.Message}");
                MessageBox.Show($"Error creating reservation invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateReservationInvoice_ClickInternal(Models.Product product)
        {
            try
            {
                if (product != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[RESERVATION] CreateReservationInvoice_ClickInternal called for product: {product.Name}");

                    // Get required services using established pattern
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                    if (permissionsService == null || dbService == null)
                    {
                        MessageBox.Show("Reservation services are not available. Please restart the application.",
                                      "Service Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // Create and show confirmation dialog (let dialog handle permission checks)
                    var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                    var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                    var result = await MaterialDesignThemes.Wpf.DialogHost.Show(confirmationDialog, "SalesDialog");

                    if (result is POSSystem.ViewModels.ProductToInvoiceResult invoiceResult && invoiceResult.Confirmed)
                    {
                        // Only create stock reservation, don't open draft invoice dialog
                        await CreateStockReservationOnly(invoiceResult);
                    }

                    System.Diagnostics.Debug.WriteLine($"[RESERVATION] Product reservation workflow completed for: {product.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error in CreateReservationInvoice_ClickInternal: {ex.Message}");
                MessageBox.Show($"Error creating reservation invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Creates stock reservation only without opening draft invoice dialog
        /// </summary>
        private async Task CreateStockReservationOnly(POSSystem.ViewModels.ProductToInvoiceResult invoiceResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Creating stock reservation for product: {invoiceResult.Product.Name}");

                // Use modern DI container instead of ServiceLocator
                var authService = App.ServiceProvider?.GetService(typeof(IAuthenticationService)) as AuthenticationService;
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as DatabaseService;
                var stockService = App.ServiceProvider?.GetService(typeof(POSSystem.Services.InventoryManagement.IStockService)) as POSSystem.Services.InventoryManagement.IStockService;

                if (authService == null || dbService == null || stockService == null)
                {
                    throw new InvalidOperationException("Required services are not available");
                }

                // Add reserved stock to product
                await AddReservedStockToProduct(invoiceResult.Product, invoiceResult.Quantity, stockService, dbService, authService);

                // Refresh the product display to show updated stock
                await RefreshProductDisplay(invoiceResult.Product.Id);

                // Show success message
                MessageBox.Show($"Stock reservation created successfully!\n\nProduct: {invoiceResult.Product.Name}\nQuantity: {invoiceResult.Quantity}\n\nThe product now has stock available for sale.",
                    "Stock Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Stock reservation completed for: {invoiceResult.Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error creating stock reservation: {ex.Message}");
                MessageBox.Show($"Error creating stock reservation: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateFullInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Creating full invoice for product: {result.Product.Name}");

                // Implementation would integrate with the existing full invoice creation system
                MessageBox.Show($"Full reservation invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})",
                    "Reservation Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error creating full invoice: {ex.Message}");
                throw;
            }
        }

        private async Task CreateDraftInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Creating draft reservation invoice for product: {result.Product.Name}");

                // Implementation would integrate with the existing draft invoice creation system
                MessageBox.Show($"Draft reservation invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})\n\nThis reservation will be available for admin completion.",
                    "Draft Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error creating draft invoice: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Adds reserved stock to a product and creates inventory batch if needed
        /// </summary>
        private async Task AddReservedStockToProduct(Product product, decimal quantity,
            POSSystem.Services.InventoryManagement.IStockService stockService,
            DatabaseService dbService, AuthenticationService authService)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Adding {quantity} reserved stock to product {product.Name} (ID: {product.Id})");

                var currentUser = authService.CurrentUser;
                if (currentUser == null)
                {
                    throw new InvalidOperationException("User not authenticated");
                }

                // Get the most recent batch information for batch-tracked products
                BatchStock mostRecentBatch = null;
                if (product.TrackBatches)
                {
                    var batches = dbService.GetBatchesForProduct(product.Id);
                    mostRecentBatch = batches?.OrderByDescending(b => b.CreatedAt).FirstOrDefault();
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Product tracks batches. Found {batches?.Count ?? 0} existing batches");
                }

                // Create new batch entry for the reserved stock
                if (product.TrackBatches)
                {
                    var reservationBatch = new BatchStock
                    {
                        ProductId = product.Id,
                        BatchNumber = $"RESERVE-{DateTime.Now:yyyyMMddHHmmss}",
                        Quantity = quantity,
                        ManufactureDate = DateTime.Now,
                        ExpiryDate = mostRecentBatch?.ExpiryDate, // Use expiry from most recent batch if available
                        PurchasePrice = mostRecentBatch?.PurchasePrice ?? 0m, // Use purchase price from most recent batch
                        SellingPrice = mostRecentBatch?.SellingPrice ?? product.SellingPrice,
                        Location = "Reserved Stock",
                        Notes = $"Stock reserved for out-of-stock product. Based on batch: {mostRecentBatch?.BatchNumber ?? "N/A"}",
                        CreatedAt = DateTime.Now
                    };

                    dbService.AddBatchStock(reservationBatch);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created reservation batch: {reservationBatch.BatchNumber}");
                }
                else
                {
                    // For non-batch tracked products, use the stock service to increase stock
                    stockService.IncreaseStock(product.Id, quantity, $"Stock reservation for out-of-stock product", null);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Increased stock for non-batch product by {quantity}");
                }

                // Create inventory transaction for tracking
                var transaction = new InventoryTransaction
                {
                    ProductId = product.Id,
                    TransactionType = "Stock Reservation",
                    Quantity = (int)Math.Ceiling(quantity), // Convert decimal to int, rounding up
                    TransactionDate = DateTime.Now,
                    UserId = currentUser.Id,
                    Notes = $"Stock reserved for out-of-stock product: {product.Name}. Reserved stock added via reserve invoice workflow."
                };

                dbService.AddInventoryTransaction(transaction);
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created inventory transaction for tracking");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error adding reserved stock: {ex.Message}");
                throw new Exception($"Failed to add reserved stock: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Refreshes the product display to show updated stock information
        /// </summary>
        private async Task RefreshProductDisplay(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Refreshing display for product ID: {productId}");

                // Get the updated product from database
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as DatabaseService;
                if (dbService == null) return;

                var updatedProduct = dbService.GetProductById(productId);
                if (updatedProduct == null) return;

                // Update the product in the ViewModel's collection if it exists
                var viewModel = DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    // Find and update the product in the collections
                    var productInAllProducts = viewModel.AllProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInAllProducts != null)
                    {
                        // Update stock quantity and other properties
                        productInAllProducts.StockQuantity = updatedProduct.StockQuantity;
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Updated product stock to: {updatedProduct.StockQuantity}");
                    }

                    var productInFilteredProducts = viewModel.FilteredProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInFilteredProducts != null)
                    {
                        productInFilteredProducts.StockQuantity = updatedProduct.StockQuantity;
                    }

                    // Trigger UI refresh by refreshing the view model's data
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // Call the view model's refresh method if available
                        if (viewModel.RefreshProductsCommand?.CanExecute(null) == true)
                        {
                            viewModel.RefreshProductsCommand.Execute(null);
                        }
                        else
                        {
                            // Alternative: Force a UI refresh by invalidating commands
                            CommandManager.InvalidateRequerySuggested();
                        }
                    });
                }

                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Product display refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Error refreshing product display: {ex.Message}");
            }
        }
    }
}