using System.Windows.Controls;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using POSSystem.Models;

namespace POSSystem.Views.Dialogs
{
    public partial class UnpaidSalesStatsDetailsDialog : UserControl
    {
        private readonly UnpaidSalesStatsDetailsViewModel _viewModel;

        public UnpaidSalesStatsDetailsDialog(RefactoredDashboardViewModel dashboardViewModel)
        {
            InitializeComponent();
            _viewModel = new UnpaidSalesStatsDetailsViewModel(dashboardViewModel, (DatabaseService)App.DbService);
            DataContext = _viewModel;

            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }

        public UnpaidSalesStatsDetailsDialog(RefactoredDashboardViewModel dashboardViewModel, Customer customer)
        {
            InitializeComponent();
            _viewModel = new UnpaidSalesStatsDetailsViewModel(dashboardViewModel, (DatabaseService)App.DbService, customer);
            DataContext = _viewModel;

            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }
    }
}