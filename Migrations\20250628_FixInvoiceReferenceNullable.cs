using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class FixInvoiceReferenceNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
            // First, create a backup table
            migrationBuilder.Sql(@"
                CREATE TABLE Invoice_backup AS SELECT * FROM Invoice;
            ");

            // Drop the original table
            migrationBuilder.DropTable("Invoice");

            // Recreate the table with nullable Reference field
            migrationBuilder.Sql(@"
                CREATE TABLE Invoice (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL,
                    Type TEXT NOT NULL,
                    IssueDate TEXT NOT NULL,
                    DueDate TEXT NOT NULL,
                    CustomerId INTEGER,
                    SupplierId INTEGER,
                    Subtotal DECIMAL(18,2) NOT NULL DEFAULT 0,
                    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    GrandTotal DECIMAL(18,2) NOT NULL DEFAULT 0,
                    Status TEXT NOT NULL DEFAULT 'Draft',
                    PaymentTerms TEXT,
                    Reference TEXT,
                    Notes TEXT,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
                );
            ");

            // Copy data back from backup, handling null Reference values
            migrationBuilder.Sql(@"
                INSERT INTO Invoice (Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
                                   SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
                                   Status, PaymentTerms, Reference, Notes, CreatedAt, UpdatedAt)
                SELECT Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
                       SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
                       Status, PaymentTerms, 
                       CASE WHEN Reference = '' THEN NULL ELSE Reference END,
                       Notes, CreatedAt, UpdatedAt
                FROM Invoice_backup;
            ");

            // Drop the backup table
            migrationBuilder.Sql("DROP TABLE Invoice_backup;");

            // Recreate indexes if they existed
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS IX_Invoice_CustomerId ON Invoice(CustomerId);
                CREATE INDEX IF NOT EXISTS IX_Invoice_SupplierId ON Invoice(SupplierId);
                CREATE INDEX IF NOT EXISTS IX_Invoice_InvoiceNumber ON Invoice(InvoiceNumber);
                CREATE INDEX IF NOT EXISTS IX_Invoice_Status ON Invoice(Status);
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // For rollback, recreate the table with NOT NULL Reference field
            migrationBuilder.Sql(@"
                CREATE TABLE Invoice_backup AS SELECT * FROM Invoice;
            ");

            migrationBuilder.DropTable("Invoice");

            migrationBuilder.Sql(@"
                CREATE TABLE Invoice (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL,
                    Type TEXT NOT NULL,
                    IssueDate TEXT NOT NULL,
                    DueDate TEXT NOT NULL,
                    CustomerId INTEGER,
                    SupplierId INTEGER,
                    Subtotal DECIMAL(18,2) NOT NULL DEFAULT 0,
                    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    GrandTotal DECIMAL(18,2) NOT NULL DEFAULT 0,
                    Status TEXT NOT NULL DEFAULT 'Draft',
                    PaymentTerms TEXT,
                    Reference TEXT NOT NULL DEFAULT '',
                    Notes TEXT,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
                );
            ");

            migrationBuilder.Sql(@"
                INSERT INTO Invoice (Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
                                   SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
                                   Status, PaymentTerms, Reference, Notes, CreatedAt, UpdatedAt)
                SELECT Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
                       SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
                       Status, PaymentTerms, 
                       COALESCE(Reference, ''),
                       Notes, CreatedAt, UpdatedAt
                FROM Invoice_backup;
            ");

            migrationBuilder.Sql("DROP TABLE Invoice_backup;");
        }
    }
}
