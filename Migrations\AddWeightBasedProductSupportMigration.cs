using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace POSSystem.Migrations
{
    /// <summary>
    /// Migration to add weight-based product support
    /// Adds IsWeightBased column to Products table and updates SaleItems.Quantity to decimal
    /// </summary>
    public partial class AddWeightBasedProductSupport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add IsWeightBased column to Products table
            migrationBuilder.AddColumn<bool>(
                name: "IsWeightBased",
                table: "Products",
                type: "bit",
                nullable: false,
                defaultValue: false);

            // Update SaleItems.Quantity column to support decimal values
            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "SaleItems",
                type: "decimal(18,3)",
                precision: 18,
                scale: 3,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            // Create index for weight-based product queries
            migrationBuilder.CreateIndex(
                name: "IX_Products_IsWeightBased",
                table: "Products",
                column: "IsWeightBased");

            // Set all existing products to unit-based by default
            migrationBuilder.Sql("UPDATE Products SET IsWeightBased = 0 WHERE IsWeightBased IS NULL");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove the index
            migrationBuilder.DropIndex(
                name: "IX_Products_IsWeightBased",
                table: "Products");

            // Remove IsWeightBased column from Products table
            migrationBuilder.DropColumn(
                name: "IsWeightBased",
                table: "Products");

            // Revert SaleItems.Quantity column back to int
            // Note: This will truncate any decimal values to integers
            migrationBuilder.AlterColumn<int>(
                name: "Quantity",
                table: "SaleItems",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,3)",
                oldPrecision: 18,
                oldScale: 3);
        }
    }

    /// <summary>
    /// Helper class for manual migration execution
    /// Note: This class is for reference only. Use the SQL scripts directly for migration.
    /// </summary>
    public static class WeightBasedProductMigrationHelper
    {
        /// <summary>
        /// Instructions for manual migration execution
        /// </summary>
        public static void GetMigrationInstructions()
        {
            Console.WriteLine("To apply the weight-based product migration:");
            Console.WriteLine("1. Open SQL Server Management Studio");
            Console.WriteLine("2. Connect to your POS database");
            Console.WriteLine("3. Execute the script: Migrations/AddWeightBasedProductSupport.sql");
            Console.WriteLine("4. Verify the migration with: Migrations/PostMigrationVerification.sql");
        }
    }
}
