using BCrypt.Net;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class PasswordService : IPasswordService
    {
        private const int WORK_FACTOR = 12; // Adjustable, higher = more secure but slower

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, WORK_FACTOR);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false; // Hash format exception or other issues
            }
        }
    }
} 