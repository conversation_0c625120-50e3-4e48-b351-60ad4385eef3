# 7-Segment Font Instructions

## Quick Setup Guide

1. Download a 7-segment font:
   - DSEG Fonts (recommended): https://www.keshikan.net/fonts-e.html
   - Digital-7: https://www.dafont.com/digital-7.font

2. Install the font on your system:
   - Right-click the TTF file and select "Install"
   - OR copy the TTF file to C:\Windows\Fonts

3. Restart the application

## For Developers
To embed the font in the application:

1. Save the TTF file in this folder (POSSystem/Fonts/)
2. Add this to App.xaml in the ResourceDictionary section:

```xml
<!-- Font Resources -->
<FontFamily x:Key="SevenSegmentFont">pack://application:,,,/Fonts/#DSEG7 Modern</FontFamily>
```

3. Make sure to update the project file (POSSystem.csproj) to include the font:

```xml
<ItemGroup>
  <Resource Include="Fonts\*.ttf" />
</ItemGroup>
```

4. Update the TextBlock in SalesView.xaml to use the font:

```xml
FontFamily="{StaticResource SevenSegmentFont}"
``` 