<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.CartSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{DynamicResource SelectCartToRecall}" 
        Width="400" 
        Height="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <StackPanel Margin="20">
        <!-- Title -->
        <TextBlock Text="{DynamicResource SelectCartToRecall}"
                 FontSize="18"
                 FontWeight="Bold"
                 Margin="0,0,0,20"/>

        <!-- Cart list -->
        <ListView x:Name="CartList"
                 Margin="0,0,0,20"
                 MaxHeight="350"
                 ItemsSource="{Binding HeldCarts}"
                 SelectionChanged="CartList_SelectionChanged">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <TextBlock>
                        <Run Text="{Binding Name, StringFormat='Cart {0}'}"/>
                        <Run Text=" - "/>
                        <Run Text="{Binding Items.Count}"/>
                        <Run Text=" items - "/>
                        <Run Text="{Binding GrandTotal, StringFormat=C2}"/>
                    </TextBlock>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Buttons -->
        <StackPanel Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Content="{DynamicResource RecallCart}"
                    Click="RecallCart_Click"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Margin="0,0,10,0"
                    Padding="20,10"
                    IsDefault="True"/>
            <Button Content="{DynamicResource Cancel}"
                    Click="Cancel_Click"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Padding="20,10"
                    IsCancel="True"/>
        </StackPanel>
    </StackPanel>
</Window> 