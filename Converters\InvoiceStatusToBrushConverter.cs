using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace POSSystem.Converters
{
    public class InvoiceStatusToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status.ToLower() switch
                {
                    "draft" => new SolidColorBrush(Color.FromRgb(158, 158, 158)),     // Gray
                    "issued" => new SolidColorBrush(Color.FromRgb(33, 150, 243)),     // Blue
                    "paid" => new SolidColorBrush(Color.FromRgb(76, 175, 80)),        // Green  
                    "overdue" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),     // Red
                    "cancelled" => new SolidColorBrush(Color.FromRgb(97, 97, 97)),    // Dark Gray
                    _ => new SolidColorBrush(Colors.Black)
                };
            }
            
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 