﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;

namespace POSSystem.Models
{
    /// <summary>
    /// Defines the type of item in the POS system
    /// </summary>
    public enum ProductType
    {
        /// <summary>
        /// Physical product with inventory tracking, weight/volume measurements
        /// </summary>
        Product = 0,

        /// <summary>
        /// Service-based item without physical inventory, typically time or labor-based
        /// </summary>
        Service = 1
    }

    /// <summary>
    /// Represents a product in the POS system with comprehensive inventory management, pricing, and sales tracking capabilities.
    /// </summary>
    /// <remarks>
    /// <para>The Product entity is central to the POS system and includes:</para>
    /// <list type="bullet">
    /// <item><description>Basic Information: Name, SKU, description, barcode</description></item>
    /// <item><description>Pricing: Purchase price, selling price, price history tracking</description></item>
    /// <item><description>Inventory Management: Stock quantities, reorder points, batch tracking</description></item>
    /// <item><description>Sales Analytics: Sales history, profit calculations, performance metrics</description></item>
    /// <item><description>User Features: Favorites, loyalty points, selection state</description></item>
    /// <item><description>Categorization: Category assignment for organization and reporting</description></item>
    /// </list>
    /// <para>The class supports advanced features like batch tracking for perishable items,
    /// multiple barcode support, and comprehensive audit trails.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Create a new product
    /// var product = new Product
    /// {
    ///     Name = "Wireless Mouse",
    ///     SKU = "MOUSE001",
    ///     Barcode = "1234567890123",
    ///     CategoryId = 1,
    ///     PurchasePrice = 15.00m,
    ///     SellingPrice = 29.99m,
    ///     StockQuantity = 50,
    ///     ReorderPoint = 10
    /// };
    ///
    /// // Check if product needs reordering
    /// if (product.IsLowStock)
    /// {
    ///     // Trigger reorder process
    /// }
    /// </code>
    /// </example>
    public class Product
    {
        public Product()
        {
            Name = string.Empty;
            SKU = string.Empty;
            Description = string.Empty;
            PurchasePrice = 0;
            SellingPrice = 0;
            DefaultPrice = 0;
            Type = ProductType.Product;
            Barcode = string.Empty;
            StockQuantity = 0;
            MinimumStock = 0;
            ReorderPoint = 0;
            IsActive = true;
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
            Sales = new List<SaleItem>();
            InventoryTransactions = new List<InventoryTransaction>();
            PriceHistory = new List<ProductPrice>();
            Barcodes = new List<ProductBarcode>();
            TrackBatches = false;
            Batches = new List<BatchStock>();
            FavoritedBy = new List<UserFavorite>();
            LoyaltyPoints = 0;
            IsFavorited = false;
            IsSelected = false;
        }

        public int Id { get; set; }
        
        [Required]
        public string Name { get; set; }
        
        public string? SKU { get; set; }
        
        public string? Description { get; set; }
        
        [Required]
        public decimal PurchasePrice { get; set; }
        
        [Required]
        public decimal SellingPrice { get; set; }
        
        public decimal DefaultPrice { get; set; }

        /// <summary>
        /// Defines whether this item is a physical product or a service
        /// </summary>
        public ProductType Type { get; set; } = ProductType.Product;

        /// <summary>
        /// Indicates whether this product is sold by weight (true) or by units (false)
        /// Weight-based products allow decimal quantities (e.g., 2.5 kg, 1.75 lbs)
        /// Unit-based products use whole number quantities (e.g., 1 piece, 5 items)
        /// </summary>
        public bool IsWeightBased { get; set; } = false;

        // Primary barcode for the product
        public string? Barcode { get; set; }
        
        private decimal _stockQuantity;

        /// <summary>
        /// Unified decimal stock quantity for all products
        /// Supports both weight-based (decimal) and unit-based (whole number) products
        /// </summary>
        public decimal StockQuantity
        {
            get
            {
                // ✅ BATCH TRACKING FIX: For batch-tracked products, return total from batches
                if (TrackBatches && Batches != null && Batches.Any())
                {
                    try
                    {
                        var totalBatchStock = Batches.Sum(b => b.Quantity);
                        // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                        #if DEBUG && VERBOSE_LOGGING
                        System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-UI] Product {Id} ({Name}): Calculated batch stock = {totalBatchStock} from {Batches.Count} batches");
                        #endif
                        return totalBatchStock;
                    }
                    catch (Exception ex)
                    {
                        // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                        #if DEBUG && VERBOSE_LOGGING
                        System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-UI] Product {Id}: Error calculating batch stock: {ex.Message}");
                        #endif
                        return _stockQuantity; // Fallback on error
                    }
                }

                // For regular products or when batches aren't loaded, return _stockQuantity directly
                return _stockQuantity;
            }
            set
            {
                _stockQuantity = value;
            }
        }
        public int MinimumStock { get; set; }
        public int ReorderPoint { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        public int CategoryId { get; set; }
        public int? SupplierId { get; set; }
        public int? UnitOfMeasureId { get; set; }
        
        [Required]
        public virtual Category? Category { get; set; }
        public virtual Supplier? Supplier { get; set; }
        public virtual UnitOfMeasure? UnitOfMeasure { get; set; }

        public ICollection<SaleItem> Sales { get; set; }
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; }
        public ICollection<ProductPrice> PriceHistory { get; set; }

        // Add the collection of barcodes
        public virtual ICollection<ProductBarcode> Barcodes { get; set; }

        // Add the collection of pricing tiers for bulk/pack pricing
        public virtual ICollection<ProductPriceTier> PriceTiers { get; set; } = new List<ProductPriceTier>();

        // Add computed properties for stock status (services don't track stock)
        // ✅ CRITICAL FIX: Use StockQuantity directly since it's already calculated correctly during loading
        public bool IsLowStock
        {
            get
            {
                if (Type == ProductType.Service) return false;

                // Use StockQuantity directly to avoid additional database queries
                var totalStock = StockQuantity;
                var isLow = totalStock <= MinimumStock && totalStock > 0;

                // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[STOCK_STATUS] Product {Id} ({Name}): IsLowStock = {isLow} (Stock: {totalStock}, Min: {MinimumStock})");
                #endif
                return isLow;
            }
        }

        public bool IsOutOfStock
        {
            get
            {
                if (Type == ProductType.Service) return false;

                // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                var totalStock = StockQuantity;
                var isOut = totalStock <= 0;

                // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[STOCK_STATUS] Product {Id} ({Name}): IsOutOfStock = {isOut} (Stock: {totalStock})");
                #endif
                return isOut;
            }
        }

        public bool IsInStock
        {
            get
            {
                if (Type == ProductType.Service) return true;

                // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                var totalStock = StockQuantity;
                var isIn = totalStock > 0;

                // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[STOCK_STATUS] Product {Id} ({Name}): IsInStock = {isIn} (Stock: {totalStock})");
                #endif
                return isIn;
            }
        }
        public bool IsNearExpiry => HasExpiry && DaysUntilExpiry <= 30 && DaysUntilExpiry > 0;
        public string StockStatus
        {
            get
            {
                // Services don't have stock status
                if (Type == ProductType.Service)
                    return Application.Current.TryFindResource("Service") as string ?? "Service";

                if (IsOutOfStock) return Application.Current.TryFindResource("OutOfStock") as string ?? "Out of Stock";
                if (IsLowStock) return Application.Current.TryFindResource("LowStock") as string ?? "Low Stock";

                // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                var totalStock = StockQuantity;
                var stockDisplay = totalStock % 1 == 0 ? totalStock.ToString("F0") : totalStock.ToString("N3");

                // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[STOCK_STATUS] Product {Id} ({Name}): StockStatus = 'In Stock: {stockDisplay}' (Stock: {totalStock})");
                #endif
                return $"{Application.Current.TryFindResource("InStock") as string ?? "In Stock"}: {stockDisplay}";
            }
        }

        public DateTime? ExpiryDate { get; set; }
        public bool HasExpiry => ExpiryDate.HasValue;
        public bool IsExpired => HasExpiry && ExpiryDate < DateTime.Now;
        public int? DaysUntilExpiry => HasExpiry ? 
            (int?)Math.Ceiling((ExpiryDate.Value - DateTime.Now).TotalDays) : null;

        // Add image data field
        public string? ImageData { get; set; }

        // Helper properties for UI binding
        public string? ImagePath => ImageData;
        public string? Unit => UnitOfMeasure?.Name;

        // Helper methods for unit conversion
        public decimal GetStockInBaseUnit()
        {
            return UnitOfMeasure?.ConvertToBase(StockQuantity) ?? StockQuantity;
        }

        public void SetStockFromBaseUnit(decimal baseQuantity)
        {
            StockQuantity = UnitOfMeasure?.ConvertFromBase(baseQuantity) ?? baseQuantity;
        }

        public bool TrackBatches { get; set; }
        public virtual ICollection<BatchStock> Batches { get; set; }
        public virtual ICollection<UserFavorite> FavoritedBy { get; set; } = new List<UserFavorite>();
        
        /// <summary>
        /// Gets total stock as decimal for all products (unified decimal stock system)
        /// Supports both weight-based (decimal) and unit-based (whole number) products
        /// </summary>
        public decimal GetTotalStockDecimal()
        {
            // For custom products (with negative IDs), directly return stock
            if (Id < 0)
            {
                return _stockQuantity;
            }

            // For batch-tracked products
            if (TrackBatches)
            {
                // For batch-tracked products, return the sum of batch quantities
                if (Batches != null && Batches.Any())
                {
                    try
                    {
                        var totalBatchStock = Batches.Sum(b => b.Quantity);

                        // ✅ FIX: If batch total is 0 but _stockQuantity is positive, prefer _stockQuantity
                        // This handles cases where batches might not be properly synchronized
                        if (totalBatchStock == 0 && _stockQuantity > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-FALLBACK] Product {Id}: Batch total is 0 but StockQuantity is {_stockQuantity}, using StockQuantity");
                            return _stockQuantity;
                        }

                        return totalBatchStock;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK-ERROR] Product {Id}: Error calculating batch stock: {ex.Message}");
                        return _stockQuantity; // Fallback on error
                    }
                }

                // For batch-tracked products, try to load actual batch stock from database
                if (Id > 0)
                {
                    try
                    {
                        // Quick database query to get actual batch total
                        // Use LINQ to Objects to avoid SQLite Sum aggregate issues with decimal
                        using (var context = new POSDbContext())
                        {
                            var batches = context.BatchStock
                                .Where(b => b.ProductId == Id)
                                .ToList(); // Execute query first to get data into memory

                            var batchTotal = batches.Sum(b => b.Quantity);

                            // ✅ FIX: If database batch total is 0 but _stockQuantity is positive, prefer _stockQuantity
                            if (batchTotal == 0 && _stockQuantity > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-DB-FALLBACK] Product {Id}: DB batch total is 0 but StockQuantity is {_stockQuantity}, using StockQuantity");
                                return _stockQuantity;
                            }

                            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                            #if DEBUG && VERBOSE_LOGGING
                            System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-QUERY] Product {Id}: Found actual batch stock = {batchTotal} from {batches.Count} batches");
                            #endif
                            return batchTotal;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK-BATCH-ERROR] Product {Id}: Error querying batch stock: {ex.Message}");
                        // Fall back to _stockQuantity if there's an error
                    }
                }

                // Fallback to stock quantity
                return _stockQuantity;
            }

            // For regular products without batch tracking, return stock
            return _stockQuantity;
        }

        /// <summary>
        /// Gets total stock as integer (for backward compatibility)
        /// Internally uses the unified decimal stock system
        /// </summary>
        public int GetTotalStock()
        {
            // Convert decimal stock to integer for backward compatibility
            return (int)Math.Floor(GetTotalStockDecimal());
        }

        /// <summary>
        /// Asynchronously gets the total stock as integer (for backward compatibility)
        /// Internally uses the unified decimal stock system
        /// </summary>
        public async Task<int> GetTotalStockAsync()
        {
            // Convert decimal stock to integer for backward compatibility
            var decimalStock = await GetTotalStockDecimalAsync();
            return (int)Math.Floor(decimalStock);
        }

        /// <summary>
        /// Asynchronously gets the total stock as decimal including batch quantities
        /// Use this method when you need accurate batch stock and can handle async operations
        /// </summary>
        public async Task<decimal> GetTotalStockDecimalAsync()
        {
            // For custom products (with negative IDs), directly return stock
            if (Id < 0)
            {
                return _stockQuantity;
            }

            // For regular products with batch tracking
            if (TrackBatches)
            {
                // For batch-tracked products, return the sum of batch quantities if already loaded
                if (Batches != null && Batches.Any())
                {
                    try
                    {
                        decimal totalBatchStock = Batches.Sum(b => b.Quantity);
                        System.Diagnostics.Debug.WriteLine($"[STOCK-ASYNC-FAST] Product {Id}: Batch stock = {totalBatchStock}");
                        return totalBatchStock;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK-ASYNC-ERROR] Product {Id}: Error calculating batch stock: {ex.Message}");
                        return _stockQuantity; // Fallback on error
                    }
                }

                // If no batches are loaded, load them asynchronously
                try
                {
                    using (var context = new POSDbContext())
                    {
                        var batchTotal = await context.BatchStock
                            .Where(b => b.ProductId == Id)
                            .SumAsync(b => (decimal?)b.Quantity) ?? 0m;

                        System.Diagnostics.Debug.WriteLine($"[STOCK-ASYNC-QUERY] Product {Id}: Found batch stock = {batchTotal}");
                        return batchTotal;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK-ASYNC-ERROR] Product {Id}: Error loading batches: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine($"[STOCK-ASYNC-FALLBACK] Product {Id}: No batches found, returning stock = {_stockQuantity}");
                return _stockQuantity;
            }

            // For regular products without batch tracking
            return _stockQuantity;
        }

        // Property to display decimal stock in UI
        // ✅ FIX: Use StockQuantity directly to avoid additional database queries
        [NotMapped]
        public decimal TotalStockQuantity => StockQuantity;

        // Property to track batch count
        [NotMapped]
        public int BatchCount => Batches?.Count ?? 0;

        // Property to get just the batch stock without general stock
        [NotMapped]
        public decimal BatchStockQuantity => Batches?.Sum(b => b.Quantity) ?? 0m;
        
        public IEnumerable<BatchStock> GetNearExpiryBatches(int daysThreshold = 30)
        {
            if (!TrackBatches) return Enumerable.Empty<BatchStock>();
            var thresholdDate = DateTime.Now.AddDays(daysThreshold);
            return Batches.Where(b => b.ExpiryDate.HasValue && 
                                     b.ExpiryDate.Value <= thresholdDate &&
                                     b.ExpiryDate.Value > DateTime.Now);
        }

        public decimal GetTotalValue()
        {
            // ✅ FIX: Use StockQuantity directly to avoid additional database queries
            return StockQuantity * PurchasePrice;
        }

        public decimal GetTotalRetailValue()
        {
            // ✅ FIX: Use StockQuantity directly to avoid additional database queries
            return StockQuantity * SellingPrice;
        }

        public decimal LoyaltyPoints { get; set; } // Points earned when this product is purchased

        [NotMapped]
        public bool IsFavorited { get; set; }

        [NotMapped]
        public bool IsSelected { get; set; }

        /// <summary>
        /// Helper method to format stock quantities consistently throughout the application
        /// Shows whole numbers for unit-based products and up to 3 decimal places for weight-based products
        /// </summary>
        public static string FormatStockQuantity(decimal quantity)
        {
            return quantity % 1 == 0 ? quantity.ToString("F0") : quantity.ToString("N3");
        }

        /// <summary>
        /// Gets formatted stock display for this product
        /// ✅ FIX: Use StockQuantity directly since it's already calculated correctly during loading
        /// </summary>
        [NotMapped]
        public string FormattedStock => FormatStockQuantity(StockQuantity);

        // ===== BULK PRICING METHODS =====

        /// <summary>
        /// Gets all active pricing tiers for this product, ordered by minimum quantity
        /// </summary>
        /// <returns>Active pricing tiers ordered by minimum quantity</returns>
        public IEnumerable<ProductPriceTier> GetActivePriceTiers()
        {
            return PriceTiers?
                .Where(pt => pt.IsCurrentlyValid)
                .OrderBy(pt => pt.MinimumQuantity)
                .ThenBy(pt => pt.DisplayOrder) ?? Enumerable.Empty<ProductPriceTier>();
        }

        /// <summary>
        /// Gets the best pricing tier for a given quantity
        /// </summary>
        /// <param name="quantity">Quantity to find pricing for</param>
        /// <returns>Best pricing tier or null if no tier applies</returns>
        public ProductPriceTier GetBestPriceTierForQuantity(decimal quantity)
        {
            return GetActivePriceTiers()
                .Where(pt => pt.QualifiesForTier(quantity))
                .OrderByDescending(pt => pt.MinimumQuantity) // Get the highest qualifying tier
                .FirstOrDefault();
        }

        /// <summary>
        /// Calculates the best price for a given quantity considering all pricing tiers
        /// </summary>
        /// <param name="quantity">Quantity to calculate price for</param>
        /// <returns>Best total price for the quantity</returns>
        public decimal CalculateBestPriceForQuantity(decimal quantity)
        {
            var bestTier = GetBestPriceTierForQuantity(quantity);
            if (bestTier != null)
            {
                return bestTier.CalculatePriceForQuantity(quantity);
            }

            // Fall back to regular selling price
            return quantity * SellingPrice;
        }

        /// <summary>
        /// Gets the effective unit price for a given quantity (considering bulk pricing)
        /// </summary>
        /// <param name="quantity">Quantity to calculate unit price for</param>
        /// <returns>Effective unit price</returns>
        public decimal GetEffectiveUnitPrice(decimal quantity)
        {
            var bestTier = GetBestPriceTierForQuantity(quantity);
            return bestTier?.EffectiveUnitPrice ?? SellingPrice;
        }

        /// <summary>
        /// Checks if this product has any active pricing tiers
        /// </summary>
        [NotMapped]
        public bool HasBulkPricing => GetActivePriceTiers().Any();

        /// <summary>
        /// Gets the next pricing tier that would apply with a higher quantity
        /// </summary>
        /// <param name="currentQuantity">Current quantity</param>
        /// <returns>Next pricing tier or null if none available</returns>
        public ProductPriceTier GetNextPriceTier(decimal currentQuantity)
        {
            return GetActivePriceTiers()
                .Where(pt => pt.MinimumQuantity > currentQuantity)
                .OrderBy(pt => pt.MinimumQuantity)
                .FirstOrDefault();
        }

        /// <summary>
        /// Gets or sets the current batch price for display purposes using FIFO logic.
        /// For batch-tracked products, this should be set to the selling price of the oldest batch with quantity > 0.
        /// For non-batch products, this should be set to the regular selling price.
        /// </summary>
        [NotMapped]
        public decimal CurrentBatchPrice { get; set; }

        /// <summary>
        /// Gets or sets the current batch purchase price for cost calculations using FIFO logic.
        /// For batch-tracked products, this should be set to the purchase price of the oldest batch with quantity > 0.
        /// For non-batch products, this should be set to the regular purchase price.
        /// </summary>
        [NotMapped]
        public decimal CurrentBatchPurchasePrice { get; set; }

        /// <summary>
        /// Calculates and sets the current batch prices using FIFO logic
        /// </summary>
        public void CalculateCurrentBatchPrices()
        {
            if (TrackBatches && Batches != null && Batches.Any())
            {
                // Find the oldest batch with available quantity (FIFO)
                var oldestBatch = Batches
                    .Where(b => b.Quantity > 0)
                    .OrderBy(b => b.CreatedAt)
                    .ThenBy(b => b.Id)
                    .FirstOrDefault();

                if (oldestBatch != null)
                {
                    CurrentBatchPrice = oldestBatch.SellingPrice;
                    CurrentBatchPurchasePrice = oldestBatch.PurchasePrice;
                }
                else
                {
                    // No batches with stock, fall back to product prices
                    CurrentBatchPrice = SellingPrice;
                    CurrentBatchPurchasePrice = PurchasePrice;
                }
            }
            else
            {
                // Non-batch tracked products use regular prices
                CurrentBatchPrice = SellingPrice;
                CurrentBatchPurchasePrice = PurchasePrice;
            }
        }

        /// <summary>
        /// Gets the weighted average cost of all available batches
        /// </summary>
        /// <returns>Weighted average cost based on batch quantities and costs</returns>
        public decimal GetWeightedAverageCost()
        {
            if (!TrackBatches || Batches == null || !Batches.Any(b => b.Quantity > 0))
            {
                return PurchasePrice;
            }

            var availableBatches = Batches.Where(b => b.Quantity > 0).ToList();
            if (!availableBatches.Any())
            {
                return PurchasePrice;
            }

            decimal totalValue = availableBatches.Sum(b => b.Quantity * b.PurchasePrice);
            decimal totalQuantity = availableBatches.Sum(b => b.Quantity);

            return totalQuantity > 0 ? totalValue / totalQuantity : PurchasePrice;
        }

        /// <summary>
        /// Gets the total inventory value using actual batch prices
        /// </summary>
        /// <returns>Total inventory value based on batch selling prices</returns>
        public decimal GetTotalInventoryValue()
        {
            if (TrackBatches && Batches != null && Batches.Any())
            {
                return Batches.Where(b => b.Quantity > 0).Sum(b => b.Quantity * b.SellingPrice);
            }
            return StockQuantity * SellingPrice;
        }

        /// <summary>
        /// Gets the total inventory cost using actual batch costs
        /// </summary>
        /// <returns>Total inventory cost based on batch purchase prices</returns>
        public decimal GetTotalInventoryCost()
        {
            if (TrackBatches && Batches != null && Batches.Any())
            {
                return Batches.Where(b => b.Quantity > 0).Sum(b => b.Quantity * b.PurchasePrice);
            }
            return StockQuantity * PurchasePrice;
        }

        /// <summary>
        /// Calculates the FIFO cost basis for a given quantity of this product
        /// </summary>
        /// <param name="quantity">Quantity to calculate cost for</param>
        /// <returns>Weighted average cost based on FIFO allocation</returns>
        public decimal CalculateFIFOCostBasis(decimal quantity)
        {
            if (!TrackBatches || Batches == null || !Batches.Any())
            {
                // Non-batch tracked products use the current purchase price
                return PurchasePrice;
            }

            // Get available batches in FIFO order (oldest first)
            var availableBatches = Batches
                .Where(b => b.Quantity > 0)
                .OrderBy(b => b.CreatedAt)
                .ThenBy(b => b.Id)
                .ToList();

            if (!availableBatches.Any())
            {
                return PurchasePrice; // Fallback to product price if no batches available
            }

            decimal remainingQuantity = quantity;
            decimal totalCost = 0;
            decimal totalQuantityUsed = 0;

            foreach (var batch in availableBatches)
            {
                if (remainingQuantity <= 0) break;

                decimal quantityFromThisBatch = Math.Min(remainingQuantity, batch.Quantity);
                totalCost += quantityFromThisBatch * batch.PurchasePrice;
                totalQuantityUsed += quantityFromThisBatch;
                remainingQuantity -= quantityFromThisBatch;
            }

            // Return weighted average cost
            return totalQuantityUsed > 0 ? totalCost / totalQuantityUsed : PurchasePrice;
        }

        /// <summary>
        /// Gets detailed FIFO cost allocation information for a given quantity
        /// </summary>
        /// <param name="quantity">Quantity to allocate</param>
        /// <returns>List of batch allocations with costs</returns>
        public List<(int batchId, string batchNumber, decimal quantityUsed, decimal costPerUnit, decimal totalCost)> GetFIFOAllocation(decimal quantity)
        {
            var allocations = new List<(int batchId, string batchNumber, decimal quantityUsed, decimal costPerUnit, decimal totalCost)>();

            if (!TrackBatches || Batches == null || !Batches.Any())
            {
                // Non-batch tracked products
                allocations.Add((0, "N/A", quantity, PurchasePrice, quantity * PurchasePrice));
                return allocations;
            }

            // Get available batches in FIFO order
            var availableBatches = Batches
                .Where(b => b.Quantity > 0)
                .OrderBy(b => b.CreatedAt)
                .ThenBy(b => b.Id)
                .ToList();

            decimal remainingQuantity = quantity;

            foreach (var batch in availableBatches)
            {
                if (remainingQuantity <= 0) break;

                decimal quantityFromThisBatch = Math.Min(remainingQuantity, batch.Quantity);
                decimal totalCostFromBatch = quantityFromThisBatch * batch.PurchasePrice;

                allocations.Add((batch.Id, batch.BatchNumber, quantityFromThisBatch, batch.PurchasePrice, totalCostFromBatch));
                remainingQuantity -= quantityFromThisBatch;
            }

            return allocations;
        }

        /// <summary>
        /// Calculates potential savings if customer increases quantity to next tier
        /// </summary>
        /// <param name="currentQuantity">Current quantity</param>
        /// <returns>Savings information or null if no better tier available</returns>
        public (decimal suggestedQuantity, decimal totalSavings, decimal newUnitPrice)? GetNextTierSavings(decimal currentQuantity)
        {
            var nextTier = GetNextPriceTier(currentQuantity);
            if (nextTier == null) return null;

            var currentPrice = CalculateBestPriceForQuantity(currentQuantity);
            var newPrice = nextTier.CalculatePriceForQuantity(nextTier.MinimumQuantity);
            var currentUnitPrice = GetEffectiveUnitPrice(currentQuantity);

            // Calculate savings per unit when moving to next tier
            var savingsPerUnit = currentUnitPrice - nextTier.EffectiveUnitPrice;
            var totalSavings = savingsPerUnit * nextTier.MinimumQuantity;

            return (nextTier.MinimumQuantity, totalSavings, nextTier.EffectiveUnitPrice);
        }
    }
}
