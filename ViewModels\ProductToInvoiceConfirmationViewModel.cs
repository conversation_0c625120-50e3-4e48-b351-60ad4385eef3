using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Helpers;
using CommunityToolkit.Mvvm.Input;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ViewModel for the product-to-invoice confirmation dialog
    /// </summary>
    public class ProductToInvoiceConfirmationViewModel : INotifyPropertyChanged
    {
        private readonly UserPermissionsService _permissionsService;
        private readonly DatabaseService _dbService;

        // Private fields
        private Product _product;
        private decimal _quantity = 1;
        private Customer _selectedCustomer;

        public ProductToInvoiceConfirmationViewModel(
            Product product,
            UserPermissionsService permissionsService,
            DatabaseService dbService)
        {
            _product = product ?? throw new ArgumentNullException(nameof(product));
            _permissionsService = permissionsService ?? throw new ArgumentNullException(nameof(permissionsService));
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));

            // Initialize collections
            AvailableCustomers = new ObservableCollection<Customer>();

            // Initialize commands
            CreateInvoiceCommand = new RelayCommand(_ => CreateInvoice(), _ => CanCreateInvoice());
            CancelCommand = new RelayCommand(_ => Cancel());

            // Load data
            LoadCustomers();
            
            // Initialize permission-based properties
            InitializePermissions();
        }

        #region Properties

        public Product Product
        {
            get => _product;
            set
            {
                _product = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(EstimatedTotal));
                OnPropertyChanged(nameof(ProductName));
                OnPropertyChanged(nameof(ProductPrice));
                OnPropertyChanged(nameof(ProductCategory));
                OnPropertyChanged(nameof(ProductImagePath));
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                _quantity = Math.Max(0.001m, value); // Ensure positive quantity
                OnPropertyChanged();
                OnPropertyChanged(nameof(EstimatedTotal));
                OnPropertyChanged(nameof(QuantityDisplay));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                _selectedCustomer = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CustomerDisplay));
            }
        }

        public ObservableCollection<Customer> AvailableCustomers { get; }

        // Computed properties
        public string ProductName => Product?.Name ?? (Application.Current.TryFindResource("UnknownProduct") as string ?? "Unknown Product");

        public string ProductPrice => Product?.SellingPrice.ToString("C") ?? "$0.00";

        public string ProductCategory => Product?.Category?.Name ?? (Application.Current.TryFindResource("NoCategory") as string ?? "No Category");
        
        public string ProductImagePath => Product?.ImagePath ?? "/Images/default-product.png";
        
        public decimal EstimatedTotal => Product != null ? Product.SellingPrice * Quantity : 0;
        
        public string EstimatedTotalDisplay => EstimatedTotal.ToString("C");
        
        public string QuantityDisplay => Product?.IsWeightBased == true 
            ? $"{Quantity:0.###} {Product.Unit ?? "kg"}" 
            : $"{Quantity:0.##}";

        public bool CanSelectCustomer => _permissionsService.CanSelectCustomersForInvoices();
        
        public string CustomerDisplay => SelectedCustomer != null
            ? $"{SelectedCustomer.FirstName} {SelectedCustomer.LastName}"
            : (Application.Current.TryFindResource("NoCustomerSelected") as string ?? "No customer selected");

        public string PermissionMessage => _permissionsService.CanCreateFullInvoices()
            ? (Application.Current.TryFindResource("FullInvoicePermissionMessage") as string ?? "You can create a complete invoice with full control over pricing and payment terms.")
            : (Application.Current.TryFindResource("StockReservationPermissionMessage") as string ?? "This will create a stock reservation that adds inventory and requires admin completion. The reserved stock will be immediately available for sales, and an administrator will complete the final invoice processing.");

        public string CreateButtonText => _permissionsService.CanCreateFullInvoices()
            ? (Application.Current.TryFindResource("CreateInvoice") as string ?? "Create Invoice")
            : (Application.Current.TryFindResource("CreateStockReservation") as string ?? "Create Stock Reservation");

        public string DialogTitle => _permissionsService.CanCreateFullInvoices()
            ? (Application.Current.TryFindResource("CreateInvoice") as string ?? "Create Invoice")
            : (Application.Current.TryFindResource("CreateStockReservation") as string ?? "Create Stock Reservation");

        #endregion

        #region Commands

        public ICommand CreateInvoiceCommand { get; }
        public ICommand CancelCommand { get; }

        #endregion

        #region Command Implementations

        private bool CanCreateInvoice()
        {
            return Product != null && Quantity > 0;
        }

        private void CreateInvoice()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_VM] CreateInvoice command executed");

                // Create result object
                var result = new ProductToInvoiceResult
                {
                    Confirmed = true,
                    Product = Product,
                    Quantity = Quantity,
                    Customer = SelectedCustomer,
                    EstimatedTotal = EstimatedTotal,
                    CreateFullInvoice = _permissionsService.CanCreateFullInvoices()
                };

                System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_VM] Dialog result created, calling RequestClose");

                // Set dialog result and close
                DialogResult = result;
                RequestClose?.Invoke();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_VM] Error creating invoice: {ex.Message}");
                // In a real application, you might want to show an error message
            }
        }

        private void Cancel()
        {
            System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_VM] Cancel command executed");
            DialogResult = new ProductToInvoiceResult { Confirmed = false };
            RequestClose?.Invoke();
        }

        #endregion

        #region Helper Methods

        private void InitializePermissions()
        {
            OnPropertyChanged(nameof(CanSelectCustomer));
            OnPropertyChanged(nameof(PermissionMessage));
            OnPropertyChanged(nameof(CreateButtonText));
            OnPropertyChanged(nameof(DialogTitle));
        }

        private void LoadCustomers()
        {
            try
            {
                if (CanSelectCustomer)
                {
                    var customers = _dbService.GetCustomers()
                        .Where(c => c.IsActive)
                        .OrderBy(c => c.FirstName)
                        .ThenBy(c => c.LastName)
                        .ToList();

                    AvailableCustomers.Clear();
                    foreach (var customer in customers)
                    {
                        AvailableCustomers.Add(customer);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_TO_INVOICE_VM] Error loading customers: {ex.Message}");
            }
        }

        #endregion

        #region Events and Properties

        public event PropertyChangedEventHandler PropertyChanged;
        public event Action RequestClose;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ProductToInvoiceResult DialogResult { get; private set; }

        #endregion
    }

    /// <summary>
    /// Result object for the product-to-invoice confirmation dialog
    /// </summary>
    public class ProductToInvoiceResult
    {
        public bool Confirmed { get; set; }
        public Product Product { get; set; }
        public decimal Quantity { get; set; }
        public Customer Customer { get; set; }
        public decimal EstimatedTotal { get; set; }
        public bool CreateFullInvoice { get; set; }
        public string ErrorMessage { get; set; }

        public static ProductToInvoiceResult CreateSuccess(Product product, decimal quantity, Customer customer = null)
        {
            return new ProductToInvoiceResult
            {
                Confirmed = true,
                Product = product,
                Quantity = quantity,
                Customer = customer,
                EstimatedTotal = product.SellingPrice * quantity
            };
        }

        public static ProductToInvoiceResult CreateCancelled()
        {
            return new ProductToInvoiceResult
            {
                Confirmed = false
            };
        }

        public static ProductToInvoiceResult CreateError(string errorMessage)
        {
            return new ProductToInvoiceResult
            {
                Confirmed = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
