using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Models.Analytics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Analytics
{
    /// <summary>
    /// Service for analyzing bulk pricing performance and generating insights.
    /// Provides comprehensive analytics on bulk pricing usage, customer savings, and business impact.
    /// </summary>
    public class BulkPricingAnalyticsService
    {
        /// <summary>
        /// Generates a comprehensive bulk pricing report for a given period.
        /// </summary>
        /// <param name="startDate">Start date for the report period</param>
        /// <param name="endDate">End date for the report period</param>
        /// <returns>Comprehensive bulk pricing analytics report</returns>
        public async Task<BulkPricingReport> GenerateReportAsync(DateTime startDate, DateTime endDate)
        {
            using var context = new POSDbContext();
            
            var report = new BulkPricingReport
            {
                ReportPeriod = new DateRange { StartDate = startDate, EndDate = endDate },
                GeneratedAt = DateTime.Now
            };

            // Get sales data for the period
            var sales = await context.Sales
                .Include(s => s.Items)
                    .ThenInclude(si => si.Product)
                        .ThenInclude(p => p.PriceTiers)
                .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                .ToListAsync();

            // Calculate bulk pricing metrics
            await CalculateBulkPricingMetrics(report, sales);
            await CalculateProductPerformance(report, sales);
            await CalculateCustomerSavings(report, sales);
            await CalculateTrendAnalysis(report, startDate, endDate);

            return report;
        }

        /// <summary>
        /// Gets bulk pricing performance for specific products.
        /// </summary>
        /// <param name="productIds">List of product IDs to analyze</param>
        /// <param name="startDate">Start date for analysis</param>
        /// <param name="endDate">End date for analysis</param>
        /// <returns>Product-specific bulk pricing performance data</returns>
        public async Task<List<ProductBulkPricingPerformance>> GetProductPerformanceAsync(
            List<int> productIds, DateTime startDate, DateTime endDate)
        {
            using var context = new POSDbContext();
            
            var performance = new List<ProductBulkPricingPerformance>();

            foreach (var productId in productIds)
            {
                var product = await context.Products
                    .Include(p => p.PriceTiers)
                    .FirstOrDefaultAsync(p => p.Id == productId);

                if (product == null) continue;

                var salesItems = await context.SaleItems
                    .Include(si => si.Sale)
                    .Where(si => si.ProductId == productId && 
                                si.Sale.SaleDate >= startDate && 
                                si.Sale.SaleDate <= endDate)
                    .ToListAsync();

                var productPerformance = CalculateProductPerformance(product, salesItems);
                performance.Add(productPerformance);
            }

            return performance;
        }

        /// <summary>
        /// Analyzes bulk pricing tier effectiveness.
        /// </summary>
        /// <param name="startDate">Start date for analysis</param>
        /// <param name="endDate">End date for analysis</param>
        /// <returns>Analysis of how effective each pricing tier is</returns>
        public async Task<List<PriceTierEffectiveness>> AnalyzeTierEffectivenessAsync(DateTime startDate, DateTime endDate)
        {
            using var context = new POSDbContext();
            
            var effectiveness = new List<PriceTierEffectiveness>();

            var activeTiers = await context.ProductPriceTiers
                .Include(pt => pt.Product)
                .Where(pt => pt.IsActive)
                .ToListAsync();

            foreach (var tier in activeTiers)
            {
                var salesInTier = await context.SaleItems
                    .Include(si => si.Sale)
                    .Where(si => si.ProductId == tier.ProductId &&
                                si.Quantity >= tier.MinimumQuantity &&
                                (tier.MaximumQuantity == null || si.Quantity <= tier.MaximumQuantity) &&
                                si.Sale.SaleDate >= startDate &&
                                si.Sale.SaleDate <= endDate)
                    .ToListAsync();

                var tierEffectiveness = new PriceTierEffectiveness
                {
                    PriceTier = tier,
                    TimesUsed = salesInTier.Count,
                    TotalQuantitySold = salesInTier.Sum(si => si.Quantity),
                    TotalRevenue = salesInTier.Sum(si => si.Total),
                    AverageOrderSize = salesInTier.Any() ? salesInTier.Average(si => si.Quantity) : 0,
                    CustomerSavings = CalculateTierSavings(tier, salesInTier)
                };

                effectiveness.Add(tierEffectiveness);
            }

            return effectiveness.OrderByDescending(e => e.TotalRevenue).ToList();
        }

        /// <summary>
        /// Gets bulk pricing adoption trends over time.
        /// </summary>
        /// <param name="startDate">Start date for trend analysis</param>
        /// <param name="endDate">End date for trend analysis</param>
        /// <param name="intervalType">Type of interval (daily, weekly, monthly)</param>
        /// <returns>Trend data showing bulk pricing adoption over time</returns>
        public async Task<List<BulkPricingTrendData>> GetAdoptionTrendsAsync(
            DateTime startDate, DateTime endDate, TrendInterval intervalType = TrendInterval.Weekly)
        {
            using var context = new POSDbContext();
            
            var trends = new List<BulkPricingTrendData>();
            var intervals = GenerateIntervals(startDate, endDate, intervalType);

            foreach (var interval in intervals)
            {
                var sales = await context.Sales
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                            .ThenInclude(p => p.PriceTiers)
                    .Where(s => s.SaleDate >= interval.StartDate && s.SaleDate <= interval.EndDate)
                    .ToListAsync();

                var bulkPricingSales = sales.Where(s => s.Items.Any(HasBulkPricing)).ToList();

                var trendData = new BulkPricingTrendData
                {
                    Period = interval,
                    TotalSales = sales.Count,
                    BulkPricingSales = bulkPricingSales.Count,
                    AdoptionRate = sales.Count > 0 ? (decimal)bulkPricingSales.Count / sales.Count * 100 : 0,
                    TotalSavings = bulkPricingSales.Sum(s => CalculateSaleSavings(s)),
                    AverageSavingsPerSale = bulkPricingSales.Any() ? 
                        bulkPricingSales.Average(s => CalculateSaleSavings(s)) : 0
                };

                trends.Add(trendData);
            }

            return trends;
        }

        private async Task CalculateBulkPricingMetrics(BulkPricingReport report, List<Sale> sales)
        {
            var bulkPricingSales = sales.Where(s => s.Items.Any(HasBulkPricing)).ToList();
            
            report.TotalSales = sales.Count;
            report.BulkPricingSales = bulkPricingSales.Count;
            report.BulkPricingAdoptionRate = sales.Count > 0 ? 
                (decimal)bulkPricingSales.Count / sales.Count * 100 : 0;
            
            report.TotalCustomerSavings = bulkPricingSales.Sum(s => CalculateSaleSavings(s));
            report.AverageSavingsPerSale = bulkPricingSales.Any() ? 
                bulkPricingSales.Average(s => CalculateSaleSavings(s)) : 0;
            
            report.TotalBulkPricingRevenue = bulkPricingSales.Sum(s => s.GrandTotal);
            report.BulkPricingRevenuePercentage = sales.Sum(s => s.GrandTotal) > 0 ?
                report.TotalBulkPricingRevenue / sales.Sum(s => s.GrandTotal) * 100 : 0;
        }

        private async Task CalculateProductPerformance(BulkPricingReport report, List<Sale> sales)
        {
            var productPerformance = new Dictionary<int, ProductBulkPricingPerformance>();

            foreach (var sale in sales)
            {
                foreach (var item in sale.Items.Where(HasBulkPricing))
                {
                    if (!productPerformance.ContainsKey(item.ProductId))
                    {
                        productPerformance[item.ProductId] = new ProductBulkPricingPerformance
                        {
                            Product = item.Product,
                            TotalQuantitySold = 0,
                            TotalRevenue = 0,
                            CustomerSavings = 0,
                            TimesOrdered = 0
                        };
                    }

                    var performance = productPerformance[item.ProductId];
                    performance.TotalQuantitySold += item.Quantity;
                    performance.TotalRevenue += item.Total;
                    performance.CustomerSavings += CalculateItemSavings(item);
                    performance.TimesOrdered++;
                }
            }

            report.TopPerformingProducts = productPerformance.Values
                .OrderByDescending(p => p.TotalRevenue)
                .Take(10)
                .ToList();
        }

        private async Task CalculateCustomerSavings(BulkPricingReport report, List<Sale> sales)
        {
            var customerSavings = new Dictionary<int?, decimal>();

            foreach (var sale in sales.Where(s => s.Items.Any(HasBulkPricing)))
            {
                var customerId = sale.CustomerId;
                var saleSavings = CalculateSaleSavings(sale);

                if (!customerSavings.ContainsKey(customerId))
                    customerSavings[customerId] = 0;

                customerSavings[customerId] += saleSavings;
            }

            report.TopSavingCustomers = customerSavings
                .Where(kvp => kvp.Key.HasValue)
                .OrderByDescending(kvp => kvp.Value)
                .Take(10)
                .Select(kvp => new CustomerSavings
                {
                    CustomerId = kvp.Key.Value,
                    TotalSavings = kvp.Value
                })
                .ToList();
        }

        private async Task CalculateTrendAnalysis(BulkPricingReport report, DateTime startDate, DateTime endDate)
        {
            // Calculate week-over-week trends
            var weeklyTrends = await GetAdoptionTrendsAsync(startDate, endDate, TrendInterval.Weekly);
            
            if (weeklyTrends.Count >= 2)
            {
                var lastWeek = weeklyTrends[weeklyTrends.Count - 1];
                var previousWeek = weeklyTrends[weeklyTrends.Count - 2];
                
                report.AdoptionRateChange = lastWeek.AdoptionRate - previousWeek.AdoptionRate;
                report.SavingsGrowth = lastWeek.TotalSavings - previousWeek.TotalSavings;
            }
        }

        private ProductBulkPricingPerformance CalculateProductPerformance(Product product, List<SaleItem> salesItems)
        {
            var bulkPricingItems = salesItems.Where(si => HasBulkPricing(si)).ToList();
            
            return new ProductBulkPricingPerformance
            {
                Product = product,
                TotalQuantitySold = bulkPricingItems.Sum(si => si.Quantity),
                TotalRevenue = bulkPricingItems.Sum(si => si.Total),
                CustomerSavings = bulkPricingItems.Sum(si => CalculateItemSavings(si)),
                TimesOrdered = bulkPricingItems.Count,
                AverageOrderSize = bulkPricingItems.Any() ? bulkPricingItems.Average(si => si.Quantity) : 0
            };
        }

        private decimal CalculateTierSavings(ProductPriceTier tier, List<SaleItem> salesItems)
        {
            decimal totalSavings = 0;
            
            foreach (var item in salesItems)
            {
                var regularPrice = item.Product?.SellingPrice ?? 0;
                var tierPrice = tier.EffectiveUnitPrice;
                var savings = (regularPrice - tierPrice) * item.Quantity;
                totalSavings += Math.Max(0, savings);
            }
            
            return totalSavings;
        }

        private bool HasBulkPricing(SaleItem item)
        {
            if (item.Product?.PriceTiers == null || !item.Product.PriceTiers.Any())
                return false;

            var applicableTier = item.Product.PriceTiers
                .Where(pt => pt.IsCurrentlyValid && pt.QualifiesForTier(item.Quantity))
                .OrderByDescending(pt => pt.MinimumQuantity)
                .FirstOrDefault();

            return applicableTier != null && applicableTier.EffectiveUnitPrice < item.Product.SellingPrice;
        }

        private decimal CalculateItemSavings(SaleItem item)
        {
            if (!HasBulkPricing(item))
                return 0;

            var regularPrice = item.Product?.SellingPrice ?? 0;
            var actualUnitPrice = item.Quantity > 0 ? item.Total / item.Quantity : 0;
            return Math.Max(0, (regularPrice - actualUnitPrice) * item.Quantity);
        }

        private decimal CalculateSaleSavings(Sale sale)
        {
            return sale.Items.Sum(item => CalculateItemSavings(item));
        }

        private List<DateRange> GenerateIntervals(DateTime startDate, DateTime endDate, TrendInterval intervalType)
        {
            var intervals = new List<DateRange>();
            var current = startDate;

            while (current < endDate)
            {
                var intervalEnd = intervalType switch
                {
                    TrendInterval.Daily => current.AddDays(1),
                    TrendInterval.Weekly => current.AddDays(7),
                    TrendInterval.Monthly => current.AddMonths(1),
                    _ => current.AddDays(7)
                };

                intervals.Add(new DateRange
                {
                    StartDate = current,
                    EndDate = Math.Min(intervalEnd.Ticks, endDate.Ticks) == endDate.Ticks ? endDate : intervalEnd
                });

                current = intervalEnd;
            }

            return intervals;
        }
    }
}
