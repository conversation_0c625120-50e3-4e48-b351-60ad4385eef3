<Window x:Class="POSSystem.Views.Dialogs.WeightBasedProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Weight-Based Product Selection"
        Width="520" Height="620"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="Transparent"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Modern Color Palette -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2563EB"/>
        <SolidColorBrush x:Key="PrimaryHoverBrush" Color="#1D4ED8"/>
        <SolidColorBrush x:Key="PrimaryPressedBrush" Color="#1E40AF"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#64748B"/>
        <SolidColorBrush x:Key="SecondaryHoverBrush" Color="#475569"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#10B981"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#E2E8F0"/>
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#1E293B"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#64748B"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#059669"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#D97706"/>

        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="ModernShadow"
                         Color="#000000"
                         Opacity="0.15"
                         BlurRadius="20"
                         ShadowDepth="8"
                         Direction="270"/>

        <!-- Card Shadow Effect -->
        <DropShadowEffect x:Key="CardShadow"
                         Color="#000000"
                         Opacity="0.08"
                         BlurRadius="12"
                         ShadowDepth="4"
                         Direction="270"/>

        <!-- Primary Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="Margin" Value="8,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryPressedBrush}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Secondary Button Style -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="Margin" Value="8,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TabControl Style -->
        <Style x:Key="ModernTabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Tab Headers -->
                            <Border Grid.Row="0"
                                    Background="{StaticResource BackgroundBrush}"
                                    CornerRadius="12,12,0,0"
                                    BorderThickness="1,1,1,0"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    Margin="0,0,0,0">
                                <TabPanel x:Name="HeaderPanel"
                                         IsItemsHost="True"
                                         Background="Transparent"
                                         Margin="8,8,8,0"/>
                            </Border>

                            <!-- Tab Content -->
                            <Border Grid.Row="1"
                                    Background="{StaticResource SurfaceBrush}"
                                    CornerRadius="0,0,12,12"
                                    BorderThickness="1,0,1,1"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    Padding="20,16,20,20">
                                <ContentPresenter x:Name="PART_SelectedContentHost"
                                                ContentSource="SelectedContent"/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TabItem Style -->
        <Style x:Key="ModernTabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="0,0,4,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="TabBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8,8,0,0"
                                BorderThickness="0"
                                Margin="{TemplateBinding Margin}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter x:Name="ContentSite"
                                            ContentSource="Header"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="TabBorder" Property="Background" Value="{StaticResource SurfaceBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                                <Setter TargetName="TabBorder" Property="BorderThickness" Value="1,1,1,0"/>
                                <Setter TargetName="TabBorder" Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="TabBorder" Property="Background" Value="{StaticResource BackgroundBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsSelected" Value="True"/>
                                    <Condition Property="IsMouseOver" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="TabBorder" Property="Background" Value="{StaticResource SurfaceBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        </Style>

        <!-- Header Text Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        </Style>

        <!-- Subtitle Text Style -->
        <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        </Style>

        <!-- Label Text Style -->
        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        </Style>
    </Window.Resources>

    <!-- Main Container with Modern Design -->
    <Border Style="{StaticResource CardStyle}"
            Margin="16"
            Effect="{StaticResource ModernShadow}">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Product Information Header -->
            <Border Grid.Row="0"
                    Background="{StaticResource BackgroundBrush}"
                    Padding="16"
                    CornerRadius="12"
                    Margin="0,0,0,16"
                    BorderThickness="1"
                    BorderBrush="{StaticResource BorderBrush}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ProductNameText"
                                  Text="{DynamicResource ProductName}"
                                  Style="{StaticResource HeaderTextStyle}"
                                  Margin="0,0,0,6"/>
                        <TextBlock x:Name="PricePerUnitText"
                                  Text="Price per unit: $0.00"
                                  Style="{StaticResource SubtitleTextStyle}"
                                  Margin="0,0,0,4"/>
                        <TextBlock x:Name="StockInfoText"
                                  Text="Available stock: Loading..."
                                  Style="{StaticResource SubtitleTextStyle}"
                                  Foreground="{StaticResource AccentBrush}"
                                  FontWeight="SemiBold"/>
                    </StackPanel>

                    <Border Grid.Column="1"
                            Background="{StaticResource AccentBrush}"
                            CornerRadius="16"
                            Padding="10,6"
                            VerticalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚖️"
                                      FontSize="12"
                                      Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource WeightBased}"
                                      FontSize="11"
                                      FontWeight="SemiBold"
                                      Foreground="White"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- Tab-Based Input Selection -->
            <TabControl x:Name="InputMethodTabControl"
                       Grid.Row="1"
                       Style="{StaticResource ModernTabControlStyle}"
                       Margin="0,0,0,16"
                       SelectionChanged="TabControl_SelectionChanged">

                <!-- Amount Input Tab -->
                <TabItem x:Name="AmountTab"
                        Header="💰 Enter Amount"
                        Style="{StaticResource ModernTabItemStyle}"
                        IsSelected="True"
                        ToolTip="Enter the amount you want to spend">
                    <StackPanel Margin="0,8,0,0">
                        <TextBlock Text="{DynamicResource HowMuchSpend}"
                                  Style="{StaticResource LabelTextStyle}"
                                  Margin="0,0,0,8"/>
                        

                        <Border Background="{StaticResource SurfaceBrush}"
                                CornerRadius="8"
                                BorderThickness="2"
                                BorderBrush="{StaticResource BorderBrush}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0"
                                          Text="$"
                                          FontSize="18"
                                          FontWeight="SemiBold"
                                          Foreground="{StaticResource TextSecondaryBrush}"
                                          VerticalAlignment="Center"
                                          Margin="16,0,8,0"/>
                                <TextBox x:Name="AmountTextBox"
                                        Grid.Column="1"
                                        Style="{StaticResource ModernTextBoxStyle}"
                                        BorderThickness="0"
                                        Background="Transparent"
                                        Margin="0"
                                        Padding="8,12,16,12"
                                        TextChanged="AmountTextBox_TextChanged"
                                        PreviewTextInput="NumericTextBox_PreviewTextInput"
                                        ToolTip="Enter amount (e.g., 5.00)"/>
                            </Grid>
                        </Border>
                    </StackPanel>
                </TabItem>

                <!-- Weight Input Tab -->
                <TabItem x:Name="WeightTab"
                        Header="⚖️ Enter Weight"
                        Style="{StaticResource ModernTabItemStyle}"
                        ToolTip="Enter the weight or quantity you want to purchase">
                    <StackPanel Margin="0,8,0,0">
                        <TextBlock Text="{DynamicResource HowMuchWeight}"
                                  Style="{StaticResource LabelTextStyle}"
                                  Margin="0,0,0,8"/>
                        

                        <TextBox x:Name="WeightTextBox"
                                Style="{StaticResource ModernTextBoxStyle}"
                                TextChanged="WeightTextBox_TextChanged"
                                PreviewTextInput="NumericTextBox_PreviewTextInput"
                                ToolTip="Enter weight/quantity (e.g., 2.5)"/>
                    </StackPanel>
                </TabItem>
            </TabControl>

            <!-- Calculation Results -->
            <Border Grid.Row="2"
                    Background="{StaticResource BackgroundBrush}"
                    Padding="16"
                    CornerRadius="12"
                    Margin="0,0,0,16"
                    BorderThickness="1"
                    BorderBrush="{StaticResource BorderBrush}">
                <StackPanel>
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0"
                                  Text="📊"
                                  FontSize="16"
                                  Margin="0,0,8,0"
                                  VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1"
                                  Text="{DynamicResource CalculationSummary}"
                                  Style="{StaticResource LabelTextStyle}"
                                  VerticalAlignment="Center"/>
                    </Grid>

                    <TextBlock x:Name="CalculationText"
                              Text="{DynamicResource SelectTabEnterValue}"
                              FontSize="13"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              TextWrapping="Wrap"
                              Margin="0,0,0,12"/>

                    <!-- Results Grid -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Total Amount Card -->
                        <Border Grid.Column="0"
                                Background="{StaticResource SurfaceBrush}"
                                CornerRadius="8"
                                Padding="12,10"
                                Margin="0,0,6,0"
                                BorderThickness="1"
                                BorderBrush="{StaticResource BorderBrush}">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource TotalCost}"
                                          FontSize="11"
                                          Foreground="{StaticResource TextSecondaryBrush}"
                                          Margin="0,0,0,4"/>
                                <TextBlock x:Name="TotalAmountText"
                                          Text="$0.00"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="{StaticResource SuccessBrush}"/>
                            </StackPanel>
                        </Border>

                        <!-- Total Weight Card -->
                        <Border Grid.Column="1"
                                Background="{StaticResource SurfaceBrush}"
                                CornerRadius="8"
                                Padding="12,10"
                                Margin="6,0,0,0"
                                BorderThickness="1"
                                BorderBrush="{StaticResource BorderBrush}">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource TotalWeight}"
                                          FontSize="11"
                                          Foreground="{StaticResource TextSecondaryBrush}"
                                          Margin="0,0,0,4"/>
                                <TextBlock x:Name="TotalWeightText"
                                          Text="0.00"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="{StaticResource SuccessBrush}"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="3"
                       Orientation="Horizontal"
                       HorizontalAlignment="Right"
                       Margin="0,8,0,0">
                <Button Content="Cancel"
                        Style="{StaticResource SecondaryButtonStyle}"
                        Click="CancelButton_Click"
                        Padding="20,10"
                        ToolTip="Cancel and close dialog (Esc)"/>
                <Button x:Name="AddToCartButton"
                        IsEnabled="False"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Click="AddToCartButton_Click"
                        Padding="20,10"
                        ToolTip="Add item to cart (Enter)">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🛒" FontSize="14" Margin="0,0,6,0"/>
                        <TextBlock Text="{DynamicResource AddToCart}"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
