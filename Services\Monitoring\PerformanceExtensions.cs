using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace POSSystem.Services.Monitoring
{
    /// <summary>
    /// Extension methods for easy performance monitoring integration
    /// </summary>
    public static class PerformanceExtensions
    {
        /// <summary>
        /// Execute an action with performance monitoring
        /// </summary>
        public static void ExecuteWithMonitoring(this PerformanceMonitoringService monitor, 
            string operationName, Action action, string category = null)
        {
            using var tracker = monitor.StartTracking(operationName, category);
            try
            {
                action();
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Execute an async action with performance monitoring
        /// </summary>
        public static async Task ExecuteWithMonitoringAsync(this PerformanceMonitoringService monitor,
            string operationName, Func<Task> action, string category = null)
        {
            using var tracker = monitor.StartTracking(operationName, category);
            try
            {
                await action();
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Execute a function with performance monitoring and return result
        /// </summary>
        public static T ExecuteWithMonitoring<T>(this PerformanceMonitoringService monitor,
            string operationName, Func<T> func, string category = null)
        {
            using var tracker = monitor.StartTracking(operationName, category);
            try
            {
                var result = func();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }

        /// <summary>
        /// Execute an async function with performance monitoring and return result
        /// </summary>
        public static async Task<T> ExecuteWithMonitoringAsync<T>(this PerformanceMonitoringService monitor,
            string operationName, Func<Task<T>> func, string category = null)
        {
            using var tracker = monitor.StartTracking(operationName, category);
            try
            {
                var result = await func();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }

        /// <summary>
        /// Monitor database operations
        /// </summary>
        public static T MonitorDatabaseOperation<T>(this PerformanceMonitoringService monitor,
            string operationName, Func<T> operation, int? recordCount = null)
        {
            using var tracker = monitor.StartTracking(operationName, "Database");
            
            if (recordCount.HasValue)
            {
                tracker.AddMetadata("RecordCount", recordCount.Value);
            }

            try
            {
                var result = operation();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }

        /// <summary>
        /// Monitor async database operations
        /// </summary>
        public static async Task<T> MonitorDatabaseOperationAsync<T>(this PerformanceMonitoringService monitor,
            string operationName, Func<Task<T>> operation, int? recordCount = null)
        {
            using var tracker = monitor.StartTracking(operationName, "Database");
            
            if (recordCount.HasValue)
            {
                tracker.AddMetadata("RecordCount", recordCount.Value);
            }

            try
            {
                var result = await operation();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }

        /// <summary>
        /// Monitor UI operations
        /// </summary>
        public static void MonitorUIOperation(this PerformanceMonitoringService monitor,
            string operationName, Action operation)
        {
            using var tracker = monitor.StartTracking(operationName, "UI");
            
            try
            {
                operation();
                tracker.AddMetadata("Success", true);
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }

        /// <summary>
        /// Monitor business logic operations
        /// </summary>
        public static T MonitorBusinessOperation<T>(this PerformanceMonitoringService monitor,
            string operationName, Func<T> operation, object inputData = null)
        {
            using var tracker = monitor.StartTracking(operationName, "Business");
            
            if (inputData != null)
            {
                tracker.AddMetadata("InputType", inputData.GetType().Name);
            }

            try
            {
                var result = operation();
                tracker.AddMetadata("Success", true);
                if (result != null)
                {
                    tracker.AddMetadata("OutputType", result.GetType().Name);
                }
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                throw;
            }
        }
    }

    /// <summary>
    /// Performance monitoring attribute for automatic method monitoring
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class MonitorPerformanceAttribute : Attribute
    {
        public string OperationName { get; set; }
        public string Category { get; set; }
        public bool IncludeParameters { get; set; } = false;
        public bool IncludeResult { get; set; } = false;

        public MonitorPerformanceAttribute(string operationName = null, string category = null)
        {
            OperationName = operationName;
            Category = category;
        }
    }

    /// <summary>
    /// Performance monitoring interceptor for dependency injection
    /// </summary>
    public class PerformanceMonitoringInterceptor
    {
        private readonly PerformanceMonitoringService _monitor;
        private readonly ILogger<PerformanceMonitoringInterceptor> _logger;

        public PerformanceMonitoringInterceptor(
            PerformanceMonitoringService monitor,
            ILogger<PerformanceMonitoringInterceptor> logger = null)
        {
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
            _logger = logger;
        }

        /// <summary>
        /// Intercept method calls for automatic performance monitoring
        /// </summary>
        public T Intercept<T>(string methodName, string className, Func<T> method)
        {
            var operationName = $"{className}.{methodName}";
            
            using var tracker = _monitor.StartTracking(operationName, "Service");
            
            try
            {
                var result = method();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                _logger?.LogError(ex, "Error in monitored method {OperationName}", operationName);
                throw;
            }
        }

        /// <summary>
        /// Intercept async method calls for automatic performance monitoring
        /// </summary>
        public async Task<T> InterceptAsync<T>(string methodName, string className, Func<Task<T>> method)
        {
            var operationName = $"{className}.{methodName}";
            
            using var tracker = _monitor.StartTracking(operationName, "Service");
            
            try
            {
                var result = await method();
                tracker.AddMetadata("Success", true);
                return result;
            }
            catch (Exception ex)
            {
                tracker.AddMetadata("Error", ex.Message);
                tracker.AddMetadata("Success", false);
                _logger?.LogError(ex, "Error in monitored async method {OperationName}", operationName);
                throw;
            }
        }
    }

    /// <summary>
    /// Service collection extensions for performance monitoring
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add performance monitoring services to DI container
        /// </summary>
        public static IServiceCollection AddPerformanceMonitoring(this IServiceCollection services)
        {
            services.AddSingleton<PerformanceMonitoringService>();
            services.AddScoped<PerformanceMonitoringInterceptor>();
            return services;
        }
    }

    /// <summary>
    /// Performance monitoring helper for common scenarios
    /// </summary>
    public static class PerformanceHelper
    {
        /// <summary>
        /// Create a performance tracker for a using statement
        /// </summary>
        public static IDisposable Track(PerformanceMonitoringService monitor, string operationName, string category = null)
        {
            return monitor.StartTracking(operationName, category);
        }

        /// <summary>
        /// Measure execution time of an action
        /// </summary>
        public static TimeSpan Measure(Action action)
        {
            var stopwatch = Stopwatch.StartNew();
            action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Measure execution time of an async action
        /// </summary>
        public static async Task<TimeSpan> MeasureAsync(Func<Task> action)
        {
            var stopwatch = Stopwatch.StartNew();
            await action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Get current memory usage in MB
        /// </summary>
        public static long GetMemoryUsageMB()
        {
            var process = Process.GetCurrentProcess();
            return process.WorkingSet64 / (1024 * 1024);
        }

        /// <summary>
        /// Force garbage collection and return memory freed
        /// </summary>
        public static long ForceGCAndGetMemoryFreed()
        {
            var beforeGC = GC.GetTotalMemory(false);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var afterGC = GC.GetTotalMemory(false);
            return (beforeGC - afterGC) / (1024 * 1024); // Return MB freed
        }
    }
}
