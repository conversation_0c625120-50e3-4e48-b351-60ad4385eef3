using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Applies minimal changes to an ObservableCollection to match a desired list, using a key selector.
    /// Targets reduced UI churn versus wholesale replacement: removes missing, inserts new, and moves existing to match order.
    /// Assumes unique keys in the desired list.
    /// </summary>
    public static class CollectionDiffer
    {
        public static void ApplyByKey<T, TKey>(ObservableCollection<T> current, IList<T> desired, Func<T, TKey> keySelector)
        {
            if (current == null) throw new ArgumentNullException(nameof(current));
            if (desired == null) throw new ArgumentNullException(nameof(desired));
            if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

            // Fast path: identical reference
            if (ReferenceEquals(current, desired)) return;

            // Build lookup for desired keys
            var desiredKeys = new HashSet<TKey>(desired.Select(keySelector));

            // Remove items not in desired (iterate backwards)
            for (int i = current.Count - 1; i >= 0; i--)
            {
                var curKey = keySelector(current[i]);
                if (!desiredKeys.Contains(curKey))
                {
                    current.RemoveAt(i);
                }
            }

            // Rebuild index map for current after removals
            var indexByKey = new Dictionary<TKey, int>(current.Count);
            for (int i = 0; i < current.Count; i++)
            {
                var key = keySelector(current[i]);
                if (!indexByKey.ContainsKey(key))
                    indexByKey.Add(key, i);
            }

            // Insert/move to match desired order
            for (int targetIndex = 0; targetIndex < desired.Count; targetIndex++)
            {
                var desiredItem = desired[targetIndex];
                var desiredKey = keySelector(desiredItem);

                if (indexByKey.TryGetValue(desiredKey, out int curIndex))
                {
                    if (curIndex != targetIndex)
                    {
                        // Move existing item to target index
                        current.Move(curIndex, targetIndex);
                        // Update indices in map due to move
                        if (curIndex < targetIndex)
                        {
                            // Items between curIndex+1..targetIndex shift left
                            foreach (var k in indexByKey.Keys.ToList())
                            {
                                var idx = indexByKey[k];
                                if (idx > curIndex && idx <= targetIndex)
                                    indexByKey[k] = idx - 1;
                            }
                        }
                        else
                        {
                            // Items between targetIndex..curIndex-1 shift right
                            foreach (var k in indexByKey.Keys.ToList())
                            {
                                var idx = indexByKey[k];
                                if (idx >= targetIndex && idx < curIndex)
                                    indexByKey[k] = idx + 1;
                            }
                        }
                        indexByKey[desiredKey] = targetIndex;
                    }
                }
                else
                {
                    // Insert new item
                    current.Insert(targetIndex, desiredItem);

                    // Update indices in map due to insert
                    foreach (var k in indexByKey.Keys.ToList())
                    {
                        if (indexByKey[k] >= targetIndex)
                            indexByKey[k] = indexByKey[k] + 1;
                    }
                    indexByKey[desiredKey] = targetIndex;
                }
            }

            // If current has extra items at the end (shouldn't after removal), trim
            while (current.Count > desired.Count)
            {
                current.RemoveAt(current.Count - 1);
            }
        }
    }
}

