using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Windows;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.Views;

namespace POSSystem.ViewModels.Dashboard
{
    public class StatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private readonly string _statType;
        private bool _isLoading;
        private string _title;
        private string _subtitle;
        private decimal _totalSales;
        private string _salesGrowth;
        private Brush _salesGrowthColor;
        private int _transactionCount;
        private string _transactionGrowth;
        private Brush _transactionGrowthColor;
        private decimal _averageTransaction;
        private string _avgTransactionGrowth;
        private Brush _avgTransactionGrowthColor;
        private SeriesCollection _trendSeries;
        private string[] _trendLabels;
        private SeriesCollection _hourlyDistributionSeries;
        private string[] _hourlyLabels;
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;
        private ObservableCollection<Product> _products;
        private Product _selectedProduct;
        private bool _isProductFilterEnabled;
        private ObservableCollection<Category> _categories;
        private Category _selectedCategory;
        private bool _isCategoryFilterEnabled;
        private SeriesCollection _salesByDaysSeries;
        private string[] _salesByDaysLabels;
        private Dictionary<DayOfWeek, decimal> _salesByDayData;
        private ObservableCollection<TopProductItem> _topProducts;
        private string _barcodeSearch;
        private ICommand _searchByBarcodeCommand;
        private ICommand _openProductSelectionCommand;

        public StatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService,
            string statType)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;
            _statType = statType;
            
            // Initialize properties
            var salesText = Application.Current.TryFindResource("Sales") as string ?? "Sales";
            var analysisText = Application.Current.TryFindResource("Analysis") as string ?? "Analysis";
            Title = $"{salesText} {analysisText}";
            Subtitle = Application.Current.TryFindResource("DetailedMetricsAndTrends") as string ?? "Detailed metrics and trends";
            
            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };

            // Set initial period
            SelectedTrendPeriod = TrendPeriods.First();

            // Initialize collections
            Products = new ObservableCollection<Product>();
            Categories = new ObservableCollection<Category>();
            TopProducts = new ObservableCollection<TopProductItem>();

            // Load initial data
            _ = InitializeAsync();
        }

        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                await Task.WhenAll(
                    LoadProductsAsync(),
                    LoadCategoriesAsync(),
                    LoadDataAsync()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing stats details: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        public ObservableCollection<Product> Products
        {
            get => _products;
            set { _products = value; OnPropertyChanged(); }
        }

        public Product SelectedProduct
        {
            get => _selectedProduct;
            set 
            { 
                _selectedProduct = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public bool IsProductFilterEnabled
        {
            get => _isProductFilterEnabled;
            set 
            { 
                _isProductFilterEnabled = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set { _categories = value; OnPropertyChanged(); }
        }

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set 
            { 
                _selectedCategory = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public bool IsCategoryFilterEnabled
        {
            get => _isCategoryFilterEnabled;
            set 
            { 
                _isCategoryFilterEnabled = value;
                OnPropertyChanged();
                if (!value)
                {
                    SelectedCategory = null;
                }
                _ = LoadDataAsync();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set { _totalSales = value; OnPropertyChanged(); }
        }

        public string SalesGrowth
        {
            get => _salesGrowth;
            set { _salesGrowth = value; OnPropertyChanged(); }
        }

        public Brush SalesGrowthColor
        {
            get => _salesGrowthColor;
            set { _salesGrowthColor = value; OnPropertyChanged(); }
        }

        public int TransactionCount
        {
            get => _transactionCount;
            set { _transactionCount = value; OnPropertyChanged(); }
        }

        public string TransactionGrowth
        {
            get => _transactionGrowth;
            set { _transactionGrowth = value; OnPropertyChanged(); }
        }

        public Brush TransactionGrowthColor
        {
            get => _transactionGrowthColor;
            set { _transactionGrowthColor = value; OnPropertyChanged(); }
        }

        public decimal AverageTransaction
        {
            get => _averageTransaction;
            set { _averageTransaction = value; OnPropertyChanged(); }
        }

        public string AvgTransactionGrowth
        {
            get => _avgTransactionGrowth;
            set { _avgTransactionGrowth = value; OnPropertyChanged(); }
        }

        public Brush AvgTransactionGrowthColor
        {
            get => _avgTransactionGrowthColor;
            set { _avgTransactionGrowthColor = value; OnPropertyChanged(); }
        }

        public SeriesCollection TrendSeries
        {
            get => _trendSeries;
            set { _trendSeries = value; OnPropertyChanged(); }
        }

        public string[] TrendLabels
        {
            get => _trendLabels;
            set { _trendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection HourlyDistributionSeries
        {
            get => _hourlyDistributionSeries;
            set { _hourlyDistributionSeries = value; OnPropertyChanged(); }
        }

        public string[] HourlyLabels
        {
            get => _hourlyLabels;
            set { _hourlyLabels = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set 
            { 
                _selectedTrendPeriod = value; 
                OnPropertyChanged();
                // Reload all data when period changes
                _ = LoadDataAsync();
            }
        }

        public SeriesCollection SalesByDaysSeries
        {
            get => _salesByDaysSeries;
            set { _salesByDaysSeries = value; OnPropertyChanged(); }
        }

        public string[] SalesByDaysLabels
        {
            get => _salesByDaysLabels;
            set { _salesByDaysLabels = value; OnPropertyChanged(); }
        }

        public Dictionary<DayOfWeek, decimal> SalesByDayData
        {
            get => _salesByDayData;
            set { _salesByDayData = value; OnPropertyChanged(); }
        }

        public ObservableCollection<TopProductItem> TopProducts
        {
            get => _topProducts;
            set { _topProducts = value; OnPropertyChanged(); }
        }

        public Func<double, string> CurrencyFormatter => value => value.ToString("C0");

        public string BarcodeSearch
        {
            get => _barcodeSearch;
            set
            {
                if (_barcodeSearch != value)
                {
                    _barcodeSearch = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand SearchByBarcodeCommand => _searchByBarcodeCommand ??= new RelayCommand(_ => _ = SearchByBarcode());
        public ICommand OpenProductSelectionCommand => _openProductSelectionCommand ??= new RelayCommand(_ => OpenProductSelection());

        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load data in parallel
                await Task.WhenAll(
                    LoadMetricsAsync(),
                    LoadTrendDataAsync(),
                    LoadHourlyDistributionAsync(),
                    LoadSalesByDaysAsync(),
                    LoadTopProductsAsync()
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading stats details: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                var products = await Task.Run(() => _dbService.GetAllProducts());
                Products = new ObservableCollection<Product>(products);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading products: {ex.Message}");
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await Task.Run(() => _dbService.GetAllCategories());
                Categories = new ObservableCollection<Category>(categories);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading categories: {ex.Message}");
            }
        }

        private async Task LoadMetricsAsync()
        {
            try
            {
                // Get date ranges based on selected period
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var previousStartDate = startDate.AddDays(-(endDate - startDate).Days);
                var previousEndDate = startDate.AddSeconds(-1);

                // DEBUG: Log the calculated date ranges
                System.Diagnostics.Debug.WriteLine($"[METRICS DEBUG] Current period: {startDate:yyyy-MM-dd HH:mm:ss} to {endDate:yyyy-MM-dd HH:mm:ss}");
                System.Diagnostics.Debug.WriteLine($"[METRICS DEBUG] Previous period: {previousStartDate:yyyy-MM-dd HH:mm:ss} to {previousEndDate:yyyy-MM-dd HH:mm:ss}");
                System.Diagnostics.Debug.WriteLine($"[METRICS DEBUG] Selected period: {SelectedTrendPeriod?.ResourceKey ?? "NULL"}");

                // Get sales data
                var currentSales = await GetFilteredSalesAsync(startDate, endDate);
                var previousSales = await GetFilteredSalesAsync(previousStartDate, previousEndDate);

                // Calculate current period metrics
                TotalSales = currentSales.Sum(s => s.GrandTotal);
                TransactionCount = currentSales.Count;
                AverageTransaction = TransactionCount > 0 
                    ? TotalSales / TransactionCount 
                    : 0;

                // Calculate previous period metrics
                var prevTotalSales = previousSales.Sum(s => s.GrandTotal);
                var prevTransCount = previousSales.Count;
                var prevAvgTrans = prevTransCount > 0 ? prevTotalSales / prevTransCount : 0;

                // Calculate growth percentages
                var salesGrowth = CalculateGrowthPercentage(prevTotalSales, TotalSales);
                var transactionGrowth = CalculateGrowthPercentage(prevTransCount, TransactionCount);
                var avgTransactionGrowth = CalculateGrowthPercentage(prevAvgTrans, AverageTransaction);

                // Set growth indicators with proper formatting
                SalesGrowth = FormatGrowthText(salesGrowth);
                SalesGrowthColor = GetGrowthColor(salesGrowth);

                TransactionGrowth = FormatGrowthText(transactionGrowth);
                TransactionGrowthColor = GetGrowthColor(transactionGrowth);

                AvgTransactionGrowth = FormatGrowthText(avgTransactionGrowth);
                AvgTransactionGrowthColor = GetGrowthColor(avgTransactionGrowth);

                // Update subtitle with filter info
                UpdateSubtitle();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading metrics: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }

        private async Task<List<Sale>> GetFilteredSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sales = await Task.Run(() => _dbService.GetSalesByDateRange(startDate, endDate));
                System.Diagnostics.Debug.WriteLine($"[STATS DEBUG] Retrieved {sales.Count} sales from database for period {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                if (sales.Any())
                {
                    var statuses = sales.Select(s => s.Status).Distinct().ToList();
                    System.Diagnostics.Debug.WriteLine($"[STATS DEBUG] Sale statuses found: {string.Join(", ", statuses)}");
                    var totalAmount = sales.Sum(s => s.GrandTotal);
                    System.Diagnostics.Debug.WriteLine($"[STATS DEBUG] Total sales amount: {totalAmount:C}");
                }

                if (IsProductFilterEnabled && SelectedProduct != null)
                {
                    var productId = SelectedProduct.Id;
                    var productName = SelectedProduct.Name;
                    System.Diagnostics.Debug.WriteLine($"Filtering sales by product: {productName} (ID: {productId})");

                    var filteredSales = sales.Where(s =>
                        s.Items != null &&
                        s.Items.Any(i => i.ProductId == productId)
                    ).ToList();

                    System.Diagnostics.Debug.WriteLine($"Found {filteredSales.Count} sales containing product {productName}");

                    // Detailed verification of filtered data
                    foreach (var sale in filteredSales.Take(3))  // Log first 3 sales for verification
                    {
                        var matchingItems = sale.Items.Where(i => i.ProductId == productId).ToList();
                        System.Diagnostics.Debug.WriteLine($"Sale {sale.Id}: Found {matchingItems.Count} items of product {productName}");
                        foreach (var item in matchingItems)
                        {
                            System.Diagnostics.Debug.WriteLine($"  Item: Quantity={item.Quantity}, UnitPrice={item.UnitPrice:C2}");
                        }
                    }

                    return filteredSales;
                }
                else if (IsCategoryFilterEnabled && SelectedCategory != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Filtering by category: {SelectedCategory.Name}");
                    var categoryProducts = await Task.Run(() => _dbService.GetAllProducts()
                        .Where(p => p.CategoryId == SelectedCategory.Id)
                        .Select(p => p.Id)
                        .ToList());

                    sales = sales.Where(s =>
                        s.Items != null &&
                        s.Items.Any(i => categoryProducts.Contains(i.ProductId))
                    ).ToList();
                    System.Diagnostics.Debug.WriteLine($"After category filter: {sales.Count} sales");
                }

                return sales;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetFilteredSalesAsync: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return new List<Sale>();
            }
        }

        private void UpdateSubtitle()
        {
            if (IsProductFilterEnabled && SelectedProduct != null)
            {
                var analysisFor = Application.Current.TryFindResource("AnalysisFor")?.ToString() ?? "Analysis for";
                Subtitle = $"{analysisFor} {SelectedProduct.Name}";
            }
            else if (IsCategoryFilterEnabled && SelectedCategory != null)
            {
                var analysisFor = Application.Current.TryFindResource("AnalysisFor")?.ToString() ?? "Analysis for";
                var category = Application.Current.TryFindResource("Category")?.ToString() ?? "category";
                Subtitle = $"{analysisFor} {SelectedCategory.Name} {category}";
            }
            else
            {
                Subtitle = Application.Current.TryFindResource("DetailedMetricsAndTrends")?.ToString() ?? "Detailed metrics and trends";
            }
        }

        private async Task LoadTrendDataAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var groupedSales = new List<DatabaseService.SalesTrendPoint>();
                var labels = new List<string>();

                // Group data differently based on the selected period
                if (SelectedTrendPeriod?.ResourceKey == "TimePeriod_Today")
                {
                    // Group by hour for today
                    groupedSales = sales
                        .GroupBy(s => s.SaleDate.Hour)
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = startDate.Date.AddHours(g.Key),
                            Value = g.Sum(s => s.GrandTotal)
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("HH:00"));
                    }
                }
                else if (SelectedTrendPeriod?.ResourceKey == "TimePeriod_ThisYear")
                {
                    // Group by month for year view
                    groupedSales = sales
                        .GroupBy(s => new { s.SaleDate.Year, s.SaleDate.Month })
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Value = g.Sum(s => s.GrandTotal)
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("MMM"));
                    }
                }
                else
                {
                    // Default to weekly grouping (for ThisWeek and ThisMonth)
                    groupedSales = sales
                        .GroupBy(s => s.SaleDate.Date)
                        .Select(g => new DatabaseService.SalesTrendPoint
                        {
                            Date = g.Key,
                            Value = g.Sum(s => s.GrandTotal)
                        })
                        .OrderBy(p => p.Date)
                        .ToList();

                    foreach (var point in groupedSales)
                    {
                        labels.Add(point.Date.ToString("MMM dd"));
                    }
                }

                var values = new ChartValues<decimal>(groupedSales.Select(p => p.Value));

                TrendSeries = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "Sales",
                        Values = values,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8,
                        LineSmoothness = 0.3,
                        StrokeThickness = 2
                    }
                };

                TrendLabels = labels.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading trend data: {ex.Message}");
            }
        }

        private async Task LoadHourlyDistributionAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                // Initialize dictionary with all hours set to 0
                var hourlyData = Enumerable.Range(0, 24)
                    .ToDictionary(
                        hour => hour,
                        hour => 0m
                    );

                // Update values for hours with sales
                var salesByHour = sales
                    .GroupBy(s => s.SaleDate.Hour)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(s => s.GrandTotal)
                    );

                // Merge with defaults
                foreach (var sale in salesByHour)
                {
                    hourlyData[sale.Key] = sale.Value;
                }

                var values = new ChartValues<decimal>();
                var labels = new List<string>();

                for (int hour = 0; hour < 24; hour++)
                {
                    values.Add(hourlyData[hour]);
                    labels.Add($"{hour:00}:00");
                }

                HourlyDistributionSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "Sales by Hour",
                        Values = values,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3"))
                    }
                };

                HourlyLabels = labels.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading hourly distribution: {ex.Message}");
            }
        }

        private async Task LoadSalesByDaysAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                // Create dictionary for all days with 0 as default value
                var allDays = Enum.GetValues(typeof(DayOfWeek))
                    .Cast<DayOfWeek>()
                    .ToDictionary(
                        day => day,
                        day => 0m
                    );

                // Update values for days with sales
                var salesByDay = sales
                    .GroupBy(s => s.SaleDate.DayOfWeek)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(s => s.GrandTotal)
                    );

                // Merge with defaults
                foreach (var sale in salesByDay)
                {
                    allDays[sale.Key] = sale.Value;
                }

                var values = new ChartValues<decimal>();
                var labels = new List<string>();

                // Order by day of week starting from Sunday
                foreach (var day in allDays.OrderBy(d => (int)d.Key))
                {
                    values.Add(day.Value);
                    labels.Add(day.Key.ToString());
                }

                SalesByDaysSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "Sales by Day",
                        Values = values,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"))
                    }
                };

                SalesByDaysLabels = labels.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sales by days: {ex.Message}");
            }
        }

        private async Task LoadTopProductsAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeFromPeriod(SelectedTrendPeriod);
                var sales = await GetFilteredSalesAsync(startDate, endDate);

                var topProducts = sales
                    .SelectMany(s => s.Items)
                    .GroupBy(i => i.Product)
                    .Select(g => new TopProductItem
                    {
                        Product = g.Key,
                        TotalQuantity = g.Sum(i => i.Quantity), // ✅ WEIGHT-BASED FIX: Remove (int) cast to preserve decimal quantities
                        TotalSales = g.Sum(i => i.Quantity * i.UnitPrice)
                    })
                    .OrderByDescending(p => p.TotalSales)
                    .Take(5)
                    .ToList();

                TopProducts = new ObservableCollection<TopProductItem>(topProducts);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading top products: {ex.Message}");
            }
        }

        private (DateTime startDate, DateTime endDate) GetDateRangeFromPeriod(TrendPeriodItem period)
        {
            var now = DateTime.Now;

            switch (period?.ResourceKey)
            {
                case "TimePeriod_Today":
                    // Today: start at beginning of day (00:00:00), end at current time
                    return (now.Date, now);

                case "TimePeriod_ThisWeek":
                    // Get the first day of the current week (Sunday)
                    var firstDayOfWeek = now.Date.AddDays(-(int)now.DayOfWeek);
                    // End at current time
                    return (firstDayOfWeek, now);

                case "TimePeriod_ThisMonth":
                    // This Month: start at beginning of month, end at current time
                    return (new DateTime(now.Year, now.Month, 1), now);

                case "TimePeriod_ThisYear":
                    // This Year: start at beginning of year, end at current time
                    return (new DateTime(now.Year, 1, 1), now);

                default:
                    // Default to today if no period is selected
                    return (now.Date, now);
            }
        }

        private decimal CalculateGrowthPercentage(decimal previous, decimal current)
        {
            if (previous == 0)
                return current > 0 ? 100 : 0;
            
            return ((current - previous) / previous) * 100;
        }

        private string FormatGrowthText(decimal growth)
        {
            var sign = growth >= 0 ? "+" : "";
            return $"{sign}{growth:F1}%";
        }

        private Brush GetGrowthColor(decimal growth)
        {
            if (growth > 0)
                return new SolidColorBrush(Colors.Green);
            else if (growth < 0)
                return new SolidColorBrush(Colors.Red);
            else
                return new SolidColorBrush(Colors.Gray);
        }

        private async Task SearchByBarcode()
        {
            if (string.IsNullOrWhiteSpace(BarcodeSearch)) return;

            try
            {
                var product = await Task.Run(() => _dbService.GetProductByBarcode(BarcodeSearch));
                if (product != null)
                {
                    SelectedProduct = product;
                    IsProductFilterEnabled = true;
                    await LoadDataAsync();
                }
                else
                {
                    // Show snackbar message that product was not found
                    await DialogHost.Show(new SnackbarMessageDialog
                    {
                        Message = Application.Current.TryFindResource("ProductNotFound")?.ToString() ?? "Product not found"
                    }, "RootDialog");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error searching by barcode: {ex.Message}");
                await DialogHost.Show(new SnackbarMessageDialog
                {
                    Message = Application.Current.TryFindResource("ErrorSearchingProduct")?.ToString() ?? "Error searching product"
                }, "RootDialog");
            }
            finally
            {
                BarcodeSearch = string.Empty;
            }
        }

        private void OpenProductSelection()
        {
            var productSelectionWindow = new ProductSelectionWindow();
            
            // Set owner to the current window for proper modal behavior
            if (Application.Current.MainWindow != null)
            {
                productSelectionWindow.Owner = Application.Current.MainWindow;
            }

            if (productSelectionWindow.ShowDialog() == true)
            {
                var selectedProduct = productSelectionWindow.SelectedProduct;
                if (selectedProduct != null)
                {
                    SelectedProduct = selectedProduct;
                    IsProductFilterEnabled = true;
                    _ = LoadDataAsync();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public class TopProductItem
        {
            public Product Product { get; set; }
            public decimal TotalQuantity { get; set; } // ✅ WEIGHT-BASED FIX: Changed from int to decimal to support weight-based products
            public decimal TotalSales { get; set; }
        }

        public class TrendPeriodItem
        {
            private string _resourceKey;
            private string _displayName;

            public string ResourceKey
            {
                get => _resourceKey;
                set
                {
                    _resourceKey = value;
                    // Update display name when resource key changes
                    DisplayName = Application.Current.TryFindResource(_resourceKey)?.ToString() ?? _resourceKey;
                }
            }

            public string DisplayName
            {
                get => _displayName;
                private set => _displayName = value;
            }
        }
    }
} 