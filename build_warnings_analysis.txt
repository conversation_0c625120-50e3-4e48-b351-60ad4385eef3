﻿Build started 7/9/2025 2:17:08 PM.
     1>Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" on node 1 (Restore target(s)).
     1>ValidateSolutionConfiguration:
         Building solution configuration "Debug|Any CPU".
       _GetAllRestoreProjectPathItems:
         Determining projects to restore...
     1>Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (1) is building "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (2:4) on node 1 (_GenerateRestoreGraphProjectEntry target(s)).
     2>C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\targets\Microsoft.NET.Sdk.FrameworkReferenceResolution.targets(65,5): warning NETSDK1086: A FrameworkReference for 'Microsoft.WindowsDesktop.App.WPF' was included in the project. This is implicitly referenced by the .NET SDK and you do not typically need to reference it from your project. For more information, see https://aka.ms/sdkimplicitrefs [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj]
     2>Done Building Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (_GenerateRestoreGraphProjectEntry target(s)).
     1>Restore:
         X.509 certificate chain validation will use the default trust store selected by .NET for code signing.
         X.509 certificate chain validation will use the default trust store selected by .NET for timestamping.
     1>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.DependencyInjection (>= 8.0.2) but Microsoft.Extensions.DependencyInjection 8.0.2 was not found. Microsoft.Extensions.DependencyInjection 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
     1>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Hosting (>= 8.0.2) but Microsoft.Extensions.Hosting 8.0.2 was not found. Microsoft.Extensions.Hosting 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
     1>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Logging (>= 8.0.2) but Microsoft.Extensions.Logging 8.0.2 was not found. Microsoft.Extensions.Logging 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
     1>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
     1>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts.Wpf 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
         Assets file has not changed. Skipping assets file writing. Path: D:\Programs\Programming Projects\Ai Projects\POSSystem\obj\project.assets.json
         Restored D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj (in 98 ms).
         
         NuGet Config files used:
             C:\Users\<USER>\AppData\Roaming\NuGet\NuGet.Config
             C:\Program Files (x86)\NuGet\Config\Microsoft.VisualStudio.FallbackLocation.config
             C:\Program Files (x86)\NuGet\Config\Microsoft.VisualStudio.Offline.config
         
         Feeds used:
             https://api.nuget.org/v3/index.json
             C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\
         All projects are up-to-date for restore.
     1>Done Building Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (Restore target(s)).
   1:2>Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" on node 1 (default targets).
     1>ValidateSolutionConfiguration:
         Building solution configuration "Debug|Any CPU".
   1:2>Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (1:2) is building "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (2:6) on node 1 (default targets).
     2>C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\targets\Microsoft.NET.Sdk.FrameworkReferenceResolution.targets(65,5): warning NETSDK1086: A FrameworkReference for 'Microsoft.WindowsDesktop.App.WPF' was included in the project. This is implicitly referenced by the .NET SDK and you do not typically need to reference it from your project. For more information, see https://aka.ms/sdkimplicitrefs [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj]
     2>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.DependencyInjection (>= 8.0.2) but Microsoft.Extensions.DependencyInjection 8.0.2 was not found. Microsoft.Extensions.DependencyInjection 9.0.0 was resolved instead.
     2>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Hosting (>= 8.0.2) but Microsoft.Extensions.Hosting 8.0.2 was not found. Microsoft.Extensions.Hosting 9.0.0 was resolved instead.
     2>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Logging (>= 8.0.2) but Microsoft.Extensions.Logging 8.0.2 was not found. Microsoft.Extensions.Logging 9.0.0 was resolved instead.
     2>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.
     2>D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts.Wpf 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.
     2>MainResourcesGeneration:
       Skipping target "MainResourcesGeneration" because all output files are up-to-date with respect to the input files.
       _GenerateSourceLinkFile:
         Source Link is empty, file 'obj\Debug\net8.0-windows\POSSystem.sourcelink.json' does not exist.
       CoreCompile:
       Skipping target "CoreCompile" because all output files are up-to-date with respect to the input files.
       _CreateAppHost:
       Skipping target "_CreateAppHost" because all output files are up-to-date with respect to the input files.
       _CopyOutOfDateSourceItemsToOutputDirectory:
       Skipping target "_CopyOutOfDateSourceItemsToOutputDirectory" because all output files are up-to-date with respect to the input files.
       _CopyAppConfigFile:
       Skipping target "_CopyAppConfigFile" because all output files are up-to-date with respect to the input files.
       GenerateBuildDependencyFile:
       Skipping target "GenerateBuildDependencyFile" because all output files are up-to-date with respect to the input files.
       GenerateBuildRuntimeConfigurationFiles:
       Skipping target "GenerateBuildRuntimeConfigurationFiles" because all output files are up-to-date with respect to the input files.
       CopyFilesToOutputDirectory:
         POSSystem -> D:\Programs\Programming Projects\Ai Projects\POSSystem\bin\Debug\net8.0-windows\POSSystem.dll
     2>Done Building Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (default targets).
     1>Done Building Project "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (default targets).

Build succeeded.

       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (Restore target) (1) ->
       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (_GenerateRestoreGraphProjectEntry target) (2:4) ->
       (ProcessFrameworkReferences target) -> 
         C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\targets\Microsoft.NET.Sdk.FrameworkReferenceResolution.targets(65,5): warning NETSDK1086: A FrameworkReference for 'Microsoft.WindowsDesktop.App.WPF' was included in the project. This is implicitly referenced by the .NET SDK and you do not typically need to reference it from your project. For more information, see https://aka.ms/sdkimplicitrefs [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj]


       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (Restore target) (1) ->
       (Restore target) -> 
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.DependencyInjection (>= 8.0.2) but Microsoft.Extensions.DependencyInjection 8.0.2 was not found. Microsoft.Extensions.DependencyInjection 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Hosting (>= 8.0.2) but Microsoft.Extensions.Hosting 8.0.2 was not found. Microsoft.Extensions.Hosting 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Logging (>= 8.0.2) but Microsoft.Extensions.Logging 8.0.2 was not found. Microsoft.Extensions.Logging 9.0.0 was resolved instead. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts.Wpf 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project. [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln]


       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (default target) (1:2) ->
       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (default target) (2:6) ->
       (ProcessFrameworkReferences target) -> 
         C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\targets\Microsoft.NET.Sdk.FrameworkReferenceResolution.targets(65,5): warning NETSDK1086: A FrameworkReference for 'Microsoft.WindowsDesktop.App.WPF' was included in the project. This is implicitly referenced by the .NET SDK and you do not typically need to reference it from your project. For more information, see https://aka.ms/sdkimplicitrefs [D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj]


       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.sln" (default target) (1:2) ->
       "D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj" (default target) (2:6) ->
       (ResolvePackageAssets target) -> 
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.DependencyInjection (>= 8.0.2) but Microsoft.Extensions.DependencyInjection 8.0.2 was not found. Microsoft.Extensions.DependencyInjection 9.0.0 was resolved instead.
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Hosting (>= 8.0.2) but Microsoft.Extensions.Hosting 8.0.2 was not found. Microsoft.Extensions.Hosting 9.0.0 was resolved instead.
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1603: POSSystem depends on Microsoft.Extensions.Logging (>= 8.0.2) but Microsoft.Extensions.Logging 8.0.2 was not found. Microsoft.Extensions.Logging 9.0.0 was resolved instead.
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.
         D:\Programs\Programming Projects\Ai Projects\POSSystem\POSSystem.csproj : warning NU1701: Package 'LiveCharts.Wpf 0.9.7' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.

    12 Warning(s)
    0 Error(s)

Time Elapsed 00:00:02.19
