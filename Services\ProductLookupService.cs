using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class ProductLookupService : IProductLookupService
    {
        private readonly string _connectionString;
        private readonly IAlertService _alertService;

        public ProductLookupService(IAlertService alertService)
        {
            _connectionString = "Data Source=productsDB.db;Version=3;";
            _alertService = alertService;
        }

        public async Task<Product> LookupProductByBarcodeAsync(string barcode)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // First check if the database exists and has the required tables
                    var command = connection.CreateCommand();
                    command.CommandText = @"
                        SELECT COUNT(*) FROM sqlite_master 
                        WHERE type='table' AND name IN ('Products', 'ProductBarcodes')";
                    
                    var tableCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                    if (tableCount < 2)
                    {
                        _alertService.ShowError("Products database not found or not properly initialized");
                        return null;
                    }

                    // Query the product with barcode
                    command = connection.CreateCommand();
                    command.CommandText = @"
                        SELECT 
                            p.Name,
                            p.Description,
                            p.Brand,
                            p.Categories,
                            p.PurchasePrice,
                            p.SellingPrice,
                            p.ImageData,
                            pb.Barcode,
                            pb.IsPrimary,
                            pb.Description as BarcodeDescription
                        FROM Products p
                        INNER JOIN ProductBarcodes pb ON p.Id = pb.ProductId
                        WHERE pb.Barcode = @barcode
                        AND p.IsActive = 1";
                    
                    command.Parameters.AddWithValue("@barcode", barcode);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            var product = new Product
                            {
                                Name = !reader.IsDBNull(0) ? reader.GetString(0) : string.Empty,
                                Description = !reader.IsDBNull(1) ? reader.GetString(1) : string.Empty,
                                Supplier = new Supplier 
                                { 
                                    Name = !reader.IsDBNull(2) ? reader.GetString(2) : string.Empty // Brand
                                },
                                PurchasePrice = !reader.IsDBNull(4) ? reader.GetDecimal(4) : 0m,
                                SellingPrice = !reader.IsDBNull(5) ? reader.GetDecimal(5) : 0m,
                                ImageData = !reader.IsDBNull(6) ? reader.GetString(6) : null,
                                IsActive = true,
                                Barcodes = new System.Collections.Generic.List<ProductBarcode>
                                {
                                    new ProductBarcode 
                                    { 
                                        Barcode = !reader.IsDBNull(7) ? reader.GetString(7) : barcode,
                                        IsPrimary = !reader.IsDBNull(8) && Convert.ToBoolean(reader.GetInt32(8)),
                                        Description = !reader.IsDBNull(9) ? reader.GetString(9) : "Imported from products database",
                                        CreatedAt = DateTime.Now
                                    }
                                }
                            };

                            // Set category if available
                            if (!reader.IsDBNull(3))
                            {
                                var categoryName = reader.GetString(3);
                                product.Category = new Category { Name = categoryName };
                            }

                            return product;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error looking up product: {ex.Message}");
                return null;
            }
        }

        // Interface implementation methods
        public Product FindProductByBarcode(string barcode)
        {
            return LookupProductByBarcodeAsync(barcode).Result;
        }

        public List<Product> SearchProducts(string searchTerm)
        {
            // Simple implementation - in a real scenario, this would search the database
            try
            {
                using (var context = new POSSystem.Data.POSDbContext())
                {
                    return context.Products
                        .Where(p => p.Name.Contains(searchTerm) ||
                                   p.Description.Contains(searchTerm) ||
                                   p.SKU.Contains(searchTerm))
                        .Take(50) // Limit results
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error searching products: {ex.Message}");
                return new List<Product>();
            }
        }

        public Product FindProductBySku(string sku)
        {
            try
            {
                using (var context = new POSSystem.Data.POSDbContext())
                {
                    return context.Products.FirstOrDefault(p => p.SKU == sku);
                }
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error finding product by SKU: {ex.Message}");
                return null;
            }
        }
    }
}