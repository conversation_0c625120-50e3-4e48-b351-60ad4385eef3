<UserControl x:Class="POSSystem.Views.Dialogs.DatabaseMigrationDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="600" Height="500">
    
    <materialDesign:Card Margin="16" Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="Database Migration Tool" 
                          Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="Migrate product data from legacy pos2.db to current pos.db" 
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>

            <!-- Database Status -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <materialDesign:Card Grid.Row="0" Grid.Column="0" Margin="0,0,5,10" Padding="16">
                    <StackPanel>
                        <TextBlock Text="Legacy Database (pos2.db)" FontWeight="Bold"/>
                        <TextBlock x:Name="txtLegacyStatus" Text="Checking..." Margin="0,5,0,0"/>
                        <TextBlock x:Name="txtLegacyProductCount" Text="" Margin="0,2,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Grid.Row="0" Grid.Column="1" Margin="5,0,0,10" Padding="16">
                    <StackPanel>
                        <TextBlock Text="Current Database (pos.db)" FontWeight="Bold"/>
                        <TextBlock x:Name="txtCurrentStatus" Text="Checking..." Margin="0,5,0,0"/>
                        <TextBlock x:Name="txtCurrentProductCount" Text="" Margin="0,2,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Migration Options -->
            <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Migration Options" FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <CheckBox x:Name="chkCreateBackup" Content="Create backup before migration" 
                             IsChecked="True" Margin="0,5"/>
                    <CheckBox x:Name="chkSkipExisting" Content="Skip products that already exist" 
                             IsChecked="True" Margin="0,5"/>
                    <CheckBox x:Name="chkValidateIntegrity" Content="Validate data integrity after migration" 
                             IsChecked="True" Margin="0,5"/>
                    
                    <TextBlock Text="Default category for products without category:" 
                              Margin="0,10,0,5"/>
                    <TextBox x:Name="txtDefaultCategory" Text="Migrated Items" 
                            materialDesign:HintAssist.Hint="Category name"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Progress Section -->
            <materialDesign:Card Grid.Row="3" Padding="16" Margin="0,0,0,20" 
                                x:Name="progressCard" Visibility="Collapsed">
                <StackPanel>
                    <TextBlock x:Name="txtProgressStatus" Text="Starting migration..." 
                              FontWeight="Bold" Margin="0,0,0,10"/>
                    <ProgressBar x:Name="progressBar" Height="8" 
                                materialDesign:TransitionAssist.DisableTransitions="True"/>
                    <ScrollViewer x:Name="scrollProgress" Height="100" Margin="0,10,0,0"
                                 VerticalScrollBarVisibility="Auto">
                        <TextBlock x:Name="txtProgressLog" FontFamily="Consolas" FontSize="11"
                                  Background="{DynamicResource MaterialDesignCardBackground}"/>
                    </ScrollViewer>
                </StackPanel>
            </materialDesign:Card>

            <!-- Buttons -->
            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="btnStartMigration" Content="START MIGRATION" 
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,10,0" Click="StartMigration_Click"
                       Background="{DynamicResource PrimaryHueMidBrush}"/>
                <Button x:Name="btnClose" Content="CLOSE" 
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="Close_Click"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
