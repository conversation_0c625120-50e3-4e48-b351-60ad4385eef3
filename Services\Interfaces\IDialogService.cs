using System.Threading.Tasks;

namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for dialog services
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// Shows an information message dialog
        /// </summary>
        /// <param name="message">Message to display</param>
        /// <param name="title">Dialog title</param>
        void ShowMessage(string message, string title = "Information");

        /// <summary>
        /// Shows an error message dialog
        /// </summary>
        /// <param name="message">Error message to display</param>
        /// <param name="title">Dialog title</param>
        void ShowError(string message, string title = "Error");

        /// <summary>
        /// Shows a warning message dialog
        /// </summary>
        /// <param name="message">Warning message to display</param>
        /// <param name="title">Dialog title</param>
        void ShowWarning(string message, string title = "Warning");

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        /// <param name="message">Confirmation message</param>
        /// <param name="title">Dialog title</param>
        /// <returns>True if user confirmed, false otherwise</returns>
        bool ShowConfirmation(string message, string title = "Confirm");

        /// <summary>
        /// Shows an input dialog
        /// </summary>
        /// <param name="message">Input prompt message</param>
        /// <param name="title">Dialog title</param>
        /// <param name="defaultValue">Default input value</param>
        /// <returns>User input or null if cancelled</returns>
        string ShowInput(string message, string title = "Input", string defaultValue = "");

        /// <summary>
        /// Shows an information message dialog asynchronously
        /// </summary>
        /// <param name="message">Message to display</param>
        /// <param name="title">Dialog title</param>
        Task ShowMessageAsync(string message, string title = "Information");

        /// <summary>
        /// Shows a confirmation dialog asynchronously
        /// </summary>
        /// <param name="message">Confirmation message</param>
        /// <param name="title">Dialog title</param>
        /// <returns>True if user confirmed, false otherwise</returns>
        Task<bool> ShowConfirmationAsync(string message, string title = "Confirm");

        /// <summary>
        /// Shows a dialog using the main application DialogHost
        /// </summary>
        /// <param name="content">The content to display in the dialog</param>
        /// <returns>The dialog result</returns>
        Task<object> ShowDialog(object content);

        /// <summary>
        /// Closes the currently open dialog
        /// </summary>
        void CloseDialog();

        /// <summary>
        /// Closes the dialog with a specific result
        /// </summary>
        /// <param name="result">Result to return from the dialog</param>
        void CloseDialogWithResult(object result);
    }
}
