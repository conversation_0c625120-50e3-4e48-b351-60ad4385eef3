using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Tests
{
    [TestClass]
    public class BulkPricingIntegrationTests
    {
        private POSDbContext _context;
        private UnifiedDataService _dataService;
        private Product _testProduct;

        [TestInitialize]
        public async Task Setup()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            
            // Create mock services for UnifiedDataService
            var mockProductService = new MockProductManagementService();
            var mockSalesService = new MockSalesManagementService();
            var mockCustomerService = new MockCustomerManagementService();
            var mockUserService = new MockUserManagementService();
            var mockInventoryService = new MockInventoryManagementService();
            var mockDatabaseService = new MockDatabaseService();

            _dataService = new UnifiedDataService(
                mockProductService,
                mockSalesService,
                mockCustomerService,
                mockUserService,
                mockInventoryService,
                mockDatabaseService
            );

            await SetupTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _context?.Dispose();
        }

        [TestMethod]
        public async Task AddPriceTierAsync_ValidTier_SavesSuccessfully()
        {
            // Arrange
            var priceTier = new ProductPriceTier
            {
                ProductId = _testProduct.Id,
                MinimumQuantity = 15m,
                UnitPrice = 0.70m,
                TierName = "15-Unit Bulk",
                IsActive = true
            };

            // Act
            var result = await _dataService.AddPriceTierAsync(priceTier);

            // Assert
            Assert.IsTrue(result > 0);
            
            var savedTier = await _context.ProductPriceTiers.FindAsync(result);
            Assert.IsNotNull(savedTier);
            Assert.AreEqual(priceTier.ProductId, savedTier.ProductId);
            Assert.AreEqual(priceTier.MinimumQuantity, savedTier.MinimumQuantity);
            Assert.AreEqual(priceTier.UnitPrice, savedTier.UnitPrice);
            Assert.AreEqual(priceTier.TierName, savedTier.TierName);
        }

        [TestMethod]
        public async Task GetProductPriceTiersAsync_ExistingProduct_ReturnsOrderedTiers()
        {
            // Act
            var tiers = await _dataService.GetProductPriceTiersAsync(_testProduct.Id);

            // Assert
            Assert.IsNotNull(tiers);
            Assert.AreEqual(3, tiers.Count);
            
            // Should be ordered by minimum quantity
            Assert.AreEqual(5m, tiers[0].MinimumQuantity);
            Assert.AreEqual(10m, tiers[1].MinimumQuantity);
            Assert.AreEqual(20m, tiers[2].MinimumQuantity);
        }

        [TestMethod]
        public async Task UpdatePriceTierAsync_ExistingTier_UpdatesSuccessfully()
        {
            // Arrange
            var tiers = await _dataService.GetProductPriceTiersAsync(_testProduct.Id);
            var tierToUpdate = tiers.First();
            var originalUpdatedAt = tierToUpdate.UpdatedAt;
            
            tierToUpdate.UnitPrice = 0.85m;
            tierToUpdate.TierName = "Updated 5-Pack";

            // Act
            var result = await _dataService.UpdatePriceTierAsync(tierToUpdate);

            // Assert
            Assert.IsTrue(result);
            
            var updatedTier = await _context.ProductPriceTiers.FindAsync(tierToUpdate.Id);
            Assert.IsNotNull(updatedTier);
            Assert.AreEqual(0.85m, updatedTier.UnitPrice);
            Assert.AreEqual("Updated 5-Pack", updatedTier.TierName);
            Assert.IsTrue(updatedTier.UpdatedAt > originalUpdatedAt);
        }

        [TestMethod]
        public async Task DeletePriceTierAsync_ExistingTier_DeletesSuccessfully()
        {
            // Arrange
            var tiers = await _dataService.GetProductPriceTiersAsync(_testProduct.Id);
            var tierToDelete = tiers.First();

            // Act
            var result = await _dataService.DeletePriceTierAsync(tierToDelete.Id);

            // Assert
            Assert.IsTrue(result);
            
            var deletedTier = await _context.ProductPriceTiers.FindAsync(tierToDelete.Id);
            Assert.IsNull(deletedTier);
            
            // Verify remaining tiers
            var remainingTiers = await _dataService.GetProductPriceTiersAsync(_testProduct.Id);
            Assert.AreEqual(2, remainingTiers.Count);
        }

        [TestMethod]
        public async Task GetAllActivePriceTiersAsync_MultipleProducts_ReturnsOnlyActive()
        {
            // Arrange
            // Add an inactive tier
            var inactiveTier = new ProductPriceTier
            {
                ProductId = _testProduct.Id,
                MinimumQuantity = 50m,
                UnitPrice = 0.50m,
                IsActive = false
            };
            _context.ProductPriceTiers.Add(inactiveTier);
            await _context.SaveChangesAsync();

            // Act
            var activeTiers = await _dataService.GetAllActivePriceTiersAsync();

            // Assert
            Assert.IsNotNull(activeTiers);
            Assert.IsTrue(activeTiers.All(t => t.IsActive));
            Assert.IsFalse(activeTiers.Any(t => t.Id == inactiveTier.Id));
        }

        [TestMethod]
        public async Task CalculateBestPricingAsync_ValidProduct_ReturnsCorrectPricing()
        {
            // Arrange
            var quantity = 10m;

            // Act
            var result = await _dataService.CalculateBestPricingAsync(_testProduct.Id, quantity);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(quantity, result.RequestedQuantity);
            Assert.IsNotNull(result.AppliedTier);
            Assert.AreEqual(0.75m, result.EffectiveUnitPrice); // 10-unit tier
            Assert.AreEqual(7.50m, result.TotalPrice);
            Assert.AreEqual(2.50m, result.TotalSavings);
        }

        [TestMethod]
        public async Task GetQuantitySuggestionsAsync_SmallQuantity_ReturnsSuggestions()
        {
            // Arrange
            var currentQuantity = 3m;

            // Act
            var suggestions = await _dataService.GetQuantitySuggestionsAsync(_testProduct.Id, currentQuantity);

            // Assert
            Assert.IsNotNull(suggestions);
            Assert.IsTrue(suggestions.Count > 0);
            
            var firstSuggestion = suggestions.First();
            Assert.IsTrue(firstSuggestion.SuggestedQuantity > currentQuantity);
            Assert.IsTrue(firstSuggestion.TotalSavings > 0);
        }

        [TestMethod]
        public async Task GetProductsWithBulkPricingAsync_ReturnsOnlyProductsWithTiers()
        {
            // Arrange
            // Add a product without bulk pricing
            var regularProduct = new Product
            {
                Name = "Regular Product",
                SellingPrice = 5.00m,
                IsActive = true
            };
            _context.Products.Add(regularProduct);
            await _context.SaveChangesAsync();

            // Act
            var productsWithBulkPricing = await _dataService.GetProductsWithBulkPricingAsync();

            // Assert
            Assert.IsNotNull(productsWithBulkPricing);
            Assert.IsTrue(productsWithBulkPricing.All(p => p.PriceTiers.Any(pt => pt.IsActive)));
            Assert.IsFalse(productsWithBulkPricing.Any(p => p.Id == regularProduct.Id));
        }

        [TestMethod]
        public async Task DeleteProductPriceTiersAsync_ExistingProduct_DeletesAllTiers()
        {
            // Arrange
            var originalTierCount = await _context.ProductPriceTiers
                .CountAsync(pt => pt.ProductId == _testProduct.Id);
            Assert.IsTrue(originalTierCount > 0);

            // Act
            var result = await _dataService.DeleteProductPriceTiersAsync(_testProduct.Id);

            // Assert
            Assert.IsTrue(result);
            
            var remainingTiers = await _context.ProductPriceTiers
                .CountAsync(pt => pt.ProductId == _testProduct.Id);
            Assert.AreEqual(0, remainingTiers);
        }

        [TestMethod]
        public async Task BulkPricingWorkflow_EndToEnd_WorksCorrectly()
        {
            // Arrange - Create a new product
            var newProduct = new Product
            {
                Name = "Workflow Test Product",
                SellingPrice = 2.00m,
                IsActive = true
            };
            _context.Products.Add(newProduct);
            await _context.SaveChangesAsync();

            // Act 1 - Add pricing tiers
            var tier1 = new ProductPriceTier
            {
                ProductId = newProduct.Id,
                MinimumQuantity = 6m,
                UnitPrice = 1.80m,
                TierName = "6-Pack",
                IsActive = true
            };

            var tier2 = new ProductPriceTier
            {
                ProductId = newProduct.Id,
                MinimumQuantity = 12m,
                UnitPrice = 1.60m,
                TierName = "12-Pack",
                IsActive = true
            };

            var tier1Id = await _dataService.AddPriceTierAsync(tier1);
            var tier2Id = await _dataService.AddPriceTierAsync(tier2);

            // Assert 1 - Tiers were added
            Assert.IsTrue(tier1Id > 0);
            Assert.IsTrue(tier2Id > 0);

            // Act 2 - Calculate pricing for different quantities
            var pricing5 = await _dataService.CalculateBestPricingAsync(newProduct.Id, 5m);
            var pricing8 = await _dataService.CalculateBestPricingAsync(newProduct.Id, 8m);
            var pricing15 = await _dataService.CalculateBestPricingAsync(newProduct.Id, 15m);

            // Assert 2 - Correct pricing applied
            Assert.AreEqual(2.00m, pricing5.EffectiveUnitPrice); // Regular price
            Assert.AreEqual(1.80m, pricing8.EffectiveUnitPrice); // 6-pack tier
            Assert.AreEqual(1.60m, pricing15.EffectiveUnitPrice); // 12-pack tier

            // Act 3 - Get suggestions
            var suggestions = await _dataService.GetQuantitySuggestionsAsync(newProduct.Id, 4m);

            // Assert 3 - Suggestions provided
            Assert.IsTrue(suggestions.Count > 0);
            Assert.AreEqual(6m, suggestions.First().SuggestedQuantity);

            // Act 4 - Update tier
            tier1.UnitPrice = 1.75m;
            var updateResult = await _dataService.UpdatePriceTierAsync(tier1);

            // Assert 4 - Update successful
            Assert.IsTrue(updateResult);

            // Act 5 - Verify updated pricing
            var updatedPricing = await _dataService.CalculateBestPricingAsync(newProduct.Id, 8m);
            Assert.AreEqual(1.75m, updatedPricing.EffectiveUnitPrice);
        }

        private async Task SetupTestData()
        {
            // Create test product
            _testProduct = new Product
            {
                Name = "Test Product",
                SellingPrice = 1.00m,
                IsActive = true
            };

            _context.Products.Add(_testProduct);
            await _context.SaveChangesAsync();

            // Create test pricing tiers
            var priceTiers = new List<ProductPriceTier>
            {
                new ProductPriceTier
                {
                    ProductId = _testProduct.Id,
                    MinimumQuantity = 5m,
                    UnitPrice = 0.80m,
                    TierName = "5-Pack",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new ProductPriceTier
                {
                    ProductId = _testProduct.Id,
                    MinimumQuantity = 10m,
                    UnitPrice = 0.75m,
                    TierName = "10-Pack",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new ProductPriceTier
                {
                    ProductId = _testProduct.Id,
                    MinimumQuantity = 20m,
                    UnitPrice = 0.60m,
                    TierName = "Bulk Rate",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            };

            _context.ProductPriceTiers.AddRange(priceTiers);
            await _context.SaveChangesAsync();
        }
    }

    // Mock services for testing (simplified implementations)
    public class MockProductManagementService : IProductManagementService
    {
        public Task<List<Product>> GetAllProductsAsync() => Task.FromResult(new List<Product>());
        public Task<Product> GetProductByIdAsync(int id) => Task.FromResult((Product)null);
        public Task<Product> GetProductByBarcodeAsync(string barcode) => Task.FromResult((Product)null);
        public Task<List<Product>> SearchProductsAsync(string searchTerm, int maxResults = 50) => Task.FromResult(new List<Product>());
        public Task<int> AddProductAsync(Product product) => Task.FromResult(0);
        public Task<bool> UpdateProductAsync(Product product) => Task.FromResult(false);
        public Task<bool> DeleteProductAsync(int id) => Task.FromResult(false);
        public Task<List<Product>> GetLowStockProductsAsync() => Task.FromResult(new List<Product>());
        public Task<List<Product>> GetExpiringProductsAsync(int daysThreshold = 30) => Task.FromResult(new List<Product>());
    }

    public class MockSalesManagementService : ISalesManagementService
    {
        public Task<List<Sale>> GetAllSalesAsync() => Task.FromResult(new List<Sale>());
        public Task<Sale> GetSaleByIdAsync(int id) => Task.FromResult((Sale)null);
        public Task<List<Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate) => Task.FromResult(new List<Sale>());
        public Task<int> SaveSaleAsync(Sale sale) => Task.FromResult(0);
        public Task<bool> UpdateSaleAsync(Sale sale) => Task.FromResult(false);
        public Task<bool> DeleteSaleAsync(int id) => Task.FromResult(false);
        public Task<List<Sale>> GetUnpaidSalesAsync() => Task.FromResult(new List<Sale>());
        public Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate) => Task.FromResult(0m);
    }

    public class MockCustomerManagementService : ICustomerManagementService
    {
        public Task<List<Customer>> GetAllCustomersAsync() => Task.FromResult(new List<Customer>());
        public Task<Customer> GetCustomerByIdAsync(int id) => Task.FromResult((Customer)null);
        public Task<Customer> GetCustomerByEmailAsync(string email) => Task.FromResult((Customer)null);
        public Task<int> AddCustomerAsync(Customer customer) => Task.FromResult(0);
        public Task<bool> UpdateCustomerAsync(Customer customer) => Task.FromResult(false);
        public Task<bool> DeleteCustomerAsync(int id) => Task.FromResult(false);
        public Task<List<Customer>> SearchCustomersAsync(string searchTerm) => Task.FromResult(new List<Customer>());
    }

    public class MockUserManagementService : IUserManagementService
    {
        public Task<List<User>> GetAllUsersAsync() => Task.FromResult(new List<User>());
        public Task<User> GetUserByIdAsync(int id) => Task.FromResult((User)null);
        public Task<User> GetUserByUsernameAsync(string username) => Task.FromResult((User)null);
        public Task<int> AddUserAsync(User user) => Task.FromResult(0);
        public Task<bool> UpdateUserAsync(User user) => Task.FromResult(false);
        public Task<bool> DeleteUserAsync(int id) => Task.FromResult(false);
        public Task<bool> ValidateUserCredentialsAsync(string username, string password) => Task.FromResult(false);
    }

    public class MockInventoryManagementService : IInventoryManagementService
    {
        public Task<List<Category>> GetAllCategoriesAsync() => Task.FromResult(new List<Category>());
        public Task<Category> GetCategoryByIdAsync(int id) => Task.FromResult((Category)null);
        public Task<int> AddCategoryAsync(Category category) => Task.FromResult(0);
        public Task<bool> UpdateCategoryAsync(Category category) => Task.FromResult(false);
        public Task<bool> DeleteCategoryAsync(int id) => Task.FromResult(false);
        public Task<List<UnitOfMeasure>> GetAllUnitsOfMeasureAsync() => Task.FromResult(new List<UnitOfMeasure>());
    }

    public class MockDatabaseService : IDatabaseService
    {
        // Implement required methods with minimal functionality for testing
        public List<Product> GetAllProducts() => new List<Product>();
        public void AddProduct(Product product) { }
        public void UpdateProduct(Product product) { }
        public void DeleteProduct(int id) { }
        public List<Sale> GetAllSales() => new List<Sale>();
        public void AddSale(Sale sale) { }
        public List<Customer> GetAllCustomers() => new List<Customer>();
        public void AddCustomer(Customer customer) { }
        public void UpdateCustomer(Customer customer) { }
        public Customer GetCustomerById(int id) => null;
        public List<User> GetAllUsers() => new List<User>();
        public User GetUserById(int id) => null;
        public List<Category> GetAllCategories() => new List<Category>();
        public Task<List<Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate) => Task.FromResult(new List<Sale>());
        public List<Sale> GetUnpaidSales() => new List<Sale>();
        public POSDbContext Context => null;
        public event EventHandler CategoryUpdated;
    }
}
