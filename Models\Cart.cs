using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Models
{
    public class Cart : INotifyPropertyChanged
    {
        private ObservableCollection<CartItem> _items;
        private Customer _customer;
        private decimal _discountPercentage;
        private decimal _taxRate;
        private DateTime _createdAt;
        private string _status;
        private string _name;
        private bool _isOnHold;
        private DateTime? _heldAt;
        private decimal _tax;
        private int? _customerId;
        private ObservableCollection<Discount> _discounts;
        private bool _isActive;
        private static readonly BulkPricingService _bulkPricingService = new BulkPricingService();

        public decimal PendingLoyaltyPoints { get; set; }
        public decimal PendingLoyaltyDiscount { get; set; }

        public Cart()
        {
            Items = new ObservableCollection<CartItem>();
            Items.CollectionChanged += Items_CollectionChanged;
            CreatedAt = DateTime.Now;
            Status = "Active";
            DiscountPercentage = 0;
            TaxRate = 0; // Set tax rate to 0
            Name = $"Cart {DateTime.Now.Ticks}";
            Discounts = new ObservableCollection<Discount>();
            IsActive = false; // New cart is not active by default
        }

        private void Items_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            UpdateTotals();
            OnPropertyChanged(nameof(Items));
            OnPropertyChanged(nameof(TotalItems));
            OnPropertyChanged("Items.Count"); // Add explicit notification for Items.Count
            OnPropertyChanged("HasItems"); // Add notification for HasItems
        }

        public bool HasItems => Items?.Count > 0;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CartItem> Items
        {
            get => _items;
            set
            {
                if (_items != null)
                {
                    _items.CollectionChanged -= Items_CollectionChanged;
                }
                _items = value;
                if (_items != null)
                {
                    _items.CollectionChanged += Items_CollectionChanged;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(TotalItems));
                OnPropertyChanged("Items.Count"); // Add explicit notification for Items.Count
                OnPropertyChanged("HasItems"); // Add notification for HasItems
                UpdateTotals();
            }
        }

        public Customer Customer
        {
            get => _customer;
            set
            {
                _customer = value;
                OnPropertyChanged();
            }
        }

        public decimal DiscountPercentage
        {
            get => _discountPercentage;
            set
            {
                _discountPercentage = value;
                OnPropertyChanged();
                UpdateTotals();
            }
        }

        public decimal TaxRate
        {
            get => _taxRate;
            set
            {
                _taxRate = value;
                OnPropertyChanged();
                UpdateTotals();
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                _createdAt = value;
                OnPropertyChanged();
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public bool IsOnHold
        {
            get => _isOnHold;
            set
            {
                _isOnHold = value;
                OnPropertyChanged();
            }
        }

        public DateTime? HeldAt
        {
            get => _heldAt;
            set
            {
                _heldAt = value;
                OnPropertyChanged();
            }
        }

        public decimal Subtotal
        {
            get
            {
                decimal total = 0;
                foreach (var item in Items)
                {
                    total += item.Total;
                }
                return total;
            }
            private set
            {
                OnPropertyChanged();
                UpdateDependentTotals();
            }
        }

        private void UpdateDependentTotals()
        {
            OnPropertyChanged(nameof(TaxableAmount));
            OnPropertyChanged(nameof(TaxAmount));
            OnPropertyChanged(nameof(GrandTotal));
        }

        public void UpdateTotals()
        {
            OnPropertyChanged(nameof(Subtotal));
            OnPropertyChanged(nameof(DiscountAmount));
            OnPropertyChanged(nameof(TaxableAmount));
            OnPropertyChanged(nameof(TaxAmount));
            OnPropertyChanged(nameof(GrandTotal));
            OnPropertyChanged(nameof(TotalItems));
            OnPropertyChanged("Items.Count"); // Add explicit notification for Items.Count
        }

        public decimal DiscountAmount
        {
            get
            {
                decimal totalDiscount = 0;
                
                // Calculate item-level discounts
                foreach (var item in Items)
                {
                    totalDiscount += item.DiscountAmount;
                }
                
                // Calculate cart-wide discounts
                if (Discounts != null)
                {
                    foreach (var discount in Discounts)
                    {
                        // DiscountValue already contains the monetary amount for both percentage and fixed discounts
                        totalDiscount += discount.DiscountValue;
                    }
                }

                // Add any pending loyalty discount
                if (PendingLoyaltyDiscount > 0)
                {
                    totalDiscount += PendingLoyaltyDiscount;
                }

                return totalDiscount;
            }
            private set
            {
                OnPropertyChanged();
                UpdateDependentTotals();
            }
        }

        public decimal TaxableAmount
        {
            get => Subtotal - DiscountAmount;
            private set
            {
                OnPropertyChanged();
                UpdateDependentTotals();
            }
        }

        public decimal TaxAmount
        {
            get => TaxableAmount * (TaxRate / 100);
            private set
            {
                OnPropertyChanged();
                UpdateDependentTotals();
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                _tax = value;
                OnPropertyChanged();
            }
        }

        public decimal GrandTotal
        {
            get => Subtotal - DiscountAmount + TaxAmount;
            private set
            {
                OnPropertyChanged();
            }
        }

        public decimal TotalItems => Items.Sum(item => item.Quantity);

        // ===== BULK PRICING PROPERTIES =====

        /// <summary>
        /// Total amount saved due to bulk pricing across all items
        /// </summary>
        public decimal TotalBulkSavings => Items.Sum(item => item.BulkSavings);

        /// <summary>
        /// Total regular price (without bulk pricing) for all items
        /// </summary>
        public decimal TotalRegularPrice => Items.Sum(item => item.RegularTotal);

        /// <summary>
        /// Whether any items in the cart have bulk pricing applied
        /// </summary>
        public bool HasBulkPricing => Items.Any(item => item.HasBulkPricing);

        /// <summary>
        /// Percentage of total savings due to bulk pricing
        /// </summary>
        public decimal BulkSavingsPercentage
        {
            get
            {
                if (TotalRegularPrice > 0)
                    return (TotalBulkSavings / TotalRegularPrice) * 100;
                return 0;
            }
        }

        /// <summary>
        /// Display text for bulk savings
        /// </summary>
        public string BulkSavingsDisplay
        {
            get
            {
                if (HasBulkPricing)
                    return $"Bulk Savings: {TotalBulkSavings:C2} ({BulkSavingsPercentage:F1}%)";
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets all items that have bulk pricing applied
        /// </summary>
        public IEnumerable<CartItem> BulkPricedItems => Items.Where(item => item.HasBulkPricing);

        /// <summary>
        /// Gets the number of items with bulk pricing
        /// </summary>
        public int BulkPricedItemsCount => BulkPricedItems.Count();

        public void AddItem(Product product, decimal quantity = 1)
        {
            var existingItem = Items.FirstOrDefault(i => i.Product.Id == product.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
                // Bulk pricing will be recalculated automatically via the Quantity setter
            }
            else
            {
                var newItem = new CartItem
                {
                    Product = product,
                    Quantity = quantity,
                    UnitPrice = product.SellingPrice
                };

                // Apply bulk pricing to the new item
                newItem.ApplyBulkPricing();

                Items.Add(newItem);
            }
            UpdateTotals();
            OnPropertyChanged("HasItems");
            OnPropertyChanged(nameof(HasBulkPricing));
            OnPropertyChanged(nameof(TotalBulkSavings));
            OnPropertyChanged(nameof(BulkSavingsDisplay));
        }

        public void RemoveItem(CartItem item)
        {
            Items.Remove(item);
            UpdateTotals();
            OnPropertyChanged("HasItems"); // Add notification for HasItems
        }

        public void Clear()
        {
            Items.Clear();
            Customer = null;
            DiscountPercentage = 0;
            ClearDiscounts();
            PendingLoyaltyPoints = 0;
            PendingLoyaltyDiscount = 0;
            UpdateTotals();
            OnPropertyChanged("HasItems"); // Add notification for HasItems
        }

        public int Id { get; set; }
        public int? CustomerId
        {
            get => _customerId;
            set
            {
                _customerId = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Discount> Discounts
        {
            get
            {
                if (_discounts == null)
                {
                    _discounts = new ObservableCollection<Discount>();
                }
                return _discounts;
            }
            set
            {
                _discounts = value;
                OnPropertyChanged();
            }
        }

        public void AddDiscount(Discount discount)
        {
            if (discount == null) return;
            Discounts.Add(discount);
        }

        public void RemoveDiscount(Discount discount)
        {
            if (discount == null) return;
            Discounts.Remove(discount);
        }

        public void ClearDiscounts()
        {
            Discounts.Clear();
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged();
            }
        }

        public void ClearAllDiscounts()
        {
            // Clear item-level discounts
            foreach (var item in Items)
            {
                if (item.Discounts != null)
                {
                    item.Discounts.Clear();
                }
            }
            
            // Clear cart-wide discounts
            if (Discounts != null)
            {
                Discounts.Clear();
            }
            
            // Update totals
            UpdateTotals();
        }

        // ===== BULK PRICING METHODS =====

        /// <summary>
        /// Refreshes bulk pricing for all items in the cart
        /// </summary>
        public void RefreshBulkPricing()
        {
            foreach (var item in Items)
            {
                item.RefreshBulkPricing();
            }

            UpdateTotals();
            OnPropertyChanged(nameof(HasBulkPricing));
            OnPropertyChanged(nameof(TotalBulkSavings));
            OnPropertyChanged(nameof(BulkSavingsDisplay));
        }

        /// <summary>
        /// Gets bulk pricing suggestions for all items in the cart
        /// </summary>
        /// <returns>Dictionary of cart items and their quantity suggestions</returns>
        public Dictionary<CartItem, List<QuantitySuggestion>> GetBulkPricingSuggestions()
        {
            var suggestions = new Dictionary<CartItem, List<QuantitySuggestion>>();

            foreach (var item in Items.Where(i => i.Product?.HasBulkPricing == true))
            {
                var itemSuggestions = item.GetQuantitySuggestions();
                if (itemSuggestions.Any())
                {
                    suggestions[item] = itemSuggestions;
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Calculates the total potential savings if all bulk pricing suggestions are applied
        /// </summary>
        /// <returns>Total potential additional savings</returns>
        public decimal GetPotentialAdditionalSavings()
        {
            decimal potentialSavings = 0;

            var suggestions = GetBulkPricingSuggestions();
            foreach (var kvp in suggestions)
            {
                var item = kvp.Key;
                var itemSuggestions = kvp.Value;

                // Take the best suggestion for each item
                var bestSuggestion = itemSuggestions
                    .OrderByDescending(s => s.TotalSavings)
                    .FirstOrDefault();

                if (bestSuggestion != null)
                {
                    potentialSavings += bestSuggestion.TotalSavings;
                }
            }

            return potentialSavings;
        }

        /// <summary>
        /// Gets a summary of bulk pricing benefits for the entire cart
        /// </summary>
        /// <returns>Bulk pricing summary</returns>
        public CartBulkPricingSummary GetBulkPricingSummary()
        {
            var cartItems = Items.Select(item => (item.Product, item.Quantity));
            var cartResult = BulkPricingCalculator.CalculateCartBulkPricing(cartItems);

            return new CartBulkPricingSummary
            {
                TotalItems = Items.Count,
                BulkPricedItems = BulkPricedItemsCount,
                TotalSavings = TotalBulkSavings,
                SavingsPercentage = BulkSavingsPercentage,
                PotentialAdditionalSavings = GetPotentialAdditionalSavings(),
                HasBulkPricing = HasBulkPricing,
                SuggestionsAvailable = GetBulkPricingSuggestions().Any()
            };
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// Summary of bulk pricing benefits for a cart
    /// </summary>
    public class CartBulkPricingSummary
    {
        public int TotalItems { get; set; }
        public int BulkPricedItems { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal SavingsPercentage { get; set; }
        public decimal PotentialAdditionalSavings { get; set; }
        public bool HasBulkPricing { get; set; }
        public bool SuggestionsAvailable { get; set; }

        public string SummaryText
        {
            get
            {
                if (!HasBulkPricing && !SuggestionsAvailable)
                    return "No bulk pricing available";

                var text = $"{BulkPricedItems} of {TotalItems} items have bulk pricing";

                if (TotalSavings > 0)
                    text += $" (Save {TotalSavings:C2})";

                if (PotentialAdditionalSavings > 0)
                    text += $". Additional {PotentialAdditionalSavings:C2} savings available";

                return text;
            }
        }
    }
}