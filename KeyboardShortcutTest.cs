using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Input;
using POSSystem.ViewModels;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test class to verify keyboard shortcut functionality in SalesViewGrid
    /// </summary>
    public class KeyboardShortcutTest
    {
        private SaleViewModel _viewModel;

        public KeyboardShortcutTest()
        {
            try
            {
                _viewModel = new SaleViewModel();
                Debug.WriteLine("[TEST] SaleViewModel created successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[TEST] Error creating SaleViewModel: {ex.Message}");
            }
        }
        
        public void TestAllShortcuts()
        {
            Debug.WriteLine("=== KEYBOARD SHORTCUT TEST ===");
            
            // Test Search Commands
            TestCommand("F3 (Search)", _viewModel.SearchCommand);
            TestCommand("Ctrl+F (Search)", _viewModel.SearchCommand);
            
            // Test Payment Commands  
            TestCommand("F4 (Payment)", _viewModel.ProcessPaymentCommand);
            TestCommand("Ctrl+Enter (Payment)", _viewModel.ProcessPaymentCommand);
            
            // Test Cart Management Commands
            TestCommand("Delete (Remove from Cart)", _viewModel.RemoveFromCartCommand);
            TestCommand("+ (Increase Quantity)", _viewModel.IncreaseQuantityCommand);
            TestCommand("- (Decrease Quantity)", _viewModel.DecreaseQuantityCommand);
            
            // Test Customer Commands
            TestCommand("F5 (Redeem Points)", _viewModel.RedeemPointsCommand);
            
            // Test Misc Commands
            TestCommand("Escape (Clear Search)", _viewModel.ClearSearchCommand);
            TestCommand("Ctrl+N (New Cart)", _viewModel.CreateNewCartCommand);
            
            Debug.WriteLine("=== TEST COMPLETE ===");
        }
        
        private void TestCommand(string shortcutName, ICommand command)
        {
            try
            {
                if (command == null)
                {
                    Debug.WriteLine($"❌ {shortcutName}: Command is NULL");
                    return;
                }
                
                bool canExecute = command.CanExecute(null);
                Debug.WriteLine($"{(canExecute ? "✅" : "⚠️")} {shortcutName}: CanExecute = {canExecute}");
                
                if (canExecute)
                {
                    try
                    {
                        command.Execute(null);
                        Debug.WriteLine($"   ✅ {shortcutName}: Execute succeeded");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"   ❌ {shortcutName}: Execute failed - {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ {shortcutName}: Test failed - {ex.Message}");
            }
        }
        
        public void TestFocusAndEventHandling()
        {
            Debug.WriteLine("=== FOCUS AND EVENT HANDLING TEST ===");
            
            // Test if the UserControl is properly configured for keyboard input
            Debug.WriteLine("Checking UserControl configuration:");
            Debug.WriteLine("- Focusable: Should be True");
            Debug.WriteLine("- IsFocusScope: Should be True");
            Debug.WriteLine("- InputBindings: Should contain 10 key bindings");
            
            // Test specific problematic scenarios
            Debug.WriteLine("\nTesting problematic scenarios:");
            Debug.WriteLine("1. Testing when search box has focus");
            Debug.WriteLine("2. Testing when cart item is selected");
            Debug.WriteLine("3. Testing when no cart items exist");
            Debug.WriteLine("4. Testing when dialog is open");
            
            Debug.WriteLine("=== FOCUS TEST COMPLETE ===");
        }
    }
}
