using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class ComboBoxItemToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return new ComboBoxItem { Content = stringValue };
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ComboBoxItem comboBoxItem)
            {
                return comboBoxItem.Content?.ToString();
            }
            return null;
        }
    }
} 