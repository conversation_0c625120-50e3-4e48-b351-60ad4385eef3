using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Input;
using System.Security.Cryptography;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Windows.Media;
using POSLicensingSystem.Utilities;

namespace POSLicensingSystem.ViewModels
{
    public class TestLicensingViewModel : ViewModelBase
    {
        // Generation properties
        private string _businessName = "Test Business";
        private string _systemId = string.Empty;
        private DateTime _expirationDate = DateTime.Now.AddYears(1);
        private string _generatedKey = string.Empty;
        private bool _useHexFormat = true; // Default to hex format for POSSystem compatibility
        
        // Validation properties
        private string _validationBusinessName = "Test Business";
        private string _validationSystemId = string.Empty;
        private string _licenseKey = string.Empty;
        private string _validationResult = "Not validated yet";
        private Brush _validationResultColor = Brushes.LightGray;
        private Brush _validationTextColor = Brushes.Black;
        private string _debugOutput = string.Empty;
        
        // Generation properties
        public string BusinessName
        {
            get => _businessName;
            set => SetProperty(ref _businessName, value);
        }
        
        public string SystemId
        {
            get => _systemId;
            set => SetProperty(ref _systemId, value);
        }
        
        public DateTime ExpirationDate
        {
            get => _expirationDate;
            set => SetProperty(ref _expirationDate, value);
        }
        
        public string GeneratedKey
        {
            get => _generatedKey;
            set => SetProperty(ref _generatedKey, value);
        }
        
        public bool UseHexFormat
        {
            get => _useHexFormat;
            set => SetProperty(ref _useHexFormat, value);
        }
        
        // Validation properties
        public string ValidationBusinessName
        {
            get => _validationBusinessName;
            set => SetProperty(ref _validationBusinessName, value);
        }
        
        public string ValidationSystemId
        {
            get => _validationSystemId;
            set => SetProperty(ref _validationSystemId, value);
        }
        
        public string LicenseKey
        {
            get => _licenseKey;
            set => SetProperty(ref _licenseKey, value);
        }
        
        public string ValidationResult
        {
            get => _validationResult;
            set => SetProperty(ref _validationResult, value);
        }
        
        public Brush ValidationResultColor
        {
            get => _validationResultColor;
            set => SetProperty(ref _validationResultColor, value);
        }
        
        public Brush ValidationTextColor
        {
            get => _validationTextColor;
            set => SetProperty(ref _validationTextColor, value);
        }
        
        public string DebugOutput
        {
            get => _debugOutput;
            set => SetProperty(ref _debugOutput, value);
        }
        
        // Commands
        public ICommand GenerateTestKeyCommand { get; }
        public ICommand ValidateTestKeyCommand { get; }
        public ICommand CopyFromGeneratedCommand { get; }
        public ICommand CloseCommand { get; }
        public ICommand RunDiagnosticsCommand { get; }
        public ICommand ClearDebugOutputCommand { get; }
        
        public TestLicensingViewModel()
        {
            // Generate a hardware ID if empty
            if (string.IsNullOrEmpty(SystemId))
            {
                SystemId = HardwareInfo.GetSystemId();
                ValidationSystemId = SystemId;
            }
            
            // Initialize commands
            GenerateTestKeyCommand = new RelayCommand(_ => ExecuteGenerateTestKey());
            ValidateTestKeyCommand = new RelayCommand(_ => ExecuteValidateTestKey(), _ => !string.IsNullOrEmpty(LicenseKey));
            CopyFromGeneratedCommand = new RelayCommand(_ => ExecuteCopyFromGenerated(), _ => !string.IsNullOrEmpty(GeneratedKey));
            CloseCommand = new RelayCommand(_ => Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive)?.Close());
            RunDiagnosticsCommand = new RelayCommand(_ => ExecuteRunDiagnostics());
            ClearDebugOutputCommand = new RelayCommand(_ => DebugOutput = string.Empty);
        }
        
        private void ExecuteGenerateTestKey()
        {
            try
            {
                LogDebug("=== GENERATING TEST LICENSE KEY ===");
                LogDebug($"Business Name: {BusinessName}");
                LogDebug($"System ID: {SystemId}");
                LogDebug($"Expiration Date: {ExpirationDate:d}");
                
                // Create a license data object
                var licenseData = new Dictionary<string, object>
                {
                    { "BusinessName", BusinessName.Trim() },
                    { "ExpirationDate", ExpirationDate },
                    { "Type", 1 }, // Professional license
                    { "TerminalCount", 2 }, // 2 terminals
                    { "HardwareId", SystemId.Trim() }
                };
                
                // Convert to JSON
                string jsonLicense = JsonSerializer.Serialize(licenseData);
                LogDebug($"JSON: {jsonLicense}");
                
                // Generate POSSystem compatible hex-encoded license key
                if (UseHexFormat)
                {
                    // POSSystem format: HEX encoded data + salt + HMAC signature
                    const string SALT = "POSSystem2024";
                    
                    // Add salt to JSON (POSSystem expects this)
                    string jsonWithSalt = jsonLicense + SALT;
                    LogDebug($"JSON with salt: {jsonWithSalt}");
                    
                    // Convert to bytes
                    byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonWithSalt);
                    
                    // Convert to hex string
                    string jsonHex = BitConverter.ToString(jsonBytes).Replace("-", "");
                    LogDebug($"JSON as Hex: {jsonHex}");
                    
                    // Calculate signature using POSSystem's approach
                    string secretKey = GeneratePOSSystemSecretKey();
                    LogDebug($"Secret key: {secretKey}");
                    
                    // Calculate HMAC of the data using the secret key
                    byte[] keyBytes = Encoding.UTF8.GetBytes(secretKey);
                    using (var hmac = new HMACSHA256(keyBytes))
                    {
                        byte[] signatureBytes = hmac.ComputeHash(jsonBytes);
                        string signatureHex = BitConverter.ToString(signatureBytes).Replace("-", "");
                        LogDebug($"Signature: {signatureHex}");
                        
                        // Final license key: data hex + signature hex
                        string licenseKey = jsonHex + signatureHex;
                        
                        // Format with dashes for readability
                        var formattedKey = new StringBuilder();
                        for (int i = 0; i < licenseKey.Length; i += 5)
                        {
                            if (i > 0)
                                formattedKey.Append('-');
                            
                            // Handle the case where we're at the end and have fewer than 5 characters left
                            int charsToTake = Math.Min(5, licenseKey.Length - i);
                            formattedKey.Append(licenseKey.Substring(i, charsToTake));
                        }
                        
                        GeneratedKey = formattedKey.ToString();
                        LogDebug($"Final HEX license key: {GeneratedKey}");
                        LogDebug($"Key length: {GeneratedKey.Length} characters");
                    }
                }
                else
                {
                    // Original Base64 format with marker
                    // Convert JSON to bytes and then to Base64 string
                    byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonLicense);
                    string jsonBase64 = Convert.ToBase64String(jsonBytes);
                    LogDebug($"JSON as Base64: {jsonBase64}");
                    
                    // Add a signature marker
                    const string SIGNATURE_MARKER = "##POS2024##";
                    
                    // Calculate signature for the license data
                    string signature = CalculateSignature(jsonLicense);
                    LogDebug($"Added signature: {signature}");
                    
                    // Combine all parts
                    string licenseKey = jsonBase64 + SIGNATURE_MARKER + signature;
                    
                    // Format with dashes for readability
                    var formattedKey = new StringBuilder();
                    for (int i = 0; i < licenseKey.Length; i += 5)
                    {
                        if (i > 0)
                            formattedKey.Append('-');
                        
                        // Handle the case where we're at the end and have fewer than 5 characters left
                        int charsToTake = Math.Min(5, licenseKey.Length - i);
                        formattedKey.Append(licenseKey.Substring(i, charsToTake));
                    }
                    
                    GeneratedKey = formattedKey.ToString();
                    LogDebug($"Final Base64 license key: {GeneratedKey}");
                    LogDebug($"Key length: {GeneratedKey.Length} characters");
                }
                
                LogDebug("=== GENERATION COMPLETE ===");
            }
            catch (Exception ex)
            {
                LogDebug($"ERROR: {ex.Message}");
                MessageBox.Show($"Error generating test key: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        // Generate the secret key using the same algorithm as POSSystem
        private string GeneratePOSSystemSecretKey()
        {
            try
            {
                // POSSystem uses these constants
                const string ASSEMBLY_SEED = "POSSystem_v1.0";
                const string SALT = "POSSystem2024";
                
                // Combine the assembly info with the license data salt
                string combinedData = $"{ASSEMBLY_SEED}:{SALT}";
                
                // Create a SHA256 hash of the combined data
                byte[] hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(combinedData));
                
                // Convert to base64 
                string base64Hash = Convert.ToBase64String(hashBytes);
                
                // Make it more complex with a fixed pattern
                return $"{base64Hash.Substring(0, 16)}!{base64Hash.Substring(16, 8)}#";
            }
            catch (Exception ex)
            {
                LogDebug($"Error generating POSSystem secret key: {ex.Message}");
                // Fallback with reduced security
                return "POSSystem_SecretKey!2024#";
            }
        }
        
        private void ExecuteValidateTestKey()
        {
            try
            {
                LogDebug("=== VALIDATING LICENSE KEY ===");
                LogDebug($"Business Name: {ValidationBusinessName}");
                LogDebug($"System ID: {ValidationSystemId}");
                LogDebug($"License Key: {LicenseKey}");
                
                // Analyze the license key format first
                string licenseReport = DiagnosticTools.AnalyzeLicenseKey(LicenseKey);
                LogDebug("License Format Analysis:");
                LogDebug(licenseReport);
                
                // Check if this is potentially a hex-encoded key from POSSystem
                string cleanedKey = Regex.Replace(LicenseKey, "[-\\s]", "");
                bool isHexKey = Regex.IsMatch(cleanedKey, "^[0-9A-Fa-f]+$");
                
                if (isHexKey && !cleanedKey.Contains("##POS2024##"))
                {
                    LogDebug("Detected potential POSSystem hex-encoded license key");
                    LogDebug("Using hex license validation logic...");
                }
                
                // Try to validate the license
                bool isValid = ValidateLicense();
                
                if (isValid)
                {
                    ValidationResult = "✓ License key is valid";
                    ValidationResultColor = Brushes.LightGreen;
                    ValidationTextColor = Brushes.DarkGreen;
                    
                    // Show additional debug information in success case
                    LogDebug("License validation successful!");
                    LogDebug("License data matches expected values:");
                    LogDebug($"- Business Name: {ValidationBusinessName}");
                    LogDebug($"- Hardware ID: {ValidationSystemId}");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"ERROR: {ex.Message}");
                ValidationResult = $"× Error: {ex.Message}";
                ValidationResultColor = Brushes.MistyRose;
                ValidationTextColor = Brushes.Red;
            }
        }
        
        private void ExecuteCopyFromGenerated()
        {
            try
            {
                LicenseKey = GeneratedKey;
                LogDebug("Copied generated key to validation field");
            }
            catch (Exception ex)
            {
                LogDebug($"ERROR: {ex.Message}");
                MessageBox.Show($"Error copying key: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private bool ValidateLicense()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(LicenseKey))
                {
                    LogDebug("License key is empty");
                    ValidationResult = "× License key is empty";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
            
                // Remove dashes from the license key
                string cleanedKey = Regex.Replace(LicenseKey, "-", "");
                LogDebug($"Cleaned license key: {cleanedKey}");
                LogDebug($"Key length: {cleanedKey.Length}");

                // Check the format of the license key
                bool isHexFormat = IsHexString(cleanedKey);
                bool containsBase64Marker = cleanedKey.Contains("##POS2024##");
                bool containsHexMarker = cleanedKey.Contains("504F5353797374656D32303234"); // "POSSystem2024" in hex
                
                LogDebug($"Is hex format: {isHexFormat}");
                LogDebug($"Contains Base64 marker: {containsBase64Marker}");
                LogDebug($"Contains hex marker: {containsHexMarker}");
                
                // First, check for hex format license key (POSSystem compatible)
                if (isHexFormat && containsHexMarker)
                {
                    LogDebug("Using hex format validation logic...");
                    return ValidateHexLicense();
                }
                // Then check for Base64 format with marker
                else if (containsBase64Marker)
                {
                    LogDebug("Using Base64 format validation logic...");
                    return ValidateBase64License(cleanedKey);
                }
                // If it appears to be a hex string but no marker, try hex anyway
                else if (isHexFormat)
                {
                    LogDebug("Attempting hex validation without marker...");
                    return ValidateHexLicense();
                }
                // Otherwise, assume it's a Base64 license without marker
                else
                {
                    LogDebug("Attempting Base64 validation without marker...");
                    return ValidateBase64License(cleanedKey);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"ERROR: {ex.Message}");
                ValidationResult = $"× Error: {ex.Message}";
                ValidationResultColor = Brushes.MistyRose;
                ValidationTextColor = Brushes.Red;
                return false;
            }
        }
        
        private bool ValidateBase64License(string cleanedKey)
        {
            try
            {
                // Find signature marker
                const string SIGNATURE_MARKER = "##POS2024##";
                int markerIndex = cleanedKey.IndexOf(SIGNATURE_MARKER);
                
                if (markerIndex < 0)
                {
                    LogDebug("Missing signature marker");
                    ValidationResult = "× Missing signature marker";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                LogDebug($"Found signature marker at index {markerIndex}");
                
                // Extract Base64 data and signature
                string base64Data = cleanedKey.Substring(0, markerIndex);
                LogDebug($"Base64 data part length: {base64Data.Length} characters");
                
                string signaturePart = cleanedKey.Substring(markerIndex + SIGNATURE_MARKER.Length);
                LogDebug($"Signature part length: {signaturePart.Length} characters");
                
                if (signaturePart.Length < 32)
                {
                    LogDebug("Signature part is too short");
                    ValidationResult = "× Signature part is too short";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                // Decode Base64 to JSON
                byte[] jsonBytes;
                try
                {
                    jsonBytes = Convert.FromBase64String(base64Data);
                }
                catch (Exception ex)
                {
                    LogDebug($"Error decoding Base64: {ex.Message}");
                    ValidationResult = "× Invalid Base64 encoding";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                string jsonString = Encoding.UTF8.GetString(jsonBytes);
                LogDebug($"Decoded JSON: {jsonString}");
                
                // Validate JSON and signature
                return ValidateLicenseData(jsonString, signaturePart);
            }
            catch (Exception ex)
            {
                LogDebug($"Error in Base64 validation: {ex.Message}");
                ValidationResult = $"× Error in Base64 validation: {ex.Message}";
                ValidationResultColor = Brushes.MistyRose;
                ValidationTextColor = Brushes.Red;
                return false;
            }
        }
        
        private bool ValidateHexLicense()
        {
            try
            {
                string cleanedKey = Regex.Replace(LicenseKey, "[-\\s]", "");
                LogDebug($"Cleaned license key: {cleanedKey}");
                LogDebug($"Key length: {cleanedKey.Length}");
                
                // POSSystem format: HEX encoded data + salt + HMAC signature
                // The signature is the last 64 characters (32 bytes for SHA256)
                if (cleanedKey.Length <= 64)
                {
                    LogDebug("License key is too short");
                    ValidationResult = "× License key is too short";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                // POSSystem format: data ends with "POSSystem2024" before the signature
                const string posSystemSuffix = "504F5353797374656D32303234"; // "POSSystem2024" in hex
                
                // Find where the data ends and the signature begins
                int suffixIndex = cleanedKey.IndexOf(posSystemSuffix);
                if (suffixIndex < 0)
                {
                    LogDebug("Could not find POSSystem suffix marker");
                    ValidationResult = "× Invalid license format (missing POSSystem marker)";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                string jsonHex = cleanedKey.Substring(0, suffixIndex + posSystemSuffix.Length);
                LogDebug($"JSON hex part length: {jsonHex.Length} characters");
                
                // The signature is the remaining part
                string signatureHex = cleanedKey.Substring(suffixIndex + posSystemSuffix.Length);
                LogDebug($"Signature hex part length: {signatureHex.Length} characters");
                
                if (signatureHex.Length < 32)
                {
                    LogDebug("Signature part is too short");
                    ValidationResult = "× Signature part is too short";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }

                // Convert the hex JSON back to a string
                byte[] jsonBytes = HexToBytes(jsonHex);
                string jsonString = Encoding.UTF8.GetString(jsonBytes);
                LogDebug($"Decoded JSON: {jsonString}");
                
                // Remove POSSystem salt from the JSON
                const string SALT = "POSSystem2024";
                if (!jsonString.EndsWith(SALT))
                {
                    LogDebug("JSON string doesn't end with expected salt");
                    ValidationResult = "× Invalid license format (wrong salt ending)";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
                
                // Remove the salt to get just the JSON data
                string jsonData = jsonString.Substring(0, jsonString.Length - SALT.Length);
                LogDebug($"JSON data without salt: {jsonData}");

                // Parse the JSON to validate the structure
                try
                {
                    using (JsonDocument document = JsonDocument.Parse(jsonData))
                    {
                        JsonElement root = document.RootElement;
                        LogDebug("Successfully parsed JSON data");

                        // Verify business name matches
                        if (!root.TryGetProperty("BusinessName", out JsonElement businessNameElement))
                        {
                            LogDebug("Missing BusinessName property");
                            ValidationResult = "× Missing BusinessName property";
                            ValidationResultColor = Brushes.MistyRose;
                            ValidationTextColor = Brushes.Red;
                            return false;
                        }

                        string businessName = businessNameElement.GetString() ?? string.Empty;
                        LogDebug($"License business name: '{businessName}', expected: '{ValidationBusinessName}'");
                        
                        if (!string.Equals(businessName, ValidationBusinessName, StringComparison.OrdinalIgnoreCase))
                        {
                            LogDebug("Business name mismatch");
                            ValidationResult = $"× Business name mismatch: Expected '{ValidationBusinessName}', found '{businessName}'";
                            ValidationResultColor = Brushes.MistyRose;
                            ValidationTextColor = Brushes.Red;
                            return false;
                        }

                        // Verify hardware ID
                        if (!root.TryGetProperty("HardwareId", out JsonElement hardwareIdElement))
                        {
                            LogDebug("Missing HardwareId property");
                            ValidationResult = "× Missing HardwareId property";
                            ValidationResultColor = Brushes.MistyRose;
                            ValidationTextColor = Brushes.Red;
                            return false;
                        }

                        string hardwareId = hardwareIdElement.GetString() ?? string.Empty;
                        LogDebug($"License hardware ID: {hardwareId}");
                        LogDebug($"Expected system ID: {ValidationSystemId}");
                        
                        if (!string.Equals(hardwareId, ValidationSystemId, StringComparison.OrdinalIgnoreCase))
                        {
                            LogDebug("Hardware ID mismatch");
                            ValidationResult = "× Hardware ID mismatch";
                            ValidationResultColor = Brushes.MistyRose;
                            ValidationTextColor = Brushes.Red;
                            return false;
                        }

                        // Verify signature - calculate using the full string with salt
                        string calculatedSignature = CalculateSignature(jsonString);
                        LogDebug($"Calculated signature: {calculatedSignature}");
                        LogDebug($"License signature: {signatureHex}");
                        
                        if (!string.Equals(calculatedSignature, signatureHex, StringComparison.OrdinalIgnoreCase))
                        {
                            LogDebug("Invalid signature");
                            ValidationResult = "× Invalid signature";
                            ValidationResultColor = Brushes.MistyRose;
                            ValidationTextColor = Brushes.Red;
                            return false;
                        }

                        // All verifications passed
                        LogDebug("=== LICENSE VALIDATION SUCCESSFUL ===");
                        ValidationResult = "✓ License key is valid";
                        ValidationResultColor = Brushes.LightGreen;
                        ValidationTextColor = Brushes.DarkGreen;
                        return true;
                    }
                }
                catch (JsonException ex)
                {
                    LogDebug($"Error parsing JSON data: {ex.Message}");
                    ValidationResult = $"× Error parsing JSON: {ex.Message}";
                    ValidationResultColor = Brushes.MistyRose;
                    ValidationTextColor = Brushes.Red;
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error in hex validation: {ex.Message}");
                ValidationResult = $"× Error in hex validation: {ex.Message}";
                ValidationResultColor = Brushes.MistyRose;
                ValidationTextColor = Brushes.Red;
                return false;
            }
        }
        
        private bool ValidateLicenseData(string jsonString, string signaturePart)
        {
            try
            {
                // Parse the JSON license data
                using (JsonDocument document = JsonDocument.Parse(jsonString))
                {
                    JsonElement root = document.RootElement;
                    LogDebug("Successfully parsed JSON data");

                    // Verify business name matches
                    if (!root.TryGetProperty("BusinessName", out JsonElement businessNameElement))
                    {
                        LogDebug("Missing BusinessName property");
                        ValidationResult = "× Missing BusinessName property";
                        ValidationResultColor = Brushes.MistyRose;
                        ValidationTextColor = Brushes.Red;
                        return false;
                    }

                    string businessName = businessNameElement.GetString() ?? string.Empty;
                    LogDebug($"License business name: '{businessName}', expected: '{ValidationBusinessName}'");
                    
                    if (!string.Equals(businessName, ValidationBusinessName, StringComparison.OrdinalIgnoreCase))
                    {
                        LogDebug("Business name mismatch");
                        ValidationResult = $"× Business name mismatch: Expected '{ValidationBusinessName}', found '{businessName}'";
                        ValidationResultColor = Brushes.MistyRose;
                        ValidationTextColor = Brushes.Red;
                        return false;
                    }

                    // Verify hardware ID
                    if (!root.TryGetProperty("HardwareId", out JsonElement hardwareIdElement))
                    {
                        LogDebug("Missing HardwareId property");
                        ValidationResult = "× Missing HardwareId property";
                        ValidationResultColor = Brushes.MistyRose;
                        ValidationTextColor = Brushes.Red;
                        return false;
                    }

                    string hardwareId = hardwareIdElement.GetString() ?? string.Empty;
                    LogDebug($"License hardware ID: {hardwareId}");
                    LogDebug($"Expected system ID: {ValidationSystemId}");
                    
                    if (!string.Equals(hardwareId, ValidationSystemId, StringComparison.OrdinalIgnoreCase))
                    {
                        LogDebug("Hardware ID mismatch");
                        ValidationResult = "× Hardware ID mismatch";
                        ValidationResultColor = Brushes.MistyRose;
                        ValidationTextColor = Brushes.Red;
                        return false;
                    }

                    // Verify signature - this time using the correct approach
                    string calculatedSignature = CalculateSignature(jsonString);
                    LogDebug($"Calculated signature: {calculatedSignature}");
                    LogDebug($"License signature: {signaturePart}");
                    
                    if (!string.Equals(calculatedSignature, signaturePart, StringComparison.OrdinalIgnoreCase))
                    {
                        LogDebug("Invalid signature");
                        ValidationResult = "× Invalid signature";
                        ValidationResultColor = Brushes.MistyRose;
                        ValidationTextColor = Brushes.Red;
                        return false;
                    }

                    // All verifications passed
                    LogDebug("=== LICENSE VALIDATION SUCCESSFUL ===");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error validating license data: {ex.Message}");
                ValidationResult = $"× Error validating data: {ex.Message}";
                ValidationResultColor = Brushes.MistyRose;
                ValidationTextColor = Brushes.Red;
                return false;
            }
        }
        
        private bool IsHexString(string text)
        {
            // Check if the string contains only hex characters (0-9, A-F)
            return Regex.IsMatch(text, "^[0-9A-Fa-f]+$");
        }
        
        private byte[] HexToBytes(string hex)
        {
            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            return bytes;
        }
        
        private string CalculateSignature(string data)
        {
            // For hex-encoded license validation, we need to use the POSSystem approach
            if (IsHexString(Regex.Replace(LicenseKey, "-", "").Trim()))
            {
                string secretKey = GeneratePOSSystemSecretKey();
                
                // Check if data already contains the salt
                const string SALT = "POSSystem2024";
                string dataToHash = data; // Use data as-is, don't add salt again
                
                byte[] keyBytes = Encoding.UTF8.GetBytes(secretKey);
                using (var hmac = new HMACSHA256(keyBytes))
                {
                    byte[] dataBytes = Encoding.UTF8.GetBytes(dataToHash);
                    byte[] signatureBytes = hmac.ComputeHash(dataBytes);
                    
                    return BitConverter.ToString(signatureBytes).Replace("-", "");
                }
            }
            else
            {
                // Original Base64 format approach
                string secretKey = "POS2024SecretKey"; // This would normally be stored securely
                string dataToHash = data + secretKey;
                
                using (var sha = SHA256.Create())
                {
                    byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(dataToHash));
                    
                    // Use the first 16 bytes of the hash as a signature
                    var signature = new StringBuilder();
                    for (int i = 0; i < 16; i++)
                    {
                        signature.Append(hashBytes[i].ToString("X2"));
                    }
                    
                    return signature.ToString();
                }
            }
        }
        
        private void ExecuteRunDiagnostics()
        {
            try
            {
                LogDebug("=== RUNNING DIAGNOSTIC TESTS ===");
                
                // Get current system ID
                string currentId = HardwareInfo.GetSystemId();
                LogDebug($"Current System ID: {currentId}");
                
                // Run diagnostic analysis on hardware ID
                string diagnosticReport = DiagnosticTools.AnalyzeHardwareId(currentId);
                LogDebug(diagnosticReport);
                
                // Check validation system ID if different
                if (!string.IsNullOrEmpty(ValidationSystemId) && ValidationSystemId != currentId)
                {
                    LogDebug("=== ANALYZING VALIDATION SYSTEM ID ===");
                    string validationReport = DiagnosticTools.AnalyzeHardwareId(ValidationSystemId);
                    LogDebug(validationReport);
                }
                
                // Analyze license key format if available
                if (!string.IsNullOrEmpty(LicenseKey))
                {
                    LogDebug("=== ANALYZING LICENSE KEY ===");
                    string licenseReport = DiagnosticTools.AnalyzeLicenseKey(LicenseKey);
                    LogDebug(licenseReport);
                }
                
                // Analyze generated key if available
                if (!string.IsNullOrEmpty(GeneratedKey))
                {
                    LogDebug("=== ANALYZING GENERATED LICENSE KEY ===");
                    string generatedReport = DiagnosticTools.AnalyzeLicenseKey(GeneratedKey);
                    LogDebug(generatedReport);
                }
                
                LogDebug("=== DIAGNOSTIC TESTS COMPLETE ===");
            }
            catch (Exception ex)
            {
                LogDebug($"ERROR running diagnostics: {ex.Message}");
                LogDebug(ex.StackTrace);
            }
        }
        
        private void LogDebug(string message)
        {
            Debug.WriteLine(message);
            DebugOutput += message + Environment.NewLine;
        }
    }
} 