using System;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Diagnostics;

namespace POSSystem.Controls
{
    /// <summary>
    /// A virtualizing panel that arranges items in a wrapping layout (like WrapPanel) 
    /// but with UI virtualization support for better performance with large collections
    /// </summary>
    public class VirtualizingWrapPanel : VirtualizingPanel, IScrollInfo
    {
        #region Fields

        private Size _extent = new Size(0, 0);
        private Size _viewport = new Size(0, 0);
        private Point _offset = new Point(0, 0);
        private ScrollViewer _owner;
        private UIElementCollection _children;
        private ItemsControl _itemsControl;
        private IItemContainerGenerator _generator;
        private int _itemCount;
        private Size _childSize = new Size(140, 180); // Default item size
        private Size _availableSize;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the uniform item width
        /// </summary>
        public double ItemWidth
        {
            get { return (double)GetValue(ItemWidthProperty); }
            set { SetValue(ItemWidthProperty, value); }
        }

        /// <summary>
        /// Identifies the ItemWidth dependency property
        /// </summary>
        public static readonly DependencyProperty ItemWidthProperty =
            DependencyProperty.Register("ItemWidth", typeof(double), typeof(VirtualizingWrapPanel),
                new FrameworkPropertyMetadata(140.0, FrameworkPropertyMetadataOptions.AffectsMeasure));

        /// <summary>
        /// Gets or sets the uniform item height
        /// </summary>
        public double ItemHeight
        {
            get { return (double)GetValue(ItemHeightProperty); }
            set { SetValue(ItemHeightProperty, value); }
        }

        /// <summary>
        /// Identifies the ItemHeight dependency property
        /// </summary>
        public static readonly DependencyProperty ItemHeightProperty =
            DependencyProperty.Register("ItemHeight", typeof(double), typeof(VirtualizingWrapPanel),
                new FrameworkPropertyMetadata(180.0, FrameworkPropertyMetadataOptions.AffectsMeasure));

        /// <summary>
        /// Gets or sets the orientation (horizontal or vertical)
        /// </summary>
        public Orientation Orientation
        {
            get { return (Orientation)GetValue(OrientationProperty); }
            set { SetValue(OrientationProperty, value); }
        }

        /// <summary>
        /// Identifies the Orientation dependency property
        /// </summary>
        public static readonly DependencyProperty OrientationProperty =
            StackPanel.OrientationProperty.AddOwner(typeof(VirtualizingWrapPanel),
                new FrameworkPropertyMetadata(Orientation.Horizontal, FrameworkPropertyMetadataOptions.AffectsMeasure));

        #endregion

        #region Constructor

        public VirtualizingWrapPanel()
        {
            this.ClipToBounds = true;
            this.RenderTransform = Transform.Identity;
        }

        #endregion

        #region Methods

        private void UpdateChildSize()
        {
            _childSize = new Size(ItemWidth, ItemHeight);
        }

        protected override void OnInitialized(EventArgs e)
        {
            base.OnInitialized(e);
            
            try
            {
                // Check if this panel is properly hosted in an ItemsControl
                _itemsControl = ItemsControl.GetItemsOwner(this);
                
                // Only access InternalChildren if we have a parent ItemsControl
                if (_itemsControl != null)
                {
                    _children = InternalChildren;
                    _generator = ItemContainerGenerator;
                }
                else
                {
                    // Log warning - panel is not properly hosted
                    Debug.WriteLine("Warning: VirtualizingWrapPanel is not hosted in an ItemsControl.");
                }
                
                UpdateChildSize();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing VirtualizingWrapPanel: {ex.Message}");
            }
        }

        protected override void OnItemsChanged(object sender, ItemsChangedEventArgs args)
        {
            base.OnItemsChanged(sender, args);
            
            try
            {
                if (args.Action == NotifyCollectionChangedAction.Remove ||
                    args.Action == NotifyCollectionChangedAction.Replace ||
                    args.Action == NotifyCollectionChangedAction.Reset)
                {
                    // Force a full remeasure for these operations
                    MeasureOverride(_viewport);
                }
                
                // Safely check for null _itemsControl before accessing its properties
                if (_itemsControl != null && _itemsControl.Items != null)
                {
                    _itemCount = _itemsControl.Items.Count;
                    EnsureScrollInfo();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OnItemsChanged: {ex.Message}");
            }
        }

        private void EnsureScrollInfo()
        {
            // Make sure the viewport is valid (handle infinity)
            Size safeViewport = _viewport;
            if (double.IsInfinity(safeViewport.Width))
            {
                safeViewport.Width = 1000;
            }
            if (double.IsInfinity(safeViewport.Height))
            {
                safeViewport.Height = 1000;
            }

            // Update extent based on the current item count and size
            int itemsPerRow = Math.Max(1, (int)Math.Floor(safeViewport.Width / _childSize.Width));
            double rowCount = Math.Ceiling((double)_itemCount / itemsPerRow);
            
            // Ensure extent is never smaller than viewport
            double extentWidth = Math.Max(safeViewport.Width, itemsPerRow * _childSize.Width);
            double extentHeight = Math.Max(safeViewport.Height, rowCount * _childSize.Height);
            
            _extent = new Size(extentWidth, extentHeight);
            
            // Ensure offset is valid for the current extent and viewport
            if (_offset.X + safeViewport.Width > _extent.Width)
            {
                _offset.X = Math.Max(0, _extent.Width - safeViewport.Width);
            }
            
            if (_offset.Y + safeViewport.Height > _extent.Height)
            {
                _offset.Y = Math.Max(0, _extent.Height - safeViewport.Height);
            }
            
            if (_owner != null)
            {
                _owner.InvalidateScrollInfo();
            }
        }

        protected override Size MeasureOverride(Size availableSize)
        {
            // Handle infinite available size by using a reasonable default
            Size constrainedSize = availableSize;
            if (double.IsInfinity(constrainedSize.Width))
            {
                constrainedSize.Width = ItemWidth * 10; // Default to 10 items width if no constraint
            }
            if (double.IsInfinity(constrainedSize.Height))
            {
                constrainedSize.Height = ItemHeight * 10; // Default to 10 items height if no constraint
            }

            // Update the view port and the extent
            _viewport = constrainedSize;

            // Get the actual number of visible items
            _itemCount = ItemsControl.GetItemsOwner(this)?.Items?.Count ?? 0;
            _childSize = new Size(ItemWidth, ItemHeight);

            // Store original available size for children
            _availableSize = constrainedSize;

            // Update scroll information
            EnsureScrollInfo();

            // If there are no items, we return the available size as the desired size with zero vertical if there are no rows
            if (_itemCount == 0)
            {
                return new Size(constrainedSize.Width, 0);
            }

            // ✅ CRITICAL FIX: Generate visual containers for items
            GenerateVisibleItems(constrainedSize);

            // Calculate items per row and total rows
            int itemsPerRow = Math.Max(1, (int)Math.Floor(constrainedSize.Width / ItemWidth));
            double rowCount = Math.Ceiling((double)_itemCount / itemsPerRow);

            // Calculate resulting size
            double resultWidth = Math.Min(constrainedSize.Width, itemsPerRow * ItemWidth);
            double resultHeight = Math.Min(constrainedSize.Height, rowCount * ItemHeight);

            // Make sure we never return infinity
            if (double.IsInfinity(resultWidth)) resultWidth = ItemWidth * itemsPerRow;
            if (double.IsInfinity(resultHeight)) resultHeight = ItemHeight * rowCount;

            return new Size(resultWidth, resultHeight);
        }

        protected override Size ArrangeOverride(Size finalSize)
        {
            if (_itemCount == 0 || _children == null)
            {
                return finalSize;
            }

            try
            {
                // Calculate layout parameters
                int itemsPerRow = Math.Max(1, (int)Math.Floor(finalSize.Width / _childSize.Width));

                // Arrange each visible child
                for (int i = 0; i < _children.Count; i++)
                {
                    UIElement child = _children[i];
                    if (child == null) continue;

                    // Get the actual item index for this child
                    GeneratorPosition childPos = new GeneratorPosition(i, 0);
                    int itemIndex = _generator?.IndexFromGeneratorPosition(childPos) ?? i;

                    // Calculate grid position
                    int row = itemIndex / itemsPerRow;
                    int col = itemIndex % itemsPerRow;

                    // Calculate actual position accounting for scroll offset
                    double x = col * _childSize.Width;
                    double y = (row * _childSize.Height) - _offset.Y;

                    // Arrange the child
                    child.Arrange(new Rect(x, y, _childSize.Width, _childSize.Height));
                }

                // ✅ PERFORMANCE FIX: Reduced debug output to improve frame rates
                #if DEBUG && VERBOSE_LOGGING
                Debug.WriteLine($"[VirtualizingWrapPanel] Arranged {_children.Count} items in {finalSize}");
                #endif
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[VirtualizingWrapPanel] Error arranging items: {ex.Message}");
            }

            return finalSize;
        }
        
        /// <summary>
        /// ✅ CRITICAL FIX: Generate visual containers for visible items
        /// </summary>
        private void GenerateVisibleItems(Size availableSize)
        {
            if (_generator == null || _itemCount == 0)
                return;

            try
            {
                // Calculate which items are visible
                int itemsPerRow = Math.Max(1, (int)Math.Floor(availableSize.Width / ItemWidth));
                int firstVisibleRow = Math.Max(0, (int)Math.Floor(_offset.Y / ItemHeight));
                int lastVisibleRow = Math.Min(
                    (int)Math.Ceiling((_offset.Y + availableSize.Height) / ItemHeight),
                    (int)Math.Ceiling((double)_itemCount / itemsPerRow) - 1);

                int firstVisibleIndex = firstVisibleRow * itemsPerRow;
                int lastVisibleIndex = Math.Min(_itemCount - 1, (lastVisibleRow + 1) * itemsPerRow - 1);

                // Clean up items that are no longer visible
                CleanUpItems(firstVisibleIndex, lastVisibleIndex);

                // Generate new visible items
                GeneratorPosition startPos = _generator.GeneratorPositionFromIndex(firstVisibleIndex);
                int childIndex = (startPos.Offset == 0) ? startPos.Index : startPos.Index + 1;

                using (_generator.StartAt(startPos, GeneratorDirection.Forward, true))
                {
                    for (int itemIndex = firstVisibleIndex; itemIndex <= lastVisibleIndex; itemIndex++)
                    {
                        bool newlyRealized;
                        UIElement child = _generator.GenerateNext(out newlyRealized) as UIElement;

                        if (child != null)
                        {
                            if (newlyRealized)
                            {
                                // Add the child to the panel
                                if (childIndex >= _children.Count)
                                {
                                    AddInternalChild(child);
                                }
                                else
                                {
                                    InsertInternalChild(childIndex, child);
                                }

                                // Prepare the container
                                _generator.PrepareItemContainer(child);
                            }

                            // Measure the child
                            child.Measure(new Size(ItemWidth, ItemHeight));
                            childIndex++;
                        }
                    }
                }

                // ✅ PERFORMANCE FIX: Reduced debug output to improve frame rates
                #if DEBUG && VERBOSE_LOGGING
                Debug.WriteLine($"[VirtualizingWrapPanel] Generated {_children.Count} visible items (indices {firstVisibleIndex}-{lastVisibleIndex})");
                #endif
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[VirtualizingWrapPanel] Error generating items: {ex.Message}");
            }
        }

        private void CleanUpItems(int firstVisibleItemIndex, int lastVisibleItemIndex)
        {
            if (_generator == null || _children == null)
                return;

            try
            {
                // Clean up the no longer visible children
                for (int i = _children.Count - 1; i >= 0; i--)
                {
                    GeneratorPosition childPos = new GeneratorPosition(i, 0);
                    int itemIndex = _generator.IndexFromGeneratorPosition(childPos);

                    if (itemIndex < firstVisibleItemIndex || itemIndex > lastVisibleItemIndex)
                    {
                        // This item is not visible anymore, remove it
                        _generator.Remove(childPos, 1);
                        RemoveInternalChildRange(i, 1);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[VirtualizingWrapPanel] Error cleaning up items: {ex.Message}");
            }
        }

        public void LineUp()
        {
            SetVerticalOffset(_offset.Y - 50);
        }

        public void LineDown()
        {
            SetVerticalOffset(_offset.Y + 50);
        }

        public void LineLeft()
        {
            SetHorizontalOffset(_offset.X - 50);
        }

        public void LineRight()
        {
            SetHorizontalOffset(_offset.X + 50);
        }

        public void PageUp()
        {
            SetVerticalOffset(_offset.Y - _viewport.Height);
        }

        public void PageDown()
        {
            SetVerticalOffset(_offset.Y + _viewport.Height);
        }

        public void PageLeft()
        {
            SetHorizontalOffset(_offset.X - _viewport.Width);
        }

        public void PageRight()
        {
            SetHorizontalOffset(_offset.X + _viewport.Width);
        }

        public void MouseWheelUp()
        {
            SetVerticalOffset(_offset.Y - 50);
        }

        public void MouseWheelDown()
        {
            SetVerticalOffset(_offset.Y + 50);
        }

        public void MouseWheelLeft()
        {
            SetHorizontalOffset(_offset.X - 50);
        }

        public void MouseWheelRight()
        {
            SetHorizontalOffset(_offset.X + 50);
        }

        public void SetHorizontalOffset(double offset)
        {
            if (offset < 0 || _viewport.Width >= _extent.Width)
            {
                offset = 0;
            }
            else if (offset + _viewport.Width >= _extent.Width)
            {
                offset = _extent.Width - _viewport.Width;
            }

            _offset.X = offset;
            InvalidateArrange();

            if (_owner != null)
            {
                _owner.InvalidateScrollInfo();
            }
        }

        public void SetVerticalOffset(double offset)
        {
            if (offset < 0 || _viewport.Height >= _extent.Height)
            {
                offset = 0;
            }
            else if (offset + _viewport.Height >= _extent.Height)
            {
                offset = _extent.Height - _viewport.Height;
            }

            _offset.Y = offset;
            InvalidateMeasure();

            if (_owner != null)
            {
                _owner.InvalidateScrollInfo();
            }
        }

        public Rect MakeVisible(Visual visual, Rect rectangle)
        {
            if (visual == null || visual == this || !IsAncestorOf(visual))
            {
                return Rect.Empty;
            }

            // Get the position of the visual relative to this panel
            var transformToAncestor = visual.TransformToAncestor(this);
            var rect = transformToAncestor.TransformBounds(rectangle);

            // Check if the rectangle is outside the visible area
            if (rect.Bottom > _offset.Y + _viewport.Height)
            {
                // Item is below viewport, scroll down to make it visible
                SetVerticalOffset(rect.Bottom - _viewport.Height);
            }
            else if (rect.Top < _offset.Y)
            {
                // Item is above viewport, scroll up to make it visible
                SetVerticalOffset(rect.Top);
            }

            // Return the visible rectangle
            return new Rect(rect.X, rect.Y - _offset.Y, rect.Width, rect.Height);
        }

        #endregion

        #region IScrollInfo Implementation

        public bool CanHorizontallyScroll { get; set; }
        public bool CanVerticallyScroll { get; set; }

        public double ExtentHeight => _extent.Height;
        public double ExtentWidth => _extent.Width;

        public double HorizontalOffset => _offset.X;
        public double VerticalOffset => _offset.Y;

        public double ViewportHeight => _viewport.Height;
        public double ViewportWidth => _viewport.Width;

        public ScrollViewer ScrollOwner
        {
            get { return _owner; }
            set
            {
                _owner = value;
                if (_owner != null)
                {
                    _owner.InvalidateScrollInfo();
                }
            }
        }

        #endregion
    }
} 