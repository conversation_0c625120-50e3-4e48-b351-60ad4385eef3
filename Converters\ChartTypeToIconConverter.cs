using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.ViewModels;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    public class ChartTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ChartType chartType)
            {
                return chartType switch
                {
                    ChartType.Line => PackIconKind.ChartLine,
                    ChartType.Bar => PackIconKind.ChartBar,
                    _ => PackIconKind.ChartLine
                };
            }
            return PackIconKind.ChartLine;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 