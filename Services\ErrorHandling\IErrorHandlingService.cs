using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.ErrorHandling
{
    /// <summary>
    /// Interface for centralized error handling service
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Handle async operations with error management
        /// </summary>
        Task<T> HandleAsync<T>(Func<Task<T>> operation, string operationName, T defaultValue = default(T), bool showUserMessage = true);

        /// <summary>
        /// Handle synchronous operations with error management
        /// </summary>
        T Handle<T>(Func<T> operation, string operationName, T defaultValue = default(T), bool showUserMessage = true);

        /// <summary>
        /// Handle void operations with error management
        /// </summary>
        bool HandleVoid(Action operation, string operationName, bool showUserMessage = true);

        /// <summary>
        /// Handle async void operations with error management
        /// </summary>
        Task<bool> HandleVoidAsync(Func<Task> operation, string operationName, bool showUserMessage = true);

        /// <summary>
        /// Log error with structured information
        /// </summary>
        void LogError(Exception exception, string operationName, Dictionary<string, object> additionalData = null);

        /// <summary>
        /// Async version of LogError
        /// </summary>
        Task LogErrorAsync(Exception exception, string operationName, Dictionary<string, object> additionalData = null);

        /// <summary>
        /// Show user-friendly error message
        /// </summary>
        void ShowUserFriendlyError(Exception exception, string operationName);

        /// <summary>
        /// Async version of ShowUserFriendlyError
        /// </summary>
        Task ShowUserFriendlyErrorAsync(Exception exception, string operationName);

        /// <summary>
        /// Get recent errors for debugging
        /// </summary>
        List<ErrorLogEntry> GetRecentErrors();

        /// <summary>
        /// Check if an exception is a critical system error
        /// </summary>
        bool IsCriticalError(Exception exception);

        /// <summary>
        /// Check if an exception is a transient error that might be retried
        /// </summary>
        bool IsTransientError(Exception exception);
    }
}
