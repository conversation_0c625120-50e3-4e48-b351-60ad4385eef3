using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// ✅ CRITICAL DATABASE OPTIMIZATION: Service to create and manage database indexes for optimal query performance
    /// </summary>
    public class DatabaseIndexOptimizer : IDisposable
    {
        private readonly ILogger<DatabaseIndexOptimizer> _logger;
        private readonly string _connectionString;
        private bool _disposed;

        public DatabaseIndexOptimizer(ILogger<DatabaseIndexOptimizer> logger = null)
        {
            _logger = logger;
            _connectionString = "Data Source=pos.db";
        }

        /// <summary>
        /// ✅ CRITICAL: Apply all essential database indexes for POS system performance
        /// </summary>
        public async Task ApplyEssentialIndexesAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                Debug.WriteLine("[INDEX-OPTIMIZER] Starting essential database index optimization...");

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // ✅ PERFORMANCE FIX: Check if database is properly initialized before creating indexes
                if (!await IsDatabaseInitializedAsync(connection))
                {
                    Debug.WriteLine("[INDEX-OPTIMIZER] ⚠️ Database not fully initialized yet, skipping index creation");
                    return;
                }

                var indexCommands = GetEssentialIndexCommands();
                var createdCount = 0;
                var existingCount = 0;
                var skippedCount = 0;

                foreach (var (indexName, sql) in indexCommands)
                {
                    try
                    {
                        // ✅ PERFORMANCE FIX: Check if the table exists before creating index
                        var tableName = ExtractTableNameFromIndex(sql);
                        if (!await TableExistsAsync(connection, tableName))
                        {
                            skippedCount++;
                            Debug.WriteLine($"[INDEX-OPTIMIZER] ⚠️ Table '{tableName}' does not exist, skipping index: {indexName}");
                            continue;
                        }

                        using var cmd = new SqliteCommand(sql, connection);
                        await cmd.ExecuteNonQueryAsync();
                        createdCount++;
                        Debug.WriteLine($"[INDEX-OPTIMIZER] ✅ Created/verified index: {indexName}");
                    }
                    catch (SqliteException ex) when (ex.Message.Contains("already exists"))
                    {
                        existingCount++;
                        Debug.WriteLine($"[INDEX-OPTIMIZER] ✓ Index already exists: {indexName}");
                    }
                    catch (SqliteException ex) when (ex.Message.Contains("no such table"))
                    {
                        skippedCount++;
                        Debug.WriteLine($"[INDEX-OPTIMIZER] ⚠️ Table does not exist, skipping index: {indexName}");
                    }
                    catch (NotSupportedException ex)
                    {
                        // SQLite doesn't support some advanced index features - this is expected
                        Debug.WriteLine($"[INDEX-OPTIMIZER] ⚠️ Index feature not supported in SQLite: {indexName} - {ex.Message}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Error creating index {indexName}: {ex.Message}");
                        // ✅ PERFORMANCE FIX: Don't log errors for missing tables during startup
                        if (!ex.Message.Contains("no such table"))
                        {
                            _logger?.LogError(ex, "Error creating index {IndexName}", indexName);
                        }
                    }
                }

                // Update database statistics for better query planning
                await UpdateDatabaseStatisticsAsync(connection);

                stopwatch.Stop();
                Debug.WriteLine($"[INDEX-OPTIMIZER] Index optimization completed in {stopwatch.ElapsedMilliseconds}ms");
                Debug.WriteLine($"[INDEX-OPTIMIZER] Summary: {createdCount} created/verified, {existingCount} already existed, {skippedCount} skipped (tables not ready)");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Critical error during index optimization: {ex.Message}");
                _logger?.LogError(ex, "Critical error during index optimization");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Get all essential index commands for POS system
        /// </summary>
        private List<(string IndexName, string SQL)> GetEssentialIndexCommands()
        {
            return new List<(string, string)>
            {
                // ✅ CRITICAL: Products table indexes (most frequently queried)
                ("IX_Products_IsActive", "CREATE INDEX IF NOT EXISTS IX_Products_IsActive ON Products(IsActive)"),
                ("IX_Products_SKU", "CREATE INDEX IF NOT EXISTS IX_Products_SKU ON Products(SKU)"),
                ("IX_Products_Name", "CREATE INDEX IF NOT EXISTS IX_Products_Name ON Products(Name)"),
                ("IX_Products_CategoryId", "CREATE INDEX IF NOT EXISTS IX_Products_CategoryId ON Products(CategoryId)"),
                ("IX_Products_StockQuantity", "CREATE INDEX IF NOT EXISTS IX_Products_StockQuantity ON Products(StockQuantity)"),
                ("IX_Products_ReorderPoint", "CREATE INDEX IF NOT EXISTS IX_Products_ReorderPoint ON Products(ReorderPoint)"),
                
                // ✅ CRITICAL: Sales table indexes (transaction processing)
                ("IX_Sales_SaleDate", "CREATE INDEX IF NOT EXISTS IX_Sales_SaleDate ON Sales(SaleDate)"),
                ("IX_Sales_PaymentStatus", "CREATE INDEX IF NOT EXISTS IX_Sales_PaymentStatus ON Sales(PaymentStatus)"),
                ("IX_Sales_Status", "CREATE INDEX IF NOT EXISTS IX_Sales_Status ON Sales(Status)"),
                ("IX_Sales_CustomerId", "CREATE INDEX IF NOT EXISTS IX_Sales_CustomerId ON Sales(CustomerId)"),
                ("IX_Sales_UserId", "CREATE INDEX IF NOT EXISTS IX_Sales_UserId ON Sales(UserId)"),
                ("IX_Sales_InvoiceNumber", "CREATE INDEX IF NOT EXISTS IX_Sales_InvoiceNumber ON Sales(InvoiceNumber)"),
                
                // ✅ CRITICAL: SaleItems table indexes (product performance analysis)
                ("IX_SaleItems_SaleId", "CREATE INDEX IF NOT EXISTS IX_SaleItems_SaleId ON SaleItems(SaleId)"),
                ("IX_SaleItems_ProductId", "CREATE INDEX IF NOT EXISTS IX_SaleItems_ProductId ON SaleItems(ProductId)"),
                ("IX_SaleItems_ProductId_SaleId", "CREATE INDEX IF NOT EXISTS IX_SaleItems_ProductId_SaleId ON SaleItems(ProductId, SaleId)"),
                
                // ✅ CRITICAL: Customers table indexes (customer lookup)
                ("IX_Customers_Phone", "CREATE INDEX IF NOT EXISTS IX_Customers_Phone ON Customers(Phone)"),
                ("IX_Customers_Email", "CREATE INDEX IF NOT EXISTS IX_Customers_Email ON Customers(Email)"),
                ("IX_Customers_LoyaltyCode", "CREATE INDEX IF NOT EXISTS IX_Customers_LoyaltyCode ON Customers(LoyaltyCode)"),
                ("IX_Customers_IsActive", "CREATE INDEX IF NOT EXISTS IX_Customers_IsActive ON Customers(IsActive)"),
                
                // ✅ CRITICAL: ProductBarcodes table indexes (barcode scanning)
                ("IX_ProductBarcodes_Barcode", "CREATE INDEX IF NOT EXISTS IX_ProductBarcodes_Barcode ON ProductBarcodes(Barcode)"),
                ("IX_ProductBarcodes_ProductId", "CREATE INDEX IF NOT EXISTS IX_ProductBarcodes_ProductId ON ProductBarcodes(ProductId)"),
                ("IX_ProductBarcodes_IsPrimary", "CREATE INDEX IF NOT EXISTS IX_ProductBarcodes_IsPrimary ON ProductBarcodes(IsPrimary)"),
                
                // ✅ CRITICAL: BatchStock table indexes (inventory tracking)
                ("IX_BatchStock_ProductId", "CREATE INDEX IF NOT EXISTS IX_BatchStock_ProductId ON BatchStock(ProductId)"),
                ("IX_BatchStock_ExpiryDate", "CREATE INDEX IF NOT EXISTS IX_BatchStock_ExpiryDate ON BatchStock(ExpiryDate)"),
                ("IX_BatchStock_Quantity", "CREATE INDEX IF NOT EXISTS IX_BatchStock_Quantity ON BatchStock(Quantity)"),
                
                // ✅ CRITICAL: Composite indexes for common query patterns
                ("IX_Products_Active_Category", "CREATE INDEX IF NOT EXISTS IX_Products_Active_Category ON Products(IsActive, CategoryId)"),
                ("IX_Products_Active_Stock", "CREATE INDEX IF NOT EXISTS IX_Products_Active_Stock ON Products(IsActive, StockQuantity)"),
                ("IX_Sales_Date_Status", "CREATE INDEX IF NOT EXISTS IX_Sales_Date_Status ON Sales(SaleDate, PaymentStatus)"),
                ("IX_Sales_Date_Customer", "CREATE INDEX IF NOT EXISTS IX_Sales_Date_Customer ON Sales(SaleDate, CustomerId)"),
                
                // ✅ PERFORMANCE: Covering indexes for high-frequency queries
                ("IX_Products_List_Covering", @"CREATE INDEX IF NOT EXISTS IX_Products_List_Covering 
                    ON Products(IsActive, CategoryId, Name, SKU, SellingPrice, StockQuantity, ReorderPoint)"),
                
                ("IX_Sales_Dashboard_Covering", @"CREATE INDEX IF NOT EXISTS IX_Sales_Dashboard_Covering 
                    ON Sales(SaleDate, Status, PaymentStatus, GrandTotal, CustomerId, UserId)"),
                
                ("IX_SaleItems_Analysis_Covering", @"CREATE INDEX IF NOT EXISTS IX_SaleItems_Analysis_Covering 
                    ON SaleItems(ProductId, SaleId, Quantity, UnitPrice, Total)"),
                
                // ✅ PERFORMANCE: Partial indexes for specific scenarios
                ("IX_Products_LowStock", @"CREATE INDEX IF NOT EXISTS IX_Products_LowStock 
                    ON Products(StockQuantity, ReorderPoint) WHERE IsActive = 1 AND StockQuantity <= ReorderPoint"),
                
                ("IX_Sales_Unpaid", @"CREATE INDEX IF NOT EXISTS IX_Sales_Unpaid 
                    ON Sales(SaleDate, GrandTotal) WHERE PaymentStatus = 'Unpaid'"),
                
                ("IX_Products_OutOfStock", @"CREATE INDEX IF NOT EXISTS IX_Products_OutOfStock 
                    ON Products(Name, CategoryId) WHERE IsActive = 1 AND StockQuantity = 0"),
                
                // ✅ SEARCH: Text search optimization
                ("IX_Products_Search", @"CREATE INDEX IF NOT EXISTS IX_Products_Search 
                    ON Products(IsActive, Name COLLATE NOCASE, SKU COLLATE NOCASE)"),
                
                ("IX_Customers_Search", @"CREATE INDEX IF NOT EXISTS IX_Customers_Search 
                    ON Customers(IsActive, FirstName COLLATE NOCASE, LastName COLLATE NOCASE, Phone, Email COLLATE NOCASE)")
            };
        }

        /// <summary>
        /// ✅ CRITICAL: Update database statistics for optimal query planning
        /// </summary>
        private async Task UpdateDatabaseStatisticsAsync(SqliteConnection connection)
        {
            try
            {
                Debug.WriteLine("[INDEX-OPTIMIZER] Updating database statistics...");

                using var cmd = new SqliteCommand("ANALYZE", connection);
                await cmd.ExecuteNonQueryAsync();

                Debug.WriteLine("[INDEX-OPTIMIZER] ✅ Database statistics updated");
            }
            catch (NotSupportedException ex)
            {
                // Some SQLite versions or configurations might not support ANALYZE
                Debug.WriteLine($"[INDEX-OPTIMIZER] ⚠️ Database statistics update not supported: {ex.Message}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Error updating statistics: {ex.Message}");
                _logger?.LogError(ex, "Error updating database statistics");
            }
        }

        /// <summary>
        /// ✅ MONITORING: Get index usage statistics
        /// </summary>
        public async Task<List<IndexUsageInfo>> GetIndexUsageStatsAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var stats = new List<IndexUsageInfo>();
                
                using var cmd = new SqliteCommand(@"
                    SELECT name, sql, rootpage 
                    FROM sqlite_master 
                    WHERE type = 'index' AND name LIKE 'IX_%'
                    ORDER BY name", connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    stats.Add(new IndexUsageInfo
                    {
                        IndexName = reader.GetString("name"),
                        Definition = reader.IsDBNull("sql") ? "System Index" : reader.GetString("sql"),
                        RootPage = reader.GetInt32("rootpage")
                    });
                }

                Debug.WriteLine($"[INDEX-OPTIMIZER] Found {stats.Count} custom indexes");
                return stats;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Error getting index stats: {ex.Message}");
                _logger?.LogError(ex, "Error getting index usage statistics");
                return new List<IndexUsageInfo>();
            }
        }

        /// <summary>
        /// ✅ MAINTENANCE: Optimize database file (VACUUM)
        /// </summary>
        public async Task OptimizeDatabaseFileAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                Debug.WriteLine("[INDEX-OPTIMIZER] Starting database file optimization (VACUUM)...");
                
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get file size before optimization
                var dbPath = connection.DataSource;
                var sizeBefore = new FileInfo(dbPath).Length / (1024 * 1024); // MB
                
                using var cmd = new SqliteCommand("VACUUM", connection);
                cmd.CommandTimeout = 300; // 5 minutes timeout for VACUUM
                await cmd.ExecuteNonQueryAsync();
                
                var sizeAfter = new FileInfo(dbPath).Length / (1024 * 1024); // MB
                var savedSpace = sizeBefore - sizeAfter;
                
                stopwatch.Stop();
                Debug.WriteLine($"[INDEX-OPTIMIZER] ✅ Database file optimized in {stopwatch.ElapsedMilliseconds}ms");
                Debug.WriteLine($"[INDEX-OPTIMIZER] File size: {sizeBefore}MB → {sizeAfter}MB (saved {savedSpace}MB)");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Error during database file optimization: {ex.Message}");
                _logger?.LogError(ex, "Error during database file optimization");
                throw;
            }
        }

        /// <summary>
        /// ✅ DIAGNOSTICS: Check for missing indexes on frequently queried columns
        /// </summary>
        public async Task<List<string>> CheckForMissingIndexesAsync()
        {
            try
            {
                var missingIndexes = new List<string>();
                
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Check for tables without proper indexes
                var checkQueries = new Dictionary<string, string>
                {
                    ["Products.IsActive"] = "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND sql LIKE '%Products%IsActive%'",
                    ["Sales.SaleDate"] = "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND sql LIKE '%Sales%SaleDate%'",
                    ["SaleItems.ProductId"] = "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND sql LIKE '%SaleItems%ProductId%'",
                    ["ProductBarcodes.Barcode"] = "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND sql LIKE '%ProductBarcodes%Barcode%'"
                };

                foreach (var (indexName, query) in checkQueries)
                {
                    using var cmd = new SqliteCommand(query, connection);
                    var count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                    
                    if (count == 0)
                    {
                        missingIndexes.Add(indexName);
                    }
                }

                if (missingIndexes.Count > 0)
                {
                    Debug.WriteLine($"[INDEX-OPTIMIZER] ⚠️ Found {missingIndexes.Count} missing critical indexes");
                    foreach (var missing in missingIndexes)
                    {
                        Debug.WriteLine($"[INDEX-OPTIMIZER]   - Missing: {missing}");
                    }
                }
                else
                {
                    Debug.WriteLine("[INDEX-OPTIMIZER] ✅ All critical indexes are present");
                }

                return missingIndexes;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] ❌ Error checking for missing indexes: {ex.Message}");
                _logger?.LogError(ex, "Error checking for missing indexes");
                return new List<string>();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Check if database is properly initialized
        /// </summary>
        private async Task<bool> IsDatabaseInitializedAsync(SqliteConnection connection)
        {
            try
            {
                // Check if core tables exist
                var coreTableQuery = @"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='table' AND name IN ('Products', 'Sales', 'Users', 'Categories')";

                using var cmd = new SqliteCommand(coreTableQuery, connection);
                var coreTableCount = Convert.ToInt32(await cmd.ExecuteScalarAsync());

                return coreTableCount >= 4; // At least 4 core tables should exist
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] Error checking database initialization: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Check if a specific table exists
        /// </summary>
        private async Task<bool> TableExistsAsync(SqliteConnection connection, string tableName)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=@tableName";
                using var cmd = new SqliteCommand(query, connection);
                cmd.Parameters.AddWithValue("@tableName", tableName);
                var count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                return count > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] Error checking table existence for '{tableName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Extract table name from CREATE INDEX SQL
        /// </summary>
        private string ExtractTableNameFromIndex(string sql)
        {
            try
            {
                // Extract table name from "CREATE INDEX ... ON TableName(...)"
                var onIndex = sql.IndexOf(" ON ", StringComparison.OrdinalIgnoreCase);
                if (onIndex == -1) return "";

                var afterOn = sql.Substring(onIndex + 4).Trim();
                var parenIndex = afterOn.IndexOf('(');
                if (parenIndex == -1) return afterOn;

                return afterOn.Substring(0, parenIndex).Trim();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INDEX-OPTIMIZER] Error extracting table name from SQL: {ex.Message}");
                return "";
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
        }
    }

    /// <summary>
    /// Information about database index usage
    /// </summary>
    public class IndexUsageInfo
    {
        public string IndexName { get; set; }
        public string Definition { get; set; }
        public int RootPage { get; set; }
    }
}
