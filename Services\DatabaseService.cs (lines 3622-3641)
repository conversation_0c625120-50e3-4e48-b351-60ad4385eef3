using System.Data.SQLite;

public void MigrateUserPermissionsTable()
{
    try
    {
        using var connection = new SQLiteConnection(_connectionString);
        connection.Open();
        using var transaction = connection.BeginTransaction();

        try
        {
            // Drop existing UserPermissions table if it exists
            var dropTableSql = "DROP TABLE IF EXISTS UserPermissions;";
            using (var dropCommand = new SQLiteCommand(dropTableSql, connection))
            {
                dropCommand.ExecuteNonQuery();
            }

            // Create the UserPermissions table
            var createTableSql = @"
                CREATE TABLE IF NOT EXISTS UserPermissions (
            ";
            using (var createCommand = new SQLiteCommand(createTableSql, connection))
            {
                createCommand.ExecuteNonQuery();
            }

            transaction.Commit();
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            throw;
        }
    }
    catch (Exception ex)
    {
        throw;
    }
} 