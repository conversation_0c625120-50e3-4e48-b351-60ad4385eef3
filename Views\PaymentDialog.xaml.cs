using System;
using System.Windows;
using POSSystem.Models;

namespace POSSystem.Views
{
    public partial class PaymentDialog : Window
    {
        private readonly Sale _sale;
        public decimal PaymentAmount { get; private set; }

        public PaymentDialog(Sale sale)
        {
            InitializeComponent();
            _sale = sale;
            DataContext = sale;
            PaymentAmount = sale.GrandTotal;
            PaymentAmountInput.Text = PaymentAmount.ToString("N2");

            // Update payment amount when payment option changes
            FullPaymentOption.Checked += (s, e) => 
            {
                PaymentAmount = _sale.GrandTotal;
                PaymentAmountInput.Text = PaymentAmount.ToString("N2");
            };

            // Validate payment amount on text change
            PaymentAmountInput.TextChanged += (s, e) =>
            {
                if (decimal.TryParse(PaymentAmountInput.Text, out decimal amount))
                {
                    if (amount > _sale.GrandTotal)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("PaymentAmountExceedsTotal") as string ?? "Payment amount cannot exceed the total amount.",
                            Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        PaymentAmountInput.Text = _sale.GrandTotal.ToString("N2");
                    }
                    else if (amount <= 0)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("PaymentAmountMustBePositive") as string ?? "Payment amount must be greater than zero.",
                            Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        PaymentAmountInput.Text = _sale.GrandTotal.ToString("N2");
                    }
                }
            };
        }

        private void ProcessButton_Click(object sender, RoutedEventArgs e)
        {
            if (!decimal.TryParse(PaymentAmountInput.Text, out decimal amount))
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("InvalidPaymentAmount") as string ?? "Please enter a valid payment amount.",
                    Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return;
            }

            PaymentAmount = amount;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
} 