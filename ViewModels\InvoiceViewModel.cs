using Microsoft.Data.Sqlite;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Views;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using POSSystem.Services.InventoryManagement;

namespace POSSystem.ViewModels
{
    public class InvoiceViewModel : ViewModelBase
    {
        private readonly IDatabaseService _dbService;
        private readonly IAuthenticationService _authService;
        private readonly IStockService _stockService;
        private ObservableCollection<Invoice> _invoices;

        private Invoice _selectedInvoice;
        private string _searchQuery;
        private string _statusFilter;
        private string _typeFilter;
        private bool _isLoading = false;
        private bool _isEditing = false;
        private bool _isCreating = false;
        private bool _isDetailVisible = false;

        // Invoice details being edited
        private int _invoiceId;
        private string _invoiceNumber;
        private string _invoiceType = "Purchase";
        private DateTime _issueDate = DateTime.Now;
        private DateTime _dueDate = DateTime.Now.AddDays(30);
        private int? _customerId;
        private int? _supplierId;
        private decimal _subtotal;
        private decimal _discountAmount;
        private decimal _taxAmount;
        private decimal _grandTotal;
        private string _status = "Paid";
        private string _paymentTerms = "Net 30";
        private string _reference;
        private string _notes;
        private ObservableCollection<InvoiceItem> _invoiceItems;
        private ObservableCollection<InvoicePayment> _invoicePayments;
        private ObservableCollection<Product> _availableProducts;
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Supplier> _suppliers;

        // Selected item for editing
        private InvoiceItem _selectedItem;
        private int _selectedProductId;
        private decimal _quantity = 1m;
        private decimal _unitPrice;
        private decimal _total;

        // Add missing properties for the view

        // New properties for barcode search
        private bool _isBarcodeSearchMode = true;
        public bool IsBarcodeSearchMode
        {
            get { return _isBarcodeSearchMode; }
            set
            {
                _isBarcodeSearchMode = value;
                OnPropertyChanged();
            }
        }

        private string _barcodeSearch;
        public string BarcodeSearch
        {
            get { return _barcodeSearch; }
            set
            {
                _barcodeSearch = value;
                OnPropertyChanged();
            }
        }

        // Property to track if a product is currently selected
        private bool _isProductSelected;
        public bool IsProductSelected
        {
            get { return _isProductSelected; }
            set
            {
                _isProductSelected = value;
                OnPropertyChanged();
            }
        }

        // New property for selling price
        private decimal _sellingPrice;
        public decimal SellingPrice
        {
            get { return _sellingPrice; }
            set
            {
                _sellingPrice = value;
                OnPropertyChanged();
            }
        }

        // Selected product reference for additional details
        private Product _selectedProduct;
        public Product SelectedProduct
        {
            get { return _selectedProduct; }
            set
            {
                _selectedProduct = value;
                OnPropertyChanged();
                IsProductSelected = _selectedProduct != null;

                // When a product is selected, update pricing information
                if (_selectedProduct != null)
                {
                    if (InvoiceType == "Sales")
                    {
                        // Use batch-specific price for Sales when batch tracking is enabled (FIFO oldest batch)
                        if (_selectedProduct.TrackBatches)
                        {
                            var oldestBatch = _dbService.GetBatchesForProduct(_selectedProduct.Id)
                                ?.Where(b => b.Quantity > 0)
                                ?.OrderBy(b => b.CreatedAt)
                                ?.ThenBy(b => b.Id)
                                ?.FirstOrDefault();
                            var price = oldestBatch?.SellingPrice ?? _selectedProduct.SellingPrice;
                            UnitPrice = price;
                            SellingPrice = price;
                        }
                        else
                        {
                            UnitPrice = _selectedProduct.SellingPrice;
                            SellingPrice = _selectedProduct.SellingPrice;
                        }
                    }
                    else if (InvoiceType == "Purchase")
                    {
                        UnitPrice = _selectedProduct.PurchasePrice;

                        // Prefer the most recent non-zero batch selling price for this product
                        try
                        {
                            var recentBatchPrice = _dbService
                                .GetBatchesForProduct(_selectedProduct.Id)
                                ?.Where(b => b.SellingPrice > 0)
                                ?.OrderByDescending(b => b.CreatedAt)
                                ?.Select(b => (decimal?)b.SellingPrice)
                                ?.FirstOrDefault();

                            SellingPrice = recentBatchPrice ?? _selectedProduct.SellingPrice;
                        }
                        catch
                        {
                            // Fallback to product-level selling price
                            SellingPrice = _selectedProduct.SellingPrice;
                        }
                    }

                    // Update stock status
                    UpdateStockStatus();
                }
            }
        }

        // Property for checking if stock is sufficient
        private bool _isStockSufficient = true;
        public bool IsStockSufficient
        {
            get { return _isStockSufficient; }
            set
            {
                _isStockSufficient = value;
                OnPropertyChanged();
            }
        }

        // Property to show remaining stock after transaction
        private decimal _remainingStock;
        public decimal RemainingStock
        {
            get { return _remainingStock; }
            set
            {
                _remainingStock = value;
                OnPropertyChanged();
            }
        }

        // Status message for the status bar
        private string _statusMessage;
        public string StatusMessage
        {
            get { return _statusMessage; }
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // Validation message for add item form
        private string _validationMessage;
        public string ValidationMessage
        {
            get { return _validationMessage; }
            set
            {
                _validationMessage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasValidationMessage));
            }
        }

        // Event to request focus on the Quantity field in the view
        public event EventHandler FocusQuantityRequested;
        private void RequestFocusQuantity()
        {
            try { FocusQuantityRequested?.Invoke(this, EventArgs.Empty); } catch { }
        }

        // Controls whether to show advanced invoice details
        private bool _showAllDetails;
        public bool ShowAllDetails
        {
            get => _showAllDetails;
            set
            {
                if (_showAllDetails != value)
                {
                    _showAllDetails = value;
                    OnPropertyChanged();
                }
            }
        }

        // Whether DueDate should be visible/enabled based on status
        public bool ShowDueDate => !(string.Equals(Status, "Paid", StringComparison.OrdinalIgnoreCase)
                                   || string.Equals(Status, "Completed", StringComparison.OrdinalIgnoreCase)
                                   || string.Equals(Status, "Cancelled", StringComparison.OrdinalIgnoreCase));

        public bool HasValidationMessage => !string.IsNullOrEmpty(ValidationMessage);

        // Flag to indicate if there are payments
        private bool _hasPayments;
        public bool HasPayments
        {
            get { return _hasPayments; }
            set
            {
                _hasPayments = value;
                OnPropertyChanged();
            }
        }

        // Balance due calculation
        private decimal _balanceDue;
        public decimal BalanceDue
        {
            get { return _balanceDue; }
            set
            {
                _balanceDue = value;
                OnPropertyChanged();
            }
        }

        // Batch tracking properties
        private ObservableCollection<BatchStock> _availableBatches;
        public ObservableCollection<BatchStock> AvailableBatches
        {
            get { return _availableBatches; }
            set
            {
                _availableBatches = value;
                OnPropertyChanged();
            }
        }

        private BatchStock _selectedBatch;
        public BatchStock SelectedBatch
        {
            get { return _selectedBatch; }
            set
            {
                _selectedBatch = value;
                OnPropertyChanged();
                // Update expiry date and other fields based on selected batch
                if (_selectedBatch != null && SelectedProduct != null)
                {
                    // Update both the product and the separate property for the datepicker
                    SelectedProduct.ExpiryDate = _selectedBatch.ExpiryDate;
                    ProductExpiryDate = _selectedBatch.ExpiryDate;
                }
            }
        }

        private string _batchNumber;
        public string BatchNumber
        {
            get { return _batchNumber; }
            set
            {
                _batchNumber = value;
                OnPropertyChanged();
            }
        }

        private DateTime? _manufacturingDate;
        public DateTime? ManufacturingDate
        {
            get { return _manufacturingDate; }
            set
            {
                _manufacturingDate = value;
                OnPropertyChanged();
            }
        }

        // Product expiry date property (for the datepicker)
        private DateTime? _productExpiryDate;
        public DateTime? ProductExpiryDate
        {
            get { return _productExpiryDate; }
            set
            {
                _productExpiryDate = value;
                OnPropertyChanged();

                // When the expiry date is manually changed, update the selected product's expiry date
                if (SelectedProduct != null)
                {
                    SelectedProduct.ExpiryDate = value;
                }
            }
        }

        // Check if there are invoice items
        public bool HasItems => InvoiceItems != null && InvoiceItems.Count > 0;

        // Properties to control UI based on invoice type
        public bool IsInvoiceTypeCustomer => InvoiceType == "Sales";
        public bool IsInvoiceTypeSupplier => InvoiceType == "Purchase";

        // Title for the dialog based on mode
        public string DialogTitle => IsCreating ? "Create Invoice" : "Edit Invoice";

        // Property to check if an invoice is selected
        public bool HasSelectedInvoice => SelectedInvoice != null;

        // Add properties for batch handling
        private bool _useExistingBatch;
        public bool UseExistingBatch
        {
            get { return _useExistingBatch; }
            set
            {
                _useExistingBatch = value;
                OnPropertyChanged();

                if (_useExistingBatch && SelectedProductId > 0)
                {
                    // Load all batches when "Use Existing Batch" is checked
                    LoadAvailableBatchesForProductNoFilter(SelectedProductId);
                }
                else
                {
                    // Clear batch selection and ensure we'll create a new batch
                    SelectedBatch = null;

                    // Reset batch number to generate a new one
                    if (string.IsNullOrEmpty(BatchNumber))
                    {
                        BatchNumber = $"BATCH-{DateTime.Now:yyyyMMddHHmmssfff}-{SelectedProductId}";
                    }
                }
            }
        }

        #region Commands
        public ICommand LoadInvoicesCommand { get; private set; }
        public ICommand CreateInvoiceCommand { get; private set; }
        public ICommand EditInvoiceCommand { get; private set; }
        public ICommand SaveInvoiceCommand { get; private set; }
        public ICommand CancelEditCommand { get; private set; }
        public ICommand DeleteInvoiceCommand { get; private set; }
        public ICommand AddItemCommand { get; private set; }
        public ICommand RemoveItemCommand { get; private set; }
        public ICommand EditItemCommand { get; private set; }
        public ICommand UpdateItemCommand { get; private set; }
        public ICommand RecalculateTotalsCommand { get; private set; }
        public ICommand SearchByBarcodeCommand { get; private set; }
        public ICommand PrintInvoiceCommand { get; private set; }
        public ICommand OpenProductSelectionCommand { get; private set; }
        #endregion

        public InvoiceViewModel(IDatabaseService dbService, IAuthenticationService authService, IStockService stockService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _stockService = stockService ?? throw new ArgumentNullException(nameof(stockService));

            try
            {
                // Ensure invoice tables exist in database
                _dbService.EnsureInvoiceTablesExist();

                // Initialize collections
                Invoices = new ObservableCollection<Invoice>();
                InvoiceItems = new ObservableCollection<InvoiceItem>();
                InvoicePayments = new ObservableCollection<InvoicePayment>();
                AvailableProducts = new ObservableCollection<Product>();
                Customers = new ObservableCollection<Customer>();
                Suppliers = new ObservableCollection<Supplier>();
                AvailableBatches = new ObservableCollection<BatchStock>();

                // Subscribe to supplier updates - simple timer approach
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(3);
                timer.Tick += (s, e) => RefreshSuppliers();
                timer.Start();

                // Initialize commands
                LoadInvoicesCommand = new RelayCommand(param => LoadInvoices());
                CreateInvoiceCommand = new RelayCommand(param => CreateInvoice());
                EditInvoiceCommand = new RelayCommand(param => EditInvoice(), param => CanEditInvoice());
                SaveInvoiceCommand = new RelayCommand(param => SaveInvoice(), param => CanSaveInvoice());
                CancelEditCommand = new RelayCommand(param => CancelEdit());
                DeleteInvoiceCommand = new RelayCommand(param => DeleteInvoice(), param => CanDeleteInvoice());
                AddItemCommand = new RelayCommand(param => AddItem(), param => CanAddItem());
                RemoveItemCommand = new RelayCommand(param => RemoveItem(param), param => CanRemoveItem(param));
                EditItemCommand = new RelayCommand(param => BeginEditItem(param), param => CanEditItem(param));
                UpdateItemCommand = new RelayCommand(param => CommitEditItem(param), param => CanCommitEditItem(param));
                RecalculateTotalsCommand = new RelayCommand(param => RecalculateTotals());
                SearchByBarcodeCommand = new RelayCommand(param => SearchByBarcode(), param => CanSearchByBarcode());
                PrintInvoiceCommand = new RelayCommand(param => PrintInvoice(), param => CanPrintInvoice());
                OpenProductSelectionCommand = new RelayCommand(param => OpenProductSelection());

                // Set defaults
                StatusFilter = "All";
                TypeFilter = "All";
                IssueDate = DateTime.Today;
                DueDate = DateTime.Today.AddDays(30);
                Status = "Paid";
                PaymentTerms = "Net 30";
                Quantity = 1;
                IsBarcodeSearchMode = true;

                // Load initial data
                LoadLookupData();
                LoadInvoices();

                // NOTE: If you need to import existing purchase orders, uncomment this method
                // ImportExistingPurchaseOrders();

                StatusMessage = "Ready";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error initializing: {ex.Message}";
                MessageBox.Show($"Error initializing invoice system: {ex.Message}", "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Properties
        public ObservableCollection<Invoice> Invoices
        {
            get { return _invoices; }
            set
            {
                _invoices = value;
                OnPropertyChanged();
            }
        }

        public Invoice SelectedInvoice
        {
            get { return _selectedInvoice; }
            set
            {
                _selectedInvoice = value;
                if (_selectedInvoice != null && !_isEditing && !_isCreating)
                {
                    LoadInvoiceDetails(_selectedInvoice.Id);
                    IsDetailVisible = true;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedInvoice));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string SearchQuery
        {
            get { return _searchQuery; }
            set
            {
                _searchQuery = value;
                OnPropertyChanged();
                FilterInvoices();
            }
        }

        public string StatusFilter
        {
            get { return _statusFilter; }
            set
            {
                _statusFilter = value;
                OnPropertyChanged();
                FilterInvoices();
            }
        }

        public string TypeFilter
        {
            get { return _typeFilter; }
            set
            {
                _typeFilter = value;
                OnPropertyChanged();
                FilterInvoices();
            }
        }

        public bool IsLoading
        {
            get { return _isLoading; }
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditing
        {
            get { return _isEditing; }
            set
            {
                _isEditing = value;
                OnPropertyChanged();
            }
        }

        public bool IsCreating
        {
            get { return _isCreating; }
            set
            {
                _isCreating = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DialogTitle));
            }
        }

        public bool IsDetailVisible
        {
            get { return _isDetailVisible; }
            set
            {
                _isDetailVisible = value;
                OnPropertyChanged();
            }
        }

        // Invoice detail properties
        public int InvoiceId
        {
            get { return _invoiceId; }
            set
            {
                _invoiceId = value;
                OnPropertyChanged();
            }
        }

        public string InvoiceNumber
        {
            get { return _invoiceNumber; }
            set
            {
                _invoiceNumber = value;
                OnPropertyChanged();
            }
        }

        public string InvoiceType
        {
            get { return _invoiceType; }
            set
            {
                string oldValue = _invoiceType;
                _invoiceType = value;

                // Clear the inappropriate ID based on the new invoice type
                if (_invoiceType == "Sales")
                {
                    // If changing to Sales, clear any supplier selection
                    _supplierId = null;
                    OnPropertyChanged(nameof(SupplierId));
                }
                else if (_invoiceType == "Purchase")
                {
                    // If changing to Purchase, clear any customer selection
                    _customerId = null;
                    OnPropertyChanged(nameof(CustomerId));
                }

                OnPropertyChanged();
                // Also notify for the dependent properties
                OnPropertyChanged(nameof(IsInvoiceTypeCustomer));
                OnPropertyChanged(nameof(IsInvoiceTypeSupplier));

                // Update the stock calculation when invoice type changes
                UpdateStockStatus();
            }
        }

        public DateTime IssueDate
        {
            get { return _issueDate; }
            set
            {
                _issueDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime DueDate
        {
            get { return _dueDate; }
            set
            {
                _dueDate = value;
                OnPropertyChanged();
            }
        }

        public int? CustomerId
        {
            get { return _customerId; }
            set
            {
                _customerId = value;
                if (_customerId.HasValue && _supplierId.HasValue)
                {
                    // If both customer and supplier are set, clear the other based on invoice type
                    if (InvoiceType == "Purchase")
                        _customerId = null;
                    else
                        _supplierId = null;
                }
                OnPropertyChanged();
            }
        }

        public int? SupplierId
        {
            get { return _supplierId; }
            set
            {
                _supplierId = value;
                if (_customerId.HasValue && _supplierId.HasValue)
                {
                    // If both customer and supplier are set, clear the other based on invoice type
                    if (InvoiceType == "Sales")
                        _supplierId = null;
                    else
                        _customerId = null;
                }
                OnPropertyChanged();
            }
        }

        public decimal Subtotal
        {
            get { return _subtotal; }
            set
            {
                _subtotal = value;
                OnPropertyChanged();
            }
        }

        public decimal DiscountAmount
        {
            get { return _discountAmount; }
            set
            {
                _discountAmount = value;
                RecalculateTotals();
                OnPropertyChanged();
            }
        }

        public decimal TaxAmount
        {
            get { return _taxAmount; }
            set
            {
                _taxAmount = value;
                RecalculateTotals();
                OnPropertyChanged();
            }
        }

        public decimal GrandTotal
        {
            get { return _grandTotal; }
            set
            {
                _grandTotal = value;
                OnPropertyChanged();
            }
        }

        public string Status
        {
            get { return _status; }
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowDueDate));
            }
        }

        public string PaymentTerms
        {
            get { return _paymentTerms; }
            set
            {
                _paymentTerms = value;
                OnPropertyChanged();
            }
        }

        public string Reference
        {
            get { return _reference; }
            set
            {
                _reference = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get { return _notes; }
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<InvoiceItem> InvoiceItems
        {
            get { return _invoiceItems; }
            set
            {
                _invoiceItems = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<InvoicePayment> InvoicePayments
        {
            get { return _invoicePayments; }
            set
            {
                _invoicePayments = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Product> AvailableProducts
        {
            get { return _availableProducts; }
            set
            {
                _availableProducts = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Customer> Customers
        {
            get { return _customers; }
            set
            {
                _customers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get { return _suppliers; }
            set
            {
                _suppliers = value;
                OnPropertyChanged();
            }
        }

        public InvoiceItem SelectedItem
        {
            get { return _selectedItem; }
            set
            {
                _selectedItem = value;
                if (_selectedItem != null)
                {
                    SelectedProductId = _selectedItem.ProductId;
                    Quantity = _selectedItem.Quantity;
                    UnitPrice = _selectedItem.UnitPrice;
                    Total = _selectedItem.Total;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsItemInEditMode));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        // Flag for inline editing state (bound from the view)
        public bool IsItemInEditMode => SelectedItem != null && _itemBeingEdited == SelectedItem;

        // Method to check if a specific item is being edited
        public bool IsItemBeingEdited(InvoiceItem item)
        {
            return _itemBeingEdited != null && _itemBeingEdited == item;
        }

        // Property to trigger UI refresh for button visibility
        private int _editModeRefreshTrigger = 0;
        public int EditModeRefreshTrigger
        {
            get => _editModeRefreshTrigger;
            set
            {
                _editModeRefreshTrigger = value;
                OnPropertyChanged();
            }
        }

        private InvoiceItem _itemBeingEdited;

        public int SelectedProductId
        {
            get { return _selectedProductId; }
            set
            {
                _selectedProductId = value;
                OnPropertyChanged();

                // When a product is selected, load its details and update related fields
                if (_selectedProductId > 0)
                {
                    var product = AvailableProducts.FirstOrDefault(p => p.Id == _selectedProductId);
                    if (product != null)
                    {
                        SelectedProduct = product;
                        // Set UnitPrice based on invoice type
                        if (InvoiceType == "Sales")
                        {
                            // For sales, prefer batch-specific price from FIFO oldest batch if tracking batches
                            if (product.TrackBatches)
                            {
                                var oldestBatch = _dbService.GetBatchesForProduct(product.Id)
                                    ?.Where(b => b.Quantity > 0)
                                    ?.OrderBy(b => b.CreatedAt)
                                    ?.ThenBy(b => b.Id)
                                    ?.FirstOrDefault();
                                UnitPrice = oldestBatch?.SellingPrice ?? product.SellingPrice;
                            }
                            else
                            {
                                UnitPrice = product.SellingPrice;
                            }
                        }
                        else if (InvoiceType == "Purchase")
                        {
                            UnitPrice = product.PurchasePrice;
                        }
                        else
                        {
                            UnitPrice = product.DefaultPrice;
                        }

                        // Ensure UnitPrice is never 0 - use fallback values
                        if (UnitPrice <= 0)
                        {
                            if (product.SellingPrice > 0)
                            {
                                UnitPrice = product.SellingPrice;
                            }
                            else if (product.PurchasePrice > 0)
                            {
                                UnitPrice = product.PurchasePrice;
                            }
                            else if (product.DefaultPrice > 0)
                            {
                                UnitPrice = product.DefaultPrice;
                            }
                            else
                            {
                                // Last resort - set a minimal price
                                UnitPrice = 0.01m;
                            }
                        }

                        SellingPrice = product.SellingPrice;

                        // Update the ProductExpiryDate property with the product's expiry date
                        ProductExpiryDate = product.ExpiryDate;

                        // Load batches based on invoice type and batch tracking settings
                        if (product.TrackBatches)
                        {
                            if (InvoiceType == "Sales")
                            {
                                // For sales, only show batches with available stock
                                LoadAvailableBatchesForProduct(_selectedProductId);
                            }
                            else if (InvoiceType == "Purchase" && UseExistingBatch)
                            {
                                // For purchases, show all batches when adding to existing
                                LoadAvailableBatchesForProductNoFilter(_selectedProductId);
                            }
                        }

                        UpdateStockStatus();
                        RecalculateItemTotal();
                    }
                }
                else
                {
                    SelectedProduct = null;
                    UnitPrice = 0;
                    SellingPrice = 0;
                    ProductExpiryDate = null;
                    AvailableBatches.Clear();
                }

                // Clear validation message when product selection changes
                ValidationMessage = string.Empty;

                // Update command states
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public decimal Quantity
        {
            get { return _quantity; }
            set
            {
                _quantity = Math.Max(1m, value); // Ensure quantity is at least 1
                OnPropertyChanged();

                // Update the total when quantity changes
                RecalculateItemTotal();

                // Update stock status when quantity changes
                UpdateStockStatus();

                // Clear validation message when quantity changes
                ValidationMessage = string.Empty;

                // Update command states
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public decimal UnitPrice
        {
            get { return _unitPrice; }
            set
            {
                _unitPrice = value;
                RecalculateItemTotal();
                OnPropertyChanged();

                // Clear validation message when unit price changes
                ValidationMessage = string.Empty;

                CommandManager.InvalidateRequerySuggested();
            }
        }

        public decimal Total
        {
            get { return _total; }
            set
            {
                _total = value;
                OnPropertyChanged();
            }
        }
        #endregion

        #region Methods
        private void LoadInvoices()
        {
                IsLoading = true;
                StatusMessage = "Loading invoices...";

            try
            {
                // Ensure the invoice tables exist
                _dbService.EnsureInvoiceTablesExist();

                // Get invoices with the selected filters
                var invoices = _dbService.GetInvoices(
                    TypeFilter == "All" ? null : TypeFilter,
                    StatusFilter == "All" ? null : StatusFilter);

                Invoices.Clear();

                // Apply search filter if applicable
                if (!string.IsNullOrWhiteSpace(SearchQuery))
                {
                    var searchQuery = SearchQuery.ToLower();
                    invoices = invoices.Where(i =>
                        i.InvoiceNumber.ToLower().Contains(searchQuery) ||
                        i.Status.ToLower().Contains(searchQuery) ||
                        (i.Notes != null && i.Notes.ToLower().Contains(searchQuery))).ToList();
                }

                foreach (var invoice in invoices)
                {
                    Invoices.Add(invoice);
                }

                StatusMessage = $"{Invoices.Count} invoices loaded";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading invoices: {ex.Message}";
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }



        public void RefreshSuppliers()
        {
            try
            {
                var suppliers = _dbService.GetAllSuppliers();
                var currentCount = Suppliers.Count;
                var newCount = suppliers.Count;

                // Only refresh if the count has changed
                if (currentCount != newCount)
                {
                    Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Suppliers.Clear();
                        foreach (var supplier in suppliers)
                        {
                            Suppliers.Add(supplier);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing suppliers: {ex.Message}");
            }
        }

        private void LoadLookupData()
        {
            try
            {
                // Ensure all required tables exist
                _dbService.EnsureInvoiceTablesExist();

                // Load products
                var products = _dbService.GetAllProducts();
                AvailableProducts.Clear();
                foreach (var product in products)
                {
                    AvailableProducts.Add(product);
                }

                // Load customers
                var customers = _dbService.GetAllCustomers();
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                // Load suppliers
                RefreshSuppliers();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading lookup data: {ex.Message}";
                MessageBox.Show($"Error loading lookup data: {ex.Message}", "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadInvoiceDetails(int invoiceId)
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Loading invoice details...";

                var invoice = _dbService.GetInvoiceById(invoiceId);
                if (invoice != null)
                {
                    InvoiceId = invoice.Id;
                    InvoiceNumber = invoice.InvoiceNumber;
                    InvoiceType = invoice.Type;
                    IssueDate = invoice.IssueDate;
                    DueDate = invoice.DueDate;
                    CustomerId = invoice.CustomerId;
                    SupplierId = invoice.SupplierId;
                    Subtotal = invoice.Subtotal;
                    DiscountAmount = invoice.DiscountAmount;
                    TaxAmount = invoice.TaxAmount;
                    GrandTotal = invoice.GrandTotal;
                    Status = invoice.Status;
                    PaymentTerms = invoice.PaymentTerms;
                    Reference = invoice.Reference;
                    Notes = invoice.Notes;

                    // Clear and load items
                    InvoiceItems.Clear();
                    if (invoice.Items != null)
                    {
                        foreach (var item in invoice.Items)
                        {
                            InvoiceItems.Add(item);
                        }
                    }

                    // Clear and load payments, set HasPayments and BalanceDue
                    InvoicePayments.Clear();
                    decimal totalPayments = 0;
                    if (invoice.Payments != null && invoice.Payments.Any())
                    {
                        foreach (var payment in invoice.Payments)
                        {
                            InvoicePayments.Add(payment);
                            totalPayments += payment.Amount;
                        }
                        HasPayments = true;
                        BalanceDue = GrandTotal - totalPayments;
                    }
                    else
                    {
                        HasPayments = false;
                        BalanceDue = GrandTotal;
                    }

                    StatusMessage = $"Loaded invoice {InvoiceNumber}";
                }
                else
                {
                    StatusMessage = $"Invoice with ID {invoiceId} not found";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading invoice: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CreateInvoice()
        {
            ClearForm();
            IsCreating = true;
            IsEditing = true;
            IsDetailVisible = true;

            // Generate a new invoice number
            try
            {
                int nextInvoiceNumber = _dbService.GetNextInvoiceNumber();
                InvoiceNumber = $"INV-{DateTime.Now:yyyyMMdd}-{nextInvoiceNumber:D4}";
            }
            catch
            {
                // If there's an error generating the invoice number, create a default one
                InvoiceNumber = $"INV-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private void EditInvoice()
        {
            if (SelectedInvoice == null)
                return;

            LoadInvoiceDetails(SelectedInvoice.Id);
            IsEditing = true;
            IsCreating = false;
        }

        private bool CanEditInvoice()
        {
            return SelectedInvoice != null && !IsEditing && !IsCreating;
        }

        private void SaveInvoice()
        {
            try
            {
                // Debug info
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] SaveInvoice - Starting to save invoice {InvoiceNumber}");
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Invoice Type: {InvoiceType}, Status: {Status}, Items Count: {InvoiceItems.Count}");

                // Get current user for CreatedByUserId field
                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    MessageBox.Show("No user is currently logged in. Please log in and try again.", "Authentication Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Create invoice object
                var invoice = new Invoice
                {
                    InvoiceNumber = InvoiceNumber,
                    Type = InvoiceType,
                    IssueDate = IssueDate,
                    DueDate = DueDate,
                    CustomerId = CustomerId,
                    SupplierId = SupplierId,
                    Subtotal = Subtotal,
                    DiscountAmount = DiscountAmount,
                    TaxAmount = TaxAmount,
                    GrandTotal = GrandTotal,
                    Status = Status,
                    PaymentTerms = PaymentTerms,
                    Reference = Reference,
                    Notes = Notes,
                    Items = InvoiceItems.ToList(),
                    Payments = InvoicePayments.ToList(),
                    // Set required fields for two-tier invoice system
                    CreatedByUserId = currentUser.Id,
                    DraftCreatedAt = DateTime.Now,
                    RequiresAdminCompletion = false, // Set to false for direct invoice creation
                    // Ensure timestamp fields are set
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                if (IsCreating)
                {
                    // Create new invoice
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Creating new invoice");
                    int newInvoiceId = _dbService.CreateInvoice(invoice);
                    InvoiceId = newInvoiceId;
                    invoice.Id = newInvoiceId;
                    Invoices.Add(invoice);

                    // Process batches based on invoice type and status
                    if (InvoiceType == "Purchase" && (Status == "Received" || Status == "Paid"))
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing Purchase invoice with {Status} status");
                        ProcessPurchaseInvoiceForBatches(invoice);
                    }
                    else if (InvoiceType == "Sales" && (Status == "Completed" || Status == "Paid"))
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing Sales invoice with Completed/Paid status");
                        ProcessSalesInvoiceForBatches(invoice);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] No stock processing needed for Type: {InvoiceType}, Status: {Status}");
                    }

                    MessageBox.Show($"Invoice {InvoiceNumber} created successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);

                    // Refresh available products to reflect updated quantities
                    RefreshProductData();
                }
                else
                {
                    // Update existing invoice
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Updating existing invoice");
                    invoice.Id = InvoiceId;
                    _dbService.UpdateInvoice(invoice);

                    // Update the invoice in the collection
                    var existingInvoice = Invoices.FirstOrDefault(i => i.Id == InvoiceId);
                    if (existingInvoice != null)
                    {
                        string previousStatus = existingInvoice.Status;
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Previous status: {previousStatus}, New status: {Status}");

                        int index = Invoices.IndexOf(existingInvoice);
                        Invoices[index] = invoice;

                        // Process batches if status has changed to Received/Completed
                        if (previousStatus != Status)
                        {
                            if (InvoiceType == "Purchase" && (Status == "Received" || Status == "Paid"))
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing Purchase invoice with new {Status} status");
                                ProcessPurchaseInvoiceForBatches(invoice);
                            }
                            else if (InvoiceType == "Sales" && (Status == "Completed" || Status == "Paid"))
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing Sales invoice with new Completed/Paid status");
                                ProcessSalesInvoiceForBatches(invoice);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] No stock processing needed for Type: {InvoiceType}, Status: {Status}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Status unchanged, skipping stock processing");
                        }
                    }

                    MessageBox.Show($"Invoice {InvoiceNumber} updated successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);

                    // Refresh available products to reflect updated quantities
                    RefreshProductData();
                }

                IsEditing = false;
                IsCreating = false;
                SelectedInvoice = invoice;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error saving invoice: {ex.Message}");
                MessageBox.Show($"Error saving invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanSaveInvoice()
        {
            // ✅ PERFORMANCE FIX: Reduce debug output frequency to prevent UI blocking
            bool isValid = true;
            var validationIssues = new List<string>();

            // Validate required fields efficiently
            if (string.IsNullOrWhiteSpace(InvoiceNumber))
            {
                validationIssues.Add("Invoice Number is missing");
                isValid = false;
            }

            if (string.IsNullOrWhiteSpace(InvoiceType))
            {
                validationIssues.Add("Invoice Type is missing");
                isValid = false;
            }

            if (string.IsNullOrWhiteSpace(Status))
            {
                validationIssues.Add("Status is missing");
                isValid = false;
            }

            // Validate that invoice has at least one item
            if (InvoiceItems == null || InvoiceItems.Count == 0)
            {
                validationIssues.Add("No invoice items added");
                isValid = false;
            }

            // Validate customer or supplier based on type
            if (InvoiceType == "Sales" && !CustomerId.HasValue)
            {
                validationIssues.Add("Customer is not selected for Sales invoice");
                isValid = false;
            }

            if (InvoiceType == "Purchase" && !SupplierId.HasValue)
            {
                validationIssues.Add("Supplier is not selected for Purchase invoice");
                isValid = false;
            }

            // ✅ PERFORMANCE FIX: Only output debug info when there are validation issues
            // This prevents constant debug output that can cause UI blocking
            if (!isValid && validationIssues.Count > 0)
            {
                var debugInfo = "CanSaveInvoice validation:\n- " + string.Join("\n- ", validationIssues);
                System.Diagnostics.Debug.WriteLine(debugInfo);
            }

            return isValid;
        }

        private void CancelEdit()
        {
            if (IsCreating)
            {
                IsDetailVisible = false;
            }
            else if (SelectedInvoice != null)
            {
                // Reload original invoice details
                LoadInvoiceDetails(SelectedInvoice.Id);
            }

            IsEditing = false;
            IsCreating = false;
        }

        private void DeleteInvoice()
        {
            if (SelectedInvoice == null)
                return;

            var result = MessageBox.Show($"Are you sure you want to delete invoice {SelectedInvoice.InvoiceNumber}?",
                "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _dbService.DeleteInvoice(SelectedInvoice.Id);
                    Invoices.Remove(SelectedInvoice);
                    MessageBox.Show("Invoice deleted successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                    IsDetailVisible = false;
                    SelectedInvoice = null;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private bool CanDeleteInvoice()
        {
            return SelectedInvoice != null && !IsEditing && !IsCreating;
        }

        private void AddItem()
        {
            // Clear previous validation message
            ValidationMessage = string.Empty;

            if (SelectedProductId <= 0)
            {
                ValidationMessage = "Please select a product";
                return;
            }

            if (UnitPrice <= 0)
            {
                ValidationMessage = "Unit price must be greater than zero";
                return;
            }

            if (Quantity <= 0)
            {
                ValidationMessage = "Quantity must be greater than zero";
                return;
            }

            // Check stock availability for sales invoices (skip for services)
            if (InvoiceType == "Sales" && SelectedProduct.Type != ProductType.Service && !IsStockSufficient)
            {
                var result = MessageBox.Show(
                    $"Warning: Insufficient stock available. Current stock: {SelectedProduct.StockQuantity}, Requested: {Quantity}.\n\nDo you want to continue anyway?",
                    "Stock Warning",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );

                if (result == MessageBoxResult.No)
                {
                    return;
                }
            }

            var product = AvailableProducts.FirstOrDefault(p => p.Id == SelectedProductId);
            if (product != null)
            {
                // Record batch information for Purchase invoices to use later
                string batchNumberToUse = null;
                int? selectedBatchId = null;
                bool useExistingBatchValue = false;

                if (InvoiceType == "Purchase" && product.TrackBatches)
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Recording batch info for purchase. UseExistingBatch: {UseExistingBatch}");

                    // If using an existing batch, ensure it's properly selected
                    if (UseExistingBatch && SelectedBatch != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Selected batch: {SelectedBatch.BatchNumber}, ID: {SelectedBatch.Id}");
                        selectedBatchId = SelectedBatch.Id;
                        batchNumberToUse = SelectedBatch.BatchNumber;
                        useExistingBatchValue = true;
                    }
                    else
                    {
                        // Ensure we have a batch number for new batches
                        if (string.IsNullOrEmpty(BatchNumber))
                        {
                            batchNumberToUse = $"BATCH-{DateTime.Now:yyyyMMddHHmmssfff}-{SelectedProductId}";
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Generated batch number: {batchNumberToUse}");
                        }
                        else
                        {
                            batchNumberToUse = BatchNumber;
                        }
                        useExistingBatchValue = false;
                    }
                }

                // Check if item already exists in invoice
                var existingItem = InvoiceItems.FirstOrDefault(i => i.ProductId == SelectedProductId);

                // For purchase invoices with batch tracking, we should add a new item for different batches or different prices
                bool shouldAddNewItem = (InvoiceType == "Purchase" && product.TrackBatches) || existingItem == null;

                if (shouldAddNewItem)
                {
                    // Make a copy of the product to preserve editable properties like ExpiryDate
                    Product productCopy = new Product
                    {
                        Id = product.Id,
                        Name = product.Name,
                        SKU = product.SKU,
                        Description = product.Description,
                        PurchasePrice = product.PurchasePrice,
                        SellingPrice = product.SellingPrice,
                        Barcode = product.Barcode,
                        StockQuantity = product.StockQuantity,
                        CategoryId = product.CategoryId,
                        TrackBatches = product.TrackBatches,
                        // Use the ProductExpiryDate from the DatePicker control
                        ExpiryDate = ProductExpiryDate
                    };

                    // Add new item
                    var newItem = new InvoiceItem
                    {
                        InvoiceId = InvoiceId,
                        ProductId = SelectedProductId,
                        ProductName = product.Name, // Set the required ProductName field
                        Product = productCopy,
                        Quantity = Quantity,
                        UnitPrice = UnitPrice,
                        SellingPrice = SellingPrice,
                        Total = Quantity * UnitPrice,
                        CreatedAt = DateTime.Now // Set the required CreatedAt field
                    };

                    // Preserve batch information by setting appropriate properties on the invoice item
                    if (InvoiceType == "Purchase" && product.TrackBatches)
                    {
                        // Store batch information directly in the invoice item
                        newItem.BatchNumber = batchNumberToUse;

                        // Store additional batch information as custom properties
                        newItem.CustomProperties = new Dictionary<string, object>
                        {
                            { "UseExistingBatch", useExistingBatchValue },
                            { "SelectedBatchId", selectedBatchId },
                            { "ManufacturingDate", ManufacturingDate }
                        };

                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Stored batch info on item: Batch#{batchNumberToUse}, UseExisting:{useExistingBatchValue}, BatchId:{selectedBatchId}");
                    }

                    InvoiceItems.Add(newItem);
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Added new item with ProductId:{newItem.ProductId}, UnitPrice:{newItem.UnitPrice}");
                }
                else
                {
                    // Update existing item for non-batch items or sales invoices
                    existingItem.Quantity += Quantity;
                    existingItem.UnitPrice = UnitPrice;
                    existingItem.SellingPrice = SellingPrice;
                    existingItem.Total = existingItem.Quantity * existingItem.UnitPrice;

                    // Update product data including expiry date
                    if (existingItem.Product != null && SelectedProduct != null)
                    {
                        existingItem.Product.ExpiryDate = SelectedProduct.ExpiryDate;
                    }

                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] AddItem - Updated existing item. New quantity: {existingItem.Quantity}");
                }

                // Reset fields for next item
                SelectedProductId = 0;
                Quantity = 1;
                UnitPrice = 0;
                SellingPrice = 0;
                BarcodeSearch = string.Empty;
                BatchNumber = string.Empty;
                ManufacturingDate = null;
                ProductExpiryDate = null;
                SelectedBatch = null;
                UseExistingBatch = false;

                // Recalculate invoice totals
                RecalculateTotals();

                // Update command states
                CommandManager.InvalidateRequerySuggested();

                // Clear validation message on successful add
                ValidationMessage = string.Empty;

                StatusMessage = $"Added {product.Name} to invoice";
            }
        }

        private bool CanAddItem()
        {
            return SelectedProductId > 0 && Quantity > 0 && UnitPrice > 0;
        }

        private void RemoveItem(object parameter)
        {
            // Handle both parameter-based removal (from ItemsControl) and SelectedItem-based removal (from DataGrid)
            InvoiceItem itemToRemove = null;

            if (parameter is InvoiceItem invoiceItem)
            {
                // Parameter-based removal (from ItemsControl button)
                itemToRemove = invoiceItem;
            }
            else if (SelectedItem != null)
            {
                // SelectedItem-based removal (from DataGrid or other selection-based UI)
                itemToRemove = SelectedItem;
            }

            if (itemToRemove != null && InvoiceItems.Contains(itemToRemove))
            {
                InvoiceItems.Remove(itemToRemove);

                // Clear SelectedItem if it was the removed item
                if (SelectedItem == itemToRemove)
                {
                    SelectedItem = null;
                }

                RecalculateTotals();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        private bool CanRemoveItem(object parameter)
        {
            // Handle both parameter-based and SelectedItem-based scenarios
            if (parameter is InvoiceItem invoiceItem)
            {
                return InvoiceItems.Contains(invoiceItem);
            }

            return SelectedItem != null && InvoiceItems.Contains(SelectedItem);
        }

        private void RecalculateItemTotal()
        {
            Total = Quantity * UnitPrice;
        }

        private void RecalculateTotals()
        {
            try
            {
                // Safely calculate subtotal with null checks
                Subtotal = 0;

                if (InvoiceItems != null)
                {
                    foreach (var item in InvoiceItems.Where(i => i != null))
                    {
                        // Use explicit null check for Total
                        if (item.Total != null)
                        {
                            Subtotal += item.Total;
                        }
                        else
                        {
                            // If Total is null, recalculate from Quantity and UnitPrice
                            Subtotal += item.Quantity * item.UnitPrice;
                        }
                    }
                }

                GrandTotal = Subtotal - DiscountAmount + TaxAmount;

                // Also calculate balance due if there are payments
                if (HasPayments && InvoicePayments != null && InvoicePayments.Count > 0)
                {
                    decimal totalPayments = 0;
                    foreach (var payment in InvoicePayments.Where(p => p != null))
                    {
                        totalPayments += payment.Amount;
                    }
                    BalanceDue = GrandTotal - totalPayments;
                }
                else
                {
                    BalanceDue = GrandTotal;
                }

                // Indicate the command state has changed
                CommandManager.InvalidateRequerySuggested();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in RecalculateTotals: {ex.Message}");
                // Continue without crashing, just log the error
            }
        }

        private void ClearForm()
        {
            InvoiceId = 0;
            InvoiceNumber = string.Empty;
            InvoiceType = "Purchase";
            IssueDate = DateTime.Now;
            DueDate = DateTime.Now.AddDays(30);
            CustomerId = null;
            // Default supplier selection: choose the first available supplier if any
            SupplierId = Suppliers != null && Suppliers.Count > 0 ? Suppliers[0].Id : (int?)null;
            Subtotal = 0;
            DiscountAmount = 0;
            TaxAmount = 0;
            GrandTotal = 0;
            Status = "Paid";
            PaymentTerms = "Net 30";
            Reference = string.Empty;
            Notes = string.Empty;
            InvoiceItems.Clear();
            InvoicePayments.Clear();

            // Clear batch tracking information
            BatchNumber = string.Empty;
            ManufacturingDate = null;
            SelectedBatch = null;
            AvailableBatches.Clear();
        }

        private void FilterInvoices()
        {
            // Logic for filtering invoices based on search query, status, and type
            try
            {
                var filteredInvoices = _dbService.GetInvoices(
                    type: (_typeFilter == "All" || string.IsNullOrWhiteSpace(_typeFilter)) ? null : _typeFilter,
                    status: (_statusFilter == "All" || string.IsNullOrWhiteSpace(_statusFilter)) ? null : _statusFilter);

                // Additional search query filtering
                if (!string.IsNullOrWhiteSpace(_searchQuery))
                {
                    string searchTermLower = _searchQuery.ToLower();
                    filteredInvoices = filteredInvoices.Where(i =>
                        i.InvoiceNumber.ToLower().Contains(searchTermLower) ||
                        (i.Status != null && i.Status.ToLower().Contains(searchTermLower)) ||
                        (i.Notes != null && i.Notes.ToLower().Contains(searchTermLower)) ||
                        (i.Customer != null && (i.Customer.FirstName + " " + i.Customer.LastName).ToLower().Contains(searchTermLower)) ||
                        (i.Supplier != null && i.Supplier.Name.ToLower().Contains(searchTermLower))).ToList();
                }

                Invoices.Clear();
                foreach (var invoice in filteredInvoices)
                {
                    Invoices.Add(invoice);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering invoices: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchByBarcode()
        {
            if (string.IsNullOrWhiteSpace(BarcodeSearch))
            {
                StatusMessage = "Please enter a barcode to search";
                return;
            }

            try
            {
                var product = AvailableProducts.FirstOrDefault(p =>
                    p.Barcodes != null && p.Barcodes.Any(b => b.Barcode.Equals(BarcodeSearch, StringComparison.OrdinalIgnoreCase)));

                if (product != null)
                {
                    SelectedProductId = product.Id;
                    StatusMessage = $"Product found: {product.Name}";

                    // Request focus on quantity field for immediate editing
                    RequestFocusQuantity();
                }
                else
                {
                    StatusMessage = "No product found with that barcode";
                    SelectedProduct = null;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error searching: {ex.Message}";
            }
        }

        private bool CanSearchByBarcode()
        {
            return IsBarcodeSearchMode && !string.IsNullOrWhiteSpace(BarcodeSearch);
        }

        private void UpdateStockStatus()
        {
            if (SelectedProduct != null)
            {
                // Calculate remaining stock based on invoice type
                if (InvoiceType == "Sales")
                {
                    // For sales, we subtract quantity from stock
                    RemainingStock = SelectedProduct.StockQuantity - Quantity;
                    IsStockSufficient = RemainingStock >= 0;
                }
                else if (InvoiceType == "Purchase")
                {
                    // For purchases, we add quantity to stock
                    RemainingStock = SelectedProduct.StockQuantity + Quantity;
                    IsStockSufficient = true; // Always sufficient for purchases
                }
                else
                {
                    // Fallback case
                    RemainingStock = SelectedProduct.StockQuantity;
                    IsStockSufficient = true;
                }
            }
            else
            {
                RemainingStock = 0;
                IsStockSufficient = true;
            }
        }

        private void LoadAvailableBatchesForProduct(int productId)
        {
            if (productId <= 0)
            {
                AvailableBatches.Clear();
                return;
            }

            try
            {
                var batches = _dbService.GetBatchesForProduct(productId)
                    .Where(b => b.Quantity > 0)
                    .OrderBy(b => b.CreatedAt)
                    .ThenBy(b => b.Id)
                    .ToList();

                AvailableBatches.Clear();
                foreach (var batch in batches)
                {
                    // Add display name property for UI display
                    batch.DisplayName = $"Batch #{batch.BatchNumber} - Exp: {batch.ExpiryDate?.ToString("MM/dd/yyyy") ?? "N/A"} - Qty: {batch.Quantity}";
                    AvailableBatches.Add(batch);
                }

                if (AvailableBatches.Count > 0)
                {
                    SelectedBatch = AvailableBatches[0]; // Select first batch by default
                }
                else
                {
                    SelectedBatch = null;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading batches: {ex.Message}";
            }
        }

        private string GenerateBatchNumber(string invoiceNumber, InvoiceItem item)
        {
            // Generate a unique batch number based on invoice number and current date
            return $"{invoiceNumber}-{DateTime.Now:yyyyMMddHHmmss}-{item.ProductId}";
        }

        private void ProcessPurchaseInvoiceForBatches(Invoice invoice)
        {
            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] ProcessPurchaseInvoiceForBatches - Starting for Invoice #{invoice.InvoiceNumber}");

            if (invoice.Type != "Purchase" || (invoice.Status != "Received" && invoice.Status != "Paid"))
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Exiting ProcessPurchaseInvoiceForBatches - Type: {invoice.Type}, Status: {invoice.Status} is not valid for processing");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing {invoice.Items?.Count ?? 0} items for purchase invoice");

            foreach (var item in invoice.Items)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing purchase item: ProductId={item.ProductId}, Quantity={item.Quantity}");

                if (item.Product != null && (item.Product.TrackBatches || item.Product.HasExpiry))
                {
                    // Determine batch handling strategy
                    bool handleAsSeparateBatch = true;
                    int existingBatchId = -1;

                    // Check for batch information stored on the invoice item
                    if (!string.IsNullOrEmpty(item.BatchNumber))
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Found batch information on invoice item: {item.BatchNumber}");

                        // Check if we should use an existing batch
                        if (item.CustomProperties != null &&
                            item.CustomProperties.TryGetValue("UseExistingBatch", out object useExistingObj) &&
                            useExistingObj is bool useExisting &&
                            useExisting)
                        {
                            // Get the selected batch ID
                            if (item.CustomProperties.TryGetValue("SelectedBatchId", out object selectedBatchIdObj) &&
                                selectedBatchIdObj is int selectedBatchId &&
                                selectedBatchId > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Using selected batch ID from item: {selectedBatchId}");
                                handleAsSeparateBatch = false;
                                existingBatchId = selectedBatchId;
                            }
                        }
                    }
                    // If no batch info on the item or using the UI state (old method fallback)
                    else if (UseExistingBatch && SelectedBatch != null && SelectedBatch.Id > 0 && SelectedBatch.ProductId == item.ProductId)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Using selected batch from UI: {SelectedBatch.BatchNumber}, ID: {SelectedBatch.Id}");
                        handleAsSeparateBatch = false;
                        existingBatchId = SelectedBatch.Id;
                    }
                    // If not using selected batch or if the "Create New Batch" option was selected
                    else if (UseExistingBatch == false || (SelectedBatch != null && SelectedBatch.Id == -1))
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Creating a new batch for product {item.Product.Name}");
                        handleAsSeparateBatch = true;
                    }
                    // If there's a batch number provided, do NOT merge automatically; create a new batch for invoice traceability
                    else if (!string.IsNullOrEmpty(BatchNumber) || !string.IsNullOrEmpty(item.BatchNumber))
                    {
                        System.Diagnostics.Debug.WriteLine("[STOCK DEBUG] Batch number provided on invoice item; will create a distinct batch (no merge).");
                        handleAsSeparateBatch = true;
                    }

                    if (handleAsSeparateBatch)
                    {
                        // Generate a unique batch number if not provided or if using the default "NEW_BATCH"
                        string batchNumber = !string.IsNullOrEmpty(item.BatchNumber) ? item.BatchNumber : BatchNumber;
                        if (string.IsNullOrEmpty(batchNumber) || batchNumber == "NEW_BATCH")
                        {
                            batchNumber = GenerateBatchNumber(invoice.InvoiceNumber, item);
                        }

                        // Get the manufacturing date from item if available
                        DateTime? manufacturingDate = null;
                        if (item.CustomProperties != null && item.CustomProperties.TryGetValue("ManufacturingDate", out object mfgDateObj))
                        {
                            manufacturingDate = mfgDateObj as DateTime?;
                        }

                        // Create a new batch for this purchase
                        // Determine selling price: prefer invoice item selling price, else fall back to product's default
                        var effectiveSellingPrice = (item.SellingPrice > 0m)
                            ? item.SellingPrice
                            : (item.Product?.SellingPrice ?? 0m);

                        // If the requested batch number already exists for this product, generate a unique variant to keep batches distinct
                        var existingSameBatch = _dbService.GetBatchesForProduct(item.ProductId)?.FirstOrDefault(b => b.BatchNumber == batchNumber);
                        if (existingSameBatch != null)
                        {
                            var uniqueBatchNumber = $"{batchNumber}-{DateTime.Now:HHmmssfff}";
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Batch {batchNumber} already exists for product {item.Product.Name}. Regenerating unique batch number: {uniqueBatchNumber}");
                            batchNumber = uniqueBatchNumber;
                        }

                        BatchStock newBatch = new BatchStock
                        {
                            ProductId = item.ProductId,
                            Quantity = item.Quantity,
                            ExpiryDate = item.Product.ExpiryDate,
                            BatchNumber = batchNumber,
                            ManufactureDate = manufacturingDate ?? DateTime.Now,
                            PurchasePrice = item.UnitPrice,
                            SellingPrice = effectiveSellingPrice,
                            Location = "",
                            Notes = $"Auto-generated from invoice {invoice.InvoiceNumber}",
                            CreatedAt = DateTime.Now
                        };

                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Adding new batch for product {item.Product.Name}, Quantity: {item.Quantity}, Batch#: {newBatch.BatchNumber}, Cost: {newBatch.PurchasePrice}, Selling: {effectiveSellingPrice}");

                        try
                        {
                            // Add the batch to the database via stock service with complete batch information
                            _stockService.IncreaseStockNewBatch(item.ProductId, item.Quantity, $"Purchase Invoice {invoice.InvoiceNumber}",
                                newBatch.BatchNumber, newBatch.PurchasePrice, newBatch.ExpiryDate,
                                newBatch.ManufactureDate, newBatch.Location, newBatch.Notes, effectiveSellingPrice);
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Successfully created/updated batch for product {item.Product.Name}");

                            StatusMessage = $"Added new batch for product {item.Product.Name}";
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error creating batch: {ex.Message}");
                            StatusMessage = $"Error creating batch: {ex.Message}";

                            // Fall back to updating general stock if batch creation fails
                            _stockService.IncreaseStock(item.ProductId, item.Quantity, $"Purchase Invoice {invoice.InvoiceNumber}", null);
                        }
                    }
                    else
                    {
                        // Add to existing batch
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Adding to existing batch (ID: {existingBatchId}) for product {item.Product.Name}, Adding quantity: {item.Quantity}");

                        try
                        {
                            var effectiveSellingPriceExisting = (item.SellingPrice > 0m) ? item.SellingPrice : (item.Product?.SellingPrice ?? 0m);
                            _stockService.IncreaseStock(item.ProductId, item.Quantity, $"Purchase Invoice {invoice.InvoiceNumber}", SelectedBatch?.BatchNumber ?? item.BatchNumber, null, null, null, null, null, effectiveSellingPriceExisting);
                            StatusMessage = $"Added to existing batch for product {item.Product.Name}";
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error updating existing batch: {ex.Message}");

                            // Fall back to updating general stock if batch update fails
                            _stockService.IncreaseStock(item.ProductId, item.Quantity, $"Purchase Invoice {invoice.InvoiceNumber}", null);
                        }
                    }
                }
                else
                {
                    // For non-batch products, just update the stock quantity
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Updating general stock for product {item.Product.Name}, Adding: {item.Quantity}");
                    _stockService.IncreaseStock(item.ProductId, item.Quantity, $"Purchase Invoice {invoice.InvoiceNumber}", null);
                    StatusMessage = $"Updated stock for product {item.Product.Name}";
                }
            }

            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Completed processing purchase invoice for batches");

            // Notify UI about stock changes for all affected products
            try
            {
                var affectedProductIds = invoice.Items?.Select(i => i.ProductId).Distinct().ToList() ?? new List<int>();
                foreach (var pid in affectedProductIds)
                {
                    // Use DatabaseService to get the final synced stock (denormalized is source of truth in grids)
                    var prod = _dbService.GetProductById(pid);
                    if (prod != null)
                    {
                        var finalQty = prod.TrackBatches ? prod.GetTotalStockDecimal() : prod.StockQuantity;
                        ViewModels.SaleViewModel.NotifyProductStockChanged(pid, finalQty, this);
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Fired ProductStockChanged for product {pid} after purchase invoice. New stock: {finalQty}");
                    }
                }
            }
            catch (Exception notifyEx)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error notifying stock changes after purchase invoice: {notifyEx.Message}");
            }
        }

        private void ProcessSalesInvoiceForBatches(Invoice invoice)
        {
            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] ProcessSalesInvoiceForBatches - Starting for Invoice #{invoice.InvoiceNumber}");

            if (invoice.Type != "Sales" || (invoice.Status != "Completed" && invoice.Status != "Paid"))
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Exiting ProcessSalesInvoiceForBatches - Type: {invoice.Type}, Status: {invoice.Status}");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing {invoice.Items?.Count ?? 0} items for sales invoice");

            foreach (var item in invoice.Items)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Processing item: ProductId={item.ProductId}, Quantity={item.Quantity}");

                if (item.Product != null && item.Product.TrackBatches)
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Product tracks batches: {item.Product.Name}");
                    decimal remainingQuantity = item.Quantity;

                    // Get all batches for this product, ordered by creation date (FIFO approach)
                    var batches = _dbService.GetBatchesForProduct(item.ProductId)
                        .Where(b => b.Quantity > 0)
                        .OrderBy(b => b.CreatedAt)
                        .ThenBy(b => b.Id)
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Found {batches.Count} batches with stock");

                    foreach (var batch in batches)
                    {
                        if (remainingQuantity <= 0)
                            break;

                        decimal quantityFromBatch = Math.Min(batch.Quantity, remainingQuantity);

                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Deducting {quantityFromBatch} from batch {batch.BatchNumber} (Before: {batch.Quantity}, After: {batch.Quantity - quantityFromBatch})");

                        // Reduce quantity from this batch
                        batch.Quantity -= quantityFromBatch;
                        _dbService.UpdateBatch(batch.Id, batch);

                        remainingQuantity -= quantityFromBatch;
                        StatusMessage = $"Reduced {quantityFromBatch} from batch {batch.BatchNumber}";
                    }

                    // If there's still quantity needed but no batches available
                    if (remainingQuantity > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Deducting {remainingQuantity} from general stock for product {item.ProductId}");
                        // Deduct from general stock
                        _stockService.DecreaseStock(item.ProductId, remainingQuantity, $"Sales Invoice {invoice.InvoiceNumber}");
                        StatusMessage = $"Deducted {remainingQuantity} from general stock for {item.Product.Name}";
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Product does not track batches: {item.Product?.Name ?? "Unknown"}, deducting {item.Quantity} from stock");
                    // For non-batch products, just update the stock quantity
                    _stockService.DecreaseStock(item.ProductId, item.Quantity, $"Sales Invoice {invoice.InvoiceNumber}");
                    StatusMessage = $"Reduced stock for product {item.Product?.Name ?? "Unknown"}";
                }
            }

            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Completed processing sales invoice for batches");

            // Notify UI about stock changes for all affected products
            try
            {
                var affectedProductIds = invoice.Items?.Select(i => i.ProductId).Distinct().ToList() ?? new List<int>();
                foreach (var pid in affectedProductIds)
                {
                    var prod = _dbService.GetProductById(pid);
                    if (prod != null)
                    {
                        var finalQty = prod.TrackBatches ? prod.GetTotalStockDecimal() : prod.StockQuantity;
                        ViewModels.SaleViewModel.NotifyProductStockChanged(pid, finalQty, this);
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Fired ProductStockChanged for product {pid} after sales invoice. New stock: {finalQty}");
                    }
                }
            }
            catch (Exception notifyEx)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error notifying stock changes after sales invoice: {notifyEx.Message}");
            }
        }

        private void RefreshProductData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[STOCK DEBUG] RefreshProductData - Starting to refresh product data");
                StatusMessage = "Refreshing product data...";

                // Reload products from database to get updated stock quantities
                var products = _dbService.GetAllProducts();
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Loaded {products.Count} products from database");

                // Sample a few products to check stock levels
                if (products.Count > 0)
                {
                    foreach (var product in products.Take(Math.Min(5, products.Count)))
                    {
                        System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Product ID: {product.Id}, Name: {product.Name}, Stock: {product.StockQuantity}");

                        // Log batch information for batch-tracked products
                        if (product.TrackBatches)
                        {
                            var batches = _dbService.GetBatchesForProduct(product.Id);
                            System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Product {product.Name} has {batches.Count} batches");

                            foreach (var batch in batches.Take(3)) // Log up to 3 batches
                            {
                                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Batch: {batch.BatchNumber}, Qty: {batch.Quantity}, Price: {batch.PurchasePrice}");
                            }
                        }
                    }
                }

                // Clear existing products and add refreshed data
                AvailableProducts.Clear();
                foreach (var product in products)
                {
                    AvailableProducts.Add(product);
                }

                // Also refresh any currently selected product
                if (SelectedProductId > 0)
                {
                    var refreshedProduct = products.FirstOrDefault(p => p.Id == SelectedProductId);
                    if (refreshedProduct != null)
                    {
                        SelectedProduct = refreshedProduct;

                        // Also refresh batch information if applicable
                        if (refreshedProduct.TrackBatches)
                        {
                            LoadAvailableBatchesForProductNoFilter(refreshedProduct.Id);
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("[STOCK DEBUG] RefreshProductData - Completed refresh");
                StatusMessage = "Product data refreshed successfully";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK DEBUG] Error refreshing product data: {ex.Message}");
                StatusMessage = $"Error refreshing product data: {ex.Message}";
            }
        }

        // Method to load all batches without filtering by quantity > 0
        private void LoadAvailableBatchesForProductNoFilter(int productId)
        {
            if (productId <= 0)
            {
                AvailableBatches.Clear();
                return;
            }

            try
            {
                var batches = _dbService.GetBatchesForProduct(productId)
                    .OrderBy(b => b.CreatedAt)
                    .ThenBy(b => b.Id)
                    .ToList();

                AvailableBatches.Clear();
                foreach (var batch in batches)
                {
                    // Add display name property for UI display
                    batch.DisplayName = $"Batch #{batch.BatchNumber} - Exp: {batch.ExpiryDate?.ToString("MM/dd/yyyy") ?? "N/A"} - Qty: {batch.Quantity}";
                    AvailableBatches.Add(batch);
                }

                // Add option for new batch
                var newBatchOption = new BatchStock
                {
                    Id = -1,
                    BatchNumber = "NEW_BATCH",
                    DisplayName = "Create New Batch"
                };
                AvailableBatches.Insert(0, newBatchOption);

                if (AvailableBatches.Count > 0)
                {
                    SelectedBatch = AvailableBatches[0]; // Select first option by default
                }
                else
                {
                    SelectedBatch = null;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading batches: {ex.Message}";
            }
        }

        /// <summary>
        /// Prints the currently selected invoice
        /// </summary>
        private void PrintInvoice()
        {
            try
            {
                StatusMessage = "Printing invoice...";

                // Create a new instance of InvoicePrintService
                var printService = new InvoicePrintService();

                // Print the invoice
                bool success = printService.PrintInvoice(SelectedInvoice);

                if (success)
                {
                    StatusMessage = "Invoice printed successfully";
                }
                else
                {
                    StatusMessage = "Printing was cancelled or failed";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error printing invoice: {ex.Message}";
                MessageBox.Show($"Error printing invoice: {ex.Message}", "Print Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Determines if the PrintInvoice command can be executed
        /// </summary>
        private bool CanPrintInvoice()
        {
            return SelectedInvoice != null && !IsEditing && !IsCreating;
        }

        // Method to import existing purchase orders into the invoice system
        // NOTE: This method is commented out as it requires implementation of database methods.
        // Implement when needed as part of the migration from the old purchase order system.
        /*
        private void ImportExistingPurchaseOrders()
        {
            try
            {
                // Code to import purchase orders would go here
                // This would need to be customized based on your database structure and requirements

                // Example pseudocode:
                // 1. Retrieve all purchase orders
                // 2. For each order, create a corresponding invoice
                // 3. For each item in the order, create a corresponding invoice item
                // 4. Save all new records to the database
                // 5. Refresh the UI
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error importing purchase orders: {ex.Message}";
            }
        }
        */

        // Helper method to map purchase order status to invoice status
        private string MapPurchaseOrderStatusToInvoiceStatus(string status)
        {
            return status switch
            {
                "Paid" => "Paid",
                "Pending" => "Draft",
                "Approved" => "Issued",
                "Received" => "Paid",
                "Cancelled" => "Cancelled",
                _ => "Draft"
            };
        }

        private void OpenProductSelection()
        {
            var productSelectionWindow = new ProductSelectionWindow();

            // Show the window as a dialog
            if (productSelectionWindow.ShowDialog() == true)
            {
                // Get the selected product
                var selectedProduct = productSelectionWindow.SelectedProduct;
                if (selectedProduct != null)
                {
                    // Update the selected product in the invoice
                    SelectedProductId = selectedProduct.Id;
                    SelectedProduct = selectedProduct;

                    // Set default values
                    Quantity = 1;
                    // Note: UnitPrice will be set by the SelectedProduct setter based on invoice type
                    SellingPrice = selectedProduct.SellingPrice;
                    ProductExpiryDate = selectedProduct.ExpiryDate;

                    // Request focus on quantity field for immediate editing
                    RequestFocusQuantity();
                }
            }
        }

        // Begin editing an existing invoice item
        private void BeginEditItem(object parameter)
        {
            if (parameter is InvoiceItem item && InvoiceItems.Contains(item))
            {
                _itemBeingEdited = item;
                SelectedItem = item;
                SelectedProductId = item.ProductId;
                Quantity = item.Quantity;
                UnitPrice = item.UnitPrice;
                Total = item.Total;
                OnPropertyChanged(nameof(IsItemInEditMode));

                // Force UI refresh for button visibility
                CommandManager.InvalidateRequerySuggested();
                EditModeRefreshTrigger++;
            }
        }

        private bool CanEditItem(object parameter)
        {
            return parameter is InvoiceItem item && InvoiceItems.Contains(item);
        }

        // Commit inline edits back to the selected item
        private void CommitEditItem(object parameter)
        {
            var target = parameter as InvoiceItem ?? SelectedItem;
            if (target == null) return;

            // Basic validation similar to AddItem()
            if (Quantity <= 0)
            {
                ValidationMessage = "Quantity must be greater than zero";
                return;
            }
            if (UnitPrice <= 0)
            {
                ValidationMessage = "Unit price must be greater than zero";
                return;
            }

            // Update the target item properties (InvoiceItem now implements INotifyPropertyChanged)
            target.Quantity = Quantity;
            target.UnitPrice = UnitPrice;
            target.SellingPrice = SellingPrice > 0 ? SellingPrice : target.SellingPrice;
            target.CalculateTotal();

            // Clear edit state
            _itemBeingEdited = null;
            OnPropertyChanged(nameof(IsItemInEditMode));

            // Force UI refresh for button visibility
            CommandManager.InvalidateRequerySuggested();
            EditModeRefreshTrigger++;

            // Recalculate totals and refresh commands
            RecalculateTotals();
            CommandManager.InvalidateRequerySuggested();

            // Force refresh of the InvoiceItems collection to ensure UI updates
            OnPropertyChanged(nameof(InvoiceItems));

            // Clear the form fields after successful edit
            SelectedProductId = 0;
            Quantity = 1;
            UnitPrice = 0;
            SellingPrice = 0;
            SelectedItem = null;

            ValidationMessage = string.Empty;
            StatusMessage = "Item updated successfully";
        }

        private bool CanCommitEditItem(object parameter)
        {
            return SelectedItem != null && Quantity > 0 && UnitPrice > 0;
        }


        #endregion
    }
}