using System;
using System.Linq;
using System.Windows;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Services
{
    /// <summary>
    /// Validates that the permission system is working correctly after fixes
    /// </summary>
    public class PermissionSystemValidator
    {
        private readonly DatabaseService _dbService;
        private readonly UserPermissionsService _permissionsService;
        private readonly PermissionTestHelper _testHelper;

        public PermissionSystemValidator()
        {
            _dbService = new DatabaseService();
            _permissionsService = new UserPermissionsService(_dbService);
            _testHelper = new PermissionTestHelper();
        }

        /// <summary>
        /// Runs a comprehensive validation of the permission system
        /// </summary>
        public bool ValidatePermissionSystem()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== Starting Permission System Validation ===");

                // Test 1: Validate database structure
                if (!ValidateDatabaseStructure())
                {
                    System.Diagnostics.Debug.WriteLine("❌ Database structure validation failed");
                    return false;
                }

                // Test 2: Validate existing users have permissions
                if (!ValidateExistingUsers())
                {
                    System.Diagnostics.Debug.WriteLine("❌ Existing users validation failed");
                    return false;
                }

                // Test 3: Test permission enforcement
                if (!TestPermissionEnforcement())
                {
                    System.Diagnostics.Debug.WriteLine("❌ Permission enforcement test failed");
                    return false;
                }

                // Test 4: Test custom permission creation
                if (!TestCustomPermissionCreation())
                {
                    System.Diagnostics.Debug.WriteLine("❌ Custom permission creation test failed");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("✅ All permission system validation tests passed!");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Permission system validation failed with exception: {ex.Message}");
                return false;
            }
        }

        private bool ValidateDatabaseStructure()
        {
            System.Diagnostics.Debug.WriteLine("--- Validating Database Structure ---");

            try
            {
                // Check if UserPermissions table exists and has data
                using (var context = new POSSystem.Data.POSDbContext())
                {
                    var permissionsCount = context.UserPermissions.Count();
                    System.Diagnostics.Debug.WriteLine($"UserPermissions table has {permissionsCount} records");

                    if (permissionsCount == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ No permissions found - this might be expected for a new system");
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database structure validation failed: {ex.Message}");
                return false;
            }
        }

        private bool ValidateExistingUsers()
        {
            System.Diagnostics.Debug.WriteLine("--- Validating Existing Users ---");

            try
            {
                var users = _dbService.GetAllUsers().Where(u => u.IsActive).ToList();
                System.Diagnostics.Debug.WriteLine($"Found {users.Count} active users");

                foreach (var user in users)
                {
                    var permissions = _permissionsService.GetUserPermissions(user.Id);
                    if (permissions == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ User {user.Username} has no permissions record");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"✓ User {user.Username} has permissions configured");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Existing users validation failed: {ex.Message}");
                return false;
            }
        }

        private bool TestPermissionEnforcement()
        {
            System.Diagnostics.Debug.WriteLine("--- Testing Permission Enforcement ---");

            try
            {
                // Get the first admin user for testing
                var adminUser = _dbService.GetAllUsers()
                    .FirstOrDefault(u => u.IsActive && u.UserRole?.Name == "Admin");

                if (adminUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("No admin user found for testing");
                    return false;
                }

                _permissionsService.Initialize(adminUser);

                // Test various permission checks
                var testPermissions = new[]
                {
                    "sales.create",
                    "products.manage",
                    "users.manage",
                    "reports.view",
                    "settings.access"
                };

                foreach (var permission in testPermissions)
                {
                    var hasPermission = _permissionsService.HasPermission(permission);
                    System.Diagnostics.Debug.WriteLine($"Admin user permission '{permission}': {hasPermission}");
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Permission enforcement test failed: {ex.Message}");
                return false;
            }
        }

        private bool TestCustomPermissionCreation()
        {
            System.Diagnostics.Debug.WriteLine("--- Testing Custom Permission Creation ---");

            try
            {
                // Create a test user with custom permissions
                var testUsername = $"testuser_{DateTime.Now.Ticks}";
                
                var customPermissions = new UserPermissions
                {
                    CanCreateSales = true,
                    CanVoidSales = false,
                    CanApplyDiscount = true,
                    CanViewSalesHistory = true,
                    CanManageProducts = false,
                    CanManageCategories = false,
                    CanViewInventory = true,
                    CanAdjustInventory = false,
                    CanManageExpenses = false,
                    CanManageCashDrawer = false,
                    CanViewReports = false,
                    CanManagePrices = false,
                    CanManageCustomers = true,
                    CanManageSuppliers = false,
                    CanManageUsers = false,
                    CanManageRoles = false,
                    CanAccessSettings = false,
                    CanViewLogs = false
                };

                var testUser = _testHelper.CreateTestUser(testUsername, 3, customPermissions); // Cashier role
                if (testUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("Failed to create test user");
                    return false;
                }

                // Test the custom permissions
                _permissionsService.Initialize(testUser);

                // Verify custom permissions are working
                var canCreateSales = _permissionsService.HasPermission("sales.create");
                var canVoidSales = _permissionsService.HasPermission("sales.void");
                var canManageProducts = _permissionsService.HasPermission("products.manage");
                var canManageUsers = _permissionsService.HasPermission("users.manage");

                System.Diagnostics.Debug.WriteLine($"Test user can create sales: {canCreateSales} (expected: true)");
                System.Diagnostics.Debug.WriteLine($"Test user can void sales: {canVoidSales} (expected: false)");
                System.Diagnostics.Debug.WriteLine($"Test user can manage products: {canManageProducts} (expected: false)");
                System.Diagnostics.Debug.WriteLine($"Test user can manage users: {canManageUsers} (expected: false)");

                // Validate results
                bool testPassed = canCreateSales == true && 
                                 canVoidSales == false && 
                                 canManageProducts == false && 
                                 canManageUsers == false;

                if (testPassed)
                {
                    System.Diagnostics.Debug.WriteLine("✓ Custom permissions test passed");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ Custom permissions test failed");
                }

                // Clean up test user
                try
                {
                    using (var context = new POSSystem.Data.POSDbContext())
                    {
                        var userToDelete = context.Users.Find(testUser.Id);
                        var permissionsToDelete = context.UserPermissions.FirstOrDefault(p => p.UserId == testUser.Id);
                        
                        if (permissionsToDelete != null)
                            context.UserPermissions.Remove(permissionsToDelete);
                        if (userToDelete != null)
                            context.Users.Remove(userToDelete);
                        
                        context.SaveChanges();
                        System.Diagnostics.Debug.WriteLine($"Test user {testUsername} cleaned up");
                    }
                }
                catch (Exception cleanupEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Failed to clean up test user: {cleanupEx.Message}");
                }

                return testPassed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Custom permission creation test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shows a summary dialog with validation results
        /// </summary>
        public void ShowValidationResults()
        {
            var isValid = ValidatePermissionSystem();
            
            var message = isValid 
                ? "✅ Permission System Validation PASSED\n\nThe custom permissions feature is working correctly:\n\n" +
                  "• Users can be created with custom permissions\n" +
                  "• Custom permissions are properly enforced\n" +
                  "• Permission checks work as expected\n" +
                  "• Database integration is functioning\n\n" +
                  "The 'Add User with Custom Permissions' feature is now fully functional!"
                : "❌ Permission System Validation FAILED\n\nSome issues were detected with the permission system. " +
                  "Please check the debug output for details and ensure all fixes have been applied correctly.";

            var title = isValid ? "Validation Successful" : "Validation Failed";
            var icon = isValid ? MessageBoxImage.Information : MessageBoxImage.Warning;

            MessageBox.Show(message, title, MessageBoxButton.OK, icon);
        }
    }
}
