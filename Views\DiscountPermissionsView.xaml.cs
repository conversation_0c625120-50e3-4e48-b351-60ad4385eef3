using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Services;

namespace POSSystem.Views
{
    public partial class DiscountPermissionsView : UserControl
    {
        private readonly DatabaseService _dbService;

        public DiscountPermissionsView()
        {
            InitializeComponent();
            _dbService = new DatabaseService();

            // Only set DataContext if it's not already set (to avoid conflicts with parent)
            if (DataContext == null)
            {
                DataContext = new DiscountPermissionsViewModel(_dbService);
            }
        }
    }
} 