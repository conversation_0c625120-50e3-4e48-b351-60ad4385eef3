using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Diagnostics;

namespace POSLicensingSystem.Utilities
{
    /// <summary>
    /// Utility class for hardware-related operations, such as generating a unique hardware ID.
    /// </summary>
    public static class HardwareInfo
    {
        /// <summary>
        /// Generates a unique system ID based on hardware components.
        /// </summary>
        /// <returns>A Base64-encoded hardware ID string.</returns>
        public static string GetSystemId()
        {
            try
            {
                // Get individual component hashes instead of a single combined hash
                var components = new Dictionary<string, string>
                {
                    { "CPU", GetComponentHash(GetProcessorId()) },
                    { "Motherboard", GetComponentHash(GetMotherboardInfo()) },
                    { "BIOS", GetComponentHash(GetBiosInfo()) },
                    { "Disk", GetComponentHash(GetDiskDriveInfo()) },
                    { "MAC", GetComponentHash(GetMacAddress()) },
                    { "OS", GetComponentHash(GetOsInfo()) }
                };

                // Create a combined string with labeled components
                // Format: CPU:hash|Motherboard:hash|BIOS:hash|Disk:hash|MAC:hash|OS:hash
                string combinedHash = string.Join("|", components.Select(c => $"{c.Key}:{c.Value}"));
                
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(combinedHash)).TrimEnd('=');
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error collecting hardware info: {ex.Message}");
                // Fallback to basic system info if WMI fails
                var fallbackInfo = $"{Environment.ProcessorCount}|{Environment.MachineName}|{Environment.OSVersion}";
                return Convert.ToBase64String(Encoding.UTF8.GetBytes("FALLBACK:" + fallbackInfo)).TrimEnd('=');
            }
        }
        
        // Get hash for a single component
        private static string GetComponentHash(string componentInfo)
        {
            if (string.IsNullOrEmpty(componentInfo))
                return string.Empty;
                
            using (var sha = SHA256.Create())
            {
                var hash = sha.ComputeHash(Encoding.UTF8.GetBytes(componentInfo));
                return Convert.ToBase64String(hash).Substring(0, 10); // First 10 chars is enough for component comparison
            }
        }

        // Hardware information gathering methods
        private static string GetProcessorId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            return obj["ProcessorId"]?.ToString() ?? "";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private static string GetMotherboardInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Product, Manufacturer FROM Win32_BaseBoard"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var product = obj["Product"]?.ToString() ?? "";
                            var manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                            return $"{manufacturer}|{product}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private static string GetBiosInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Version FROM Win32_BIOS"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var version = obj["Version"]?.ToString() ?? "";
                            return $"{version}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private static string GetDiskDriveInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Model FROM Win32_DiskDrive"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var model = obj["Model"]?.ToString() ?? "";
                            return $"{model}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private static string GetMacAddress()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE PhysicalAdapter=True"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var mac = obj["MACAddress"]?.ToString() ?? "";
                            if (!string.IsNullOrEmpty(mac))
                                return mac;
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private static string GetOsInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Version FROM Win32_OperatingSystem"))
                {
                    var collection = searcher.Get();
                    foreach (var obj in collection)
                    {
                        using (obj)
                        {
                            var serial = obj["SerialNumber"]?.ToString() ?? "";
                            var version = obj["Version"]?.ToString() ?? "";
                            return $"{version}|{serial}";
                        }
                    }
                }
            }
            catch { }
            return Environment.OSVersion.VersionString;
        }
        
        /// <summary>
        /// Gets a reduced form of the hardware ID for display purposes.
        /// </summary>
        /// <returns>A shortened version of the hardware ID.</returns>
        public static string GetFormattedSystemId()
        {
            string fullId = GetSystemId();
            
            // If the ID is too long, show only the first and last part
            if (fullId.Length > 20)
            {
                return fullId.Substring(0, 10) + "..." + fullId.Substring(fullId.Length - 10);
            }
            
            return fullId;
        }
    }
} 