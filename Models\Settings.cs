using System;

namespace POSSystem.Models
{
    public class AppSettings
    {
        public DisplaySettings Display { get; set; } = new();
        public RegionalSettings Regional { get; set; } = new();
        public CompanySettings Company { get; set; } = new();
        public LoyaltySettings Loyalty { get; set; } = new();
    }

    public class DisplaySettings
    {
        public string Language { get; set; } = "en";
        public string Theme { get; set; } = "Default";
        public string SalesLayoutTheme { get; set; } = "Grid";
    }

    public class RegionalSettings
    {
        public string DateFormat { get; set; } = "US";
        public string Currency { get; set; } = "DZD";
    }

    public class CompanySettings
    {
        public string CompanyName { get; set; } = "My Company";
        public string Address { get; set; } = "123 Main St";
        public string Phone { get; set; } = "(*************";
        public string Email { get; set; } = "<EMAIL>";
        public decimal TaxRate { get; set; } = 0.0m;
        public string ReceiptFooter { get; set; } = "Thank you for your business!";
        public string Logo { get; set; } = string.Empty;
    }

    public class LoyaltySettings
    {
        public bool Enabled { get; set; } = false;
        public decimal PointsPerPurchase { get; set; } = 1.0m;
        public decimal RedemptionRate { get; set; } = 0.01m;
    }
} 