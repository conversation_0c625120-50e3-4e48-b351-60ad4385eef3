using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;

namespace POSSystem.Services.Caching
{
    /// <summary>
    /// High-performance caching service specifically optimized for product data
    /// Implements intelligent cache invalidation and performance monitoring
    /// </summary>
    public class ProductCacheService
    {
        private readonly ConcurrentDictionary<string, CacheEntry<List<Product>>> _productCache;
        private readonly ConcurrentDictionary<string, CacheEntry<int>> _countCache;
        private readonly ConcurrentDictionary<int, CacheEntry<Product>> _singleProductCache;
        private readonly object _lockObject = new object();
        
        // Cache configuration
        private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _countCacheExpiry = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _singleProductExpiry = TimeSpan.FromMinutes(15);
        private readonly int _maxCacheSize = 100;

        public ProductCacheService()
        {
            _productCache = new ConcurrentDictionary<string, CacheEntry<List<Product>>>();
            _countCache = new ConcurrentDictionary<string, CacheEntry<int>>();
            _singleProductCache = new ConcurrentDictionary<int, CacheEntry<Product>>();
            
            Debug.WriteLine("[PRODUCT_CACHE] ProductCacheService initialized");
        }

        /// <summary>
        /// Get cached products for a specific page with intelligent cache key generation
        /// </summary>
        public async Task<List<Product>> GetCachedProductsAsync(
            int page, 
            int pageSize, 
            int? categoryId = null, 
            Func<Task<List<Product>>> dataLoader = null)
        {
            var cacheKey = GenerateProductCacheKey(page, pageSize, categoryId);
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // Check cache first
                if (_productCache.TryGetValue(cacheKey, out var cachedEntry))
                {
                    if (!cachedEntry.IsExpired)
                    {
                        stopwatch.Stop();
                        Debug.WriteLine($"[PRODUCT_CACHE] ✅ Cache HIT for {cacheKey} in {stopwatch.ElapsedMilliseconds}ms");
                        return cachedEntry.Value;
                    }
                    else
                    {
                        // Remove expired entry
                        _productCache.TryRemove(cacheKey, out _);
                        Debug.WriteLine($"[PRODUCT_CACHE] 🗑️ Removed expired cache entry: {cacheKey}");
                    }
                }

                // Cache miss - load data if loader provided
                if (dataLoader != null)
                {
                    Debug.WriteLine($"[PRODUCT_CACHE] ❌ Cache MISS for {cacheKey}, loading data...");
                    
                    var loadStopwatch = Stopwatch.StartNew();
                    var data = await dataLoader();
                    loadStopwatch.Stop();
                    
                    // Cache the result
                    var entry = new CacheEntry<List<Product>>(data, _defaultExpiry);
                    _productCache.TryAdd(cacheKey, entry);
                    
                    // Cleanup old entries if cache is getting too large
                    await CleanupCacheIfNeeded();
                    
                    stopwatch.Stop();
                    Debug.WriteLine($"[PRODUCT_CACHE] 💾 Cached {data.Count} products for {cacheKey} (load: {loadStopwatch.ElapsedMilliseconds}ms, total: {stopwatch.ElapsedMilliseconds}ms)");
                    
                    return data;
                }

                return new List<Product>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PRODUCT_CACHE] ❌ Error in GetCachedProductsAsync: {ex.Message}");
                return new List<Product>();
            }
        }

        /// <summary>
        /// Get cached product count with intelligent caching
        /// </summary>
        public async Task<int> GetCachedProductCountAsync(
            int? categoryId = null,
            Func<Task<int>> countLoader = null)
        {
            var cacheKey = $"count_{categoryId?.ToString() ?? "all"}";
            
            try
            {
                // Check cache first
                if (_countCache.TryGetValue(cacheKey, out var cachedEntry))
                {
                    if (!cachedEntry.IsExpired)
                    {
                        Debug.WriteLine($"[PRODUCT_CACHE] ✅ Count cache HIT for {cacheKey}");
                        return cachedEntry.Value;
                    }
                    else
                    {
                        _countCache.TryRemove(cacheKey, out _);
                    }
                }

                // Cache miss - load count if loader provided
                if (countLoader != null)
                {
                    Debug.WriteLine($"[PRODUCT_CACHE] ❌ Count cache MISS for {cacheKey}, loading...");
                    
                    var count = await countLoader();
                    var entry = new CacheEntry<int>(count, _countCacheExpiry);
                    _countCache.TryAdd(cacheKey, entry);
                    
                    Debug.WriteLine($"[PRODUCT_CACHE] 💾 Cached count {count} for {cacheKey}");
                    return count;
                }

                return 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PRODUCT_CACHE] ❌ Error in GetCachedProductCountAsync: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Cache a single product by ID
        /// </summary>
        public void CacheProduct(Product product)
        {
            if (product?.Id > 0)
            {
                var entry = new CacheEntry<Product>(product, _singleProductExpiry);
                _singleProductCache.AddOrUpdate(product.Id, entry, (key, oldValue) => entry);
                Debug.WriteLine($"[PRODUCT_CACHE] 💾 Cached single product: {product.Id} - {product.Name}");
            }
        }

        /// <summary>
        /// Get a single cached product by ID
        /// </summary>
        public Product GetCachedProduct(int productId)
        {
            if (_singleProductCache.TryGetValue(productId, out var entry))
            {
                if (!entry.IsExpired)
                {
                    Debug.WriteLine($"[PRODUCT_CACHE] ✅ Single product cache HIT for ID {productId}");
                    return entry.Value;
                }
                else
                {
                    _singleProductCache.TryRemove(productId, out _);
                }
            }
            
            return null;
        }

        /// <summary>
        /// Invalidate cache entries when products are modified
        /// </summary>
        public void InvalidateProductCache(int? categoryId = null, int? productId = null)
        {
            var invalidatedCount = 0;
            
            try
            {
                // Invalidate product list caches
                var keysToRemove = _productCache.Keys.Where(key => 
                    categoryId == null || key.Contains($"cat_{categoryId}")).ToList();
                
                foreach (var key in keysToRemove)
                {
                    if (_productCache.TryRemove(key, out _))
                        invalidatedCount++;
                }

                // Invalidate count caches
                var countKeysToRemove = _countCache.Keys.Where(key =>
                    categoryId == null || key.Contains($"_{categoryId}")).ToList();
                
                foreach (var key in countKeysToRemove)
                {
                    if (_countCache.TryRemove(key, out _))
                        invalidatedCount++;
                }

                // Invalidate single product cache if specified
                if (productId.HasValue)
                {
                    if (_singleProductCache.TryRemove(productId.Value, out _))
                        invalidatedCount++;
                }

                Debug.WriteLine($"[PRODUCT_CACHE] 🗑️ Invalidated {invalidatedCount} cache entries (categoryId: {categoryId}, productId: {productId})");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PRODUCT_CACHE] ❌ Error invalidating cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear all cached data
        /// </summary>
        public void ClearAllCache()
        {
            var totalCleared = _productCache.Count + _countCache.Count + _singleProductCache.Count;
            
            _productCache.Clear();
            _countCache.Clear();
            _singleProductCache.Clear();
            
            Debug.WriteLine($"[PRODUCT_CACHE] 🗑️ Cleared all cache ({totalCleared} entries)");
        }

        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        public ProductCacheStatistics GetCacheStatistics()
        {
            return new ProductCacheStatistics
            {
                ProductCacheCount = _productCache.Count,
                CountCacheCount = _countCache.Count,
                SingleProductCacheCount = _singleProductCache.Count,
                TotalMemoryUsage = EstimateMemoryUsage()
            };
        }

        private string GenerateProductCacheKey(int page, int pageSize, int? categoryId)
        {
            return $"products_p{page}_s{pageSize}_cat_{categoryId?.ToString() ?? "all"}";
        }

        private async Task CleanupCacheIfNeeded()
        {
            if (_productCache.Count > _maxCacheSize)
            {
                await Task.Run(() =>
                {
                    var expiredKeys = _productCache
                        .Where(kvp => kvp.Value.IsExpired)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    var removedCount = 0;
                    foreach (var key in expiredKeys)
                    {
                        if (_productCache.TryRemove(key, out _))
                            removedCount++;
                    }

                    Debug.WriteLine($"[PRODUCT_CACHE] 🧹 Cleanup removed {removedCount} expired entries");
                });
            }
        }

        private long EstimateMemoryUsage()
        {
            // Rough estimation of memory usage
            return (_productCache.Count * 1000) + (_countCache.Count * 50) + (_singleProductCache.Count * 500);
        }
    }

    /// <summary>
    /// Cache entry with expiration tracking
    /// </summary>
    public class CacheEntry<T>
    {
        public T Value { get; }
        public DateTime ExpiryTime { get; }
        public bool IsExpired => DateTime.UtcNow > ExpiryTime;

        public CacheEntry(T value, TimeSpan expiry)
        {
            Value = value;
            ExpiryTime = DateTime.UtcNow.Add(expiry);
        }
    }

    /// <summary>
    /// Product cache statistics for monitoring
    /// </summary>
    public class ProductCacheStatistics
    {
        public int ProductCacheCount { get; set; }
        public int CountCacheCount { get; set; }
        public int SingleProductCacheCount { get; set; }
        public long TotalMemoryUsage { get; set; }
    }
}
