<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.PriceTierDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="420"
             Background="Transparent">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:CurrencyFormatConverter x:Key="CurrencyFormatConverter"/>

        <!-- Simplified, Clean Styles -->
        <Style x:Key="InputFieldStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="Height" Value="48"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="DatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="Height" Value="48"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
            <Setter Property="LineHeight" Value="16"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="44"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="24,0"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
            <Setter Property="Height" Value="44"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="24,0"/>
        </Style>

        <Style x:Key="HelpTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,4,0,0"/>
            <Setter Property="LineHeight" Value="16"/>
            <Setter Property="MinHeight" Value="16"/>
        </Style>

        <Style x:Key="SavingsTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,4,0,16"/>
            <Setter Property="LineHeight" Value="18"/>
            <Setter Property="MinHeight" Value="18"/>
        </Style>
    </UserControl.Resources>

    <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
            UniformCornerRadius="8"
            md:ElevationAssist.Elevation="Dp4"
            MaxWidth="400"
            Margin="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Simplified Header -->
            <Border Grid.Row="0"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    CornerRadius="8,8,0,0"
                    Padding="20,16">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <md:PackIcon Kind="Tag"
                                Width="20" Height="20"
                                VerticalAlignment="Center"
                                Foreground="White"
                                Margin="0,0,8,0"/>
                    <TextBlock x:Name="DialogTitle"
                             Text="Bulk Discount"
                             FontSize="16"
                             FontWeight="Medium"
                             Foreground="White"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Enhanced Content with New Features -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="0,0,8,0">
                <StackPanel Margin="24,24,24,20">

                    <!-- Simple explanation -->
                    <TextBlock Text="Set up a discount for customers who buy in bulk"
                              Style="{StaticResource HelpTextStyle}"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,20"/>

                    <!-- Tier Name (Optional) -->
                    <TextBlock Text="Discount Name (Optional)" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtTierName"
                            Style="{StaticResource InputFieldStyle}"
                            md:HintAssist.Hint="e.g., 10-Pack Deal, Bulk Discount"
                            Text="{Binding TierName}"/>

                    <!-- Minimum Quantity -->
                    <TextBlock Text="Minimum Quantity *" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtMinimumQuantity"
                            Style="{StaticResource InputFieldStyle}"
                            md:HintAssist.Hint="How many items to qualify?"
                            Text="{Binding MinimumQuantity, StringFormat=F0}"
                            PreviewTextInput="DecimalValidation_PreviewTextInput"/>
                    <TextBlock Text="Customers need to buy at least this many items to get the discount"
                              Style="{StaticResource HelpTextStyle}"/>

                    <!-- Pack Pricing Toggle -->
                    <CheckBox x:Name="chkPackPricing"
                             Content="Use Pack Pricing"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,8,0,16"
                             Checked="PackPricing_Changed"
                             Unchecked="PackPricing_Changed"/>
                    <TextBlock Text="Set a total price for the entire pack instead of per-item pricing"
                              Style="{StaticResource HelpTextStyle}"
                              Visibility="{Binding IsChecked, ElementName=chkPackPricing, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                    <!-- Pack Price (when pack pricing is enabled) -->
                    <TextBlock Text="Pack Price *"
                              Style="{StaticResource LabelStyle}"
                              Visibility="{Binding IsChecked, ElementName=chkPackPricing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBox x:Name="txtPackPrice"
                            Style="{StaticResource InputFieldStyle}"
                            md:HintAssist.Hint="Total price for the entire pack"
                            Text="{Binding PackPrice, StringFormat=F2}"
                            PreviewTextInput="DecimalValidation_PreviewTextInput"
                            Visibility="{Binding IsChecked, ElementName=chkPackPricing, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                    <!-- Unit Price (when pack pricing is disabled OR calculated display when enabled) -->
                    <TextBlock x:Name="lblUnitPrice" Text="Discounted Price Per Item *" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtUnitPrice"
                            Style="{StaticResource InputFieldStyle}"
                            md:HintAssist.Hint="New price per item"
                            Text="{Binding UnitPrice, StringFormat=F2}"
                            PreviewTextInput="DecimalValidation_PreviewTextInput"/>

                    <!-- Calculated Unit Price Display (for pack pricing) -->
                    <TextBlock x:Name="txtCalculatedUnitPrice"
                              Style="{StaticResource HelpTextStyle}"
                              Visibility="Collapsed"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                    <!-- Savings Preview -->
                    <TextBlock x:Name="txtSavingsPreview"
                              Text="Enter valid prices to see savings"
                              Style="{StaticResource SavingsTextStyle}"
                              Foreground="{DynamicResource SecondaryHueMidBrush}"/>

                    <!-- Date Range Section -->
                    <TextBlock Text="Date Range (Optional)"
                              Style="{StaticResource LabelStyle}"
                              Margin="0,8,0,4"/>
                    <TextBlock Text="Set when this discount is active"
                              Style="{StaticResource HelpTextStyle}"/>

                    <Grid Margin="0,8,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Effective Date" Style="{StaticResource LabelStyle}" Margin="0,0,0,4"/>
                            <DatePicker x:Name="dpEffectiveDate"
                                       Style="{StaticResource DatePickerStyle}"
                                       md:HintAssist.Hint="When discount starts"
                                       SelectedDate="{Binding EffectiveDate}"
                                       SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Expiration Date" Style="{StaticResource LabelStyle}" Margin="0,0,0,4"/>
                            <DatePicker x:Name="dpExpirationDate"
                                       Style="{StaticResource DatePickerStyle}"
                                       md:HintAssist.Hint="When discount ends"
                                       SelectedDate="{Binding ExpirationDate}"
                                       SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                        </StackPanel>
                    </Grid>

                    <!-- Buttons -->
                    <Grid Margin="0,20,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button x:Name="btnCancel"
                              Grid.Column="0"
                              Content="Cancel"
                              Style="{StaticResource SecondaryButtonStyle}"
                              Click="Cancel_Click"
                              Margin="0,0,8,0"/>

                        <Button x:Name="btnSave"
                              Grid.Column="1"
                              Content="Save Discount"
                              Style="{StaticResource PrimaryButtonStyle}"
                              Click="Save_Click"
                              Margin="8,0,0,0"/>
                    </Grid>

                </StackPanel>
            </ScrollViewer>
        </Grid>
    </md:Card>
</UserControl>
