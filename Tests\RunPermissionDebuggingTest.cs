using System;
using POSSystem.Tests;

namespace POSSystem.Tests
{
    /// <summary>
    /// Console application to run the permission debugging test
    /// </summary>
    class RunPermissionDebuggingTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("POS System - Permission Debugging Test");
            Console.WriteLine("=====================================");
            Console.WriteLine();

            var test = new PermissionDebuggingTest();
            test.TestCompletePermissionFlow();

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
