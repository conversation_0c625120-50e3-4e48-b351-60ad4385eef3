using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.Interfaces;
using POSSystem.Services;
using System.Diagnostics;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// ✅ PERFORMANCE CRITICAL: Async wrapper service for database operations to prevent UI thread blocking
    /// Provides high-performance async methods for the most commonly used database operations
    /// </summary>
    public class AsyncDatabaseService
    {
        private readonly IDatabaseService _databaseService;
        private readonly SemaphoreSlim _semaphore;

        public AsyncDatabaseService(IDatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _semaphore = new SemaphoreSlim(10, 10); // Limit concurrent operations
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Get product by barcode with UI thread protection
        /// </summary>
        public async Task<Product> GetProductByBarcodeAsync(string barcode, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return null;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                // Use the new async method if available, otherwise wrap synchronous call
                if (_databaseService is DatabaseService dbService)
                {
                    return await dbService.GetProductByBarcodeAsync(barcode, cancellationToken);
                }
                else
                {
                    // Fallback for interface implementations
                    return await Task.Run(() => _databaseService.GetProductByBarcode(barcode), cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Get product by ID with UI thread protection
        /// </summary>
        public async Task<Product> GetProductByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            if (id <= 0)
                return null;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                // Use the new async method if available, otherwise wrap synchronous call
                if (_databaseService is DatabaseService dbService)
                {
                    return await dbService.GetProductByIdAsync(id, cancellationToken);
                }
                else
                {
                    // Fallback for interface implementations
                    return await Task.Run(() => _databaseService.GetProductById(id), cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Get all products with full details with UI thread protection
        /// </summary>
        public async Task<List<Product>> GetAllProductsWithFullDetailsAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                // Use the new async method if available, otherwise wrap synchronous call
                if (_databaseService is DatabaseService dbService)
                {
                    return await dbService.GetAllProductsWithFullDetailsAsync(cancellationToken);
                }
                else
                {
                    // Fallback for interface implementations
                    return await Task.Run(() => _databaseService.GetAllProducts(), cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Get products with pagination and UI thread protection
        /// </summary>
        public async Task<List<Product>> GetProductsPagedAsync(int pageSize, int offset, int? categoryId = null, 
            string searchText = null, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                return await _databaseService.GetProductsAsync(pageSize, offset, categoryId, searchText, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Execute database operation with timeout and UI thread protection
        /// </summary>
        public async Task<T> ExecuteWithTimeoutAsync<T>(Func<Task<T>> operation, int timeoutMs = 5000, 
            CancellationToken cancellationToken = default)
        {
            using var timeoutCts = new CancellationTokenSource(timeoutMs);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            await _semaphore.WaitAsync(combinedCts.Token);
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var result = await operation();
                stopwatch.Stop();

                if (stopwatch.ElapsedMilliseconds > 1000)
                {
                    Debug.WriteLine($"⚠️ Slow database operation: {stopwatch.ElapsedMilliseconds}ms");
                }

                return result;
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
            {
                Debug.WriteLine($"🚨 Database operation timed out after {timeoutMs}ms");
                throw new TimeoutException($"Database operation timed out after {timeoutMs}ms");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE CRITICAL: Batch execute multiple operations with UI thread protection
        /// </summary>
        public async Task<List<T>> ExecuteBatchAsync<T>(IEnumerable<Func<Task<T>>> operations, 
            int maxConcurrency = 5, CancellationToken cancellationToken = default)
        {
            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var tasks = operations.Select(async operation =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await operation();
                }
                finally
                {
                    semaphore.Release();
                }
            });

            return (await Task.WhenAll(tasks)).ToList();
        }

        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
