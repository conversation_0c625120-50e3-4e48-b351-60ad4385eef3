using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class AddProductExpiryDateColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Check if column exists first
            migrationBuilder.Sql(@"
                PRAGMA foreign_keys=off;
                BEGIN TRANSACTION;
                
                -- Add ExpiryDate column if it doesn't exist
                ALTER TABLE Products ADD COLUMN ExpiryDate TEXT NULL;
                
                COMMIT;
                PRAGMA foreign_keys=on;");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // SQLite doesn't support dropping columns
        }
    }
} 