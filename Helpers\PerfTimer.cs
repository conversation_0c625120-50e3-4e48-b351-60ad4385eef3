using System;
using System.Diagnostics;

namespace POSSystem.Helpers
{
    public readonly struct PerfTimer : IDisposable
    {
        private readonly string _label;
        private readonly Stopwatch _sw;
        private readonly long _startGen0;
        private readonly long _startGen1;
        private readonly long _startGen2;

        public PerfTimer(string label)
        {
            _label = label;
            _sw = Stopwatch.StartNew();
            _startGen0 = GC.CollectionCount(0);
            _startGen1 = GC.CollectionCount(1);
            _startGen2 = GC.CollectionCount(2);
        }

        public void Dispose()
        {
            _sw.Stop();
            var elapsed = _sw.ElapsedMilliseconds;
            var g0 = GC.CollectionCount(0) - _startGen0;
            var g1 = GC.CollectionCount(1) - _startGen1;
            var g2 = GC.CollectionCount(2) - _startGen2;
            Debug.WriteLine($"[PERF] {_label}: {elapsed} ms, GC(G0={g0}, G1={g1}, G2={g2})");
        }
    }
}

