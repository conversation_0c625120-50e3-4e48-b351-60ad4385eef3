using System;
using System.Collections.Generic;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class LoyaltyProgram
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal PointsPerDollar { get; set; }
        public decimal MonetaryValuePerPoint { get; set; }
        public int ExpiryMonths { get; set; }
        public decimal MinimumPointsRedemption { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public virtual ICollection<LoyaltyTier> Tiers { get; set; }
    }
} 