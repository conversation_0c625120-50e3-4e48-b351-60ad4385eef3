using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using POSSystem.Data;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.IO;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using POSSystem.Views;
using POSSystem.Models.DTOs;

namespace POSSystem.Views.Dialogs
{
    public partial class ProductDialog : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly IAlertService _alertService;
        private readonly ProductLookupService _productLookupService;
        private readonly BulkPricingService _bulkPricingService;
        private Product _existingProduct;
        private bool _isEditMode;
        private string _currentImageBase64;
        private string _dialogIdentifier = "RootDialog";
        private List<ProductBarcode> _temporaryBarcodes;
        private ObservableCollection<PriceTierDto> _priceTiers;
        private bool _isBulkPricingExpanded = false;

        public Product Result { get; private set; }

        public ProductsViewModel ViewModel { get; private set; }

        public ObservableCollection<PriceTierDto> PriceTiers
        {
            get => _priceTiers;
            set
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTiers setter called with {value?.Count ?? 0} items");
                _priceTiers = value;
                PriceTiersList.ItemsSource = _priceTiers;
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTiersList.ItemsSource set to collection with {_priceTiers?.Count ?? 0} items");

                // Subscribe to collection changes for debugging
                if (_priceTiers != null)
                {
                    _priceTiers.CollectionChanged += (s, e) =>
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTiers collection changed: Action={e.Action}, NewItems={e.NewItems?.Count ?? 0}, OldItems={e.OldItems?.Count ?? 0}");
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Total items in collection: {_priceTiers.Count}");
                    };
                }
            }
        }

        public ProductDialog(ProductsViewModel viewModel, string dialogIdentifier, Product existingProduct = null)
        {
            InitializeComponent();

            // Verify that critical UI controls are initialized
            if (rbProduct == null || rbService == null)
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Warning: Radio buttons not properly initialized after InitializeComponent()");
            }

            if (rbWeightBased == null || rbUnitBased == null)
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Warning: Weight-based radio buttons not found in XAML");
            }

            ViewModel = viewModel;
            _dialogIdentifier = dialogIdentifier;

            // Use the application's main service provider instead of creating a new one
            try
            {
                // Use App.ServiceProvider to get the existing DatabaseService instance
                _dbService = App.ServiceProvider?.GetService<IDatabaseService>() as DatabaseService;

                // Fallback to manual creation if DI fails (for backward compatibility)
                if (_dbService == null)
                {
                    System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Warning: Could not get DatabaseService from App.ServiceProvider, creating manually");
                    _dbService = new DatabaseService();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Successfully obtained DatabaseService from App.ServiceProvider (no new initialization)");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Error getting DatabaseService from App.ServiceProvider: {ex.Message}");
                _dbService = new DatabaseService();
            }

            // Create the lookup service for external product database
            _alertService = new SimpleAlertService();
            _productLookupService = new ProductLookupService(_alertService);
            _bulkPricingService = new BulkPricingService();

            // Initialize pricing tiers collection
            PriceTiers = new ObservableCollection<PriceTierDto>();
            
            // Initialize view model if not provided
            if (ViewModel == null)
            {
                ViewModel = new ProductsViewModel(_alertService);
                _ = ViewModel.LoadInitialData();
            }
            
            DataContext = ViewModel;

            // Subscribe to sales method changes from ViewModel
            ViewModel.OnSalesMethodChanged = (isWeightBased) =>
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG] Received sales method change notification: {isWeightBased}");
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateQuantityInputForSalesMethod(isWeightBased);
                }));
            };

            // Debug logging for categories
            System.Diagnostics.Debug.WriteLine($"[DIALOG DEBUG] Categories loaded: {ViewModel.Categories?.Count ?? 0}");
            if (ViewModel.Categories != null)
            {
                foreach (var cat in ViewModel.Categories)
                {
                    System.Diagnostics.Debug.WriteLine($"[DIALOG DEBUG] Category: {cat.Name} (ID: {cat.Id})");
                }
            }

            // Register checkbox events
            chkTrackBatches.Checked += chkTrackBatches_CheckedChanged;
            chkTrackBatches.Unchecked += chkTrackBatches_CheckedChanged;

            // Register loaded event to ensure data is ready
            Loaded += ProductDialog_Loaded;

            // Initialize quantity field to be enabled by default
            txtQuantity.IsEnabled = true;
            txtQuantity.Opacity = 1.0;

            // If a product is provided, we're in edit mode
            if (existingProduct != null)
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG DEBUG] Edit mode - Product ID: {existingProduct.Id}, Name: {existingProduct.Name}");
                _existingProduct = existingProduct;
                _isEditMode = true;

                // Load complete product data from database to ensure all related data is available
                _ = Task.Run(async () => await LoadCompleteProductData(existingProduct.Id));

                // Load initial data from the provided product
                LoadProductData(existingProduct);
                DialogTitle.Text = (string)Application.Current.Resources["EditProduct"];
                btnSave.Content = (string)Application.Current.Resources["Update"];

                // Check if the product is favorited by the current user
                CheckIfProductIsFavorited(existingProduct.Id);
            }
            else
            {
                _isEditMode = false;
                DialogTitle.Text = (string)Application.Current.Resources["AddProduct"];
                btnSave.Content = (string)Application.Current.Resources["AddProduct"];
                
                // Set default values
                txtMinimumStock.Text = "5";
                chkIsActive.IsChecked = true;
                chkTrackBatches.IsChecked = true;
                chkIsFavorite.IsChecked = false;

                // Ensure batch section is visible in create mode
                if (BatchInfoSection != null)
                {
                    BatchInfoSection.Visibility = Visibility.Visible;
                    System.Diagnostics.Debug.WriteLine("[DIALOG DEBUG] Batch section visible in create mode");
                }

                // Apply initial UI state based on batch tracking
                chkTrackBatches_CheckedChanged(chkTrackBatches, new RoutedEventArgs());
                
                // Set default sales method to unit-based and apply initial unit filtering
                if (rbUnitBased != null)
                {
                    rbUnitBased.IsChecked = true;
                    // Apply initial unit filtering for unit-based products
                    FilterUnitsForSalesMethod(false);
                }
                else
                {
                    // Fallback: Set default unit of measure to "piece" (ID 1)
                    if (ViewModel.UnitsOfMeasure != null && ViewModel.UnitsOfMeasure.Any())
                    {
                        // Find the unit of measure with name "piece" or equivalent
                        var pieceUnit = ViewModel.UnitsOfMeasure.FirstOrDefault(u =>
                            u.Name.Equals("piece", StringComparison.OrdinalIgnoreCase) ||
                            u.Name.Equals("قطعة", StringComparison.OrdinalIgnoreCase) ||
                            u.Name.Equals("pièce", StringComparison.OrdinalIgnoreCase));

                        if (pieceUnit != null)
                        {
                            ViewModel.SelectedUnitOfMeasureId = pieceUnit.Id;
                        }
                        else if (ViewModel.UnitsOfMeasure.Count > 0)
                        {
                            // If "piece" is not found, select the first unit as fallback
                            ViewModel.SelectedUnitOfMeasureId = ViewModel.UnitsOfMeasure.First().Id;
                        }
                    }
                }
            }
        }

        private void LoadProductData(Product product)
        {
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Loading data for product ID: {product.Id}, Name: {product.Name}");
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] CategoryId: {product.CategoryId}, UnitOfMeasureId: {product.UnitOfMeasureId}");
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] PurchasePrice: {product.PurchasePrice}, SellingPrice: {product.SellingPrice}");
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] MinimumStock: {product.MinimumStock}, StockQuantity: {product.StockQuantity}");

            // Set product type radio buttons with null checks
            bool isService = product.Type == ProductType.Service;
            if (rbProduct != null && rbService != null)
            {
                rbProduct.IsChecked = !isService;
                rbService.IsChecked = isService;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[LOAD_PRODUCT_DATA] Warning: Radio buttons not yet initialized");
            }

            // Update UI based on product type
            UpdateUIForItemType(isService);
            UpdateDialogTitle(isService);

            // Set sales method radio buttons with null checks
            if (rbWeightBased != null && rbUnitBased != null)
            {
                rbWeightBased.IsChecked = product.IsWeightBased;
                rbUnitBased.IsChecked = !product.IsWeightBased;

                // Update ViewModel
                if (ViewModel != null)
                {
                    ViewModel.IsWeightBased = product.IsWeightBased;
                }

                // Update quantity input behavior
                UpdateQuantityInputForSalesMethod(product.IsWeightBased);

                System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] IsWeightBased: {product.IsWeightBased}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[LOAD_PRODUCT_DATA] Warning: Sales method radio buttons not yet initialized");
            }

            txtName.Text = product.Name;
            txtDescription.Text = product.Description;
            txtPurchasePrice.Text = product.PurchasePrice.ToString("0.00");
            txtSellingPrice.Text = product.SellingPrice.ToString("0.00");

            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Set txtPurchasePrice.Text to: '{txtPurchasePrice.Text}'");
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Set txtSellingPrice.Text to: '{txtSellingPrice.Text}'");
            
            // Show total stock quantity (general + batches)
            if (product.TrackBatches)
            {
                // Load batches first to ensure accurate totals
                LoadBatchesForProduct(product);
                
                // Show total stock (general + batch) with decimal formatting
                var totalStock = product.GetTotalStockDecimal();
                txtCurrentStock.Text = totalStock % 1 == 0 ? totalStock.ToString("F0") : totalStock.ToString("0.###");
                txtCurrentStock.IsReadOnly = true;
                txtCurrentStock.IsEnabled = false; // Disable editing for batch-tracked products
                
                // Add batch information to the UI
                UpdateBatchInformation(product);
                
                // Set visual indication that quantity can only be changed through batch management
                txtCurrentStock.ToolTip = "Total Stock (Manage via Batches)";
                btnManageStock.Visibility = Visibility.Visible;
            }
            else
            {
                // For non-batch products, show the stock quantity with decimal formatting
                var stockQuantity = product.StockQuantity;
                txtCurrentStock.Text = stockQuantity % 1 == 0 ? stockQuantity.ToString("F0") : stockQuantity.ToString("0.###");
                txtCurrentStock.IsReadOnly = false;
                txtCurrentStock.IsEnabled = true; // Enable editing for non-batch products
                txtCurrentStock.ToolTip = "Current Stock";
                btnManageStock.Visibility = Visibility.Collapsed;
            }
            
            txtMinimumStock.Text = product.MinimumStock.ToString();
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Set txtMinimumStock.Text to: '{txtMinimumStock.Text}'");

            chkIsActive.IsChecked = product.IsActive;
            chkIsFavorite.IsChecked = product.IsFavorited;

            // Set category selection properly
            ViewModel.SelectedCategoryId = product.CategoryId;
            cmbCategory.SelectedValue = product.CategoryId;
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Set category - ViewModel.SelectedCategoryId: {ViewModel.SelectedCategoryId}, cmbCategory.SelectedValue: {cmbCategory.SelectedValue}");
            System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Available categories count: {ViewModel.Categories?.Count ?? 0}");
            if (ViewModel.Categories != null)
            {
                var matchingCategory = ViewModel.Categories.FirstOrDefault(c => c.Id == product.CategoryId);
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Found matching category: {matchingCategory?.Name ?? "NOT FOUND"}");
            }

            // Set unit of measure selection properly
            if (product.UnitOfMeasureId.HasValue)
            {
                ViewModel.SelectedUnitOfMeasureId = product.UnitOfMeasureId.Value;
                cmbUnitOfMeasure.SelectedValue = product.UnitOfMeasureId.Value;
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCT_DATA] Set UOM - ViewModel.SelectedUnitOfMeasureId: {ViewModel.SelectedUnitOfMeasureId}, cmbUnitOfMeasure.SelectedValue: {cmbUnitOfMeasure.SelectedValue}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[LOAD_PRODUCT_DATA] Product has no UnitOfMeasureId");
            }

            // Initialize quantity to 1.0 for existing products (edit mode)
            ViewModel.ProductQuantity = 1.0m;
            txtQuantity.Text = "1.000";
            txtQuantity.IsEnabled = true; // Ensure quantity field is enabled by default

            chkHasExpiry.IsChecked = product.HasExpiry;
            dpExpiryDate.SelectedDate = product.ExpiryDate;
            chkTrackBatches.IsChecked = product.TrackBatches;

            // Load primary barcode
            var primaryBarcode = product.Barcodes?.FirstOrDefault(b => b.IsPrimary);
            if (primaryBarcode != null)
            {
                txtBarcode.Text = primaryBarcode.Barcode;
            }

            // Load product image
            _currentImageBase64 = product.ImageData;
            UpdateImageDisplay(_currentImageBase64);

            // Load pricing tiers
            LoadPricingTiers(product);

            // Force refresh ComboBox selections after a short delay to ensure data binding is complete
            Dispatcher.BeginInvoke(new Action(() => RefreshComboBoxSelections(product)), System.Windows.Threading.DispatcherPriority.Loaded);

            // Hide batch section in edit mode to reduce clutter
            if (_isEditMode && BatchInfoSection != null)
            {
                BatchInfoSection.Visibility = Visibility.Collapsed;
                System.Diagnostics.Debug.WriteLine("[LOAD_PRODUCT_DATA] Batch section hidden in edit mode");
            }

            System.Diagnostics.Debug.WriteLine("[LOAD_PRODUCT_DATA] Product data loading completed");
        }

        /// <summary>
        /// Forces refresh of ComboBox selections to ensure they display correctly
        /// </summary>
        private void RefreshComboBoxSelections(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] Refreshing ComboBox selections for product {product.Name}");

                // Force refresh category selection
                if (ViewModel.Categories != null && ViewModel.Categories.Any())
                {
                    var category = ViewModel.Categories.FirstOrDefault(c => c.Id == product.CategoryId);
                    if (category != null)
                    {
                        cmbCategory.SelectedItem = category;
                        ViewModel.SelectedCategory = category;
                        System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] Set category to: {category.Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] Category with ID {product.CategoryId} not found in collection");
                    }
                }

                // Force refresh UOM selection
                if (ViewModel.UnitsOfMeasure != null && ViewModel.UnitsOfMeasure.Any() && product.UnitOfMeasureId.HasValue)
                {
                    var uom = ViewModel.UnitsOfMeasure.FirstOrDefault(u => u.Id == product.UnitOfMeasureId.Value);
                    if (uom != null)
                    {
                        cmbUnitOfMeasure.SelectedItem = uom;
                        ViewModel.SelectedUnitOfMeasureId = uom.Id;
                        System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] Set UOM to: {uom.Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] UOM with ID {product.UnitOfMeasureId} not found in collection");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[REFRESH_COMBOS] Error refreshing ComboBox selections: {ex.Message}");
            }
        }

        private void LoadPricingTiers(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Starting to load pricing tiers for product: {product?.Name ?? "NULL"} (ID: {product?.Id ?? -1})");

                PriceTiers.Clear();
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] PriceTiers collection cleared");

                if (product == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Product is null - no tiers to load");
                    chkEnableBulkPricing.IsChecked = false;
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Product.PriceTiers is null: {product.PriceTiers == null}");

                if (product.PriceTiers != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Product.PriceTiers count: {product.PriceTiers.Count}");

                    foreach (var tier in product.PriceTiers.OrderBy(t => t.MinimumQuantity))
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Processing tier: ID={tier.Id}, TierName='{tier.TierName}', MinQty={tier.MinimumQuantity}, UnitPrice={tier.UnitPrice}");

                        var dto = PriceTierDto.FromEntity(tier);
                        dto.IsNew = false; // Mark as existing since it came from database
                        dto.IsModified = false; // Mark as unmodified
                        PriceTiers.Add(dto);

                        System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Added DTO: ID={dto.Id}, TierName='{dto.TierName}', MinQty={dto.MinimumQuantity}, UnitPrice={dto.UnitPrice}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Product.PriceTiers is null - checking if we need to load from database");

                    // Try to load pricing tiers from database directly
                    if (product.Id > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Product has valid ID ({product.Id}) - attempting to load tiers from database");
                        LoadPricingTiersFromDatabase(product.Id);
                    }
                }

                // Update bulk pricing checkbox
                chkEnableBulkPricing.IsChecked = PriceTiers.Any();
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Bulk pricing checkbox set to: {chkEnableBulkPricing.IsChecked}");

                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Final result: Loaded {PriceTiers.Count} pricing tiers for product {product?.Name}");

                // Log each loaded tier for verification
                for (int i = 0; i < PriceTiers.Count; i++)
                {
                    var tier = PriceTiers[i];
                    System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Tier {i}: '{tier.TierName}' - MinQty: {tier.MinimumQuantity}, UnitPrice: {tier.UnitPrice}");
                }

                // Update pricing tiers count indicator
                UpdatePricingTiersIndicator();

                // Note: Bulk pricing section remains collapsed by default for cleaner UI
                // Users can manually expand it when needed
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Bulk pricing section remains collapsed by default for cleaner UI");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[LOAD_PRICING_TIERS] Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error loading pricing tiers: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Loads complete product data from database including all related entities
        /// </summary>
        private async Task LoadCompleteProductData(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Loading complete product data for ID: {productId}");

                using (var context = new POSDbContext())
                {
                    var completeProduct = await context.Products
                        .AsNoTracking()
                        .Include(p => p.Category)
                        .Include(p => p.UnitOfMeasure)
                        .Include(p => p.Barcodes)
                        .Include(p => p.PriceTiers.Where(pt => pt.IsActive))
                        .FirstOrDefaultAsync(p => p.Id == productId);

                    if (completeProduct != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Found complete product data");
                        System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] PurchasePrice: {completeProduct.PurchasePrice}, MinimumStock: {completeProduct.MinimumStock}");
                        System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Category: {completeProduct.Category?.Name ?? "NULL"}, PriceTiers: {completeProduct.PriceTiers?.Count ?? 0}");

                        // Update the existing product reference with complete data
                        _existingProduct = completeProduct;

                        // Update UI on the main thread
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Updating UI with complete product data");
                            LoadProductData(completeProduct);
                        });
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Product not found in database");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_COMPLETE] Error loading complete product data: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the bulk pricing section expand/collapse
        /// </summary>
        private void BulkPricingHeader_Click(object sender, RoutedEventArgs e)
        {
            ToggleBulkPricingSection();
        }

        /// <summary>
        /// Toggles the bulk pricing section expanded/collapsed state
        /// </summary>
        private void ToggleBulkPricingSection()
        {
            _isBulkPricingExpanded = !_isBulkPricingExpanded;

            System.Diagnostics.Debug.WriteLine($"[BULK_PRICING_UI] Toggling bulk pricing section - Expanded: {_isBulkPricingExpanded}");

            if (_isBulkPricingExpanded)
            {
                ExpandBulkPricingSection();
            }
            else
            {
                CollapseBulkPricingSection();
            }
        }

        /// <summary>
        /// Expands the bulk pricing section with animation
        /// </summary>
        private void ExpandBulkPricingSection()
        {
            if (BulkPricingContent != null && iconExpandCollapseBulkPricing != null && rotateTransformBulkPricing != null)
            {
                BulkPricingContent.Visibility = Visibility.Visible;

                // Animate the arrow rotation
                var rotateAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0,
                    To = 180,
                    Duration = TimeSpan.FromMilliseconds(200),
                    EasingFunction = new System.Windows.Media.Animation.QuadraticEase()
                };

                rotateTransformBulkPricing.BeginAnimation(System.Windows.Media.RotateTransform.AngleProperty, rotateAnimation);

                System.Diagnostics.Debug.WriteLine("[BULK_PRICING_UI] Bulk pricing section expanded");
            }
        }

        /// <summary>
        /// Collapses the bulk pricing section with animation
        /// </summary>
        private void CollapseBulkPricingSection()
        {
            if (BulkPricingContent != null && iconExpandCollapseBulkPricing != null && rotateTransformBulkPricing != null)
            {
                // Animate the arrow rotation
                var rotateAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 180,
                    To = 0,
                    Duration = TimeSpan.FromMilliseconds(200),
                    EasingFunction = new System.Windows.Media.Animation.QuadraticEase()
                };

                rotateAnimation.Completed += (s, e) =>
                {
                    BulkPricingContent.Visibility = Visibility.Collapsed;
                };

                rotateTransformBulkPricing.BeginAnimation(System.Windows.Media.RotateTransform.AngleProperty, rotateAnimation);

                System.Diagnostics.Debug.WriteLine("[BULK_PRICING_UI] Bulk pricing section collapsed");
            }
        }

        /// <summary>
        /// Updates the pricing tiers count indicator in the bulk pricing header
        /// </summary>
        private void UpdatePricingTiersIndicator()
        {
            if (PricingTiersIndicator != null && PricingTiersCount != null)
            {
                int tierCount = PriceTiers?.Count ?? 0;

                if (tierCount > 0)
                {
                    PricingTiersCount.Text = tierCount.ToString();
                    PricingTiersIndicator.Visibility = Visibility.Visible;
                    System.Diagnostics.Debug.WriteLine($"[BULK_PRICING_UI] Showing pricing tiers indicator with count: {tierCount}");
                }
                else
                {
                    PricingTiersIndicator.Visibility = Visibility.Collapsed;
                    System.Diagnostics.Debug.WriteLine("[BULK_PRICING_UI] Hiding pricing tiers indicator - no tiers");
                }
            }
        }

        /// <summary>
        /// Note: Bulk pricing section is kept collapsed by default for a cleaner, more focused UI.
        /// Users can manually expand it when they need to view or modify pricing tiers.
        /// This reduces visual clutter and emphasizes the core product information.
        /// </summary>

        private async void LoadPricingTiersFromDatabase(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Loading pricing tiers from database for product ID: {productId}");

                using (var context = new POSDbContext())
                {
                    var dbTiers = await context.ProductPriceTiers
                        .Where(pt => pt.ProductId == productId && pt.IsActive)
                        .OrderBy(pt => pt.MinimumQuantity)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Found {dbTiers.Count} tiers in database");

                    foreach (var dbTier in dbTiers)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] DB Tier: ID={dbTier.Id}, TierName='{dbTier.TierName}', MinQty={dbTier.MinimumQuantity}, UnitPrice={dbTier.UnitPrice}");

                        var dto = PriceTierDto.FromEntity(dbTier);
                        dto.IsNew = false; // Mark as existing since it came from database
                        dto.IsModified = false; // Mark as unmodified
                        PriceTiers.Add(dto);

                        System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Added DTO from DB: ID={dto.Id}, TierName='{dto.TierName}', MinQty={dto.MinimumQuantity}, UnitPrice={dto.UnitPrice}");
                    }

                    // Update checkbox after loading from database
                    chkEnableBulkPricing.IsChecked = PriceTiers.Any();
                    System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Updated checkbox to: {chkEnableBulkPricing.IsChecked}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Error loading from database: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[LOAD_DB_TIERS] Stack trace: {ex.StackTrace}");
            }
        }

        private async Task SavePricingTiers(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Starting to save pricing tiers for product: {product?.Name ?? "NULL"} (ID: {product?.Id ?? -1})");
                System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] PriceTiers collection count: {PriceTiers.Count}");

                // Log all tiers in the UI collection
                for (int i = 0; i < PriceTiers.Count; i++)
                {
                    var uiTier = PriceTiers[i];
                    System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] UI Tier {i}: ID={uiTier.Id}, TierName='{uiTier.TierName}', MinQty={uiTier.MinimumQuantity}, UnitPrice={uiTier.UnitPrice}, IsNew={uiTier.IsNew}");
                }

                if (product?.Id <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE_PRICING_TIERS] Invalid product ID, cannot save pricing tiers");
                    return;
                }

                using (var context = new POSDbContext())
                {
                    // Load existing pricing tiers for this product
                    var existingTiers = await context.ProductPriceTiers
                        .Where(pt => pt.ProductId == product.Id)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Found {existingTiers.Count} existing tiers in database");

                    foreach (var existing in existingTiers)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Existing DB Tier: ID={existing.Id}, TierName='{existing.TierName}', MinQty={existing.MinimumQuantity}, UnitPrice={existing.UnitPrice}");
                    }

                    // Remove tiers that are no longer in the UI (match by content, not just ID)
                    var tiersToRemove = existingTiers
                        .Where(existing => !PriceTiers.Any(ui =>
                            (ui.Id == existing.Id && ui.Id > 0) || // Match by ID if UI tier has valid ID
                            (ui.TierName == existing.TierName &&
                             Math.Abs(ui.MinimumQuantity - existing.MinimumQuantity) < 0.001m &&
                             Math.Abs(ui.UnitPrice - existing.UnitPrice) < 0.01m))) // Match by content
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Tiers to remove: {tiersToRemove.Count}");

                    foreach (var tierToRemove in tiersToRemove)
                    {
                        context.ProductPriceTiers.Remove(tierToRemove);
                        System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Removing tier: {tierToRemove.TierName ?? tierToRemove.Id.ToString()}");
                    }

                    // Add or update tiers from the UI
                    foreach (var uiTier in PriceTiers)
                    {
                        // Try to find existing tier by ID first, then by content
                        var existingTier = existingTiers.FirstOrDefault(existing =>
                            (uiTier.Id > 0 && existing.Id == uiTier.Id) || // Match by ID if UI tier has valid ID
                            (uiTier.Id == 0 && existing.TierName == uiTier.TierName &&
                             Math.Abs(existing.MinimumQuantity - uiTier.MinimumQuantity) < 0.001m &&
                             Math.Abs(existing.UnitPrice - uiTier.UnitPrice) < 0.01m)); // Match by content for new tiers

                        if (existingTier == null)
                        {
                            // Add new tier
                            var newTier = uiTier.ToEntity();
                            newTier.ProductId = product.Id;
                            newTier.CreatedAt = DateTime.Now;
                            newTier.UpdatedAt = DateTime.Now;

                            context.ProductPriceTiers.Add(newTier);
                            System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Adding new tier: {newTier.TierName ?? "Unnamed"}");
                        }
                        else
                        {
                            // Update existing tier (we already found it above)
                            if (existingTier != null && (uiTier.IsModified || uiTier.Id == 0))
                            {
                                // Update properties
                                existingTier.MinimumQuantity = uiTier.MinimumQuantity;
                                existingTier.MaximumQuantity = uiTier.MaximumQuantity;
                                existingTier.UnitPrice = uiTier.UnitPrice;
                                existingTier.PackPrice = uiTier.PackPrice;
                                existingTier.TierName = string.IsNullOrWhiteSpace(uiTier.TierName) ? null : uiTier.TierName;
                                existingTier.Description = string.IsNullOrWhiteSpace(uiTier.Description) ? null : uiTier.Description;
                                existingTier.IsActive = uiTier.IsActive;
                                existingTier.DisplayOrder = uiTier.DisplayOrder;
                                existingTier.EffectiveDate = uiTier.EffectiveDate;
                                existingTier.ExpirationDate = uiTier.ExpirationDate;
                                existingTier.UpdatedAt = DateTime.Now;

                                context.ProductPriceTiers.Update(existingTier);
                                System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Updating existing tier: {existingTier.TierName ?? existingTier.Id.ToString()} (was ID={existingTier.Id})");

                                // Update the UI tier's ID to match the database tier for future operations
                                uiTier.Id = existingTier.Id;
                            }
                            else if (existingTier != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Tier already exists and unchanged: {existingTier.TierName ?? existingTier.Id.ToString()}");
                                // Update the UI tier's ID to match the database tier for future operations
                                uiTier.Id = existingTier.Id;
                            }
                        }
                    }

                    // Save changes
                    await context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Successfully saved {PriceTiers.Count} pricing tiers for product {product.Name}");

                    // Mark all tiers as saved (no longer new or modified)
                    foreach (var tier in PriceTiers)
                    {
                        tier.IsNew = false;
                        tier.IsModified = false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SAVE_PRICING_TIERS] Error: {ex.Message}");
                MessageBox.Show($"Error saving pricing tiers: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw; // Re-throw to prevent dialog from closing if pricing tiers fail to save
            }
        }

        private void LoadBatchesForProduct(Product product)
        {
            try
            {
                if (product == null || !product.TrackBatches)
                    return;
                
                // Ensure we have the latest batch data from the database
                var batches = _dbService.GetBatchesForProduct(product.Id);
                
                // Update the product's batch collection
                product.Batches = new List<BatchStock>(batches);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading batch information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateBatchInformation(Product product)
        {
            try
            {
                if (product == null || !product.TrackBatches)
                    return;
                
                // Get the batch information section (assuming it exists in your XAML)
                var batchInfoSection = this.FindName("BatchInfoSection") as UIElement;
                
                // If we have a UI element for batch info, update it
                if (batchInfoSection != null)
                {
                    batchInfoSection.Visibility = Visibility.Visible;
                    
                    // Update batch count label if it exists
                    var batchCountLabel = this.FindName("txtBatchCount") as TextBlock;
                    if (batchCountLabel != null)
                        batchCountLabel.Text = product.BatchCount.ToString();
                    
                    // Update batch quantity label if it exists
                    var batchQuantityLabel = this.FindName("txtBatchQuantity") as TextBlock;
                    if (batchQuantityLabel != null)
                        batchQuantityLabel.Text = product.BatchStockQuantity.ToString();
                }
                else
                {
                    // If no dedicated UI element exists, we'll add batch info to the stock tooltip
                    txtCurrentStock.ToolTip = $"Current Stock (includes {product.BatchCount} batches with total of {product.BatchStockQuantity} units)";
                }
            }
            catch (Exception ex)
            {
                // Log but don't disrupt the UI
                System.Diagnostics.Debug.WriteLine($"Error updating batch information: {ex.Message}");
            }
        }

        private void UpdateImageDisplay(string base64)
        {
            if (!string.IsNullOrEmpty(base64))
            {
                try
                {
                    var imageSource = new BitmapImage();
                    byte[] imageData = Convert.FromBase64String(base64);
                    
                    using (var ms = new MemoryStream(imageData))
                    {
                        imageSource.BeginInit();
                        imageSource.StreamSource = ms;
                        imageSource.CacheOption = BitmapCacheOption.OnLoad;
                        imageSource.EndInit();
                    }
                    
                    productImage.Source = imageSource;
                    productImage.Visibility = Visibility.Visible;
                    noImagePlaceholder.Visibility = Visibility.Collapsed;
                }
                catch (FormatException)
                {
                    // Invalid base64 string, show placeholder instead
                    productImage.Source = null;
                    productImage.Visibility = Visibility.Collapsed;
                    noImagePlaceholder.Visibility = Visibility.Visible;
                }
            }
            else
            {
                productImage.Source = null;
                productImage.Visibility = Visibility.Collapsed;
                noImagePlaceholder.Visibility = Visibility.Visible;
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DialogHost.IsDialogOpen(_dialogIdentifier))
                {
                    DialogHost.Close(_dialogIdentifier);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error closing dialog: {ex.Message}");
                // Try using the command as a fallback
                DialogHost.CloseDialogCommand.Execute(null, null);
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Starting product save operation...");
                
                if (!ValidateInput())
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Input validation failed");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Input validation passed");

                if (!decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice))
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Invalid purchase price format");
                    MessageBox.Show("Please enter a valid purchase price", "Validation Error");
                    return;
                }

                if (!decimal.TryParse(txtSellingPrice.Text, out decimal sellingPrice))
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Invalid selling price format");
                    MessageBox.Show("Please enter a valid selling price", "Validation Error");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Price validation passed");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Purchase Price: {purchasePrice}, Selling Price: {sellingPrice}");

                var product = _isEditMode ? _existingProduct : new Product();
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Mode: {(_isEditMode ? "Edit" : "Add")}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Product ID: {product.Id}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] _isEditMode: {_isEditMode}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] _existingProduct is null: {_existingProduct == null}");
                
                product.Name = txtName.Text;
                product.Description = txtDescription.Text;
                product.PurchasePrice = purchasePrice;
                product.SellingPrice = sellingPrice;

                // Set product type based on radio button selection using helper method
                product.Type = GetIsServiceSelected() ? ProductType.Service : ProductType.Product;

                // Set weight-based sales method
                product.IsWeightBased = rbWeightBased?.IsChecked == true;
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] IsWeightBased: {product.IsWeightBased}");

                // Get the stock quantity from UI as decimal
                decimal stockQuantity = 0m;
                if (decimal.TryParse(txtCurrentStock.Text, out decimal parsedStock))
                {
                    stockQuantity = parsedStock;
                }
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Stock quantity parsed: {stockQuantity}");
                
                // Handle stock based on batch tracking
                bool trackBatches = chkTrackBatches.IsChecked.GetValueOrDefault();
                product.TrackBatches = trackBatches;
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Track batches: {trackBatches}");
                
                if (trackBatches)
                {
                    if (_isEditMode)
                    {
                        System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Existing batch-tracked product - stock managed via batches");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Creating initial batch for new product");
                        if (stockQuantity > 0)
                        {
                            // Initialize batches collection if needed
                            if (product.Batches == null)
                            {
                                product.Batches = new HashSet<BatchStock>();
                            }

                            // Create the initial batch with decimal quantity
                            var initialBatch = new BatchStock
                            {
                                BatchNumber = "INITIAL",
                                Quantity = stockQuantity,
                                ManufactureDate = DateTime.Now,
                                CreatedAt = DateTime.Now,
                                PurchasePrice = product.PurchasePrice,
                                SellingPrice = product.SellingPrice,
                                ExpiryDate = product.ExpiryDate
                            };

                            // Add the batch to the collection
                            product.Batches.Add(initialBatch);
                            System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Initial batch created with quantity: {stockQuantity}");

                            // For batch-tracked products, set stock quantity to the batch total
                            // This ensures the database constraint is satisfied while maintaining batch tracking
                            product.StockQuantity = stockQuantity;
                            System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Set stock quantity to {stockQuantity} for batch-tracked product (will be managed via batches)");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Setting non-batch product stock to: {stockQuantity}");
                    product.StockQuantity = stockQuantity;
                    if (product.Batches != null)
                    {
                        product.Batches.Clear();
                    }
                }
                
                if (int.TryParse(txtMinimumStock.Text, out int minimumStock))
                {
                    product.MinimumStock = minimumStock;
                }
                
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] ViewModel.SelectedCategoryId: {ViewModel.SelectedCategoryId}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] ViewModel.SelectedCategory: {ViewModel.SelectedCategory?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Category SelectedValue: {cmbCategory.SelectedValue}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Category SelectedItem: {cmbCategory.SelectedItem}");
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Categories count: {ViewModel.Categories?.Count ?? 0}");
                if (cmbCategory.SelectedItem is Category selectedCat)
                {
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Selected category: {selectedCat.Name} (ID: {selectedCat.Id})");
                }
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] UOM selection: {cmbUnitOfMeasure.SelectedValue}");

                // Validate CategoryId using the ViewModel binding
                if (ViewModel.SelectedCategoryId <= 0)
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["CategoryRequired"] ?? "Please select a category.",
                        (string)Application.Current.Resources["ValidationError"] ?? "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate UnitOfMeasureId
                if (cmbUnitOfMeasure.SelectedValue == null || (int)cmbUnitOfMeasure.SelectedValue <= 0)
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["UnitOfMeasureRequired"] ?? "Please select a unit of measure.",
                        (string)Application.Current.Resources["ValidationError"] ?? "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                product.CategoryId = ViewModel.SelectedCategoryId;
                product.UnitOfMeasureId = ViewModel.SelectedUnitOfMeasureId > 0 ? ViewModel.SelectedUnitOfMeasureId : (int?)null;
                product.IsActive = chkIsActive.IsChecked.GetValueOrDefault();
                
                // Handle expiry date
                if (chkHasExpiry.IsChecked.GetValueOrDefault() && dpExpiryDate.SelectedDate.HasValue)
                {
                    product.ExpiryDate = dpExpiryDate.SelectedDate.Value;
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Setting expiry date: {product.ExpiryDate}");
                }
                else
                {
                    product.ExpiryDate = null;
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] No expiry date set");
                }
                
                // Store the image data
                product.ImageData = _currentImageBase64;
                System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Image data present: {!string.IsNullOrEmpty(_currentImageBase64)}");
                
                // Handle barcodes - use temporary barcodes if available, otherwise use the single barcode field
                if (_temporaryBarcodes != null && _temporaryBarcodes.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Using temporary barcodes: {_temporaryBarcodes.Count}");
                    if (product.Barcodes == null)
                        product.Barcodes = new HashSet<ProductBarcode>();
                    else
                        product.Barcodes.Clear();

                    foreach (var tempBarcode in _temporaryBarcodes)
                    {
                        product.Barcodes.Add(new ProductBarcode
                        {
                            Barcode = tempBarcode.Barcode,
                            IsPrimary = tempBarcode.IsPrimary,
                            Description = tempBarcode.Description ?? "Barcode",
                            CreatedAt = DateTime.Now
                        });
                        System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Added barcode from temporary list: {tempBarcode.Barcode}");
                    }
                }
                else
                {
                    // Handle single barcode from text field
                    string barcode = txtBarcode.Text?.Trim();
                    if (!string.IsNullOrEmpty(barcode))
                    {
                        System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Processing single barcode: {barcode}");
                        if (product.Barcodes == null)
                            product.Barcodes = new HashSet<ProductBarcode>();

                        var existingBarcode = product.Barcodes.FirstOrDefault(b => b.IsPrimary);
                        if (existingBarcode != null)
                        {
                            existingBarcode.Barcode = barcode;
                            existingBarcode.Description = "Primary barcode";
                            System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Updated existing primary barcode");
                        }
                        else
                        {
                            product.Barcodes.Add(new ProductBarcode
                            {
                                Barcode = barcode,
                                IsPrimary = true,
                                Description = "Primary barcode",
                                CreatedAt = DateTime.Now
                            });
                            System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Added new primary barcode");
                        }
                    }
                }

                // Initialize other collections as HashSet
                if (product.Barcodes == null)
                {
                    product.Barcodes = new HashSet<ProductBarcode>();
                }
                if (product.Sales == null)
                {
                    product.Sales = new HashSet<SaleItem>();
                }
                if (product.InventoryTransactions == null)
                {
                    product.InventoryTransactions = new HashSet<InventoryTransaction>();
                }
                if (product.PriceHistory == null)
                {
                    product.PriceHistory = new HashSet<ProductPrice>();
                }

                System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] About to save product to database...");

                int savedProductId = 0;
                if (_isEditMode)
                {
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Updating existing product with ID: {product.Id}");

                    // SAFETY CHECK: Ensure we have a valid product ID for updates
                    if (product.Id <= 0)
                    {
                        System.Diagnostics.Debug.WriteLine("[SAVE ERROR] Invalid product ID for update operation!");
                        MessageBox.Show("Error: Invalid product ID for update. Cannot update product.", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    ViewModel.UpdateProduct(product);
                    savedProductId = product.Id;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Adding new product");

                    // SAFETY CHECK: Ensure we don't have an ID for new products
                    if (product.Id > 0)
                    {
                        System.Diagnostics.Debug.WriteLine("[SAVE WARNING] Product has ID but we're in add mode - resetting ID to 0");
                        product.Id = 0;
                    }

                    savedProductId = ViewModel.AddProduct(product);
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Product saved with ID: {savedProductId}");
                }

                // Verify the product was actually saved
                if (savedProductId <= 0 && !_isEditMode)
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE ERROR] Product was not saved properly - ID is 0 or negative");
                    MessageBox.Show("Error: Product was not saved properly. Please try again.", "Save Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Update the product ID if it was a new product
                if (!_isEditMode && savedProductId > 0)
                {
                    product.Id = savedProductId;
                    System.Diagnostics.Debug.WriteLine($"[SAVE DEBUG] Updated product ID to: {product.Id}");
                }

                System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Product saved successfully");

                // Save pricing tiers
                await SavePricingTiers(product);

                // Close the dialog with the result
                if (DialogHost.IsDialogOpen(_dialogIdentifier))
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE DEBUG] Closing dialog with success");
                    DialogHost.Close(_dialogIdentifier, product);
                }
            }
            catch (Exception ex)
            {
                // Enhanced error logging
                System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Exception type: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Stack trace: {ex.StackTrace}");
                
                // Special handling for DbUpdateException
                if (ex is Microsoft.EntityFrameworkCore.DbUpdateException dbEx)
                {
                    System.Diagnostics.Debug.WriteLine("[SAVE ERROR] Database Update Exception Details:");
                    
                    // Log all inner exceptions
                    var currentException = dbEx;
                    int level = 0;
                    while (currentException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Level {level} Exception:");
                        System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Message: {currentException.Message}");
                        
                        if (currentException.InnerException != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Inner Exception: {currentException.InnerException.Message}");
                            System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Inner Exception Type: {currentException.InnerException.GetType().Name}");
                        }
                        
                        // If it's a DbUpdateException, try to get more specific entity validation errors
                        if (currentException is Microsoft.EntityFrameworkCore.DbUpdateException updateEx)
                        {
                            foreach (var entry in updateEx.Entries)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Entity Type: {entry.Entity.GetType().Name}");
                                System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] State: {entry.State}");
                                System.Diagnostics.Debug.WriteLine("[SAVE ERROR] Current Values:");
                                foreach (var prop in entry.CurrentValues.Properties)
                                {
                                    var value = entry.CurrentValues[prop];
                                    System.Diagnostics.Debug.WriteLine($"[SAVE ERROR]     {prop.Name}: {value}");
                                }
                            }
                        }
                        
                        currentException = currentException.InnerException as Microsoft.EntityFrameworkCore.DbUpdateException;
                        level++;
                    }
                    
                    MessageBox.Show(
                        $"Database Error while saving product:\n\n" +
                        $"Error Type: {dbEx.GetType().Name}\n" +
                        $"Message: {dbEx.Message}\n\n" +
                        $"Inner Exception: {dbEx.InnerException?.Message ?? "None"}\n\n" +
                        "Please check the logs for more details.",
                        "Database Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
                else 
                {
                    // If there's an inner exception, log that too
                    if (ex.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Inner exception type: {ex.InnerException.GetType().Name}");
                        System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Inner exception message: {ex.InnerException.Message}");
                        System.Diagnostics.Debug.WriteLine($"[SAVE ERROR] Inner exception stack trace: {ex.InnerException.StackTrace}");
                    }
                    
                    MessageBox.Show(
                        $"Error saving product:\n\nError Type: {ex.GetType().Name}\nMessage: {ex.Message}\n\nInner Exception: {ex.InnerException?.Message ?? "None"}",
                        "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }
        
        private bool ValidateInput()
        {
            // Use helper method to safely get item type
            bool isService = GetIsServiceSelected();
            string itemType = isService ? "Service" : "Product";

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show($"{itemType} name is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtName.Focus();
                return false;
            }

            if (cmbCategory.SelectedValue == null)
            {
                MessageBox.Show("Please select a category.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbCategory.Focus();
                return false;
            }

            if (!decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                MessageBox.Show("Please enter a valid purchase price.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtPurchasePrice.Focus();
                return false;
            }

            if (!decimal.TryParse(txtSellingPrice.Text, out decimal sellingPrice) || sellingPrice < 0)
            {
                MessageBox.Show("Please enter a valid selling price.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtSellingPrice.Focus();
                return false;
            }

            // Stock validation only applies to products, not services
            if (!isService)
            {
                if (!decimal.TryParse(txtCurrentStock.Text, out decimal stockQuantity) || stockQuantity < 0)
                {
                    MessageBox.Show("Please enter a valid stock quantity.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCurrentStock.Focus();
                    return false;
                }
            }

            if (chkHasExpiry.IsChecked == true && !dpExpiryDate.SelectedDate.HasValue)
            {
                MessageBox.Show("Please select an expiry date or uncheck the 'Has Expiry' option.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                dpExpiryDate.Focus();
                return false;
            }

            // Validate quantity
            if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
            {
                MessageBox.Show("Please enter a valid quantity greater than 0.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtQuantity.Focus();
                return false;
            }

            // For non-single units, ensure quantity is meaningful
            if (cmbUnitOfMeasure.SelectedItem is UnitOfMeasure selectedUnit)
            {
                var singleUnitNames = new[] { "piece", "item", "unit", "each", "قطعة", "pièce", "stück", "pieza" };
                bool isSingleUnit = singleUnitNames.Any(name =>
                    selectedUnit.Name.Equals(name, StringComparison.OrdinalIgnoreCase) ||
                    selectedUnit.Abbreviation.Equals(name, StringComparison.OrdinalIgnoreCase));

                if (!isSingleUnit && quantity < 0.001m)
                {
                    MessageBox.Show("Please enter a meaningful quantity for measurement units.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtQuantity.Focus();
                    return false;
                }
            }

            return true;
        }
        
        private void NumberValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow decimal numbers with up to 2 decimal places
            var regex = new System.Text.RegularExpressions.Regex(@"^[0-9]*(?:\.[0-9]*)?$");
            string newText = ((TextBox)sender).Text + e.Text;
            e.Handled = !regex.IsMatch(newText);
        }

        private void DecimalValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Enhanced decimal validation with context-aware precision
            var textBox = sender as TextBox;
            string newText = textBox.Text + e.Text;

            // Basic decimal format validation
            var regex = new System.Text.RegularExpressions.Regex(@"^[0-9]*(?:\.[0-9]*)?$");
            if (!regex.IsMatch(newText))
            {
                e.Handled = true;
                return;
            }

            // Context-aware decimal precision based on unit type and field
            if (newText.Contains("."))
            {
                string[] parts = newText.Split('.');
                if (parts.Length > 1)
                {
                    int maxDecimals = 3; // Default for weight/volume products

                    // Determine precision based on context
                    if (textBox == txtCurrentStock && cmbUnitOfMeasure.SelectedItem is UnitOfMeasure selectedUnit)
                    {
                        if (ViewModel.IsUnitBasedUnit(selectedUnit))
                        {
                            maxDecimals = 0; // No decimals for unit-based stock
                        }
                        else if (ViewModel.IsWeightUnit(selectedUnit) || ViewModel.IsVolumeUnit(selectedUnit))
                        {
                            maxDecimals = 3; // Up to 3 decimal places for weight/volume stock
                        }
                    }
                    else if (textBox == txtQuantity)
                    {
                        // Quantity field always allows 3 decimal places for flexibility
                        maxDecimals = 3;
                    }

                    if (parts[1].Length > maxDecimals)
                    {
                        e.Handled = true;
                        return;
                    }
                }
            }

            e.Handled = false;
        }

        private void CmbUnitOfMeasure_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbUnitOfMeasure.SelectedItem is UnitOfMeasure selectedUnit)
            {
                // Check if this is a single unit (piece, item, etc.)
                var singleUnitNames = new[] { "piece", "item", "unit", "each", "قطعة", "pièce", "stück", "pieza" };
                bool isSingleUnit = singleUnitNames.Any(name =>
                    selectedUnit.Name.Equals(name, StringComparison.OrdinalIgnoreCase) ||
                    selectedUnit.Abbreviation.Equals(name, StringComparison.OrdinalIgnoreCase));

                if (isSingleUnit)
                {
                    // For single units, set quantity to 1 and make it less prominent
                    txtQuantity.Text = "1.000";
                    txtQuantity.IsEnabled = false;
                    txtQuantity.Opacity = 0.6;
                }
                else
                {
                    // For measurement units, enable quantity input
                    txtQuantity.IsEnabled = true;
                    txtQuantity.Opacity = 1.0;
                    if (string.IsNullOrEmpty(txtQuantity.Text) || txtQuantity.Text == "1.000")
                    {
                        txtQuantity.Text = "1.000";
                    }

                    // Automatically focus on quantity field for immediate data entry
                    // Use Dispatcher to ensure the UI has updated before setting focus
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        txtQuantity.Focus();
                        txtQuantity.SelectAll(); // Select all text for easy replacement
                    }), System.Windows.Threading.DispatcherPriority.Input);
                }

                // Update stock field hint and placeholder based on unit type
                UpdateStockFieldForUnitType();
            }
        }

        private void IntegerValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only integers
            var regex = new System.Text.RegularExpressions.Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }
        
        private void TxtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                
                string barcode = txtBarcode.Text.Trim();
                if (!string.IsNullOrEmpty(barcode))
                {
                    try
                    {
                        // First check in external database
                        _ = LookupProductByBarcodeAsync(barcode);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(
                            $"Error searching for barcode: {ex.Message}",
                            "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async Task LookupProductByBarcodeAsync(string barcode)
        {
            // First try to look up in the external product database
            var externalProduct = await _productLookupService.LookupProductByBarcodeAsync(barcode);
            if (externalProduct != null)
            {
                var result = MessageBox.Show(
                    "Product found in external database. Would you like to auto-fill the product details?",
                    "Product Found",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Auto-fill the form with product information from the external database
                    AutoFillExternalProductInformation(externalProduct);
                    return;
                }
            }
            
            // If not found in external database or user declined, check the local database
            try
            {
                // Look up the product by barcode in local database
                var product = _dbService.GetProductByBarcode(barcode);
                
                if (product != null)
                {
                    // If we're in edit mode and the barcode belongs to a different product,
                    // alert the user and don't proceed
                    if (_isEditMode && product.Id != _existingProduct.Id)
                    {
                        MessageBox.Show(
                            $"This barcode already belongs to another product: {product.Name}",
                            "Duplicate Barcode",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    
                    // Auto-fill the form with product information
                    AutoFillProductInformation(product);
                    
                    // If we're in add mode, show a message that we found a product
                    if (!_isEditMode)
                    {
                        MessageBox.Show(
                            $"Found existing product: {product.Name}. The form has been filled with the product information.",
                            "Product Found",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    // Barcode doesn't exist, inform the user it's available
                    MessageBox.Show(
                        "This barcode is available for use.",
                        "Barcode Available",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error searching for barcode in local database: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        public void AutoFillExternalProductInformation(Product externalProduct)
        {
            _isEditMode = false;
            
            // Update UI to show we're in add mode with external import indicator
            DialogTitle.Text = "Add Product (Imported from External Database)";
            btnSave.Content = (string)Application.Current.Resources["Save"];
            
            // Fill in basic information
            txtName.Text = externalProduct.Name;
            txtDescription.Text = externalProduct.Description;
            
            // Select category if it exists by name
            if (externalProduct.Category != null && ViewModel?.Categories != null)
            {
                var category = ViewModel.Categories.FirstOrDefault(c =>
                    c.Name.Equals(externalProduct.Category.Name, StringComparison.OrdinalIgnoreCase));

                if (category != null)
                    ViewModel.SelectedCategoryId = category.Id;
            }
            
            // Fill in pricing information
            txtPurchasePrice.Text = externalProduct.PurchasePrice.ToString("0.00");
            txtSellingPrice.Text = externalProduct.SellingPrice.ToString("0.00");
            
            // Note: Barcode will be set separately using SetBarcodeFromExternal method
            
            // Update image if available
            if (!string.IsNullOrEmpty(externalProduct.ImageData))
            {
                _currentImageBase64 = externalProduct.ImageData;
                UpdateImageDisplay(_currentImageBase64);
            }
            
            // Default values for other fields
            txtMinimumStock.Text = "10";
            txtCurrentStock.Text = "0";
            
            chkIsActive.IsChecked = true;
        }
        
        private void AutoFillProductInformation(Product product)
        {
            // Set the existing product reference
            _existingProduct = product;
            _isEditMode = true;
            
            // Update UI to show we're in edit mode
            DialogTitle.Text = (string)Application.Current.Resources["EditProduct"];
            btnSave.Content = (string)Application.Current.Resources["Update"];
            
            // Fill in basic information
            txtName.Text = product.Name;
            txtDescription.Text = product.Description;
            
            // Select category if it exists
            ViewModel.SelectedCategoryId = product.CategoryId;
            
            // Select unit of measure if it exists
            if (product.UnitOfMeasureId.HasValue)
            {
                ViewModel.SelectedUnitOfMeasureId = product.UnitOfMeasureId.Value;
            }
            
            // Fill in pricing and stock information
            txtPurchasePrice.Text = product.PurchasePrice.ToString("0.00");
            txtSellingPrice.Text = product.SellingPrice.ToString("0.00");
            txtMinimumStock.Text = product.MinimumStock.ToString();
            var stockQuantity = product.StockQuantity;
            txtCurrentStock.Text = stockQuantity % 1 == 0 ? stockQuantity.ToString("F0") : stockQuantity.ToString("0.###");
            
            // Set other properties
            chkIsActive.IsChecked = product.IsActive;
            chkIsFavorite.IsChecked = product.IsFavorited;
            chkHasExpiry.IsChecked = product.HasExpiry;
            chkTrackBatches.IsChecked = product.TrackBatches;
            
            if (product.HasExpiry && product.ExpiryDate.HasValue)
                dpExpiryDate.SelectedDate = product.ExpiryDate.Value;
            
            // Set the barcode field if available
            var primaryBarcode = product.Barcodes?.FirstOrDefault(b => b.IsPrimary);
            if (primaryBarcode != null)
                txtBarcode.Text = primaryBarcode.Barcode;
            
            // Update image if available
            if (!string.IsNullOrEmpty(product.ImageData))
            {
                _currentImageBase64 = product.ImageData;
                UpdateImageDisplay(_currentImageBase64);
            }
            else
            {
                _currentImageBase64 = null;
                UpdateImageDisplay(null);
            }
        }
        
        private void GenerateBarcode_Click(object sender, RoutedEventArgs e)
        {
            // Generate a new barcode (this is placeholder logic - adjust as needed)
            var random = new Random();
            var barcode = DateTime.Now.ToString("yyMMddHHmmss") + random.Next(10, 99).ToString();
            txtBarcode.Text = barcode;
        }
        
        /// <summary>
        /// Sets the barcode text in the dialog
        /// </summary>
        /// <param name="barcode">The barcode to set</param>
        public void SetBarcode(string barcode)
        {
            if (!string.IsNullOrWhiteSpace(barcode))
            {
                txtBarcode.Text = barcode;
                // External database lookup removed per user request
            }
        }

        /// <summary>
        /// Sets the barcode from external import and makes it read-only to prevent conflicts
        /// </summary>
        /// <param name="barcode">The barcode from external database</param>
        public void SetBarcodeFromExternal(string barcode)
        {
            if (!string.IsNullOrWhiteSpace(barcode))
            {
                txtBarcode.Text = barcode;
                txtBarcode.IsReadOnly = true;
                txtBarcode.ToolTip = "This barcode was imported from external database and cannot be modified";

                // Add visual indicator that this is from external source
                txtBarcode.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromArgb(20, 33, 150, 243)); // Light blue tint
            }
        }
        
        private void ManageBarcodes_Click(object sender, RoutedEventArgs e)
        {
            // Only allow barcode management if we have a product
            Product productToManage = _existingProduct;
            
            // If we're in add mode, we need to create a temporary product with the current data
            if (!_isEditMode)
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["ProductNameRequired"],
                        (string)Application.Current.Resources["ValidationError"],
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Create a temporary product with the current field data
                productToManage = new Product
                {
                    Name = txtName.Text,
                    Id = -1, // Temporary ID
                    Barcodes = new HashSet<ProductBarcode>()
                };
                
                // Add the current barcode if it exists
                if (!string.IsNullOrWhiteSpace(txtBarcode.Text))
                {
                    productToManage.Barcodes.Add(new ProductBarcode 
                    { 
                        Barcode = txtBarcode.Text,
                        IsPrimary = true,
                        Description = "Primary barcode" 
                    });
                }
            }
            
            if (productToManage != null)
            {
                try
                {
                    // Close the current dialog temporarily
                    DialogHost.Close(_dialogIdentifier);
                    
                    // Create and show the barcode management window
                    var barcodeWindow = new BarcodeManagementWindow(productToManage, _dbService);
                    
                    if (barcodeWindow.ShowDialog() == true)
                    {
                        // Update the product with new barcodes if needed
                        if (_isEditMode)
                        {
                            // The database service has already updated the product's barcodes
                            // Refresh the barcode display
                            var primaryBarcode = productToManage.Barcodes?.FirstOrDefault(b => b.IsPrimary);
                            if (primaryBarcode != null)
                            {
                                txtBarcode.Text = primaryBarcode.Barcode;
                            }
                        }
                        else
                        {
                            // For new product, store the barcodes for later use when saving
                            _temporaryBarcodes = productToManage.Barcodes?.ToList() ?? new List<ProductBarcode>();

                            // Update the barcode field with the primary one
                            var primaryBarcode = productToManage.Barcodes?.FirstOrDefault(b => b.IsPrimary);
                            if (primaryBarcode != null)
                            {
                                txtBarcode.Text = primaryBarcode.Barcode;
                            }
                        }
                    }
                    
                    // Reopen the product dialog
                    DialogHost.Show(this, _dialogIdentifier);
                }
                catch (Exception ex)
                {
            MessageBox.Show(
                        $"Error opening barcode management: {ex.Message}",
                        "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    
                    // Make sure to reopen the dialog if there was an error
                    DialogHost.Show(this, _dialogIdentifier);
                }
            }
        }
        
        private void UploadImage_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Select Product Image",
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.gif)|*.jpg;*.jpeg;*.png;*.gif",
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var imagePath = openFileDialog.FileName;
                    byte[] imageData = File.ReadAllBytes(imagePath);
                    _currentImageBase64 = Convert.ToBase64String(imageData);
                    UpdateImageDisplay(_currentImageBase64);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Error loading image: {ex.Message}",
                        "Image Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private void RemoveImage_Click(object sender, RoutedEventArgs e)
        {
            _currentImageBase64 = null;
            UpdateImageDisplay(null);
        }
        
        private async void AddCategory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create and show the CategoryDialog with the correct dialog identifier
                var categoryDialog = new CategoryDialog(null, _dialogIdentifier);

                // Close the current dialog temporarily
                DialogHost.Close(_dialogIdentifier);

                // Show the category dialog using the same dialog host identifier
                var result = await DialogHost.Show(categoryDialog, _dialogIdentifier);

                if (result is Category newCategory)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] Received new category '{newCategory.Name}' with ID: {newCategory.Id}");

                    // Refresh the categories collection from database using the existing database service
                    var categories = _dbService.GetAllCategories();

                    // Update the ViewModel's categories collection on the UI thread
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        ViewModel.Categories.Clear();
                        foreach (var category in categories)
                        {
                            ViewModel.Categories.Add(category);
                        }

                        System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] Categories refreshed. Total count: {ViewModel.Categories.Count}");
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] New category '{newCategory.Name}' should be selected after dialog reopens");
                    });

                    // Store the category ID to select after dialog reopens
                    var categoryIdToSelect = newCategory.Id;
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] Will attempt to select category ID: {categoryIdToSelect}");

                    // Trigger the CategoryChanged event to notify other ViewModels
                    CategoriesViewModel.NotifyCategoryAdded(newCategory);

                    // Set the selection after the dialog is reopened (in the finally block)
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(500); // Wait for dialog to reopen
                        await SetSelectedCategoryAsync(categoryIdToSelect);
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error adding category: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Make sure the product dialog is reopened
                if (!DialogHost.IsDialogOpen(_dialogIdentifier))
                {
                    await DialogHost.Show(this, _dialogIdentifier);
                }
            }
        }

        private int? GetCurrentUserId()
        {
            try
            {
                // Try to get the current user ID from the AuthService in the MainWindow
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow != null)
                {
                    // First try using a direct type check with MainWindow
                    var mainWindowType = mainWindow.GetType();
                    if (mainWindowType.Name == "MainWindow")
                    {
                        // Use reflection to get the _authService field
                        var authServiceField = mainWindowType.GetField("_authService", 
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        
                        if (authServiceField != null)
                        {
                            var authService = authServiceField.GetValue(mainWindow);
                            
                            // Get the CurrentUser property from AuthService
                            var currentUserProperty = authService.GetType().GetProperty("CurrentUser");
                            if (currentUserProperty != null)
                            {
                                var currentUser = currentUserProperty.GetValue(authService) as Models.User;
                                if (currentUser != null && currentUser.Id > 0)
                                {
                                    return currentUser.Id;
                                }
                            }
                        }
                    }
                }
                
                // If we couldn't get the user from MainWindow, try another approach
                // Look for a static current user property or field in the App class
                var appType = typeof(Application).Assembly.GetType("POSSystem.App");
                if (appType != null)
                {
                    var currentUserProperty = appType.GetProperty("CurrentUser", 
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                        
                    if (currentUserProperty != null)
                    {
                        var currentUser = currentUserProperty.GetValue(null) as Models.User;
                        if (currentUser != null && currentUser.Id > 0)
                        {
                            return currentUser.Id;
                        }
                    }
                }
                
                // Fallback: just get first active user from database
                using (var context = new POSDbContext())
                {
                    var anyUser = context.Users.FirstOrDefault(u => u.IsActive);
                    if (anyUser != null)
                    {
                        return anyUser.Id;
                    }
                }
                
                // If all else fails, return null
                return null;
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error getting current user: {ex.Message}");
                return null;
            }
        }

        private void CmbCategory_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var comboBox = sender as ComboBox;
            var selectedCategory = comboBox?.SelectedItem as Category;
            System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] ComboBox SelectionChanged: {selectedCategory?.Name ?? "null"} (ID: {selectedCategory?.Id ?? 0})");
        }

        /// <summary>
        /// Sets the selected category after ensuring the dialog is fully loaded
        /// </summary>
        public async Task SetSelectedCategoryAsync(int categoryId)
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Wait for the dialog to be fully loaded
                await Task.Delay(200);

                var category = ViewModel.Categories.FirstOrDefault(c => c.Id == categoryId);
                if (category != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] SetSelectedCategoryAsync: Setting category '{category.Name}' (ID: {category.Id})");

                    // Set the selection using multiple approaches
                    ViewModel.SelectedCategory = category;
                    ViewModel.SelectedCategoryId = category.Id;
                    cmbCategory.SelectedItem = category;
                    cmbCategory.SelectedValue = category.Id;

                    // Force refresh
                    cmbCategory.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] SetSelectedCategoryAsync completed. Selected: {((Category)cmbCategory.SelectedItem)?.Name ?? "null"}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT DIALOG] SetSelectedCategoryAsync: Category with ID {categoryId} not found");
                }
            });
        }

        private async Task UpdateFavoriteStatusAsync(int productId, bool isFavorited)
        {
            try
            {
                // Get the current user ID
                var userId = GetCurrentUserId();
                if (!userId.HasValue)
                {
                    _alertService.ShowError("Unable to update favorites: No user is currently logged in.");
                    return;
                }

                using (var context = new POSDbContext())
                {
                    // Check if there's an existing favorite entry
                    var existingFavorite = await context.UserFavorites
                        .FirstOrDefaultAsync(f => f.UserId == userId.Value && f.ProductId == productId);

                    if (isFavorited)
                    {
                        // Add to favorites if not already favorited
                        if (existingFavorite == null)
                        {
                            var newFavorite = new UserFavorite
                            {
                                UserId = userId.Value,
                                ProductId = productId,
                                CreatedAt = DateTime.Now
                            };
                            
                            context.UserFavorites.Add(newFavorite);
                            await context.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        // Remove from favorites if currently favorited
                        if (existingFavorite != null)
                        {
                            context.UserFavorites.Remove(existingFavorite);
                            await context.SaveChangesAsync();
                        }
                    }
                    
                    // Update local product state
                    if (_existingProduct != null)
                    {
                        _existingProduct.IsFavorited = isFavorited;
                    }
                }
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error updating favorite status: {ex.Message}");
            }
        }

        private async void CheckIfProductIsFavorited(int productId)
        {
            try
            {
                // Get the current user ID
                var userId = GetCurrentUserId();
                if (!userId.HasValue)
                {
                    // No user logged in
                    chkIsFavorite.IsChecked = false;
                    return;
                }

                using (var context = new POSDbContext())
                {
                    // Check if there's an existing favorite entry
                    var isFavorited = await context.UserFavorites
                        .AnyAsync(f => f.UserId == userId.Value && f.ProductId == productId);
                    
                    // Update the checkbox
                    chkIsFavorite.IsChecked = isFavorited;
                    
                    // Also update the product's IsFavorited property
                    if (_existingProduct != null)
                    {
                        _existingProduct.IsFavorited = isFavorited;
                    }
                }
            }
            catch (Exception ex)
            {
                _alertService.ShowError($"Error checking favorite status: {ex.Message}");
                chkIsFavorite.IsChecked = false;
            }
        }

        private void ManageBatches_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_existingProduct == null || !_existingProduct.TrackBatches)
                {
                    MessageBox.Show("You must save the product with batch tracking enabled before managing batches.", 
                        "Info", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // First, ensure we have the latest product data
                LoadBatchesForProduct(_existingProduct);

                // Open the batch management window
                var batchWindow = new BatchStockWindow(_existingProduct, _dbService)
                {
                    Owner = Window.GetWindow(this)
                };

                batchWindow.ShowDialog();

                // After closing the batch window, reload the product data with updated batches
                LoadBatchesForProduct(_existingProduct);

                // Update the UI with the new batch information
                var totalStock = _existingProduct.GetTotalStockDecimal();
                txtCurrentStock.Text = totalStock % 1 == 0 ? totalStock.ToString("F0") : totalStock.ToString("0.###");
                UpdateBatchInformation(_existingProduct);

                // Log the updated information
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Product '{_existingProduct.Name}' updated after batch management.");
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] New total stock: {_existingProduct.GetTotalStockDecimal()}, " +
                    $"Batch count: {_existingProduct.BatchCount}, Batch stock: {_existingProduct.BatchStockQuantity}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error in ManageBatches_Click: {ex.Message}");
                MessageBox.Show($"Error managing batches: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Safely gets the current item type selection
        /// </summary>
        /// <returns>True if Service is selected, False if Product is selected or if controls are not initialized</returns>
        private bool GetIsServiceSelected()
        {
            return rbService?.IsChecked == true;
        }

        private void ItemType_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                // Add null checks to prevent NullReferenceException
                if (rbService == null || rbProduct == null)
                {
                    System.Diagnostics.Debug.WriteLine("[ITEM_TYPE] Radio buttons not yet initialized, skipping event");
                    return;
                }

                bool isService = GetIsServiceSelected();
                UpdateUIForItemType(isService);
                UpdateDialogTitle(isService);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error in ItemType_Changed: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Stack trace: {ex.StackTrace}");
            }
        }

        private void UnitBased_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[SALES_METHOD] UnitBased_Click triggered!");

                if (ViewModel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] Setting IsWeightBased to FALSE");
                    ViewModel.IsWeightBased = false;

                    // Apply smart unit filtering for unit-based products
                    FilterUnitsForSalesMethod(false);

                    UpdateQuantityInputForSalesMethod(false);
                    System.Diagnostics.Debug.WriteLine("[SALES_METHOD] Unit-based mode activated with smart unit filtering");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] Error in UnitBased_Click: {ex.Message}");
            }
        }

        private void WeightBased_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[SALES_METHOD] WeightBased_Click triggered!");

                if (ViewModel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] Setting IsWeightBased to TRUE");
                    ViewModel.IsWeightBased = true;

                    // Apply smart unit filtering for weight/volume-based products
                    FilterUnitsForSalesMethod(true);

                    UpdateQuantityInputForSalesMethod(true);
                    System.Diagnostics.Debug.WriteLine("[SALES_METHOD] Weight-based mode activated with smart unit filtering");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] Error in WeightBased_Click: {ex.Message}");
            }
        }

        private void UpdateQuantityInputForSalesMethod(bool isWeightBased)
        {
            try
            {
                if (txtQuantity != null)
                {
                    if (isWeightBased)
                    {
                        // For weight-based products, allow decimal input
                        txtQuantity.ToolTip = "Enter weight quantity (e.g., 2.5, 1.75)";

                        // Update placeholder text to show weight example
                        MaterialDesignThemes.Wpf.HintAssist.SetHint(txtQuantity, "Weight (e.g., 2.5)");

                        // Set a sample weight value if currently 1.000
                        if (ViewModel != null && ViewModel.ProductQuantity == 1.0m)
                        {
                            ViewModel.ProductQuantity = 1.0m; // Keep as 1.0 but user can enter decimals
                        }
                    }
                    else
                    {
                        // For unit-based products, suggest whole numbers
                        txtQuantity.ToolTip = "Enter unit quantity (whole numbers recommended)";

                        // Update placeholder text to show unit example
                        MaterialDesignThemes.Wpf.HintAssist.SetHint(txtQuantity, "Units (e.g., 5)");

                        // If current quantity has decimals, round it to whole number
                        if (ViewModel != null && ViewModel.ProductQuantity != Math.Floor(ViewModel.ProductQuantity))
                        {
                            ViewModel.ProductQuantity = Math.Max(1.0m, Math.Floor(ViewModel.ProductQuantity));
                        }
                    }
                }

                // Force UI update to show the visual indicator
                var indicator = this.FindName("salesMethodIndicator") as Border;
                if (indicator != null)
                {
                    indicator.Visibility = isWeightBased ? Visibility.Visible : Visibility.Collapsed;
                }

                System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] UI updated for {(isWeightBased ? "weight-based" : "unit-based")} sales");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES_METHOD] Error updating quantity input: {ex.Message}");
            }
        }

        private void UpdateUIForItemType(bool isService)
        {
            try
            {
                // Update field hints and labels dynamically
                UpdateFieldHints(isService);

                // Update unit of measure visibility and behavior
                if (cmbUnitOfMeasure != null)
                {
                    if (isService)
                    {
                        // For services, hide weight/volume units and show only time/count units
                        FilterUnitsForServices();
                        // Set default to "hour" or "unit" for services
                        SetDefaultServiceUnit();
                    }
                    else
                    {
                        // For products, apply smart unit filtering based on current sales method
                        bool isWeightBased = rbWeightBased?.IsChecked == true;
                        FilterUnitsForSalesMethod(isWeightBased);
                    }
                }

                // Update quantity field behavior
                if (txtQuantity != null)
                {
                    if (isService)
                    {
                        // For services, quantity often represents hours or service units
                        txtQuantity.ToolTip = "Enter the quantity of service units (e.g., hours, sessions)";
                    }
                    else
                    {
                        // For products, quantity represents physical items
                        txtQuantity.ToolTip = "Enter the quantity of physical items";
                    }
                }

                // Update stock-related fields visibility
                UpdateStockFieldsVisibility(!isService);

                // Update batch tracking availability
                if (chkTrackBatches != null)
                {
                    chkTrackBatches.IsEnabled = !isService;
                    if (isService)
                    {
                        chkTrackBatches.IsChecked = false;
                    }
                }

                // Update expiry date relevance
                if (chkHasExpiry != null)
                {
                    chkHasExpiry.IsEnabled = !isService;
                    if (isService)
                    {
                        chkHasExpiry.IsChecked = false;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] UI updated for {(isService ? "Service" : "Product")} mode");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error updating UI: {ex.Message}");
            }
        }

        private void UpdateFieldHints(bool isService)
        {
            try
            {
                // Update name field hint
                if (txtName != null)
                {
                    MaterialDesignThemes.Wpf.HintAssist.SetHint(txtName,
                        isService ? "Service Name" : Application.Current.Resources["ProductName"]?.ToString() ?? "Product Name");
                }

                // Update description field hint
                if (txtDescription != null)
                {
                    MaterialDesignThemes.Wpf.HintAssist.SetHint(txtDescription,
                        isService ? "Service Description" : "Product Description");
                }

                // Update quantity field hint
                if (txtQuantity != null)
                {
                    MaterialDesignThemes.Wpf.HintAssist.SetHint(txtQuantity,
                        isService ? "Service Quantity" : Application.Current.Resources["ProductQuantity"]?.ToString() ?? "Product Quantity");
                }

                // Update price field hints
                if (txtPurchasePrice != null)
                {
                    MaterialDesignThemes.Wpf.HintAssist.SetHint(txtPurchasePrice,
                        isService ? "Service Cost" : "Purchase Price");
                }

                if (txtSellingPrice != null)
                {
                    MaterialDesignThemes.Wpf.HintAssist.SetHint(txtSellingPrice,
                        isService ? "Service Price" : "Selling Price");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error updating field hints: {ex.Message}");
            }
        }

        private void UpdateDialogTitle(bool isService)
        {
            try
            {
                if (DialogTitle != null)
                {
                    if (_isEditMode)
                    {
                        DialogTitle.Text = isService ? "Edit Service" : (string)Application.Current.Resources["EditProduct"];
                    }
                    else
                    {
                        DialogTitle.Text = isService ? "Add Service" : (string)Application.Current.Resources["AddProduct"];
                    }
                }

                // Update the icon
                var icon = this.FindName("DialogIcon") as MaterialDesignThemes.Wpf.PackIcon;
                if (icon != null)
                {
                    icon.Kind = isService ? MaterialDesignThemes.Wpf.PackIconKind.AccountCog : MaterialDesignThemes.Wpf.PackIconKind.Package;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error updating dialog title: {ex.Message}");
            }
        }

        private void FilterUnitsForServices()
        {
            try
            {
                if (ViewModel != null)
                {
                    // Use the ViewModel's service unit filtering
                    var serviceUnits = ViewModel.GetServiceUnits();
                    cmbUnitOfMeasure.ItemsSource = serviceUnits;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error filtering units for services: {ex.Message}");
            }
        }

        private void SetDefaultServiceUnit()
        {
            try
            {
                if (ViewModel != null)
                {
                    var defaultUnit = ViewModel.GetDefaultServiceUnit();
                    if (defaultUnit != null)
                    {
                        cmbUnitOfMeasure.SelectedValue = defaultUnit.Id;
                        ViewModel.SelectedUnitOfMeasureId = defaultUnit.Id;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error setting default service unit: {ex.Message}");
            }
        }

        private void RestoreAllUnits()
        {
            try
            {
                if (ViewModel?.UnitsOfMeasure != null)
                {
                    // Restore all units for products
                    cmbUnitOfMeasure.ItemsSource = ViewModel.UnitsOfMeasure;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error restoring all units: {ex.Message}");
            }
        }

        /// <summary>
        /// Filters units based on the current sales method (weight-based vs unit-based)
        /// </summary>
        private void FilterUnitsForSalesMethod(bool isWeightBased)
        {
            try
            {
                if (ViewModel == null) return;

                System.Diagnostics.Debug.WriteLine($"[UNIT_FILTER] Filtering units for {(isWeightBased ? "weight-based" : "unit-based")} sales method");

                if (isWeightBased)
                {
                    // For weight-based products, show weight and volume units
                    var weightUnits = ViewModel.GetWeightUnits();
                    var volumeUnits = ViewModel.GetVolumeUnits();

                    // Combine weight and volume units
                    var combinedUnits = new ObservableCollection<UnitOfMeasure>();
                    foreach (var unit in weightUnits)
                        combinedUnits.Add(unit);
                    foreach (var unit in volumeUnits)
                        combinedUnits.Add(unit);

                    cmbUnitOfMeasure.ItemsSource = combinedUnits;

                    // Set default to weight unit (kg)
                    var defaultUnit = ViewModel.GetDefaultWeightUnit();
                    if (defaultUnit != null)
                    {
                        cmbUnitOfMeasure.SelectedValue = defaultUnit.Id;
                        ViewModel.SelectedUnitOfMeasureId = defaultUnit.Id;
                        System.Diagnostics.Debug.WriteLine($"[UNIT_FILTER] Set default weight unit: {defaultUnit.Name}");
                    }
                }
                else
                {
                    // For unit-based products, show only unit-based units
                    var unitBasedUnits = ViewModel.GetUnitBasedUnits();
                    cmbUnitOfMeasure.ItemsSource = unitBasedUnits;

                    // Set default to unit-based unit (piece)
                    var defaultUnit = ViewModel.GetDefaultUnitBasedUnit();
                    if (defaultUnit != null)
                    {
                        cmbUnitOfMeasure.SelectedValue = defaultUnit.Id;
                        ViewModel.SelectedUnitOfMeasureId = defaultUnit.Id;
                        System.Diagnostics.Debug.WriteLine($"[UNIT_FILTER] Set default unit-based unit: {defaultUnit.Name}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[UNIT_FILTER] Unit filtering completed. Available units: {cmbUnitOfMeasure.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[UNIT_FILTER] Error filtering units for sales method: {ex.Message}");
                // Fall back to showing all units
                RestoreAllUnits();
            }
        }

        /// <summary>
        /// Updates the current stock field hint, placeholder, and visual indicators based on the selected unit type
        /// </summary>
        private void UpdateStockFieldForUnitType()
        {
            try
            {
                if (cmbUnitOfMeasure.SelectedItem is UnitOfMeasure selectedUnit && txtCurrentStock != null)
                {
                    // Update the hint based on unit type
                    if (ViewModel.IsWeightUnit(selectedUnit))
                    {
                        md:HintAssist.SetHint(txtCurrentStock, $"Current Stock ({selectedUnit.Abbreviation})");
                        txtCurrentStock.ToolTip = $"Enter stock quantity in {selectedUnit.Name}. Decimal values are supported for weight-based products.";

                        // Show weight indicator
                        UpdateStockUnitIndicator("Weight", "Scale", true);
                    }
                    else if (ViewModel.IsVolumeUnit(selectedUnit))
                    {
                        md:HintAssist.SetHint(txtCurrentStock, $"Current Stock ({selectedUnit.Abbreviation})");
                        txtCurrentStock.ToolTip = $"Enter stock quantity in {selectedUnit.Name}. Decimal values are supported for volume-based products.";

                        // Show volume indicator
                        UpdateStockUnitIndicator("Volume", "Cup", true);
                    }
                    else
                    {
                        md:HintAssist.SetHint(txtCurrentStock, $"Current Stock ({selectedUnit.Abbreviation})");
                        txtCurrentStock.ToolTip = $"Enter stock quantity in {selectedUnit.Name}.";

                        // Hide indicator for unit-based products
                        UpdateStockUnitIndicator("", "", false);
                    }

                    System.Diagnostics.Debug.WriteLine($"[STOCK_FIELD] Updated stock field hint and indicators for unit: {selectedUnit.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_FIELD] Error updating stock field for unit type: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the visual indicator for the stock field based on unit type
        /// </summary>
        private void UpdateStockUnitIndicator(string text, string iconKind, bool isVisible)
        {
            try
            {
                // Find the elements by name to handle cases where they might not be directly accessible
                var stockIndicator = this.FindName("stockUnitIndicator") as Border;
                var stockText = this.FindName("stockUnitText") as TextBlock;
                var stockIcon = this.FindName("stockUnitIcon") as MaterialDesignThemes.Wpf.PackIcon;

                if (stockIndicator != null)
                {
                    stockIndicator.Visibility = isVisible ? Visibility.Visible : Visibility.Collapsed;

                    if (isVisible && stockText != null)
                    {
                        stockText.Text = text;

                        // Update icon based on type
                        if (!string.IsNullOrEmpty(iconKind) && stockIcon != null)
                        {
                            if (Enum.TryParse<MaterialDesignThemes.Wpf.PackIconKind>(iconKind, out var iconEnum))
                            {
                                stockIcon.Kind = iconEnum;
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"[STOCK_INDICATOR] Updated stock unit indicator: {text} ({iconKind}) - Visible: {isVisible}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK_INDICATOR] Stock unit indicator elements not found - this is expected if not using visual indicators");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_INDICATOR] Error updating stock unit indicator: {ex.Message}");
            }
        }

        private void UpdateStockFieldsVisibility(bool showStockFields)
        {
            try
            {
                // Update stock quantity field
                if (txtCurrentStock != null)
                {
                    txtCurrentStock.IsEnabled = showStockFields;
                    txtCurrentStock.Opacity = showStockFields ? 1.0 : 0.5;
                    if (!showStockFields)
                    {
                        txtCurrentStock.Text = "N/A";
                    }
                }

                // Update minimum stock field
                if (txtMinimumStock != null)
                {
                    txtMinimumStock.IsEnabled = showStockFields;
                    txtMinimumStock.Opacity = showStockFields ? 1.0 : 0.5;
                    if (!showStockFields)
                    {
                        txtMinimumStock.Text = "0";
                    }
                }

                // Note: ReorderPoint field not present in current XAML, skipping update
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ITEM_TYPE] Error updating stock fields visibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads only Categories and UnitsOfMeasure without affecting the Products collection
        /// This prevents duplication in the main view when the dialog opens
        /// </summary>
        private async Task LoadDialogEssentialDataOnly()
        {
            try
            {
                using (var context = new POSDbContext())
                {
                    // Load only Categories and UnitsOfMeasure - DO NOT load products
                    var categoriesTask = context.Categories.AsNoTracking().OrderBy(c => c.Name).ToListAsync();
                    var unitsTask = context.UnitsOfMeasure.AsNoTracking().OrderBy(u => u.Name).ToListAsync();

                    await Task.WhenAll(categoriesTask, unitsTask);

                    // Update only Categories and UnitsOfMeasure
                    ViewModel.Categories = new POSSystem.Collections.OptimizedObservableCollection<Category>(categoriesTask.Result);
                    ViewModel.UnitsOfMeasure = new POSSystem.Collections.OptimizedObservableCollection<UnitOfMeasure>(unitsTask.Result);

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Loaded {categoriesTask.Result.Count} categories and {unitsTask.Result.Count} units of measure");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Error loading essential data: {ex.Message}");
            }
        }

        private void chkTrackBatches_CheckedChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                bool isTracking = chkTrackBatches.IsChecked == true;
                
                if (isTracking)
                {
                    if (_isEditMode)
                    {
                        // For existing products with batch tracking
                        txtCurrentStock.IsReadOnly = true;
                        txtCurrentStock.ToolTip = "Total Stock (Manage via Batches)";
                        btnManageStock.Visibility = Visibility.Visible;
                        
                        // Show batch info section
                        if (_existingProduct != null)
                        {
                            BatchInfoSection.Visibility = Visibility.Visible;
                        }
                    }
                    else
                    {
                        // For new products with batch tracking, allow setting initial stock
                        txtCurrentStock.IsReadOnly = false;
                        txtCurrentStock.ToolTip = "Initial Stock Quantity";
                        btnManageStock.Visibility = Visibility.Collapsed;
                        BatchInfoSection.Visibility = Visibility.Collapsed;
                    }
                }
                else
                {
                    // When batch tracking is disabled, always allow stock editing
                    txtCurrentStock.IsReadOnly = false;
                    txtCurrentStock.ToolTip = "Current Stock";
                    btnManageStock.Visibility = Visibility.Collapsed;
                    BatchInfoSection.Visibility = Visibility.Collapsed;
                    
                    if (_isEditMode && _existingProduct != null)
                    {
                        // If switching from batch to non-batch in edit mode,
                        // show the total of all batches as the current stock
                        var totalBatchStock = _existingProduct.GetTotalStockDecimal();
                        txtCurrentStock.Text = totalBatchStock % 1 == 0 ? totalBatchStock.ToString("F0") : totalBatchStock.ToString("0.###");
                    }
                }

                // Ensure the stock quantity field is enabled when it should be editable
                txtCurrentStock.IsEnabled = !txtCurrentStock.IsReadOnly;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error in chkTrackBatches_CheckedChanged: {ex.Message}");
            }
        }

        private async void ProductDialog_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Dialog loaded - ensuring ComboBox data without reloading products");

            // Only ensure Categories and UnitsOfMeasure are loaded, NOT products
            // This prevents duplication in the main view's product list
            if (ViewModel.Categories == null || ViewModel.Categories.Count == 0 ||
                ViewModel.UnitsOfMeasure == null || ViewModel.UnitsOfMeasure.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Loading Categories and UnitsOfMeasure only");
                await LoadDialogEssentialDataOnly();

                // If we're in edit mode, reload the product data after categories are loaded
                if (_isEditMode && _existingProduct != null)
                {
                    System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] Reloading product data after categories loaded");
                    LoadProductData(_existingProduct);
                }
            }

            // Force UI update to ensure ComboBoxes are populated
            cmbCategory.ItemsSource = ViewModel.Categories;
            cmbUnitOfMeasure.ItemsSource = ViewModel.UnitsOfMeasure;

            // Set default unit of measure after data is loaded
            if (!_isEditMode && ViewModel.UnitsOfMeasure != null && ViewModel.UnitsOfMeasure.Any())
            {
                var pieceUnit = ViewModel.UnitsOfMeasure.FirstOrDefault(u =>
                    u.Name.Equals("piece", StringComparison.OrdinalIgnoreCase) ||
                    u.Name.Equals("Piece", StringComparison.OrdinalIgnoreCase));

                if (pieceUnit != null)
                {
                    ViewModel.SelectedUnitOfMeasureId = pieceUnit.Id;
                    cmbUnitOfMeasure.SelectedValue = pieceUnit.Id;
                }
                else if (ViewModel.UnitsOfMeasure.Count > 0)
                {
                    ViewModel.SelectedUnitOfMeasureId = ViewModel.UnitsOfMeasure.First().Id;
                    cmbUnitOfMeasure.SelectedValue = ViewModel.UnitsOfMeasure.First().Id;
                }
            }

            // Set default category if not in edit mode
            if (!_isEditMode && ViewModel.Categories != null && ViewModel.Categories.Any())
            {
                var firstCategory = ViewModel.Categories.First();
                ViewModel.SelectedCategoryId = firstCategory.Id;
                cmbCategory.SelectedValue = firstCategory.Id;
            }

            // Initialize quantity for new products
            if (!_isEditMode)
            {
                ViewModel.ProductQuantity = 1.0m;
                txtQuantity.Text = "1.000";
                txtQuantity.IsEnabled = true; // Ensure quantity field is enabled by default
                txtQuantity.Opacity = 1.0; // Ensure full opacity

                // Initialize weight-based toggle to unit-based by default
                ViewModel.IsWeightBased = false;
            }

            // Initialize the UI for the current sales method (both edit and new modes)
            bool isWeightBased = ViewModel?.IsWeightBased ?? false;
            UpdateQuantityInputForSalesMethod(isWeightBased);

            // Manually set radio button states
            if (rbUnitBased != null && rbWeightBased != null)
            {
                rbUnitBased.IsChecked = !isWeightBased;
                rbWeightBased.IsChecked = isWeightBased;
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Radio buttons set - Unit: {!isWeightBased}, Weight: {isWeightBased}");
            }

            System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Dialog loaded - IsWeightBased: {isWeightBased}");
        }

        // ===== BULK PRICING EVENT HANDLERS =====

        private async void AddPriceTier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] AddPriceTier_Click called");
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Current PriceTiers count: {PriceTiers.Count}");
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTiersList.ItemsSource: {PriceTiersList.ItemsSource?.GetType().Name ?? "NULL"}");

                var product = _existingProduct ?? new Product { Id = 0, SellingPrice = decimal.TryParse(txtSellingPrice.Text, out var price) ? price : 0 };
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Product for dialog: {product.Name ?? "New Product"}, ID: {product.Id}");
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Calling PriceTierDialog.ShowAsync with dialogIdentifier: {_dialogIdentifier}");

                var newTier = await PriceTierDialog.ShowAsync(product, null, _dialogIdentifier);

                System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTierDialog.ShowAsync returned: {(newTier != null ? "Valid result" : "NULL")}");

                if (newTier != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Received tier data: TierName='{newTier.TierName}', MinQty={newTier.MinimumQuantity}, UnitPrice={newTier.UnitPrice}, PackPrice={newTier.PackPrice}");
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Before adding - PriceTiers count: {PriceTiers.Count}");

                    PriceTiers.Add(newTier);

                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] After adding - PriceTiers count: {PriceTiers.Count}");
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] PriceTiersList.ItemsSource count: {(PriceTiersList.ItemsSource as System.Collections.ICollection)?.Count ?? -1}");

                    chkEnableBulkPricing.IsChecked = true;
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] Bulk pricing checkbox enabled");

                    // Force UI update
                    PriceTiersList.Items.Refresh();
                    System.Diagnostics.Debug.WriteLine($"[PRODUCT_DIALOG] UI refresh called");

                    System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Successfully added pricing tier: {newTier.DisplayName}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[PRODUCT_DIALOG] PriceTierDialog returned null - no tier added");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Error adding pricing tier: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error adding pricing tier: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EditPriceTier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var tier = button?.CommandParameter as PriceTierDto;

                if (tier != null)
                {
                    var product = _existingProduct ?? new Product { Id = 0, SellingPrice = decimal.TryParse(txtSellingPrice.Text, out var price) ? price : 0 };
                    var editedTier = await PriceTierDialog.ShowAsync(product, tier, _dialogIdentifier);

                    if (editedTier != null)
                    {
                        var index = PriceTiers.IndexOf(tier);
                        if (index >= 0)
                        {
                            PriceTiers[index] = editedTier;
                            System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Edited pricing tier: {editedTier.DisplayName}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Error editing pricing tier: {ex.Message}");
                MessageBox.Show($"Error editing pricing tier: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeletePriceTier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var tier = button?.CommandParameter as PriceTierDto;

                if (tier != null)
                {
                    var result = MessageBox.Show(
                        $"Are you sure you want to delete the pricing tier '{tier.DisplayName}'?",
                        "Confirm Delete",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        PriceTiers.Remove(tier);

                        // Update bulk pricing checkbox
                        chkEnableBulkPricing.IsChecked = PriceTiers.Any();

                        System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Deleted pricing tier: {tier.DisplayName}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Error deleting pricing tier: {ex.Message}");
                MessageBox.Show($"Error deleting pricing tier: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EnableBulkPricing_CheckedChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                var isEnabled = chkEnableBulkPricing.IsChecked ?? false;

                if (!isEnabled && PriceTiers.Any())
                {
                    var result = MessageBox.Show(
                        "Disabling bulk pricing will remove all pricing tiers. Are you sure?",
                        "Confirm Disable",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        PriceTiers.Clear();
                    }
                    else
                    {
                        chkEnableBulkPricing.IsChecked = true;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Bulk pricing enabled: {isEnabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BULK_PRICING] Error in EnableBulkPricing_CheckedChanged: {ex.Message}");
            }
        }

        // Simple implementation of IAlertService that just shows message boxes
        private class SimpleAlertService : IAlertService
        {
            public void CheckExpiringProducts() { }

            public void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product") { }

            public List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1) => new List<ProductAlert>();

            public List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1) => new List<ProductAlert>();

            public int GetTotalAlertsCount() => 0;

            public List<ProductAlert> GetUnreadAlerts() => new List<ProductAlert>();

            public List<ProductAlert> GetUnreadAlertsBasic() => new List<ProductAlert>();

            public int GetUnreadAlertsCount() => 0;

            public void MarkAlertAsRead(int alertId) { }

            public void MarkAllAlertsAsRead() { }

            public void ClearAlertCache() { }
            
            public void ShowError(string message)
            {
                MessageBox.Show(message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 