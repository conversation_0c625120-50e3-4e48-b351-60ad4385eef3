using System;
using System.Windows;
using System.Windows.Input;
using System.Security.Cryptography;
using System.Management;
using System.Text;
using System.Linq;
using System.Diagnostics;
using System.Text.Json;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.IO;
using System.Threading;
using POSLicensingSystem.Utilities;

namespace POSLicensingSystem.ViewModels
{
    public class LicenseActivationViewModel : ViewModelBase
    {
        private string _businessName = string.Empty;
        private string _licenseKey = string.Empty;
        private string _errorMessage = string.Empty;
        private bool _hasError;
        private string _systemId = string.Empty;
        private bool _isActivating = false;
        private string _successMessage = string.Empty;
        private bool _showSuccessMessage = false;

        public string BusinessName
        {
            get => _businessName;
            set => SetProperty(ref _businessName, value);
        }

        public string LicenseKey
        {
            get => _licenseKey;
            set => SetProperty(ref _licenseKey, value);
        }

        public string SystemId
        {
            get => _systemId;
            set => SetProperty(ref _systemId, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                HasError = !string.IsNullOrEmpty(value);
                ShowSuccessMessage = string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(_successMessage);
            }
        }

        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        public bool IsActivating
        {
            get => _isActivating;
            set => SetProperty(ref _isActivating, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        public bool ShowSuccessMessage
        {
            get => _showSuccessMessage;
            set => SetProperty(ref _showSuccessMessage, value);
        }

        // Commands
        public ICommand ActivateCommand { get; }
        public ICommand ExitCommand { get; }
        public ICommand GenerateSystemIdCommand { get; }
        public ICommand CopySystemIdCommand { get; }

        public LicenseActivationViewModel()
        {
            // Initialize commands
            ActivateCommand = new RelayCommand(ExecuteActivate, CanExecuteActivate);
            ExitCommand = new RelayCommand(_ => Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive)?.Close());
            GenerateSystemIdCommand = new RelayCommand(_ => ExecuteGenerateSystemId());
            CopySystemIdCommand = new RelayCommand(_ => ExecuteCopySystemId(), _ => !string.IsNullOrEmpty(SystemId));

            // Get the hardware ID
            _systemId = HardwareInfo.GetSystemId();
        }

        private bool CanExecuteActivate(object? parameter)
        {
            return !string.IsNullOrWhiteSpace(BusinessName) && 
                   !string.IsNullOrWhiteSpace(LicenseKey) &&
                   !string.IsNullOrWhiteSpace(SystemId) &&
                   !IsActivating;
        }

        private async void ExecuteActivate(object? parameter)
        {
            try
            {
                // Set flag to indicate activation is in progress
                IsActivating = true;
                ErrorMessage = string.Empty;
                SuccessMessage = string.Empty;

                Debug.WriteLine($"Starting license activation for business: '{BusinessName}' with system ID: {SystemId}");
                Debug.WriteLine($"License key: {LicenseKey}");

                // Add a small delay to show processing feedback
                await System.Threading.Tasks.Task.Delay(1000);

                // Implement license activation logic
                if (ValidateLicense())
                {
                    Debug.WriteLine("License validation successful!");
                    
                    // Successful activation
                    MessageBox.Show(
                        "License activated successfully!\nThe application will now restart to apply the changes.",
                        "Activation Successful",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );
                    
                    // Close the activation window
                    Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive)?.Close();
                    
                    // In a real implementation, we would save the license data here
                    // SaveLicenseData();
                    
                    // Restart the application
                    // System.Diagnostics.Process.Start(Application.ResourceAssembly.Location);
                    // Application.Current.Shutdown();

                    SuccessMessage = "License activated successfully!";
                    
                    // Close the window after a short delay
                    var timer = new System.Threading.Timer(_ =>
                    {
                        App.Current.Dispatcher.Invoke(() =>
                        {
                            var window = parameter as Window;
                            window?.Close();
                        });
                    }, null, 2000, Timeout.Infinite);
                }
                else
                {
                    // Error message is set by the ValidateLicense method
                    Debug.WriteLine($"License validation failed: {ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error activating license: {ex.Message}";
                Debug.WriteLine($"License activation error: {ex}");
            }
            finally
            {
                // Reset activation flag
                IsActivating = false;
            }
        }

        private bool ValidateLicense()
        {
            Debug.WriteLine($"Validating license key for business: '{BusinessName}' with system ID: {SystemId}");
            
            // Use the new LicenseValidator utility and capture the error message
            string error;
            bool isValid = LicenseValidator.ValidateLicense(LicenseKey, BusinessName, SystemId, out error);
            
            if (!isValid && !string.IsNullOrEmpty(error))
            {
                ErrorMessage = error;
                Debug.WriteLine($"Validation failed with error: {error}");
            }
            
            return isValid;
        }

        private void ExecuteGenerateSystemId()
        {
            try
            {
                SystemId = HardwareInfo.GetSystemId();
                ErrorMessage = string.Empty;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error generating System ID: {ex.Message}";
                SystemId = string.Empty;
                Debug.WriteLine($"System ID generation error: {ex}");
            }
        }

        private void ExecuteCopySystemId()
        {
            try
            {
                Clipboard.SetText(SystemId);
                MessageBox.Show("System ID copied to clipboard!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error copying to clipboard: {ex.Message}";
                Debug.WriteLine($"Clipboard error: {ex}");
            }
        }
    }

    // Simple relay command implementation
    public class RelayCommand : ICommand
    {
        private readonly Action<object?> _execute;
        private readonly Predicate<object?>? _canExecute;

        public RelayCommand(Action<object?> execute, Predicate<object?>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add => CommandManager.RequerySuggested += value;
            remove => CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object? parameter) => _canExecute == null || _canExecute(parameter);

        public void Execute(object? parameter) => _execute(parameter);
    }
} 