using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Data;
using System.Threading.Tasks;
using System.Diagnostics;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class CashDrawerService : ICashDrawerService
    {
        private readonly IDatabaseService _dbService;
        private readonly POSDbContext _context;
        public event EventHandler<CashDrawerEventArgs> CashDrawerUpdated;

        public CashDrawerService(IDatabaseService dbService)
        {
            _dbService = dbService;
            _context = ((DatabaseService)dbService).Context; // Cast to access Context property
        }

        // Legacy constructor for backward compatibility
        public CashDrawerService() : this(new DatabaseService())
        {
        }

        public CashDrawer GetCurrentDrawer()
        {
            return GetCurrentDrawerWithDetails();
        }

        /// <summary>
        /// Gets the current open cash drawer with minimal data (no user details or transactions)
        /// Use this for simple status checks and basic operations
        /// </summary>
        public CashDrawer GetCurrentDrawerBasic()
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[CashDrawerService] GetCurrentDrawerBasic() called at {DateTime.Now:HH:mm:ss.fff} - Using optimized query");
            #endif
            return _context.CashDrawers
                .Where(d => d.Status == "Open")
                .OrderByDescending(d => d.OpenedAt)
                .FirstOrDefault();
        }

        /// <summary>
        /// Gets the current open cash drawer with user details but no transactions
        /// Use this when you need user info but not transaction history
        /// </summary>
        public CashDrawer GetCurrentDrawerWithUsers()
        {
            return _context.CashDrawers
                .Include(d => d.OpenedBy)
                .Include(d => d.ClosedBy)
                .Where(d => d.Status == "Open")
                .OrderByDescending(d => d.OpenedAt)
                .FirstOrDefault();
        }

        /// <summary>
        /// Gets the current open cash drawer with all related data (users and transactions)
        /// Use this only when you need the complete data set
        /// </summary>
        public CashDrawer GetCurrentDrawerWithDetails()
        {
            Debug.WriteLine($"[CashDrawerService] GetCurrentDrawerWithDetails() called at {DateTime.Now:HH:mm:ss.fff} - This will execute the heavy query");
            return _context.CashDrawers
                .Include(d => d.OpenedBy)
                .Include(d => d.ClosedBy)
                .Include(d => d.Transactions)
                .ThenInclude(t => t.PerformedBy)
                .Where(d => d.Status == "Open")
                .OrderByDescending(d => d.OpenedAt)
                .FirstOrDefault();
        }

        public void OpenDrawer(CashDrawer drawer)
        {
            try
            {
                if (GetCurrentDrawerBasic() != null)
                    throw new InvalidOperationException("A cash drawer is already open");

                if (drawer.OpenedById == null || drawer.OpenedById <= 0)
                    throw new InvalidOperationException("OpenedById is required");

                var user = _context.Users.Find(drawer.OpenedById);
                if (user == null)
                    throw new InvalidOperationException("Invalid user");

                // Initialize the drawer with opening balance
                drawer.Status = "Open";
                drawer.OpenedAt = DateTime.Now;
                drawer.OpenedById = user.Id;  // Set only the foreign key
                drawer.ClosedById = null;     // Explicitly set to null
                drawer.ClosedBy = null;       // Explicitly set to null

                if (drawer.Transactions == null)
                {
                    drawer.Transactions = new ObservableCollection<CashTransaction>();
                }

                // Use a transaction to ensure consistency
                using (var transaction = _context.Database.BeginTransaction())
                {
                    try
                    {
                        // Add the drawer to the context
                        _context.CashDrawers.Add(drawer);
                        _context.SaveChanges();

                        // Calculate initial balances
                        var openingTransaction = new CashTransaction
                        {
                            Amount = drawer.OpeningBalance,
                            Type = "Opening",
                            Reason = "Opening Balance",
                            Notes = "Initial drawer opening balance",
                            Timestamp = DateTime.Now,
                            CashDrawerId = drawer.Id,
                            PerformedById = user.Id
                        };

                        _context.CashTransactions.Add(openingTransaction);
                        _context.SaveChanges();

                        // Commit the transaction
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        // Rollback on error
                        transaction.Rollback();
                        throw new InvalidOperationException($"Failed to open cash drawer: {ex.Message}", ex);
                    }
                }

                // Reload the drawer with transactions
                var updatedDrawer = _context.CashDrawers
                    .Include(d => d.OpenedBy)
                    .Include(d => d.Transactions)
                    .FirstOrDefault(d => d.Id == drawer.Id);

                if (updatedDrawer != null)
                {
                    OnCashDrawerUpdated(updatedDrawer);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening cash drawer: {ex.Message}");
                Debug.WriteLine($"Inner exception: {ex.InnerException?.Message}");
                throw new InvalidOperationException($"Failed to open cash drawer: {ex.InnerException?.Message ?? ex.Message}", ex);
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Made async to use async database methods
        /// </summary>
        public async Task CloseDrawerAsync(CashDrawer drawer)
        {
            if (drawer == null)
                throw new ArgumentNullException(nameof(drawer));

            try
            {
                var user = await _dbService.GetDefaultUserAsync();
                if (user == null)
                    throw new InvalidOperationException("Invalid user");

                // Find the drawer directly from the database to avoid tracking conflicts
                var drawerToUpdate = _context.CashDrawers.Find(drawer.Id);
                if (drawerToUpdate == null)
                    throw new InvalidOperationException($"Cash drawer with ID {drawer.Id} not found.");

                // Update only the specific properties needed for closing
                drawerToUpdate.Status = "Closed";
                drawerToUpdate.ClosedAt = DateTime.Now;
                drawerToUpdate.ClosedById = user.Id;
                drawerToUpdate.ActualBalance = drawer.ActualBalance;
                drawerToUpdate.Notes = drawer.Notes;

                _context.SaveChanges();

                // Update the passed-in drawer object's properties to reflect changes
                drawer.Status = drawerToUpdate.Status;
                drawer.ClosedAt = drawerToUpdate.ClosedAt;
                drawer.ClosedById = drawerToUpdate.ClosedById;

                OnCashDrawerUpdated(drawerToUpdate);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error closing cash drawer: {ex.Message}");
                throw new InvalidOperationException($"Failed to close cash drawer: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Made async to use async database methods
        /// </summary>
        public async Task AddTransactionAsync(CashTransaction transaction)
        {
            try
            {
                // First check if the transaction already has a cash drawer ID set
                if (transaction.CashDrawerId <= 0)
                {
                    // If no drawer ID is set, get the current drawer (basic info only)
                    var currentDrawer = GetCurrentDrawerBasic();
                    if (currentDrawer == null || currentDrawer.Status != "Open")
                    {
                        throw new InvalidOperationException("No open cash drawer found.");
                    }
                    transaction.CashDrawerId = currentDrawer.Id;
                }
                else
                {
                    // If drawer ID is already set, verify that it exists and is open
                    var drawer = _context.CashDrawers
                        .FirstOrDefault(d => d.Id == transaction.CashDrawerId);
                    
                    if (drawer == null)
                    {
                        throw new InvalidOperationException($"Cash drawer with ID {transaction.CashDrawerId} not found.");
                    }
                    
                    if (drawer.Status != "Open")
                    {
                        throw new InvalidOperationException($"Cash drawer with ID {transaction.CashDrawerId} is not open.");
                    }
                }

                // Set the performer if not already set
                if (transaction.PerformedBy == null && transaction.PerformedById == 0)
                {
                    var defaultUser = await _dbService.GetDefaultUserAsync();
                    var user = _context.Users.Find(defaultUser?.Id ?? 0);
                    if (user == null)
                    {
                        throw new InvalidOperationException("No valid user found to perform the transaction.");
                    }
                    transaction.PerformedById = user.Id;
                }

                // Ensure timestamp is set
                if (transaction.Timestamp == default)
                {
                    transaction.Timestamp = DateTime.Now;
                }

                // Add the transaction to the context
                _context.CashTransactions.Add(transaction);
                _context.SaveChanges();
                
                // Refresh the drawer data and notify listeners
                var updatedDrawer = _context.CashDrawers
                    .Include(d => d.OpenedBy)
                    .Include(d => d.ClosedBy)
                    .Include(d => d.Transactions)
                    .ThenInclude(t => t.PerformedBy)
                    .FirstOrDefault(d => d.Id == transaction.CashDrawerId);
                
                if (updatedDrawer != null)
                {
                    OnCashDrawerUpdated(updatedDrawer);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding cash transaction: {ex.Message}");
                throw new InvalidOperationException($"Failed to add cash transaction: {ex.Message}", ex);
            }
        }

        public List<CashTransaction> GetTransactions(CashDrawer drawer)
        {
            return _context.CashTransactions
                .Where(t => t.CashDrawerId == drawer.Id)
                .OrderByDescending(t => t.Timestamp)
                .ToList();
        }

        public decimal GetTotalCashSales(CashDrawer drawer)
        {
            return drawer?.Transactions
                .Where(t => t.Type == "Sale")
                .Sum(t => t.Amount) ?? 0;
        }

        public decimal GetTotalPayouts(CashDrawer drawer)
        {
            return drawer?.Transactions
                .Where(t => t.Type == "Payout" || t.Type == "Out")
                .Sum(t => Math.Abs(t.Amount)) ?? 0;
        }

        public List<CashDrawer> GetAllCashDrawers(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.CashDrawers
                .Include(d => d.OpenedBy)
                .Include(d => d.ClosedBy)
                .Include(d => d.Transactions)
                    .ThenInclude(t => t.PerformedBy)
                .OrderByDescending(d => d.OpenedAt)
                .AsQueryable();

            if (startDate.HasValue)
                query = query.Where(d => d.OpenedAt >= startDate.Value);
            
            if (endDate.HasValue)
                query = query.Where(d => d.OpenedAt <= endDate.Value);

            return query.ToList();
        }

        public decimal GetDrawerBalance(CashDrawer drawer)
        {
            if (drawer == null) return 0;

            var balance = drawer.OpeningBalance;
            foreach (var transaction in drawer.Transactions)
            {
                if (transaction.Type == "Sale" || transaction.Type == "In")
                    balance += transaction.Amount;
                else if (transaction.Type == "Payout" || transaction.Type == "Out")
                    balance -= transaction.Amount;
            }
            return balance;
        }

        public async Task<List<BusinessExpense>> GetBusinessExpenses()
        {
            return await _context.BusinessExpenses
                .Include(e => e.User)
                .OrderByDescending(e => e.Date)
                .ToListAsync();
        }

        public async Task<BusinessExpense> AddBusinessExpense(BusinessExpense expense)
        {
            _context.BusinessExpenses.Add(expense);
            await _context.SaveChangesAsync();
            return expense;
        }

        public async Task<BusinessExpense> UpdateBusinessExpense(BusinessExpense expense)
        {
            var existingExpense = await _context.BusinessExpenses.FindAsync(expense.Id);
            if (existingExpense == null)
            {
                throw new InvalidOperationException($"Business expense with ID {expense.Id} not found.");
            }

            // Update the existing entity's properties
            _context.Entry(existingExpense).CurrentValues.SetValues(expense);
            
            await _context.SaveChangesAsync();
            return existingExpense;
        }

        public async Task DeleteBusinessExpense(int id)
        {
            var expense = await _context.BusinessExpenses.FindAsync(id);
            if (expense != null)
            {
                _context.BusinessExpenses.Remove(expense);
                await _context.SaveChangesAsync();
            }
        }

        protected virtual void OnCashDrawerUpdated(CashDrawer drawer)
        {
            CashDrawerUpdated?.Invoke(this, new CashDrawerEventArgs(drawer));
        }

        // Interface implementation methods
        public CashDrawer GetActiveCashDrawer()
        {
            return GetCurrentDrawerBasic();
        }

        public void OpenCashDrawer(decimal openingBalance, int userId)
        {
            var drawer = new CashDrawer
            {
                OpeningBalance = openingBalance,
                OpenedBy = _context.Users.Find(userId),
                OpenedAt = DateTime.Now,
                Status = "Open"
            };
            OpenDrawer(drawer);
        }

        public void CloseCashDrawer(int cashDrawerId, decimal closingBalance, int userId)
        {
            var drawer = _context.CashDrawers.Find(cashDrawerId);
            if (drawer != null)
            {
                drawer.ActualBalance = closingBalance; // Use ActualBalance instead of ClosingBalance
                drawer.ClosedBy = _context.Users.Find(userId);
                drawer.ClosedAt = DateTime.Now;
                CloseDrawer(drawer); // This will call the legacy sync method
            }
        }

        public List<CashDrawer> GetCashDrawerHistory()
        {
            return GetAllCashDrawers();
        }

        /// <summary>
        /// ✅ CRITICAL FIX: Legacy synchronous method - use CloseDrawerAsync() for better performance
        /// This method no longer blocks the UI thread
        /// </summary>
        public void CloseDrawer(CashDrawer drawer)
        {
            _ = CloseDrawerAsync(drawer);
        }

        /// <summary>
        /// Legacy synchronous method - use AddTransactionAsync() for better performance
        /// </summary>
        public void AddTransaction(CashTransaction transaction)
        {
            AddTransactionAsync(transaction).Wait();
        }

        public void AddCashTransaction(CashTransaction transaction)
        {
            AddTransaction(transaction);
        }

        public List<CashTransaction> GetCashTransactions(int cashDrawerId)
        {
            var drawer = _context.CashDrawers.Find(cashDrawerId);
            return drawer != null ? GetTransactions(drawer) : new List<CashTransaction>();
        }
    }

    public class CashDrawerEventArgs : EventArgs
    {
        public CashDrawer Drawer { get; }

        public CashDrawerEventArgs(CashDrawer drawer)
        {
            Drawer = drawer;
        }
    }
} 