-- Update existing products to create low stock scenarios for testing
-- This will help us verify that the dashboard low stock functionality works correctly

-- First, let's see what products exist
SELECT Id, Name, StockQuantity, MinimumStock, ReorderPoint, IsActive, Type 
FROM Products 
WHERE IsActive = 1;

-- Update products to create low stock scenarios
-- Make Potato Chips low stock (below minimum)
UPDATE Products 
SET StockQuantity = 8, MinimumStock = 10, ReorderPoint = 20 
WHERE Name = 'Potato Chips' AND IsActive = 1;

-- Make USB Cable low stock (below minimum)
UPDATE Products 
SET StockQuantity = 3, MinimumStock = 5, ReorderPoint = 10 
WHERE Name = 'USB Cable' AND IsActive = 1;

-- Keep Cola with normal stock
UPDATE Products 
SET StockQuantity = 100, MinimumStock = 20, ReorderPoint = 40 
WHERE Name = 'Cola' AND IsActive = 1;

-- Add some additional test products if they don't exist
INSERT OR IGNORE INTO Products (
    Name, SKU, Barcode, Description, 
    PurchasePrice, SellingPrice, StockQuantity, 
    MinimumStock, ReorderPoint, IsActive, 
    CreatedAt, CategoryId, Type
) VALUES 
(
    'Energy Drink', 'BEV002', '111222333', 'High caffeine energy drink',
    1.00, 2.50, 0, 15, 25, 1,
    datetime('now'), 1, 0
),
(
    'Chocolate Bar', 'SNK002', '444555666', 'Premium chocolate bar',
    1.25, 3.00, 2, 10, 15, 1,
    datetime('now'), 2, 0
);

-- Verify the changes
SELECT Id, Name, StockQuantity, MinimumStock, ReorderPoint, IsActive, Type,
       CASE 
           WHEN StockQuantity = 0 THEN 'OUT OF STOCK'
           WHEN StockQuantity <= MinimumStock OR StockQuantity <= ReorderPoint THEN 'LOW STOCK'
           ELSE 'NORMAL'
       END as StockStatus
FROM Products 
WHERE IsActive = 1
ORDER BY StockQuantity;

-- Show expected low stock count
SELECT 
    COUNT(*) as TotalProducts,
    SUM(CASE WHEN StockQuantity = 0 THEN 1 ELSE 0 END) as OutOfStockCount,
    SUM(CASE WHEN StockQuantity > 0 AND (StockQuantity <= MinimumStock OR StockQuantity <= ReorderPoint) THEN 1 ELSE 0 END) as LowStockCount
FROM Products 
WHERE IsActive = 1 AND Type != 1; -- Exclude services (Type = 1)
