using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models.Dashboard;

namespace POSSystem.Services.ChartOptimization
{
    /// <summary>
    /// ✅ PRIORITY 4 OPTIMIZATION: Chart performance optimizer for large datasets
    /// </summary>
    public static class ChartPerformanceOptimizer
    {
        // Performance thresholds
        private const int SMALL_DATASET_THRESHOLD = 20;
        private const int MEDIUM_DATASET_THRESHOLD = 50;
        private const int LARGE_DATASET_THRESHOLD = 100;
        private const int VERY_LARGE_DATASET_THRESHOLD = 200;

        /// <summary>
        /// ✅ OPTIMIZATION: Create performance-optimized column series based on dataset size
        /// </summary>
        public static ColumnSeries CreateOptimizedColumnSeries(string title, IEnumerable<double> values, Brush fill = null)
        {
            var valuesList = values.ToList();
            var dataSize = valuesList.Count;
            
            return new ColumnSeries
            {
                Title = title,
                Values = new ChartValues<double>(valuesList),
                Fill = fill ?? new SolidColorBrush(Colors.DodgerBlue),
                
                // ✅ PERFORMANCE: Disable data labels for large datasets
                DataLabels = dataSize <= SMALL_DATASET_THRESHOLD,
                
                // ✅ PERFORMANCE: Adjust column width based on dataset size
                MaxColumnWidth = GetOptimalColumnWidth(dataSize),
                
                // ✅ PERFORMANCE: Disable stroke for very large datasets
                Stroke = dataSize <= LARGE_DATASET_THRESHOLD ? Brushes.White : Brushes.Transparent,
                StrokeThickness = dataSize <= MEDIUM_DATASET_THRESHOLD ? 1 : 0
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create performance-optimized line series based on dataset size
        /// </summary>
        public static LineSeries CreateOptimizedLineSeries(string title, IEnumerable<decimal> values, Brush stroke = null)
        {
            var valuesList = values.ToList();
            var dataSize = valuesList.Count;
            
            return new LineSeries
            {
                Title = title,
                Values = new ChartValues<decimal>(valuesList),
                Stroke = stroke ?? new SolidColorBrush(Colors.DodgerBlue),
                
                // ✅ PERFORMANCE: Remove points for large datasets
                PointGeometry = dataSize <= SMALL_DATASET_THRESHOLD ? DefaultGeometries.Circle : null,
                PointGeometrySize = GetOptimalPointSize(dataSize),
                
                // ✅ PERFORMANCE: Disable smoothing for large datasets
                LineSmoothness = GetOptimalSmoothness(dataSize),
                
                // ✅ PERFORMANCE: Disable data labels for medium+ datasets
                DataLabels = dataSize <= 10,
                
                // ✅ PERFORMANCE: Optimize stroke thickness
                StrokeThickness = GetOptimalStrokeThickness(dataSize),
                
                // ✅ PERFORMANCE: Transparent fill for better performance
                Fill = dataSize <= MEDIUM_DATASET_THRESHOLD 
                    ? new SolidColorBrush(((SolidColorBrush)stroke ?? new SolidColorBrush(Colors.DodgerBlue)).Color) { Opacity = 0.1 }
                    : Brushes.Transparent
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create performance-optimized pie series with smart data labels
        /// </summary>
        public static PieSeries CreateOptimizedPieSeries(string title, decimal value, Brush fill = null, int totalSeriesCount = 1)
        {
            return new PieSeries
            {
                Title = title,
                Values = new ChartValues<decimal> { value },
                Fill = fill ?? GetRandomBrush(),
                
                // ✅ PERFORMANCE: Disable data labels for large pie charts
                DataLabels = totalSeriesCount <= 15,
                
                // ✅ PERFORMANCE: Optimize label point for large datasets
                LabelPoint = totalSeriesCount <= 15 
                    ? (point => $"{title}: {point.Y:N0} ({point.Participation:P1})")
                    : (point => point.Participation > 0.05 ? $"{point.Participation:P0}" : ""),
                
                // ✅ PERFORMANCE: Reduce stroke for large pie charts
                Stroke = totalSeriesCount <= 10 ? Brushes.White : Brushes.Transparent,
                StrokeThickness = totalSeriesCount <= 10 ? 2 : 0
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Optimize chart series collection for performance
        /// </summary>
        public static SeriesCollection OptimizeSeriesCollection(SeriesCollection originalSeries)
        {
            if (originalSeries == null || originalSeries.Count == 0)
                return new SeriesCollection();

            var optimizedSeries = new SeriesCollection();
            
            foreach (var series in originalSeries)
            {
                switch (series)
                {
                    case ColumnSeries columnSeries:
                        optimizedSeries.Add(OptimizeColumnSeries(columnSeries));
                        break;
                    case LineSeries lineSeries:
                        optimizedSeries.Add(OptimizeLineSeries(lineSeries));
                        break;
                    case PieSeries pieSeries:
                        optimizedSeries.Add(OptimizePieSeries(pieSeries, originalSeries.Count));
                        break;
                    default:
                        optimizedSeries.Add(series);
                        break;
                }
            }
            
            return optimizedSeries;
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Data sampling for very large datasets
        /// </summary>
        public static List<T> SampleDataForChart<T>(List<T> data, int maxPoints = VERY_LARGE_DATASET_THRESHOLD)
        {
            if (data == null || data.Count <= maxPoints)
                return data;

            // Use systematic sampling to maintain data distribution
            var step = (double)data.Count / maxPoints;
            var sampledData = new List<T>();
            
            for (int i = 0; i < maxPoints; i++)
            {
                var index = (int)(i * step);
                if (index < data.Count)
                {
                    sampledData.Add(data[index]);
                }
            }
            
            return sampledData;
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Get chart performance recommendations
        /// </summary>
        public static ChartPerformanceRecommendation GetPerformanceRecommendation(int dataPointCount)
        {
            return new ChartPerformanceRecommendation
            {
                DataPointCount = dataPointCount,
                PerformanceLevel = GetPerformanceLevel(dataPointCount),
                ShouldUseDataLabels = dataPointCount <= SMALL_DATASET_THRESHOLD,
                ShouldUsePoints = dataPointCount <= SMALL_DATASET_THRESHOLD,
                ShouldUseSmoothing = dataPointCount <= MEDIUM_DATASET_THRESHOLD,
                ShouldSampleData = dataPointCount > VERY_LARGE_DATASET_THRESHOLD,
                RecommendedMaxPoints = VERY_LARGE_DATASET_THRESHOLD,
                OptimalColumnWidth = GetOptimalColumnWidth(dataPointCount),
                OptimalPointSize = GetOptimalPointSize(dataPointCount)
            };
        }

        #region Private Helper Methods

        private static int GetOptimalColumnWidth(int dataSize)
        {
            return dataSize switch
            {
                <= SMALL_DATASET_THRESHOLD => 50,
                <= MEDIUM_DATASET_THRESHOLD => 35,
                <= LARGE_DATASET_THRESHOLD => 25,
                <= VERY_LARGE_DATASET_THRESHOLD => 15,
                _ => 10
            };
        }

        private static int GetOptimalPointSize(int dataSize)
        {
            return dataSize switch
            {
                <= SMALL_DATASET_THRESHOLD => 8,
                <= MEDIUM_DATASET_THRESHOLD => 6,
                <= LARGE_DATASET_THRESHOLD => 4,
                _ => 0
            };
        }

        private static double GetOptimalSmoothness(int dataSize)
        {
            return dataSize switch
            {
                <= SMALL_DATASET_THRESHOLD => 0.7,
                <= MEDIUM_DATASET_THRESHOLD => 0.5,
                <= LARGE_DATASET_THRESHOLD => 0.3,
                _ => 0
            };
        }

        private static int GetOptimalStrokeThickness(int dataSize)
        {
            return dataSize switch
            {
                <= MEDIUM_DATASET_THRESHOLD => 3,
                <= LARGE_DATASET_THRESHOLD => 2,
                _ => 1
            };
        }

        private static ChartPerformanceLevel GetPerformanceLevel(int dataPointCount)
        {
            return dataPointCount switch
            {
                <= SMALL_DATASET_THRESHOLD => ChartPerformanceLevel.Optimal,
                <= MEDIUM_DATASET_THRESHOLD => ChartPerformanceLevel.Good,
                <= LARGE_DATASET_THRESHOLD => ChartPerformanceLevel.Moderate,
                <= VERY_LARGE_DATASET_THRESHOLD => ChartPerformanceLevel.Poor,
                _ => ChartPerformanceLevel.Critical
            };
        }

        private static ColumnSeries OptimizeColumnSeries(ColumnSeries original)
        {
            var dataSize = original.Values?.Count ?? 0;
            
            original.DataLabels = dataSize <= SMALL_DATASET_THRESHOLD;
            original.MaxColumnWidth = GetOptimalColumnWidth(dataSize);
            original.Stroke = dataSize <= LARGE_DATASET_THRESHOLD ? original.Stroke : Brushes.Transparent;
            original.StrokeThickness = dataSize <= MEDIUM_DATASET_THRESHOLD ? original.StrokeThickness : 0;
            
            return original;
        }

        private static LineSeries OptimizeLineSeries(LineSeries original)
        {
            var dataSize = original.Values?.Count ?? 0;
            
            original.PointGeometry = dataSize <= SMALL_DATASET_THRESHOLD ? original.PointGeometry : null;
            original.PointGeometrySize = GetOptimalPointSize(dataSize);
            original.LineSmoothness = GetOptimalSmoothness(dataSize);
            original.DataLabels = dataSize <= 10;
            original.StrokeThickness = GetOptimalStrokeThickness(dataSize);
            
            if (dataSize > MEDIUM_DATASET_THRESHOLD)
            {
                original.Fill = Brushes.Transparent;
            }
            
            return original;
        }

        private static PieSeries OptimizePieSeries(PieSeries original, int totalSeriesCount)
        {
            original.DataLabels = totalSeriesCount <= 15;
            original.Stroke = totalSeriesCount <= 10 ? original.Stroke : Brushes.Transparent;
            original.StrokeThickness = totalSeriesCount <= 10 ? original.StrokeThickness : 0;
            
            return original;
        }

        private static Brush GetRandomBrush()
        {
            var colors = new[] { Colors.DodgerBlue, Colors.LimeGreen, Colors.Orange, Colors.Red, Colors.Purple, Colors.Teal };
            var random = new Random();
            return new SolidColorBrush(colors[random.Next(colors.Length)]);
        }

        #endregion
    }

    /// <summary>
    /// Chart performance recommendation data
    /// </summary>
    public class ChartPerformanceRecommendation
    {
        public int DataPointCount { get; set; }
        public ChartPerformanceLevel PerformanceLevel { get; set; }
        public bool ShouldUseDataLabels { get; set; }
        public bool ShouldUsePoints { get; set; }
        public bool ShouldUseSmoothing { get; set; }
        public bool ShouldSampleData { get; set; }
        public int RecommendedMaxPoints { get; set; }
        public int OptimalColumnWidth { get; set; }
        public int OptimalPointSize { get; set; }
    }

    /// <summary>
    /// Chart performance levels
    /// </summary>
    public enum ChartPerformanceLevel
    {
        Optimal,    // <= 20 points
        Good,       // <= 50 points
        Moderate,   // <= 100 points
        Poor,       // <= 200 points
        Critical    // > 200 points
    }
}
