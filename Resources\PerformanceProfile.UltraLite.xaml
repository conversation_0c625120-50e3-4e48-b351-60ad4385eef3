<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes">
    <!-- Ultra Lite: maximize FPS by minimizing visuals -->

    <!-- Disable animations by overriding storyboards to no-ops -->
    <Storyboard x:Key="ProductCardHoverEnterAnimation"/>
    <Storyboard x:Key="ProductCardHoverExitAnimation"/>

    <!-- Global low-cost rendering -->
    <Style TargetType="Image">
        <Setter Property="RenderOptions.BitmapScalingMode" Value="LowQuality"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>
    <Style TargetType="TextBlock">
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- Product Card style: no effects, no scaling -->
    <Style x:Key="ModernProductCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FFFFFF"/>
                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Material buttons without elevation/shadows -->
    <Style x:Key="AppPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="4"/>
    </Style>

    <!-- Remove shadows from cards -->
    <Style TargetType="md:Card">
        <Setter Property="md:ElevationAssist.Elevation" Value="Dp0"/>
    </Style>

</ResourceDictionary>

