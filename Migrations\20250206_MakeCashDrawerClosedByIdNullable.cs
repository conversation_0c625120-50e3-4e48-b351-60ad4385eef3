using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class MakeCashDrawerClosedByIdNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop existing foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers");

            // Alter column to be nullable
            migrationBuilder.AlterColumn<int>(
                name: "ClosedById",
                table: "CashDrawers",
                type: "INTEGER",
                nullable: true);

            // Re-create foreign key with nullable reference
            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers",
                column: "ClosedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers");

            // Alter column back to non-nullable
            migrationBuilder.AlterColumn<int>(
                name: "ClosedById",
                table: "CashDrawers",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            // Re-create foreign key
            migrationBuilder.AddForeignKey(
                name: "FK_CashDrawers_Users_ClosedById",
                table: "CashDrawers",
                column: "ClosedById",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
} 