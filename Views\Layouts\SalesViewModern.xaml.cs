using System.Windows.Controls;
using System.Windows;
using System.Windows.Input;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using System.Windows.Media;
using POSSystem.ViewModels;
using POSSystem.Models;
using System.Linq;

namespace POSSystem.Views.Layouts
{
    public partial class SalesViewModern : UserControl
    {
        private readonly ISettingsService _settingsService;

        public SalesViewModern()
        {
            InitializeComponent();
            _settingsService = new SettingsService();

            // Initialize data context if not set
            if (DataContext == null)
            {
                DataContext = new SaleViewModel();
            }

            this.Loaded += SalesViewModern_Loaded;

            // Subscribe to DataContext changes for event wiring
            this.DataContextChanged += OnDataContextChanged;
            this.Unloaded += OnUnloaded;

        }

        private SaleViewModel ViewModel => (SaleViewModel)DataContext;

        private void SalesViewModern_Loaded(object sender, RoutedEventArgs e)
        {
            // Initialize theme selection button
            CreateThemeSelectionButton();

            // ✅ CRITICAL FIX: Initialize products in background to prevent UI blocking
            if (ViewModel != null && ViewModel.FilteredProducts.Count == 0)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ViewModel.RefreshProducts();
                        System.Diagnostics.Debug.WriteLine("[SALESVIEW-MODERN] Background product initialization completed");
                    }
                    catch (Exception ex)
                    {
                            System.Diagnostics.Debug.WriteLine($"[SALESVIEW-MODERN] Error initializing products: {ex.Message}");
                    }
                });
            }
        }

        private void CreateThemeSelectionButton()
        {
            // Create a button to open theme selection
            var themeButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignIconButton"),
                ToolTip = Application.Current.FindResource("ChangeLayout") as string,
                Margin = new Thickness(8),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Width = 36,
                Height = 36
            };

            themeButton.Content = new PackIcon
            {
                Kind = PackIconKind.ViewDashboard,
                Width = 24,
                Height = 24
            };

            themeButton.Click += ThemeButton_Click;

            // Add to the main grid - using safe casting
            if (this.Content is Grid mainGrid)
            {
                Grid.SetRow(themeButton, 0);
                mainGrid.Children.Add(themeButton);
            }
        }

        private async void ThemeButton_Click(object sender, RoutedEventArgs e)
        {
            // For DialogHost compatibility, inform user about theme selection
            MessageBox.Show(
                "To avoid DialogHost conflicts, theme selection is now controlled from the parent container." +
                "\n\nPlease use the theme button in the top-right corner of the screen.",
                "Layout Information",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is SaleViewModel)
            {
                SaleViewModel.SaleCompleted -= OnSaleCompleted;
                SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            }
            if (e.NewValue is SaleViewModel)
            {
                SaleViewModel.SaleCompleted += OnSaleCompleted;
                SaleViewModel.ProductStockChanged += OnProductStockChanged;
            }
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            SaleViewModel.SaleCompleted -= OnSaleCompleted;
            SaleViewModel.ProductStockChanged -= OnProductStockChanged;
            this.DataContextChanged -= OnDataContextChanged;
            this.Unloaded -= OnUnloaded;
        }

        private async void OnSaleCompleted(object sender, System.EventArgs e)
        {
            try
            {
                await ViewModel.RefreshProducts();
            }
            catch { }
        }

        private void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            var product = ViewModel?.FilteredProducts?.FirstOrDefault(p => p.Id == e.ProductId);
            if (product != null)
            {
                product.StockQuantity = e.NewStockQuantity;
            }
        }




        private async Task ShowThemeSelectionDialog()
        {
            var view = new StackPanel { Margin = new Thickness(16) };

            // Create title
            var title = new TextBlock
            {
                Text = Application.Current.FindResource("SelectLayout") as string,
                Style = (Style)FindResource("MaterialDesignHeadline6TextBlock"),
                Margin = new Thickness(0, 0, 0, 16)
            };
            view.Children.Add(title);

            // Current layout theme
            var currentTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

            // Create layout options
            var themes = new[]
            {
                new { Code = "Standard", Name = Application.Current.FindResource("StandardLayout") as string, Description = "Default sales layout with product cards and cart" },
                new { Code = "Compact", Name = Application.Current.FindResource("CompactLayout") as string, Description = "Compact layout with smaller product cards and simplified cart" },
                new { Code = "Modern", Name = Application.Current.FindResource("ModernLayout") as string, Description = "Modern layout with large product images and enhanced visual elements" },
                new { Code = "Grid", Name = Application.Current.FindResource("GridLayout") as string, Description = "Grid-based layout with tabular product listing and detailed information" }
            };

            foreach (var theme in themes)
            {
                var isSelected = theme.Code == currentTheme;

                var card = new Card
                {
                    Margin = new Thickness(0, 0, 0, 8),
                    Padding = new Thickness(16, 12, 16, 12),
                    Background = isSelected
                        ? (SolidColorBrush)FindResource("PrimaryHueLightBrush")
                        : (SolidColorBrush)FindResource("MaterialDesignCardBackground"),
                    UniformCornerRadius = 4
                };

                var panel = new StackPanel();

                var header = new TextBlock
                {
                    Text = theme.Name,
                    Style = (Style)FindResource("MaterialDesignSubtitle1TextBlock"),
                    FontWeight = isSelected ? FontWeights.Bold : FontWeights.Normal
                };

                var description = new TextBlock
                {
                    Text = theme.Description,
                    Style = (Style)FindResource("MaterialDesignBody2TextBlock"),
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.8,
                    Margin = new Thickness(0, 4, 0, 0)
                };

                panel.Children.Add(header);
                panel.Children.Add(description);

                card.Content = panel;

                // Make the card clickable
                card.Tag = theme.Code;
                var cardBorder = new Border();
                cardBorder.Child = card;
                cardBorder.MouseDown += ThemeCard_MouseDown;
                cardBorder.Cursor = Cursors.Hand;

                view.Children.Add(cardBorder);
            }

            // Add note
            var note = new TextBlock
            {
                Text = "The layout change will take effect after restarting the application.",
                Style = (Style)FindResource("MaterialDesignBody2TextBlock"),
                Foreground = (SolidColorBrush)FindResource("MaterialDesignBodyLight"),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 16, 0, 0)
            };
            view.Children.Add(note);

            await DialogHost.Show(view, "SalesDialog");
        }

        private void ThemeCard_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border &&
                border.Child is Card card &&
                card.Tag is string themeCode)
            {
                // Save the selected theme
                _settingsService.SaveSetting("SalesLayoutTheme", themeCode);

                // Ask user if they want to restart
                var result = MessageBox.Show(
                    (Application.Current.FindResource("LayoutChangeRestart") as string) + " " +
                    (Application.Current.FindResource("RestartNow") as string),
                    (Application.Current.FindResource("ThemeChanged") as string),
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Restart the application
                    System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    Application.Current.Shutdown();
                }

                // Close the dialog
                DialogHost.Close("SalesDialog");
            }
        }
    }
}