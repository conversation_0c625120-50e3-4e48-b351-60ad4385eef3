-- Basic Test Data Generator
-- This script inserts the absolute minimum data needed for testing

PRAGMA foreign_keys = OFF; -- Temporarily disable foreign key constraints

BEGIN TRANSACTION;

-- Create a sample sale
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                  TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  ('TEST-INV-001', datetime('now', '-1 day'), 1, 1, 100.00, 0.00, 0.00, 100.00, 100.00, 0.00, 'Cash', 'Paid', 'Completed', 2);

-- Add sale items
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES
  (last_insert_rowid(), 1, 2, 50.00, 100.00);  -- 2 items at $50 each

-- Add a cash drawer
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
VALUES
  (500.00, 600.00, 600.00, 600.00, 0.00, 'Closed', datetime('now', '-1 day', '09:00:00'), 
   datetime('now', '-1 day', '18:00:00'), 1, 1, 'Test drawer');

-- Add a discount
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
VALUES
  (1, 10, 50.00, 45.00, 1, 'Test discount', 
   (SELECT Id FROM Sales WHERE InvoiceNumber = 'TEST-INV-001'), 
   NULL, 1, datetime('now', '-1 day'), 1);

-- Add an inventory transaction
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
VALUES
  (1, 'Sale', -2, 50.00, 'TEST-INV-001', 'Test inventory transaction', 
   datetime('now', '-1 day'), 1);

PRAGMA foreign_keys = ON; -- Re-enable foreign key constraints

COMMIT; 