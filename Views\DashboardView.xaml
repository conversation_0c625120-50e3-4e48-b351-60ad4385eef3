<UserControl x:Class="POSSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:System="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d"
             d:DesignHeight="1802" d:DesignWidth="1080">

    <UserControl.Resources>
        <!-- Card Style -->
        <Style x:Key="DashboardCard" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="UniformCornerRadius" Value="6"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <TranslateTransform/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <!-- MouseOver trigger -->
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
                </Trigger>

                <!-- MouseEnter event for translation animation only -->
                <EventTrigger RoutedEvent="MouseEnter">
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                To="-2" Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>

                <!-- MouseLeave event for translation animation only -->
                <EventTrigger RoutedEvent="MouseLeave">
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                To="0" Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </Style.Triggers>
        </Style>

        <!-- Growth Text Style -->
        <Style x:Key="GrowthText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="0,4,0,0"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>

        <!-- Add InverseBooleanConverter -->
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>

        <!-- Add ObjectDataProvider for ChartType enum -->
        <ObjectDataProvider x:Key="ChartTypeValues"
                          MethodName="GetValues"
                          ObjectType="{x:Type System:Type}">
            <ObjectDataProvider.MethodParameters>
                <x:Type Type="vm:ChartType"/>
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>

        <!-- Header Style -->
        <Style x:Key="CardHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>

        <!-- Metric Value Style -->
        <Style x:Key="MetricValue" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>

        <!-- Trend Indicator Style -->
        <Style x:Key="TrendIndicator" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="8,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <converters:AlertTypeToColorConverter x:Key="AlertTypeToColorConverter"/>
        <converters:EqualityToBooleanConverter x:Key="EqualityToBooleanConverter"/>
        <converters:GrowthToColorConverter x:Key="GrowthToColorConverter"/>
        <converters:GrowthToIconConverter x:Key="GrowthToIconConverter"/>
        <converters:ChartTypeToDisplayConverter x:Key="ChartTypeToDisplayConverter"/>
        <converters:ChartTypeToIconConverter x:Key="ChartTypeToIconConverter"/>
        <converters:EnumToIndexConverter x:Key="EnumToIndexConverter"/>
        <converters:BooleanToTextConverter x:Key="BooleanToTextConverter"/>
        <converters:MetricValueConverter x:Key="MetricValueConverter"/>
        <converters:UserInitialsConverter x:Key="UserInitialsConverter"/>

        <!-- Skeleton Loading Animation -->
        <Storyboard x:Key="SkeletonAnimation">
            <DoubleAnimation
                Storyboard.TargetProperty="Opacity"
                From="0.5" To="1" Duration="0:0:1"
                RepeatBehavior="Forever" AutoReverse="True"/>
        </Storyboard>

        <!-- Data Update Animation -->
        <Storyboard x:Key="DataUpdateAnimation">
            <DoubleAnimation
                Storyboard.TargetProperty="Opacity"
                From="0" To="1" Duration="0:0:0.3"/>
            <DoubleAnimation
                Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                From="10" To="0" Duration="0:0:0.3"/>
        </Storyboard>

        <!-- Skeleton Loading Template -->
        <DataTemplate x:Key="SkeletonTemplate">
            <materialDesign:Card Background="{DynamicResource MaterialDesignSurfaceBackground}" 
                               UniformCornerRadius="4" 
                               Margin="0,4"
                               materialDesign:ElevationAssist.Elevation="Dp1">
                <materialDesign:Card.Triggers>
                    <EventTrigger RoutedEvent="Loaded">
                        <BeginStoryboard Storyboard="{StaticResource SkeletonAnimation}"/>
                    </EventTrigger>
                </materialDesign:Card.Triggers>
            </materialDesign:Card>
        </DataTemplate>

        <!-- Loading Overlay Style -->
        <Style x:Key="LoadingOverlay" TargetType="Grid">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="Opacity" Value="0.8"/>
            <Setter Property="Visibility" Value="Collapsed"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                    <Setter Property="Visibility" Value="Visible"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <materialDesign:DialogHost Identifier="RootDialog" DialogMargin="16" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="317*"/>
                <ColumnDefinition Width="223*"/>
            </Grid.ColumnDefinitions>
            <!-- Collapsible Sidebar -->

            <!-- Main Content -->
            <Viewbox Margin="0" Stretch="Uniform" StretchDirection="DownOnly" Grid.ColumnSpan="2" 
                     HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                <Grid Width="1600" MinWidth="1080" MaxHeight="900">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <!-- Period Selection -->
                            <RowDefinition Height="Auto"/>
                            <!-- Quick Stats -->
                            <RowDefinition Height="Auto"/>
                            <!-- Product and Category Performance -->
                            <RowDefinition Height="Auto"/>
                            <!-- Customer Insights -->
                            <RowDefinition Height="Auto"/>
                            <!-- User Performance -->
                        </Grid.RowDefinitions>

                        <!-- Period Selection -->
                        <materialDesign:Card x:Name="OverviewSection" 
                                          Style="{StaticResource DashboardCard}" 
                                          Grid.Row="0" 
                                          Margin="4,0,4,12">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <!-- Business Overview Title -->
                                <TextBlock Text="{DynamicResource BusinessOverview}" 
                                         Style="{StaticResource CardHeader}"
                                         FontSize="20"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         VerticalAlignment="Center"
                                         Grid.Column="0"
                                         x:Name="BusinessOverviewTitle"/>
                                         
                                <StackPanel Grid.Column="1" Orientation="Vertical" VerticalAlignment="Center">
                                    <!-- Refresh Dashboard Button -->
                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Command="{Binding RefreshDashboardCommand}"
                                            FontSize="10"
                                            Height="28"
                                            Width="120"
                                            Margin="0,0,0,8"
                                            HorizontalAlignment="Right"
                                            ToolTip="Refresh all dashboard data and clear cache"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            BorderBrush="{DynamicResource PrimaryHueMidBrush}">
                                        <Button.Content>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Refresh"
                                                                       Width="14" Height="14"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,4,0"/>
                                                <TextBlock Text="Refresh" FontSize="10"/>
                                            </StackPanel>
                                        </Button.Content>
                                    </Button>

                                    <TextBlock Text="{DynamicResource PeriodFilter}"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             HorizontalAlignment="Right"
                                             Margin="0,0,0,4"/>
                                    <ComboBox Width="150"
                                            Margin="8,0,0,0"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Background="{DynamicResource MaterialDesignPaper}"
                                            ItemsSource="{Binding QuickStatsPeriods}"
                                            SelectedItem="{Binding SelectedQuickStatsPeriod}"
                                            ToolTip="Select time period to filter all dashboard data">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding DisplayName, Converter={StaticResource ResourceKeyToStringConverter}}" />
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>
                                
                                <TextBlock Text="📊 All dashboard data updates dynamically based on the selected time period"
                                         Grid.Row="1"
                                         Grid.ColumnSpan="2"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         FontSize="12"
                                         Opacity="0.8"
                                         Margin="0,8,0,0"
                                         ToolTip="Sales cards, profit metrics, charts, and all dashboard components will automatically update to show data for the selected time period"/>
                            </Grid>
                        </materialDesign:Card>

                        <!-- Quick Stats Row -->
                        <Grid Grid.Row="1" Margin="0,4,0,4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Today's Sales Card -->
                            <materialDesign:Card Grid.Column="0" Style="{StaticResource DashboardCard}"
                                               Visibility="{Binding IsSalesCardVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                               Cursor="Hand"
                                               Tag="Sales"
                                               MouseLeftButtonUp="StatsCard_MouseLeftButtonUp">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding SalesCardTitle}"
                                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                  Margin="8,0,0,0"
                                                  x:Name="TodaySalesTitle"/>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                HorizontalAlignment="Right"
                                                Width="24" Height="24"
                                                Padding="0"
                                                materialDesign:RippleAssist.Feedback="{DynamicResource PrimaryHueMidBrush}">
                                            <materialDesign:PackIcon Kind="ChevronRight"/>
                                        </Button>
                                    </DockPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                        <TextBlock Text="{Binding PeriodSales, StringFormat={}{0:N2}}"
                                                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                                        <TextBlock Text=" "
                                                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                                        <TextBlock Text="{DynamicResource CurrencySymbol}"
                                                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                                    </StackPanel>
                                    <TextBlock Text="{Binding SalesGrowth, StringFormat={}{0:+0.0;-0.0;0}%}"
                                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                              Foreground="{Binding SalesGrowthColor}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Gross Profit Card with Tooltip -->
                            <materialDesign:Card Grid.Column="1" Style="{StaticResource DashboardCard}"
                                                Visibility="{Binding IsProfitCardVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                Cursor="Hand"
                                                Tag="Profit"
                                                MouseLeftButtonUp="StatsCard_MouseLeftButtonUp">
                                <StackPanel>
                                    <DockPanel LastChildFill="False">
                                        <TextBlock Text="{Binding ProfitCardTitle}"
                                                  Style="{StaticResource CardHeader}"
                                                  Foreground="{DynamicResource MaterialDesignBody}"/>
                                    </DockPanel>
                                    <DockPanel>
                                        <TextBlock Text="{Binding FilteredProfit, StringFormat={}{0:N2}}"
                                                  Style="{StaticResource MetricValue}"
                                                  Foreground="{DynamicResource MaterialDesignBody}"/>
                                        <TextBlock Text="{DynamicResource CurrencySymbol}" 
                                                  Style="{StaticResource MetricValue}"
                                                  Foreground="{DynamicResource MaterialDesignBody}"
                                                  Margin="4,0,0,0"/>
                                    </DockPanel>
                                    <DockPanel>
                                        <TextBlock Text="{Binding ProfitGrowth, StringFormat={}{0:+0.0;-0.0;0}%}"
                                                  Style="{StaticResource GrowthText}"
                                                  Foreground="{Binding ProfitGrowthColor}"/>
                                    </DockPanel>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Stock Expiry Card -->
                            <materialDesign:Card Grid.Column="2" Style="{StaticResource DashboardCard}"
                                               Tag="Expiry"
                                               MouseLeftButtonUp="StatsCard_MouseLeftButtonUp">
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource StockExpiry}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="AlertCircle" 
                                                               Width="24" Height="24" 
                                                               Foreground="#ffc107"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding ExpiringProductsCount}" 
                                                 Style="{StaticResource MetricValue}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 Margin="8,0"/>
                                        <TextBlock Text="{DynamicResource ProductsExpiringSoon}" 
                                                 VerticalAlignment="Center" 
                                                 Opacity="0.7"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </DockPanel>
                                    <TextBlock Foreground="{DynamicResource MaterialDesignBody}" 
                                             FontSize="12"
                                             Margin="0,4,0,0">
                                        <Run Text="{Binding ExpiredProductsCount}"/>
                                        <Run Text=" "/>
                                        <Run Text="{DynamicResource ProductsExpired}"/>
                                    </TextBlock>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Low Stock Card -->
                            <materialDesign:Card Grid.Column="3" Style="{StaticResource DashboardCard}"
                                               Cursor="Hand">
                                <materialDesign:Card.InputBindings>
                                    <MouseBinding MouseAction="LeftClick" 
                                                Command="{Binding ShowLowStockStatsCommand}"/>
                                </materialDesign:Card.InputBindings>
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource LowStockAlert}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="PackageVariant" 
                                                               Width="24" Height="24" 
                                                               Foreground="#fd7e14"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding LowStockCount}" 
                                                 Style="{StaticResource MetricValue}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 Margin="8,0"/>
                                        <TextBlock Text="{DynamicResource ItemsLowOnStock}" 
                                                 VerticalAlignment="Center" 
                                                 Opacity="0.7"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </DockPanel>
                                    <TextBlock Foreground="{DynamicResource MaterialDesignBody}" 
                                             FontSize="12"
                                             Margin="0,4,0,0">
                                        <Run Text="{Binding OutOfStockCount}"/>
                                        <Run Text=" "/>
                                        <Run Text="{DynamicResource ItemsOutOfStock}"/>
                                    </TextBlock>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Overdue Orders Card -->
                            <materialDesign:Card Grid.Column="4" Style="{StaticResource DashboardCard}" Visibility="Collapsed">
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource OverdueOrders}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="ClockAlert" 
                                                               Width="24" Height="24" 
                                                               Foreground="#dc3545"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding OverdueOrdersCount}" 
                                                 Style="{StaticResource MetricValue}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 Margin="8,0"/>
                                        <TextBlock Text="{DynamicResource OverdueOrdersText}" 
                                                 VerticalAlignment="Center" 
                                                 Opacity="0.7"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </DockPanel>
                                    <TextBlock Foreground="{DynamicResource MaterialDesignBody}" 
                                             FontSize="12"
                                             Margin="0,4,0,0">
                                        <Run Text="{DynamicResource TotalOverdueValue}"/>
                                        <Run Text=": "/>
                                        <Run Text="{Binding TotalOverdueAmount, StringFormat={}{0:N2}}"/>
                                        <Run Text=" "/>
                                        <Run Text="{DynamicResource CurrencySymbol}"/>
                                    </TextBlock>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Unpaid Sales Card -->
                            <materialDesign:Card Grid.Column="4" Style="{StaticResource DashboardCard}"
                                                Cursor="Hand"
                                                Tag="UnpaidSales"
                                                MouseLeftButtonUp="StatsCard_MouseLeftButtonUp">
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource UnpaidSales}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="CashRemove" 
                                                               Width="24" Height="24" 
                                                               Foreground="#ff9800"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding UnpaidSalesCount}" 
                                                 Style="{StaticResource MetricValue}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 Margin="8,0"/>
                                        <TextBlock Text="{DynamicResource UnpaidSalesText}" 
                                                 VerticalAlignment="Center" 
                                                 Opacity="0.7"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </DockPanel>
                                    <TextBlock Foreground="{DynamicResource MaterialDesignBody}" 
                                             FontSize="12"
                                             Margin="0,4,0,0">
                                        <Run Text="{DynamicResource TotalUnpaidAmount}"/>
                                        <Run Text=": "/>
                                        <Run Text="{Binding UnpaidSalesAmount, StringFormat={}{0:N2}}"/>
                                        <Run Text=" "/>
                                        <Run Text="{DynamicResource CurrencySymbol}"/>
                                    </TextBlock>
                                    <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                        <TextBlock Text="{Binding OverdueSalesCount}"
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                        <TextBlock Text=" " 
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                        <TextBlock Text="{DynamicResource SalesPastDue}"
                                                 Foreground="#dc3545"
                                                 FontSize="12"/>
                                        <TextBlock Text=" (" 
                                                 Foreground="#dc3545" 
                                                 FontSize="12"
                                                 Margin="4,0,0,0"/>
                                        <TextBlock Text="{Binding OverdueSalesAmount, StringFormat={}{0:N2}}"
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                        <TextBlock Text=" " 
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                        <TextBlock Text="{DynamicResource CurrencySymbol}"
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                        <TextBlock Text=")" 
                                                 Foreground="#dc3545" 
                                                 FontSize="12"/>
                                    </StackPanel>
                                </StackPanel>
                                <materialDesign:Card.InputBindings>
                                    <MouseBinding MouseAction="LeftClick" 
                                                Command="{Binding ShowUnpaidSalesStatsCommand}"/>
                                </materialDesign:Card.InputBindings>
                            </materialDesign:Card>

                            <!-- Expenses Card -->
                            <materialDesign:Card Grid.Column="5" Style="{StaticResource DashboardCard}" 
                                               Visibility="{Binding IsExpensesCardVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                               Tag="Expenses"
                                               Cursor="Hand"
                                               MouseLeftButtonUp="StatsCard_MouseLeftButtonUp">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <DockPanel Grid.Row="0">
                                        <materialDesign:PackIcon Kind="CreditCardOutline" 
                                                               Width="24" Height="24" 
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{DynamicResource BusinessExpenses}" 
                                                 Style="{StaticResource CardHeader}" 
                                                 Margin="8,0,0,4"/>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                HorizontalAlignment="Right"
                                                Width="24" Height="24"
                                                Padding="0"
                                                materialDesign:RippleAssist.Feedback="{DynamicResource PrimaryHueMidBrush}">
                                            <materialDesign:PackIcon Kind="ChevronRight"/>
                                        </Button>
                                    </DockPanel>

                                    <!-- Content -->
                                    <StackPanel Grid.Row="1">
                                        <DockPanel>
                                            <TextBlock Text="{Binding MonthlyExpensesAmount, StringFormat={}{0:N2}}" 
                                                     Style="{StaticResource MetricValue}"/>
                                            <TextBlock Text="{DynamicResource CurrencySymbol}" 
                                                     Style="{StaticResource MetricValue}"
                                                     Margin="4,0,0,0"/>
                                        </DockPanel>
                                        <TextBlock Text="{DynamicResource MonthlyExpenses}"
                                                 FontSize="12"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                 Margin="0,4,0,0"/>
                                        <TextBlock>
                                            <Run Text="{Binding UpcomingExpensesCount}"/>
                                            <Run Text=" "/>
                                            <Run Text="{DynamicResource UpcomingExpenses}"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>
                        </Grid>

                        <!-- Product and Category Performance Row -->
                        <Grid x:Name="ProductPerformanceSection" Grid.Row="2" Margin="0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*"/>
                                <ColumnDefinition Width="2*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Section Loading Indicator -->
                            <Grid Grid.ColumnSpan="2" 
                                  Background="{DynamicResource MaterialDesignPaper}" 
                                  Opacity="0.8"
                                  Visibility="{Binding IsProductPerformanceLoading, Converter={StaticResource BooleanToVisibilityConverter}, FallbackValue=Collapsed}">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <ProgressBar IsIndeterminate="True" Width="40" Height="40"
                                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    <TextBlock Text="{DynamicResource LoadingProductData}" 
                                             Margin="0,8,0,0"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </Grid>

                            <!-- Product Performance -->
                            <materialDesign:Card Grid.Column="0" Style="{StaticResource DashboardCard}" Margin="0,0,4,0">
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource ProductPerformance}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <!-- Product Metrics Tabs -->
                                    <TabControl Style="{StaticResource MaterialDesignTabControl}" 
                                              TabStripPlacement="Top"
                                              x:Name="ProductTabControl"
                                              SelectionChanged="TabControl_SelectionChanged"
                                              Tag="Product"
                                              Margin="0,8,0,0">
                                        <TabItem Header="{DynamicResource Revenue}" Tag="Revenue">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding ProductChartSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="{Binding IsTabActiveForChart, Converter={StaticResource InverseBooleanConverter}}">
                                                <lvc:CartesianChart.DataTooltip>
                                                    <lvc:DefaultTooltip SelectionMode="SharedXValues"
                                                                      Background="#333333"
                                                                      Foreground="White"/>
                                                </lvc:CartesianChart.DataTooltip>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource Products}"
                                                             Labels="{Binding ProductChartLabels}"
                                                             FontSize="11"
                                                             Foreground="Black"
                                                             LabelsRotation="15"
                                                             ShowLabels="True">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Revenue}"
                                                             LabelFormatter="{Binding ProductValueFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1000" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource Profit}" Tag="Profit">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding ProfitChartSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="{Binding IsTabActiveForChart, Converter={StaticResource InverseBooleanConverter}}">
                                                <lvc:CartesianChart.DataTooltip>
                                                    <lvc:DefaultTooltip SelectionMode="SharedXValues"
                                                                      Background="#333333"
                                                                      Foreground="White"/>
                                                </lvc:CartesianChart.DataTooltip>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource Products}"
                                                             Labels="{Binding ProductChartLabels}"
                                                             FontSize="11"
                                                             Foreground="Black"
                                                             LabelsRotation="15"
                                                             ShowLabels="True">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Profit}"
                                                             LabelFormatter="{Binding ProductValueFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1000" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource Margin}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding MarginChartSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.DataTooltip>
                                                    <lvc:DefaultTooltip SelectionMode="SharedXValues"
                                                                      Background="#333333"
                                                                      Foreground="White"/>
                                                </lvc:CartesianChart.DataTooltip>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource Products}"
                                                             Labels="{Binding ProductChartLabels}"
                                                             FontSize="11"
                                                             Foreground="Black"
                                                             LabelsRotation="15"
                                                             ShowLabels="True">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource ProfitMargin}"
                                                             LabelFormatter="{Binding MarginFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="10" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource ItemsSold}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding ItemsSoldChartSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.DataTooltip>
                                                    <lvc:DefaultTooltip SelectionMode="SharedXValues"
                                                                      Background="#333333"
                                                                      Foreground="White"/>
                                                </lvc:CartesianChart.DataTooltip>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource Products}"
                                                             Labels="{Binding ProductChartLabels}"
                                                             FontSize="11"
                                                             Foreground="Black"
                                                             LabelsRotation="15"
                                                             ShowLabels="True">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource ItemsSold}"
                                                             LabelFormatter="{Binding ItemsSoldFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="50" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                            </lvc:CartesianChart>
                                        </TabItem>
                                    </TabControl>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Category Performance -->
                            <materialDesign:Card Grid.Column="1" Style="{StaticResource DashboardCard}" Margin="4,0,0,0">
                                <StackPanel>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Text="{DynamicResource CategoryPerformance}" 
                                                 Style="{StaticResource CardHeader}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 Grid.Column="0"/>
                                                 
                                        <Button Grid.Column="1"
                                              x:Name="btnCategoryDetails"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="{DynamicResource DetailedCategoryStats}"
                                              Click="OnCategoryDetailsClicked">
                                            <materialDesign:PackIcon Kind="ChartBoxOutline" Width="22" Height="22"/>
                                        </Button>
                                    </Grid>
                                    
                                    <!-- Category Metrics Tabs -->
                                    <TabControl Style="{StaticResource MaterialDesignTabControl}" 
                                              TabStripPlacement="Top"
                                              Margin="0,8,0,0">
                                        <TabItem Header="{DynamicResource Revenue}">
                                            <lvc:PieChart Series="{Binding CategoryChartSeries}"
                                                         LegendLocation="Right"
                                                         Height="400"
                                                         InnerRadius="0"
                                                         StartingRotationAngle="0"
                                                         Hoverable="True">
                                                <lvc:PieChart.ChartLegend>
                                                    <lvc:DefaultLegend BulletSize="15"
                                                              Orientation="Vertical"
                                                              VerticalAlignment="Center"
                                                              Margin="20,0,0,0"
                                                              FontSize="12"/>
                                                </lvc:PieChart.ChartLegend>
                                                <lvc:PieChart.DataTooltip>
                                                    <lvc:DefaultTooltip Background="#333333" Foreground="White"/>
                                                </lvc:PieChart.DataTooltip>
                                            </lvc:PieChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource Profit}">
                                            <lvc:PieChart Series="{Binding CategoryProfitSeries}"
                                                         LegendLocation="Right"
                                                         Height="400"
                                                         InnerRadius="0"
                                                         StartingRotationAngle="0"
                                                         Hoverable="True">
                                                <lvc:PieChart.ChartLegend>
                                                    <lvc:DefaultLegend BulletSize="15"
                                                              Orientation="Vertical"
                                                              VerticalAlignment="Center"
                                                              Margin="20,0,0,0"
                                                              FontSize="12"/>
                                                </lvc:PieChart.ChartLegend>
                                                <lvc:PieChart.DataTooltip>
                                                    <lvc:DefaultTooltip Background="#333333" Foreground="White"/>
                                                </lvc:PieChart.DataTooltip>
                                            </lvc:PieChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource Margin}">
                                            <lvc:PieChart Series="{Binding CategoryMarginSeries}"
                                                         LegendLocation="Right"
                                                         Height="400"
                                                         InnerRadius="0"
                                                         StartingRotationAngle="0"
                                                         Hoverable="True">
                                                <lvc:PieChart.ChartLegend>
                                                    <lvc:DefaultLegend BulletSize="15"
                                                              Orientation="Vertical"
                                                              VerticalAlignment="Center"
                                                              Margin="20,0,0,0"
                                                              FontSize="12"/>
                                                </lvc:PieChart.ChartLegend>
                                                <lvc:PieChart.DataTooltip>
                                                    <lvc:DefaultTooltip Background="#333333" Foreground="White"/>
                                                </lvc:PieChart.DataTooltip>
                                            </lvc:PieChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource ItemsSold}">
                                            <lvc:PieChart Series="{Binding CategoryItemsSoldSeries}"
                                                         LegendLocation="Right"
                                                         Height="400"
                                                         InnerRadius="0"
                                                         StartingRotationAngle="0"
                                                         Hoverable="True">
                                                <lvc:PieChart.ChartLegend>
                                                    <lvc:DefaultLegend BulletSize="15"
                                                              Orientation="Vertical"
                                                              VerticalAlignment="Center"
                                                              Margin="20,0,0,0"
                                                              FontSize="12"/>
                                                </lvc:PieChart.ChartLegend>
                                                <lvc:PieChart.DataTooltip>
                                                    <lvc:DefaultTooltip Background="#333333" Foreground="White"/>
                                                </lvc:PieChart.DataTooltip>
                                            </lvc:PieChart>
                                        </TabItem>
                                    </TabControl>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <!-- User Performance -->
                        <Grid x:Name="UserPerformanceSection" Grid.Row="4" Margin="0,12" Visibility="Collapsed">
                            <!-- Section Loading Indicator -->
                            <Grid Background="{DynamicResource MaterialDesignPaper}" 
                                  Opacity="0.8"
                                  Visibility="{Binding IsUserPerformanceLoading, Converter={StaticResource BooleanToVisibilityConverter}, FallbackValue=Collapsed}">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <ProgressBar IsIndeterminate="True" Width="40" Height="40"
                                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    <TextBlock Text="{DynamicResource LoadingUserData}" 
                                             Margin="0,8,0,0"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </Grid>

                            <materialDesign:Card Style="{StaticResource DashboardCard}">
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource UserPerformance}" 
                                             Style="{StaticResource CardHeader}"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>

                                    <ScrollViewer HorizontalScrollBarVisibility="Auto" 
                                                VerticalScrollBarVisibility="Disabled"
                                                Margin="0,16,0,24">
                                        <ItemsControl ItemsSource="{Binding UserPerformances}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <materialDesign:Card Width="300" 
                                                            Margin="8"
                                                                      Background="{DynamicResource MaterialDesignPaper}"
                                                                      Foreground="{DynamicResource MaterialDesignBody}"
                                                                      UniformCornerRadius="8"
                                                            Padding="16">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                            </Grid.RowDefinitions>

                                                            <!-- User Image or Initials Circle -->
                                                            <Ellipse Grid.Row="0" Grid.Column="0" 
                                                                    Width="40" Height="40" 
                                                                    Fill="{Binding User.Color}" 
                                                                    Grid.RowSpan="2"
                                                                    VerticalAlignment="Top"
                                                                    Margin="0,0,12,0"/>

                                                            <TextBlock Grid.Row="0" Grid.Column="0" 
                                                                     Text="{Binding User.Initials}" 
                                                                     FontWeight="SemiBold"
                                                                     Foreground="White"
                                                                         HorizontalAlignment="Center"
                                                                         VerticalAlignment="Center"
                                                                     Grid.RowSpan="2"
                                                                     Margin="0,0,12,0"/>

                                                            <!-- User Name and Role -->
                                                            <TextBlock Grid.Row="0" Grid.Column="1" 
                                                                     Text="{Binding User.Name}" 
                                                                     FontWeight="SemiBold"
                                                                     Foreground="{DynamicResource MaterialDesignBody}"/>

                                                            <TextBlock Grid.Row="1" Grid.Column="1" 
                                                                     Text="{Binding User.Role}" 
                                                                     Opacity="0.7"
                                                                     Margin="0,0,0,12"
                                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                                                            <!-- Sales Metrics -->
                                                            <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                                                     Text="{DynamicResource TotalSales}"
                                                                     FontWeight="SemiBold"
                                                                     Margin="0,0,0,4"
                                                                     Foreground="{DynamicResource MaterialDesignBody}"/>

                                                            <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                                                      Orientation="Horizontal"
                                                                      Margin="0,0,0,8">
                                                                <TextBlock Text="{Binding SalesAmount, StringFormat={}{0:N2}}"
                                                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                                                <TextBlock Text=" "
                                                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                                                <TextBlock Text="{DynamicResource CurrencySymbol}"
                                                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                                            </StackPanel>

                                                            <!-- Progress Bar -->
                                                            <ProgressBar Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2"
                                                                       Minimum="0" Maximum="100"
                                                                       Value="{Binding PerformancePercentage}"
                                                                       Height="6"
                                                                       Background="#E0E0E0"/>
                                                        </Grid>
                                                    </materialDesign:Card>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </ScrollViewer>

                                    <!-- Performance Charts -->
                                    <TabControl Style="{StaticResource MaterialDesignTabControl}" 
                                              TabStripPlacement="Top">
                                        <TabItem Header="{DynamicResource SalesPerformance}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding UserSalesPerformanceSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Users}"
                                                             Labels="{Binding UserPerformanceLabels}"
                                                             FontSize="11">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource SalesAmount}"
                                                             LabelFormatter="{Binding CurrencyFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="5000" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource Transactions}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding UserTransactionsSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Users}"
                                                             Labels="{Binding UserPerformanceLabels}"
                                                             FontSize="11">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource NumberOfTransactions}"
                                                             LabelFormatter="{Binding NumberFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="25" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource CustomersServed}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding UserCustomersSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Users}"
                                                             Labels="{Binding UserPerformanceLabels}"
                                                             FontSize="11">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource NumberOfCustomers}"
                                                             LabelFormatter="{Binding NumberFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="25" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                            </lvc:CartesianChart>
                                        </TabItem>

                                        <TabItem Header="{DynamicResource ConversionRate}">
                                            <lvc:CartesianChart Height="300"
                                                              Series="{Binding UserConversionSeries}"
                                                              LegendLocation="Right"
                                                              DisableAnimations="False">
                                                <lvc:CartesianChart.AxisY>
                                                    <lvc:Axis Title="{DynamicResource Users}"
                                                             Labels="{Binding UserPerformanceLabels}"
                                                             FontSize="11">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="1" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisY>
                                                <lvc:CartesianChart.AxisX>
                                                    <lvc:Axis Title="{DynamicResource ConversionRate}"
                                                             LabelFormatter="{Binding NumberFormatter}">
                                                        <lvc:Axis.Separator>
                                                            <lvc:Separator Step="10" />
                                                        </lvc:Axis.Separator>
                                                    </lvc:Axis>
                                                </lvc:CartesianChart.AxisX>
                                            </lvc:CartesianChart>
                                        </TabItem>
                                    </TabControl>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>
                    </Grid>

                    <!-- Loading Overlay -->
                    <Grid Style="{StaticResource LoadingOverlay}">
                        <Rectangle Fill="{DynamicResource MaterialDesignPaper}" Opacity="0.8"/>
                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" Width="40" Height="40"
                                       Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource LoadingDashboardData}" 
                                     Margin="0,8,0,0"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Skeleton Loading for Quick Stats -->
                    <Grid x:Name="SkeletonGrid" 
                          Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <WrapPanel>
                            <materialDesign:Card Width="350" Height="120" 
                                    Margin="8" 
                                    Style="{StaticResource DashboardCard}">
                                <Border Background="{DynamicResource MaterialDesignSurfaceBackground}">
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="Loaded">
                                            <BeginStoryboard Storyboard="{StaticResource SkeletonAnimation}"/>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>
                            </materialDesign:Card>
                            <materialDesign:Card Width="350" Height="120" 
                                    Margin="8" 
                                    Style="{StaticResource DashboardCard}">
                                <Border Background="{DynamicResource MaterialDesignSurfaceBackground}">
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="Loaded">
                                            <BeginStoryboard Storyboard="{StaticResource SkeletonAnimation}"/>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>
                            </materialDesign:Card>
                            <materialDesign:Card Width="350" Height="120" 
                                    Margin="8" 
                                    Style="{StaticResource DashboardCard}">
                                <Border Background="{DynamicResource MaterialDesignSurfaceBackground}">
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="Loaded">
                                            <BeginStoryboard Storyboard="{StaticResource SkeletonAnimation}"/>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>
                            </materialDesign:Card>
                        </WrapPanel>
                    </Grid>

                    <!-- Data Content with Update Animation -->
                    <Grid x:Name="ContentGrid" 
                          RenderTransformOrigin="0.5,0.5"
                          Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=inverse}">
                        <Grid.RenderTransform>
                            <TranslateTransform/>
                        </Grid.RenderTransform>
                        <Grid.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard Storyboard="{StaticResource DataUpdateAnimation}"/>
                            </EventTrigger>
                        </Grid.Triggers>
                        <!-- Existing content here -->
                    </Grid>
                </Grid>
            </Viewbox>
        </Grid>
    </materialDesign:DialogHost>
</UserControl> 