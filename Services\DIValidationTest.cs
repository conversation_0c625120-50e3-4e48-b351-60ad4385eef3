using Microsoft.Extensions.DependencyInjection;
using POSSystem.Services.Interfaces;
using System;
using System.Diagnostics;

namespace POSSystem.Services
{
    /// <summary>
    /// Test class to validate dependency injection configuration
    /// </summary>
    public static class DIValidationTest
    {
        /// <summary>
        /// Validates that all services can be resolved from the DI container
        /// </summary>
        /// <returns>True if all services can be resolved, false otherwise</returns>
        public static bool ValidateServiceRegistration()
        {
            try
            {
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                
                // Test core services
                TestService<IDatabaseService>(serviceProvider, "DatabaseService");
                TestService<IAuthenticationService>(serviceProvider, "AuthenticationService");
                TestService<IPasswordService>(serviceProvider, "PasswordService");
                TestService<ISettingsService>(serviceProvider, "SettingsService");
                TestService<IThemeService>(serviceProvider, "ThemeService");
                TestService<ILicenseService>(serviceProvider, "LicenseService");
                TestService<IDialogService>(serviceProvider, "DialogService");
                TestService<ISoundService>(serviceProvider, "SoundService");
                
                // Test business services
                TestService<IAlertService>(serviceProvider, "AlertService");
                TestService<ICashDrawerService>(serviceProvider, "CashDrawerService");
                TestService<ICustomerService>(serviceProvider, "CustomerService");
                TestService<IDiscountService>(serviceProvider, "DiscountService");
                TestService<IFavoriteService>(serviceProvider, "FavoriteService");
                TestService<IUserPermissionsService>(serviceProvider, "UserPermissionsService");
                
                // Test utility services
                TestService<IInvoiceNumberService>(serviceProvider, "InvoiceNumberService");
                TestService<IProductLookupService>(serviceProvider, "ProductLookupService");
                TestService<IInvoicePrintService>(serviceProvider, "InvoicePrintService");
                TestService<ISalePrintService>(serviceProvider, "SalePrintService");
                TestService<IChartService>(serviceProvider, "ChartService");
                
                Debug.WriteLine("✅ All services validated successfully!");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Service validation failed: {ex.Message}");
                return false;
            }
        }
        
        private static void TestService<T>(IServiceProvider serviceProvider, string serviceName)
        {
            try
            {
                var service = serviceProvider.GetRequiredService<T>();
                if (service != null)
                {
                    Debug.WriteLine($"✅ {serviceName} resolved successfully");
                }
                else
                {
                    Debug.WriteLine($"⚠️ {serviceName} resolved but is null");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to resolve {serviceName}: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Tests ViewModel resolution
        /// </summary>
        public static bool ValidateViewModelRegistration()
        {
            try
            {
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                
                // Test main ViewModels
                TestViewModel<ViewModels.SaleViewModel>(serviceProvider, "SaleViewModel");
                TestViewModel<ViewModels.ProductsViewModel>(serviceProvider, "ProductsViewModel");
                TestViewModel<ViewModels.DashboardViewModel>(serviceProvider, "DashboardViewModel");
                TestViewModel<ViewModels.CustomersViewModel>(serviceProvider, "CustomersViewModel");
                TestViewModel<ViewModels.CategoriesViewModel>(serviceProvider, "CategoriesViewModel");
                TestViewModel<ViewModels.UsersViewModel>(serviceProvider, "UsersViewModel");
                TestViewModel<ViewModels.SuppliersViewModel>(serviceProvider, "SuppliersViewModel");
                TestViewModel<ViewModels.ReportsViewModel>(serviceProvider, "ReportsViewModel");
                TestViewModel<ViewModels.SettingsViewModel>(serviceProvider, "SettingsViewModel");
                TestViewModel<ViewModels.CashDrawerViewModel>(serviceProvider, "CashDrawerViewModel");
                
                Debug.WriteLine("✅ All ViewModels validated successfully!");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ ViewModel validation failed: {ex.Message}");
                return false;
            }
        }
        
        private static void TestViewModel<T>(IServiceProvider serviceProvider, string viewModelName)
        {
            try
            {
                var viewModel = serviceProvider.GetRequiredService<T>();
                if (viewModel != null)
                {
                    Debug.WriteLine($"✅ {viewModelName} resolved successfully");
                }
                else
                {
                    Debug.WriteLine($"⚠️ {viewModelName} resolved but is null");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to resolve {viewModelName}: {ex.Message}");
                throw;
            }
        }
    }
}
