using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class BooleanToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string textValues)
            {
                var values = textValues.Split(',');
                if (values.Length == 2)
                {
                    return boolValue ? values[0] : values[1];
                }
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && parameter is string textValues)
            {
                var values = textValues.Split(',');
                if (values.Length == 2)
                {
                    return stringValue == values[0];
                }
            }
            return false;
        }
    }
} 