using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Diagnostics
{
    /// <summary>
    /// Diagnostic service to verify weight-based product functionality
    /// </summary>
    public class WeightBasedDiagnostics
    {
        private readonly DatabaseService _dbService;

        public WeightBasedDiagnostics(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// Runs comprehensive diagnostics for weight-based product functionality
        /// </summary>
        public async Task<DiagnosticResult> RunDiagnosticsAsync()
        {
            var result = new DiagnosticResult();
            
            Debug.WriteLine("=== Weight-Based Product Diagnostics ===");
            
            try
            {
                // Test 1: Check database schema
                await CheckDatabaseSchema(result);
                
                // Test 2: Check existing weight-based products
                await CheckExistingWeightBasedProducts(result);
                
                // Test 3: Test search functionality
                await TestSearchFunctionality(result);
                
                // Test 4: Test barcode functionality
                await TestBarcodeFunctionality(result);
                
                // Test 5: Test product creation
                await TestProductCreation(result);
                
            }
            catch (Exception ex)
            {
                result.AddError($"Diagnostic failed with exception: {ex.Message}");
                Debug.WriteLine($"[DIAGNOSTICS] Error: {ex.Message}");
            }
            
            Debug.WriteLine("=== Diagnostics Complete ===");
            return result;
        }

        private async Task CheckDatabaseSchema(DiagnosticResult result)
        {
            Debug.WriteLine("[DIAGNOSTICS] Checking database schema...");
            
            try
            {
                // Check if IsWeightBased column exists by trying to query it
                var testQuery = _dbService.Context.Products
                    .Select(p => new { p.Id, p.IsWeightBased })
                    .Take(1)
                    .ToList();
                
                result.AddSuccess("✅ IsWeightBased column exists in Products table");
                Debug.WriteLine("[DIAGNOSTICS] ✅ Database schema OK");
            }
            catch (Exception ex)
            {
                result.AddError($"❌ Database schema issue: {ex.Message}");
                result.AddError("   → Need to run migration: Migrations/AddWeightBasedProductSupport.sql");
                Debug.WriteLine($"[DIAGNOSTICS] ❌ Schema error: {ex.Message}");
            }
        }

        private async Task CheckExistingWeightBasedProducts(DiagnosticResult result)
        {
            Debug.WriteLine("[DIAGNOSTICS] Checking existing weight-based products...");
            
            try
            {
                var weightBasedProducts = _dbService.Context.Products
                    .Where(p => p.IsWeightBased == true)
                    .Select(p => new { p.Id, p.Name, p.SKU, p.IsWeightBased })
                    .ToList();
                
                if (weightBasedProducts.Any())
                {
                    result.AddSuccess($"✅ Found {weightBasedProducts.Count} weight-based products");
                    foreach (var product in weightBasedProducts.Take(3))
                    {
                        result.AddInfo($"   - {product.Name} (SKU: {product.SKU})");
                    }
                    Debug.WriteLine($"[DIAGNOSTICS] ✅ Found {weightBasedProducts.Count} weight-based products");
                }
                else
                {
                    result.AddWarning("⚠️ No weight-based products found");
                    result.AddInfo("   → Create test weight-based products to verify functionality");
                    Debug.WriteLine("[DIAGNOSTICS] ⚠️ No weight-based products found");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"❌ Error checking weight-based products: {ex.Message}");
                Debug.WriteLine($"[DIAGNOSTICS] ❌ Error: {ex.Message}");
            }
        }

        private async Task TestSearchFunctionality(DiagnosticResult result)
        {
            Debug.WriteLine("[DIAGNOSTICS] Testing search functionality...");
            
            try
            {
                // Test search for weight-based products
                var searchResults = await _dbService.SearchProductsAsync("weight");
                
                var weightBasedInResults = searchResults.Where(p => p.IsWeightBased).ToList();
                
                if (weightBasedInResults.Any())
                {
                    result.AddSuccess($"✅ Search functionality working - found {weightBasedInResults.Count} weight-based products");
                    Debug.WriteLine($"[DIAGNOSTICS] ✅ Search OK - found {weightBasedInResults.Count} weight-based products");
                }
                else
                {
                    result.AddWarning("⚠️ Search functionality may have issues - no weight-based products in search results");
                    result.AddInfo("   → Try searching for specific weight-based product names");
                    Debug.WriteLine("[DIAGNOSTICS] ⚠️ Search may have issues");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"❌ Search functionality error: {ex.Message}");
                Debug.WriteLine($"[DIAGNOSTICS] ❌ Search error: {ex.Message}");
            }
        }

        private async Task TestBarcodeFunctionality(DiagnosticResult result)
        {
            Debug.WriteLine("[DIAGNOSTICS] Testing barcode functionality...");
            
            try
            {
                // Find a product with a barcode
                var productWithBarcode = _dbService.Context.Products
                    .Where(p => p.Barcodes.Any())
                    .Select(p => new { 
                        p.Id, 
                        p.Name, 
                        p.IsWeightBased,
                        Barcode = p.Barcodes.FirstOrDefault().Barcode 
                    })
                    .FirstOrDefault();
                
                if (productWithBarcode != null)
                {
                    // Test barcode lookup
                    var foundProduct = _dbService.GetProductByBarcode(productWithBarcode.Barcode);
                    
                    if (foundProduct != null)
                    {
                        result.AddSuccess($"✅ Barcode functionality working");
                        result.AddInfo($"   - Found: {foundProduct.Name}");
                        result.AddInfo($"   - IsWeightBased: {foundProduct.IsWeightBased}");
                        result.AddInfo($"   - Type: {foundProduct.Type}");
                        Debug.WriteLine($"[DIAGNOSTICS] ✅ Barcode OK - found {foundProduct.Name}");
                    }
                    else
                    {
                        result.AddError("❌ Barcode lookup failed - product not found");
                        Debug.WriteLine("[DIAGNOSTICS] ❌ Barcode lookup failed");
                    }
                }
                else
                {
                    result.AddWarning("⚠️ No products with barcodes found for testing");
                    result.AddInfo("   → Add barcodes to products for barcode testing");
                    Debug.WriteLine("[DIAGNOSTICS] ⚠️ No products with barcodes");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"❌ Barcode functionality error: {ex.Message}");
                Debug.WriteLine($"[DIAGNOSTICS] ❌ Barcode error: {ex.Message}");
            }
        }

        private async Task TestProductCreation(DiagnosticResult result)
        {
            Debug.WriteLine("[DIAGNOSTICS] Testing product creation capability...");
            
            try
            {
                // Test if we can create a weight-based product (without actually saving)
                var testProduct = new Product
                {
                    Name = "Test Weight Product",
                    SKU = "TEST-WEIGHT-001",
                    IsWeightBased = true,
                    Type = ProductType.Product,
                    SellingPrice = 5.99m,
                    StockQuantity = 10
                };
                
                // Validate the product properties
                if (testProduct.IsWeightBased)
                {
                    result.AddSuccess("✅ Weight-based product creation capability verified");
                    Debug.WriteLine("[DIAGNOSTICS] ✅ Product creation OK");
                }
                else
                {
                    result.AddError("❌ Weight-based property not working correctly");
                    Debug.WriteLine("[DIAGNOSTICS] ❌ Product creation issue");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"❌ Product creation test error: {ex.Message}");
                Debug.WriteLine($"[DIAGNOSTICS] ❌ Creation error: {ex.Message}");
            }
        }
    }

    public class DiagnosticResult
    {
        public List<string> Messages { get; } = new List<string>();
        public bool HasErrors => Messages.Any(m => m.StartsWith("❌"));
        public bool HasWarnings => Messages.Any(m => m.StartsWith("⚠️"));
        public bool IsSuccess => !HasErrors;

        public void AddSuccess(string message)
        {
            Messages.Add(message);
        }

        public void AddWarning(string message)
        {
            Messages.Add(message);
        }

        public void AddError(string message)
        {
            Messages.Add(message);
        }

        public void AddInfo(string message)
        {
            Messages.Add(message);
        }

        public string GetSummary()
        {
            var summary = string.Join("\n", Messages);
            summary += "\n\n=== Summary ===\n";
            
            if (IsSuccess && !HasWarnings)
            {
                summary += "🎉 All diagnostics passed! Weight-based products should be working correctly.";
            }
            else if (IsSuccess && HasWarnings)
            {
                summary += "⚠️ Diagnostics passed with warnings. Some features may need setup.";
            }
            else
            {
                summary += "❌ Diagnostics failed. Please address the errors above.";
            }
            
            return summary;
        }
    }
}
