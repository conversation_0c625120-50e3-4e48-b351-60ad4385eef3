﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using POSSystem.Data;

#nullable disable

namespace POSSystem.Migrations
{
    [DbContext(typeof(POSDbContext))]
    [Migration("20250205114336_UpdateCashTransactionConfiguration")]
    partial class UpdateCashTransactionConfiguration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.2");

            modelBuilder.Entity("Audit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("NewValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("RecordId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("POSSystem.Models.BatchStock", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ManufactureDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("BatchStock", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ActualBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ClosedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ClosedById")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("CurrentBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Difference")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExpectedBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OpenedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("OpenedById")
                        .IsRequired()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("OpeningBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ClosedById");

                    b.HasIndex("OpenedById");

                    b.ToTable("CashDrawers", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.CashTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CashDrawerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PerformedById")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Reason")
                        .HasColumnType("TEXT");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CashDrawerId");

                    b.HasIndex("PerformedById");

                    b.ToTable("CashTransactions", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ParentCategoryId");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("Id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("Address");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("Email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("FirstName");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER")
                        .HasColumnName("IsActive");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("LastName");

                    b.Property<DateTime?>("LastVisit")
                        .HasColumnType("TEXT")
                        .HasColumnName("LastVisit");

                    b.Property<string>("LoyaltyCode")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("LoyaltyCode");

                    b.Property<decimal>("LoyaltyPoints")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue(0m)
                        .HasColumnName("LoyaltyPoints");

                    b.Property<int?>("LoyaltyTierId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("Phone");

                    b.Property<decimal>("TotalSpent")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue(0m)
                        .HasColumnName("TotalSpent");

                    b.Property<int>("TotalVisits")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0)
                        .HasColumnName("TotalVisits");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id");

                    b.HasIndex("LoyaltyTierId");

                    b.ToTable("Customers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "123 Main St",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4592),
                            Email = "<EMAIL>",
                            FirstName = "John",
                            IsActive = 1,
                            LastName = "Doe",
                            LastVisit = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4570),
                            LoyaltyCode = "JD12345",
                            LoyaltyPoints = 100m,
                            Phone = "************",
                            TotalSpent = 500.00m,
                            TotalVisits = 5,
                            UpdatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4592)
                        },
                        new
                        {
                            Id = 2,
                            Address = "456 Oak Ave",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4598),
                            Email = "<EMAIL>",
                            FirstName = "Jane",
                            IsActive = 1,
                            LastName = "Smith",
                            LastVisit = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4596),
                            LoyaltyCode = "JS67890",
                            LoyaltyPoints = 250m,
                            Phone = "************",
                            TotalSpent = 800.00m,
                            TotalVisits = 8,
                            UpdatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4598)
                        });
                });

            modelBuilder.Entity("POSSystem.Models.Discount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AppliedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("AppliedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ApprovedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("DiscountTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<decimal>("FinalPrice")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("DECIMAL(10,2)");

                    b.Property<int>("ReasonId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SaleItemId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("AppliedByUserId");

                    b.HasIndex("ApprovedByUserId");

                    b.HasIndex("DiscountTypeId");

                    b.HasIndex("ReasonId");

                    b.HasIndex("SaleId");

                    b.HasIndex("SaleItemId");

                    b.ToTable("Discounts");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("ApprovalThreshold")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DiscountTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("MaxFixedAmount")
                        .HasPrecision(10, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("MaxPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("MinPricePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("TEXT");

                    b.Property<bool>("RequiresApproval")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DiscountTypeId");

                    b.HasIndex("RoleId");

                    b.ToTable("DiscountPermissions", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.DiscountReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("DiscountReasons");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "MANAGER",
                            Description = "Manager Special",
                            IsActive = true
                        },
                        new
                        {
                            Id = 2,
                            Code = "DAMAGED",
                            Description = "Damaged Item",
                            IsActive = true
                        },
                        new
                        {
                            Id = 3,
                            Code = "PRICEMATCH",
                            Description = "Price Match",
                            IsActive = true
                        },
                        new
                        {
                            Id = 4,
                            Code = "CUSTOMER",
                            Description = "Customer Satisfaction",
                            IsActive = true
                        },
                        new
                        {
                            Id = 5,
                            Code = "PROMO",
                            Description = "Promotion",
                            IsActive = true
                        },
                        new
                        {
                            Id = 6,
                            Code = "BULK",
                            Description = "Bulk Purchase",
                            IsActive = true
                        },
                        new
                        {
                            Id = 7,
                            Code = "LOYALTY",
                            Description = "Loyalty Discount",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("POSSystem.Models.DiscountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("DiscountTypes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Percentage off the original price",
                            Name = "Percentage"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Fixed amount off the original price",
                            Name = "Fixed Amount"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Override with a specific price",
                            Name = "Price Override"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.InventoryTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Reference")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("TransactionType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserId");

                    b.ToTable("InventoryTransactions");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyProgram", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ExpiryMonths")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MinimumPointsRedemption")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MonetaryValuePerPoint")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PointsPerDollar")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("LoyaltyPrograms");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 954, DateTimeKind.Local).AddTicks(5954),
                            Description = "Standard customer loyalty program",
                            ExpiryMonths = 12,
                            IsActive = true,
                            MinimumPointsRedemption = 100m,
                            MonetaryValuePerPoint = 0.01m,
                            Name = "Standard Rewards",
                            PointsPerDollar = 1.0m
                        });
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Benefits")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("LoyaltyProgramId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MinimumPoints")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PointsMultiplier")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("LoyaltyProgramId");

                    b.ToTable("LoyaltyTiers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Benefits = "Basic rewards earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 0m,
                            Name = "Bronze",
                            PointsMultiplier = 1.0m
                        },
                        new
                        {
                            Id = 2,
                            Benefits = "25% bonus points earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 1000m,
                            Name = "Silver",
                            PointsMultiplier = 1.25m
                        },
                        new
                        {
                            Id = 3,
                            Benefits = "50% bonus points earning",
                            LoyaltyProgramId = 1,
                            MinimumPoints = 5000m,
                            Name = "Gold",
                            PointsMultiplier = 1.5m
                        });
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Points")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("LoyaltyTransactions");
                });

            modelBuilder.Entity("POSSystem.Models.Payment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ReferenceNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MinimumStock")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PurchasePrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("ReorderPoint")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SKU")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SellingPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("TrackBatches")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<int?>("UnitOfMeasureId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("UnitOfMeasureId");

                    b.ToTable("Products", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.ProductAlert", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AlertType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ReferenceType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductAlerts", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.ProductBarcode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("Barcode")
                        .IsUnique();

                    b.HasIndex("ProductId");

                    b.ToTable("ProductBarcodes");
                });

            modelBuilder.Entity("POSSystem.Models.ProductPrice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("TEXT");

                    b.Property<string>("PriceType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPrices");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GrandTotal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentReference")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Pending");

                    b.Property<decimal>("Subtotal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("PurchaseOrders", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PurchaseOrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("UnitCost")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderId");

                    b.ToTable("PurchaseOrderItems", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5828),
                            Description = "System Administrator",
                            IsActive = 1,
                            Name = "Admin"
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5833),
                            Description = "Store Manager",
                            IsActive = 1,
                            Name = "Manager"
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5836),
                            Description = "Store Cashier",
                            IsActive = 1,
                            Name = "Cashier"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountPaid")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Change")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DiscountAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GrandTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SaleDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Subtotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("TEXT");

                    b.Property<int>("TotalItems")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("UserId");

                    b.ToTable("Sales", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.SaleHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ActionDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("AdjustmentAmount")
                        .HasColumnType("TEXT");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.HasIndex("UserId");

                    b.ToTable("SaleHistory", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.SaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Total")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SaleId");

                    b.ToTable("SaleItems");
                });

            modelBuilder.Entity("POSSystem.Models.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Suppliers", (string)null);
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("BaseUnitId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("ConversionFactor")
                        .HasPrecision(18, 6)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BaseUnitId");

                    b.ToTable("UnitsOfMeasure", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Abbreviation = "pc",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8293),
                            IsActive = 1,
                            Name = "Piece",
                            Type = "Unit"
                        },
                        new
                        {
                            Id = 2,
                            Abbreviation = "kg",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8297),
                            IsActive = 1,
                            Name = "Kilogram",
                            Type = "Weight"
                        },
                        new
                        {
                            Id = 3,
                            Abbreviation = "g",
                            BaseUnitId = 2,
                            ConversionFactor = 0.001m,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8307),
                            IsActive = 1,
                            Name = "Gram",
                            Type = "Weight"
                        },
                        new
                        {
                            Id = 4,
                            Abbreviation = "L",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8310),
                            IsActive = 1,
                            Name = "Liter",
                            Type = "Volume"
                        },
                        new
                        {
                            Id = 5,
                            Abbreviation = "mL",
                            BaseUnitId = 4,
                            ConversionFactor = 0.001m,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8315),
                            IsActive = 1,
                            Name = "Milliliter",
                            Type = "Volume"
                        },
                        new
                        {
                            Id = 6,
                            Abbreviation = "box",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8318),
                            IsActive = 1,
                            Name = "Box",
                            Type = "Package"
                        },
                        new
                        {
                            Id = 7,
                            Abbreviation = "ctn",
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8321),
                            IsActive = 1,
                            Name = "Carton",
                            Type = "Package"
                        },
                        new
                        {
                            Id = 8,
                            Abbreviation = "dz",
                            BaseUnitId = 1,
                            ConversionFactor = 12m,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8352),
                            IsActive = 1,
                            Name = "Dozen",
                            Type = "Unit"
                        });
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PhotoPath")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("default-user.png");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("Users", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 952, DateTimeKind.Local).AddTicks(103),
                            Email = "<EMAIL>",
                            FirstName = "Admin",
                            IsActive = 1,
                            LastName = "User",
                            Password = "admin123",
                            Phone = "************",
                            PhotoPath = "default-user.png",
                            RoleId = 1,
                            UpdatedAt = new DateTime(2025, 2, 5, 12, 43, 35, 952, DateTimeKind.Local).AddTicks(109),
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("Audit", b =>
                {
                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany("Audits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.BatchStock", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Batches")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.HasOne("POSSystem.Models.User", "ClosedBy")
                        .WithMany()
                        .HasForeignKey("ClosedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.User", "OpenedBy")
                        .WithMany()
                        .HasForeignKey("OpenedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ClosedBy");

                    b.Navigation("OpenedBy");
                });

            modelBuilder.Entity("POSSystem.Models.CashTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.CashDrawer", null)
                        .WithMany("Transactions")
                        .HasForeignKey("CashDrawerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "PerformedBy")
                        .WithMany()
                        .HasForeignKey("PerformedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("PerformedBy");
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.HasOne("POSSystem.Models.Category", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentCategoryId");

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.HasOne("POSSystem.Models.LoyaltyTier", "LoyaltyTier")
                        .WithMany()
                        .HasForeignKey("LoyaltyTierId");

                    b.Navigation("LoyaltyTier");
                });

            modelBuilder.Entity("POSSystem.Models.Discount", b =>
                {
                    b.HasOne("POSSystem.Models.User", "AppliedByUser")
                        .WithMany()
                        .HasForeignKey("AppliedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.DiscountType", "DiscountType")
                        .WithMany("Discounts")
                        .HasForeignKey("DiscountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.DiscountReason", "Reason")
                        .WithMany("Discounts")
                        .HasForeignKey("ReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany()
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.SaleItem", "SaleItem")
                        .WithMany()
                        .HasForeignKey("SaleItemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AppliedByUser");

                    b.Navigation("ApprovedByUser");

                    b.Navigation("DiscountType");

                    b.Navigation("Reason");

                    b.Navigation("Sale");

                    b.Navigation("SaleItem");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountPermission", b =>
                {
                    b.HasOne("POSSystem.Models.DiscountType", "DiscountType")
                        .WithMany("Permissions")
                        .HasForeignKey("DiscountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Role", "Role")
                        .WithMany("DiscountPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DiscountType");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("POSSystem.Models.InventoryTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("InventoryTransactions")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTier", b =>
                {
                    b.HasOne("POSSystem.Models.LoyaltyProgram", "Program")
                        .WithMany("Tiers")
                        .HasForeignKey("LoyaltyProgramId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Program");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyTransaction", b =>
                {
                    b.HasOne("POSSystem.Models.Customer", "Customer")
                        .WithMany("LoyaltyTransactions")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("POSSystem.Models.Payment", b =>
                {
                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany("Payments")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.HasOne("POSSystem.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId");

                    b.HasOne("POSSystem.Models.UnitOfMeasure", "UnitOfMeasure")
                        .WithMany("Products")
                        .HasForeignKey("UnitOfMeasureId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Category");

                    b.Navigation("Supplier");

                    b.Navigation("UnitOfMeasure");
                });

            modelBuilder.Entity("POSSystem.Models.ProductAlert", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.ProductBarcode", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Barcodes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.ProductPrice", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("PriceHistory")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.HasOne("POSSystem.Models.Supplier", "Supplier")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrderItem", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Items")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrder");
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.HasOne("POSSystem.Models.Customer", "Customer")
                        .WithMany("Sales")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany("Sales")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.SaleHistory", b =>
                {
                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany()
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Sale");

                    b.Navigation("User");
                });

            modelBuilder.Entity("POSSystem.Models.SaleItem", b =>
                {
                    b.HasOne("POSSystem.Models.Product", "Product")
                        .WithMany("Sales")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POSSystem.Models.Sale", "Sale")
                        .WithMany("Items")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.HasOne("POSSystem.Models.UnitOfMeasure", "BaseUnit")
                        .WithMany("DerivedUnits")
                        .HasForeignKey("BaseUnitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("BaseUnit");
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.HasOne("POSSystem.Models.Role", "UserRole")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("POSSystem.Models.CashDrawer", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("POSSystem.Models.Category", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("POSSystem.Models.Customer", b =>
                {
                    b.Navigation("LoyaltyTransactions");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountReason", b =>
                {
                    b.Navigation("Discounts");
                });

            modelBuilder.Entity("POSSystem.Models.DiscountType", b =>
                {
                    b.Navigation("Discounts");

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("POSSystem.Models.LoyaltyProgram", b =>
                {
                    b.Navigation("Tiers");
                });

            modelBuilder.Entity("POSSystem.Models.Product", b =>
                {
                    b.Navigation("Barcodes");

                    b.Navigation("Batches");

                    b.Navigation("InventoryTransactions");

                    b.Navigation("PriceHistory");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("POSSystem.Models.PurchaseOrder", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("POSSystem.Models.Role", b =>
                {
                    b.Navigation("DiscountPermissions");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("POSSystem.Models.Sale", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("POSSystem.Models.Supplier", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("POSSystem.Models.UnitOfMeasure", b =>
                {
                    b.Navigation("DerivedUnits");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("POSSystem.Models.User", b =>
                {
                    b.Navigation("Audits");

                    b.Navigation("Sales");
                });
#pragma warning restore 612, 618
        }
    }
}
