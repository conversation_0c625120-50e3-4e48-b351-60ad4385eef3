using System;
using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    public class LessThanZeroConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue < 0;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class GreaterThanZeroConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue > 0;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class TransactionTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string transactionType)
            {
                string paramType = parameter as string;
                
                switch (transactionType.ToLower())
                {
                    case "sale":
                        return paramType == "Type" ? "Sale" : "Cash Sale";
                    case "in":
                        return paramType == "Type" ? "Cash In" : "Cash Added";
                    case "out":
                    case "payout":
                        return paramType == "Type" ? "Cash Out" : "Cash Removed";
                    default:
                        return transactionType;
                }
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class TransactionTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string transactionType)
            {
                switch (transactionType.ToLower())
                {
                    case "sale":
                        return PackIconKind.CashRegister;
                    case "in":
                        return PackIconKind.CashPlus;
                    case "out":
                    case "payout":
                        return PackIconKind.CashMinus;
                    default:
                        return PackIconKind.Cash;
                }
            }
            return PackIconKind.Cash;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class DrawerStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                switch (status.ToLower())
                {
                    case "open":
                        return "Cash Drawer is Open";
                    case "closed":
                        return "Cash Drawer is Closed";
                    default:
                        return status;
                }
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 