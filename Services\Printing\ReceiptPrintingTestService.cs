using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services.Printing
{
    /// <summary>
    /// Service for testing receipt printing functionality
    /// </summary>
    public class ReceiptPrintingTestService
    {
        private readonly IEnhancedReceiptPrintService _receiptPrintService;
        private readonly ILogger<ReceiptPrintingTestService> _logger;

        public ReceiptPrintingTestService(IEnhancedReceiptPrintService receiptPrintService, ILogger<ReceiptPrintingTestService> logger = null)
        {
            _receiptPrintService = receiptPrintService ?? throw new ArgumentNullException(nameof(receiptPrintService));
            _logger = logger;
        }

        /// <summary>
        /// Test PDF export functionality with a sample sale
        /// </summary>
        public async Task<bool> TestPdfExportAsync(string testFilePath = null)
        {
            try
            {
                LogInfo("Starting PDF export test");

                // Create test sale
                var testSale = CreateTestSale();

                // Use default path if none provided
                if (string.IsNullOrEmpty(testFilePath))
                {
                    var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                    var testDirectory = Path.Combine(documentsPath, "POS Receipts", "Test");
                    Directory.CreateDirectory(testDirectory);
                    testFilePath = Path.Combine(testDirectory, $"TestReceipt_{DateTime.Now:yyyyMMdd_HHmmss}.xps");
                }

                LogInfo($"Testing PDF export to: {testFilePath}");

                // Test the export
                bool success = await _receiptPrintService.SaveReceiptAsPdfAsync(testSale, testFilePath);

                if (success)
                {
                    LogInfo("PDF export test completed successfully");
                    
                    // Verify file was created
                    if (File.Exists(testFilePath))
                    {
                        var fileInfo = new FileInfo(testFilePath);
                        LogInfo($"Test file created: {testFilePath} (Size: {fileInfo.Length} bytes)");
                        
                        // Show success message
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show(
                                $"PDF export test successful!\n\nFile created: {testFilePath}\nSize: {fileInfo.Length} bytes\n\nYou can open this file with Windows XPS Viewer to verify the content.",
                                "Test Successful",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        });
                        
                        return true;
                    }
                    else
                    {
                        LogError("PDF export reported success but file was not created");
                        return false;
                    }
                }
                else
                {
                    LogError("PDF export test failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error during PDF export test: {ex.Message}", ex);
                
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        $"PDF export test failed with error:\n{ex.Message}\n\nPlease check the logs for more details.",
                        "Test Failed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                });
                
                return false;
            }
        }

        /// <summary>
        /// Test receipt printing system configuration
        /// </summary>
        public async Task<bool> TestSystemConfigurationAsync()
        {
            try
            {
                LogInfo("Testing receipt printing system configuration");

                // Test printer configurations
                var printerConfigs = await _receiptPrintService.GetPrinterConfigurationsAsync();
                LogInfo($"Found {printerConfigs.Count} printer configurations");

                // Test receipt templates
                var templates = await _receiptPrintService.GetReceiptTemplatesAsync();
                LogInfo($"Found {templates.Count} receipt templates");

                // Test print job history (with test sale)
                var testSale = CreateTestSale();
                var printJobs = await _receiptPrintService.GetPrintJobHistoryAsync(testSale.Id);
                LogInfo($"Found {printJobs.Count} print jobs for test sale");

                bool configurationValid = printerConfigs.Count > 0 && templates.Count > 0;

                if (configurationValid)
                {
                    LogInfo("System configuration test passed");
                    
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(
                            $"Receipt printing system configuration test passed!\n\n" +
                            $"Printer Configurations: {printerConfigs.Count}\n" +
                            $"Receipt Templates: {templates.Count}\n" +
                            $"Print Jobs: {printJobs.Count}",
                            "Configuration Test Passed",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    });
                }
                else
                {
                    LogError("System configuration test failed - missing configurations or templates");
                    
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(
                            "Receipt printing system configuration test failed!\n\n" +
                            "Please ensure the database is properly initialized with default receipt printing data.",
                            "Configuration Test Failed",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    });
                }

                return configurationValid;
            }
            catch (Exception ex)
            {
                LogError($"Error during system configuration test: {ex.Message}", ex);
                return false;
            }
        }

        private Sale CreateTestSale()
        {
            return new Sale
            {
                Id = 999999,
                InvoiceNumber = $"TEST-{DateTime.Now:yyyyMMddHHmmss}",
                SaleDate = DateTime.Now,
                PaymentMethod = "Cash",
                PaymentStatus = "Paid",
                Status = "Completed",
                Subtotal = 150.00m,
                DiscountAmount = 15.00m,
                TaxAmount = 13.50m,
                GrandTotal = 148.50m,
                AmountPaid = 150.00m,
                Items = new List<SaleItem>
                {
                    new SaleItem
                    {
                        Id = 1,
                        ProductId = 1,
                        Product = new Product { Name = "Test Product 1", Barcode = "*********" },
                        Quantity = 2,
                        UnitPrice = 50.00m,
                        Total = 100.00m
                    },
                    new SaleItem
                    {
                        Id = 2,
                        ProductId = 2,
                        Product = new Product { Name = "Test Product 2", Barcode = "*********" },
                        Quantity = 1,
                        UnitPrice = 50.00m,
                        Total = 50.00m
                    }
                },
                Customer = new Customer
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Customer",
                    Phone = "************",
                    Email = "<EMAIL>"
                }
            };
        }

        private void LogInfo(string message)
        {
            _logger?.LogInformation(message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT TEST] {message}");
        }

        private void LogError(string message, Exception ex = null)
        {
            _logger?.LogError(ex, message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT TEST ERROR] {message}");
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT TEST ERROR] Exception: {ex}");
            }
        }
    }
}
