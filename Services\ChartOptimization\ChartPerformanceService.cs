using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using LiveCharts;
using LiveCharts.Wpf;
using LiveCharts.Defaults;
using POSSystem.Models;

namespace POSSystem.Services.ChartOptimization
{
    /// <summary>
    /// ✅ NEW: Chart performance optimization service for LiveCharts
    /// Implements data virtualization, sampling, and performance optimizations for large datasets
    /// </summary>
    public class ChartPerformanceService
    {
        private const int MAX_CHART_POINTS = 50; // Reduced from 100 for better memory usage
        private const int SAMPLING_THRESHOLD = 200; // Reduced from 500 for earlier sampling
        private readonly Dictionary<string, ChartCache> _chartCache = new();
        private const int MAX_CACHE_SIZE = 10; // Limit cache size to prevent memory bloat

        /// <summary>
        /// ✅ OPTIMIZATION: Create optimized sales trend series with data sampling
        /// </summary>
        public SeriesCollection CreateOptimizedSalesTrendSeries(List<Sale> sales, ChartType chartType)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // Group sales by date and calculate totals
                var salesByDate = sales
                    .Where(s => s.SaleDate != null)
                    .GroupBy(s => s.SaleDate.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        Total = g.Sum(s => s.GrandTotal),
                        Count = g.Count()
                    })
                    .OrderBy(x => x.Date)
                    .ToList();

                // Apply data sampling if needed
                var optimizedData = ApplyDataSampling(salesByDate, MAX_CHART_POINTS);

                var series = new SeriesCollection();

                switch (chartType)
                {
                    case ChartType.Line:
                        series.Add(CreateOptimizedLineSeries("Sales", optimizedData.Select(d => new DateTimePoint(d.Date, (double)d.Total))));
                        break;

                    case ChartType.Column:
                        series.Add(CreateOptimizedColumnSeries("Sales", optimizedData.Select(d => (double)d.Total)));
                        break;

                    case ChartType.Area:
                        var areaSeries = new LineSeries // Use LineSeries instead of AreaSeries for compatibility
                        {
                            Title = "Sales",
                            Values = new ChartValues<DateTimePoint>(optimizedData.Select(d => new DateTimePoint(d.Date, (double)d.Total))),
                            PointGeometry = null, // Remove points for better performance
                            LineSmoothness = 0.3,
                            Fill = System.Windows.Media.Brushes.LightBlue
                        };
                        series.Add(areaSeries);
                        break;
                }

                stopwatch.Stop();
                Debug.WriteLine($"ChartPerformanceService: Created optimized sales trend series in {stopwatch.ElapsedMilliseconds}ms ({optimizedData.Count} points)");

                return series;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating optimized sales trend series: {ex.Message}");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create optimized product performance chart
        /// </summary>
        public SeriesCollection CreateOptimizedProductSeries(List<ProductPerformance> products, int maxProducts = 20)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Take top performing products and optimize data
                var topProducts = products
                    .OrderByDescending(p => p.Revenue)
                    .Take(maxProducts)
                    .ToList();

                var series = new SeriesCollection();

                // Create column series for revenue
                var revenueSeries = new ColumnSeries
                {
                    Title = "Revenue",
                    Values = new ChartValues<double>(topProducts.Select(p => (double)p.Revenue)),
                    DataLabels = false, // Disable data labels for performance
                    MaxColumnWidth = 40
                };

                series.Add(revenueSeries);

                stopwatch.Stop();
                Debug.WriteLine($"ChartPerformanceService: Created optimized product series in {stopwatch.ElapsedMilliseconds}ms ({topProducts.Count} products)");

                return series;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating optimized product series: {ex.Message}");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create optimized line series with performance settings
        /// </summary>
        private LineSeries CreateOptimizedLineSeries(string title, IEnumerable<DateTimePoint> data)
        {
            return new LineSeries
            {
                Title = title,
                Values = new ChartValues<DateTimePoint>(data),
                PointGeometry = null, // Remove points for better performance
                LineSmoothness = 0, // Disable smoothing for performance
                StrokeThickness = 2,
                Fill = System.Windows.Media.Brushes.Transparent
            };
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create optimized column series with performance settings
        /// </summary>
        private ColumnSeries CreateOptimizedColumnSeries(string title, IEnumerable<double> data)
        {
            return new ColumnSeries
            {
                Title = title,
                Values = new ChartValues<double>(data),
                DataLabels = false, // Disable data labels for performance
                MaxColumnWidth = 50
            };
        }

        /// <summary>
        /// ✅ CORE: Apply data sampling to reduce chart points for performance
        /// </summary>
        private List<T> ApplyDataSampling<T>(List<T> data, int maxPoints)
        {
            if (data.Count <= maxPoints)
                return data;

            // Calculate sampling interval
            var interval = (double)data.Count / maxPoints;
            var sampledData = new List<T>();

            for (int i = 0; i < maxPoints; i++)
            {
                var index = (int)(i * interval);
                if (index < data.Count)
                {
                    sampledData.Add(data[index]);
                }
            }

            Debug.WriteLine($"ChartPerformanceService: Sampled {data.Count} points down to {sampledData.Count} points");
            return sampledData;
        }

        /// <summary>
        /// ✅ CACHING: Cache chart data to avoid regeneration
        /// </summary>
        public void CacheChartData(string key, SeriesCollection series, TimeSpan? ttl = null)
        {
            try
            {
                var expiry = DateTime.Now.Add(ttl ?? TimeSpan.FromMinutes(5));
                _chartCache[key] = new ChartCache
                {
                    Series = series,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = expiry
                };

                Debug.WriteLine($"ChartPerformanceService: Cached chart data for key '{key}' until {expiry:HH:mm:ss}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error caching chart data: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CACHING: Get cached chart data if available and not expired
        /// </summary>
        public SeriesCollection GetCachedChartData(string key)
        {
            try
            {
                if (_chartCache.TryGetValue(key, out var cache) && cache.ExpiresAt > DateTime.Now)
                {
                    Debug.WriteLine($"ChartPerformanceService: Using cached chart data for key '{key}'");
                    return cache.Series;
                }

                // Remove expired cache
                if (cache != null)
                {
                    _chartCache.Remove(key);
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting cached chart data: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// ✅ CLEANUP: Clear expired cache entries
        /// </summary>
        public void ClearExpiredCache()
        {
            try
            {
                var now = DateTime.Now;
                var expiredKeys = _chartCache
                    .Where(kvp => kvp.Value.ExpiresAt <= now)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _chartCache.Remove(key);
                }

                if (expiredKeys.Count > 0)
                {
                    Debug.WriteLine($"ChartPerformanceService: Cleared {expiredKeys.Count} expired cache entries");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing expired cache: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Create hourly sales pattern chart with optimized data
        /// </summary>
        public SeriesCollection CreateHourlyPatternSeries(List<Sale> sales)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Group by hour and calculate averages
                var hourlyData = sales
                    .Where(s => s.SaleDate != null)
                    .GroupBy(s => s.SaleDate.Hour)
                    .Select(g => new
                    {
                        Hour = g.Key,
                        AverageSales = g.Average(s => s.GrandTotal),
                        TransactionCount = g.Count()
                    })
                    .OrderBy(x => x.Hour)
                    .ToList();

                var series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "Hourly Sales Pattern",
                        Values = new ChartValues<double>(hourlyData.Select(h => (double)h.AverageSales)),
                        DataLabels = false,
                        MaxColumnWidth = 30
                    }
                };

                stopwatch.Stop();
                Debug.WriteLine($"ChartPerformanceService: Created hourly pattern series in {stopwatch.ElapsedMilliseconds}ms");

                return series;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating hourly pattern series: {ex.Message}");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// ✅ MEMORY: Dispose of chart resources properly
        /// </summary>
        public void DisposeChartResources(SeriesCollection series)
        {
            try
            {
                if (series != null)
                {
                    foreach (var s in series)
                    {
                        s.Values?.Clear();
                    }
                    series.Clear();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error disposing chart resources: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// ✅ CACHE: Chart cache entry
    /// </summary>
    internal class ChartCache
    {
        public SeriesCollection Series { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    /// <summary>
    /// ✅ ENUM: Chart types for optimization
    /// </summary>
    public enum ChartType
    {
        Line,
        Column,
        Area,
        Pie
    }
}
