<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.LoyaltyProgramView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToTextConverter x:Key="BooleanToTextConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:StringEqualsVisibilityConverter x:Key="StringEqualsVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="0,10,0,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Programs List -->
        <StackPanel Grid.Column="0" Margin="0,0,10,0">
            <TextBlock Text="{DynamicResource LoyaltyPrograms}" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
            <ListView ItemsSource="{Binding Programs}"
                      SelectedItem="{Binding SelectedProgram}"
                      Height="300"
                      Margin="0,0,0,10">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <StackPanel>
                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{DynamicResource PointsPerDA}"/>
                                <TextBlock Text="{Binding PointsPerDollar}"/>
                                <TextBlock Text="{DynamicResource ValuePerPointDA}"/>
                                <TextBlock Text="{Binding MonetaryValuePerPoint}"/>
                                <TextBlock Text="{DynamicResource Separator}"/>
                                <TextBlock Text="{Binding IsActive, Converter={StaticResource BooleanToTextConverter}, ConverterParameter='Active,Inactive'}"/>
                            </StackPanel>
                        </StackPanel>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>

            <!-- Program Details -->
            <Grid Visibility="{Binding IsEditingProgram, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel>
                    <TextBox Text="{Binding EditingProgram.Name, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource ProgramNameHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingProgram.Description, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource DescriptionHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingProgram.PointsPerDollar, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                             materialDesign:HintAssist.Hint="{DynamicResource PointsPerDollarHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"
                             PreviewTextInput="NumberValidationTextBox"/>

                    <TextBox Text="{Binding EditingProgram.MonetaryValuePerPoint, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                             materialDesign:HintAssist.Hint="{DynamicResource ValuePerPointHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             PreviewTextInput="NumberValidationTextBox"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingProgram.ExpiryMonths, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource PointsExpiryHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             PreviewTextInput="IntegerValidationTextBox"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingProgram.MinimumPointsRedemption, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                             materialDesign:HintAssist.Hint="{DynamicResource MinimumPointsRedemptionHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             PreviewTextInput="NumberValidationTextBox"
                             Margin="0,0,0,10"/>

                    <CheckBox IsChecked="{Binding EditingProgram.IsActive}"
                              Content="{DynamicResource ProgramActive}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <Button Content="{DynamicResource SaveProgram}"
                                Command="{Binding SaveProgramCommand}"
                                Style="{StaticResource MaterialDesignFlatButton}"
                                Margin="0,0,10,0"/>
                        <Button Content="{DynamicResource Cancel}"
                                Command="{Binding CancelEditProgramCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <Button Content="{DynamicResource AddNewProgram}"
                    Command="{Binding AddProgramCommand}"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    HorizontalAlignment="Left"
                    Margin="0,10,0,0"
                    Visibility="{Binding IsEditingProgram, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

            <StackPanel Orientation="Horizontal"
                        Margin="0,10,0,0"
                        Visibility="{Binding IsEditingProgram, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <Button Content="{DynamicResource EditProgram}"
                        Command="{Binding EditProgramCommand}"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="0,0,10,0"/>
                <Button Content="{DynamicResource DeactivateProgram}"
                        Command="{Binding DeactivateProgramCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"/>
                <Button Content="{DynamicResource RemoveProgram}"
                        Command="{Binding RemoveProgramCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Foreground="Red"
                        BorderBrush="Red"/>
            </StackPanel>
        </StackPanel>

        <!-- Tiers List -->
        <StackPanel Grid.Column="1" Margin="10,0,0,0">
            <TextBlock Text="{DynamicResource ProgramTiers}" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
            <ListView ItemsSource="{Binding Tiers}"
                      SelectedItem="{Binding SelectedTier}"
                      Height="300"
                      Margin="0,0,0,10">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <StackPanel>
                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{DynamicResource MinPoints}"/>
                                <TextBlock Text="{Binding MinimumPoints}"/>
                                <TextBlock Text="{DynamicResource Multiplier}"/>
                                <TextBlock Text="{Binding PointsMultiplier}"/>
                            </StackPanel>
                            <TextBlock Text="{Binding Benefits}" TextWrapping="Wrap"/>
                        </StackPanel>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>

            <!-- Tier Details -->
            <Grid Visibility="{Binding IsEditingTier, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel>
                    <TextBox Text="{Binding EditingTier.Name, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource TierNameHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingTier.MinimumPoints, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource MinimumPointsRequiredHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingTier.PointsMultiplier, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource PointsMultiplierHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <TextBox Text="{Binding EditingTier.Benefits, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="{DynamicResource BenefitsDescriptionHint}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,10"/>

                    <StackPanel Orientation="Horizontal">
                        <Button Content="{DynamicResource SaveTier}"
                                Command="{Binding SaveTierCommand}"
                                Style="{StaticResource MaterialDesignFlatButton}"
                                Margin="0,0,10,0"/>
                        <Button Content="{DynamicResource Cancel}"
                                Command="{Binding CancelEditTierCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <Button Content="{DynamicResource AddNewTier}"
                    Command="{Binding AddTierCommand}"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    HorizontalAlignment="Left"
                    Margin="0,10,0,0"
                    Visibility="{Binding IsEditingTier, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
        </StackPanel>
    </Grid>
</UserControl> 