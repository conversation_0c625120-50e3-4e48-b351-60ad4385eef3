using POSSystem.Models;

namespace POSSystem.Models
{
    public class Report
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; } // Sales/Inventory/Financial
        public DateTime GeneratedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Parameters { get; set; } // JSON string of report parameters
        public string Format { get; set; } // PDF/Excel/CSV
        public string StoragePath { get; set; }
        public int GeneratedById { get; set; }
        public virtual User GeneratedBy { get; set; }
    }
} 