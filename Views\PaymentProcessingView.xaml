<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.PaymentProcessingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="450"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <Style x:Key="PaymentMethodButton" TargetType="RadioButton" BasedOn="{StaticResource MaterialDesignRadioButton}">
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="GroupName" Value="PaymentMethod"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- Use the built-in WPF converter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MaxWidth="520"
                         MaxHeight="700"
                         Margin="16">
        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header with Icon and Title -->
            <StackPanel Grid.Row="0"
                      Orientation="Horizontal"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="CreditCardOutline"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       Margin="0,0,10,0"/>
                <TextBlock Text="{DynamicResource ProcessPayment}"
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         FontWeight="Medium"
                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                         VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Scrollable Content Area -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         MaxHeight="500">
                <StackPanel>

                    <!-- Payment Amount Summary -->
                    <materialDesign:Card Background="{DynamicResource PrimaryHueDarkBrush}"
                                       UniformCornerRadius="8"
                                       materialDesign:ElevationAssist.Elevation="Dp2"
                                       Margin="0,0,0,20">
                <Grid Margin="16,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Subtotal -->
                    <TextBlock Grid.Row="0" Grid.Column="0"
                             Text="{DynamicResource Subtotal}"
                             FontSize="14"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             Margin="0,0,0,6"/>
                    <TextBlock Grid.Row="0" Grid.Column="1"
                             Text="{Binding Subtotal, StringFormat={}{0:N2} DA}"
                             FontSize="14"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             HorizontalAlignment="Right"
                             Margin="0,0,0,6"/>

                    <!-- Discount -->
                    <TextBlock Grid.Column="0" Grid.Row="1"
                             Text="{DynamicResource Discount}"
                             FontSize="14"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             Margin="0,0,0,8"/>
                    <TextBlock Grid.Column="1" Grid.Row="1"
                             Text="{Binding Discount, StringFormat={}{0:N2} DA}"
                             FontSize="14"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             HorizontalAlignment="Right"
                             Margin="0,0,0,8"/>

                    <!-- Separator -->
                    <Separator Grid.Row="1"
                             Background="{DynamicResource MaterialDesignPaper}"
                             Opacity="0.3"
                             Margin="0,17,0,28" Grid.RowSpan="2"/>

                    <!-- Total Amount Due -->
                    <TextBlock Grid.Row="2" Grid.Column="0"
                             Text="{DynamicResource AmountDue}"
                             FontWeight="Bold"
                             FontSize="16"
                             Foreground="{DynamicResource SecondaryHueLightBrush}"
                             Margin="0,10,0,0"/>
                    <TextBlock Grid.Row="2" Grid.Column="1"
                             Text="{Binding AmountDue, StringFormat={}{0:N2} DA}"
                             FontWeight="Bold"
                             FontSize="20"
                             Foreground="{DynamicResource SecondaryHueLightBrush}"
                             HorizontalAlignment="Right"
                             Margin="0,10,0,0"/>
                </Grid>
            </materialDesign:Card>

                    <!-- Payment Method & Amount Section -->
                    <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Side: Payment Methods -->
                <StackPanel Grid.Column="0" Margin="0,0,12,0">
                    <TextBlock Text="{DynamicResource PaymentMethod}"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             FontWeight="Medium"
                             Margin="0,0,0,10"/>

                    <!-- Payment Methods -->
                    <StackPanel>
                        <!-- Cash Option -->
                        <RadioButton IsChecked="{Binding IsCashSelected}"
                                   GroupName="PaymentMethod"
                                   Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cash" 
                                                       Width="20" 
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="{DynamicResource Cash}" 
                                         VerticalAlignment="Center"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </RadioButton>

                        <!-- Unpaid Option -->
                        <RadioButton IsChecked="{Binding IsUnpaidSelected}"
                                   GroupName="PaymentMethod"
                                   Margin="0,0,0,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CalendarClock" 
                                                       Width="20" 
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="{DynamicResource Unpaid}" 
                                         VerticalAlignment="Center"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </RadioButton>
                    </StackPanel>
                </StackPanel>

                <!-- Right Side: Cash Panel -->
                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                    <TextBlock Text="{DynamicResource AmountTendered}"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             FontWeight="Medium"
                             Margin="0,0,0,10"/>

                    <!-- ✅ PERFORMANCE FIX: Changed to LostFocus to reduce excessive property change notifications -->
                    <TextBox Text="{Binding AmountTendered, UpdateSourceTrigger=LostFocus}"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="{DynamicResource EnterAmount}"
                           FontSize="16"
                           Margin="0,0,0,8">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding ProcessPaymentCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- Quick Amounts -->
                    <WrapPanel Orientation="Horizontal" Margin="0,8,0,0">
                        <WrapPanel.Style>
                            <Style TargetType="WrapPanel">
                                <Setter Property="Visibility" Value="Visible"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsCashSelected}" Value="False">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </WrapPanel.Style>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Height="36" 
                              Margin="0,0,8,8"
                              Command="{Binding SetPredefinedAmountCommand}"
                              CommandParameter="500"
                              Background="{DynamicResource MaterialDesignPaper}"
                              BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cash" 
                                                       Width="16" 
                                                       Height="16"
                                                       Margin="0,0,4,0"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="500 DA" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Height="36" 
                              Margin="0,0,8,8"
                              Command="{Binding SetPredefinedAmountCommand}"
                              CommandParameter="1000"
                              Background="{DynamicResource MaterialDesignPaper}"
                              BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cash" 
                                                       Width="16" 
                                                       Height="16"
                                                       Margin="0,0,4,0"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="1000 DA" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Height="36" 
                              Margin="0,0,8,8"
                              Command="{Binding SetPredefinedAmountCommand}"
                              CommandParameter="2000"
                              Background="{DynamicResource MaterialDesignPaper}"
                              BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cash" 
                                                       Width="16" 
                                                       Height="16"
                                                       Margin="0,0,4,0"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="2000 DA" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </WrapPanel>
                    
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsCashSelected}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                </StackPanel>
                
                <!-- Unpaid Sales Fields -->
                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                    <TextBlock Text="{DynamicResource DueDate}"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             FontWeight="Medium"
                             Margin="0,0,0,10"/>
                    
                    <DatePicker materialDesign:HintAssist.Hint="{DynamicResource SelectDueDate}"
                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                              SelectedDate="{Binding DueDate}"
                              DisplayDateStart="{Binding MinDueDate, Mode=OneWay}"
                              Margin="0,0,0,16"/>
                    
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsUnpaidSelected}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                </StackPanel>
            </Grid>

                    <!-- Customer Required Warning -->
                    <materialDesign:Card Background="#FFF8E1" Margin="0,0,0,16" UniformCornerRadius="4">
                <StackPanel Orientation="Horizontal" Margin="8">
                    <materialDesign:PackIcon Kind="Alert" Foreground="#FF9800" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="{DynamicResource CustomerRequiredMessage}"
                            TextWrapping="Wrap" VerticalAlignment="Center" Foreground="#FF6F00"/>
                </StackPanel>
                <materialDesign:Card.Style>
                    <Style TargetType="materialDesign:Card" BasedOn="{StaticResource {x:Type materialDesign:Card}}">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding IsUnpaidSelected}" Value="True"/>
                                    <Condition Binding="{Binding ParentViewModel.SelectedCustomer}" Value="{x:Null}"/>
                                </MultiDataTrigger.Conditions>
                                <Setter Property="Visibility" Value="Visible"/>
                            </MultiDataTrigger>
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding IsUnpaidSelected}" Value="True"/>
                                    <Condition Binding="{Binding ParentViewModel.SelectedCustomer.Id}" Value="-1"/>
                                </MultiDataTrigger.Conditions>
                                <Setter Property="Visibility" Value="Visible"/>
                            </MultiDataTrigger>
                        </Style.Triggers>
                    </Style>
                </materialDesign:Card.Style>
            </materialDesign:Card>

                    <!-- Change Amount (if applicable) -->
                    <Grid Margin="0,0,0,16">
                <Grid.Style>
                    <Style TargetType="Grid">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsChangeVisible}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Grid.Style>
                <Border Background="{DynamicResource MaterialDesignBackground}" 
                      BorderBrush="{DynamicResource PrimaryHueLightBrush}"
                      CornerRadius="4"
                      Padding="14,10">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CurrencyUsd" 
                                               Width="22" 
                                               Height="22"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>

                        <TextBlock Text="{DynamicResource Change}" 
                                 FontWeight="SemiBold"
                                 FontSize="15"
                                 Margin="0,0,10,0"
                                 VerticalAlignment="Center"/>

                        <TextBlock Text="{Binding Change, StringFormat={}{0:N2} DA}"
                                 FontWeight="SemiBold"
                                 FontSize="16"
                                 Foreground="{Binding ChangeTextColor}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

                    <!-- Customer Information with optional Loyalty -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="0,14,0,14">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Customer Info -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Account" 
                                               Width="22" 
                                               Height="22"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                        <TextBlock Text="{Binding CustomerName}"
                                 VerticalAlignment="Center"
                                 FontSize="15"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                    </StackPanel>

                    <!-- Add Customer Button -->
                    <Button Grid.Column="1"
                          Width="32"
                          Height="32"
                          Margin="0,0,8,0"
                          Command="{Binding LookupCustomerCommand}">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                <Style.Triggers>
                                    <!-- Highlight button when unpaid is selected and no customer -->
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsUnpaidSelected}" Value="True"/>
                                            <Condition Binding="{Binding ParentViewModel.SelectedCustomer}" Value="{x:Null}"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FFF8E1"/>
                                        <Setter Property="BorderBrush" Value="#FF9800"/>
                                        <Setter Property="BorderThickness" Value="1"/>
                                    </MultiDataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsUnpaidSelected}" Value="True"/>
                                            <Condition Binding="{Binding ParentViewModel.SelectedCustomer.Id}" Value="-1"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FFF8E1"/>
                                        <Setter Property="BorderBrush" Value="#FF9800"/>
                                        <Setter Property="BorderThickness" Value="1"/>
                                    </MultiDataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Button.ToolTip>
                            <ToolTip>
                                <StackPanel>
                                    <TextBlock Text="{DynamicResource SelectCustomer}" FontWeight="Bold"/>
                                    <TextBlock Text="{DynamicResource CustomerRequiredMessage}" 
                                             Visibility="{Binding IsUnpaidSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </ToolTip>
                        </Button.ToolTip>
                        <materialDesign:PackIcon Kind="AccountPlus" 
                                               Width="18" 
                                               Height="18"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Button>

                    <!-- Loyalty Points (if applicable) -->
                    <StackPanel Grid.Column="2" 
                              Orientation="Horizontal"
                              Visibility="{Binding HasLoyaltyCustomer, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="Gift" 
                                               Width="20" 
                                               Height="20"
                                               VerticalAlignment="Center"
                                               Margin="0,0,4,0"
                                               Foreground="{DynamicResource SecondaryHueMidBrush}"/>

                        <TextBlock Text="{Binding LoyaltyPointsText}"
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource SecondaryHueMidBrush}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

                    <!-- Receipt Printing Options -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="0,14,0,14">
                <StackPanel>
                    <TextBlock Text="{DynamicResource ReceiptOptions}"
                             Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                             FontWeight="Medium"
                             Margin="0,0,0,8"/>

                    <StackPanel Orientation="Horizontal">
                        <!-- Print Receipt Checkbox -->
                        <CheckBox IsChecked="{Binding PrintReceiptEnabled}"
                                Content="Print Receipt"
                                Style="{StaticResource MaterialDesignCheckBox}"
                                Margin="0,0,16,0"
                                VerticalAlignment="Center">
                            <CheckBox.ToolTip>
                                <ToolTip>
                                    <TextBlock Text="{DynamicResource AutomaticallyPrintReceipt}"/>
                                </ToolTip>
                            </CheckBox.ToolTip>
                        </CheckBox>

                        <!-- Show Print Dialog Checkbox -->
                        <CheckBox IsChecked="{Binding ShowPrintDialog}"
                                Content="Show Print Dialog"
                                Style="{StaticResource MaterialDesignCheckBox}"
                                Margin="0,0,16,0"
                                VerticalAlignment="Center"
                                IsEnabled="{Binding PrintReceiptEnabled}">
                            <CheckBox.ToolTip>
                                <ToolTip>
                                    <TextBlock Text="{DynamicResource ShowPrinterSelectionDialog}"/>
                                </ToolTip>
                            </CheckBox.ToolTip>
                        </CheckBox>

                        <!-- Save as PDF Checkbox -->
                        <CheckBox IsChecked="{Binding SaveAsPdf}"
                                Content="Save as PDF"
                                Style="{StaticResource MaterialDesignCheckBox}"
                                VerticalAlignment="Center"
                                IsEnabled="{Binding PrintReceiptEnabled}">
                            <CheckBox.ToolTip>
                                <ToolTip>
                                    <TextBlock Text="{DynamicResource SaveReceiptAsPDFBackup}"/>
                                </ToolTip>
                            </CheckBox.ToolTip>
                        </CheckBox>
                        </StackPanel>
                    </StackPanel>
                </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons (Fixed at bottom) -->
            <StackPanel Grid.Row="2"
                      Orientation="Horizontal"
                      HorizontalAlignment="Right"
                      Margin="0,16,0,0">
                <Button Content="{DynamicResource Cancel}"
                      Command="{Binding CancelCommand}"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,12,0"
                      Width="120"
                      Height="40"/>

                <Button Content="{DynamicResource Confirm}"
                      Command="{Binding ProcessPaymentCommand}"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{DynamicResource PrimaryHueMidBrush}"
                      Foreground="{DynamicResource MaterialDesignPaper}"
                      IsEnabled="{Binding CanProcessPayment}"
                      Width="120"
                      Height="40">
                </Button>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl> 