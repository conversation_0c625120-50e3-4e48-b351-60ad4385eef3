using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.Models;

namespace POSSystem.Converters
{
    public class TotalStockConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Product product)
            {
                // ✅ FIX: Use StockQuantity directly since it's already calculated correctly during loading
                // This avoids additional database queries that GetTotalStockDecimal() would make
                var stockDecimal = product.StockQuantity;
                var stockDisplay = stockDecimal % 1 == 0 ? stockDecimal.ToString("F0") : stockDecimal.ToString("N3");
                return $"Stock: {stockDisplay}";
            }
            return "Stock: 0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 