using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Performance Test Data Configuration
    /// 
    /// This class provides methods to configure and set up test data for performance testing.
    /// It integrates with the existing test data infrastructure and provides different
    /// data volume configurations for various performance testing scenarios.
    /// </summary>
    public static class PerformanceTestDataConfiguration
    {
        /// <summary>
        /// Data volume configurations for different test scenarios
        /// </summary>
        public enum DataVolumeLevel
        {
            Light,      // Small dataset for quick tests
            Medium,     // Medium dataset for standard performance tests
            Heavy,      // Large dataset for stress testing
            Extreme     // Very large dataset for maximum load testing
        }

        /// <summary>
        /// Configuration settings for different data volume levels
        /// </summary>
        public static readonly Dictionary<DataVolumeLevel, DataVolumeConfig> VolumeConfigurations = 
            new Dictionary<DataVolumeLevel, DataVolumeConfig>
            {
                [DataVolumeLevel.Light] = new DataVolumeConfig
                {
                    ProductCount = 500,
                    CustomerCount = 200,
                    SalesCount = 1000,
                    DaysOfHistory = 30,
                    Description = "Light load for quick performance tests"
                },
                [DataVolumeLevel.Medium] = new DataVolumeConfig
                {
                    ProductCount = 2000,
                    CustomerCount = 1000,
                    SalesCount = 5000,
                    DaysOfHistory = 60,
                    Description = "Medium load for standard performance testing"
                },
                [DataVolumeLevel.Heavy] = new DataVolumeConfig
                {
                    ProductCount = 10000,
                    CustomerCount = 5000,
                    SalesCount = 25000,
                    DaysOfHistory = 90,
                    Description = "Heavy load for stress testing"
                },
                [DataVolumeLevel.Extreme] = new DataVolumeConfig
                {
                    ProductCount = 50000,
                    CustomerCount = 20000,
                    SalesCount = 100000,
                    DaysOfHistory = 180,
                    Description = "Extreme load for maximum stress testing"
                }
            };

        /// <summary>
        /// Sets up performance test data using the existing SQL scripts
        /// </summary>
        public static async Task SetupPerformanceTestDataAsync(POSDbContext context, DataVolumeLevel volumeLevel, 
            ILogger logger = null)
        {
            var config = VolumeConfigurations[volumeLevel];
            var stopwatch = Stopwatch.StartNew();

            logger?.LogInformation($"Setting up performance test data: {config.Description}");
            logger?.LogInformation($"Target: {config.ProductCount} products, {config.CustomerCount} customers, " +
                                 $"{config.SalesCount} sales over {config.DaysOfHistory} days");

            try
            {
                // Clear existing test data
                await ClearExistingTestDataAsync(context);

                // Set up categories first
                await SetupCategoriesAsync(context);

                // Set up products
                await SetupProductsAsync(context, config.ProductCount);

                // Set up customers
                await SetupCustomersAsync(context, config.CustomerCount);

                // Set up sales with realistic distribution
                await SetupSalesAsync(context, config.SalesCount, config.DaysOfHistory);

                // Set up additional test data
                await SetupAdditionalTestDataAsync(context, config);

                stopwatch.Stop();
                logger?.LogInformation($"Performance test data setup completed in {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error setting up performance test data");
                throw;
            }
        }

        /// <summary>
        /// Uses the existing SQL performance test script for maximum data volume
        /// </summary>
        public static async Task SetupUsingExistingSQLScriptAsync(string databasePath, ILogger logger = null)
        {
            logger?.LogInformation("Setting up performance test data using existing SQL script");

            try
            {
                var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", 
                    "TestDatabase", "Performance_Test_Data.sql");

                if (!File.Exists(scriptPath))
                {
                    throw new FileNotFoundException($"Performance test SQL script not found at: {scriptPath}");
                }

                // Create backup of current database
                var backupPath = databasePath.Replace(".db", "_backup_before_perf.db");
                if (File.Exists(databasePath))
                {
                    File.Copy(databasePath, backupPath, true);
                    logger?.LogInformation($"Database backup created: {backupPath}");
                }

                // Execute the SQL script
                await ExecuteSQLScriptAsync(databasePath, scriptPath, logger);

                logger?.LogInformation("Performance test data setup completed using SQL script");
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error setting up performance test data using SQL script");
                throw;
            }
        }

        private static async Task ClearExistingTestDataAsync(POSDbContext context)
        {
            // Clear test data that might interfere with performance tests
            await context.Database.ExecuteSqlRawAsync(
                "DELETE FROM SaleItems WHERE SaleId IN (SELECT Id FROM Sales WHERE InvoiceNumber LIKE 'TEST-%' OR InvoiceNumber LIKE 'PERF-%')");
            await context.Database.ExecuteSqlRawAsync(
                "DELETE FROM Sales WHERE InvoiceNumber LIKE 'TEST-%' OR InvoiceNumber LIKE 'PERF-%'");
            await context.Database.ExecuteSqlRawAsync(
                "DELETE FROM Products WHERE Name LIKE 'Test Product%' OR SKU LIKE 'TEST%'");
            await context.Database.ExecuteSqlRawAsync(
                "DELETE FROM Customers WHERE Name LIKE 'Test Customer%' OR Email LIKE '%@test.com'");
        }

        private static async Task SetupCategoriesAsync(POSDbContext context)
        {
            var categories = new List<Category>
            {
                new Category { Name = "Electronics", Description = "Electronic devices and accessories", IsActive = true },
                new Category { Name = "Clothing", Description = "Apparel and fashion items", IsActive = true },
                new Category { Name = "Food & Beverages", Description = "Food and drink items", IsActive = true },
                new Category { Name = "Books & Media", Description = "Books, magazines, and media", IsActive = true },
                new Category { Name = "Home & Garden", Description = "Home improvement and garden supplies", IsActive = true },
                new Category { Name = "Sports & Outdoors", Description = "Sports equipment and outdoor gear", IsActive = true },
                new Category { Name = "Health & Beauty", Description = "Health and beauty products", IsActive = true },
                new Category { Name = "Automotive", Description = "Car parts and accessories", IsActive = true },
                new Category { Name = "Toys & Games", Description = "Toys and gaming products", IsActive = true },
                new Category { Name = "Office Supplies", Description = "Office and business supplies", IsActive = true }
            };

            // Only add categories that don't already exist
            foreach (var category in categories)
            {
                var exists = await context.Categories.AnyAsync(c => c.Name == category.Name);
                if (!exists)
                {
                    context.Categories.Add(category);
                }
            }

            await context.SaveChangesAsync();
        }

        private static async Task SetupProductsAsync(POSDbContext context, int productCount)
        {
            var categories = await context.Categories.ToListAsync();
            var random = new Random(42); // Fixed seed for consistent tests
            var products = new List<Product>();

            for (int i = 1; i <= productCount; i++)
            {
                var category = categories[random.Next(categories.Count)];
                var basePrice = 5 + random.Next(1, 1000);

                products.Add(new Product
                {
                    Name = $"Performance Test Product {i:D6}",
                    SKU = $"PERF{i:D8}",
                    Description = $"Performance test product {i} in {category.Name} category",
                    SellingPrice = basePrice,
                    PurchasePrice = basePrice * 0.6m,
                    StockQuantity = 50 + random.Next(0, 500),
                    CategoryId = category.Id,
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddDays(-random.Next(0, 365))
                });

                // Add in batches to avoid memory issues
                if (products.Count >= 1000)
                {
                    context.Products.AddRange(products);
                    await context.SaveChangesAsync();
                    products.Clear();
                }
            }

            // Add remaining products
            if (products.Count > 0)
            {
                context.Products.AddRange(products);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SetupCustomersAsync(POSDbContext context, int customerCount)
        {
            var random = new Random(42);
            var customers = new List<Customer>();

            for (int i = 1; i <= customerCount; i++)
            {
                customers.Add(new Customer
                {
                    Name = $"Performance Test Customer {i:D6}",
                    Email = $"perftest{i:D6}@example.com",
                    Phone = $"555-{random.Next(1000, 9999)}",
                    Address = $"{random.Next(100, 9999)} Performance Test Street",
                    City = "Test City",
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddDays(-random.Next(0, 365))
                });

                // Add in batches
                if (customers.Count >= 1000)
                {
                    context.Customers.AddRange(customers);
                    await context.SaveChangesAsync();
                    customers.Clear();
                }
            }

            // Add remaining customers
            if (customers.Count > 0)
            {
                context.Customers.AddRange(customers);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SetupSalesAsync(POSDbContext context, int salesCount, int daysOfHistory)
        {
            var products = await context.Products.Where(p => p.Name.StartsWith("Performance Test")).ToListAsync();
            var customers = await context.Customers.Where(c => c.Name.StartsWith("Performance Test")).ToListAsync();
            var random = new Random(42);

            var batchSize = 500;
            for (int batch = 0; batch < salesCount; batch += batchSize)
            {
                var sales = new List<Sale>();
                var currentBatchSize = Math.Min(batchSize, salesCount - batch);

                for (int i = 0; i < currentBatchSize; i++)
                {
                    var saleId = batch + i + 1;
                    var customer = customers[random.Next(customers.Count)];
                    var saleDate = DateTime.Now.AddDays(-random.Next(0, daysOfHistory));

                    var sale = new Sale
                    {
                        CustomerId = customer.Id,
                        SaleDate = saleDate,
                        CreatedAt = saleDate,
                        Status = "Completed",
                        PaymentStatus = random.Next(0, 10) > 8 ? "Unpaid" : "Paid",
                        InvoiceNumber = $"PERF-{saleId:D8}",
                        SaleItems = CreateSaleItems(products, random)
                    };

                    // Calculate totals
                    sale.Subtotal = sale.SaleItems.Sum(item => item.Quantity * item.UnitPrice);
                    sale.TaxAmount = sale.Subtotal * 0.1m; // 10% tax
                    sale.GrandTotal = sale.Subtotal + sale.TaxAmount;

                    sales.Add(sale);
                }

                context.Sales.AddRange(sales);
                await context.SaveChangesAsync();
            }
        }

        private static List<SaleItem> CreateSaleItems(List<Product> products, Random random)
        {
            var itemCount = random.Next(1, 8); // 1-7 items per sale
            var saleItems = new List<SaleItem>();

            for (int i = 0; i < itemCount; i++)
            {
                var product = products[random.Next(products.Count)];
                var quantity = random.Next(1, 5);

                saleItems.Add(new SaleItem
                {
                    ProductId = product.Id,
                    Quantity = quantity,
                    UnitPrice = product.SellingPrice,
                    TotalPrice = quantity * product.SellingPrice
                });
            }

            return saleItems;
        }

        private static async Task SetupAdditionalTestDataAsync(POSDbContext context, DataVolumeConfig config)
        {
            // Add any additional test data setup here (loyalty programs, discounts, etc.)
            await Task.CompletedTask;
        }

        private static async Task ExecuteSQLScriptAsync(string databasePath, string scriptPath, ILogger logger)
        {
            // This would execute the SQL script using SQLite command line or similar
            // For now, we'll log that this functionality would be implemented
            logger?.LogInformation($"Would execute SQL script: {scriptPath} on database: {databasePath}");
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Configuration for data volume levels
    /// </summary>
    public class DataVolumeConfig
    {
        public int ProductCount { get; set; }
        public int CustomerCount { get; set; }
        public int SalesCount { get; set; }
        public int DaysOfHistory { get; set; }
        public string Description { get; set; }
    }
}
