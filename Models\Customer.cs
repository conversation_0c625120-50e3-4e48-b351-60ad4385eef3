using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace POSSystem.Models
{
    public class Customer
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public string FirstName { get; set; }
        
        [Required]
        public string LastName { get; set; }
        
        [NotMapped]
        public string Name => $"{FirstName} {LastName}";
        
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public bool IsActive { get; set; } = true;
        public string LoyaltyCode { get; set; }
        public decimal LoyaltyPoints { get; set; }
        public DateTime? LastVisit { get; set; }
        public int TotalVisits { get; set; }
        public decimal TotalSpent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? LoyaltyTierId { get; set; }
        [ForeignKey("LoyaltyTierId")]
        public virtual LoyaltyTier LoyaltyTier { get; set; }
        public ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public ICollection<LoyaltyTransaction> LoyaltyTransactions { get; set; } = new List<LoyaltyTransaction>();

        // Add computed property for full name
        public string FullName => $"{FirstName} {LastName}";
    }
} 