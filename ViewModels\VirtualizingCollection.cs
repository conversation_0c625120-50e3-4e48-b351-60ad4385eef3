using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Windows;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// A collection that provides virtualization for large datasets to improve performance.
    /// Only loads a subset of items into memory and provides methods to load more as needed.
    /// </summary>
    /// <typeparam name="T">The type of items in the collection</typeparam>
    public class VirtualizingCollection<T> : ObservableCollection<T>
    {
        private readonly List<T> _sourceItems;
        private readonly int _pageSize;
        private int _currentOffset;
        private int _virtualCount;
        private bool _isVirtualizing;

        /// <summary>
        /// Creates a new virtualizing collection.
        /// </summary>
        /// <param name="sourceItems">The source list of items</param>
        /// <param name="pageSize">The number of items to load at once</param>
        public VirtualizingCollection(List<T> sourceItems, int pageSize = 20)
        {
            _sourceItems = sourceItems ?? new List<T>();
            _pageSize = pageSize;
            _virtualCount = _sourceItems.Count;
            _isVirtualizing = _virtualCount > _pageSize * 2;
            
            // If we're not virtualizing (small collection), add all items
            if (!_isVirtualizing)
            {
                foreach (var item in _sourceItems)
                {
                    Add(item);
                }
            }
            else
            {
                // Otherwise, add the first page
                LoadItems(0, _pageSize);
            }
        }

        /// <summary>
        /// Total count of all items (including those not loaded)
        /// </summary>
        public int TotalCount => _virtualCount;
        
        /// <summary>
        /// Whether the collection is currently virtualizing
        /// </summary>
        public bool IsVirtualizing => _isVirtualizing;

        /// <summary>
        /// Loads a specific range of items
        /// </summary>
        /// <param name="offset">The starting index</param>
        /// <param name="count">The number of items to load</param>
        public void LoadItems(int offset, int count)
        {
            if (!_isVirtualizing) return; // No need to virtualize small collections
            
            _currentOffset = offset;
            int endIndex = Math.Min(offset + count, _virtualCount);
            
            Application.Current.Dispatcher.Invoke(() => {
                this.Clear();
                for (int i = offset; i < endIndex; i++)
                {
                    if (i < _sourceItems.Count)
                    {
                        this.Add(_sourceItems[i]);
                    }
                }
            });
        }

        /// <summary>
        /// Loads more items after the current ones
        /// </summary>
        /// <param name="count">Number of additional items to load</param>
        public void LoadMoreItems(int count)
        {
            if (!_isVirtualizing) return;
            
            int currentCount = this.Count;
            int newEndIndex = Math.Min(_currentOffset + currentCount + count, _virtualCount);
            
            Application.Current.Dispatcher.Invoke(() => {
                for (int i = _currentOffset + currentCount; i < newEndIndex; i++)
                {
                    if (i < _sourceItems.Count)
                    {
                        this.Add(_sourceItems[i]);
                    }
                }
            });
        }

        /// <summary>
        /// Loads all items, disabling virtualization
        /// </summary>
        public void LoadAllItems()
        {
            if (!_isVirtualizing) return;
            
            // Switch off virtualization and load all items
            _isVirtualizing = false;
            
            Application.Current.Dispatcher.Invoke(() => {
                this.Clear();
                foreach (var item in _sourceItems)
                {
                    this.Add(item);
                }
            });
        }

        /// <summary>
        /// Gets a copy of all the source items, even those not loaded
        /// </summary>
        public List<T> GetAllItems()
        {
            return new List<T>(_sourceItems);
        }
        
        /// <summary>
        /// Gets a sorted view of the top N items based on a sorting function
        /// </summary>
        public List<T> GetTopItems(Func<T, IComparable> sortKeySelector, int count, bool descending = true)
        {
            if (descending)
                return _sourceItems.OrderByDescending(sortKeySelector).Take(count).ToList();
            else
                return _sourceItems.OrderBy(sortKeySelector).Take(count).ToList();
        }
    }
} 