<Window x:Class="POSSystem.Views.DebugLogViewer"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Debug Log Viewer" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" Background="#F0F0F0">
            <Button Name="RefreshButton" Content="🔄 Refresh" Margin="5" Padding="10,5" Click="RefreshButton_Click"/>
            <Button Name="OpenLogsButton" Content="📁 Open Logs Folder" Margin="5" Padding="10,5" Click="OpenLogsButton_Click"/>
            <Button Name="PerformanceButton" Content="⚡ Performance Only" Margin="5" Padding="10,5" Click="PerformanceButton_Click"/>
            <Button Name="TailButton" Content="📄 Last 50 Lines" Margin="5" Padding="10,5" Click="TailButton_Click"/>
            <Separator Margin="10,0"/>
            <TextBox Name="SearchBox" Width="200" Margin="5" VerticalAlignment="Center" 
                     Text="Search logs..." GotFocus="SearchBox_GotFocus" LostFocus="SearchBox_LostFocus"/>
            <Button Name="SearchButton" Content="🔍 Search" Margin="5" Padding="10,5" Click="SearchButton_Click"/>
        </StackPanel>
        
        <!-- Tab Control for different log views -->
        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="📋 Latest Debug Log">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Name="DebugLogTextBox" 
                             IsReadOnly="True" 
                             FontFamily="Consolas" 
                             FontSize="11"
                             Background="#1E1E1E" 
                             Foreground="#DCDCDC"
                             TextWrapping="NoWrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="⚡ Performance Log">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Name="PerformanceLogTextBox" 
                             IsReadOnly="True" 
                             FontFamily="Consolas" 
                             FontSize="11"
                             Background="#1E1E1E" 
                             Foreground="#DCDCDC"
                             TextWrapping="NoWrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="🔍 Search Results">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Name="SearchResultsTextBox" 
                             IsReadOnly="True" 
                             FontFamily="Consolas" 
                             FontSize="11"
                             Background="#1E1E1E" 
                             Foreground="#DCDCDC"
                             TextWrapping="NoWrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="📊 Log Summary">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Name="SummaryTextBox" 
                             IsReadOnly="True" 
                             FontFamily="Consolas" 
                             FontSize="11"
                             Background="#1E1E1E" 
                             Foreground="#DCDCDC"
                             TextWrapping="Wrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="Ready"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="LogInfoText" Text=""/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
