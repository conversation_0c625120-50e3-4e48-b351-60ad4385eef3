using System;
using System.Threading.Tasks;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Simple console application to run POS System performance tests
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("POS System Performance Test Runner");
            Console.WriteLine("==================================");
            Console.WriteLine();

            try
            {
                using var testRunner = new SimplePerformanceTestRunner();
                await testRunner.RunPerformanceTestsAsync();
                
                Console.WriteLine();
                Console.WriteLine("Performance tests completed successfully!");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running performance tests: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }
    }
}
