using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Security.Cryptography;
using System.Text;
using System.Diagnostics;
using System.Text.Json;
using System.Threading.Tasks;

namespace POSLicensingSystem.ViewModels
{
    public class LicenseGeneratorViewModel : ViewModelBase
    {
        private string _businessName = string.Empty;
        private string _systemId = string.Empty;
        private string _licenseType = string.Empty;
        private string _terminalCount = "1";
        private DateTime _expirationDate = DateTime.Now.AddYears(1);
        private string _generatedLicenseKey = string.Empty;
        private bool _isGenerating = false;
        private string _errorMessage = string.Empty;
        private bool _hasError;
        
        public string BusinessName
        {
            get => _businessName;
            set => SetProperty(ref _businessName, value);
        }
        
        public string SystemId
        {
            get => _systemId;
            set => SetProperty(ref _systemId, value);
        }
        
        public string LicenseType
        {
            get => _licenseType;
            set => SetProperty(ref _licenseType, value);
        }
        
        public string TerminalCount
        {
            get => _terminalCount;
            set => SetProperty(ref _terminalCount, value);
        }
        
        public DateTime ExpirationDate
        {
            get => _expirationDate;
            set => SetProperty(ref _expirationDate, value);
        }
        
        public string GeneratedLicenseKey
        {
            get => _generatedLicenseKey;
            set => SetProperty(ref _generatedLicenseKey, value);
        }
        
        public bool IsGenerating
        {
            get => _isGenerating;
            set => SetProperty(ref _isGenerating, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                HasError = !string.IsNullOrEmpty(value);
            }
        }

        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }
        
        public List<string> LicenseTypes { get; } = new List<string> 
        { 
            "Standard", 
            "Professional", 
            "Enterprise" 
        };
        
        // Commands
        public ICommand GenerateLicenseCommand { get; }
        public ICommand CopyLicenseKeyCommand { get; }
        public ICommand CloseCommand { get; }
        
        public LicenseGeneratorViewModel()
        {
            // Set default license type
            LicenseType = LicenseTypes.First();
            
            // Initialize commands
            GenerateLicenseCommand = new RelayCommand(ExecuteGenerateLicense, CanExecuteGenerateLicense);
            CopyLicenseKeyCommand = new RelayCommand(ExecuteCopyLicenseKey, _ => !string.IsNullOrEmpty(GeneratedLicenseKey) && !IsGenerating);
            CloseCommand = new RelayCommand(_ => Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive)?.Close(), _ => !IsGenerating);
        }
        
        private bool CanExecuteGenerateLicense(object? parameter)
        {
            if (IsGenerating)
                return false;
                
            if (string.IsNullOrWhiteSpace(BusinessName) || 
                string.IsNullOrWhiteSpace(SystemId) ||
                string.IsNullOrWhiteSpace(LicenseType) ||
                string.IsNullOrWhiteSpace(TerminalCount))
                return false;
            
            // Validate terminal count is a number between 1-9
            if (!int.TryParse(TerminalCount, out int count) || count < 1 || count > 9)
                return false;
            
            // Validate expiration date is in the future
            if (ExpirationDate <= DateTime.Now)
                return false;
            
            return true;
        }
        
        private async void ExecuteGenerateLicense(object? parameter)
        {
            try
            {
                // Clear any previous errors
                ErrorMessage = string.Empty;
                
                // Set flag to indicate generation is in progress
                IsGenerating = true;
                
                // Clear previous license key
                GeneratedLicenseKey = string.Empty;
                
                // Add a small delay to show processing feedback
                await Task.Delay(1000);
                
                // Basic validation
                if (string.IsNullOrWhiteSpace(BusinessName))
                {
                    ErrorMessage = "Business name is required";
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(SystemId))
                {
                    ErrorMessage = "System ID is required";
                    return;
                }
                
                if (!int.TryParse(TerminalCount, out int count) || count < 1 || count > 9)
                {
                    ErrorMessage = "Terminal count must be a number between 1 and 9";
                    return;
                }
                
                if (ExpirationDate <= DateTime.Now)
                {
                    ErrorMessage = "Expiration date must be in the future";
                    return;
                }
                
                // Generate license key based on input parameters
                GeneratedLicenseKey = GenerateLicenseKey();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error generating license key: {ex.Message}";
                Debug.WriteLine($"License generation error: {ex}");
            }
            finally
            {
                // Reset generation flag
                IsGenerating = false;
            }
        }
        
        private void ExecuteCopyLicenseKey(object? parameter)
        {
            try
            {
                Clipboard.SetText(GeneratedLicenseKey);
                MessageBox.Show("License key copied to clipboard!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error copying to clipboard: {ex.Message}";
                Debug.WriteLine($"Clipboard error: {ex}");
            }
        }
        
        private string GenerateLicenseKey()
        {
            try
            {
                Debug.WriteLine("=== Starting license key generation ===");
                
                // Create a license data object
                var licenseData = new Dictionary<string, object>
                {
                    { "BusinessName", BusinessName.Trim() },
                    { "ExpirationDate", ExpirationDate },
                    { "Type", GetLicenseTypeValue() },
                    { "TerminalCount", int.Parse(TerminalCount) },
                    { "HardwareId", SystemId.Trim() }
                };
                
                Debug.WriteLine($"License data - Business: '{BusinessName}', Expiration: {ExpirationDate}, Type: {LicenseType}, Terminals: {TerminalCount}, System ID: {SystemId}");
                
                // Convert to JSON
                string jsonLicense = JsonSerializer.Serialize(licenseData);
                Debug.WriteLine($"JSON license data: {jsonLicense}");
                
                // Convert JSON to bytes
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonLicense);
                Debug.WriteLine($"JSON bytes length: {jsonBytes.Length}");
                
                // Convert JSON bytes to Base64 string (more compact than hex)
                string jsonBase64 = Convert.ToBase64String(jsonBytes);
                Debug.WriteLine($"JSON as Base64: {jsonBase64}");
                Debug.WriteLine($"Base64 length: {jsonBase64.Length}");
                
                // Add a signature marker
                const string SIGNATURE_MARKER = "##POS2024##";
                
                // Calculate signature for the license data
                string signature = CalculateSignature(jsonLicense);
                Debug.WriteLine($"Calculated signature: {signature}");
                
                // Combine all parts
                string licenseKey = jsonBase64 + SIGNATURE_MARKER + signature;
                
                // Format with dashes for readability every 5 characters
                var formattedKey = new StringBuilder();
                for (int i = 0; i < licenseKey.Length; i += 5)
                {
                    if (i > 0)
                        formattedKey.Append('-');
                    
                    // Handle the case where we're at the end and have fewer than 5 characters left
                    int charsToTake = Math.Min(5, licenseKey.Length - i);
                    formattedKey.Append(licenseKey.Substring(i, charsToTake));
                }
                
                string result = formattedKey.ToString();
                Debug.WriteLine($"Final formatted license key: {result}");
                Debug.WriteLine($"Final key length: {result.Length} characters");
                Debug.WriteLine("=== License key generation complete ===");
                
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GenerateLicenseKey: {ex.Message}");
                Debug.WriteLine($"Exception details: {ex}");
                throw;
            }
        }
        
        private int GetLicenseTypeValue()
        {
            return LicenseType switch
            {
                "Standard" => 0,
                "Professional" => 1,
                "Enterprise" => 2,
                _ => 0 // Default to Standard
            };
        }
        
        private string CalculateSignature(string data)
        {
            // Create a hash of the license data with a secret key
            string secretKey = "POS2024SecretKey"; // This would normally be stored securely
            string dataToHash = data + secretKey;
            
            using (var sha = SHA256.Create())
            {
                byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(dataToHash));
                
                // Use the first 16 bytes of the hash as a signature
                var signature = new StringBuilder();
                for (int i = 0; i < 16; i++)
                {
                    signature.Append(hashBytes[i].ToString("X2"));
                }
                
                return signature.ToString();
            }
        }
    }
} 