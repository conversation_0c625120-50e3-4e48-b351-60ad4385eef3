using Microsoft.EntityFrameworkCore.Migrations;

namespace POSSystem.Migrations
{
    public partial class AddProductBarcodes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create new ProductBarcodes table
            migrationBuilder.CreateTable(
                name: "ProductBarcodes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Barcode = table.Column<string>(type: "TEXT", nullable: false),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsPrimary = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductBarcodes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductBarcodes_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create index on Barcode for faster lookups
            migrationBuilder.CreateIndex(
                name: "IX_ProductBarcodes_Barcode",
                table: "ProductBarcodes",
                column: "Barcode",
                unique: true);

            // Create index on ProductId
            migrationBuilder.CreateIndex(
                name: "IX_ProductBarcodes_ProductId",
                table: "ProductBarcodes",
                column: "ProductId");

            // Migrate existing barcodes to the new table
            migrationBuilder.Sql(@"
                INSERT INTO ProductBarcodes (Barcode, ProductId, IsPrimary, CreatedAt)
                SELECT Barcode, Id, 1, datetime('now')
                FROM Products
                WHERE Barcode IS NOT NULL AND Barcode != '';
            ");

            // Remove old Barcode column
            migrationBuilder.DropColumn(
                name: "Barcode",
                table: "Products");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Add back the Barcode column
            migrationBuilder.AddColumn<string>(
                name: "Barcode",
                table: "Products",
                type: "TEXT",
                nullable: true);

            // Migrate primary barcodes back to Products table
            migrationBuilder.Sql(@"
                UPDATE Products
                SET Barcode = (
                    SELECT Barcode
                    FROM ProductBarcodes
                    WHERE ProductBarcodes.ProductId = Products.Id
                    AND IsPrimary = 1
                    LIMIT 1
                );
            ");

            // Drop the ProductBarcodes table
            migrationBuilder.DropTable(
                name: "ProductBarcodes");
        }
    }
} 