using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Helper class for general performance optimizations
    /// </summary>
    public static class PerformanceHelper
    {
        /// <summary>
        /// Enables virtualization on an ItemsControl
        /// </summary>
        /// <param name="control">The ItemsControl to optimize</param>
        public static void EnableVirtualization(ItemsControl control)
        {
            if (control == null)
                return;
                
            // Apply virtualization settings
            VirtualizingPanel.SetIsVirtualizing(control, true);
            VirtualizingPanel.SetVirtualizationMode(control, VirtualizationMode.Recycling);
            VirtualizingPanel.SetCacheLengthUnit(control, VirtualizationCacheLengthUnit.Page);
            VirtualizingPanel.SetCacheLength(control, new VirtualizationCacheLength(1, 2));
            
            // Set ScrollViewer settings if applicable
            if (control is ListBox listBox)
            {
                ScrollViewer.SetCanContentScroll(listBox, true);
                ScrollViewer.SetIsDeferredScrollingEnabled(listBox, true);
            }
        }
        
        /// <summary>
        /// Optimizes rendering for a specific UI element
        /// </summary>
        /// <param name="element">The UI element to optimize</param>
        public static void OptimizeRendering(FrameworkElement element)
        {
            if (element == null)
                return;
                
            // Apply rendering optimizations
            RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
            RenderOptions.SetClearTypeHint(element, ClearTypeHint.Enabled);
            RenderOptions.SetEdgeMode(element, EdgeMode.Aliased);
            
            // Apply bitmap caching for complex elements
            if (ShouldApplyBitmapCache(element))
            {
                element.CacheMode = new BitmapCache
                {
                    EnableClearType = true,
                    SnapsToDevicePixels = true,
                    RenderAtScale = 1.0
                };
            }
            
            // Set other optimization properties
            element.UseLayoutRounding = true;
            
            // For scrollable content, optimize scrolling
            if (element is ScrollViewer scrollViewer)
            {
                scrollViewer.CanContentScroll = true;
                scrollViewer.IsDeferredScrollingEnabled = true;
                scrollViewer.PanningMode = PanningMode.VerticalOnly;
                scrollViewer.PanningDeceleration = 0.001;
                scrollViewer.PanningRatio = 3.0;
            }
        }
        
        /// <summary>
        /// Determines if bitmap caching should be applied to an element
        /// </summary>
        /// <param name="element">The element to check</param>
        /// <returns>True if bitmap caching should be applied</returns>
        private static bool ShouldApplyBitmapCache(FrameworkElement element)
        {
            // Only apply to certain types of controls that benefit from caching
            return element is Panel ||
                  element is ItemsControl ||
                  element is Border ||
                  element is TextBlock ||
                  element.Name?.Contains("container", StringComparison.OrdinalIgnoreCase) == true ||
                  element.Name?.Contains("panel", StringComparison.OrdinalIgnoreCase) == true;
        }
        
        /// <summary>
        /// Adds event throttling to an event handler
        /// </summary>
        /// <typeparam name="T">The event args type</typeparam>
        /// <param name="handler">The original event handler</param>
        /// <param name="throttleMilliseconds">Throttle interval in milliseconds</param>
        /// <returns>A throttled event handler</returns>
        public static EventHandler<T> ThrottleEvent<T>(EventHandler<T> handler, int throttleMilliseconds = 100)
            where T : EventArgs
        {
            if (handler == null)
                return null;
                
            DateTime lastRun = DateTime.MinValue;
            
            return (sender, args) =>
            {
                DateTime now = DateTime.Now;
                if ((now - lastRun).TotalMilliseconds >= throttleMilliseconds)
                {
                    lastRun = now;
                    handler(sender, args);
                }
            };
        }
        
        /// <summary>
        /// Batch update helper for collections
        /// </summary>
        /// <param name="action">The action to perform in a batched update</param>
        /// <param name="dispatcher">Optional dispatcher to use</param>
        public static void BatchUpdate(Action action, System.Windows.Threading.Dispatcher dispatcher = null)
        {
            if (action == null)
                return;

            using (new DeferredOperationContext())
            {
                if (dispatcher != null && !dispatcher.CheckAccess())
                {
                    dispatcher.Invoke(action);
                }
                else
                {
                    action();
                }
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Efficiently replace entire ObservableCollection content with monitoring
        /// This prevents multiple PropertyChanged events during bulk updates
        /// </summary>
        public static void ReplaceCollectionContent<T>(ObservableCollection<T> collection, IEnumerable<T> newItems)
        {
            if (collection == null || newItems == null)
                return;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var itemList = newItems.ToList();

            try
            {
                // Clear collection efficiently
                collection.Clear();

                // For small collections, add all at once
                if (itemList.Count <= 50)
                {
                    foreach (var item in itemList)
                    {
                        collection.Add(item);
                    }
                }
                else
                {
                    // For larger collections, use batched approach
                    const int batchSize = 25; // Smaller batches for better responsiveness

                    for (int i = 0; i < itemList.Count; i += batchSize)
                    {
                        var batch = itemList.Skip(i).Take(batchSize);
                        foreach (var item in batch)
                        {
                            collection.Add(item);
                        }

                        // Yield control to UI thread every few batches
                        if (i % (batchSize * 2) == 0)
                        {
                            Application.Current?.Dispatcher.Invoke(() => { }, DispatcherPriority.Background);
                        }
                    }
                }
            }
            finally
            {
                stopwatch.Stop();
                if (stopwatch.ElapsedMilliseconds > 50)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ SLOW COLLECTION UPDATE: {itemList.Count} items took {stopwatch.ElapsedMilliseconds}ms");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ FAST COLLECTION UPDATE: {itemList.Count} items in {stopwatch.ElapsedMilliseconds}ms");
                }
            }
        }

        /// <summary>
        /// Execute an operation on a background thread and return to UI thread
        /// </summary>
        public static async Task<T> ExecuteOnBackgroundThread<T>(Func<T> operation, string operationName = "Background Operation")
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                var result = await Task.Run(operation);
                stopwatch.Stop();

                System.Diagnostics.Debug.WriteLine($"✅ {operationName} completed on background thread in {stopwatch.ElapsedMilliseconds}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"❌ {operationName} failed on background thread after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Execute an async operation on a background thread with performance monitoring
        /// </summary>
        public static async Task<T> ExecuteOnBackgroundThreadAsync<T>(Func<Task<T>> operation, string operationName = "Background Async Operation")
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                var result = await Task.Run(async () => await operation());
                stopwatch.Stop();

                System.Diagnostics.Debug.WriteLine($"✅ {operationName} completed on background thread in {stopwatch.ElapsedMilliseconds}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"❌ {operationName} failed on background thread after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Context for deferring operations during batch updates
        /// </summary>
        private class DeferredOperationContext : IDisposable
        {
            public DeferredOperationContext()
            {
                // Suppress layout updates during batch operations
                try
                {
                    if (Application.Current?.Dispatcher != null && Application.Current.Dispatcher.CheckAccess())
                    {
                        OperationCountIncrement();
                    }
                }
                catch
                {
                    // Ignore exceptions - we'll try to clean up in Dispose
                }
            }
            
            public void Dispose()
            {
                try
                {
                    if (Application.Current?.Dispatcher != null && Application.Current.Dispatcher.CheckAccess())
                    {
                        OperationCountDecrement();
                    }
                }
                catch
                {
                    // Ignore exceptions in cleanup
                }
            }
            
            private static void OperationCountIncrement()
            {
                // Increment deferred operation count here
                // This is a placeholder for actual implementation
            }
            
            private static void OperationCountDecrement()
            {
                // Decrement deferred operation count here
                // This is a placeholder for actual implementation
            }
        }
    }
} 