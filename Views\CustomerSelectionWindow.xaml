<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.CustomerSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:POSSystem.Converters"
        Title="{DynamicResource SelectCustomer}" Height="550" Width="800"
        WindowStartupLocation="CenterOwner"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignBackground}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        WindowStyle="ToolWindow"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- Local converters for use in this window -->
        <converters:InitialsConverter x:Key="InitialsConverter"/>
        <converters:ZeroToVisibilityConverter x:Key="ZeroToVisibilityConverter"/>
    </Window.Resources>
    
    <materialDesign:DialogHost Identifier="CustomerSelectionDialog">
        <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" 
                   Background="{DynamicResource PrimaryHueMidBrush}"
                   Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Title -->
                    <StackPanel Grid.Column="0" 
                              Orientation="Horizontal" 
                              VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountMultiple" 
                                               Width="32" 
                                               Height="32"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource MaterialDesignPaper}"
                                               Margin="0,0,16,0"/>
                        <TextBlock Text="{DynamicResource SelectCustomer}" 
                                 FontSize="24" 
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource MaterialDesignPaper}"/>
        </StackPanel>

                    <!-- Search Box -->
                    <materialDesign:Card Grid.Column="1" 
                                       Margin="32,0,0,0" 
                                       UniformCornerRadius="8"
                                       Background="{DynamicResource MaterialDesignPaper}"
                                       materialDesign:ElevationAssist.Elevation="Dp2"
                                       MinWidth="320">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <materialDesign:PackIcon Kind="Magnify" 
                                                   Grid.Column="0"
                                                   Width="20" 
                                                   Height="20"
                                                   VerticalAlignment="Center"
                                                   Margin="12,0,0,0"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <TextBox x:Name="txtSearch" 
                                   Grid.Column="1"
                                   TextChanged="TxtSearch_TextChanged"
                                   materialDesign:HintAssist.Hint="{DynamicResource SearchCustomersHint}"
                                   Style="{StaticResource MaterialDesignTextBox}"
                                   FontSize="14"
                                   BorderThickness="0"
                                   Padding="12,14"
                                   VerticalAlignment="Center"/>
                            
                            <Button x:Name="btnClearSearch"
                                  Grid.Column="2"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="{DynamicResource ClearSearch}"
                                  Width="40"
                                  Visibility="Collapsed"
                                  Click="BtnClearSearch_Click">
                                <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                            </Button>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </Border>
            
            <!-- Customer List with Cards -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Info Panel -->
                    <Border Grid.Row="0" 
                          Background="{DynamicResource SecondaryHueLightBrush}"
                          CornerRadius="6"
                          Padding="16,12"
                          Margin="0,0,0,16">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" 
                                                   Width="22" 
                                                   Height="22"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,12,0"/>
                            <TextBlock Text="{DynamicResource DoubleClickToSelectCustomer}" 
                                     VerticalAlignment="Center"
                                     TextWrapping="Wrap"
                                     Foreground="{DynamicResource PrimaryHueDarkBrush}"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Customer DataGrid with modern styling -->
                    <DataGrid Grid.Row="1" 
                            x:Name="dgCustomers"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  SelectionMode="Single"
                            MouseDoubleClick="DgCustomers_MouseDoubleClick"
                            CanUserSortColumns="True"
                            CanUserResizeColumns="True"
                            materialDesign:DataGridAssist.CellPadding="16 8"
                            materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                            BorderThickness="0"
                            Background="Transparent"
                            RowBackground="{DynamicResource MaterialDesignPaper}"
                            AlternatingRowBackground="{DynamicResource MaterialDesignBackground}">
                        
                        <DataGrid.Resources>
                            <SolidColorBrush x:Key="MaterialDesignDataGridRowHoverBackground" Color="#0F000000"/>
                        </DataGrid.Resources>
                        
                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                            </Style>
                        </DataGrid.RowStyle>
                        
            <DataGrid.Columns>
                            <DataGridTemplateColumn Width="70">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{DynamicResource PrimaryHueLightBrush}" 
                                              Width="40" 
                                              Height="40" 
                                              CornerRadius="20"
                                              HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding Path=FirstName, Converter={StaticResource InitialsConverter}}" 
                                                     VerticalAlignment="Center"
                                                     HorizontalAlignment="Center"
                                                     FontWeight="Medium"
                                                     Foreground="{DynamicResource MaterialDesignPaper}"
                                                     FontSize="16"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="{DynamicResource CustomerId}" 
                                              Binding="{Binding Id}" 
                                              Width="50"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            
                            <DataGridTextColumn Header="{DynamicResource FirstName}" 
                                              Binding="{Binding FirstName}" 
                                              Width="*"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            
                            <DataGridTextColumn Header="{DynamicResource LastName}" 
                                              Binding="{Binding LastName}" 
                                              Width="*"
                                              ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                            
                            <DataGridTemplateColumn Header="{DynamicResource Phone}" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Phone" 
                                                                   Width="16" 
                                                                   Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                   Margin="0,0,8,0"
                                                                   Opacity="0.7"/>
                                            <TextBlock Text="{Binding Phone}" 
                                                     VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTemplateColumn Header="{DynamicResource Email}" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Email" 
                                                                   Width="16" 
                                                                   Height="16" 
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                   Margin="0,0,8,0"
                                                                   Opacity="0.7"/>
                                            <TextBlock Text="{Binding Email}" 
                                                     VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTemplateColumn Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              Click="BtnSelectItem_Click"
                                              Tag="{Binding}"
                                              ToolTip="{DynamicResource SelectCustomer}">
                                            <materialDesign:PackIcon Kind="CheckCircle" Width="18" Height="18"/>
                                        </Button>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

                    <!-- No Results Message -->
                    <TextBlock Grid.Row="1"
                             Text="{DynamicResource NoCustomersFound}"
                             HorizontalAlignment="Center"
                             VerticalAlignment="Center"
                             FontSize="16"
                             Opacity="0.7"
                             Visibility="{Binding ElementName=dgCustomers, Path=Items.Count, Converter={StaticResource ZeroToVisibilityConverter}}"/>
                </Grid>
            </ScrollViewer>
            
            <!-- Action Buttons -->
            <Border Grid.Row="2" 
                   Background="{DynamicResource MaterialDesignCardBackground}"
                   materialDesign:ElevationAssist.Elevation="Dp3"
                   Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Left side button - Add New Customer -->
                    <Button Grid.Column="0"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Content="{DynamicResource AddNewCustomer}"
                           Margin="0,0,8,0"
                           Click="BtnAddNewCustomer_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountPlus" 
                                                          Width="18" 
                                                          Height="18"
                                                          VerticalAlignment="Center"
                                                          Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                    
                    <!-- Right side buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Content="{DynamicResource Cancel}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="0,0,16,0"
                               Click="BtnCancel_Click"/>
                        
                        <Button x:Name="btnSelect" 
                               Content="{DynamicResource Select}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="{DynamicResource PrimaryHueMidBrush}"
                               Foreground="{DynamicResource MaterialDesignPaper}"
                               IsEnabled="{Binding ElementName=dgCustomers, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"
                               Click="BtnSelect_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CheckCircle" 
                                                              Width="18" 
                                                              Height="18"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
        </StackPanel>
    </Grid>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window> 