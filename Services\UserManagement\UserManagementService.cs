using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ErrorHandling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BCrypt.Net;

namespace POSSystem.Services.UserManagement
{
    /// <summary>
    /// Focused service for user management operations
    /// Extracted from the large DatabaseService to improve maintainability
    /// </summary>
    public class UserManagementService : IUserManagementService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<UserManagementService> _logger;
        private readonly IErrorHandlingService _errorHandler;

        public UserManagementService(
            POSDbContext context,
            ILogger<UserManagementService> logger = null,
            IErrorHandlingService errorHandler = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _errorHandler = errorHandler;
        }

        /// <summary>
        /// Authenticate user with username and password
        /// </summary>
        public async Task<User> AuthenticateUserAsync(string username, string password)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(username))
                    throw new ArgumentException("Username cannot be empty", nameof(username));

                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("Password cannot be empty", nameof(password));

                var user = await _context.Users
                    .AsNoTracking()
                    .Include(u => u.UserRole)
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                    return null;

                // Verify password
                bool isValidPassword = false;
                try
                {
                    // Try BCrypt first (new format)
                    isValidPassword = BCrypt.Net.BCrypt.Verify(password, user.Password);
                }
                catch
                {
                    // Fallback to plain text comparison (legacy format)
                    isValidPassword = user.Password == password;
                }

                if (!isValidPassword)
                    return null;

                _logger?.LogInformation("User {Username} authenticated successfully", username);
                return user;
            }, "Authenticate User", null, false); // Don't show user message for auth failures

            return result;
        }

        /// <summary>
        /// Get all users
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Users
                    .AsNoTracking()
                    .Include(u => u.UserRole)
                    .OrderBy(u => u.Username)
                    .ToListAsync();
            }, "Get All Users", new List<User>()) ?? new List<User>();
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        public async Task<User> GetUserByIdAsync(int id)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("User ID must be greater than 0", nameof(id));

                return await _context.Users
                    .AsNoTracking()
                    .Include(u => u.UserRole)
                    .FirstOrDefaultAsync(u => u.Id == id);
            }, "Get User By ID", null) ?? null;
        }

        /// <summary>
        /// Add new user
        /// </summary>
        public async Task<int> AddUserAsync(User user, string password)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateUser(user);
                ValidatePassword(password);

                // Check for duplicate username
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == user.Username);

                if (existingUser != null)
                    throw new InvalidOperationException($"User with username '{user.Username}' already exists");

                // Hash password
                user.Password = BCrypt.Net.BCrypt.HashPassword(password);
                user.CreatedAt = DateTime.Now;
                user.UpdatedAt = DateTime.Now;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully added user {UserId} - {Username}", user.Id, user.Username);
                return user.Id;
            }, "Add User", 0);

            return result;
        }

        /// <summary>
        /// Update existing user
        /// </summary>
        public async Task<bool> UpdateUserAsync(User user)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateUser(user);

                if (user.Id <= 0)
                    throw new ArgumentException("User ID must be greater than 0 for updates", nameof(user));

                var existingUser = await _context.Users.FindAsync(user.Id);
                if (existingUser == null)
                    throw new InvalidOperationException($"User with ID {user.Id} not found");

                // Check for duplicate username if changed
                if (user.Username != existingUser.Username)
                {
                    var duplicateUser = await _context.Users
                        .FirstOrDefaultAsync(u => u.Username == user.Username && u.Id != user.Id);

                    if (duplicateUser != null)
                        throw new InvalidOperationException($"User with username '{user.Username}' already exists");
                }

                // Update properties (don't update password here - use separate method)
                existingUser.Username = user.Username;
                existingUser.FirstName = user.FirstName;
                existingUser.LastName = user.LastName;
                existingUser.Email = user.Email;
                existingUser.RoleId = user.RoleId;
                existingUser.IsActive = user.IsActive;
                existingUser.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated user {UserId} - {Username}", user.Id, user.Username);
                return true;
            }, "Update User", false);

            return result;
        }

        /// <summary>
        /// Update user password
        /// </summary>
        public async Task<bool> UpdateUserPasswordAsync(int userId, string newPassword)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (userId <= 0)
                    throw new ArgumentException("User ID must be greater than 0", nameof(userId));

                ValidatePassword(newPassword);

                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    throw new InvalidOperationException($"User with ID {userId} not found");

                // Hash new password
                user.Password = BCrypt.Net.BCrypt.HashPassword(newPassword);
                user.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated password for user {UserId} - {Username}", userId, user.Username);
                return true;
            }, "Update User Password", false);

            return result;
        }

        /// <summary>
        /// Delete user
        /// </summary>
        public async Task<bool> DeleteUserAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("User ID must be greater than 0", nameof(id));

                var user = await _context.Users.FindAsync(id);
                if (user == null)
                    throw new InvalidOperationException($"User with ID {id} not found");

                // Check if user has any sales
                var hasSales = await _context.Sales.AnyAsync(s => s.UserId == id);
                if (hasSales)
                {
                    // Soft delete - mark as inactive instead of hard delete
                    user.IsActive = false;
                    user.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Soft deleted user {UserId} - {Username} (marked as inactive)", id, user.Username);
                }
                else
                {
                    // Hard delete if no sales
                    _context.Users.Remove(user);
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Hard deleted user {UserId} - {Username}", id, user.Username);
                }

                return true;
            }, "Delete User", false);

            return result;
        }

        /// <summary>
        /// Get default user (for system operations)
        /// </summary>
        public async Task<User> GetDefaultUserAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Users
                    .AsNoTracking()
                    .Include(u => u.UserRole)
                    .FirstOrDefaultAsync(u => u.Username == "admin" || u.Id == 1);
            }, "Get Default User", null) ?? null;
        }

        /// <summary>
        /// Get user permissions
        /// </summary>
        public async Task<UserPermissions> GetUserPermissionsAsync(int userId)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (userId <= 0)
                    throw new ArgumentException("User ID must be greater than 0", nameof(userId));

                return await _context.UserPermissions
                    .AsNoTracking()
                    .FirstOrDefaultAsync(up => up.UserId == userId);
            }, "Get User Permissions", null) ?? null;
        }

        /// <summary>
        /// Update user permissions
        /// </summary>
        public async Task<bool> UpdateUserPermissionsAsync(UserPermissions permissions)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (permissions == null)
                    throw new ArgumentNullException(nameof(permissions), "Permissions cannot be null");

                if (permissions.UserId <= 0)
                    throw new ArgumentException("User ID must be greater than 0", nameof(permissions));

                var existingPermissions = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == permissions.UserId);

                if (existingPermissions == null)
                {
                    // Add new permissions
                    _context.UserPermissions.Add(permissions);
                }
                else
                {
                    // Update existing permissions
                    existingPermissions.CanManageProducts = permissions.CanManageProducts;
                    existingPermissions.CanManageCustomers = permissions.CanManageCustomers;
                    existingPermissions.CanManageUsers = permissions.CanManageUsers;
                    existingPermissions.CanViewReports = permissions.CanViewReports;
                    existingPermissions.CanAccessSettings = permissions.CanAccessSettings;
                    existingPermissions.CanApplyDiscount = permissions.CanApplyDiscount;
                    existingPermissions.CanVoidSales = permissions.CanVoidSales;
                }

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated permissions for user {UserId}", permissions.UserId);
                return true;
            }, "Update User Permissions", false);

            return result;
        }

        /// <summary>
        /// Get all roles
        /// </summary>
        public async Task<List<Role>> GetAllRolesAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Roles
                    .AsNoTracking()
                    .OrderBy(r => r.Name)
                    .ToListAsync();
            }, "Get All Roles", new List<Role>()) ?? new List<Role>();
        }

        /// <summary>
        /// Validate user data
        /// </summary>
        private void ValidateUser(User user)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user), "User cannot be null");

            if (string.IsNullOrWhiteSpace(user.Username))
                throw new ArgumentException("Username cannot be empty", nameof(user));

            if (string.IsNullOrWhiteSpace(user.FirstName) || string.IsNullOrWhiteSpace(user.LastName))
                throw new ArgumentException("First name and last name cannot be empty", nameof(user));

            if (!string.IsNullOrWhiteSpace(user.Email) && !IsValidEmail(user.Email))
                throw new ArgumentException("Invalid email format", nameof(user));

            if (user.RoleId <= 0)
                throw new ArgumentException("Role ID must be greater than 0", nameof(user));
        }

        /// <summary>
        /// Validate password
        /// </summary>
        private void ValidatePassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be empty");

            if (password.Length < 6)
                throw new ArgumentException("Password must be at least 6 characters long");
        }

        /// <summary>
        /// Simple email validation
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
