using System.Windows.Controls;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    public partial class ExpiryStatsDetailsDialog : UserControl
    {
        private readonly ExpiryStatsDetailsViewModel _viewModel;
        
        public ExpiryStatsDetailsDialog(
            RefactoredDashboardViewModel dashboardViewModel)
        {
            InitializeComponent();
            var dbService = new DatabaseService();
            _viewModel = new ExpiryStatsDetailsViewModel(dashboardViewModel, dbService);
            DataContext = _viewModel;
            
            // Load data when dialog is shown
            Loaded += async (s, e) => await _viewModel.LoadDataAsync();
        }
        
        private void CloseButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            DialogHost.CloseDialogCommand.Execute(null, null);
        }
    }
} 