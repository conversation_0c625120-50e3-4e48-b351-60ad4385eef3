using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.ViewModels;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Views.Dialogs
{
    public partial class BarcodeSearchDialog : UserControl
    {
        private readonly SaleViewModel _viewModel;
        private readonly DatabaseService _dbService;
        private readonly IProductLookupService _productLookupService;
        private readonly IAlertService _alertService;
        public Product FoundProduct { get; private set; }
        
        public BarcodeSearchDialog(SaleViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            _dbService = new DatabaseService();

            // Initialize external product lookup service
            _alertService = new SimpleAlertService();
            _productLookupService = new ProductLookupService(_alertService);

            // Set focus to the barcode textbox
            Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() => {
                txtBarcode.Focus();
                txtBarcode.SelectAll();
            }));
        }
        
        private void TxtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                SearchBarcode();
            }
        }
        
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            SearchBarcode();
        }
        
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog
            DialogHost.Close("SalesDialog");
        }
        
        private async void SearchBarcode()
        {
            string barcode = txtBarcode.Text?.Trim();

            if (string.IsNullOrWhiteSpace(barcode))
            {
                ShowError("Please enter a barcode.");
                return;
            }

            try
            {
                // First, search for product by barcode in main database
                FoundProduct = _viewModel.GetProductByBarcode(barcode);

                if (FoundProduct != null)
                {
                    // Show success message
                    ShowSuccess($"Product found: {FoundProduct.Name}");

                    // Return the found product
                    DialogHost.Close("SalesDialog", FoundProduct);
                    return;
                }

                // If not found in main database, search external ProductsDB
                ShowInfo("Searching external product database...");

                // ✅ CRITICAL FIX: Remove .Result to prevent UI thread blocking
                var externalProduct = await ((ProductLookupService)_productLookupService).LookupProductByBarcodeAsync(barcode);
                if (externalProduct != null)
                {
                    // Product found in external database - show import dialog
                    var importDialog = new ExternalProductImportDialog(externalProduct, barcode);
                    var result = await ShowExternalProductImportDialog(importDialog);

                    if (result is ExternalProductImportDialog importDialogResult && importDialogResult.OpenAddProductDialog)
                    {
                        // Close this dialog and open the Add Product dialog with pre-filled data
                        await OpenAddProductDialogWithExternalData(externalProduct, barcode);
                    }
                    else
                    {
                        ShowError("Product not imported. Please try a different barcode.");
                    }
                }
                else
                {
                    ShowError("No product found with this barcode in main or external database.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error searching for product: {ex.Message}");
            }
        }
        
        private void ShowError(string message)
        {
            txtResult.Text = message;
            txtResult.Foreground = System.Windows.Media.Brushes.Red;
            txtResult.Visibility = Visibility.Visible;
        }

        private void ShowSuccess(string message)
        {
            txtResult.Text = message;
            txtResult.Foreground = Application.Current.Resources["SecondaryHueMidBrush"] as System.Windows.Media.Brush;
            txtResult.Visibility = Visibility.Visible;
        }

        private void ShowInfo(string message)
        {
            txtResult.Text = message;
            txtResult.Foreground = System.Windows.Media.Brushes.Blue;
            txtResult.Visibility = Visibility.Visible;
        }

        private async Task<object> ShowExternalProductImportDialog(ExternalProductImportDialog importDialog)
        {
            // Try different DialogHost identifiers in order of preference
            string[] dialogIdentifiers = { "SalesDialog", "MainSalesDialog", "MainDialog" };

            foreach (string identifier in dialogIdentifiers)
            {
                try
                {
                    return await DialogHost.Show(importDialog, identifier);
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                {
                    // This DialogHost identifier doesn't exist, try the next one
                    continue;
                }
            }

            // If all DialogHost attempts fail, fall back to a simple MessageBox
            var result = MessageBox.Show(
                $"Product found in external database:\n\n" +
                $"Name: {importDialog.ExternalProduct.Name}\n" +
                $"Price: {importDialog.ExternalProduct.SellingPrice:C}\n" +
                $"Description: {importDialog.ExternalProduct.Description}\n\n" +
                $"Would you like to import this product?",
                "Product Found in External Database",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            return result == MessageBoxResult.Yes;
        }
        
        private UIElement CreateProductDetailsDialog(Product product)
        {
            // Create a new user control for product details
            var detailsControl = new StackPanel
            {
                Margin = new Thickness(16)
            };
            
            // Header
            var header = new TextBlock
            {
                Text = product.Name,
                FontSize = 20,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16)
            };
            
            // Product details
            var priceText = new TextBlock
            {
                Text = $"Price: {product.SellingPrice:N2} DA",
                Margin = new Thickness(0, 0, 0, 8)
            };
            
            var stockText = new TextBlock
            {
                Text = $"Stock: {product.StockQuantity}",
                Margin = new Thickness(0, 0, 0, 8)
            };
            
            var skuText = new TextBlock
            {
                Text = $"SKU: {product.SKU}",
                Margin = new Thickness(0, 0, 0, 8)
            };
            
            // Separator
            var separator = new Separator
            {
                Margin = new Thickness(0, 16, 0, 16)
            };
            
            // Add to Cart button
            var addButton = new Button
            {
                Content = "Add to Cart",
                HorizontalAlignment = HorizontalAlignment.Stretch,
                Margin = new Thickness(0, 8, 0, 0),
            };
            addButton.Click += (s, e) => {
                // ✅ BARCODE SCANNER FIX: Use AddToCartCommand for consistency with product cards
                if (_viewModel.AddToCartCommand.CanExecute(product))
                {
                    _viewModel.AddToCartCommand.Execute(product);
                }
                DialogHost.Close("SalesDialog");
            };
            
            // Cancel button
            var cancelButton = new Button
            {
                Content = "Close",
                Style = Application.Current.Resources["MaterialDesignOutlinedButton"] as Style,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                Margin = new Thickness(0, 8, 0, 0),
            };
            cancelButton.Click += (s, e) => {
                DialogHost.Close("SalesDialog");
            };
            
            // Add all elements to the panel
            detailsControl.Children.Add(header);
            detailsControl.Children.Add(priceText);
            detailsControl.Children.Add(stockText);
            detailsControl.Children.Add(skuText);
            detailsControl.Children.Add(separator);
            detailsControl.Children.Add(addButton);
            detailsControl.Children.Add(cancelButton);
            
            return detailsControl;
        }

        private async System.Threading.Tasks.Task ImportExternalProduct(Product externalProduct, string barcode)
        {
            try
            {
                ShowInfo("Importing product...");

                // Create a new product based on external data
                var newProduct = new Product
                {
                    Name = externalProduct.Name,
                    Description = externalProduct.Description ?? string.Empty,
                    SKU = GenerateUniqueSKU(externalProduct.Name),
                    PurchasePrice = externalProduct.PurchasePrice,
                    SellingPrice = externalProduct.SellingPrice,
                    DefaultPrice = externalProduct.SellingPrice, // Set DefaultPrice to SellingPrice
                    StockQuantity = 0, // Start with 0 stock - user needs to add inventory
                    MinimumStock = 10, // Default minimum stock
                    ReorderPoint = 5, // Default reorder point
                    LoyaltyPoints = 0, // Default loyalty points
                    IsActive = true,
                    ImageData = externalProduct.ImageData,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // Set category if available
                if (externalProduct.Category != null && !string.IsNullOrWhiteSpace(externalProduct.Category.Name))
                {
                    // Try to find existing category or create new one
                    var existingCategory = _dbService.GetCategoryByName(externalProduct.Category.Name);
                    if (existingCategory != null)
                    {
                        newProduct.CategoryId = existingCategory.Id;
                    }
                    else
                    {
                        // Create new category
                        var newCategory = new Category
                        {
                            Name = externalProduct.Category.Name,
                            IsActive = true
                        };
                        _dbService.AddCategory(newCategory);
                        newProduct.CategoryId = newCategory.Id; // AddCategory sets the ID on the object
                    }
                }
                else
                {
                    // Fallback: Use default "Imported" category
                    var defaultCategory = _dbService.GetCategoryByName("Imported") ??
                                        _dbService.GetCategoryByName("General") ??
                                        _dbService.GetCategoryByName("Uncategorized");

                    if (defaultCategory != null)
                    {
                        newProduct.CategoryId = defaultCategory.Id;
                    }
                    else
                    {
                        // Create default category if none exists
                        var importedCategory = new Category
                        {
                            Name = "Imported",
                            Description = "Products imported from external database",
                            IsActive = true
                        };
                        _dbService.AddCategory(importedCategory);
                        newProduct.CategoryId = importedCategory.Id;
                    }
                }

                // Add the product to the database
                var productId = _dbService.AddProduct(newProduct);

                // Add the barcode
                _dbService.AddBarcodeToProduct(productId, barcode, true, "Imported from external database");

                // Reload the product with full details
                FoundProduct = _dbService.GetProductById(productId);

                if (FoundProduct != null)
                {
                    ShowSuccess($"Product imported successfully: {FoundProduct.Name}");

                    // Small delay to show success message
                    await System.Threading.Tasks.Task.Delay(1000);

                    // Return the imported product
                    DialogHost.Close("SalesDialog", FoundProduct);
                }
                else
                {
                    ShowError("Product imported but could not be retrieved.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error importing product: {ex.Message}");
            }
        }

        private string GenerateUniqueSKU(string productName)
        {
            // Generate a unique SKU based on product name and timestamp
            var baseSku = productName.Length > 3 ? productName.Substring(0, 3).ToUpper() : productName.ToUpper();
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            return $"{baseSku}-{timestamp}";
        }

        // Simple implementation of IAlertService for external product lookup
        private class SimpleAlertService : IAlertService
        {
            public void CheckExpiringProducts() { }
            public void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product") { }
            public List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1) => new List<ProductAlert>();
            public List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1) => new List<ProductAlert>();
            public int GetTotalAlertsCount() => 0;
            public List<ProductAlert> GetUnreadAlerts() => new List<ProductAlert>();
            public List<ProductAlert> GetUnreadAlertsBasic() => new List<ProductAlert>();
            public int GetUnreadAlertsCount() => 0;
            public void MarkAlertAsRead(int alertId) { }
            public void MarkAllAlertsAsRead() { }
            public void ShowError(string message)
            {
                System.Windows.MessageBox.Show(message, "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            public void ClearAlertCache() { }
        }

        private async System.Threading.Tasks.Task OpenAddProductDialogWithExternalData(Product externalProduct, string barcode)
        {
            try
            {
                // Close this dialog first
                DialogHost.Close("SalesDialog");

                // Create a ProductsViewModel instance (we need this for the ProductDialog)
                var repositoryAdapter = (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter)) ??
                    throw new InvalidOperationException("RepositoryServiceAdapter not available from DI");
                var databaseService = _dbService as DatabaseService ??
                    throw new InvalidOperationException("DatabaseService not available");
                var productsViewModel = new ProductsViewModel(repositoryAdapter, new SimpleAlertService(), databaseService);
                await productsViewModel.LoadInitialData();

                // Create the ProductDialog with pre-filled external data
                var productDialog = new ProductDialog(productsViewModel, "SalesDialog");

                // Pre-fill the dialog with external product data
                productDialog.AutoFillExternalProductInformation(externalProduct);

                // Set the barcode (this should be locked to prevent conflicts)
                productDialog.SetBarcodeFromExternal(barcode);

                // Show the dialog
                var result = await DialogHost.Show(productDialog, "SalesDialog");

                if (result is Product savedProduct)
                {
                    // Product was successfully saved
                    ShowSuccess($"Product '{savedProduct.Name}' added successfully!");

                    // Small delay to show success message
                    await System.Threading.Tasks.Task.Delay(1000);

                    // Return the saved product
                    DialogHost.Close("SalesDialog", savedProduct);
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error opening Add Product dialog: {ex.Message}");
            }
        }
    }
}