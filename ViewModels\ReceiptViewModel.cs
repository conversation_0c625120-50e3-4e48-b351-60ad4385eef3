﻿using POSSystem.Models;
using System;
using System.Collections.ObjectModel;

namespace POSSystem.ViewModels
{
    public class ReceiptViewModel
    {
        public DateTime SaleDate { get; set; }
        public decimal Subtotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxRate { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal GrandTotal { get; set; }
        public ObservableCollection<Models.CartItem> SaleItems { get; set; }
        
    }
}