using System;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Linq;
using System.Windows.Media;
using System.Windows.Input;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Helper class for optimizing search operations
    /// </summary>
    public static class SearchHelper
    {
        // Default debounce delay in milliseconds
        private const int DefaultDebounceDelay = 350;
        
        /// <summary>
        /// Attaches a debounced search handler to a TextBox
        /// </summary>
        /// <param name="textBox">The TextBox to attach the handler to</param>
        /// <param name="searchAction">The action to perform when the debounced search is triggered</param>
        /// <param name="delayMilliseconds">The debounce delay in milliseconds (default: 350ms)</param>
        public static void AttachDebouncedSearch(TextBox textBox, Action<string> searchAction, int delayMilliseconds = DefaultDebounceDelay)
        {
            if (textBox == null || searchAction == null)
                return;
                
            // Create timer for debounce
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(delayMilliseconds)
            };
            
            // Stop the timer when it ticks and execute the search
            timer.Tick += (sender, args) =>
            {
                timer.Stop();
                searchAction(textBox.Text);
            };
            
            // Add text changed handler that resets the timer
            textBox.TextChanged += (sender, args) =>
            {
                timer.Stop();
                timer.Start();
            };
        }
        
        /// <summary>
        /// Creates a debounced version of an action
        /// </summary>
        /// <typeparam name="T">The type of the parameter</typeparam>
        /// <param name="action">The action to debounce</param>
        /// <param name="delayMilliseconds">The debounce delay in milliseconds</param>
        /// <returns>A debounced action</returns>
        public static Action<T> Debounce<T>(Action<T> action, int delayMilliseconds = DefaultDebounceDelay)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));
                
            DispatcherTimer timer = null;
            
            return parameter =>
            {
                // Stop existing timer
                timer?.Stop();
                
                // Create new timer
                timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(delayMilliseconds)
                };
                
                // Set up the timer to execute the action and stop itself
                timer.Tick += (sender, args) =>
                {
                    timer.Stop();
                    action(parameter);
                };
                
                // Start the timer
                timer.Start();
            };
        }
        
        /// <summary>
        /// Optimizes a search operation for a large collection
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <param name="collectionSize">The size of the collection to search</param>
        /// <returns>Whether to use full-text search or partial comparison</returns>
        public static bool ShouldUseFullTextSearch(string searchText, int collectionSize)
        {
            // For large collections, only do full text search with 3+ characters
            if (collectionSize > 10000)
                return searchText?.Length >= 3;
                
            // For medium collections, do full text search with 2+ characters
            if (collectionSize > 1000)
                return searchText?.Length >= 2;
                
            // For small collections, always do full text search
            return true;
        }
        
        /// <summary>
        /// Creates an optimized case-insensitive search predicate
        /// </summary>
        /// <typeparam name="T">The type of items in the collection</typeparam>
        /// <param name="searchText">The search text</param>
        /// <param name="propertySelector">A function that selects the property to search on</param>
        /// <returns>A predicate for searching</returns>
        public static Predicate<T> CreateSearchPredicate<T>(string searchText, Func<T, string> propertySelector)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return _ => true;
                
            var searchLower = searchText.ToLowerInvariant();
            
            return item =>
            {
                var value = propertySelector(item);
                return value != null && value.ToLowerInvariant().Contains(searchLower);
            };
        }
        
        /// <summary>
        /// Create a cached and throttled search function 
        /// </summary>
        /// <typeparam name="T">The type of search results</typeparam>
        /// <param name="searchFunc">The actual search function to throttle</param>
        /// <param name="cacheSize">The number of recent search results to cache</param>
        /// <returns>A throttled search function that uses caching</returns>
        public static Func<string, T> CreateThrottledSearch<T>(Func<string, T> searchFunc, int cacheSize = 10)
        {
            if (searchFunc == null)
                throw new ArgumentNullException(nameof(searchFunc));
                
            // Simple LRU cache
            var cache = new System.Collections.Generic.Dictionary<string, T>(cacheSize);
            var cacheQueue = new System.Collections.Generic.Queue<string>(cacheSize);
            
            DateTime lastSearchTime = DateTime.MinValue;
            const int MIN_INTERVAL_MS = 150; // Minimum time between searches
            
            return searchText => 
            {
                // Check cache first
                if (cache.TryGetValue(searchText, out T result))
                {
                    return result;
                }
                
                // Throttle search frequency
                var now = DateTime.Now;
                var elapsed = (now - lastSearchTime).TotalMilliseconds;
                
                if (elapsed < MIN_INTERVAL_MS)
                {
                    // Simple backoff strategy - sleep for a bit
                    Thread.Sleep((int)(MIN_INTERVAL_MS - elapsed));
                }
                
                // Perform the search
                lastSearchTime = DateTime.Now;
                result = searchFunc(searchText);
                
                // Add to cache
                if (cacheQueue.Count >= cacheSize)
                {
                    var oldestKey = cacheQueue.Dequeue();
                    cache.Remove(oldestKey);
                }
                
                cache[searchText] = result;
                cacheQueue.Enqueue(searchText);
                
                return result;
            };
        }
        
        /// <summary>
        /// Optimizes a TextBox specifically for barcode scanning, applying different behaviors than regular search
        /// </summary>
        /// <param name="textBox">The TextBox to optimize for barcode scanning</param>
        /// <returns>A cleanup action to remove optimizations if needed</returns>
        public static Action OptimizeForBarcodeScanning(TextBox textBox)
        {
            if (textBox == null)
                return () => { };
                
            // For barcode scanners, we want immediate operation without debounce
            // But we need to ensure the UI remains responsive
            
            // Clear any existing handlers first
            KeyEventHandler keyDownHandler = null;
            TextChangedEventHandler textChangedHandler = null;
            
            // Use SolidColorBrush instances that can be reused
            var normalBackground = textBox.Background;
            var hintBackground = new SolidColorBrush(Colors.LightYellow);
            hintBackground.Freeze(); // Freeze for better performance
            
            // Prepare the textbox for scanning
            textBox.MaxLength = 30; // Most barcodes are under 30 chars
            textBox.VerticalContentAlignment = VerticalAlignment.Center;
            
            // For better performance when scanning, we'll avoid excessive UI updates
            // by throttling the visual feedback during rapid input
            DateTime lastVisualUpdate = DateTime.MinValue;
            const int VISUAL_UPDATE_THRESHOLD_MS = 150;
            
            // Handle text changed with minimal processing
            textChangedHandler = (s, e) => 
            {
                if (DateTime.Now.Subtract(lastVisualUpdate).TotalMilliseconds > VISUAL_UPDATE_THRESHOLD_MS)
                {
                    lastVisualUpdate = DateTime.Now;
                    
                    // Only change the background if it's a potential barcode
                    if (textBox.Text.Length > 7 && textBox.Text.All(char.IsDigit))
                    {
                        textBox.Background = hintBackground;
                    }
                    else
                    {
                        textBox.Background = normalBackground;
                    }
                }
            };
            
            textBox.TextChanged += textChangedHandler;
            
            // Return a cleanup action
            return () => 
            {
                if (textChangedHandler != null)
                    textBox.TextChanged -= textChangedHandler;
                
                if (keyDownHandler != null)
                    textBox.KeyDown -= keyDownHandler;
                    
                // Reset visual states
                textBox.Background = normalBackground;
            };
        }
    }
} 