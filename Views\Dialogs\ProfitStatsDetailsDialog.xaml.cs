using System.Windows.Controls;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    public partial class ProfitStatsDetailsDialog : UserControl
    {
        public ProfitStatsDetailsDialog(
            RefactoredDashboardViewModel dashboardViewModel,
            string statType)
        {
            InitializeComponent();
            var dbService = new DatabaseService();
            DataContext = new ProfitStatsDetailsViewModel(
                dashboardViewModel,
                dbService,
                statType);
        }

        private void CloseButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            DialogHost.CloseDialogCommand.Execute(null, null);
        }
    }
} 