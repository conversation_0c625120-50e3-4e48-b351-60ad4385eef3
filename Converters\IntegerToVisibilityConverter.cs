using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class IntegerToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int count && parameter != null)
            {
                int compareValue = System.Convert.ToInt32(parameter);
                return count == compareValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 