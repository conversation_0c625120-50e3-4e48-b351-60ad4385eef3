using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.Services
{
    /// <summary>
    /// Service for handling bulk/pack pricing calculations and logic.
    /// Provides methods to calculate optimal pricing, suggest quantities, and handle edge cases.
    /// </summary>
    public class BulkPricingService
    {
        /// <summary>
        /// Calculates the best pricing for a given product and quantity.
        /// Returns pricing information including tier used, total price, and savings.
        /// </summary>
        /// <param name="product">Product to calculate pricing for</param>
        /// <param name="quantity">Desired quantity</param>
        /// <returns>Pricing calculation result</returns>
        public BulkPricingResult CalculateBestPricing(Product product, decimal quantity)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            if (quantity <= 0)
                throw new ArgumentException("Quantity must be positive", nameof(quantity));

            var result = new BulkPricingResult
            {
                Product = product,
                RequestedQuantity = quantity,
                RegularUnitPrice = product.SellingPrice,
                RegularTotalPrice = quantity * product.SellingPrice
            };

            // Get the best pricing tier for this quantity
            var bestTier = product.GetBestPriceTierForQuantity(quantity);
            
            if (bestTier != null)
            {
                result.AppliedTier = bestTier;
                result.EffectiveUnitPrice = bestTier.EffectiveUnitPrice;
                result.TotalPrice = bestTier.CalculatePriceForQuantity(quantity);
                result.TotalSavings = result.RegularTotalPrice - result.TotalPrice;
                result.SavingsPercentage = result.RegularTotalPrice > 0 
                    ? (result.TotalSavings / result.RegularTotalPrice) * 100 
                    : 0;
            }
            else
            {
                // No tier applies, use regular pricing
                result.EffectiveUnitPrice = product.SellingPrice;
                result.TotalPrice = result.RegularTotalPrice;
                result.TotalSavings = 0;
                result.SavingsPercentage = 0;
            }

            return result;
        }

        /// <summary>
        /// Suggests optimal quantities based on available pricing tiers.
        /// Returns suggestions that would provide better pricing for the customer.
        /// </summary>
        /// <param name="product">Product to analyze</param>
        /// <param name="currentQuantity">Current quantity being considered</param>
        /// <param name="maxSuggestions">Maximum number of suggestions to return</param>
        /// <returns>List of quantity suggestions with pricing benefits</returns>
        public List<QuantitySuggestion> GetQuantitySuggestions(Product product, decimal currentQuantity, int maxSuggestions = 3)
        {
            if (product == null || !product.HasBulkPricing)
                return new List<QuantitySuggestion>();

            var suggestions = new List<QuantitySuggestion>();
            var currentPricing = CalculateBestPricing(product, currentQuantity);
            
            // Get all tiers that offer better pricing than current
            var betterTiers = product.GetActivePriceTiers()
                .Where(tier => tier.MinimumQuantity > currentQuantity && 
                              tier.EffectiveUnitPrice < currentPricing.EffectiveUnitPrice)
                .OrderBy(tier => tier.MinimumQuantity)
                .Take(maxSuggestions);

            foreach (var tier in betterTiers)
            {
                var tierPricing = CalculateBestPricing(product, tier.MinimumQuantity);
                var additionalQuantity = tier.MinimumQuantity - currentQuantity;
                var additionalCost = additionalQuantity * tier.EffectiveUnitPrice;
                var totalSavingsOnNewQuantity = tierPricing.TotalSavings;

                suggestions.Add(new QuantitySuggestion
                {
                    SuggestedQuantity = tier.MinimumQuantity,
                    AdditionalQuantity = additionalQuantity,
                    AdditionalCost = additionalCost,
                    NewUnitPrice = tier.EffectiveUnitPrice,
                    TotalSavings = totalSavingsOnNewQuantity,
                    SavingsPercentage = tierPricing.SavingsPercentage,
                    TierName = tier.GetDisplayText(),
                    Description = $"Buy {additionalQuantity:0.###} more to save {totalSavingsOnNewQuantity:C2}"
                });
            }

            return suggestions;
        }

        /// <summary>
        /// Handles partial quantity scenarios where customer wants less than a pack size.
        /// Provides options for how to handle the situation.
        /// </summary>
        /// <param name="product">Product being purchased</param>
        /// <param name="desiredQuantity">Quantity customer wants</param>
        /// <returns>Options for handling partial quantities</returns>
        public PartialQuantityOptions HandlePartialQuantity(Product product, decimal desiredQuantity)
        {
            if (product == null || !product.HasBulkPricing)
                return null;

            var options = new PartialQuantityOptions
            {
                DesiredQuantity = desiredQuantity,
                Product = product
            };

            // Find the smallest tier that the desired quantity doesn't meet
            var nextTier = product.GetActivePriceTiers()
                .Where(tier => tier.MinimumQuantity > desiredQuantity)
                .OrderBy(tier => tier.MinimumQuantity)
                .FirstOrDefault();

            if (nextTier != null)
            {
                // Option 1: Buy at regular price
                options.RegularPriceOption = new PricingOption
                {
                    Quantity = desiredQuantity,
                    UnitPrice = product.SellingPrice,
                    TotalPrice = desiredQuantity * product.SellingPrice,
                    Description = $"Buy {desiredQuantity:0.###} at regular price"
                };

                // Option 2: Upgrade to next tier
                var tierPricing = CalculateBestPricing(product, nextTier.MinimumQuantity);
                options.UpgradeOption = new PricingOption
                {
                    Quantity = nextTier.MinimumQuantity,
                    UnitPrice = nextTier.EffectiveUnitPrice,
                    TotalPrice = tierPricing.TotalPrice,
                    Description = $"Upgrade to {nextTier.GetDisplayText()} and save {tierPricing.TotalSavings:C2}",
                    ExtraQuantity = nextTier.MinimumQuantity - desiredQuantity
                };

                // Calculate if upgrade is worth it
                options.UpgradeRecommended = options.UpgradeOption.TotalPrice <= 
                    options.RegularPriceOption.TotalPrice * 1.1m; // Recommend if within 10% of regular price
            }

            return options;
        }

        /// <summary>
        /// Validates a pricing tier configuration for business logic errors.
        /// </summary>
        /// <param name="tier">Pricing tier to validate</param>
        /// <param name="product">Product the tier belongs to</param>
        /// <returns>Validation result with any errors found</returns>
        public ValidationResult ValidatePriceTier(ProductPriceTier tier, Product product)
        {
            var result = new ValidationResult();

            if (tier == null)
            {
                result.AddError("Pricing tier cannot be null");
                return result;
            }

            if (product == null)
            {
                result.AddError("Product cannot be null");
                return result;
            }

            // Validate quantity ranges
            if (tier.MinimumQuantity <= 0)
                result.AddError("Minimum quantity must be positive");

            if (tier.MaximumQuantity.HasValue && tier.MaximumQuantity.Value <= tier.MinimumQuantity)
                result.AddError("Maximum quantity must be greater than minimum quantity");

            // Validate pricing
            if (tier.UnitPrice <= 0)
                result.AddError("Unit price must be positive");

            if (tier.PackPrice.HasValue && tier.PackPrice.Value <= 0)
                result.AddError("Pack price must be positive");

            // Business logic validations
            if (tier.EffectiveUnitPrice >= product.SellingPrice)
                result.AddWarning($"Tier price ({tier.EffectiveUnitPrice:C2}) is not better than regular price ({product.SellingPrice:C2})");

            // Check for overlapping tiers
            var overlappingTiers = product.GetActivePriceTiers()
                .Where(pt => pt.Id != tier.Id && DoTiersOverlap(tier, pt))
                .ToList();

            if (overlappingTiers.Any())
                result.AddError($"Tier overlaps with existing tiers: {string.Join(", ", overlappingTiers.Select(t => t.GetDisplayText()))}");

            return result;
        }

        /// <summary>
        /// Checks if two pricing tiers have overlapping quantity ranges.
        /// </summary>
        private bool DoTiersOverlap(ProductPriceTier tier1, ProductPriceTier tier2)
        {
            var tier1Max = tier1.MaximumQuantity ?? decimal.MaxValue;
            var tier2Max = tier2.MaximumQuantity ?? decimal.MaxValue;

            return tier1.MinimumQuantity < tier2Max && tier2.MinimumQuantity < tier1Max;
        }
    }

    /// <summary>
    /// Result of a bulk pricing calculation.
    /// </summary>
    public class BulkPricingResult
    {
        public Product Product { get; set; }
        public decimal RequestedQuantity { get; set; }
        public ProductPriceTier AppliedTier { get; set; }
        public decimal RegularUnitPrice { get; set; }
        public decimal RegularTotalPrice { get; set; }
        public decimal EffectiveUnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal SavingsPercentage { get; set; }
        
        public bool HasBulkDiscount => AppliedTier != null && TotalSavings > 0;
        public string SavingsDisplay => HasBulkDiscount ? $"Save {TotalSavings:C2} ({SavingsPercentage:F1}%)" : "";
    }

    /// <summary>
    /// Suggestion for a better quantity to purchase.
    /// </summary>
    public class QuantitySuggestion
    {
        public decimal SuggestedQuantity { get; set; }
        public decimal AdditionalQuantity { get; set; }
        public decimal AdditionalCost { get; set; }
        public decimal NewUnitPrice { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal SavingsPercentage { get; set; }
        public string TierName { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Options for handling partial quantities.
    /// </summary>
    public class PartialQuantityOptions
    {
        public decimal DesiredQuantity { get; set; }
        public Product Product { get; set; }
        public PricingOption RegularPriceOption { get; set; }
        public PricingOption UpgradeOption { get; set; }
        public bool UpgradeRecommended { get; set; }
    }

    /// <summary>
    /// A pricing option for partial quantity handling.
    /// </summary>
    public class PricingOption
    {
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string Description { get; set; }
        public decimal ExtraQuantity { get; set; }
    }

    /// <summary>
    /// Result of validation operations.
    /// </summary>
    public class ValidationResult
    {
        public List<string> Errors { get; } = new List<string>();
        public List<string> Warnings { get; } = new List<string>();
        
        public bool IsValid => !Errors.Any();
        public bool HasWarnings => Warnings.Any();

        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
    }

    /// <summary>
    /// Advanced bulk pricing calculator with optimization features.
    /// </summary>
    public static class BulkPricingCalculator
    {
        /// <summary>
        /// Calculates the most cost-effective way to purchase a desired quantity.
        /// May suggest buying more to get better pricing or splitting across tiers.
        /// </summary>
        /// <param name="product">Product with pricing tiers</param>
        /// <param name="desiredQuantity">Quantity customer wants</param>
        /// <returns>Optimized purchase recommendation</returns>
        public static OptimizedPurchaseRecommendation GetOptimizedPurchase(Product product, decimal desiredQuantity)
        {
            if (product == null || !product.HasBulkPricing)
            {
                return new OptimizedPurchaseRecommendation
                {
                    DesiredQuantity = desiredQuantity,
                    RecommendedQuantity = desiredQuantity,
                    TotalPrice = desiredQuantity * product.SellingPrice,
                    UnitPrice = product.SellingPrice,
                    IsOptimal = true,
                    Reason = "No bulk pricing available"
                };
            }

            var tiers = product.GetActivePriceTiers().ToList();
            var bestOption = FindBestPurchaseOption(tiers, desiredQuantity, product.SellingPrice);

            return bestOption;
        }

        private static OptimizedPurchaseRecommendation FindBestPurchaseOption(
            List<ProductPriceTier> tiers,
            decimal desiredQuantity,
            decimal regularPrice)
        {
            var options = new List<PurchaseOption>();

            // Option 1: Buy exactly what's desired at regular price
            options.Add(new PurchaseOption
            {
                Quantity = desiredQuantity,
                TotalPrice = desiredQuantity * regularPrice,
                UnitPrice = regularPrice,
                Description = "Regular pricing"
            });

            // Option 2: Find applicable tier for desired quantity
            var applicableTier = tiers
                .Where(t => t.QualifiesForTier(desiredQuantity))
                .OrderByDescending(t => t.MinimumQuantity)
                .FirstOrDefault();

            if (applicableTier != null)
            {
                options.Add(new PurchaseOption
                {
                    Quantity = desiredQuantity,
                    TotalPrice = applicableTier.CalculatePriceForQuantity(desiredQuantity),
                    UnitPrice = applicableTier.EffectiveUnitPrice,
                    Description = $"Using {applicableTier.GetDisplayText()}"
                });
            }

            // Option 3: Check if buying more to reach next tier is beneficial
            var nextTier = tiers
                .Where(t => t.MinimumQuantity > desiredQuantity)
                .OrderBy(t => t.MinimumQuantity)
                .FirstOrDefault();

            if (nextTier != null)
            {
                var nextTierPrice = nextTier.CalculatePriceForQuantity(nextTier.MinimumQuantity);
                var currentBestPrice = options.Min(o => o.TotalPrice);

                // Only suggest if the total cost increase is reasonable (within 15% of current best)
                if (nextTierPrice <= currentBestPrice * 1.15m)
                {
                    options.Add(new PurchaseOption
                    {
                        Quantity = nextTier.MinimumQuantity,
                        TotalPrice = nextTierPrice,
                        UnitPrice = nextTier.EffectiveUnitPrice,
                        Description = $"Upgrade to {nextTier.GetDisplayText()} for better unit price",
                        ExtraQuantity = nextTier.MinimumQuantity - desiredQuantity
                    });
                }
            }

            // Find the best option (lowest total price, or lowest unit price if total is similar)
            var bestOption = options
                .OrderBy(o => o.TotalPrice)
                .ThenBy(o => o.UnitPrice)
                .First();

            return new OptimizedPurchaseRecommendation
            {
                DesiredQuantity = desiredQuantity,
                RecommendedQuantity = bestOption.Quantity,
                TotalPrice = bestOption.TotalPrice,
                UnitPrice = bestOption.UnitPrice,
                ExtraQuantity = bestOption.ExtraQuantity,
                IsOptimal = bestOption.Quantity == desiredQuantity,
                Reason = bestOption.Description,
                AlternativeOptions = options.Where(o => o != bestOption).ToList()
            };
        }

        /// <summary>
        /// Calculates bulk pricing for multiple products in a cart.
        /// Handles complex scenarios where different products have different pricing tiers.
        /// </summary>
        /// <param name="cartItems">Items in the cart with their quantities</param>
        /// <returns>Optimized pricing for the entire cart</returns>
        public static CartBulkPricingResult CalculateCartBulkPricing(IEnumerable<(Product product, decimal quantity)> cartItems)
        {
            var result = new CartBulkPricingResult();

            foreach (var (product, quantity) in cartItems)
            {
                var itemPricing = new BulkPricingService().CalculateBestPricing(product, quantity);
                result.ItemPricings.Add(itemPricing);

                result.TotalRegularPrice += itemPricing.RegularTotalPrice;
                result.TotalOptimizedPrice += itemPricing.TotalPrice;
            }

            result.TotalSavings = result.TotalRegularPrice - result.TotalOptimizedPrice;
            result.SavingsPercentage = result.TotalRegularPrice > 0
                ? (result.TotalSavings / result.TotalRegularPrice) * 100
                : 0;

            return result;
        }
    }

    /// <summary>
    /// Represents an optimized purchase recommendation.
    /// </summary>
    public class OptimizedPurchaseRecommendation
    {
        public decimal DesiredQuantity { get; set; }
        public decimal RecommendedQuantity { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal ExtraQuantity { get; set; }
        public bool IsOptimal { get; set; }
        public string Reason { get; set; }
        public List<PurchaseOption> AlternativeOptions { get; set; } = new List<PurchaseOption>();
    }

    /// <summary>
    /// Represents a purchase option.
    /// </summary>
    public class PurchaseOption
    {
        public decimal Quantity { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal ExtraQuantity { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Result of bulk pricing calculation for an entire cart.
    /// </summary>
    public class CartBulkPricingResult
    {
        public List<BulkPricingResult> ItemPricings { get; set; } = new List<BulkPricingResult>();
        public decimal TotalRegularPrice { get; set; }
        public decimal TotalOptimizedPrice { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal SavingsPercentage { get; set; }

        public bool HasSavings => TotalSavings > 0;
        public string SavingsDisplay => HasSavings ? $"Save {TotalSavings:C2} ({SavingsPercentage:F1}%)" : "";
    }
}
