using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;

namespace POSSystem.Services.Startup
{
    /// <summary>
    /// ✅ CRITICAL STARTUP OPTIMIZATION: Database connection and schema optimization for faster startup
    /// </summary>
    public class DatabaseStartupOptimizer : IDisposable
    {
        private readonly ILogger<DatabaseStartupOptimizer> _logger;
        private readonly StartupPerformanceMonitor _performanceMonitor;
        private readonly ConcurrentDictionary<string, object> _schemaCache;
        private readonly SemaphoreSlim _connectionSemaphore;
        private bool _disposed;

        // Configuration
        private const int MAX_CONCURRENT_CONNECTIONS = 3;
        private const int CONNECTION_TIMEOUT_MS = 5000;
        private const int SCHEMA_CACHE_EXPIRY_HOURS = 24;

        public DatabaseStartupOptimizer(StartupPerformanceMonitor performanceMonitor = null,
            ILogger<DatabaseStartupOptimizer> logger = null)
        {
            _performanceMonitor = performanceMonitor;
            _logger = logger;
            _schemaCache = new ConcurrentDictionary<string, object>();
            _connectionSemaphore = new SemaphoreSlim(MAX_CONCURRENT_CONNECTIONS, MAX_CONCURRENT_CONNECTIONS);

            Debug.WriteLine("✅ [DB-STARTUP-OPT] Database Startup Optimizer initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Optimize database connection and schema for startup
        /// </summary>
        public async Task OptimizeDatabaseForStartupAsync(string connectionString, CancellationToken cancellationToken = default)
        {
            using var overallTracker = _performanceMonitor?.TrackStartupPhase("DatabaseStartupOptimization", "Database");
            
            try
            {
                Debug.WriteLine("[DB-STARTUP-OPT] Starting database startup optimization");

                // Phase 1: Optimize connection string
                var optimizedConnectionString = OptimizeConnectionString(connectionString);

                // Phase 2: Warm up connection pool
                await WarmUpConnectionPoolAsync(optimizedConnectionString, cancellationToken);

                // Phase 3: Cache schema information
                await CacheSchemaInformationAsync(optimizedConnectionString, cancellationToken);

                // Phase 4: Apply startup-specific optimizations
                await ApplyStartupOptimizationsAsync(optimizedConnectionString, cancellationToken);

                Debug.WriteLine("✅ [DB-STARTUP-OPT] Database startup optimization completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error during database startup optimization: {ex.Message}");
                _logger?.LogError(ex, "Error during database startup optimization");
                throw;
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Optimize connection string for startup performance
        /// </summary>
        private string OptimizeConnectionString(string connectionString)
        {
            using var optimizeTracker = _performanceMonitor?.TrackStartupPhase("OptimizeConnectionString", "Database");
            
            try
            {
                var builder = new SqliteConnectionStringBuilder(connectionString);

                // Enable connection pooling
                builder.Pooling = true;

                // Set cache size for better performance (in KB)
                if (!builder.ConnectionString.Contains("Cache Size"))
                {
                    builder.Add("Cache Size", "10000"); // 10MB cache
                }

                // Enable WAL mode for better concurrency
                if (!builder.ConnectionString.Contains("Journal Mode"))
                {
                    builder.Add("Journal Mode", "WAL");
                }

                // Set synchronous mode for better performance
                if (!builder.ConnectionString.Contains("Synchronous"))
                {
                    builder.Add("Synchronous", "NORMAL");
                }

                // Set temp store to memory for better performance
                if (!builder.ConnectionString.Contains("Temp Store"))
                {
                    builder.Add("Temp Store", "Memory");
                }

                // Set page size for better performance
                if (!builder.ConnectionString.Contains("Page Size"))
                {
                    builder.Add("Page Size", "4096");
                }

                var optimizedConnectionString = builder.ConnectionString;
                Debug.WriteLine($"[DB-STARTUP-OPT] Connection string optimized with performance settings");

                return optimizedConnectionString;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error optimizing connection string: {ex.Message}");
                return connectionString; // Return original if optimization fails
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Warm up connection pool for faster subsequent connections
        /// </summary>
        private async Task WarmUpConnectionPoolAsync(string connectionString, CancellationToken cancellationToken)
        {
            using var warmupTracker = _performanceMonitor?.TrackStartupPhase("WarmUpConnectionPool", "Database");
            
            try
            {
                Debug.WriteLine("[DB-STARTUP-OPT] Warming up connection pool");

                var warmupTasks = new Task[MAX_CONCURRENT_CONNECTIONS];

                for (int i = 0; i < MAX_CONCURRENT_CONNECTIONS; i++)
                {
                    warmupTasks[i] = WarmUpSingleConnectionAsync(connectionString, cancellationToken);
                }

                await Task.WhenAll(warmupTasks);
                Debug.WriteLine($"✅ [DB-STARTUP-OPT] Connection pool warmed up with {MAX_CONCURRENT_CONNECTIONS} connections");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error warming up connection pool: {ex.Message}");
                // Don't throw - warmup failure shouldn't block startup
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Warm up a single connection
        /// </summary>
        private async Task WarmUpSingleConnectionAsync(string connectionString, CancellationToken cancellationToken)
        {
            await _connectionSemaphore.WaitAsync(cancellationToken);

            try
            {
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(CONNECTION_TIMEOUT_MS);

                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync(timeoutCts.Token);

                // Execute a simple query to fully initialize the connection
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT 1";
                await command.ExecuteScalarAsync(timeoutCts.Token);

                Debug.WriteLine("[DB-STARTUP-OPT] Connection warmed up successfully");
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                Debug.WriteLine("⚠️ [DB-STARTUP-OPT] Connection warmup cancelled");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ [DB-STARTUP-OPT] Connection warmup failed: {ex.Message}");
                // Don't throw - individual connection failures shouldn't block startup
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Cache schema information for faster subsequent queries
        /// </summary>
        private async Task CacheSchemaInformationAsync(string connectionString, CancellationToken cancellationToken)
        {
            using var cacheTracker = _performanceMonitor?.TrackStartupPhase("CacheSchemaInformation", "Database");
            
            try
            {
                Debug.WriteLine("[DB-STARTUP-OPT] Caching schema information");

                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync(cancellationToken);

                // Cache table information
                await CacheTableInformationAsync(connection, cancellationToken);

                // Cache index information
                await CacheIndexInformationAsync(connection, cancellationToken);

                Debug.WriteLine($"✅ [DB-STARTUP-OPT] Schema information cached ({_schemaCache.Count} items)");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error caching schema information: {ex.Message}");
                // Don't throw - schema caching failure shouldn't block startup
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Cache table information
        /// </summary>
        private async Task CacheTableInformationAsync(SqliteConnection connection, CancellationToken cancellationToken)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name";

                using var reader = await command.ExecuteReaderAsync(cancellationToken);
                var tables = new List<string>();

                while (await reader.ReadAsync(cancellationToken))
                {
                    tables.Add(reader.GetString(0));
                }

                _schemaCache.TryAdd("tables", tables);
                Debug.WriteLine($"[DB-STARTUP-OPT] Cached {tables.Count} table names");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ [DB-STARTUP-OPT] Error caching table information: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Cache index information
        /// </summary>
        private async Task CacheIndexInformationAsync(SqliteConnection connection, CancellationToken cancellationToken)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT name FROM sqlite_master WHERE type='index' ORDER BY name";

                using var reader = await command.ExecuteReaderAsync(cancellationToken);
                var indexes = new List<string>();

                while (await reader.ReadAsync(cancellationToken))
                {
                    indexes.Add(reader.GetString(0));
                }

                _schemaCache.TryAdd("indexes", indexes);
                Debug.WriteLine($"[DB-STARTUP-OPT] Cached {indexes.Count} index names");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ [DB-STARTUP-OPT] Error caching index information: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Apply startup-specific database optimizations
        /// </summary>
        private async Task ApplyStartupOptimizationsAsync(string connectionString, CancellationToken cancellationToken)
        {
            using var optimizationTracker = _performanceMonitor?.TrackStartupPhase("ApplyStartupOptimizations", "Database");
            
            try
            {
                Debug.WriteLine("[DB-STARTUP-OPT] Applying startup-specific optimizations");

                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync(cancellationToken);

                // Apply PRAGMA settings for better startup performance
                var pragmaSettings = new[]
                {
                    "PRAGMA cache_size = 10000",           // 10MB cache
                    "PRAGMA temp_store = MEMORY",          // Store temp tables in memory
                    "PRAGMA mmap_size = 268435456",        // 256MB memory-mapped I/O
                    "PRAGMA optimize",                     // Optimize query planner
                    "PRAGMA analysis_limit = 1000"        // Limit analysis for faster startup
                };

                foreach (var pragma in pragmaSettings)
                {
                    try
                    {
                        using var command = connection.CreateCommand();
                        command.CommandText = pragma;
                        await command.ExecuteNonQueryAsync(cancellationToken);
                        Debug.WriteLine($"[DB-STARTUP-OPT] Applied: {pragma}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ [DB-STARTUP-OPT] Failed to apply {pragma}: {ex.Message}");
                    }
                }

                Debug.WriteLine("✅ [DB-STARTUP-OPT] Startup-specific optimizations applied");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error applying startup optimizations: {ex.Message}");
                // Don't throw - optimization failure shouldn't block startup
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get cached schema information
        /// </summary>
        public T GetCachedSchemaInfo<T>(string key) where T : class
        {
            return _schemaCache.TryGetValue(key, out var value) ? value as T : null;
        }

        /// <summary>
        /// ✅ PUBLIC API: Check if database file exists and is accessible
        /// </summary>
        public bool ValidateDatabaseFile(string connectionString)
        {
            try
            {
                var builder = new SqliteConnectionStringBuilder(connectionString);
                var dataSource = builder.DataSource;

                if (string.IsNullOrEmpty(dataSource) || dataSource == ":memory:")
                {
                    return true; // In-memory database is always valid
                }

                return File.Exists(dataSource);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error validating database file: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Get optimization statistics
        /// </summary>
        public DatabaseOptimizationStats GetOptimizationStats()
        {
            return new DatabaseOptimizationStats
            {
                CachedSchemaItems = _schemaCache.Count,
                MaxConcurrentConnections = MAX_CONCURRENT_CONNECTIONS,
                ConnectionTimeoutMs = CONNECTION_TIMEOUT_MS
            };
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _connectionSemaphore?.Dispose();
                _schemaCache.Clear();

                Debug.WriteLine("✅ [DB-STARTUP-OPT] Database Startup Optimizer disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [DB-STARTUP-OPT] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Statistics for database optimization
    /// </summary>
    public class DatabaseOptimizationStats
    {
        public int CachedSchemaItems { get; set; }
        public int MaxConcurrentConnections { get; set; }
        public int ConnectionTimeoutMs { get; set; }
    }
}
