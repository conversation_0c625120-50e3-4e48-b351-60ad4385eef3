-- Heavy Load Test Data Generator for POS System
-- This script generates an extremely large volume of data to test application performance
-- WARNING: This will generate TENS OF THOUSANDS of records - only use for performance testing

-- Store original triggers for later restoration (optional)
CREATE TEMPORARY TABLE saved_triggers AS
SELECT name, sql FROM sqlite_master 
WHERE type='trigger' AND name IN ('update_loyalty_points_after_sale', 'create_sale_history', 'update_inventory_after_sale');

-- Drop problematic triggers
DROP TRIGGER IF EXISTS update_loyalty_points_after_sale;
DROP TRIGGER IF EXISTS create_sale_history;
DROP TRIGGER IF EXISTS update_inventory_after_sale;

-- Disable foreign keys for better performance
PRAGMA foreign_keys = OFF;

-- Set pragmas for better performance
PRAGMA synchronous = OFF;
PRAGMA journal_mode = MEMORY;
PRAGMA temp_store = MEMORY;
PRAGMA cache_size = 100000; -- Use a large cache to speed up the process

BEGIN TRANSACTION;

-- Helper function to generate random numbers between min and max
CREATE TEMPORARY FUNCTION RandomInt(min INT, max INT) 
RETURNS INT AS 
BEGIN
    RETURN min + ABS(RANDOM()) % (max - min + 1);
END;

CREATE TEMPORARY FUNCTION RandomDecimal(min DECIMAL, max DECIMAL, places INT)
RETURNS DECIMAL AS
BEGIN
    RETURN ROUND(min + (ABS(RANDOM()) % 1000000) / 1000000.0 * (max - min), places);
END;

-- Clean up existing test data from heavy load tests
DELETE FROM UserFavorites WHERE CreatedAt > datetime('now', '-31 days');
DELETE FROM LoyaltyTransactions WHERE TransactionDate > datetime('now', '-31 days');
DELETE FROM InventoryTransactions WHERE TransactionDate > datetime('now', '-31 days');
DELETE FROM CashTransactions WHERE Timestamp > datetime('now', '-31 days');
DELETE FROM Discounts WHERE AppliedAt > datetime('now', '-31 days');
DELETE FROM SaleItems WHERE SaleId IN (SELECT Id FROM Sales WHERE InvoiceNumber LIKE 'HEAVY-%');
DELETE FROM Sales WHERE InvoiceNumber LIKE 'HEAVY-%';
DELETE FROM CashDrawers WHERE Notes LIKE '%Heavy Load%';

-- Create date generator for the last 31 days (full month)
CREATE TEMPORARY TABLE DateRange AS
WITH RECURSIVE dates(date_value) AS (
  SELECT datetime('now', '-31 days')
  UNION ALL
  SELECT datetime(date_value, '+1 day')
  FROM dates
  WHERE date_value < datetime('now')
)
SELECT date_value FROM dates;

-- Generate cash drawers for each day
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
SELECT 
    500.00 AS OpeningBalance,
    500.00 + RandomDecimal(800, 3500, 2) AS CurrentBalance,
    500.00 + RandomDecimal(800, 3500, 2) AS ExpectedBalance,
    CASE 
        WHEN RandomInt(1, 10) <= 7 THEN 500.00 + RandomDecimal(800, 3500, 2) -- Exact match 70% of time
        WHEN RandomInt(1, 10) <= 9 THEN 500.00 + RandomDecimal(800, 3500, 2) + RandomDecimal(1, 50, 2) -- Small overage 20%
        ELSE 500.00 + RandomDecimal(800, 3500, 2) - RandomDecimal(1, 50, 2) -- Small shortage 10%
    END AS ActualBalance,
    CASE 
        WHEN RandomInt(1, 10) <= 7 THEN 0.00 -- Exact match 70% of time
        WHEN RandomInt(1, 10) <= 9 THEN RandomDecimal(1, 50, 2) -- Small overage 20%
        ELSE -RandomDecimal(1, 50, 2) -- Small shortage 10%
    END AS Difference,
    'Closed' AS Status,
    datetime(date_value, '09:00:00') AS OpenedAt,
    datetime(date_value, '18:00:00') AS ClosedAt,
    CASE RandomInt(1, 3) WHEN 1 THEN 1 WHEN 2 THEN 2 ELSE 3 END AS OpenedById,
    CASE RandomInt(1, 2) WHEN 1 THEN 1 ELSE 2 END AS ClosedById,
    'Heavy Load test drawer for ' || date(date_value) AS Notes
FROM DateRange;

-- Generate sales with variation in counts per day of week
-- Even more sales than the standard performance test
CREATE TEMPORARY TABLE SalesPerDay AS
SELECT 
    date_value,
    CASE 
        WHEN strftime('%w', date_value) = '0' THEN RandomInt(50, 100) -- Sunday
        WHEN strftime('%w', date_value) = '1' THEN RandomInt(20, 40)  -- Monday
        WHEN strftime('%w', date_value) = '2' THEN RandomInt(30, 60)  -- Tuesday
        WHEN strftime('%w', date_value) = '3' THEN RandomInt(30, 70)  -- Wednesday
        WHEN strftime('%w', date_value) = '4' THEN RandomInt(40, 80)  -- Thursday
        WHEN strftime('%w', date_value) = '5' THEN RandomInt(80, 120) -- Friday
        WHEN strftime('%w', date_value) = '6' THEN RandomInt(100, 150) -- Saturday
    END AS sale_count
FROM DateRange;

-- Create a temp table to hold sale data as we generate it
CREATE TEMPORARY TABLE TempSales (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT,
    SaleDate TEXT,
    CustomerId INTEGER,
    UserId INTEGER,
    Subtotal NUMERIC,
    DiscountAmount NUMERIC,
    TaxAmount NUMERIC,
    GrandTotal NUMERIC,
    AmountPaid NUMERIC,
    Change NUMERIC,
    PaymentMethod TEXT,
    PaymentStatus TEXT,
    Status TEXT,
    TotalItems INTEGER
);

-- Create sale records - with much higher volume than standard test
WITH SaleGenerator AS (
    SELECT 
        d.date_value,
        'HEAVY-' || strftime('%Y%m%d', d.date_value) || '-' || PRINTF('%04d', s.rowid) AS InvoiceNumber,
        datetime(d.date_value, 
            PRINTF('%02d', RandomInt(9, 17)) || ':' || 
            PRINTF('%02d', RandomInt(0, 59)) || ':' || 
            PRINTF('%02d', RandomInt(0, 59))
        ) AS SaleDate,
        CASE WHEN RandomInt(1, 4) <= 3 THEN RandomInt(1, 4) ELSE NULL END AS CustomerId,
        CASE RandomInt(1, 100) 
            WHEN 1 THEN 1   -- Admin (1%)
            WHEN 2 THEN 1   -- Admin (1%)
            WHEN 3 THEN 1   -- Admin (1%)
            WHEN 4 THEN 2   -- Manager (1%)
            WHEN 5 THEN 2   -- Manager (1%)
            WHEN 6 THEN 2   -- Manager (1%)
            WHEN 7 THEN 2   -- Manager (1%)
            WHEN 8 THEN 2   -- Manager (1%)
            ELSE 3          -- Cashier (92%)
        END AS UserId,
        CASE RandomInt(1, 10)
            WHEN 1 THEN 'Cash'
            WHEN 2 THEN 'Cash'
            WHEN 3 THEN 'Cash'
            WHEN 4 THEN 'Cash'
            WHEN 5 THEN 'Cash'  -- Cash: 50%
            WHEN 6 THEN 'Card'
            WHEN 7 THEN 'Card'
            WHEN 8 THEN 'Card'  -- Card: 30%
            WHEN 9 THEN 'Mobile'
            ELSE 'Mobile'       -- Mobile: 20%
        END AS PaymentMethod,
        'Paid' AS PaymentStatus,
        'Completed' AS Status,
        RandomInt(1, 15) AS ItemCount  -- Higher item count per sale
    FROM SalesPerDay d
    JOIN (SELECT rowid FROM SalesPerDay LIMIT 10000000) s -- Cross join to generate multiple sales per day
    WHERE s.rowid <= d.sale_count
)
INSERT INTO TempSales (
    InvoiceNumber, SaleDate, CustomerId, UserId, 
    PaymentMethod, PaymentStatus, Status, TotalItems
)
SELECT 
    InvoiceNumber, SaleDate, CustomerId, UserId,
    PaymentMethod, PaymentStatus, Status, ItemCount
FROM SaleGenerator;

-- Create a helper table for product weighted selection
CREATE TEMPORARY TABLE ProductWeights AS
SELECT 
    Id, 
    Name,
    CategoryId,
    -- Electronics are less frequently purchased
    CASE 
        WHEN CategoryId IN (1, 2, 3) THEN 10  -- Electronics (less frequent)
        WHEN CategoryId IN (4, 5, 6) THEN 75  -- Groceries (more frequent)
        ELSE 30                               -- Clothing (medium frequency)
    END AS weight,
    SellingPrice
FROM Products
WHERE IsActive = 1;

-- Generate SaleItems based on the TempSales
CREATE TEMPORARY TABLE TempSaleItems (
    SaleId INTEGER,
    ProductId INTEGER,
    Quantity INTEGER,
    UnitPrice NUMERIC,
    Total NUMERIC
);

-- Generate sale items - logic for each sale with larger quantities
WITH SaleItemGenerator AS (
    SELECT 
        s.Id AS SaleId,
        s.TotalItems AS ItemCount,
        (SELECT p.Id FROM ProductWeights p, 
            (SELECT ABS(RANDOM()) % SUM(weight) AS rand FROM ProductWeights) r
            WHERE r.rand < (SELECT SUM(weight) FROM ProductWeights p2 WHERE p2.Id <= p.Id)
            ORDER BY p.Id LIMIT 1
        ) AS ProductId,
        RandomInt(1, 6) AS Quantity -- Higher quantities
    FROM TempSales s
    -- Cross join to generate multiple items per sale
    CROSS JOIN (SELECT 1 AS n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 
                UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10
                UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15) numbers
    WHERE numbers.n <= s.TotalItems
)
INSERT INTO TempSaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
SELECT 
    sig.SaleId,
    sig.ProductId,
    sig.Quantity,
    p.SellingPrice AS UnitPrice,
    ROUND(sig.Quantity * p.SellingPrice, 2) AS Total
FROM SaleItemGenerator sig
JOIN Products p ON sig.ProductId = p.Id;

-- Calculate totals for each sale
UPDATE TempSales 
SET 
    Subtotal = (SELECT COALESCE(SUM(Total), 0) FROM TempSaleItems WHERE SaleId = TempSales.Id),
    -- Apply random discounts to about 30% of sales (more than standard test)
    DiscountAmount = CASE 
        WHEN RandomInt(1, 10) <= 3 THEN ROUND((SELECT COALESCE(SUM(Total), 0) FROM TempSaleItems WHERE SaleId = TempSales.Id) * RandomDecimal(0.05, 0.25, 2), 2)
        ELSE 0.00
    END,
    TaxAmount = 0.00 -- No tax in this example
;

-- Finalize sale calculations
UPDATE TempSales
SET 
    GrandTotal = Subtotal - DiscountAmount + TaxAmount,
    AmountPaid = CASE 
        WHEN PaymentMethod = 'Cash' THEN 
            -- Round up to nearest 0.50 or 1.00 for cash
            CASE
                WHEN (Subtotal - DiscountAmount + TaxAmount) % 1 <= 0.5 
                THEN CEIL((Subtotal - DiscountAmount + TaxAmount) * 2) / 2
                ELSE CEIL(Subtotal - DiscountAmount + TaxAmount)
            END
        ELSE Subtotal - DiscountAmount + TaxAmount -- Exact amount for card/mobile
    END;

UPDATE TempSales
SET Change = CASE 
    WHEN PaymentMethod = 'Cash' THEN AmountPaid - GrandTotal
    ELSE 0.00
END;

-- Insert the finalized sales - using LIMIT to avoid overloading with too many records
-- Adjust this number for higher or lower volume
INSERT INTO Sales (
    InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
    TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems
)
SELECT 
    InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
    TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems
FROM TempSales
LIMIT 3000; -- Adjust this number as needed - higher number = more data and slower performance

-- Insert the SaleItems linking to actual Sales table
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
SELECT 
    s.Id AS SaleId,
    tsi.ProductId,
    tsi.Quantity,
    tsi.UnitPrice,
    tsi.Total
FROM TempSaleItems tsi
JOIN TempSales ts ON tsi.SaleId = ts.Id
JOIN Sales s ON s.InvoiceNumber = ts.InvoiceNumber
WHERE s.InvoiceNumber LIKE 'HEAVY-%';

-- Create discounts for the sales that have discount amounts
INSERT INTO Discounts (
    DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
    Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive
)
SELECT 
    -- Most are percentage discounts, some are fixed amount
    CASE WHEN RandomInt(1, 5) <= 4 THEN 1 ELSE 2 END AS DiscountTypeId,
    -- For percentage, value is 5-25%, for fixed its $2-$50
    CASE 
        WHEN RandomInt(1, 5) <= 4 THEN RandomInt(5, 25) -- Percentage
        ELSE RandomDecimal(2, 50, 2) -- Fixed amount
    END AS DiscountValue,
    s.Subtotal AS OriginalPrice,
    s.GrandTotal AS FinalPrice,
    -- Random reason
    RandomInt(1, 7) AS ReasonId,
    -- Random comment
    CASE RandomInt(1, 10)
        WHEN 1 THEN 'Customer loyalty discount'
        WHEN 2 THEN 'Weekly special'
        WHEN 3 THEN 'Damaged packaging'
        WHEN 4 THEN 'Bulk purchase discount'
        WHEN 5 THEN 'Seasonal promotion'
        WHEN 6 THEN 'End of day clearance'
        WHEN 7 THEN 'Product on sale'
        WHEN 8 THEN 'Clearance item'
        WHEN 9 THEN 'Display model'
        ELSE 'Manager approval'
    END AS Comment,
    s.Id AS SaleId,
    NULL AS SaleItemId, -- Cart-wide discount
    s.UserId AS AppliedByUserId,
    s.SaleDate AS AppliedAt,
    1 AS IsActive
FROM Sales s
WHERE s.DiscountAmount > 0 AND s.InvoiceNumber LIKE 'HEAVY-%';

-- Generate inventory transactions for each sale item
INSERT INTO InventoryTransactions (
    ProductId, TransactionType, Quantity, UnitPrice, Reference, Notes, TransactionDate, UserId
)
SELECT 
    si.ProductId,
    'Sale' AS TransactionType,
    -si.Quantity AS Quantity, -- Negative for sales
    si.UnitPrice,
    s.InvoiceNumber AS Reference,
    'Heavy load test sale' AS Notes,
    s.SaleDate AS TransactionDate,
    s.UserId
FROM SaleItems si
JOIN Sales s ON si.SaleId = s.Id
WHERE s.InvoiceNumber LIKE 'HEAVY-%';

-- Add some purchase orders to restock inventory (more than standard test)
INSERT INTO PurchaseOrders (
    OrderNumber, OrderDate, DueDate, SupplierId, Subtotal, TaxAmount, GrandTotal,
    Status, Notes, CreatedByUserId, PaymentMethod, PaymentReference, PaymentDate, CreatedAt, UpdatedAt
)
SELECT
    'HEAVY-PO-' || strftime('%Y%m%d', date_value) || '-' || PRINTF('%02d', rowid) AS OrderNumber,
    date_value AS OrderDate,
    datetime(date_value, '+3 days') AS DueDate,
    RandomInt(1, 3) AS SupplierId,
    RandomDecimal(1000, 5000, 2) AS Subtotal,
    RandomDecimal(100, 500, 2) AS TaxAmount,
    RandomDecimal(1100, 5500, 2) AS GrandTotal,
    CASE RandomInt(1, 4)
        WHEN 1 THEN 'Pending'
        WHEN 2 THEN 'Shipped'
        WHEN 3 THEN 'Received'
        ELSE 'Completed'
    END AS Status,
    'Heavy load test purchase order' AS Notes,
    RandomInt(1, 2) AS CreatedByUserId,
    CASE RandomInt(1, 3)
        WHEN 1 THEN 'Bank Transfer'
        WHEN 2 THEN 'Credit Card'
        ELSE 'Check'
    END AS PaymentMethod,
    'REF-' || ABS(RANDOM()) % 1000000 AS PaymentReference,
    datetime(date_value, '+1 day') AS PaymentDate,
    date_value AS CreatedAt,
    date_value AS UpdatedAt
FROM DateRange
WHERE date_value >= datetime('now', '-30 days') 
  AND rowid % 3 = 0;  -- Create PO every 3 days

-- Add purchase order items
INSERT INTO PurchaseOrderItems (
    PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, BatchNumber,
    Location, Notes, ExpiryDate
)
SELECT
    po.Id AS PurchaseOrderId,
    p.Id AS ProductId,
    RandomInt(10, 50) AS Quantity,
    p.PurchasePrice AS UnitCost,
    p.SellingPrice AS SellingPrice,
    'BATCH-HEAVY-' || po.Id || '-' || p.Id AS BatchNumber,
    CASE
        WHEN p.CategoryId IN (1, 2, 3) THEN 'Electronics Section'
        WHEN p.CategoryId IN (4, 5, 6) THEN 'Grocery Department'
        ELSE 'Apparel Area'
    END AS Location,
    'Heavy load test purchase order item' AS Notes,
    CASE
        WHEN p.CategoryId IN (4, 5, 6) THEN datetime('now', '+' || RandomInt(30, 90) || ' days')
        ELSE NULL
    END AS ExpiryDate
FROM PurchaseOrders po
CROSS JOIN (
    SELECT p.*, ROW_NUMBER() OVER (ORDER BY RANDOM()) AS rn
    FROM Products p
    WHERE p.IsActive = 1
) p
WHERE po.OrderNumber LIKE 'HEAVY-PO-%'
AND p.rn <= 5;  -- Each PO has 5 different products

-- Generate inventory transactions for received purchase orders
INSERT INTO InventoryTransactions (
    ProductId, TransactionType, Quantity, UnitPrice, Reference, Notes, TransactionDate, UserId
)
SELECT
    poi.ProductId,
    'Purchase' AS TransactionType,
    poi.Quantity AS Quantity,
    poi.UnitCost AS UnitPrice,
    po.OrderNumber AS Reference,
    'Heavy load test purchase' AS Notes,
    po.DueDate AS TransactionDate,
    po.CreatedByUserId AS UserId
FROM PurchaseOrderItems poi
JOIN PurchaseOrders po ON poi.PurchaseOrderId = po.Id
WHERE po.OrderNumber LIKE 'HEAVY-PO-%'
AND po.Status IN ('Received', 'Completed');

-- Generate cash transactions for cash drawer activities
-- Opening balances
INSERT INTO CashTransactions (
    CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById
)
SELECT 
    cd.Id AS CashDrawerId,
    'Opening Balance' AS Type,
    cd.OpeningBalance AS Amount,
    cd.OpenedAt AS Timestamp,
    'OPEN-HEAVY-' || STRFTIME('%Y%m%d', cd.OpenedAt) AS Reference,
    'Daily opening' AS Reason,
    'Heavy load test opening balance' AS Notes,
    cd.OpenedById AS PerformedById
FROM CashDrawers cd
WHERE cd.Notes LIKE '%Heavy Load%';

-- Cash sales payments
INSERT INTO CashTransactions (
    CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById
)
SELECT 
    cd.Id AS CashDrawerId,
    'Sale Payment' AS Type,
    s.AmountPaid AS Amount,
    s.SaleDate AS Timestamp,
    s.InvoiceNumber AS Reference,
    'Cash sale' AS Reason,
    'Heavy load test cash payment' AS Notes,
    s.UserId AS PerformedById
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash' AND s.InvoiceNumber LIKE 'HEAVY-%';

-- Closing balances
INSERT INTO CashTransactions (
    CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById
)
SELECT 
    cd.Id AS CashDrawerId,
    'Closing Balance' AS Type,
    -cd.ActualBalance AS Amount, -- Negative as money is removed
    cd.ClosedAt AS Timestamp,
    'CLOSE-HEAVY-' || STRFTIME('%Y%m%d', cd.ClosedAt) AS Reference,
    'Daily closing' AS Reason,
    CASE
        WHEN cd.Difference = 0 THEN 'Balanced perfectly'
        WHEN cd.Difference > 0 THEN 'Over by $' || ROUND(cd.Difference, 2)
        ELSE 'Short by $' || ROUND(ABS(cd.Difference), 2)
    END AS Notes,
    cd.ClosedById AS PerformedById
FROM CashDrawers cd
WHERE cd.Notes LIKE '%Heavy Load%';

-- Generate loyalty transactions for each customer purchase
INSERT INTO LoyaltyTransactions (
    CustomerId, Points, Description, TransactionDate
)
SELECT
    s.CustomerId,
    CAST(s.GrandTotal * (
        SELECT lp.PointsPerDollar * lt.PointsMultiplier
        FROM Customers c
        JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
        JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
        WHERE c.Id = s.CustomerId
    ) AS INTEGER) AS Points,
    'Points earned from purchase ' || s.InvoiceNumber AS Description,
    s.SaleDate AS TransactionDate
FROM Sales s
WHERE s.CustomerId IS NOT NULL AND s.InvoiceNumber LIKE 'HEAVY-%';

-- Generate more point redemptions (about 20% of sales with customers - higher than standard test)
INSERT INTO LoyaltyTransactions (
    CustomerId, Points, Description, TransactionDate
)
SELECT
    s.CustomerId,
    -CAST(RandomInt(100, 1000) AS INTEGER) AS Points,
    'Points redemption on ' || DATE(s.SaleDate) AS Description,
    s.SaleDate AS TransactionDate
FROM Sales s
WHERE s.CustomerId IS NOT NULL AND s.InvoiceNumber LIKE 'HEAVY-%'
AND RandomInt(1, 5) = 1 -- 20% of sales
GROUP BY s.CustomerId, DATE(s.SaleDate)
HAVING SUM(s.GrandTotal) > 20; -- Only on days with significant purchases

-- Update customer loyalty points based on transactions
UPDATE Customers
SET LoyaltyPoints = (
    SELECT COALESCE(SUM(Points), 0)
    FROM LoyaltyTransactions
    WHERE CustomerId = Customers.Id
);

-- Update customer loyalty tiers based on points
UPDATE Customers
SET LoyaltyTierId = (
    SELECT lt.Id
    FROM LoyaltyTiers lt
    WHERE lt.LoyaltyProgramId = 1 -- Only one loyalty program
    AND lt.MinimumPoints <= Customers.LoyaltyPoints
    ORDER BY lt.MinimumPoints DESC
    LIMIT 1
);

-- Generate user favorites (each user has many favorites)
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
SELECT DISTINCT
    u.Id AS UserId,
    p.Id AS ProductId,
    datetime('now', '-' || RandomInt(1, 31) || ' days', '+' || RandomInt(0, 23) || ' hours') AS CreatedAt
FROM Users u
CROSS JOIN Products p
WHERE RandomInt(1, 3) = 1 -- Only select about 33% of products
AND NOT EXISTS (
    SELECT 1 FROM UserFavorites uf 
    WHERE uf.UserId = u.Id AND uf.ProductId = p.Id
);

-- Clean up temporary tables
DROP TABLE IF EXISTS DateRange;
DROP TABLE IF EXISTS SalesPerDay;
DROP TABLE IF EXISTS TempSales;
DROP TABLE IF EXISTS TempSaleItems;
DROP TABLE IF EXISTS ProductWeights;

-- Re-enable foreign keys
PRAGMA foreign_keys = ON;

-- Restore performance for normal operation
PRAGMA synchronous = NORMAL;
PRAGMA journal_mode = DELETE;
PRAGMA temp_store = DEFAULT;
PRAGMA cache_size = -2000; -- Reset to default

COMMIT;

-- Clean up temporary tables
DROP TABLE IF EXISTS saved_triggers; 