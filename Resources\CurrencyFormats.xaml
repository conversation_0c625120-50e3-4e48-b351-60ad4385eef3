<?xml version="1.0" encoding="utf-8"?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- Currency Format Resources - Moved to language-specific files -->
    <!-- CurrencySymbol and CurrencyFormat are now defined in Strings.en.xaml, Strings.ar.xaml, etc. -->
    <system:String x:Key="NumberFormat">{0:N0}</system:String>
    <system:String x:Key="DecimalFormat">{0:N2}</system:String>
    
    <!-- Common Number Labels -->
    <system:String x:Key="Unit">Unit</system:String>
    <system:String x:Key="Qty">Qty</system:String>
    
    <!-- Receipt Text -->
    <system:String x:Key="ReceiptSubtotal">Subtotal: {0:N2} DA</system:String>
    <system:String x:Key="ReceiptDiscount">Discount: {0:N2} DA</system:String>
    <system:String x:Key="ReceiptTax">Tax: {0:N2} DA</system:String>
    <system:String x:Key="ReceiptTotal">TOTAL: {0:N2} DA</system:String>
    <system:String x:Key="ThankYouMessage">Thank you for your business!</system:String>

</ResourceDictionary> 