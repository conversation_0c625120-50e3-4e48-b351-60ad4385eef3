using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Commands;

namespace POSSystem.ViewModels
{
    public class UserPermissionsViewModel : INotifyPropertyChanged
    {
        private UserPermissions _permissions;

        public UserPermissions Permissions
        {
            get => _permissions;
            set
            {
                _permissions = value;
                OnPropertyChanged();
            }
        }

        public ICommand SelectAllCommand { get; }
        public ICommand ClearAllCommand { get; }

        public UserPermissionsViewModel()
        {
            Permissions = new UserPermissions();
            SelectAllCommand = new RelayCommand(_ => SelectAll());
            ClearAllCommand = new RelayCommand(_ => ClearAll());
        }

        public void LoadPermissions(UserPermissions permissions)
        {
            if (permissions == null)
            {
                // If Permissions is null, create a new one
                if (Permissions == null)
                {
                    Permissions = new UserPermissions
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                }
                else
                {
                    // Reset existing permissions to default values
                    Permissions.Id = 0;
                    Permissions.UserId = 0;
                    Permissions.CanCreateSales = false;
                    Permissions.CanVoidSales = false;
                    Permissions.CanApplyDiscount = false;
                    Permissions.CanViewSalesHistory = false;
                    Permissions.CanManageProducts = false;
                    Permissions.CanManageCategories = false;
                    Permissions.CanViewInventory = false;
                    Permissions.CanAdjustInventory = false;
                    Permissions.CanManageExpenses = false;
                    Permissions.CanManageCashDrawer = false;
                    Permissions.CanViewReports = false;
                    Permissions.CanManagePrices = false;
                    Permissions.CanManageCustomers = false;
                    Permissions.CanManageSuppliers = false;
                    Permissions.CanManageUsers = false;
                    Permissions.CanManageRoles = false;
                    Permissions.CanAccessSettings = false;
                    Permissions.CanViewLogs = false;
                    Permissions.CreatedAt = DateTime.Now;
                    Permissions.UpdatedAt = DateTime.Now;
                }
                return;
            }

            // Update existing Permissions object instead of creating a new one
            // This preserves data binding to the UI
            if (Permissions == null)
            {
                Permissions = new UserPermissions();
            }

            Permissions.Id = permissions.Id;
            Permissions.UserId = permissions.UserId;
            Permissions.CanCreateSales = permissions.CanCreateSales;
            Permissions.CanVoidSales = permissions.CanVoidSales;
            Permissions.CanApplyDiscount = permissions.CanApplyDiscount;
            Permissions.CanViewSalesHistory = permissions.CanViewSalesHistory;
            Permissions.CanManageProducts = permissions.CanManageProducts;
            Permissions.CanManageCategories = permissions.CanManageCategories;
            Permissions.CanViewInventory = permissions.CanViewInventory;
            Permissions.CanAdjustInventory = permissions.CanAdjustInventory;
            Permissions.CanManageExpenses = permissions.CanManageExpenses;
            Permissions.CanManageCashDrawer = permissions.CanManageCashDrawer;
            Permissions.CanViewReports = permissions.CanViewReports;
            Permissions.CanManagePrices = permissions.CanManagePrices;
            Permissions.CanManageCustomers = permissions.CanManageCustomers;
            Permissions.CanManageSuppliers = permissions.CanManageSuppliers;
            Permissions.CanManageUsers = permissions.CanManageUsers;
            Permissions.CanManageRoles = permissions.CanManageRoles;
            Permissions.CanAccessSettings = permissions.CanAccessSettings;
            Permissions.CanViewLogs = permissions.CanViewLogs;
            Permissions.CreatedAt = permissions.CreatedAt;
            Permissions.UpdatedAt = DateTime.Now;
        }

        private void SelectAll()
        {
            // Update existing Permissions object directly to preserve data binding
            if (Permissions != null)
            {
                Permissions.CanCreateSales = true;
                Permissions.CanVoidSales = true;
                Permissions.CanApplyDiscount = true;
                Permissions.CanViewSalesHistory = true;
                Permissions.CanManageProducts = true;
                Permissions.CanManageCategories = true;
                Permissions.CanViewInventory = true;
                Permissions.CanAdjustInventory = true;
                Permissions.CanManageExpenses = true;
                Permissions.CanManageCashDrawer = true;
                Permissions.CanViewReports = true;
                Permissions.CanManagePrices = true;
                Permissions.CanManageCustomers = true;
                Permissions.CanManageSuppliers = true;
                Permissions.CanManageUsers = true;
                Permissions.CanManageRoles = true;
                Permissions.CanAccessSettings = true;
                Permissions.CanViewLogs = true;
            }
        }

        private void ClearAll()
        {
            // Update existing Permissions object directly to preserve data binding
            if (Permissions != null)
            {
                Permissions.CanCreateSales = false;
                Permissions.CanVoidSales = false;
                Permissions.CanApplyDiscount = false;
                Permissions.CanViewSalesHistory = false;
                Permissions.CanManageProducts = false;
                Permissions.CanManageCategories = false;
                Permissions.CanViewInventory = false;
                Permissions.CanAdjustInventory = false;
                Permissions.CanManageExpenses = false;
                Permissions.CanManageCashDrawer = false;
                Permissions.CanViewReports = false;
                Permissions.CanManagePrices = false;
                Permissions.CanManageCustomers = false;
                Permissions.CanManageSuppliers = false;
                Permissions.CanManageUsers = false;
                Permissions.CanManageRoles = false;
                Permissions.CanAccessSettings = false;
                Permissions.CanViewLogs = false;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 