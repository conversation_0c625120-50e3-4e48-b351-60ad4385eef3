using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Data;
using POSSystem.Helpers;
using POSSystem.Collections;
using POSSystem.Services.UI;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using System.Threading;
using System.Diagnostics;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: Enhanced ProductsViewModel with advanced UI rendering optimizations
    /// </summary>
    public class ProductsViewModel : ViewModelBase
    {
        private readonly RepositoryServiceAdapter _repositoryAdapter;
        private readonly DatabaseService _dbService; // Keep for fallback during migration
        public readonly ProductLookupService _productLookupService;
        private readonly IAlertService _alertService;
        private readonly POSSystem.Services.Caching.CachedRepositoryService _cachedRepository;
        private readonly POSSystem.Services.QueryOptimization.OptimizedQueryService _optimizedQueries;
        private readonly POSSystem.Services.BackgroundLoading.ProgressiveDataLoader _progressiveLoader;
        private readonly POSSystem.Services.Caching.ProductCacheService _productCache;
        private readonly POSSystem.Services.Performance.ProductsPerformanceMonitor _performanceMonitor;
        private readonly AdvancedVirtualizationService<Product> _virtualizationService;

        // ✅ CRITICAL UI OPTIMIZATION: Use optimized collections for better performance
        private OptimizedObservableCollection<Product> _products;
        private List<Product> _allProducts;
        private OptimizedObservableCollection<Category> _categories;
        private OptimizedObservableCollection<UnitOfMeasure> _unitsOfMeasure;
        private int _lowStockCount;
        private int _outOfStockCount;
        private int _totalProducts;
        private decimal _inventoryValue;
        private decimal _inventoryCost;
        private string _currentBarcode;
        private Product _currentProduct;
        private int _currentPage = 1;
        private int _pageSize = 50;
        private int _totalPages;
        private bool _isLoading;
        private string _searchText;
        private Category _selectedCategory;
        private string _selectedStockFilter;
        private CancellationTokenSource _searchCancellation;
        private ICommand _clearFiltersCommand;
        private readonly object _searchLock = new object();
        private Task _currentSearchTask;
        private const int SEARCH_DELAY_MS = 300;

        // Add a flag to control property change notifications
        private bool _isBatchUpdate = false;

        // Use CollectionViewSource for smoother rendering
        private CollectionViewSource _productsViewSource;
        public ICollectionView ProductsView => _productsViewSource?.View;

        // Add a field to hold our debounced search action
        private Action<string> _debouncedSearch;

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();

                // Update dependent properties
                OnPropertyChanged(nameof(CanGoToPreviousPage));
                OnPropertyChanged(nameof(CanGoToNextPage));
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText == value) return;

                _searchText = value;
                OnPropertyChanged();

                // Cancel previous search
                _searchCancellation?.Cancel();
                _searchCancellation = new CancellationTokenSource();

                // Initialize debounced search if needed
                if (_debouncedSearch == null)
                {
                    _debouncedSearch = SearchHelper.Debounce<string>(searchTerm =>
                    {
                        // We need to execute the search but can't directly await inside a lambda
                        // So we'll just start the task and capture the token for cancellation
                        var token = _searchCancellation.Token;
                        Task.Run(async () =>
                        {
                            try
                            {
                                // Reset to first page when searching
                                await ExecuteSearchAsync(searchTerm, true);
                            }
                            catch (TaskCanceledException)
                            {
                                // Search was canceled, ignore
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error in debounced search: {ex.Message}");
                            }
                        }, token);
                    }, SEARCH_DELAY_MS);
                }

                // Trigger the debounced search
                _debouncedSearch(value);
            }
        }

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                _selectedCategory = value;
                OnPropertyChanged();
                // ✅ PERFORMANCE FIX: Use async method to prevent UI blocking
                _ = LoadPagedProductsAsync();
            }
        }

        // Property for ComboBox binding in ProductDialog
        public int SelectedCategoryId
        {
            get => _selectedCategory?.Id ?? 0;
            set
            {
                if (value > 0)
                {
                    _selectedCategory = Categories?.FirstOrDefault(c => c.Id == value);
                }
                else
                {
                    _selectedCategory = null;
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(SelectedCategory));
            }
        }

        // Property for Unit of Measure ComboBox binding in ProductDialog
        private int _selectedUnitOfMeasureId;
        public int SelectedUnitOfMeasureId
        {
            get => _selectedUnitOfMeasureId;
            set
            {
                _selectedUnitOfMeasureId = value;
                OnPropertyChanged();
                // Reset quantity to 1 when unit changes to "piece" or similar single units
                if (value > 0 && UnitsOfMeasure != null)
                {
                    var selectedUnit = UnitsOfMeasure.FirstOrDefault(u => u.Id == value);
                    if (selectedUnit != null && IsSingleUnit(selectedUnit))
                    {
                        ProductQuantity = 1.0m;
                    }
                }
            }
        }

        // Property for Quantity input in ProductDialog
        private decimal _productQuantity = 1.0m;
        public decimal ProductQuantity
        {
            get => _productQuantity;
            set
            {
                _productQuantity = Math.Max(0.001m, value); // Ensure positive numbers only
                OnPropertyChanged();
            }
        }

        // Property for tracking current item type in ProductDialog
        private ProductType _currentItemType = ProductType.Product;
        public ProductType CurrentItemType
        {
            get => _currentItemType;
            set
            {
                _currentItemType = value;
                OnPropertyChanged();
                // Reset quantity to appropriate default when type changes
                if (value == ProductType.Service)
                {
                    ProductQuantity = 1.0m; // Services typically start with 1 unit
                }
                else
                {
                    ProductQuantity = 1.0m; // Products also start with 1 unit
                }
            }
        }

        // Property for tracking whether the product is weight-based in ProductDialog
        private bool _isWeightBased = false;
        public bool IsWeightBased
        {
            get => _isWeightBased;
            set
            {
                if (_isWeightBased != value)
                {
                    System.Diagnostics.Debug.WriteLine($"[VIEWMODEL] IsWeightBased changing from {_isWeightBased} to {value}");
                    _isWeightBased = value;
                    OnPropertyChanged();

                    // Notify the dialog about the change
                    OnSalesMethodChanged?.Invoke(value);

                    // When switching to weight-based, allow decimal quantities
                    // When switching to unit-based, round to whole numbers
                    if (!value && ProductQuantity != Math.Floor(ProductQuantity))
                    {
                        ProductQuantity = Math.Max(1.0m, Math.Floor(ProductQuantity));
                    }

                    System.Diagnostics.Debug.WriteLine($"[VIEWMODEL] IsWeightBased changed to {value}");
                }
            }
        }

        // Event to notify the dialog when sales method changes
        public Action<bool> OnSalesMethodChanged { get; set; }

        public string SelectedStockFilter
        {
            get => _selectedStockFilter;
            set
            {
                _selectedStockFilter = value;
                OnPropertyChanged();
                // ✅ PERFORMANCE FIX: Use async method to prevent UI blocking
                _ = LoadPagedProductsAsync();
            }
        }

        public string CurrentBarcode
        {
            get => _currentBarcode;
            set
            {
                _currentBarcode = value;
                OnPropertyChanged();
            }
        }

        public Product CurrentProduct
        {
            get => _currentProduct;
            set
            {
                _currentProduct = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Optimized collection for better rendering performance
        /// </summary>
        public OptimizedObservableCollection<Product> Products
        {
            get => _products;
            set
            {
                if (SetProperty(ref _products, value))
                {
                    // Update the collection view source with optimized handling
                    if (_productsViewSource == null)
                    {
                        _productsViewSource = new CollectionViewSource { Source = _products };
                        OnPropertyChanged(nameof(ProductsView));
                    }
                    else
                    {
                        Application.Current.Dispatcher.BeginInvoke(() =>
                        {
                            if (_productsViewSource == null)
                            {
                                _productsViewSource = new CollectionViewSource();
                            }
                            _productsViewSource.Source = _products;
                            OnPropertyChanged(nameof(ProductsView));
                        }, DispatcherPriority.DataBind);
                    }
                }
            }
        }

        public List<Product> AllProducts
        {
            get => _allProducts;
            set
            {
                _allProducts = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Optimized collection for categories
        /// </summary>
        public OptimizedObservableCollection<Category> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Optimized collection for units of measure
        /// </summary>
        public OptimizedObservableCollection<UnitOfMeasure> UnitsOfMeasure
        {
            get => _unitsOfMeasure;
            set => SetProperty(ref _unitsOfMeasure, value);
        }

        public int LowStockCount
        {
            get => _lowStockCount;
            set
            {
                _lowStockCount = value;
                OnPropertyChanged();
            }
        }

        public int OutOfStockCount
        {
            get => _outOfStockCount;
            set
            {
                _outOfStockCount = value;
                OnPropertyChanged();
            }
        }

        public int TotalProducts
        {
            get => _totalProducts;
            set
            {
                _totalProducts = value;
                OnPropertyChanged();
            }
        }

        public decimal InventoryValue
        {
            get => _inventoryValue;
            set
            {
                _inventoryValue = value;
                OnPropertyChanged();
            }
        }

        public decimal InventoryCost
        {
            get => _inventoryCost;
            set
            {
                _inventoryCost = value;
                OnPropertyChanged();
            }
        }

        public bool CanGoToPreviousPage => CurrentPage > 1;
        public bool CanGoToNextPage => CurrentPage < TotalPages;

        private RelayCommand _firstPageCommand;
        private RelayCommand _previousPageCommand;
        private RelayCommand _nextPageCommand;
        private RelayCommand _lastPageCommand;

        public ICommand FirstPageCommand => _firstPageCommand ??= new RelayCommand(
            _ => { CurrentPage = 1; _ = LoadPagedProductsAsync(); },
            _ => CanGoToPreviousPage);

        public ICommand PreviousPageCommand => _previousPageCommand ??= new RelayCommand(
            _ => { CurrentPage--; _ = LoadPagedProductsAsync(); },
            _ => CanGoToPreviousPage);

        public ICommand NextPageCommand => _nextPageCommand ??= new RelayCommand(
            _ => { CurrentPage++; _ = LoadPagedProductsAsync(); },
            _ => CanGoToNextPage);

        public ICommand LastPageCommand => _lastPageCommand ??= new RelayCommand(
            _ => { CurrentPage = TotalPages; _ = LoadPagedProductsAsync(); },
            _ => CanGoToNextPage);

        public ICommand ClearFiltersCommand => _clearFiltersCommand ??= new RelayCommand(_ => ClearFilters());

        // NEW: Proper dependency injection constructor with caching, optimization, and progressive loading
        public ProductsViewModel(
            RepositoryServiceAdapter repositoryAdapter,
            IAlertService alertService,
            DatabaseService databaseService,
            POSSystem.Services.Caching.CachedRepositoryService cachedRepository = null,
            POSSystem.Services.QueryOptimization.OptimizedQueryService optimizedQueries = null,
            POSSystem.Services.BackgroundLoading.ProgressiveDataLoader progressiveLoader = null)
        {
            _repositoryAdapter = repositoryAdapter ?? throw new ArgumentNullException(nameof(repositoryAdapter));
            _dbService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _alertService = alertService;
            _cachedRepository = cachedRepository; // Optional for now - fallback to repository adapter
            _optimizedQueries = optimizedQueries; // Optional - for performance optimization
            _progressiveLoader = progressiveLoader; // Optional - for progressive loading
            _productCache = new POSSystem.Services.Caching.ProductCacheService();
            _performanceMonitor = new POSSystem.Services.Performance.ProductsPerformanceMonitor();
            _productLookupService = new ProductLookupService(alertService);
            _searchCancellation = new CancellationTokenSource();

            // ✅ CRITICAL UI OPTIMIZATION: Initialize optimized collections and virtualization
            _products = new OptimizedObservableCollection<Product>();
            _categories = new OptimizedObservableCollection<Category>();
            _unitsOfMeasure = new OptimizedObservableCollection<UnitOfMeasure>();
            _virtualizationService = new AdvancedVirtualizationService<Product>(pageSize: 50, bufferSize: 20);

            // Initialize CollectionViewSource to prevent null reference exceptions
            _productsViewSource = new CollectionViewSource { Source = _products };

            // Subscribe to category changes
            CategoriesViewModel.CategoryChanged += OnCategoryChanged;

            // ✅ FIX: Subscribe to static stock change events from SaleViewModel
            // This ensures ProductsView updates when stock reservations occur
            SaleViewModel.ProductStockChanged += OnProductStockChanged;

            Debug.WriteLine("✅ [PRODUCTS-VM] ProductsViewModel initialized with UI optimizations");
        }

        // LEGACY: Keep for backward compatibility during migration
        public ProductsViewModel(IAlertService alertService) : this(
            App.ServiceProvider?.GetService<RepositoryServiceAdapter>() ??
                throw new InvalidOperationException("RepositoryServiceAdapter not available from DI"),
            alertService,
            App.ServiceProvider?.GetService<DatabaseService>() ??
                throw new InvalidOperationException("DatabaseService not available from DI"))
        {
            System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Using legacy constructor with DI services");
        }

        // Add destructor to unsubscribe from events
        ~ProductsViewModel()
        {
            CategoriesViewModel.CategoryChanged -= OnCategoryChanged;
            SaleViewModel.ProductStockChanged -= OnProductStockChanged;
        }

        private async void OnCategoryChanged(object sender, CategoryUpdateEventArgs e)
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // ✅ Use injected DatabaseService context instead of creating new one
                try
                {
                    var categories = await _dbService.Context.Categories
                        .AsNoTracking()
                        .OrderBy(c => c.Name)
                        .ToListAsync();
                    Categories = new OptimizedObservableCollection<Category>(categories);
                    System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Categories refreshed via DI context");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Error refreshing categories: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// ✅ FIX: Handle stock change events from SaleViewModel to keep ProductsView synchronized
        /// </summary>
        private async void OnProductStockChanged(object sender, SaleViewModel.ProductStockChangedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Received stock change event for product {e.ProductId}, new stock: {e.NewStockQuantity}");

                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    // Find and update the product in our collections
                    var productToUpdate = Products?.FirstOrDefault(p => p.Id == e.ProductId);
                    if (productToUpdate != null)
                    {
                        // ✅ PERFORMANCE FIX: Use async version to prevent UI thread blocking
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                var updatedProduct = await _dbService.GetProductByIdAsync(e.ProductId);
                                if (updatedProduct != null)
                                {
                                    // Update the product in the collection on UI thread
                                    await Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        var index = Products.IndexOf(productToUpdate);
                                        if (index >= 0)
                                        {
                                            Products[index] = updatedProduct;
                                        }
                                    });

                                    System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Updated product {updatedProduct.Name} with new stock: {updatedProduct.StockQuantity}");

                                    // Update statistics to reflect the change
                                    await UpdateProductStatisticsAsync();

                                    // ✅ ENHANCED FIX: Force complete UI refresh to ensure all stock values are updated
                                    await Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        OnPropertyChanged(nameof(ProductsView));
                                        OnPropertyChanged(nameof(Products));
                                    });
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error updating product {e.ProductId}: {ex.Message}");
                            }
                        });
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Product {e.ProductId} not found in current Products collection");

                        // ✅ FIX: If product not found in collection, try to load it and add it
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                var newProduct = await _dbService.GetProductByIdAsync(e.ProductId);
                                if (newProduct != null)
                                {
                                    await Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        // Add the product to the collection if it's not already there
                                        if (Products != null && !Products.Any(p => p.Id == e.ProductId))
                                        {
                                            Products.Add(newProduct);
                                            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Added new product {newProduct.Name} to collection with stock: {newProduct.StockQuantity}");
                                        }

                                        // Force UI refresh
                                        OnPropertyChanged(nameof(ProductsView));
                                        OnPropertyChanged(nameof(Products));
                                    });

                                    // Update statistics to reflect the change
                                    await UpdateProductStatisticsAsync();
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error loading new product {e.ProductId}: {ex.Message}");
                            }
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Error handling stock change event: {ex.Message}");
            }
        }

        /// <summary>
        /// Load only categories and units of measure without affecting product data
        /// Used by refresh operations to avoid overwriting correctly loaded product data
        /// </summary>
        public async Task LoadCategoriesOnly()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Loading categories only...");

                // ✅ Use injected DatabaseService context instead of creating new one
                var context = _dbService.Context;
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                // ✅ Load essential reference data with caching and optimization
                Task<List<Category>> categoriesTask;
                Task<List<UnitOfMeasure>> unitsTask;

                if (_cachedRepository != null)
                {
                    // Use cached repository for reference data (long-term cache)
                    categoriesTask = _cachedRepository.GetCategoriesAsync().ContinueWith(async t => (await t).ToList()).Unwrap();
                    unitsTask = _cachedRepository.GetUnitsOfMeasureAsync().ContinueWith(async t => (await t).ToList()).Unwrap();
                }
                else
                {
                    // Fallback to direct database access
                    categoriesTask = context.Categories.AsNoTracking().ToListAsync();
                    unitsTask = context.UnitsOfMeasure.AsNoTracking().ToListAsync();
                }

                // Wait for reference data
                await Task.WhenAll(categoriesTask, unitsTask);

                var categories = await categoriesTask;
                var unitsOfMeasure = await unitsTask;

                // Update UI with reference data only
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    PerformanceHelper.BatchUpdate(() =>
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Loading {categories.Count} categories");
                        Categories = new OptimizedObservableCollection<Category>(categories);
                        UnitsOfMeasure = new OptimizedObservableCollection<UnitOfMeasure>(unitsOfMeasure);
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Categories collection now has {Categories.Count} items");
                    });
                });

                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Categories-only loading completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Error loading categories: {ex.Message}");
                throw;
            }
        }

        public async Task LoadInitialData()
        {
            IsLoading = true;
            bool wasInBatchUpdate = _isBatchUpdate;

            try
            {
                // ✅ Use injected DatabaseService context instead of creating new one
                var context = _dbService.Context;
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    _isBatchUpdate = true;

                    // Initialize the CollectionViewSource properly
                    if (_productsViewSource == null)
                    {
                        _productsViewSource = new CollectionViewSource();
                        OnPropertyChanged(nameof(ProductsView));
                    }

                    // Create observable collection with initial capacity to reduce resizing
                    if (Products == null)
                    {
                        Products = new OptimizedObservableCollection<Product>();
                        // Ensure _productsViewSource is initialized before using it
                        if (_productsViewSource == null)
                        {
                            _productsViewSource = new CollectionViewSource();
                        }
                        _productsViewSource.Source = Products;
                    }

                    // ✅ Load essential data with caching and optimization for better performance
                    Task<List<Category>> categoriesTask;
                    Task<List<UnitOfMeasure>> unitsTask;

                    if (_cachedRepository != null)
                    {
                        // ✅ CRITICAL FIX: Remove .Result to prevent UI thread blocking
                        // Use cached repository for reference data (long-term cache)
                        categoriesTask = _cachedRepository.GetCategoriesAsync().ContinueWith(async t => (await t).ToList()).Unwrap();
                        unitsTask = _cachedRepository.GetUnitsOfMeasureAsync().ContinueWith(async t => (await t).ToList()).Unwrap();
                    }
                    else
                    {
                        // Fallback to direct database access
                        categoriesTask = context.Categories.AsNoTracking().ToListAsync();
                        unitsTask = context.UnitsOfMeasure.AsNoTracking().ToListAsync();
                    }

                    // ✅ OPTIMIZED: Only count active products
                    var countTask = context.Products.Where(p => p.IsActive).CountAsync();

                    // Wait for all essential data
                    await Task.WhenAll(categoriesTask, unitsTask, countTask);

                    // ✅ CRITICAL FIX: Remove .Result calls to prevent UI thread blocking
                    var categories = await categoriesTask;
                    var unitsOfMeasure = await unitsTask;
                    var totalProductCount = await countTask;

                    // Update UI with initial data - use PerformanceHelper.BatchUpdate
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() =>
                        {
                            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Loading {categories.Count} categories");
                            Categories = new OptimizedObservableCollection<Category>(categories);

                            UnitsOfMeasure = new OptimizedObservableCollection<UnitOfMeasure>(unitsOfMeasure);
                            TotalProducts = totalProductCount;
                            TotalPages = (int)Math.Ceiling(TotalProducts / (double)PageSize);

                            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Categories collection now has {Categories.Count} items");
                        });
                    });

                    // ✅ OPTIMIZED: Load first page of products with performance optimization
                    List<Product> initialProducts;

                    if (_optimizedQueries != null)
                    {
                        // ❌ DISABLED: Optimized projection query creates Product objects without batch relationships
                        // This causes StockQuantity property to fall back to stale _stockQuantity field
                        // Use the fallback query instead which properly includes batch data
                        System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Skipping optimized queries to ensure batch data is loaded");

                        // Force use of fallback query with proper batch relationships
                        initialProducts = await context.Products
                            .Include(p => p.Category)
                            .Include(p => p.Barcodes)
                            .Include(p => p.Batches) // ✅ CRITICAL: Include batches for accurate stock calculation
                            .Where(p => p.IsActive)
                            .OrderByDescending(p => p.CreatedAt)
                            .ThenBy(p => p.Name)
                            .Skip((CurrentPage - 1) * PageSize)
                            .Take(PageSize)
                            .AsNoTracking()
                            .ToListAsync();
                    }
                    else
                    {
                        // ✅ FIX: Fallback to standard query with active filter AND batch relationships
                        // Sort by CreatedAt descending first to show newest products, then by name
                        initialProducts = await context.Products
                            .Include(p => p.Category)
                            .Include(p => p.Barcodes)
                            .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                            .Where(p => p.IsActive && p.Id > 0) // ✅ CUSTOM PRODUCT FIX: Add active filter and exclude custom products (negative IDs)
                            .OrderByDescending(p => p.CreatedAt)
                            .ThenBy(p => p.Name)
                            .Skip((CurrentPage - 1) * PageSize)
                            .Take(PageSize)
                            .AsNoTracking()
                            .ToListAsync();
                    }

                    // ✅ FIX: Set CurrentBatchPrice properties for products loaded with full Batches collections
                    foreach (var product in initialProducts)
                    {
                        SetCurrentBatchPrices(product);
                    }

                    // ✅ PERFORMANCE FIX: Use efficient collection replacement instead of individual adds
                    PerformanceHelper.BatchUpdate(() =>
                    {
                        PerformanceHelper.ReplaceCollectionContent(Products, initialProducts);
                        // Ensure _productsViewSource is initialized before using it
                        if (_productsViewSource == null)
                        {
                            _productsViewSource = new CollectionViewSource();
                        }
                        _productsViewSource.Source = Products;
                        OnPropertyChanged(nameof(ProductsView));
                    }, Application.Current.Dispatcher);

                    System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Initial data loaded using DI context");

                // ✅ FIX: Remove background loading that overwrites correctly loaded product data
                // The initial products are already loaded with correct batch relationships above
                // Background loading was creating new Product objects without Batches collection
                // causing StockQuantity property to fall back to potentially stale _stockQuantity field

                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Skipping background product loading to preserve correct batch data");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading initial data: {ex.Message}",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Always reset the batch update flag and loading state
                _isBatchUpdate = wasInBatchUpdate;
                IsLoading = false;

                // Notify critical properties
                if (!_isBatchUpdate)
                {
                    OnPropertyChanged(nameof(ProductsView));
                    OnPropertyChanged(nameof(CurrentPage));
                    OnPropertyChanged(nameof(TotalPages));
                }
            }
        }

        // NEW: High-performance repository-based loading with emergency timeout protection
        public async Task LoadPagedProductsAsync()
        {
            if (IsLoading) return;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var operationId = _performanceMonitor?.StartOperation("LoadPagedProductsAsync",
                $"Page {CurrentPage}, Size {PageSize}, Category: {SelectedCategory?.Name ?? "All"}") ?? "N/A";

            try
            {
                IsLoading = true;
                _searchCancellation?.Cancel();
                _searchCancellation = new CancellationTokenSource();

                Debug.WriteLine($"[PERF-{operationId}] Starting optimized product loading...");

                // ✅ EMERGENCY FIX: Use emergency timeout protection to prevent severe blocking
                var products = await EmergencyPerformanceFix.ExecuteWithEmergencyTimeout(async () =>
                {
                    return await PerformanceHelper.ExecuteOnBackgroundThreadAsync(async () =>
                    {
                        try
                        {
                            // Use repository for much better performance
                            var repositoryProducts = await _repositoryAdapter.GetProductsPagedAsync(CurrentPage, PageSize);

                            // Apply client-side filtering if needed (repository doesn't support all filters yet)
                            if (SelectedCategory != null)
                            {
                                repositoryProducts = repositoryProducts.Where(p => p.CategoryId == SelectedCategory.Id).ToList();
                            }

                            if (!string.IsNullOrEmpty(SelectedStockFilter) && SelectedStockFilter != "All")
                            {
                                switch (SelectedStockFilter)
                                {
                                    case "LowStock":
                                        repositoryProducts = repositoryProducts.Where(p => p.IsLowStock).ToList();
                                        break;
                                    case "OutOfStock":
                                        repositoryProducts = repositoryProducts.Where(p => p.IsOutOfStock).ToList();
                                        break;
                                }
                            }

                            // Ensure CurrentBatchPrice properties are set for grid binding
                            if (repositoryProducts != null)
                            {
                                foreach (var prod in repositoryProducts)
                                {
                                    SetCurrentBatchPrices(prod);
                                }
                            }
                            return repositoryProducts;
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[PERF-{operationId}] Repository loading failed: {ex.Message}");
                            throw;
                        }
                    }, $"Product Loading (Page {CurrentPage})");
                }, 3000, $"LoadPagedProducts_Page{CurrentPage}"); // 3 second timeout

                // ✅ PERFORMANCE FIX: Update UI efficiently with batching and emergency yields
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    PerformanceHelper.BatchUpdate(() =>
                    {
                        PerformanceHelper.ReplaceCollectionContent(Products, products);
                        TotalPages = (int)Math.Ceiling(TotalProducts / (double)PageSize);

                        // Ensure _productsViewSource is initialized before using it
                        if (_productsViewSource == null)
                        {
                            _productsViewSource = new CollectionViewSource();
                        }
                        _productsViewSource.Source = Products;
                        OnPropertyChanged(nameof(ProductsView));
                    });
                }, DispatcherPriority.Normal, _searchCancellation.Token);

                // Force UI yield after update
                await EmergencyPerformanceFix.ForceUIYield();

                // Update statistics efficiently in background
                _ = Task.Run(async () => await UpdateProductStatisticsAsync());

                stopwatch.Stop();
                Debug.WriteLine($"[PERF-{operationId}] ✅ Loaded {products.Count} products in {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (TimeoutException tex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[PERF-{operationId}] 🚨 TIMEOUT: Operation cancelled after timeout: {tex.Message}");

                // Show minimal products to prevent complete UI freeze
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    if (Products.Count == 0)
                    {
                        Products = new OptimizedObservableCollection<Product>();
                    }
                    _alertService?.ShowError("Product loading timed out. Please try again.");
                });
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[PERF-{operationId}] ❌ Failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");

                // Fallback to legacy method with timeout
                try
                {
                    await EmergencyPerformanceFix.ExecuteWithEmergencyTimeout<object>(
                        async () => { await LoadPagedProductsLegacy(); return null; },
                        5000,
                        "LoadPagedProductsLegacy_Fallback");
                }
                catch (Exception fallbackEx)
                {
                    Debug.WriteLine($"[PERF-{operationId}] Fallback also failed: {fallbackEx.Message}");
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _alertService?.ShowError($"Failed to load products: {ex.Message}");
                    });
                }
            }
            finally
            {
                _performanceMonitor?.StopOperation("LoadPagedProductsAsync", operationId,
                    $"Completed with {Products?.Count ?? 0} products");
                await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = false);
            }
        }

        // LEGACY: Keep original method as fallback
        public async Task LoadPagedProductsLegacy()
        {
            if (IsLoading) return;

            // Store the initial state of the batch update flag
            bool wasInBatchUpdate = _isBatchUpdate;

            try
            {
                IsLoading = true;
                _searchCancellation?.Cancel();
                _searchCancellation = new CancellationTokenSource();

                using (var context = new POSDbContext())
                {
                    _isBatchUpdate = true;

                    // Configure context for better performance
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    context.ChangeTracker.AutoDetectChangesEnabled = false;

                    // Step 1: Build efficient query with only required fields using projection
                    var query = context.Products
                        .AsNoTracking()
                        .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                        .Include(p => p.Category)
                        .Include(p => p.Barcodes)
                        .Where(p => p.IsActive && p.Id > 0) // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs)
                        .Select(p => new
                        {
                            p.Id,
                            p.Name,
                            p.SKU,
                            p.Description,
                            p.PurchasePrice,
                            p.SellingPrice,
                            p.StockQuantity,
                            p.MinimumStock,
                            p.ReorderPoint,
                            p.TrackBatches,
                            p.IsActive,
                            p.ExpiryDate,
                            p.ImageData,
                            p.LoyaltyPoints,
                            p.UnitOfMeasureId,
                            p.SupplierId,
                            p.CreatedAt,
                            CategoryId = p.CategoryId,
                            CategoryName = p.Category.Name,
                            Barcodes = p.Barcodes.Select(b => new { b.Id, b.Barcode, b.IsPrimary, b.Description }).ToList(),
                            HasBatches = p.Batches.Any(),
                            BatchCount = p.TrackBatches ? p.Batches.Count : 0,
                            TotalBatchQuantity = p.TrackBatches ? p.Batches.Sum(b => b.Quantity) : 0,

                            // ✅ FIX: Include batch pricing data for CurrentBatchPrice calculation
                            CurrentBatchSellingPrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                                p.Batches.Where(b => b.Quantity > 0)
                                         .OrderBy(b => b.CreatedAt)
                                         .ThenBy(b => b.Id)
                                         .Select(b => b.SellingPrice)
                                         .FirstOrDefault() : p.SellingPrice,
                            CurrentBatchPurchasePrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                                p.Batches.Where(b => b.Quantity > 0)
                                         .OrderBy(b => b.CreatedAt)
                                         .ThenBy(b => b.Id)
                                         .Select(b => b.PurchasePrice)
                                         .FirstOrDefault() : p.PurchasePrice
                        });

                    // Step 2: Apply filters efficiently
                    if (SelectedCategory != null)
                    {
                        query = query.Where(p => p.CategoryId == SelectedCategory.Id);
                    }

                    if (!string.IsNullOrEmpty(SelectedStockFilter) && SelectedStockFilter != "All")
                    {
                        switch (SelectedStockFilter)
                        {
                            case "LowStock":
                                query = query.Where(p =>
                                    (!p.TrackBatches && p.StockQuantity <= p.MinimumStock && p.StockQuantity > 0) ||
                                    (p.TrackBatches && (p.StockQuantity + p.TotalBatchQuantity) <= p.MinimumStock &&
                                    (p.StockQuantity + p.TotalBatchQuantity) > 0));
                                break;
                            case "OutOfStock":
                                query = query.Where(p =>
                                    (!p.TrackBatches && p.StockQuantity == 0) ||
                                    (p.TrackBatches && p.StockQuantity == 0 && p.TotalBatchQuantity == 0));
                                break;
                        }
                    }

                    // Step 3: Get count and data in parallel with optimized queries
                    var countTask = query.CountAsync(_searchCancellation.Token);
                    var productsQueryResult = await query
                        .OrderByDescending(p => p.CreatedAt)
                        .ThenBy(p => p.Name)
                        .Skip((CurrentPage - 1) * PageSize)
                        .Take(PageSize)
                        .ToListAsync(_searchCancellation.Token);

                    // Get total count
                    var totalCount = await countTask;

                    // Step 4: Map the results to our Product objects
                    var products = productsQueryResult.Select(p => new Product
                    {
                        Id = p.Id,
                        Name = p.Name,
                        SKU = p.SKU,
                        Description = p.Description,
                        PurchasePrice = p.PurchasePrice,
                        SellingPrice = p.SellingPrice,
                        // ✅ FIX: Use calculated batch quantity for batch-tracked products, otherwise use stock quantity
                        StockQuantity = p.TrackBatches ? p.TotalBatchQuantity : p.StockQuantity,
                        MinimumStock = p.MinimumStock,
                        ReorderPoint = p.ReorderPoint,
                        TrackBatches = p.TrackBatches,
                        IsActive = p.IsActive,
                        ExpiryDate = p.ExpiryDate,
                        ImageData = p.ImageData,
                        LoyaltyPoints = p.LoyaltyPoints,
                        UnitOfMeasureId = p.UnitOfMeasureId,
                        SupplierId = p.SupplierId,
                        CategoryId = p.CategoryId,
                        Category = new Category { Id = p.CategoryId, Name = p.CategoryName },
                        Barcodes = p.Barcodes.Select(b => new ProductBarcode
                        {
                            Id = b.Id,
                            ProductId = p.Id,
                            Barcode = b.Barcode,
                            IsPrimary = b.IsPrimary,
                            Description = b.Description
                        }).ToList(),
                        // ✅ FIX: Set pre-calculated batch pricing values
                        CurrentBatchPrice = p.CurrentBatchSellingPrice,
                        CurrentBatchPurchasePrice = p.CurrentBatchPurchasePrice
                    }).ToList();

                    // Step 5: Only load batch details if absolutely necessary
                    if (products.Any(p => p.TrackBatches))
                    {
                        var productIdsNeedingBatches = products.Where(p => p.TrackBatches).Select(p => p.Id).ToList();

                        // Use a single query to get all batches at once rather than per product
                        var batches = await context.BatchStock
                            .AsNoTracking()
                            .Where(b => productIdsNeedingBatches.Contains(b.ProductId))
                            .Select(b => new {
                                b.Id,
                                b.ProductId,
                                b.BatchNumber,
                                b.Quantity,
                                b.ManufactureDate,
                                b.ExpiryDate,
                                b.PurchasePrice,
                                b.Location,
                                b.Notes,
                                b.CreatedAt
                            })
                            .ToListAsync(_searchCancellation.Token);

                        // Group batches by product ID for efficient assignment
                        var batchesByProduct = batches.GroupBy(b => b.ProductId).ToDictionary(g => g.Key, g => g.ToList());

                        // Associate batches with their respective products
                        foreach (var product in products.Where(p => p.TrackBatches))
                        {
                            if (batchesByProduct.TryGetValue(product.Id, out var productBatches))
                            {
                                product.Batches = productBatches.Select(b => new BatchStock
                                {
                                    Id = b.Id,
                                    ProductId = b.ProductId,
                                    BatchNumber = b.BatchNumber,
                                    Quantity = b.Quantity,
                                    ManufactureDate = b.ManufactureDate,
                                    ExpiryDate = b.ExpiryDate,
                                    PurchasePrice = b.PurchasePrice,
                                    Location = b.Location,
                                    Notes = b.Notes,
                                    CreatedAt = b.CreatedAt
                                }).ToHashSet();
                            }

                            // ✅ Ensure CurrentBatchPrice properties are set for search results
                            SetCurrentBatchPrices(product);
                        }
                    }

                    // Step 6: Update the UI with the new data using batch update
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() =>
                        {
                            TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize);
                            TotalProducts = totalCount;
                            Products = new OptimizedObservableCollection<Product>(products);

                            // Update the collection view source
                            // Ensure _productsViewSource is initialized before using it
                            if (_productsViewSource == null)
                            {
                                _productsViewSource = new CollectionViewSource();
                            }
                            _productsViewSource.Source = Products;
                            OnPropertyChanged(nameof(ProductsView));
                        });
                    });

                    // Step 7: Update statistics separately for better UI responsiveness
                    await Task.Run(async () => await UpdateStatisticsAsync());

                    _isBatchUpdate = false;
                    NotifyBatchPropertyChanges();
                }
            }
            catch (TaskCanceledException)
            {
                // Operation was cancelled, ignore
            }
            catch (Exception ex)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageBox.Show($"Error loading products: {ex.Message}",
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
            finally
            {
                // Always reset the batch update flag to its original state
                _isBatchUpdate = wasInBatchUpdate;

                // Always set IsLoading to false
                IsLoading = false;

                // ✅ PERFORMANCE MONITORING: Stop timing
                UIPerformanceMonitor.StopTiming("ProductsViewModel.LoadPagedProducts");
            }
        }

        // OPTIMIZED: High-performance method with comprehensive monitoring
        public async Task LoadPagedProducts()
        {
            // ✅ PERFORMANCE FIX: Always use async version to prevent UI blocking
            await LoadPagedProductsAsync();
        }

        /// <summary>
        /// ULTRA-OPTIMIZED: High-performance product loading with comprehensive optimizations
        /// Target: <500ms execution time
        /// </summary>
        private async Task LoadPagedProductsOptimized(string operationId)
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                _searchCancellation?.Cancel();
                _searchCancellation = new CancellationTokenSource();

                // Step 1: Load data on background thread with caching
                var dataLoadId = _performanceMonitor.StartOperation("DataLoad",
                    $"Page {CurrentPage}, Cache enabled");

                List<Product> products;

                // Execute data loading on background thread to prevent UI blocking
                products = await PerformanceHelper.ExecuteOnBackgroundThreadAsync(async () =>
                {
                    // Check cache first for significant performance boost
                    return await _productCache.GetCachedProductsAsync(
                        CurrentPage,
                        PageSize,
                        SelectedCategory?.Id,
                        async () =>
                        {
                            // Cache miss - load from database with optimized query
                            var dbLoadId = _performanceMonitor.StartOperation("DatabaseQuery",
                                "Cache miss - loading from database");
                            try
                            {
                                var result = await LoadProductsFromDatabase(operationId);
                                _performanceMonitor.StopOperation("DatabaseQuery", dbLoadId,
                                    $"Loaded {result.Count} products from database");
                                return result;
                            }
                            catch (Exception ex)
                            {
                                _performanceMonitor.StopOperation("DatabaseQuery", dbLoadId,
                                    $"Failed: {ex.Message}");
                                throw;
                            }
                        });
                }, $"Product Loading (Page {CurrentPage})");

                _performanceMonitor.StopOperation("DataLoad", dataLoadId,
                    $"Got {products.Count} products");


                // Step 2: Execute UI update on UI thread with batching
                var uiUpdateId = _performanceMonitor.StartOperation("UIUpdate",
                    $"Updating UI with {products.Count} products");

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        // Use high-performance collection replacement
                        PerformanceHelper.BatchUpdate(() =>
                        {
                            PerformanceHelper.ReplaceCollectionContent(Products, products);
                            // Ensure _productsViewSource is initialized before using it
                            if (_productsViewSource == null)
                            {
                                _productsViewSource = new CollectionViewSource();
                            }
                            _productsViewSource.Source = Products;
                            OnPropertyChanged(nameof(ProductsView));
                        });
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[PERF-{operationId}] UI update error: {ex.Message}");
                        throw;
                    }
                }, DispatcherPriority.Normal, _searchCancellation.Token);

                _performanceMonitor.StopOperation("UIUpdate", uiUpdateId,
                    $"UI updated with {products.Count} products");

                Debug.WriteLine($"[PERF-{operationId}] ✅ Optimized loading completed successfully with {products.Count} products");
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine($"[PERF-{operationId}] Operation cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERF-{operationId}] ❌ Optimized loading failed: {ex.Message}");
                throw;
            }
            finally
            {
                await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = false);
            }
        }

        /// <summary>
        /// Load products from database with highly optimized query
        /// </summary>
        private async Task<List<Product>> LoadProductsFromDatabase(string operationId)
        {
            var dbStopwatch = System.Diagnostics.Stopwatch.StartNew();

            using (var context = new POSDbContext())
            {
                // Configure context for maximum performance
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                context.ChangeTracker.AutoDetectChangesEnabled = false;
                context.Database.SetCommandTimeout(30);

                // Build highly optimized query with minimal data transfer
                var query = context.Products
                    .AsNoTracking()
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .Where(p => p.IsActive && p.Id > 0) // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs)
                    .Select(p => new
                    {
                        // Core product data
                        p.Id, p.Name, p.SKU, p.Description,
                        p.PurchasePrice, p.SellingPrice, p.StockQuantity,
                        p.MinimumStock, p.ReorderPoint, p.TrackBatches,
                        p.ExpiryDate, p.ImageData, p.LoyaltyPoints,
                        p.UnitOfMeasureId, p.SupplierId, p.CreatedAt,
                        p.CategoryId,

                        // Optimized related data
                        CategoryName = p.Category.Name,

                        // Pre-calculated batch data to avoid N+1 queries
                        TotalBatchQuantity = p.TrackBatches ?
                            p.Batches.Where(b => b.Quantity > 0).Sum(b => b.Quantity) : 0,
                        BatchCount = p.TrackBatches ? p.Batches.Count() : 0,

                        // ✅ FIX: Include batch pricing data for CurrentBatchPrice calculation
                        CurrentBatchSellingPrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                            p.Batches.Where(b => b.Quantity > 0)
                                     .OrderBy(b => b.CreatedAt)
                                     .ThenBy(b => b.Id)
                                     .Select(b => b.SellingPrice)
                                     .FirstOrDefault() : p.SellingPrice,
                        CurrentBatchPurchasePrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                            p.Batches.Where(b => b.Quantity > 0)
                                     .OrderBy(b => b.CreatedAt)
                                     .ThenBy(b => b.Id)
                                     .Select(b => b.PurchasePrice)
                                     .FirstOrDefault() : p.PurchasePrice,

                        // Primary barcode only for performance
                        PrimaryBarcode = p.Barcodes
                            .Where(b => b.IsPrimary)
                            .Select(b => b.Barcode)
                            .FirstOrDefault() ??
                            p.Barcodes.Select(b => b.Barcode).FirstOrDefault()
                    });

                // Apply category filter efficiently
                if (SelectedCategory != null)
                {
                    query = query.Where(p => p.CategoryId == SelectedCategory.Id);
                }

                // Execute optimized query with pagination
                var projectedData = await query
                    .OrderByDescending(p => p.CreatedAt)
                    .ThenBy(p => p.Name)
                    .Skip((CurrentPage - 1) * PageSize)
                    .Take(PageSize)
                    .ToListAsync(_searchCancellation.Token);

                dbStopwatch.Stop();
                Debug.WriteLine($"[PERF-{operationId}] Database query completed in {dbStopwatch.ElapsedMilliseconds}ms ({projectedData.Count} products)");

                // Transform to Product objects efficiently
                var transformStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var products = projectedData.Select(p => new Product
                {
                    Id = p.Id,
                    Name = p.Name,
                    SKU = p.SKU,
                    Description = p.Description,
                    PurchasePrice = p.PurchasePrice,
                    SellingPrice = p.SellingPrice,
                    // ✅ FIX: Use calculated batch quantity for batch-tracked products, otherwise use stock quantity
                    StockQuantity = p.TrackBatches ? p.TotalBatchQuantity : p.StockQuantity,
                    MinimumStock = p.MinimumStock,
                    ReorderPoint = p.ReorderPoint,
                    TrackBatches = p.TrackBatches,
                    ExpiryDate = p.ExpiryDate,
                    ImageData = p.ImageData,
                    LoyaltyPoints = p.LoyaltyPoints,
                    UnitOfMeasureId = p.UnitOfMeasureId,
                    SupplierId = p.SupplierId,
                    CreatedAt = p.CreatedAt,
                    CategoryId = p.CategoryId,
                    IsActive = true,
                    Category = new Category { Id = p.CategoryId, Name = p.CategoryName },
                    Barcodes = !string.IsNullOrEmpty(p.PrimaryBarcode) ?
                        new List<ProductBarcode> { new ProductBarcode { Barcode = p.PrimaryBarcode, IsPrimary = true } } :
                        new List<ProductBarcode>(),
                    // ✅ FIX: Set pre-calculated batch pricing values
                    CurrentBatchPrice = p.CurrentBatchSellingPrice,
                    CurrentBatchPurchasePrice = p.CurrentBatchPurchasePrice
                }).ToList();

                transformStopwatch.Stop();
                Debug.WriteLine($"[PERF-{operationId}] Object transformation completed in {transformStopwatch.ElapsedMilliseconds}ms");

                return products;
            }
        }

        /// <summary>
        /// Force refresh the UI to show updated stock values
        /// This method refreshes the UI without reloading from database
        /// </summary>
        public void ForceRefreshUI()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Force refreshing UI to show updated stock values...");

                // Force refresh all UI-bound properties
                OnPropertyChanged(nameof(ProductsView));
                OnPropertyChanged(nameof(Products));
                OnPropertyChanged(nameof(TotalProducts));
                OnPropertyChanged(nameof(LowStockCount));
                OnPropertyChanged(nameof(OutOfStockCount));
                OnPropertyChanged(nameof(InventoryValue));
                OnPropertyChanged(nameof(InventoryCost));

                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] UI refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Error during UI refresh: {ex.Message}");
            }
        }

        /// <summary>
        /// Force refresh the database context and reload products
        /// This method ensures we get the latest data from the database
        /// </summary>
        public async Task ForceRefreshFromDatabase()
        {
            var refreshId = _performanceMonitor.StartOperation("ForceRefresh",
                "Clearing caches and reloading from database");

            try
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Starting force refresh from database...");

                // Clear all cached data
                Products.Clear();

                // Clear product cache
                _productCache.ClearAllCache();
                Debug.WriteLine("[PRODUCTS] Cleared product cache");

                // Clear cached repository if available
                if (_cachedRepository != null)
                {
                    _cachedRepository.ClearCache();
                    Debug.WriteLine("[PRODUCTS] Cleared cached repository data");
                }

                // Force the database service to refresh its context
                if (_dbService?.Context != null)
                {
                    // Detach all tracked entities to ensure fresh data
                    var trackedEntities = _dbService.Context.ChangeTracker.Entries().ToList();
                    foreach (var entity in trackedEntities)
                    {
                        entity.State = Microsoft.EntityFrameworkCore.EntityState.Detached;
                    }
                    System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Detached {trackedEntities.Count} tracked entities");
                }

                // Reset pagination to first page
                CurrentPage = 1;

                // Reload data using the optimized method
                await LoadPagedProducts();

                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VM] Force refresh completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VM] Error during force refresh: {ex.Message}");
                throw;
            }
            finally
            {
                _performanceMonitor.StopOperation("ForceRefresh", refreshId,
                    $"Refreshed {Products.Count} products");
            }
        }

        /// <summary>
        /// Invalidate cache when products are modified
        /// </summary>
        public void InvalidateProductCache(int? categoryId = null, int? productId = null)
        {
            _productCache.InvalidateProductCache(categoryId, productId);
            Debug.WriteLine($"[PRODUCTS] Cache invalidated for categoryId: {categoryId}, productId: {productId}");
        }

        /// <summary>
        /// Run integrated performance tests on this ProductsViewModel instance
        /// </summary>
        public async Task<POSSystem.Services.Testing.PerformanceTestResults> RunPerformanceTestsAsync()
        {
            try
            {
                var testService = new POSSystem.Services.Testing.IntegratedPerformanceTestService();
                Debug.WriteLine("[PRODUCTS] Starting integrated performance tests...");

                var results = await testService.RunProductsViewModelTestsAsync(this);

                Debug.WriteLine($"[PRODUCTS] Performance tests completed. Success: {results.Success}");
                Debug.WriteLine($"[PRODUCTS] Test results saved to: {testService.GetLogFilePath()}");

                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PRODUCTS] Performance tests failed: {ex.Message}");
                throw;
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            // ✅ FIX: Redirect to the updated statistics method that uses CurrentBatchPrice properties
            await UpdateProductStatisticsAsync();
        }

        // NEW: High-performance statistics using repository
        private async Task UpdateProductStatisticsAsync()
        {
            try
            {
                // ✅ FIX: Use loaded products with proper batch price calculations for accurate inventory calculations
                if (AllProducts != null && AllProducts.Any())
                {
                    // Use batch update for better UI performance
                    PerformanceHelper.BatchUpdate(() =>
                    {
                        TotalProducts = AllProducts.Count;
                        LowStockCount = AllProducts.Count(p => p.IsLowStock);
                        OutOfStockCount = AllProducts.Count(p => p.IsOutOfStock);

                        // ✅ FIX: Use actual batch-based inventory calculations for accurate values
                        InventoryValue = AllProducts.Sum(p => p.GetTotalInventoryValue());
                        InventoryCost = AllProducts.Sum(p => p.GetTotalInventoryCost());
                    });

                    Debug.WriteLine($"[BATCH_PRICING] Statistics calculated with batch pricing: Total={TotalProducts}, Value={InventoryValue:C}, Cost={InventoryCost:C}");
                }
                else
                {
                    // Fallback path: compute accurate inventory totals directly from database
                    var (total, lowStock, inventoryValue) = await _repositoryAdapter.GetProductStatisticsAsync();
                    var (calcValue, calcCost) = await ComputeInventoryTotalsAsync();

                    PerformanceHelper.BatchUpdate(() =>
                    {
                        TotalProducts = total;
                        LowStockCount = lowStock;
                        OutOfStockCount = 0; // Could be extended in repository method
                        InventoryValue = calcValue; // Use accurate DB-calculated value
                        InventoryCost = calcCost;   // Use accurate DB-calculated cost
                    });

                    Debug.WriteLine($"[REPOSITORY] Statistics loaded: Total={total}, LowStock={lowStock}, Value={calcValue:C}, Cost={calcCost:C}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[STATISTICS] Error updating statistics: {ex.Message}");
                // Fallback to legacy method
                UpdateProductStatisticsLegacy();
            }
        }

        // LEGACY: Keep original method as fallback
        private void UpdateProductStatisticsLegacy()
        {
            if (AllProducts == null) return;

            // Use batch update for better performance
            PerformanceHelper.BatchUpdate(() =>
            {
                // Update total products count
                TotalProducts = AllProducts.Count;

                // Update low stock count - use GetTotalStock() method for consistency
                LowStockCount = AllProducts.Count(p => p.IsLowStock);

                // Update out of stock count - use GetTotalStock() method for consistency
                OutOfStockCount = AllProducts.Count(p => p.IsOutOfStock);

                // ✅ FIX: Update inventory values to use CurrentBatchPrice properties
                InventoryCost = AllProducts.Sum(p => p.CurrentBatchPurchasePrice * p.GetTotalStock());
                InventoryValue = AllProducts.Sum(p => p.CurrentBatchPrice * p.GetTotalStock());
            });
        }

        // WRAPPER: For backward compatibility
        private void UpdateProductStatistics()
        {
            // Try async version first, fallback to legacy if needed
            _ = Task.Run(async () =>
            {
                try
                {
                    await UpdateProductStatisticsAsync();
                }
                catch
                {
                    await Application.Current.Dispatcher.InvokeAsync(() => UpdateProductStatisticsLegacy());
                }
            });
        }

        // New method to notify all property changes at once after a batch update
        private void NotifyBatchPropertyChanges()
        {
            // Temporarily disable batch update mode to allow property notifications
            bool original = _isBatchUpdate;
            _isBatchUpdate = false;

            try
            {
                // Notify changes for all important properties
                OnPropertyChanged(nameof(TotalProducts));
                OnPropertyChanged(nameof(LowStockCount));
                OnPropertyChanged(nameof(OutOfStockCount));
                OnPropertyChanged(nameof(InventoryCost));
                OnPropertyChanged(nameof(InventoryValue));
                OnPropertyChanged(nameof(CurrentPage));
                OnPropertyChanged(nameof(TotalPages));
                OnPropertyChanged(nameof(CanGoToNextPage));
                OnPropertyChanged(nameof(CanGoToPreviousPage));
                OnPropertyChanged(nameof(ProductsView));
            }
            finally
            {
                // Restore original batch update state
                _isBatchUpdate = original;
            }
        }

        public int AddProduct(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] ===== STARTING ADD PRODUCT OPERATION =====");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Product Name: {product.Name}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Product ID: {product.Id}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Category ID: {product.CategoryId}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Track Batches: {product.TrackBatches}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Stock Quantity: {product.StockQuantity}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Batch Count: {product.Batches?.Count ?? 0}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Barcode Count: {product.Barcodes?.Count ?? 0}");

                int productId;
                using (var context = new POSDbContext())
                {
                    try
                    {
                        // Initialize collections
                        System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Initializing collections...");
                        product.Barcodes ??= new HashSet<ProductBarcode>();
                        product.Batches ??= new HashSet<BatchStock>();
                        product.Sales ??= new HashSet<SaleItem>();
                        product.InventoryTransactions ??= new HashSet<InventoryTransaction>();
                        product.PriceHistory ??= new HashSet<ProductPrice>();

                        // Set default values
                        product.CreatedAt = DateTime.Now;
                        product.UpdatedAt = DateTime.Now;

                        System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Adding product to context...");
                        // Ensure StockQuantity is never null or invalid
                        if (product.StockQuantity < 0)
                        {
                            product.StockQuantity = 0m;
                            System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Fixed negative StockQuantity to 0");
                        }

                        System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Final StockQuantity before save: {product.StockQuantity}");

                        // Add the product first
                        context.Products.Add(product);

                        try
                        {
                            System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Saving product to database...");
                            context.SaveChanges();
                            productId = product.Id;
                            System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Product saved successfully with ID: {productId}");

                            // If product tracks batches and has initial batch, save it
                            if (product.TrackBatches && product.Batches != null && product.Batches.Any())
                            {
                                System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Processing batches...");
                                var existingBatches = context.BatchStock.Where(b => b.ProductId == productId).ToList();

                                foreach (var batch in product.Batches)
                                {
                                    // Check if this batch already exists
                                    var existingBatch = existingBatches.FirstOrDefault(b =>
                                        b.BatchNumber == batch.BatchNumber &&
                                        b.ProductId == productId);

                                    if (existingBatch == null)
                                    {
                                        batch.ProductId = productId; // Ensure the batch has the correct product ID
                                        System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Adding batch: {batch.BatchNumber} with quantity: {batch.Quantity}");
                                        context.BatchStock.Add(batch);
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Batch already exists: {batch.BatchNumber}, skipping...");
                                    }
                                }

                                if (context.ChangeTracker.HasChanges())
                                {
                                    System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Saving batches...");
                                    context.SaveChanges();
                                    System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT] Saved batches for product {productId}");
                                }
                            }
                        }
                        catch (DbUpdateException dbEx)
                        {
                            System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT_ERROR] Database update exception occurred");
                            System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Message: {dbEx.Message}");
                            if (dbEx.InnerException != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Inner exception: {dbEx.InnerException.Message}");

                                // Handle specific foreign key constraint errors
                                if (dbEx.InnerException.Message.Contains("FOREIGN KEY constraint failed"))
                                {
                                    string errorMessage = "Failed to save product due to invalid references:\n";

                                    // Check for specific foreign key issues
                                    var constraintEntries = dbEx.Entries;
                                    foreach (var entry in constraintEntries)
                                    {
                                        if (entry.Entity is Product prod)
                                        {
                                            if (prod.CategoryId <= 0)
                                                errorMessage += "• Invalid category selected\n";
                                            if (prod.UnitOfMeasureId <= 0)
                                                errorMessage += "• Invalid unit of measure selected\n";
                                            if (prod.SupplierId.HasValue && prod.SupplierId <= 0)
                                                errorMessage += "• Invalid supplier selected\n";
                                        }
                                    }

                                    errorMessage += "\nPlease ensure all required fields are properly selected.";
                                    MessageBox.Show(errorMessage, "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                                    return -1;
                                }
                            }

                            // Log validation errors if any
                            var entries = dbEx.Entries;
                            foreach (var entry in entries)
                            {
                                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Entity Type: {entry.Entity.GetType().Name}");
                                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] State: {entry.State}");
                                foreach (var prop in entry.CurrentValues.Properties)
                                {
                                    var value = entry.CurrentValues[prop];
                                    System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Property {prop.Name}: {value}");
                                }
                            }
                            throw; // Re-throw after logging
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Error during context operations: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Inner exception: {ex.InnerException.Message}");
                        }
                        throw; // Re-throw after logging
                    }
                }

                System.Diagnostics.Debug.WriteLine("[ADD_PRODUCT] Product added successfully, UI will be refreshed by caller");
                // Note: UI refresh will be handled by the calling code to avoid double-refresh

                return productId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Unhandled exception: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Inner exception: {ex.InnerException.Message}");
                    System.Diagnostics.Debug.WriteLine($"[ADD_PRODUCT_ERROR] Inner stack trace: {ex.InnerException.StackTrace}");
                }
                MessageBox.Show($"Error saving product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return -1;
            }
        }

        public void UpdateProduct(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[UPDATE_PRODUCT] ===== STARTING UPDATE PRODUCT OPERATION =====");
                System.Diagnostics.Debug.WriteLine($"[UPDATE_PRODUCT] Product ID: {product.Id}");
                System.Diagnostics.Debug.WriteLine($"[UPDATE_PRODUCT] Product Name: {product.Name}");

                using (var context = new POSDbContext())
                {
                    // Get existing product with all related data
                    var existingProduct = context.Products
                        .Include(p => p.Barcodes)
                        .Include(p => p.Batches)
                        .Include(p => p.Category)
                        .Include(p => p.Supplier)
                        .Include(p => p.UnitOfMeasure)
                        .FirstOrDefault(p => p.Id == product.Id);

                    if (existingProduct != null)
                    {
                        bool batchTrackingChanged = existingProduct.TrackBatches != product.TrackBatches;
                        bool wasTracking = existingProduct.TrackBatches;

                        // Update the product properties
                        existingProduct.Name = product.Name;
                        existingProduct.Description = product.Description;
                        existingProduct.PurchasePrice = product.PurchasePrice;
                        existingProduct.SellingPrice = product.SellingPrice;
                        existingProduct.StockQuantity = product.StockQuantity;
                        existingProduct.MinimumStock = product.MinimumStock;
                        existingProduct.ReorderPoint = product.ReorderPoint;
                        existingProduct.CategoryId = product.CategoryId;
                        existingProduct.UnitOfMeasureId = product.UnitOfMeasureId;
                        existingProduct.IsActive = product.IsActive;
                        existingProduct.ExpiryDate = product.ExpiryDate;
                        existingProduct.TrackBatches = product.TrackBatches;
                        existingProduct.UpdatedAt = DateTime.Now;
                        existingProduct.LoyaltyPoints = product.LoyaltyPoints;

                        // Handle batch tracking changes
                        if (batchTrackingChanged)
                        {
                            if (product.TrackBatches && !wasTracking)
                            {
                                // Changed from not tracking to tracking batches
                                // Create an initial batch with the current stock quantity
                                var initialBatch = new BatchStock
                                {
                                    ProductId = product.Id,
                                    BatchNumber = "INITIAL",
                                    Quantity = product.StockQuantity,
                                    ManufactureDate = DateTime.Now,
                                    CreatedAt = DateTime.Now,
                                    PurchasePrice = product.PurchasePrice,
                                    SellingPrice = product.SellingPrice
                                };

                                if (existingProduct.Batches == null)
                                    existingProduct.Batches = new List<BatchStock>();

                                existingProduct.Batches.Add(initialBatch);
                            }
                            else if (!product.TrackBatches && wasTracking)
                            {
                                // Changed from tracking to not tracking
                                // Set stock quantity to the sum of all batches
                                var totalBatchQuantity = existingProduct.Batches?.Sum(b => b.Quantity) ?? 0;
                                existingProduct.StockQuantity = totalBatchQuantity;

                                // Clear the batches
                                if (existingProduct.Batches != null)
                                {
                                    context.BatchStock.RemoveRange(existingProduct.Batches);
                                    existingProduct.Batches.Clear();
                                }
                            }
                        }

                        // Save changes
                        context.SaveChanges();
                        System.Diagnostics.Debug.WriteLine("[UPDATE_PRODUCT] Product updated successfully in database");
                    }
                }

                System.Diagnostics.Debug.WriteLine("[UPDATE_PRODUCT] Update complete, UI will be refreshed by caller");
                // Note: UI refresh will be handled by the calling code to avoid double-refresh
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void DeleteProduct(int id)
        {
            try
            {
                using (var context = new POSDbContext())
                {
                    var product = context.Products.Find(id);
                    if (product != null)
                    {
                        context.Products.Remove(product);
                        context.SaveChanges();
                        _ = LoadPagedProducts(); // Reload all data to refresh the lists and statistics
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LookupProductByBarcodeAsync(string barcode)
        {
            var product = await _productLookupService.LookupProductByBarcodeAsync(barcode);
            if (product != null)
            {
                var result = MessageBox.Show(
                    "Product found in database. Would you like to auto-fill the product details?",
                    "Product Found",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    CurrentProduct = product;
                    OnPropertyChanged(nameof(CurrentProduct));
                }
            }
        }

        private async Task ExecuteSearchAsync(string searchTerm, bool resetPage = false)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                // If search is cleared, reload products using normal pagination
                if (resetPage)
                {
                    // Use dispatcher to update CurrentPage and then load products
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _isBatchUpdate = true;
                        CurrentPage = 1;
                        _isBatchUpdate = false;
                        OnPropertyChanged(nameof(CurrentPage));
                    });
                }
                await LoadPagedProducts();
                return;
            }

            try
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoading = true;
                    // Reset page if requested
                    if (resetPage)
                    {
                        _isBatchUpdate = true;
                        CurrentPage = 1;
                        _isBatchUpdate = false;
                        OnPropertyChanged(nameof(CurrentPage));
                    }
                });

                var normalizedSearch = searchTerm.Trim().ToUpperInvariant();

                using (var context = new POSDbContext())
                {
                    var query = context.Products
                        .AsNoTracking()
                        .Include(p => p.Category)
                        .Include(p => p.Barcodes)
                        .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                        .Where(p => p.IsActive && p.Id > 0 && ( // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs)
                            p.Name.ToUpper().Contains(normalizedSearch) ||
                            p.SKU.ToUpper().Contains(normalizedSearch) ||
                            p.Barcodes.Any(b => b.Barcode.ToUpper().Contains(normalizedSearch)) ||
                            (p.Description != null && p.Description.ToUpper().Contains(normalizedSearch))
                        ))
                        .AsQueryable();

                    // Apply category filter if selected
                    if (SelectedCategory != null)
                    {
                        query = query.Where(p => p.CategoryId == SelectedCategory.Id);
                    }

                    // Apply stock filter if selected
                    if (!string.IsNullOrEmpty(SelectedStockFilter) && SelectedStockFilter != "All")
                    {
                        switch (SelectedStockFilter)
                        {
                            case "LowStock":
                                query = query.Where(p =>
                                    (!p.TrackBatches && p.StockQuantity <= p.MinimumStock && p.StockQuantity > 0) ||
                                    (p.TrackBatches && (p.StockQuantity + p.Batches.Sum(b => b.Quantity)) <= p.MinimumStock &&
                                    (p.StockQuantity + p.Batches.Sum(b => b.Quantity)) > 0));
                                break;
                            case "OutOfStock":
                                query = query.Where(p =>
                                    (!p.TrackBatches && p.StockQuantity == 0) ||
                                    (p.TrackBatches && (p.StockQuantity == 0 && (!p.Batches.Any() || p.Batches.Sum(b => b.Quantity) == 0))));
                                break;
                        }
                    }

                    // Execute query and pagination sequentially to avoid concurrent operations
                    var totalCount = await query.CountAsync(_searchCancellation.Token);

                    var results = await query
                        .OrderBy(p => p.Name)
                        .Skip((CurrentPage - 1) * PageSize)
                        .Take(PageSize)
                        .ToListAsync(_searchCancellation.Token);

                    // Get products needing batch data
                    var productsNeedingBatches = results.Where(p => p.TrackBatches).ToList();
                    if (productsNeedingBatches.Any())
                    {
                        // Make sure we're done with the current context
                        context.ChangeTracker.Clear();

                        // Load batches with a separate context
                        using (var batchContext = new POSDbContext())
                        {
                            var productIds = productsNeedingBatches.Select(p => p.Id).ToList();
                            var batches = await batchContext.BatchStock
                                .Where(b => productIds.Contains(b.ProductId))
                                .AsNoTracking()
                                .ToListAsync(_searchCancellation.Token);

                            // Associate batches with products
                            foreach (var product in productsNeedingBatches)
                            {
                                product.Batches = batches
                                    .Where(b => b.ProductId == product.Id)
                                    .ToList();
                            }
                        }
                    }

                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() =>
                        {
                            TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize);
                            Products = new OptimizedObservableCollection<Product>(results);
                            // Ensure _productsViewSource is initialized before using it
                            if (_productsViewSource == null)
                            {
                                _productsViewSource = new CollectionViewSource();
                            }
                            _productsViewSource.Source = Products;
                            OnPropertyChanged(nameof(ProductsView));
                        });
                    });
                }
            }
            catch (TaskCanceledException)
            {
                // Search was cancelled, ignore
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Search error: {ex.Message}");
            }
            finally
            {
                await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = false);
            }
        }

        public void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedCategory = null;
            SelectedStockFilter = null;
            CurrentPage = 1;
            _ = LoadPagedProducts();
        }

        public async Task<bool> AddProductAsync(Product product)
        {
            try
            {
                // Use Task.Run to execute the synchronous AddProduct method on a background thread
                int productId = await Task.Run(() => AddProduct(product));

                // If productId is greater than 0, the product was added successfully
                return productId > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding product asynchronously: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Override OnPropertyChanged to support batch updates
        /// </summary>
        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            // Skip property notifications during batch updates
            if (_isBatchUpdate) return;

            // Use the optimized base class implementation
            base.OnPropertyChanged(propertyName);
        }

        #region Progressive Loading

        /// <summary>
        /// Load products using progressive loading for better UI responsiveness
        /// </summary>
        private async Task LoadProductsProgressivelyAsync()
        {
            if (_progressiveLoader == null)
            {
                // Fallback to standard loading
                await LoadPagedProductsAsync();
                return;
            }

            try
            {
                IsLoading = true;

                var cacheKey = $"products:page:{CurrentPage}:size:{PageSize}";

                // Essential data loader (fast, minimal data)
                Func<Task<List<Product>>> essentialLoader = async () =>
                {
                    if (_optimizedQueries != null)
                    {
                        var items = await _optimizedQueries.GetProductsForListAsync(CurrentPage, PageSize);
                        return items.Select(item => new Product
                        {
                            Id = item.Id,
                            Name = item.Name,
                            SKU = item.SKU,
                            SellingPrice = item.SellingPrice,
                            StockQuantity = item.StockQuantity,
                            CreatedAt = item.CreatedAt
                        }).ToList();
                    }
                    else
                    {
                        // Fallback to basic query
                        var context = _dbService.Context;
                        return await context.Products
                            .AsNoTracking()
                            .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                            .Where(p => p.IsActive && p.Id > 0) // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs)
                            .Select(p => new Product
                            {
                                Id = p.Id,
                                Name = p.Name,
                                SKU = p.SKU,
                                SellingPrice = p.SellingPrice,
                                // ✅ FIX: Use calculated batch quantity for batch-tracked products, otherwise use stock quantity
                                StockQuantity = p.TrackBatches ? (p.Batches.Sum(b => b.Quantity)) : p.StockQuantity,
                                CreatedAt = p.CreatedAt,
                                TrackBatches = p.TrackBatches,
                                // ✅ FIX: Set CurrentBatchPrice properties using FIFO logic
                                CurrentBatchPrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                                    p.Batches.Where(b => b.Quantity > 0)
                                             .OrderBy(b => b.CreatedAt)
                                             .ThenBy(b => b.Id)
                                             .Select(b => b.SellingPrice)
                                             .FirstOrDefault() : p.SellingPrice,
                                CurrentBatchPurchasePrice = p.TrackBatches && p.Batches.Any(b => b.Quantity > 0) ?
                                    p.Batches.Where(b => b.Quantity > 0)
                                             .OrderBy(b => b.CreatedAt)
                                             .ThenBy(b => b.Id)
                                             .Select(b => b.PurchasePrice)
                                             .FirstOrDefault() : p.PurchasePrice
                            })
                            .OrderByDescending(p => p.CreatedAt)
                            .ThenBy(p => p.Name)
                            .Skip((CurrentPage - 1) * PageSize)
                            .Take(PageSize)
                            .ToListAsync();
                    }
                };

                // Full data loader (complete data with relationships)
                Func<Task<List<Product>>> fullLoader = async () =>
                {
                    var context = _dbService.Context;
                    var products = await context.Products
                        .AsNoTracking()
                        .Include(p => p.Category)
                        .Include(p => p.Barcodes)
                        .Include(p => p.Batches) // ✅ FIX: Include batches for CurrentBatchPrice calculation
                        .Where(p => p.IsActive)
                        .OrderByDescending(p => p.CreatedAt)
                        .ThenBy(p => p.Name)
                        .Skip((CurrentPage - 1) * PageSize)
                        .Take(PageSize)
                        .ToListAsync();

                    // ✅ FIX: Set CurrentBatchPrice properties for products with full Batches collections
                    foreach (var product in products)
                    {
                        SetCurrentBatchPrices(product);
                    }

                    return products;
                };

                // Load progressively
                await _progressiveLoader.LoadProgressivelyAsync(
                    cacheKey,
                    essentialLoader,
                    fullLoader,
                    TimeSpan.FromMinutes(10),
                    onEssentialDataLoaded: (products) =>
                    {
                        // Update UI with essential data immediately
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            Products.Clear();
                            foreach (var product in products)
                            {
                                Products.Add(product);
                            }
                            OnPropertyChanged(nameof(ProductsView));
                        });
                    },
                    onFullDataLoaded: (products) =>
                    {
                        // Update UI with complete data
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            Products.Clear();
                            foreach (var product in products)
                            {
                                Products.Add(product);
                            }
                            OnPropertyChanged(nameof(ProductsView));
                        });
                    }
                );
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PRODUCTS_VM] Progressive loading error: {ex.Message}");
                _alertService?.ShowError($"Failed to load products: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        /// <summary>
        /// ✅ CRITICAL UI OPTIMIZATION: Enhanced disposal with UI resource cleanup
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Cancel and dispose search operations
                _searchCancellation?.Cancel();
                _searchCancellation?.Dispose();

                // Dispose UI optimization services
                _virtualizationService?.Dispose();

                // Note: _performanceMonitor and _productCache may not implement IDisposable
                // This is acceptable as they are lightweight services

                // Unsubscribe from events to prevent memory leaks
                CategoriesViewModel.CategoryChanged -= OnCategoryChanged;
                SaleViewModel.ProductStockChanged -= OnProductStockChanged;

                // Clear collections
                _products?.Clear();
                _categories?.Clear();
                _unitsOfMeasure?.Clear();
                _allProducts?.Clear();

                Debug.WriteLine("✅ [PRODUCTS-VM] ProductsViewModel disposed with UI optimizations");
            }

            base.Dispose(disposing);
        }

        // Add the TriggerImmediateSearch method
        public void TriggerImmediateSearch()
        {
            // Cancel any pending debounced search
            _searchCancellation?.Cancel();
            _searchCancellation = new CancellationTokenSource();

            // Execute search immediately
            Task.Run(async () => await ExecuteSearchAsync(_searchText, true));
        }

        /// <summary>
        /// Helper method to determine if a unit of measure represents a single unit (piece, item, etc.)
        /// </summary>
        private bool IsSingleUnit(UnitOfMeasure unit)
        {
            if (unit == null) return false;

            var singleUnitNames = new[] { "piece", "item", "unit", "each", "قطعة", "pièce", "stück", "pieza" };
            return singleUnitNames.Any(name =>
                unit.Name.Equals(name, StringComparison.OrdinalIgnoreCase) ||
                unit.Abbreviation.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Helper method to determine if a unit of measure is appropriate for services
        /// </summary>
        public bool IsServiceUnit(UnitOfMeasure unit)
        {
            if (unit == null) return false;

            // Service units typically include time-based units and count units
            var serviceUnitTypes = new[] { "Unit", "Time" };
            var serviceUnitNames = new[] { "hour", "minute", "session", "piece", "item", "unit", "each", "service" };

            return serviceUnitTypes.Any(type =>
                unit.Type.Equals(type, StringComparison.OrdinalIgnoreCase)) ||
                serviceUnitNames.Any(name =>
                    unit.Name.ToLower().Contains(name) ||
                    unit.Abbreviation.ToLower().Contains(name));
        }

        /// <summary>
        /// Gets units of measure filtered for services
        /// </summary>
        public ObservableCollection<UnitOfMeasure> GetServiceUnits()
        {
            if (UnitsOfMeasure == null) return new ObservableCollection<UnitOfMeasure>();

            var serviceUnits = UnitsOfMeasure.Where(IsServiceUnit).ToList();
            return new ObservableCollection<UnitOfMeasure>(serviceUnits);
        }

        /// <summary>
        /// Gets the default unit of measure for services (preferably "hour" or "unit")
        /// </summary>
        public UnitOfMeasure GetDefaultServiceUnit()
        {
            if (UnitsOfMeasure == null) return null;

            // Try to find "hour" first for time-based services
            var hourUnit = UnitsOfMeasure.FirstOrDefault(u =>
                u.Name.ToLower().Contains("hour"));

            if (hourUnit != null) return hourUnit;

            // Fall back to "unit" or "piece"
            var unitUnit = UnitsOfMeasure.FirstOrDefault(u =>
                u.Name.ToLower().Contains("unit") ||
                u.Name.ToLower().Contains("piece"));

            if (unitUnit != null) return unitUnit;

            // Fall back to first service-appropriate unit
            return UnitsOfMeasure.FirstOrDefault(IsServiceUnit);
        }

        /// <summary>
        /// Helper method to determine if a unit of measure is appropriate for weight-based products
        /// </summary>
        public bool IsWeightUnit(UnitOfMeasure unit)
        {
            if (unit == null) return false;

            // Weight units include those with Type = "Weight" or weight-related names
            var weightUnitNames = new[] { "kilogram", "kg", "gram", "g", "pound", "lb", "ounce", "oz", "ton", "tonne" };

            return unit.Type.Equals("Weight", StringComparison.OrdinalIgnoreCase) ||
                   weightUnitNames.Any(name =>
                       unit.Name.ToLower().Contains(name) ||
                       unit.Abbreviation.ToLower().Contains(name));
        }

        /// <summary>
        /// Helper method to determine if a unit of measure is appropriate for volume/liquid products
        /// </summary>
        public bool IsVolumeUnit(UnitOfMeasure unit)
        {
            if (unit == null) return false;

            // Volume units include those with Type = "Volume" or volume-related names
            var volumeUnitNames = new[] { "liter", "litre", "l", "milliliter", "millilitre", "ml", "gallon", "gal", "quart", "qt", "pint", "pt", "cup", "fluid", "fl" };

            return unit.Type.Equals("Volume", StringComparison.OrdinalIgnoreCase) ||
                   volumeUnitNames.Any(name =>
                       unit.Name.ToLower().Contains(name) ||
                       unit.Abbreviation.ToLower().Contains(name));
        }

        /// <summary>
        /// Helper method to determine if a unit of measure is appropriate for unit-based products
        /// </summary>
        public bool IsUnitBasedUnit(UnitOfMeasure unit)
        {
            if (unit == null) return false;

            // Unit-based units include those with Type = "Unit" or "Package" or count-related names
            var unitBasedTypes = new[] { "Unit", "Package" };
            var unitBasedNames = new[] { "piece", "item", "unit", "each", "box", "pack", "dozen", "pair", "set", "bundle", "carton", "bottle" };

            return unitBasedTypes.Any(type =>
                unit.Type.Equals(type, StringComparison.OrdinalIgnoreCase)) ||
                unitBasedNames.Any(name =>
                    unit.Name.ToLower().Contains(name) ||
                    unit.Abbreviation.ToLower().Contains(name));
        }

        /// <summary>
        /// Gets units of measure filtered for weight-based products
        /// </summary>
        public ObservableCollection<UnitOfMeasure> GetWeightUnits()
        {
            if (UnitsOfMeasure == null) return new ObservableCollection<UnitOfMeasure>();

            var weightUnits = UnitsOfMeasure.Where(IsWeightUnit).ToList();
            return new ObservableCollection<UnitOfMeasure>(weightUnits);
        }

        /// <summary>
        /// Gets units of measure filtered for volume/liquid products
        /// </summary>
        public ObservableCollection<UnitOfMeasure> GetVolumeUnits()
        {
            if (UnitsOfMeasure == null) return new ObservableCollection<UnitOfMeasure>();

            var volumeUnits = UnitsOfMeasure.Where(IsVolumeUnit).ToList();
            return new ObservableCollection<UnitOfMeasure>(volumeUnits);
        }

        /// <summary>
        /// Gets units of measure filtered for unit-based products
        /// </summary>
        public ObservableCollection<UnitOfMeasure> GetUnitBasedUnits()
        {
            if (UnitsOfMeasure == null) return new ObservableCollection<UnitOfMeasure>();

            var unitBasedUnits = UnitsOfMeasure.Where(IsUnitBasedUnit).ToList();
            return new ObservableCollection<UnitOfMeasure>(unitBasedUnits);
        }

        /// <summary>
        /// Gets the default unit of measure for weight-based products (preferably "kg")
        /// </summary>
        public UnitOfMeasure GetDefaultWeightUnit()
        {
            if (UnitsOfMeasure == null) return null;

            // Try to find "kilogram" or "kg" first
            var kgUnit = UnitsOfMeasure.FirstOrDefault(u =>
                u.Name.Equals("kilogram", StringComparison.OrdinalIgnoreCase) ||
                u.Abbreviation.Equals("kg", StringComparison.OrdinalIgnoreCase));

            if (kgUnit != null) return kgUnit;

            // Fall back to first weight unit
            return UnitsOfMeasure.FirstOrDefault(IsWeightUnit);
        }

        /// <summary>
        /// Gets the default unit of measure for volume/liquid products (preferably "L")
        /// </summary>
        public UnitOfMeasure GetDefaultVolumeUnit()
        {
            if (UnitsOfMeasure == null) return null;

            // Try to find "liter" or "L" first
            var literUnit = UnitsOfMeasure.FirstOrDefault(u =>
                u.Name.Equals("liter", StringComparison.OrdinalIgnoreCase) ||
                u.Name.Equals("litre", StringComparison.OrdinalIgnoreCase) ||
                u.Abbreviation.Equals("l", StringComparison.OrdinalIgnoreCase));

            if (literUnit != null) return literUnit;

            // Fall back to first volume unit
            return UnitsOfMeasure.FirstOrDefault(IsVolumeUnit);
        }

        /// <summary>
        /// Gets the default unit of measure for unit-based products (preferably "piece")
        /// </summary>
        public UnitOfMeasure GetDefaultUnitBasedUnit()
        {
            if (UnitsOfMeasure == null) return null;

            // Try to find "piece" first
            var pieceUnit = UnitsOfMeasure.FirstOrDefault(u =>
                u.Name.Equals("piece", StringComparison.OrdinalIgnoreCase) ||
                u.Abbreviation.Equals("pc", StringComparison.OrdinalIgnoreCase));

            if (pieceUnit != null) return pieceUnit;

            // Fall back to first unit-based unit
            return UnitsOfMeasure.FirstOrDefault(IsUnitBasedUnit);
        }

        /// <summary>
        /// Sets the CurrentBatchPrice properties for a product based on its Batches collection
        /// </summary>
        private void SetCurrentBatchPrices(Product product)
        {
            if (product == null) return;

            if (!product.TrackBatches || product.Batches == null || !product.Batches.Any())
            {
                // For non-batch products or products without batches, use regular prices
                product.CurrentBatchPrice = product.SellingPrice;
                product.CurrentBatchPurchasePrice = product.PurchasePrice;
                return;
            }

            // Find the oldest batch with quantity > 0 (FIFO logic)
            var oldestBatch = product.Batches
                .Where(b => b.Quantity > 0)
                .OrderBy(b => b.CreatedAt)
                .ThenBy(b => b.Id)
                .FirstOrDefault();

            if (oldestBatch != null)
            {
                product.CurrentBatchPrice = oldestBatch.SellingPrice;
                product.CurrentBatchPurchasePrice = oldestBatch.PurchasePrice;
            }
            else
            {
                // Fallback to regular prices if no batches with quantity found
                product.CurrentBatchPrice = product.SellingPrice;
                product.CurrentBatchPurchasePrice = product.PurchasePrice;
            }
        }

        /// <summary>
        /// Compute accurate inventory totals (value and cost) directly from database using batch pricing
        /// </summary>
        private async Task<(decimal value, decimal cost)> ComputeInventoryTotalsAsync()
        {
            try
            {
                using (var context = new POSDbContext())
                {
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                    // ✅ FIX: Use double for SQLite compatibility, then convert back to decimal
                    // Sum batch-tracked products using batch prices (FIFO value per batch unit)
                    var batchTotals = await context.Products
                        .Where(p => p.IsActive && p.TrackBatches)
                        .Select(p => new
                        {
                            Value = p.Batches.Sum(b => (double)(b.Quantity * b.SellingPrice)),
                            Cost = p.Batches.Sum(b => (double)(b.Quantity * b.PurchasePrice))
                        })
                        .ToListAsync();

                    var batchValue = (decimal)batchTotals.Sum(x => x.Value);
                    var batchCost = (decimal)batchTotals.Sum(x => x.Cost);

                    // Sum non-batch products using product prices
                    var nonBatchTotals = await context.Products
                        .Where(p => p.IsActive && !p.TrackBatches)
                        .Select(p => new
                        {
                            Value = (double)(p.SellingPrice * p.StockQuantity),
                            Cost = (double)(p.PurchasePrice * p.StockQuantity)
                        })
                        .ToListAsync();

                    var nonBatchValue = (decimal)nonBatchTotals.Sum(x => x.Value);
                    var nonBatchCost = (decimal)nonBatchTotals.Sum(x => x.Cost);

                    return (batchValue + nonBatchValue, batchCost + nonBatchCost);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[INVENTORY] Error computing totals: {ex.Message}");
                return (0, 0);
            }
        }

}

}
