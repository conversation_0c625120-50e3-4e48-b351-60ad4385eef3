﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource AppBackgroundGradient}"
             FocusManager.FocusedElement="{Binding ElementName=txtSearch}">

    <UserControl.InputBindings>
        <KeyBinding Key="F3" Command="{x:Static local:SalesView.FocusSearchCommand}"/>
        <KeyBinding Modifiers="Control" Key="F" Command="{x:Static local:SalesView.FocusSearchCommand}"/>
        <KeyBinding Modifiers="Control" Key="N" Command="{Binding CreateNewCartCommand}"/>
        <KeyBinding Modifiers="Control" Key="P" Command="{x:Static local:SalesView.ShowCustomProductCommand}"/>
        <KeyBinding Modifiers="Control" Key="R" Command="{x:Static local:SalesView.ClearFilterCommand}"/>
        <KeyBinding Key="Delete" Command="{Binding RemoveFromCartCommand}" CommandParameter="{Binding SelectedCartItem.Product.Id}"/>
        <KeyBinding Key="F12" Command="{x:Static local:SalesView.ShowPaymentCommand}"/>
        <KeyBinding Modifiers="Control" Key="Enter" Command="{x:Static local:SalesView.ShowPaymentCommand}"/>
        <KeyBinding Key="F4" Command="{Binding LookupCustomerCommand}"/>
        <KeyBinding Key="F5" Command="{Binding RedeemPointsCommand}"/>
        <KeyBinding Key="OemPlus" Command="{Binding IncreaseQuantityCommand}"/>
        <KeyBinding Key="OemMinus" Command="{Binding DecreaseQuantityCommand}"/>
        <KeyBinding Key="Add" Command="{Binding IncreaseQuantityCommand}"/>
        <KeyBinding Key="Subtract" Command="{Binding DecreaseQuantityCommand}"/>
        <KeyBinding Key="F6" Command="{Binding ShowFavoritesCommand}"/>
    </UserControl.InputBindings>

    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:LengthToVisibilityConverter x:Key="LengthToVisibilityConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStockColorConverter x:Key="BooleanToStockColorConverter"/>
        <converters:TotalStockConverter x:Key="TotalStockConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <converters:QuantityDisplayConverter x:Key="QuantityDisplayConverter"/>
        <converters:QuantityWithUnitConverter x:Key="QuantityWithUnitConverter"/>
        
        <!-- Colors -->
        <SolidColorBrush x:Key="SearchBackgroundBrush" Color="#f5f7fa"/>
        <SolidColorBrush x:Key="PrimaryActionBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="PopularItemsBrush" Color="#673AB7"/>
        <SolidColorBrush x:Key="RecentSalesBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="DiscountBrush" Color="#E91E63"/>
        <SolidColorBrush x:Key="NeutralBrush" Color="#607D8B"/>
        <SolidColorBrush x:Key="FavoritesBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="PageBackgroundBrush" Color="Transparent"/>
        <SolidColorBrush x:Key="CartBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="ProductCardBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="SectionBackgroundBrush" Color="#FFFFFF"/>
        
        <!-- Button Styles -->
        <Style x:Key="ColoredActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Background" Value="{StaticResource PrimaryActionBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="Height" Value="48"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource PrimaryHueDarkBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="QuickAccessButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Height" Value="36"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="6"/>
        </Style>

        <Style x:Key="FavoriteToggleButton" TargetType="ToggleButton">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="4"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <!-- Add hover effect to product cards -->
        <Style x:Key="ProductCard" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="{StaticResource ProductCardBackgroundBrush}"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <materialDesign:DialogHost Identifier="SalesDialog">
        <Grid Margin="12" Background="Transparent">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="380"/>
            </Grid.ColumnDefinitions>

            <!-- Left Side - Products -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,12,0" UniformCornerRadius="12" Background="{StaticResource SectionBackgroundBrush}" materialDesign:ElevationAssist.Elevation="Dp1">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Search and Category Filter -->
                    <materialDesign:Card Grid.Row="0" 
                                       Margin="0,0,0,16"
                                       Background="{StaticResource HeaderBackgroundBrush}"
                                       UniformCornerRadius="12"
                                       materialDesign:ElevationAssist.Elevation="Dp1">
                        <Grid Margin="16,12">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Search Box and Category -->
                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="txtSearch"
                                        Grid.Column="0"
                                        Style="{StaticResource MaterialDesignFilledTextBox}"
                                        materialDesign:HintAssist.Hint="{DynamicResource ProductSearchHint}"
                                        materialDesign:TextFieldAssist.HasClearButton="True"
                                        materialDesign:TextFieldAssist.PrefixText="🔍"
                                        KeyDown="txtSearch_KeyDown"
                                        TextChanged="txtSearch_TextChanged"
                                        Margin="0,0,16,0"
                                        Height="48"
                                        TextOptions.TextFormattingMode="Ideal"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True"
                                        SnapsToDevicePixels="True"
                                        RenderOptions.ClearTypeHint="Enabled"
                                        RenderOptions.BitmapScalingMode="Fant"
                                        CacheMode="BitmapCache"
                                        materialDesign:TextFieldAssist.CharacterCounterVisibility="Collapsed"
                                        materialDesign:TextFieldAssist.TextFieldCornerRadius="4">
                                    <TextBox.Resources>
                                        <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignFilledTextBox}">
                                            <Setter Property="FontFamily" Value="{StaticResource MaterialDesignFont}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsSearching}" Value="True">
                                                    <Setter Property="materialDesign:TextFieldAssist.HasTrailingIcon" Value="False"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsSearching}" Value="False">
                                                    <Setter Property="materialDesign:TextFieldAssist.HasTrailingIcon" Value="True"/>
                                                    <Setter Property="materialDesign:TextFieldAssist.TrailingIcon" Value="Magnify"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBox.Resources>
                                </TextBox>

                                <ComboBox Grid.Column="1"
                                         x:Name="categoryFilter"
                                         Style="{StaticResource MaterialDesignFilledComboBox}"
                                         materialDesign:HintAssist.Hint="{DynamicResource Category}"
                                         ItemsSource="{Binding Categories}"
                                         SelectedItem="{Binding SelectedCategory, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         DisplayMemberPath="Name"
                                         Width="180"
                                         Height="48"
                                         Margin="0,0,16,0"
                                         SelectionChanged="CategoryFilter_SelectionChanged">
                                    <ComboBox.ItemContainerStyle>
                                        <Style TargetType="ComboBoxItem">
                                            <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        </Style>
                                    </ComboBox.ItemContainerStyle>
                                </ComboBox>

                                <Button Grid.Column="2"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        Height="48"
                                        Click="ManualAdd_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                                                <TextBlock Text="{DynamicResource CustomProduct}"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </Grid>

                            <!-- Quick Access Buttons -->
                            <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal"
                                      Margin="0,8,0,0">
                                <materialDesign:Card Background="{DynamicResource MaterialDesignBackground}"
                                                   UniformCornerRadius="8"
                                                   materialDesign:ElevationAssist.Elevation="Dp1">
                                    <StackPanel Orientation="Horizontal">
                                        <!-- Popular Items Button -->
                                        <Button x:Name="PopularItemsButton"
                                                Style="{StaticResource MaterialDesignFlatButton}"
                                                Click="PopularItems_Click"
                                                Height="40"
                                                Margin="4,4,0,4"
                                                Background="{StaticResource PopularItemsBrush}"
                                                Foreground="White"
                                                materialDesign:ButtonAssist.CornerRadius="6">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="TrendingUp" 
                                                                       Width="20" 
                                                                       Height="20"
                                                                       Margin="0,0,8,0"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{DynamicResource PopularItems}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>

                                        <!-- Favorites Button -->
                                        <Button Command="{Binding ShowFavoritesCommand}"
                                                Style="{StaticResource MaterialDesignFlatButton}"
                                                Height="40"
                                                Margin="4,4,0,4"
                                                Background="{StaticResource FavoritesBrush}"
                                                Foreground="White"
                                                materialDesign:ButtonAssist.CornerRadius="6">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Heart" 
                                                                       Width="20" 
                                                                       Height="20"
                                                                       Margin="0,0,8,0"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{DynamicResource Favorites}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>

                                        <!-- Clear Filter Button -->
                                        <Button Click="ClearFilter_Click"
                                                Style="{StaticResource MaterialDesignFlatButton}"
                                                Height="40"
                                                Margin="4,4,4,4"
                                                Background="{StaticResource NeutralBrush}"
                                                Foreground="White"
                                                materialDesign:ButtonAssist.CornerRadius="6">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FilterRemove" 
                                                                       Width="20" 
                                                                       Height="20"
                                                                       Margin="0,0,8,0"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{DynamicResource ClearFilter}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>
                                    </StackPanel>
                                </materialDesign:Card>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Products List -->
                    <Grid Grid.Row="2">
                        <ListBox ItemsSource="{Binding FilteredProducts}"
                                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                ScrollViewer.VerticalScrollBarVisibility="Auto"
                                ScrollViewer.ScrollChanged="ProductsScrollViewer_ScrollChanged"
                                VirtualizingStackPanel.IsVirtualizing="True"
                                VirtualizingStackPanel.VirtualizationMode="Recycling"
                                VirtualizingPanel.ScrollUnit="Pixel"
                                VirtualizingPanel.CacheLength="1,2"
                                VirtualizingPanel.CacheLengthUnit="Page"
                                Background="Transparent"
                                BorderThickness="0">
                            <ListBox.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <VirtualizingStackPanel 
                                        IsItemsHost="True"
                                        Orientation="Vertical"
                                        VirtualizingStackPanel.IsVirtualizing="True"
                                        VirtualizingStackPanel.VirtualizationMode="Recycling">
                                        <VirtualizingStackPanel.Resources>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="Margin" Value="6"/>
                                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                            </Style>
                                        </VirtualizingStackPanel.Resources>
                                    </VirtualizingStackPanel>
                                </ItemsPanelTemplate>
                            </ListBox.ItemsPanel>
                            <ListBox.Template>
                                <ControlTemplate TargetType="ListBox">
                                    <ScrollViewer x:Name="ScrollViewer"
                                                Focusable="False"
                                                Padding="{TemplateBinding Padding}"
                                                CanContentScroll="True">
                                        <WrapPanel IsItemsHost="True" 
                                                  Orientation="Horizontal" 
                                                  Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=ScrollViewer}}"
                                                  HorizontalAlignment="Left"/>
                                    </ScrollViewer>
                                </ControlTemplate>
                            </ListBox.Template>
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ListBoxItem">
                                                <ContentPresenter/>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="Margin" Value="6"/>
                                    <Setter Property="CacheMode" Value="BitmapCache"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                </Style>
                            </ListBox.ItemContainerStyle>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Width="200" 
                                                      Height="280"
                                                      Background="{StaticResource ProductCardBackgroundBrush}"
                                                      UniformCornerRadius="12"
                                                      materialDesign:ElevationAssist.Elevation="Dp2"
                                                      MouseDown="Product_MouseDown"
                                                      CacheMode="BitmapCache">
                                        <Grid>
                                            <!-- Main Content -->
                                            <Grid Margin="16" CacheMode="BitmapCache">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="140"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Product Image Container -->
                                                <Border Grid.Row="0" 
                                                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                        BorderThickness="1"
                                                        CornerRadius="8"
                                                        Background="White"
                                                        CacheMode="BitmapCache">
                                                    <Grid>
                                                        <!-- Placeholder when no image -->
                                                        <StackPanel x:Name="noImagePlaceholder"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalAlignment="Center"
                                                                  Visibility="{Binding ImageData, Converter={StaticResource NullToVisibilityConverter}, ConverterParameter=True}">
                                                            <materialDesign:PackIcon Kind="Image"
                                                                                Width="32" Height="32"
                                                                                Opacity="0.3"/>
                                                        </StackPanel>
                                                        
                                                        <!-- Product Image -->
                                                        <Image Source="{Binding ImageData, Converter={StaticResource Base64ToImageConverter}}"
                                                               Stretch="Uniform"
                                                               RenderOptions.BitmapScalingMode="Fant"
                                                               CacheMode="BitmapCache"
                                                               Visibility="{Binding ImageData, Converter={StaticResource NullToVisibilityConverter}}"/>
                                                    </Grid>
                                                </Border>

                                                <!-- Overlay Elements (Status and Favorite) -->
                                                <Grid Grid.Row="0"
                                                      Panel.ZIndex="1">
                                                    <!-- Status Overlay -->
                                                    <materialDesign:Card Background="#f44336"
                                                                      Opacity="0.9"
                                                                      UniformCornerRadius="8"
                                                                      Margin="0,0,32,0"
                                                                      HorizontalAlignment="Right"
                                                                      VerticalAlignment="Top"
                                                                      Visibility="{Binding IsOutOfStock, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <TextBlock Text="{DynamicResource OutOfStock}"
                                                                 Foreground="White"
                                                                 Margin="8,4"
                                                                 FontWeight="Medium"/>
                                                    </materialDesign:Card>

                                                    <!-- Enhanced Out-of-Stock Overlay with Reservation Option -->
                                                    <Border Grid.RowSpan="4"
                                                            CornerRadius="12"
                                                            Panel.ZIndex="3">
                                                        <Border.Background>
                                                            <!-- Semi-transparent overlay to show product info underneath -->
                                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1" Opacity="0.85">
                                                                <GradientStop Color="#1F2937" Offset="0.0"/>
                                                                <GradientStop Color="#111827" Offset="1.0"/>
                                                            </LinearGradientBrush>
                                                        </Border.Background>
                                                        <Grid>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="*"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                            </Grid.RowDefinitions>

                                                            <!-- Product name and price visible when out of stock -->
                                                            <StackPanel Grid.Row="0" Margin="12,12,12,8">
                                                                <TextBlock Text="{Binding Name}"
                                                                           Foreground="White"
                                                                           FontWeight="SemiBold"
                                                                           FontSize="14"
                                                                           TextAlignment="Center"
                                                                           TextWrapping="Wrap"
                                                                           MaxHeight="40"
                                                                           TextTrimming="CharacterEllipsis"/>
                                                                <TextBlock Text="{Binding SellingPrice, Converter={StaticResource CurrencyFormatConverter}}"
                                                                           Foreground="#E5E7EB"
                                                                           FontWeight="Medium"
                                                                           FontSize="12"
                                                                           TextAlignment="Center"
                                                                           Margin="0,4,0,0"/>
                                                            </StackPanel>

                                                            <!-- Out of stock indicator and reservation button -->
                                                            <StackPanel Grid.Row="2"
                                                                        HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"
                                                                        Margin="12,8,12,12">
                                                                <materialDesign:PackIcon Kind="AlertCircleOutline"
                                                                                         Width="24" Height="24"
                                                                                         Foreground="#EF4444"
                                                                                         HorizontalAlignment="Center"
                                                                                         Margin="0,0,0,6"/>
                                                                <TextBlock Text="{DynamicResource OutOfStock}"
                                                                           Foreground="#EF4444"
                                                                           FontWeight="Bold"
                                                                           HorizontalAlignment="Center"
                                                                           FontSize="12"
                                                                           Margin="0,0,0,12"/>

                                                                <!-- Reserve button for creating reservation invoice -->
                                                                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                                                        Background="#3B82F6"
                                                                        BorderBrush="#2563EB"
                                                                        Foreground="White"
                                                                        FontSize="10"
                                                                        Height="28"
                                                                        MinWidth="80"
                                                                        Padding="12,4"
                                                                        Click="CreateReservationInvoice_Click"
                                                                        Tag="{Binding}"
                                                                        ToolTip="Create a reservation invoice for this out-of-stock product"
                                                                        Visibility="{Binding DataContext.CanCreateInvoices, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                                    <StackPanel Orientation="Horizontal">
                                                                        <materialDesign:PackIcon Kind="CalendarClock"
                                                                                                 Width="12" Height="12"
                                                                                                 VerticalAlignment="Center"
                                                                                                 Margin="0,0,4,0"/>
                                                                        <TextBlock Text="Reserve"
                                                                                   VerticalAlignment="Center"
                                                                                   FontSize="10"
                                                                                   FontWeight="Medium"/>
                                                                    </StackPanel>
                                                                </Button>
                                                            </StackPanel>
                                                        </Grid>
                                                        <Border.Style>
                                                            <Style TargetType="Border">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding IsOutOfStock}" Value="True">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Border.Style>
                                                    </Border>

                                                    <!-- Favorite Toggle -->
                                                    <ToggleButton Style="{StaticResource MaterialDesignActionToggleButton}"
                                                                HorizontalAlignment="Right"
                                                                VerticalAlignment="Top"
                                                                Width="32" Height="32"
                                                                Margin="0,0,0,0"
                                                                Command="{Binding DataContext.ToggleFavoriteCommand, 
                                                                         RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                CommandParameter="{Binding}"
                                                                IsChecked="{Binding IsFavorited, Mode=OneWay}">
                                                        <ToggleButton.Content>
                                                            <materialDesign:PackIcon Kind="Heart" 
                                                                                   Foreground="#757575"/>
                                                        </ToggleButton.Content>
                                                        <materialDesign:ToggleButtonAssist.OnContent>
                                                            <materialDesign:PackIcon Kind="Heart" 
                                                                                   Foreground="#f44336"/>
                                                        </materialDesign:ToggleButtonAssist.OnContent>
                                                    </ToggleButton>

                                                    <!-- Status Indicators -->
                                                    <StackPanel HorizontalAlignment="Left" 
                                                              VerticalAlignment="Top" 
                                                              Margin="0,4,4,0">
                                                        <!-- Low Stock Indicator -->
                                                        <materialDesign:PackIcon Kind="AlertCircle"
                                                                               Width="24" Height="24"
                                                                               Foreground="#ff9800"
                                                                               Visibility="{Binding IsLowStock, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                        
                                                        <!-- Expiry Indicator -->
                                                        <materialDesign:PackIcon Kind="CalendarAlert"
                                                                               Width="24" Height="24"
                                                                               Foreground="#e91e63"
                                                                               Margin="0,4,0,0"
                                                                               Visibility="{Binding IsNearExpiry, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                    </StackPanel>
                                                </Grid>

                                                <!-- Product Name -->
                                                <TextBlock Grid.Row="1"
                                                         Text="{Binding Name}"
                                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                         FontWeight="Medium"
                                                         TextTrimming="CharacterEllipsis"
                                                         Margin="0,12,0,4"/>

                                                <!-- Price -->
                                                <TextBlock Grid.Row="2"
                                                         Text="{Binding SellingPrice, StringFormat={}{0:N2} DA}"
                                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                         FontWeight="Bold"
                                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                         Margin="0,0,0,12"/>

                                                <!-- Stock Status -->
                                                <Border Grid.Row="3" 
                                                        CornerRadius="8"
                                                        Background="{DynamicResource MaterialDesignBackground}"
                                                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                        BorderThickness="1">
                                                    <TextBlock Text="{Binding StockStatus}"
                                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                             Margin="8,4"
                                                             HorizontalAlignment="Center"/>
                                                </Border>

                                                <!-- Add to Cart Button -->
                                                <Button Grid.Row="4"
                                                        Style="{StaticResource MaterialDesignFlatButton}"
                                                        Command="{Binding DataContext.AddToCartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        IsEnabled="{Binding IsInStock}"
                                                        Margin="0,12,0,0">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="CartPlus" 
                                                                               Width="20" 
                                                                               Height="20"
                                                                               Margin="0,0,8,0"/>
                                                        <TextBlock Text="{DynamicResource AddToCart}"/>
                                                    </StackPanel>
                                                </Button>
                                            </Grid>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                        
                        <!-- Loading Indicator -->
                        <materialDesign:Card HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           Margin="0,0,0,16"
                                           Padding="16,8"
                                           materialDesign:ElevationAssist.Elevation="Dp4"
                                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                           Value="0"
                                           IsIndeterminate="True"
                                           Width="20"
                                           Height="20"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="Loading more products..."
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Right Side - Cart -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Customer Loyalty Section -->
                <materialDesign:Card Grid.Row="0"
                        Background="{StaticResource HeaderBackgroundBrush}"
                        UniformCornerRadius="12"
                        materialDesign:ElevationAssist.Elevation="Dp1"
                        Margin="0,0,0,8">
                    <Grid Margin="8,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Customer Info -->
                        <Button Grid.Column="0"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Click="AddCustomer_Click"
                                Width="32" Height="32"
                                Padding="4"
                                ToolTip="{DynamicResource SelectCustomer}"
                                Margin="0,0,8,0">
                            <materialDesign:PackIcon Kind="AccountPlus" 
                                                   Width="20" 
                                                   Height="20"/>
                        </Button>

                        <!-- Selected Customer Display -->
                        <StackPanel Grid.Column="1" 
                                  Orientation="Horizontal" 
                                  VerticalAlignment="Center"
                                  Margin="0,4">
                            <!-- Customer Name -->
                            <TextBlock Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}}">
                                <Run Text="{Binding SelectedCustomer.FirstName}"/>
                                <Run Text=" "/>
                                <Run Text="{Binding SelectedCustomer.LastName}"/>
                            </TextBlock>
                            
                            <!-- Loyalty Points Value -->
                            <TextBlock Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                     Visibility="{Binding HasLoyaltyCustomer, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     Text="{Binding CustomerLoyaltyPoints, StringFormat=N0}"/>
                        </StackPanel>

                        <!-- Points Info/Redeem Button -->
                        <Button Grid.Column="2"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Click="RedeemPoints_Click"
                                Width="32" Height="32"
                                Padding="4"
                                Margin="0,0,4,0"
                                IsEnabled="{Binding CanRedeemPoints}"
                                Visibility="{Binding HasLoyaltyCustomer, Converter={StaticResource BooleanToVisibilityConverter}}"
                                ToolTip="{DynamicResource RedeemPoints}">
                            <materialDesign:PackIcon Kind="Gift" Width="20" Height="20"/>
                        </Button>

                        <!-- Clear Customer Button -->
                        <Button Grid.Column="3"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Click="ClearCustomer_Click"
                               Width="32" Height="32"
                               Padding="4"
                               Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}}"
                               ToolTip="{DynamicResource Clear}">
                            <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                        </Button>
                    </Grid>
                </materialDesign:Card>

                <!-- Cart Section -->
                <materialDesign:Card Grid.Row="1"
                                   Background="{StaticResource CartBackgroundBrush}"
                                   UniformCornerRadius="12"
                                   materialDesign:ElevationAssist.Elevation="Dp1"
                                   Margin="0,0,0,8">
                    <DockPanel>
                        <TabControl ItemsSource="{Binding Carts}"
                                  SelectedItem="{Binding ActiveCart}"
                                  Style="{StaticResource MaterialDesignTabControl}"
                                  materialDesign:ColorZoneAssist.Mode="PrimaryLight"
                                  IsSynchronizedWithCurrentItem="True"
                                  VirtualizingStackPanel.IsVirtualizing="True"
                                  VirtualizingStackPanel.VirtualizationMode="Recycling">
                            <TabControl.ItemTemplate>
                                <DataTemplate>
                                    <DockPanel LastChildFill="True">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                Click="CloseCart_Click"
                                                Width="24" Height="24"
                                                Padding="0"
                                                DockPanel.Dock="Right"
                                                Margin="8,0,0,0"
                                                ToolTip="{DynamicResource CloseCart}">
                                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                        </Button>
                                        <TextBlock Text="{Binding Name}"
                                                 Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                    </DockPanel>
                                </DataTemplate>
                            </TabControl.ItemTemplate>
                            <TabControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <VirtualizingStackPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </TabControl.ItemsPanel>
                            <TabControl.ContentTemplate>
                                <DataTemplate>
                                    <Grid DataContext="{Binding}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- Cart Items -->
                                        <DataGrid Grid.Row="0"
                                                 Style="{StaticResource AppDataGridStyle}"
                                                 ItemsSource="{Binding Items, UpdateSourceTrigger=PropertyChanged, NotifyOnSourceUpdated=True}"
                                                 SelectedItem="{Binding DataContext.SelectedCartItem, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                 AutoGenerateColumns="False"
                                                 CanUserAddRows="False"
                                                 SelectionMode="Single"
                                                 IsReadOnly="True"
                                                 HeadersVisibility="None"
                                                 SelectionChanged="DataGrid_SelectionChanged"
                                                 VirtualizingPanel.IsVirtualizing="True"
                                                 VirtualizingPanel.VirtualizationMode="Recycling"
                                                 VirtualizingPanel.ScrollUnit="Pixel"
                                                 VirtualizingPanel.CacheLength="1,2"
                                                 VirtualizingPanel.CacheLengthUnit="Page"
                                                 Margin="4,0,4,4">
                                            <DataGrid.Columns>
                                                <DataGridTemplateColumn Width="0.5*">
                                                    <DataGridTemplateColumn.CellTemplate>
                                                        <DataTemplate>
                                                            <StackPanel Margin="4,2">
                                                                <TextBlock Text="{Binding Product.Name}"
                                                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                                         TextWrapping="Wrap"/>
                                                                <TextBlock Text="{Binding Total, StringFormat={}{0:N2} DA}"
                                                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>
                                                <DataGridTemplateColumn Width="0.4*">
                                                    <DataGridTemplateColumn.CellTemplate>
                                                        <DataTemplate>
                                                            <StackPanel Orientation="Horizontal" Margin="2,2">
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        Click="DecreaseQuantity_Click"
                                                                        Width="24" Height="24"
                                                                        Padding="0">
                                                                    <materialDesign:PackIcon Kind="Minus" Width="14" Height="14"/>
                                                                </Button>
                                                                <TextBlock Text="{Binding Converter={StaticResource QuantityDisplayConverter}}"
                                                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                                         VerticalAlignment="Center"
                                                                         MinWidth="20"
                                                                         TextAlignment="Center"/>
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        Click="IncreaseQuantity_Click"
                                                                        Width="24" Height="24"
                                                                        Padding="0">
                                                                    <materialDesign:PackIcon Kind="Plus" Width="14" Height="14"/>
                                                                </Button>
                                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                        Click="SetQuantity_Click"
                                                                        Width="24" Height="24"
                                                                        Padding="0"
                                                                        ToolTip="{DynamicResource SetQuantity}">
                                                                    <materialDesign:PackIcon Kind="Counter" Width="14" Height="14"/>
                                                                </Button>
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>
                                                <DataGridTemplateColumn Width="0.1*">
                                                    <DataGridTemplateColumn.CellTemplate>
                                                        <DataTemplate>
                                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                    Click="RemoveItem_Click"
                                                                    Width="24" Height="24"
                                                                    Padding="0">
                                                                <materialDesign:PackIcon Kind="Delete" Width="14" Height="14"/>
                                                            </Button>
                                                        </DataTemplate>
                                                    </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>
                                            </DataGrid.Columns>
                                        </DataGrid>

                                        <!-- Buttons Panel -->
                                        <StackPanel Grid.Row="1" 
                                                  Orientation="Horizontal" 
                                                  HorizontalAlignment="Right"
                                                  Margin="0,0,4,0">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Click="ApplyItemDiscount_Click"
                                                    IsEnabled="{Binding HasSelectedCartItem}"
                                                    Width="28" Height="28"
                                                    Padding="0"
                                                    Margin="0,0,8,0"
                                                    ToolTip="{DynamicResource ApplyItemDiscount}">
                                                <materialDesign:PackIcon Kind="Tag" Width="16" Height="16"/>
                                            </Button>

                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Click="ApplyCartDiscount_Click"
                                                    IsEnabled="{Binding HasCartItems}"
                                                    Width="28" Height="28"
                                                    Padding="0"
                                                    Margin="0,0,8,0"
                                                    ToolTip="{DynamicResource ApplyCartDiscount}">
                                                <materialDesign:PackIcon Kind="Sale" Width="16" Height="16"/>
                                            </Button>

                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Click="NewCart_Click"
                                                    Width="28" Height="28"
                                                    Padding="0"
                                                    ToolTip="{DynamicResource NewCart}">
                                                <materialDesign:PackIcon Kind="CartPlus" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </TabControl.ContentTemplate>
                        </TabControl>
                    </DockPanel>
                </materialDesign:Card>

                <!-- Cart Summary -->
                <materialDesign:Card Grid.Row="2"
                        Background="{StaticResource SectionBackgroundBrush}"
                        UniformCornerRadius="12"
                        materialDesign:ElevationAssist.Elevation="Dp3"
                        Margin="0,0,0,0">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                            
                        <!-- Totals -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Subtotal -->
                            <Border Grid.Row="0" Grid.ColumnSpan="2" 
                                  Background="{DynamicResource MaterialDesignBackground}"
                                  CornerRadius="8"
                                  Margin="0,0,0,8"
                                  Padding="12,8">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CashRegister" 
                                                             Width="20" Height="20" 
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                        <TextBlock Text="{DynamicResource Subtotal}" 
                                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                 FontWeight="Medium"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <TextBlock Text="{Binding CurrentCart.Subtotal, StringFormat={}{0:N2} DA}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Grid.Column="1"
                                             FontWeight="Medium"
                                             TextAlignment="Right"/>
                                </Grid>
                            </Border>

                            <!-- Grand Total -->
                            <Border Grid.Row="1" Grid.ColumnSpan="2"
                                  Background="#121212"
                                  BorderBrush="#333333"
                                  BorderThickness="1"
                                  CornerRadius="8"
                                  Padding="8,8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Scanline Effect -->
                                    <Rectangle Grid.RowSpan="2" Fill="#121212" Opacity="0.1">
                                        <Rectangle.OpacityMask>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="Transparent" Offset="0.0"/>
                                                <GradientStop Color="Black" Offset="0.2"/>
                                                <GradientStop Color="Transparent" Offset="0.4"/>
                                                <GradientStop Color="Black" Offset="0.6"/>
                                                <GradientStop Color="Transparent" Offset="0.8"/>
                                                <GradientStop Color="Black" Offset="1.0"/>
                                            </LinearGradientBrush>
                                        </Rectangle.OpacityMask>
                                    </Rectangle>

                                    <!-- Digital Display Total (no Viewbox scaling to avoid pixelation) -->
                                    <Grid Grid.Row="0" Margin="4">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock
                                             FontFamily="DSEG7 Modern, DSEG7 Classic, Digital-7, 7 Segment, LED Digital, Consolas, Monospace"
                                             FontSize="56"
                                             FontWeight="Normal"
                                             Foreground="#00FF00"
                                             TextAlignment="Center"
                                             VerticalAlignment="Center"
                                             HorizontalAlignment="Center"
                                             TextOptions.TextFormattingMode="Display"
                                             TextOptions.TextRenderingMode="ClearType"
                                             TextOptions.TextHintingMode="Fixed"
                                             UseLayoutRounding="True"
                                             SnapsToDevicePixels="True"
                                             RenderOptions.ClearTypeHint="Enabled"
                                             RenderOptions.EdgeMode="Aliased">
                                            <TextBlock.Text>
                                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                                    <Binding Path="CurrentCart.GrandTotal"/>
                                                    <Binding Source="{StaticResource DA}"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                            <TextBlock.Effect>
                                                <DropShadowEffect ShadowDepth="0" BlurRadius="6" Color="#00FF00" Opacity="0.9"/>
                                            </TextBlock.Effect>
                                        </TextBlock>
                                    </Grid>
                                    
                                    <!-- Discount Display -->
                                    <Grid Grid.Row="1" 
                                         HorizontalAlignment="Right"
                                         Margin="0,4,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0"
                                                 Text="{DynamicResource Discount}"
                                                 FontFamily="Consolas, Courier New"
                                                 FontSize="14"
                                                 Foreground="#AAAAAA"
                                                 Margin="0,0,4,0"/>
                                        <TextBlock Grid.Column="1"
                                                 Text="{Binding CurrentCart.DiscountAmount, StringFormat=\{0:N2\}}"
                                                 FontFamily="Consolas, Courier New"
                                                 FontSize="14"
                                                 Foreground="#FFFF00"
                                                 TextAlignment="Right">
                                            <TextBlock.Effect>
                                                <DropShadowEffect ShadowDepth="0" BlurRadius="2" Color="#FFFF00" Opacity="0.5"/>
                                            </TextBlock.Effect>
                                        </TextBlock>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- Process Payment Button -->
                        <Button Grid.Row="1"
                                Style="{StaticResource MaterialDesignFlatButton}"
                                Background="{StaticResource PrimaryActionBrush}"
                                Foreground="White"
                                materialDesign:ButtonAssist.CornerRadius="8"
                                Height="56"
                                FontSize="18"
                                FontWeight="Medium"
                                Click="ProcessPayment_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CreditCard" 
                                                       Width="28" 
                                                       Height="28"
                                                       Margin="0,0,12,0"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource ProcessPaymentButton}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</UserControl>