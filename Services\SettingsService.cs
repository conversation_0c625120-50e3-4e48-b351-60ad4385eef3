using System;
using System.Configuration;
using System.IO;
using System.Windows.Media.Imaging;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class SettingsService : ISettingsService
    {
        // ✅ CRITICAL FIX: Cache settings to prevent repeated ConfigurationManager access
        private static readonly Dictionary<string, string> _settingsCache = new Dictionary<string, string>();
        private static readonly object _cacheLock = new object();
        private static bool _cacheInitialized = false;

        private void InitializeCache()
        {
            if (_cacheInitialized) return;

            lock (_cacheLock)
            {
                if (_cacheInitialized) return;

                try
                {
                    // Load all settings into cache at once
                    var appSettings = ConfigurationManager.AppSettings;
                    foreach (string key in appSettings.AllKeys)
                    {
                        _settingsCache[key] = appSettings[key];
                    }
                    _cacheInitialized = true;
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] Cached {_settingsCache.Count} settings");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] Error initializing cache: {ex.Message}");
                    _cacheInitialized = true; // Prevent retry loops
                }
            }
        }

        public string GetSetting(string key)
        {
            try
            {
                InitializeCache();

                lock (_cacheLock)
                {
                    return _settingsCache.TryGetValue(key, out string value) ? value : null;
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        public void SetSetting(string key, string value)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                config.AppSettings.Settings.Remove(key);
                config.AppSettings.Settings.Add(key, value);
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");

                // ✅ CRITICAL FIX: Update cache when setting is changed
                lock (_cacheLock)
                {
                    _settingsCache[key] = value;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error saving setting: {ex.Message}", ex);
            }
        }

        public BitmapImage GetCompanyLogo()
        {
            try
            {
                string logoBase64 = GetSetting("CompanyLogo");
                if (string.IsNullOrEmpty(logoBase64))
                {
                    return null;
                }

                // Convert Base64 string to image
                byte[] imageData = Convert.FromBase64String(logoBase64);
                var imageSource = new BitmapImage();
                
                using (var ms = new MemoryStream(imageData))
                {
                    imageSource.BeginInit();
                    imageSource.StreamSource = ms;
                    imageSource.CacheOption = BitmapCacheOption.OnLoad;
                    imageSource.EndInit();
                }
                
                return imageSource;
            }
            catch (Exception)
            {
                // If there's any error loading the logo, return null
                return null;
            }
        }

        public void RemoveSetting(string key)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                config.AppSettings.Settings.Remove(key);
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error removing setting: {ex.Message}", ex);
            }
        }

        public bool HasSetting(string key)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return !string.IsNullOrEmpty(value);
            }
            catch (Exception)
            {
                return false;
            }
        }

        public void SaveSettings()
        {
            // Settings are saved immediately in SetSetting method
            // This method is here for interface compliance
        }

        public void ReloadSettings()
        {
            try
            {
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reloading settings: {ex.Message}", ex);
            }
        }

        public void SaveSetting(string key, string value)
        {
            SetSetting(key, value);
        }

        string ISettingsService.GetCompanyLogo()
        {
            return GetSetting("CompanyLogo");
        }
    }
}