using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Extension of the Sale model with properties specifically needed for dashboard functionality.
    /// If the original Sale class is modified to include these properties, this class can be removed.
    /// </summary>
    public class DashboardSale
    {
        /// <summary>
        /// ID of the sale
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Date and time of the sale
        /// </summary>
        public DateTime Date { get; set; }
        
        /// <summary>
        /// Total amount of the sale
        /// </summary>
        public decimal GrandTotal { get; set; }
        
        /// <summary>
        /// Profit of the sale
        /// </summary>
        public decimal Profit { get; set; }
        
        /// <summary>
        /// Total number of items in the sale
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// Factory method to create a DashboardSale from a regular Sale
        /// </summary>
        public static DashboardSale FromSale(POSSystem.Models.Sale sale)
        {
            return new DashboardSale
            {
                Id = sale.Id,
                Date = sale.SaleDate,
                GrandTotal = sale.GrandTotal,
                Profit = CalculateProfit(sale),
                TotalItems = sale.TotalItems
            };
        }
        
        /// <summary>
        /// Calculates the profit for a sale based on its items using actual FIFO cost basis
        /// </summary>
        private static decimal CalculateProfit(POSSystem.Models.Sale sale)
        {
            decimal profit = 0;

            if (sale.Items == null)
                return profit;

            foreach (var item in sale.Items)
            {
                if (item.Product != null)
                {
                    // Use actual cost basis if available, otherwise fall back to product purchase price
                    decimal costBasis = item.ActualCostBasis > 0 ? item.ActualCostBasis : item.Product.PurchasePrice;
                    profit += (item.UnitPrice - costBasis) * item.Quantity;
                }
            }

            return profit;
        }
    }
    
    /// <summary>
    /// Extension method class for Sale-related operations
    /// </summary>
    public static class SaleExtensions
    {
        /// <summary>
        /// Converts a list of Sales to DashboardSales
        /// </summary>
        public static List<DashboardSale> ToDashboardSales(this List<POSSystem.Models.Sale> sales)
        {
            var result = new List<DashboardSale>();

            if (sales == null)
                return result;

            foreach (var sale in sales)
            {
                result.Add(DashboardSale.FromSale(sale));
            }

            return result;
        }
    }
} 