using System;
using System.Collections.Generic;

namespace POSSystem.Services.Migration
{
    /// <summary>
    /// Result of database migration operation
    /// </summary>
    public class MigrationResult
    {
        public MigrationResult()
        {
            Errors = new List<string>();
            Warnings = new List<string>();
            StartTime = DateTime.Now;
        }

        public bool IsSuccess { get; set; }
        public string Message { get; set; } = "";
        public string BackupPath { get; set; } = "";
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;

        // Migration statistics
        public int LegacyProductCount { get; set; }
        public int MigratedProductCount { get; set; }
        public int SkippedProductCount { get; set; }
        public int MigratedCategoryCount { get; set; }
        public int FinalProductCount { get; set; }

        public List<string> Errors { get; set; }
        public List<string> Warnings { get; set; }

        public string GetSummary()
        {
            var summary = $"Migration Summary:\n";
            summary += $"- Status: {(IsSuccess ? "SUCCESS" : "FAILED")}\n";
            summary += $"- Duration: {Duration.TotalSeconds:F1} seconds\n";
            summary += $"- Legacy Products Found: {LegacyProductCount}\n";
            summary += $"- Products Migrated: {MigratedProductCount}\n";
            summary += $"- Products Skipped: {SkippedProductCount}\n";
            summary += $"- Categories Migrated: {MigratedCategoryCount}\n";
            summary += $"- Final Product Count: {FinalProductCount}\n";
            
            if (!string.IsNullOrEmpty(BackupPath))
                summary += $"- Backup Created: {BackupPath}\n";

            if (Errors.Count > 0)
            {
                summary += $"\nErrors ({Errors.Count}):\n";
                foreach (var error in Errors)
                    summary += $"  - {error}\n";
            }

            if (Warnings.Count > 0)
            {
                summary += $"\nWarnings ({Warnings.Count}):\n";
                foreach (var warning in Warnings)
                    summary += $"  - {warning}\n";
            }

            return summary;
        }
    }

    /// <summary>
    /// Schema information from legacy database
    /// </summary>
    public class LegacyDatabaseSchema
    {
        public LegacyDatabaseSchema()
        {
            Tables = new List<string>();
        }

        public List<string> Tables { get; set; }
        public int ProductCount { get; set; }
        public int CategoryCount { get; set; }
        public bool HasProductBarcodes => Tables.Contains("ProductBarcodes");
        public bool HasBatchStock => Tables.Contains("BatchStock");
        public bool HasSuppliers => Tables.Contains("Suppliers");
    }

    /// <summary>
    /// Product data extracted from legacy database
    /// </summary>
    public class ProductMigrationData
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string? SKU { get; set; }
        public string Description { get; set; } = "";
        public decimal PurchasePrice { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal StockQuantity { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderPoint { get; set; }
        public string? Barcode { get; set; }
        public string CategoryName { get; set; } = "General";
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Migration configuration options
    /// </summary>
    public class MigrationOptions
    {
        public bool CreateBackup { get; set; } = true;
        public bool SkipExistingProducts { get; set; } = true;
        public bool MigrateInactiveProducts { get; set; } = false;
        public bool ValidateIntegrity { get; set; } = true;
        public string DefaultCategoryName { get; set; } = "Migrated Items";
        public bool PreserveLegacyIds { get; set; } = false;
    }
}
