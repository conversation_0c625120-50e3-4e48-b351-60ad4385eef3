-- Fix Weight-Based Product Stock Quantities
-- Run this in SQLite DB Browser to give weight-based products proper stock

-- Check current stock status of weight-based products
SELECT 'Current Weight-Based Products Stock Status:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    IsActive,
    CASE 
        WHEN StockQuantity = 0 THEN 'Out of Stock'
        WHEN StockQuantity <= MinimumStock AND StockQuantity > 0 THEN 'Low Stock'
        WHEN StockQuantity > 0 THEN 'In Stock'
        ELSE 'Unknown'
    END as CurrentStatus
FROM Products 
WHERE IsWeightBased = 1 AND IsActive = 1
ORDER BY Id;

-- Update Product 529 (www1) to have stock
UPDATE Products 
SET 
    StockQuantity = 10,
    MinimumStock = 2,
    UpdatedAt = datetime('now')
WHERE Id = 529 AND IsWeightBased = 1;

-- Update Product 483 (if it exists) to have stock
UPDATE Products 
SET 
    StockQuantity = 15,
    MinimumStock = 3,
    UpdatedAt = datetime('now')
WHERE Id = 483 AND IsWeightBased = 1;

-- Update any other weight-based products that have 0 stock
UPDATE Products 
SET 
    StockQuantity = 20,
    MinimumStock = 5,
    UpdatedAt = datetime('now')
WHERE IsWeightBased = 1 
  AND IsActive = 1 
  AND StockQuantity = 0;

-- Show updated stock status
SELECT 'Updated Weight-Based Products Stock Status:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    IsActive,
    CASE 
        WHEN StockQuantity = 0 THEN 'Out of Stock'
        WHEN StockQuantity <= MinimumStock AND StockQuantity > 0 THEN 'Low Stock'
        WHEN StockQuantity > 0 THEN 'In Stock'
        ELSE 'Unknown'
    END as NewStatus
FROM Products 
WHERE IsWeightBased = 1 AND IsActive = 1
ORDER BY Id;

-- Verify specific products
SELECT 'Verification - Product 529 (www1):' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    'Should now show as IN STOCK' as ExpectedResult
FROM Products 
WHERE Id = 529;

SELECT 'Verification - Product 483:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    'Should now show as IN STOCK' as ExpectedResult
FROM Products 
WHERE Id = 483;
