using POSSystem.Models;
using POSSystem.Services.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    public class UserPermissionsService : IUserPermissionsService
    {
        private readonly IDatabaseService _dbService;
        private User _currentUser;

        public UserPermissionsService(IDatabaseService dbService)
        {
            _dbService = dbService;
            _currentUser = null;
        }

        public void Initialize(User user)
        {
            _currentUser = user;
        }

        public User CurrentUser
        {
            get
            {
                if (_currentUser == null)
                {
                    // Try to get the current user from AuthenticationService
                    var authService = GetAuthenticationService();
                    if (authService != null)
                    {
                        _currentUser = authService.CurrentUser;
                    }
                }
                return _currentUser;
            }
        }

        public UserPermissions UserPermissions
        {
            get
            {
                if (CurrentUser == null) return null;
                return GetUserPermissions(CurrentUser.Id);
            }
        }

        private bool IsInRole(string roleName)
        {
            if (CurrentUser?.UserRole == null) return false;
            return string.Equals(CurrentUser.UserRole.Name, roleName, StringComparison.OrdinalIgnoreCase);
        }

        private AuthenticationService GetAuthenticationService()
        {
            try
            {
                return new AuthenticationService(_dbService);
            }
            catch
            {
                return null;
            }
        }

        public bool HasPermission(string permissionKey)
        {
            if (CurrentUser == null || CurrentUser.UserRole == null)
            {
                System.Diagnostics.Debug.WriteLine($"Permission check failed: No current user or role for '{permissionKey}'");
                return false;
            }

            System.Diagnostics.Debug.WriteLine($"Checking permission '{permissionKey}' for user '{CurrentUser.Username}' (ID: {CurrentUser.Id})");

            // Get user's custom permissions from database
            var userPermissions = GetUserPermissions(CurrentUser.Id);
            if (userPermissions == null)
            {
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] No custom permissions found for user {CurrentUser.Id} (Role: {CurrentUser.UserRole?.Name}), falling back to role-based permissions");

                // CRITICAL: Use proper role-based fallback logic instead of just checking for Admin
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Using role-based fallback for user {CurrentUser.Username}");

                // Fallback to role-based permissions if no custom permissions exist
                return GetRoleBasedPermission(permissionKey, CurrentUser.UserRole?.Name);
            }

            System.Diagnostics.Debug.WriteLine($"Found custom permissions for user {CurrentUser.Id}:");
            System.Diagnostics.Debug.WriteLine($"  - CanCreateSales: {userPermissions.CanCreateSales}");
            System.Diagnostics.Debug.WriteLine($"  - CanVoidSales: {userPermissions.CanVoidSales}");
            System.Diagnostics.Debug.WriteLine($"  - CanManageProducts: {userPermissions.CanManageProducts}");
            System.Diagnostics.Debug.WriteLine($"  - CanManageUsers: {userPermissions.CanManageUsers}");

            // Map permission keys to UserPermissions properties
            return permissionKey.ToLower() switch
            {
                // Sales permissions
                "sales.create" or "createsales" => userPermissions.CanCreateSales,
                "sales.void" or "voidsales" => userPermissions.CanVoidSales,
                "sales.discount" or "applydiscount" or "discount.applyany" or "discount.applylimited" => userPermissions.CanApplyDiscount,
                "sales.history" or "viewsaleshistory" => userPermissions.CanViewSalesHistory,
                "sales.manage" or "salespayment.process" => userPermissions.CanCreateSales, // Basic sales operations

                // Product permissions
                "products.manage" or "manageproducts" => userPermissions.CanManageProducts,
                "categories.manage" or "managecategories" => userPermissions.CanManageCategories,
                "inventory.view" or "viewinventory" or "viewproducts" => userPermissions.CanViewInventory,
                "inventory.adjust" or "adjustinventory" => userPermissions.CanAdjustInventory,

                // Financial permissions
                "expenses.manage" or "manageexpenses" => userPermissions.CanManageExpenses,
                "cashdrawer.manage" or "managecashdrawer" => userPermissions.CanManageCashDrawer,
                "reports.view" or "viewreports" => userPermissions.CanViewReports,
                "prices.manage" or "manageprices" => userPermissions.CanManagePrices,
                "finance.view" or "unpaidtransactions.view" => userPermissions.CanViewReports,
                "finance.manage" or "invoices.manage" => userPermissions.CanManageExpenses,

                // Customer & Supplier permissions
                "customers.manage" or "managecustomers" or "viewcustomers" => userPermissions.CanManageCustomers,
                "suppliers.manage" or "managesuppliers" => userPermissions.CanManageSuppliers,

                // Administrative permissions
                "users.manage" or "manageusers" => userPermissions.CanManageUsers,
                "roles.manage" or "manageroles" => userPermissions.CanManageRoles,
                "settings.access" or "accesssettings" => userPermissions.CanAccessSettings,
                "logs.view" or "viewlogs" => userPermissions.CanViewLogs,

                // Invoice permissions (Two-Tier System)
                "invoices.create_full" => userPermissions.CanCreateFullInvoices,
                "invoices.create_draft" => userPermissions.CanCreateDraftInvoices,
                "invoices.complete_drafts" => userPermissions.CanCompleteInvoiceDrafts,
                "invoices.view_pending_drafts" => userPermissions.CanViewPendingDrafts,
                "invoices.modify_pricing" => userPermissions.CanModifyInvoicePricing,
                "invoices.set_payment_terms" => userPermissions.CanSetPaymentTerms,
                "invoices.select_customers" => userPermissions.CanSelectCustomersForInvoices,
                "invoices.delete_drafts" => userPermissions.CanDeleteDraftInvoices,
                "invoices.reject_drafts" => userPermissions.CanRejectDraftInvoices,
                "invoices.manage_settings" => userPermissions.CanManageInvoiceSettings,

                // Entity deletion permissions
                var deletePermission when deletePermission.EndsWith(".delete") =>
                    userPermissions.CanManageUsers, // Only users with user management can delete entities

                // Default fallback - NEVER grant permissions for unknown keys
                // This ensures custom permissions are strictly enforced
                _ => false // Deny access to unknown permissions for security
            };
        }

        public bool CanProcessSalePayment()
        {
            // Simplified implementation
            return HasPermission("SalesPayment.Process") || HasPermission("Sales.Manage");
        }

        public bool CanViewUnpaidTransactions()
        {
            // Simplified implementation
            return HasPermission("UnpaidTransactions.View") || HasPermission("Finance.View");
        }

        public bool CanManageInvoices()
        {
            // Simplified implementation
            return HasPermission("Invoices.Manage") || HasPermission("Finance.Manage");
        }

        public bool CanApplyDiscount(decimal discountAmount)
        {
            // Simplified implementation
            return HasPermission("Discount.ApplyAny") || HasPermission("Discount.ApplyLimited");
        }

        // Two-Tier Invoice System Permission Methods
        public bool CanCreateFullInvoices()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanCreateFullInvoices;
            }

            // Fallback to role-based permissions
            return HasPermission("invoices.create_full") || IsInRole("Admin");
        }

        public bool CanCreateDraftInvoices()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanCreateDraftInvoices;
            }

            // Non-admins can create drafts, admins can create both
            return HasPermission("invoices.create_draft") ||
                   HasPermission("invoices.create_full") ||
                   !IsInRole("Admin"); // Non-admins default to draft creation
        }

        public bool CanCompleteInvoiceDrafts()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanCompleteInvoiceDrafts;
            }

            // Only admins and managers can complete drafts by default
            return HasPermission("invoices.complete_drafts") ||
                   IsInRole("Admin") ||
                   IsInRole("Manager");
        }

        public bool CanViewPendingDrafts()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanViewPendingDrafts;
            }

            // Admins and managers can view pending drafts
            return HasPermission("invoices.view_pending_drafts") ||
                   CanCompleteInvoiceDrafts();
        }

        public bool CanModifyInvoicePricing()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanModifyInvoicePricing;
            }

            // Only admins can modify pricing by default
            return HasPermission("invoices.modify_pricing") || IsInRole("Admin");
        }

        public bool CanSetPaymentTerms()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanSetPaymentTerms;
            }

            // Admins and managers can set payment terms
            return HasPermission("invoices.set_payment_terms") ||
                   IsInRole("Admin") ||
                   IsInRole("Manager");
        }

        public bool CanSelectCustomersForInvoices()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanSelectCustomersForInvoices;
            }

            // Most users can select customers unless restricted
            return HasPermission("invoices.select_customers") ||
                   HasPermission("customers.view") ||
                   !IsInRole("Cashier"); // Cashiers might be restricted
        }

        public bool CanDeleteDraftInvoices()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanDeleteDraftInvoices;
            }

            // Only admins can delete drafts by default
            return HasPermission("invoices.delete_drafts") || IsInRole("Admin");
        }

        public bool CanRejectDraftInvoices()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanRejectDraftInvoices;
            }

            // Admins and managers can reject drafts
            return HasPermission("invoices.reject_drafts") ||
                   CanCompleteInvoiceDrafts();
        }

        public bool CanManageInvoiceSettings()
        {
            if (CurrentUser == null) return false;

            // Check custom permissions first
            if (UserPermissions != null)
            {
                return UserPermissions.CanManageInvoiceSettings;
            }

            // Only admins can manage invoice settings
            return HasPermission("invoices.manage_settings") || IsInRole("Admin");
        }

        public async Task<bool> CanDeleteEntityAsync(string entityType, int entityId)
        {
            // Simplified implementation
            // Only admins can delete entities
            return HasPermission($"{entityType}.Delete");
        }

        // Interface implementation methods
        public UserPermissions GetUserPermissions(int userId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Searching for custom permissions for user ID: {userId}");

                using (var context = new POSSystem.Data.POSDbContext())
                {
                    var permissions = context.UserPermissions.FirstOrDefault(p => p.UserId == userId);

                    if (permissions != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Found custom permissions for user {userId}:");
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS]   - CanManageUsers: {permissions.CanManageUsers}");
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS]   - CanManageProducts: {permissions.CanManageProducts}");
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS]   - CanViewReports: {permissions.CanViewReports}");
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS]   - CanAccessSettings: {permissions.CanAccessSettings}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] No custom permissions found in database for user {userId}");

                        // Check if there are any permissions in the table at all
                        var totalPermissions = context.UserPermissions.Count();
                        System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Total permissions records in database: {totalPermissions}");
                    }

                    return permissions;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] ERROR getting user permissions for user {userId}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        public void UpdateUserPermissions(UserPermissions permissions)
        {
            try
            {
                using (var context = new POSSystem.Data.POSDbContext())
                {
                    context.UserPermissions.Update(permissions);
                    context.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                throw;
            }
        }

        public bool HasPermission(int userId, string permission)
        {
            var user = _dbService.GetUserById(userId);
            if (user != null)
            {
                Initialize(user);
                return HasPermission(permission);
            }
            return false;
        }

        /// <summary>
        /// Creates default permissions for a user based on their role
        /// </summary>
        public UserPermissions CreateDefaultPermissions(int userId, int roleId)
        {
            var isAdmin = roleId == 1; // Admin role
            var isManager = roleId == 2; // Manager role

            return new UserPermissions
            {
                UserId = userId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,

                // Sales permissions
                CanCreateSales = true,
                CanVoidSales = isAdmin || isManager,
                CanApplyDiscount = isAdmin || isManager,
                CanViewSalesHistory = true,

                // Product permissions
                CanManageProducts = isAdmin || isManager,
                CanManageCategories = isAdmin,
                CanViewInventory = true,
                CanAdjustInventory = isAdmin || isManager,

                // Financial permissions
                CanManageExpenses = isAdmin,
                CanManageCashDrawer = isAdmin || isManager,
                CanViewReports = isAdmin || isManager,
                CanManagePrices = isAdmin,

                // Customer & Supplier permissions
                CanManageCustomers = isAdmin || isManager,
                CanManageSuppliers = isAdmin,

                // Administrative permissions
                CanManageUsers = isAdmin,
                CanManageRoles = isAdmin,
                CanAccessSettings = isAdmin,
                CanViewLogs = isAdmin,

                // Invoice permissions (Two-Tier System)
                CanCreateFullInvoices = isAdmin, // Only admins can create full invoices by default
                CanCreateDraftInvoices = true, // All users can create draft invoices
                CanCompleteInvoiceDrafts = isAdmin || isManager, // Admins and managers can complete drafts
                CanViewPendingDrafts = isAdmin || isManager, // Admins and managers can view pending drafts
                CanModifyInvoicePricing = isAdmin, // Only admins can modify pricing
                CanSetPaymentTerms = isAdmin || isManager, // Admins and managers can set payment terms
                CanSelectCustomersForInvoices = true, // All users can select customers (configurable)
                CanDeleteDraftInvoices = isAdmin, // Only admins can delete drafts
                CanRejectDraftInvoices = isAdmin || isManager, // Admins and managers can reject drafts
                CanManageInvoiceSettings = isAdmin // Only admins can manage invoice settings
            };
        }

        /// <summary>
        /// Validates that a user has the required permission for an action
        /// </summary>
        public bool ValidatePermission(string action, string entityType = null)
        {
            if (CurrentUser == null)
            {
                System.Diagnostics.Debug.WriteLine($"Permission denied: No current user for action '{action}'");
                return false;
            }

            var permissionKey = string.IsNullOrEmpty(entityType) ? action : $"{entityType}.{action}";
            var hasPermission = HasPermission(permissionKey);

            if (!hasPermission)
            {
                System.Diagnostics.Debug.WriteLine($"Permission denied: User '{CurrentUser.Username}' lacks permission '{permissionKey}'");
            }

            return hasPermission;
        }

        /// <summary>
        /// Gets role-based permission for users without custom permissions
        /// </summary>
        private bool GetRoleBasedPermission(string permissionKey, string roleName)
        {
            if (string.IsNullOrEmpty(roleName))
            {
                System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] No role name provided for permission '{permissionKey}' - DENIED");
                return false;
            }

            System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Checking role-based permission '{permissionKey}' for role '{roleName}'");

            switch (roleName.ToLower())
            {
                case "admin":
                    System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Admin role - GRANTED for '{permissionKey}'");
                    return true; // Admin has all permissions

                case "manager":
                    var managerResult = permissionKey.ToLower() switch
                    {
                        "users.manage" or "manageusers" => false, // Managers can't manage users
                        "roles.manage" or "manageroles" => false, // Managers can't manage roles
                        "settings.access" or "accesssettings" => false, // Managers can't access settings
                        "logs.view" or "viewlogs" => false, // Managers can't view logs
                        _ => true // Managers have most other permissions
                    };
                    System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Manager role - {(managerResult ? "GRANTED" : "DENIED")} for '{permissionKey}'");
                    return managerResult;

                case "cashier":
                    var cashierResult = permissionKey.ToLower() switch
                    {
                        "sales.create" or "createsales" => true,
                        "sales.view" or "viewsales" => true,
                        "products.view" or "viewproducts" => true,
                        "customers.view" or "viewcustomers" => true,
                        "inventory.view" or "viewinventory" => true,
                        _ => false // Cashiers have limited permissions
                    };
                    System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Cashier role - {(cashierResult ? "GRANTED" : "DENIED")} for '{permissionKey}'");
                    return cashierResult;

                default:
                    System.Diagnostics.Debug.WriteLine($"[USERPERMISSIONS] Unknown role '{roleName}' - DENIED for '{permissionKey}'");
                    return false;
            }
        }
    }
}