using POSSystem.Models;
using POSSystem.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Commands;

namespace POSSystem.ViewModels
{
    public class UsersViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private ObservableCollection<User> _users;
        private ObservableCollection<Role> _roles;
        private ObservableCollection<Role> _filterRoles;
        private string _searchText;
        private User _selectedUser;
        private User _newUser;
        private bool _isEditMode;
        private PasswordBox _passwordBox;
        private Role _selectedRoleFilter;
        private int _totalUsers;
        private int _activeUsers;
        private decimal _totalSales;
        private ICommand _editUserCommand;
        private ICommand _deleteUserCommand;
        private ICommand _saveUserCommand;
        private ICommand _cancelEditCommand;

        public ObservableCollection<User> Users
        {
            get => _users;
            set
            {
                _users = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Role> Roles
        {
            get => _roles;
            set
            {
                _roles = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Role> FilterRoles
        {
            get => _filterRoles;
            set
            {
                _filterRoles = value;
                OnPropertyChanged();
            }
        }

        public User SelectedUser
        {
            get => _selectedUser;
            set
            {
                _selectedUser = value;
                OnPropertyChanged();
            }
        }

        public User NewUser
        {
            get => _newUser;
            set
            {
                _newUser = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged();
                
                try
                {
                    // Find the UsersView in the visual tree
                    var window = Application.Current?.MainWindow;
                    if (window == null) 
                    {
                        System.Diagnostics.Trace.WriteLine("Warning: MainWindow is null");
                        return;
                    }

                    var mainContent = window.FindName("mainContent") as ContentControl;
                    if (mainContent?.Content is UserControl usersView)
                    {
                        var button = usersView.FindName("btnSaveUser") as Button;
                        var titleText = usersView.FindName("titleText") as TextBlock;
                        
                        if (button != null)
                        {
                            button.Content = value ? 
                                Application.Current.FindResource("Update") : 
                                Application.Current.FindResource("AddUser");
                        }
                        else
                        {
                            System.Diagnostics.Trace.WriteLine("Warning: btnSaveUser not found in UsersView");
                        }
                        
                        if (titleText != null)
                        {
                            titleText.Text = value ? 
                                Application.Current.FindResource("ModifyUser").ToString() : 
                                Application.Current.FindResource("AddNewUser").ToString();
                        }
                        else
                        {
                            System.Diagnostics.Trace.WriteLine("Warning: titleText not found in UsersView");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Trace.WriteLine("Warning: Could not find UsersView in visual tree");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.WriteLine($"Warning: Could not update UI elements in IsEditMode: {ex.Message}");
                    System.Diagnostics.Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                }
            }
        }

        public Role SelectedRoleFilter
        {
            get => _selectedRoleFilter;
            set
            {
                _selectedRoleFilter = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public int TotalUsers
        {
            get => _totalUsers;
            set
            {
                _totalUsers = value;
                OnPropertyChanged();
            }
        }

        public int ActiveUsers
        {
            get => _activeUsers;
            set
            {
                _activeUsers = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set
            {
                _totalSales = value;
                OnPropertyChanged();
            }
        }

        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand => _editUserCommand ??= new RelayCommand<User>(EditUser);
        public ICommand DeleteUserCommand => _deleteUserCommand ??= new RelayCommand<User>(DeleteUser);
        public ICommand SaveUserCommand => _saveUserCommand ??= new RelayCommand(_ => SaveUser());
        public ICommand CancelEditCommand => _cancelEditCommand ??= new RelayCommand(_ => CancelEdit());

        public UsersViewModel()
        {
            _dbService = new DatabaseService();
            LoadData();
            InitializeNewUser();

            AddUserCommand = new RelayCommand(_ => StartAddUser());
        }

        public void SetPasswordBox(PasswordBox passwordBox)
        {
            _passwordBox = passwordBox;
        }

        public void LoadData()
        {
            try 
            {
                var users = _dbService.GetAllUsers();
                var roles = _dbService.GetAllRoles().Where(r => r.IsActive).ToList();
                
                if (roles == null || !roles.Any())
                {
                    MessageBox.Show("No active roles found in the system. Please configure roles first.", 
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                Users = new ObservableCollection<User>(users ?? new List<User>());
                Roles = new ObservableCollection<Role>(roles);
                
                // Create separate collection for filter with "All" option
                var allRole = new Role 
                { 
                    Id = 0, 
                    Name = "All", 
                    Description = "Show all roles",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                var filterRoles = new List<Role> { allRole };
                filterRoles.AddRange(roles);
                FilterRoles = new ObservableCollection<Role>(filterRoles);
                
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            TotalUsers = Users?.Count ?? 0;
            ActiveUsers = Users?.Count(u => u.IsActive) ?? 0;
            TotalSales = _dbService.GetTotalSalesByAllUsers();
        }

        private void ApplyFilters()
        {
            var filteredUsers = _dbService.GetAllUsers();

            if (!string.IsNullOrEmpty(SearchText))
            {
                string searchLower = SearchText.ToLower();
                filteredUsers = filteredUsers.Where(u => 
                    u.Username.ToLower().Contains(searchLower) ||
                    u.FirstName.ToLower().Contains(searchLower) ||
                    u.LastName.ToLower().Contains(searchLower) ||
                    u.Email.ToLower().Contains(searchLower)).ToList();
            }

            if (SelectedRoleFilter != null)
            {
                filteredUsers = filteredUsers.Where(u => u.RoleId == SelectedRoleFilter.Id).ToList();
            }

            Users = new ObservableCollection<User>(filteredUsers);
            UpdateStatistics();
        }

        private void StartAddUser()
        {
            IsEditMode = false;
            InitializeNewUser();
            if (_passwordBox != null)
            {
                _passwordBox.Password = string.Empty;
            }
        }

        private void EditUser(User user)
        {
            if (user == null) return;
            
            try
            {
                // Find the role object from our Roles collection
                var userRole = Roles?.FirstOrDefault(r => r.Id == user.RoleId);
                if (userRole == null)
                {
                    MessageBox.Show($"Could not find role with ID {user.RoleId}. Please check role configuration.", 
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                NewUser = new User
                {
                    Id = user.Id,
                    Username = user.Username ?? "",
                    FirstName = user.FirstName ?? "",
                    LastName = user.LastName ?? "",
                    Email = user.Email ?? "",
                    Phone = user.Phone ?? "",
                    RoleId = userRole.Id,
                    UserRole = userRole,
                    IsActive = user.IsActive,
                    PhotoPath = user.PhotoPath ?? "default-user.png",
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = DateTime.Now
                };
                
                IsEditMode = true;
                
                // Ensure we notify that the NewUser property has changed
                OnPropertyChanged(nameof(NewUser));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error editing user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteUser(User user)
        {
            if (user == null) return;
            
            // Get localized strings
            var confirmMessage = string.Format(
                FindResourceString("DialogConfirmDeleteUser") ?? "Are you sure you want to delete user {0}?", 
                user.Username);
            var confirmTitle = FindResourceString("DialogConfirmTitle") ?? "Confirm";
            var errorTitle = FindResourceString("DialogErrorTitle") ?? "Error";
            var successTitle = FindResourceString("DialogSuccessTitle") ?? "Success";
            var errorDeleteMessage = FindResourceString("DialogErrorUserDeletion") ?? "Error deleting user: {0}";
            var successMessage = FindResourceString("DialogSuccessUserDeleted") ?? "User deleted successfully!";
            
            var result = MessageBox.Show(
                confirmMessage,
                confirmTitle,
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
        
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _dbService.DeleteUser(user.Id);
                    LoadData();
                    
                    MessageBox.Show(
                        successMessage,
                        successTitle,
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        string.Format(errorDeleteMessage, ex.Message),
                        errorTitle,
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private void SaveUser()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting SaveUser method...");
                
                if (NewUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("ERROR: NewUser is null");
                    MessageBox.Show("Cannot save user: User object is not initialized.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Validating user data:");
                System.Diagnostics.Debug.WriteLine($"- Username: '{NewUser.Username}'");
                System.Diagnostics.Debug.WriteLine($"- FirstName: '{NewUser.FirstName}'");
                System.Diagnostics.Debug.WriteLine($"- LastName: '{NewUser.LastName}'");
                System.Diagnostics.Debug.WriteLine($"- RoleId: {NewUser.RoleId}");
                System.Diagnostics.Debug.WriteLine($"- Role: {NewUser.UserRole?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"- Password box is null: {_passwordBox == null}");
                if (_passwordBox != null)
                {
                    System.Diagnostics.Debug.WriteLine($"- Password length: {_passwordBox.Password?.Length ?? 0}");
                }

                // Validate required fields
                if (string.IsNullOrWhiteSpace(NewUser.Username))
                {
                    System.Diagnostics.Debug.WriteLine("Validation failed: Username is empty");
                    MessageBox.Show("Username is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Check username uniqueness
                var existingUser = _dbService.GetAllUsers()
                    .FirstOrDefault(u => u.Username.Equals(NewUser.Username, StringComparison.OrdinalIgnoreCase) 
                                       && u.Id != NewUser.Id);
                if (existingUser != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Validation failed: Username '{NewUser.Username}' already exists");
                    MessageBox.Show("Username already exists. Please choose a different username.", 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(NewUser.FirstName))
                {
                    System.Diagnostics.Debug.WriteLine("Validation failed: FirstName is empty");
                    MessageBox.Show("First Name is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(NewUser.LastName))
                {
                    System.Diagnostics.Debug.WriteLine("Validation failed: LastName is empty");
                    MessageBox.Show("Last Name is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (NewUser.RoleId == 0)
                {
                    System.Diagnostics.Debug.WriteLine("Validation failed: RoleId is 0");
                    MessageBox.Show("Role is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Handle password
                if (!IsEditMode && string.IsNullOrWhiteSpace(_passwordBox?.Password))
                {
                    System.Diagnostics.Debug.WriteLine("Validation failed: Password is empty for new user");
                    MessageBox.Show("Password is required for new users.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Set password if provided
                if (!string.IsNullOrWhiteSpace(_passwordBox?.Password))
                {
                    NewUser.Password = _passwordBox.Password;
                    System.Diagnostics.Debug.WriteLine("Password has been set from password box");
                }

                // Clear the photo path to use the default embedded resource
                if (string.IsNullOrWhiteSpace(NewUser.PhotoPath) || 
                    NewUser.PhotoPath.Contains("default-user.png"))
                {
                    System.Diagnostics.Debug.WriteLine("Setting default photo path");
                    NewUser.PhotoPath = null; // This will make it use the default embedded resource
                }

                System.Diagnostics.Debug.WriteLine($"About to {(IsEditMode ? "update" : "add")} user. PhotoPath: {NewUser.PhotoPath ?? "null"}");

                try
                {
                    if (IsEditMode)
                    {
                        System.Diagnostics.Debug.WriteLine("Updating existing user...");
                        _dbService.UpdateUser(NewUser);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Adding new user...");
                        _dbService.AddUser(NewUser);
                    }

                    System.Diagnostics.Debug.WriteLine("User saved successfully");
                    LoadData();
                    CancelEdit();
                    MessageBox.Show("User saved successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Database operation failed: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                    if (ex.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                        System.Diagnostics.Debug.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                    }
                    throw; // Re-throw to be caught by outer catch block
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SaveUser method failed: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    System.Diagnostics.Debug.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                MessageBox.Show($"Error saving user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            InitializeNewUser();
            if (_passwordBox != null)
            {
                _passwordBox.Password = string.Empty;
            }
        }

        private string GetResourcesPath()
        {
            try
            {
                var exePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var exeDir = System.IO.Path.GetDirectoryName(exePath);
                var resourcesDir = System.IO.Path.Combine(exeDir, "Resources", "Images");

                // Create the directory if it doesn't exist
                if (!System.IO.Directory.Exists(resourcesDir))
                {
                    System.IO.Directory.CreateDirectory(resourcesDir);
                }

                return resourcesDir;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting resources path: {ex.Message}");
                // If all else fails, use the current directory
                var fallbackPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Images");
                if (!System.IO.Directory.Exists(fallbackPath))
                {
                    System.IO.Directory.CreateDirectory(fallbackPath);
                }
                return fallbackPath;
            }
        }

        private void InitializeNewUser()
        {
            try
            {
                // Get the default role (Cashier)
                var defaultRole = Roles?.FirstOrDefault(r => r.Name == "Cashier") ?? Roles?.FirstOrDefault();
                if (defaultRole == null)
                {
                    MessageBox.Show("No roles available. Please configure roles first.", 
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                
                // Initialize with empty user
                NewUser = new User
                {
                    IsActive = true,
                    RoleId = defaultRole.Id,
                    UserRole = defaultRole,
                    PhotoPath = null, // This will use the default embedded resource
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    Username = "",
                    FirstName = "",
                    LastName = "",
                    Email = "",
                    Phone = ""
                };

                // Ensure we notify that the NewUser property has changed
                OnPropertyChanged(nameof(NewUser));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing new user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Helper method to find resource string
        private string FindResourceString(string key)
        {
            return Application.Current.TryFindResource(key) as string;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 