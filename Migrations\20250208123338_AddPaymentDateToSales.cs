﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentDateToSales : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "PaymentDate",
                table: "Sales",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Amount",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "Date",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "SellingPrice",
                table: "PurchaseOrderItems",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6625), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6596), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6625) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6630), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6629), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6631) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 324, DateTimeKind.Local).AddTicks(6327));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7402));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7406));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7409));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9161));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9164));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9172));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9174));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9178));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9180));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9183));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9195));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(9827), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(9833) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaymentDate",
                table: "Sales");

            migrationBuilder.DropColumn(
                name: "Amount",
                table: "SaleHistory");

            migrationBuilder.DropColumn(
                name: "Date",
                table: "SaleHistory");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "SaleHistory");

            migrationBuilder.DropColumn(
                name: "SellingPrice",
                table: "PurchaseOrderItems");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1886), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1850), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1887) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1897), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1894), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(1898) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 464, DateTimeKind.Local).AddTicks(6687));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3644));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3650));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(3655));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1439));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1445));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1458));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1464));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1470));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1475));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1480));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 16, 29, 29, 467, DateTimeKind.Local).AddTicks(1486));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(8236), new DateTime(2025, 2, 5, 16, 29, 29, 462, DateTimeKind.Local).AddTicks(8247) });
        }
    }
}
