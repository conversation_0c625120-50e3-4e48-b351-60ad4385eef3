using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;

namespace POSSystem.Services.Caching
{
    /// <summary>
    /// ✅ PERFORMANCE OPTIMIZATION: Centralized category caching service to eliminate N+1 queries
    /// </summary>
    public static class CategoryCacheService
    {
        private static readonly ConcurrentDictionary<int, Category> _categoryCache = new();
        private static readonly ConcurrentDictionary<int, string> _categoryNameCache = new();
        private static DateTime _lastCacheRefresh = DateTime.MinValue;
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(10);
        private static readonly object _refreshLock = new object();

        /// <summary>
        /// Get category name by ID with caching
        /// </summary>
        public static string GetCategoryName(int categoryId)
        {
            EnsureCacheIsValid();
            return _categoryNameCache.TryGetValue(categoryId, out var name) ? name : "Unknown Category";
        }

        /// <summary>
        /// Get category by ID with caching
        /// </summary>
        public static Category GetCategory(int categoryId)
        {
            EnsureCacheIsValid();
            return _categoryCache.TryGetValue(categoryId, out var category) ? category : null;
        }

        /// <summary>
        /// Get all categories from cache
        /// </summary>
        public static List<Category> GetAllCategories()
        {
            EnsureCacheIsValid();
            return _categoryCache.Values.OrderBy(c => c.Name).ToList();
        }

        /// <summary>
        /// Get category name dictionary for bulk operations
        /// </summary>
        public static Dictionary<int, string> GetCategoryNameDictionary()
        {
            EnsureCacheIsValid();
            return _categoryNameCache.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Force cache refresh
        /// </summary>
        public static void InvalidateCache()
        {
            lock (_refreshLock)
            {
                _lastCacheRefresh = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Refresh cache asynchronously
        /// </summary>
        public static async Task RefreshCacheAsync()
        {
            await Task.Run(() => RefreshCache());
        }

        private static void EnsureCacheIsValid()
        {
            if (DateTime.Now - _lastCacheRefresh > _cacheExpiry)
            {
                lock (_refreshLock)
                {
                    if (DateTime.Now - _lastCacheRefresh > _cacheExpiry)
                    {
                        RefreshCache();
                    }
                }
            }
        }

        private static void RefreshCache()
        {
            try
            {
                using var dbService = new DatabaseService();
                var categories = dbService.GetAllCategories();
                
                _categoryCache.Clear();
                _categoryNameCache.Clear();
                
                foreach (var category in categories)
                {
                    _categoryCache.TryAdd(category.Id, category);
                    _categoryNameCache.TryAdd(category.Id, category.Name);
                }
                
                _lastCacheRefresh = DateTime.Now;
                System.Diagnostics.Debug.WriteLine($"CategoryCacheService: Refreshed cache with {categories.Count} categories");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CategoryCacheService: Error refreshing cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Add or update a category in cache
        /// </summary>
        public static void UpdateCategory(Category category)
        {
            if (category != null)
            {
                _categoryCache.AddOrUpdate(category.Id, category, (key, oldValue) => category);
                _categoryNameCache.AddOrUpdate(category.Id, category.Name, (key, oldValue) => category.Name);
            }
        }

        /// <summary>
        /// Remove a category from cache
        /// </summary>
        public static void RemoveCategory(int categoryId)
        {
            _categoryCache.TryRemove(categoryId, out _);
            _categoryNameCache.TryRemove(categoryId, out _);
        }

        /// <summary>
        /// Get cache statistics for debugging
        /// </summary>
        public static (int Count, DateTime LastRefresh, bool IsExpired) GetCacheStats()
        {
            return (
                Count: _categoryCache.Count,
                LastRefresh: _lastCacheRefresh,
                IsExpired: DateTime.Now - _lastCacheRefresh > _cacheExpiry
            );
        }
    }
}
