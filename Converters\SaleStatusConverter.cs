using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class SaleStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                string resourceKey = status switch
                {
                    "Completed" => "PaymentStatusCompleted",
                    "Voided" => "PaymentStatusVoided",
                    "Refunded" => "PaymentStatusRefunded",
                    "Partially Refunded" => "PaymentStatusPartiallyRefunded",
                    "Pending" => "PaymentStatusPending",
                    _ => status
                };
                
                return Application.Current.TryFindResource(resourceKey) as string ?? status;
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 