using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels;
using POSSystem.Models;

namespace POSSystem.Services.UI
{
    /// <summary>
    /// High-performance dialog caching service to improve dialog opening performance
    /// by reusing frequently accessed dialogs instead of recreating them each time.
    /// </summary>
    public class DialogCacheService
    {
        private static readonly Lazy<DialogCacheService> _instance = new Lazy<DialogCacheService>(() => new DialogCacheService());
        public static DialogCacheService Instance => _instance.Value;

        // Thread-safe cache for dialog instances
        private readonly ConcurrentDictionary<string, WeakReference> _dialogCache = new ConcurrentDictionary<string, WeakReference>();
        
        // Cache for dialog ViewModels to avoid recreation
        private readonly ConcurrentDictionary<string, WeakReference> _viewModelCache = new ConcurrentDictionary<string, WeakReference>();

        // Maximum cache size to prevent memory leaks
        private const int MAX_CACHE_SIZE = 20;

        private DialogCacheService() { }

        /// <summary>
        /// Gets or creates a cached CustomProductDialog
        /// </summary>
        public CustomProductDialog GetOrCreateCustomProductDialog()
        {
            const string cacheKey = "CustomProductDialog";
            
            if (_dialogCache.TryGetValue(cacheKey, out var weakRef) && 
                weakRef.Target is CustomProductDialog existingDialog && 
                existingDialog != null)
            {
                // Reset the dialog state for reuse
                ResetCustomProductDialog(existingDialog);
                return existingDialog;
            }

            // Create new dialog and cache it
            var newDialog = new CustomProductDialog();
            _dialogCache[cacheKey] = new WeakReference(newDialog);
            
            CleanupCacheIfNeeded();
            return newDialog;
        }

        /// <summary>
        /// Gets or creates a cached ProductDetailsDialog
        /// </summary>
        public ProductDetailsDialog GetOrCreateProductDetailsDialog(Product product, SaleViewModel viewModel)
        {
            string cacheKey = $"ProductDetailsDialog_{product?.Id}";
            
            if (_dialogCache.TryGetValue(cacheKey, out var weakRef) && 
                weakRef.Target is ProductDetailsDialog existingDialog && 
                existingDialog != null)
            {
                // Update the dialog with new data
                UpdateProductDetailsDialog(existingDialog, product, viewModel);
                return existingDialog;
            }

            // Create new dialog and cache it
            var newDialog = new ProductDetailsDialog(product, viewModel);
            _dialogCache[cacheKey] = new WeakReference(newDialog);
            
            CleanupCacheIfNeeded();
            return newDialog;
        }

        /// <summary>
        /// Gets or creates a cached PaymentProcessingViewModel
        /// </summary>
        public PaymentProcessingViewModel GetOrCreatePaymentProcessingViewModel(SaleViewModel parentViewModel, string dialogIdentifier = "SalesDialog")
        {
            string cacheKey = $"PaymentProcessingViewModel_{parentViewModel?.GetHashCode()}";
            
            if (_viewModelCache.TryGetValue(cacheKey, out var weakRef) && 
                weakRef.Target is PaymentProcessingViewModel existingViewModel && 
                existingViewModel != null)
            {
                // Update the ViewModel with new data
                UpdatePaymentProcessingViewModel(existingViewModel, parentViewModel);
                return existingViewModel;
            }

            // Create new ViewModel and cache it
            var newViewModel = new PaymentProcessingViewModel(parentViewModel, dialogIdentifier);
            _viewModelCache[cacheKey] = new WeakReference(newViewModel);
            
            CleanupCacheIfNeeded();
            return newViewModel;
        }

        /// <summary>
        /// Preloads commonly used dialogs in background to improve first-time performance
        /// </summary>
        public void PreloadCommonDialogs()
        {
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    // Preload CustomProductDialog
                    var customDialog = new CustomProductDialog();
                    _dialogCache["CustomProductDialog"] = new WeakReference(customDialog);
                    
                    System.Diagnostics.Debug.WriteLine("[DIALOG_CACHE] Preloaded common dialogs");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[DIALOG_CACHE] Error preloading dialogs: {ex.Message}");
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        /// <summary>
        /// Clears the dialog cache to free memory
        /// </summary>
        public void ClearCache()
        {
            _dialogCache.Clear();
            _viewModelCache.Clear();
            System.Diagnostics.Debug.WriteLine("[DIALOG_CACHE] Cache cleared");
        }

        /// <summary>
        /// Gets cache statistics for performance monitoring
        /// </summary>
        public (int DialogCount, int ViewModelCount) GetCacheStats()
        {
            return (_dialogCache.Count, _viewModelCache.Count);
        }

        #region Private Helper Methods

        private void ResetCustomProductDialog(CustomProductDialog dialog)
        {
            try
            {
                // Reset form fields to default state using the dialog's built-in method
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // Use reflection to call the private ResetForm method
                    var resetMethod = typeof(CustomProductDialog).GetMethod("ResetForm",
                        BindingFlags.NonPublic | BindingFlags.Instance);
                    resetMethod?.Invoke(dialog, null);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG_CACHE] Error resetting CustomProductDialog: {ex.Message}");

                // Fallback: Reset text boxes directly
                try
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        if (dialog.FindName("txtProductName") is TextBox productNameBox)
                            productNameBox.Text = "Custom Item";
                        if (dialog.FindName("txtPrice") is TextBox priceBox)
                            priceBox.Clear();
                        if (dialog.FindName("txtQuantity") is TextBox quantityBox)
                            quantityBox.Text = "1";
                    });
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"[DIALOG_CACHE] Fallback reset also failed: {fallbackEx.Message}");
                }
            }
        }

        private void UpdateProductDetailsDialog(ProductDetailsDialog dialog, Product product, SaleViewModel viewModel)
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // Update the dialog's product and viewmodel references
                    var productProperty = typeof(ProductDetailsDialog).GetProperty("Product");
                    productProperty?.SetValue(dialog, product);
                    
                    // Refresh the dialog's data context
                    dialog.DataContext = dialog;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG_CACHE] Error updating ProductDetailsDialog: {ex.Message}");
            }
        }

        private void UpdatePaymentProcessingViewModel(PaymentProcessingViewModel viewModel, SaleViewModel parentViewModel)
        {
            try
            {
                // Update the ViewModel with fresh data from parent
                // This would require exposing update methods in PaymentProcessingViewModel
                System.Diagnostics.Debug.WriteLine("[DIALOG_CACHE] PaymentProcessingViewModel updated with fresh data");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DIALOG_CACHE] Error updating PaymentProcessingViewModel: {ex.Message}");
            }
        }

        private void CleanupCacheIfNeeded()
        {
            if (_dialogCache.Count > MAX_CACHE_SIZE)
            {
                // Remove dead references
                var keysToRemove = new List<string>();
                foreach (var kvp in _dialogCache)
                {
                    if (!kvp.Value.IsAlive)
                        keysToRemove.Add(kvp.Key);
                }

                foreach (var key in keysToRemove)
                {
                    _dialogCache.TryRemove(key, out _);
                }
            }

            if (_viewModelCache.Count > MAX_CACHE_SIZE)
            {
                // Remove dead references
                var keysToRemove = new List<string>();
                foreach (var kvp in _viewModelCache)
                {
                    if (!kvp.Value.IsAlive)
                        keysToRemove.Add(kvp.Key);
                }

                foreach (var key in keysToRemove)
                {
                    _viewModelCache.TryRemove(key, out _);
                }
            }
        }

        #endregion
    }
}
