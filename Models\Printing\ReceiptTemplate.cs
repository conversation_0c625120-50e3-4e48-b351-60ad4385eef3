using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models.Printing
{
    /// <summary>
    /// Represents a receipt template configuration
    /// </summary>
    public class ReceiptTemplate
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        public string Description { get; set; }
        
        /// <summary>
        /// Template type: Standard, Thermal, Custom
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TemplateType { get; set; }
        
        /// <summary>
        /// Paper width in characters (for thermal) or inches (for standard)
        /// </summary>
        public int PaperWidth { get; set; } = 48; // Default for thermal 80mm
        
        /// <summary>
        /// Font size for receipt text
        /// </summary>
        public int FontSize { get; set; } = 12;
        
        /// <summary>
        /// Whether to include company logo
        /// </summary>
        public bool IncludeLogo { get; set; } = true;
        
        /// <summary>
        /// Whether to include company information
        /// </summary>
        public bool IncludeCompanyInfo { get; set; } = true;
        
        /// <summary>
        /// Whether to include customer information
        /// </summary>
        public bool IncludeCustomerInfo { get; set; } = true;
        
        /// <summary>
        /// Whether to include item details
        /// </summary>
        public bool IncludeItemDetails { get; set; } = true;
        
        /// <summary>
        /// Whether to include payment information
        /// </summary>
        public bool IncludePaymentInfo { get; set; } = true;
        
        /// <summary>
        /// Whether to include barcode/QR code
        /// </summary>
        public bool IncludeBarcode { get; set; } = false;
        
        /// <summary>
        /// Custom footer text
        /// </summary>
        public string FooterText { get; set; }
        
        /// <summary>
        /// Whether this is the default template
        /// </summary>
        public bool IsDefault { get; set; } = false;
        
        /// <summary>
        /// Whether this template is active
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Last modified date
        /// </summary>
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// JSON configuration for advanced template settings
        /// </summary>
        public string AdvancedSettings { get; set; }
    }
    
    /// <summary>
    /// Printer configuration settings
    /// </summary>
    public class PrinterConfiguration
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        /// <summary>
        /// Printer type: Thermal, Standard, PDF
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PrinterType { get; set; }
        
        /// <summary>
        /// System printer name
        /// </summary>
        public string PrinterName { get; set; }
        
        /// <summary>
        /// Paper size: A4, Letter, Thermal80mm, Thermal58mm
        /// </summary>
        [StringLength(50)]
        public string PaperSize { get; set; } = "Thermal80mm";
        
        /// <summary>
        /// Print quality: Draft, Normal, High
        /// </summary>
        [StringLength(50)]
        public string PrintQuality { get; set; } = "Normal";
        
        /// <summary>
        /// Number of copies to print
        /// </summary>
        public int Copies { get; set; } = 1;
        
        /// <summary>
        /// Whether this is the default printer
        /// </summary>
        public bool IsDefault { get; set; } = false;
        
        /// <summary>
        /// Whether this printer is active
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Connection settings (JSON)
        /// </summary>
        public string ConnectionSettings { get; set; }
        
        /// <summary>
        /// Associated receipt template ID
        /// </summary>
        public int? ReceiptTemplateId { get; set; }
        
        /// <summary>
        /// Associated receipt template
        /// </summary>
        public virtual ReceiptTemplate ReceiptTemplate { get; set; }
    }
    
    /// <summary>
    /// Receipt printing settings
    /// </summary>
    public class ReceiptPrintSettings
    {
        public int Id { get; set; }
        
        /// <summary>
        /// Whether to automatically print receipts after sale completion
        /// </summary>
        public bool AutoPrintEnabled { get; set; } = true;
        
        /// <summary>
        /// Whether to show print dialog before printing
        /// </summary>
        public bool ShowPrintDialog { get; set; } = false;
        
        /// <summary>
        /// Whether to save receipts as PDF backup
        /// </summary>
        public bool SaveAsPdfBackup { get; set; } = false;
        
        /// <summary>
        /// PDF backup directory path
        /// </summary>
        public string PdfBackupPath { get; set; }
        
        /// <summary>
        /// Default printer configuration ID
        /// </summary>
        public int? DefaultPrinterConfigId { get; set; }
        
        /// <summary>
        /// Default receipt template ID
        /// </summary>
        public int? DefaultReceiptTemplateId { get; set; }
        
        /// <summary>
        /// Whether to enable print preview
        /// </summary>
        public bool EnablePrintPreview { get; set; } = false;
        
        /// <summary>
        /// Timeout for print operations in seconds
        /// </summary>
        public int PrintTimeoutSeconds { get; set; } = 30;
        
        /// <summary>
        /// Whether to retry failed print jobs
        /// </summary>
        public bool RetryFailedPrints { get; set; } = true;
        
        /// <summary>
        /// Number of retry attempts
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;
        
        /// <summary>
        /// Default printer configuration
        /// </summary>
        public virtual PrinterConfiguration DefaultPrinterConfig { get; set; }
        
        /// <summary>
        /// Default receipt template
        /// </summary>
        public virtual ReceiptTemplate DefaultReceiptTemplate { get; set; }
    }
    
    /// <summary>
    /// Receipt print job tracking
    /// </summary>
    public class ReceiptPrintJob
    {
        public int Id { get; set; }
        
        /// <summary>
        /// Associated sale ID
        /// </summary>
        public int SaleId { get; set; }
        
        /// <summary>
        /// Print job status: Pending, Printing, Completed, Failed, Cancelled
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Status { get; set; }
        
        /// <summary>
        /// Printer configuration used
        /// </summary>
        public int PrinterConfigId { get; set; }
        
        /// <summary>
        /// Receipt template used
        /// </summary>
        public int ReceiptTemplateId { get; set; }
        
        /// <summary>
        /// Print job creation time
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Print job start time
        /// </summary>
        public DateTime? StartedAt { get; set; }
        
        /// <summary>
        /// Print job completion time
        /// </summary>
        public DateTime? CompletedAt { get; set; }
        
        /// <summary>
        /// Error message if failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Number of retry attempts
        /// </summary>
        public int RetryCount { get; set; } = 0;
        
        /// <summary>
        /// User who initiated the print job
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// Associated sale
        /// </summary>
        public virtual Sale Sale { get; set; }
        
        /// <summary>
        /// Printer configuration used
        /// </summary>
        public virtual PrinterConfiguration PrinterConfig { get; set; }
        
        /// <summary>
        /// Receipt template used
        /// </summary>
        public virtual ReceiptTemplate ReceiptTemplate { get; set; }
        
        /// <summary>
        /// User who initiated the print job
        /// </summary>
        public virtual User User { get; set; }
    }
}
