using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using System.Diagnostics;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ PERFORMANCE CRITICAL: UI Thread Protection Helper
    /// Provides methods to ensure heavy operations don't block the UI thread
    /// </summary>
    public static class UIThreadProtection
    {
        private static readonly SemaphoreSlim _backgroundOperationSemaphore = new SemaphoreSlim(5, 5);

        /// <summary>
        /// ✅ CRITICAL: Execute operation on background thread with UI thread protection
        /// </summary>
        public static async Task<T> ExecuteOnBackgroundAsync<T>(Func<Task<T>> operation, 
            string operationName = "Background Operation", int timeoutMs = 10000)
        {
            if (Application.Current?.Dispatcher?.CheckAccess() == false)
            {
                // Already on background thread, execute directly
                return await operation();
            }

            await _backgroundOperationSemaphore.WaitAsync();
            try
            {
                var stopwatch = Stopwatch.StartNew();
                Debug.WriteLine($"[UI-PROTECTION] Starting {operationName} on background thread");

                using var cts = new CancellationTokenSource(timeoutMs);
                var result = await Task.Run(async () => await operation(), cts.Token);

                stopwatch.Stop();
                Debug.WriteLine($"[UI-PROTECTION] {operationName} completed in {stopwatch.ElapsedMilliseconds}ms");

                if (stopwatch.ElapsedMilliseconds > 2000)
                {
                    Debug.WriteLine($"⚠️ [UI-PROTECTION] Slow operation detected: {operationName} took {stopwatch.ElapsedMilliseconds}ms");
                }

                return result;
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine($"🚨 [UI-PROTECTION] {operationName} timed out after {timeoutMs}ms");
                throw new TimeoutException($"{operationName} timed out after {timeoutMs}ms");
            }
            finally
            {
                _backgroundOperationSemaphore.Release();
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Execute operation on background thread (void return)
        /// </summary>
        public static async Task ExecuteOnBackgroundAsync(Func<Task> operation, 
            string operationName = "Background Operation", int timeoutMs = 10000)
        {
            if (Application.Current?.Dispatcher?.CheckAccess() == false)
            {
                // Already on background thread, execute directly
                await operation();
                return;
            }

            await _backgroundOperationSemaphore.WaitAsync();
            try
            {
                var stopwatch = Stopwatch.StartNew();
                Debug.WriteLine($"[UI-PROTECTION] Starting {operationName} on background thread");

                using var cts = new CancellationTokenSource(timeoutMs);
                await Task.Run(async () => await operation(), cts.Token);

                stopwatch.Stop();
                Debug.WriteLine($"[UI-PROTECTION] {operationName} completed in {stopwatch.ElapsedMilliseconds}ms");

                if (stopwatch.ElapsedMilliseconds > 2000)
                {
                    Debug.WriteLine($"⚠️ [UI-PROTECTION] Slow operation detected: {operationName} took {stopwatch.ElapsedMilliseconds}ms");
                }
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine($"🚨 [UI-PROTECTION] {operationName} timed out after {timeoutMs}ms");
                throw new TimeoutException($"{operationName} timed out after {timeoutMs}ms");
            }
            finally
            {
                _backgroundOperationSemaphore.Release();
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Update UI safely from background thread
        /// </summary>
        public static async Task UpdateUIAsync(Action uiUpdate, DispatcherPriority priority = DispatcherPriority.Background)
        {
            if (Application.Current?.Dispatcher == null)
                return;

            if (Application.Current.Dispatcher.CheckAccess())
            {
                // Already on UI thread, execute directly
                uiUpdate();
            }
            else
            {
                // Marshal to UI thread
                await Application.Current.Dispatcher.InvokeAsync(uiUpdate, priority);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Update UI safely from background thread with return value
        /// </summary>
        public static async Task<T> UpdateUIAsync<T>(Func<T> uiUpdate, DispatcherPriority priority = DispatcherPriority.Background)
        {
            if (Application.Current?.Dispatcher == null)
                return default(T);

            if (Application.Current.Dispatcher.CheckAccess())
            {
                // Already on UI thread, execute directly
                return uiUpdate();
            }
            else
            {
                // Marshal to UI thread
                return await Application.Current.Dispatcher.InvokeAsync(uiUpdate, priority);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Execute database operation with UI thread protection and progress reporting
        /// </summary>
        public static async Task<T> ExecuteDatabaseOperationAsync<T>(Func<Task<T>> databaseOperation, 
            Action<string> progressCallback = null, string operationName = "Database Operation")
        {
            progressCallback?.Invoke($"Starting {operationName}...");

            try
            {
                var result = await ExecuteOnBackgroundAsync(databaseOperation, operationName);
                progressCallback?.Invoke($"{operationName} completed successfully");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke($"{operationName} failed: {ex.Message}");
                Debug.WriteLine($"[UI-PROTECTION] {operationName} error: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Batch update UI collections with performance optimization
        /// </summary>
        public static async Task BatchUpdateUIAsync(Action batchUpdate, string operationName = "Batch UI Update")
        {
            await UpdateUIAsync(() =>
            {
                var stopwatch = Stopwatch.StartNew();
                Debug.WriteLine($"[UI-PROTECTION] Starting {operationName}");

                batchUpdate();

                stopwatch.Stop();
                Debug.WriteLine($"[UI-PROTECTION] {operationName} completed in {stopwatch.ElapsedMilliseconds}ms");

                if (stopwatch.ElapsedMilliseconds > 500)
                {
                    Debug.WriteLine($"⚠️ [UI-PROTECTION] Slow UI update: {operationName} took {stopwatch.ElapsedMilliseconds}ms");
                }
            }, DispatcherPriority.Background);
        }

        /// <summary>
        /// ✅ CRITICAL: Force UI to yield control to prevent freezing
        /// </summary>
        public static async Task ForceUIYieldAsync()
        {
            if (Application.Current?.Dispatcher != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() => { }, DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Check if we're on the UI thread
        /// </summary>
        public static bool IsOnUIThread()
        {
            return Application.Current?.Dispatcher?.CheckAccess() == true;
        }

        /// <summary>
        /// ✅ CRITICAL: Get current thread information for debugging
        /// </summary>
        public static string GetThreadInfo()
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            var isUIThread = IsOnUIThread();
            return $"Thread {threadId} (UI: {isUIThread})";
        }
    }
}
