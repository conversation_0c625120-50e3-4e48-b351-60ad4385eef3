<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.SnackbarMessageDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d">
    
    <materialDesign:Card Padding="8" MinWidth="200">
        <StackPanel>
            <TextBlock Text="{Binding Message}"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     TextWrapping="Wrap"
                     Margin="0,0,0,8"/>
            
            <Button Content="OK"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    HorizontalAlignment="Right"/>
        </StackPanel>
    </materialDesign:Card>
    
</UserControl> 