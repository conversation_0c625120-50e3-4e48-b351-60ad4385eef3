-- Working Test Data Generator
-- This script handles trigger issues by temporarily dropping them

-- Store original triggers for later restoration (optional)
CREATE TEMPORARY TABLE saved_triggers AS
SELECT name, sql FROM sqlite_master 
WHERE type='trigger' AND name IN ('update_loyalty_points_after_sale', 'create_sale_history', 'update_inventory_after_sale');

-- Drop problematic triggers
DROP TRIGGER IF EXISTS update_loyalty_points_after_sale;
DROP TRIGGER IF EXISTS create_sale_history;
DROP TRIGGER IF EXISTS update_inventory_after_sale;

-- Disable foreign keys
PRAGMA foreign_keys = OFF;

BEGIN TRANSACTION;

-- Clean up any existing test data
DELETE FROM UserFavorites WHERE CreatedAt > datetime('now', '-1 day');
DELETE FROM LoyaltyTransactions WHERE TransactionDate > datetime('now', '-1 day');
DELETE FROM InventoryTransactions WHERE TransactionDate > datetime('now', '-1 day');
DELETE FROM SaleItems WHERE SaleId IN (SELECT Id FROM Sales WHERE SaleDate > datetime('now', '-1 day'));
DELETE FROM Discounts WHERE AppliedAt > datetime('now', '-1 day');
DELETE FROM Sales WHERE SaleDate > datetime('now', '-1 day');

-- Add a cash drawer
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
VALUES
  (500.00, 750.00, 700.00, 750.00, 50.00, 'Closed', datetime('now', '-8 hours'), 
   datetime('now'), 1, 1, 'Test day with $50 overage');

-- Create a few sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                  TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  -- Cash sale with no customer
  ('TEST-001', datetime('now', '-6 hours'), NULL, 1, 80.00, 0.00, 0.00, 80.00, 100.00, 20.00, 'Cash', 'Paid', 'Completed', 2),
  
  -- Card sale with customer
  ('TEST-002', datetime('now', '-4 hours'), 1, 1, 150.00, 15.00, 0.00, 135.00, 135.00, 0.00, 'Card', 'Paid', 'Completed', 3),
  
  -- Mobile payment
  ('TEST-003', datetime('now', '-2 hours'), 2, 1, 65.00, 0.00, 0.00, 65.00, 65.00, 0.00, 'Mobile', 'Paid', 'Completed', 1);

-- Add sale items
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES
  -- Items for TEST-001
  (last_insert_rowid() - 2, 1, 1, 50.00, 50.00),
  (last_insert_rowid() - 2, 5, 2, 15.00, 30.00),
  
  -- Items for TEST-002
  (last_insert_rowid() - 1, 2, 1, 100.00, 100.00),
  (last_insert_rowid() - 1, 9, 1, 50.00, 50.00),
  
  -- Items for TEST-003
  (last_insert_rowid(), 4, 1, 65.00, 65.00);

-- Update product stock manually (since we dropped the trigger)
UPDATE Products SET StockQuantity = StockQuantity - 1 WHERE Id = 1;
UPDATE Products SET StockQuantity = StockQuantity - 2 WHERE Id = 5;
UPDATE Products SET StockQuantity = StockQuantity - 1 WHERE Id = 2;
UPDATE Products SET StockQuantity = StockQuantity - 1 WHERE Id = 9;
UPDATE Products SET StockQuantity = StockQuantity - 1 WHERE Id = 4;

-- Add a discount
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
VALUES
  (1, 10, 150.00, 135.00, 1, 'Loyal customer discount', 
   (SELECT Id FROM Sales WHERE InvoiceNumber = 'TEST-002'), 
   NULL, 1, datetime('now', '-4 hours'), 1);

-- Add inventory transactions manually
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
VALUES
  -- For TEST-001
  (1, 'Sale', -1, 50.00, 'TEST-001', 'Sale transaction', datetime('now', '-6 hours'), 1),
  (5, 'Sale', -2, 15.00, 'TEST-001', 'Sale transaction', datetime('now', '-6 hours'), 1),
  
  -- For TEST-002
  (2, 'Sale', -1, 100.00, 'TEST-002', 'Sale transaction', datetime('now', '-4 hours'), 1),
  (9, 'Sale', -1, 50.00, 'TEST-002', 'Sale transaction', datetime('now', '-4 hours'), 1),
  
  -- For TEST-003
  (4, 'Sale', -1, 65.00, 'TEST-003', 'Sale transaction', datetime('now', '-2 hours'), 1);

-- Add loyalty transactions manually
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
VALUES
  -- For Customer 1 (TEST-002)
  (1, 135, 'Points earned from sale TEST-002', datetime('now', '-4 hours')),
  
  -- For Customer 2 (TEST-003)
  (2, 65, 'Points earned from sale TEST-003', datetime('now', '-2 hours'));

-- Update customer loyalty points
UPDATE Customers 
SET LoyaltyPoints = (
  SELECT COALESCE(SUM(Points), 0) 
  FROM LoyaltyTransactions 
  WHERE CustomerId = Customers.Id
)
WHERE Id IN (1, 2);

-- Add cash transactions
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
VALUES
  -- Opening balance
  ((SELECT MAX(Id) FROM CashDrawers), 'Opening Balance', 500.00, datetime('now', '-8 hours'), 
   'OPEN-TEST', 'Daily opening', 'Test opening balance', 1),
  
  -- Cash sale payment
  ((SELECT MAX(Id) FROM CashDrawers), 'Sale Payment', 100.00, datetime('now', '-6 hours'),
   'TEST-001', 'Cash sale', 'Test cash payment', 1),
   
  -- Closing balance
  ((SELECT MAX(Id) FROM CashDrawers), 'Closing Balance', -750.00, datetime('now'),
   'CLOSE-TEST', 'Daily closing', 'Test closing with $50 overage', 1);

-- Add a user favorite
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
VALUES
  (1, 2, datetime('now', '-1 hour'));

COMMIT;

PRAGMA foreign_keys = ON;

-- Optionally restore triggers with corrected SQL (commented out for safety)
/*
-- Only restore create_sale_history as it doesn't reference non-existent columns
CREATE TRIGGER create_sale_history
AFTER UPDATE OF Status ON Sales
FOR EACH ROW
WHEN NEW.Status != OLD.Status
BEGIN
    INSERT INTO SaleHistory (
        SaleId,
        PreviousStatus,
        NewStatus,
        UserId,
        Notes,
        CreatedAt
    )
    VALUES (
        NEW.Id,
        OLD.Status,
        NEW.Status,
        NEW.UserId,
        'Status changed from ' || OLD.Status || ' to ' || NEW.Status,
        datetime('now')
    );
END;
*/

-- Clean up temporary tables
DROP TABLE IF EXISTS saved_triggers; 