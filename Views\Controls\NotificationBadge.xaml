<UserControl x:Class="POSSystem.Views.Controls.NotificationBadge"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        
        <!-- Notification Badge Styles -->
        <Style x:Key="NotificationBadgeStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SecondaryHueMidBrush}"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="MinWidth" Value="20"/>
            <Setter Property="MinHeight" Value="20"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,-8,-8,0"/>
            <Setter Property="Panel.ZIndex" Value="1"/>
        </Style>

        <Style x:Key="NotificationTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="4,2"/>
        </Style>

        <Style x:Key="PulseAnimation" TargetType="Border">
            <Style.Triggers>
                <DataTrigger Binding="{Binding HasNewNotifications}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard RepeatBehavior="Forever">
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               From="1" To="0.3" Duration="0:0:1"
                                               AutoReverse="True"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.EnterActions>
                    <DataTrigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               To="1" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.ExitActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- Main Content (Icon or Button) -->
        <ContentPresenter Content="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=MainContent}"/>
        
        <!-- Notification Badge -->
        <Border Visibility="{Binding NotificationCount, Converter={StaticResource CountToVisibilityConverter}}">
            <Border.Style>
                <Style TargetType="Border" BasedOn="{StaticResource NotificationBadgeStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding HasNewNotifications}" Value="True">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="3">
                                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                       From="1" To="0.3" Duration="0:0:0.5"
                                                       AutoReverse="True"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            
            <TextBlock Style="{StaticResource NotificationTextStyle}">
                <TextBlock.Text>
                    <Binding Path="NotificationCount">
                        <Binding.Converter>
                            <converters:CountDisplayConverter/>
                        </Binding.Converter>
                    </Binding>
                </TextBlock.Text>
            </TextBlock>
        </Border>
    </Grid>
</UserControl>
