using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ PERFORMANCE MONITORING: Monitor UI performance and identify bottlenecks
    /// </summary>
    public static class UIPerformanceMonitor
    {
        private static readonly Dictionary<string, Stopwatch> _timers = new();
        private static readonly Dictionary<string, List<long>> _measurements = new();
        private static bool _isEnabled = true;

        /// <summary>
        /// Enable or disable performance monitoring
        /// </summary>
        public static bool IsEnabled
        {
            get => _isEnabled;
            set => _isEnabled = value;
        }

        /// <summary>
        /// Start timing an operation
        /// </summary>
        public static void StartTiming(string operationName)
        {
            if (!_isEnabled) return;

            if (!_timers.ContainsKey(operationName))
            {
                _timers[operationName] = new Stopwatch();
                _measurements[operationName] = new List<long>();
            }

            _timers[operationName].Restart();
        }

        /// <summary>
        /// Stop timing an operation and record the result
        /// </summary>
        public static long StopTiming(string operationName)
        {
            if (!_isEnabled || !_timers.ContainsKey(operationName))
                return 0;

            _timers[operationName].Stop();
            var elapsed = _timers[operationName].ElapsedMilliseconds;
            
            _measurements[operationName].Add(elapsed);
            
            // ✅ ENHANCED LOGGING: Log slow operations to both Debug and Trace (files)
            if (elapsed > 100) // More than 100ms
            {
                var message = $"⚠️ SLOW UI OPERATION: {operationName} took {elapsed}ms";
                Debug.WriteLine(message);
                System.Diagnostics.Trace.WriteLine(message); // Also write to file
            }
            else if (elapsed > 16) // More than one frame at 60fps
            {
                var message = $"⚡ UI OPERATION: {operationName} took {elapsed}ms";
                Debug.WriteLine(message);
                System.Diagnostics.Trace.WriteLine(message); // Also write to file
            }

            return elapsed;
        }

        /// <summary>
        /// Time an operation with automatic start/stop
        /// </summary>
        public static T TimeOperation<T>(string operationName, Func<T> operation)
        {
            if (!_isEnabled)
                return operation();

            StartTiming(operationName);
            try
            {
                return operation();
            }
            finally
            {
                StopTiming(operationName);
            }
        }

        /// <summary>
        /// Time an async operation
        /// </summary>
        public static async System.Threading.Tasks.Task<T> TimeOperationAsync<T>(string operationName, Func<System.Threading.Tasks.Task<T>> operation)
        {
            if (!_isEnabled)
                return await operation();

            StartTiming(operationName);
            try
            {
                return await operation();
            }
            finally
            {
                StopTiming(operationName);
            }
        }

        /// <summary>
        /// Get performance statistics for an operation
        /// </summary>
        public static (double avgMs, long minMs, long maxMs, int count) GetStats(string operationName)
        {
            if (!_measurements.ContainsKey(operationName) || _measurements[operationName].Count == 0)
                return (0, 0, 0, 0);

            var measurements = _measurements[operationName];
            var avg = measurements.Average();
            var min = measurements.Min();
            var max = measurements.Max();
            var count = measurements.Count;

            return (avg, min, max, count);
        }

        /// <summary>
        /// ✅ ENHANCED LOGGING: Log all performance statistics to both Debug and files
        /// </summary>
        public static void LogAllStats()
        {
            if (!_isEnabled) return;

            var header = "=== UI PERFORMANCE STATISTICS ===";
            var footer = "================================";

            Debug.WriteLine(header);
            System.Diagnostics.Trace.WriteLine(header); // Also write to file

            foreach (var operation in _measurements.Keys)
            {
                var (avg, min, max, count) = GetStats(operation);
                var statLine = $"{operation}: Avg={avg:F1}ms, Min={min}ms, Max={max}ms, Count={count}";
                Debug.WriteLine(statLine);
                System.Diagnostics.Trace.WriteLine(statLine); // Also write to file
            }

            Debug.WriteLine(footer);
            System.Diagnostics.Trace.WriteLine(footer); // Also write to file
        }

        /// <summary>
        /// Clear all performance data
        /// </summary>
        public static void ClearStats()
        {
            _measurements.Clear();
            _timers.Clear();
        }

        /// <summary>
        /// Monitor UI thread responsiveness with enhanced reporting
        /// </summary>
        public static void StartUIResponsivenessMonitoring()
        {
            // ✅ EMERGENCY FIX: Temporarily disable monitoring to prevent self-blocking
            Debug.WriteLine("🚨 UIPerformanceMonitor temporarily disabled to prevent self-blocking during sales view loading");
            return;

            if (!_isEnabled) return;

            // ✅ CRITICAL FIX: Reduce monitoring frequency to prevent performance monitor from causing blocking
            var timer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(250) // Reduced frequency to prevent monitor overhead
            };

            var lastCheck = DateTime.UtcNow;
            var blockingEvents = new List<(DateTime Time, double Duration)>();

            timer.Tick += (s, e) =>
            {
                var now = DateTime.UtcNow;
                var actualInterval = (now - lastCheck).TotalMilliseconds;
                lastCheck = now;

                // ✅ PERFORMANCE FIX: Adjust threshold for reduced monitoring frequency
                // If the actual interval is significantly longer than expected, the UI thread was blocked
                if (actualInterval > 375) // More than 50% longer than 250ms (adjusted for new interval)
                {
                    var blockDuration = actualInterval - 250; // Adjusted for new monitoring interval
                    blockingEvents.Add((now, blockDuration));

                    // Categorize blocking severity for POS system
                    string severity;
                    string impact;

                    if (blockDuration > 1000)
                    {
                        severity = "🔴 CRITICAL";
                        impact = "Transaction disruption likely";
                    }
                    else if (blockDuration > 500)
                    {
                        severity = "🟠 HIGH";
                        impact = "Noticeable user delay";
                    }
                    else if (blockDuration > 200)
                    {
                        severity = "🟡 MEDIUM";
                        impact = "Minor responsiveness issue";
                    }
                    else
                    {
                        severity = "🟢 LOW";
                        impact = "Acceptable for POS";
                    }

                    var message = $"{severity} UI THREAD BLOCKED for {blockDuration:F0}ms - {impact}";
                    Debug.WriteLine(message);
                    System.Diagnostics.Trace.WriteLine(message);

                    // ✅ EMERGENCY FIX: Activate emergency mode for critical blocks
                    EmergencyPerformanceFix.CheckForEmergencyActivation(blockDuration);

                    // ✅ CRITICAL FIX: Move expensive stack trace to background thread to prevent additional blocking
                    if (blockDuration > 1000)
                    {
                        // Don't create stack trace on UI thread - it's expensive and causes more blocking
                        _ = Task.Run(() =>
                        {
                            try
                            {
                                var stackTrace = new System.Diagnostics.StackTrace(true);
                                Debug.WriteLine($"Stack trace for critical block:\n{stackTrace}");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error creating stack trace: {ex.Message}");
                            }
                        });

                        // Force emergency measures for severe blocks
                        if (blockDuration > 5000)
                        {
                            Debug.WriteLine("🚨 SEVERE BLOCKING DETECTED - Activating emergency measures");
                            EmergencyPerformanceFix.ActivateEmergencyMode();
                        }
                    }

                    // Clean up old events (keep last 100)
                    if (blockingEvents.Count > 100)
                    {
                        blockingEvents.RemoveRange(0, blockingEvents.Count - 100);
                    }
                }
            };

            timer.Start();

            // Report summary every 5 minutes
            var summaryTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMinutes(5)
            };

            summaryTimer.Tick += (s, e) =>
            {
                if (blockingEvents.Count > 0)
                {
                    var recentEvents = blockingEvents.Where(evt => evt.Time > DateTime.UtcNow.AddMinutes(-5)).ToList();
                    if (recentEvents.Count > 0)
                    {
                        var avgDuration = recentEvents.Average(evt => evt.Duration);
                        var maxDuration = recentEvents.Max(evt => evt.Duration);
                        var criticalCount = recentEvents.Count(evt => evt.Duration > 1000);

                        Debug.WriteLine($"📊 UI PERFORMANCE SUMMARY (Last 5 min): {recentEvents.Count} blocks, " +
                                      $"Avg: {avgDuration:F0}ms, Max: {maxDuration:F0}ms, Critical: {criticalCount}");
                    }
                }
            };

            summaryTimer.Start();
        }
    }
}
