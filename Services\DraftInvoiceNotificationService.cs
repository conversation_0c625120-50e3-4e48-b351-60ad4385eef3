using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    /// <summary>
    /// Service for managing notifications in the two-tier draft invoice system
    /// </summary>
    public class DraftInvoiceNotificationService : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private readonly IAuthenticationService _authService;
        private readonly System.Timers.Timer _notificationTimer;
        private int _pendingDraftCount;
        private bool _isInitialized;

        public event PropertyChangedEventHandler PropertyChanged;
        public event EventHandler<DraftInvoiceNotificationEventArgs> NewDraftCreated;
        public event EventHandler<DraftInvoiceNotificationEventArgs> DraftCompleted;
        public event EventHandler<DraftInvoiceNotificationEventArgs> DraftExpired;
        public event EventHandler<DraftInvoiceNotificationEventArgs> DraftRejected;

        public DraftInvoiceNotificationService(DatabaseService dbService, IAuthenticationService authService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));

            // Check for pending drafts every 30 seconds
            _notificationTimer = new System.Timers.Timer(30000);
            _notificationTimer.Elapsed += OnNotificationTimerElapsed;
            _notificationTimer.AutoReset = true;
        }

        public int PendingDraftCount
        {
            get => _pendingDraftCount;
            private set
            {
                if (_pendingDraftCount != value)
                {
                    _pendingDraftCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasPendingDrafts));
                    OnPropertyChanged(nameof(PendingDraftsMessage));
                }
            }
        }

        public bool HasPendingDrafts => PendingDraftCount > 0;

        public string PendingDraftsMessage => PendingDraftCount switch
        {
            0 => "No pending draft invoices",
            1 => "1 draft invoice pending admin completion",
            _ => $"{PendingDraftCount} draft invoices pending admin completion"
        };

        /// <summary>
        /// Initializes the notification service and starts monitoring
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_isInitialized) return;

            try
            {
                await RefreshPendingCountAsync();
                _notificationTimer.Start();
                _isInitialized = true;
                
                System.Diagnostics.Debug.WriteLine("[DRAFT_NOTIFICATION_SERVICE] Initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error initializing: {ex.Message}");
            }
        }

        /// <summary>
        /// Stops the notification service
        /// </summary>
        public void Stop()
        {
            _notificationTimer?.Stop();
            _isInitialized = false;
        }

        /// <summary>
        /// Creates a notification when a new draft invoice is created
        /// </summary>
        public async Task NotifyDraftCreatedAsync(int invoiceId, int createdByUserId)
        {
            try
            {
                using var context = new POSDbContext();
                
                var invoice = await context.Invoice.FindAsync(invoiceId);
                if (invoice == null) return;

                var notification = DraftInvoiceNotification.CreateDraftCreatedNotification(
                    invoiceId, createdByUserId, invoice.InvoiceNumber);

                context.DraftInvoiceNotifications.Add(notification);
                await context.SaveChangesAsync();

                // Trigger real-time notification event
                NewDraftCreated?.Invoke(this, new DraftInvoiceNotificationEventArgs
                {
                    InvoiceId = invoiceId,
                    CreatedByUserId = createdByUserId,
                    NotificationType = "DRAFT_CREATED",
                    InvoiceNumber = invoice.InvoiceNumber
                });

                // Refresh pending count
                await RefreshPendingCountAsync();

                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Draft created notification sent for invoice {invoice.InvoiceNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error creating draft notification: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a notification when a draft invoice is completed
        /// </summary>
        public async Task NotifyDraftCompletedAsync(int invoiceId, int originalCreatorId, string completedByUserName)
        {
            try
            {
                using var context = new POSDbContext();
                
                var invoice = await context.Invoice.FindAsync(invoiceId);
                if (invoice == null) return;

                var notification = DraftInvoiceNotification.CreateDraftCompletedNotification(
                    invoiceId, originalCreatorId, invoice.InvoiceNumber, completedByUserName);

                context.DraftInvoiceNotifications.Add(notification);
                await context.SaveChangesAsync();

                // Trigger real-time notification event
                DraftCompleted?.Invoke(this, new DraftInvoiceNotificationEventArgs
                {
                    InvoiceId = invoiceId,
                    CreatedByUserId = originalCreatorId,
                    NotificationType = "DRAFT_COMPLETED",
                    InvoiceNumber = invoice.InvoiceNumber,
                    CompletedByUserName = completedByUserName
                });

                // Refresh pending count
                await RefreshPendingCountAsync();

                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Draft completed notification sent for invoice {invoice.InvoiceNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error creating completion notification: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a notification when a draft invoice is rejected
        /// </summary>
        public async Task NotifyDraftRejectedAsync(int invoiceId, int originalCreatorId, string reason)
        {
            try
            {
                using var context = new POSDbContext();
                
                var invoice = await context.Invoice.FindAsync(invoiceId);
                if (invoice == null) return;

                var notification = DraftInvoiceNotification.CreateDraftRejectedNotification(
                    invoiceId, originalCreatorId, invoice.InvoiceNumber, reason);

                context.DraftInvoiceNotifications.Add(notification);
                await context.SaveChangesAsync();

                // Trigger real-time notification event
                DraftRejected?.Invoke(this, new DraftInvoiceNotificationEventArgs
                {
                    InvoiceId = invoiceId,
                    CreatedByUserId = originalCreatorId,
                    NotificationType = "DRAFT_REJECTED",
                    InvoiceNumber = invoice.InvoiceNumber,
                    RejectionReason = reason
                });

                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Draft rejected notification sent for invoice {invoice.InvoiceNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error creating rejection notification: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets unread notifications for a specific user
        /// </summary>
        public async Task<List<DraftInvoiceNotification>> GetUnreadNotificationsAsync(int userId)
        {
            try
            {
                using var context = new POSDbContext();
                return await context.DraftInvoiceNotifications
                    .Include(n => n.Invoice)
                    .Include(n => n.CreatedByUser)
                    .Where(n => n.CreatedByUserId == userId && !n.IsRead)
                    .OrderByDescending(n => n.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error getting unread notifications: {ex.Message}");
                return new List<DraftInvoiceNotification>();
            }
        }

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        public async Task<bool> MarkNotificationAsReadAsync(int notificationId)
        {
            try
            {
                using var context = new POSDbContext();
                var notification = await context.DraftInvoiceNotifications.FindAsync(notificationId);
                
                if (notification != null)
                {
                    notification.MarkAsRead();
                    await context.SaveChangesAsync();
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error marking notification as read: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Marks all notifications as read for a user
        /// </summary>
        public async Task<bool> MarkAllNotificationsAsReadAsync(int userId)
        {
            try
            {
                using var context = new POSDbContext();
                var notifications = await context.DraftInvoiceNotifications
                    .Where(n => n.CreatedByUserId == userId && !n.IsRead)
                    .ToListAsync();

                foreach (var notification in notifications)
                {
                    notification.MarkAsRead();
                }

                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error marking all notifications as read: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the count of pending draft invoices
        /// </summary>
        public async Task<int> GetPendingDraftCountAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await context.Invoice
                    .CountAsync(i => i.RequiresAdminCompletion && i.Status == "Draft");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error getting pending draft count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Refreshes the pending draft count
        /// </summary>
        public async Task RefreshPendingCountAsync()
        {
            try
            {
                var currentUser = _authService.CurrentUser;
                if (currentUser != null && _authService.HasPermission("invoices.complete_drafts"))
                {
                    var pendingCount = await GetPendingDraftCountAsync();
                    
                    if (Application.Current != null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            PendingDraftCount = pendingCount;
                        });
                    }
                    else
                    {
                        PendingDraftCount = pendingCount;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_NOTIFICATION_SERVICE] Error refreshing pending count: {ex.Message}");
            }
        }

        private async void OnNotificationTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            await RefreshPendingCountAsync();
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _notificationTimer?.Stop();
            _notificationTimer?.Dispose();
        }
    }

    /// <summary>
    /// Event arguments for draft invoice notification events
    /// </summary>
    public class DraftInvoiceNotificationEventArgs : EventArgs
    {
        public int InvoiceId { get; set; }
        public int CreatedByUserId { get; set; }
        public string NotificationType { get; set; }
        public string InvoiceNumber { get; set; }
        public string CompletedByUserName { get; set; }
        public string RejectionReason { get; set; }
    }
}
