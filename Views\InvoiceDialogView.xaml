<UserControl
    x:Class="POSSystem.Views.InvoiceDialogView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:POSSystem.Views"
    xmlns:vm="clr-namespace:POSSystem.ViewModels"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:converters="clr-namespace:POSSystem.Converters"
    TextElement.Foreground="{DynamicResource MaterialDesignBody}"
    TextElement.FontWeight="Regular"
    TextElement.FontSize="13"
    TextOptions.TextFormattingMode="Ideal"
    TextOptions.TextRenderingMode="Auto"
    Background="{DynamicResource MaterialDesignPaper}"
    FontFamily="{DynamicResource MaterialDesignFont}"
    mc:Ignorable="d"
    d:DesignHeight="700" d:DesignWidth="1000">

    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryColor" Color="#2196F3"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#FFC107"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#F44336"/>
        <converters:ComboBoxValueConverter x:Key="ComboBoxValueConverter"/>
        <converters:EmptyCollectionToVisibilityConverter x:Key="EmptyCollectionToVisibilityConverter"/>
        <converters:IntegerToVisibilityConverter x:Key="IntegerToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:StockColorConverter x:Key="StockColorConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <converters:CurrencyFormatConverter x:Key="CurrencyFormatConverter"/>
        <converters:MultiBooleanToVisibilityConverter x:Key="MultiBooleanToVisibilityConverter"/>
        <converters:ItemEditModeConverter x:Key="ItemEditModeConverter"/>
    </UserControl.Resources>

    <materialDesign:Card MinWidth="900" Width="950" MaxWidth="1200" MinHeight="650" MaxHeight="800" Padding="0" UniformCornerRadius="8"
                       Background="{DynamicResource MaterialDesignBackground}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <!-- Header -->
            <materialDesign:ColorZone
                Grid.Row="0"
                Mode="PrimaryMid"
                Padding="12"
                materialDesign:ShadowAssist.ShadowDepth="Depth1">
                <DockPanel>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileEditOutline" Height="24" Width="24" VerticalAlignment="Center"/>
                        <TextBlock
                            Text="{Binding DialogTitle}"
                            FontSize="18"
                            FontWeight="Medium"
                            VerticalAlignment="Center"
                            Margin="10,0,0,0">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding DialogTitle}" Value="{x:Null}">
                                            <Setter Property="Text" Value="{DynamicResource CreateInvoice}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding DialogTitle}" Value="">
                                            <Setter Property="Text" Value="{DynamicResource CreateInvoice}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                    <!-- Right side with Invoice Number and Total -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock
                            Text="{Binding InvoiceNumber, StringFormat='#{0}'}"
                            FontSize="16"
                            FontWeight="Medium"
                            VerticalAlignment="Center"
                            Margin="0,0,24,0"/>

                        <!-- Prominent Total Display -->
                        <Border
                            Background="#4CAF50"
                            CornerRadius="6"
                            Padding="16,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Text="Total "
                                    FontWeight="SemiBold"
                                    FontSize="14"
                                    Foreground="White"
                                    VerticalAlignment="Center"/>
                                <TextBlock
                                    Text="{Binding GrandTotal, Converter={StaticResource CurrencyFormatConverter}}"
                                    FontWeight="Bold"
                                    FontSize="18"
                                    Foreground="White"
                                    VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Scrollable Content Area -->
            <ScrollViewer
                Grid.Row="2"
                VerticalScrollBarVisibility="Auto"
                HorizontalScrollBarVisibility="Disabled"
                MaxHeight="600"
                Padding="16,8">
                <StackPanel>
                    <!-- Invoice Details Section -->
                    <StackPanel Margin="16,8,16,16">
                        <!-- Section Header -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Kind="FileEditOutline"
                                    Width="18"
                                    Height="18"
                                    Foreground="{StaticResource PrimaryColor}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"/>
                                <TextBlock
                                    Text="{DynamicResource InvoiceDetails}"
                                    FontWeight="SemiBold"
                                    FontSize="16"
                                    Foreground="{StaticResource PrimaryColor}"
                                    VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Toggle advanced details -->
                            <ToggleButton
                                Grid.Column="2"
                                Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                IsChecked="{Binding ShowAllDetails}"
                                ToolTip="{DynamicResource MoreDetails}"/>
                        </Grid>

                        <!-- Main Invoice Fields - 4 columns horizontal layout -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Customer/Supplier Selection -->
                            <ComboBox
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="{DynamicResource SelectSupplier}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                ItemsSource="{Binding Suppliers}"
                                SelectedValue="{Binding SupplierId}"
                                SelectedValuePath="Id"
                                DisplayMemberPath="Name"
                                Visibility="{Binding IsInvoiceTypeSupplier, Converter={StaticResource BooleanToVisibilityConverter}}"
                                materialDesign:HintAssist.FloatingScale="0.75"/>

                            <ComboBox
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="{DynamicResource SelectCustomer}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                ItemsSource="{Binding Customers}"
                                SelectedValue="{Binding CustomerId}"
                                SelectedValuePath="Id"
                                DisplayMemberPath="Name"
                                Visibility="{Binding IsInvoiceTypeCustomer, Converter={StaticResource BooleanToVisibilityConverter}}"
                                materialDesign:HintAssist.FloatingScale="0.75"/>

                            <!-- Invoice Type -->
                            <ComboBox
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="{DynamicResource InvoiceType}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:TextFieldAssist.HasClearButton="False"
                                SelectedValue="{Binding InvoiceType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Tag"
                                IsEnabled="{Binding IsCreating}"
                                materialDesign:HintAssist.FloatingScale="0.75">
                                <ComboBoxItem Content="{DynamicResource Sales}" Tag="Sales"/>
                                <ComboBoxItem Content="{DynamicResource Purchase}" Tag="Purchase"/>
                            </ComboBox>

                            <!-- Status -->
                            <ComboBox
                                Grid.Column="4"
                                materialDesign:HintAssist.Hint="{DynamicResource Status}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:TextFieldAssist.HasClearButton="False"
                                SelectedValue="{Binding Status, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Tag"
                                materialDesign:HintAssist.FloatingScale="0.75">
                                <ComboBoxItem Content="{DynamicResource Draft}" Tag="Draft"/>
                                <ComboBoxItem Content="{DynamicResource Issued}" Tag="Issued"/>
                                <ComboBoxItem Content="{DynamicResource Paid}" Tag="Paid"/>
                                <ComboBoxItem Content="{DynamicResource Completed}" Tag="Completed"/>
                                <ComboBoxItem Content="{DynamicResource Overdue}" Tag="Overdue"/>
                                <ComboBoxItem Content="{DynamicResource Cancelled}" Tag="Cancelled"/>
                            </ComboBox>

                            <!-- Due Date -->
                            <DatePicker
                                Grid.Column="6"
                                materialDesign:HintAssist.Hint="{DynamicResource DueDate}"
                                Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                SelectedDate="{Binding DueDate}"
                                IsEnabled="{Binding ShowDueDate}"
                                Visibility="{Binding ShowDueDate, Converter={StaticResource BooleanToVisibilityConverter}}"
                                materialDesign:HintAssist.FloatingScale="0.75"/>
                        </Grid>

                        <!-- Advanced Details Section (collapsed by default) -->
                        <Grid Margin="0,16,0,0" Visibility="{Binding ShowAllDetails, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Reference Number -->
                            <TextBox
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="{DynamicResource ReferenceNumber}"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="{Binding Reference}"
                                materialDesign:HintAssist.FloatingScale="0.75"/>

                            <!-- Payment Terms -->
                            <ComboBox
                                Grid.Column="2"
                                materialDesign:HintAssist.Hint="{DynamicResource PaymentTerms}"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:TextFieldAssist.HasClearButton="False"
                                SelectedValue="{Binding PaymentTerms, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectedValuePath="Tag"
                                materialDesign:HintAssist.FloatingScale="0.75">
                                <ComboBoxItem Content="{DynamicResource DueOnReceipt}" Tag="Due on Receipt"/>
                                <ComboBoxItem Content="{DynamicResource Net15}" Tag="Net 15"/>
                                <ComboBoxItem Content="{DynamicResource Net30}" Tag="Net 30"/>
                                <ComboBoxItem Content="{DynamicResource Net60}" Tag="Net 60"/>
                            </ComboBox>

                            <!-- Notes -->
                            <TextBox
                                Grid.Column="4"
                                materialDesign:HintAssist.Hint="{DynamicResource InvoiceNotesHint}"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="{Binding Notes}"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="56"
                                MaxHeight="100"
                                materialDesign:HintAssist.FloatingScale="0.75"/>
                        </Grid>
                    </StackPanel>

                    <!-- Notes and Payments (side by side) - REMOVING THIS SECTION -->

                    <!-- Invoice Items Section -->
                    <StackPanel Margin="16,0,16,16">
                        <!-- Section Header -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Kind="FormatListBulleted"
                                    Width="18"
                                    Height="18"
                                    Foreground="{StaticResource PrimaryColor}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"/>
                                <TextBlock
                                    Text="{DynamicResource InvoiceItems}"
                                    FontWeight="SemiBold"
                                    FontSize="16"
                                    Foreground="{StaticResource PrimaryColor}"
                                    VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock
                                Grid.Column="2"
                                Text="{Binding InvoiceItems.Count, StringFormat='{}{0} items'}"
                                Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Product Search and Add Section -->
                        <Grid Margin="0,0,0,16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Search Row -->
                            <Grid Grid.Row="0" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Search Type Toggle -->
                                <ToggleButton
                                    Grid.Column="0"
                                    Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                    IsChecked="{Binding IsBarcodeSearchMode}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,12,0"
                                    ToolTip="{DynamicResource BarcodeToggleTooltip}"/>

                                <!-- Browse Products Button -->
                                <Button
                                    Grid.Column="1"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Command="{Binding OpenProductSelectionCommand}"
                                    ToolTip="{DynamicResource BrowseProducts}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,12,0"
                                    Height="48"
                                    Width="48"
                                    Padding="8">
                                    <materialDesign:PackIcon Kind="Magnify" Height="24" Width="24"/>
                                </Button>

                                <!-- Search TextBox/ComboBox -->
                                <TextBox
                                    Grid.Column="2"
                                    materialDesign:HintAssist.Hint="Scan or Enter Barcode"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Text="{Binding BarcodeSearch, UpdateSourceTrigger=PropertyChanged}"
                                    Visibility="{Binding IsBarcodeSearchMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    materialDesign:TextFieldAssist.HasClearButton="True"
                                    Height="48"
                                    FontSize="14"
                                    VerticalContentAlignment="Center">
                                    <TextBox.InputBindings>
                                        <KeyBinding Key="Return" Command="{Binding SearchByBarcodeCommand}"/>
                                    </TextBox.InputBindings>
                                </TextBox>

                                <ComboBox
                                    Grid.Column="2"
                                    materialDesign:HintAssist.Hint="{DynamicResource SelectProduct}"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    ItemsSource="{Binding AvailableProducts}"
                                    SelectedValue="{Binding SelectedProductId}"
                                    SelectedValuePath="Id"
                                    DisplayMemberPath="Name"
                                    Visibility="{Binding IsBarcodeSearchMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}"
                                    Height="48"
                                    FontSize="14"/>

                                <!-- Search/Browse Button -->
                                <Button
                                    Grid.Column="3"
                                    Command="{Binding SearchByBarcodeCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="{StaticResource PrimaryColor}"
                                    ToolTip="{DynamicResource SearchByBarcode}"
                                    Visibility="{Binding IsBarcodeSearchMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    Margin="12,0,0,0"
                                    Height="48"
                                    Width="48"
                                    Padding="8">
                                    <materialDesign:PackIcon Kind="Barcode" Height="24" Width="24"/>
                                </Button>
                            </Grid>

                            <!-- Enhanced Product Details Display (when product is selected) -->
                            <materialDesign:Card
                                Grid.Row="1"
                                Margin="0,0,0,16"
                                Padding="0"
                                UniformCornerRadius="12"
                                materialDesign:ShadowAssist.ShadowDepth="Depth2"
                                Background="{DynamicResource MaterialDesignCardBackground}"
                                Visibility="{Binding IsProductSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Border Padding="20,16">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Product Image -->
                                        <Border
                                            Grid.Column="0"
                                            Width="80"
                                            Height="80"
                                            CornerRadius="12"
                                            Background="#F8F9FA"
                                            BorderBrush="#E9ECEF"
                                            BorderThickness="2"
                                            VerticalAlignment="Center">
                                            <Grid>
                                                <!-- Product Image (if available) -->
                                                <Image
                                                    Source="{Binding SelectedProduct.ImageData, Converter={StaticResource Base64ToImageConverter}}"
                                                    Stretch="UniformToFill"
                                                    VerticalAlignment="Center"
                                                    HorizontalAlignment="Center">
                                                    <Image.Style>
                                                        <Style TargetType="Image">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding SelectedProduct.ImageData}" Value="{x:Null}">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding SelectedProduct.ImageData}" Value="">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Image.Style>
                                                </Image>

                                                <!-- Placeholder Icon (when no image) -->
                                                <materialDesign:PackIcon
                                                    Kind="Package"
                                                    Width="40"
                                                    Height="40"
                                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                    VerticalAlignment="Center"
                                                    HorizontalAlignment="Center">
                                                    <materialDesign:PackIcon.Style>
                                                        <Style TargetType="materialDesign:PackIcon">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding SelectedProduct.ImageData}" Value="{x:Null}">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding SelectedProduct.ImageData}" Value="">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </materialDesign:PackIcon.Style>
                                                </materialDesign:PackIcon>
                                            </Grid>
                                        </Border>

                                        <!-- Product Information -->
                                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                            <!-- Product Name -->
                                            <TextBlock
                                                Text="{Binding SelectedProduct.Name}"
                                                FontWeight="SemiBold"
                                                FontSize="18"
                                                Foreground="{DynamicResource MaterialDesignBody}"
                                                Margin="0,0,0,8"
                                                TextWrapping="Wrap"/>

                                            <!-- Product Details Row -->
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Current Stock Badge -->
                                                <Border
                                                    Grid.Column="0"
                                                    Background="#E8F5E8"
                                                    CornerRadius="16"
                                                    Padding="12,6"
                                                    Margin="0,0,12,0">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon
                                                            Kind="Package"
                                                            Width="16"
                                                            Height="16"
                                                            Foreground="#2E7D32"
                                                            VerticalAlignment="Center"
                                                            Margin="0,0,6,0"/>
                                                        <TextBlock
                                                            Text="Stock:"
                                                            FontSize="12"
                                                            Foreground="#2E7D32"
                                                            FontWeight="Medium"
                                                            VerticalAlignment="Center"
                                                            Margin="0,0,4,0"/>
                                                        <TextBlock
                                                            Text="{Binding SelectedProduct.StockQuantity}"
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Foreground="#1B5E20"
                                                            VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Border>

                                                <!-- SKU Badge -->
                                                <Border
                                                    Grid.Column="1"
                                                    Background="#E3F2FD"
                                                    CornerRadius="16"
                                                    Padding="12,6"
                                                    Margin="0,0,12,0">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon
                                                            Kind="Tag"
                                                            Width="16"
                                                            Height="16"
                                                            Foreground="#1976D2"
                                                            VerticalAlignment="Center"
                                                            Margin="0,0,6,0"/>
                                                        <TextBlock
                                                            Text="SKU:"
                                                            FontSize="12"
                                                            Foreground="#1976D2"
                                                            FontWeight="Medium"
                                                            VerticalAlignment="Center"
                                                            Margin="0,0,4,0"/>
                                                        <TextBlock
                                                            Text="{Binding SelectedProduct.SKU}"
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Foreground="#0D47A1"
                                                            VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Border>
                                            </Grid>
                                        </StackPanel>

                                        <!-- Barcode Section -->
                                        <StackPanel Grid.Column="3" VerticalAlignment="Center" HorizontalAlignment="Right">
                                            <Border
                                                Background="#FFF3E0"
                                                CornerRadius="8"
                                                Padding="12,8">
                                                <StackPanel>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,4">
                                                        <materialDesign:PackIcon
                                                            Kind="Barcode"
                                                            Width="16"
                                                            Height="16"
                                                            Foreground="#F57C00"
                                                            VerticalAlignment="Center"
                                                            Margin="0,0,4,0"/>
                                                        <TextBlock
                                                            Text="Barcode"
                                                            FontSize="11"
                                                            Foreground="#F57C00"
                                                            FontWeight="Medium"
                                                            VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                    <TextBlock
                                                        Text="{Binding SelectedProduct.Barcode}"
                                                        FontSize="13"
                                                        FontWeight="Bold"
                                                        Foreground="#E65100"
                                                        HorizontalAlignment="Center"
                                                        FontFamily="Consolas"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </materialDesign:Card>

                            <!-- Enhanced Input Section -->
                            <materialDesign:Card
                                Grid.Row="2"
                                Padding="0"
                                UniformCornerRadius="12"
                                materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                Background="{DynamicResource MaterialDesignCardBackground}">
                                <Border Padding="20,16">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- Section Header -->
                                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,0">
                                            <materialDesign:PackIcon
                                                Kind="Edit"
                                                Width="20"
                                                Height="20"
                                                Foreground="{StaticResource PrimaryColor}"
                                                VerticalAlignment="Center"
                                                Margin="0,0,8,0"/>
                                            <TextBlock
                                                Text="Item Details"
                                                FontWeight="SemiBold"
                                                FontSize="16"
                                                Foreground="{StaticResource PrimaryColor}"
                                                VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Input Fields and Action Section -->
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="16"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Input Fields Grid -->
                                            <Grid Grid.Column="0">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="12"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- First Row: Quantity and Prices -->
                                                <Grid Grid.Row="0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="140"/>
                                                        <ColumnDefinition Width="16"/>
                                                        <ColumnDefinition Width="140"/>
                                                        <ColumnDefinition Width="16"/>
                                                        <ColumnDefinition Width="140"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Quantity with Icon -->
                                                    <Border Grid.Column="0" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Border Grid.Column="0" Background="#F8F9FA" Width="40" CornerRadius="8,0,0,8">
                                                                <materialDesign:PackIcon
                                                                    Kind="Counter"
                                                                    Width="18"
                                                                    Height="18"
                                                                    Foreground="{StaticResource PrimaryColor}"
                                                                    VerticalAlignment="Center"
                                                                    HorizontalAlignment="Center"/>
                                                            </Border>
                                                            <TextBox
                                                                x:Name="txtQuantity"
                                                                Grid.Column="1"
                                                                materialDesign:HintAssist.Hint="{DynamicResource Quantity}"
                                                                Style="{StaticResource MaterialDesignTextBox}"
                                                                Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                                                Height="48"
                                                                FontSize="14"
                                                                FontWeight="SemiBold"
                                                                VerticalContentAlignment="Center"
                                                                BorderThickness="0"
                                                                Padding="12,0"
                                                                GotFocus="TxtQuantity_GotFocus"/>
                                                        </Grid>
                                                    </Border>

                                                    <!-- Unit Price with Icon -->
                                                    <Border Grid.Column="2" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Border Grid.Column="0" Background="#FFF3E0" Width="40" CornerRadius="8,0,0,8">
                                                                <materialDesign:PackIcon
                                                                    Kind="CurrencyUsd"
                                                                    Width="18"
                                                                    Height="18"
                                                                    Foreground="#FF9800"
                                                                    VerticalAlignment="Center"
                                                                    HorizontalAlignment="Center"/>
                                                            </Border>
                                                            <TextBox
                                                                Grid.Column="1"
                                                                materialDesign:HintAssist.Hint="{DynamicResource UnitPrice}"
                                                                Style="{StaticResource MaterialDesignTextBox}"
                                                                Text="{Binding UnitPrice, StringFormat=N2}"
                                                                Height="48"
                                                                FontSize="14"
                                                                FontWeight="SemiBold"
                                                                VerticalContentAlignment="Center"
                                                                BorderThickness="0"
                                                                Padding="12,0"/>
                                                        </Grid>
                                                    </Border>

                                                    <!-- Selling Price with Icon -->
                                                    <Border Grid.Column="4" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Border Grid.Column="0" Background="#E8F5E8" Width="40" CornerRadius="8,0,0,8">
                                                                <materialDesign:PackIcon
                                                                    Kind="CashMultiple"
                                                                    Width="18"
                                                                    Height="18"
                                                                    Foreground="#4CAF50"
                                                                    VerticalAlignment="Center"
                                                                    HorizontalAlignment="Center"/>
                                                            </Border>
                                                            <TextBox
                                                                Grid.Column="1"
                                                                materialDesign:HintAssist.Hint="{DynamicResource SellingPrice}"
                                                                Style="{StaticResource MaterialDesignTextBox}"
                                                                Text="{Binding SellingPrice, StringFormat=N2}"
                                                                Height="48"
                                                                FontSize="14"
                                                                FontWeight="SemiBold"
                                                                VerticalContentAlignment="Center"
                                                                BorderThickness="0"
                                                                Padding="12,0"
                                                                ToolTip="{DynamicResource SellingPriceTooltip}"/>
                                                        </Grid>
                                                    </Border>
                                                </Grid>

                                                <!-- Second Row: Expiry Date -->
                                                <Grid Grid.Row="2">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="200"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Expiry Date with Icon -->
                                                    <Border Grid.Column="0" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Border Grid.Column="0" Background="#FFEBEE" Width="40" CornerRadius="8,0,0,8">
                                                                <materialDesign:PackIcon
                                                                    Kind="Calendar"
                                                                    Width="18"
                                                                    Height="18"
                                                                    Foreground="#F44336"
                                                                    VerticalAlignment="Center"
                                                                    HorizontalAlignment="Center"/>
                                                            </Border>
                                                            <DatePicker
                                                                Grid.Column="1"
                                                                materialDesign:HintAssist.Hint="{DynamicResource ExpiryDate}"
                                                                Style="{StaticResource MaterialDesignDatePicker}"
                                                                SelectedDate="{Binding ProductExpiryDate, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                Height="48"
                                                                FontSize="14"
                                                                BorderThickness="0"
                                                                Padding="12,0">
                                                                <DatePicker.ToolTip>
                                                                    <TextBlock Text="{DynamicResource ExpiryDateTooltip}" TextWrapping="Wrap" MaxWidth="250"/>
                                                                </DatePicker.ToolTip>
                                                            </DatePicker>
                                                        </Grid>
                                                    </Border>
                                                </Grid>
                                            </Grid>

                                            <!-- Right Side: Stock Info and Add Button -->
                                            <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                <!-- Remaining Stock Display -->
                                                <materialDesign:Card
                                                    Padding="16,12"
                                                    UniformCornerRadius="8"
                                                    Background="#E8F5E8"
                                                    Margin="0,0,0,12"
                                                    Visibility="{Binding IsProductSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                    <StackPanel HorizontalAlignment="Center">
                                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,4">
                                                            <materialDesign:PackIcon
                                                                Kind="Package"
                                                                Width="16"
                                                                Height="16"
                                                                Foreground="#2E7D32"
                                                                VerticalAlignment="Center"
                                                                Margin="0,0,4,0"/>
                                                            <TextBlock
                                                                Text="Remaining"
                                                                FontSize="12"
                                                                Foreground="#2E7D32"
                                                                FontWeight="Medium"
                                                                VerticalAlignment="Center"/>
                                                        </StackPanel>
                                                        <TextBlock
                                                            Text="{Binding RemainingStock}"
                                                            FontSize="20"
                                                            FontWeight="Bold"
                                                            Foreground="#1B5E20"
                                                            HorizontalAlignment="Center"/>
                                                    </StackPanel>
                                                </materialDesign:Card>

                                                <!-- Add Item Button -->
                                                <Button
                                                    Command="{Binding AddItemCommand}"
                                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                                    Background="#4CAF50"
                                                    Foreground="White"
                                                    materialDesign:ButtonAssist.CornerRadius="8"
                                                    Width="120"
                                                    Height="56"
                                                    FontSize="14"
                                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="Plus" Width="20" Height="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                                        <TextBlock Text="{DynamicResource Add}" VerticalAlignment="Center" FontWeight="SemiBold"/>
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Grid>
                                </Border>
                            </materialDesign:Card>
                        </Grid>
                    </StackPanel>
                        <!-- Validation Message -->
                        <TextBlock
                            Text="{Binding ValidationMessage}"
                            Foreground="{StaticResource ErrorColor}"
                            FontWeight="Medium"
                            Margin="16,8,16,0"
                            TextWrapping="Wrap"
                            Visibility="{Binding HasValidationMessage, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <!-- Invoice Items List -->
                        <ScrollViewer
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Disabled"
                            MinHeight="120"
                            MaxHeight="400"
                            Margin="16,8,16,16">
                            <ItemsControl ItemsSource="{Binding InvoiceItems}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <!-- Enhanced Card Design with Shadow and Better Spacing -->
                                        <materialDesign:Card
                                            Margin="0,0,0,12"
                                            Padding="0"
                                            UniformCornerRadius="8"
                                            materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                            Background="{DynamicResource MaterialDesignCardBackground}">
                                            <Border Padding="20,16">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="16"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Product Image -->
                                                    <Border
                                                        Grid.Column="0"
                                                        Width="64"
                                                        Height="64"
                                                        CornerRadius="8"
                                                        Background="#F5F5F5"
                                                        BorderBrush="#E0E0E0"
                                                        BorderThickness="1"
                                                        VerticalAlignment="Top">
                                                        <Grid>
                                                            <!-- Product Image (if available) -->
                                                            <Image
                                                                Source="{Binding Product.ImageData, Converter={StaticResource Base64ToImageConverter}}"
                                                                Stretch="UniformToFill"
                                                                VerticalAlignment="Center"
                                                                HorizontalAlignment="Center">
                                                                <Image.Style>
                                                                    <Style TargetType="Image">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding Product.ImageData}" Value="{x:Null}">
                                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                            </DataTrigger>
                                                                            <DataTrigger Binding="{Binding Product.ImageData}" Value="">
                                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                            </DataTrigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </Image.Style>
                                                            </Image>

                                                            <!-- Placeholder Icon (when no image) -->
                                                            <materialDesign:PackIcon
                                                                Kind="Package"
                                                                Width="32"
                                                                Height="32"
                                                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                VerticalAlignment="Center"
                                                                HorizontalAlignment="Center">
                                                                <materialDesign:PackIcon.Style>
                                                                    <Style TargetType="materialDesign:PackIcon">
                                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding Product.ImageData}" Value="{x:Null}">
                                                                                <Setter Property="Visibility" Value="Visible"/>
                                                                            </DataTrigger>
                                                                            <DataTrigger Binding="{Binding Product.ImageData}" Value="">
                                                                                <Setter Property="Visibility" Value="Visible"/>
                                                                            </DataTrigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </materialDesign:PackIcon.Style>
                                                            </materialDesign:PackIcon>
                                                        </Grid>
                                                    </Border>

                                                    <!-- Product Details -->
                                                    <StackPanel Grid.Column="2" VerticalAlignment="Top">
                                                        <!-- Product Name -->
                                                        <TextBlock
                                                            Text="{Binding Product.Name}"
                                                            FontWeight="SemiBold"
                                                            FontSize="16"
                                                            Foreground="{DynamicResource MaterialDesignBody}"
                                                            Margin="0,0,0,8"
                                                            TextWrapping="Wrap"/>

                                                        <!-- Product Details Row -->
                                                        <Grid Margin="0,0,0,8">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <!-- Quantity Badge -->
                                                            <Border
                                                                Grid.Column="0"
                                                                Background="{StaticResource PrimaryColor}"
                                                                CornerRadius="12"
                                                                Padding="12,4"
                                                                Margin="0,0,12,0">
                                                                <StackPanel Orientation="Horizontal">
                                                                    <materialDesign:PackIcon
                                                                        Kind="Counter"
                                                                        Width="14"
                                                                        Height="14"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"
                                                                        Margin="0,0,4,0"/>
                                                                    <TextBlock
                                                                        Text="{Binding Quantity, StringFormat='{}{0:0.##}'}"
                                                                        FontSize="12"
                                                                        FontWeight="SemiBold"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                                </StackPanel>
                                                            </Border>

                                                            <!-- Unit Price Badge -->
                                                            <Border
                                                                Grid.Column="1"
                                                                Background="#FF9800"
                                                                CornerRadius="12"
                                                                Padding="12,4"
                                                                Margin="0,0,12,0">
                                                                <StackPanel Orientation="Horizontal">
                                                                    <materialDesign:PackIcon
                                                                        Kind="CurrencyUsd"
                                                                        Width="14"
                                                                        Height="14"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"
                                                                        Margin="0,0,4,0"/>
                                                                    <TextBlock
                                                                        Text="{Binding UnitPrice, Converter={StaticResource CurrencyFormatConverter}}"
                                                                        FontSize="12"
                                                                        FontWeight="SemiBold"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                                </StackPanel>
                                                            </Border>

                                                            <!-- Total Badge -->
                                                            <Border
                                                                Grid.Column="2"
                                                                Background="#4CAF50"
                                                                CornerRadius="12"
                                                                Padding="12,4">
                                                                <StackPanel Orientation="Horizontal">
                                                                    <materialDesign:PackIcon
                                                                        Kind="Calculator"
                                                                        Width="14"
                                                                        Height="14"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"
                                                                        Margin="0,0,4,0"/>
                                                                    <TextBlock
                                                                        Text="{Binding Total, Converter={StaticResource CurrencyFormatConverter}}"
                                                                        FontSize="12"
                                                                        FontWeight="Bold"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                                </StackPanel>
                                                            </Border>
                                                        </Grid>

                                                        <!-- Additional Product Info -->
                                                        <StackPanel Orientation="Horizontal" Opacity="0.7">
                                                            <TextBlock
                                                                Text="SKU: "
                                                                FontSize="12"
                                                                Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                            <TextBlock
                                                                Text="{Binding Product.SKU}"
                                                                FontSize="12"
                                                                FontWeight="Medium"
                                                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                Margin="0,0,16,0"/>
                                                            <TextBlock
                                                                Text="Barcode: "
                                                                FontSize="12"
                                                                Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                            <TextBlock
                                                                Text="{Binding Product.Barcode}"
                                                                FontSize="12"
                                                                FontWeight="Medium"
                                                                Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                        </StackPanel>
                                                    </StackPanel>

                                                    <!-- Edit Button -->
                                                    <Button
                                                        Grid.Column="3"
                                                        Command="{Binding DataContext.EditItemCommand, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type UserControl}}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="40"
                                                        Height="40"
                                                        ToolTip="{DynamicResource Edit}"
                                                        VerticalAlignment="Top"
                                                        Margin="8,0,8,0">
                                                        <Button.Visibility>
                                                            <MultiBinding Converter="{StaticResource ItemEditModeConverter}" ConverterParameter="Inverse">
                                                                <Binding Path="DataContext" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"/>
                                                                <Binding Path="."/>
                                                                <Binding Path="DataContext.EditModeRefreshTrigger" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"/>
                                                            </MultiBinding>
                                                        </Button.Visibility>
                                                        <materialDesign:PackIcon
                                                            Kind="PencilOutline"
                                                            Width="18"
                                                            Height="18"
                                                            Foreground="{StaticResource PrimaryColor}"/>
                                                    </Button>

                                                    <!-- Save Button (visible when editing) -->
                                                    <Button
                                                        Grid.Column="3"
                                                        Command="{Binding DataContext.UpdateItemCommand, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type UserControl}}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="40"
                                                        Height="40"
                                                        ToolTip="Save Changes"
                                                        VerticalAlignment="Top"
                                                        Margin="8,0,8,0">
                                                        <Button.Visibility>
                                                            <MultiBinding Converter="{StaticResource ItemEditModeConverter}">
                                                                <Binding Path="DataContext" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"/>
                                                                <Binding Path="."/>
                                                                <Binding Path="DataContext.EditModeRefreshTrigger" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"/>
                                                            </MultiBinding>
                                                        </Button.Visibility>
                                                        <materialDesign:PackIcon
                                                            Kind="ContentSave"
                                                            Width="18"
                                                            Height="18"
                                                            Foreground="#4CAF50"/>
                                                    </Button>

                                                    <!-- Remove Button -->
                                                    <Button
                                                        Grid.Column="4"
                                                        Command="{Binding DataContext.RemoveItemCommand, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type UserControl}}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="40"
                                                        Height="40"
                                                        ToolTip="{DynamicResource RemoveItem}"
                                                        VerticalAlignment="Top">
                                                        <materialDesign:PackIcon
                                                            Kind="DeleteOutline"
                                                            Width="18"
                                                            Height="18"
                                                            Foreground="{StaticResource ErrorColor}"/>
                                                    </Button>
                                                </Grid>
                                            </Border>
                                        </materialDesign:Card>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Empty Items Message -->
                        <Border
                            x:Name="EmptyItemsMessage"
                            Background="#F5F5F5"
                            Padding="32"
                            Margin="16,8,16,16"
                            CornerRadius="4">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding InvoiceItems.Count}" Value="0">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <StackPanel HorizontalAlignment="Center" Orientation="Vertical">
                                <materialDesign:PackIcon
                                    Kind="CartPlus"
                                    Width="48"
                                    Height="48"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,12"/>
                                <TextBlock
                                    Text="{DynamicResource NoItemsAddedYet}"
                                    FontSize="16"
                                    FontWeight="Medium"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,4"/>
                                <TextBlock
                                    Text="{DynamicResource AddItemsMessage}"
                                    FontSize="14"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    HorizontalAlignment="Center"
                                    TextWrapping="Wrap"
                                    TextAlignment="Center"/>
                            </StackPanel>
                        </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer with action buttons (Fixed at bottom) -->
            <Border Grid.Row="3" Background="{DynamicResource MaterialDesignCardBackground}" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
                <Grid MinHeight="70" Margin="16,12,16,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left side - Additional info or future buttons -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Space for future left-aligned buttons if needed -->
                    </StackPanel>

                    <!-- Right side - Action buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button
                            Command="{Binding CancelEditCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,16,0"
                            Width="120"
                            Height="40"
                            FontSize="14"
                            materialDesign:ButtonAssist.CornerRadius="4"
                            Content="{DynamicResource Cancel}"/>
                        <Button
                            Command="{Binding SaveInvoiceCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="{StaticResource PrimaryColor}"
                            Foreground="White"
                            materialDesign:ButtonAssist.CornerRadius="4"
                            Width="150"
                            Height="40"
                            FontSize="14"
                            FontWeight="Medium"
                            Content="{DynamicResource SaveInvoice}" />
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:Card>
</UserControl>