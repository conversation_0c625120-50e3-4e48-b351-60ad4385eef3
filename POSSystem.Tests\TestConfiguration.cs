using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Services.DataAccess;
using POSSystem.Services.ProductManagement;
using POSSystem.Services.SalesManagement;
using POSSystem.Services.CustomerManagement;
using POSSystem.Services.UserManagement;
using POSSystem.Services.InventoryManagement;
using POSSystem.Services.QueryOptimization;
using POSSystem.Services.Interfaces;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test configuration and dependency injection setup for unit tests
    /// </summary>
    public static class TestConfiguration
    {
        /// <summary>
        /// Create a service provider configured for testing
        /// </summary>
        public static ServiceProvider CreateTestServiceProvider()
        {
            var services = new ServiceCollection();
            ConfigureTestServices(services);
            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Configure services for testing
        /// </summary>
        public static void ConfigureTestServices(IServiceCollection services)
        {
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add in-memory database
            services.AddDbContext<POSDbContext>(options =>
            {
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString());
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Add management services
            services.AddScoped<IProductManagementService, ProductManagementService>();
            services.AddScoped<ISalesManagementService, SalesManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagementService>();
            services.AddScoped<IUserManagementService, UserManagementService>();
            services.AddScoped<IInventoryManagementService, InventoryManagementService>();

            // Add query optimization services
            services.AddScoped<OptimizedQueryService>();
            services.AddScoped<DatabaseIndexService>();

            // Add unified data service
            services.AddScoped<UnifiedDataService>();

            // Add mock database service for fallback testing
            services.AddScoped<IDatabaseService, MockDatabaseService>();
        }

        /// <summary>
        /// Create a test database context with in-memory database
        /// </summary>
        public static POSDbContext CreateTestDbContext()
        {
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .EnableSensitiveDataLogging()
                .EnableDetailedErrors()
                .Options;

            return new POSDbContext(options);
        }

        /// <summary>
        /// Seed test data into the database context
        /// </summary>
        public static async Task SeedTestDataAsync(POSDbContext context)
        {
            // Clear existing data
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            // Add categories
            var categories = new[]
            {
                new POSSystem.Models.Category { Id = 1, Name = "Electronics", Description = "Electronic devices", IsActive = true },
                new POSSystem.Models.Category { Id = 2, Name = "Clothing", Description = "Apparel and accessories", IsActive = true },
                new POSSystem.Models.Category { Id = 3, Name = "Books", Description = "Books and publications", IsActive = true }
            };
            context.Categories.AddRange(categories);

            // Add products
            var products = new[]
            {
                new POSSystem.Models.Product 
                { 
                    Id = 1, Name = "Laptop", SKU = "ELEC001", Barcode = "123456789", CategoryId = 1, 
                    SellingPrice = 999.99m, PurchasePrice = 750.00m, StockQuantity = 10, ReorderPoint = 5, IsActive = true 
                },
                new POSSystem.Models.Product 
                { 
                    Id = 2, Name = "T-Shirt", SKU = "CLOTH001", Barcode = "987654321", CategoryId = 2, 
                    SellingPrice = 29.99m, PurchasePrice = 15.00m, StockQuantity = 50, ReorderPoint = 10, IsActive = true 
                },
                new POSSystem.Models.Product 
                { 
                    Id = 3, Name = "Programming Book", SKU = "BOOK001", Barcode = "456789123", CategoryId = 3, 
                    SellingPrice = 49.99m, PurchasePrice = 30.00m, StockQuantity = 25, ReorderPoint = 5, IsActive = true 
                }
            };
            context.Products.AddRange(products);

            // Add customers
            var customers = new[]
            {
                new POSSystem.Models.Customer 
                { 
                    Id = 1, Name = "John Doe", Email = "<EMAIL>", Phone = "************", IsActive = true 
                },
                new POSSystem.Models.Customer 
                { 
                    Id = 2, Name = "Jane Smith", Email = "<EMAIL>", Phone = "************", IsActive = true 
                }
            };
            context.Customers.AddRange(customers);

            // Add users
            var users = new[]
            {
                new POSSystem.Models.User 
                { 
                    Id = 1, Username = "admin", Email = "<EMAIL>", FirstName = "Admin", LastName = "User", IsActive = true 
                },
                new POSSystem.Models.User 
                { 
                    Id = 2, Username = "cashier", Email = "<EMAIL>", FirstName = "Cashier", LastName = "User", IsActive = true 
                }
            };
            context.Users.AddRange(users);

            // Add sales
            var sales = new[]
            {
                new POSSystem.Models.Sale 
                { 
                    Id = 1, SaleDate = DateTime.Today, CustomerId = 1, UserId = 1, 
                    Subtotal = 999.99m, TaxAmount = 80.00m, GrandTotal = 1079.99m, 
                    PaymentMethod = "Credit Card", PaymentStatus = "Paid" 
                },
                new POSSystem.Models.Sale 
                { 
                    Id = 2, SaleDate = DateTime.Today.AddDays(-1), CustomerId = 2, UserId = 2, 
                    Subtotal = 79.98m, TaxAmount = 6.40m, GrandTotal = 86.38m, 
                    PaymentMethod = "Cash", PaymentStatus = "Paid" 
                }
            };
            context.Sales.AddRange(sales);

            // Add sale items
            var saleItems = new[]
            {
                new POSSystem.Models.SaleItem 
                { 
                    Id = 1, SaleId = 1, ProductId = 1, Quantity = 1, UnitPrice = 999.99m, Total = 999.99m 
                },
                new POSSystem.Models.SaleItem 
                { 
                    Id = 2, SaleId = 2, ProductId = 2, Quantity = 2, UnitPrice = 29.99m, Total = 59.98m 
                },
                new POSSystem.Models.SaleItem 
                { 
                    Id = 3, SaleId = 2, ProductId = 3, Quantity = 1, UnitPrice = 19.99m, Total = 19.99m 
                }
            };
            context.SaleItems.AddRange(saleItems);

            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Mock implementation of IDatabaseService for testing fallback scenarios
    /// </summary>
    public class MockDatabaseService : IDatabaseService
    {
        private readonly List<POSSystem.Models.Product> _products = new();
        private readonly List<POSSystem.Models.Sale> _sales = new();
        private readonly List<POSSystem.Models.Customer> _customers = new();

        // Implement required interface members
        public event EventHandler CategoryUpdated;
        public object Context => null;

        public List<POSSystem.Models.Product> GetAllProducts() => _products.Where(p => p.IsActive).ToList();
        public List<POSSystem.Models.Product> SearchProducts(string searchTerm) => 
            _products.Where(p => p.IsActive && 
                (p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 p.SKU.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))).ToList();

        public void AddProduct(POSSystem.Models.Product product)
        {
            product.Id = _products.Count + 1;
            _products.Add(product);
        }

        public void UpdateProduct(POSSystem.Models.Product product)
        {
            var existing = _products.FirstOrDefault(p => p.Id == product.Id);
            if (existing != null)
            {
                existing.Name = product.Name;
                existing.SellingPrice = product.SellingPrice;
                existing.StockQuantity = product.StockQuantity;
            }
        }

        public void DeleteProduct(int id)
        {
            var product = _products.FirstOrDefault(p => p.Id == id);
            if (product != null)
            {
                product.IsActive = false;
            }
        }

        public void AddSale(POSSystem.Models.Sale sale)
        {
            sale.Id = _sales.Count + 1;
            _sales.Add(sale);
        }

        public async Task<List<POSSystem.Models.Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            return await Task.FromResult(_sales.Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate).ToList());
        }

        public List<POSSystem.Models.Customer> GetAllCustomers() => _customers.Where(c => c.IsActive).ToList();

        public void AddCustomer(POSSystem.Models.Customer customer)
        {
            customer.Id = _customers.Count + 1;
            _customers.Add(customer);
        }

        public void UpdateCustomer(POSSystem.Models.Customer customer)
        {
            var existing = _customers.FirstOrDefault(c => c.Id == customer.Id);
            if (existing != null)
            {
                existing.Name = customer.Name;
                existing.Email = customer.Email;
                existing.Phone = customer.Phone;
            }
        }

        public void DeleteCustomer(int id)
        {
            var customer = _customers.FirstOrDefault(c => c.Id == id);
            if (customer != null)
            {
                customer.IsActive = false;
            }
        }

        // Add other required interface methods as needed...
        public void AddUser(POSSystem.Models.User user) { }
        public void UpdateUser(POSSystem.Models.User user) { }
        public void DeleteUser(int id) { }
        public void AddCategory(POSSystem.Models.Category category) { }
        public void UpdateCategory(POSSystem.Models.Category category) { }
        public void DeleteCategory(int id) { }
        public List<POSSystem.Models.TopProductItem> GetTopSellingProducts(int count) => new();

        // Implement IDisposable
        public void Dispose() { }

        // Add missing interface methods with minimal implementations
        public POSSystem.Models.User AuthenticateUser(string username, string password) => null;
        public List<POSSystem.Models.User> GetAllUsers() => new();
        public POSSystem.Models.User GetUserById(int id) => null;
        public Task<List<POSSystem.Models.Product>> GetProductsAsync(int pageSize, int offset, int? categoryId = null, string searchText = null, CancellationToken cancellationToken = default) => Task.FromResult(new List<POSSystem.Models.Product>());
        public POSSystem.Models.Product GetProductById(int id) => null;
        public Task<POSSystem.Models.Product> GetProductByIdAsync(int id, CancellationToken cancellationToken = default) => Task.FromResult((POSSystem.Models.Product)null);
        public POSSystem.Models.Product GetProductByBarcode(string barcode) => null;
        public Task<POSSystem.Models.Product> GetProductByBarcodeAsync(string barcode, CancellationToken cancellationToken = default) => Task.FromResult((POSSystem.Models.Product)null);
        public Task<List<POSSystem.Models.Product>> GetAllProductsWithFullDetailsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new List<POSSystem.Models.Product>());
        public List<POSSystem.Models.Product> GetLowStockProducts() => new();
        public Task<List<POSSystem.Models.Product>> GetLowStockProductsAsync() => Task.FromResult(new List<POSSystem.Models.Product>());
        public List<POSSystem.Models.Product> GetExpiringProducts(int daysAhead) => new();
        public List<POSSystem.Models.Category> GetAllCategories() => new();
        public POSSystem.Models.Category GetCategoryById(int id) => null;
        public POSSystem.Models.Customer GetCustomerById(int id) => null;
        public POSSystem.Models.Customer GetCustomerByLoyaltyCode(string loyaltyCode) => null;
        public List<POSSystem.Models.Sale> GetAllSales() => new();
        public void UpdateSale(POSSystem.Models.Sale sale) { }
        public void DeleteSale(int id) { }
        public POSSystem.Models.Sale GetSaleById(int id) => null;
        public List<POSSystem.Models.Sale> GetUnpaidSales() => new();
        public decimal GetSalesTotal(DateTime date) => 0;
        public List<POSSystem.Models.Sale> GetRecentSales(int limit) => new();
        public Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate) => Task.FromResult((0, 0m));
        public Task<POSSystem.Models.SalesMetrics> GetSalesMetricsAsync(DateTime startDate, DateTime endDate) => Task.FromResult((POSSystem.Models.SalesMetrics)null);
        public Task<List<POSSystem.Models.Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default) => Task.FromResult(new List<POSSystem.Models.Sale>());
        public List<POSSystem.Models.Supplier> GetAllSuppliers() => new();
        public void AddSupplier(POSSystem.Models.Supplier supplier) { }
        public void UpdateSupplier(POSSystem.Models.Supplier supplier) { }
        public void DeleteSupplier(int id) { }
        public POSSystem.Models.Supplier GetSupplierById(int id) => null;
        public void AddInventoryTransaction(POSSystem.Models.InventoryTransaction transaction) { }
        public List<POSSystem.Models.InventoryTransaction> GetInventoryTransactions(int? productId = null) => new();
        public void UpdateProductStock(int productId, decimal newQuantity, string reason) { }
        public void AddBatchStock(POSSystem.Models.BatchStock batch) { }
        public List<POSSystem.Models.BatchStock> GetBatchesForProduct(int productId) => new();
        public void AddStockToBatch(int batchId, decimal quantity) { }
        public void UpdateBatch(int batchId, POSSystem.Models.BatchStock updatedBatch) { }
        public List<POSSystem.Models.CashDrawer> GetAllCashDrawers() => new();
        public POSSystem.Models.CashDrawer GetActiveCashDrawer() => null;
        public void AddCashDrawer(POSSystem.Models.CashDrawer cashDrawer) { }
        public void UpdateCashDrawer(POSSystem.Models.CashDrawer cashDrawer) { }
        public List<POSSystem.Models.BusinessExpense> GetAllBusinessExpenses() => new();
        public void AddBusinessExpense(POSSystem.Models.BusinessExpense expense) { }
        public void UpdateBusinessExpense(POSSystem.Models.BusinessExpense expense) { }
        public void DeleteBusinessExpense(int id) { }
        public List<POSSystem.Models.PurchaseOrder> GetAllPurchaseOrders() => new();
        public List<POSSystem.Models.PurchaseOrder> GetUnpaidPurchaseOrders() => new();
        public void AddPurchaseOrder(POSSystem.Models.PurchaseOrder purchaseOrder) { }
        public void UpdatePurchaseOrder(POSSystem.Models.PurchaseOrder purchaseOrder) { }
        public void EnsureInvoiceTablesExist() { }
        public List<POSSystem.Models.Invoice> GetInvoices(string type = null, string status = null, int? customerId = null, int? supplierId = null) => new();
        public POSSystem.Models.Invoice GetInvoiceById(int invoiceId) => null;
        public int GetNextInvoiceNumber() => 1;
        public int CreateInvoice(POSSystem.Models.Invoice invoice) => 1;
        public bool UpdateInvoice(POSSystem.Models.Invoice invoice) => false;
        public bool DeleteInvoice(int invoiceId) => false;
        public List<POSSystem.Models.LoyaltyProgram> GetAllLoyaltyPrograms() => new();
        public void AddLoyaltyProgram(POSSystem.Models.LoyaltyProgram program) { }
        public void UpdateLoyaltyProgram(POSSystem.Models.LoyaltyProgram program) { }
        public POSSystem.Models.LoyaltyProgram GetActiveLoyaltyProgram() => null;
        public void SaveLoyaltyTransaction(POSSystem.Models.LoyaltyTransaction transaction) { }
        public List<POSSystem.Models.DiscountType> GetAllDiscountTypes() => new();
        public List<POSSystem.Models.DiscountReason> GetAllDiscountReasons() => new();
        public List<POSSystem.Models.SalesTrend> GetSalesTrends(DateTime startDate, DateTime endDate) => new();
        public void MigratePasswords() { }
        public void InitializeDefaultRoles() { }
        public void BackupDatabase(string backupPath) { }
        public void RestoreDatabase(string backupPath) { }
        public POSSystem.Models.User GetDefaultUser() => null;
        public Task<POSSystem.Models.User> GetDefaultUserAsync() => Task.FromResult((POSSystem.Models.User)null);
        public Task<POSSystem.Models.Customer> GetCustomerByLoyaltyCodeAsync(string loyaltyCode) => Task.FromResult((POSSystem.Models.Customer)null);
        public Task<List<POSSystem.Models.Product>> SearchProductsAsync(string searchTerm) => Task.FromResult(new List<POSSystem.Models.Product>());
        public Task<List<POSSystem.Models.CashDrawer>> GetAllCashDrawersAsync() => Task.FromResult(new List<POSSystem.Models.CashDrawer>());
        public Task<POSSystem.Models.CashDrawer> GetActiveCashDrawerAsync() => Task.FromResult((POSSystem.Models.CashDrawer)null);
        public Task<List<POSSystem.Models.Product>> GetUserFavoritesAsync(int userId) => Task.FromResult(new List<POSSystem.Models.Product>());
    }
}
