using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Threading;

namespace POSSystem.Services.Caching
{
    /// <summary>
    /// Advanced in-memory caching service with intelligent cache management
    /// Features: Multi-level caching, dependency tracking, cache warming, statistics
    /// </summary>
    public class AdvancedCacheService : ICacheService
    {
        private readonly ConcurrentDictionary<string, CacheEntry> _cache = new();
        private readonly ConcurrentDictionary<string, HashSet<string>> _dependencies = new();
        private readonly ILogger<AdvancedCacheService> _logger;
        private readonly Timer _cleanupTimer;
        private readonly CacheStatistics _statistics = new();

        // Backing fields for thread-safe counters
        private int _hitCount = 0;
        private int _missCount = 0;

        // Cache level configurations
        private readonly Dictionary<CacheLevel, CacheConfig> _levelConfigs = new()
        {
            { CacheLevel.Reference, new CacheConfig { DefaultTTL = TimeSpan.FromHours(4), MaxItems = 1000 } },
            { CacheLevel.Business, new CacheConfig { DefaultTTL = TimeSpan.FromMinutes(30), MaxItems = 5000 } },
            { CacheLevel.Transactional, new CacheConfig { DefaultTTL = TimeSpan.FromMinutes(5), MaxItems = 2000 } },
            { CacheLevel.Temporary, new CacheConfig { DefaultTTL = TimeSpan.FromMinutes(2), MaxItems = 1000 } }
        };

        public AdvancedCacheService(ILogger<AdvancedCacheService> logger = null)
        {
            _logger = logger;

            // Start cleanup timer (runs every 5 minutes)
            _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

            _logger?.LogInformation("AdvancedCacheService initialized with multi-level caching");
        }

        public async Task<T> GetAsync<T>(string key)
        {
            return await Task.FromResult(GetSync<T>(key));
        }

        private T GetSync<T>(string key)
        {
            if (_cache.TryGetValue(key, out var entry))
            {
                if (entry.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    Interlocked.Increment(ref _missCount);
                    return default(T);
                }

                Interlocked.Increment(ref _hitCount);
                entry.LastAccessed = DateTime.UtcNow;

                try
                {
                    return JsonSerializer.Deserialize<T>(entry.Data);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Failed to deserialize cached data for key {Key}", key);
                    _cache.TryRemove(key, out _);
                    Interlocked.Increment(ref _missCount);
                    return default(T);
                }
            }

            Interlocked.Increment(ref _missCount);
            return default(T);
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            await Task.Run(() => SetSync(key, value, expiry));
        }

        private void SetSync<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                var serializedData = JsonSerializer.Serialize(value);
                var entry = new CacheEntry
                {
                    Data = serializedData,
                    CreatedAt = DateTime.UtcNow,
                    LastAccessed = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(expiry ?? TimeSpan.FromMinutes(30)),
                    Level = DetermineCacheLevel(key)
                };

                _cache.AddOrUpdate(key, entry, (k, existing) => entry);

                // Enforce per-level capacity to prevent unbounded growth
                EnforceLevelCapacity(entry.Level);

                // Update statistics
                _statistics.TotalKeys = _cache.Count;

                _logger?.LogDebug("Cached data for key {Key} with expiry {Expiry}", key, entry.ExpiresAt);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to cache data for key {Key}", key);
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
        {
            var cached = await GetAsync<T>(key);
            if (cached != null)
            {
                return cached;
            }

            var value = await factory();
            if (!EqualityComparer<T>.Default.Equals(value, default(T)))
            {
                await SetAsync(key, value, expiry);
            }

            return value;
        }

        public async Task RemoveAsync(string key)
        {
            await Task.Run(() =>
            {
                _cache.TryRemove(key, out _);
                _statistics.TotalKeys = _cache.Count;
            });
        }

        public async Task ClearAsync()
        {
            await Task.Run(() =>
            {
                _cache.Clear();
                _dependencies.Clear();
                _statistics.TotalKeys = 0;
                _statistics.LastCleared = DateTime.UtcNow;
                _logger?.LogInformation("Cache cleared");
            });
        }

        public async Task<bool> ExistsAsync(string key)
        {
            return await Task.FromResult(_cache.ContainsKey(key) && !_cache[key].IsExpired);
        }

        public async Task SetWithDependencyAsync<T>(string key, T value, string[] dependencies, TimeSpan? expiry = null)
        {
            await SetAsync(key, value, expiry);

            // Track dependencies
            foreach (var dependency in dependencies)
            {
                _dependencies.AddOrUpdate(dependency,
                    new HashSet<string> { key },
                    (k, existing) => { existing.Add(key); return existing; });
            }
        }

        public async Task InvalidateDependenciesAsync(string dependency)
        {
            if (_dependencies.TryGetValue(dependency, out var dependentKeys))
            {
                foreach (var key in dependentKeys)
                {
                    await RemoveAsync(key);
                }
                _dependencies.TryRemove(dependency, out _);

                _logger?.LogDebug("Invalidated {Count} cache entries for dependency {Dependency}",
                    dependentKeys.Count, dependency);
            }
        }

        private CacheLevel DetermineCacheLevel(string key)
        {
            // Smart cache level determination based on key patterns
            if (key.Contains("categories") || key.Contains("units") || key.Contains("settings"))
                return CacheLevel.Reference;

            if (key.Contains("products") || key.Contains("customers"))
                return CacheLevel.Business;

            if (key.Contains("sales") || key.Contains("inventory") || key.Contains("stock"))
                return CacheLevel.Transactional;

            return CacheLevel.Temporary;
        }

        private void EnforceLevelCapacity(CacheLevel level)
        {
            try
            {
                if (!_levelConfigs.TryGetValue(level, out var cfg)) return;

                // Identify entries of this level and sort by LastAccessed (LRU eviction)
                var candidates = _cache
                    .Where(kv => kv.Value.Level == level)
                    .OrderBy(kv => kv.Value.LastAccessed)
                    .ToList();

                int overflow = candidates.Count - cfg.MaxItems;
                if (overflow <= 0) return;

                foreach (var kv in candidates.Take(overflow))
                {
                    _cache.TryRemove(kv.Key, out _);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error enforcing cache capacity for level {Level}", level);
            }
        }

        private void CleanupExpiredEntries(object state)
        {
            try
            {
                var expiredKeys = _cache
                    .Where(kvp => kvp.Value.IsExpired)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _cache.TryRemove(key, out _);
                }

                if (expiredKeys.Count > 0)
                {
                    _statistics.TotalKeys = _cache.Count;
                    _logger?.LogDebug("Cleaned up {Count} expired cache entries", expiredKeys.Count);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during cache cleanup");
            }
        }

        public CacheStatistics GetStatistics()
        {
            _statistics.TotalKeys = _cache.Count;
            _statistics.HitCount = _hitCount;
            _statistics.MissCount = _missCount;
            _statistics.KeysByLevel = _cache.Values
                .GroupBy(e => e.Level)
                .ToDictionary(g => g.Key, g => g.Count());

            return _statistics;
        }

        public async Task<Dictionary<string, object>> GetCacheInfoAsync()
        {
            var hitCount = _hitCount;
            var missCount = _missCount;
            var totalRequests = hitCount + missCount;
            var hitRatio = totalRequests > 0 ? (double)hitCount / totalRequests : 0;

            return await Task.FromResult(new Dictionary<string, object>
            {
                ["TotalKeys"] = _cache.Count,
                ["HitRatio"] = hitRatio,
                ["TotalRequests"] = totalRequests,
                ["HitCount"] = hitCount,
                ["MissCount"] = missCount,
                ["LevelDistribution"] = _cache.Values.GroupBy(e => e.Level).ToDictionary(g => g.Key, g => g.Count())
            });
        }

        // Additional methods will be added in the next part...
        public async Task<T> GetFromLevel<T>(CacheLevel level, string key)
        {
            return await GetAsync<T>($"{level}:{key}");
        }

        public async Task SetToLevel<T>(CacheLevel level, string key, T value, TimeSpan? expiry = null)
        {
            var levelKey = $"{level}:{key}";
            var levelExpiry = expiry ?? _levelConfigs[level].DefaultTTL;
            await SetAsync(levelKey, value, levelExpiry);
        }

        public async Task WarmCacheAsync()
        {
            _logger?.LogInformation("Starting cache warming...");
            // Implementation will be added when we integrate with repositories
            await Task.CompletedTask;
        }

        public async Task PreloadReferenceDataAsync()
        {
            _logger?.LogInformation("Preloading reference data...");
            // Implementation will be added when we integrate with repositories
            await Task.CompletedTask;
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }

    internal class CacheEntry
    {
        public string Data { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastAccessed { get; set; }
        public DateTime ExpiresAt { get; set; }
        public CacheLevel Level { get; set; }
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;
    }

    internal class CacheConfig
    {
        public TimeSpan DefaultTTL { get; set; }
        public int MaxItems { get; set; }
    }
}
