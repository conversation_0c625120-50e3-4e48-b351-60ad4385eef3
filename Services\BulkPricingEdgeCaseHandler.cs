using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.Services
{
    /// <summary>
    /// Handles complex edge cases and scenarios for bulk pricing functionality.
    /// Provides robust handling of partial quantities, mixed pricing tiers, stock limitations, and conflicts.
    /// </summary>
    public class BulkPricingEdgeCaseHandler
    {
        private readonly BulkPricingService _bulkPricingService;

        public BulkPricingEdgeCaseHandler()
        {
            _bulkPricingService = new BulkPricingService();
        }

        /// <summary>
        /// Handles partial quantity scenarios where customer wants less than minimum tier quantity.
        /// Provides intelligent recommendations for optimal purchasing decisions.
        /// </summary>
        /// <param name="product">Product with bulk pricing</param>
        /// <param name="desiredQuantity">Quantity customer wants</param>
        /// <returns>Comprehensive options for handling partial quantities</returns>
        public PartialQuantityHandlingResult HandlePartialQuantity(Product product, decimal desiredQuantity)
        {
            var result = new PartialQuantityHandlingResult
            {
                Product = product,
                DesiredQuantity = desiredQuantity,
                Options = new List<PartialQuantityOption>()
            };

            if (product == null || !product.HasBulkPricing)
            {
                result.RecommendedOption = CreateRegularPriceOption(product, desiredQuantity);
                result.Options.Add(result.RecommendedOption);
                return result;
            }

            var tiers = product.GetActivePriceTiers().ToList();
            
            // Option 1: Buy at regular price (exact quantity)
            var regularOption = CreateRegularPriceOption(product, desiredQuantity);
            result.Options.Add(regularOption);

            // Option 2: Find applicable tier for current quantity
            var applicableTier = product.GetBestPriceTierForQuantity(desiredQuantity);
            if (applicableTier != null)
            {
                var tierOption = new PartialQuantityOption
                {
                    Type = PartialQuantityOptionType.ApplicableTier,
                    Quantity = desiredQuantity,
                    UnitPrice = applicableTier.EffectiveUnitPrice,
                    TotalPrice = applicableTier.CalculatePriceForQuantity(desiredQuantity),
                    Description = $"Use {applicableTier.GetDisplayText()} pricing",
                    Savings = (product.SellingPrice - applicableTier.EffectiveUnitPrice) * desiredQuantity,
                    TierUsed = applicableTier
                };
                result.Options.Add(tierOption);
            }

            // Option 3: Upgrade to next tier for better pricing
            var nextTier = tiers.Where(t => t.MinimumQuantity > desiredQuantity)
                                .OrderBy(t => t.MinimumQuantity)
                                .FirstOrDefault();

            if (nextTier != null)
            {
                var upgradeOption = CreateUpgradeOption(product, desiredQuantity, nextTier);
                if (IsUpgradeWorthwhile(regularOption, upgradeOption))
                {
                    result.Options.Add(upgradeOption);
                }
            }

            // Option 4: Split purchase across multiple tiers (for very large quantities)
            if (desiredQuantity > 50) // Only for large quantities
            {
                var splitOption = CreateSplitPurchaseOption(product, desiredQuantity, tiers);
                if (splitOption != null)
                {
                    result.Options.Add(splitOption);
                }
            }

            // Determine recommended option
            result.RecommendedOption = DetermineRecommendedOption(result.Options);
            
            return result;
        }

        /// <summary>
        /// Handles mixed pricing tiers in a cart where different items have different bulk pricing rules.
        /// Optimizes the entire cart for maximum savings.
        /// </summary>
        /// <param name="cart">Cart with multiple items</param>
        /// <returns>Optimized cart configuration with recommendations</returns>
        public MixedPricingOptimizationResult OptimizeMixedPricingCart(Cart cart)
        {
            var result = new MixedPricingOptimizationResult
            {
                OriginalCart = cart,
                OptimizedItems = new List<OptimizedCartItem>(),
                TotalOptimization = new CartOptimization()
            };

            foreach (var item in cart.Items)
            {
                var optimizedItem = OptimizeCartItem(item);
                result.OptimizedItems.Add(optimizedItem);
            }

            // Calculate total optimization impact
            result.TotalOptimization.OriginalTotal = cart.GrandTotal;
            result.TotalOptimization.OptimizedTotal = result.OptimizedItems.Sum(oi => oi.RecommendedTotal);
            result.TotalOptimization.TotalSavings = result.TotalOptimization.OriginalTotal - result.TotalOptimization.OptimizedTotal;
            result.TotalOptimization.SavingsPercentage = result.TotalOptimization.OriginalTotal > 0 ?
                (result.TotalOptimization.TotalSavings / result.TotalOptimization.OriginalTotal) * 100 : 0;

            return result;
        }

        /// <summary>
        /// Handles stock limitations when bulk pricing requires more quantity than available.
        /// Provides alternatives and partial fulfillment options.
        /// </summary>
        /// <param name="product">Product with stock limitations</param>
        /// <param name="desiredQuantity">Desired quantity</param>
        /// <param name="availableStock">Available stock quantity</param>
        /// <returns>Stock limitation handling options</returns>
        public StockLimitationHandlingResult HandleStockLimitations(Product product, decimal desiredQuantity, decimal availableStock)
        {
            var result = new StockLimitationHandlingResult
            {
                Product = product,
                DesiredQuantity = desiredQuantity,
                AvailableStock = availableStock,
                Options = new List<StockLimitationOption>()
            };

            if (availableStock >= desiredQuantity)
            {
                // No stock limitation, proceed normally
                var normalOption = new StockLimitationOption
                {
                    Type = StockLimitationOptionType.FullFulfillment,
                    Quantity = desiredQuantity,
                    Description = "Full quantity available",
                    IsRecommended = true
                };
                result.Options.Add(normalOption);
                result.RecommendedOption = normalOption;
                return result;
            }

            // Option 1: Partial fulfillment with available stock
            var partialOption = CreatePartialFulfillmentOption(product, availableStock);
            result.Options.Add(partialOption);

            // Option 2: Backorder remaining quantity
            // Only allow backorders for non-service products (physical products can be backordered)
            if (product.Type == ProductType.Product)
            {
                var backorderOption = CreateBackorderOption(product, desiredQuantity, availableStock);
                result.Options.Add(backorderOption);
            }

            // Option 3: Alternative products with similar pricing
            var alternativeOptions = FindAlternativeProducts(product, desiredQuantity);
            result.Options.AddRange(alternativeOptions);

            result.RecommendedOption = DetermineStockRecommendation(result.Options);
            
            return result;
        }

        /// <summary>
        /// Resolves conflicts between overlapping pricing tiers or conflicting business rules.
        /// </summary>
        /// <param name="product">Product with potential conflicts</param>
        /// <param name="quantity">Quantity being processed</param>
        /// <returns>Conflict resolution result</returns>
        public PricingConflictResolution ResolvePricingConflicts(Product product, decimal quantity)
        {
            var result = new PricingConflictResolution
            {
                Product = product,
                Quantity = quantity,
                Conflicts = new List<PricingConflict>(),
                Resolution = new ConflictResolution()
            };

            if (product?.PriceTiers == null)
                return result;

            var applicableTiers = product.PriceTiers
                .Where(pt => pt.IsCurrentlyValid && pt.QualifiesForTier(quantity))
                .ToList();

            // Check for overlapping tiers
            var overlappingTiers = FindOverlappingTiers(applicableTiers, quantity);
            if (overlappingTiers.Any())
            {
                result.Conflicts.Add(new PricingConflict
                {
                    Type = ConflictType.OverlappingTiers,
                    Description = "Multiple pricing tiers apply to this quantity",
                    AffectedTiers = overlappingTiers
                });
            }

            // Check for expired tiers still being used
            var expiredTiers = product.PriceTiers
                .Where(pt => pt.ExpirationDate.HasValue && pt.ExpirationDate < DateTime.Now)
                .ToList();

            if (expiredTiers.Any())
            {
                result.Conflicts.Add(new PricingConflict
                {
                    Type = ConflictType.ExpiredTiers,
                    Description = "Some pricing tiers have expired",
                    AffectedTiers = expiredTiers
                });
            }

            // Check for pricing inconsistencies
            var inconsistentTiers = FindInconsistentPricing(product.PriceTiers.ToList());
            if (inconsistentTiers.Any())
            {
                result.Conflicts.Add(new PricingConflict
                {
                    Type = ConflictType.InconsistentPricing,
                    Description = "Pricing tiers have inconsistent unit prices",
                    AffectedTiers = inconsistentTiers
                });
            }

            // Resolve conflicts
            if (result.Conflicts.Any())
            {
                result.Resolution = ResolveConflicts(result.Conflicts, quantity);
            }
            else
            {
                result.Resolution.ResolvedTier = product.GetBestPriceTierForQuantity(quantity);
                result.Resolution.ResolutionMethod = "No conflicts found, using best available tier";
            }

            return result;
        }

        private PartialQuantityOption CreateRegularPriceOption(Product product, decimal quantity)
        {
            return new PartialQuantityOption
            {
                Type = PartialQuantityOptionType.RegularPrice,
                Quantity = quantity,
                UnitPrice = product?.SellingPrice ?? 0,
                TotalPrice = (product?.SellingPrice ?? 0) * quantity,
                Description = "Buy at regular price",
                Savings = 0
            };
        }

        private PartialQuantityOption CreateUpgradeOption(Product product, decimal desiredQuantity, ProductPriceTier nextTier)
        {
            var extraQuantity = nextTier.MinimumQuantity - desiredQuantity;
            var totalPrice = nextTier.CalculatePriceForQuantity(nextTier.MinimumQuantity);
            var regularPrice = product.SellingPrice * nextTier.MinimumQuantity;
            
            return new PartialQuantityOption
            {
                Type = PartialQuantityOptionType.UpgradeToNextTier,
                Quantity = nextTier.MinimumQuantity,
                UnitPrice = nextTier.EffectiveUnitPrice,
                TotalPrice = totalPrice,
                Description = $"Buy {extraQuantity:0.###} more to reach {nextTier.GetDisplayText()}",
                Savings = regularPrice - totalPrice,
                ExtraQuantity = extraQuantity,
                TierUsed = nextTier
            };
        }

        private PartialQuantityOption CreateSplitPurchaseOption(Product product, decimal quantity, List<ProductPriceTier> tiers)
        {
            // Complex algorithm to split large quantities across multiple tiers for optimal pricing
            // This is a simplified version - in practice, this would use dynamic programming
            
            var bestTier = tiers.Where(t => t.MinimumQuantity <= quantity)
                                .OrderByDescending(t => t.MinimumQuantity)
                                .FirstOrDefault();
            
            if (bestTier == null) return null;

            var tierQuantity = Math.Floor(quantity / bestTier.MinimumQuantity) * bestTier.MinimumQuantity;
            var remainingQuantity = quantity - tierQuantity;
            
            var tierPrice = bestTier.CalculatePriceForQuantity(tierQuantity);
            var remainingPrice = remainingQuantity * product.SellingPrice;
            
            return new PartialQuantityOption
            {
                Type = PartialQuantityOptionType.SplitPurchase,
                Quantity = quantity,
                UnitPrice = (tierPrice + remainingPrice) / quantity,
                TotalPrice = tierPrice + remainingPrice,
                Description = $"Split: {tierQuantity:0.###} at {bestTier.GetDisplayText()}, {remainingQuantity:0.###} at regular price",
                Savings = (product.SellingPrice * quantity) - (tierPrice + remainingPrice),
                TierUsed = bestTier
            };
        }

        private bool IsUpgradeWorthwhile(PartialQuantityOption regularOption, PartialQuantityOption upgradeOption)
        {
            // Upgrade is worthwhile if the additional cost is reasonable (within 20% of original)
            var additionalCost = upgradeOption.TotalPrice - regularOption.TotalPrice;
            var costIncrease = regularOption.TotalPrice > 0 ? additionalCost / regularOption.TotalPrice : 0;
            
            return costIncrease <= 0.2m && upgradeOption.Savings > 0;
        }

        private PartialQuantityOption DetermineRecommendedOption(List<PartialQuantityOption> options)
        {
            // Prioritize options with savings, then lowest total cost
            return options.OrderByDescending(o => o.Savings)
                         .ThenBy(o => o.TotalPrice)
                         .FirstOrDefault();
        }

        private OptimizedCartItem OptimizeCartItem(CartItem item)
        {
            var optimized = new OptimizedCartItem
            {
                OriginalItem = item,
                CurrentQuantity = item.Quantity,
                CurrentTotal = item.Total
            };

            if (item.Product?.HasBulkPricing == true)
            {
                var suggestions = _bulkPricingService.GetQuantitySuggestions(item.Product, item.Quantity);
                if (suggestions.Any())
                {
                    var bestSuggestion = suggestions.OrderByDescending(s => s.TotalSavings).First();
                    optimized.RecommendedQuantity = bestSuggestion.SuggestedQuantity;
                    optimized.RecommendedTotal = bestSuggestion.SuggestedQuantity * bestSuggestion.NewUnitPrice;
                    optimized.PotentialSavings = bestSuggestion.TotalSavings;
                    optimized.Recommendation = bestSuggestion.Description;
                }
            }

            if (optimized.RecommendedQuantity == 0)
            {
                optimized.RecommendedQuantity = item.Quantity;
                optimized.RecommendedTotal = item.Total;
            }

            return optimized;
        }

        private StockLimitationOption CreatePartialFulfillmentOption(Product product, decimal availableStock)
        {
            var pricing = _bulkPricingService.CalculateBestPricing(product, availableStock);
            
            return new StockLimitationOption
            {
                Type = StockLimitationOptionType.PartialFulfillment,
                Quantity = availableStock,
                TotalPrice = pricing.TotalPrice,
                Description = $"Buy available stock ({availableStock:0.###} units)",
                IsRecommended = true
            };
        }

        private StockLimitationOption CreateBackorderOption(Product product, decimal desiredQuantity, decimal availableStock)
        {
            var backorderQuantity = desiredQuantity - availableStock;
            
            return new StockLimitationOption
            {
                Type = StockLimitationOptionType.Backorder,
                Quantity = desiredQuantity,
                Description = $"Buy {availableStock:0.###} now, backorder {backorderQuantity:0.###}",
                IsRecommended = false
            };
        }

        private List<StockLimitationOption> FindAlternativeProducts(Product product, decimal desiredQuantity)
        {
            // This would typically query the database for similar products
            // For now, return empty list as this requires database access
            return new List<StockLimitationOption>();
        }

        private StockLimitationOption DetermineStockRecommendation(List<StockLimitationOption> options)
        {
            return options.FirstOrDefault(o => o.IsRecommended) ?? options.FirstOrDefault();
        }

        private List<ProductPriceTier> FindOverlappingTiers(List<ProductPriceTier> tiers, decimal quantity)
        {
            var overlapping = new List<ProductPriceTier>();
            
            for (int i = 0; i < tiers.Count; i++)
            {
                for (int j = i + 1; j < tiers.Count; j++)
                {
                    var tier1 = tiers[i];
                    var tier2 = tiers[j];
                    
                    if (DoTiersOverlap(tier1, tier2) && tier1.QualifiesForTier(quantity) && tier2.QualifiesForTier(quantity))
                    {
                        if (!overlapping.Contains(tier1)) overlapping.Add(tier1);
                        if (!overlapping.Contains(tier2)) overlapping.Add(tier2);
                    }
                }
            }
            
            return overlapping;
        }

        private bool DoTiersOverlap(ProductPriceTier tier1, ProductPriceTier tier2)
        {
            var tier1Max = tier1.MaximumQuantity ?? decimal.MaxValue;
            var tier2Max = tier2.MaximumQuantity ?? decimal.MaxValue;

            return tier1.MinimumQuantity < tier2Max && tier2.MinimumQuantity < tier1Max;
        }

        private List<ProductPriceTier> FindInconsistentPricing(List<ProductPriceTier> tiers)
        {
            var inconsistent = new List<ProductPriceTier>();
            var sortedTiers = tiers.OrderBy(t => t.MinimumQuantity).ToList();
            
            for (int i = 1; i < sortedTiers.Count; i++)
            {
                var previousTier = sortedTiers[i - 1];
                var currentTier = sortedTiers[i];
                
                // Higher quantity tiers should have lower or equal unit prices
                if (currentTier.EffectiveUnitPrice > previousTier.EffectiveUnitPrice)
                {
                    if (!inconsistent.Contains(previousTier)) inconsistent.Add(previousTier);
                    if (!inconsistent.Contains(currentTier)) inconsistent.Add(currentTier);
                }
            }
            
            return inconsistent;
        }

        private ConflictResolution ResolveConflicts(List<PricingConflict> conflicts, decimal quantity)
        {
            var resolution = new ConflictResolution();
            
            // Simple resolution strategy: use the tier with the lowest effective unit price
            var allAffectedTiers = conflicts.SelectMany(c => c.AffectedTiers).Distinct().ToList();
            var validTiers = allAffectedTiers.Where(t => t.IsCurrentlyValid && t.QualifiesForTier(quantity)).ToList();
            
            if (validTiers.Any())
            {
                resolution.ResolvedTier = validTiers.OrderBy(t => t.EffectiveUnitPrice).First();
                resolution.ResolutionMethod = "Selected tier with lowest unit price from conflicting tiers";
            }
            else
            {
                resolution.ResolutionMethod = "No valid tiers found, using regular pricing";
            }
            
            return resolution;
        }
    }

    // ===== EDGE CASE HANDLING DATA MODELS =====

    /// <summary>
    /// Result of partial quantity handling with multiple options.
    /// </summary>
    public class PartialQuantityHandlingResult
    {
        public Product Product { get; set; }
        public decimal DesiredQuantity { get; set; }
        public List<PartialQuantityOption> Options { get; set; } = new List<PartialQuantityOption>();
        public PartialQuantityOption RecommendedOption { get; set; }

        public bool HasBetterOptions => Options.Any(o => o.Savings > 0);
        public decimal MaxPotentialSavings => Options.Max(o => o.Savings);
    }

    /// <summary>
    /// Option for handling partial quantities.
    /// </summary>
    public class PartialQuantityOption
    {
        public PartialQuantityOptionType Type { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string Description { get; set; }
        public decimal Savings { get; set; }
        public decimal ExtraQuantity { get; set; }
        public ProductPriceTier TierUsed { get; set; }

        public bool HasSavings => Savings > 0;
        public string SavingsDisplay => HasSavings ? $"Save {Savings:C2}" : "";
    }

    public enum PartialQuantityOptionType
    {
        RegularPrice,
        ApplicableTier,
        UpgradeToNextTier,
        SplitPurchase
    }

    /// <summary>
    /// Result of mixed pricing optimization for entire cart.
    /// </summary>
    public class MixedPricingOptimizationResult
    {
        public Cart OriginalCart { get; set; }
        public List<OptimizedCartItem> OptimizedItems { get; set; } = new List<OptimizedCartItem>();
        public CartOptimization TotalOptimization { get; set; }

        public bool HasOptimizations => OptimizedItems.Any(oi => oi.PotentialSavings > 0);
        public decimal TotalPotentialSavings => OptimizedItems.Sum(oi => oi.PotentialSavings);
    }

    /// <summary>
    /// Optimized version of a cart item.
    /// </summary>
    public class OptimizedCartItem
    {
        public CartItem OriginalItem { get; set; }
        public decimal CurrentQuantity { get; set; }
        public decimal CurrentTotal { get; set; }
        public decimal RecommendedQuantity { get; set; }
        public decimal RecommendedTotal { get; set; }
        public decimal PotentialSavings { get; set; }
        public string Recommendation { get; set; }

        public bool HasOptimization => PotentialSavings > 0;
        public decimal AdditionalQuantity => RecommendedQuantity - CurrentQuantity;
    }

    /// <summary>
    /// Cart optimization summary.
    /// </summary>
    public class CartOptimization
    {
        public decimal OriginalTotal { get; set; }
        public decimal OptimizedTotal { get; set; }
        public decimal TotalSavings { get; set; }
        public decimal SavingsPercentage { get; set; }

        public string SavingsDisplay => $"Save {TotalSavings:C2} ({SavingsPercentage:F1}%)";
    }

    /// <summary>
    /// Result of stock limitation handling.
    /// </summary>
    public class StockLimitationHandlingResult
    {
        public Product Product { get; set; }
        public decimal DesiredQuantity { get; set; }
        public decimal AvailableStock { get; set; }
        public List<StockLimitationOption> Options { get; set; } = new List<StockLimitationOption>();
        public StockLimitationOption RecommendedOption { get; set; }

        public bool HasStockIssue => AvailableStock < DesiredQuantity;
        public decimal ShortfallQuantity => Math.Max(0, DesiredQuantity - AvailableStock);
    }

    /// <summary>
    /// Option for handling stock limitations.
    /// </summary>
    public class StockLimitationOption
    {
        public StockLimitationOptionType Type { get; set; }
        public decimal Quantity { get; set; }
        public decimal TotalPrice { get; set; }
        public string Description { get; set; }
        public bool IsRecommended { get; set; }
        public Product AlternativeProduct { get; set; }

        public string PriceDisplay => TotalPrice.ToString("C2");
    }

    public enum StockLimitationOptionType
    {
        FullFulfillment,
        PartialFulfillment,
        Backorder,
        AlternativeProduct
    }

    /// <summary>
    /// Result of pricing conflict resolution.
    /// </summary>
    public class PricingConflictResolution
    {
        public Product Product { get; set; }
        public decimal Quantity { get; set; }
        public List<PricingConflict> Conflicts { get; set; } = new List<PricingConflict>();
        public ConflictResolution Resolution { get; set; }

        public bool HasConflicts => Conflicts.Any();
        public string ConflictSummary => $"{Conflicts.Count} conflicts found";
    }

    /// <summary>
    /// Represents a pricing conflict.
    /// </summary>
    public class PricingConflict
    {
        public ConflictType Type { get; set; }
        public string Description { get; set; }
        public List<ProductPriceTier> AffectedTiers { get; set; } = new List<ProductPriceTier>();

        public string SeverityLevel => Type switch
        {
            ConflictType.OverlappingTiers => "High",
            ConflictType.ExpiredTiers => "Medium",
            ConflictType.InconsistentPricing => "Low",
            _ => "Unknown"
        };
    }

    public enum ConflictType
    {
        OverlappingTiers,
        ExpiredTiers,
        InconsistentPricing
    }

    /// <summary>
    /// Resolution for pricing conflicts.
    /// </summary>
    public class ConflictResolution
    {
        public ProductPriceTier ResolvedTier { get; set; }
        public string ResolutionMethod { get; set; }
        public List<string> ActionsRequired { get; set; } = new List<string>();

        public bool IsResolved => ResolvedTier != null || !string.IsNullOrEmpty(ResolutionMethod);
    }
}
