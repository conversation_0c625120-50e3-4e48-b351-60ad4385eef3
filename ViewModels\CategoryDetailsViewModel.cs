using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using LiveCharts;
using System.Windows.Controls;
using System.Windows.Input;
using LiveCharts.Wpf;
using POSSystem.Services;
using POSSystem.Models;
using POSSystem.ViewModels.Dashboard;

namespace POSSystem.ViewModels
{
    public class CategoryStatistics
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal Revenue { get; set; }
        public decimal Profit { get; set; }
        public decimal Margin { get; set; }
        public decimal ItemsSold { get; set; }
        public int ProductCount { get; set; }
    }

    public class CategoryDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private ObservableCollection<CategoryStatistics> _categories;
        private ObservableCollection<CategoryStatistics> _filteredCategories;
        private ObservableCollection<TimePeriod> _timePeriods;
        private TimePeriod _selectedTimePeriod;
        private string _searchText;
        private bool _isLoading;
        private SeriesCollection _revenueChartSeries;
        private SeriesCollection _profitChartSeries;
        private SeriesCollection _marginChartSeries;
        private SeriesCollection _itemsSoldChartSeries;
        private string[] _categoryLabels;
        private DateTime? _customStartDate;
        private DateTime? _customEndDate;
        private bool _isUsingCustomDateRange;
        private string _errorMessage;
        private string _dateRangeText;

        public CategoryDetailsViewModel(RefactoredDashboardViewModel dashboardViewModel)
        {
            _dashboardViewModel = dashboardViewModel;
            Categories = new ObservableCollection<CategoryStatistics>();
            FilteredCategories = new ObservableCollection<CategoryStatistics>();
            
            // Initialize periods
            TimePeriods = new ObservableCollection<TimePeriod>(_dashboardViewModel.QuickStatsPeriods);
            SelectedTimePeriod = _dashboardViewModel.SelectedGlobalPeriod;
            
            // Initialize commands
            ExportCommand = new RelayCommand<DataGrid>(Export);
            
            // Initialize chart formatters
            CurrencyFormatter = value => value.ToString("C0");
            PercentFormatter = value => value.ToString("0.0") + "%";
            NumberFormatter = value => value.ToString("N0");
        }
        
        public ObservableCollection<CategoryStatistics> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }
        
        public ObservableCollection<CategoryStatistics> FilteredCategories
        {
            get => _filteredCategories;
            set
            {
                _filteredCategories = value;
                OnPropertyChanged();
            }
        }
        
        public ObservableCollection<TimePeriod> TimePeriods
        {
            get => _timePeriods;
            set
            {
                _timePeriods = value;
                OnPropertyChanged();
            }
        }
        
        public TimePeriod SelectedTimePeriod
        {
            get => _selectedTimePeriod;
            set
            {
                _selectedTimePeriod = value;
                OnPropertyChanged();
                
                // Reload data when period changes
                _ = LoadDataAsync();
            }
        }
        
        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
                
                // Filter the categories based on search text
                ApplyFilter();
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }
        
        public SeriesCollection RevenueChartSeries
        {
            get => _revenueChartSeries;
            set
            {
                _revenueChartSeries = value;
                OnPropertyChanged();
            }
        }
        
        public SeriesCollection ProfitChartSeries
        {
            get => _profitChartSeries;
            set
            {
                _profitChartSeries = value;
                OnPropertyChanged();
            }
        }
        
        public SeriesCollection MarginChartSeries
        {
            get => _marginChartSeries;
            set
            {
                _marginChartSeries = value;
                OnPropertyChanged();
            }
        }
        
        public SeriesCollection ItemsSoldChartSeries
        {
            get => _itemsSoldChartSeries;
            set
            {
                _itemsSoldChartSeries = value;
                OnPropertyChanged();
            }
        }
        
        public string[] CategoryLabels
        {
            get => _categoryLabels;
            set
            {
                _categoryLabels = value;
                OnPropertyChanged();
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged();
            }
        }

        public string DateRangeText
        {
            get => _dateRangeText;
            set
            {
                _dateRangeText = value;
                OnPropertyChanged();
            }
        }
        
        public Func<double, string> CurrencyFormatter { get; }
        public Func<double, string> PercentFormatter { get; }
        public Func<double, string> NumberFormatter { get; }
        
        public ICommand ExportCommand { get; }

        #region INotifyPropertyChanged
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        #endregion
        
        /// <summary>
        /// Loads category statistics from the dashboard view model
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                // Use existing dashboard data if available, otherwise load it
                if (!_dashboardViewModel.IsProductPerformanceLoading)
                {
                    await _dashboardViewModel.LoadSectionAsync("ProductPerformance");
                }
                
                // Get products data from dashboard view model
                var products = _dashboardViewModel.TopProducts;
                
                if (products == null || products.Count == 0)
                {
                    Debug.WriteLine("No product data available for category statistics");
                    return;
                }
                
                // Get all categories from database
                var dbService = new DatabaseService();
                var allCategories = await Task.Run(() => dbService.GetAllCategories());
                
                // Process category statistics
                await Task.Run(() => {
                    // Group products by category
                    var categoryStats = products
                        .GroupBy(p => p.Category)
                        .Select(g => {
                            // Get the actual category object
                            var category = allCategories.FirstOrDefault(c => c.Name == g.Key);
                            
                            Debug.WriteLine($"Creating category stats for: '{g.Key}' (ID: {category?.Id ?? 0})");
                            
                            return new CategoryStatistics {
                                Id = category?.Id ?? 0,
                                Name = g.Key,
                                Revenue = g.Sum(p => p.Revenue),
                                Profit = g.Sum(p => p.Profit),
                                Margin = g.Sum(p => p.Revenue) > 0 
                                    ? Math.Round(g.Sum(p => p.Profit) / g.Sum(p => p.Revenue) * 100, 2) 
                                    : 0,
                                ItemsSold = g.Sum(p => p.ItemsSold),
                                ProductCount = g.Count()
                            };
                        })
                        .OrderByDescending(c => c.Revenue)
                        .ToList();
                    
                    Debug.WriteLine($"Generated {categoryStats.Count} category statistics entries");
                    foreach (var stat in categoryStats)
                    {
                        Debug.WriteLine($"Category: '{stat.Name}', Revenue: {stat.Revenue}, Products: {stat.ProductCount}");
                    }
                    
                    // Update UI collections
                    Application.Current.Dispatcher.Invoke(() => {
                        Categories = new ObservableCollection<CategoryStatistics>(categoryStats);
                        
                        // Apply filter to update filtered categories
                        ApplyFilter();
                    });
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading category data: {ex.Message}");
                ErrorMessage = $"Failed to load category data: {ex.Message}";
                MessageBox.Show(
                    $"Error loading category data: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        /// <summary>
        /// Applies the current search filter to categories
        /// </summary>
        private void ApplyFilter()
        {
            if (Categories == null)
                return;
                
            IEnumerable<CategoryStatistics> filtered = Categories;
            
            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var search = SearchText.ToLower().Trim();
                filtered = filtered.Where(c => 
                    c.Name.ToLower().Contains(search));
            }
            
            // Update filtered collection
            FilteredCategories = new ObservableCollection<CategoryStatistics>(filtered);
            
            // Update charts with filtered data
            UpdateCharts();
        }
        
        /// <summary>
        /// Updates chart data based on filtered categories
        /// </summary>
        private void UpdateCharts()
        {
            if (FilteredCategories == null || FilteredCategories.Count == 0)
            {
                // Create empty chart series
                RevenueChartSeries = new SeriesCollection();
                ProfitChartSeries = new SeriesCollection();
                MarginChartSeries = new SeriesCollection();
                ItemsSoldChartSeries = new SeriesCollection();
                CategoryLabels = new string[0];
                return;
            }
            
            // Take top 10 categories for charts
            var topCategories = FilteredCategories
                .OrderByDescending(c => c.Revenue)
                .Take(10)
                .ToList();
            
            // Create chart labels - for better display, ensure proper formatting
            CategoryLabels = topCategories.Select(c => TruncateLongName(c.Name)).ToArray();
            
            // Use a different chart type for better display of category names
            RevenueChartSeries = new SeriesCollection {
                new ColumnSeries {
                    Title = "Revenue",
                    Values = new ChartValues<decimal>(topCategories.Select(c => c.Revenue)),
                    Fill = System.Windows.Media.Brushes.DodgerBlue,
                    DataLabels = true,
                    FontSize = 11
                }
            };
            
            // Create profit chart series
            ProfitChartSeries = new SeriesCollection {
                new ColumnSeries {
                    Title = "Profit",
                    Values = new ChartValues<decimal>(topCategories.Select(c => c.Profit)),
                    Fill = System.Windows.Media.Brushes.ForestGreen,
                    DataLabels = true,
                    FontSize = 11
                }
            };
            
            // Create margin chart series
            MarginChartSeries = new SeriesCollection {
                new ColumnSeries {
                    Title = "Margin %",
                    Values = new ChartValues<decimal>(topCategories.Select(c => c.Margin)),
                    Fill = System.Windows.Media.Brushes.Orange,
                    DataLabels = true,
                    FontSize = 11
                }
            };
            
            // Create items sold chart series
            ItemsSoldChartSeries = new SeriesCollection {
                new ColumnSeries {
                    Title = "Items Sold",
                    Values = new ChartValues<decimal>(topCategories.Select(c => c.ItemsSold)),
                    Fill = System.Windows.Media.Brushes.Crimson,
                    DataLabels = true,
                    FontSize = 11
                }
            };
        }
        
        /// <summary>
        /// Truncates long category names for better display in charts
        /// </summary>
        private string TruncateLongName(string name)
        {
            return name.Length > 15 ? name.Substring(0, 12) + "..." : name;
        }
        
        /// <summary>
        /// Exports data to CSV
        /// </summary>
        private void Export(DataGrid dataGrid)
        {
            try
            {
                if (dataGrid == null || FilteredCategories == null || FilteredCategories.Count == 0)
                    return;
                    
                // Show saving dialog
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = "CategoryStatistics",
                    DefaultExt = ".csv",
                    Filter = "CSV documents (.csv)|*.csv"
                };
                
                if (dialog.ShowDialog() != true)
                    return;
                    
                // Export to CSV
                var lines = new List<string> {
                    // Header line
                    "Category,Revenue,Profit,Margin (%),Items Sold,Product Count"
                };
                
                // Data lines
                foreach (var category in FilteredCategories)
                {
                    lines.Add($"\"{category.Name}\",{category.Revenue},{category.Profit},{category.Margin},{category.ItemsSold},{category.ProductCount}");
                }
                
                // Write to file
                System.IO.File.WriteAllLines(dialog.FileName, lines);
                
                // Show success message
                MessageBox.Show(
                    "Data exported successfully",
                    "Export Completed",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error exporting data: {ex.Message}");
                MessageBox.Show(
                    $"Error exporting data: {ex.Message}",
                    "Export Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// Sets a custom date range and refreshes the data
        /// </summary>
        public async Task SetCustomDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            _customStartDate = startDate;
            _customEndDate = endDate;
            _isUsingCustomDateRange = true;
            
            // Format date range for display
            DateRangeText = $"{startDate:MMM d, yyyy} - {endDate:MMM d, yyyy}";
            
            // Reload data with the custom date range
            await LoadDataAsync();
        }

        /// <summary>
        /// Navigates to a specific date and refreshes the data
        /// </summary>
        public async Task NavigateToDateAsync(int daysOffset)
        {
            try
            {
                // Calculate the new date(s)
                DateTime baseDate;
                
                if (_customStartDate.HasValue && daysOffset != 0)
                {
                    // Use current custom date as base
                    baseDate = _customStartDate.Value;
                }
                else if (daysOffset == 0)
                {
                    // Today
                    baseDate = DateTime.Today;
                } 
                else 
                {
                    // Default to today
                    baseDate = DateTime.Today;
                }
                
                // Calculate new date
                DateTime newDate = baseDate.AddDays(daysOffset);
                
                // Set as custom date range (single day)
                _customStartDate = newDate;
                _customEndDate = newDate.AddDays(1).AddSeconds(-1); // End of the same day
                _isUsingCustomDateRange = true;
                
                // Update date range text for display
                string displayName = $"{newDate:MMM d, yyyy}";
                
                // Create a custom time period
                var customPeriod = new TimePeriod
                {
                    DisplayName = displayName,
                    Type = TimePeriodType.Custom,
                    Days = 1
                };
                
                // Update selected period
                SelectedTimePeriod = customPeriod;
                
                // Reload data for the new date
                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to date: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Simple relay command implementation
    /// </summary>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Predicate<T> _canExecute;
        
        public RelayCommand(Action<T> execute, Predicate<T> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        
        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute((T)parameter);
        }
        
        public void Execute(object parameter)
        {
            _execute((T)parameter);
        }
        
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
    }
} 