using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.Helpers
{
    /// <summary>
    /// ✅ PERFORMANCE OPTIMIZATION: Helper for managing UI thread operations efficiently
    /// </summary>
    public static class UIThreadHelper
    {
        /// <summary>
        /// Execute heavy computation on background thread, then update UI
        /// </summary>
        public static async Task ExecuteWithUIUpdate<T>(
            Func<Task<T>> backgroundWork,
            Action<T> uiUpdate,
            Action<Exception> errorHandler = null)
        {
            try
            {
                // Execute heavy work on background thread
                var result = await Task.Run(backgroundWork);
                
                // Update UI on main thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    uiUpdate(result);
                }, DispatcherPriority.Normal);
            }
            catch (Exception ex)
            {
                if (errorHandler != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        errorHandler(ex);
                    });
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"UIThreadHelper error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Execute action on UI thread with proper priority
        /// </summary>
        public static async Task RunOnUIThread(Action action, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (Application.Current.Dispatcher.CheckAccess())
            {
                action();
            }
            else
            {
                await Application.Current.Dispatcher.InvokeAsync(action, priority);
            }
        }

        /// <summary>
        /// Execute function on UI thread and return result
        /// </summary>
        public static async Task<T> RunOnUIThread<T>(Func<T> function, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (Application.Current.Dispatcher.CheckAccess())
            {
                return function();
            }
            else
            {
                return await Application.Current.Dispatcher.InvokeAsync(function, priority);
            }
        }

        /// <summary>
        /// Yield control to allow UI updates during long operations
        /// </summary>
        public static async Task YieldToUI()
        {
            await Application.Current.Dispatcher.InvokeAsync(() => { }, DispatcherPriority.Background);
        }

        /// <summary>
        /// Execute action with progress reporting to prevent UI freezing
        /// </summary>
        public static async Task ExecuteWithProgress<T>(
            Func<IProgress<int>, Task<T>> backgroundWork,
            Action<T> onComplete,
            Action<int> onProgress = null,
            Action<Exception> onError = null)
        {
            try
            {
                var progress = new Progress<int>(percent =>
                {
                    Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        onProgress?.Invoke(percent);
                    }, DispatcherPriority.Background);
                });

                var result = await Task.Run(() => backgroundWork(progress));
                
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    onComplete(result);
                }, DispatcherPriority.Normal);
            }
            catch (Exception ex)
            {
                if (onError != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        onError(ex);
                    });
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ExecuteWithProgress error: {ex.Message}");
                }
            }
        }
    }
}
