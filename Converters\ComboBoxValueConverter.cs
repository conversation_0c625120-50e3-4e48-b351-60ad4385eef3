using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class ComboBoxValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                // Find the ComboBoxItem with the matching Tag value
                System.Collections.IEnumerable items = targetType.GetProperty("Items")?.GetValue(parameter) as System.Collections.IEnumerable;
                if (items != null)
                {
                    foreach (ComboBoxItem item in items)
                    {
                        if (item.Tag != null && item.Tag.ToString() == stringValue)
                        {
                            return item;
                        }
                    }
                }
                
                // If no matching Tag is found, try to match by content
                foreach (ComboBoxItem item in items)
                {
                    if (item.Content?.ToString() == stringValue)
                    {
                        return item;
                    }
                }
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ComboBoxItem comboBoxItem)
            {
                // Prefer Tag if available for consistent values across languages
                if (comboBoxItem.Tag != null)
                {
                    return comboBoxItem.Tag.ToString();
                }
                // Fall back to content if Tag is not available
                return comboBoxItem.Content?.ToString();
            }
            return null;
        }
    }
} 