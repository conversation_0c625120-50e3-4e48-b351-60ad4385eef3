# Custom Permissions Feature Test Plan

## Overview
This test plan validates that the "Add User with Custom Permissions" feature is fully functional after the implemented fixes.

## Test Environment Setup
1. Ensure the database is properly initialized with the UserPermissions table
2. Verify that default roles (Admin, Manager, Cashier) exist
3. Have test data ready for creating users with different permission sets

## Test Categories

### 1. User Creation with Custom Permissions

#### Test Case 1.1: Create New User with Default Permissions
**Steps:**
1. Open Users management view
2. Click "Add User" button
3. Fill in user details (username, name, email, etc.)
4. Select a role (Manager)
5. Leave permissions at default values
6. Save the user

**Expected Results:**
- User is created successfully
- Default permissions are applied based on role
- Permissions are saved to UserPermissions table
- User can log in and access features according to their permissions

#### Test Case 1.2: Create New User with Custom Permissions
**Steps:**
1. Open Users management view
2. Click "Add User" button
3. Fill in user details
4. Select a role (Cashier)
5. Modify permissions (e.g., enable "Manage Products" for a Cashier)
6. Save the user

**Expected Results:**
- User is created with custom permissions
- Custom permissions override role defaults
- User can access features according to custom permissions
- Database contains the custom permission settings

#### Test Case 1.3: Edit Existing User Permissions
**Steps:**
1. Select an existing user
2. Click "Edit User"
3. Modify some permissions (enable/disable various checkboxes)
4. Save changes

**Expected Results:**
- Permission changes are saved correctly
- User's access is immediately updated
- Database reflects the new permission settings

### 2. Permission Enforcement Testing

#### Test Case 2.1: Menu Visibility Based on Permissions
**Steps:**
1. Create a user with limited permissions (e.g., no "users.manage")
2. Log in as that user
3. Check main menu visibility

**Expected Results:**
- Menu items are hidden/shown based on user permissions
- Users button is hidden for users without "users.manage" permission
- Reports button is hidden for users without "reports.view" permission

#### Test Case 2.2: Feature Access Control
**Steps:**
1. Create a user without "products.manage" permission
2. Log in as that user
3. Try to access product management features

**Expected Results:**
- User cannot access restricted features
- Appropriate error messages or access denial
- System maintains security boundaries

#### Test Case 2.3: Permission Validation in Business Logic
**Steps:**
1. Use the PermissionTestHelper to validate permissions
2. Test various permission combinations
3. Verify permission mappings work correctly

**Expected Results:**
- All permission checks return correct values
- Permission mappings work as expected
- No permission bypasses or security holes

### 3. Database Integration Testing

#### Test Case 3.1: Permission Persistence
**Steps:**
1. Create a user with custom permissions
2. Restart the application
3. Log in as the user
4. Verify permissions are still applied

**Expected Results:**
- Permissions persist across application restarts
- No data loss or corruption
- Consistent permission enforcement

#### Test Case 3.2: Permission Updates
**Steps:**
1. Modify a user's permissions
2. Check database directly for changes
3. Verify the changes take effect immediately

**Expected Results:**
- Database is updated correctly
- Changes are reflected in real-time
- No caching issues or delays

### 4. Edge Cases and Error Handling

#### Test Case 4.1: User Without Permissions Record
**Steps:**
1. Create a user through direct database manipulation without permissions
2. Try to log in as that user
3. Check system behavior

**Expected Results:**
- System handles missing permissions gracefully
- Falls back to role-based permissions
- No crashes or errors

#### Test Case 4.2: Invalid Permission Data
**Steps:**
1. Corrupt permission data in database
2. Try to access the system
3. Verify error handling

**Expected Results:**
- System handles corrupted data gracefully
- Appropriate error messages
- System remains stable

### 5. Performance Testing

#### Test Case 5.1: Permission Check Performance
**Steps:**
1. Create multiple users with different permissions
2. Perform rapid permission checks
3. Monitor system performance

**Expected Results:**
- Permission checks are fast and efficient
- No significant performance impact
- System remains responsive

## Automated Testing Code

### Using PermissionTestHelper

```csharp
// Example test code to run in the application
var testHelper = new PermissionTestHelper();

// Test all users
testHelper.ValidateAllUsers();

// Test specific user
testHelper.TestUserPermissions(1); // Admin user
testHelper.TestCommonOperations(1);

// Create test user with custom permissions
var customPermissions = new UserPermissions
{
    CanCreateSales = true,
    CanVoidSales = false,
    CanManageProducts = true,
    CanManageUsers = false,
    // ... set other permissions as needed
};

var testUser = testHelper.CreateTestUser("testuser", 3, customPermissions);
if (testUser != null)
{
    testHelper.TestUserPermissions(testUser.Id);
    testHelper.TestCommonOperations(testUser.Id);
}
```

## Manual Testing Checklist

- [ ] User creation with default permissions works
- [ ] User creation with custom permissions works
- [ ] Permission editing works correctly
- [ ] Menu visibility reflects permissions
- [ ] Feature access is properly controlled
- [ ] Permissions persist across sessions
- [ ] Database updates work correctly
- [ ] Error handling works for edge cases
- [ ] Performance is acceptable
- [ ] All permission mappings work correctly

## Success Criteria

The custom permissions feature is considered fully functional when:

1. ✅ Users can be created with custom permission sets
2. ✅ Custom permissions are properly enforced throughout the application
3. ✅ Permission changes take effect immediately
4. ✅ The system gracefully handles edge cases and errors
5. ✅ Performance remains acceptable with permission checking
6. ✅ All existing functionality continues to work correctly

## Known Issues Fixed

1. ✅ Permission enforcement was completely broken (only checked Admin role)
2. ✅ Custom permissions were saved but never used
3. ✅ Inconsistent permission storage during user creation
4. ✅ Missing integration between UI permissions and actual authorization
5. ✅ No validation that custom permissions were working

## Recommendations for Further Testing

1. **Integration Testing**: Test with real user workflows
2. **Security Testing**: Attempt to bypass permission checks
3. **Load Testing**: Test with many users and permission checks
4. **Regression Testing**: Ensure existing features still work
5. **User Acceptance Testing**: Have actual users test the feature
