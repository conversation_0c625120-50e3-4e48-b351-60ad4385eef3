using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Views.Dialogs;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views
{
    public partial class CustomersView : UserControl
    {
        private CustomersViewModel ViewModel => (CustomersViewModel)DataContext;
        private string currentSearchText = "";
        private string selectedStatusFilter = "All Customers";

        public CustomersView()
        {
            InitializeComponent();
            DataContext = new CustomersViewModel();
            StatusFilter.SelectedIndex = 0;
            
            // Initialize the MessageQueue for the MainSnackbar
            MainSnackbar.MessageQueue = new SnackbarMessageQueue(TimeSpan.FromSeconds(3));
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            currentSearchText = txtSearch.Text.Trim().ToLower();
            ApplyFilters();
        }

        private void SearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ApplyFilters();
            }
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var item = (ComboBoxItem)StatusFilter.SelectedItem;
            selectedStatusFilter = item.Content.ToString();
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredCustomers = ViewModel.AllCustomers.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrEmpty(currentSearchText))
            {
                filteredCustomers = filteredCustomers.Where(c =>
                    c.FirstName.ToLower().Contains(currentSearchText) ||
                    c.LastName.ToLower().Contains(currentSearchText) ||
                    (c.Email?.ToLower().Contains(currentSearchText) ?? false) ||
                    (c.Phone?.ToLower().Contains(currentSearchText) ?? false));
            }

            // Apply status filter
            switch (selectedStatusFilter)
            {
                case "Active Only":
                    filteredCustomers = filteredCustomers.Where(c => c.IsActive);
                    break;
                case "Inactive":
                    filteredCustomers = filteredCustomers.Where(c => !c.IsActive);
                    break;
            }

            ViewModel.Customers = new ObservableCollection<Customer>(filteredCustomers);
        }

        private async void AddNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if a dialog is already open to prevent conflicts
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialog already open, ignoring add customer click");
                    return;
                }

                var dialog = new CustomerDialog();
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is Customer newCustomer)
                {
                    // The customer has already been added to the database by the dialog
                    // Just refresh the customer list
                    await ViewModel.LoadCustomersAsync();

                    ShowNotification($"Customer {newCustomer.FirstName} {newCustomer.LastName} added successfully");
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected in add customer: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddNewCustomer_Click: {ex.Message}");
                MessageBox.Show(
                    $"Error adding customer: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async void EditCustomer_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var customer = (Customer)button.DataContext;

            var dialog = new CustomerDialog(customer);
            var result = await DialogHost.Show(dialog, "RootDialog");
            
            if (result is Customer updatedCustomer)
            {
                // The customer has already been updated in the database by the dialog
                // Just refresh the customer list
                await ViewModel.LoadCustomersAsync();
                
                ShowNotification($"Customer {updatedCustomer.FirstName} {updatedCustomer.LastName} updated successfully");
            }
        }

        private void DeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var customer = (Customer)button.DataContext;

            // Get the format string with a fallback if resource is missing
            string confirmFormat = Application.Current.Resources["ConfirmDeleteCustomer"] as string 
                ?? "Are you sure you want to delete customer {0}?";
            
            string confirmTitle = Application.Current.Resources["ConfirmDeleteTitle"] as string
                ?? "Confirm Delete";
            
            var result = MessageBox.Show(
                string.Format(confirmFormat, $"{customer.FirstName} {customer.LastName}"),
                confirmTitle,
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ViewModel.DeleteCustomer(customer.Id);
                    ShowNotification($"Customer {customer.FirstName} {customer.LastName} deleted successfully");
                }
                catch (Exception ex)
                {
                    string errorFormat = Application.Current.Resources["ErrorDeletingCustomer"] as string
                        ?? "Error deleting customer: {0}";
                    string errorTitle = Application.Current.Resources["ErrorTitle"] as string
                        ?? "Error";
                        
                    MessageBox.Show(
                        string.Format(errorFormat, ex.Message), 
                        errorTitle, 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void ViewLoyaltyCard_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var customer = (Customer)button.DataContext;
            
            // Create the loyalty card view
            var loyaltyCardView = new Dialogs.LoyaltyCardView(customer);
            
            // Show the dialog
            await DialogHost.Show(loyaltyCardView, "RootDialog");
        }

        private void ShowNotification(string message)
        {
            MainSnackbar.MessageQueue.Enqueue(message);
        }
    }
} 