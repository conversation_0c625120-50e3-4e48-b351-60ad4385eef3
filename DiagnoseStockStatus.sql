-- Diagnose Stock Status for Product 483
-- Run this in SQLite DB Browser to check stock status calculation

-- Check the specific product details
SELECT 'Product 483 Details:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    ReorderPoint,
    IsWeightBased,
    Type,
    IsActive,
    TrackBatches
FROM Products 
WHERE Id = 483;

-- Check if there are any batch records for this product
SELECT 'Batch Records for Product 483:' as Info;
SELECT 
    Id,
    ProductId,
    BatchNumber,
    Quantity,
    ExpiryDate,
    IsActive
FROM BatchStock 
WHERE ProductId = 483;

-- Calculate what the stock status should be
SELECT 'Stock Status Calculation:' as Info;
SELECT 
    Id,
    Name,
    StockQuantity,
    MinimumStock,
    CASE 
        WHEN Type = 1 THEN 'Service (no stock tracking)'
        WHEN StockQuantity = 0 THEN 'Out of Stock'
        WHEN StockQuantity <= MinimumStock AND StockQuantity > 0 THEN 'Low Stock'
        WHEN StockQuantity > 0 THEN 'In Stock'
        ELSE 'Unknown'
    END as CalculatedStockStatus,
    CASE 
        WHEN Type = 1 THEN 'N/A (Service)'
        WHEN StockQuantity = 0 THEN 'TRUE (Out of Stock)'
        ELSE 'FALSE (Has Stock)'
    END as ShouldShowOutOfStock,
    CASE 
        WHEN Type = 1 THEN 'N/A (Service)'
        WHEN StockQuantity <= MinimumStock AND StockQuantity > 0 THEN 'TRUE (Low Stock)'
        ELSE 'FALSE (Normal Stock)'
    END as ShouldShowLowStock
FROM Products 
WHERE Id = 483;

-- Check all weight-based products and their stock status
SELECT 'All Weight-Based Products Stock Status:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    Type,
    CASE 
        WHEN Type = 1 THEN 'Service'
        WHEN StockQuantity = 0 THEN 'Out of Stock'
        WHEN StockQuantity <= MinimumStock AND StockQuantity > 0 THEN 'Low Stock'
        WHEN StockQuantity > 0 THEN 'In Stock'
        ELSE 'Unknown'
    END as StockStatus
FROM Products 
WHERE IsWeightBased = 1 AND IsActive = 1
ORDER BY Id;

-- Check if there are any products that should be in stock but might show as out of stock
SELECT 'Products with Stock > 0 but might show as Out of Stock:' as Info;
SELECT 
    Id,
    Name,
    SKU,
    StockQuantity,
    MinimumStock,
    IsWeightBased,
    Type,
    TrackBatches
FROM Products 
WHERE StockQuantity > 0 
  AND IsActive = 1
  AND Type != 1  -- Not a service
ORDER BY IsWeightBased DESC, Id;
