using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;

namespace POSSystem.Views.Controls
{
    /// <summary>
    /// Interaction logic for NotificationBadge.xaml
    /// </summary>
    public partial class NotificationBadge : UserControl, INotifyPropertyChanged
    {
        public NotificationBadge()
        {
            InitializeComponent();
            DataContext = this;
        }

        #region Dependency Properties

        public static readonly DependencyProperty NotificationCountProperty =
            DependencyProperty.Register(nameof(NotificationCount), typeof(int), typeof(NotificationBadge),
                new PropertyMetadata(0, OnNotificationCountChanged));

        public static readonly DependencyProperty HasNewNotificationsProperty =
            DependencyProperty.Register(nameof(HasNewNotifications), typeof(bool), typeof(NotificationBadge),
                new PropertyMetadata(false));

        public static readonly DependencyProperty MainContentProperty =
            DependencyProperty.Register(nameof(MainContent), typeof(object), typeof(NotificationBadge),
                new PropertyMetadata(null));

        public int NotificationCount
        {
            get => (int)GetValue(NotificationCountProperty);
            set => SetValue(NotificationCountProperty, value);
        }

        public bool HasNewNotifications
        {
            get => (bool)GetValue(HasNewNotificationsProperty);
            set => SetValue(HasNewNotificationsProperty, value);
        }

        public object MainContent
        {
            get => GetValue(MainContentProperty);
            set => SetValue(MainContentProperty, value);
        }

        #endregion

        #region Event Handlers

        private static void OnNotificationCountChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is NotificationBadge badge)
            {
                var newCount = (int)e.NewValue;
                var oldCount = (int)e.OldValue;
                
                // Set HasNewNotifications if count increased
                if (newCount > oldCount && newCount > 0)
                {
                    badge.HasNewNotifications = true;
                    
                    // Reset HasNewNotifications after animation
                    badge.Dispatcher.BeginInvoke(new System.Action(() =>
                    {
                        System.Threading.Tasks.Task.Delay(2000).ContinueWith(_ =>
                        {
                            badge.Dispatcher.Invoke(() => badge.HasNewNotifications = false);
                        });
                    }));
                }
                else if (newCount == 0)
                {
                    badge.HasNewNotifications = false;
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
