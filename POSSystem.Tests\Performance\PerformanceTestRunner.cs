using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;
using POSSystem.Services.Monitoring;
using POSSystem.Utilities;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Performance Test Runner
    /// 
    /// Orchestrates the execution of all performance tests and generates comprehensive reports.
    /// This class provides:
    /// 1. Automated execution of all performance test suites
    /// 2. Comprehensive performance reporting with detailed metrics
    /// 3. Performance trend analysis and recommendations
    /// 4. Integration with existing debugging infrastructure
    /// 5. File-based output for detailed analysis
    /// </summary>
    public class PerformanceTestRunner : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly ComprehensivePerformanceTestSuite _testSuite;
        private readonly string _reportPath;
        private readonly List<PerformanceTestResult> _allResults;
        private readonly Stopwatch _totalTestTime;

        public PerformanceTestRunner(ITestOutputHelper output)
        {
            _output = output;
            _testSuite = new ComprehensivePerformanceTestSuite(output);
            _allResults = new List<PerformanceTestResult>();
            _totalTestTime = Stopwatch.StartNew();

            // Set up performance report file
            var reportsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceReports");
            Directory.CreateDirectory(reportsDirectory);
            _reportPath = Path.Combine(reportsDirectory, $"performance_report_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.html");
        }

        [Fact]
        public async Task RunComprehensivePerformanceTests()
        {
            _output.WriteLine("=".PadRight(100, '='));
            _output.WriteLine("STARTING COMPREHENSIVE POS SYSTEM PERFORMANCE TESTS");
            _output.WriteLine("=".PadRight(100, '='));

            try
            {
                // Execute all performance test suites
                await ExecuteProductScanningTests();
                await ExecuteCartManagementTests();
                await ExecutePaymentProcessingTests();
                await ExecuteSalesCompletionTests();
                await ExecuteDashboardLoadingTests();
                await ExecuteChartGenerationTests();

                // Generate comprehensive performance report
                await GeneratePerformanceReport();

                // Analyze results and provide recommendations
                AnalyzePerformanceResults();

                _output.WriteLine("=".PadRight(100, '='));
                _output.WriteLine("COMPREHENSIVE PERFORMANCE TESTS COMPLETED SUCCESSFULLY");
                _output.WriteLine($"Total Test Duration: {_totalTestTime.Elapsed:hh\\:mm\\:ss}");
                _output.WriteLine($"Performance Report: {_reportPath}");
                _output.WriteLine("=".PadRight(100, '='));
            }
            catch (Exception ex)
            {
                _output.WriteLine($"ERROR: Performance tests failed: {ex.Message}");
                throw;
            }
        }

        private async Task ExecuteProductScanningTests()
        {
            _output.WriteLine("Executing Product Scanning Performance Tests...");
            
            var coreTests = new CorePOSOperationsPerformanceTests(_testSuite, _output);
            
            try
            {
                await coreTests.ProductScanningPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Product Scanning Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Product Scanning Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "ProductScanningPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task ExecuteCartManagementTests()
        {
            _output.WriteLine("Executing Cart Management Performance Tests...");
            
            var coreTests = new CorePOSOperationsPerformanceTests(_testSuite, _output);
            
            try
            {
                await coreTests.CartManagementPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Cart Management Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Cart Management Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "CartManagementPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task ExecutePaymentProcessingTests()
        {
            _output.WriteLine("Executing Payment Processing Performance Tests...");
            
            var coreTests = new CorePOSOperationsPerformanceTests(_testSuite, _output);
            
            try
            {
                await coreTests.PaymentProcessingPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Payment Processing Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Payment Processing Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "PaymentProcessingPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task ExecuteSalesCompletionTests()
        {
            _output.WriteLine("Executing Sales Completion Performance Tests...");
            
            var coreTests = new CorePOSOperationsPerformanceTests(_testSuite, _output);
            
            try
            {
                await coreTests.SalesCompletionPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Sales Completion Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Sales Completion Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "SalesCompletionPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task ExecuteDashboardLoadingTests()
        {
            _output.WriteLine("Executing Dashboard Loading Performance Tests...");
            
            var dashboardTests = new DashboardPerformanceTests(_testSuite, _output);
            
            try
            {
                await dashboardTests.DashboardLoadingPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Dashboard Loading Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Dashboard Loading Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "DashboardLoadingPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task ExecuteChartGenerationTests()
        {
            _output.WriteLine("Executing Chart Generation Performance Tests...");
            
            var dashboardTests = new DashboardPerformanceTests(_testSuite, _output);
            
            try
            {
                await dashboardTests.ChartGenerationPerformance_ShouldMeetPerformanceTargets();
                _output.WriteLine("✅ Chart Generation Tests: PASSED");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"❌ Chart Generation Tests: FAILED - {ex.Message}");
                _allResults.Add(new PerformanceTestResult
                {
                    TestName = "ChartGenerationPerformance",
                    Passed = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                });
            }
        }

        private async Task GeneratePerformanceReport()
        {
            _output.WriteLine("Generating comprehensive performance report...");

            try
            {
                var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();
                var summary = performanceMonitor.GetSummary();

                var reportHtml = GenerateHtmlReport(summary);
                await File.WriteAllTextAsync(_reportPath, reportHtml);

                // Also generate a text summary for console output
                var textSummary = GenerateTextSummary(summary);
                _output.WriteLine(textSummary);

                _output.WriteLine($"📊 Detailed performance report saved to: {_reportPath}");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Warning: Could not generate performance report: {ex.Message}");
            }
        }

        private string GenerateHtmlReport(PerformanceSummary summary)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html><head>");
            html.AppendLine("<title>POS System Performance Test Report</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine(".header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }");
            html.AppendLine(".metric { margin: 10px 0; padding: 10px; border-left: 4px solid #007acc; }");
            html.AppendLine(".passed { border-left-color: #28a745; }");
            html.AppendLine(".failed { border-left-color: #dc3545; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine("</style>");
            html.AppendLine("</head><body>");

            // Header
            html.AppendLine("<div class='header'>");
            html.AppendLine("<h1>POS System Performance Test Report</h1>");
            html.AppendLine($"<p><strong>Generated:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine($"<p><strong>Test Duration:</strong> {_totalTestTime.Elapsed:hh\\:mm\\:ss}</p>");
            html.AppendLine("</div>");

            // Summary metrics
            html.AppendLine("<h2>Performance Summary</h2>");
            html.AppendLine($"<div class='metric'><strong>Total Operations:</strong> {summary.TotalOperations}</div>");
            html.AppendLine($"<div class='metric'><strong>Average Response Time:</strong> {summary.AverageResponseTime:F2}ms</div>");
            html.AppendLine($"<div class='metric'><strong>Peak Memory Usage:</strong> {summary.PeakMemoryUsageMB:F2}MB</div>");
            html.AppendLine($"<div class='metric'><strong>Total Alerts:</strong> {summary.RecentAlerts.Count}</div>");

            // Slowest operations
            if (summary.SlowestOperations.Any())
            {
                html.AppendLine("<h2>Slowest Operations</h2>");
                html.AppendLine("<table>");
                html.AppendLine("<tr><th>Operation</th><th>Average Time (ms)</th><th>Count</th></tr>");
                
                foreach (var op in summary.SlowestOperations.Take(10))
                {
                    html.AppendLine($"<tr><td>{op.OperationName}</td><td>{op.AverageMs:F2}</td><td>{op.Count}</td></tr>");
                }
                
                html.AppendLine("</table>");
            }

            // Performance alerts
            if (summary.RecentAlerts.Any())
            {
                html.AppendLine("<h2>Performance Alerts</h2>");
                html.AppendLine("<table>");
                html.AppendLine("<tr><th>Timestamp</th><th>Operation</th><th>Threshold (ms)</th><th>Actual (ms)</th></tr>");
                
                foreach (var alert in summary.RecentAlerts.Take(20))
                {
                    html.AppendLine($"<tr><td>{alert.Timestamp:HH:mm:ss}</td><td>{alert.OperationName}</td><td>{alert.ThresholdMs}</td><td>{alert.ActualMs}</td></tr>");
                }
                
                html.AppendLine("</table>");
            }

            // Test results
            if (_allResults.Any())
            {
                html.AppendLine("<h2>Test Results</h2>");
                html.AppendLine("<table>");
                html.AppendLine("<tr><th>Test Name</th><th>Status</th><th>Duration</th><th>Error Message</th></tr>");
                
                foreach (var result in _allResults)
                {
                    var statusClass = result.Passed ? "passed" : "failed";
                    var status = result.Passed ? "✅ PASSED" : "❌ FAILED";
                    html.AppendLine($"<tr class='{statusClass}'><td>{result.TestName}</td><td>{status}</td><td>{result.Duration.TotalMilliseconds:F2}ms</td><td>{result.ErrorMessage ?? ""}</td></tr>");
                }
                
                html.AppendLine("</table>");
            }

            html.AppendLine("</body></html>");
            return html.ToString();
        }

        private string GenerateTextSummary(PerformanceSummary summary)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("📊 PERFORMANCE TEST SUMMARY");
            sb.AppendLine("=".PadRight(50, '='));
            sb.AppendLine($"Total Operations: {summary.TotalOperations}");
            sb.AppendLine($"Average Response Time: {summary.AverageResponseTime:F2}ms");
            sb.AppendLine($"Peak Memory Usage: {summary.PeakMemoryUsageMB:F2}MB");
            sb.AppendLine($"Performance Alerts: {summary.RecentAlerts.Count}");
            
            if (summary.SlowestOperations.Any())
            {
                sb.AppendLine("\n🐌 SLOWEST OPERATIONS:");
                foreach (var op in summary.SlowestOperations.Take(5))
                {
                    sb.AppendLine($"  • {op.OperationName}: {op.AverageMs:F2}ms (count: {op.Count})");
                }
            }

            var passedTests = _allResults.Count(r => r.Passed);
            var totalTests = _allResults.Count;
            sb.AppendLine($"\n✅ TEST RESULTS: {passedTests}/{totalTests} tests passed");

            return sb.ToString();
        }

        private void AnalyzePerformanceResults()
        {
            _output.WriteLine("\n🔍 PERFORMANCE ANALYSIS & RECOMMENDATIONS:");
            
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();
            var summary = performanceMonitor.GetSummary();

            // Analyze response times
            if (summary.AverageResponseTime > 100)
            {
                _output.WriteLine("⚠️  Average response time is above 100ms - consider optimizing database queries");
            }
            else
            {
                _output.WriteLine("✅ Average response time is within acceptable limits");
            }

            // Analyze memory usage
            if (summary.PeakMemoryUsageMB > 100)
            {
                _output.WriteLine("⚠️  Peak memory usage is above 100MB - consider implementing memory optimization");
            }
            else
            {
                _output.WriteLine("✅ Memory usage is within acceptable limits");
            }

            // Analyze alerts
            if (summary.RecentAlerts.Count > 10)
            {
                _output.WriteLine($"⚠️  {summary.RecentAlerts.Count} performance alerts detected - review slowest operations");
            }
            else
            {
                _output.WriteLine("✅ Performance alerts are within acceptable limits");
            }

            // Provide specific recommendations
            _output.WriteLine("\n💡 RECOMMENDATIONS:");
            _output.WriteLine("1. Monitor database query performance regularly");
            _output.WriteLine("2. Implement caching for frequently accessed data");
            _output.WriteLine("3. Consider pagination for large data sets");
            _output.WriteLine("4. Optimize chart generation algorithms");
            _output.WriteLine("5. Use background processing for non-critical operations");
        }

        public void Dispose()
        {
            _totalTestTime?.Stop();
            _testSuite?.Dispose();
        }
    }
}
