using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using POSSystem.Models;
using POSSystem.Services;
using System.Linq;
using System;

namespace POSSystem.ViewModels
{
    public class BatchStockViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private readonly Product _product;
        private ObservableCollection<BatchStock> _batches;

        public Product Product => _product;

        public ObservableCollection<BatchStock> Batches
        {
            get => _batches;
            set
            {
                _batches = value;
                OnPropertyChanged();
                UpdateStats();
            }
        }

        public decimal TotalStock => Batches?.Sum(b => b.Quantity) ?? 0m;
        
        public decimal AverageCost => Batches?.Any() == true 
            ? Batches.Sum(b => b.Quantity * b.PurchasePrice) / TotalStock 
            : 0;
        
        public int ExpiringSoon => Batches?.Count(b => 
            b.ExpiryDate.HasValue && 
            b.ExpiryDate.Value.Date <= DateTime.Now.AddDays(30)) ?? 0;

        public BatchStockViewModel(Product product, DatabaseService dbService)
        {
            _product = product;
            _dbService = dbService;
            LoadBatches();
        }

        public void LoadBatches()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Loading batches for product '{_product.Name}' (ID: {_product.Id})");
                var batches = _dbService.GetBatchesForProduct(_product.Id);
                Batches = new ObservableCollection<BatchStock>(batches);
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Loaded {Batches.Count} batches with total quantity: {TotalStock}");

                // Debug each batch's selling price
                foreach (var batch in batches)
                {
                    System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Batch {batch.BatchNumber}: ID={batch.Id}, Quantity={batch.Quantity}, PurchasePrice={batch.PurchasePrice}, SellingPrice={batch.SellingPrice}");
                }

                // Specifically look for the most recent batch
                var mostRecentBatch = batches.OrderByDescending(b => b.Id).FirstOrDefault();
                if (mostRecentBatch != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Most recent batch: {mostRecentBatch.BatchNumber} (ID: {mostRecentBatch.Id}) has SellingPrice: {mostRecentBatch.SellingPrice}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error loading batches: {ex.Message}");
                Batches = new ObservableCollection<BatchStock>();
            }
        }

        private void UpdateStats()
        {
            OnPropertyChanged(nameof(TotalStock));
            OnPropertyChanged(nameof(AverageCost));
            OnPropertyChanged(nameof(ExpiringSoon));
        }

        public void AddBatch(BatchStock batch)
        {
            try
            {
                batch.ProductId = _product.Id;
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Adding batch: {batch.BatchNumber}, Quantity: {batch.Quantity} for product ID: {_product.Id}");
                _dbService.AddBatchStock(batch);
                LoadBatches();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Error adding batch: {ex.Message}");
                throw;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 