<UserControl
    x:Class="POSSystem.Views.InvoicesView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
    xmlns:local="clr-namespace:POSSystem.Views"
    xmlns:vm="clr-namespace:POSSystem.ViewModels"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:converters="clr-namespace:POSSystem.Converters"
    TextElement.Foreground="{DynamicResource MaterialDesignBody}"
    TextElement.FontWeight="Regular"
    TextElement.FontSize="13"
    TextOptions.TextFormattingMode="Ideal" 
    TextOptions.TextRenderingMode="Auto"
    Background="{DynamicResource MaterialDesignPaper}"
    FontFamily="{DynamicResource MaterialDesignFont}"
    mc:Ignorable="d" 
    d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Custom colors -->
            <SolidColorBrush x:Key="PrimaryColor" Color="#1976D2"/>
            <SolidColorBrush x:Key="SecondaryColor" Color="#FF5722"/>
            <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningColor" Color="#FFC107"/>
            <SolidColorBrush x:Key="ErrorColor" Color="#F44336"/>
            <SolidColorBrush x:Key="LightGrayBrush" Color="#F5F5F5"/>
            
            <!-- Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InvoiceStatusToBrushConverter x:Key="StatusToBrushConverter" />
            <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
            <converters:StringToIconConverter x:Key="StringToIconConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16" materialDesign:ShadowAssist.ShadowDepth="Depth1">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon Kind="FileDocument" Width="32" Height="32" VerticalAlignment="Center"/>
                    <TextBlock Text="{DynamicResource InvoiceManagement}" FontSize="24" FontWeight="Medium" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
                <Button 
                    Command="{Binding CreateInvoiceCommand}" 
                    Style="{StaticResource MaterialDesignRaisedButton}" 
                    Background="{StaticResource PrimaryColor}"
                    Foreground="White"
                    HorizontalAlignment="Right"
                    materialDesign:ButtonAssist.CornerRadius="4"
                    Padding="8,4"
                    ToolTip="{DynamicResource CreateNewInvoice}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0" />
                        <TextBlock Text="{DynamicResource CreateInvoice}" />
                    </StackPanel>
                </Button>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- Filters -->
        <materialDesign:Card Grid.Row="1" Margin="16,16,16,8" Padding="16" Background="{DynamicResource MaterialDesignBackground}" materialDesign:ShadowAssist.ShadowDepth="Depth1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <DockPanel Grid.Column="0" Margin="0,0,16,0">
                    <materialDesign:PackIcon Kind="Magnify" Width="24" Height="24" VerticalAlignment="Bottom" Margin="0,0,8,6"/>
                    <TextBox 
                        Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                        materialDesign:HintAssist.Hint="{DynamicResource SearchInvoices}" 
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:TextFieldAssist.HasClearButton="True"/>
                </DockPanel>

                <!-- Status Filter -->
                <DockPanel Grid.Column="1" Margin="8,0,8,0">
                    <TextBlock Text="{DynamicResource Status}" Style="{StaticResource MaterialDesignBody1TextBlock}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox
                        SelectedValue="{Binding StatusFilter, Mode=TwoWay}"
                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                        materialDesign:HintAssist.Hint="{DynamicResource SelectStatus}"
                        SelectedValuePath="Tag">
                        <ComboBoxItem Content="{DynamicResource All}" Tag="All" />
                        <ComboBoxItem Content="{DynamicResource Draft}" Tag="Draft" />
                        <ComboBoxItem Content="{DynamicResource Issued}" Tag="Issued" />
                        <ComboBoxItem Content="{DynamicResource Paid}" Tag="Paid" />
                        <ComboBoxItem Content="{DynamicResource Overdue}" Tag="Overdue" />
                        <ComboBoxItem Content="{DynamicResource Cancelled}" Tag="Cancelled" />
                    </ComboBox>
                </DockPanel>

                <!-- Type Filter -->
                <DockPanel Grid.Column="2" Margin="8,0,8,0">
                    <TextBlock Text="{DynamicResource Type}" Style="{StaticResource MaterialDesignBody1TextBlock}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox
                        SelectedValue="{Binding TypeFilter, Mode=TwoWay}"
                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                        materialDesign:HintAssist.Hint="{DynamicResource SelectType}"
                        SelectedValuePath="Tag">
                        <ComboBoxItem Content="{DynamicResource All}" Tag="All" />
                        <ComboBoxItem Content="{DynamicResource Sales}" Tag="Sales" />
                        <ComboBoxItem Content="{DynamicResource Purchase}" Tag="Purchase" />
                    </ComboBox>
                </DockPanel>

                <!-- Refresh Button -->
                <Button 
                    Grid.Column="3"
                    Command="{Binding LoadInvoicesCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="{DynamicResource RefreshInvoiceList}"
                    Margin="8,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Margin="0,0,4,0" />
                        <TextBlock Text="{DynamicResource Refresh}" />
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Content Area -->
        <Grid Grid.Row="2" Margin="16,0,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Invoice List -->
            <materialDesign:Card Grid.Column="0" materialDesign:ShadowAssist.ShadowDepth="Depth1" Margin="0,8,8,0">
                <DockPanel>
                    <materialDesign:ColorZone DockPanel.Dock="Top" Mode="PrimaryLight" Padding="8" materialDesign:ShadowAssist.ShadowDepth="Depth1">
                        <DockPanel>
                            <materialDesign:PackIcon Kind="FileMultiple" Width="24" Height="24" VerticalAlignment="Center" />
                            <TextBlock Text="{DynamicResource Invoices}" FontSize="16" FontWeight="Medium" Margin="8,0,0,0" />
                            <TextBlock Margin="4,0,0,0" FontSize="14" VerticalAlignment="Center">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="({0})">
                                        <Binding Path="Invoices.Count" Mode="OneWay"/>
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </DockPanel>
                    </materialDesign:ColorZone>
                    
                    <ListView 
                        ItemsSource="{Binding Invoices}" 
                        SelectedItem="{Binding SelectedInvoice}" 
                        BorderThickness="0"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        materialDesign:ScrollBarAssist.ThumbCornerRadius="4"
                        Background="Transparent">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource MaterialDesignListBoxItem}">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                <Setter Property="Margin" Value="0,4" />
                                <Setter Property="Padding" Value="0" />
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <materialDesign:Card 
                                    Padding="8" 
                                    Margin="4" 
                                    materialDesign:ShadowAssist.ShadowDepth="Depth0" 
                                    UniformCornerRadius="4"
                                    Background="White">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Status indicator bar -->
                                        <Rectangle 
                                            Grid.Column="0" 
                                            Fill="{Binding Status, Converter={StaticResource StatusToBrushConverter}}"
                                            Width="8"
                                            HorizontalAlignment="Left"
                                            Margin="-8,0,0,0"/>

                                        <!-- Main content -->
                                        <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock 
                                                Text="{Binding InvoiceNumber}" 
                                                FontWeight="Medium"
                                                FontSize="14"
                                                Foreground="{StaticResource PrimaryColor}"/>
                                            <Grid Margin="0,4,0,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,16,0">
                                                    <materialDesign:PackIcon Kind="{Binding Type, Converter={StaticResource StringToIconConverter}}"
                                                                          Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,4,0"
                                                                          Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                                    <TextBlock>
                                                        <Run Text="{Binding Type}" FontStyle="Italic"/>
                                                        <Run Text=" " FontStyle="Italic"/>
                                                        <Run Text="{DynamicResource Invoice}" FontStyle="Italic"/>
                                                    </TextBlock>
                                                </StackPanel>

                                                <Border Grid.Column="1" 
                                                      Background="{Binding Status, Converter={StaticResource StatusToBrushConverter}}"
                                                      CornerRadius="2"
                                                      Padding="4,1"
                                                      Margin="0,0,16,0">
                                                    <TextBlock Text="{Binding Status}" Foreground="White" FontSize="11" />
                                                </Border>
                                                
                                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Calendar" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock>
                                                        <Run Text="{Binding IssueDate, StringFormat=d}" FontSize="11"/>
                                                    </TextBlock>
                                                    <TextBlock Text=" - " FontSize="11" Margin="4,0"/>
                                                    <TextBlock Text="{Binding DueDate, StringFormat=d}" FontSize="11" 
                                                             Foreground="{Binding DueDate, Converter={StaticResource DueDateColorConverter}}"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>

                                        <!-- Amount -->
                                        <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                            <TextBlock 
                                                Text="{Binding GrandTotal, StringFormat=C2}" 
                                                FontWeight="SemiBold"
                                                FontSize="16"
                                                Foreground="{StaticResource MaterialDesignDarkForeground}"/>
                                            <TextBlock 
                                                Text="{Binding PaymentStatus}" 
                                                HorizontalAlignment="Right"
                                                FontSize="11"
                                                Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                        </StackPanel>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </DockPanel>
            </materialDesign:Card>

            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch" Background="#CCCCCC" />

            <!-- Invoice Details -->
            <materialDesign:Card Grid.Column="2" materialDesign:ShadowAssist.ShadowDepth="Depth1" Margin="8,8,0,0">
                <DockPanel>
                    <materialDesign:ColorZone DockPanel.Dock="Top" Mode="PrimaryLight" Padding="8" materialDesign:ShadowAssist.ShadowDepth="Depth1">
                        <DockPanel>
                            <materialDesign:PackIcon Kind="FileDocument" Width="24" Height="24" VerticalAlignment="Center" />
                            <TextBlock Text="{DynamicResource InvoiceDetails}" FontSize="16" FontWeight="Medium" Margin="8,0,0,0" />
                        </DockPanel>
                    </materialDesign:ColorZone>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel Visibility="{Binding HasSelectedInvoice, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <!-- Header with Invoice Number and Type -->
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding SelectedInvoice.InvoiceNumber}" 
                                             FontSize="20" 
                                             FontWeight="Bold" 
                                             Foreground="{StaticResource PrimaryColor}"/>
                                    <TextBlock Text="{Binding SelectedInvoice.InvoiceType}" 
                                             FontSize="14" 
                                             FontStyle="Italic" 
                                             Foreground="{StaticResource SecondaryColor}"/>
                                </StackPanel>

                                <Border Grid.Column="1"
                                      Background="{Binding SelectedInvoice.Status, Converter={StaticResource StatusToBrushConverter}}"
                                      CornerRadius="4"
                                      Padding="12,6">
                                    <TextBlock Text="{Binding SelectedInvoice.Status}" 
                                             Foreground="White" 
                                             FontWeight="Medium"/>
                                </Border>
                            </Grid>

                            <!-- Dates Card -->
                            <materialDesign:Card Margin="0,8" Padding="12" UniformCornerRadius="4">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Issue Date -->
                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,8,0">
                                        <TextBlock Text="{DynamicResource IssueDate}" 
                                                 FontSize="12" 
                                                 Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                            <materialDesign:PackIcon Kind="CalendarEdit" 
                                                                   Width="16" Height="16" 
                                                                   VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding SelectedInvoice.IssueDate, StringFormat=d}" 
                                                     FontWeight="Medium" 
                                                     Margin="4,0,0,0"/>
                                        </StackPanel>
                                    </StackPanel>
                                    
                                    <!-- Due Date -->
                                    <StackPanel Grid.Column="1" Grid.Row="0">
                                        <TextBlock Text="{DynamicResource DueDate}" 
                                                 FontSize="12" 
                                                 Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                            <materialDesign:PackIcon Kind="CalendarClock" 
                                                                   Width="16" Height="16" 
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{Binding SelectedInvoice.DueDate, Converter={StaticResource DueDateColorConverter}}"/>
                                            <TextBlock Text="{Binding SelectedInvoice.DueDate, StringFormat=d}" 
                                                     FontWeight="Medium" 
                                                     Margin="4,0,0,0"
                                                     Foreground="{Binding SelectedInvoice.DueDate, Converter={StaticResource DueDateColorConverter}}"/>
                                        </StackPanel>
                                    </StackPanel>
                                    
                                    <!-- Payment Terms -->
                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,16,8,0">
                                        <TextBlock Text="{DynamicResource PaymentTerms}" 
                                                 FontSize="12" 
                                                 Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                        <TextBlock Text="{Binding SelectedInvoice.PaymentTerms}" 
                                                 FontWeight="Medium" 
                                                 Margin="0,4,0,0"/>
                                </StackPanel>

                                    <!-- Reference -->
                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="0,16,0,0">
                                        <TextBlock Text="{DynamicResource Reference}" 
                                                 FontSize="12" 
                                                 Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                        <TextBlock Text="{Binding SelectedInvoice.Reference}" 
                                                 FontWeight="Medium" 
                                                 Margin="0,4,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Customer/Supplier Info -->
                            <materialDesign:Card Margin="0,8" Padding="12" UniformCornerRadius="4">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="{Binding SelectedInvoice.Type}" FontWeight="Medium" />
                                        <TextBlock Text=" " FontWeight="Medium" />
                                        <TextBlock Text="{DynamicResource FromTo}" FontWeight="Medium" />
                                    </StackPanel>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <materialDesign:PackIcon Grid.Column="0"
                                                               Kind="AccountCircle"
                                                               Width="32" Height="32"
                                                               VerticalAlignment="Top"
                                                               Margin="0,0,16,0"
                                                               Foreground="{StaticResource SecondaryColor}"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding SelectedInvoice.Customer.FullName}" 
                                                     FontWeight="SemiBold" 
                                                     FontSize="16"/>
                                            <TextBlock Text="{Binding SelectedInvoice.Customer.Email}" 
                                                     FontSize="12" 
                                                     Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                            <TextBlock Text="{Binding SelectedInvoice.Customer.Phone}" 
                                                     FontSize="12" 
                                                     Foreground="{StaticResource MaterialDesignBodyLight}"
                                                     Margin="0,4,0,0"/>
                                            <TextBlock Text="{Binding SelectedInvoice.Customer.Address}" 
                                                     TextWrapping="Wrap" 
                                                     Margin="0,8,0,0"/>
                                </StackPanel>
                            </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Financial Summary -->
                            <materialDesign:Card Margin="0,8" Padding="12" UniformCornerRadius="4" Background="{StaticResource PrimaryColor}" Foreground="White">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="{DynamicResource Subtotal}" Opacity="0.7"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedInvoice.Subtotal, StringFormat=C2}" 
                                             HorizontalAlignment="Right"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="{DynamicResource Tax}" Opacity="0.7" Margin="0,8,0,0"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedInvoice.TaxAmount, StringFormat=C2}" 
                                             HorizontalAlignment="Right" Margin="0,8,0,0"/>
                                    
                                    <Separator Grid.Row="2" Grid.ColumnSpan="2" Margin="0,8" Background="White" Opacity="0.3"/>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="{DynamicResource Total}" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedInvoice.GrandTotal, StringFormat=C2}" 
                                             FontWeight="Bold" FontSize="18" HorizontalAlignment="Right"/>
                            </Grid>
                            </materialDesign:Card>

                            <!-- Action Buttons -->
                            <StackPanel Orientation="Horizontal" Margin="0,16,0,0" HorizontalAlignment="Right">
                                <Button Command="{Binding EditInvoiceCommand}" 
                                      Style="{StaticResource MaterialDesignFlatButton}" 
                                      Margin="8,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="PencilOutline" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="{DynamicResource Edit}"/>
                                    </StackPanel>
                                </Button>
                                <Button Command="{Binding DeleteInvoiceCommand}" 
                                      Style="{StaticResource MaterialDesignFlatButton}" 
                                      Foreground="{StaticResource ErrorColor}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="DeleteOutline" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="{DynamicResource Delete}"/>
                                    </StackPanel>
                                </Button>
                                <Button Command="{Binding PrintInvoiceCommand}" 
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Background="{StaticResource PrimaryColor}"
                                      Foreground="White" 
                                      Margin="8,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Printer" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="{DynamicResource Print}"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </DockPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Height="30">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock>
                    <Run Text="{DynamicResource TotalInvoices}"/>
                    <Run Text="{Binding Invoices.Count, Mode=OneWay}"/>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>

        <!-- Loading Overlay -->
        <Grid Grid.Row="2" Grid.RowSpan="2" Background="#80000000" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="-478,46,478,-170">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Background="White" Margin="20">
                <TextBlock Text="{DynamicResource Loading}" FontSize="16"/>
                <ProgressBar IsIndeterminate="True" Height="5" Margin="0,10,0,0" Width="200"/>
            </StackPanel>
        </Grid>

        <!-- Edit Invoice Dialog -->
        <materialDesign:DialogHost 
            Identifier="EditInvoiceDialog"
            DialogTheme="Inherit"
            CloseOnClickAway="False"
            IsOpen="{Binding IsEditing}">
            <materialDesign:DialogHost.DialogContent>
                <local:InvoiceDialogView />
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</UserControl> 