using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Dashboard;
using POSSystem.Data;
using LiveCharts;
using LiveCharts.Wpf;
using System.Windows.Media;
using System.Windows.Input;
using System.Windows;
using System.Windows.Threading;
using System.Collections.Generic;
using System.Threading;
using System.Diagnostics;
using CommunityToolkit.Mvvm.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Refactored DashboardViewModel that uses the extracted services.
    /// This class acts as a coordinator between the different services and the UI.
    /// </summary>
    public class RefactoredDashboardViewModel : INotifyPropertyChanged, IDisposable
    {
        // Service references
        private readonly IDashboardDataProvider _dataProvider;
        private readonly POSSystem.ViewModels.Dashboard.ChartService _chartService;
        private readonly DashboardDataService _dataService;
        private readonly MetricsCalculationService _metricsService;
        private readonly ChartParameterManager _parameterManager;

        private readonly POSSystem.Services.RealTime.DashboardUpdateService _realTimeUpdateService;
        
        // UI state fields
        private decimal _todaySales;
        private decimal _weekSales;
        private decimal _monthSales;
        private decimal _grossProfit;
        private decimal _profitMargin;
        private decimal _filteredSales;
        private decimal _filteredProfit;
        private decimal _filteredProfitMargin;
        private decimal _filteredMetricValue;
        private decimal _salesGrowth;
        private decimal _profitGrowth;
        private decimal _metricGrowth;
        private TimePeriod _selectedQuickStatsPeriod;
        private TimePeriod _selectedProfitPeriod;
        private TimePeriod _selectedMetricPeriod;
        private TimePeriod _selectedGlobalPeriod;
        private MetricType _selectedMetricType;
        private SeriesCollection _salesTrendSeries;
        private string[] _salesTrendLabels;
        private DateTime _startDate;
        private DateTime _endDate;
        private string _selectedDateRange;
        private bool _isZoomed;
        private ChartParameter _selectedChartParameter;
        private ChartType _selectedChartType;
        private VirtualizingCollection<ProductPerformance> _topProducts;
        private ObservableCollection<Alert> _activeAlerts;
        private bool _isLoading;
        private readonly Dictionary<string, bool> _sectionLoadingStates;
        private int _loadingOperations;
        private bool _disposed;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private CartesianChart _trendChart;
        private string _trendChartNoDataMessage;
        private bool _trendChartIsLoading;

        private bool _isDetailViewVisible;
        
        // Product chart fields
        private SeriesCollection _productChartSeries;
        private SeriesCollection _profitChartSeries;
        private SeriesCollection _marginChartSeries;
        private SeriesCollection _itemsSoldChartSeries;
        private SeriesCollection _categoryChartSeries;
        private SeriesCollection _categoryProfitSeries;
        private SeriesCollection _categoryMarginSeries;
        private SeriesCollection _categoryItemsSoldSeries;
        private string[] _productChartLabels = Array.Empty<string>();
        private bool _isProductPerformanceLoading;
        private bool _isCustomerInsightsLoading;
        private bool _isUserPerformanceLoading;
        private Dictionary<string, bool> _isTabActive;
        
        // Expense tracking fields
        private int _monthlyExpensesCount;
        private decimal _monthlyExpensesAmount;
        private int _upcomingExpensesCount;
        private decimal _upcomingExpensesAmount;
        private decimal _monthlyExpenses;
        private decimal _expensesGrowth;
        private decimal _upcomingExpenses;
        
        // Add missing statistics fields
        private int _salesCount;
        private int _expiringProductsCount;
        private int _expiredProductsCount;
        private int _lowStockCount;
        private int _outOfStockCount;
        private int _overdueOrdersCount;
        private decimal _totalOverdueAmount;
        private int _unpaidSalesCount;
        private decimal _unpaidSalesAmount;
        private int _overdueSalesCount;
        private decimal _overdueSalesAmount;
        
        // Visibility controls
        private bool _isSalesCardVisible = true;
        private bool _isExpensesCardVisible = true;
        
        // Series for customer and user analytics
        private SeriesCollection _topCustomerRevenueSeries;
        private SeriesCollection _topCustomerOrdersSeries;
        private SeriesCollection _topCustomerAvgOrderSeries;
        private SeriesCollection _topCustomerItemsSeries;
        private SeriesCollection _userSalesPerformanceSeries;
        private SeriesCollection _userTransactionsSeries;
        private SeriesCollection _userCustomersSeries;
        private SeriesCollection _userConversionSeries;
        private ObservableCollection<UserPerformance> _userPerformances;
        
        private ICommand _showLowStockStatsCommand;
        private ICommand _showUnpaidSalesStatsCommand;
        private ICommand _refreshDashboardCommand;
        
        public event PropertyChangedEventHandler PropertyChanged;
        public event EventHandler<string> ScrollToSectionRequested;

        /// <summary>
        /// Creates a new instance of the RefactoredDashboardViewModel with proper dependency injection
        /// </summary>
        public RefactoredDashboardViewModel(
            IDashboardDataProvider dataProvider,
            DashboardDataService dataService,
            POSSystem.ViewModels.Dashboard.ChartService chartService,
            MetricsCalculationService metricsService,
            ChartParameterManager parameterManager,

            POSSystem.Services.RealTime.DashboardUpdateService realTimeUpdateService = null)
        {
            System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Constructor started with DI services");

            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _chartService = chartService ?? throw new ArgumentNullException(nameof(chartService));
            _metricsService = metricsService ?? throw new ArgumentNullException(nameof(metricsService));
            _parameterManager = parameterManager ?? throw new ArgumentNullException(nameof(parameterManager));

            _realTimeUpdateService = realTimeUpdateService; // Optional service
            _cancellationTokenSource = new CancellationTokenSource();
            _sectionLoadingStates = new Dictionary<string, bool>();
            _isTabActive = new Dictionary<string, bool>();

            // Subscribe to real-time updates if service is available
            if (_realTimeUpdateService != null)
            {
                _realTimeUpdateService.MetricsUpdated += OnRealTimeMetricsUpdated;
                System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Subscribed to real-time updates");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Real-time service is NULL - updates will not work");
            }

            // Subscribe to sale completion events for immediate dashboard updates
            POSSystem.ViewModels.SaleViewModel.SaleCompleted += OnSaleCompleted;
            System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Subscribed to SaleCompleted events");

            // Initialize collections
            InitializeQuickStatsPeriods();
            InitializeChartParameters();
            InitializeMetricTypes();

            // Complete initialization
            InitializeAfterConstruction();
        }

        /// <summary>
        /// LEGACY: Constructor for backward compatibility during migration
        /// </summary>
        public RefactoredDashboardViewModel(IDashboardDataProvider dataProvider) : this(
            dataProvider,
            App.ServiceProvider?.GetService(typeof(DashboardDataService)) as DashboardDataService ?? new DashboardDataService(dataProvider),
            App.ServiceProvider?.GetService(typeof(POSSystem.ViewModels.Dashboard.ChartService)) as POSSystem.ViewModels.Dashboard.ChartService ?? new POSSystem.ViewModels.Dashboard.ChartService(dataProvider),
            App.ServiceProvider?.GetService(typeof(MetricsCalculationService)) as MetricsCalculationService ?? new MetricsCalculationService(),
            App.ServiceProvider?.GetService(typeof(ChartParameterManager)) as ChartParameterManager ?? new ChartParameterManager(),

            App.ServiceProvider?.GetService(typeof(POSSystem.Services.RealTime.DashboardUpdateService)) as POSSystem.Services.RealTime.DashboardUpdateService)
        {
            System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Using legacy constructor with fallback service creation");

            // Debug: Check if real-time service was resolved
            var realTimeService = App.ServiceProvider?.GetService(typeof(POSSystem.Services.RealTime.DashboardUpdateService));
            System.Diagnostics.Debug.WriteLine($"RefactoredDashboardViewModel: Real-time service from DI: {(realTimeService != null ? "FOUND" : "NULL")}");

            // Start async loading
            StartAsyncInitialization();
        }

        private void InitializeAfterConstruction()
        {
            // Set default period to Today
            SelectedQuickStatsPeriod = QuickStatsPeriods.FirstOrDefault(p => p.Type == TimePeriodType.Today);

            // Initialize commands
            UpdateDateRangeCommand = new RelayCommand(_ => UpdateDateRange());
            ResetZoomCommand = new RelayCommand(_ => ResetZoom());
            SelectChartParameterCommand = new RelayCommand(p => SelectChartParameter((ChartParameter)p));
            CloseDetailCommand = new RelayCommand(_ => { IsDetailViewVisible = false; });

            // Set currency formatter
            CurrencyFormatter = value => $"{value:N2} DA";

            // Start async loading
            StartAsyncInitialization();
        }

        private void StartAsyncInitialization()
        {
            // Load initial data
            _ = Task.Run(async () =>
            {
                try
                {
                    await LoadQuickStatsAsync();
                    System.Diagnostics.Debug.WriteLine("Constructor: LoadQuickStatsAsync completed successfully");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Constructor: LoadQuickStatsAsync failed: {ex.Message}");
                    // Set a test value to verify the property works
                    TodaySales = 999.99m;
                }
            });
        }

        private void InitializeQuickStatsPeriods()
        {
            QuickStatsPeriods.Clear();
            QuickStatsPeriods.Add(new TimePeriod { Type = TimePeriodType.Today, DisplayName = "TimePeriod_Today" });
            QuickStatsPeriods.Add(new TimePeriod { Type = TimePeriodType.Yesterday, DisplayName = "TimePeriod_Yesterday" });
            QuickStatsPeriods.Add(new TimePeriod { Type = TimePeriodType.Week, DisplayName = "TimePeriod_Week" });
            QuickStatsPeriods.Add(new TimePeriod { Type = TimePeriodType.Month, DisplayName = "TimePeriod_Month", IsCurrentMonth = true });
            QuickStatsPeriods.Add(new TimePeriod { Type = TimePeriodType.Year, DisplayName = "TimePeriod_Year" });

            // Set default selection to Today
            SelectedQuickStatsPeriod = QuickStatsPeriods.FirstOrDefault(p => p.Type == TimePeriodType.Today);
        }
        
        private void InitializeMetricTypes()
        {
            var metricTypes = new ObservableCollection<MetricType>
            {
                new MetricType { DisplayName = "Sales", Key = "sales", IsCurrency = true },
                new MetricType { DisplayName = "Profit", Key = "profit", IsCurrency = true },
                new MetricType { DisplayName = "Margin", Key = "margin", IsCurrency = false },
                new MetricType { DisplayName = "Items", Key = "items", IsCurrency = false }
            };

            _parameterManager.MetricTypes = metricTypes;
            SelectedMetricType = metricTypes.FirstOrDefault();
        }
        
        private void InitializeChartParameters()
        {
            var parameters = new ObservableCollection<ChartParameter>
            {
                new ChartParameter 
                { 
                    Name = "Sales", 
                    Key = "sales", 
                    Description = "Total sales amount",
                    Color = Colors.Blue,
                    SecondaryMetric = "growth"
                },
                new ChartParameter 
                { 
                    Name = "Profit", 
                    Key = "profit", 
                    Description = "Total profit",
                    Color = Colors.Green,
                    SecondaryMetric = "margin"
                },
                new ChartParameter 
                { 
                    Name = "Items Sold", 
                    Key = "items", 
                    Description = "Number of items sold",
                    Color = Colors.Orange,
                    SecondaryMetric = "growth"
                }
            };

            _parameterManager.ChartParameters = parameters;
            SelectedChartParameter = parameters.FirstOrDefault();
        }
        
        #region Properties
        
        // Chart type selection
        public ChartType SelectedChartType
        {
            get => _selectedChartType;
            set 
            { 
                _selectedChartType = value; 
                OnPropertyChanged();
                _ = LoadSalesTrendDataAsync();
            }
        }
        
        // Quick stats properties
        public decimal TodaySales
        {
            get => _todaySales;
            set
            {
                _todaySales = value;
                OnPropertyChanged();
                System.Diagnostics.Debug.WriteLine($"TodaySales set to: {value:N2}");
            }
        }

        /// <summary>
        /// Dynamic sales title that changes based on selected period
        /// </summary>
        public string SalesCardTitle
        {
            get
            {
                if (SelectedQuickStatsPeriod == null)
                    return GetLocalizedString("SalesCard_TodaysSales") ?? "Today's Sales";

                return SelectedQuickStatsPeriod.Type switch
                {
                    TimePeriodType.Today => GetLocalizedString("SalesCard_TodaysSales") ?? "Today's Sales",
                    TimePeriodType.Yesterday => GetLocalizedString("SalesCard_YesterdaysSales") ?? "Yesterday's Sales",
                    TimePeriodType.Week => GetLocalizedString("SalesCard_ThisWeeksSales") ?? "This Week's Sales",
                    TimePeriodType.Month => GetLocalizedString("SalesCard_ThisMonthsSales") ?? "This Month's Sales",
                    TimePeriodType.Year => GetLocalizedString("SalesCard_ThisYearsSales") ?? "This Year's Sales",
                    _ => GetLocalizedString("SalesCard_Sales") ?? "Sales"
                };
            }
        }

        /// <summary>
        /// Dynamic sales value that represents the current period's sales
        /// </summary>
        public decimal PeriodSales
        {
            get => _todaySales; // For now, use the same backing field
            set
            {
                _todaySales = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TodaySales)); // Keep both properties in sync
                System.Diagnostics.Debug.WriteLine($"PeriodSales set to: {value:N2} for period: {SelectedQuickStatsPeriod?.DisplayName}");
            }
        }

        /// <summary>
        /// Dynamic profit title that changes based on selected period
        /// </summary>
        public string ProfitCardTitle
        {
            get
            {
                if (SelectedQuickStatsPeriod == null)
                    return GetLocalizedString("ProfitCard_GrossProfit") ?? "Gross Profit";

                return SelectedQuickStatsPeriod.Type switch
                {
                    TimePeriodType.Today => GetLocalizedString("ProfitCard_TodaysProfit") ?? "Today's Profit",
                    TimePeriodType.Yesterday => GetLocalizedString("ProfitCard_YesterdaysProfit") ?? "Yesterday's Profit",
                    TimePeriodType.Week => GetLocalizedString("ProfitCard_ThisWeeksProfit") ?? "This Week's Profit",
                    TimePeriodType.Month => GetLocalizedString("ProfitCard_ThisMonthsProfit") ?? "This Month's Profit",
                    TimePeriodType.Year => GetLocalizedString("ProfitCard_ThisYearsProfit") ?? "This Year's Profit",
                    _ => GetLocalizedString("ProfitCard_GrossProfit") ?? "Gross Profit"
                };
            }
        }

        /// <summary>
        /// Dynamic chart title that changes based on selected period
        /// </summary>
        public string ChartTitle
        {
            get
            {
                if (SelectedQuickStatsPeriod == null) return "Sales Trend";

                return SelectedQuickStatsPeriod.Type switch
                {
                    TimePeriodType.Today => "Today's Sales Trend",
                    TimePeriodType.Yesterday => "Yesterday's Sales",
                    TimePeriodType.Week => "This Week's Sales Trend",
                    TimePeriodType.Month => "This Month's Sales Trend",
                    TimePeriodType.Year => "This Year's Sales Trend",
                    _ => "Sales Trend"
                };
            }
        }
        
        public decimal WeekSales
        {
            get => _weekSales;
            set { _weekSales = value; OnPropertyChanged(); }
        }
        
        public decimal MonthSales
        {
            get => _monthSales;
            set { _monthSales = value; OnPropertyChanged(); }
        }
        
        public decimal GrossProfit
        {
            get => _grossProfit;
            set { _grossProfit = value; OnPropertyChanged(); }
        }
        
        public decimal ProfitMargin
        {
            get => _profitMargin;
            set { _profitMargin = value; OnPropertyChanged(); }
        }
        
        // Product data
        public VirtualizingCollection<ProductPerformance> TopProducts
        {
            get => _topProducts;
            set { _topProducts = value; OnPropertyChanged(); }
        }
        
        // Alerts
        public ObservableCollection<Alert> ActiveAlerts
        {
            get => _activeAlerts;
            set { _activeAlerts = value; OnPropertyChanged(); }
        }
        
        // Chart data
        public SeriesCollection SalesTrendSeries
        {
            get => _salesTrendSeries;
            set { _salesTrendSeries = value; OnPropertyChanged(); }
        }
        
        public string[] SalesTrendLabels
        {
            get => _salesTrendLabels;
            set { _salesTrendLabels = value; OnPropertyChanged(); }
        }
        
        // Product chart properties
        public SeriesCollection ProductChartSeries
        {
            get => _productChartSeries;
            set { _productChartSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection ProfitChartSeries
        {
            get => _profitChartSeries;
            set { _profitChartSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection MarginChartSeries
        {
            get => _marginChartSeries;
            set { _marginChartSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection ItemsSoldChartSeries
        {
            get => _itemsSoldChartSeries;
            set { _itemsSoldChartSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection CategoryChartSeries
        {
            get => _categoryChartSeries;
            set { _categoryChartSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection CategoryProfitSeries
        {
            get => _categoryProfitSeries;
            set { _categoryProfitSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection CategoryMarginSeries
        {
            get => _categoryMarginSeries;
            set { _categoryMarginSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection CategoryItemsSoldSeries
        {
            get => _categoryItemsSoldSeries;
            set { _categoryItemsSoldSeries = value; OnPropertyChanged(); }
        }
        
        public string[] ProductChartLabels
        {
            get => _productChartLabels ?? Array.Empty<string>();
            set { _productChartLabels = value ?? Array.Empty<string>(); OnPropertyChanged(); }
        }
        
        public bool IsProductPerformanceLoading
        {
            get => _isProductPerformanceLoading;
            set { _isProductPerformanceLoading = value; OnPropertyChanged(); }
        }
        
        public bool IsCustomerInsightsLoading
        {
            get => _isCustomerInsightsLoading;
            set { _isCustomerInsightsLoading = value; OnPropertyChanged(); }
        }
        
        public bool IsUserPerformanceLoading
        {
            get => _isUserPerformanceLoading;
            set { _isUserPerformanceLoading = value; OnPropertyChanged(); }
        }
        
        public Func<double, string> ProductValueFormatter => value => CurrencyFormatter(value);
        
        public Func<double, string> MarginFormatter => value => $"{value:N1}%";
        
        public Func<double, string> ItemsSoldFormatter => value => value.ToString("0.###"); // ✅ WEIGHT-BASED FIX: Show decimal quantities instead of rounding to whole numbers
        
        public bool IsTabActive(string tabKey)
        {
            if (_isTabActive.TryGetValue(tabKey, out bool isActive))
                return isActive;
            return false;
        }
        
        public bool IsTabActiveForChart => true; // Default to true for binding compatibility
        
        public Func<double, string> CurrencyFormatter { get; set; }
        
        // Date range
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                    _isZoomed = true;
                    OnPropertyChanged(nameof(IsZoomed));
                }
            }
        }
        
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (_endDate != value)
                {
                    _endDate = value;
                    OnPropertyChanged();
                    _isZoomed = true;
                    OnPropertyChanged(nameof(IsZoomed));
                }
            }
        }
        
        public string SelectedDateRange
        {
            get => _selectedDateRange;
            set
            {
                if (_selectedDateRange != value)
                {
                    _selectedDateRange = value;
                    OnPropertyChanged();
                    UpdateDateRangeBasedOnSelection(value);
                }
            }
        }
        
        // Date ranges from parameter manager
        public ObservableCollection<string> DateRanges => new ObservableCollection<string>
        {
            "Today",
            "Yesterday",
            "Last 7 Days",
            "Last 30 Days",
            "This Month",
            "Last Month",
            "Last 90 Days",
            "This Year"
        };
        
        // Commands
        public ICommand UpdateDateRangeCommand { get; private set; }
        public ICommand ResetZoomCommand { get; private set; }
        public ICommand SelectChartParameterCommand { get; private set; }
        public ICommand CategoryClickCommand { get; private set; }
        public ICommand CloseDetailCommand { get; private set; }
        
        public bool IsZoomed
        {
            get => _isZoomed;
            private set { _isZoomed = value; OnPropertyChanged(); }
        }
        
        // Periods from parameter manager
        public ObservableCollection<TimePeriod> QuickStatsPeriods => _parameterManager.TimePeriods;
        
        public ObservableCollection<MetricType> MetricTypes => _parameterManager.MetricTypes;
        
        /// <summary>
        /// Global period selection that affects all dashboard components
        /// </summary>
        public TimePeriod SelectedGlobalPeriod
        {
            get => _selectedGlobalPeriod;
            set
            {
                if (_selectedGlobalPeriod != value && value != null)
                {
                    Debug.WriteLine($"===== PERIOD CHANGED: {_selectedGlobalPeriod?.DisplayName} -> {value.DisplayName} =====");
                    
                    _selectedGlobalPeriod = value;
                    OnPropertyChanged();
                    
                    // Update all individual period selections
                    _selectedQuickStatsPeriod = value;
                    _selectedProfitPeriod = value;
                    _selectedMetricPeriod = value;
                    
                    // Trigger property changed for UI
                    OnPropertyChanged(nameof(SelectedQuickStatsPeriod));
                    OnPropertyChanged(nameof(SelectedProfitPeriod));
                    OnPropertyChanged(nameof(SelectedMetricPeriod));
                    
                    // Refresh all dashboard data with the new period
                    _ = RefreshAllDashboardData();
                }
            }
        }
        
        public TimePeriod SelectedQuickStatsPeriod
        {
            get => _selectedQuickStatsPeriod;
            set
            {
                if (_selectedQuickStatsPeriod != value)
                {
                    _selectedQuickStatsPeriod = value;
                    OnPropertyChanged();

                    // Update dynamic titles for all cards
                    OnPropertyChanged(nameof(SalesCardTitle));
                    OnPropertyChanged(nameof(ProfitCardTitle));
                    OnPropertyChanged(nameof(ChartTitle));

                    // Force clear the cache to ensure fresh data when switching periods
                    _dataService.ForceClearCache();

                    // Update date range based on selection
                    UpdateDateRangeForPeriod(value);

                    // Load fresh data for all components
                    _ = LoadAllComponentsForPeriodAsync();
                }
            }
        }
        
        public TimePeriod SelectedProfitPeriod
        {
            get => _selectedProfitPeriod;
            set
            {
                if (_selectedProfitPeriod != value)
                {
                    _selectedProfitPeriod = value;
                    OnPropertyChanged();
                    _ = LoadFilteredDataAsync();
                }
            }
        }
        
        public TimePeriod SelectedMetricPeriod
        {
            get => _selectedMetricPeriod;
            set
            {
                if (_selectedMetricPeriod != value)
                {
                    _selectedMetricPeriod = value;
                    OnPropertyChanged();
                    _ = LoadFilteredDataAsync();
                }
            }
        }
        
        public MetricType SelectedMetricType
        {
            get => _selectedMetricType;
            set
            {
                if (_selectedMetricType != value)
                {
                    bool wasChanging = _selectedMetricType != null;
                    _selectedMetricType = value;
                    OnPropertyChanged();
                    
                    if (wasChanging)
                    {
                        _ = LoadFilteredDataAsync();
                    }
                }
            }
        }
        
        // Filtered metrics
        public decimal FilteredSales
        {
            get => _filteredSales;
            set { _filteredSales = value; OnPropertyChanged(); }
        }
        
        public decimal FilteredProfit
        {
            get => _filteredProfit;
            set { _filteredProfit = value; OnPropertyChanged(); }
        }
        
        public decimal FilteredProfitMargin
        {
            get => _filteredProfitMargin;
            set { _filteredProfitMargin = value; OnPropertyChanged(); }
        }
        
        public decimal FilteredMetricValue
        {
            get => _filteredMetricValue;
            set { _filteredMetricValue = value; OnPropertyChanged(); OnPropertyChanged(nameof(FormattedMetricValue)); }
        }
        
        public string FormattedMetricValue
        {
            get
            {
                if (_selectedMetricType == null) return "0";
                
                switch (_selectedMetricType.Key.ToLower())
                {
                    case "sales": return $"{FilteredMetricValue:N2} DA";
                    case "profit": return $"{FilteredMetricValue:N2} DA";
                    case "margin": return $"{FilteredMetricValue:N2}%";
                    case "items": return $"{FilteredMetricValue:N0}";
                    default: return FilteredMetricValue.ToString("N2");
                }
            }
        }
        
        // Growth metrics
        public decimal SalesGrowth
        {
            get => _salesGrowth;
            set { _salesGrowth = value; OnPropertyChanged(); }
        }
        
        public decimal ProfitGrowth
        {
            get => _profitGrowth;
            set { _profitGrowth = value; OnPropertyChanged(); }
        }
        
        public decimal MetricGrowth
        {
            get => _metricGrowth;
            set { _metricGrowth = value; OnPropertyChanged(); }
        }
        
        // Chart parameters from parameter manager
        public ObservableCollection<ChartParameter> ChartParameters => _parameterManager.ChartParameters;
        
        public ChartParameter SelectedChartParameter
        {
            get => _selectedChartParameter;
            set 
            { 
                if (_selectedChartParameter != value)
                { 
                    _selectedChartParameter = value; 
                    OnPropertyChanged();
                    _ = LoadSalesTrendDataAsync();
                }
            }
        }
        
        // Chart related properties
        public string TrendChartNoDataMessage
        {
            get => _trendChartNoDataMessage;
            set { _trendChartNoDataMessage = value; OnPropertyChanged(); }
        }
        
        public bool TrendChartIsLoading
        {
            get => _trendChartIsLoading;
            set { _trendChartIsLoading = value; OnPropertyChanged(); }
        }
        
        public CartesianChart TrendChart
        {
            get => _trendChart;
            set { _trendChart = value; OnPropertyChanged(); }
        }
        
        // Loading state
        public bool IsLoading
        {
            get => _isLoading;
            set 
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }
        
        public bool IsSectionLoading(string section)
        {
            return _sectionLoadingStates.TryGetValue(section, out bool isLoading) && isLoading;
        }
        


        public bool IsDetailViewVisible
        {
            get => _isDetailViewVisible;
            set { _isDetailViewVisible = value; OnPropertyChanged(); }
        }
        
        // Expense tracking properties
        public int MonthlyExpensesCount
        {
            get => _monthlyExpensesCount;
            set { _monthlyExpensesCount = value; OnPropertyChanged(); }
        }
        
        public decimal MonthlyExpensesAmount
        {
            get => _monthlyExpensesAmount;
            set { _monthlyExpensesAmount = value; OnPropertyChanged(); }
        }
        
        public int UpcomingExpensesCount
        {
            get => _upcomingExpensesCount;
            set { _upcomingExpensesCount = value; OnPropertyChanged(); }
        }
        
        public decimal UpcomingExpensesAmount
        {
            get => _upcomingExpensesAmount;
            set { _upcomingExpensesAmount = value; OnPropertyChanged(); }
        }
        
        public decimal MonthlyExpenses
        {
            get => _monthlyExpenses;
            set 
            { 
                if (_monthlyExpenses != value)
                {
                    _monthlyExpenses = value;
                    OnPropertyChanged();
                }
            }
        }
        
        public decimal ExpensesGrowth
        {
            get => _expensesGrowth;
            set 
            { 
                if (_expensesGrowth != value)
                {
                    _expensesGrowth = value;
                    OnPropertyChanged();
                }
            }
        }
        
        public decimal UpcomingExpenses
        {
            get => _upcomingExpenses;
            set 
            { 
                if (_upcomingExpenses != value)
                {
                    _upcomingExpenses = value;
                    OnPropertyChanged();
                }
            }
        }
        
        // New Dashboard KPI properties
        public int SalesCount
        {
            get => _salesCount;
            set { _salesCount = value; OnPropertyChanged(); }
        }
        
        public int ExpiringProductsCount
        {
            get => _expiringProductsCount;
            set { _expiringProductsCount = value; OnPropertyChanged(); }
        }
        
        public int ExpiredProductsCount
        {
            get => _expiredProductsCount;
            set { _expiredProductsCount = value; OnPropertyChanged(); }
        }
        
        public int LowStockCount
        {
            get => _lowStockCount;
            set { _lowStockCount = value; OnPropertyChanged(); }
        }
        
        public int OutOfStockCount
        {
            get => _outOfStockCount;
            set { _outOfStockCount = value; OnPropertyChanged(); }
        }
        
        public int OverdueOrdersCount
        {
            get => _overdueOrdersCount;
            set { _overdueOrdersCount = value; OnPropertyChanged(); }
        }
        
        public decimal TotalOverdueAmount
        {
            get => _totalOverdueAmount;
            set { _totalOverdueAmount = value; OnPropertyChanged(); }
        }
        
        public int UnpaidSalesCount
        {
            get => _unpaidSalesCount;
            set { _unpaidSalesCount = value; OnPropertyChanged(); }
        }
        
        public decimal UnpaidSalesAmount
        {
            get => _unpaidSalesAmount;
            set { _unpaidSalesAmount = value; OnPropertyChanged(); }
        }
        
        public int OverdueSalesCount
        {
            get => _overdueSalesCount;
            set { _overdueSalesCount = value; OnPropertyChanged(); }
        }
        
        public decimal OverdueSalesAmount
        {
            get => _overdueSalesAmount;
            set { _overdueSalesAmount = value; OnPropertyChanged(); }
        }
        
        // UI visibility properties
        public bool IsSalesCardVisible
        {
            get => _isSalesCardVisible;
            set { _isSalesCardVisible = value; OnPropertyChanged(); }
        }
        
        public bool IsExpensesCardVisible
        {
            get => _isExpensesCardVisible;
            set { _isExpensesCardVisible = value; OnPropertyChanged(); }
        }
        
        // Customer analytics series
        public SeriesCollection TopCustomerRevenueSeries
        {
            get => _topCustomerRevenueSeries;
            set { _topCustomerRevenueSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection TopCustomerOrdersSeries
        {
            get => _topCustomerOrdersSeries;
            set { _topCustomerOrdersSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection TopCustomerAvgOrderSeries
        {
            get => _topCustomerAvgOrderSeries;
            set { _topCustomerAvgOrderSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection TopCustomerItemsSeries
        {
            get => _topCustomerItemsSeries;
            set { _topCustomerItemsSeries = value; OnPropertyChanged(); }
        }
        
        // User analytics series
        public SeriesCollection UserSalesPerformanceSeries
        {
            get => _userSalesPerformanceSeries;
            set { _userSalesPerformanceSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection UserTransactionsSeries
        {
            get => _userTransactionsSeries;
            set { _userTransactionsSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection UserCustomersSeries
        {
            get => _userCustomersSeries;
            set { _userCustomersSeries = value; OnPropertyChanged(); }
        }
        
        public SeriesCollection UserConversionSeries
        {
            get => _userConversionSeries;
            set { _userConversionSeries = value; OnPropertyChanged(); }
        }
        
        public ObservableCollection<UserPerformance> UserPerformances
        {
            get => _userPerformances;
            set { _userPerformances = value; OnPropertyChanged(); }
        }
        
        // Alias for CloseDetailCommand to match binding
        public ICommand CloseDetailViewCommand => CloseDetailCommand;
        
        public bool IsProfitCardVisible => true;

        public Brush ProfitGrowthColor => GetGrowthColor(ProfitGrowth);

        private Brush GetGrowthColor(decimal growth)
        {
            if (growth > 0)
                return new SolidColorBrush(Colors.Green);
            if (growth < 0)
                return new SolidColorBrush(Colors.Red);
            return new SolidColorBrush(Colors.Gray);
        }
        
        public ICommand ShowLowStockStatsCommand
        {
            get
            {
                return _showLowStockStatsCommand ?? (_showLowStockStatsCommand = new RelayCommand(async _ =>
                {
                    try
                    {
                        // Check if a dialog is already open to prevent conflicts
                        if (DialogHost.IsDialogOpen("RootDialog"))
                        {
                            Debug.WriteLine("Dialog already open, ignoring ShowLowStockStatsCommand");
                            return;
                        }

                        // ⚡ PERFORMANCE: Preload data before showing dialog
                        await PreloadLowStockDataAsync();

                        var dialog = new LowStockStatsDetailsDialog(this);
                        await DialogHost.Show(dialog, "RootDialog");
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
                    {
                        Debug.WriteLine($"DialogHost conflict detected in ShowLowStockStatsCommand: {ex.Message}");
                        // Silently ignore this error as it's a timing issue, not a real error
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error showing low stock stats: {ex.Message}");
                        MessageBox.Show(
                            Application.Current.TryFindResource("ErrorShowingStats")?.ToString() ?? "Error showing statistics",
                            "Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }));
            }
        }

        public ICommand ShowUnpaidSalesStatsCommand
        {
            get
            {
                return _showUnpaidSalesStatsCommand ?? (_showUnpaidSalesStatsCommand = new RelayCommand(async _ =>
                {
                    try
                    {
                        // Check if a dialog is already open to prevent conflicts
                        if (DialogHost.IsDialogOpen("RootDialog"))
                        {
                            Debug.WriteLine("Dialog already open, ignoring ShowUnpaidSalesStatsCommand");
                            return;
                        }

                        var dialog = new UnpaidSalesStatsDetailsDialog(this);
                        await DialogHost.Show(dialog, "RootDialog");
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
                    {
                        Debug.WriteLine($"DialogHost conflict detected in ShowUnpaidSalesStatsCommand: {ex.Message}");
                        // Silently ignore this error as it's a timing issue, not a real error
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error showing unpaid sales stats: {ex.Message}");
                        MessageBox.Show(
                            Application.Current.TryFindResource("ErrorShowingStats")?.ToString() ?? "Error showing statistics",
                            "Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }));
            }
        }

        /// <summary>
        /// Command to refresh all dashboard data and clear cache
        /// </summary>
        public ICommand RefreshDashboardCommand
        {
            get
            {
                return _refreshDashboardCommand ?? (_refreshDashboardCommand = new RelayCommand(async _ =>
                {
                    try
                    {
                        Debug.WriteLine("RefreshDashboardCommand: Starting dashboard refresh");

                        // Force clear all caches to ensure fresh data
                        _dataService.ForceClearCache();

                        // Force refresh the real-time service if available
                        if (_realTimeUpdateService != null)
                        {
                            await _realTimeUpdateService.ForceUpdateAsync();
                        }

                        // Refresh all dashboard data
                        await RefreshAllDashboardData();

                        Debug.WriteLine("RefreshDashboardCommand: Dashboard refresh completed");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error refreshing dashboard: {ex.Message}");
                        MessageBox.Show($"Error refreshing dashboard: {ex.Message}", "Refresh Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }));
            }
        }

        // Command for testing customer-specific unpaid sales
        private ICommand _showCustomerUnpaidSalesCommand;
        public ICommand ShowCustomerUnpaidSalesCommand
        {
            get
            {
                return _showCustomerUnpaidSalesCommand ?? (_showCustomerUnpaidSalesCommand = new RelayCommand(async _ =>
                {
                    try
                    {
                        // Check if a dialog is already open to prevent conflicts
                        if (DialogHost.IsDialogOpen("RootDialog"))
                        {
                            Debug.WriteLine("Dialog already open, ignoring ShowCustomerUnpaidSalesCommand");
                            return;
                        }

                        // Show customer selection window
                        var customerWindow = new Views.CustomerSelectionWindow();

                        if (Application.Current.MainWindow != null &&
                            Application.Current.MainWindow.IsLoaded)
                        {
                            customerWindow.Owner = Application.Current.MainWindow;
                        }

                        var result = customerWindow.ShowDialog();

                        if (result == true && customerWindow.SelectedCustomer != null)
                        {
                            // Show unpaid sales for selected customer
                            var dialog = new UnpaidSalesStatsDetailsDialog(this, customerWindow.SelectedCustomer);
                            await DialogHost.Show(dialog, "RootDialog");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error showing customer unpaid sales: {ex.Message}");
                        MessageBox.Show(
                            $"Error showing customer unpaid sales: {ex.Message}",
                            "Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }));
            }
        }

        public string CustomMetricDescription
        {
            get
            {
                if (SelectedMetricType == null) return "";
                switch (SelectedMetricType.Key)
                {
                    case "sales": return $"{FilteredMetricValue:N2} DA";
                    case "profit": return $"{FilteredMetricValue:N2} DA";
                    case "margin": return $"{FilteredMetricValue:N2}%";
                    case "items": return $"{FilteredMetricValue:N0}";
                    default: return FilteredMetricValue.ToString("N2");
                }
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Loads all dashboard data
        /// </summary>
        public async Task LoadDashboardDataAsync()
        {
            try
            {
                StartLoading("Dashboard");
                System.Diagnostics.Debug.WriteLine("LoadDashboardDataAsync: Starting dashboard data load");

                // Get date range from the selected period
                var dateRange = _parameterManager.GetDateRange(_selectedGlobalPeriod);
                _startDate = dateRange.start;
                _endDate = dateRange.end;
                System.Diagnostics.Debug.WriteLine($"LoadDashboardDataAsync: Date range {_startDate:yyyy-MM-dd} to {_endDate:yyyy-MM-dd}");

                // Update UI properties
                OnPropertyChanged(nameof(StartDate));
                OnPropertyChanged(nameof(EndDate));

                // Load all initial data in parallel
                System.Diagnostics.Debug.WriteLine("LoadDashboardDataAsync: Starting parallel tasks");
                await Task.WhenAll(
                    LoadQuickStatsAsync(),
                    LoadSalesTrendDataAsync(),
                    LoadExpensesMetricsAsync(), // Use selected period, not global dates
                    LoadFilteredDataAsync(),
                    LoadAlertsDataAsync()  // ✅ FIX: Include alerts data in main dashboard load
                );
                System.Diagnostics.Debug.WriteLine("LoadDashboardDataAsync: All parallel tasks completed");

                // Trigger property changes to ensure UI updates
                OnPropertyChanged(nameof(GrossProfit));
                OnPropertyChanged(nameof(ProfitMargin));
                OnPropertyChanged(nameof(FilteredProfit));
                OnPropertyChanged(nameof(FilteredProfitMargin));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading dashboard: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                ActiveAlerts.Add(new Alert
                {
                    Type = AlertType.Error,
                    Message = "Failed to load dashboard data",
                    Icon = "⚠"
                });
            }
            finally
            {
                StopLoading("Dashboard");
            }
        }
        
        /// <summary>
        /// Loads a specific section of the dashboard
        /// </summary>
        public async Task LoadSectionAsync(string sectionName)
        {
            if (IsSectionLoading(sectionName))
                return;
                
            try
            {
                StartLoading(sectionName);
                
                switch (sectionName)
                {
                    case "Overview":
                        await LoadQuickStatsAsync();
                        await LoadSalesTrendDataAsync();
                        await LoadExpensesMetricsAsync(); // Use selected period
                        break;
                        
                    case "SalesTrend":
                        await LoadSalesTrendDataAsync();
                        break;
                        
                    case "ProductPerformance":
                        await LoadProductPerformanceDataAsync();
                        break;

                    case "Expenses":
                        await LoadExpensesMetricsAsync(); // Use selected period
                        break;
                    default:
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading section {sectionName}: {ex.Message}");
            }
            finally
            {
                StopLoading(sectionName);
            }
        }
        
        /// <summary>
        /// Refreshes the dashboard alerts
        /// </summary>
        public async Task RefreshAlertsAsync()
        {
            try
            {
                var alertData = await _dataProvider.GetDashboardAlertsAsync();
                
                Application.Current.Dispatcher.Invoke(() => {
                    ActiveAlerts.Clear();
                    
                    if (alertData?.LowStockProducts?.Count > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Warning,
                            Message = $"{alertData.LowStockProducts.Count} products have low stock",
                            Icon = "⚠"
                        });
                    }
                    
                    if (alertData?.ExpiringProducts?.Count > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Warning,
                            Message = $"{alertData.ExpiringProducts.Count} products are expiring soon",
                            Icon = "⚠"
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing alerts: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Refreshes quick stats data
        /// </summary>
        public async Task RefreshQuickStatsAsync()
        {
            await LoadQuickStatsAsync();
        }
        
        /// <summary>
        /// Refreshes sales trend data
        /// </summary>
        public async Task RefreshSalesTrendAsync()
        {
            await LoadSalesTrendDataAsync();
        }
        
        /// <summary>
        /// Triggers scrolling to a specific section
        /// </summary>
        public void ScrollToSection(string sectionName)
        {
            ScrollToSectionRequested?.Invoke(this, sectionName);
        }
        
        /// <summary>
        /// Refreshes all dashboard data for the selected period
        /// </summary>
        private async Task RefreshAllDashboardData()
        {
            try
            {
                // Start loading indicator
                StartLoading("Dashboard");
                
                // Get date range from the selected period
                var dateRange = _parameterManager.GetDateRange(_selectedGlobalPeriod);
                _startDate = dateRange.start;
                _endDate = dateRange.end;
                
                Debug.WriteLine($"===== REFRESHING ALL DATA: Period={_selectedGlobalPeriod.DisplayName}, Range={_startDate:yyyy-MM-dd} to {_endDate:yyyy-MM-dd} =====");
                
                // Update all UI properties
                OnPropertyChanged(nameof(StartDate));
                OnPropertyChanged(nameof(EndDate));
                
                // Refresh all dashboard components in parallel
                await Task.WhenAll(
                    LoadQuickStatsAsync(),
                    LoadSalesTrendDataAsync(),
                    LoadExpensesMetricsAsync(_startDate, _endDate),
                    LoadProductPerformanceDataAsync(),
                    LoadFilteredDataAsync()
                );
                
                // Optional: If customer insights or user performance sections are visible, refresh them too

                
                Debug.WriteLine($"===== REFRESH COMPLETED: Period={_selectedGlobalPeriod.DisplayName} =====");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing dashboard: {ex.Message}");
            }
            finally
            {
                // Stop loading indicator
                StopLoading("Dashboard");
            }
        }
        
        /// <summary>
        /// ✅ PERFORMANCE FIX: Loads dashboard alerts data on background thread to prevent UI blocking
        /// </summary>
        private async Task LoadAlertsDataAsync()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Initialize UI collections on UI thread first
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    if (ActiveAlerts == null)
                    {
                        ActiveAlerts = new ObservableCollection<Alert>();
                    }
                    else
                    {
                        ActiveAlerts.Clear();
                    }
                }, DispatcherPriority.Background);

                // ✅ PERFORMANCE FIX: Load all data on background thread to prevent UI blocking
                var alertData = await Task.Run(async () => {
                    var data = new {
                        ExpiringProducts = _dataProvider.GetExpiringProducts(30) ?? new List<Product>(),
                        LowStockProducts = await _dataProvider.GetLowStockProductsAsync() ?? new List<Product>(),
                        UnpaidSales = _dataProvider.GetUnpaidSales() ?? new List<Sale>()
                    };

                    // ✅ DEBUG: Add detailed logging to understand what's happening
                    Debug.WriteLine($"[LOW_STOCK_DEBUG] Total products from database query: {data.LowStockProducts.Count}");

                    if (data.LowStockProducts.Any())
                    {
                        Debug.WriteLine($"[LOW_STOCK_DEBUG] First few products:");
                        foreach (var product in data.LowStockProducts.Take(5))
                        {
                            var totalStock = product.GetTotalStock();
                            var isLowStock = totalStock > 0 && (totalStock <= product.MinimumStock || totalStock <= product.ReorderPoint);
                            var isOutOfStock = totalStock <= 0;
                            Debug.WriteLine($"[LOW_STOCK_DEBUG]   - {product.Name}: Stock={totalStock}, Min={product.MinimumStock}, Reorder={product.ReorderPoint}, IsLow={isLowStock}, IsOut={isOutOfStock}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"[LOW_STOCK_DEBUG] No products returned from database query - this explains why count is 0");
                    }

                    var lowStockCount = data.LowStockProducts.Count(p => p != null && p.GetTotalStock() > 0 &&
                                                                         (p.GetTotalStock() <= p.MinimumStock || p.GetTotalStock() <= p.ReorderPoint));
                    var outOfStockCount = data.LowStockProducts.Count(p => p != null && p.GetTotalStock() <= 0);

                    Debug.WriteLine($"[LOW_STOCK_DEBUG] Calculated low stock count: {lowStockCount}");
                    Debug.WriteLine($"[LOW_STOCK_DEBUG] Calculated out of stock count: {outOfStockCount}");

                    return new {
                        ExpiringProductsCount = data.ExpiringProducts.Count(p => p != null && p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry > 0 && p.DaysUntilExpiry <= 30),
                        ExpiredProductsCount = data.ExpiringProducts.Count(p => p != null && p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry <= 0),
                        // ✅ FIX: Use consistent low stock logic - products are already filtered by the database query
                        // so we just need to count products that have stock > 0 and are considered low stock
                        LowStockCount = lowStockCount,
                        OutOfStockCount = outOfStockCount,
                        UnpaidSalesCount = data.UnpaidSales.Count(),
                        UnpaidSalesAmount = data.UnpaidSales.Sum(s => s?.RemainingAmount ?? 0),
                        OverdueSales = data.UnpaidSales.Where(s => s != null && s.DueDate.HasValue && s.DueDate.Value < DateTime.Now).ToList()
                    };
                });

                // ✅ PERFORMANCE FIX: Update UI properties on main thread with background priority
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    ExpiringProductsCount = alertData.ExpiringProductsCount;
                    ExpiredProductsCount = alertData.ExpiredProductsCount;
                    LowStockCount = alertData.LowStockCount;
                    OutOfStockCount = alertData.OutOfStockCount;
                    UnpaidSalesCount = alertData.UnpaidSalesCount;
                    UnpaidSalesAmount = alertData.UnpaidSalesAmount;
                    OverdueSalesCount = alertData.OverdueSales.Count();
                    OverdueSalesAmount = alertData.OverdueSales.Sum(s => s?.RemainingAmount ?? 0);
                }, DispatcherPriority.Background);

                // ✅ PERFORMANCE FIX: Add alerts on UI thread with background priority
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    if (ExpiringProductsCount > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Warning,
                            Message = $"{ExpiringProductsCount} products expiring soon",
                            Icon = "⚠"
                        });
                    }

                    if (ExpiredProductsCount > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Error,
                            Message = $"{ExpiredProductsCount} products expired",
                            Icon = "⚠"
                        });
                    }

                    if (LowStockCount > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Warning,
                            Message = $"{LowStockCount} products low on stock",
                            Icon = "⚠"
                        });
                    }

                    if (OutOfStockCount > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Error,
                            Message = $"{OutOfStockCount} products out of stock",
                            Icon = "⚠"
                        });
                    }

                    if (OverdueSalesCount > 0)
                    {
                        ActiveAlerts.Add(new Alert
                        {
                            Type = AlertType.Warning,
                            Message = $"{OverdueSalesCount} overdue sales ({OverdueSalesAmount:C2})",
                            Icon = "⚠"
                        });
                    }
                }, DispatcherPriority.Background);

                // ✅ PERFORMANCE FIX: Property change notifications are handled by the UI updates above
                OnPropertyChanged(nameof(LowStockCount));
                OnPropertyChanged(nameof(OutOfStockCount));
                OnPropertyChanged(nameof(UnpaidSalesCount));
                OnPropertyChanged(nameof(UnpaidSalesAmount));
                OnPropertyChanged(nameof(OverdueSalesCount));
                OnPropertyChanged(nameof(OverdueSalesAmount));
                OnPropertyChanged(nameof(ActiveAlerts));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading alerts: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        #endregion
        
        #region Private Methods
        
        /// <summary>
        /// Sets up progressive loading for the dashboard
        /// </summary>
        private void InitializeProgressiveLoading()
        {
            // No need to load everything at once
            // The view will call LoadSectionAsync when sections become visible
        }
        
        /// <summary>
        /// Marks a section as loading
        /// </summary>
        private void StartLoading(string section)
        {
            _sectionLoadingStates[section] = true;
            OnPropertyChanged($"IsSectionLoading_{section}");
            
            _loadingOperations++;
            IsLoading = _loadingOperations > 0;
        }
        
        /// <summary>
        /// Marks a section as no longer loading
        /// </summary>
        private void StopLoading(string section)
        {
            _sectionLoadingStates[section] = false;
            OnPropertyChanged($"IsSectionLoading_{section}");
            
            _loadingOperations = Math.Max(0, _loadingOperations - 1);
            IsLoading = _loadingOperations > 0;
        }
        
        /// <summary>
        /// Updates the date range based on selection
        /// </summary>
        private void UpdateDateRangeBasedOnSelection(string range)
        {
            // Find matching time period
            var period = _parameterManager.TimePeriods.FirstOrDefault(p => p.DisplayName == range);
            if (period != null)
            {
                var dateRange = _parameterManager.GetDateRange(period);
                StartDate = dateRange.start;
                EndDate = dateRange.end;
            }
        }
        
        /// <summary>
        /// Reset zoom on chart
        /// </summary>
        private void ResetZoom()
        {
            // Reset to default date range
            SelectedDateRange = "Last 30 Days";
            IsZoomed = false;
        }
        
        /// <summary>
        /// Handle chart parameter selection
        /// </summary>
        private void SelectChartParameter(ChartParameter parameter)
        {
            if (parameter != null)
            {
                SelectedChartParameter = parameter;
            }
        }
        
        /// <summary>
        /// Update the date range
        /// </summary>
        private void UpdateDateRange()
        {
            _ = LoadSalesTrendDataAsync();
        }
        
        /// <summary>
        /// Loads quick stats data
        /// </summary>
        private async Task LoadQuickStatsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("LoadQuickStatsAsync: Starting quick stats load");
                var now = DateTime.Now;
                var today = DateTime.Today;
                System.Diagnostics.Debug.WriteLine($"LoadQuickStatsAsync: Today = {today:yyyy-MM-dd}, Now = {now:yyyy-MM-dd HH:mm:ss}");
                Debug.WriteLine($"=== LoadQuickStatsAsync: Current time: {now:yyyy-MM-dd HH:mm:ss} ===");

                // First, let's check if there are any sales at all in the database
                var allSalesData = await _dataService.GetSalesDataAsync(DateTime.MinValue, DateTime.MaxValue, "all_time");
                Debug.WriteLine($"=== Total sales in database: {allSalesData?.Count ?? 0} ===");
                if (allSalesData?.Any() == true)
                {
                    Debug.WriteLine($"=== Date range of sales: {allSalesData.Min(s => s.Date):yyyy-MM-dd} to {allSalesData.Max(s => s.Date):yyyy-MM-dd} ===");
                    Debug.WriteLine($"=== Total value of all sales: {allSalesData.Sum(s => s.GrandTotal):N2} DA ===");
                }

                // Get sales data for the selected period
                var (periodStart, periodEnd) = GetDateRangeForPeriod(SelectedQuickStatsPeriod ?? QuickStatsPeriods.FirstOrDefault());
                var periodSalesData = await _dataService.GetSalesDataAsync(periodStart, periodEnd, $"period_{SelectedQuickStatsPeriod?.Type}");
                PeriodSales = periodSalesData?.Sum(s => s.GrandTotal) ?? 0;
                Debug.WriteLine($"=== Period sales ({periodStart:yyyy-MM-dd} to {periodEnd:yyyy-MM-dd HH:mm:ss}): {PeriodSales:N2} DA from {periodSalesData?.Count ?? 0} records ===");

                // No fallback to recent sales - show actual 0 when no sales exist for the period
                Debug.WriteLine($"=== Period sales for {today:yyyy-MM-dd}: {PeriodSales:N2} DA (no fallback applied) ===");

                // Get week's sales data (always calculate for comparison)
                var weekStart = today.AddDays(-7);
                var weekSalesData = await _dataService.GetSalesDataAsync(weekStart, now, "week");
                WeekSales = weekSalesData?.Sum(s => s.GrandTotal) ?? 0;
                Debug.WriteLine($"=== Week sales ({weekStart:yyyy-MM-dd} to {now:yyyy-MM-dd HH:mm:ss}): {WeekSales:N2} DA from {weekSalesData?.Count ?? 0} records ===");

                // Get month's sales data (always calculate for comparison)
                var monthStart = new DateTime(today.Year, today.Month, 1);
                var monthSalesData = await _dataService.GetSalesDataAsync(monthStart, now, "month");
                MonthSales = monthSalesData?.Sum(s => s.GrandTotal) ?? 0;
                Debug.WriteLine($"=== Month sales: {MonthSales:N2} DA ===");

                // Calculate period-based profit
                GrossProfit = periodSalesData?.Sum(s => s.Profit) ?? 0;
                Debug.WriteLine($"=== Period profit: {GrossProfit:N2} DA ===");
                
                // Calculate gross profit and margin for the selected period
                var dateRange = _parameterManager.GetDateRange(_selectedQuickStatsPeriod ?? _selectedGlobalPeriod);
                var globalPeriodSalesData = await _dataService.GetSalesDataAsync(dateRange.start, dateRange.end, (_selectedQuickStatsPeriod ?? _selectedGlobalPeriod)?.DisplayName);

                if (globalPeriodSalesData != null && globalPeriodSalesData.Any())
                {
                    decimal totalSales = globalPeriodSalesData.Sum(s => s.GrandTotal);
                    decimal totalProfit = globalPeriodSalesData.Sum(s => s.Profit);

                    GrossProfit = totalProfit;
                    ProfitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

                    Debug.WriteLine($"=== Gross profit: {GrossProfit:N2} DA ===");
                    Debug.WriteLine($"=== Profit margin: {ProfitMargin:N2}% ===");
                }
                else
                {
                    GrossProfit = 0;
                    ProfitMargin = 0;
                    Debug.WriteLine("=== No sales data found for profit calculation ===");
                }

                // Load expenses metrics for the SELECTED QUICK STATS PERIOD (not global period)
                var expenseDateRange = _parameterManager.GetDateRange(_selectedQuickStatsPeriod ?? _selectedGlobalPeriod);
                Debug.WriteLine($"=== Loading expenses for period: {(_selectedQuickStatsPeriod ?? _selectedGlobalPeriod)?.DisplayName} ({expenseDateRange.start:yyyy-MM-dd} to {expenseDateRange.end:yyyy-MM-dd}) ===");
                await LoadExpensesMetricsAsync(expenseDateRange.start, expenseDateRange.end);
                
                Debug.WriteLine($"=== LoadQuickStatsAsync completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading quick stats: {ex.Message}");
                // Reset all values on error
                TodaySales = 0;
                WeekSales = 0;
                MonthSales = 0;
                GrossProfit = 0;
                ProfitMargin = 0;
            }
        }
        
        /// <summary>
        /// Gets the correct date range for expense loading based on selected period
        /// </summary>
        private (DateTime start, DateTime end) GetExpenseDateRange()
        {
            // Use the selected quick stats period for expense calculations
            var selectedPeriod = _selectedQuickStatsPeriod ?? _selectedGlobalPeriod;
            if (selectedPeriod != null)
            {
                var dateRange = _parameterManager.GetDateRange(selectedPeriod);
                Debug.WriteLine($"GetExpenseDateRange: Using period '{selectedPeriod.DisplayName}' -> {dateRange.start:yyyy-MM-dd} to {dateRange.end:yyyy-MM-dd}");
                return dateRange;
            }

            // Fallback to global dates
            Debug.WriteLine($"GetExpenseDateRange: Fallback to global dates -> {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}");
            return (StartDate, EndDate);
        }

        /// <summary>
        /// Loads expenses data for the dashboard using the selected period
        /// </summary>
        private async Task LoadExpensesMetricsAsync()
        {
            var dateRange = GetExpenseDateRange();
            await LoadExpensesMetricsAsync(dateRange.start, dateRange.end);
        }

        /// <summary>
        /// Loads expenses data for the dashboard
        /// </summary>
        private async Task LoadExpensesMetricsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // Get real expense data from the database
                var expenses = await _dataProvider.GetBusinessExpensesAsync(startDate, endDate);
                Debug.WriteLine($"Found {expenses?.Count ?? 0} business expenses from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                // Get previous period for comparison
                var previousPeriod = _parameterManager.GetPreviousPeriodRange(startDate, endDate);
                var previousExpenses = await _dataProvider.GetBusinessExpensesAsync(previousPeriod.start, previousPeriod.end);
                
                // Calculate current period metrics
                decimal currentTotal = expenses?.Sum(e => e.Amount) ?? 0;
                decimal previousTotal = previousExpenses?.Sum(e => e.Amount) ?? 0;
                
                // Set properties
                MonthlyExpensesCount = expenses?.Count ?? 0;
                MonthlyExpensesAmount = currentTotal;
                MonthlyExpenses = currentTotal;
                
                // Calculate growth percentage
                ExpensesGrowth = previousTotal > 0 ? ((currentTotal - previousTotal) / previousTotal) * 100 : 0;
                
                // Get upcoming expenses for the next 30 days
                var upcomingStart = DateTime.Now;
                var upcomingEnd = DateTime.Now.AddDays(30);
                var upcomingExpenses = await _dataProvider.GetBusinessExpensesAsync(upcomingStart, upcomingEnd);
                
                // Set upcoming expenses properties
                UpcomingExpensesCount = upcomingExpenses?.Count ?? 0;
                UpcomingExpensesAmount = upcomingExpenses?.Sum(e => e.Amount) ?? 0;
                UpcomingExpenses = upcomingExpenses?.Sum(e => e.Amount) ?? 0;
                
                Debug.WriteLine($"Current period expenses: {currentTotal:N2} DA");
                Debug.WriteLine($"Previous period expenses: {previousTotal:N2} DA");
                Debug.WriteLine($"Expenses growth: {ExpensesGrowth:N2}%");
                Debug.WriteLine($"Upcoming expenses: {UpcomingExpenses:N2} DA");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading expenses metrics: {ex.Message}");
                // Reset values on error
                MonthlyExpensesCount = 0;
                MonthlyExpensesAmount = 0;
                MonthlyExpenses = 0;
                ExpensesGrowth = 0;
                UpcomingExpensesCount = 0;
                UpcomingExpensesAmount = 0;
                UpcomingExpenses = 0;
            }
        }
        
        /// <summary>
        /// Loads sales trend data
        /// </summary>
        private async Task LoadSalesTrendDataAsync()
        {
            try
            {
                await Application.Current.Dispatcher.InvokeAsync(() => StartLoading("SalesTrend"));

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    TrendChartIsLoading = true;
                    TrendChartNoDataMessage = string.Empty;
                });

                // Use the selected period for trend data
                var (startDate, endDate) = GetDateRangeForPeriod(SelectedQuickStatsPeriod ?? QuickStatsPeriods.FirstOrDefault());
                Debug.WriteLine($"=== Loading sales trend for period: {SelectedQuickStatsPeriod?.DisplayName} ({startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}) ===");
                
                // Get sales data for the selected period
                Debug.WriteLine($"=== LoadSalesTrendDataAsync: Loading chart data for {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd} ===");
                var aggregations = await _dataService.GetSalesAggregationAsync(
                    startDate, endDate, _selectedChartParameter?.Key ?? "sales",
                    SelectedQuickStatsPeriod?.DisplayName ?? "Today");

                Debug.WriteLine($"=== Chart aggregations: {aggregations?.Count ?? 0} data points ===");
                if (aggregations?.Any() == true)
                {
                    Debug.WriteLine($"=== Chart data range: {aggregations.Min(a => a.Date):yyyy-MM-dd} to {aggregations.Max(a => a.Date):yyyy-MM-dd} ===");
                    Debug.WriteLine($"=== Chart total sales: {aggregations.Sum(a => a.TotalSales):N2} DA ===");
                }

                if (aggregations == null || !aggregations.Any())
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        TrendChartNoDataMessage = "No data available for the selected period";
                        SalesTrendSeries = new SeriesCollection();
                        SalesTrendLabels = Array.Empty<string>();
                    });
                    Debug.WriteLine("=== No chart data available ===");
                    return;
                }
                
                // Extract the metric data
                double dateSpan = (endDate - startDate).TotalDays;
                var (values, labels) = _dataService.ExtractMetricData(
                    aggregations,
                    _selectedChartParameter?.Key ?? "sales",
                    date => FormatDateLabel(date, aggregations.Count));
                
                // Create chart series on UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        var mainSeries = _chartService.CreateMainSeries(values, dateSpan, _selectedChartParameter?.Key);

                        if (mainSeries == null)
                        {
                            TrendChartNoDataMessage = "Could not create chart with the available data";
                            return;
                        }

                        var series = new SeriesCollection { mainSeries };

                        // Add trend line if we have enough data
                        if (values.Count >= 5)
                        {
                            var trendLine = _chartService.CalculateTrendLine(values);
                            if (trendLine != null)
                            {
                                series.Add(trendLine);
                            }
                        }

                        // Add moving average for shorter periods
                        if (dateSpan <= 90 && values.Count >= 7)
                        {
                            var movingAverage = _chartService.CalculateMovingAverage(values, dateSpan);
                            if (movingAverage != null && movingAverage.Count > 0)
                            {
                                var movingAverageSeries = _chartService.CreateMovingAverageSeries(movingAverage);
                                if (movingAverageSeries != null)
                                {
                                    series.Add(movingAverageSeries);
                                }
                            }
                        }

                        // Update UI properties
                        SalesTrendSeries = series;
                        SalesTrendLabels = labels.ToArray();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error creating chart series: {ex.Message}");
                        TrendChartNoDataMessage = "Error creating chart";
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading sales trend: {ex.Message}");
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    TrendChartNoDataMessage = "Error loading chart data";
                });
            }
            finally
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StopLoading("SalesTrend");
                    TrendChartIsLoading = false;
                });
            }
        }
        
        /// <summary>
        /// Loads filtered metrics based on selected period and type
        /// </summary>
        private async Task LoadFilteredDataAsync()
        {
            if (SelectedQuickStatsPeriod == null) return;

            try
            {
                var (startDate, endDate) = GetDateRangeForPeriod(SelectedQuickStatsPeriod);
                var (prevStartDate, prevEndDate) = GetPreviousPeriodRange(startDate, endDate);

                Debug.WriteLine($"=== LoadFilteredDataAsync: Period={SelectedQuickStatsPeriod.DisplayName} ===");
                Debug.WriteLine($"=== Current period: {startDate:yyyy-MM-dd HH:mm:ss} to {endDate:yyyy-MM-dd HH:mm:ss} ===");
                Debug.WriteLine($"=== Previous period: {prevStartDate:yyyy-MM-dd HH:mm:ss} to {prevEndDate:yyyy-MM-dd HH:mm:ss} ===");

                // Load current period data
                var currentSales = await _dataService.GetSalesDataAsync(startDate, endDate);
                var previousSales = await _dataService.GetSalesDataAsync(prevStartDate, prevEndDate);

                Debug.WriteLine($"=== Current sales: {currentSales?.Count ?? 0} records, Total: {currentSales?.Sum(s => s.GrandTotal) ?? 0:N2} DA ===");
                Debug.WriteLine($"=== Previous sales: {previousSales?.Count ?? 0} records, Total: {previousSales?.Sum(s => s.GrandTotal) ?? 0:N2} DA ===");

                // Update Sales Card
                FilteredSales = currentSales.Sum(s => s.GrandTotal);
                var prevTotal = previousSales.Sum(s => s.GrandTotal);
                SalesGrowth = prevTotal > 0 ? ((FilteredSales - prevTotal) / prevTotal) * 100 : 0;

                // Update Profit Card
                FilteredProfit = currentSales.Sum(s => s.Profit);
                var prevProfit = previousSales.Sum(s => s.Profit);
                ProfitGrowth = prevProfit > 0 ? ((FilteredProfit - prevProfit) / prevProfit) * 100 : 0;
                FilteredProfitMargin = FilteredSales > 0 ? (FilteredProfit / FilteredSales) * 100 : 0;

                // Update Expenses Card
                var currentExpenses = await _dataProvider.GetBusinessExpensesAsync(startDate, endDate);
                var previousExpenses = await _dataProvider.GetBusinessExpensesAsync(prevStartDate, prevEndDate);
                
                MonthlyExpensesAmount = currentExpenses.Sum(e => e.Amount);
                MonthlyExpensesCount = currentExpenses.Count();
                var prevExpensesTotal = previousExpenses.Sum(e => e.Amount);
                ExpensesGrowth = prevExpensesTotal > 0 ? ((MonthlyExpensesAmount - prevExpensesTotal) / prevExpensesTotal) * 100 : 0;

                // Update Unpaid Sales Card
                var unpaidSales = _dataProvider.GetUnpaidSales()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .ToList();
                
                UnpaidSalesCount = unpaidSales.Count();
                UnpaidSalesAmount = unpaidSales.Sum(s => s.RemainingAmount);
                
                var overdueSales = unpaidSales.Where(s => s.DueDate.HasValue && s.DueDate.Value < DateTime.Now).ToList();
                OverdueSalesCount = overdueSales.Count();
                OverdueSalesAmount = overdueSales.Sum(s => s.RemainingAmount);

                // Update Stock Alert Card
                var expiringProducts = _dataProvider.GetExpiringProducts(30);
                // ✅ FIX: Add null check for DaysUntilExpiry
                ExpiringProductsCount = expiringProducts.Count(p => p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry > 0 && p.DaysUntilExpiry <= 30);
                ExpiredProductsCount = expiringProducts.Count(p => p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry <= 0);

                var lowStockProducts = await _dataProvider.GetLowStockProductsAsync();
                // ✅ FIX: Use consistent low stock logic
                LowStockCount = lowStockProducts.Count(p => p.GetTotalStock() > 0 &&
                                                            (p.GetTotalStock() <= p.MinimumStock || p.GetTotalStock() <= p.ReorderPoint));
                OutOfStockCount = lowStockProducts.Count(p => p.GetTotalStock() <= 0);

                // Update other metrics based on selection
                UpdateMetricValue(currentSales, previousSales);

                // Notify UI of all changes
                OnPropertyChanged(nameof(FilteredSales));
                OnPropertyChanged(nameof(SalesGrowth));
                OnPropertyChanged(nameof(FilteredProfit));
                OnPropertyChanged(nameof(ProfitGrowth));
                OnPropertyChanged(nameof(FilteredProfitMargin));
                OnPropertyChanged(nameof(MonthlyExpensesAmount));
                OnPropertyChanged(nameof(MonthlyExpensesCount));
                OnPropertyChanged(nameof(ExpensesGrowth));
                OnPropertyChanged(nameof(UnpaidSalesCount));
                OnPropertyChanged(nameof(UnpaidSalesAmount));
                OnPropertyChanged(nameof(OverdueSalesCount));
                OnPropertyChanged(nameof(OverdueSalesAmount));
                OnPropertyChanged(nameof(ExpiringProductsCount));
                OnPropertyChanged(nameof(ExpiredProductsCount));
                OnPropertyChanged(nameof(LowStockCount));
                OnPropertyChanged(nameof(OutOfStockCount));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading filtered data: {ex.Message}");
            }
        }
        
        private (DateTime start, DateTime end) GetPreviousPeriodRange(DateTime start, DateTime end)
        {
            var periodLength = end - start;
            
            // For "Today" or "Yesterday" (1-day periods)
            if (periodLength.Days <= 1)
            {
                return (start.AddDays(-1), end.AddDays(-1));
            }
            // For "This Month" or "Last Month"
            else if (start.Day == 1 && end.Day == DateTime.DaysInMonth(end.Year, end.Month))
            {
                var previousMonthStart = start.AddMonths(-1);
                return (
                    new DateTime(previousMonthStart.Year, previousMonthStart.Month, 1),
                    new DateTime(previousMonthStart.Year, previousMonthStart.Month, DateTime.DaysInMonth(previousMonthStart.Year, previousMonthStart.Month))
                );
            }
            // For other periods (Last 7 days, Last 30 days, etc.)
            else
            {
                return (
                    start.AddDays(-periodLength.Days),
                    start.AddMilliseconds(-1)
                );
            }
        }
        
        /// <summary>
        /// Loads product performance data
        /// </summary>
        private async Task LoadProductPerformanceDataAsync()
        {
            try
            {
                StartLoading("ProductPerformance");
                IsProductPerformanceLoading = true;

                // Note: Sample data creation removed for now to avoid compilation issues

                // Use the same date range as sales data to ensure consistency
                var (startDate, endDate) = GetDateRangeForPeriod(SelectedQuickStatsPeriod ?? QuickStatsPeriods.FirstOrDefault());

                Debug.WriteLine($"================ PRODUCT PERFORMANCE DEBUGGING =================");
                Debug.WriteLine($"Selected period: {SelectedQuickStatsPeriod?.DisplayName}");
                Debug.WriteLine($"Date range: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                // Debug: Check actual SaleItems in database
                try
                {
                    using (var context = new POSDbContext())
                    {
                        var allSaleItems = await context.SaleItems
                            .Include(si => si.Sale)
                            .Include(si => si.Product)
                            .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                            .ToListAsync();

                        Debug.WriteLine($"=== DATABASE SALEITEMS DEBUG ===");
                        Debug.WriteLine($"Total SaleItems in date range: {allSaleItems.Count}");

                        foreach (var item in allSaleItems.Take(5))
                        {
                            Debug.WriteLine($"SaleItem ID: {item.Id}, SaleId: {item.SaleId}, ProductId: {item.ProductId}, " +
                                          $"Product: {item.Product?.Name ?? "NULL"}, Quantity: {item.Quantity}, " +
                                          $"Sale Date: {item.Sale?.SaleDate:yyyy-MM-dd HH:mm:ss}");
                        }

                        var productSales = allSaleItems
                            .GroupBy(si => si.ProductId)
                            .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(si => si.Quantity), Count = g.Count() })
                            .OrderByDescending(x => x.TotalQuantity)
                            .ToList();

                        Debug.WriteLine($"=== PRODUCT SALES AGGREGATION ===");
                        foreach (var ps in productSales.Take(5))
                        {
                            Debug.WriteLine($"ProductId: {ps.ProductId}, TotalQuantity: {ps.TotalQuantity}, SaleItems: {ps.Count}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error debugging SaleItems: {ex.Message}");
                }

                // First try to get any products at all to see if there's data
                var allProducts = await _dataProvider.GetTopSellingProductsAsync(25);
                Debug.WriteLine($"Total products available: {allProducts?.Count ?? 0}");
                
                // Try to get products with date filtering first
                List<Product> products = null;
                try
                {
                    products = await _dataProvider.GetTopSellingProductsAsync(25, startDate, endDate);
                    Debug.WriteLine($"Retrieved {products?.Count ?? 0} products with date filtering");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error with date filtering: {ex.Message}");
                    // Fall back to getting all products without date filtering
                    products = allProducts;
                    Debug.WriteLine($"Using fallback: {products?.Count ?? 0} products without date filtering");
                }

                if (products == null || products.Count == 0)
                {
                    Debug.WriteLine("No product data returned from database - trying fallback");
                    // If still no data, try the basic method without date filtering
                    products = allProducts;

                    if (products == null || products.Count == 0)
                    {
                        Debug.WriteLine("No products found even with fallback - creating empty charts");

                        // Create empty chart series to show "No data" message
                        await Application.Current.Dispatcher.InvokeAsync(() => {
                            ProductChartSeries = new SeriesCollection();
                            ProfitChartSeries = new SeriesCollection();
                            MarginChartSeries = new SeriesCollection();
                            ItemsSoldChartSeries = new SeriesCollection();
                            CategoryChartSeries = new SeriesCollection();
                            CategoryProfitSeries = new SeriesCollection();
                            CategoryMarginSeries = new SeriesCollection();
                            CategoryItemsSoldSeries = new SeriesCollection();
                            TopProducts = new VirtualizingCollection<ProductPerformance>(new List<ProductPerformance>());
                        });
                        return;
                    }
                }
                
                // Inspect the data structure
                var firstProduct = products.FirstOrDefault();
                if (firstProduct != null)
                {
                    Debug.WriteLine($"First product: {firstProduct.Name}");
                    Debug.WriteLine($"Has Sales collection: {firstProduct.Sales != null}");
                    Debug.WriteLine($"Sales count: {firstProduct.Sales?.Count ?? 0}");
                    
                    var firstSale = firstProduct.Sales?.FirstOrDefault();
                    if (firstSale != null)
                    {
                        Debug.WriteLine($"First sale: Product={firstSale.Product?.Name}, Quantity={firstSale.Quantity}");
                        Debug.WriteLine($"Has Sale reference: {firstSale.Sale != null}");
                        if (firstSale.Sale != null)
                        {
                            Debug.WriteLine($"Sale date: {firstSale.Sale.SaleDate}");
                        }
                    }
                }
                
                // If we have no date range selected, use a one-year fallback
                if (startDate == default || endDate == default)
                {
                    Debug.WriteLine("WARNING: Using fallback date range (last year)");
                    endDate = DateTime.Now;
                    startDate = endDate.AddYears(-1);
                }
                
                Debug.WriteLine($"Using date range for filtering: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                // Process the data WITH date filtering now
                var productPerfList = await Task.Run(() => products.Select(product => {
                    var category = product.CategoryId > 0 ? _dataProvider.GetCategoryById(product.CategoryId) : null;
                    
                    // Calculate values with safety checks
                    decimal revenue = 0;
                    decimal profit = 0;
                    int itemsSold = 0;
                    
                    try
                    {
                        if (product.Sales != null)
                        {
                            // Filter sales by the selected date range
                            var salesList = product.Sales
                                .Where(s => s.Sale != null && 
                                           s.Sale.SaleDate >= startDate && 
                                           s.Sale.SaleDate <= endDate)
                                .ToList();
                            
                            Debug.WriteLine($"Product {product.Name}: Filtered from {product.Sales.Count} to {salesList.Count} sales");
                            
                            // If no sales in the period, show actual 0 values
                            if (salesList.Count == 0)
                            {
                                Debug.WriteLine($"No sales found in period for {product.Name}. Showing actual 0 values.");
                                revenue = 0;
                                profit = 0;
                                itemsSold = 0;
                            }
                            else
                            {
                                // Calculate metrics on filtered sales
                                revenue = salesList.Sum(s => s.Quantity * s.Product.SellingPrice);
                                profit = salesList.Sum(s => s.Quantity * (s.Product.SellingPrice - s.Product.PurchasePrice));
                                itemsSold = (int)salesList.Sum(s => s.Quantity);
                            }
                            
                            Debug.WriteLine($"Product {product.Name}: Revenue={revenue}, Profit={profit}, Items={itemsSold}");
                        }
                        else
                        {
                            Debug.WriteLine($"Product {product.Name} has no sales data - showing 0 values");
                            revenue = 0;
                            profit = 0;
                            itemsSold = 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error calculating values for product {product.Name}: {ex.Message}");
                    }
                    
                    return new ProductPerformance
                    {
                        Name = string.IsNullOrWhiteSpace(product.Name) ? "Unnamed Product" : product.Name,
                        Category = category?.Name ?? "Uncategorized",
                        Revenue = revenue,
                        Profit = profit,
                        ItemsSold = itemsSold
                    };
                }).ToList());
                
                // ✅ PERFORMANCE FIX: Create virtualized collection on UI thread
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    TopProducts = new VirtualizingCollection<ProductPerformance>(productPerfList);
                    Debug.WriteLine($"Created VirtualizingCollection with {productPerfList.Count} products");
                    Debug.WriteLine($"TopProducts.Count = {TopProducts.Count()}, IsVirtualizing = {TopProducts.IsVirtualizing}");
                }, DispatcherPriority.Normal);

                // ✅ PERFORMANCE FIX: Update charts asynchronously to prevent UI blocking
                var chartUpdateTasks = new[]
                {
                    UpdateProductChartsAsync(productPerfList),
                    UpdateCategoryChartsAsync(productPerfList)
                };

                await Task.WhenAll(chartUpdateTasks);
                
                Debug.WriteLine("================ PRODUCT PERFORMANCE DEBUG END =================");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading product performance: {ex.Message}");
                ActiveAlerts?.Add(new Alert
                {
                    Type = AlertType.Error,
                    Message = "Failed to load product performance data",
                    Icon = "⚠"
                });
            }
            finally
            {
                StopLoading("ProductPerformance");
                IsProductPerformanceLoading = false;
            }
        }
        
        /// <summary>
        /// ✅ PERFORMANCE FIX: Async version to prevent UI blocking
        /// </summary>
        private async Task UpdateProductChartsAsync(List<ProductPerformance> topProducts)
        {
            Debug.WriteLine($"UpdateProductChartsAsync: Starting with {topProducts?.Count ?? 0} products");

            if (topProducts == null || !topProducts.Any())
            {
                Debug.WriteLine("UpdateProductCharts: No product data - creating empty series");
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    ProductChartSeries = new SeriesCollection();
                    ProfitChartSeries = new SeriesCollection();
                    MarginChartSeries = new SeriesCollection();
                    ItemsSoldChartSeries = new SeriesCollection();
                }, DispatcherPriority.Background);
                return;
            }

            Debug.WriteLine($"UpdateProductCharts: Processing {topProducts.Count} products");

            // Filter products with revenue > 0 for chart display
            var productsWithRevenue = topProducts.Where(p => p.Revenue > 0).OrderByDescending(p => p.Revenue).ToList();
            Debug.WriteLine($"Products with revenue > 0: {productsWithRevenue.Count}");

            if (productsWithRevenue.Count == 0)
            {
                Debug.WriteLine("UpdateProductCharts: No products with revenue > 0 - creating empty series");
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    ProductChartSeries = new SeriesCollection();
                    ProfitChartSeries = new SeriesCollection();
                    MarginChartSeries = new SeriesCollection();
                    ItemsSoldChartSeries = new SeriesCollection();
                    ProductChartLabels = new string[0];
                }, DispatcherPriority.Background);
                return;
            }

            // ✅ FIX: Set ProductChartLabels with individual product names for X-axis (limit to top 5 for better readability)
            var productNames = productsWithRevenue.Take(5).Select(p => p.Name).ToArray();
            Debug.WriteLine($"Setting ProductChartLabels with {productNames.Length} product names: {string.Join(", ", productNames)}");

            // ✅ PERFORMANCE FIX: Get translated strings and create chart data on background thread
            string revenueText = null, profitText = null, marginText = null, itemsSoldText = null, currencySymbol = null;

            await Application.Current.Dispatcher.InvokeAsync(() => {
                revenueText = Application.Current.TryFindResource("Revenue") as string ?? "Revenue";
                profitText = Application.Current.TryFindResource("Profit") as string ?? "Profit";
                marginText = Application.Current.TryFindResource("ProfitMargin") as string ?? "Profit Margin";
                itemsSoldText = Application.Current.TryFindResource("ItemsSold") as string ?? "Items Sold";
                currencySymbol = Application.Current.TryFindResource("CurrencySymbol") as string ?? "DA";
            }, DispatcherPriority.Background);

            // Create chart data on background thread (limit to top 5 for better readability)
            var top5Products = productsWithRevenue.Take(5).ToList();
            var revenueValues = new ChartValues<decimal>(top5Products.Select(p => p.Revenue));
            var profitValues = new ChartValues<decimal>(top5Products.Select(p => p.Profit));
            var marginValues = new ChartValues<decimal>(top5Products.Select(p => p.Margin));
            var itemsSoldValues = new ChartValues<int>(top5Products.Select(p => p.ItemsSold));

            // ✅ PERFORMANCE FIX: Update UI properties on main thread with low priority
            await Application.Current.Dispatcher.InvokeAsync(() => {
                // ✅ FIX: Set ProductChartLabels for X-axis labels
                ProductChartLabels = productNames;
                Debug.WriteLine($"ProductChartLabels set with {ProductChartLabels.Length} labels");

                ProductChartSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = revenueText,
                        Values = revenueValues,
                        Fill = new SolidColorBrush(Colors.DodgerBlue),
                        DataLabels = true,
                        LabelPoint = point => $"{point.Y:N0} {currencySymbol}"
                    }
                };

                ProfitChartSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = profitText,
                        Values = profitValues,
                        Fill = new SolidColorBrush(Colors.ForestGreen),
                        DataLabels = true,
                        LabelPoint = point => $"{point.Y:N0} {currencySymbol}"
                    }
                };

                MarginChartSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = marginText,
                        Values = marginValues,
                        Fill = new SolidColorBrush(Colors.Goldenrod),
                        DataLabels = true,
                        LabelPoint = point => $"{point.Y:N1}%"
                    }
                };

                ItemsSoldChartSeries = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = itemsSoldText,
                        Values = itemsSoldValues,
                        Fill = new SolidColorBrush(Colors.OrangeRed),
                        DataLabels = true,
                        LabelPoint = point => $"{point.Y:N0}"
                    }
                };
                Debug.WriteLine($"UpdateProductChartsAsync: UI updated with product charts");
            }, DispatcherPriority.Background);
        }
        
        // Helper class for category data to avoid anonymous type issues
        public class CategoryData
        {
            public string Category { get; set; }
            public decimal Revenue { get; set; }
            public decimal Profit { get; set; }
            public int ItemsSold { get; set; }
            public decimal Margin { get; set; }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Async version to prevent UI blocking
        /// Shows individual products instead of categories for better user visibility
        /// </summary>
        private async Task UpdateCategoryChartsAsync(List<ProductPerformance> products)
        {
            Debug.WriteLine($"UpdateCategoryChartsAsync: Starting with {products?.Count ?? 0} products");

            if (products == null || products.Count == 0)
            {
                Debug.WriteLine("UpdateCategoryCharts: No products data for category charts");

                // ✅ PERFORMANCE FIX: Update UI on background priority
                await Application.Current.Dispatcher.InvokeAsync(() => {
                    CategoryChartSeries = new SeriesCollection();
                    CategoryProfitSeries = new SeriesCollection();
                    CategoryMarginSeries = new SeriesCollection();
                    CategoryItemsSoldSeries = new SeriesCollection();
                }, DispatcherPriority.Background);
                return;
            }

            Debug.WriteLine($"UpdateCategoryCharts: Processing {products.Count} products");

            // ✅ ENHANCEMENT: Show individual products instead of categories for better visibility
            // Take top 5 products by revenue and show them with product names as labels
            var topProducts = products
                .Where(p => !string.IsNullOrWhiteSpace(p.Name) && p.Revenue > 0) // Skip empty names and zero revenue
                .OrderByDescending(p => p.Revenue)
                .Take(5)
                .ToList();

            Debug.WriteLine($"Created {topProducts.Count} top products for charts");

            // Get resource strings on UI thread
            string noData = null, currencySymbol = null, valueText = null;
            await Application.Current.Dispatcher.InvokeAsync(() => {
                noData = Application.Current.TryFindResource("NoData") as string ?? "No Data";
                currencySymbol = Application.Current.TryFindResource("CurrencySymbol") as string ?? "DA";
                valueText = Application.Current.TryFindResource("Value") as string ?? "Value";
            }, DispatcherPriority.Background);

            Debug.WriteLine($"Products with revenue > 0 for charts: {topProducts.Count}");
                
            // ✅ PERFORMANCE FIX: Create chart series collections and prepare data on background thread
            var random = new Random(DateTime.Now.Millisecond);
            var categoryChartSeries = new SeriesCollection();
            var categoryProfitSeries = new SeriesCollection();
            var categoryMarginSeries = new SeriesCollection();
            var categoryItemsSoldSeries = new SeriesCollection();

            // Add pie slices for each product that has revenue > 0
            foreach (var product in topProducts)
            {
                Debug.WriteLine($"Adding product to charts: {product.Name}, Revenue: {product.Revenue}, Profit: {product.Profit}");

                // Generate a consistent color for this product
                var color = new SolidColorBrush(Color.FromRgb(
                    (byte)random.Next(100, 255),
                    (byte)random.Next(100, 255),
                    (byte)random.Next(100, 255)));

                // Truncate long product names for better display
                var displayName = product.Name.Length > 20 ? product.Name.Substring(0, 17) + "..." : product.Name;

                // ✅ ENHANCEMENT: Create chart series with product names as labels
                categoryChartSeries.Add(new PieSeries
                {
                    Title = displayName,
                    Values = new ChartValues<decimal> { product.Revenue },
                    DataLabels = true,
                    LabelPoint = point => $"{displayName}: {product.Revenue:N0} {currencySymbol}",
                    Fill = color
                });

                categoryProfitSeries.Add(new PieSeries
                {
                    Title = displayName,
                    Values = new ChartValues<decimal> { product.Profit },
                    DataLabels = true,
                    LabelPoint = point => $"{displayName}: {product.Profit:N0} {currencySymbol}",
                    Fill = color
                });

                categoryMarginSeries.Add(new PieSeries
                {
                    Title = displayName,
                    Values = new ChartValues<decimal> { product.Margin },
                    DataLabels = true,
                    LabelPoint = point => $"{displayName}: {product.Margin:N1}%",
                    Fill = color
                });

                categoryItemsSoldSeries.Add(new PieSeries
                {
                    Title = displayName,
                    Values = new ChartValues<int> { product.ItemsSold },
                    DataLabels = true,
                    LabelPoint = point => $"{displayName}: {product.ItemsSold:N0}",
                    Fill = color
                });
            }
            
            // If no products were added, leave charts empty to show proper "No Data" state
            if (categoryChartSeries.Count == 0)
            {
                Debug.WriteLine("No products added to charts - leaving empty to show 'No Data' state");
            }

            // ✅ PERFORMANCE FIX: Update UI properties on main thread with background priority
            await Application.Current.Dispatcher.InvokeAsync(() => {
                CategoryChartSeries = categoryChartSeries;
                CategoryProfitSeries = categoryProfitSeries;
                CategoryMarginSeries = categoryMarginSeries;
                CategoryItemsSoldSeries = categoryItemsSoldSeries;
                Debug.WriteLine($"UpdateCategoryChartsAsync: UI updated with {categoryChartSeries.Count} category series");
            }, DispatcherPriority.Background);
        }
        
        /// <summary>
        /// Formats a date label based on the number of points
        /// </summary>
        private string FormatDateLabel(DateTime date, int totalPoints)
        {
            // Choose appropriate format based on data density
            if (totalPoints <= 7)
            {
                // For daily data, show day and month
                return date.ToString("MMM d");
            }
            else if (totalPoints <= 31)
            {
                // For weekly data, show day only
                return date.ToString("d");
            }
            else if (totalPoints <= 60)
            {
                // For monthly data, show abbreviated month
                return date.ToString("MMM");
            }
            else
            {
                // For yearly data, show abbreviated month and year
                return date.ToString("MMM yy");
            }
        }
        

        
        /// <summary>
        /// Property change notification
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Handles real-time metrics updates from DashboardUpdateService
        /// </summary>
        private async void OnRealTimeMetricsUpdated(object sender, POSSystem.Services.RealTime.DashboardMetricsUpdatedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"RefactoredDashboardViewModel: Received real-time update - Day: {e.DaySales:C}, Hour: {e.HourSales:C}");

                // Update UI on the main thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Update today's sales if we're showing today's data
                    if (SelectedQuickStatsPeriod?.Type == TimePeriodType.Today)
                    {
                        TodaySales = e.DaySales;
                        FilteredSales = e.DaySales;

                        // Update transaction count
                        if (e.DayTransactions > 0)
                        {
                            // We don't have a direct property for today's transactions, but we can update related metrics
                            System.Diagnostics.Debug.WriteLine($"Today's transactions: {e.DayTransactions}");
                        }
                    }

                    // Update unpaid sales count
                    UnpaidSalesCount = e.PendingTransactions;

                    // Trigger property change notifications
                    OnPropertyChanged(nameof(TodaySales));
                    OnPropertyChanged(nameof(FilteredSales));
                    OnPropertyChanged(nameof(UnpaidSalesCount));

                }, System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling real-time metrics update: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles sale completion events for immediate dashboard updates
        /// </summary>
        private async void OnSaleCompleted(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Sale completed - triggering immediate dashboard refresh");

                // Clear cache to ensure fresh data
                _dataService.ForceClearCache();

                // Force immediate update of real-time service
                if (_realTimeUpdateService != null)
                {
                    await _realTimeUpdateService.ForceUpdateAsync();
                }

                // Refresh dashboard data on UI thread
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Refresh the current period's data
                        await LoadQuickStatsAsync();

                        System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Dashboard refreshed after sale completion");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error refreshing dashboard after sale: {ex.Message}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling sale completion: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// Dispose implementation
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing && !_disposed)
            {
                // Unsubscribe from real-time updates
                if (_realTimeUpdateService != null)
                {
                    _realTimeUpdateService.MetricsUpdated -= OnRealTimeMetricsUpdated;
                    System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Unsubscribed from real-time updates");
                }

                // Unsubscribe from sale completion events
                POSSystem.ViewModels.SaleViewModel.SaleCompleted -= OnSaleCompleted;
                System.Diagnostics.Debug.WriteLine("RefactoredDashboardViewModel: Unsubscribed from SaleCompleted events");

                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();

                _disposed = true;
            }
        }
        
        /// <summary>
        /// Checks if a section has been loaded already
        /// </summary>
        private bool IsSectionLoaded(string sectionName)
        {
            return _sectionLoadingStates.ContainsKey(sectionName) && !_sectionLoadingStates[sectionName];
        }
        
        private async Task UpdateDateRangeAsync()
        {
            try
            {
                IsLoading = true;
                await LoadDataForDateRangeAsync(StartDate, EndDate);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ResetZoomAsync()
        {
            try
            {
                IsLoading = true;
                var dateRange = _parameterManager.GetDateRange(_selectedQuickStatsPeriod);
                StartDate = dateRange.start;
                EndDate = dateRange.end;
                IsZoomed = false;
                await LoadDataForDateRangeAsync(StartDate, EndDate);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SelectChartParameterAsync(ChartParameter parameter)
        {
            if (parameter == null) return;

            try
            {
                IsLoading = true;
                _selectedChartParameter = parameter;
                OnPropertyChanged(nameof(SelectedChartParameter));
                await LoadChartDataAsync();
            }
            finally
            {
                IsLoading = false;
            }
        }



        private async Task CloseDetailViewAsync()
        {
            IsDetailViewVisible = false;
            await Task.CompletedTask;
        }

        private async Task LoadDataForDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                StartLoading("DateRange");
                await Task.WhenAll(
                    LoadQuickStatsAsync(),
                    LoadSalesTrendDataAsync(),
                    LoadExpensesMetricsAsync(startDate, endDate),
                    LoadProductPerformanceDataAsync()
                );
            }
            finally
            {
                StopLoading("DateRange");
            }
        }

        private async Task LoadChartDataAsync()
        {
            try
            {
                StartLoading("Chart");
                await LoadSalesTrendDataAsync();
            }
            finally
            {
                StopLoading("Chart");
            }
        }



        private void UpdateDateRangeForPeriod(TimePeriod period)
        {
            if (period == null) return;

            var now = DateTime.Now;
            switch (period.Type)
            {
                case TimePeriodType.Today:
                    StartDate = DateTime.Today;
                    EndDate = now;
                    break;
                case TimePeriodType.Yesterday:
                    StartDate = DateTime.Today.AddDays(-1);
                    EndDate = DateTime.Today.AddSeconds(-1);
                    break;
                case TimePeriodType.Week:
                    StartDate = DateTime.Today.AddDays(-7);
                    EndDate = now;
                    break;
                case TimePeriodType.Month:
                    StartDate = DateTime.Today.AddDays(-30);
                    EndDate = now;
                    break;
                case TimePeriodType.Year:
                    StartDate = DateTime.Today.AddDays(-365);
                    EndDate = now;
                    break;
            }
        }

        private async Task LoadQuickStatsDataAsync()
        {
            try
            {
                IsLoading = true;
                Debug.WriteLine($"LoadQuickStatsDataAsync: Starting to load quick stats data");

                // Get today's date range
                var today = DateTime.Today;
                var now = DateTime.Now;
                Debug.WriteLine($"LoadQuickStatsDataAsync: Today range: {today:yyyy-MM-dd HH:mm:ss} to {now:yyyy-MM-dd HH:mm:ss}");

                // Load sales data for today
                var todaySales = await _dataService.GetSalesDataAsync(today, now);
                Debug.WriteLine($"LoadQuickStatsDataAsync: Retrieved {todaySales.Count} sales records for today");

                TodaySales = todaySales.Sum(s => s.GrandTotal);
                Debug.WriteLine($"LoadQuickStatsDataAsync: TodaySales calculated as {TodaySales:N2} DA");

                // Calculate other metrics based on selected period
                await LoadFilteredDataAsync();

                // Load additional data that might be needed
                await Task.WhenAll(
                    LoadExpensesMetricsAsync(), // Use selected period
                    LoadAlertsDataAsync()
                );

                Debug.WriteLine($"LoadQuickStatsDataAsync: Completed loading quick stats. TodaySales = {TodaySales:N2} DA");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading quick stats: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                // Handle error appropriately
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Removed async keyword since no await operations are performed
        /// </summary>
        private void UpdateMetricValue(List<DashboardSale> currentSales, List<DashboardSale> previousSales)
        {
            if (SelectedMetricType == null) return;

            decimal currentValue = 0;
            decimal previousValue = 0;

            switch (SelectedMetricType.Key)
            {
                case "sales":
                    currentValue = currentSales.Sum(s => s.GrandTotal);
                    previousValue = previousSales.Sum(s => s.GrandTotal);
                    break;
                case "profit":
                    currentValue = currentSales.Sum(s => s.Profit);
                    previousValue = previousSales.Sum(s => s.Profit);
                    break;
                case "margin":
                    currentValue = currentSales.Sum(s => s.GrandTotal) > 0 
                        ? (currentSales.Sum(s => s.Profit) / currentSales.Sum(s => s.GrandTotal)) * 100 
                        : 0;
                    previousValue = previousSales.Sum(s => s.GrandTotal) > 0 
                        ? (previousSales.Sum(s => s.Profit) / previousSales.Sum(s => s.GrandTotal)) * 100 
                        : 0;
                    break;
                case "items":
                    currentValue = currentSales.Sum(s => s.TotalItems);
                    previousValue = previousSales.Sum(s => s.TotalItems);
                    break;
            }

            FilteredMetricValue = currentValue;
            MetricGrowth = previousValue > 0 ? ((currentValue - previousValue) / previousValue) * 100 : 0;

            OnPropertyChanged(nameof(FormattedMetricValue));
            OnPropertyChanged(nameof(CustomMetricDescription));
        }

        private (DateTime start, DateTime end) GetDateRangeForPeriod(TimePeriod period)
        {
            var now = DateTime.Now;
            var today = DateTime.Today;

            switch (period.Type)
            {
                case TimePeriodType.Today:
                    return (today, now);
                case TimePeriodType.Yesterday:
                    return (today.AddDays(-1), today.AddSeconds(-1));
                case TimePeriodType.Week:
                    return (today.AddDays(-7), now);
                case TimePeriodType.Month:
                    if (period.IsCurrentMonth)
                    {
                        // Current month: from first day of month to now
                        var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfMonth, now);
                    }
                    else if (period.IsLastMonth)
                    {
                        // Last month: full previous month
                        var firstDayOfLastMonth = new DateTime(today.Year, today.Month, 1).AddMonths(-1);
                        var firstDayOfThisMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfLastMonth, firstDayOfThisMonth.AddSeconds(-1));
                    }
                    else if (period.Days > 0)
                    {
                        // Last N days
                        return (today.AddDays(-period.Days), now);
                    }
                    else
                    {
                        // Default to current month
                        var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                        return (firstDayOfMonth, now);
                    }
                case TimePeriodType.Year:
                    return (today.AddDays(-365), now);
                default:
                    return (today, now);
            }
        }
        
        /// <summary>
        /// Loads all dashboard components for the selected period
        /// </summary>
        private async Task LoadAllComponentsForPeriodAsync()
        {
            try
            {
                StartLoading("PeriodChange");
                Debug.WriteLine($"=== Loading all components for period: {SelectedQuickStatsPeriod?.DisplayName} ===");

                // Get date range for the selected period
                var (startDate, endDate) = GetDateRangeForPeriod(SelectedQuickStatsPeriod);

                // Update internal date range properties
                _startDate = startDate;
                _endDate = endDate;
                OnPropertyChanged(nameof(StartDate));
                OnPropertyChanged(nameof(EndDate));

                // Load all dashboard components in parallel
                await Task.WhenAll(
                    LoadPeriodSalesAsync(startDate, endDate),
                    LoadPeriodProfitAsync(startDate, endDate),
                    LoadSalesTrendDataAsync(),
                    LoadProductPerformanceDataAsync(),
                    LoadExpensesMetricsAsync(), // Use selected period, not startDate/endDate
                    LoadAlertsDataAsync(),
                    LoadFilteredDataAsync(),
                    UpdateAllMetricsForPeriodAsync()
                );

                Debug.WriteLine($"=== Completed loading all components for period: {SelectedQuickStatsPeriod?.DisplayName} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading components for period: {ex.Message}");
            }
            finally
            {
                StopLoading("PeriodChange");
            }
        }

        /// <summary>
        /// Loads sales data for the specified period
        /// </summary>
        private async Task LoadPeriodSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var salesData = await _dataService.GetSalesDataAsync(startDate, endDate, $"period_{SelectedQuickStatsPeriod?.Type}");
                PeriodSales = salesData?.Sum(s => s.GrandTotal) ?? 0;

                // No fallback to recent sales - show actual 0 when no sales exist for the period
                Debug.WriteLine($"=== Period sales (no fallback): {PeriodSales:N2} DA ===");

                Debug.WriteLine($"=== Period sales ({startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}): {PeriodSales:N2} DA ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading period sales: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads profit data for the specified period
        /// </summary>
        private async Task LoadPeriodProfitAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var salesData = await _dataService.GetSalesDataAsync(startDate, endDate, $"profit_{SelectedQuickStatsPeriod?.Type}");
                GrossProfit = salesData?.Sum(s => s.Profit) ?? 0;

                // Also update filtered profit for consistency
                FilteredProfit = GrossProfit;

                Debug.WriteLine($"=== Period profit ({startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}): {GrossProfit:N2} DA ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading period profit: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates all metric cards to reflect the selected period
        /// </summary>
        private async Task UpdateAllMetricsForPeriodAsync()
        {
            try
            {
                var (startDate, endDate) = GetDateRangeForPeriod(SelectedQuickStatsPeriod);

                // Update all metrics in parallel
                await Task.WhenAll(
                    UpdateStockMetricsAsync(),
                    LoadExpensesMetricsAsync() // Use selected period
                );

                // Update unpaid sales metrics (now synchronous)
                UpdateUnpaidSalesMetrics();

                Debug.WriteLine($"=== Updated all metrics for period: {SelectedQuickStatsPeriod?.DisplayName} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating metrics for period: {ex.Message}");
            }
        }

        private async Task UpdateStockMetricsAsync()
        {
            try
            {
                // Stock metrics are generally not period-dependent, but we can refresh them
                var lowStockProducts = await _dataProvider.GetLowStockProductsAsync();
                // ✅ FIX: Use consistent low stock logic
                LowStockCount = lowStockProducts?.Count(p => p.GetTotalStock() > 0 &&
                                                             (p.GetTotalStock() <= p.MinimumStock || p.GetTotalStock() <= p.ReorderPoint)) ?? 0;
                OutOfStockCount = lowStockProducts?.Count(p => p.GetTotalStock() <= 0) ?? 0;

                var expiringProducts = _dataProvider.GetExpiringProducts(30);
                // ✅ FIX: Add null check for DaysUntilExpiry
                ExpiringProductsCount = expiringProducts?.Count(p => p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry > 0 && p.DaysUntilExpiry <= 30) ?? 0;
                ExpiredProductsCount = expiringProducts?.Count(p => p.DaysUntilExpiry.HasValue && p.DaysUntilExpiry <= 0) ?? 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating stock metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ⚡ PERFORMANCE: Preload low stock data for instant dialog opening
        /// </summary>
        private async Task PreloadLowStockDataAsync()
        {
            try
            {
                // Use the same data service that the dialog will use
                var lowStockDataService = new LowStockDataService(new POSDbContext());
                await lowStockDataService.PreloadDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading low stock data: {ex.Message}");
                // Don't throw - this is just an optimization
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Removed async keyword since no await operations are performed
        /// </summary>
        private void UpdateUnpaidSalesMetrics()
        {
            try
            {
                var unpaidSales = _dataProvider.GetUnpaidSales() ?? new List<Sale>();
                UnpaidSalesCount = unpaidSales.Count();
                UnpaidSalesAmount = unpaidSales.Sum(s => s?.RemainingAmount ?? 0);

                var overdueSales = unpaidSales.Where(s => s != null && s.DueDate.HasValue && s.DueDate.Value < DateTime.Now).ToList();
                OverdueSalesCount = overdueSales.Count();
                OverdueSalesAmount = overdueSales.Sum(s => s?.RemainingAmount ?? 0);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating unpaid sales metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method to get localized strings from application resources
        /// </summary>
        private string GetLocalizedString(string key)
        {
            try
            {
                return System.Windows.Application.Current.TryFindResource(key) as string;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}

/// <summary>
/// Represents a business expense for use in the dashboard
/// </summary>
public class DashboardBusinessExpense
{
    public int Id { get; set; }
    public DateTime Date { get; set; }
    public string Description { get; set; }
    public decimal Amount { get; set; }
    public DashboardExpenseCategory Category { get; set; }
    public int UserId { get; set; }
    public DashboardUser User { get; set; }
}

/// <summary>
/// Simplified User class for dashboard display
/// </summary>
public class DashboardUser
{
    public int Id { get; set; }
    public string Username { get; set; }
    public string Email { get; set; }
}

/// <summary>
/// Categories for business expenses
/// </summary>
public enum DashboardExpenseCategory
{
    Rent,
    Utilities,
    Salaries,
    Supplies,
    Marketing,
    Insurance,
    Taxes,
    Maintenance,
    OperatingCost,
    Other
}

/// <summary>
/// Represents user performance data for dashboard display
/// </summary>
public class UserPerformance
{
    public int UserId { get; set; }
    public string Username { get; set; }
    public string DisplayName { get; set; }
    public decimal SalesAmount { get; set; }
    public int TransactionCount { get; set; }
    public int CustomerCount { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal ConversionRate { get; set; }
    public string PhotoPath { get; set; }
    
    // Formatted values for display
    public string FormattedSales => $"{SalesAmount:N2} DA";
    public string FormattedTransactions => $"{TransactionCount:N0}";
    public string FormattedCustomers => $"{CustomerCount:N0}";
    public string FormattedAvgOrder => $"{AverageOrderValue:N2} DA";
    public string FormattedConversion => $"{ConversionRate:N1}%";
} 
