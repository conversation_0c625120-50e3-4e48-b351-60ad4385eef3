using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services.Monitoring
{
    /// <summary>
    /// Service for providing real-time performance dashboard data
    /// </summary>
    public class PerformanceDashboardService
    {
        private readonly PerformanceMonitoringService _monitor;
        private readonly ILogger<PerformanceDashboardService> _logger;
        private readonly Timer _updateTimer;
        private PerformanceDashboardData _currentData;
        private readonly object _lockObject = new object();

        public PerformanceDashboardService(
            PerformanceMonitoringService monitor,
            ILogger<PerformanceDashboardService> logger = null)
        {
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
            _logger = logger;
            
            _currentData = new PerformanceDashboardData();
            
            // Update dashboard data every 10 seconds
            _updateTimer = new Timer(UpdateDashboardData, null, TimeSpan.Zero, TimeSpan.FromSeconds(10));
        }

        /// <summary>
        /// Get current performance dashboard data
        /// </summary>
        public PerformanceDashboardData GetDashboardData()
        {
            lock (_lockObject)
            {
                return _currentData.Clone();
            }
        }

        /// <summary>
        /// Get real-time system metrics
        /// </summary>
        public SystemMetrics GetSystemMetrics()
        {
            var process = Process.GetCurrentProcess();
            
            return new SystemMetrics
            {
                Timestamp = DateTime.Now,
                CpuUsagePercent = GetCpuUsage(),
                MemoryUsageMB = process.WorkingSet64 / (1024 * 1024),
                PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                GCMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024),
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount,
                GCGen0Collections = GC.CollectionCount(0),
                GCGen1Collections = GC.CollectionCount(1),
                GCGen2Collections = GC.CollectionCount(2)
            };
        }

        /// <summary>
        /// Get performance trends over time
        /// </summary>
        public PerformanceTrends GetPerformanceTrends(TimeSpan period)
        {
            var metrics = _monitor.GetMetrics();
            var cutoffTime = DateTime.Now - period;

            var trends = new PerformanceTrends
            {
                Period = period,
                GeneratedAt = DateTime.Now,
                OperationTrends = new List<OperationTrend>()
            };

            foreach (var metric in metrics.Values.Where(m => m.LastRecorded >= cutoffTime))
            {
                var trend = new OperationTrend
                {
                    OperationName = metric.OperationName,
                    AverageResponseTime = metric.AverageMs,
                    RecentAverageResponseTime = metric.RecentAverageMs,
                    TotalExecutions = metric.Count,
                    MinResponseTime = metric.MinMs,
                    MaxResponseTime = metric.MaxMs,
                    TrendDirection = GetTrendDirection(metric.AverageMs, metric.RecentAverageMs)
                };

                trends.OperationTrends.Add(trend);
            }

            trends.OperationTrends = trends.OperationTrends
                .OrderByDescending(t => t.TotalExecutions)
                .ToList();

            return trends;
        }

        /// <summary>
        /// Get performance alerts summary
        /// </summary>
        public AlertsSummary GetAlertsSummary()
        {
            var summary = _monitor.GetSummary();
            
            return new AlertsSummary
            {
                GeneratedAt = DateTime.Now,
                TotalAlerts = summary.RecentAlerts.Count,
                CriticalAlerts = summary.RecentAlerts.Count(a => a.Level == AlertLevel.Critical),
                WarningAlerts = summary.RecentAlerts.Count(a => a.Level == AlertLevel.Warning),
                InfoAlerts = summary.RecentAlerts.Count(a => a.Level == AlertLevel.Info),
                RecentAlerts = summary.RecentAlerts.Take(10).ToList(),
                MostProblematicOperations = summary.SlowestOperations.Take(5).ToList()
            };
        }

        /// <summary>
        /// Get database performance metrics
        /// </summary>
        public DatabasePerformanceMetrics GetDatabaseMetrics()
        {
            var metrics = _monitor.GetMetrics();
            var dbMetrics = metrics.Values.Where(m => m.OperationName.StartsWith("Database.")).ToList();

            return new DatabasePerformanceMetrics
            {
                GeneratedAt = DateTime.Now,
                TotalQueries = dbMetrics.Sum(m => m.Count),
                AverageQueryTime = dbMetrics.Any() ? dbMetrics.Average(m => m.AverageMs) : 0,
                SlowestQueries = dbMetrics.OrderByDescending(m => m.AverageMs).Take(5).ToList(),
                QueryTypeBreakdown = dbMetrics.GroupBy(m => GetQueryType(m.OperationName))
                    .ToDictionary(g => g.Key, g => new QueryTypeMetrics
                    {
                        Count = g.Sum(m => m.Count),
                        AverageTime = g.Average(m => m.AverageMs),
                        TotalTime = g.Sum(m => m.TotalMs)
                    })
            };
        }

        private void UpdateDashboardData(object state)
        {
            try
            {
                var newData = new PerformanceDashboardData
                {
                    LastUpdated = DateTime.Now,
                    SystemMetrics = GetSystemMetrics(),
                    PerformanceSummary = _monitor.GetSummary(),
                    AlertsSummary = GetAlertsSummary(),
                    DatabaseMetrics = GetDatabaseMetrics(),
                    TrendData = GetPerformanceTrends(TimeSpan.FromHours(1))
                };

                lock (_lockObject)
                {
                    _currentData = newData;
                }

                _logger?.LogDebug("Performance dashboard data updated");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating performance dashboard data");
            }
        }

        private double GetCpuUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;
                
                Thread.Sleep(100); // Small delay to measure CPU usage
                
                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;
                
                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                
                return cpuUsageTotal * 100;
            }
            catch
            {
                return 0; // Return 0 if unable to calculate
            }
        }

        private TrendDirection GetTrendDirection(double average, double recentAverage)
        {
            var threshold = 0.1; // 10% threshold
            var change = (recentAverage - average) / average;

            if (Math.Abs(change) < threshold)
                return TrendDirection.Stable;
            
            return change > 0 ? TrendDirection.Increasing : TrendDirection.Decreasing;
        }

        private string GetQueryType(string operationName)
        {
            if (operationName.Contains("Query") || operationName.Contains("Get") || operationName.Contains("Search"))
                return "SELECT";
            if (operationName.Contains("Insert") || operationName.Contains("Add"))
                return "INSERT";
            if (operationName.Contains("Update"))
                return "UPDATE";
            if (operationName.Contains("Delete"))
                return "DELETE";
            
            return "OTHER";
        }

        public void Dispose()
        {
            _updateTimer?.Dispose();
        }
    }

    /// <summary>
    /// Performance dashboard data container
    /// </summary>
    public class PerformanceDashboardData
    {
        public DateTime LastUpdated { get; set; }
        public SystemMetrics SystemMetrics { get; set; }
        public PerformanceSummary PerformanceSummary { get; set; }
        public AlertsSummary AlertsSummary { get; set; }
        public DatabasePerformanceMetrics DatabaseMetrics { get; set; }
        public PerformanceTrends TrendData { get; set; }

        public PerformanceDashboardData Clone()
        {
            return new PerformanceDashboardData
            {
                LastUpdated = LastUpdated,
                SystemMetrics = SystemMetrics,
                PerformanceSummary = PerformanceSummary,
                AlertsSummary = AlertsSummary,
                DatabaseMetrics = DatabaseMetrics,
                TrendData = TrendData
            };
        }
    }

    /// <summary>
    /// System performance metrics
    /// </summary>
    public class SystemMetrics
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public long PrivateMemoryMB { get; set; }
        public long GCMemoryMB { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public int GCGen0Collections { get; set; }
        public int GCGen1Collections { get; set; }
        public int GCGen2Collections { get; set; }
    }

    /// <summary>
    /// Performance trends over time
    /// </summary>
    public class PerformanceTrends
    {
        public TimeSpan Period { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<OperationTrend> OperationTrends { get; set; } = new List<OperationTrend>();
    }

    /// <summary>
    /// Individual operation trend data
    /// </summary>
    public class OperationTrend
    {
        public string OperationName { get; set; }
        public double AverageResponseTime { get; set; }
        public double RecentAverageResponseTime { get; set; }
        public int TotalExecutions { get; set; }
        public long MinResponseTime { get; set; }
        public long MaxResponseTime { get; set; }
        public TrendDirection TrendDirection { get; set; }
    }

    /// <summary>
    /// Alerts summary information
    /// </summary>
    public class AlertsSummary
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalAlerts { get; set; }
        public int CriticalAlerts { get; set; }
        public int WarningAlerts { get; set; }
        public int InfoAlerts { get; set; }
        public List<PerformanceAlert> RecentAlerts { get; set; } = new List<PerformanceAlert>();
        public List<PerformanceMetric> MostProblematicOperations { get; set; } = new List<PerformanceMetric>();
    }

    /// <summary>
    /// Database-specific performance metrics
    /// </summary>
    public class DatabasePerformanceMetrics
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalQueries { get; set; }
        public double AverageQueryTime { get; set; }
        public List<PerformanceMetric> SlowestQueries { get; set; } = new List<PerformanceMetric>();
        public Dictionary<string, QueryTypeMetrics> QueryTypeBreakdown { get; set; } = new Dictionary<string, QueryTypeMetrics>();
    }

    /// <summary>
    /// Query type performance metrics
    /// </summary>
    public class QueryTypeMetrics
    {
        public int Count { get; set; }
        public double AverageTime { get; set; }
        public long TotalTime { get; set; }
    }

    /// <summary>
    /// Performance trend direction
    /// </summary>
    public enum TrendDirection
    {
        Decreasing,
        Stable,
        Increasing
    }
}
