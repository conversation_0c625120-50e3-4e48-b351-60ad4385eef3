using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using POSSystem.Services;
using POSSystem.Services.RealTime;
using POSSystem.Services.QueryOptimization;
using POSSystem.ViewModels.Dashboard;

namespace POSSystem.Scripts
{
    /// <summary>
    /// Simple test to verify service registration is working correctly
    /// </summary>
    public static class TestServiceRegistration
    {
        public static async Task RunTest()
        {
            try
            {
                Console.WriteLine("=== Service Registration Test ===");
                
                // Create service provider
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                
                // Test 1: Check if DashboardUpdateService can be resolved
                Console.WriteLine("\n1. Testing DashboardUpdateService registration...");
                var dashboardUpdateService = serviceProvider.GetService<DashboardUpdateService>();
                if (dashboardUpdateService != null)
                {
                    Console.WriteLine("   ✅ DashboardUpdateService resolved successfully");
                    
                    // Test if it can start
                    try
                    {
                        await dashboardUpdateService.ForceUpdateAsync();
                        Console.WriteLine("   ✅ DashboardUpdateService.ForceUpdateAsync() executed successfully");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ DashboardUpdateService.ForceUpdateAsync() failed: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ DashboardUpdateService could not be resolved");
                }
                
                // Test 2: Check if DashboardQueryService can be resolved
                Console.WriteLine("\n2. Testing DashboardQueryService registration...");
                var queryService = serviceProvider.GetService<DashboardQueryService>();
                if (queryService != null)
                {
                    Console.WriteLine("   ✅ DashboardQueryService resolved successfully");
                }
                else
                {
                    Console.WriteLine("   ❌ DashboardQueryService could not be resolved");
                }
                
                // Test 3: Check if RefactoredDashboardViewModel can be resolved with real-time service
                Console.WriteLine("\n3. Testing RefactoredDashboardViewModel registration...");
                var dashboardViewModel = serviceProvider.GetService<RefactoredDashboardViewModel>();
                if (dashboardViewModel != null)
                {
                    Console.WriteLine("   ✅ RefactoredDashboardViewModel resolved successfully");
                    
                    // Check if it has the real-time service
                    var realTimeServiceField = typeof(RefactoredDashboardViewModel)
                        .GetField("_realTimeUpdateService", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    
                    if (realTimeServiceField != null)
                    {
                        var realTimeService = realTimeServiceField.GetValue(dashboardViewModel);
                        if (realTimeService != null)
                        {
                            Console.WriteLine("   ✅ RefactoredDashboardViewModel has real-time service injected");
                        }
                        else
                        {
                            Console.WriteLine("   ⚠️ RefactoredDashboardViewModel real-time service is null");
                        }
                    }
                    else
                    {
                        Console.WriteLine("   ❌ Could not access _realTimeUpdateService field");
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ RefactoredDashboardViewModel could not be resolved");
                }
                
                // Test 4: Test the dependency chain
                Console.WriteLine("\n4. Testing dependency chain...");
                using (var scope = serviceProvider.CreateScope())
                {
                    try
                    {
                        var dbContext = scope.ServiceProvider.GetService<POSSystem.Data.POSDbContext>();
                        Console.WriteLine($"   POSDbContext: {(dbContext != null ? "✅ Available" : "❌ Not available")}");
                        
                        var databaseService = scope.ServiceProvider.GetService<POSSystem.Services.Interfaces.IDatabaseService>();
                        Console.WriteLine($"   IDatabaseService: {(databaseService != null ? "✅ Available" : "❌ Not available")}");
                        
                        var queryServiceScoped = scope.ServiceProvider.GetService<DashboardQueryService>();
                        Console.WriteLine($"   DashboardQueryService: {(queryServiceScoped != null ? "✅ Available" : "❌ Not available")}");
                        
                        var updateServiceScoped = scope.ServiceProvider.GetService<DashboardUpdateService>();
                        Console.WriteLine($"   DashboardUpdateService: {(updateServiceScoped != null ? "✅ Available" : "❌ Not available")}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ Error in dependency chain test: {ex.Message}");
                    }
                }
                
                Console.WriteLine("\n=== Test Complete ===");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed with error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
