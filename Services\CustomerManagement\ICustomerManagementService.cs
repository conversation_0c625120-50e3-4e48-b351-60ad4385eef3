using POSSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.CustomerManagement
{
    /// <summary>
    /// Interface for customer management operations
    /// </summary>
    public interface ICustomerManagementService
    {
        /// <summary>
        /// Get all customers
        /// </summary>
        Task<List<Customer>> GetAllCustomersAsync();

        /// <summary>
        /// Get customer by ID
        /// </summary>
        Task<Customer> GetCustomerByIdAsync(int id);

        /// <summary>
        /// Get customer by loyalty code
        /// </summary>
        Task<Customer> GetCustomerByLoyaltyCodeAsync(string loyaltyCode);

        /// <summary>
        /// Add new customer
        /// </summary>
        Task<int> AddCustomerAsync(Customer customer);

        /// <summary>
        /// Update existing customer
        /// </summary>
        Task<bool> UpdateCustomerAsync(Customer customer);

        /// <summary>
        /// Delete customer (soft delete if has sales, hard delete otherwise)
        /// </summary>
        Task<bool> DeleteCustomerAsync(int id);

        /// <summary>
        /// Search customers by name, email, or phone
        /// </summary>
        Task<List<Customer>> SearchCustomersAsync(string searchTerm);

        /// <summary>
        /// Get customers with loyalty points
        /// </summary>
        Task<List<Customer>> GetCustomersWithLoyaltyPointsAsync();

        /// <summary>
        /// Update customer loyalty points
        /// </summary>
        Task<bool> UpdateCustomerLoyaltyPointsAsync(int customerId, int points);

        /// <summary>
        /// Get top customers by purchase amount
        /// </summary>
        Task<List<Customer>> GetTopCustomersAsync(int count = 10);
    }
}
