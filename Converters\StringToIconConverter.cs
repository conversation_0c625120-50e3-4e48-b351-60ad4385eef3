using System;
using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Converters
{
    public class StringToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.ToLower() switch
                {
                    "sales" => PackIconKind.TrendingUp,
                    "purchase" => PackIconKind.TrendingDown,
                    "quote" => PackIconKind.FileDocument,
                    "credit" => PackIconKind.CreditCardRefund,
                    "debit" => PackIconKind.CreditCard,
                    _ => PackIconKind.File
                };
            }

            return PackIconKind.File;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 