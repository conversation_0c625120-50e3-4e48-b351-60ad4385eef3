using System;
using System.Security.Claims;
using POSSystem.Models;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IDatabaseService _dbService;
        private User _currentUser;

        public AuthenticationService(IDatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
        }

        public User CurrentUser
        {
            get
            {
                // ✅ PERFORMANCE FIX: Removed frequent debug logging that was impacting frame rates
                return _currentUser;
            }
        }

        public bool Login(string username, string password)
        {
            System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Login called for username: {username}");
            _currentUser = _dbService.AuthenticateUser(username, password);
            System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Login result: {_currentUser?.Username ?? "NULL"} (ID: {_currentUser?.Id ?? -1})");
            return _currentUser != null;
        }

        public void Logout()
        {
            _currentUser = null;
        }

        public bool HasPermission(string permission)
        {
            if (_currentUser?.UserRole == null)
            {
                System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Permission check failed: No current user or role for '{permission}'");
                return false;
            }

            System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Checking permission '{permission}' for user '{_currentUser.Username}' (Role: {_currentUser.UserRole.Name})");

            // Use UserPermissionsService for consistent permission checking
            try
            {
                var permissionsService = new UserPermissionsService(_dbService);
                permissionsService.Initialize(_currentUser);
                var result = permissionsService.HasPermission(permission);

                System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] UserPermissionsService returned: {result} for permission '{permission}'");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] ERROR in UserPermissionsService: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Stack trace: {ex.StackTrace}");

                // CRITICAL: Only use fallback for users without custom permissions
                // Check if user has custom permissions first
                try
                {
                    var permissionsService = new UserPermissionsService(_dbService);
                    var userPermissions = permissionsService.GetUserPermissions(_currentUser.Id);

                    if (userPermissions != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] User {_currentUser.Username} has custom permissions - NOT using role-based fallback");
                        // User has custom permissions but there was an error - deny access for safety
                        return false;
                    }

                    System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] User {_currentUser.Username} has no custom permissions - using role-based fallback");
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] ERROR checking for custom permissions: {ex2.Message}");
                    // If we can't determine if user has custom permissions, deny access for safety
                    return false;
                }

                // Fallback to role-based logic ONLY if user has no custom permissions
                switch (_currentUser.UserRole.Name.ToLower())
                {
                    case "admin":
                        System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Using Admin fallback for permission '{permission}' - GRANTED");
                        return true; // Admin has all permissions
                    case "manager":
                        var managerResult = permission.ToLower() != "manageusers";
                        System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Using Manager fallback for permission '{permission}' - {(managerResult ? "GRANTED" : "DENIED")}");
                        return managerResult; // Managers can't manage users
                    case "cashier":
                        var cashierResult = permission.ToLower() switch
                        {
                            "sales" => true,
                            "viewproducts" => true,
                            "viewcustomers" => true,
                            _ => false
                        };
                        System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Using Cashier fallback for permission '{permission}' - {(cashierResult ? "GRANTED" : "DENIED")}");
                        return cashierResult;
                    default:
                        System.Diagnostics.Debug.WriteLine($"[AUTHSERVICE] Unknown role '{_currentUser.UserRole.Name}' - DENIED");
                        return false;
                }
            }
        }

        public bool IsInRole(string role)
        {
            return _currentUser?.UserRole?.Name.Equals(role, StringComparison.OrdinalIgnoreCase) ?? false;
        }
    }
} 