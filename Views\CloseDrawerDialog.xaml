<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CloseDrawerDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="400">
    <StackPanel Margin="16">
        <TextBlock Text="{DynamicResource CloseDrawer}"
                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                 Margin="0,0,0,16"/>

        <Grid Margin="0,0,0,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource ExpectedBalance}"
                     Grid.Row="0" Grid.Column="0"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"/>
            <TextBlock x:Name="ExpectedBalanceText"
                     Grid.Row="0" Grid.Column="1"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     FontWeight="Bold"/>

            <TextBox x:Name="ActualBalanceTextBox"
                    Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    materialDesign:HintAssist.Hint="{DynamicResource ActualBalance}"
                    TextChanged="ActualBalance_TextChanged"
                    Margin="0,8,0,0"/>

            <TextBlock Text="{DynamicResource Difference}"
                     Grid.Row="2" Grid.Column="0"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     Margin="0,8,0,0"/>
            <TextBlock x:Name="DifferenceText"
                     Grid.Row="2" Grid.Column="1"
                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                     FontWeight="Bold"
                     Margin="0,8,0,0"/>
        </Grid>

        <TextBox x:Name="NotesTextBox"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                materialDesign:HintAssist.Hint="{DynamicResource Notes}"
                TextWrapping="Wrap"
                AcceptsReturn="True"
                Height="80"
                Margin="0,0,0,16"/>

        <StackPanel Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="{x:Null}"
                    Content="{DynamicResource Cancel}"
                    Margin="0,0,8,0"/>
            <Button Style="{StaticResource MaterialDesignFlatButton}"
                    Click="CloseDrawer_Click"
                    Content="{DynamicResource Close}"/>
        </StackPanel>
    </StackPanel>
</UserControl> 