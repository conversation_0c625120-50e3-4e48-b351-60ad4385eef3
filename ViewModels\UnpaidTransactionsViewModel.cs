using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using System.Windows.Input;
using POSSystem.Views;
using System.Windows.Data;
using POSSystem.Data;

namespace POSSystem.ViewModels
{
    public class UnpaidTransactionsViewModel : INotifyPropertyChanged
    {
        private readonly IDatabaseService _dbService;
        private ObservableCollection<Sale> _unpaidSales;
        private ObservableCollection<CustomerUnpaidGroup> _groupedUnpaidSales;
        private readonly UserPermissionsService _permissionsService;
        private readonly AlertService _alertService;
        private decimal _unpaidSalesTotal;
        private int _unpaidSalesCount;
        private decimal _pastDueSalesAmount;
        private int _overdueSalesCount;
        private bool _hasOverdueSales;
        private string _searchText;
        private ICollectionView _groupedUnpaidSalesView;

        public event Action<Sale> ShowSaleDetails;

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    FilterGroupedSales();
                }
            }
        }

        private void FilterGroupedSales()
        {
            if (_groupedUnpaidSalesView != null)
            {
                _groupedUnpaidSalesView.Refresh();
                UpdateTotals();
            }
        }

        private bool FilterSales(CustomerUnpaidGroup group)
        {
            if (string.IsNullOrWhiteSpace(SearchText))
                return true;

            string searchText = SearchText.ToLower();

            // Search in customer name
            if (group.CustomerName.ToLower().Contains(searchText))
                return true;

            // Search in sales
            return group.Sales.Any(sale =>
                sale.InvoiceNumber.ToLower().Contains(searchText) ||
                (sale.Customer?.FirstName?.ToLower().Contains(searchText) ?? false) ||
                (sale.Customer?.LastName?.ToLower().Contains(searchText) ?? false));
        }

        private void UpdateTotals()
        {
            var filteredGroups = GroupedUnpaidSales.Where(FilterSales);
            UnpaidSalesTotal = filteredGroups.Sum(g => g.TotalUnpaidAmount);

            var today = DateTime.Now.Date;
            var overdueSales = filteredGroups
                .SelectMany(g => g.Sales)
                .Where(s => s.DueDate.HasValue && s.DueDate.Value.Date < today)
                .ToList();

            OverdueSalesCount = overdueSales.Count;
            HasOverdueSales = overdueSales.Any();
            PastDueSalesAmount = overdueSales.Sum(s => s.RemainingAmount);
        }

        public class CustomerUnpaidGroup : INotifyPropertyChanged
        {
            private string _customerName;
            private decimal _totalUnpaidAmount;
            private int _totalUnpaidTransactions;
            private ObservableCollection<Sale> _sales;
            private bool _isExpanded;

            public string CustomerName
            {
                get => _customerName;
                set
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }

            public decimal TotalUnpaidAmount
            {
                get => _totalUnpaidAmount;
                set
                {
                    _totalUnpaidAmount = value;
                    OnPropertyChanged();
                }
            }

            public int TotalUnpaidTransactions
            {
                get => _totalUnpaidTransactions;
                set
                {
                    _totalUnpaidTransactions = value;
                    OnPropertyChanged();
                }
            }

            public ObservableCollection<Sale> Sales
            {
                get => _sales;
                set
                {
                    _sales = value;
                    OnPropertyChanged();
                }
            }

            public bool IsExpanded
            {
                get => _isExpanded;
                set
                {
                    _isExpanded = value;
                    OnPropertyChanged();
                }
            }

            public ICommand ToggleExpandCommand { get; }
            public ICommand ProcessAllPaymentsCommand { get; }

            public CustomerUnpaidGroup(Action<CustomerUnpaidGroup> processAllPayments)
            {
                ToggleExpandCommand = new RelayCommand(_ => IsExpanded = !IsExpanded);
                ProcessAllPaymentsCommand = new RelayCommand(_ => processAllPayments(this));
            }

            public event PropertyChangedEventHandler PropertyChanged;
            protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        public class RelayCommand : ICommand
        {
            private readonly Action<object> _execute;
            private readonly Func<object, bool> _canExecute;

            public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
            {
                _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                _canExecute = canExecute;
            }

            public event EventHandler CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }

            public bool CanExecute(object parameter)
            {
                return _canExecute == null || _canExecute(parameter);
            }

            public void Execute(object parameter)
            {
                _execute(parameter);
            }
        }

        public UnpaidTransactionsViewModel(IDatabaseService dbService)
        {
            _dbService = dbService;
            _permissionsService = new UserPermissionsService((DatabaseService)dbService);

            // Initialize AlertService with the database context from the DatabaseService
            _alertService = new AlertService((POSDbContext)dbService.Context, (DatabaseService)dbService);

            UnpaidSales = new ObservableCollection<Sale>();
            GroupedUnpaidSales = new ObservableCollection<CustomerUnpaidGroup>();

            // Load data
            LoadUnpaidTransactions();
        }

        public ObservableCollection<Sale> UnpaidSales
        {
            get => _unpaidSales;
            set { _unpaidSales = value; OnPropertyChanged(); }
        }

        public ObservableCollection<CustomerUnpaidGroup> GroupedUnpaidSales
        {
            get => _groupedUnpaidSales;
            set { _groupedUnpaidSales = value; OnPropertyChanged(); }
        }

        public decimal UnpaidSalesTotal
        {
            get => _unpaidSalesTotal;
            set { _unpaidSalesTotal = value; OnPropertyChanged(); }
        }

        public int UnpaidSalesCount
        {
            get => _unpaidSalesCount;
            set { _unpaidSalesCount = value; OnPropertyChanged(); }
        }

        public decimal PastDueSalesAmount
        {
            get => _pastDueSalesAmount;
            set { _pastDueSalesAmount = value; OnPropertyChanged(); }
        }
        
        public int OverdueSalesCount
        {
            get => _overdueSalesCount;
            set { _overdueSalesCount = value; OnPropertyChanged(); }
        }
        
        public bool HasOverdueSales
        {
            get => _hasOverdueSales;
            set { _hasOverdueSales = value; OnPropertyChanged(); }
        }

        public void LoadUnpaidTransactions()
        {
            try
            {
                // Get unpaid sales from database
                var unpaidSales = _dbService.GetUnpaidSales();

                // Group sales by customer
                var groupedSales = unpaidSales
                    .GroupBy(s => new {
                        CustomerId = s.CustomerId ?? -1,
                        CustomerName = s.Customer != null ? $"{s.Customer.FirstName} {s.Customer.LastName}" : "Guest Customer"
                    })
                    .Select(g => new CustomerUnpaidGroup(ProcessAllCustomerPayments)
                    {
                        CustomerName = g.Key.CustomerName,
                        Sales = new ObservableCollection<Sale>(g.OrderByDescending(s => s.SaleDate)),
                        TotalUnpaidAmount = g.Sum(s => s.RemainingAmount),
                        TotalUnpaidTransactions = g.Count()
                    })
                    .OrderByDescending(g => g.TotalUnpaidAmount);

                UnpaidSales = new ObservableCollection<Sale>(unpaidSales);
                GroupedUnpaidSales = new ObservableCollection<CustomerUnpaidGroup>(groupedSales);
                
                // Get unpaid purchase orders from database - now handled by invoices
                // Remove purchase order processing logic
                
                // Calculate totals
                UnpaidSalesTotal = unpaidSales.Sum(s => s.RemainingAmount);
                UnpaidSalesCount = unpaidSales.Count;
                // Remove purchase order count update
                
                // Check for overdue sales
                var today = DateTime.Now.Date;
                var overdueSales = unpaidSales.Where(s => s.DueDate.HasValue && s.DueDate.Value.Date < today).ToList();
                OverdueSalesCount = overdueSales.Count;
                HasOverdueSales = overdueSales.Any();
                PastDueSalesAmount = overdueSales.Sum(s => s.RemainingAmount);
                
                // If there are overdue sales, create alerts
                if (HasOverdueSales)
                {
                    foreach (var sale in overdueSales)
                    {
                        var daysOverdue = (today - sale.DueDate.Value.Date).Days;
                        if (daysOverdue > 0)
                        {
                            var customerName = sale.Customer != null ? $"{sale.Customer.FirstName} {sale.Customer.LastName}" : "Guest Customer";
                            var alertMessage = $"Invoice #{sale.InvoiceNumber} for {customerName} - {daysOverdue} days overdue (Amount: {sale.RemainingAmount:N2})";
                            
                            // Create an alert with the right arguments
                            _alertService.CreateAlert(sale.Id, "OverdueSale", alertMessage, "Sale");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading unpaid transactions: {ex.Message}", "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void ProcessSalePayment(Sale sale, decimal paymentAmount)
        {
            try
            {
                // Update sale payment status and amount
                sale.AmountPaid += paymentAmount;
                decimal remainingAmount = sale.GrandTotal - sale.AmountPaid;
                
                if (remainingAmount <= 0)
                {
                    sale.PaymentStatus = "Paid";
                    _dbService.UpdateSale(sale);
                    
                    // Remove the sale from both collections
                    UnpaidSales.Remove(sale);
                    var customerGroup = GroupedUnpaidSales.FirstOrDefault(g => g.Sales.Contains(sale));
                    if (customerGroup != null)
                    {
                        customerGroup.Sales.Remove(sale);
                        customerGroup.TotalUnpaidAmount -= paymentAmount;
                        customerGroup.TotalUnpaidTransactions--;
                        
                        // Remove the group if it's empty
                        if (customerGroup.Sales.Count == 0)
                        {
                            GroupedUnpaidSales.Remove(customerGroup);
                        }
                    }
                }
                else
                {
                    sale.PaymentStatus = "Partial";
                    _dbService.UpdateSale(sale);
                    
                    // Update the customer group totals
                    var customerGroup = GroupedUnpaidSales.FirstOrDefault(g => g.Sales.Contains(sale));
                    if (customerGroup != null)
                    {
                        customerGroup.TotalUnpaidAmount -= paymentAmount;
                    }
                    
                    // Refresh the sales list to get updated data
                    LoadUnpaidTransactions();
                }

                // Update totals
                UnpaidSalesTotal = UnpaidSales.Sum(s => s.RemainingAmount);

                // Update overdue sales information
                var overdueSales = UnpaidSales.Where(s => s.IsOverdue).ToList();
                HasOverdueSales = overdueSales.Any();
                OverdueSalesCount = overdueSales.Count;

                MessageBox.Show(
                    string.Format(
                        Application.Current.TryFindResource("PaymentProcessedSuccessfully") as string ?? "Payment of {0:N2} DA processed successfully.",
                        paymentAmount),
                    Application.Current.TryFindResource("Success") as string ?? "Success",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{Application.Current.TryFindResource("ErrorProcessingPayment") ?? "Error processing payment"}: {ex.Message}",
                    Application.Current.TryFindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        public void ProcessAllCustomerPayments(CustomerUnpaidGroup customerGroup)
        {
            var dialog = new BatchPaymentDialog(
                customerGroup.CustomerName,
                customerGroup.Sales,
                customerGroup.TotalUnpaidAmount,
                sale => ShowSaleDetails?.Invoke(sale));

            if (dialog.ShowDialog() == true)
            {
                decimal paymentAmount = dialog.PaymentAmount;
                string paymentMethod = dialog.SelectedPaymentMethod;

                // If full payment is less than total amount, distribute proportionally
                bool isPartialPayment = paymentAmount < customerGroup.TotalUnpaidAmount;
                decimal remainingPayment = paymentAmount;

                foreach (var sale in customerGroup.Sales.ToList())
                {
                    decimal salePayment;
                    if (isPartialPayment)
                    {
                        // Calculate proportional payment for this sale
                        decimal ratio = sale.RemainingAmount / customerGroup.TotalUnpaidAmount;
                        salePayment = Math.Round(paymentAmount * ratio, 2);

                        // Adjust for rounding errors on the last payment
                        if (sale == customerGroup.Sales.Last())
                        {
                            salePayment = remainingPayment;
                        }
                    }
                    else
                    {
                        salePayment = sale.RemainingAmount;
                    }

                    // Process the payment
                    ProcessSalePayment(sale, salePayment);
                    remainingPayment -= salePayment;
                }

                MessageBox.Show(
                    string.Format(
                        Application.Current.TryFindResource("BatchPaymentProcessedSuccessfully") as string ??
                        "Batch payment of {0:N2} DA processed successfully for {1}.",
                        paymentAmount,
                        customerGroup.CustomerName),
                    Application.Current.TryFindResource("Success") as string ?? "Success",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 