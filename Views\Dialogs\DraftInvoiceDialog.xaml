<UserControl x:Class="POSSystem.Views.Dialogs.DraftInvoiceDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             Background="Transparent">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        
        <!-- Custom styles for the draft invoice dialog -->
        <Style x:Key="DraftDialogCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="24"/>
            <Setter Property="MaxWidth" Value="900"/>
            <Setter Property="MinHeight" Value="600"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        </Style>

        <Style x:Key="SectionCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
        </Style>

        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>
    </UserControl.Resources>

    <materialDesign:Card Style="{StaticResource DraftDialogCard}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="FileDocumentEdit" 
                                       Width="32" Height="32" 
                                       Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                       VerticalAlignment="Center"/>
                <TextBlock Text="Create Draft Invoice" 
                          Style="{StaticResource HeaderTextBlock}"
                          Margin="12,0,0,0" 
                          VerticalAlignment="Center"/>
                <materialDesign:Chip Content="Draft Mode" 
                                   Margin="16,0,0,0" 
                                   Background="{DynamicResource SecondaryHueMidBrush}"
                                   Foreground="White"/>
            </StackPanel>

            <!-- Basic Info Section -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource SectionCard}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="16"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Customer Selection -->
                    <ComboBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="Customer (Optional)"
                             ItemsSource="{Binding AvailableCustomers}"
                             SelectedItem="{Binding SelectedCustomer}"
                             DisplayMemberPath="FullName"
                             IsEnabled="{Binding CanSelectCustomer}"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                    
                    <!-- Invoice Type (Read-only) -->
                    <TextBox Grid.Column="2"
                            materialDesign:HintAssist.Hint="Invoice Type"
                            Text="Sales Invoice"
                            IsReadOnly="True"
                            Background="{DynamicResource MaterialDesignDivider}"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                </Grid>
            </materialDesign:Card>

            <!-- Product Selection and Items -->
            <Grid Grid.Row="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Product Addition Section -->
                <materialDesign:Card Grid.Row="0" Style="{StaticResource SectionCard}">
                    <StackPanel>
                        <TextBlock Text="Add Products" 
                                  Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                  Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Product Search/Selection -->
                            <ComboBox Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Search and select product"
                                     ItemsSource="{Binding AvailableProducts}"
                                     SelectedItem="{Binding SelectedProduct}"
                                     DisplayMemberPath="Name"
                                     IsEditable="True"
                                     Margin="0,0,8,0"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                            
                            <!-- Quantity -->
                            <TextBox Grid.Column="1"
                                    materialDesign:HintAssist.Hint="Quantity"
                                    Text="{Binding ProductQuantity, UpdateSourceTrigger=PropertyChanged}"
                                    Margin="0,0,8,0"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            
                            <!-- Unit Price (Read-only) -->
                            <TextBox Grid.Column="2"
                                    materialDesign:HintAssist.Hint="Unit Price"
                                    Text="{Binding UnitPriceDisplay, Mode=OneWay}"
                                    IsReadOnly="True"
                                    Background="{DynamicResource MaterialDesignDivider}"
                                    Margin="0,0,8,0"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            
                            <!-- Add Button -->
                            <Button Grid.Column="3"
                                   Content="Add"
                                   Command="{Binding AddProductCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Items List -->
                <ScrollViewer Grid.Row="1" 
                             VerticalScrollBarVisibility="Auto"
                             Margin="0,8,0,0">
                    <StackPanel>
                        <!-- Items Header -->
                        <TextBlock Text="Invoice Items" 
                                  Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                  Margin="8,0,0,8"/>
                        
                        <!-- No Items Message -->
                        <materialDesign:Card Padding="24" 
                                           Visibility="{Binding HasItems, Converter={StaticResource InverseBoolToVisibilityConverter}}"
                                           Background="{DynamicResource MaterialDesignCardBackground}">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="ShoppingCartOff" 
                                                       Width="48" Height="48" 
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock Text="No items added yet" 
                                          Style="{StaticResource MaterialDesignBody1TextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                          HorizontalAlignment="Center"
                                          Margin="0,8,0,0"/>
                                <TextBlock Text="Use the form above to add products to this invoice" 
                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <!-- Items List -->
                        <ItemsControl ItemsSource="{Binding DraftItems}"
                                     Visibility="{Binding HasItems, Converter={StaticResource BoolToVisibilityConverter}}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,4" Padding="16" 
                                                       Background="{DynamicResource MaterialDesignCardBackground}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding ProductName}" 
                                                          FontWeight="Medium"
                                                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                                <TextBlock Text="{Binding ProductSku}" 
                                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                            </StackPanel>
                                            
                                            <TextBlock Grid.Column="1" 
                                                      Text="{Binding QuantityDisplay}" 
                                                      VerticalAlignment="Center" 
                                                      TextAlignment="Center"
                                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                            
                                            <TextBlock Grid.Column="2" 
                                                      Text="{Binding UnitPriceDisplay}" 
                                                      VerticalAlignment="Center" 
                                                      TextAlignment="Right"
                                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                            
                                            <TextBlock Grid.Column="3" 
                                                      Text="{Binding TotalDisplay}" 
                                                      VerticalAlignment="Center" 
                                                      TextAlignment="Right" 
                                                      FontWeight="Bold"
                                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                            
                                            <Button Grid.Column="4" 
                                                   Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.RemoveItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Remove item">
                                                <materialDesign:PackIcon Kind="Delete" 
                                                                       Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                            </Button>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
            
            <!-- Totals Section -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource SectionCard}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock Text="{Binding PermissionMessage}"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  TextWrapping="Wrap"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" 
                                      Text="Subtotal:" 
                                      FontWeight="Medium"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding SubtotalDisplay}" 
                                      FontWeight="Bold"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </Grid>
                        
                        <TextBlock Text="Final pricing and taxes will be set by administrator"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  TextAlignment="Right"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
            
            <!-- Action Buttons -->
            <StackPanel Grid.Row="4" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right" 
                       Margin="0,16,0,0">
                <Button Content="Cancel" 
                       Command="{Binding CancelCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,8,0"/>
                <Button Content="Save Draft for Admin Review" 
                       Command="{Binding SaveDraftCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       IsEnabled="{Binding HasItems}"/>
            </StackPanel>
            
            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="5" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Value="0"
                                IsIndeterminate="True"
                                Width="48" Height="48"/>
                    <TextBlock Text="{Binding StatusMessage}" 
                              Style="{StaticResource MaterialDesignBody1TextBlock}"
                              Foreground="White"
                              HorizontalAlignment="Center"
                              Margin="0,16,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </materialDesign:Card>
</UserControl>
