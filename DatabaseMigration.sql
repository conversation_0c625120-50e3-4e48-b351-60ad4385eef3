-- POS System Database Migration Script
-- This script migrates data from old.pos.db to the current pos.db
-- Created: 2025-01-10
-- Purpose: Preserve valuable data from old database while maintaining current schema

-- First, let's attach the old database
ATTACH DATABASE 'D:\Programs\Programming Projects\Ai Projects\POSSystem\bin\Debug\net8.0-windows\pos - Copy.db' AS old_db;

-- Disable foreign key constraints temporarily for migration
PRAGMA foreign_keys = OFF;

-- Start transaction for data integrity
BEGIN TRANSACTION;

-- ============================================================================
-- STEP 1: Migrate Core Reference Data (Categories, Suppliers, Units of Measure)
-- ============================================================================

-- Migrate Categories (avoiding duplicates)
INSERT OR IGNORE INTO Categories (Id, Name, Description, ParentCategoryId, IsActive)
SELECT Id, Name, Description, ParentCategoryId, IsActive 
FROM old_db.Categories 
WHERE Id NOT IN (SELECT Id FROM Categories);

-- Migrate Suppliers (avoiding duplicates)
INSERT OR IGNORE INTO Suppliers (Id, Name, ContactName, Email, Phone, Address, Website, Notes, IsActive, ProductCount, CreatedAt, UpdatedAt)
SELECT Id, Name, ContactName, Email, Phone, Address, Website, Notes, IsActive, ProductCount, CreatedAt, UpdatedAt 
FROM old_db.Suppliers 
WHERE Id NOT IN (SELECT Id FROM Suppliers);

-- Migrate Units of Measure (avoiding duplicates)
INSERT OR IGNORE INTO UnitsOfMeasure (Id, Name, Abbreviation, Type, BaseUnitId, ConversionFactor, IsActive, CreatedAt, UpdatedAt)
SELECT Id, Name, Abbreviation, Type, BaseUnitId, ConversionFactor, IsActive, CreatedAt, UpdatedAt 
FROM old_db.UnitsOfMeasure 
WHERE Id NOT IN (SELECT Id FROM UnitsOfMeasure);

-- ============================================================================
-- STEP 2: Migrate User Management Data (Roles, Users, Permissions)
-- ============================================================================

-- Migrate Roles (avoiding duplicates)
INSERT OR IGNORE INTO Roles (Id, Name, Description, IsActive, CreatedAt, UpdatedAt)
SELECT Id, Name, Description, IsActive, CreatedAt, UpdatedAt 
FROM old_db.Roles 
WHERE Id NOT IN (SELECT Id FROM Roles);

-- Migrate Users (avoiding duplicates by username)
INSERT OR IGNORE INTO Users (Id, Username, Password, FirstName, LastName, Email, Phone, RoleId, IsActive, PhotoPath, CreatedAt, UpdatedAt)
SELECT Id, Username, Password, FirstName, LastName, Email, Phone, RoleId, IsActive, PhotoPath, CreatedAt, UpdatedAt 
FROM old_db.Users 
WHERE Username NOT IN (SELECT Username FROM Users);

-- Migrate User Permissions (avoiding duplicates)
INSERT OR IGNORE INTO UserPermissions (
    UserId, CanCreateSales, CanVoidSales, CanApplyDiscount, CanViewSalesHistory,
    CanManageProducts, CanManageCategories, CanViewInventory, CanAdjustInventory,
    CanManageExpenses, CanManageCashDrawer, CanViewReports, CanManagePrices,
    CanManageCustomers, CanManageSuppliers, CanManageUsers, CanManageRoles,
    CanAccessSettings, CanViewLogs, CreatedAt, UpdatedAt
)
SELECT 
    UserId, CanCreateSales, CanVoidSales, CanApplyDiscount, CanViewSalesHistory,
    CanManageProducts, CanManageCategories, CanViewInventory, CanAdjustInventory,
    CanManageExpenses, CanManageCashDrawer, CanViewReports, CanManagePrices,
    CanManageCustomers, CanManageSuppliers, CanManageUsers, CanManageRoles,
    CanAccessSettings, CanViewLogs, CreatedAt, UpdatedAt
FROM old_db.UserPermissions 
WHERE UserId NOT IN (SELECT UserId FROM UserPermissions);

-- ============================================================================
-- STEP 3: Migrate Loyalty Program Data
-- ============================================================================

-- Migrate Loyalty Programs (avoiding duplicates)
INSERT OR IGNORE INTO LoyaltyPrograms (Id, Name, Description, PointsPerDollar, MonetaryValuePerPoint, ExpiryMonths, MinimumPointsRedemption, IsActive, CreatedAt)
SELECT Id, Name, Description, PointsPerDollar, MonetaryValuePerPoint, ExpiryMonths, MinimumPointsRedemption, IsActive, CreatedAt 
FROM old_db.LoyaltyPrograms 
WHERE Id NOT IN (SELECT Id FROM LoyaltyPrograms);

-- Migrate Loyalty Tiers (avoiding duplicates)
INSERT OR IGNORE INTO LoyaltyTiers (Id, LoyaltyProgramId, Name, MinimumPoints, PointsMultiplier, Benefits)
SELECT Id, LoyaltyProgramId, Name, MinimumPoints, PointsMultiplier, Benefits 
FROM old_db.LoyaltyTiers 
WHERE Id NOT IN (SELECT Id FROM LoyaltyTiers);

-- ============================================================================
-- STEP 4: Migrate Customer Data
-- ============================================================================

-- Migrate Customers (avoiding duplicates by email or loyalty code)
INSERT OR IGNORE INTO Customers (
    Id, FirstName, LastName, Email, Phone, Address, IsActive, 
    LoyaltyCode, LoyaltyPoints, LastVisit, TotalVisits, TotalSpent, 
    CreatedAt, UpdatedAt, LoyaltyTierId
)
SELECT 
    Id, FirstName, LastName, Email, Phone, Address, IsActive, 
    LoyaltyCode, LoyaltyPoints, LastVisit, TotalVisits, TotalSpent, 
    CreatedAt, UpdatedAt, LoyaltyTierId
FROM old_db.Customers 
WHERE Id NOT IN (SELECT Id FROM Customers) 
AND (Email IS NULL OR Email NOT IN (SELECT Email FROM Customers WHERE Email IS NOT NULL))
AND (LoyaltyCode IS NULL OR LoyaltyCode NOT IN (SELECT LoyaltyCode FROM Customers WHERE LoyaltyCode IS NOT NULL));

-- Migrate Loyalty Transactions
INSERT OR IGNORE INTO LoyaltyTransactions (Id, CustomerId, Points, TransactionDate, Description)
SELECT Id, CustomerId, Points, TransactionDate, Description 
FROM old_db.LoyaltyTransactions 
WHERE Id NOT IN (SELECT Id FROM LoyaltyTransactions);

-- ============================================================================
-- STEP 5: Migrate Product Data
-- ============================================================================

-- Migrate Products (avoiding duplicates by SKU)
-- Note: Current schema has different field types and additional fields
INSERT OR IGNORE INTO Products (
    Id, Name, SKU, Description, PurchasePrice, SellingPrice, DefaultPrice, Type, IsWeightBased, Barcode,
    StockQuantity, MinimumStock, ReorderPoint, IsActive, CreatedAt, UpdatedAt,
    CategoryId, SupplierId, UnitOfMeasureId, ExpiryDate, ImageData, TrackBatches, LoyaltyPoints
)
SELECT
    Id, Name, SKU, Description,
    CAST(PurchasePrice AS TEXT) as PurchasePrice,
    CAST(SellingPrice AS TEXT) as SellingPrice,
    CAST(SellingPrice AS TEXT) as DefaultPrice,
    0 as Type, -- Default to Product type
    0 as IsWeightBased, -- Default to false
    (SELECT Barcode FROM old_db.ProductBarcodes WHERE ProductId = old_db.Products.Id AND IsPrimary = 1 LIMIT 1) as Barcode,
    CAST(StockQuantity AS TEXT) as StockQuantity,
    MinimumStock, ReorderPoint, IsActive, CreatedAt, UpdatedAt,
    CategoryId, SupplierId, UnitOfMeasureId, ExpiryDate, ImageData, TrackBatches,
    CAST(LoyaltyPoints AS TEXT) as LoyaltyPoints
FROM old_db.Products
WHERE Id NOT IN (SELECT Id FROM Products)
AND (SKU IS NULL OR SKU NOT IN (SELECT SKU FROM Products WHERE SKU IS NOT NULL));

-- Migrate Product Barcodes
INSERT OR IGNORE INTO ProductBarcodes (Id, Barcode, ProductId, IsPrimary, CreatedAt, Description)
SELECT Id, Barcode, ProductId, IsPrimary, CreatedAt, Description 
FROM old_db.ProductBarcodes 
WHERE Barcode NOT IN (SELECT Barcode FROM ProductBarcodes);

-- Migrate Product Prices
INSERT OR IGNORE INTO ProductPrices (Id, ProductId, Price, EffectiveDate, PriceType)
SELECT Id, ProductId, Price, EffectiveDate, PriceType 
FROM old_db.ProductPrices 
WHERE Id NOT IN (SELECT Id FROM ProductPrices);

-- Migrate Product Alerts
INSERT OR IGNORE INTO ProductAlerts (Id, ProductId, AlertType, Message, CreatedAt, IsRead, ReadAt, ReferenceId, ReferenceType)
SELECT Id, ProductId, AlertType, Message, CreatedAt, IsRead, ReadAt, ReferenceId, ReferenceType 
FROM old_db.ProductAlerts 
WHERE Id NOT IN (SELECT Id FROM ProductAlerts);

-- Migrate Batch Stock
INSERT OR IGNORE INTO BatchStock (Id, ProductId, BatchNumber, Quantity, ManufactureDate, ExpiryDate, PurchasePrice, Location, Notes, CreatedAt)
SELECT Id, ProductId, BatchNumber, Quantity, ManufactureDate, ExpiryDate, PurchasePrice, Location, Notes, CreatedAt 
FROM old_db.BatchStock 
WHERE Id NOT IN (SELECT Id FROM BatchStock);

-- Migrate User Favorites
INSERT OR IGNORE INTO UserFavorites (Id, UserId, ProductId, CreatedAt)
SELECT Id, UserId, ProductId, CreatedAt 
FROM old_db.UserFavorites 
WHERE Id NOT IN (SELECT Id FROM UserFavorites);

-- ============================================================================
-- STEP 6: Migrate Sales and Transaction Data
-- ============================================================================

-- Migrate Sales (avoiding duplicates by invoice number)
INSERT OR IGNORE INTO Sales (
    Id, InvoiceNumber, SaleDate, DueDate, CustomerId, UserId, Subtotal, DiscountAmount,
    TaxAmount, GrandTotal, PaymentMethod, PaymentStatus, AmountPaid, Change, Status, TotalItems
)
SELECT 
    Id, InvoiceNumber, SaleDate, DueDate, CustomerId, UserId, Subtotal, DiscountAmount,
    TaxAmount, GrandTotal, PaymentMethod, PaymentStatus, AmountPaid, Change, Status, TotalItems
FROM old_db.Sales 
WHERE InvoiceNumber NOT IN (SELECT InvoiceNumber FROM Sales);

-- Migrate Sale Items
INSERT OR IGNORE INTO SaleItems (Id, SaleId, ProductId, Quantity, UnitPrice, Total)
SELECT Id, SaleId, ProductId, Quantity, UnitPrice, Total 
FROM old_db.SaleItems 
WHERE Id NOT IN (SELECT Id FROM SaleItems);

-- Migrate Payments
INSERT OR IGNORE INTO Payments (Id, SaleId, PaymentDate, PaymentMethod, Amount, ReferenceNumber, Status)
SELECT Id, SaleId, PaymentDate, PaymentMethod, Amount, ReferenceNumber, Status 
FROM old_db.Payments 
WHERE Id NOT IN (SELECT Id FROM Payments);

-- Migrate Sale History
INSERT OR IGNORE INTO SaleHistory (Id, SaleId, Action, Reason, UserId, ActionDate, AdjustmentAmount)
SELECT Id, SaleId, Action, Reason, UserId, ActionDate, AdjustmentAmount 
FROM old_db.SaleHistory 
WHERE Id NOT IN (SELECT Id FROM SaleHistory);

-- ============================================================================
-- STEP 7: Migrate Discount and Business Data
-- ============================================================================

-- Migrate Discount Types
INSERT OR IGNORE INTO DiscountTypes (Id, Name, Description)
SELECT Id, Name, Description 
FROM old_db.DiscountTypes 
WHERE Id NOT IN (SELECT Id FROM DiscountTypes);

-- Migrate Discount Reasons
INSERT OR IGNORE INTO DiscountReasons (Id, Code, Description, IsActive)
SELECT Id, Code, Description, IsActive 
FROM old_db.DiscountReasons 
WHERE Id NOT IN (SELECT Id FROM DiscountReasons);

-- Migrate Discount Permissions
INSERT OR IGNORE INTO DiscountPermissions (
    Id, RoleId, DiscountTypeId, MaxPercentage, MaxFixedAmount, MinPricePercentage,
    RequiresApproval, ApprovalThreshold, IsActive, CreatedAt, UpdatedAt
)
SELECT 
    Id, RoleId, DiscountTypeId, MaxPercentage, MaxFixedAmount, MinPricePercentage,
    RequiresApproval, ApprovalThreshold, IsActive, CreatedAt, UpdatedAt
FROM old_db.DiscountPermissions 
WHERE Id NOT IN (SELECT Id FROM DiscountPermissions);

-- Migrate Discounts
INSERT OR IGNORE INTO Discounts (
    Id, SaleId, SaleItemId, DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice,
    ReasonId, Comment, AppliedByUserId, ApprovedByUserId, AppliedAt, ApprovedAt, IsActive
)
SELECT 
    Id, SaleId, SaleItemId, DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice,
    ReasonId, Comment, AppliedByUserId, ApprovedByUserId, AppliedAt, ApprovedAt, IsActive
FROM old_db.Discounts 
WHERE Id NOT IN (SELECT Id FROM Discounts);

-- ============================================================================
-- STEP 8: Migrate Cash Drawer and Business Expense Data
-- ============================================================================

-- Migrate Cash Drawers
INSERT OR IGNORE INTO CashDrawers (
    Id, OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, Difference,
    OpenedAt, ClosedAt, OpenedById, ClosedById, Status, Notes
)
SELECT 
    Id, OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, Difference,
    OpenedAt, ClosedAt, OpenedById, ClosedById, Status, Notes
FROM old_db.CashDrawers 
WHERE Id NOT IN (SELECT Id FROM CashDrawers);

-- Migrate Cash Transactions
INSERT OR IGNORE INTO CashTransactions (
    Id, CashDrawerId, Type, Amount, Reason, Notes, Timestamp, PerformedById, Reference
)
SELECT 
    Id, CashDrawerId, Type, Amount, Reason, Notes, Timestamp, PerformedById, Reference
FROM old_db.CashTransactions 
WHERE Id NOT IN (SELECT Id FROM CashTransactions);

-- Migrate Business Expenses
INSERT OR IGNORE INTO BusinessExpenses (
    Id, Description, Amount, Date, Category, Frequency, NextDueDate, Notes, CashDrawerId, UserId
)
SELECT 
    Id, Description, Amount, Date, Category, Frequency, NextDueDate, Notes, CashDrawerId, UserId
FROM old_db.BusinessExpenses 
WHERE Id NOT IN (SELECT Id FROM BusinessExpenses);

-- ============================================================================
-- STEP 9: Migrate Purchase Order Data
-- ============================================================================

-- Migrate Purchase Orders
INSERT OR IGNORE INTO PurchaseOrders (
    Id, OrderNumber, OrderDate, DueDate, SupplierId, Status, PaymentMethod, PaymentReference,
    PaymentDate, CreatedAt, CreatedByUserId, UpdatedAt, Notes, Subtotal, TaxAmount, GrandTotal
)
SELECT 
    Id, OrderNumber, OrderDate, DueDate, SupplierId, Status, PaymentMethod, PaymentReference,
    PaymentDate, CreatedAt, CreatedByUserId, UpdatedAt, Notes, Subtotal, TaxAmount, GrandTotal
FROM old_db.PurchaseOrders 
WHERE Id NOT IN (SELECT Id FROM PurchaseOrders);

-- Migrate Purchase Order Items
INSERT OR IGNORE INTO PurchaseOrderItems (
    Id, PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, Notes, BatchNumber, ExpiryDate, Location
)
SELECT 
    Id, PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, Notes, BatchNumber, ExpiryDate, Location
FROM old_db.PurchaseOrderItems 
WHERE Id NOT IN (SELECT Id FROM PurchaseOrderItems);

-- ============================================================================
-- STEP 10: Migrate Inventory and Audit Data
-- ============================================================================

-- Migrate Inventory Transactions
INSERT OR IGNORE INTO InventoryTransactions (
    Id, ProductId, TransactionType, Quantity, UnitPrice, Reference, TransactionDate, Notes, UserId
)
SELECT 
    Id, ProductId, TransactionType, Quantity, UnitPrice, Reference, TransactionDate, Notes, UserId
FROM old_db.InventoryTransactions 
WHERE Id NOT IN (SELECT Id FROM InventoryTransactions);

-- Migrate Audit records
INSERT OR IGNORE INTO Audit (Id, TableName, Action, RecordId, OldValues, NewValues, Timestamp, UserId)
SELECT Id, TableName, Action, RecordId, OldValues, NewValues, Timestamp, UserId 
FROM old_db.Audit 
WHERE Id NOT IN (SELECT Id FROM Audit);

-- ============================================================================
-- FINALIZATION
-- ============================================================================

-- Update sqlite_sequence to maintain proper auto-increment values
UPDATE sqlite_sequence SET seq = (
    SELECT MAX(seq) FROM (
        SELECT seq FROM sqlite_sequence WHERE name = sqlite_sequence.name
        UNION ALL
        SELECT seq FROM old_db.sqlite_sequence WHERE name = sqlite_sequence.name
    )
) WHERE name IN (SELECT name FROM old_db.sqlite_sequence);

-- Re-enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Commit the transaction
COMMIT;

-- Detach the old database
DETACH DATABASE old_db;

-- Verify migration results
SELECT 'Migration completed successfully. Data counts:' as Status;
SELECT 'Users: ' || COUNT(*) FROM Users;
SELECT 'Products: ' || COUNT(*) FROM Products;
SELECT 'Categories: ' || COUNT(*) FROM Categories;
SELECT 'Sales: ' || COUNT(*) FROM Sales;
SELECT 'Customers: ' || COUNT(*) FROM Customers;
SELECT 'Suppliers: ' || COUNT(*) FROM Suppliers;
