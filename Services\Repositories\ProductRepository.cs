using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Repositories
{
    /// <summary>
    /// CORRECT implementation of Product repository
    /// This shows how to properly use injected DbContext
    /// </summary>
    public class ProductRepository : IProductRepository
    {
        private readonly POSDbContext _context;
        private readonly ILogger<ProductRepository> _logger;

        // ✅ CORRECT: Use injected DbContext
        public ProductRepository(POSDbContext context, ILogger<ProductRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
        }

        public async Task<Product> GetByIdAsync(int id)
        {
            try
            {
                // ✅ CORRECT: Use injected context, not new connection
                return await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Supplier)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .AsNoTracking() // Performance optimization
                    .FirstOrDefaultAsync(p => p.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting product {ProductId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Product>> GetPagedAsync(int page, int pageSize)
        {
            try
            {
                // ✅ CORRECT: Pagination with barcode filtering for POS system
                return await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .Where(p => p.IsActive &&
                               (p.Barcodes.Any() || !string.IsNullOrEmpty(p.Barcode))) // Only products with barcodes
                    .OrderBy(p => p.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting paged products page {Page}, size {PageSize}", page, pageSize);
                throw;
            }
        }

        public async Task<IEnumerable<Product>> SearchAsync(string searchTerm, int maxResults = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<Product>();

                // ✅ CORRECT: Efficient search with projection and barcode support
                var products = await _context.Products
                    .Where(p => p.IsActive &&
                               (p.Name.Contains(searchTerm) ||
                                p.SKU.Contains(searchTerm) ||
                                p.Description.Contains(searchTerm)))
                    .Include(p => p.Category)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .Take(maxResults) // Configurable limit for performance
                    .AsNoTracking()
                    .ToListAsync();

                // Also search by barcode if no products found by name/SKU
                if (!products.Any())
                {
                    var barcodeProducts = await _context.Products
                        .Where(p => p.IsActive && p.Barcodes.Any(b => b.Barcode.Contains(searchTerm)))
                        .Include(p => p.Category)
                        .Include(p => p.Barcodes)
                        .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                        .Take(maxResults)
                        .AsNoTracking()
                        .ToListAsync();

                    products = products.Concat(barcodeProducts).ToList();
                }

                return products;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error searching products with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<Product> CreateAsync(Product product)
        {
            try
            {
                product.CreatedAt = DateTime.Now;
                product.UpdatedAt = DateTime.Now;

                // ✅ CORRECT: Use injected context
                _context.Products.Add(product);
                await _context.SaveChangesAsync();
                
                return product;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating product {ProductName}", product.Name);
                throw;
            }
        }

        public async Task UpdateAsync(Product product)
        {
            try
            {
                var existingProduct = await _context.Products.FindAsync(product.Id);
                if (existingProduct == null)
                    throw new InvalidOperationException($"Product with ID {product.Id} not found");

                // ✅ CORRECT: Update tracked entity
                existingProduct.Name = product.Name;
                existingProduct.Description = product.Description;
                existingProduct.SKU = product.SKU;
                existingProduct.CategoryId = product.CategoryId;
                existingProduct.SupplierId = product.SupplierId;
                existingProduct.PurchasePrice = product.PurchasePrice;
                existingProduct.SellingPrice = product.SellingPrice;
                existingProduct.StockQuantity = product.StockQuantity;
                existingProduct.MinimumStock = product.MinimumStock;
                existingProduct.ReorderPoint = product.ReorderPoint;
                existingProduct.IsActive = product.IsActive;
                existingProduct.UpdatedAt = DateTime.Now;
                existingProduct.UnitOfMeasureId = product.UnitOfMeasureId;
                existingProduct.ExpiryDate = product.ExpiryDate;
                existingProduct.ImageData = product.ImageData;
                existingProduct.TrackBatches = product.TrackBatches;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating product {ProductId}", product.Id);
                throw;
            }
        }

        public async Task DeleteAsync(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product != null)
                {
                    // Soft delete
                    product.IsActive = false;
                    product.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error deleting product {ProductId}", id);
                throw;
            }
        }

        public async Task UpdateStockAsync(int productId, int newQuantity)
        {
            try
            {
                var product = await _context.Products.FindAsync(productId);
                if (product != null)
                {
                    product.StockQuantity = newQuantity;
                    product.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating stock for product {ProductId}", productId);
                throw;
            }
        }

        public async Task<bool> ExistsByBarcodeAsync(string barcode)
        {
            try
            {
                return await _context.ProductBarcodes
                    .AnyAsync(pb => pb.Barcode == barcode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if product exists with barcode {Barcode}", barcode);
                throw;
            }
        }

        public async Task<bool> ExistsByIdAsync(int id)
        {
            try
            {
                return await _context.Products.AnyAsync(p => p.Id == id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error checking if product exists with ID {ProductId}", id);
                throw;
            }
        }

        public async Task<Product> GetByBarcodeAsync(string barcode)
        {
            try
            {
                return await _context.Products
                    .Where(p => p.IsActive && p.Barcodes.Any(b => b.Barcode == barcode))
                    .Include(p => p.Category)
                    .Include(p => p.Supplier)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting product by barcode {Barcode}", barcode);
                throw;
            }
        }

        public async Task<IEnumerable<Product>> GetExpiringProductsAsync(int daysThreshold = 30)
        {
            try
            {
                var thresholdDate = DateTime.Now.AddDays(daysThreshold);
                return await _context.Products
                    .Where(p => p.IsActive &&
                               p.ExpiryDate.HasValue &&
                               p.ExpiryDate.Value <= thresholdDate)
                    .Include(p => p.Category)
                    .OrderBy(p => p.ExpiryDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting expiring products with threshold {DaysThreshold}", daysThreshold);
                throw;
            }
        }

        public async Task<bool> SoftDeleteAsync(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product != null)
                {
                    product.IsActive = false;
                    product.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error soft deleting product {ProductId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Product>> GetLowStockAsync()
        {
            try
            {
                return await _context.Products
                    .Where(p => p.IsActive && p.StockQuantity <= p.ReorderPoint)
                    .Include(p => p.Category)
                    .Include(p => p.Batches) // ✅ PERFORMANCE FIX: Include batch data
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting low stock products");
                throw;
            }
        }

        public async Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId)
        {
            try
            {
                return await _context.Products
                    .Where(p => p.IsActive && p.Id > 0 && p.CategoryId == categoryId) // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs)
                    .Include(p => p.Category)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting products by category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<IEnumerable<Product>> GetAllAsync()
        {
            try
            {
                // ⚠️ WARNING: This should be used carefully - consider pagination
                return await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .Include(p => p.Batches) // ✅ CRITICAL FIX: Include batches for accurate stock calculation
                    .Where(p => p.IsActive &&
                               (p.Barcodes.Any() || !string.IsNullOrEmpty(p.Barcode))) // Only products with barcodes
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting all products");
                throw;
            }
        }

        public async Task<IEnumerable<Product>> CreateBatchAsync(IEnumerable<Product> products)
        {
            try
            {
                var now = DateTime.Now;
                foreach (var product in products)
                {
                    product.CreatedAt = now;
                    product.UpdatedAt = now;
                }

                _context.Products.AddRange(products);
                await _context.SaveChangesAsync();
                
                return products;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating batch of products");
                throw;
            }
        }

        public async Task UpdateBatchAsync(IEnumerable<Product> products)
        {
            try
            {
                var now = DateTime.Now;
                foreach (var product in products)
                {
                    product.UpdatedAt = now;
                }

                _context.Products.UpdateRange(products);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating batch of products");
                throw;
            }
        }

        // Statistics methods - replacing heavy DatabaseService queries
        // ✅ FIX: Use ConfigureAwait(false) to prevent deadlocks and improve performance
        public async Task<int> GetTotalCountAsync()
        {
            try
            {
                return await _context.Products
                    .AsNoTracking() // ✅ FIX: Add AsNoTracking for better performance
                    .CountAsync(p => p.IsActive)
                    .ConfigureAwait(false); // ✅ FIX: Prevent deadlocks
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting total product count");
                throw;
            }
        }

        public async Task<int> GetLowStockCountAsync()
        {
            try
            {
                return await _context.Products
                    .AsNoTracking() // ✅ FIX: Add AsNoTracking for better performance
                    .CountAsync(p => p.IsActive && p.StockQuantity <= p.ReorderPoint)
                    .ConfigureAwait(false); // ✅ FIX: Prevent deadlocks
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting low stock count");
                throw;
            }
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            try
            {
                // ✅ FIX: Use AsNoTracking and ConfigureAwait for better performance
                // SQLite doesn't support Sum on decimal, so we use client-side evaluation
                var products = await _context.Products
                    .AsNoTracking() // ✅ FIX: Add AsNoTracking for better performance
                    .Where(p => p.IsActive)
                    .Select(p => new { p.StockQuantity, p.PurchasePrice })
                    .ToListAsync()
                    .ConfigureAwait(false); // ✅ FIX: Prevent deadlocks

                return products.Sum(p => (decimal)p.StockQuantity * p.PurchasePrice);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating total inventory value");
                throw;
            }
        }
    }
}
