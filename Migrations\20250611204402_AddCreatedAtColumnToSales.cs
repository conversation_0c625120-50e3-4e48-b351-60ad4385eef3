﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddCreatedAtColumnToSales : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7272), new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7250), new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7273) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7278), new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7277), new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(7279) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 970, DateTimeKind.Local).AddTicks(3474));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(8331));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(8334));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 965, DateTimeKind.Local).AddTicks(8339));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5798));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5805));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5823));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5829));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5837));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5843));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5848));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 44, 1, 973, DateTimeKind.Local).AddTicks(5911));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 44, 1, 966, DateTimeKind.Local).AddTicks(2563), new DateTime(2025, 6, 11, 21, 44, 1, 966, DateTimeKind.Local).AddTicks(2570) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5847), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5818), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5848) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5853), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5852), new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(5854) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 182, DateTimeKind.Local).AddTicks(8170));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(6981));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(6994));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 180, DateTimeKind.Local).AddTicks(7000));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6933));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6938));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6950));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6953));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6958));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6962));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(6965));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 6, 11, 21, 41, 43, 185, DateTimeKind.Local).AddTicks(7008));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 11, 21, 41, 43, 181, DateTimeKind.Local).AddTicks(1392), new DateTime(2025, 6, 11, 21, 41, 43, 181, DateTimeKind.Local).AddTicks(1399) });
        }
    }
}
