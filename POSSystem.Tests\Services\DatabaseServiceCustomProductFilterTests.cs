using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services;
using Xunit;
using Microsoft.Extensions.Logging;

namespace POSSystem.Tests.Services
{
    /// <summary>
    /// Tests to verify that custom products (with negative IDs) are properly excluded
    /// from regular product listings in DatabaseService methods.
    /// </summary>
    public class DatabaseServiceCustomProductFilterTests : IDisposable
    {
        private readonly POSDbContext _context;
        private readonly DatabaseService _databaseService;

        public DatabaseServiceCustomProductFilterTests()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);

            // Create a mock logger
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<DatabaseService>();

            _databaseService = new DatabaseService(_context, logger);

            // Seed test data
            SeedTestData();
        }

        private void SeedTestData()
        {
            // Add a test category
            var category = new Category
            {
                Id = 1,
                Name = "Test Category",
                Description = "Test category for products",
                IsActive = true
            };
            _context.Categories.Add(category);

            // Add regular products (positive IDs)
            var regularProduct1 = new Product
            {
                Id = 1,
                Name = "Regular Product 1",
                SKU = "REG001",
                Barcode = "123456789",
                CategoryId = 1,
                SellingPrice = 10.00m,
                PurchasePrice = 5.00m,
                StockQuantity = 100,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            var regularProduct2 = new Product
            {
                Id = 2,
                Name = "Regular Product 2",
                SKU = "REG002",
                Barcode = "987654321",
                CategoryId = 1,
                SellingPrice = 15.00m,
                PurchasePrice = 8.00m,
                StockQuantity = 50,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // Add custom products (negative IDs) - these should be excluded
            var customProduct1 = new Product
            {
                Id = -1,
                Name = "Custom: Custom Product 1",
                SKU = "CUSTOM-1",
                CategoryId = 1,
                SellingPrice = 20.00m,
                PurchasePrice = 14.00m,
                StockQuantity = 1,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            var customProduct2 = new Product
            {
                Id = -2,
                Name = "Custom: Custom Product 2",
                SKU = "CUSTOM-2",
                CategoryId = 1,
                SellingPrice = 25.00m,
                PurchasePrice = 17.50m,
                StockQuantity = 1,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.Products.AddRange(regularProduct1, regularProduct2, customProduct1, customProduct2);

            // Add barcodes for regular products (required by some methods)
            _context.ProductBarcodes.AddRange(
                new ProductBarcode { ProductId = 1, Barcode = "123456789" },
                new ProductBarcode { ProductId = 2, Barcode = "987654321" }
            );

            _context.SaveChanges();
        }

        [Fact]
        public async Task GetProductsAsync_ShouldExcludeCustomProducts()
        {
            // Act
            var result = await _databaseService.GetProductsAsync(
                pageSize: 10,
                offset: 0,
                categoryId: null,
                searchText: null
            );

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public void GetAllProducts_ShouldExcludeCustomProducts()
        {
            // Act
            var result = _databaseService.GetAllProducts();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public async Task GetAllProductsAsync_ShouldExcludeCustomProducts()
        {
            // Act
            var result = await _databaseService.GetAllProductsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public async Task SearchProductsAsync_ShouldExcludeCustomProducts()
        {
            // Act - search for "Product" which should match both regular and custom products
            var result = await _databaseService.SearchProductsAsync("Product");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public void SearchProducts_ShouldExcludeCustomProducts()
        {
            // Act - search for "Product" which should match both regular and custom products
            var result = _databaseService.SearchProducts("Product");

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public async Task GetAllProductsWithFullDetailsAsync_ShouldExcludeCustomProducts()
        {
            // Act
            var result = await _databaseService.GetAllProductsWithFullDetailsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public void GetAllProductsWithFullDetails_ShouldExcludeCustomProducts()
        {
            // Act
            var result = _databaseService.GetAllProductsWithFullDetails();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public async Task GetProductsAsync_WithCategoryFilter_ShouldExcludeCustomProducts()
        {
            // Act - filter by category 1 which contains both regular and custom products
            var result = await _databaseService.GetProductsAsync(
                pageSize: 10,
                offset: 0,
                categoryId: 1,
                searchText: null
            );

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "should only return regular products in the category, not custom products");
            result.All(p => p.Id > 0).Should().BeTrue("all returned products should have positive IDs");
            result.All(p => p.CategoryId == 1).Should().BeTrue("all returned products should be in category 1");
            result.Should().Contain(p => p.Name == "Regular Product 1");
            result.Should().Contain(p => p.Name == "Regular Product 2");
            result.Should().NotContain(p => p.Name.StartsWith("Custom:"));
        }

        [Fact]
        public async Task GetProductsAsync_WithSearchText_ShouldExcludeCustomProducts()
        {
            // Act - search for "Custom" which should match custom products but they should be excluded
            var result = await _databaseService.GetProductsAsync(
                pageSize: 10,
                offset: 0,
                categoryId: null,
                searchText: "Custom"
            );

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("should not return custom products even when searching for 'Custom'");
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
