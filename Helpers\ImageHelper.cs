using System;
using System.IO;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using System.Windows;

namespace POSSystem.Helpers
{
    public static class ImageHelper
    {
        /// <summary>
        /// Converts a base64 string to a BitmapImage that can be used in WPF
        /// </summary>
        public static BitmapImage Base64ToImage(string base64String)
        {
            try
            {
                if (string.IsNullOrEmpty(base64String))
                    return null;

                byte[] imageBytes = Convert.FromBase64String(base64String);
                using (var memoryStream = new MemoryStream(imageBytes))
                {
                    var image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.StreamSource = memoryStream;
                    image.EndInit();
                    image.Freeze(); // Makes the image readable from any thread
                    return image;
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Converts a BitmapImage to a base64 string
        /// </summary>
        public static string ImageToBase64(BitmapImage image)
        {
            try
            {
                if (image == null)
                    return null;

                var encoder = new JpegBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(image));

                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    byte[] imageBytes = memoryStream.ToArray();
                    return Convert.ToBase64String(imageBytes);
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Converts a file path to a base64 string
        /// </summary>
        public static string FileToBase64(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return null;

                byte[] imageBytes = File.ReadAllBytes(filePath);
                return Convert.ToBase64String(imageBytes);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Resizes an image to the specified dimensions while maintaining aspect ratio
        /// </summary>
        public static BitmapImage ResizeImage(BitmapImage originalImage, int maxWidth, int maxHeight)
        {
            try
            {
                if (originalImage == null)
                    return null;

                double ratioX = (double)maxWidth / originalImage.PixelWidth;
                double ratioY = (double)maxHeight / originalImage.PixelHeight;
                double ratio = Math.Min(ratioX, ratioY);

                int newWidth = (int)(originalImage.PixelWidth * ratio);
                int newHeight = (int)(originalImage.PixelHeight * ratio);

                var resizedImage = new TransformedBitmap(originalImage, 
                    new ScaleTransform(ratio, ratio));

                var output = new BitmapImage();
                var encoder = new JpegBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(resizedImage));

                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    memoryStream.Position = 0;

                    output.BeginInit();
                    output.CacheOption = BitmapCacheOption.OnLoad;
                    output.StreamSource = memoryStream;
                    output.EndInit();
                    output.Freeze();
                }

                return output;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Validates if a string is a valid base64 image
        /// </summary>
        public static bool IsValidBase64Image(string base64String)
        {
            if (string.IsNullOrEmpty(base64String))
                return false;

            try
            {
                byte[] imageBytes = Convert.FromBase64String(base64String);
                using (var memoryStream = new MemoryStream(imageBytes))
                {
                    var image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.StreamSource = memoryStream;
                    image.EndInit();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Optimizes an image by reducing its quality and size
        /// </summary>
        public static string OptimizeImage(string base64String, int maxWidth = 800, int maxHeight = 800, int quality = 75)
        {
            try
            {
                if (string.IsNullOrEmpty(base64String))
                    return null;

                // Convert base64 to image
                var originalImage = Base64ToImage(base64String);
                if (originalImage == null)
                    return null;

                // Resize the image
                var resizedImage = ResizeImage(originalImage, maxWidth, maxHeight);
                if (resizedImage == null)
                    return null;

                // Create encoder with quality setting
                var encoder = new JpegBitmapEncoder();
                encoder.QualityLevel = quality;
                encoder.Frames.Add(BitmapFrame.Create(resizedImage));

                // Convert back to base64
                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    byte[] imageBytes = memoryStream.ToArray();
                    return Convert.ToBase64String(imageBytes);
                }
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
} 