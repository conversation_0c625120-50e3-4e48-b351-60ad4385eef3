using System;
using System.Threading.Tasks;
using POSSystem.Services.Caching;
using POSSystem.Services.Performance;
using POSSystem.Helpers;

namespace POSSystem
{
    /// <summary>
    /// Simple compilation test to verify the performance optimizations compile correctly
    /// </summary>
    public class CompilationTest
    {
        public static async Task TestCompilationAsync()
        {
            try
            {
                // Test ProductCacheService compilation
                var productCache = new ProductCacheService();
                var stats = productCache.GetCacheStatistics();
                Console.WriteLine($"Product cache initialized with {stats.ProductCacheCount} products");

                // Test ProductsPerformanceMonitor compilation
                var perfMonitor = new ProductsPerformanceMonitor();
                var opId = perfMonitor.StartOperation("TestOperation", "Compilation test");
                perfMonitor.StopOperation("TestOperation", opId, "Test completed");

                // Test CachedRepositoryService.ClearCache() method
                // Note: CachedRepositoryService now requires DatabaseService, so we'll skip this test
                // var cachedRepo = new CachedRepositoryService(new AdvancedCacheService(null), databaseService);
                // cachedRepo.ClearCache(); // This should now compile
                Console.WriteLine("✅ CachedRepositoryService constructor updated (test skipped)");

                // Test PerformanceHelper.ExecuteOnBackgroundThreadAsync() method
                var result = await PerformanceHelper.ExecuteOnBackgroundThreadAsync(async () =>
                {
                    await Task.Delay(10); // Simulate async work
                    return "Background task completed";
                }, "Test Background Operation");
                Console.WriteLine($"✅ PerformanceHelper.ExecuteOnBackgroundThreadAsync() result: {result}");

                // Test IntegratedPerformanceTestService compilation
                var testService = new POSSystem.Services.Testing.IntegratedPerformanceTestService();
                Console.WriteLine("✅ IntegratedPerformanceTestService initialized successfully");

                Console.WriteLine("✅ All performance optimization classes and methods compile successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Compilation test failed: {ex.Message}");
            }
        }

        public static void TestCompilation()
        {
            TestCompilationAsync().Wait();
        }
    }
}
