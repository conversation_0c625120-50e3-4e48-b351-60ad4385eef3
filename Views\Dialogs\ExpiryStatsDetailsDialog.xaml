<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.ExpiryStatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             mc:Ignorable="d"
             MinWidth="640" MinHeight="480"
             Background="{DynamicResource MaterialDesignPaper}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid Margin="24"
              MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
              MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource ExpiryStats}"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       VerticalAlignment="Center"/>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Click="CloseButton_Click"
                    ToolTip="{DynamicResource Close}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Period Filter -->
            <TextBlock Grid.Column="0" 
                       Text="{DynamicResource Period}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       VerticalAlignment="Center"
                       Margin="0,0,8,0"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding TrendPeriods}"
                      SelectedItem="{Binding SelectedTrendPeriod}"
                      DisplayMemberPath="DisplayName"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,16,0"/>

            <!-- Category Filter -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,8,0">
                <CheckBox IsChecked="{Binding IsCategoryFilterEnabled}"
                          Content="{DynamicResource Category}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            <ComboBox Grid.Column="3"
                      IsEnabled="{Binding IsCategoryFilterEnabled}"
                      ItemsSource="{Binding Categories}"
                      SelectedItem="{Binding SelectedCategory}"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </Grid>

        <!-- Subtitle -->
        <TextBlock Grid.Row="2" 
                   Text="{Binding Subtitle}"
                   Style="{StaticResource MaterialDesignBody1TextBlock}"
                   Opacity="0.6"
                   Margin="0,0,0,8"/>

        <!-- Main Content -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metrics Cards -->
            <UniformGrid Rows="1" Margin="0,0,0,16">
                <!-- Total Expiring Products Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertCircle" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource TotalExpiringProducts}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Text="{Binding TotalExpiringProducts}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2"/>
                        <TextBlock Text="{DynamicResource ProductsNeedingAttention}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.6"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Expired Products Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertOctagon" 
                                                   Width="20" Height="20"
                                                   Foreground="Red"/>
                            <TextBlock Text="{DynamicResource ExpiredProducts}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Text="{Binding ExpiredProducts}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2"/>
                        <TextBlock Text="{DynamicResource ProductsExpired}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.6"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Near Expiry Products Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertOutline" 
                                                   Width="20" Height="20"
                                                   Foreground="Orange"/>
                            <TextBlock Text="{DynamicResource NearExpiryProducts}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Text="{Binding NearExpiryProducts}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2"/>
                        <TextBlock Text="{DynamicResource ProductsExpiringSoon}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.6"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Potential Loss Card -->
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="20" Height="20"
                                                   Foreground="Red"/>
                            <TextBlock Text="{DynamicResource PotentialLoss}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <DockPanel>
                            <TextBlock Text="{Binding PotentialLoss, StringFormat=N2}"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Margin="0,4,0,2"/>
                            <TextBlock Text="{DynamicResource CurrencySymbol}"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Margin="4,4,0,2"/>
                        </DockPanel>
                        <TextBlock Text="{DynamicResource EstimatedLossValue}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.6"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Padding="8">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Expiry Trend -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpiryTrend}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpiryTrend}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding ExpiryTrendSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding ExpiryTrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            LabelFormatter="{Binding NumberFormatter}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Category Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource CategoryDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartPie"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource CategoryDistribution}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:PieChart Grid.Row="1"
                                        Series="{Binding CategoryDistributionSeries}"
                                        LegendLocation="Right"/>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Expiry by Days -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpiryByDays}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpiryByDays}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding ExpiryByDaysSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding ExpiryByDaysLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            LabelFormatter="{Binding NumberFormatter}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 4: Expiring Products List -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ExpiringProducts}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ExpiringProducts}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <DataGrid Grid.Row="1"
                                      ItemsSource="{Binding ExpiringProducts}"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      Style="{StaticResource MaterialDesignDataGrid}"
                                      Height="400"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridRow">
                                        <Style.Triggers>
                                            <!-- Expired products (red) -->
                                            <DataTrigger Binding="{Binding IsExpired}" Value="True">
                                                <Setter Property="Background" Value="#1FFF0000"/>
                                                <Setter Property="Foreground" Value="#FF0000"/>
                                            </DataTrigger>
                                            <!-- Nearly expired products (yellow) -->
                                            <DataTrigger Binding="{Binding IsNearlyExpired}" Value="True">
                                                <Setter Property="Background" Value="#1FFFD700"/>
                                                <Setter Property="Foreground" Value="#FFA500"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}"
                                                      Binding="{Binding Name}"
                                                      Width="200"
                                                      MaxWidth="200"/>
                                    <DataGridTextColumn Header="{DynamicResource ExpiryDate}"
                                                      Binding="{Binding ExpiryDate, StringFormat=d}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                    <DataGridTextColumn Header="{DynamicResource Stock}"
                                                      Binding="{Binding StockQuantity}"
                                                      Width="100"
                                                      MaxWidth="100"/>
                                    <DataGridTextColumn Header="{DynamicResource Value}"
                                                      Binding="{Binding PurchasePrice, StringFormat=N2}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                    <DataGridTemplateColumn Width="80"
                                                          MaxWidth="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.EditProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="{DynamicResource EditProduct}">
                                                    <materialDesign:PackIcon Kind="Edit" Width="20" Height="20"/>
                                                </Button>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4"
              Background="{DynamicResource MaterialDesignPaper}"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                      VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="24"
                           Height="24"/>
                <TextBlock Text="{DynamicResource LoadingData}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Margin="0,8,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>