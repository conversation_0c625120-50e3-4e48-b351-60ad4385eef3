using System.Collections.Generic;
using POSSystem.Models;

namespace POSSystem.Services
{
    public interface IAlertService
    {
        void CheckExpiringProducts();
        void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product");
        List<ProductAlert> GetUnreadAlerts();
        List<ProductAlert> GetUnreadAlertsBasic();
        List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1);
        List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1);
        int GetTotalAlertsCount();
        int GetUnreadAlertsCount();
        void MarkAlertAsRead(int alertId);
        void MarkAllAlertsAsRead();
        void ShowError(string message);
        void ClearAlertCache();
    }
} 