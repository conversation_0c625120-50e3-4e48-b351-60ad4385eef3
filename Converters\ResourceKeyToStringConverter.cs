using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class ResourceKeyToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string resourceKey)
            {
                var localizedString = Application.Current.TryFindResource(resourceKey);
                return localizedString?.ToString() ?? resourceKey;
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 