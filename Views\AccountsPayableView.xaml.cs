using POSSystem.Models;
using POSSystem.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Shapes;

namespace POSSystem.Views
{
    public partial class AccountsPayableView : UserControl
    {
        private AccountsPayableViewModel ViewModel => (AccountsPayableViewModel)DataContext;

        public AccountsPayableView()
        {
            InitializeComponent();
            DataContext = new AccountsPayableViewModel();
        }

        private void StatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is AccountsPayableViewModel vm && sender is ComboBox cb)
            {
                vm.SelectedStatus = (cb.SelectedItem as ComboBoxItem)?.Content.ToString();
            }
        }

        private void SupplierFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is AccountsPayableViewModel vm && sender is ComboBox cb)
            {
                vm.SelectedSupplier = cb.SelectedItem as Supplier;
            }
        }

        private void StartDate_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is AccountsPayableViewModel vm && sender is DatePicker dp)
            {
                vm.StartDate = dp.SelectedDate;
            }
        }

        private void EndDate_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is AccountsPayableViewModel vm && sender is DatePicker dp)
            {
                vm.EndDate = dp.SelectedDate;
            }
        }

        private void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is PurchaseOrder order)
            {
                // TODO: Implement view details functionality
                MessageBox.Show($"Order Details:\nOrder #: {order.OrderNumber}\nSupplier: {order.Supplier?.Name}\nTotal Items: {order.Items.Count}", 
                    "Order Details", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ProcessPayment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is PurchaseOrder order)
            {
                // Clear previous values
                cmbPaymentMethod.SelectedIndex = -1;
                txtReferenceNumber.Clear();
                txtNotes.Clear();
                
                paymentDetailsPopup.DataContext = order;
                paymentDetailsPopup.IsOpen = true;
            }
        }

        private void CancelPayment_Click(object sender, RoutedEventArgs e)
        {
            CloseAndResetPopup();
        }

        private void ConfirmPayment_Click(object sender, RoutedEventArgs e)
        {
            if (paymentDetailsPopup.DataContext is PurchaseOrder order)
            {
                var paymentMethod = (cmbPaymentMethod.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (string.IsNullOrEmpty(paymentMethod))
                {
                    MessageBox.Show("Please select a payment method.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (ViewModel.ProcessPayment(order, paymentMethod, txtReferenceNumber.Text, txtNotes.Text))
                {
                    CloseAndResetPopup();
                }
            }
        }

        private void ClosePopupOnOutsideClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.OriginalSource is Rectangle)
            {
                CloseAndResetPopup();
            }
        }

        private void CloseAndResetPopup()
        {
            paymentDetailsPopup.IsOpen = false;
            paymentDetailsPopup.DataContext = null;
            cmbPaymentMethod.SelectedIndex = -1;
            txtReferenceNumber.Clear();
            txtNotes.Clear();
        }
    }
} 