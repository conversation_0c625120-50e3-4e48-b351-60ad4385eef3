<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:POSSystem.Converters"
                    xmlns:system="clr-namespace:System">
    
    <!-- System Converters -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    
    <!-- Custom Converters -->
    <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    <converters:CustomerFullNameConverter x:Key="CustomerFullNameConverter"/>
    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    <converters:CategoryIdToNameConverter x:Key="CategoryIdToNameConverter"/>
    <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
    <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
    <converters:PrimaryBarcodeConverter x:Key="PrimaryBarcodeConverter"/>
    <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
    <converters:BooleanToStatusConverter x:Key="BooleanToStatusConverter"/>
    <converters:CurrencyConverter x:Key="CurrencyConverter"/>
    <converters:DateRangeToResourceConverter x:Key="DateRangeToResourceConverter"/>
    <converters:DrawerStatusConverter x:Key="DrawerStatusConverter"/>
    <converters:TransactionTypeConverter x:Key="TransactionTypeConverter"/>
    <converters:SaleStatusConverter x:Key="SaleStatusConverter"/>
    <converters:InitialsConverter x:Key="InitialsConverter"/>
    <converters:ZeroToVisibilityConverter x:Key="ZeroToVisibilityConverter"/>
    <converters:ResourceKeyConverter x:Key="ResourceKeyConverter"/>
    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    <converters:BaseThemeConverter x:Key="BaseThemeConverter"/>
    <converters:DynamicResourceConverter x:Key="DynamicResourceConverter"/>
    <converters:ResourceKeyToStringConverter x:Key="ResourceKeyToStringConverter"/>

    <!-- Quantity and Stock Display Converters -->
    <converters:QuantityDisplayConverter x:Key="QuantityDisplayConverter"/>
    <converters:QuantityWithUnitConverter x:Key="QuantityWithUnitConverter"/>
    <converters:StockDisplayConverter x:Key="StockDisplayConverter"/>
    <converters:StockQuantityConverter x:Key="StockQuantityConverter"/>
    <converters:UnitOfMeasureNameConverter x:Key="UnitOfMeasureNameConverter"/>

    <!-- Excel Export Related Translations -->
    <system:String x:Key="Period">Period</system:String>
    <system:String x:Key="To">to</system:String>
    <system:String x:Key="ReportExportedSuccessfully">Report exported successfully!</system:String>
    <system:String x:Key="ErrorExportingToExcel">Error exporting to Excel</system:String>
    
    <!-- Column Headers -->
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="TransactionNumber">Transaction Number</system:String>
    <system:String x:Key="Type">Type</system:String>
    <system:String x:Key="Name">Name</system:String>
    <system:String x:Key="ItemCount">Item Count</system:String>
    <system:String x:Key="Amount">Amount</system:String>
    <system:String x:Key="Status">Status</system:String>
    <system:String x:Key="ProductName">Product Name</system:String>
    <system:String x:Key="Category">Category</system:String>
    <system:String x:Key="QuantitySold">Quantity Sold</system:String>
    <system:String x:Key="Revenue">Revenue</system:String>
    <system:String x:Key="Customer">Customer</system:String>
    <system:String x:Key="TotalPurchases">Total Purchases</system:String>
    <system:String x:Key="TotalSpent">Total Spent</system:String>
    <system:String x:Key="LastPurchase">Last Purchase</system:String>
    <system:String x:Key="SKU">SKU</system:String>
    <system:String x:Key="StockQuantity">Stock Quantity</system:String>
    <system:String x:Key="MinimumStock">Minimum Stock</system:String>
    <system:String x:Key="Value">Value</system:String>

    <!-- Status Values -->
    <system:String x:Key="StatusActive">Active</system:String>
    <system:String x:Key="StatusInactive">Inactive</system:String>
    <system:String x:Key="StatusPending">Pending</system:String>
    <system:String x:Key="StatusCompleted">Completed</system:String>
    <system:String x:Key="StatusCancelled">Cancelled</system:String>
    <system:String x:Key="StatusInStock">In Stock</system:String>
    <system:String x:Key="StatusLowStock">Low Stock</system:String>
    <system:String x:Key="StatusOutOfStock">Out of Stock</system:String>

    <!-- Message Strings -->
    <system:String x:Key="Success">Success</system:String>
    <system:String x:Key="Error">Error</system:String>
    <system:String x:Key="Warning">Warning</system:String>
    <system:String x:Key="Information">Information</system:String>
    <system:String x:Key="Details">Details</system:String>
    <system:String x:Key="ViewSaleDetails">View Sale Details</system:String>
    <system:String x:Key="SaleDetails">Sale Details</system:String>

    <!-- Report Types -->
    <system:String x:Key="SalesSummary">Sales Summary</system:String>
    <system:String x:Key="PurchaseOrdersSummary">Purchase Orders Summary</system:String>
    <system:String x:Key="InventoryStatus">Inventory Status</system:String>
    <system:String x:Key="TopProducts">Top Products</system:String>
    <system:String x:Key="CustomerActivity">Customer Activity</system:String>
    <system:String x:Key="SupplierActivity">Supplier Activity</system:String>
    
</ResourceDictionary> 