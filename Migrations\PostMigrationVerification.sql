-- Post-Migration Verification
-- Run this after the migration to ensure everything is working correctly

PRINT '=== POST-MIGRATION VERIFICATION ===';
PRINT '';

-- Test 1: Verify IsWeightBased column exists and has correct values
PRINT 'TEST 1: Verifying IsWeightBased column...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
BEGIN
    PRINT '✅ IsWeightBased column exists';
    
    -- Show distribution of weight-based vs unit-based products
    SELECT 
        CASE WHEN IsWeightBased = 1 THEN 'Weight-Based' ELSE 'Unit-Based' END as ProductType,
        COUNT(*) as Count
    FROM Products
    GROUP BY IsWeightBased;
END
ELSE
BEGIN
    PRINT '❌ IsWeightBased column missing';
END

PRINT '';

-- Test 2: Verify SaleItems.Quantity is decimal
PRINT 'TEST 2: Verifying SaleItems.Quantity data type...';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    CASE 
        WHEN DATA_TYPE = 'decimal' AND NUMERIC_PRECISION = 18 AND NUMERIC_SCALE = 3 
        THEN '✅ Correct'
        ELSE '❌ Incorrect'
    END as Status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';

PRINT '';

-- Test 3: Verify indexes were created
PRINT 'TEST 3: Verifying indexes...';
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Products_IsWeightBased')
BEGIN
    PRINT '✅ IX_Products_IsWeightBased index exists';
END
ELSE
BEGIN
    PRINT '❌ IX_Products_IsWeightBased index missing';
END

PRINT '';

-- Test 4: Test inserting a weight-based product
PRINT 'TEST 4: Testing weight-based product insertion...';
BEGIN TRY
    -- Insert a test weight-based product
    INSERT INTO Products (
        Name, SKU, Description, PurchasePrice, SellingPrice, 
        CategoryId, IsWeightBased, StockQuantity, MinimumStock, 
        IsActive, CreatedAt, UpdatedAt, Type
    ) VALUES (
        'TEST Weight Product', 'TEST-WEIGHT-001', 'Test weight-based product',
        2.50, 4.99, 1, 1, 1000, 50, 1, GETDATE(), GETDATE(), 0
    );
    
    PRINT '✅ Successfully inserted weight-based product';
    
    -- Clean up test data
    DELETE FROM Products WHERE SKU = 'TEST-WEIGHT-001';
    PRINT '✅ Test data cleaned up';
    
END TRY
BEGIN CATCH
    PRINT '❌ Failed to insert weight-based product: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- Test 5: Test inserting a decimal quantity sale item
PRINT 'TEST 5: Testing decimal quantity in SaleItems...';
BEGIN TRY
    -- First, we need a valid SaleId and ProductId
    DECLARE @TestSaleId INT = 1;
    DECLARE @TestProductId INT = 1;
    
    -- Check if we have any existing sales and products
    IF EXISTS (SELECT TOP 1 Id FROM Sales)
    BEGIN
        SELECT TOP 1 @TestSaleId = Id FROM Sales;
    END
    
    IF EXISTS (SELECT TOP 1 Id FROM Products)
    BEGIN
        SELECT TOP 1 @TestProductId = Id FROM Products;
    END
    
    -- Insert test sale item with decimal quantity
    INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
    VALUES (@TestSaleId, @TestProductId, 2.5, 4.99, 12.475);
    
    PRINT '✅ Successfully inserted SaleItem with decimal quantity (2.5)';
    
    -- Verify the decimal was stored correctly
    DECLARE @StoredQuantity DECIMAL(18,3);
    SELECT TOP 1 @StoredQuantity = Quantity 
    FROM SaleItems 
    WHERE SaleId = @TestSaleId AND ProductId = @TestProductId 
    ORDER BY Id DESC;
    
    IF @StoredQuantity = 2.5
    BEGIN
        PRINT '✅ Decimal quantity stored correctly: ' + CAST(@StoredQuantity AS VARCHAR(10));
    END
    ELSE
    BEGIN
        PRINT '❌ Decimal quantity not stored correctly. Got: ' + CAST(@StoredQuantity AS VARCHAR(10));
    END
    
    -- Clean up test data
    DELETE FROM SaleItems 
    WHERE SaleId = @TestSaleId AND ProductId = @TestProductId AND Quantity = 2.5;
    PRINT '✅ Test data cleaned up';
    
END TRY
BEGIN CATCH
    PRINT '❌ Failed to test decimal quantities: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- Test 6: Show sample data
PRINT 'TEST 6: Sample data verification...';
PRINT 'Sample Products (showing IsWeightBased status):';
SELECT TOP 5 
    Id, 
    Name, 
    IsWeightBased,
    CASE WHEN IsWeightBased = 1 THEN 'Weight-Based' ELSE 'Unit-Based' END as ProductType,
    StockQuantity
FROM Products
ORDER BY Id;

PRINT '';
PRINT 'Sample SaleItems (showing decimal quantities):';
SELECT TOP 5 
    Id,
    SaleId,
    ProductId,
    Quantity,
    UnitPrice,
    Total
FROM SaleItems
ORDER BY Id DESC;

PRINT '';
PRINT '=== VERIFICATION COMPLETE ===';
PRINT '';
PRINT 'If all tests show ✅, your migration was successful!';
PRINT 'You can now:';
PRINT '1. Build and run your POS application';
PRINT '2. Create weight-based products in the Product Dialog';
PRINT '3. Test decimal quantities in sales';
PRINT '4. Follow the testing checklist for comprehensive validation';
