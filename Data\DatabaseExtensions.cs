using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Data
{
    /// <summary>
    /// Extensions methods for database performance optimization
    /// </summary>
    public static class DatabaseExtensions
    {
        /// <summary>
        /// Optimizes a SQLite connection with performance-enhancing pragmas
        /// </summary>
        public static SqliteConnection Optimize(this SqliteConnection connection)
        {
            // Apply performance optimizations
            POSDbContext.OptimizeSqliteConnection(connection);
            return connection;
        }

        /// <summary>
        /// Creates a new optimized connection from a connection string
        /// </summary>
        public static SqliteConnection CreateOptimizedConnection(string connectionString)
        {
            try
            {
                // Handle null or empty connection string
                if (string.IsNullOrWhiteSpace(connectionString))
                {
                    throw new ArgumentException("Connection string cannot be null or empty", nameof(connectionString));
                }
                
                // Check if the connection string is already formatted properly
                if (!connectionString.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase))
                {
                    // If it's just a path, format it properly
                    connectionString = $"Data Source={connectionString}";
                }
                
                // Set pooling in the connection string for better performance
                var builder = new SqliteConnectionStringBuilder(connectionString);
                builder.Pooling = true;
                
                var connection = new SqliteConnection(builder.ToString());
                return connection.Optimize();
            }
            catch (ArgumentException ex)
            {
                System.Diagnostics.Debug.WriteLine($"Invalid connection string argument: {ex.Message}");
                throw; // Rethrow argument exceptions to indicate a configuration issue
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating optimized connection: {ex.Message}");
                
                // Last resort fallback - try creating a direct connection
                if (!string.IsNullOrWhiteSpace(connectionString))
                {
                    // Strip any existing prefixes to avoid duplication
                    if (connectionString.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase))
                    {
                        connectionString = connectionString.Substring("Data Source=".Length);
                    }
                    
                    try
                    {
                        return new SqliteConnection($"Data Source={connectionString}").Optimize();
                    }
                    catch
                    {
                        // If this also fails, we have a fundamental connection string issue
                        throw new InvalidOperationException($"Unable to create a valid database connection. Please check your database path configuration.");
                    }
                }
                else
                {
                    throw new InvalidOperationException("Cannot create database connection with empty connection string");
                }
            }
        }

        /// <summary>
        /// Extension method to apply pagination to a query
        /// </summary>
        public static IQueryable<T> ApplyPaging<T>(this IQueryable<T> query, int pageNumber, int pageSize)
        {
            return query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);
        }

        /// <summary>
        /// Extension method to execute a batched query for large datasets
        /// </summary>
        public static async Task<List<T>> ToListBatchedAsync<T>(this IQueryable<T> query, int batchSize = 1000)
        {
            List<T> results = new List<T>();
            int skip = 0;
            List<T> batch;

            do
            {
                batch = await query.Skip(skip).Take(batchSize).ToListAsync();
                results.AddRange(batch);
                skip += batchSize;
            } while (batch.Count == batchSize);

            return results;
        }

        /// <summary>
        /// Extension method to create a lightweight projection of a product 
        /// with only the fields needed for display in lists
        /// </summary>
        public static IQueryable<object> AsProductListItems(this IQueryable<POSSystem.Models.Product> query)
        {
            return query.Select(p => new
            {
                p.Id,
                p.Name,
                p.SKU,
                p.SellingPrice,
                p.StockQuantity,
                p.MinimumStock,
                p.IsActive,
                CategoryName = p.Category.Name,
                BarcodePrimary = p.Barcodes.Where(b => b.IsPrimary).Select(b => b.Barcode).FirstOrDefault()
            });
        }
    }
} 