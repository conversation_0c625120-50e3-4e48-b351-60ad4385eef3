using POSSystem.Models;

namespace POSSystem.Models
{
    public class Tax
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal Rate { get; set; }
        public bool IsCompound { get; set; }
        public bool IsActive { get; set; }
        public string AppliesTo { get; set; } // Product/Category/All
        public int? ProductId { get; set; }
        public int? CategoryId { get; set; }
        public virtual Product Product { get; set; }
        public virtual Category Category { get; set; }
    }
} 