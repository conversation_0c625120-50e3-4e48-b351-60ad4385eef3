using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using POSSystem.Services.DataAccess;
using POSSystem.ViewModels.Dashboard.Commands;
using POSSystem.ViewModels.Dashboard.Services;
using POSSystem.ViewModels.Dashboard.State;
using static POSSystem.ViewModels.Dashboard.Commands.DashboardCommandManager;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Improved DashboardViewModel that demonstrates better architecture with separated concerns.
    /// This version uses composition to delegate responsibilities to specialized components.
    /// </summary>
    /// <remarks>
    /// <para>This improved version demonstrates the following architectural improvements:</para>
    /// <list type="bullet">
    /// <item><description>Separation of Concerns: Commands, state, and services are in separate classes</description></item>
    /// <item><description>Improved Testability: Each component can be tested independently</description></item>
    /// <item><description>Better Maintainability: Changes to specific functionality are isolated</description></item>
    /// <item><description>Cleaner Code: The main ViewModel focuses on coordination rather than implementation</description></item>
    /// <item><description>Extensibility: New features can be added without modifying existing code</description></item>
    /// </list>
    /// <para>This approach can be gradually applied to the existing DashboardViewModel without breaking changes.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Usage in a View or Window
    /// var dataService = serviceProvider.GetService&lt;UnifiedDataService&gt;();
    /// var logger = serviceProvider.GetService&lt;ILogger&lt;ImprovedDashboardViewModel&gt;&gt;();
    /// 
    /// var viewModel = new ImprovedDashboardViewModel(dataService, logger);
    /// await viewModel.InitializeAsync();
    /// 
    /// // The ViewModel now has clean separation of concerns
    /// viewModel.Commands.RefreshDashboardCommand.Execute(null);
    /// viewModel.State.SelectedPeriod = TimePeriod.Week;
    /// </code>
    /// </example>
    public class ImprovedDashboardViewModel : INotifyPropertyChanged, IDisposable
    {
        #region Private Fields

        private readonly ILogger<ImprovedDashboardViewModel> _logger;
        private readonly Services.IDashboardDataService _dataService;
        private readonly IDashboardChartService _chartService;
        private bool _isInitialized = false;
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ImprovedDashboardViewModel.
        /// </summary>
        /// <param name="unifiedDataService">The unified data service for data operations</param>
        /// <param name="logger">Optional logger for diagnostic information</param>
        public ImprovedDashboardViewModel(
            UnifiedDataService unifiedDataService,
            ILogger<ImprovedDashboardViewModel> logger = null)
        {
            _logger = logger;

            // Initialize services
            _dataService = new Services.DashboardDataService(unifiedDataService);
            _chartService = new DashboardChartService();

            // Initialize state manager
            State = new DashboardStateManager();
            State.PropertyChanged += OnStatePropertyChanged;

            // Initialize command manager
            Commands = new DashboardCommandManager(_dataService, _chartService, RefreshUI);

            _logger?.LogInformation("ImprovedDashboardViewModel initialized successfully");
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the state manager for dashboard state and properties.
        /// </summary>
        public DashboardStateManager State { get; }

        /// <summary>
        /// Gets the command manager for dashboard commands.
        /// </summary>
        public DashboardCommandManager Commands { get; }

        /// <summary>
        /// Gets whether the ViewModel has been initialized.
        /// </summary>
        public bool IsInitialized => _isInitialized;

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the dashboard asynchronously by loading initial data.
        /// </summary>
        /// <returns>A task representing the asynchronous initialization operation</returns>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
            {
                _logger?.LogWarning("Dashboard already initialized, skipping initialization");
                return;
            }

            try
            {
                _logger?.LogInformation("Initializing dashboard...");
                State.SetLoadingState(true, "Initializing dashboard...");

                // Load initial data
                await LoadDashboardDataAsync();

                _isInitialized = true;
                State.SetLoadingState(false);

                _logger?.LogInformation("Dashboard initialization completed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during dashboard initialization");
                State.SetErrorState($"Failed to initialize dashboard: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Loads all dashboard data asynchronously.
        /// </summary>
        /// <returns>A task representing the asynchronous load operation</returns>
        public async Task LoadDashboardDataAsync()
        {
            try
            {
                _logger?.LogInformation("Loading dashboard data...");
                State.SetLoadingState(true, "Loading dashboard data...");
                State.ClearErrorState();

                // Load all data in parallel for better performance
                var salesTask = _dataService.GetSalesMetricsAsync();
                var profitTask = _dataService.GetProfitMetricsAsync();
                var topProductsTask = _dataService.GetTopProductsAsync();
                var customerTask = _dataService.GetCustomerAnalyticsAsync();

                await Task.WhenAll(salesTask, profitTask, topProductsTask, customerTask);

                // Update state with loaded data
                State.SalesMetrics = await salesTask;
                State.ProfitMetrics = await profitTask;
                State.TopProducts = await topProductsTask;
                State.CustomerAnalytics = await customerTask;

                // Update charts
                await UpdateChartsAsync();

                State.SetLoadingState(false);
                _logger?.LogInformation("Dashboard data loaded successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading dashboard data");
                State.SetErrorState($"Failed to load dashboard data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Refreshes all dashboard data and charts.
        /// </summary>
        /// <returns>A task representing the asynchronous refresh operation</returns>
        public async Task RefreshAsync()
        {
            try
            {
                _logger?.LogInformation("Refreshing dashboard...");
                State.SetRefreshingState(true, "Refreshing dashboard...");

                // Refresh data service
                await _dataService.RefreshAllDataAsync();

                // Reload dashboard data
                await LoadDashboardDataAsync();

                State.SetRefreshingState(false);
                _logger?.LogInformation("Dashboard refresh completed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error refreshing dashboard");
                State.SetErrorState($"Failed to refresh dashboard: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Changes the time period and refreshes data accordingly.
        /// </summary>
        /// <param name="period">The new time period to apply</param>
        /// <returns>A task representing the asynchronous period change operation</returns>
        public async Task ChangePeriodAsync(DashboardTimePeriod period)
        {
            try
            {
                _logger?.LogInformation("Changing dashboard period to: {Period}", period);

                State.SelectedPeriod = period;
                _dataService.SetTimePeriod(period);

                await LoadDashboardDataAsync();

                _logger?.LogInformation("Dashboard period changed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error changing dashboard period");
                State.SetErrorState($"Failed to change period: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Updates all charts with current data.
        /// </summary>
        private async Task UpdateChartsAsync()
        {
            try
            {
                _logger?.LogInformation("Updating dashboard charts...");

                // Create chart data from current metrics
                var salesTrendData = CreateSalesTrendData();
                var profitData = CreateProfitData();
                var productData = CreateProductPerformanceData();
                // Update charts in parallel
                var chartTasks = new[]
                {
                    _chartService.UpdateSalesTrendChartAsync(salesTrendData),
                    _chartService.UpdateProfitChartAsync(profitData),
                    _chartService.UpdateProductChartAsync(productData)
                };

                await Task.WhenAll(chartTasks);

                _logger?.LogInformation("Dashboard charts updated successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating dashboard charts");
                throw;
            }
        }

        /// <summary>
        /// Creates sales trend data from current metrics.
        /// </summary>
        private SalesTrendData CreateSalesTrendData()
        {
            // This would create actual chart data from the sales metrics
            // For now, returning placeholder data
            return new SalesTrendData
            {
                DataPoints = new System.Collections.Generic.List<SalesTrendPoint>(),
                Labels = new string[0],
                Period = State.SelectedPeriod
            };
        }

        /// <summary>
        /// Creates profit data from current metrics.
        /// </summary>
        private ProfitData CreateProfitData()
        {
            // This would create actual chart data from the profit metrics
            return new ProfitData
            {
                DataPoints = new System.Collections.Generic.List<ProfitDataPoint>(),
                Labels = new string[0],
                Period = State.SelectedPeriod
            };
        }

        /// <summary>
        /// Creates product performance data from current metrics.
        /// </summary>
        private ProductPerformanceData CreateProductPerformanceData()
        {
            // This would create actual chart data from the top products
            return new ProductPerformanceData
            {
                DataPoints = new System.Collections.Generic.List<ProductPerformancePoint>(),
                Labels = new string[0],
                MetricType = "Revenue"
            };
        }



        /// <summary>
        /// Refreshes the UI by notifying property changes.
        /// </summary>
        private void RefreshUI()
        {
            // This method is called by commands to refresh the UI
            // In a real implementation, this might trigger specific UI updates
            OnPropertyChanged(nameof(State));
            OnPropertyChanged(nameof(Commands));
        }

        /// <summary>
        /// Handles property changes from the state manager.
        /// </summary>
        private void OnStatePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // Forward state property changes to the UI
            OnPropertyChanged($"State.{e.PropertyName}");
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                State.PropertyChanged -= OnStatePropertyChanged;
                _logger?.LogInformation("ImprovedDashboardViewModel disposed");
                _disposed = true;
            }
        }

        #endregion
    }
}
