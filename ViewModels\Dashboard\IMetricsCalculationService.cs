using System;
using System.Collections.Generic;
using System.Linq;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface for the service responsible for calculating business metrics and growth comparisons.
    /// </summary>
    public interface IMetricsCalculationService
    {
        /// <summary>
        /// Calculates sales metrics with growth comparison
        /// </summary>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of current value and growth percentage</returns>
        (decimal current, decimal growth) CalculateSalesMetrics(
            List<SaleAggregation> currentPeriod, 
            List<SaleAggregation> previousPeriod);
        
        /// <summary>
        /// Calculates profit metrics with growth comparison
        /// </summary>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of profit value, margin percentage, and growth percentage</returns>
        (decimal profit, decimal margin, decimal growth) CalculateProfitMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod);
        
        /// <summary>
        /// Calculates sales volume metrics (items sold) with growth comparison
        /// </summary>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of items sold count (supports decimal quantities for weight-based products) and growth percentage</returns>
        (decimal itemsSold, decimal growth) CalculateItemsSoldMetrics( // ✅ WEIGHT-BASED FIX: Changed return type from int to decimal
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod);
        
        /// <summary>
        /// Calculates order count metrics with growth comparison
        /// </summary>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of order count and growth percentage</returns>
        (int orderCount, decimal growth) CalculateOrderCountMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod);
        
        /// <summary>
        /// Calculates average order value metrics with growth comparison
        /// </summary>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of average order value and growth percentage</returns>
        (decimal avgOrderValue, decimal growth) CalculateAvgOrderValueMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod);
        
        /// <summary>
        /// Gets metrics based on type and calculates their values
        /// </summary>
        /// <param name="metricType">Type of metric (sales, profit, etc.)</param>
        /// <param name="currentPeriod">Current period aggregations</param>
        /// <param name="previousPeriod">Previous period aggregations</param>
        /// <returns>Tuple of value and growth percentage</returns>
        (decimal value, decimal growth) GetMetricByType(
            string metricType,
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod);
    }
} 