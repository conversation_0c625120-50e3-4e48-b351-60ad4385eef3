using Microsoft.Extensions.DependencyInjection;
using POSSystem.Services.Interfaces;
using POSSystem.Services.Repositories;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    /// <summary>
    /// Test class to validate repository setup and functionality
    /// SAFE: This only tests, doesn't modify existing functionality
    /// </summary>
    public static class RepositoryValidationTest
    {
        /// <summary>
        /// Validates that all repositories can be resolved and basic operations work
        /// </summary>
        public static async Task<bool> ValidateRepositorySetupAsync()
        {
            try
            {
                Debug.WriteLine("🔍 Starting Repository Validation Test...");
                
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                
                // Test 1: Verify repositories can be resolved
                Debug.WriteLine("✅ Test 1: Repository Resolution");
                var productRepo = serviceProvider.GetService<IProductRepository>();
                var saleRepo = serviceProvider.GetService<ISaleRepository>();
                var customerRepo = serviceProvider.GetService<ICustomerRepository>();
                var adapter = serviceProvider.GetService<RepositoryServiceAdapter>();

                if (productRepo == null)
                {
                    Debug.WriteLine("❌ ProductRepository not resolved");
                    return false;
                }

                if (saleRepo == null)
                {
                    Debug.WriteLine("❌ SaleRepository not resolved");
                    return false;
                }

                if (customerRepo == null)
                {
                    Debug.WriteLine("❌ CustomerRepository not resolved");
                    return false;
                }

                if (adapter == null)
                {
                    Debug.WriteLine("❌ RepositoryServiceAdapter not resolved");
                    return false;
                }
                
                Debug.WriteLine("✅ All repositories resolved successfully");
                
                // Test 2: Basic repository operations (read-only, safe)
                Debug.WriteLine("✅ Test 2: Basic Repository Operations");
                
                try
                {
                    var productCount = await productRepo.GetTotalCountAsync();
                    Debug.WriteLine($"✅ Product count: {productCount}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"⚠️ Product count failed (expected if no data): {ex.Message}");
                }
                
                try
                {
                    var customerCount = await customerRepo.GetTotalCountAsync();
                    Debug.WriteLine($"✅ Customer count: {customerCount}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"⚠️ Customer count failed (expected if no data): {ex.Message}");
                }
                
                // Test 3: Adapter functionality
                Debug.WriteLine("✅ Test 3: Adapter Functionality");
                
                try
                {
                    var products = await adapter.GetProductsPagedAsync(1, 10);
                    Debug.WriteLine($"✅ Adapter paged products: {products.Count} items");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"⚠️ Adapter test failed: {ex.Message}");
                }
                
                Debug.WriteLine("🎉 Repository validation completed successfully!");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Repository validation failed: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }
        
        /// <summary>
        /// Performance comparison test between repository and DatabaseService
        /// </summary>
        public static async Task<bool> PerformanceComparisonTestAsync()
        {
            try
            {
                Debug.WriteLine("🚀 Starting Performance Comparison Test...");
                
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                var adapter = serviceProvider.GetService<RepositoryServiceAdapter>();
                var dbService = serviceProvider.GetService<IDatabaseService>();

                if (adapter == null || dbService == null)
                {
                    Debug.WriteLine("❌ Required services not available for performance test");
                    return false;
                }
                
                // Test product retrieval performance
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                // Repository approach
                stopwatch.Restart();
                var repoProducts = await adapter.GetProductsPagedAsync(1, 50);
                var repoTime = stopwatch.ElapsedMilliseconds;
                
                // DatabaseService approach
                stopwatch.Restart();
                var dbProducts = dbService.GetAllProducts().Take(50).ToList();
                var dbTime = stopwatch.ElapsedMilliseconds;
                
                Debug.WriteLine($"📊 Performance Results:");
                Debug.WriteLine($"   Repository: {repoTime}ms ({repoProducts.Count} products)");
                Debug.WriteLine($"   DatabaseService: {dbTime}ms ({dbProducts.Count} products)");
                
                if (repoTime < dbTime)
                {
                    var improvement = ((double)(dbTime - repoTime) / dbTime) * 100;
                    Debug.WriteLine($"🎯 Repository is {improvement:F1}% faster!");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Performance test failed: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Memory usage comparison test
        /// </summary>
        public static async Task<bool> MemoryUsageTestAsync()
        {
            try
            {
                Debug.WriteLine("💾 Starting Memory Usage Test...");
                
                var serviceProvider = ServiceConfiguration.CreateServiceProvider();
                var adapter = serviceProvider.GetService<RepositoryServiceAdapter>();
                var dbService = serviceProvider.GetService<IDatabaseService>();

                if (adapter == null || dbService == null)
                {
                    Debug.WriteLine("❌ Required services not available for memory test");
                    return false;
                }
                
                // Force garbage collection before test
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var initialMemory = GC.GetTotalMemory(false);
                
                // Repository approach (paged)
                var repoProducts = await adapter.GetProductsPagedAsync(1, 100);
                var repoMemory = GC.GetTotalMemory(false) - initialMemory;
                
                // Clear and reset
                repoProducts = null;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var midMemory = GC.GetTotalMemory(false);
                
                // DatabaseService approach (loads all)
                var dbProducts = dbService.GetAllProducts();
                var dbMemory = GC.GetTotalMemory(false) - midMemory;
                
                Debug.WriteLine($"💾 Memory Usage Results:");
                Debug.WriteLine($"   Repository (paged): {repoMemory / 1024}KB");
                Debug.WriteLine($"   DatabaseService (all): {dbMemory / 1024}KB");
                
                if (repoMemory < dbMemory)
                {
                    var savings = ((double)(dbMemory - repoMemory) / dbMemory) * 100;
                    Debug.WriteLine($"🎯 Repository uses {savings:F1}% less memory!");
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Memory test failed: {ex.Message}");
                return false;
            }
        }
    }
}
