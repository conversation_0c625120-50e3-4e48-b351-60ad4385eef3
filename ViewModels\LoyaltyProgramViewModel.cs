using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Commands;

namespace POSSystem.ViewModels
{
    public class LoyaltyProgramViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _dbService;
        private LoyaltyProgram _selectedProgram;
        private LoyaltyTier _selectedTier;
        private LoyaltyProgram _editingProgram;
        private LoyaltyTier _editingTier;
        private bool _isEditingProgram;
        private bool _isEditingTier;

        public ObservableCollection<LoyaltyProgram> Programs { get; private set; }
        public ObservableCollection<LoyaltyTier> Tiers { get; private set; }

        public LoyaltyProgram SelectedProgram
        {
            get => _selectedProgram;
            set
            {
                _selectedProgram = value;
                OnPropertyChanged();
                LoadTiers();
            }
        }

        public LoyaltyTier SelectedTier
        {
            get => _selectedTier;
            set
            {
                _selectedTier = value;
                OnPropertyChanged();
            }
        }

        public LoyaltyProgram EditingProgram
        {
            get => _editingProgram;
            set
            {
                _editingProgram = value;
                OnPropertyChanged();
            }
        }

        public LoyaltyTier EditingTier
        {
            get => _editingTier;
            set
            {
                _editingTier = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditingProgram
        {
            get => _isEditingProgram;
            set
            {
                _isEditingProgram = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditingTier
        {
            get => _isEditingTier;
            set
            {
                _isEditingTier = value;
                OnPropertyChanged();
            }
        }

        public ICommand AddProgramCommand { get; }
        public ICommand SaveProgramCommand { get; }
        public ICommand CancelEditProgramCommand { get; }
        public ICommand AddTierCommand { get; }
        public ICommand SaveTierCommand { get; }
        public ICommand CancelEditTierCommand { get; }
        public ICommand DeactivateProgramCommand { get; }
        public ICommand RemoveProgramCommand { get; }
        public ICommand EditProgramCommand { get; }

        public LoyaltyProgramViewModel(DatabaseService dbService)
        {
            _dbService = dbService;
            Programs = new ObservableCollection<LoyaltyProgram>();
            Tiers = new ObservableCollection<LoyaltyTier>();

            AddProgramCommand = new RelayCommand(_ => StartAddProgram());
            SaveProgramCommand = new RelayCommand(_ => SaveProgram());
            CancelEditProgramCommand = new RelayCommand(_ => CancelEditProgram());
            AddTierCommand = new RelayCommand(_ => StartAddTier(), _ => SelectedProgram != null);
            SaveTierCommand = new RelayCommand(_ => SaveTier());
            CancelEditTierCommand = new RelayCommand(_ => CancelEditTier());
            DeactivateProgramCommand = new RelayCommand(_ => DeactivateProgram(), _ => SelectedProgram != null);
            RemoveProgramCommand = new RelayCommand(_ => RemoveProgram(), _ => SelectedProgram != null);
            EditProgramCommand = new RelayCommand(_ => StartEditProgram(), _ => SelectedProgram != null);

            LoadPrograms();
        }

        private void LoadPrograms()
        {
            var programs = _dbService.GetAllLoyaltyPrograms();
            Programs.Clear();
            foreach (var program in programs)
            {
                Programs.Add(program);
            }
        }

        private void LoadTiers()
        {
            Tiers.Clear();
            if (SelectedProgram != null)
            {
                foreach (var tier in SelectedProgram.Tiers)
                {
                    Tiers.Add(tier);
                }
            }
        }

        private void StartAddProgram()
        {
            EditingProgram = new LoyaltyProgram
            {
                Name = "New Program",
                Description = "New loyalty program",
                PointsPerDollar = 1.0m,
                MonetaryValuePerPoint = 0.01m,
                ExpiryMonths = 12,
                MinimumPointsRedemption = 100,
                IsActive = true,
                CreatedAt = DateTime.Now
            };
            IsEditingProgram = true;
        }

        private void SaveProgram()
        {
            if (EditingProgram == null) return;

            _dbService.SaveLoyaltyProgram(EditingProgram);
            LoadPrograms();
            IsEditingProgram = false;
            EditingProgram = null;
        }

        private void CancelEditProgram()
        {
            IsEditingProgram = false;
            EditingProgram = null;
        }

        private void StartAddTier()
        {
            if (SelectedProgram == null) return;

            EditingTier = new LoyaltyTier
            {
                LoyaltyProgramId = SelectedProgram.Id,
                Name = "New Tier",
                MinimumPoints = 0,
                PointsMultiplier = 1.0m
            };
            IsEditingTier = true;
        }

        private void SaveTier()
        {
            if (EditingTier == null || SelectedProgram == null) return;

            _dbService.SaveLoyaltyTier(EditingTier);
            LoadTiers();
            IsEditingTier = false;
            EditingTier = null;
        }

        private void CancelEditTier()
        {
            IsEditingTier = false;
            EditingTier = null;
        }

        private void DeactivateProgram()
        {
            if (SelectedProgram == null) return;

            SelectedProgram.IsActive = false;
            _dbService.SaveLoyaltyProgram(SelectedProgram);
            LoadPrograms();
        }

        private void RemoveProgram()
        {
            if (SelectedProgram == null) return;

            var result = System.Windows.MessageBox.Show(
                "Are you sure you want to remove this loyalty program? This action cannot be undone.",
                "Confirm Removal",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                _dbService.DeleteLoyaltyProgram(SelectedProgram.Id);
                LoadPrograms();
                SelectedProgram = null;
            }
        }

        private void StartEditProgram()
        {
            if (SelectedProgram == null) return;

            EditingProgram = new LoyaltyProgram
            {
                Id = SelectedProgram.Id,
                Name = SelectedProgram.Name,
                Description = SelectedProgram.Description,
                PointsPerDollar = SelectedProgram.PointsPerDollar,
                MonetaryValuePerPoint = SelectedProgram.MonetaryValuePerPoint,
                ExpiryMonths = SelectedProgram.ExpiryMonths,
                MinimumPointsRedemption = SelectedProgram.MinimumPointsRedemption,
                IsActive = SelectedProgram.IsActive,
                CreatedAt = SelectedProgram.CreatedAt
            };
            IsEditingProgram = true;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }
    }
} 