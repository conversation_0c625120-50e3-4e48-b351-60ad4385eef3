using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Xunit;
using FluentAssertions;
using POSSystem.Models;
using POSSystem.Views.Dialogs;

namespace POSSystem.Tests.UI
{
    /// <summary>
    /// Tests for the redesigned WeightBasedProductDialog with tab-based interface
    /// </summary>
    public class WeightBasedProductDialogTests
    {
        private Product CreateTestProduct()
        {
            return new Product
            {
                Id = 1,
                Name = "Test Weight Product",
                SKU = "WEIGHT-001",
                SellingPrice = 5.99m,
                IsWeightBased = true,
                StockQuantity = 10.0m,
                IsActive = true
            };
        }

        [Fact]
        public void Constructor_ShouldInitializeWithValidProduct()
        {
            // Arrange
            var product = CreateTestProduct();

            // Act
            var dialog = new WeightBasedProductDialog(product);

            // Assert
            dialog.Product.Should().Be(product);
            dialog.SelectedAmount.Should().Be(0);
            dialog.SelectedWeight.Should().Be(0);
            dialog.IsConfirmed.Should().BeFalse();
        }

        [Fact]
        public void Constructor_ShouldThrowException_WhenProductIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new WeightBasedProductDialog(null));
        }

        [Fact]
        public void Dialog_ShouldBeCompactInSize()
        {
            // Arrange
            var product = CreateTestProduct();
            var dialog = new WeightBasedProductDialog(product);

            // Act & Assert
            dialog.Width.Should().Be(520); // Reduced from 580
            dialog.Height.Should().Be(620); // Reduced from 754
        }

        [Fact]
        public void Dialog_ShouldHaveModernStyling()
        {
            // Arrange
            var product = CreateTestProduct();
            var dialog = new WeightBasedProductDialog(product);

            // Act & Assert
            dialog.WindowStyle.Should().Be(WindowStyle.None);
            dialog.AllowsTransparency.Should().BeTrue();
            dialog.Background.Should().NotBeNull();
        }

        [Fact]
        public void Dialog_ShouldInitializeWithCorrectTitle()
        {
            // Arrange
            var product = CreateTestProduct();
            var dialog = new WeightBasedProductDialog(product);

            // Act & Assert
            dialog.Title.Should().Be("Weight-Based Product Selection");
        }

        [Fact]
        public void Dialog_ShouldHaveCorrectResizeMode()
        {
            // Arrange
            var product = CreateTestProduct();
            var dialog = new WeightBasedProductDialog(product);

            // Act & Assert
            dialog.ResizeMode.Should().Be(ResizeMode.NoResize);
        }

        [Fact]
        public void Dialog_ShouldHaveCorrectStartupLocation()
        {
            // Arrange
            var product = CreateTestProduct();
            var dialog = new WeightBasedProductDialog(product);

            // Act & Assert
            dialog.WindowStartupLocation.Should().Be(WindowStartupLocation.CenterOwner);
        }

        [Fact]
        public void Dialog_ShouldInitializePropertiesCorrectly()
        {
            // Arrange
            var product = CreateTestProduct();

            // Act
            var dialog = new WeightBasedProductDialog(product);

            // Assert
            dialog.Product.Should().NotBeNull();
            dialog.Product.Name.Should().Be("Test Weight Product");
            dialog.Product.SellingPrice.Should().Be(5.99m);
            dialog.Product.IsWeightBased.Should().BeTrue();
            dialog.SelectedAmount.Should().Be(0);
            dialog.SelectedWeight.Should().Be(0);
            dialog.IsConfirmed.Should().BeFalse();
        }

        [Theory]
        [InlineData("Test Product 1", 1.99)]
        [InlineData("Test Product 2", 10.50)]
        [InlineData("Test Product 3", 99.99)]
        public void Dialog_ShouldHandleDifferentProducts(string productName, decimal price)
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = productName,
                SKU = "TEST-001",
                SellingPrice = price,
                IsWeightBased = true,
                StockQuantity = 10.0m,
                IsActive = true
            };

            // Act
            var dialog = new WeightBasedProductDialog(product);

            // Assert
            dialog.Product.Name.Should().Be(productName);
            dialog.Product.SellingPrice.Should().Be(price);
            dialog.SelectedAmount.Should().Be(0);
            dialog.SelectedWeight.Should().Be(0);
            dialog.IsConfirmed.Should().BeFalse();
        }
    }
}
