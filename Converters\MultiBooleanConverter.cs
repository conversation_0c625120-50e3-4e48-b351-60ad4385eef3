using System;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class MultiBooleanConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // If any value is null or not a boolean, return false
            if (values == null || values.Any(v => v == null || !(v is bool)))
                return false;
                
            // Check if all values are true
            return values.All(v => (bool)v);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 