using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Data;
using Xunit;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Linq;

namespace POSSystem.Tests.Integration
{
    /// <summary>
    /// Integration tests for Product and Service functionality
    /// </summary>
    public class ProductServiceIntegrationTests : IDisposable
    {
        private readonly POSDbContext _context;
        private readonly ProductsViewModel _viewModel;

        public ProductServiceIntegrationTests()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: System.Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _viewModel = new ProductsViewModel(_context);
        }

        [Fact]
        public void Product_ShouldHaveDefaultProductType()
        {
            // Arrange & Act
            var product = new Product();

            // Assert
            product.Type.Should().Be(ProductType.Product);
        }

        [Fact]
        public void Product_ShouldAllowServiceType()
        {
            // Arrange
            var service = new Product
            {
                Name = "Consultation Service",
                Type = ProductType.Service,
                SellingPrice = 100.00m,
                PurchasePrice = 0.00m
            };

            // Act & Assert
            service.Type.Should().Be(ProductType.Service);
            service.Name.Should().Be("Consultation Service");
        }

        [Fact]
        public async Task Database_ShouldPersistProductType()
        {
            // Arrange
            var product = new Product
            {
                Name = "Test Product",
                Type = ProductType.Product,
                SellingPrice = 50.00m,
                PurchasePrice = 25.00m,
                StockQuantity = 100
            };

            var service = new Product
            {
                Name = "Test Service",
                Type = ProductType.Service,
                SellingPrice = 150.00m,
                PurchasePrice = 0.00m,
                StockQuantity = 0 // Services don't track stock
            };

            // Act
            _context.Products.Add(product);
            _context.Products.Add(service);
            await _context.SaveChangesAsync();

            // Assert
            var savedProduct = await _context.Products.FirstOrDefaultAsync(p => p.Name == "Test Product");
            var savedService = await _context.Products.FirstOrDefaultAsync(p => p.Name == "Test Service");

            savedProduct.Should().NotBeNull();
            savedProduct.Type.Should().Be(ProductType.Product);

            savedService.Should().NotBeNull();
            savedService.Type.Should().Be(ProductType.Service);
        }

        [Fact]
        public void CartItem_ShouldIdentifyServices()
        {
            // Arrange
            var service = new Product
            {
                Name = "Consultation",
                Type = ProductType.Service,
                SellingPrice = 100.00m
            };

            var cartItem = new CartItem
            {
                Product = service,
                Quantity = 2,
                UnitPrice = 100.00m
            };

            // Act & Assert
            cartItem.IsService.Should().BeTrue();
            cartItem.ItemTypeDisplay.Should().Be("Service");
        }

        [Fact]
        public void CartItem_ShouldIdentifyProducts()
        {
            // Arrange
            var product = new Product
            {
                Name = "Widget",
                Type = ProductType.Product,
                SellingPrice = 25.00m
            };

            var cartItem = new CartItem
            {
                Product = product,
                Quantity = 5,
                UnitPrice = 25.00m
            };

            // Act & Assert
            cartItem.IsService.Should().BeFalse();
            cartItem.ItemTypeDisplay.Should().Be("Product");
        }

        [Fact]
        public void ProductsViewModel_ShouldFilterServiceUnits()
        {
            // Arrange
            var allUnits = new[]
            {
                new UnitOfMeasure { Id = 1, Name = "Piece", Type = "Unit" },
                new UnitOfMeasure { Id = 2, Name = "Kilogram", Type = "Weight" },
                new UnitOfMeasure { Id = 3, Name = "Hour", Type = "Time" },
                new UnitOfMeasure { Id = 4, Name = "Liter", Type = "Volume" },
                new UnitOfMeasure { Id = 5, Name = "Session", Type = "Unit" }
            };

            _viewModel.UnitsOfMeasure = new System.Collections.ObjectModel.ObservableCollection<UnitOfMeasure>(allUnits);

            // Act
            var serviceUnits = _viewModel.GetServiceUnits();

            // Assert
            serviceUnits.Should().HaveCount(3); // Piece, Hour, Session
            serviceUnits.Should().Contain(u => u.Name == "Piece");
            serviceUnits.Should().Contain(u => u.Name == "Hour");
            serviceUnits.Should().Contain(u => u.Name == "Session");
            serviceUnits.Should().NotContain(u => u.Name == "Kilogram");
            serviceUnits.Should().NotContain(u => u.Name == "Liter");
        }

        [Fact]
        public void ProductsViewModel_ShouldGetDefaultServiceUnit()
        {
            // Arrange
            var allUnits = new[]
            {
                new UnitOfMeasure { Id = 1, Name = "Piece", Type = "Unit" },
                new UnitOfMeasure { Id = 2, Name = "Hour", Type = "Time" },
                new UnitOfMeasure { Id = 3, Name = "Kilogram", Type = "Weight" }
            };

            _viewModel.UnitsOfMeasure = new System.Collections.ObjectModel.ObservableCollection<UnitOfMeasure>(allUnits);

            // Act
            var defaultUnit = _viewModel.GetDefaultServiceUnit();

            // Assert
            defaultUnit.Should().NotBeNull();
            defaultUnit.Name.Should().Be("Hour"); // Should prefer "Hour" over "Piece"
        }

        [Theory]
        [InlineData(ProductType.Product, true)]
        [InlineData(ProductType.Service, false)]
        public void ProductsViewModel_ShouldIdentifyServiceUnits(ProductType productType, bool shouldTrackStock)
        {
            // Arrange
            var item = new Product
            {
                Name = "Test Item",
                Type = productType,
                SellingPrice = 100.00m
            };

            // Act
            bool isService = item.Type == ProductType.Service;
            bool shouldSkipStockTracking = isService;

            // Assert
            shouldSkipStockTracking.Should().Be(!shouldTrackStock);
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
