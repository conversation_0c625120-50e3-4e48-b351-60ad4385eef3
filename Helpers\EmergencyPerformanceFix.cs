using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.Helpers
{
    /// <summary>
    /// 🚨 EMERGENCY PERFORMANCE FIX: Critical measures to prevent UI thread blocking over 1000ms
    /// This class implements emergency measures to detect and prevent critical UI thread blocking
    /// </summary>
    public static class EmergencyPerformanceFix
    {
        private static readonly object _lockObject = new();
        private static bool _emergencyModeActive = false;
        private static Timer _emergencyTimer;
        private static DateTime _lastUIThreadCheck = DateTime.UtcNow;
        private static int _criticalBlockCount = 0;

        /// <summary>
        /// Activate emergency performance monitoring and fixes
        /// </summary>
        public static void ActivateEmergencyMode()
        {
            lock (_lockObject)
            {
                if (_emergencyModeActive) return;
                
                _emergencyModeActive = true;
                Debug.WriteLine("🚨 EMERGENCY PERFORMANCE MODE ACTIVATED");
                
                // Start aggressive UI thread monitoring
                StartAggressiveUIMonitoring();
                
                // Force garbage collection to free memory
                ForceGarbageCollection();
                
                // Reduce UI update frequency
                ReduceUIUpdateFrequency();
            }
        }

        /// <summary>
        /// Deactivate emergency mode when performance is stable
        /// </summary>
        public static void DeactivateEmergencyMode()
        {
            lock (_lockObject)
            {
                if (!_emergencyModeActive) return;
                
                _emergencyModeActive = false;
                _emergencyTimer?.Dispose();
                _emergencyTimer = null;
                
                Debug.WriteLine("✅ Emergency performance mode deactivated");
            }
        }

        /// <summary>
        /// Check if emergency mode should be activated based on blocking duration
        /// </summary>
        public static void CheckForEmergencyActivation(double blockingDurationMs)
        {
            if (blockingDurationMs > 1000)
            {
                Interlocked.Increment(ref _criticalBlockCount);
                
                if (_criticalBlockCount >= 3 && !_emergencyModeActive)
                {
                    ActivateEmergencyMode();
                }
            }
            else if (blockingDurationMs < 100)
            {
                // Reset counter if we have good performance
                if (_criticalBlockCount > 0)
                {
                    Interlocked.Decrement(ref _criticalBlockCount);
                }
                
                // Deactivate emergency mode if performance is stable
                if (_emergencyModeActive && _criticalBlockCount == 0)
                {
                    DeactivateEmergencyMode();
                }
            }
        }

        /// <summary>
        /// Execute operation with emergency timeout protection
        /// </summary>
        public static async Task<T> ExecuteWithEmergencyTimeout<T>(
            Func<Task<T>> operation,
            int timeoutMs = 5000,
            string operationName = "Operation")
        {
            using var cts = new CancellationTokenSource(timeoutMs);
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = await operation().ConfigureAwait(false);
                stopwatch.Stop();
                
                if (stopwatch.ElapsedMilliseconds > 1000)
                {
                    Debug.WriteLine($"🟠 SLOW OPERATION: {operationName} took {stopwatch.ElapsedMilliseconds}ms");
                }
                
                return result;
            }
            catch (OperationCanceledException) when (cts.Token.IsCancellationRequested)
            {
                stopwatch.Stop();
                Debug.WriteLine($"🔴 EMERGENCY TIMEOUT: {operationName} cancelled after {timeoutMs}ms");
                throw new TimeoutException($"Operation {operationName} timed out after {timeoutMs}ms");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"❌ EMERGENCY ERROR: {operationName} failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Force immediate UI thread yield to prevent blocking
        /// </summary>
        public static async Task ForceUIYield()
        {
            if (Application.Current?.Dispatcher != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() => { }, DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// Execute operation with forced UI yields to prevent blocking
        /// </summary>
        public static async Task ExecuteWithUIYields<T>(
            Func<Task<T>> operation,
            Action<T> onComplete,
            int yieldIntervalMs = 100)
        {
            var yieldTimer = new Timer(async _ => await ForceUIYield(), null, yieldIntervalMs, yieldIntervalMs);
            
            try
            {
                var result = await operation();
                onComplete(result);
            }
            finally
            {
                yieldTimer?.Dispose();
            }
        }

        private static void StartAggressiveUIMonitoring()
        {
            _emergencyTimer = new Timer(CheckUIResponsiveness, null, 50, 50); // Check every 50ms
        }

        private static void CheckUIResponsiveness(object state)
        {
            var now = DateTime.UtcNow;
            var timeSinceLastCheck = (now - _lastUIThreadCheck).TotalMilliseconds;
            
            if (timeSinceLastCheck > 200) // More than 200ms since last check
            {
                Debug.WriteLine($"🚨 EMERGENCY: UI thread unresponsive for {timeSinceLastCheck:F0}ms");
                
                // Try to force UI thread to respond
                try
                {
                    Application.Current?.Dispatcher?.BeginInvoke(new Action(() =>
                    {
                        _lastUIThreadCheck = DateTime.UtcNow;
                    }), DispatcherPriority.Send);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"🚨 CRITICAL: Cannot access UI thread: {ex.Message}");
                }
            }
            else
            {
                _lastUIThreadCheck = now;
            }
        }

        private static void ForceGarbageCollection()
        {
            try
            {
                Debug.WriteLine("🧹 Emergency garbage collection...");
                GC.Collect(2, GCCollectionMode.Forced, true);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Forced, true);
                Debug.WriteLine("✅ Emergency garbage collection completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Emergency GC failed: {ex.Message}");
            }
        }

        private static void ReduceUIUpdateFrequency()
        {
            try
            {
                if (Application.Current?.Dispatcher != null)
                {
                    // Reduce dispatcher priority for non-critical operations
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        Debug.WriteLine("⚡ Reducing UI update frequency for emergency mode");
                    }), DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to reduce UI frequency: {ex.Message}");
            }
        }

        /// <summary>
        /// Get current emergency mode status
        /// </summary>
        public static bool IsEmergencyModeActive => _emergencyModeActive;

        /// <summary>
        /// Get current critical block count
        /// </summary>
        public static int CriticalBlockCount => _criticalBlockCount;

        /// <summary>
        /// Reset emergency mode statistics
        /// </summary>
        public static void ResetStatistics()
        {
            lock (_lockObject)
            {
                _criticalBlockCount = 0;
                _lastUIThreadCheck = DateTime.UtcNow;
                Debug.WriteLine("📊 Emergency mode statistics reset");
            }
        }

        /// <summary>
        /// Log current emergency mode status
        /// </summary>
        public static void LogStatus()
        {
            Debug.WriteLine($"🚨 Emergency Mode Status:");
            Debug.WriteLine($"   Active: {_emergencyModeActive}");
            Debug.WriteLine($"   Critical Blocks: {_criticalBlockCount}");
            Debug.WriteLine($"   Last UI Check: {(DateTime.UtcNow - _lastUIThreadCheck).TotalMilliseconds:F0}ms ago");
        }
    }
}
