<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.LoyaltyCardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="480" d:DesignWidth="452"
             Background="Transparent">
    
    <materialDesign:Card Background="#FFFFFF"
                         UniformCornerRadius="16"
                         materialDesign:ElevationAssist.Elevation="Dp6"
                         MaxWidth="420"
                         Margin="16">
        <Grid>
            <!-- Header Section with Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{StaticResource PrimaryGradientBrush}" 
                    CornerRadius="16,16,0,0"
                    Padding="24,18">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CardAccountDetails" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="Loyalty Card" 
                             FontSize="22"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section -->
            <StackPanel Grid.Row="1" Margin="24,20" Background="Transparent">
                <!-- Customer Info -->
                <TextBlock x:Name="txtCustomerName" 
                         Text="Customer Name"
                         FontWeight="Medium"
                         FontSize="18"
                         Margin="0,0,0,4"/>
                         
                <TextBlock x:Name="txtLoyaltyCode" 
                         Text="Loyalty Code: 00000000"
                         FontSize="14"
                         Opacity="0.8"
                         Margin="0,0,0,16"/>
                
                <!-- Tabs for choosing side -->
                <Grid Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Front Card Tab -->
                    <Button x:Name="btnShowFront"
                          Grid.Column="0"
                          Content="Front Side"
                          Style="{StaticResource AppPrimaryButtonStyle}"
                          Margin="0,0,4,0"
                          Height="40"
                          Click="BtnShowFront_Click"/>
                    
                    <!-- Back Card Tab -->
                    <Button x:Name="btnShowBack"
                          Grid.Column="1"
                          Content="Back Side"
                          Style="{StaticResource AppSecondaryButtonStyle}"
                          Margin="4,0,0,0"
                          Height="40"
                          Click="BtnShowBack_Click"/>
                </Grid>
                
                <!-- Card Preview Container -->
                <Grid x:Name="cardPreviewContainer" Height="220" Margin="0,0,0,24">
                    <!-- Front of card with gradient background -->
                    <Border x:Name="frontCardPreview" 
                            Style="{StaticResource LoyaltyCardStyle}"
                            Height="200"
                            Width="316"
                            HorizontalAlignment="Center"
                            Visibility="Visible">
                        <Grid>
                            <!-- Beige curved section on left -->
                            <Border Background="#E1D0B3" 
                                    HorizontalAlignment="Left"
                                    Width="100"
                                    CornerRadius="8,0,70,8">
                                <StackPanel x:Name="FrontLogoContainer"
                                           VerticalAlignment="Center" 
                                           HorizontalAlignment="Center">
                                    <TextBlock Text="LOGO" 
                                             FontSize="18" 
                                             FontWeight="Bold" 
                                             Foreground="#192656"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Content section on right -->
                            <StackPanel Margin="110,16,16,16">
                                <!-- Customer name -->
                                <TextBlock x:Name="cardFrontName"
                                         FontSize="18" 
                                         FontWeight="Bold"
                                         Foreground="White"
                                         Text="Customer Name"/>
                                
                                <TextBlock Text="Loyalty Member" 
                                         Foreground="#E1D0B3" 
                                         FontSize="12"
                                         Opacity="0.9"
                                         Margin="0,0,0,16"/>
                                
                                <!-- Loyalty code with icon -->
                                <StackPanel Orientation="Horizontal" 
                                           Margin="0,4,0,0">
                                    <materialDesign:PackIcon Kind="Barcode" 
                                                            Width="16" 
                                                            Height="16"
                                                            Foreground="#E1D0B3"/>
                                    <TextBlock x:Name="cardFrontCode"
                                             Text="00000000" 
                                             Foreground="White" 
                                             Margin="8,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Back of card with gradient background -->
                    <Border x:Name="backCardPreview"
                            Style="{StaticResource LoyaltyCardStyle}"
                            Height="200"
                            Width="316"
                            HorizontalAlignment="Center" 
                            Visibility="Collapsed">
                        <Grid>
                            <!-- Top section with store details -->
                            <DockPanel VerticalAlignment="Top"
                                      Margin="16,16,16,0">
                                <StackPanel x:Name="BackLogoContainer"
                                          DockPanel.Dock="Left" 
                                          Width="80">
                                <TextBlock Text="LOGO" 
                                         FontSize="14" 
                                         FontWeight="Bold" 
                                         Foreground="#E1D0B3"
                                         HorizontalAlignment="Center"/>
                                <TextBlock Text="YOUR STORE" 
                                         FontSize="8" 
                                         Foreground="#E1D0B3"
                                         HorizontalAlignment="Center"
                                         TextWrapping="Wrap"
                                         TextAlignment="Center"/>
                            </StackPanel>
                            
                                <Rectangle DockPanel.Dock="Left" 
                                         Width="1" 
                                         Height="40" 
                                     Fill="#E1D0B3" 
                                         Margin="8,0"/>
                                
                                <StackPanel>
                                    <TextBlock Text="LOYALTY CARD" 
                                             FontSize="12" 
                                             FontWeight="Bold" 
                                             Foreground="#E1D0B3"/>
                                    <TextBlock x:Name="cardBackCode"
                                             Text="00000000" 
                                             Foreground="White"
                                             FontWeight="Medium" 
                                             Margin="0,4,0,0"/>
                                </StackPanel>
                            </DockPanel>
                            
                            <!-- Barcode visualization - Updated for clean, centered layout -->
                            <Border Background="#E1D0B3" 
                                    CornerRadius="0,0,8,8"
                                    VerticalAlignment="Bottom"
                                    Height="120">
                                <Grid Margin="16,10,16,16">
                                    <!-- Centered barcode container -->
                                    <StackPanel VerticalAlignment="Center" 
                                               HorizontalAlignment="Center">
                                        <!-- Simulated barcode - widened and centered -->
                                        <Canvas Height="50" 
                                               Width="220" 
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,10">
                                            <!-- First barcode segment -->
                                            <Rectangle Canvas.Left="0" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="5" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="8" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="12" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="18" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="22" Width="5" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="29" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="32" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="37" Width="2" Height="50" Fill="#192656" />
                                            <!-- Middle barcode segment -->
                                            <Rectangle Canvas.Left="45" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="50" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="53" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="59" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="63" Width="5" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="70" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="74" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="77" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="82" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="86" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="92" Width="6" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="100" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="103" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="108" Width="2" Height="50" Fill="#192656" />
                                            <!-- End barcode segment -->
                                            <Rectangle Canvas.Left="116" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="122" Width="6" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="130" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="134" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="137" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="142" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="146" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="152" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="155" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="160" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="166" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="170" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="175" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="179" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="182" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="187" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="191" Width="5" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="198" Width="1" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="201" Width="2" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="205" Width="3" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="210" Width="4" Height="50" Fill="#192656" />
                                            <Rectangle Canvas.Left="216" Width="3" Height="50" Fill="#192656" />
                                        </Canvas>
                                        
                                        <!-- Loyalty code display -->
                                        <TextBlock x:Name="cardBarcodeCode"
                                                 Text="LC-350319" 
                                                 FontSize="20"
                                                 FontWeight="Bold"
                                                 Foreground="#192656"
                                                 HorizontalAlignment="Center"
                                                 Margin="0,8,0,6"/>
                                        
                                        <TextBlock Text="Scan this barcode at checkout" 
                                                 FontSize="11"
                                                 Foreground="#192656"
                                                 Opacity="0.8"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
                
                <!-- Action Buttons -->
                <Grid Margin="0,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Print Current Side Button -->
                    <Button x:Name="btnPrintCard"
                          Grid.Column="0"
                          Style="{StaticResource AppSuccessButtonStyle}"
                          Margin="0,0,4,0"
                          Height="44"
                          Click="BtnPrintCard_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" 
                                                   Width="18" 
                                                   Height="18"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="Print Current Side"/>
                        </StackPanel>
                    </Button>
                    
                    <!-- Close Button -->
                    <Button x:Name="btnClose"
                          Grid.Column="1"
                          Style="{StaticResource AppSecondaryButtonStyle}"
                          Margin="4,0,0,0"
                          Height="44"
                          Click="BtnClose_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Close" 
                                                   Width="18" 
                                                   Height="18"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="Close"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl> 