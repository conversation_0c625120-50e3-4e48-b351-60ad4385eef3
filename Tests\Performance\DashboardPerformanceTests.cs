using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Services;
using POSSystem.Services.QueryOptimization;
using POSSystem.Services.Monitoring;
using POSSystem.ViewModels;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// ✅ NEW: Comprehensive performance tests for dashboard optimization
    /// Tests loading times, memory usage, and performance improvements
    /// </summary>
    [TestClass]
    public class DashboardPerformanceTests
    {
        private POSDbContext _context;
        private DatabaseService _databaseService;
        private DashboardQueryService _queryService;
        private DashboardPerformanceMonitor _performanceMonitor;
        private DashboardViewModel _dashboardViewModel;

        [TestInitialize]
        public void Setup()
        {
            // Setup in-memory database for testing
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _databaseService = new DatabaseService(_context);
            _queryService = new DashboardQueryService(_context);
            _performanceMonitor = new DashboardPerformanceMonitor();
            _dashboardViewModel = new DashboardViewModel(_databaseService);

            // Seed test data
            SeedTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _performanceMonitor?.Dispose();
            _dashboardViewModel?.Dispose();
            _context?.Dispose();
        }

        /// <summary>
        /// ✅ TEST: Dashboard loading time should be under 500ms for essential metrics
        /// </summary>
        [TestMethod]
        public async Task DashboardEssentialMetrics_LoadsUnder500ms()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Now;

            // Act
            using var tracker = _performanceMonitor.StartOperation("EssentialMetricsLoad", "Performance Test");
            var metrics = await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            
            // Assert
            var summary = _performanceMonitor.GetPerformanceSummary();
            Assert.IsTrue(summary.AverageLoadTime < 500, 
                $"Essential metrics loading took {summary.AverageLoadTime:F1}ms, expected < 500ms");
            
            Assert.IsNotNull(metrics);
            Assert.IsTrue(metrics.TotalSales >= 0);
            
            Debug.WriteLine($"✅ Essential metrics loaded in {summary.AverageLoadTime:F1}ms");
        }

        /// <summary>
        /// ✅ TEST: Full dashboard loading should be under 2 seconds
        /// </summary>
        [TestMethod]
        public async Task FullDashboardLoad_CompletesUnder2Seconds()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act
            using var tracker = _performanceMonitor.StartOperation("FullDashboardLoad", "Performance Test");
            await _dashboardViewModel.LoadDashboardDataAsync();
            stopwatch.Stop();

            // Assert
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000, 
                $"Full dashboard loading took {stopwatch.ElapsedMilliseconds}ms, expected < 2000ms");
            
            Debug.WriteLine($"✅ Full dashboard loaded in {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ TEST: Memory usage should not exceed 100MB during dashboard operations
        /// </summary>
        [TestMethod]
        public async Task DashboardOperations_MemoryUsageUnder100MB()
        {
            // Arrange
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var initialMemory = GC.GetTotalMemory(false);

            // Act
            using var tracker = _performanceMonitor.StartOperation("MemoryUsageTest", "Performance Test");
            
            // Perform multiple dashboard operations
            await _dashboardViewModel.LoadDashboardDataAsync();
            await _queryService.GetSalesTrendAsync(DateTime.Today.AddDays(-30), DateTime.Now);
            await _queryService.GetTopProductsAsync(DateTime.Today.AddDays(-30), DateTime.Now, 25);
            
            var finalMemory = GC.GetTotalMemory(false);
            var memoryUsed = (finalMemory - initialMemory) / (1024 * 1024); // Convert to MB

            // Assert
            Assert.IsTrue(memoryUsed < 100, 
                $"Dashboard operations used {memoryUsed}MB, expected < 100MB");
            
            Debug.WriteLine($"✅ Dashboard operations used {memoryUsed}MB memory");
        }

        /// <summary>
        /// ✅ TEST: Caching should improve subsequent load times by at least 50%
        /// </summary>
        [TestMethod]
        public async Task DashboardCaching_ImprovesPerformanceBy50Percent()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-7);
            var endDate = DateTime.Now;

            // Act - First load (no cache)
            var stopwatch1 = Stopwatch.StartNew();
            await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            stopwatch1.Stop();
            var firstLoadTime = stopwatch1.ElapsedMilliseconds;

            // Act - Second load (with cache)
            var stopwatch2 = Stopwatch.StartNew();
            await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            stopwatch2.Stop();
            var secondLoadTime = stopwatch2.ElapsedMilliseconds;

            // Assert
            var improvementPercent = ((double)(firstLoadTime - secondLoadTime) / firstLoadTime) * 100;
            Assert.IsTrue(improvementPercent >= 50 || secondLoadTime < 100, 
                $"Caching improved performance by {improvementPercent:F1}%, expected >= 50%");
            
            Debug.WriteLine($"✅ Caching improved performance by {improvementPercent:F1}% (First: {firstLoadTime}ms, Second: {secondLoadTime}ms)");
        }

        /// <summary>
        /// ✅ TEST: Large dataset handling should maintain performance
        /// </summary>
        [TestMethod]
        public async Task LargeDataset_MaintainsPerformance()
        {
            // Arrange - Add more test data
            SeedLargeTestDataset();
            var startDate = DateTime.Today.AddDays(-90);
            var endDate = DateTime.Now;

            // Act
            var stopwatch = Stopwatch.StartNew();
            using var tracker = _performanceMonitor.StartOperation("LargeDatasetTest", "Performance Test");
            
            var metrics = await _queryService.GetEssentialMetricsAsync(startDate, endDate);
            var trendData = await _queryService.GetSalesTrendAsync(startDate, endDate);
            var topProducts = await _queryService.GetTopProductsAsync(startDate, endDate, 50);
            
            stopwatch.Stop();

            // Assert
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 3000, 
                $"Large dataset operations took {stopwatch.ElapsedMilliseconds}ms, expected < 3000ms");
            
            Assert.IsNotNull(metrics);
            Assert.IsNotNull(trendData);
            Assert.IsNotNull(topProducts);
            
            Debug.WriteLine($"✅ Large dataset operations completed in {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ TEST: Progressive loading should show initial results quickly
        /// </summary>
        [TestMethod]
        public async Task ProgressiveLoading_ShowsInitialResultsQuickly()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act - Simulate progressive loading
            using var tracker = _performanceMonitor.StartOperation("ProgressiveLoadingTest", "Performance Test");
            
            // Stage 1: Essential metrics (should be very fast)
            var stage1Start = stopwatch.ElapsedMilliseconds;
            var essentialMetrics = await _queryService.GetEssentialMetricsAsync(DateTime.Today, DateTime.Now);
            var stage1Time = stopwatch.ElapsedMilliseconds - stage1Start;

            // Stage 2: Charts (can be slower)
            var stage2Start = stopwatch.ElapsedMilliseconds;
            var trendData = await _queryService.GetSalesTrendAsync(DateTime.Today.AddDays(-7), DateTime.Now);
            var stage2Time = stopwatch.ElapsedMilliseconds - stage2Start;

            stopwatch.Stop();

            // Assert
            Assert.IsTrue(stage1Time < 200, 
                $"Stage 1 (essential metrics) took {stage1Time}ms, expected < 200ms");
            
            Assert.IsTrue(stage2Time < 1000, 
                $"Stage 2 (charts) took {stage2Time}ms, expected < 1000ms");
            
            Debug.WriteLine($"✅ Progressive loading: Stage 1: {stage1Time}ms, Stage 2: {stage2Time}ms");
        }

        /// <summary>
        /// ✅ BENCHMARK: Compare performance before and after optimizations
        /// </summary>
        [TestMethod]
        public async Task PerformanceBenchmark_ShowsImprovement()
        {
            // This test would compare against baseline metrics
            // For now, we'll just ensure current performance meets targets

            var benchmarkResults = new List<BenchmarkResult>();

            // Benchmark 1: Essential metrics loading
            var stopwatch = Stopwatch.StartNew();
            await _queryService.GetEssentialMetricsAsync(DateTime.Today.AddDays(-30), DateTime.Now);
            stopwatch.Stop();
            benchmarkResults.Add(new BenchmarkResult("EssentialMetrics", stopwatch.ElapsedMilliseconds, 500));

            // Benchmark 2: Sales trend loading
            stopwatch.Restart();
            await _queryService.GetSalesTrendAsync(DateTime.Today.AddDays(-30), DateTime.Now);
            stopwatch.Stop();
            benchmarkResults.Add(new BenchmarkResult("SalesTrend", stopwatch.ElapsedMilliseconds, 1000));

            // Benchmark 3: Product performance loading
            stopwatch.Restart();
            await _queryService.GetTopProductsAsync(DateTime.Today.AddDays(-30), DateTime.Now, 25);
            stopwatch.Stop();
            benchmarkResults.Add(new BenchmarkResult("ProductPerformance", stopwatch.ElapsedMilliseconds, 800));

            // Assert all benchmarks pass
            foreach (var result in benchmarkResults)
            {
                Assert.IsTrue(result.ActualTime <= result.TargetTime, 
                    $"{result.Operation} took {result.ActualTime}ms, target was {result.TargetTime}ms");
                
                Debug.WriteLine($"✅ {result.Operation}: {result.ActualTime}ms (target: {result.TargetTime}ms)");
            }
        }

        /// <summary>
        /// ✅ HELPER: Seed basic test data
        /// </summary>
        private void SeedTestData()
        {
            // Add test products
            for (int i = 1; i <= 100; i++)
            {
                _context.Products.Add(new Models.Product
                {
                    Id = i,
                    Name = $"Product {i}",
                    SKU = $"SKU{i:D3}",
                    SellingPrice = 10 + (i % 50),
                    PurchasePrice = 5 + (i % 25),
                    StockQuantity = 100 - (i % 20),
                    IsActive = true
                });
            }

            // Add test sales
            var random = new Random(42); // Fixed seed for consistent tests
            for (int i = 1; i <= 1000; i++)
            {
                var saleDate = DateTime.Today.AddDays(-random.Next(0, 90));
                _context.Sales.Add(new Models.Sale
                {
                    Id = i,
                    SaleDate = saleDate,
                    GrandTotal = 50 + random.Next(0, 200),
                    Subtotal = 45 + random.Next(0, 180),
                    Status = "Completed",
                    PaymentStatus = random.Next(0, 10) > 8 ? "Unpaid" : "Paid"
                });
            }

            _context.SaveChanges();
        }

        /// <summary>
        /// ✅ HELPER: Seed large test dataset
        /// </summary>
        private void SeedLargeTestDataset()
        {
            var random = new Random(42);
            
            // Add more sales for large dataset testing
            for (int i = 1001; i <= 5000; i++)
            {
                var saleDate = DateTime.Today.AddDays(-random.Next(0, 180));
                _context.Sales.Add(new Models.Sale
                {
                    Id = i,
                    SaleDate = saleDate,
                    GrandTotal = 25 + random.Next(0, 300),
                    Subtotal = 20 + random.Next(0, 270),
                    Status = "Completed",
                    PaymentStatus = random.Next(0, 10) > 7 ? "Unpaid" : "Paid"
                });
            }

            _context.SaveChanges();
        }
    }

    /// <summary>
    /// ✅ HELPER: Benchmark result data
    /// </summary>
    public class BenchmarkResult
    {
        public string Operation { get; set; }
        public long ActualTime { get; set; }
        public long TargetTime { get; set; }

        public BenchmarkResult(string operation, long actualTime, long targetTime)
        {
            Operation = operation;
            ActualTime = actualTime;
            TargetTime = targetTime;
        }
    }
}
