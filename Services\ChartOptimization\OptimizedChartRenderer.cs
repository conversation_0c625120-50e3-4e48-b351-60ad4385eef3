using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models.Dashboard;
using POSSystem.Services.QueryOptimization;
using POSSystem.ViewModels;

namespace POSSystem.Services.ChartOptimization
{
    /// <summary>
    /// ✅ PRIORITY 4 OPTIMIZATION: High-performance chart renderer with smart optimizations
    /// </summary>
    public class OptimizedChartRenderer
    {
        private readonly object _renderLock = new object();
        private volatile bool _isRendering = false;

        /// <summary>
        /// ✅ OPTIMIZATION: Render product performance charts with performance optimizations
        /// </summary>
        public async Task<SeriesCollection> RenderProductChartsAsync(IEnumerable<ProductPerformance> products, string metricType)
        {
            return await Task.Run(() =>
            {
                lock (_renderLock)
                {
                    if (_isRendering) return new SeriesCollection();
                    _isRendering = true;

                    try
                    {
                        var productList = products?.ToList() ?? new List<ProductPerformance>();
                        
                        // ✅ PERFORMANCE: Sample data if too large
                        var sampledProducts = ChartPerformanceOptimizer.SampleDataForChart(productList, 50);
                        
                        var series = new SeriesCollection();
                        
                        switch (metricType?.ToLower())
                        {
                            case "revenue":
                                var revenueValues = sampledProducts.Select(p => (double)p.Revenue).ToList();
                                series.Add(ChartPerformanceOptimizer.CreateOptimizedColumnSeries(
                                    "Revenue", revenueValues, new SolidColorBrush(Colors.DodgerBlue)));
                                break;
                                
                            case "profit":
                                var profitValues = sampledProducts.Select(p => (double)p.Profit).ToList();
                                series.Add(ChartPerformanceOptimizer.CreateOptimizedColumnSeries(
                                    "Profit", profitValues, new SolidColorBrush(Colors.LimeGreen)));
                                break;
                                
                            case "margin":
                                var marginValues = sampledProducts.Select(p => (double)p.Margin).ToList();
                                series.Add(ChartPerformanceOptimizer.CreateOptimizedColumnSeries(
                                    "Margin", marginValues, new SolidColorBrush(Colors.Orange)));
                                break;
                                
                            case "items":
                                var itemsValues = sampledProducts.Select(p => (double)p.ItemsSold).ToList();
                                series.Add(ChartPerformanceOptimizer.CreateOptimizedColumnSeries(
                                    "Items Sold", itemsValues, new SolidColorBrush(Colors.Purple)));
                                break;
                                
                            default:
                                // Multi-metric view with performance optimization
                                return RenderMultiMetricChart(sampledProducts);
                        }
                        
                        return ChartPerformanceOptimizer.OptimizeSeriesCollection(series);
                    }
                    finally
                    {
                        _isRendering = false;
                    }
                }
            });
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Render pie charts with smart performance settings
        /// </summary>
        public async Task<SeriesCollection> RenderPieChartAsync(IEnumerable<ProductPerformance> products, string metricType)
        {
            return await Task.Run(() =>
            {
                lock (_renderLock)
                {
                    if (_isRendering) return new SeriesCollection();
                    _isRendering = true;

                    try
                    {
                        var productList = products?.ToList() ?? new List<ProductPerformance>();
                        
                        // ✅ PERFORMANCE: Limit pie chart slices for readability and performance
                        var topProducts = productList.Take(12).ToList();
                        
                        var series = new SeriesCollection();
                        var colorIndex = 0;
                        var colors = GetOptimizedColorPalette();
                        
                        foreach (var product in topProducts)
                        {
                            var value = GetMetricValue(product, metricType);
                            if (value > 0)
                            {
                                var pieSeries = ChartPerformanceOptimizer.CreateOptimizedPieSeries(
                                    product.Name, 
                                    value, 
                                    new SolidColorBrush(colors[colorIndex % colors.Length]),
                                    topProducts.Count);
                                    
                                series.Add(pieSeries);
                                colorIndex++;
                            }
                        }
                        
                        return series;
                    }
                    finally
                    {
                        _isRendering = false;
                    }
                }
            });
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Render trend charts with adaptive performance settings
        /// </summary>
        public async Task<SeriesCollection> RenderTrendChartAsync(List<decimal> values, List<string> labels, string title)
        {
            return await Task.Run(() =>
            {
                lock (_renderLock)
                {
                    if (_isRendering) return new SeriesCollection();
                    _isRendering = true;

                    try
                    {
                        if (values == null || values.Count == 0)
                            return new SeriesCollection();
                        
                        // ✅ PERFORMANCE: Sample data for very large datasets
                        var sampledValues = ChartPerformanceOptimizer.SampleDataForChart(values);
                        
                        var series = new SeriesCollection();
                        
                        // Main trend line
                        var mainSeries = ChartPerformanceOptimizer.CreateOptimizedLineSeries(
                            title, sampledValues, new SolidColorBrush(Colors.DodgerBlue));
                        series.Add(mainSeries);
                        
                        // Add moving average if dataset is large enough
                        if (sampledValues.Count >= 7)
                        {
                            var movingAverage = CalculateMovingAverage(sampledValues, Math.Min(7, sampledValues.Count / 3));
                            var avgSeries = ChartPerformanceOptimizer.CreateOptimizedLineSeries(
                                "Moving Average", movingAverage, new SolidColorBrush(Colors.Red));
                            
                            // Override settings for moving average
                            avgSeries.StrokeDashArray = new DoubleCollection { 4, 2 };
                            avgSeries.Fill = Brushes.Transparent;
                            avgSeries.DataLabels = false;
                            
                            series.Add(avgSeries);
                        }
                        
                        return series;
                    }
                    finally
                    {
                        _isRendering = false;
                    }
                }
            });
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Batch render multiple charts efficiently
        /// </summary>
        public async Task<Dictionary<string, SeriesCollection>> BatchRenderChartsAsync(
            IEnumerable<ProductPerformance> products, 
            string[] metricTypes)
        {
            var results = new Dictionary<string, SeriesCollection>();
            
            // Prepare data once for all charts
            var productList = products?.ToList() ?? new List<ProductPerformance>();
            var sampledProducts = ChartPerformanceOptimizer.SampleDataForChart(productList, 50);
            
            // Render charts in parallel for better performance
            var tasks = metricTypes.Select(async metricType =>
            {
                var series = await RenderProductChartsAsync(sampledProducts, metricType);
                return new { MetricType = metricType, Series = series };
            });
            
            var chartResults = await Task.WhenAll(tasks);
            
            foreach (var result in chartResults)
            {
                results[result.MetricType] = result.Series;
            }
            
            return results;
        }

        /// <summary>
        /// ✅ OPTIMIZATION: Smart chart update that only refreshes when necessary
        /// </summary>
        public async Task<bool> SmartUpdateChartAsync(SeriesCollection currentSeries, SeriesCollection newSeries)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Quick comparison to avoid unnecessary updates
                    if (currentSeries?.Count != newSeries?.Count)
                        return true; // Needs update
                    
                    if (currentSeries == null || newSeries == null)
                        return true; // Needs update
                    
                    // Compare data points count (lightweight check)
                    for (int i = 0; i < currentSeries.Count; i++)
                    {
                        var currentCount = currentSeries[i]?.Values?.Count ?? 0;
                        var newCount = newSeries[i]?.Values?.Count ?? 0;
                        
                        if (currentCount != newCount)
                            return true; // Needs update
                    }
                    
                    return false; // No update needed
                }
                catch
                {
                    return true; // Update on error to be safe
                }
            });
        }

        #region Private Helper Methods

        private SeriesCollection RenderMultiMetricChart(List<ProductPerformance> products)
        {
            var series = new SeriesCollection();
            
            // Only show top 10 products for multi-metric to avoid clutter
            var topProducts = products.Take(10).ToList();
            
            var revenueValues = topProducts.Select(p => (double)p.Revenue).ToList();
            series.Add(ChartPerformanceOptimizer.CreateOptimizedColumnSeries(
                "Revenue", revenueValues, new SolidColorBrush(Colors.DodgerBlue)));
            
            return series;
        }

        private decimal GetMetricValue(ProductPerformance product, string metricType)
        {
            return metricType?.ToLower() switch
            {
                "revenue" => product.Revenue,
                "profit" => product.Profit,
                "margin" => product.Margin,
                "items" => product.ItemsSold,
                _ => product.Revenue
            };
        }

        private Color[] GetOptimizedColorPalette()
        {
            return new[]
            {
                Colors.DodgerBlue, Colors.LimeGreen, Colors.Orange, Colors.Red,
                Colors.Purple, Colors.Teal, Colors.Gold, Colors.Crimson,
                Colors.ForestGreen, Colors.RoyalBlue, Colors.Chocolate, Colors.MediumVioletRed
            };
        }

        private List<decimal> CalculateMovingAverage(List<decimal> values, int windowSize)
        {
            var movingAverage = new List<decimal>();
            
            for (int i = 0; i < values.Count; i++)
            {
                var start = Math.Max(0, i - windowSize + 1);
                var end = i + 1;
                var window = values.Skip(start).Take(end - start);
                movingAverage.Add(window.Average());
            }
            
            return movingAverage;
        }

        #endregion
    }
}
