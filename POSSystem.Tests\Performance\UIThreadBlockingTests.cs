using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.ViewModels;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// UI Thread Blocking Tests
    /// 
    /// Tests to verify that operations don't block the UI thread, specifically:
    /// - Product.GetTotalStock() method doesn't make synchronous database calls
    /// - Dashboard loading operations are properly async
    /// - Batch data is pre-loaded to prevent fallback scenarios
    /// </summary>
    public class UIThreadBlockingTests : IClassFixture<ComprehensivePerformanceTestSuite>
    {
        private readonly ComprehensivePerformanceTestSuite _testSuite;
        private readonly ITestOutputHelper _output;

        public UIThreadBlockingTests(ComprehensivePerformanceTestSuite testSuite, ITestOutputHelper output)
        {
            _testSuite = testSuite;
            _output = output;
        }

        [Fact]
        public async Task ProductGetTotalStock_ShouldNotBlockUIThread()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 100, customerCount: 50, salesCount: 200);
            var dbService = _testSuite._serviceProvider.GetRequiredService<DatabaseService>();
            
            _testSuite.LogPerformanceMessage("Testing Product.GetTotalStock() UI thread blocking");

            // Create test products with batch tracking
            var testProducts = new List<Product>();
            using (var context = new POSDbContext())
            {
                // Create products with batch tracking enabled
                for (int i = 1; i <= 10; i++)
                {
                    var product = new Product
                    {
                        Name = $"Test Product {i}",
                        SKU = $"TEST{i:D3}",
                        SellingPrice = 10.00m,
                        PurchasePrice = 5.00m,
                        StockQuantity = 50,
                        TrackBatches = true,
                        IsActive = true
                    };
                    
                    context.Products.Add(product);
                    testProducts.Add(product);
                }
                
                await context.SaveChangesAsync();
                
                // Add batch data for some products
                foreach (var product in testProducts.Take(5))
                {
                    for (int j = 1; j <= 3; j++)
                    {
                        context.BatchStock.Add(new BatchStock
                        {
                            ProductId = product.Id,
                            BatchNumber = $"BATCH{product.Id}-{j}",
                            Quantity = 10,
                            PurchasePrice = 5.00m,
                            ExpiryDate = DateTime.Now.AddMonths(6)
                        });
                    }
                }
                
                await context.SaveChangesAsync();
            }

            // Act & Assert - Test that GetTotalStock doesn't block
            var stopwatch = Stopwatch.StartNew();
            var results = new List<int>();
            
            // Test multiple products in sequence (simulating dashboard loading)
            foreach (var product in testProducts)
            {
                var stock = product.GetTotalStock();
                results.Add(stock);
            }
            
            stopwatch.Stop();
            
            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100, 
                "GetTotalStock() should complete quickly without database calls");
            
            results.Should().NotBeEmpty("Should return stock values for all products");
            
            _testSuite.LogPerformanceMessage($"GetTotalStock() completed in {stopwatch.ElapsedMilliseconds}ms for {testProducts.Count} products");
        }

        [Fact]
        public async Task DashboardProductLoading_ShouldIncludeBatchData()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 50, customerCount: 25, salesCount: 100);
            var dbService = _testSuite._serviceProvider.GetRequiredService<DatabaseService>();
            
            _testSuite.LogPerformanceMessage("Testing dashboard product loading includes batch data");

            // Act - Load products as dashboard would
            var stopwatch = Stopwatch.StartNew();
            var products = await dbService.GetTopSellingProductsAsync(10);
            stopwatch.Stop();

            // Assert
            products.Should().NotBeEmpty("Should load products");
            
            // Check that batch data is included for products that track batches
            var batchTrackedProducts = products.Where(p => p.TrackBatches).ToList();
            if (batchTrackedProducts.Any())
            {
                foreach (var product in batchTrackedProducts)
                {
                    // The Batches collection should be loaded (not null)
                    // This prevents the GetTotalStock() method from making database calls
                    product.Batches.Should().NotBeNull("Batch data should be pre-loaded");
                }
            }
            
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, 
                "Product loading with batch data should complete within 2 seconds");
            
            _testSuite.LogPerformanceMessage($"Loaded {products.Count} products with batch data in {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task DashboardLoading_ShouldNotCauseUIThreadBlocking()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 200, customerCount: 100, salesCount: 500);
            var dashboardViewModel = _testSuite._serviceProvider.GetRequiredService<DashboardViewModel>();
            
            _testSuite.LogPerformanceMessage("Testing dashboard loading for UI thread blocking");

            // Act - Simulate dashboard loading
            var stopwatch = Stopwatch.StartNew();
            
            // This should not block the UI thread
            await dashboardViewModel.LoadDashboardDataAsync();
            
            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, 
                "Dashboard loading should complete within 5 seconds without UI blocking");
            
            _testSuite.LogPerformanceMessage($"Dashboard loading completed in {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task ProductStockCalculation_WithBatchData_ShouldBeEfficient()
        {
            // Arrange
            var dbService = _testSuite._serviceProvider.GetRequiredService<DatabaseService>();
            
            // Create a product with batch data pre-loaded
            var product = new Product
            {
                Name = "Test Product with Batches",
                SKU = "TESTBATCH001",
                SellingPrice = 15.00m,
                PurchasePrice = 8.00m,
                StockQuantity = 20,
                TrackBatches = true,
                IsActive = true,
                Batches = new List<BatchStock>
                {
                    new BatchStock { Quantity = 10, BatchNumber = "B001" },
                    new BatchStock { Quantity = 15, BatchNumber = "B002" },
                    new BatchStock { Quantity = 5, BatchNumber = "B003" }
                }
            };

            // Act - Test stock calculation with pre-loaded batch data
            var stopwatch = Stopwatch.StartNew();
            var totalStock = product.GetTotalStock();
            stopwatch.Stop();

            // Assert
            totalStock.Should().Be(30, "Should sum all batch quantities (10+15+5)");
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1, 
                "Stock calculation with pre-loaded data should be instant");
            
            _testSuite.LogPerformanceMessage($"Stock calculation with batches completed in {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task ProductStockCalculation_WithoutBatchData_ShouldUseFallback()
        {
            // Arrange
            var product = new Product
            {
                Name = "Test Product without Batches",
                SKU = "TESTFALLBACK001",
                SellingPrice = 12.00m,
                PurchasePrice = 6.00m,
                StockQuantity = 25,
                TrackBatches = true,
                IsActive = true,
                Batches = null // No batch data loaded
            };

            // Act - Test stock calculation fallback
            var stopwatch = Stopwatch.StartNew();
            var totalStock = product.GetTotalStock();
            stopwatch.Stop();

            // Assert
            totalStock.Should().Be(25, "Should fallback to StockQuantity when batches not loaded");
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1, 
                "Fallback stock calculation should be instant (no database calls)");
            
            _testSuite.LogPerformanceMessage($"Stock calculation fallback completed in {stopwatch.ElapsedMilliseconds}ms");
        }
    }
}
