using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Helpers;
using MaterialDesignThemes.Wpf;
using CommunityToolkit.Mvvm.Input;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ViewModel for the pending drafts panel
    /// </summary>
    public class PendingDraftsViewModel : INotifyPropertyChanged
    {
        private readonly DraftInvoiceService _draftInvoiceService;
        private readonly DraftInvoiceNotificationService _notificationService;
        private readonly AuthenticationService _authService;
        private readonly UserPermissionsService _permissionsService;

        // Private fields
        private bool _isLoading;
        private string _statusMessage;

        public PendingDraftsViewModel(
            DraftInvoiceService draftInvoiceService,
            DraftInvoiceNotificationService notificationService,
            AuthenticationService authService,
            UserPermissionsService permissionsService)
        {
            _draftInvoiceService = draftInvoiceService ?? throw new ArgumentNullException(nameof(draftInvoiceService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _permissionsService = permissionsService ?? throw new ArgumentNullException(nameof(permissionsService));

            // Initialize collections
            PendingDrafts = new ObservableCollection<Invoice>();

            // Initialize commands
            CompleteDraftCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand<Invoice>(draft => CompleteDraftAsync(draft), draft => CanCompleteDraft(draft));
            RefreshCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(RefreshAsync);
            ViewAllCommand = new RelayCommand(_ => ViewAll());

            // Subscribe to notification service
            _notificationService.PropertyChanged += OnNotificationServicePropertyChanged;

            // Load initial data
            _ = LoadPendingDraftsAsync();
        }

        #region Properties

        public ObservableCollection<Invoice> PendingDrafts { get; }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // Computed properties
        public bool HasPendingDrafts => PendingDrafts.Count > 0;
        
        public int PendingCount => PendingDrafts.Count;
        
        public bool CanManageDrafts => _permissionsService.CanCompleteInvoiceDrafts();

        #endregion

        #region Commands

        public ICommand CompleteDraftCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ViewAllCommand { get; }

        #endregion

        #region Command Implementations

        private bool CanCompleteDraft(Invoice draft)
        {
            return draft != null && CanManageDrafts && !IsLoading;
        }

        private async Task CompleteDraftAsync(Invoice draft)
        {
            try
            {
                if (draft == null) return;

                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Opening completion dialog for draft: {draft.InvoiceNumber}");

                // Create and show admin completion dialog
                var completionViewModel = new AdminDraftCompletionViewModel(draft, _draftInvoiceService, _authService, _permissionsService);
                var completionDialog = new POSSystem.Views.Dialogs.AdminDraftCompletionDialog(completionViewModel);

                var result = await DialogHost.Show(completionDialog, "MainDialog");

                if (completionDialog.DialogResult == true)
                {
                    // Refresh the pending drafts list
                    await RefreshAsync();
                    StatusMessage = "Draft invoice completed successfully.";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error completing draft: {ex.Message}");
                StatusMessage = $"Error completing draft: {ex.Message}";
            }
        }

        private async Task RefreshAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Refreshing pending drafts...";

                await LoadPendingDraftsAsync();

                StatusMessage = $"Loaded {PendingCount} pending drafts.";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error refreshing: {ex.Message}");
                StatusMessage = $"Error refreshing: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ViewAll()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[PENDING_DRAFTS_VM] ViewAll command executed");
                // In a real implementation, this would open a full drafts management window
                StatusMessage = "Opening full drafts management view...";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error in ViewAll: {ex.Message}");
                StatusMessage = $"Error opening drafts view: {ex.Message}";
            }
        }

        #endregion

        #region Helper Methods

        private async Task LoadPendingDraftsAsync()
        {
            try
            {
                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("[PENDING_DRAFTS_VM] No current user found");
                    return;
                }

                // Get pending drafts from service
                var pendingDrafts = await _draftInvoiceService.GetPendingDraftInvoicesAsync();

                // Update collection on UI thread
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    PendingDrafts.Clear();
                    foreach (var draft in pendingDrafts.OrderByDescending(d => d.DraftCreatedAt))
                    {
                        PendingDrafts.Add(draft);
                    }

                    // Update computed properties
                    OnPropertyChanged(nameof(HasPendingDrafts));
                    OnPropertyChanged(nameof(PendingCount));
                });

                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Loaded {pendingDrafts.Count} pending drafts");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error loading pending drafts: {ex.Message}");
                throw;
            }
        }

        private void OnNotificationServicePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                if (e.PropertyName == nameof(DraftInvoiceNotificationService.PendingDraftCount))
                {
                    // Refresh when notification service reports changes
                    _ = RefreshAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error handling notification service change: {ex.Message}");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            try
            {
                if (_notificationService != null)
                {
                    _notificationService.PropertyChanged -= OnNotificationServicePropertyChanged;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PENDING_DRAFTS_VM] Error disposing: {ex.Message}");
            }
        }

        #endregion
    }
}
