# ProductDialog NullReferenceException Fix - Test Scenarios

## Overview
This document outlines test scenarios to verify that the NullReferenceException in the ProductDialog's ItemType_Changed event handler has been fixed.

## Fixed Issues
1. **Missing Event Handlers**: Added `Checked` and `Unchecked` events to both radio buttons
2. **Null Reference Protection**: Added comprehensive null checks for radio button controls
3. **Helper Method**: Created `GetIsServiceSelected()` method for safe radio button access
4. **Defensive Programming**: Added initialization verification and error handling

## Test Scenarios

### Test 1: Basic Product/Service Type Selection
**Objective**: Verify that switching between Product and Service types works without exceptions

**Steps**:
1. Open ProductDialog (Add new product)
2. Verify "Product" is selected by default
3. Click "Service" radio button
4. Verify UI updates correctly (stock fields hidden, service units shown)
5. Click "Product" radio button
6. Verify UI updates correctly (stock fields shown, all units available)

**Expected Result**: No exceptions thrown, UI updates smoothly

### Test 2: Edit Existing Product
**Objective**: Verify that editing existing products works correctly

**Steps**:
1. Open ProductDialog in edit mode with an existing product
2. Verify the correct radio button is selected based on product type
3. Switch between Product and Service types
4. Save the product

**Expected Result**: Product type is correctly loaded and can be changed without errors

### Test 3: Service-Specific Features
**Objective**: Verify that service-specific UI behavior works correctly

**Steps**:
1. Open ProductDialog
2. Select "Service" radio button
3. Verify:
   - Stock-related fields are hidden/disabled
   - Batch tracking is disabled
   - Expiry date is disabled
   - Only appropriate units of measure are shown (time/count units)
   - Field hints change to service-specific text

**Expected Result**: All service-specific UI changes work correctly

### Test 4: Product-Specific Features
**Objective**: Verify that product-specific UI behavior works correctly

**Steps**:
1. Open ProductDialog
2. Ensure "Product" radio button is selected
3. Verify:
   - Stock-related fields are visible/enabled
   - Batch tracking is available
   - Expiry date is available
   - All units of measure are shown
   - Field hints show product-specific text

**Expected Result**: All product-specific UI features work correctly

### Test 5: Rapid Type Switching
**Objective**: Test for race conditions or timing issues

**Steps**:
1. Open ProductDialog
2. Rapidly click between Product and Service radio buttons multiple times
3. Verify no exceptions are thrown
4. Verify UI state remains consistent

**Expected Result**: No exceptions, UI remains stable

### Test 6: Dialog Initialization
**Objective**: Verify that the dialog initializes correctly

**Steps**:
1. Open ProductDialog multiple times
2. Check debug output for any initialization warnings
3. Verify radio buttons are properly initialized

**Expected Result**: No initialization errors, radio buttons work immediately

## Debug Output to Monitor

When testing, monitor the debug output for these messages:

### Success Messages:
- `[PRODUCT_DIALOG] Successfully obtained DatabaseService from App.ServiceProvider`
- `[ITEM_TYPE] UI updated for Product/Service mode`

### Warning Messages (should not appear):
- `[PRODUCT_DIALOG] Warning: Radio buttons not properly initialized`
- `[ITEM_TYPE] Radio buttons not yet initialized, skipping event`

### Error Messages (should not appear):
- `[ITEM_TYPE] Error in ItemType_Changed: Object reference not set to an instance of an object`

## Code Changes Summary

### XAML Changes:
- Added `Checked="ItemType_Changed"` to rbService
- Added `Unchecked="ItemType_Changed"` to both radio buttons

### C# Changes:
- Added null checks in `ItemType_Changed` method
- Created `GetIsServiceSelected()` helper method
- Added null checks in `LoadProductData`, `ValidateInput`, and save methods
- Added initialization verification in constructor
- Enhanced error handling with stack trace logging

## Verification Checklist

- [ ] ProductDialog opens without exceptions
- [ ] Radio button selection works correctly
- [ ] UI updates properly for Product type
- [ ] UI updates properly for Service type
- [ ] Switching between types works smoothly
- [ ] Existing product editing works correctly
- [ ] No null reference exceptions in debug output
- [ ] All service-specific features work
- [ ] All product-specific features work
- [ ] Rapid switching doesn't cause issues

## Notes

The fixes implement defensive programming principles to ensure the ProductDialog is robust against timing issues and initialization problems. The helper method `GetIsServiceSelected()` provides a safe way to access radio button state throughout the class.
