using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ErrorHandling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.CustomerManagement
{
    /// <summary>
    /// Focused service for customer management operations
    /// Extracted from the large DatabaseService to improve maintainability
    /// </summary>
    public class CustomerManagementService : ICustomerManagementService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<CustomerManagementService> _logger;
        private readonly IErrorHandlingService _errorHandler;

        public CustomerManagementService(
            POSDbContext context,
            ILogger<CustomerManagementService> logger = null,
            IErrorHandlingService errorHandler = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _errorHandler = errorHandler;
        }

        /// <summary>
        /// Get all customers
        /// </summary>
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Customers
                    .AsNoTracking()
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }, "Get All Customers", new List<Customer>()) ?? new List<Customer>();
        }

        /// <summary>
        /// Get customer by ID
        /// </summary>
        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Customer ID must be greater than 0", nameof(id));

                return await _context.Customers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.Id == id);
            }, "Get Customer By ID", null) ?? null;
        }

        /// <summary>
        /// Get customer by loyalty code
        /// </summary>
        public async Task<Customer> GetCustomerByLoyaltyCodeAsync(string loyaltyCode)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(loyaltyCode))
                    return null;

                return await _context.Customers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.LoyaltyCode == loyaltyCode);
            }, "Get Customer By Loyalty Code", null) ?? null;
        }

        /// <summary>
        /// Add new customer
        /// </summary>
        public async Task<int> AddCustomerAsync(Customer customer)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateCustomer(customer);

                // Check for duplicate loyalty code if provided
                if (!string.IsNullOrWhiteSpace(customer.LoyaltyCode))
                {
                    var existingCustomer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.LoyaltyCode == customer.LoyaltyCode);

                    if (existingCustomer != null)
                        throw new InvalidOperationException($"Customer with loyalty code '{customer.LoyaltyCode}' already exists");
                }

                customer.CreatedAt = DateTime.Now;
                customer.UpdatedAt = DateTime.Now;

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully added customer {CustomerId} - {CustomerName}", customer.Id, customer.Name);
                return customer.Id;
            }, "Add Customer", 0);

            return result;
        }

        /// <summary>
        /// Update existing customer
        /// </summary>
        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateCustomer(customer);

                if (customer.Id <= 0)
                    throw new ArgumentException("Customer ID must be greater than 0 for updates", nameof(customer));

                var existingCustomer = await _context.Customers.FindAsync(customer.Id);
                if (existingCustomer == null)
                    throw new InvalidOperationException($"Customer with ID {customer.Id} not found");

                // Check for duplicate loyalty code if changed
                if (!string.IsNullOrWhiteSpace(customer.LoyaltyCode) &&
                    customer.LoyaltyCode != existingCustomer.LoyaltyCode)
                {
                    var duplicateCustomer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.LoyaltyCode == customer.LoyaltyCode && c.Id != customer.Id);

                    if (duplicateCustomer != null)
                        throw new InvalidOperationException($"Customer with loyalty code '{customer.LoyaltyCode}' already exists");
                }

                // Update properties
                existingCustomer.FirstName = customer.FirstName;
                existingCustomer.LastName = customer.LastName;
                existingCustomer.Email = customer.Email;
                existingCustomer.Phone = customer.Phone;
                existingCustomer.Address = customer.Address;
                existingCustomer.LoyaltyCode = customer.LoyaltyCode;
                existingCustomer.LoyaltyPoints = customer.LoyaltyPoints;
                existingCustomer.IsActive = customer.IsActive;
                existingCustomer.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated customer {CustomerId} - {CustomerName}", customer.Id, customer.Name);
                return true;
            }, "Update Customer", false);

            return result;
        }

        /// <summary>
        /// Delete customer
        /// </summary>
        public async Task<bool> DeleteCustomerAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Customer ID must be greater than 0", nameof(id));

                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    throw new InvalidOperationException($"Customer with ID {id} not found");

                // Check if customer has any sales
                var hasSales = await _context.Sales.AnyAsync(s => s.CustomerId == id);
                if (hasSales)
                {
                    // Soft delete - mark as inactive instead of hard delete
                    customer.IsActive = false;
                    customer.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Soft deleted customer {CustomerId} - {CustomerName} (marked as inactive)", id, customer.Name);
                }
                else
                {
                    // Hard delete if no sales
                    _context.Customers.Remove(customer);
                    await _context.SaveChangesAsync();
                    _logger?.LogInformation("Hard deleted customer {CustomerId} - {CustomerName}", id, customer.Name);
                }

                return true;
            }, "Delete Customer", false);

            return result;
        }

        /// <summary>
        /// Search customers by name, email, or phone
        /// </summary>
        public async Task<List<Customer>> SearchCustomersAsync(string searchTerm)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return new List<Customer>();

                var term = searchTerm.ToLower();

                return await _context.Customers
                    .AsNoTracking()
                    .Where(c => c.Name.ToLower().Contains(term) ||
                               c.Email.ToLower().Contains(term) ||
                               c.Phone.ToLower().Contains(term) ||
                               c.LoyaltyCode.ToLower().Contains(term))
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }, "Search Customers", new List<Customer>()) ?? new List<Customer>();
        }

        /// <summary>
        /// Get customers with loyalty points
        /// </summary>
        public async Task<List<Customer>> GetCustomersWithLoyaltyPointsAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Customers
                    .AsNoTracking()
                    .Where(c => c.IsActive && c.LoyaltyPoints > 0)
                    .OrderByDescending(c => c.LoyaltyPoints)
                    .ToListAsync();
            }, "Get Customers With Loyalty Points", new List<Customer>()) ?? new List<Customer>();
        }

        /// <summary>
        /// Update customer loyalty points
        /// </summary>
        public async Task<bool> UpdateCustomerLoyaltyPointsAsync(int customerId, int points)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (customerId <= 0)
                    throw new ArgumentException("Customer ID must be greater than 0", nameof(customerId));

                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null)
                    throw new InvalidOperationException($"Customer with ID {customerId} not found");

                customer.LoyaltyPoints = Math.Max(0, points); // Ensure points don't go negative
                customer.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated loyalty points for customer {CustomerId}, new points: {Points}", customerId, points);
                return true;
            }, "Update Customer Loyalty Points", false);

            return result;
        }

        /// <summary>
        /// Get top customers by purchase amount
        /// </summary>
        public async Task<List<Customer>> GetTopCustomersAsync(int count = 10)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                // Get customers with their total purchase amounts
                var customerPurchases = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.CustomerId.HasValue)
                    .GroupBy(s => s.CustomerId.Value)
                    .Select(g => new { CustomerId = g.Key, TotalPurchases = g.Sum(s => s.GrandTotal) })
                    .OrderByDescending(x => x.TotalPurchases)
                    .Take(count)
                    .ToListAsync();

                // Get the customer details
                var customerIds = customerPurchases.Select(cp => cp.CustomerId).ToList();
                var customers = await _context.Customers
                    .AsNoTracking()
                    .Where(c => customerIds.Contains(c.Id))
                    .ToListAsync();

                // Combine the data (maintaining order by purchase amount)
                return customerPurchases
                    .Select(cp => customers.FirstOrDefault(c => c.Id == cp.CustomerId))
                    .Where(c => c != null)
                    .ToList();
            }, "Get Top Customers", new List<Customer>()) ?? new List<Customer>();
        }

        /// <summary>
        /// Validate customer data
        /// </summary>
        private void ValidateCustomer(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer), "Customer cannot be null");

            if (string.IsNullOrWhiteSpace(customer.FirstName) || string.IsNullOrWhiteSpace(customer.LastName))
                throw new ArgumentException("Customer first name and last name cannot be empty", nameof(customer));

            if (!string.IsNullOrWhiteSpace(customer.Email) && !IsValidEmail(customer.Email))
                throw new ArgumentException("Invalid email format", nameof(customer));

            if (customer.LoyaltyPoints < 0)
                throw new ArgumentException("Loyalty points cannot be negative", nameof(customer));
        }

        /// <summary>
        /// Simple email validation
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
