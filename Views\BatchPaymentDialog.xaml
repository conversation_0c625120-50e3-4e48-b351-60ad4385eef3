<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.BatchPaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource BatchPayment}"
        MinWidth="800"
        MinHeight="600"
        Width="Auto"
        Height="Auto"
        MaxWidth="1400"
        MaxHeight="900"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        SizeToContent="WidthAndHeight"
        KeyDown="Window_KeyDown"
        AutomationProperties.Name="{DynamicResource BatchPayment}"
        AutomationProperties.HelpText="Dialog for processing batch payments for multiple invoices"
        Loaded="Window_Loaded">

    <Window.Resources>
        <!-- Enhanced DataGrid Row Style with Smooth Animations -->
        <Style x:Key="DataGridRowStyle" TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="#E3F2FD" Opacity="0.8"/>
                        </Setter.Value>
                    </Setter>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               To="1" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               To="0.8" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource PrimaryHueLightForegroundBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced Button Hover Animation -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="md:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="md:ElevationAssist.Elevation" Value="Dp2"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="md:ElevationAssist.Elevation" Value="Dp4"/>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1.02" Duration="0:0:0.15"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1.02" Duration="0:0:0.15"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1" Duration="0:0:0.15"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1" Duration="0:0:0.15"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced Card Style with Subtle Animation -->
        <Style x:Key="ModernCardStyle" TargetType="md:Card">
            <Setter Property="md:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="UniformCornerRadius" Value="12"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="md:ElevationAssist.Elevation" Value="Dp4"/>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                               To="-2" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                               To="0" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- DialogHost for handling nested dialogs -->
    <md:DialogHost Identifier="BatchPaymentDialog" Margin="0">
        <!-- Main Dialog Card with Enhanced Styling -->
        <md:Card x:Name="MainDialogCard"
                 Background="{DynamicResource MaterialDesignPaper}"
                 Foreground="{DynamicResource MaterialDesignBody}"
                 Style="{StaticResource ModernCardStyle}"
                 md:ElevationAssist.Elevation="Dp6"
                 Margin="20"
                 MinWidth="750"
                 MinHeight="550"
                 MaxWidth="1300"
                 MaxHeight="800">
        <md:Card.RenderTransform>
            <TranslateTransform/>
        </md:Card.RenderTransform>

        <Grid Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Enhanced Header Section with Gradient Background -->
            <Grid Grid.Row="0">
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#0078D4" Offset="0.0"/>
                        <GradientStop Color="#0067B5" Offset="1.0"/>
                    </LinearGradientBrush>
                </Grid.Background>

                <Border CornerRadius="12,12,0,0" Padding="32,24">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Title and Customer Info Section -->
                        <StackPanel VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <md:PackIcon Kind="CashMultiple"
                                           Width="32" Height="32"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource BatchPayment}"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="White"
                                         FontWeight="SemiBold"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Account"
                                           Width="18" Height="18"
                                           Foreground="White"
                                           Opacity="0.9"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Customer}"
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         Foreground="White"
                                         Opacity="0.9"
                                         Margin="0,0,8,0"
                                         VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding CustomerName}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Foreground="White"
                                         FontWeight="Medium"
                                         VerticalAlignment="Center"
                                         TextTrimming="CharacterEllipsis"
                                         MaxWidth="300"
                                         ToolTip="{Binding CustomerName}"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Enhanced Summary Card -->
                        <md:Card Grid.Column="1"
                                Background="White"
                                md:ElevationAssist.Elevation="Dp3"
                                UniformCornerRadius="12"
                                Padding="20,16"
                                Margin="0,0,20,0"
                                VerticalAlignment="Center">
                            <md:Card.RenderTransform>
                                <TranslateTransform/>
                            </md:Card.RenderTransform>

                            <StackPanel MinWidth="180">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <md:PackIcon Kind="CurrencyUsd"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="{DynamicResource TotalAmount}"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Text="{Binding TotalAmount, Mode=OneWay, StringFormat={}{0:N2} DA}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         FontWeight="Bold"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,8"/>

                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <md:PackIcon Kind="FileDocument"
                                               Width="16" Height="16"
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding Sales.Count, StringFormat={}{0} Invoices}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </StackPanel>
                        </md:Card>

                        <!-- Enhanced Close Button -->
                        <Button Grid.Column="2"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Click="CancelButton_Click"
                                ToolTip="{DynamicResource Close}"
                                Width="40" Height="40"
                                VerticalAlignment="Center">
                            <Button.Background>
                                <SolidColorBrush Color="White" Opacity="0.2"/>
                            </Button.Background>
                            <Button.RenderTransform>
                                <ScaleTransform/>
                            </Button.RenderTransform>
                            <md:PackIcon Kind="Close"
                                       Width="20" Height="20"
                                       Foreground="White"/>
                        </Button>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced Content Section -->
            <ScrollViewer Grid.Row="1"
                         Background="{DynamicResource MaterialDesignPaper}"
                         Margin="24,16"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         MaxHeight="600">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*" MinHeight="200"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                <!-- Enhanced Invoices Header with Modern Controls -->
                <md:Card Background="{DynamicResource MaterialDesignBackground}"
                        md:ElevationAssist.Elevation="Dp1"
                        UniformCornerRadius="8"
                        Padding="20,16"
                        Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Title Section -->
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                            <md:PackIcon Kind="FileDocumentMultiple"
                                       Width="24" Height="24"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                            <TextBlock Text="{DynamicResource Invoices}"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     FontWeight="SemiBold"
                                     VerticalAlignment="Center"/>
                            <Border Background="{DynamicResource PrimaryHueMidBrush}"
                                  CornerRadius="12"
                                  Padding="8,4"
                                  Margin="12,0,0,0"
                                  VerticalAlignment="Center">
                                <TextBlock Text="{Binding Sales.Count}"
                                         Foreground="White"
                                         FontSize="12"
                                         FontWeight="Medium"/>
                            </Border>
                        </StackPanel>

                        <!-- Enhanced Control Panel -->
                        <StackPanel Grid.Column="1"
                                  Orientation="Horizontal"
                                  VerticalAlignment="Center">
                            <!-- Print Button with Enhanced Styling -->
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Click="PrintAllSaleDetails_Click"
                                    ToolTip="{DynamicResource PrintAllInvoices}"
                                    Height="40"
                                    md:ButtonAssist.CornerRadius="8"
                                    BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                    Foreground="{DynamicResource PrimaryHueMidBrush}"
                                    Margin="0,0,12,0">
                                <Button.RenderTransform>
                                    <ScaleTransform/>
                                </Button.RenderTransform>
                                <StackPanel Orientation="Horizontal">
                                    <md:PackIcon Kind="Printer"
                                                Width="18" Height="18"
                                                Margin="0,0,8,0"
                                                VerticalAlignment="Center"/>
                                    <TextBlock Text="{DynamicResource PrintAllInvoices}"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Enhanced Search Box -->
                            <md:Card md:ElevationAssist.Elevation="Dp1"
                                   UniformCornerRadius="8"
                                   Margin="0,0,12,0">
                                <TextBox x:Name="SearchBox"
                                        Width="250"
                                        Height="40"
                                        Style="{StaticResource MaterialDesignFilledTextBox}"
                                        md:HintAssist.Hint="{DynamicResource SearchInvoices}"
                                        md:TextFieldAssist.HasClearButton="True"
                                        md:TextFieldAssist.LeadingIcon="Magnify"
                                        md:TextFieldAssist.LeadingIconSize="18"
                                        BorderThickness="0"
                                        Background="Transparent"
                                        TabIndex="1"
                                        AutomationProperties.Name="Search Invoices"
                                        AutomationProperties.HelpText="Type to search invoices by number or customer name"/>
                            </md:Card>

                            <!-- Enhanced Sort ComboBox -->
                            <md:Card md:ElevationAssist.Elevation="Dp1"
                                   UniformCornerRadius="8">
                                <ComboBox x:Name="SortComboBox"
                                        Width="160"
                                        Height="40"
                                        Style="{StaticResource MaterialDesignFilledComboBox}"
                                        md:HintAssist.Hint="{DynamicResource SortBy}"
                                        BorderThickness="0"
                                        Background="Transparent"
                                        TabIndex="2"
                                        AutomationProperties.Name="Sort Invoices"
                                        AutomationProperties.HelpText="Select how to sort the invoice list">
                                    <ComboBoxItem>
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="CalendarClock" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="{DynamicResource Date}"/>
                                        </StackPanel>
                                    </ComboBoxItem>
                                    <ComboBoxItem>
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="CurrencyUsd" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="{DynamicResource Amount}"/>
                                        </StackPanel>
                                    </ComboBoxItem>
                                    <ComboBoxItem>
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="CalendarAlert" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="{DynamicResource DueDate}"/>
                                        </StackPanel>
                                    </ComboBoxItem>
                                </ComboBox>
                            </md:Card>
                        </StackPanel>
                    </Grid>
                </md:Card>

                <!-- Enhanced Invoices List with Modern Styling -->
                <md:Card Grid.Row="1"
                        Background="White"
                        md:ElevationAssist.Elevation="Dp2"
                        UniformCornerRadius="12"
                        Margin="0,0,0,16"
                        MinHeight="200"
                        MaxHeight="400">
                    <DataGrid x:Name="SalesDataGrid"
                             RowStyle="{StaticResource DataGridRowStyle}"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="None"
                             BorderThickness="0"
                             Background="Transparent"
                             RowBackground="Transparent"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             HeadersVisibility="Column"
                             md:DataGridAssist.CellPadding="12,8"
                             md:DataGridAssist.ColumnHeaderPadding="12,8"
                             RowHeight="56"
                             Margin="8"
                             ScrollViewer.CanContentScroll="True"
                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                             ScrollViewer.HorizontalScrollBarVisibility="Auto">
                        <DataGrid.Resources>
                            <!-- Enhanced Column Header Style -->
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#FAFAFA" Offset="0.0"/>
                                            <GradientStop Color="#E0E0E0" Offset="1.0"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="BorderThickness" Value="0,0,0,2"/>
                                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                            </Style>

                            <!-- Enhanced Cell Style -->
                            <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <!-- Enhanced Invoice Number Column -->
                            <DataGridTemplateColumn Header="{DynamicResource InvoiceNumber}" Width="200" MinWidth="160">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <Border Background="{DynamicResource PrimaryHueMidBrush}"
                                                  CornerRadius="4"
                                                  Padding="6,2"
                                                  Margin="0,0,8,0">
                                                <md:PackIcon Kind="FileDocument"
                                                           Width="14" Height="14"
                                                           Foreground="White"/>
                                            </Border>
                                            <TextBlock Text="{Binding InvoiceNumber}"
                                                     FontWeight="Medium"
                                                     VerticalAlignment="Center"
                                                     TextTrimming="CharacterEllipsis"
                                                     ToolTip="{Binding InvoiceNumber}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Enhanced Date Column -->
                            <DataGridTemplateColumn Header="{DynamicResource Date}" Width="140" MinWidth="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel VerticalAlignment="Center">
                                            <TextBlock Text="{Binding SaleDate, StringFormat={}{0:dd/MM/yyyy}}"
                                                     FontWeight="Medium"
                                                     FontSize="13"/>
                                            <TextBlock Text="{Binding SaleDate, StringFormat={}{0:HH:mm}}"
                                                     FontSize="11"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Enhanced Due Date Column with Status Indicator -->
                            <DataGridTemplateColumn Header="{DynamicResource DueDate}" Width="150" MinWidth="130">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <Border CornerRadius="8" Padding="4,2" Margin="0,0,8,0">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                                <Setter Property="Background" Value="#FFEBEE"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <md:PackIcon Width="12" Height="12">
                                                    <md:PackIcon.Style>
                                                        <Style TargetType="md:PackIcon">
                                                            <Setter Property="Kind" Value="Calendar"/>
                                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                                    <Setter Property="Kind" Value="CalendarAlert"/>
                                                                    <Setter Property="Foreground" Value="#D32F2F"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </md:PackIcon.Style>
                                                </md:PackIcon>
                                            </Border>
                                            <TextBlock VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Text" Value="{Binding DueDate, StringFormat={}{0:dd/MM/yyyy}}"/>
                                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Enhanced Amount Column -->
                            <DataGridTemplateColumn Header="{DynamicResource Amount}" Width="140" MinWidth="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel HorizontalAlignment="Right" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding GrandTotal, StringFormat={}{0:N2} DA}"
                                                     FontWeight="SemiBold"
                                                     FontSize="13"
                                                     HorizontalAlignment="Right"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Enhanced Remaining Amount Column -->
                            <DataGridTemplateColumn Header="{DynamicResource RemainingAmount}" Width="*" MinWidth="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#FFEBEE"
                                              CornerRadius="6"
                                              Padding="8,4"
                                              HorizontalAlignment="Right">
                                            <TextBlock Text="{Binding RemainingAmount, Mode=OneWay, StringFormat={}{0:N2} DA}"
                                                     Foreground="#D32F2F"
                                                     FontWeight="Bold"
                                                     FontSize="12"
                                                     HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Enhanced Action Column -->
                            <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="80" MinWidth="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                Click="ShowInvoiceDetails_Click"
                                                CommandParameter="{Binding}"
                                                ToolTip="{DynamicResource Details}"
                                                Width="36" Height="36"
                                                HorizontalAlignment="Center">
                                            <Button.RenderTransform>
                                                <ScaleTransform/>
                                            </Button.RenderTransform>
                                            <md:PackIcon Kind="Eye"
                                                       Width="18" Height="18"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        </Button>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </md:Card>

                <!-- Enhanced Payment Input Section -->
                <md:Card Grid.Row="2"
                        Background="White"
                        md:ElevationAssist.Elevation="Dp3"
                        UniformCornerRadius="12"
                        Margin="0,0,0,0">
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Payment Section Header -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                            <md:PackIcon Kind="CreditCardOutline"
                                       Width="24" Height="24"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                            <TextBlock Text="{DynamicResource PaymentConfiguration}"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     FontWeight="SemiBold"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Enhanced Partial Payment Toggle -->
                        <md:Card Grid.Row="1"
                                Background="{DynamicResource MaterialDesignBackground}"
                                md:ElevationAssist.Elevation="Dp1"
                                UniformCornerRadius="8"
                                Padding="20,16"
                                Margin="0,0,0,20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Toggle Switch -->
                                <ToggleButton x:Name="PartialPaymentToggle"
                                            Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                            ToolTip="{DynamicResource EnablePartialPayment}"
                                            VerticalAlignment="Center"
                                            Checked="PartialPaymentToggle_CheckedChanged"
                                            Unchecked="PartialPaymentToggle_CheckedChanged"
                                            TabIndex="3"
                                            AutomationProperties.Name="Enable Partial Payment"
                                            AutomationProperties.HelpText="Toggle to enable partial payment mode"/>

                                <!-- Content -->
                                <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                        <md:PackIcon Kind="CashSync"
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                                        <TextBlock Text="{DynamicResource PartialPayment}"
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 FontWeight="SemiBold"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                    <TextBlock Text="{DynamicResource EnablePartialPaymentDescription}"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- Status Badge -->
                                <Border Grid.Column="2"
                                      CornerRadius="12"
                                      Padding="12,6"
                                      VerticalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsChecked, ElementName=PartialPaymentToggle}" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock FontSize="12" FontWeight="Medium">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="Disabled"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChecked, ElementName=PartialPaymentToggle}" Value="True">
                                                        <Setter Property="Text" Value="Enabled"/>
                                                        <Setter Property="Foreground" Value="White"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </Grid>
                        </md:Card>

                        <!-- Responsive Payment Input Controls -->
                        <Grid Grid.Row="2" x:Name="PaymentControlsGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" MinWidth="280"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*" MinWidth="220"/>
                            </Grid.ColumnDefinitions>

                            <!-- Amount Input Section -->
                            <md:Card Grid.Column="0"
                                   Background="{DynamicResource MaterialDesignBackground}"
                                   md:ElevationAssist.Elevation="Dp1"
                                   UniformCornerRadius="8"
                                   Padding="16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                        <md:PackIcon Kind="CurrencyUsd"
                                                   Width="18" Height="18"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                                        <TextBlock Text="{DynamicResource PaymentAmount}"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 FontWeight="Medium"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <TextBox x:Name="PaymentAmountInput"
                                           Style="{StaticResource MaterialDesignFilledTextBox}"
                                           md:HintAssist.Hint="{DynamicResource PaymentAmount}"
                                           md:TextFieldAssist.SuffixText="DA"
                                           Text="{Binding TotalAmount, Mode=OneTime, StringFormat={}{0:N2}}"
                                           FontSize="16"
                                           FontWeight="SemiBold"
                                           Height="50"
                                           TabIndex="4"
                                           AutomationProperties.Name="Payment Amount"
                                           AutomationProperties.HelpText="Enter the amount to pay in Algerian Dinars"/>

                                    <!-- Remaining Amount Display -->
                                    <Border x:Name="RemainingAmountBorder"
                                          Background="#FFEBEE"
                                          CornerRadius="6"
                                          Padding="12,8"
                                          Margin="0,12,0,0"
                                          Visibility="Collapsed">
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="AlertCircle"
                                                       Width="16" Height="16"
                                                       Foreground="#D32F2F"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                            <TextBlock x:Name="RemainingAmountText"
                                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                     VerticalAlignment="Center">
                                                <Run Text="{DynamicResource RemainingAmount}"/>
                                                <Run Text=": "/>
                                                <Run x:Name="RemainingAmountValue"
                                                     Foreground="#D32F2F"
                                                     FontWeight="Bold"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </md:Card>

                            <!-- Payment Method Section -->
                            <md:Card Grid.Column="2"
                                   Background="{DynamicResource MaterialDesignBackground}"
                                   md:ElevationAssist.Elevation="Dp1"
                                   UniformCornerRadius="8"
                                   Padding="16"
                                   Margin="0,0,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                        <md:PackIcon Kind="CreditCard"
                                                   Width="18" Height="18"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                                        <TextBlock Text="{DynamicResource PaymentMethod}"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 Foreground="{DynamicResource MaterialDesignBody}"
                                                 FontWeight="Medium"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <ComboBox x:Name="PaymentMethodComboBox"
                                            Style="{StaticResource MaterialDesignFilledComboBox}"
                                            md:HintAssist.Hint="{DynamicResource PaymentMethod}"
                                            Height="50"
                                            TabIndex="5"
                                            AutomationProperties.Name="Payment Method"
                                            AutomationProperties.HelpText="Select the payment method for this transaction">
                                        <ComboBoxItem>
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon Kind="Cash" Width="18" Height="18" Margin="0,0,8,0"/>
                                                <TextBlock Text="{DynamicResource Cash}" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </ComboBoxItem>
                                        <ComboBoxItem>
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon Kind="CreditCard" Width="18" Height="18" Margin="0,0,8,0"/>
                                                <TextBlock Text="{DynamicResource Card}" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </ComboBoxItem>
                                    </ComboBox>
                                </StackPanel>
                            </md:Card>
                        </Grid>
                    </Grid>
                </md:Card>
                </Grid>
            </ScrollViewer>

            <!-- Enhanced Footer Section -->
            <Grid Grid.Row="2">
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#FAFAFA" Offset="0.0"/>
                        <GradientStop Color="#E0E0E0" Offset="1.0"/>
                    </LinearGradientBrush>
                </Grid.Background>

                <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="0,1,0,0"
                        CornerRadius="0,0,12,12">
                    <Grid Margin="24,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Payment Summary -->
                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Information"
                                           Width="18" Height="18"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource ReadyToProcessPaymentFor}"
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,4,0"/>
                                <TextBlock Text="{Binding CustomerName}"
                                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         FontWeight="SemiBold"
                                         VerticalAlignment="Center"
                                         TextTrimming="CharacterEllipsis"
                                         MaxWidth="200"
                                         ToolTip="{Binding CustomerName}"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="1"
                                  Orientation="Horizontal"
                                  VerticalAlignment="Center">
                            <!-- Cancel Button -->
                            <Button Click="CancelButton_Click"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Height="45"
                                    MinWidth="120"
                                    md:ButtonAssist.CornerRadius="8"
                                    BorderBrush="{DynamicResource MaterialDesignBodyLight}"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    Margin="0,0,16,0"
                                    TabIndex="6"
                                    AutomationProperties.Name="Cancel"
                                    AutomationProperties.HelpText="Cancel the batch payment operation and close the dialog">
                                <Button.RenderTransform>
                                    <ScaleTransform/>
                                </Button.RenderTransform>
                                <StackPanel Orientation="Horizontal">
                                    <md:PackIcon Kind="Close"
                                               Width="18" Height="18"
                                               Margin="0,0,8,0"
                                               VerticalAlignment="Center"/>
                                    <TextBlock Text="{DynamicResource Cancel}"
                                             VerticalAlignment="Center"
                                             FontWeight="Medium"/>
                                </StackPanel>
                            </Button>

                            <!-- Process Payment Button -->
                            <Button Click="ProcessButton_Click"
                                    Height="45"
                                    MinWidth="160"
                                    md:ButtonAssist.CornerRadius="8"
                                    FontWeight="SemiBold"
                                    TabIndex="7"
                                    IsDefault="True"
                                    AutomationProperties.Name="Process Payment"
                                    AutomationProperties.HelpText="Process the batch payment for all selected invoices">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                    <GradientStop Color="#0078D4" Offset="0.0"/>
                                                    <GradientStop Color="#0067B5" Offset="1.0"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="md:ElevationAssist.Elevation" Value="Dp3"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="md:ElevationAssist.Elevation" Value="Dp5"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <Button.RenderTransform>
                                    <ScaleTransform/>
                                </Button.RenderTransform>
                                <StackPanel Orientation="Horizontal">
                                    <md:PackIcon Kind="CheckCircle"
                                               Width="20" Height="20"
                                               Margin="0,0,8,0"
                                               VerticalAlignment="Center"/>
                                    <TextBlock Text="{DynamicResource ProcessPayment}"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>

            <!-- Sale Details Popup with enhanced visuals -->
            <Popup x:Name="saleDetailsPopup" 
                   StaysOpen="False"
                   AllowsTransparency="True"
                   Placement="Center"
                   PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Window}}">
                <md:Card Background="{DynamicResource MaterialDesignBackground}"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        UniformCornerRadius="8"
                        md:ElevationAssist.Elevation="Dp4"
                        Width="800"
                        Margin="20">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                Margin="24">
                        <StackPanel>
                            <TextBlock Text="{DynamicResource SaleDetails}" 
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Margin="0,0,0,24"/>
                            
                            <!-- Sale Info -->
                            <md:Card Background="{DynamicResource MaterialDesignSurfaceBackground}"
                                    Foreground="{DynamicResource MaterialDesignBody}"
                                    UniformCornerRadius="4"
                                    Margin="0,0,0,24">
                                <Grid Margin="16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Text="{DynamicResource InvoiceNumber}" 
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding InvoiceNumber}" 
                                             Grid.Column="1"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Margin="16,0,0,8"/>
                                    
                                    <TextBlock Text="{DynamicResource SaleDate}" 
                                             Grid.Row="1"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding SaleDate, StringFormat=g}" 
                                             Grid.Row="1" 
                                             Grid.Column="1"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Margin="16,0,0,8"/>
                                    
                                    <TextBlock Text="{DynamicResource DueDate}" 
                                             Grid.Row="2"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding DueDate, StringFormat=d, TargetNullValue='-'}" 
                                             Grid.Row="2" 
                                             Grid.Column="1"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Margin="16,0,0,8"/>
                                    
                                    <TextBlock Text="{DynamicResource Customer}" 
                                             Grid.Row="3"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Medium"/>
                                    <TextBlock Grid.Row="3" 
                                             Grid.Column="1"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             Margin="16,0,0,8">
                                        <Run Text="{Binding Customer.FirstName, Mode=OneWay}"/>
                                        <Run Text=" "/>
                                        <Run Text="{Binding Customer.LastName, Mode=OneWay}"/>
                                    </TextBlock>
                                </Grid>
                            </md:Card>

                            <!-- Items List -->
                            <TextBlock Text="{DynamicResource SaleItems}" 
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Margin="0,0,0,16"/>

                            <DataGrid ItemsSource="{Binding Items}"
                                     Style="{StaticResource MaterialDesignDataGrid}"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True"
                                     HeadersVisibility="Column"
                                     GridLinesVisibility="Horizontal"
                                     BorderThickness="1"
                                     BorderBrush="{DynamicResource MaterialDesignDivider}"
                                     Background="Transparent"
                                     RowBackground="{DynamicResource MaterialDesignPaper}"
                                     AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Margin="0,0,0,24"
                                     Height="300">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}" 
                                                      Binding="{Binding Product.Name}" 
                                                      Width="*"/>
                                    <DataGridTextColumn Header="{DynamicResource Quantity}" 
                                                      Binding="{Binding Quantity}" 
                                                      Width="100"/>
                                    <DataGridTextColumn Header="{DynamicResource UnitPrice}" 
                                                      Binding="{Binding UnitPrice, StringFormat={}{0:N2} DA}" 
                                                      Width="120"/>
                                    <DataGridTextColumn Header="{DynamicResource Total}" 
                                                      Binding="{Binding Total, StringFormat={}{0:N2} DA}" 
                                                      Width="120"/>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- Totals -->
                            <md:Card Background="{DynamicResource MaterialDesignSurfaceBackground}"
                                    Foreground="{DynamicResource MaterialDesignBody}"
                                    UniformCornerRadius="4"
                                    Margin="0,0,0,24">
                                <Grid Margin="16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Text="{DynamicResource Subtotal}" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <TextBlock Text="{Binding Subtotal, StringFormat={}{0:N2} DA}" 
                                             Grid.Column="1" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>

                                    <TextBlock Text="{DynamicResource Discount}" 
                                             Grid.Row="1" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <TextBlock Text="{Binding DiscountAmount, StringFormat={}{0:N2} DA}" 
                                             Grid.Row="1" 
                                             Grid.Column="1" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>

                                    <TextBlock Text="{DynamicResource Tax}" 
                                             Grid.Row="2" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>
                                    <TextBlock Text="{Binding TaxAmount, StringFormat={}{0:N2} DA}" 
                                             Grid.Row="2" 
                                             Grid.Column="1" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"/>

                                    <TextBlock Text="{DynamicResource GrandTotal}" 
                                             Grid.Row="3" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="{Binding GrandTotal, StringFormat={}{0:N2} DA}" 
                                             Grid.Row="3" 
                                             Grid.Column="1" 
                                             HorizontalAlignment="Right"
                                             Foreground="{DynamicResource MaterialDesignBody}"
                                             FontWeight="Bold"/>

                                    <TextBlock Text="{DynamicResource RemainingAmount}" 
                                             Grid.Row="4" 
                                             HorizontalAlignment="Right"
                                             FontWeight="Bold"
                                             Foreground="#D32F2F"/>
                                    <TextBlock Grid.Row="4" 
                                             Grid.Column="1" 
                                             HorizontalAlignment="Right"
                                             FontWeight="Bold"
                                             Foreground="#D32F2F">
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="{}{0:N2} DA">
                                                <Binding Path="RemainingAmount" Mode="OneWay"/>
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                </Grid>
                            </md:Card>

                            <!-- Actions -->
                            <StackPanel Orientation="Horizontal"
                                      HorizontalAlignment="Right">
                                <Button Content="{DynamicResource PrintSaleDetails}"
                                        Click="PrintSaleDetails_Click"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Foreground="{DynamicResource MaterialDesignBody}"
                                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                                        Margin="0,0,8,0">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon Kind="Printer" 
                                                            Margin="0,0,8,0"
                                                            VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding}"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                                <Button Content="{DynamicResource Close}"
                                        Click="CloseDetails_Click"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        Foreground="{DynamicResource MaterialDesignBody}"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </md:Card>
            </Popup>
        </Grid>
    </md:Card>
    </md:DialogHost>
</Window>
