using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using System.Timers;
using System.Windows.Controls.Primitives;
using System.Windows.Input;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Helper class for UI performance optimizations
    /// </summary>
    public static class UIOptimizationHelper
    {
        /// <summary>
        /// Apply virtualization settings to a ScrollViewer and its child controls
        /// </summary>
        public static void ApplyVirtualizationSettings(DependencyObject element)
        {
            if (element == null) return;

            // Apply virtualization settings to ScrollViewers
            if (element is ScrollViewer scrollViewer)
            {
                scrollViewer.IsDeferredScrollingEnabled = true;
                scrollViewer.CanContentScroll = true;
                
                // Add smooth scrolling for touch screens
                if (System.Windows.Input.Tablet.TabletDevices.Count > 0)
                {
                    scrollViewer.PanningMode = PanningMode.VerticalFirst;
                }
            }
            
            // Apply virtualization to ItemsControls
            if (element is ItemsControl itemsControl)
            {
                VirtualizingPanel.SetIsVirtualizing(itemsControl, true);
                VirtualizingPanel.SetVirtualizationMode(itemsControl, VirtualizationMode.Recycling);
                VirtualizingPanel.SetCacheLength(itemsControl, new VirtualizationCacheLength(1, 2));
                VirtualizingPanel.SetCacheLengthUnit(itemsControl, VirtualizationCacheLengthUnit.Page);
                VirtualizingPanel.SetScrollUnit(itemsControl, ScrollUnit.Pixel);
                
                // Apply bitmap caching
                if (itemsControl.CacheMode == null)
                {
                    itemsControl.CacheMode = new BitmapCache 
                    { 
                        EnableClearType = true, 
                        SnapsToDevicePixels = true,
                        RenderAtScale = 1.0
                    };
                }
            }
            
            // Apply bitmap caching to controls that support it
            if (element is UIElement uiElement && !(element is Panel) && uiElement.CacheMode == null)
            {
                // Only cache controls that don't change frequently
                if (!(element is TextBox) && !(element is ComboBox) && !(element is Selector))
                {
                    uiElement.CacheMode = new BitmapCache 
                    { 
                        EnableClearType = true, 
                        SnapsToDevicePixels = true 
                    };
                }
            }
            
            // Apply to all children
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(element, i);
                ApplyVirtualizationSettings(child);
            }
        }
        
        /// <summary>
        /// Create a debounced action that delays execution until input has stopped
        /// </summary>
        public static Action<T> Debounce<T>(Action<T> action, int milliseconds = 300)
        {
            System.Timers.Timer timer = null;
            
            return arg =>
            {
                timer?.Stop();
                timer?.Dispose();
                
                timer = new System.Timers.Timer(milliseconds)
                {
                    AutoReset = false
                };
                
                timer.Elapsed += (sender, _) =>
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        action(arg);
                        timer.Dispose();
                    });
                };
                
                timer.Start();
            };
        }
        
        /// <summary>
        /// Apply high-performance rendering settings to a framework element
        /// </summary>
        public static void ApplyHighPerformanceRendering(FrameworkElement element)
        {
            if (element == null) return;
            
            // Apply layout and rendering optimizations
            TextOptions.SetTextFormattingMode(element, TextFormattingMode.Ideal);
            TextOptions.SetTextRenderingMode(element, TextRenderingMode.ClearType);
            element.UseLayoutRounding = true;
            element.SnapsToDevicePixels = true;
            RenderOptions.SetClearTypeHint(element, ClearTypeHint.Enabled);
            RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
            RenderOptions.SetCachingHint(element, CachingHint.Cache);
            
            // Apply to all children
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                if (VisualTreeHelper.GetChild(element, i) is FrameworkElement child)
                {
                    ApplyHighPerformanceRendering(child);
                }
            }
        }
        
        /// <summary>
        /// Batch update UI elements in chunks to prevent UI freezing
        /// </summary>
        public static async void BatchProcessItems<T>(System.Collections.Generic.IEnumerable<T> items, 
                                                     Action<T> processAction, 
                                                     int batchSize = 10, 
                                                     DispatcherPriority priority = DispatcherPriority.Background)
        {
            var enumerator = items.GetEnumerator();
            bool hasMore = true;
            
            while (hasMore)
            {
                var batch = new System.Collections.Generic.List<T>(batchSize);
                
                for (int i = 0; i < batchSize && (hasMore = enumerator.MoveNext()); i++)
                {
                    batch.Add(enumerator.Current);
                }
                
                if (batch.Count > 0)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        foreach (var item in batch)
                        {
                            processAction(item);
                        }
                        
                        // Allow UI to update between batches
                        Dispatcher.CurrentDispatcher.Invoke(() => { }, DispatcherPriority.Background);
                    }, priority);
                }
            }
        }
    }
} 