using Microsoft.Data.Sqlite;
using System.IO;

namespace DatabasePasswordFixer
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("POS System Database Password Fix Utility");
            Console.WriteLine("=========================================");

            // Get the database path from the user or use the default path
            string defaultDbPath = @"D:\Programs\Programming Projects\Ai Projects\POSSystem\bin\Debug\net8.0-windows\pos.db";
            string databasePath;

            if (args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
            {
                databasePath = args[0];
                Console.WriteLine($"\nUsing provided database path: {databasePath}");
            }
            else
            {
                Console.WriteLine($"\nUsing default database path: {defaultDbPath}");
                Console.WriteLine("Press Enter to continue with this path or input a new path:");
                string? input = Console.ReadLine();
                databasePath = string.IsNullOrWhiteSpace(input) ? defaultDbPath : input;
            }

            if (!File.Exists(databasePath))
            {
                Console.WriteLine($"Error: Database file not found at {databasePath}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return;
            }

            try
            {
                string connectionString = $"Data Source={databasePath}";
                using (var connection = new SqliteConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("Database connection opened successfully.");

                    // First get all users
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT Id, Username, Password FROM Users";
                        using (var reader = command.ExecuteReader())
                        {
                            Console.WriteLine("\nCurrent user accounts:");
                            Console.WriteLine("ID | Username | Password (first 10 chars)");
                            Console.WriteLine("----------------------------------------");
                            
                            while (reader.Read())
                            {
                                int id = reader.GetInt32(0);
                                string username = reader.GetString(1);
                                string password = reader.GetString(2);
                                string passwordPreview = password.Length > 10 ? password.Substring(0, 10) + "..." : password;
                                
                                Console.WriteLine($"{id} | {username} | {passwordPreview}");
                            }
                        }
                    }

                    // Find all passwords that don't have BCrypt format
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT Id, Username, Password FROM Users WHERE Password NOT LIKE '$2a$%' AND Password NOT LIKE '$2b$%' AND Password NOT LIKE '$2y$%'";
                        using (var reader = command.ExecuteReader())
                        {
                            var usersToUpdate = new List<(int Id, string Username, string Password)>();
                            
                            while (reader.Read())
                            {
                                usersToUpdate.Add((
                                    reader.GetInt32(0),
                                    reader.GetString(1),
                                    reader.GetString(2)
                                ));
                            }

                            Console.WriteLine($"\nFound {usersToUpdate.Count} user(s) with non-hashed passwords.");

                            if (usersToUpdate.Count > 0)
                            {
                                Console.WriteLine("\nDo you want to update these passwords? (Y/N)");
                                string? response = Console.ReadLine();
                                
                                if (string.IsNullOrEmpty(response) || response.ToUpper() != "Y")
                                {
                                    Console.WriteLine("Operation cancelled by user.");
                                    return;
                                }

                                // Process each user
                                foreach (var user in usersToUpdate)
                                {
                                    Console.WriteLine($"Processing user: {user.Username}");
                                    string originalPassword = user.Password;
                                    
                                    // Hash the password with BCrypt
                                    string hashedPassword;
                                    if (user.Username.ToLower() == "admin")
                                    {
                                        // For admin, set a known password: "admin123"
                                        hashedPassword = BCrypt.Net.BCrypt.HashPassword("admin123", 12);
                                        Console.WriteLine("Setting admin password to 'admin123'");
                                    }
                                    else
                                    {
                                        // For other users, hash their existing password
                                        hashedPassword = BCrypt.Net.BCrypt.HashPassword(originalPassword, 12);
                                        Console.WriteLine($"Hashing existing password: {originalPassword}");
                                    }

                                    // Update the password in the database
                                    using (var updateCmd = connection.CreateCommand())
                                    {
                                        updateCmd.CommandText = "UPDATE Users SET Password = @Password, UpdatedAt = @UpdatedAt WHERE Id = @Id";
                                        updateCmd.Parameters.AddWithValue("@Password", hashedPassword);
                                        updateCmd.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("s"));
                                        updateCmd.Parameters.AddWithValue("@Id", user.Id);
                                        
                                        int rowsAffected = updateCmd.ExecuteNonQuery();
                                        Console.WriteLine($"Updated {rowsAffected} row(s) for user {user.Username}");
                                    }
                                }

                                Console.WriteLine("\nPassword update completed successfully!");
                                Console.WriteLine("\nAdmin user credentials updated:");
                                Console.WriteLine("Username: admin");
                                Console.WriteLine("Password: admin123");
                            }
                            else
                            {
                                Console.WriteLine("No users need password updates, all are already using BCrypt hashes.");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
} 