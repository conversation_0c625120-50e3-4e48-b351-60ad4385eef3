<Window x:Class="POSSystem.Views.Dialogs.PerformanceAnalysisDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="SaleViewGrid Performance Analysis"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="SaleViewGrid Performance Analysis"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Comprehensive performance testing and optimization validation"
                       Style="{StaticResource MaterialDesignBody2TextBlock}"
                       HorizontalAlignment="Center"
                       Opacity="0.7"/>
        </StackPanel>

        <!-- Performance Score Card -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Overall Score" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                    <TextBlock x:Name="ScoreText" Text="--/100" 
                               Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>

                <Separator Grid.Column="1" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="20,0"/>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="Performance Grade" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                    <TextBlock x:Name="GradeText" Text="--" 
                               Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                               Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Test Results -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
            
            <!-- Load Time Tests -->
            <TabItem Header="Load Performance">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="Load Time Performance Tests" 
                                   Style="{StaticResource MaterialDesignSubtitle1TextBlock}" 
                                   Margin="0,0,0,10"/>
                        <DataGrid x:Name="LoadTimeGrid" 
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  CanUserSortColumns="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Products" Binding="{Binding ProductCount}" Width="80"/>
                                <DataGridTextColumn Header="Total Time (ms)" Binding="{Binding TotalLoadTime}" Width="120"/>
                                <DataGridTextColumn Header="Collection Update (ms)" Binding="{Binding CollectionUpdateTime}" Width="150"/>
                                <DataGridTextColumn Header="Memory Increase (MB)" Binding="{Binding MemoryIncrease, StringFormat=F1}" Width="150"/>
                                <DataGridTextColumn Header="Rating" Binding="{Binding PerformanceRating}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Grid Layout Tests -->
            <TabItem Header="Grid Layout">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="Grid Layout Optimization Tests" 
                                   Style="{StaticResource MaterialDesignSubtitle1TextBlock}" 
                                   Margin="0,0,0,10"/>
                        <DataGrid x:Name="GridLayoutGrid" 
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  CanUserSortColumns="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Scenario" Binding="{Binding Scenario}" Width="120"/>
                                <DataGridTextColumn Header="Products" Binding="{Binding ProductCount}" Width="80"/>
                                <DataGridTextColumn Header="Columns" Binding="{Binding ActualColumns}" Width="80"/>
                                <DataGridTextColumn Header="Spacing Ratio" Binding="{Binding SpacingRatio, StringFormat=F2}" Width="100"/>
                                <DataGridTextColumn Header="Calc Time (ms)" Binding="{Binding CalculationTime}" Width="120"/>
                                <DataGridTextColumn Header="Quality" Binding="{Binding SpacingQuality}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Memory and Performance -->
            <TabItem Header="Memory and Performance">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="Memory Usage and Performance Metrics" 
                                   Style="{StaticResource MaterialDesignSubtitle1TextBlock}" 
                                   Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Memory Usage -->
                            <materialDesign:Card Grid.Row="0" Grid.Column="0" Margin="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="Memory Usage" Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                    <TextBlock x:Name="MemoryUsageText" Text="--" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- UI Responsiveness -->
                            <materialDesign:Card Grid.Row="0" Grid.Column="1" Margin="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="UI Responsiveness" Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                    <TextBlock x:Name="ResponsivenessText" Text="--" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Virtualization -->
                            <materialDesign:Card Grid.Row="1" Grid.Column="0" Margin="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="Virtualization" Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                    <TextBlock x:Name="VirtualizationText" Text="--" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Memory Leaks -->
                            <materialDesign:Card Grid.Row="1" Grid.Column="1" Margin="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="Memory Leak Risk" Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                    <TextBlock x:Name="MemoryLeakText" Text="--" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <!-- Recommendations -->
                        <Expander Header="Recommendations" Margin="0,20,0,0" IsExpanded="True">
                            <ListBox x:Name="RecommendationsList" 
                                     Background="Transparent"
                                     BorderThickness="0">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="0,5">
                                            <materialDesign:PackIcon Kind="Lightbulb" 
                                                                   VerticalAlignment="Top" 
                                                                   Margin="0,0,10,0"
                                                                   Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                            <TextBlock Text="{Binding}" 
                                                       TextWrapping="Wrap"
                                                       Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Expander>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="RunAnalysisButton" 
                    Content="Run Analysis" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0"
                    Click="RunAnalysisButton_Click"/>
            <Button x:Name="SaveReportButton" 
                    Content="Save Report" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"
                    IsEnabled="False"
                    Click="SaveReportButton_Click"/>
            <Button Content="Close" 
                    Style="{StaticResource MaterialDesignFlatButton}"
                    IsCancel="True"
                    Click="CloseButton_Click"/>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Grid.RowSpan="4" 
              Background="#80000000" 
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                             IsIndeterminate="True"
                             Width="50" Height="50"/>
                <TextBlock Text="Running Performance Analysis..." 
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="White"
                           Margin="0,20,0,0"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
