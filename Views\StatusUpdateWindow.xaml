<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.StatusUpdateWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Update Order Status" 
        Height="200" 
        Width="420"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Text="Select New Status:" 
                 FontSize="16" 
                 Margin="0,0,0,10"/>

        <ComboBox Grid.Row="1" 
                  x:Name="cboStatus"
                  VerticalAlignment="Top"
                  Margin="0,0,0,20">
            <ComboBoxItem Content="Pending"/>
            <ComboBoxItem Content="Approved"/>
            <ComboBoxItem Content="Received"/>
            <ComboBoxItem Content="Cancelled"/>
        </ComboBox>

        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="Cancel" 
                    Click="Cancel_Click"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Margin="0,0,10,0"
                    Width="80"/>
            <Button Content="Update" 
                    Click="Update_Click"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window> 