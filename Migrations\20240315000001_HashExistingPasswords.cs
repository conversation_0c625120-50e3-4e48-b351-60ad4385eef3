using Microsoft.EntityFrameworkCore.Migrations;
using System;
using BCrypt.Net;

namespace POSSystem.Migrations
{
    public partial class HashExistingPasswords : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create a temporary column for the hashed passwords
            migrationBuilder.AddColumn<string>(
                name: "HashedPassword",
                table: "Users",
                type: "TEXT",
                nullable: true);

            // Update the admin password (default: admin123)
            migrationBuilder.Sql($@"
                UPDATE Users 
                SET HashedPassword = '{BCrypt.Net.BCrypt.HashPassword("admin123", 12)}'
                WHERE Username = 'admin'");

            // Update other users' passwords (if any exist)
            migrationBuilder.Sql(@"
                UPDATE Users 
                SET HashedPassword = Password 
                WHERE Username != 'admin'");

            // Drop the old Password column
            migrationBuilder.DropColumn(
                name: "Password",
                table: "Users");

            // Rename HashedPassword to Password
            migrationBuilder.RenameColumn(
                name: "HashedPassword",
                table: "Users",
                newName: "Password");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Note: We cannot restore the original passwords once they are hashed
            migrationBuilder.Sql("UPDATE Users SET Password = 'RESET_REQUIRED' WHERE 1=1");
        }
    }
} 