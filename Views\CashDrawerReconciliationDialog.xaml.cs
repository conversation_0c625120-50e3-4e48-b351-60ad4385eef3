using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views
{
    public partial class CashDrawerReconciliationDialog : UserControl
    {
        private readonly CashDrawer _cashDrawer;
        private readonly ICashDrawerService _cashDrawerService;
        private decimal _actualCount;

        public CashDrawerReconciliationDialog(CashDrawer cashDrawer, ICashDrawerService cashDrawerService)
        {
            InitializeComponent();
            _cashDrawer = cashDrawer;
            _cashDrawerService = cashDrawerService;
            LoadCashDrawerData();
        }

        private void LoadCashDrawerData()
        {
            OpeningBalanceText.Text = _cashDrawer.OpeningBalance.ToString("C");
            TotalSalesText.Text = _cashDrawerService.GetTotalCashSales(_cashDrawer).ToString("C");
            TotalPayoutsText.Text = _cashDrawerService.GetTotalPayouts(_cashDrawer).ToString("C");
            ExpectedBalanceText.Text = _cashDrawer.ExpectedBalance.ToString("C");

            // Initialize denomination fields to 0
            Hundreds.Text = "0";
            Fifties.Text = "0";
            Twenties.Text = "0";
            Tens.Text = "0";
            Fives.Text = "0";
            Ones.Text = "0";
            Coins.Text = "0.00";

            UpdateTotals();
        }

        private void DenominationCount_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateTotals();
        }

        private void UpdateTotals()
        {
            // Calculate totals for each denomination
            decimal hundredsTotal = ParseDenomination(Hundreds.Text) * 100;
            decimal fiftiesTotal = ParseDenomination(Fifties.Text) * 50;
            decimal twentiesTotal = ParseDenomination(Twenties.Text) * 20;
            decimal tensTotal = ParseDenomination(Tens.Text) * 10;
            decimal fivesTotal = ParseDenomination(Fives.Text) * 5;
            decimal onesTotal = ParseDenomination(Ones.Text);
            decimal coinsTotal = ParseDecimal(Coins.Text);

            // Update total displays
            HundredsTotal.Text = hundredsTotal.ToString("C");
            FiftiesTotal.Text = fiftiesTotal.ToString("C");
            TwentiesTotal.Text = twentiesTotal.ToString("C");
            TensTotal.Text = tensTotal.ToString("C");
            FivesTotal.Text = fivesTotal.ToString("C");
            OnesTotal.Text = onesTotal.ToString("C");
            CoinsTotal.Text = coinsTotal.ToString("C");

            // Calculate and display actual count
            _actualCount = hundredsTotal + fiftiesTotal + twentiesTotal + tensTotal + fivesTotal + onesTotal + coinsTotal;
            ActualCountText.Text = _actualCount.ToString("C");

            // Calculate and display difference
            decimal difference = _actualCount - _cashDrawer.ExpectedBalance;
            DifferenceText.Text = difference.ToString("C");
            DifferenceText.Foreground = difference < 0 ? 
                System.Windows.Media.Brushes.Red : 
                System.Windows.Media.Brushes.Green;
        }

        private int ParseDenomination(string value)
        {
            return int.TryParse(value, out int result) ? result : 0;
        }

        private decimal ParseDecimal(string value)
        {
            return decimal.TryParse(value, out decimal result) ? result : 0m;
        }

        private void SaveReconciliation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _cashDrawer.ActualBalance = _actualCount;
                _cashDrawer.Notes = NotesTextBox.Text;
                _cashDrawer.ClosedAt = DateTime.Now;
                _cashDrawer.Status = "Closed";

                var dialogSession = DialogHost.GetDialogSession(this);
                dialogSession?.Close(_cashDrawer);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving reconciliation: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 