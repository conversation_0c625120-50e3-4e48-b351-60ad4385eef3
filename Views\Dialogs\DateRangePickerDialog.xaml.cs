using System;
using System.Windows;
using System.Windows.Controls;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for DateRangePickerDialog.xaml
    /// </summary>
    public partial class DateRangePickerDialog : Window
    {
        public DateTime StartDate { get; private set; }
        public DateTime EndDate { get; private set; }
        public bool DateRangeSelected { get; private set; }

        public DateRangePickerDialog(DateTime? initialStartDate = null, DateTime? initialEndDate = null)
        {
            InitializeComponent();
            
            // Set initial dates if provided
            if (initialStartDate.HasValue)
            {
                StartDate = initialStartDate.Value;
                StartDatePicker.SelectedDate = StartDate;
            }
            else
            {
                // Default to first day of current month
                StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                StartDatePicker.SelectedDate = StartDate;
            }
            
            if (initialEndDate.HasValue)
            {
                EndDate = initialEndDate.Value;
                EndDatePicker.SelectedDate = EndDate;
            }
            else
            {
                // Default to current date
                EndDate = DateTime.Today;
                EndDatePicker.SelectedDate = EndDate;
            }
            
            // Set window title
            Title = "Select Date Range";
            
            // Set dialog owner to application main window
            Owner = Application.Current.MainWindow;
            
            // Center on owner
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate date range
            if (!StartDatePicker.SelectedDate.HasValue || !EndDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("Please select both start and end dates.", "Invalid Date Range", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            var startDate = StartDatePicker.SelectedDate.Value;
            var endDate = EndDatePicker.SelectedDate.Value;
            
            // Ensure end date is not before start date
            if (endDate < startDate)
            {
                MessageBox.Show("End date cannot be before start date.", "Invalid Date Range", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            // Set properties
            StartDate = startDate;
            EndDate = endDate.AddDays(1).AddSeconds(-1); // Include the entire end day
            DateRangeSelected = true;
            
            // Close dialog with success
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // Close dialog without changes
            DateRangeSelected = false;
            DialogResult = false;
            Close();
        }
        
        private void StartDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // Update minimum date for end date picker
            if (StartDatePicker.SelectedDate.HasValue)
            {
                EndDatePicker.DisplayDateStart = StartDatePicker.SelectedDate.Value;
                
                // If end date is now before start date, update it
                if (EndDatePicker.SelectedDate.HasValue && 
                    EndDatePicker.SelectedDate.Value < StartDatePicker.SelectedDate.Value)
                {
                    EndDatePicker.SelectedDate = StartDatePicker.SelectedDate.Value;
                }
            }
        }
    }
} 