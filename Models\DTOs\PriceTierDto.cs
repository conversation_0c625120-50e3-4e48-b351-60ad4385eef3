using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for managing pricing tiers in the UI.
    /// Provides validation, formatting, and user-friendly properties for pricing tier management.
    /// </summary>
    public class PriceTierDto : INotifyPropertyChanged, IDataErrorInfo
    {
        private int _id;
        private int _productId;
        private decimal _minimumQuantity = 1;
        private decimal? _maximumQuantity;
        private decimal _unitPrice;
        private decimal? _packPrice;
        private string _tierName = string.Empty;
        private string _description = string.Empty;
        private bool _isActive = true;
        private int _displayOrder;
        private DateTime? _effectiveDate;
        private DateTime? _expirationDate;
        private bool _isNew;
        private bool _isModified;

        public event PropertyChangedEventHandler PropertyChanged;

        // Basic Properties
        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(); }
        }

        public int ProductId
        {
            get => _productId;
            set { _productId = value; OnPropertyChanged(); }
        }

        [Required(ErrorMessage = "Minimum quantity is required")]
        [Range(0.001, double.MaxValue, ErrorMessage = "Minimum quantity must be greater than 0")]
        public decimal MinimumQuantity
        {
            get => _minimumQuantity;
            set 
            { 
                _minimumQuantity = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(QuantityRangeDisplay));
                OnPropertyChanged(nameof(IsValidQuantityRange));
                MarkAsModified();
            }
        }

        [Range(0.001, double.MaxValue, ErrorMessage = "Maximum quantity must be greater than 0")]
        public decimal? MaximumQuantity
        {
            get => _maximumQuantity;
            set 
            { 
                _maximumQuantity = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(QuantityRangeDisplay));
                OnPropertyChanged(nameof(IsValidQuantityRange));
                MarkAsModified();
            }
        }

        [Required(ErrorMessage = "Unit price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Unit price must be greater than 0")]
        public decimal UnitPrice
        {
            get => _unitPrice;
            set 
            { 
                _unitPrice = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(EffectiveUnitPrice));
                OnPropertyChanged(nameof(PriceDisplay));
                MarkAsModified();
            }
        }

        [Range(0.01, double.MaxValue, ErrorMessage = "Pack price must be greater than 0")]
        public decimal? PackPrice
        {
            get => _packPrice;
            set 
            { 
                _packPrice = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(EffectiveUnitPrice));
                OnPropertyChanged(nameof(PriceDisplay));
                OnPropertyChanged(nameof(IsPackPricing));
                MarkAsModified();
            }
        }

        [MaxLength(50, ErrorMessage = "Tier name cannot exceed 50 characters")]
        public string TierName
        {
            get => _tierName;
            set 
            { 
                _tierName = value ?? string.Empty; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(DisplayName));
                MarkAsModified();
            }
        }

        [MaxLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
        public string Description
        {
            get => _description;
            set 
            { 
                _description = value ?? string.Empty; 
                OnPropertyChanged();
                MarkAsModified();
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set 
            { 
                _isActive = value; 
                OnPropertyChanged();
                MarkAsModified();
            }
        }

        public int DisplayOrder
        {
            get => _displayOrder;
            set 
            { 
                _displayOrder = value; 
                OnPropertyChanged();
                MarkAsModified();
            }
        }

        public DateTime? EffectiveDate
        {
            get => _effectiveDate;
            set 
            { 
                _effectiveDate = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsCurrentlyValid));
                MarkAsModified();
            }
        }

        public DateTime? ExpirationDate
        {
            get => _expirationDate;
            set 
            { 
                _expirationDate = value; 
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsCurrentlyValid));
                MarkAsModified();
            }
        }

        // UI State Properties
        public bool IsNew
        {
            get => _isNew;
            set { _isNew = value; OnPropertyChanged(); }
        }

        public bool IsModified
        {
            get => _isModified;
            set { _isModified = value; OnPropertyChanged(); }
        }

        // Calculated Properties
        public decimal EffectiveUnitPrice
        {
            get
            {
                if (PackPrice.HasValue && MinimumQuantity > 0)
                    return PackPrice.Value / MinimumQuantity;
                return UnitPrice;
            }
        }

        public bool IsPackPricing => PackPrice.HasValue;

        public string QuantityRangeDisplay
        {
            get
            {
                if (MaximumQuantity.HasValue)
                    return $"{MinimumQuantity:0.###} - {MaximumQuantity.Value:0.###}";
                return $"{MinimumQuantity:0.###}+";
            }
        }

        public string PriceDisplay
        {
            get
            {
                if (IsPackPricing)
                    return $"{PackPrice.Value:C2} pack ({EffectiveUnitPrice:C2} each)";
                return $"{UnitPrice:C2} each";
            }
        }

        public string DisplayName
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(TierName))
                    return TierName;
                return QuantityRangeDisplay;
            }
        }

        public bool IsValidQuantityRange
        {
            get
            {
                if (!MaximumQuantity.HasValue)
                    return true;
                return MaximumQuantity.Value > MinimumQuantity;
            }
        }

        public bool IsCurrentlyValid
        {
            get
            {
                var now = DateTime.Now;
                return IsActive &&
                       (EffectiveDate == null || EffectiveDate <= now) &&
                       (ExpirationDate == null || ExpirationDate > now);
            }
        }

        // Factory Methods
        public static PriceTierDto CreateNew(int productId)
        {
            return new PriceTierDto
            {
                ProductId = productId,
                IsNew = true,
                IsActive = true,
                DisplayOrder = 0
            };
        }

        public static PriceTierDto FromEntity(ProductPriceTier entity)
        {
            return new PriceTierDto
            {
                Id = entity.Id,
                ProductId = entity.ProductId,
                MinimumQuantity = entity.MinimumQuantity,
                MaximumQuantity = entity.MaximumQuantity,
                UnitPrice = entity.UnitPrice,
                PackPrice = entity.PackPrice,
                TierName = entity.TierName ?? string.Empty,
                Description = entity.Description ?? string.Empty,
                IsActive = entity.IsActive,
                DisplayOrder = entity.DisplayOrder,
                EffectiveDate = entity.EffectiveDate,
                ExpirationDate = entity.ExpirationDate,
                IsNew = false,
                IsModified = false
            };
        }

        public ProductPriceTier ToEntity()
        {
            return new ProductPriceTier
            {
                Id = Id,
                ProductId = ProductId,
                MinimumQuantity = MinimumQuantity,
                MaximumQuantity = MaximumQuantity,
                UnitPrice = UnitPrice,
                PackPrice = PackPrice,
                TierName = string.IsNullOrWhiteSpace(TierName) ? null : TierName,
                Description = string.IsNullOrWhiteSpace(Description) ? null : Description,
                IsActive = IsActive,
                DisplayOrder = DisplayOrder,
                EffectiveDate = EffectiveDate,
                ExpirationDate = ExpirationDate,
                CreatedAt = Id == 0 ? DateTime.Now : DateTime.Now, // Will be overridden for existing entities
                UpdatedAt = DateTime.Now
            };
        }

        // IDataErrorInfo Implementation
        public string Error => null;

        public string this[string columnName]
        {
            get
            {
                switch (columnName)
                {
                    case nameof(MinimumQuantity):
                        if (MinimumQuantity <= 0)
                            return "Minimum quantity must be greater than 0";
                        break;

                    case nameof(MaximumQuantity):
                        if (MaximumQuantity.HasValue && MaximumQuantity.Value <= 0)
                            return "Maximum quantity must be greater than 0";
                        if (!IsValidQuantityRange)
                            return "Maximum quantity must be greater than minimum quantity";
                        break;

                    case nameof(UnitPrice):
                        if (UnitPrice <= 0)
                            return "Unit price must be greater than 0";
                        break;

                    case nameof(PackPrice):
                        if (PackPrice.HasValue && PackPrice.Value <= 0)
                            return "Pack price must be greater than 0";
                        break;

                    case nameof(TierName):
                        if (!string.IsNullOrEmpty(TierName) && TierName.Length > 50)
                            return "Tier name cannot exceed 50 characters";
                        break;

                    case nameof(Description):
                        if (!string.IsNullOrEmpty(Description) && Description.Length > 200)
                            return "Description cannot exceed 200 characters";
                        break;
                }
                return null;
            }
        }

        private void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void MarkAsModified()
        {
            if (!IsNew)
                IsModified = true;
        }
    }
}
