using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Views.Dialogs;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views
{
    public partial class SuppliersView : UserControl
    {
        private SuppliersViewModel ViewModel => (SuppliersViewModel)DataContext;
        private string currentSearchText = "";
        private string selectedStatusFilter = "All Suppliers";

        public SuppliersView()
        {
            InitializeComponent();
            DataContext = new SuppliersViewModel();
            StatusFilter.SelectedIndex = 0;
            
            // Set statistics from ViewModel
            UpdateStatistics();
        }
        
        private void UpdateStatistics()
        {
            if (ViewModel != null)
            {
                txtTotalSuppliers.Text = ViewModel.AllSuppliers?.Count.ToString() ?? "0";
                txtActiveSuppliers.Text = ViewModel.AllSuppliers?.Count(s => s.IsActive).ToString() ?? "0";
                txtTotalProducts.Text = ViewModel.AllSuppliers?.Sum(s => s.ProductCount).ToString() ?? "0";
            }
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            currentSearchText = txtSearch.Text.Trim().ToLower();
            ApplyFilters();
        }

        private void SearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ApplyFilters();
            }
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var item = (ComboBoxItem)StatusFilter.SelectedItem;
            selectedStatusFilter = item.Content.ToString();
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredSuppliers = ViewModel.AllSuppliers.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrEmpty(currentSearchText))
            {
                filteredSuppliers = filteredSuppliers.Where(s =>
                    s.Name.ToLower().Contains(currentSearchText) ||
                    s.ContactName.ToLower().Contains(currentSearchText) ||
                    (s.Email?.ToLower().Contains(currentSearchText) ?? false) ||
                    (s.Phone?.ToLower().Contains(currentSearchText) ?? false));
            }

            // Apply status filter
            switch (selectedStatusFilter)
            {
                case "Active Only":
                    filteredSuppliers = filteredSuppliers.Where(s => s.IsActive);
                    break;
                case "Inactive":
                    filteredSuppliers = filteredSuppliers.Where(s => !s.IsActive);
                    break;
            }

            ViewModel.Suppliers = new ObservableCollection<Supplier>(filteredSuppliers);
        }

        private async void AddNewSupplier_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new SupplierDialog();
            var result = await DialogHost.Show(dialog, "RootDialog");
            
            if (result is Supplier newSupplier)
            {
                // The supplier has already been added to the database by the dialog
                // Just refresh the supplier list in the view model
                ViewModel.LoadSuppliers();
                UpdateStatistics();
                
                MessageBox.Show(
                    (string)Application.Current.Resources["SupplierSavedSuccessfully"], 
                    (string)Application.Current.Resources["SuccessTitle"], 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void EditSupplier_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var supplier = (Supplier)button.DataContext;
            
            var dialog = new SupplierDialog(supplier);
            var result = await DialogHost.Show(dialog, "RootDialog");
            
            if (result is Supplier updatedSupplier)
            {
                // The supplier has already been updated in the database by the dialog
                // Just refresh the supplier list in the view model
                ViewModel.LoadSuppliers();
                UpdateStatistics();
                
                MessageBox.Show(
                    (string)Application.Current.Resources["SupplierSavedSuccessfully"], 
                    (string)Application.Current.Resources["SuccessTitle"], 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteSupplier_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var supplier = (Supplier)button.DataContext;

            var result = MessageBox.Show(
                string.Format((string)Application.Current.Resources["ConfirmDeleteSupplier"], supplier.Name),
                (string)Application.Current.Resources["ConfirmDeleteTitle"],
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ViewModel.DeleteSupplier(supplier.Id);
                    UpdateStatistics();
                    
                    MessageBox.Show(
                        (string)Application.Current.Resources["SupplierDeletedSuccessfully"], 
                        (string)Application.Current.Resources["SuccessTitle"], 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        string.Format((string)Application.Current.Resources["ErrorDeletingSupplier"], ex.Message), 
                        (string)Application.Current.Resources["ErrorTitle"], 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }
} 