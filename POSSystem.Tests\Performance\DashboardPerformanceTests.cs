using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;
using POSSystem.Data;
using POSSystem.Services;
using POSSystem.Services.Monitoring;
using POSSystem.ViewModels;
using FluentAssertions;

namespace POSSystem.Tests.Performance
{
    /// <summary>
    /// Dashboard Performance Tests
    /// 
    /// Tests the performance of dashboard operations including:
    /// - Dashboard data loading
    /// - Chart generation and rendering
    /// - Real-time data updates
    /// - Large dataset handling
    /// - Memory usage during dashboard operations
    /// </summary>
    public class DashboardPerformanceTests : IClassFixture<ComprehensivePerformanceTestSuite>
    {
        private readonly ComprehensivePerformanceTestSuite _testSuite;
        private readonly ITestOutputHelper _output;

        public DashboardPerformanceTests(ComprehensivePerformanceTestSuite testSuite, ITestOutputHelper output)
        {
            _testSuite = testSuite;
            _output = output;
        }

        [Fact]
        public async Task DashboardLoadingPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 2000, customerCount: 1000, salesCount: 5000);
            var dashboardViewModel = _testSuite._serviceProvider.GetRequiredService<DashboardViewModel>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            _testSuite.LogPerformanceMessage("Starting Dashboard Loading Performance Test");

            // Act & Assert - Test dashboard loading operations
            await TestInitialDashboardLoad(dashboardViewModel, performanceMonitor, testResults);
            await TestDashboardDataRefresh(dashboardViewModel, performanceMonitor, testResults);
            await TestConcurrentDashboardAccess(dashboardViewModel, performanceMonitor, testResults);
            await TestDashboardMemoryUsage(dashboardViewModel, performanceMonitor, testResults);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            failedTests.Should().BeEmpty("All dashboard loading operations should meet performance targets");

            _testSuite.LogPerformanceMessage($"Dashboard Loading Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestInitialDashboardLoad(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "InitialDashboardLoad";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "DashboardLoading");

                // Simulate initial dashboard load
                await dashboardViewModel.LoadDashboardDataAsync();

                stopwatch.Stop();
                var targetTime = TimeSpan.FromSeconds(3); // 3 seconds for initial load

                tracker.AddMetadata("LoadTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("DataLoaded", true);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["LoadTime"] = stopwatch.ElapsedMilliseconds,
                        ["TargetTime"] = targetTime.TotalMilliseconds,
                        ["DataLoaded"] = true
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestDashboardDataRefresh(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "DashboardDataRefresh";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "DashboardLoading");

                // Test multiple refresh operations
                for (int i = 1; i <= 5; i++)
                {
                    await dashboardViewModel.RefreshDataAsync();
                    await Task.Delay(100); // Small delay between refreshes
                }

                stopwatch.Stop();
                var avgTime = TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds / 5.0);
                var targetTime = TimeSpan.FromSeconds(1); // 1 second per refresh

                tracker.AddMetadata("AverageRefreshTime", avgTime.TotalMilliseconds);
                tracker.AddMetadata("RefreshCount", 5);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = avgTime,
                    Passed = avgTime <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["AverageRefreshTime"] = avgTime.TotalMilliseconds,
                        ["RefreshCount"] = 5,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: Average {avgTime.TotalMilliseconds:F2}ms per refresh (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestConcurrentDashboardAccess(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "ConcurrentDashboardAccess";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "DashboardLoading");

                // Simulate concurrent dashboard access
                var concurrentTasks = new List<Task>();
                var concurrentUsers = 5;

                for (int i = 0; i < concurrentUsers; i++)
                {
                    concurrentTasks.Add(Task.Run(async () =>
                    {
                        var localDashboard = _testSuite._serviceProvider.GetRequiredService<DashboardViewModel>();
                        await localDashboard.LoadDashboardDataAsync();
                    }));
                }

                await Task.WhenAll(concurrentTasks);
                stopwatch.Stop();

                var targetTime = TimeSpan.FromSeconds(5); // 5 seconds for concurrent access

                tracker.AddMetadata("ConcurrentLoadTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ConcurrentUsers", concurrentUsers);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ConcurrentLoadTime"] = stopwatch.ElapsedMilliseconds,
                        ["ConcurrentUsers"] = concurrentUsers,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms for {concurrentUsers} users (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestDashboardMemoryUsage(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "DashboardMemoryUsage";

            try
            {
                using var tracker = monitor.StartTracking(testName, "DashboardLoading");

                // Force garbage collection before test
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var initialMemory = GC.GetTotalMemory(false);

                // Load dashboard multiple times to test memory usage
                for (int i = 1; i <= 10; i++)
                {
                    await dashboardViewModel.LoadDashboardDataAsync();
                    await dashboardViewModel.RefreshDataAsync();
                }

                var finalMemory = GC.GetTotalMemory(false);
                var memoryUsed = (finalMemory - initialMemory) / (1024 * 1024); // Convert to MB
                var targetMemoryLimit = 50; // 50MB limit

                tracker.AddMetadata("MemoryUsedMB", memoryUsed);
                tracker.AddMetadata("InitialMemoryMB", initialMemory / (1024 * 1024));
                tracker.AddMetadata("FinalMemoryMB", finalMemory / (1024 * 1024));

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = TimeSpan.Zero,
                    Passed = memoryUsed <= targetMemoryLimit,
                    Metrics = new Dictionary<string, object>
                    {
                        ["MemoryUsedMB"] = memoryUsed,
                        ["TargetMemoryLimitMB"] = targetMemoryLimit,
                        ["InitialMemoryMB"] = initialMemory / (1024 * 1024),
                        ["FinalMemoryMB"] = finalMemory / (1024 * 1024)
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {memoryUsed}MB used (Target: <{targetMemoryLimit}MB)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = TimeSpan.Zero,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        [Fact]
        public async Task ChartGenerationPerformance_ShouldMeetPerformanceTargets()
        {
            // Arrange
            await _testSuite.SetupPerformanceTestDataAsync(productCount: 1000, customerCount: 500, salesCount: 3000);
            var dashboardViewModel = _testSuite._serviceProvider.GetRequiredService<DashboardViewModel>();
            var performanceMonitor = _testSuite._serviceProvider.GetRequiredService<PerformanceMonitoringService>();

            var testResults = new List<PerformanceTestResult>();
            _testSuite.LogPerformanceMessage("Starting Chart Generation Performance Test");

            // Act & Assert - Test chart generation operations
            await TestSalesChartGeneration(dashboardViewModel, performanceMonitor, testResults);
            await TestProductChartGeneration(dashboardViewModel, performanceMonitor, testResults);
            await TestTrendChartGeneration(dashboardViewModel, performanceMonitor, testResults);
            await TestMultipleChartGeneration(dashboardViewModel, performanceMonitor, testResults);

            // Verify all tests passed
            var failedTests = testResults.Where(r => !r.Passed).ToList();
            failedTests.Should().BeEmpty("All chart generation operations should meet performance targets");

            _testSuite.LogPerformanceMessage($"Chart Generation Performance Test completed. {testResults.Count(r => r.Passed)}/{testResults.Count} tests passed");
        }

        private async Task TestSalesChartGeneration(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "SalesChartGeneration";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ChartGeneration");

                // Simulate sales chart generation
                await dashboardViewModel.UpdateSalesChartsAsync();

                stopwatch.Stop();
                var targetTime = TimeSpan.FromSeconds(2); // 2 seconds for sales chart

                tracker.AddMetadata("ChartGenerationTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ChartType", "Sales");

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ChartGenerationTime"] = stopwatch.ElapsedMilliseconds,
                        ["ChartType"] = "Sales",
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestProductChartGeneration(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "ProductChartGeneration";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ChartGeneration");

                // Simulate product chart generation
                await dashboardViewModel.UpdateProductChartsAsync();

                stopwatch.Stop();
                var targetTime = TimeSpan.FromSeconds(2); // 2 seconds for product chart

                tracker.AddMetadata("ChartGenerationTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ChartType", "Product");

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ChartGenerationTime"] = stopwatch.ElapsedMilliseconds,
                        ["ChartType"] = "Product",
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestTrendChartGeneration(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "TrendChartGeneration";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ChartGeneration");

                // Simulate trend chart generation
                await dashboardViewModel.UpdateCategoryChartsAsync();

                stopwatch.Stop();
                var targetTime = TimeSpan.FromSeconds(2); // 2 seconds for trend chart

                tracker.AddMetadata("ChartGenerationTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ChartType", "Trend");

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["ChartGenerationTime"] = stopwatch.ElapsedMilliseconds,
                        ["ChartType"] = "Trend",
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task TestMultipleChartGeneration(DashboardViewModel dashboardViewModel, PerformanceMonitoringService monitor,
            List<PerformanceTestResult> results)
        {
            var testName = "MultipleChartGeneration";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var tracker = monitor.StartTracking(testName, "ChartGeneration");

                // Simulate generating all charts simultaneously
                var chartTasks = new List<Task>
                {
                    dashboardViewModel.UpdateSalesChartsAsync(),
                    dashboardViewModel.UpdateProductChartsAsync(),
                    dashboardViewModel.UpdateCategoryChartsAsync()
                };

                await Task.WhenAll(chartTasks);
                stopwatch.Stop();

                var targetTime = TimeSpan.FromSeconds(4); // 4 seconds for all charts

                tracker.AddMetadata("TotalChartGenerationTime", stopwatch.ElapsedMilliseconds);
                tracker.AddMetadata("ChartsGenerated", chartTasks.Count);

                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = stopwatch.Elapsed <= targetTime,
                    Metrics = new Dictionary<string, object>
                    {
                        ["TotalChartGenerationTime"] = stopwatch.ElapsedMilliseconds,
                        ["ChartsGenerated"] = chartTasks.Count,
                        ["TargetTime"] = targetTime.TotalMilliseconds
                    }
                });

                _testSuite.LogPerformanceMessage($"{testName}: {stopwatch.ElapsedMilliseconds}ms for {chartTasks.Count} charts (Target: {targetTime.TotalMilliseconds}ms)");
            }
            catch (Exception ex)
            {
                results.Add(new PerformanceTestResult
                {
                    TestName = testName,
                    Duration = stopwatch.Elapsed,
                    Passed = false,
                    ErrorMessage = ex.Message
                });
            }
        }
    }
}
