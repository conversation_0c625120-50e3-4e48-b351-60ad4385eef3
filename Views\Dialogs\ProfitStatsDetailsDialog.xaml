<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.ProfitStatsDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000"
             MinWidth="640" MinHeight="480"
             Background="{DynamicResource MaterialDesignPaper}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <Grid Margin="24"
              MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}"
              MaxWidth="{x:Static SystemParameters.PrimaryScreenWidth}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Close Button -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{DynamicResource ProfitStats}"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       VerticalAlignment="Center"/>

            <Button Grid.Column="1"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Click="CloseButton_Click"
                    ToolTip="{DynamicResource Close}">
                <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Period Filter -->
            <TextBlock Grid.Column="0" 
                       Text="{DynamicResource Period}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       VerticalAlignment="Center"
                       Margin="0,0,8,0"/>
            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding TrendPeriods}"
                      SelectedItem="{Binding SelectedTrendPeriod}"
                      DisplayMemberPath="DisplayName"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,16,0"/>

            <!-- Category Filter -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,8,0">
                <CheckBox IsChecked="{Binding IsCategoryFilterEnabled}"
                          Content="{DynamicResource Category}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            <ComboBox Grid.Column="3"
                      IsEnabled="{Binding IsCategoryFilterEnabled}"
                      ItemsSource="{Binding Categories}"
                      SelectedItem="{Binding SelectedCategory}"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,16,0">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

            <!-- Product Filter -->
            <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="0,0,8,0">
                <CheckBox IsChecked="{Binding IsProductFilterEnabled}"
                          Content="{DynamicResource Product}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            <Grid Grid.Column="5" Margin="0,0,16,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         Text="{Binding BarcodeSearch, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="{DynamicResource SearchByBarcode}"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         VerticalAlignment="Center">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Return" Command="{Binding SearchByBarcodeCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <Button Grid.Column="1"
                        Command="{Binding SearchByBarcodeCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="{DynamicResource SearchByBarcode}"
                        Margin="8,0,0,0"
                        Height="40"
                        Width="40">
                    <materialDesign:PackIcon Kind="Barcode" Height="24" Width="24"/>
                </Button>

                <Button Grid.Column="2"
                        Command="{Binding OpenProductSelectionCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="{DynamicResource SelectProduct}"
                        Margin="8,0,0,0"
                        Height="40"
                        Width="40">
                    <materialDesign:PackIcon Kind="Search" Height="24" Width="24"/>
                </Button>
            </Grid>

            <!-- Net Profit Toggle -->
            <StackPanel Grid.Column="6" 
                       Orientation="Horizontal" 
                       Margin="0,0,0,0"
                       ToolTip="{DynamicResource NetProfitTooltip}">
                <TextBlock Text="{Binding ProfitTypeText}"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         VerticalAlignment="Center"
                         Margin="0,0,8,0"/>
                <ToggleButton IsChecked="{Binding ShowNetProfit}"
                            Style="{StaticResource MaterialDesignSwitchToggleButton}"/>
            </StackPanel>
        </Grid>

        <!-- Subtitle -->
        <TextBlock Grid.Row="2" 
                   Text="{Binding Subtitle}"
                   Style="{StaticResource MaterialDesignBody1TextBlock}"
                   Opacity="0.6"
                   Margin="0,0,0,8"/>

        <!-- Main Content -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Metrics Cards -->
            <UniformGrid Rows="1" Margin="0,0,0,16">
                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource TotalProfit}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="TotalProfit"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding ProfitGrowth}"
                                 Foreground="{Binding ProfitGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="ChartLineVariant" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource ProfitMargin}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock>
                            <Run Text="{Binding ProfitMargin, StringFormat=N1}"/>
                            <Run Text="%"/>
                        </TextBlock>
                        <TextBlock Text="{Binding MarginGrowth}"
                                 Foreground="{Binding MarginGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="4" Padding="12,8">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Calculator" 
                                                   Width="20" Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{DynamicResource AverageProfit}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Margin="8,0,0,0"/>
                        </DockPanel>
                        <TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,4,0,2">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="AverageProfit"/>
                                    <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock Text="{Binding AvgProfitGrowth}"
                                 Foreground="{Binding AvgProfitGrowthColor}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Analytics Section: Tabbed Interface -->
            <materialDesign:Card Grid.Row="1" Padding="8">
                <TabControl Style="{StaticResource MaterialDesignTabControl}"
                           materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                    <!-- Tab 1: Profit Trend -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource ProfitTrend}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ProfitTrend}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding TrendSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding TrendLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 2: Hourly Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource HourlyDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ClockOutline"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource HourlyDistribution}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding HourlyDistributionSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding HourlyLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 3: Daily Distribution -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource DailyDistribution}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="CalendarWeek"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource ProfitByDays}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding ProfitByDaysSeries}"
                                              LegendLocation="None"
                                              DisableAnimations="False"
                                              Margin="0,0,0,20">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Labels="{Binding ProfitByDaysLabels}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis LabelFormatter="{Binding CurrencyFormatter}"
                                            ShowLabels="True"
                                            FontSize="12"
                                            FontWeight="Normal"
                                            Foreground="{DynamicResource MaterialDesignBody}"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </TabItem>

                    <!-- Tab 4: Top Profitable Products -->
                    <TabItem>
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Margin="8,4">
                                <materialDesign:PackIcon Kind="Star"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,6,0"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           Text="{DynamicResource TopProducts}"/>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid Margin="16" MaxWidth="800">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Star"
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="{DynamicResource TopProfitableProducts}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,0"/>
                            </DockPanel>

                            <DataGrid Grid.Row="1"
                                      ItemsSource="{Binding TopProducts}"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      Style="{StaticResource MaterialDesignDataGrid}"
                                      Height="400"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}"
                                                      Binding="{Binding Product.Name}"
                                                      Width="300"
                                                      MaxWidth="300"/>
                                    <DataGridTextColumn Header="{DynamicResource Quantity}"
                                                      Binding="{Binding TotalQuantity, StringFormat=N3}"
                                                      Width="120"
                                                      MaxWidth="120"/>
                                    <DataGridTextColumn Header="{DynamicResource Profit}"
                                                      Width="120"
                                                      MaxWidth="120">
                                        <DataGridTextColumn.Binding>
                                            <MultiBinding StringFormat="{}{0:N2} {1}">
                                                <Binding Path="TotalProfit"/>
                                                <Binding Source="{x:Static Application.Current}" Path="Resources[CurrencySymbol]"/>
                                            </MultiBinding>
                                        </DataGridTextColumn.Binding>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4"
              Background="{DynamicResource MaterialDesignPaper}"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                      VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="24"
                           Height="24"/>
                <TextBlock Text="{DynamicResource LoadingData}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Margin="0,8,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
    </ScrollViewer>
</UserControl>