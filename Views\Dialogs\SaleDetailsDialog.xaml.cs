using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Printing;
using POSSystem.ViewModels;

namespace POSSystem.Views.Dialogs
{
    public partial class SaleDetailsDialog : UserControl
    {
        private readonly Sale _sale;
        private readonly EnhancedReceiptPrintService _receiptPrintService;

        public SaleDetailsDialog(Sale sale)
        {
            InitializeComponent();
            _sale = sale;
            _receiptPrintService = new EnhancedReceiptPrintService(
                (DatabaseService)(App.ServiceProvider?.GetService(typeof(DatabaseService))) ?? new DatabaseService());

            RecalculateDiscountAmount(sale);
            DataContext = sale;
        }

        private void RecalculateDiscountAmount(Sale sale)
        {
            if (sale == null) return;

            // ✅ FIXED: Recalculate subtotal from actual line item totals
            if (sale.Items != null && sale.Items.Any())
            {
                sale.Subtotal = sale.Items.Sum(item => item.Total);
            }

            // Recalculate discount amount from database
            decimal totalDiscount = 0;
            using (var context = new DatabaseService().Context)
            {
                var discounts = context.Discounts
                    .Include(d => d.DiscountType)
                    .Where(d => d.SaleId == sale.Id)
                    .ToList();
                foreach (var discount in discounts)
                {
                    // Since we now store monetary amounts for both percentage and fixed discounts,
                    // we can simply sum the DiscountValue
                    totalDiscount += discount.DiscountValue;
                }
            }
            sale.DiscountAmount = totalDiscount;

            // ✅ FIXED: Recalculate grand total to ensure consistency
            // Grand Total = Subtotal - Discount + Tax
            sale.GrandTotal = sale.Subtotal - sale.DiscountAmount + sale.TaxAmount;
        }

        private async void ReprintReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (_sale == null) return;

            try
            {
                // Disable button during printing
                ReprintReceiptButton.IsEnabled = false;
                var printingText = Application.Current.FindResource("PrintingStatus") as string ?? "Printing...";
                ReprintReceiptButton.Content = printingText;

                // Show options dialog for receipt printing
                var title = Application.Current.FindResource("ReprintReceiptDialog") as string ?? "Reprint Receipt";
                var messageFormat = Application.Current.FindResource("ReprintReceiptFullMessage") as string ?? "Reprint receipt for Invoice #{0}?\n\nClick Yes to print directly, No to show print dialog, or Cancel to abort.";
                var message = string.Format(messageFormat, _sale.InvoiceNumber);

                var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(
                    message,
                    title,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNoCancel,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Cancel)
                {
                    return;
                }

                bool showDialog = result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.No;
                bool success = await _receiptPrintService.PrintReceiptAsync(_sale, showDialog);

                if (!success)
                {
                    var failureTitle = Application.Current.FindResource("PrintResult") as string ?? "Print Result";
                    var failureMessage = Application.Current.FindResource("ReceiptPrintingFailed") as string ?? "Receipt printing failed or was cancelled.";

                    await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(
                        failureMessage,
                        failureTitle,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.OK,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error printing receipt: {ex.Message}",
                    "Print Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                // Re-enable button
                ReprintReceiptButton.IsEnabled = true;
                ReprintReceiptButton.Content = Application.Current.TryFindResource("ReprintReceipt") as string ?? "Reprint Receipt";
            }
        }

        private async void PreviewReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (_sale == null) return;

            try
            {
                PreviewReceiptButton.IsEnabled = false;
                PreviewReceiptButton.Content = "Loading...";

                await _receiptPrintService.PreviewReceiptAsync(_sale);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error showing receipt preview: {ex.Message}",
                    "Preview Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                PreviewReceiptButton.IsEnabled = true;
                PreviewReceiptButton.Content = Application.Current.TryFindResource("PreviewReceipt") as string ?? "Preview Receipt";
            }
        }

        private async void SaveReceiptAsPdf_Click(object sender, RoutedEventArgs e)
        {
            if (_sale == null) return;

            try
            {
                SavePdfButton.IsEnabled = false;
                SavePdfButton.Content = "Saving...";

                // Show save file dialog
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "Save Receipt as PDF",
                    Filter = "PDF files (*.pdf)|*.pdf|XPS files (*.xps)|*.xps|All files (*.*)|*.*",
                    DefaultExt = "pdf",
                    FileName = $"Receipt_{_sale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    bool success = await _receiptPrintService.SaveReceiptAsPdfAsync(_sale, saveFileDialog.FileName);

                    if (success)
                    {
                        MessageBox.Show(
                            "Receipt saved successfully.",
                            "Save Successful",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "Failed to save receipt.",
                            "Save Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error saving receipt: {ex.Message}",
                    "Save Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                SavePdfButton.IsEnabled = true;
                SavePdfButton.Content = Application.Current.TryFindResource("SaveAsPDF") as string ?? "Save as PDF";
            }
        }
    }
}