using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Utilities
{
    /// <summary>
    /// Utility to scan codebase for SQL function typos
    /// </summary>
    public static class SqlFunctionScanner
    {
        private static readonly List<string> FileExtensionsToScan = new List<string> 
        { 
            ".cs", ".sql", ".xaml", ".xml", ".config", ".txt" 
        };
        
        /// <summary>
        /// Scan for SQL function typos in the codebase
        /// </summary>
        /// <param name="rootDirectory">Directory to start scanning from</param>
        /// <returns>Dictionary of file paths and lines containing potential typos</returns>
        public static async Task<Dictionary<string, List<string>>> ScanForSqlFunctionTypos(string rootDirectory)
        {
            Dictionary<string, List<string>> results = new Dictionary<string, List<string>>();
            
            try
            {
                if (!Directory.Exists(rootDirectory))
                {
                    Debug.WriteLine($"Directory not found: {rootDirectory}");
                    return results;
                }
                
                // Get all files to scan
                var filesToScan = GetFilesToScan(rootDirectory);
                Debug.WriteLine($"Found {filesToScan.Count} files to scan");
                
                // Scan each file
                foreach (var filePath in filesToScan)
                {
                    try
                    {
                        var fileResults = await ScanFileForTypos(filePath);
                        if (fileResults.Any())
                        {
                            results[filePath] = fileResults;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error scanning file {filePath}: {ex.Message}");
                    }
                }
                
                Debug.WriteLine($"Scan complete. Found issues in {results.Count} files");
                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error scanning for SQL function typos: {ex.Message}");
                return results;
            }
        }
        
        /// <summary>
        /// Get all files to scan
        /// </summary>
        private static List<string> GetFilesToScan(string rootDirectory)
        {
            List<string> files = new List<string>();
            
            try
            {
                // Get all files with the specified extensions
                foreach (var extension in FileExtensionsToScan)
                {
                    var foundFiles = Directory.GetFiles(rootDirectory, $"*{extension}", SearchOption.AllDirectories);
                    files.AddRange(foundFiles);
                }
                
                // Filter out files in obj and bin directories
                files = files.Where(f => !f.Contains("\\obj\\") && !f.Contains("\\bin\\")).ToList();
                
                return files;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting files to scan: {ex.Message}");
                return files;
            }
        }
        
        /// <summary>
        /// Scan a file for SQL function typos
        /// </summary>
        private static async Task<List<string>> ScanFileForTypos(string filePath)
        {
            List<string> results = new List<string>();
            
            try
            {
                string content = await File.ReadAllTextAsync(filePath);
                
                // Look for "INSTRO" in the file
                var instroMatches = Regex.Matches(content, @"INSTRO", RegexOptions.IgnoreCase);
                if (instroMatches.Count > 0)
                {
                    // Find the lines containing "INSTRO"
                    string[] lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);
                    for (int i = 0; i < lines.Length; i++)
                    {
                        if (Regex.IsMatch(lines[i], @"INSTRO", RegexOptions.IgnoreCase))
                        {
                            results.Add($"Line {i + 1}: {lines[i].Trim()}");
                        }
                    }
                }
                
                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error scanning file {filePath}: {ex.Message}");
                return results;
            }
        }
        
        /// <summary>
        /// Fix SQL function typos in a file
        /// </summary>
        public static async Task FixSqlFunctionTypos(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Debug.WriteLine($"File not found: {filePath}");
                    return;
                }
                
                string content = await File.ReadAllTextAsync(filePath);
                
                // Replace "INSTRO" with "INSTR" (case-insensitive)
                string fixedContent = Regex.Replace(content, @"INSTRO", "INSTR", RegexOptions.IgnoreCase);
                
                // Save the fixed content if changes were made
                if (content != fixedContent)
                {
                    // Create backup of the original file
                    File.Copy(filePath, $"{filePath}.bak", true);
                    
                    // Write the fixed content
                    await File.WriteAllTextAsync(filePath, fixedContent);
                    
                    Debug.WriteLine($"Fixed SQL function typos in {filePath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error fixing SQL function typos in {filePath}: {ex.Message}");
            }
        }
    }
}
