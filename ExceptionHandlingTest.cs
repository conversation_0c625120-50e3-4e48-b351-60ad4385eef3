using System;
using System.IO;
using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Services;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test class to verify exception handling improvements
    /// </summary>
    public class ExceptionHandlingTest
    {
        /// <summary>
        /// Test database connection with error handling
        /// </summary>
        public static void TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("Testing database connection...");
                
                // Test database path creation
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "POSSystem", "test_pos.db");
                var dbDirectory = Path.GetDirectoryName(dbPath);
                
                if (!Directory.Exists(dbDirectory))
                {
                    Directory.CreateDirectory(dbDirectory);
                    Debug.WriteLine($"Created test database directory: {dbDirectory}");
                }
                
                // Test database context creation
                var optionsBuilder = new DbContextOptionsBuilder<POSDbContext>();
                optionsBuilder.UseSqlite($"Data Source={dbPath};Cache=Shared;");
                
                using (var context = new POSDbContext(optionsBuilder.Options))
                {
                    // Test database connection
                    var canConnect = context.Database.CanConnect();
                    Debug.WriteLine($"Database connection test: {(canConnect ? "SUCCESS" : "FAILED")}");
                    
                    if (!canConnect)
                    {
                        // Try to create database
                        context.Database.EnsureCreated();
                        Debug.WriteLine("Database created successfully");
                    }
                }
                
                Debug.WriteLine("Database connection test completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Database connection test failed: {ex.Message}");
                Debug.WriteLine($"Exception type: {ex.GetType().Name}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test chart initialization with null data
        /// </summary>
        public static void TestChartInitialization()
        {
            try
            {
                Debug.WriteLine("Testing chart initialization...");
                
                // Test LiveCharts SeriesCollection creation
                var series = new LiveCharts.SeriesCollection();
                Debug.WriteLine("SeriesCollection created successfully");
                
                // Test ChartValues creation with empty data
                var chartValues = new LiveCharts.ChartValues<decimal>();
                Debug.WriteLine("ChartValues created successfully");
                
                // Test with null data handling
                decimal[] nullData = null;
                var safeValues = nullData?.Length > 0 ? new LiveCharts.ChartValues<decimal>(nullData) : new LiveCharts.ChartValues<decimal>();
                Debug.WriteLine("Null data handling test passed");
                
                Debug.WriteLine("Chart initialization test completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Chart initialization test failed: {ex.Message}");
                Debug.WriteLine($"Exception type: {ex.GetType().Name}");
            }
        }
        
        /// <summary>
        /// Test file access and directory creation
        /// </summary>
        public static void TestFileAccess()
        {
            try
            {
                Debug.WriteLine("Testing file access...");
                
                var testDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "POSSystem", "Test");
                var testFile = Path.Combine(testDir, "test.txt");
                
                // Test directory creation
                if (!Directory.Exists(testDir))
                {
                    Directory.CreateDirectory(testDir);
                    Debug.WriteLine($"Test directory created: {testDir}");
                }
                
                // Test file creation
                File.WriteAllText(testFile, "Test content");
                Debug.WriteLine($"Test file created: {testFile}");
                
                // Test file reading
                var content = File.ReadAllText(testFile);
                Debug.WriteLine($"Test file content: {content}");
                
                // Cleanup
                File.Delete(testFile);
                Directory.Delete(testDir);
                Debug.WriteLine("Test cleanup completed");
                
                Debug.WriteLine("File access test completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"File access test failed: {ex.Message}");
                Debug.WriteLine($"Exception type: {ex.GetType().Name}");
            }
        }
        
        /// <summary>
        /// Run all exception handling tests
        /// </summary>
        public static void RunAllTests()
        {
            Debug.WriteLine("=== Starting Exception Handling Tests ===");
            
            TestDatabaseConnection();
            TestChartInitialization();
            TestFileAccess();
            
            Debug.WriteLine("=== Exception Handling Tests Completed ===");
        }
    }
}
