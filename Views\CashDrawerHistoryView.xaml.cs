using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Models;
using POSSystem.Services;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;

namespace POSSystem.Views
{
    public partial class CashDrawerHistoryView : UserControl
    {
        private readonly CashDrawerHistoryViewModel _viewModel;

        public CashDrawerHistoryView()
        {
            InitializeComponent();
            _viewModel = new CashDrawerHistoryViewModel();
            DataContext = _viewModel;

            // Set default date range to last 30 days
            StartDate.SelectedDate = DateTime.Now.AddDays(-30);
            EndDate.SelectedDate = DateTime.Now;
            
            // Initial data load
            _viewModel.LoadCashDrawers(StartDate.SelectedDate, EndDate.SelectedDate);
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (StartDate.SelectedDate.HasValue && EndDate.SelectedDate.HasValue)
            {
                _viewModel.LoadCashDrawers(StartDate.SelectedDate, EndDate.SelectedDate);
            }
        }
        
        private void SelectDateRange_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new DateRangePickerDialog(StartDate.SelectedDate, EndDate.SelectedDate);
            if (Window.GetWindow(this) is Window parentWindow)
            {
                dialog.Owner = parentWindow;
                var result = dialog.ShowDialog();
                
                if (result == true && dialog.DateRangeSelected)
                {
                    StartDate.SelectedDate = dialog.StartDate;
                    EndDate.SelectedDate = dialog.EndDate;
                    _viewModel.LoadCashDrawers(dialog.StartDate, dialog.EndDate);
                }
            }
        }

        private async void ViewTransactions_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var cashDrawer = button.DataContext as CashDrawer;
            
            if (cashDrawer != null)
            {
                var dialog = new CashDrawerTransactionsDialog(cashDrawer);
                await DialogHost.Show(dialog, "RootDialog");
            }
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.ExportToExcel();
        }

        private void BackToDrawer_Click(object sender, RoutedEventArgs e)
        {
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                var dbService = new DatabaseService();
                var cashDrawerService = new CashDrawerService(dbService);
                var cashDrawerViewModel = new CashDrawerViewModel(cashDrawerService, dbService);
                var cashDrawerView = new CashDrawerView();
                cashDrawerView.DataContext = cashDrawerViewModel;
                mainWindow.NavigateToView(cashDrawerView);
            }
        }
    }
} 