using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Models = POSSystem.Models;
using Data = POSSystem.Data;

namespace POSSystem.Services
{
    public class DiscountService : IDiscountService
    {
        private readonly IDatabaseService _dbService;
        private POSSystem.Data.POSDbContext Context => (POSSystem.Data.POSDbContext)_dbService.Context;

        public DiscountService(IDatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
        }

        // Legacy constructor for backward compatibility
        public DiscountService() : this(new DatabaseService())
        {
        }

        public bool CanApplyDiscount(User user, DiscountType discountType, decimal discountValue, decimal originalPrice, bool isCartWide = false)
        {
            // Capture the discount type name early to avoid ambiguity later
            string discountTypeName = discountType.Name;
            
            // Validate basic discount rules first (applies to all users)
            // 1. Percentage discount cannot exceed 100%
            if (discountTypeName == "Percentage" && discountValue > 100)
            {
                return false;
            }
            
            // 2. Fixed amount discount cannot exceed original price
            if (discountTypeName == "Fixed Amount" && discountValue > originalPrice)
            {
                return false;
            }
            
            // Calculate the final price after discount to enforce minimum price rules
            decimal finalPrice = originalPrice; // Initialize with original price as default
            if (discountTypeName == "Percentage")
            {
                finalPrice = originalPrice * (1 - (discountValue / 100));
                // Sanity check - final price cannot be negative
                if (finalPrice < 0)
                {
                    return false;
                }
            }
            else if (discountTypeName == "Fixed Amount")
            {
                finalPrice = originalPrice - discountValue;
                // Sanity check - final price cannot be negative
                if (finalPrice < 0)
                {
                    return false;
                }
            }

            // Now check user permissions
            int discountTypeId = discountType.Id;
            var permission = Context.DiscountPermissions
                .Include(dp => dp.DiscountType)
                .FirstOrDefault(dp => dp.RoleId == user.RoleId && dp.DiscountTypeId == discountTypeId && dp.IsActive);

            if (permission == null) return false;

            // For cart-wide discounts, we might want to enforce stricter limits
            decimal maxAllowedPercentage = isCartWide ?
                (permission.MaxPercentage ?? 0m) * 0.8m : // 80% of normal max for cart-wide
                permission.MaxPercentage ?? 0m;

            decimal maxAllowedFixedAmount = isCartWide ?
                (permission.MaxFixedAmount ?? 0m) * 0.8m : // 80% of normal max for cart-wide
                permission.MaxFixedAmount ?? 0m;

            // Check if user has permission for this discount type
            if (discountTypeName == "Percentage")
            {
                if (discountValue > maxAllowedPercentage)
                    return false;

                if (permission.MinPricePercentage.HasValue)
                {
                    decimal minimumAllowedPrice = originalPrice * (permission.MinPricePercentage.Value / 100);
                    if (finalPrice < minimumAllowedPrice)
                        return false;
                }
            }
            else if (discountTypeName == "Fixed Amount")
            {
                if (discountValue > maxAllowedFixedAmount)
                    return false;

                if (permission.MinPricePercentage.HasValue)
                {
                    decimal minimumAllowedPrice = originalPrice * (permission.MinPricePercentage.Value / 100);
                    if (finalPrice < minimumAllowedPrice)
                        return false;
                }
            }

            return true;
        }

        public bool RequiresApproval(User user, DiscountType discountType, decimal discountValue, bool isCartWide = false)
        {
            var permission = Context.DiscountPermissions
                .Include(dp => dp.DiscountType)
                .FirstOrDefault(dp => dp.RoleId == user.RoleId && dp.DiscountTypeId == discountType.Id && dp.IsActive);

            if (permission == null) return true;

            if (!permission.RequiresApproval) return false;

            // Cart-wide discounts might need approval at lower thresholds
            decimal approvalThreshold = isCartWide ?
                (permission.ApprovalThreshold ?? 0m) * 0.8m : // 80% of normal threshold for cart-wide
                permission.ApprovalThreshold ?? 0m;

            if (approvalThreshold > 0)
            {
                if (discountType.Name == "Percentage")
                    return discountValue > approvalThreshold;
                else if (discountType.Name == "Fixed Amount")
                    return discountValue > approvalThreshold;
            }

            return true;
        }

        public Discount CreateDiscount(
            int? saleId,
            int? saleItemId,
            int discountTypeId,
            decimal discountValue,
            decimal originalPrice,
            int reasonId,
            string comment,
            User appliedByUser,
            User approvedByUser = null,
            bool isCartWide = false)
        {
            var discountType = Context.DiscountTypes.First(dt => dt.Id == discountTypeId);

            // For percentage discounts, convert to monetary amount and store that
            decimal monetaryDiscountValue = discountValue;
            if (discountType.Name == "Percentage")
            {
                // Convert percentage to monetary amount
                monetaryDiscountValue = originalPrice * (discountValue / 100);
            }

            var discount = new Discount
            {
                SaleId = saleId,
                SaleItemId = saleItemId,
                DiscountTypeId = discountTypeId,
                DiscountType = discountType,
                DiscountValue = monetaryDiscountValue, // Store monetary amount for both percentage and fixed
                OriginalPrice = originalPrice,
                FinalPrice = originalPrice - monetaryDiscountValue, // Simple subtraction since we store monetary amount
                ReasonId = reasonId,
                Comment = isCartWide ? $"Cart-wide: {comment}" : comment,
                AppliedByUserId = appliedByUser.Id,
                ApprovedByUserId = approvedByUser?.Id,
                AppliedAt = DateTime.Now,
                ApprovedAt = approvedByUser != null ? DateTime.Now : null,
                IsActive = true
            };

            return discount;
        }

        public void ApplyCartWideDiscount(Cart cart, Discount discount)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] ApplyCartWideDiscount called - Cart: {cart?.Id}, Discount: {discount?.DiscountValue}");

                if (cart == null)
                {
                    System.Diagnostics.Debug.WriteLine("[DISCOUNT SERVICE] ERROR: Cart is null");
                    return;
                }

                if (discount == null)
                {
                    System.Diagnostics.Debug.WriteLine("[DISCOUNT SERVICE] ERROR: Discount is null");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Accessing cart.Discounts collection...");
                var discountsCollection = cart.Discounts; // This should trigger lazy initialization
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Cart.Discounts collection accessed successfully, count: {discountsCollection?.Count ?? -1}");

                // Clear any existing cart-wide discounts
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Looking for existing cart-wide discounts...");
                var existingCartWideDiscounts = discountsCollection.Where(d => d.Comment?.StartsWith("Cart-wide:") ?? false).ToList();
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Found {existingCartWideDiscounts.Count} existing cart-wide discounts");

                foreach (var existing in existingCartWideDiscounts)
                {
                    System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Removing existing discount: {existing.DiscountValue}");
                    cart.RemoveDiscount(existing);
                }

                // Add the new cart-wide discount
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Adding new cart-wide discount: {discount.DiscountValue}");
                cart.AddDiscount(discount);
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Cart-wide discount added successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] ERROR in ApplyCartWideDiscount: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[DISCOUNT SERVICE] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public void ApplyItemDiscount(CartItem item, Discount discount)
        {
            if (item == null || discount == null) return;
            item.AddDiscount(discount);
        }

        private decimal CalculateFinalPrice(int discountTypeId, decimal originalPrice, decimal discountValue)
        {
            var discountType = Context.DiscountTypes.First(dt => dt.Id == discountTypeId);

            if (discountType.Name == "Percentage")
            {
                // For percentage discounts, convert to monetary amount first
                decimal monetaryAmount = originalPrice * (discountValue / 100);
                return originalPrice - monetaryAmount;
            }
            else if (discountType.Name == "Fixed Amount")
            {
                // For fixed amount discounts, simply subtract the discount value
                return originalPrice - discountValue;
            }

            // Default case - return original price if discount type is unknown
            return originalPrice;
        }

        public List<DiscountType> GetAvailableDiscountTypes(User user, bool forCartWide = false)
        {
            var query = Context.DiscountTypes
                .Where(dt => Context.DiscountPermissions
                    .Any(dp => dp.RoleId == user.RoleId &&
                              dp.DiscountTypeId == dt.Id &&
                              dp.IsActive));

            // For cart-wide discounts, we might want to exclude certain types
            if (forCartWide)
            {
                query = query.Where(dt => dt.Name != "Price Override"); // Example: exclude price override for cart-wide
            }

            return query.ToList();
        }

        public List<DiscountReason> GetActiveDiscountReasons()
        {
            return Context.DiscountReasons
                .Where(dr => dr.IsActive)
                .ToList();
        }

        public void ApproveDiscount(Discount discount, User approver)
        {
            discount.ApprovedByUserId = approver.Id;
            discount.ApprovedAt = DateTime.Now;
            Context.SaveChanges();
        }

        public void VoidDiscount(Discount discount)
        {
            discount.IsActive = false;
            Context.SaveChanges();
        }

        // Interface implementation methods
        public bool CanApplyDiscount(int userId, decimal discountAmount)
        {
            // Simple implementation - can be enhanced with more business rules
            return discountAmount >= 0;
        }

        public void ApplyDiscount(int saleId, int discountTypeId, decimal discountValue, string reason)
        {
            // Find or create a discount reason
            var discountReason = Context.DiscountReasons
                .FirstOrDefault(dr => dr.Code == reason || dr.Description == reason);

            if (discountReason == null)
            {
                // Create a new discount reason if not found
                discountReason = new DiscountReason
                {
                    Code = reason.ToUpper().Replace(" ", ""),
                    Description = reason,
                    IsActive = true
                };
                Context.DiscountReasons.Add(discountReason);
                Context.SaveChanges();
            }

            var discount = new Discount
            {
                SaleId = saleId,
                DiscountTypeId = discountTypeId,
                DiscountValue = discountValue,
                ReasonId = discountReason.Id,
                AppliedAt = DateTime.Now,
                IsActive = true
            };

            Context.Discounts.Add(discount);
            Context.SaveChanges();
        }

        // Interface implementation methods
        public List<DiscountType> GetAllDiscountTypes()
        {
            return _dbService.GetAllDiscountTypes();
        }

        public List<DiscountReason> GetAllDiscountReasons()
        {
            return _dbService.GetAllDiscountReasons();
        }
    }
}