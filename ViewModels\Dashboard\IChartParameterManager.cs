using System;
using System.Collections.ObjectModel;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface for managing chart parameters and configurations.
    /// </summary>
    public interface IChartParameterManager
    {
        /// <summary>
        /// Gets the collection of chart parameters
        /// </summary>
        ObservableCollection<ChartParameter> ChartParameters { get; }
        
        /// <summary>
        /// Gets the collection of time periods
        /// </summary>
        ObservableCollection<TimePeriod> TimePeriods { get; }
        
        /// <summary>
        /// Gets the collection of metric types
        /// </summary>
        ObservableCollection<MetricType> MetricTypes { get; }
        
        /// <summary>
        /// Gets the collection of product metrics
        /// </summary>
        ObservableCollection<ProductMetric> ProductMetrics { get; }
        
        /// <summary>
        /// Gets a default chart parameter
        /// </summary>
        ChartParameter DefaultChartParameter { get; }
        
        /// <summary>
        /// Gets a default time period
        /// </summary>
        TimePeriod DefaultTimePeriod { get; }
        
        /// <summary>
        /// Gets a default metric type
        /// </summary>
        MetricType DefaultMetricType { get; }
        
        /// <summary>
        /// Gets a default product metric
        /// </summary>
        ProductMetric DefaultProductMetric { get; }
        
        /// <summary>
        /// Gets the appropriate date range based on a time period
        /// </summary>
        /// <param name="period">The time period</param>
        /// <returns>Start and end date tuple</returns>
        (DateTime start, DateTime end) GetDateRange(TimePeriod period);
        
        /// <summary>
        /// Gets the previous period range based on the current period
        /// </summary>
        /// <param name="start">Current period start date</param>
        /// <param name="end">Current period end date</param>
        /// <returns>Previous period start and end date tuple</returns>
        (DateTime start, DateTime end) GetPreviousPeriodRange(DateTime start, DateTime end);
        
        /// <summary>
        /// Formats a value based on a chart parameter
        /// </summary>
        /// <param name="value">The value to format</param>
        /// <param name="parameter">The chart parameter</param>
        /// <returns>Formatted value string</returns>
        string FormatValueByParameter(double value, string parameter);
    }
} 