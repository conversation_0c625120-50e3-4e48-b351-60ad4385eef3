-- POS System Test Database - Generate One Month of Simulated Data (Simplified Version)
-- This script generates a month's worth of simulated data for testing and demonstration

-- Set pragmas for better performance
PRAGMA synchronous = OFF;
PRAGMA journal_mode = MEMORY;
PRAGMA temp_store = MEMORY;
PRAGMA foreign_keys = ON;

-- Begin transaction for better performance
BEGIN TRANSACTION;

-- Clean up existing data we don't want to duplicate
DELETE FROM UserFavorites;
DELETE FROM Discounts;
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM CashTransactions;
DELETE FROM CashDrawers WHERE Id > 0;
DELETE FROM InventoryTransactions;
DELETE FROM PurchaseOrderItems;
DELETE FROM PurchaseOrders;
DELETE FROM LoyaltyTransactions;

-- Reset all product stock to initial values
UPDATE Products SET StockQuantity = 
    CASE 
        WHEN Id BETWEEN 1 AND 4 THEN 50  -- Electronics
        WHEN Id BETWEEN 5 AND 8 THEN 100 -- Groceries
        WHEN Id BETWEEN 9 AND 12 THEN 80 -- Clothing
        ELSE 50
    END;

-- Reset autoincrement counters
DELETE FROM sqlite_sequence WHERE name IN (
    'Sales', 'SaleItems', 'Discounts', 'CashDrawers', 'CashTransactions', 
    'InventoryTransactions', 'PurchaseOrders', 'PurchaseOrderItems', 'LoyaltyTransactions'
);

-- Create cash drawers for each day, one month back
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
WITH dates(date, day_of_week, busyness) AS (
    -- Generate 30 days, starting from today and going backwards
    SELECT 
        date(datetime('now', '-' || (30-rowid) || ' days')),
        CAST(strftime('%w', date(datetime('now', '-' || (30-rowid) || ' days'))) AS INTEGER),
        CASE
            -- Weekend
            WHEN CAST(strftime('%w', date(datetime('now', '-' || (30-rowid) || ' days'))) AS INTEGER) IN (0, 6) THEN ABS(RANDOM() % 4) + 7
            -- Friday
            WHEN CAST(strftime('%w', date(datetime('now', '-' || (30-rowid) || ' days'))) AS INTEGER) = 5 THEN ABS(RANDOM() % 4) + 6
            -- Monday
            WHEN CAST(strftime('%w', date(datetime('now', '-' || (30-rowid) || ' days'))) AS INTEGER) = 1 THEN ABS(RANDOM() % 3) + 3
            -- Other days
            ELSE ABS(RANDOM() % 4) + 4
        END
    FROM (SELECT rowid FROM sqlite_master LIMIT 30)
)
SELECT 
    500.00 AS OpeningBalance,
    500.00 + (100.0 * busyness) + (ABS(RANDOM()) % 100 - 50) AS CurrentBalance,
    500.00 + (100.0 * busyness) AS ExpectedBalance,
    500.00 + (100.0 * busyness) + (ABS(RANDOM()) % 100 - 50) AS ActualBalance,
    (ABS(RANDOM()) % 20 - 10) AS Difference,
    'Closed' AS Status,
    datetime(date, '09:00:00') AS OpenedAt,
    datetime(date, '18:00:00') AS ClosedAt,
    (ABS(RANDOM()) % 3) + 1 AS OpenedById,
    2 AS ClosedById,
    CASE 
        WHEN ABS(RANDOM()) % 10 = 1 THEN 'Had to recount at end of day'
        WHEN ABS(RANDOM()) % 20 = 1 THEN 'Register had technical issue'
        ELSE 'Normal business day'
    END AS Notes
FROM dates;

-- Create sales for each day based on how busy the day was
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                   TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
WITH sale_generator AS (
    SELECT 
        cd.Id AS cash_drawer_id,
        cd.OpenedAt AS opened_at,
        CASE
            -- Weekend
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) IN (0, 6) THEN ABS(RANDOM() % 4) + 7
            -- Friday
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) = 5 THEN ABS(RANDOM() % 4) + 6
            -- Monday
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) = 1 THEN ABS(RANDOM() % 3) + 3
            -- Other days
            ELSE ABS(RANDOM() % 4) + 4
        END AS busyness,
        rowid AS sale_number
    FROM CashDrawers cd
    CROSS JOIN (
        SELECT rowid FROM sqlite_master
        LIMIT (SELECT SUM(
            CASE
                WHEN CAST(strftime('%w', date(OpenedAt)) AS INTEGER) IN (0, 6) THEN 50 -- Weekend: more sales
                WHEN CAST(strftime('%w', date(OpenedAt)) AS INTEGER) = 5 THEN 40 -- Friday: quite busy
                WHEN CAST(strftime('%w', date(OpenedAt)) AS INTEGER) = 1 THEN 15 -- Monday: slower
                ELSE 30 -- Other days: average
            END
        ) FROM CashDrawers)
    )
    -- Ensure a varying number of sales per day based on busyness
    WHERE (
        rowid % 
        CASE
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) IN (0, 6) THEN 2 -- Weekend: more sales
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) = 5 THEN 3 -- Friday: quite busy
            WHEN CAST(strftime('%w', date(cd.OpenedAt)) AS INTEGER) = 1 THEN 8 -- Monday: slower
            ELSE 4 -- Other days: average
        END
    ) = 0
)
SELECT 
    'INV-' || strftime('%y%m%d', opened_at) || '-' || printf('%03d', sale_number) AS InvoiceNumber,
    -- Distribute sales throughout the day
    datetime(
        date(opened_at),
        printf(
            '%02d:%02d:%02d',
            9 + (ABS(RANDOM()) % 9),  -- Hours: 9am to 6pm
            ABS(RANDOM()) % 60,       -- Minutes: 0-59
            ABS(RANDOM()) % 60        -- Seconds: 0-59
        )
    ) AS SaleDate,
    CASE WHEN ABS(RANDOM()) % 4 = 0 THEN NULL ELSE (ABS(RANDOM()) % 4) + 1 END AS CustomerId,
    CASE WHEN ABS(RANDOM()) % 10 <= 7 THEN 3 ELSE 2 END AS UserId,
    0 AS Subtotal, -- Will be updated after adding items
    0 AS DiscountAmount, 
    0 AS TaxAmount,
    0 AS GrandTotal,
    0 AS AmountPaid,
    0 AS Change,
    CASE 
        WHEN ABS(RANDOM()) % 10 <= 5 THEN 'Card'
        WHEN ABS(RANDOM()) % 10 <= 8 THEN 'Cash'
        ELSE 'Mobile'
    END AS PaymentMethod,
    'Paid' AS PaymentStatus,
    'Completed' AS Status,
    (ABS(RANDOM()) % 4) + 1 AS TotalItems
FROM sale_generator;

-- Add items to each sale
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
SELECT 
    s.Id AS SaleId,
    -- Product selection logic: More groceries, some electronics, moderate clothing
    CASE
        WHEN ABS(RANDOM()) % 100 < 50 THEN 
            (ABS(RANDOM()) % 4) + 5  -- 50% chance for groceries (5-8)
        WHEN ABS(RANDOM()) % 100 < 70 THEN 
            (ABS(RANDOM()) % 4) + 9  -- 20% chance for clothing (9-12)
        ELSE 
            (ABS(RANDOM()) % 4) + 1  -- 30% chance for electronics (1-4)
    END AS ProductId,
    -- Quantity logic
    CASE
        WHEN ABS(RANDOM()) % 100 < 50 THEN 
            (ABS(RANDOM()) % 3) + 1  -- 1-3 items for most products
        WHEN ABS(RANDOM()) % 100 < 80 THEN 
            (ABS(RANDOM()) % 2) + 1  -- 1-2 items for more expensive products
        ELSE 
            1  -- Just 1 for very expensive items
    END AS Quantity,
    0 AS UnitPrice, -- Will be filled from Products table
    0 AS Total -- Will be calculated
FROM Sales s
CROSS JOIN (
    -- Add enough items to match TotalItems for each sale
    SELECT rowid 
    FROM sqlite_master 
    LIMIT (SELECT SUM(TotalItems) FROM Sales)
) items
-- Ensure correct number of items per sale
WHERE (SELECT COUNT(*) FROM SaleItems WHERE SaleId = s.Id) < s.TotalItems;

-- Update UnitPrice and Total in SaleItems based on Products prices
UPDATE SaleItems
SET 
    UnitPrice = (SELECT SellingPrice FROM Products WHERE Id = SaleItems.ProductId),
    Total = Quantity * (SELECT SellingPrice FROM Products WHERE Id = SaleItems.ProductId);

-- Calculate subtotals and grand totals for each sale
UPDATE Sales
SET 
    Subtotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    GrandTotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    AmountPaid = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id),
    Change = CASE 
        WHEN PaymentMethod = 'Cash' THEN 
            ROUND((SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id) + 0.01, 0) - 
            (SELECT SUM(Total) FROM SaleItems WHERE SaleId = Sales.Id)
        ELSE 0
    END;

-- Add discounts to ~20% of sales
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
SELECT
    -- 70% percentage, 30% fixed amount
    CASE WHEN ABS(RANDOM()) % 10 < 7 THEN 1 ELSE 2 END AS DiscountTypeId,
    -- Value based on discount type and user role
    CASE 
        -- Percentage discount values appropriate for the user role
        WHEN ABS(RANDOM()) % 10 < 7 THEN 
            CASE 
                WHEN s.UserId = 1 THEN ABS(RANDOM()) % 46 + 5     -- Admin: 5-50%
                WHEN s.UserId = 2 THEN ABS(RANDOM()) % 21 + 5     -- Manager: 5-25%
                ELSE ABS(RANDOM()) % 6 + 5                        -- Cashier: 5-10%
            END
        -- Fixed amount discount appropriate for the user role
        ELSE 
            CASE 
                WHEN s.UserId = 1 THEN ABS(RANDOM()) % 91 + 10    -- Admin: $10-$100
                WHEN s.UserId = 2 THEN ABS(RANDOM()) % 46 + 5     -- Manager: $5-$50
                ELSE ABS(RANDOM()) % 16 + 5                       -- Cashier: $5-$20
            END
    END AS DiscountValue,
    s.Subtotal AS OriginalPrice,
    -- Calculate final price based on discount type
    CASE 
        -- Percentage discount
        WHEN ABS(RANDOM()) % 10 < 7 THEN 
            s.Subtotal * (1 - (
                CASE 
                    WHEN s.UserId = 1 THEN (ABS(RANDOM()) % 46 + 5)
                    WHEN s.UserId = 2 THEN (ABS(RANDOM()) % 21 + 5)
                    ELSE (ABS(RANDOM()) % 6 + 5)
                END / 100.0
            ))
        -- Fixed amount discount
        ELSE 
            s.Subtotal - (
                CASE 
                    WHEN s.UserId = 1 THEN (ABS(RANDOM()) % 91 + 10)
                    WHEN s.UserId = 2 THEN (ABS(RANDOM()) % 46 + 5)
                    ELSE (ABS(RANDOM()) % 16 + 5)
                END
            )
    END AS FinalPrice,
    -- Random discount reason
    (ABS(RANDOM()) % 7) + 1 AS ReasonId,
    -- Generate typical discount comments
    CASE ABS(RANDOM()) % 5
        WHEN 0 THEN 'Regular customer discount'
        WHEN 1 THEN 'Special promotion'
        WHEN 2 THEN 'Manager approval for loyal customer'
        WHEN 3 THEN 'Product slightly damaged'
        ELSE 'Customer satisfaction'
    END AS Comment,
    s.Id AS SaleId,
    NULL AS SaleItemId, -- Whole sale discount
    s.UserId AS AppliedByUserId,
    s.SaleDate AS AppliedAt,
    1 AS IsActive
FROM Sales s
WHERE ABS(RANDOM()) % 5 = 0  -- Apply to ~20% of sales
AND s.TotalItems > 0;

-- Apply some item-specific discounts for remaining sales
INSERT INTO Discounts (DiscountTypeId, DiscountValue, OriginalPrice, FinalPrice, ReasonId, 
                      Comment, SaleId, SaleItemId, AppliedByUserId, AppliedAt, IsActive)
SELECT
    -- 80% percentage, 20% fixed amount for individual items
    CASE WHEN ABS(RANDOM()) % 10 < 8 THEN 1 ELSE 2 END AS DiscountTypeId,
    -- Value based on discount type and user role
    CASE 
        -- Percentage discount for items
        WHEN ABS(RANDOM()) % 10 < 8 THEN 
            CASE 
                WHEN s.UserId = 1 THEN ABS(RANDOM()) % 66 + 10    -- Admin: 10-75%
                WHEN s.UserId = 2 THEN ABS(RANDOM()) % 31 + 10    -- Manager: 10-40%
                ELSE ABS(RANDOM()) % 11 + 5                       -- Cashier: 5-15%
            END
        -- Fixed amount discount for items
        ELSE 
            CASE 
                WHEN s.UserId = 1 THEN ABS(RANDOM()) % 46 + 5     -- Admin: $5-$50
                WHEN s.UserId = 2 THEN ABS(RANDOM()) % 26 + 5     -- Manager: $5-$30
                ELSE ABS(RANDOM()) % 9 + 2                        -- Cashier: $2-$10
            END
    END AS DiscountValue,
    si.Total AS OriginalPrice,
    -- Calculate final price based on discount type
    CASE 
        -- Percentage discount for items
        WHEN ABS(RANDOM()) % 10 < 8 THEN 
            si.Total * (1 - (
                CASE 
                    WHEN s.UserId = 1 THEN (ABS(RANDOM()) % 66 + 10)
                    WHEN s.UserId = 2 THEN (ABS(RANDOM()) % 31 + 10)
                    ELSE (ABS(RANDOM()) % 11 + 5)
                END / 100.0
            ))
        -- Fixed amount discount for items
        ELSE 
            si.Total - (
                CASE 
                    WHEN s.UserId = 1 THEN (ABS(RANDOM()) % 46 + 5)
                    WHEN s.UserId = 2 THEN (ABS(RANDOM()) % 26 + 5)
                    ELSE (ABS(RANDOM()) % 9 + 2)
                END
            )
    END AS FinalPrice,
    -- Random discount reason
    (ABS(RANDOM()) % 7) + 1 AS ReasonId,
    -- Generate item-specific comments
    CASE ABS(RANDOM()) % 5
        WHEN 0 THEN 'Display model'
        WHEN 1 THEN 'Last item in stock'
        WHEN 2 THEN 'Price match competitor'
        WHEN 3 THEN 'Packaging damaged'
        ELSE 'Customer loyalty discount'
    END AS Comment,
    s.Id AS SaleId,
    si.Id AS SaleItemId,
    s.UserId AS AppliedByUserId,
    s.SaleDate AS AppliedAt,
    1 AS IsActive
FROM Sales s
JOIN SaleItems si ON s.Id = si.SaleId
WHERE s.Id NOT IN (SELECT SaleId FROM Discounts WHERE SaleItemId IS NULL) -- Exclude sales with whole-sale discounts
AND ABS(RANDOM()) % 10 = 0;  -- Apply to ~10% of individual items

-- Update sales with discounts
UPDATE Sales
SET 
    DiscountAmount = (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ),
    GrandTotal = Subtotal - (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ),
    AmountPaid = Subtotal - (
        SELECT COALESCE(SUM(d.OriginalPrice - d.FinalPrice), 0)
        FROM Discounts d
        WHERE d.SaleId = Sales.Id AND d.IsActive = 1
    ) + Change
WHERE Id IN (SELECT DISTINCT SaleId FROM Discounts);

-- Create weekly purchase orders
INSERT INTO PurchaseOrders (OrderNumber, OrderDate, DueDate, SupplierId, Subtotal, TaxAmount, 
                           GrandTotal, Status, Notes, CreatedByUserId, PaymentMethod, PaymentReference, 
                           PaymentDate, CreatedAt, UpdatedAt)
WITH weeks AS (
    SELECT DISTINCT 
        date(cd.OpenedAt, 'weekday 1') AS monday_date,
        CAST(strftime('%W', cd.OpenedAt) AS INTEGER) AS week_number
    FROM CashDrawers cd
    ORDER BY monday_date
)
SELECT 
    'PO-' || strftime('%y%m%d', monday_date) || '-' || (ABS(RANDOM()) % 3 + 1) AS OrderNumber,
    datetime(monday_date) AS OrderDate,
    datetime(monday_date, '+3 days') AS DueDate,
    CASE (ABS(RANDOM()) % 3 + 1)
        WHEN 1 THEN 2  -- Grocery supplier
        WHEN 2 THEN 3  -- Clothing supplier
        ELSE 1  -- Electronics supplier
    END AS SupplierId,
    0 AS Subtotal, -- Will be calculated after adding items
    0 AS TaxAmount,
    0 AS GrandTotal,
    CASE 
        WHEN date('now') > date(monday_date, '+3 days') THEN 'Received'
        WHEN date('now') > date(monday_date) THEN 'In Transit'
        ELSE 'Pending'
    END AS Status,
    CASE (ABS(RANDOM()) % 3 + 1)
        WHEN 1 THEN 'Weekly grocery restock'
        WHEN 2 THEN 'Clothing inventory replenishment'
        ELSE 'Regular electronics order'
    END AS Notes,
    CASE (ABS(RANDOM()) % 2 + 1)
        WHEN 1 THEN 1  -- Admin
        ELSE 2  -- Manager
    END AS CreatedByUserId,
    CASE (ABS(RANDOM()) % 2 + 1)
        WHEN 1 THEN 'Bank Transfer'
        ELSE 'Credit Card'
    END AS PaymentMethod,
    CASE 
        WHEN (ABS(RANDOM()) % 2 + 1) = 1 THEN 
            'TRX-' || printf('%06d', ABS(RANDOM()) % 900000 + 100000)
        ELSE 
            'CC-AUTH-' || printf('%06d', ABS(RANDOM()) % 900000 + 100000)
    END AS PaymentReference,
    CASE 
        WHEN date('now') > date(monday_date, '+1 day') THEN datetime(monday_date, '+1 day')
        ELSE NULL
    END AS PaymentDate,
    datetime(monday_date) AS CreatedAt,
    datetime(monday_date, '+1 day') AS UpdatedAt
FROM weeks
CROSS JOIN (SELECT 1 AS po_number UNION SELECT 2 UNION SELECT 3) -- 3 POs per week
ORDER BY monday_date, po_number;

-- Add items to purchase orders
INSERT INTO PurchaseOrderItems (PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice, 
                               BatchNumber, Location, Notes, ExpiryDate)
SELECT 
    po.Id AS PurchaseOrderId,
    -- Select appropriate products for the supplier
    CASE 
        WHEN po.SupplierId = 1 THEN (ABS(RANDOM()) % 4) + 1  -- Electronics (1-4)
        WHEN po.SupplierId = 2 THEN (ABS(RANDOM()) % 4) + 5  -- Grocery (5-8)
        ELSE (ABS(RANDOM()) % 4) + 9                         -- Clothing (9-12)
    END AS ProductId,
    -- Order quantity based on product type
    CASE 
        WHEN po.SupplierId = 1 THEN (ABS(RANDOM()) % 6) + 5  -- Electronics: 5-10
        WHEN po.SupplierId = 2 THEN (ABS(RANDOM()) % 31) + 20  -- Grocery: 20-50
        ELSE (ABS(RANDOM()) % 21) + 10                        -- Clothing: 10-30
    END AS Quantity,
    p.PurchasePrice AS UnitCost,
    p.SellingPrice AS SellingPrice,
    'BATCH-' || strftime('%y%m', po.OrderDate) || '-' || p.Id AS BatchNumber,
    CASE 
        WHEN po.SupplierId = 1 THEN 'Electronics Section'
        WHEN po.SupplierId = 2 THEN 'Grocery Department'
        ELSE 'Apparel Area'
    END AS Location,
    'Regular stock replenishment' AS Notes,
    -- Only set expiry for groceries
    CASE 
        WHEN po.SupplierId = 2 THEN datetime(po.OrderDate, '+' || (ABS(RANDOM()) % 61 + 30) || ' days')
        ELSE NULL
    END AS ExpiryDate
FROM PurchaseOrders po
JOIN Products p ON 
    (po.SupplierId = 1 AND p.Id BETWEEN 1 AND 4) OR  -- Electronics supplier
    (po.SupplierId = 2 AND p.Id BETWEEN 5 AND 8) OR  -- Grocery supplier
    (po.SupplierId = 3 AND p.Id BETWEEN 9 AND 12)    -- Clothing supplier
-- Add 2-4 products per purchase order
WHERE ABS(RANDOM()) % 4 < 3;  -- 75% chance of adding each eligible product

-- Update purchase order totals
UPDATE PurchaseOrders
SET 
    Subtotal = (
        SELECT SUM(Quantity * UnitCost) 
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    ),
    TaxAmount = (
        SELECT SUM(Quantity * UnitCost) * 0.1
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    ),
    GrandTotal = (
        SELECT SUM(Quantity * UnitCost) * 1.1
        FROM PurchaseOrderItems 
        WHERE PurchaseOrderId = PurchaseOrders.Id
    );

-- Create inventory transactions for received purchase orders
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
SELECT 
    poi.ProductId,
    'Purchase' AS TransactionType,
    poi.Quantity,
    poi.UnitCost AS UnitPrice,
    po.OrderNumber AS Reference,
    'Purchase order received' AS Notes,
    CASE 
        WHEN po.Status = 'Received' THEN datetime(po.DueDate)
        ELSE NULL
    END AS TransactionDate,
    po.CreatedByUserId AS UserId
FROM PurchaseOrderItems poi
JOIN PurchaseOrders po ON poi.PurchaseOrderId = po.Id
WHERE po.Status = 'Received';

-- Update product stock with received purchase orders
UPDATE Products
SET StockQuantity = StockQuantity + (
    SELECT COALESCE(SUM(it.Quantity), 0)
    FROM InventoryTransactions it
    WHERE it.ProductId = Products.Id AND it.TransactionType = 'Purchase'
);

-- Generate inventory transactions from sales (one for each sale item)
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
SELECT 
    si.ProductId,
    'Sale' AS TransactionType,
    -si.Quantity, -- Negative for sales
    si.UnitPrice,
    s.InvoiceNumber AS Reference,
    'Sale transaction' AS Notes,
    s.SaleDate AS TransactionDate,
    s.UserId
FROM SaleItems si
JOIN Sales s ON si.SaleId = s.Id;

-- Update product stock based on sales
UPDATE Products
SET StockQuantity = StockQuantity + (
    SELECT COALESCE(SUM(it.Quantity), 0)
    FROM InventoryTransactions it
    WHERE it.ProductId = Products.Id AND it.TransactionType = 'Sale'
);

-- Generate cash transactions for each cash drawer
-- Opening balance entries
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    Id AS CashDrawerId,
    'Opening Balance' AS Type,
    OpeningBalance AS Amount,
    OpenedAt AS Timestamp,
    'OPEN-' || Id AS Reference,
    'Daily opening' AS Reason,
    'Cash drawer opened for the day' AS Notes,
    OpenedById AS PerformedById
FROM CashDrawers;

-- Cash payments from sales
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Sale Payment' AS Type,
    s.AmountPaid AS Amount,
    s.SaleDate AS Timestamp,
    s.InvoiceNumber AS Reference,
    'Cash sale' AS Reason,
    'Cash payment received' AS Notes,
    s.UserId AS PerformedById
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash';

-- Add some cash refunds (around 2% of cash sales)
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Refund' AS Type,
    -s.GrandTotal AS Amount, -- Negative for refunds
    datetime(s.SaleDate, '+' || (ABS(RANDOM()) % 3 + 1) || ' hours') AS Timestamp,
    'REF-' || s.InvoiceNumber AS Reference,
    CASE ABS(RANDOM()) % 3
        WHEN 0 THEN 'Customer changed mind'
        WHEN 1 THEN 'Defective product'
        ELSE 'Wrong item purchased'
    END AS Reason,
    'Cash refund issued' AS Notes,
    2 AS PerformedById -- Manager handles refunds
FROM Sales s
JOIN CashDrawers cd ON DATE(s.SaleDate) = DATE(cd.OpenedAt)
WHERE s.PaymentMethod = 'Cash'
AND ABS(RANDOM()) % 50 = 0; -- ~2% of cash sales get refunded

-- Add "No Sale" cash drawer opens
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'No Sale' AS Type,
    0 AS Amount,
    datetime(
        date(cd.OpenedAt), 
        time(
            '09:' || printf('%02d', ABS(RANDOM()) % 60) || ':' || printf('%02d', ABS(RANDOM()) % 60)
        )
    ) AS Timestamp,
    'NOSALE-' || cd.Id || '-' || nosales.rowid AS Reference,
    CASE ABS(RANDOM()) % 3
        WHEN 0 THEN 'Change needed'
        WHEN 1 THEN 'Mistake correction'
        ELSE 'Register check'
    END AS Reason,
    'No sale drawer open' AS Notes,
    CASE WHEN ABS(RANDOM()) % 10 <= 7 THEN 3 ELSE 2 END AS PerformedById -- Mostly cashier, sometimes manager
FROM CashDrawers cd
JOIN (
    SELECT rowid 
    FROM sqlite_master 
    -- Add 1-5 no-sales per day, depending on how busy
    LIMIT (SELECT COUNT(*) FROM CashDrawers) * 3 -- Average 3 no-sales per day
) nosales
ORDER BY cd.Id;

-- Add cash drops (removing excess cash) for busy days
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    cd.Id AS CashDrawerId,
    'Cash Drop' AS Type,
    -500.00 AS Amount, -- Negative for removing cash
    datetime(
        date(cd.OpenedAt), 
        time(
            '15:' || printf('%02d', ABS(RANDOM()) % 60) || ':' || printf('%02d', ABS(RANDOM()) % 60)
        )
    ) AS Timestamp,
    'DROP-' || cd.Id AS Reference,
    'Excess cash storage' AS Reason,
    'Excess cash moved to safe' AS Notes,
    2 AS PerformedById -- Manager handles cash drops
FROM CashDrawers cd
WHERE cd.ExpectedBalance > 1000.00; -- Only for drawers that collected a lot of cash

-- Add closing balance entries
INSERT INTO CashTransactions (CashDrawerId, Type, Amount, Timestamp, Reference, Reason, Notes, PerformedById)
SELECT 
    Id AS CashDrawerId,
    'Closing Balance' AS Type,
    -ActualBalance AS Amount, -- Negative as we're removing all cash
    ClosedAt AS Timestamp,
    'CLOSE-' || Id AS Reference,
    'Daily closing' AS Reason,
    CASE
        WHEN ABS(ExpectedBalance - ActualBalance) < 0.01 THEN 'Balanced perfectly'
        WHEN ActualBalance > ExpectedBalance THEN 'Over by $' || ROUND(ActualBalance - ExpectedBalance, 2)
        ELSE 'Short by $' || ROUND(ExpectedBalance - ActualBalance, 2)
    END AS Notes,
    ClosedById AS PerformedById
FROM CashDrawers;

-- Generate loyalty transactions for customers - points earned from sales
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
SELECT 
    s.CustomerId,
    -- Points calculation (simplified)
    CAST(s.GrandTotal * (
        SELECT lp.PointsPerDollar * lt.PointsMultiplier
        FROM Customers c
        JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
        JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
        WHERE c.Id = s.CustomerId
    ) AS INTEGER) AS Points,
    'Points earned from purchase ' || s.InvoiceNumber AS Description,
    s.SaleDate AS TransactionDate
FROM Sales s
WHERE s.CustomerId IS NOT NULL;

-- Add some point redemptions (about 10% of customers redeem points)
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
SELECT 
    c.Id AS CustomerId,
    -- Redeem a portion of available points (100-500 points)
    -MIN(c.LoyaltyPoints, 100 + (ABS(RANDOM()) % 401)) AS Points,
    'Points redeemed for $' || (ABS(MIN(c.LoyaltyPoints, 100 + (ABS(RANDOM()) % 401))) * 0.01) || ' discount' AS Description,
    datetime('now', '-' || (ABS(RANDOM()) % 30 + 1) || ' days', '+' || (ABS(RANDOM()) % 24) || ' hours') AS TransactionDate
FROM Customers c
WHERE c.LoyaltyPoints >= 100 -- Only if they have enough points
AND ABS(RANDOM()) % 10 = 0; -- 10% chance

-- Update customer loyalty points based on transactions
UPDATE Customers
SET LoyaltyPoints = (
    SELECT COALESCE(SUM(Points), 0)
    FROM LoyaltyTransactions
    WHERE CustomerId = Customers.Id
);

-- Update loyalty tier based on points
UPDATE Customers
SET LoyaltyTierId = (
    SELECT lt.Id
    FROM LoyaltyTiers lt
    WHERE lt.LoyaltyProgramId = 1 -- We only have one loyalty program for now
    AND lt.MinimumPoints <= Customers.LoyaltyPoints
    ORDER BY lt.MinimumPoints DESC
    LIMIT 1
);

-- Add user favorites
INSERT INTO UserFavorites (UserId, ProductId, CreatedAt)
SELECT
    u.Id AS UserId,
    p.Id AS ProductId,
    datetime('now', '-' || (ABS(RANDOM()) % 30 + 1) || ' days') AS CreatedAt
FROM Users u
CROSS JOIN Products p
WHERE ABS(RANDOM()) % 5 = 0 -- 20% chance for each user-product combination
AND NOT EXISTS (
    SELECT 1 FROM UserFavorites uf
    WHERE uf.UserId = u.Id AND uf.ProductId = p.Id
);

COMMIT; 