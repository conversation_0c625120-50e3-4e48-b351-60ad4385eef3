using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Models.DTOs;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    /// <summary>
    /// Service for managing draft invoices in the two-tier invoice system
    /// </summary>
    public class DraftInvoiceService
    {
        private readonly DatabaseService _dbService;
        private readonly DraftInvoiceNotificationService _notificationService;
        private readonly UserPermissionsService _permissionsService;

        public DraftInvoiceService(
            DatabaseService dbService, 
            DraftInvoiceNotificationService notificationService,
            UserPermissionsService permissionsService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _permissionsService = permissionsService ?? throw new ArgumentNullException(nameof(permissionsService));
        }

        /// <summary>
        /// Creates a new draft invoice
        /// </summary>
        public async Task<DraftInvoiceResult> CreateDraftInvoiceAsync(DraftInvoiceDto draftDto, User createdByUser)
        {
            try
            {
                // Validate input
                if (draftDto == null)
                    return DraftInvoiceResult.CreateError("Draft invoice data is required.");

                if (createdByUser == null)
                    return DraftInvoiceResult.CreateError("User information is required.");

                // Validate permissions
                if (!_permissionsService.CanCreateDraftInvoices())
                    return DraftInvoiceResult.CreateError("You don't have permission to create draft invoices.");

                // Validate draft data
                if (!draftDto.IsValid(out List<string> errors))
                    return DraftInvoiceResult.CreateError("Invalid draft invoice data.", errors);

                // Get settings for validation
                var settings = await GetDraftInvoiceSettingsAsync();
                if (draftDto.Items.Count > settings.MaxDraftItemsPerInvoice)
                {
                    return DraftInvoiceResult.CreateError($"Maximum {settings.MaxDraftItemsPerInvoice} items allowed per draft invoice.");
                }

                using var context = new POSDbContext();
                using var transaction = await context.Database.BeginTransactionAsync();

                try
                {
                    // Generate invoice number if not provided
                    if (string.IsNullOrEmpty(draftDto.InvoiceNumber))
                    {
                        draftDto.InvoiceNumber = await GenerateDraftInvoiceNumberAsync();
                    }

                    // Create invoice entity
                    var invoice = new Invoice
                    {
                        InvoiceNumber = draftDto.InvoiceNumber,
                        Type = draftDto.Type,
                        IssueDate = draftDto.IssueDate,
                        DueDate = draftDto.DueDate,
                        CustomerId = draftDto.CustomerId,
                        SupplierId = draftDto.SupplierId,
                        PaymentTerms = draftDto.PaymentTerms,
                        Reference = draftDto.Reference,
                        Notes = draftDto.Notes,
                        Status = "Draft",
                        CreatedByUserId = createdByUser.Id,
                        DraftCreatedAt = DateTime.Now,
                        RequiresAdminCompletion = !_permissionsService.CanCreateFullInvoices()
                    };

                    // Add items
                    foreach (var itemDto in draftDto.Items)
                    {
                        var invoiceItem = new InvoiceItem
                        {
                            ProductId = itemDto.ProductId,
                            ProductName = itemDto.ProductName,
                            Quantity = itemDto.Quantity,
                            UnitPrice = itemDto.UnitPrice,
                            BatchNumber = itemDto.BatchNumber
                        };
                        invoiceItem.CalculateTotal();
                        invoice.Items.Add(invoiceItem);
                    }

                    // Calculate totals
                    invoice.CalculateTotals();

                    // Save to database
                    context.Invoice.Add(invoice);
                    await context.SaveChangesAsync();

                    await transaction.CommitAsync();

                    // Send notification if required
                    if (invoice.RequiresAdminCompletion && settings.AutoNotifyAdmins)
                    {
                        await _notificationService.NotifyDraftCreatedAsync(invoice.Id, createdByUser.Id);
                    }

                    return DraftInvoiceResult.CreateSuccess(invoice.Id, "Draft invoice created successfully.");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception($"Error saving draft invoice: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error creating draft: {ex.Message}");
                return DraftInvoiceResult.CreateError($"Failed to create draft invoice: {ex.Message}");
            }
        }

        /// <summary>
        /// Completes a draft invoice (admin only)
        /// </summary>
        public async Task<DraftInvoiceResult> CompleteDraftInvoiceAsync(int invoiceId, DraftInvoiceDto completionData, User completedByUser)
        {
            try
            {
                // Validate permissions
                if (!_permissionsService.CanCompleteInvoiceDrafts())
                    return DraftInvoiceResult.CreateError("You don't have permission to complete draft invoices.");

                using var context = new POSDbContext();
                using var transaction = await context.Database.BeginTransactionAsync();

                try
                {
                    var invoice = await context.Invoice
                        .Include(i => i.Items)
                        .Include(i => i.CreatedByUser)
                        .FirstOrDefaultAsync(i => i.Id == invoiceId);

                    if (invoice == null)
                        return DraftInvoiceResult.CreateError("Draft invoice not found.");

                    if (!invoice.RequiresAdminCompletion)
                        return DraftInvoiceResult.CreateError("This invoice does not require admin completion.");

                    // Update invoice with completion data
                    invoice.DiscountAmount = completionData.DiscountAmount;
                    invoice.TaxAmount = completionData.TaxAmount;
                    invoice.PaymentTerms = completionData.PaymentTerms;
                    invoice.Reference = completionData.Reference;
                    invoice.Notes = completionData.Notes;
                    invoice.IssueDate = completionData.IssueDate;
                    invoice.DueDate = completionData.DueDate;

                    // Mark as completed
                    invoice.MarkAsCompleted(completedByUser);

                    // Recalculate totals
                    invoice.CalculateTotals();

                    await context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    // Send completion notification
                    await _notificationService.NotifyDraftCompletedAsync(
                        invoice.Id, 
                        invoice.CreatedByUserId, 
                        completedByUser.FirstName + " " + completedByUser.LastName);

                    return DraftInvoiceResult.CreateSuccess(invoice.Id, "Draft invoice completed successfully.");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception($"Error completing draft invoice: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error completing draft: {ex.Message}");
                return DraftInvoiceResult.CreateError($"Failed to complete draft invoice: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all pending draft invoices
        /// </summary>
        public async Task<List<Invoice>> GetPendingDraftInvoicesAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await context.Invoice
                    .Include(i => i.Items)
                    .Include(i => i.CreatedByUser)
                    .Include(i => i.Customer)
                    .Where(i => i.RequiresAdminCompletion && i.Status == "Draft")
                    .OrderBy(i => i.DraftCreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error getting pending drafts: {ex.Message}");
                return new List<Invoice>();
            }
        }

        /// <summary>
        /// Gets draft invoices created by a specific user
        /// </summary>
        public async Task<List<Invoice>> GetUserDraftInvoicesAsync(int userId)
        {
            try
            {
                using var context = new POSDbContext();
                return await context.Invoice
                    .Include(i => i.Items)
                    .Include(i => i.CompletedByUser)
                    .Include(i => i.Customer)
                    .Where(i => i.CreatedByUserId == userId)
                    .OrderByDescending(i => i.DraftCreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error getting user drafts: {ex.Message}");
                return new List<Invoice>();
            }
        }

        /// <summary>
        /// Deletes a draft invoice
        /// </summary>
        public async Task<DraftInvoiceResult> DeleteDraftInvoiceAsync(int invoiceId, User deletedByUser)
        {
            try
            {
                // Validate permissions
                if (!_permissionsService.CanDeleteDraftInvoices())
                    return DraftInvoiceResult.CreateError("You don't have permission to delete draft invoices.");

                using var context = new POSDbContext();
                var invoice = await context.Invoice.FindAsync(invoiceId);

                if (invoice == null)
                    return DraftInvoiceResult.CreateError("Draft invoice not found.");

                if (!invoice.CanBeDeleted())
                    return DraftInvoiceResult.CreateError("This invoice cannot be deleted.");

                context.Invoice.Remove(invoice);
                await context.SaveChangesAsync();

                return DraftInvoiceResult.CreateSuccess(invoiceId, "Draft invoice deleted successfully.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error deleting draft: {ex.Message}");
                return DraftInvoiceResult.CreateError($"Failed to delete draft invoice: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets draft invoice settings
        /// </summary>
        public async Task<DraftInvoiceSettings> GetDraftInvoiceSettingsAsync()
        {
            try
            {
                using var context = new POSDbContext();
                var settings = await context.DraftInvoiceSettings.FirstOrDefaultAsync();
                return settings ?? DraftInvoiceSettings.GetDefaultSettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error getting settings: {ex.Message}");
                return DraftInvoiceSettings.GetDefaultSettings();
            }
        }

        /// <summary>
        /// Updates draft invoice settings
        /// </summary>
        public async Task<bool> UpdateDraftInvoiceSettingsAsync(DraftInvoiceSettings settings)
        {
            try
            {
                if (!_permissionsService.CanManageInvoiceSettings())
                    return false;

                if (!settings.IsValid(out string errorMessage))
                {
                    System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Invalid settings: {errorMessage}");
                    return false;
                }

                using var context = new POSDbContext();
                var existingSettings = await context.DraftInvoiceSettings.FirstOrDefaultAsync();

                if (existingSettings != null)
                {
                    // Update existing settings
                    existingSettings.ExpirationDays = settings.ExpirationDays;
                    existingSettings.AutoNotifyAdmins = settings.AutoNotifyAdmins;
                    existingSettings.AllowNonAdminCustomerSelection = settings.AllowNonAdminCustomerSelection;
                    existingSettings.RequireReasonForDraft = settings.RequireReasonForDraft;
                    existingSettings.MaxDraftItemsPerInvoice = settings.MaxDraftItemsPerInvoice;
                    existingSettings.UpdateTimestamp();
                }
                else
                {
                    // Create new settings
                    context.DraftInvoiceSettings.Add(settings);
                }

                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error updating settings: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates a unique draft invoice number
        /// </summary>
        private async Task<string> GenerateDraftInvoiceNumberAsync()
        {
            try
            {
                using var context = new POSDbContext();
                var today = DateTime.Now;
                var datePrefix = today.ToString("yyyyMMdd");
                
                var lastInvoice = await context.Invoice
                    .Where(i => i.InvoiceNumber.StartsWith($"DRAFT-{datePrefix}"))
                    .OrderByDescending(i => i.InvoiceNumber)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastInvoice != null)
                {
                    var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                    if (int.TryParse(lastNumberPart, out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"DRAFT-{datePrefix}-{nextNumber:D4}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_SERVICE] Error generating invoice number: {ex.Message}");
                return $"DRAFT-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }
    }
}
