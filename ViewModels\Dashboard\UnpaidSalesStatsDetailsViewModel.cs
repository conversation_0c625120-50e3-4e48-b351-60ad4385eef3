using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Windows;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels;
using POSSystem.Data;
using System.Windows.Threading;

namespace POSSystem.ViewModels.Dashboard
{
    public class UnpaidSalesStatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private readonly Customer _filterCustomer;
        private bool _isLoading;
        private string _title;
        private string _subtitle;
        private ICommand _viewSaleCommand;

        // Statistics
        private int _totalUnpaidSales;
        private decimal _totalUnpaidAmount;
        private int _pastDueSales;
        private decimal _pastDueAmount;

        // Collections
        private ObservableCollection<UnpaidSaleItem> _unpaidSales;
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;

        // Chart data
        private SeriesCollection _unpaidTrendSeries;
        private string[] _unpaidTrendLabels;
        private SeriesCollection _ageDistributionSeries;
        private SeriesCollection _amountRangeSeries;
        private string[] _amountRangeLabels;

        public event PropertyChangedEventHandler PropertyChanged;

        public UnpaidSalesStatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService,
            Customer filterCustomer = null)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;
            _filterCustomer = filterCustomer;

            // Initialize collections
            UnpaidSales = new ObservableCollection<UnpaidSaleItem>();

            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };

            // Set initial period
            SelectedTrendPeriod = TrendPeriods.First();

            // Set title and subtitle based on whether we're filtering by customer
            if (_filterCustomer != null)
            {
                Title = $"{Application.Current.TryFindResource("UnpaidSales")?.ToString() ?? "Unpaid Sales"} - {_filterCustomer.FullName}";
                Subtitle = $"{Application.Current.TryFindResource("DetailedUnpaidSalesMetrics")?.ToString() ?? "Detailed unpaid sales metrics and trends"} for {_filterCustomer.FullName}";
            }
            else
            {
                Title = Application.Current.TryFindResource("UnpaidSales")?.ToString() ?? "Unpaid Sales";
                Subtitle = Application.Current.TryFindResource("DetailedUnpaidSalesMetrics")?.ToString() ?? "Detailed unpaid sales metrics and trends";
            }

            // Initialize chart series
            UnpaidTrendSeries = new SeriesCollection();
            AgeDistributionSeries = new SeriesCollection();
            AmountRangeSeries = new SeriesCollection();
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public bool IsCustomerFiltered => _filterCustomer != null;

        public string CustomerName => _filterCustomer?.FullName ?? string.Empty;

        public string CustomerInfo
        {
            get
            {
                if (_filterCustomer == null) return string.Empty;

                var info = new List<string>();
                if (!string.IsNullOrEmpty(_filterCustomer.Phone))
                    info.Add($"Phone: {_filterCustomer.Phone}");
                if (!string.IsNullOrEmpty(_filterCustomer.Email))
                    info.Add($"Email: {_filterCustomer.Email}");

                return string.Join(" • ", info);
            }
        }

        public bool HasUnpaidSales => UnpaidSales?.Count > 0;

        public string NoDataMessage
        {
            get
            {
                if (_filterCustomer != null)
                {
                    return $"No unpaid invoices found for {_filterCustomer.FullName}";
                }
                return "No unpaid invoices found";
            }
        }

        public int TotalUnpaidSales
        {
            get => _totalUnpaidSales;
            set { _totalUnpaidSales = value; OnPropertyChanged(); }
        }

        public decimal TotalUnpaidAmount
        {
            get => _totalUnpaidAmount;
            set { _totalUnpaidAmount = value; OnPropertyChanged(); }
        }

        public int PastDueSales
        {
            get => _pastDueSales;
            set { _pastDueSales = value; OnPropertyChanged(); }
        }

        public decimal PastDueAmount
        {
            get => _pastDueAmount;
            set { _pastDueAmount = value; OnPropertyChanged(); }
        }

        public ObservableCollection<UnpaidSaleItem> UnpaidSales
        {
            get => _unpaidSales;
            set { _unpaidSales = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set
            {
                if (_selectedTrendPeriod != value)
                {
                    _selectedTrendPeriod = value;
                    OnPropertyChanged();
                    _ = LoadDataAsync();
                }
            }
        }

        public SeriesCollection UnpaidTrendSeries
        {
            get => _unpaidTrendSeries;
            set { _unpaidTrendSeries = value; OnPropertyChanged(); }
        }

        public string[] UnpaidTrendLabels
        {
            get => _unpaidTrendLabels;
            set { _unpaidTrendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection AgeDistributionSeries
        {
            get => _ageDistributionSeries;
            set { _ageDistributionSeries = value; OnPropertyChanged(); }
        }

        public SeriesCollection AmountRangeSeries
        {
            get => _amountRangeSeries;
            set { _amountRangeSeries = value; OnPropertyChanged(); }
        }

        public string[] AmountRangeLabels
        {
            get => _amountRangeLabels;
            set { _amountRangeLabels = value; OnPropertyChanged(); }
        }

        public ICommand ViewSaleCommand
        {
            get
            {
                return _viewSaleCommand ?? (_viewSaleCommand = new RelayCommand<UnpaidSaleItem>(async item =>
                {
                    if (item != null)
                    {
                        try
                        {
                            // Close any existing dialogs first
                            if (DialogHost.IsDialogOpen("RootDialog"))
                            {
                                DialogHost.Close("RootDialog");
                                // Add a small delay to ensure the dialog is fully closed
                                await Task.Delay(100);
                            }

                            // Show sale details dialog
                            var dialog = new SaleDetailsDialog(item._sale);
                            await DialogHost.Show(dialog, "RootDialog");
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(
                                $"Error viewing sale: {ex.Message}",
                                "Error",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }));
            }
        }

        public async Task LoadDataAsync()
        {
            if (_isLoading) return;

            try
            {
                IsLoading = true;

                // Add a small delay to allow the dialog to render
                await Task.Delay(300);

                // Load data in parallel
                var unpaidSalesTask = _dbService.GetUnpaidSalesAsync();
                await Task.WhenAll(unpaidSalesTask);
                var allUnpaidSales = await unpaidSalesTask;

                System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Retrieved {allUnpaidSales.Count} unpaid sales from database");

                // Debug: Show all customer IDs in the data
                var customerIds = allUnpaidSales.Select(s => s.CustomerId).Distinct().ToList();
                System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Customer IDs in unpaid sales: {string.Join(", ", customerIds.Select(id => id?.ToString() ?? "NULL"))}");

                // Filter by customer if specified
                List<Sale> unpaidSales;
                if (_filterCustomer != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Filtering for customer ID: {_filterCustomer.Id}, Name: {_filterCustomer.FullName}");

                    unpaidSales = allUnpaidSales.Where(s => s.CustomerId.HasValue && s.CustomerId.Value == _filterCustomer.Id).ToList();

                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Filtered to {unpaidSales.Count} unpaid sales for customer: {_filterCustomer.FullName}");

                    // Debug: Show details of filtered sales
                    foreach (var sale in unpaidSales)
                    {
                        System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Sale ID: {sale.Id}, Invoice: {sale.InvoiceNumber}, Customer ID: {sale.CustomerId}, Amount: {sale.RemainingAmount:C}");
                    }
                }
                else
                {
                    unpaidSales = allUnpaidSales;
                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] No customer filter applied, showing all {unpaidSales.Count} unpaid sales");
                }

                if (unpaidSales.Any())
                {
                    var statuses = unpaidSales.Select(s => s.PaymentStatus).Distinct().ToList();
                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Payment statuses found: {string.Join(", ", statuses)}");
                    var totalAmount = unpaidSales.Sum(s => s.RemainingAmount);
                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Total unpaid amount: {totalAmount:C}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] No unpaid sales found after filtering");
                }

                // Calculate metrics
                var metrics = CalculateMetrics(unpaidSales);

                // Update UI in a single batch
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    using (new BatchUpdate(this))
                    {
                        TotalUnpaidSales = metrics.TotalSales;
                        TotalUnpaidAmount = metrics.TotalAmount;
                        PastDueSales = metrics.PastDueSales;
                        PastDueAmount = metrics.PastDueAmount;

                        // Update unpaid sales list
                        UnpaidSales.Clear();
                        System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Updating UI with {unpaidSales.Count} sales");

                        foreach (var sale in unpaidSales.OrderByDescending(s => s.SaleDate))
                        {
                            var unpaidItem = new UnpaidSaleItem(sale);
                            UnpaidSales.Add(unpaidItem);
                            System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] Added sale to UI: Invoice {sale.InvoiceNumber}, Customer: {unpaidItem.CustomerName}, Amount: {sale.RemainingAmount:C}");
                        }

                        System.Diagnostics.Debug.WriteLine($"[UNPAID STATS DEBUG] UnpaidSales collection now has {UnpaidSales.Count} items");

                        // Trigger property change notifications for dependent properties
                        OnPropertyChanged(nameof(HasUnpaidSales));
                        OnPropertyChanged(nameof(NoDataMessage));
                    }
                }, DispatcherPriority.Background);

                // Update charts in the background
                await Task.Run(async () => await UpdateChartsAsync(unpaidSales));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading unpaid sales stats data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private (int TotalSales, decimal TotalAmount, int PastDueSales, decimal PastDueAmount) CalculateMetrics(List<Sale> sales)
        {
            var now = DateTime.Now;
            return (
                TotalSales: sales.Count,
                TotalAmount: sales.Sum(s => s.RemainingAmount),
                PastDueSales: sales.Count(s => s.DueDate < now),
                PastDueAmount: sales.Where(s => s.DueDate < now).Sum(s => s.RemainingAmount)
            );
        }

        private async Task UpdateChartsAsync(List<Sale> sales)
        {
            try
            {
                // Prepare data in parallel
                var trendData = await Task.Run(() => PrepareChartData(sales));
                var ageData = await Task.Run(() => PrepareAgeDistributionData(sales));
                var amountData = await Task.Run(() => PrepareAmountRangeData(sales));

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    using (new BatchUpdate(this))
                    {
                        // Create trend series
                        UnpaidTrendSeries = new SeriesCollection
                        {
                            new LineSeries
                            {
                                Title = "Unpaid Sales",
                                Values = new ChartValues<decimal>(trendData.Values),
                                PointGeometry = DefaultGeometries.Circle,
                                PointGeometrySize = 8
                            }
                        };
                        UnpaidTrendLabels = trendData.Labels;

                        // Create age distribution series
                        var colors = new List<Color>
                        {
                            Colors.DodgerBlue,
                            Colors.OrangeRed,
                            Colors.ForestGreen,
                            Colors.Purple,
                            Colors.Gold
                        };

                        AgeDistributionSeries = new SeriesCollection();
                        for (int i = 0; i < ageData.Count; i++)
                        {
                            var color = colors[i % colors.Count];
                            AgeDistributionSeries.Add(new PieSeries
                            {
                                Title = ageData[i].Category,
                                Values = new ChartValues<decimal> { ageData[i].Amount },
                                DataLabels = true,
                                Fill = new SolidColorBrush(color)
                            });
                        }

                        // Create amount range series
                        AmountRangeSeries = new SeriesCollection
                        {
                            new ColumnSeries
                            {
                                Title = "Sales",
                                Values = new ChartValues<int>(amountData.Values),
                                DataLabels = true
                            }
                        };
                        AmountRangeLabels = amountData.Labels;
                    }
                }, DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating charts: {ex.Message}");
            }
        }

        private (List<decimal> Values, string[] Labels) PrepareChartData(List<Sale> sales)
        {
            var trendData = sales
                .GroupBy(s => s.SaleDate.Date)
                .OrderBy(g => g.Key)
                .Take(30)
                .Select(g => new { Date = g.Key, Amount = g.Sum(s => s.RemainingAmount) })
                .ToList();

            return (
                Values: trendData.Select(d => d.Amount).ToList(),
                Labels: trendData.Select(d => d.Date.ToString("MMM dd, yyyy")).ToArray()
            );
        }

        private List<(string Category, decimal Amount)> PrepareAgeDistributionData(List<Sale> sales)
        {
            var now = DateTime.Now;
            return sales
                .GroupBy(s => GetAgeCategory(s.SaleDate, now))
                .Select(g => (
                    Category: g.Key,
                    Amount: g.Sum(s => s.RemainingAmount)
                ))
                .OrderByDescending(x => x.Amount)
                .ToList();
        }

        private (List<int> Values, string[] Labels) PrepareAmountRangeData(List<Sale> sales)
        {
            var ranges = new[]
            {
                (Min: 0m, Max: 1000m, Label: "0-1000"),
                (Min: 1000m, Max: 5000m, Label: "1000-5000"),
                (Min: 5000m, Max: 10000m, Label: "5000-10000"),
                (Min: 10000m, Max: decimal.MaxValue, Label: "10000+")
            };

            var data = ranges
                .Select(r => new
                {
                    Label = r.Label,
                    Count = sales.Count(s => s.RemainingAmount >= r.Min && s.RemainingAmount < r.Max)
                })
                .ToList();

            return (
                Values: data.Select(d => d.Count).ToList(),
                Labels: data.Select(d => d.Label).ToArray()
            );
        }

        private string GetAgeCategory(DateTime saleDate, DateTime now)
        {
            var age = (now - saleDate).Days;
            if (age <= 7) return "1 Week";
            if (age <= 30) return "1 Month";
            if (age <= 90) return "3 Months";
            if (age <= 180) return "6 Months";
            return "Over 6 Months";
        }

        // Helper class for batching property changes
        private class BatchUpdate : IDisposable
        {
            private readonly UnpaidSalesStatsDetailsViewModel _viewModel;
            private readonly bool _previousState;

            public BatchUpdate(UnpaidSalesStatsDetailsViewModel viewModel)
            {
                _viewModel = viewModel;
                _previousState = _viewModel._isBatchUpdate;
                _viewModel._isBatchUpdate = true;
            }

            public void Dispose()
            {
                _viewModel._isBatchUpdate = _previousState;
                _viewModel.OnPropertyChanged(string.Empty);
            }
        }

        private bool _isBatchUpdate;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            if (!_isBatchUpdate)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }

    public class UnpaidSaleItem : INotifyPropertyChanged
    {
        internal readonly Sale _sale;

        public int Id => _sale.Id;
        public string InvoiceNumber => _sale.InvoiceNumber;
        public DateTime SaleDate => _sale.SaleDate;
        public DateTime? DueDate => _sale.DueDate;
        public int? CustomerId => _sale.CustomerId;
        public Customer Customer => _sale.Customer;
        public decimal Subtotal => _sale.Subtotal;
        public decimal DiscountAmount => _sale.DiscountAmount;
        public decimal TaxAmount => _sale.TaxAmount;
        public decimal GrandTotal => _sale.GrandTotal;
        public string PaymentMethod => _sale.PaymentMethod;
        public string PaymentStatus => _sale.PaymentStatus;
        public decimal AmountPaid => _sale.AmountPaid;
        public decimal RemainingAmount => _sale.RemainingAmount;
        public string Status => _sale.Status;
        public int TotalItems => _sale.TotalItems;
        public ICollection<SaleItem> Items => _sale.Items;

        public bool IsPastDue => DueDate.HasValue && DueDate.Value < DateTime.Now;
        public string StatusText => IsPastDue ? 
            Application.Current.TryFindResource("Past Due")?.ToString() ?? "Past Due" : 
            Application.Current.TryFindResource("Unpaid")?.ToString() ?? "Unpaid";
        public int DaysOverdue => IsPastDue ? (DateTime.Now - DueDate.Value).Days : 0;
        public string CustomerName => Customer?.FullName ?? "N/A";

        public UnpaidSaleItem(Sale sale)
        {
            _sale = sale ?? throw new ArgumentNullException(nameof(sale));
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 