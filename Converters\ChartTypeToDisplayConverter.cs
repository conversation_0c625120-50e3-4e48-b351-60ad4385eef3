using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.ViewModels;

namespace POSSystem.Converters
{
    public class ChartTypeToDisplayConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ChartType chartType)
            {
                return chartType switch
                {
                    ChartType.Line => "Line Chart",
                    ChartType.Bar => "Bar Chart",
                    _ => value.ToString()
                };
            }
            if (value != null)
            {
                return value.ToString();
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue switch
                {
                    "Line Chart" => ChartType.Line,
                    "Bar Chart" => ChartType.Bar,
                    _ => ChartType.Line
                };
            }
            return ChartType.Line;
        }
    }
} 