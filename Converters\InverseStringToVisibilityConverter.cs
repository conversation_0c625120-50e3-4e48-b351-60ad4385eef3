using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class InverseStringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return Visibility.Collapsed;

            string stringValue = value.ToString();
            string[] targetValues = parameter.ToString().Split('|');

            // If the current status matches any of the target values, hide the element
            foreach (var target in targetValues)
            {
                if (string.Equals(stringValue, target.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return Visibility.Collapsed;
                }
            }

            // If no match is found, show the element
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 