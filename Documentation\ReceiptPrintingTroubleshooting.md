# Receipt Printing System Troubleshooting Guide

## Common PDF Export Issues and Solutions

### Issue: "Receipt printing failed" with PDF backup enabled

**Symptoms:**
- Receipt printing reports failure
- Error message: "Receipt printing failed. The sale was completed successfully, but the receipt could not be printed."
- PDF backup files are not created

**Root Causes and Solutions:**

#### 1. Database Tables Not Initialized
**Cause:** Receipt printing tables don't exist in the database
**Solution:**
```sql
-- Run the migration script
-- Execute: Migrations/AddReceiptPrintingTables.sql
```
**Verification:** Check if tables exist:
- ReceiptTemplates
- PrinterConfigurations  
- ReceiptPrintSettings
- ReceiptPrintJobs

#### 2. PDF Backup Path Not Configured
**Cause:** `PdfBackupPath` is null or empty in settings
**Solution:**
1. Go to Settings > Receipt Printing
2. Set a valid PDF backup directory path
3. Ensure the directory exists and is writable
4. Test with "Save Test PDF" button

#### 3. Directory Permission Issues
**Cause:** No write permissions to PDF backup directory
**Solution:**
1. Check directory permissions
2. Try a different directory (e.g., Documents/POS Receipts)
3. Run application as administrator (temporary test)

#### 4. XPS Document Creation Failures
**Cause:** Threading issues or missing WPF context
**Solution:** 
- The system now creates XPS documents on the UI thread
- Ensure application has proper WPF context

### Diagnostic Steps

#### Step 1: Test System Configuration
1. Go to Settings > Receipt Printing
2. Click "Test Configuration" button
3. Verify all components are properly initialized

#### Step 2: Test PDF Export
1. Go to Settings > Receipt Printing  
2. Click "Save Test PDF" button
3. Check if test file is created successfully

#### Step 3: Check Debug Output
Monitor debug console for detailed error messages:
```
[RECEIPT PRINT ERROR] Error message details
[RECEIPT PDF EXPORT ERROR] Specific export errors
[RECEIPT DB INIT] Database initialization status
```

#### Step 4: Verify File System Access
1. Check if backup directory exists
2. Test write permissions by creating a test file
3. Ensure no antivirus blocking file creation

### Error Messages and Solutions

#### "PDF backup path is not configured"
**Solution:** Set PdfBackupPath in receipt printing settings

#### "Invalid export path: Directory is null or empty"
**Solution:** Provide valid file path with existing directory

#### "No write permission to directory"
**Solution:** Change directory or fix permissions

#### "Error creating XPS document"
**Solution:** Ensure proper WPF context and document is valid

#### "Database table may not exist yet"
**Solution:** Run database migration or initialization

### Configuration Verification

#### Check Default Settings
The system creates these defaults if database is not initialized:
- PDF Backup Path: `Documents/POS Receipts`
- Auto Print: Enabled
- PDF Backup: Disabled (enable manually)
- Print Preview: Enabled

#### Verify Database Initialization
Check debug output for:
```
[RECEIPT DB INIT] Starting receipt printing database initialization
[RECEIPT DB INIT] Created default receipt template
[RECEIPT DB INIT] Created default printer configuration
[RECEIPT DB INIT] Created default print settings with PDF path: [path]
```

### Manual Testing Procedures

#### Test 1: Basic PDF Export
```csharp
// Create test sale and export
var testService = new ReceiptPrintingTestService(receiptPrintService);
bool success = await testService.TestPdfExportAsync();
```

#### Test 2: Configuration Validation
```csharp
// Verify system configuration
var testService = new ReceiptPrintingTestService(receiptPrintService);
bool configValid = await testService.TestSystemConfigurationAsync();
```

#### Test 3: Manual File Creation
```csharp
// Test file system access
string testPath = Path.Combine(backupPath, "test.txt");
File.WriteAllText(testPath, "test");
File.Delete(testPath);
```

### Performance Considerations

#### XPS Document Size
- Typical receipt: 50-200 KB
- Large receipts with many items: 500 KB - 1 MB
- Monitor disk space in backup directory

#### Threading
- XPS creation runs on UI thread to avoid context issues
- PDF backup runs asynchronously to avoid blocking sales

### Advanced Troubleshooting

#### Enable Detailed Logging
Add to application configuration:
```xml
<system.diagnostics>
  <trace autoflush="true">
    <listeners>
      <add name="textWriterTraceListener" 
           type="System.Diagnostics.TextWriterTraceListener" 
           initializeData="receipt_printing.log" />
    </listeners>
  </trace>
</system.diagnostics>
```

#### Database Debugging
Check Entity Framework logs for database issues:
```csharp
// Enable EF logging
optionsBuilder.LogTo(Console.WriteLine, LogLevel.Information);
```

#### File System Monitoring
Use Process Monitor to track file system access:
1. Filter by process name
2. Monitor file creation attempts
3. Check for access denied errors

### Recovery Procedures

#### Reset to Default Configuration
1. Delete receipt printing settings from database
2. Restart application
3. System will recreate default settings

#### Manual Database Initialization
```sql
-- Insert default settings manually
INSERT INTO ReceiptPrintSettings (
    AutoPrintEnabled, PdfBackupPath, EnablePrintPreview
) VALUES (1, 'C:\POS Receipts', 1);
```

#### Fallback PDF Export
If XPS fails, implement alternative export:
```csharp
// Use print to file as fallback
var printDialog = new PrintDialog();
printDialog.PrintToFile = true;
```

### Contact Support

If issues persist after following this guide:
1. Collect debug logs
2. Note exact error messages
3. Provide system configuration details
4. Include sample receipt data that fails

For technical support, include:
- Operating system version
- .NET Framework version  
- Database file location and size
- User account permissions
- Antivirus software details
