using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Custom localized message dialog that replaces MessageBox with properly translated buttons
    /// </summary>
    public partial class LocalizedMessageDialog : UserControl
    {
        public enum MessageBoxResult
        {
            None = 0,
            OK = 1,
            Cancel = 2,
            Yes = 6,
            No = 7
        }

        public enum MessageBoxButton
        {
            OK = 0,
            OKCancel = 1,
            YesNoCancel = 3,
            YesNo = 4
        }

        public enum MessageBoxImage
        {
            None = 0,
            Error = 16,
            Question = 32,
            Warning = 48,
            Information = 64
        }

        public MessageBoxResult Result { get; private set; } = MessageBoxResult.None;

        public LocalizedMessageDialog()
        {
            InitializeComponent();
        }

        public static async System.Threading.Tasks.Task<MessageBoxResult> ShowAsync(
            string message,
            string title = "",
            MessageBoxButton button = MessageBoxButton.OK,
            MessageBoxImage icon = MessageBoxImage.Information)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] ShowAsync called with message: '{message.Substring(0, Math.Min(50, message.Length))}...'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] ShowAsync title: '{title}'");

            var dialog = new LocalizedMessageDialog();
            dialog.SetupDialog(message, title, button, icon);

            try
            {
                // Try different DialogHost identifiers in order of preference
                string[] identifiers = { "SalesDialog", "MainWindowCashDrawerDialog", "RootDialog", "MainSalesDialog", "MainDialog" };

                foreach (var identifier in identifiers)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Trying DialogHost identifier: {identifier}");

                        // Refresh button text right before showing the dialog to ensure latest resources are used
                        dialog.LoadLocalizedButtonText();

                        await DialogHost.Show(dialog, identifier);
                        System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Successfully showed dialog with identifier: {identifier}");
                        return dialog.Result;
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] DialogHost identifier '{identifier}' not found: {ex.Message}");
                        // Try next identifier
                        continue;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Error with identifier '{identifier}': {ex.Message}");
                        continue;
                    }
                }

                // If no DialogHost works, try without identifier
                try
                {
                    System.Diagnostics.Debug.WriteLine("[LocalizedMessageDialog] Trying DialogHost without identifier");

                    // Refresh button text again before final attempt
                    dialog.LoadLocalizedButtonText();

                    await DialogHost.Show(dialog);
                    System.Diagnostics.Debug.WriteLine("[LocalizedMessageDialog] Successfully showed dialog without identifier");
                    return dialog.Result;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Failed to show dialog without identifier: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Outer exception: {ex.Message}");
            }

            // Fallback to standard MessageBox if all else fails
            System.Diagnostics.Debug.WriteLine("[LocalizedMessageDialog] Falling back to standard MessageBox");
            var standardResult = System.Windows.MessageBox.Show(message, title,
                (System.Windows.MessageBoxButton)button,
                (System.Windows.MessageBoxImage)icon);
            return (MessageBoxResult)standardResult;
        }

        private void SetupDialog(string message, string title, MessageBoxButton button, MessageBoxImage icon)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] SetupDialog called");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Message: '{message}'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Title: '{title}'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Button type: {button}");

            // Set message and title
            MessageTextBlock.Text = message;
            TitleTextBlock.Text = string.IsNullOrEmpty(title) ? GetDefaultTitle(icon) : title;

            // Set icon
            SetDialogIcon(icon);

            // Setup buttons based on type
            SetupButtons(button);

            // Load localized button text
            LoadLocalizedButtonText();

            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] SetupDialog completed");
        }

        private string GetDefaultTitle(MessageBoxImage icon)
        {
            return icon switch
            {
                MessageBoxImage.Error => Application.Current.FindResource("DialogErrorTitle") as string ?? "Error",
                MessageBoxImage.Warning => Application.Current.FindResource("DialogWarningTitle") as string ?? "Warning",
                MessageBoxImage.Question => Application.Current.FindResource("DialogConfirmTitle") as string ?? "Confirm",
                MessageBoxImage.Information => Application.Current.FindResource("DialogInfoTitle") as string ?? "Information",
                _ => Application.Current.FindResource("DialogInfoTitle") as string ?? "Information"
            };
        }

        private void SetDialogIcon(MessageBoxImage icon)
        {
            DialogIcon.Kind = icon switch
            {
                MessageBoxImage.Error => PackIconKind.AlertCircle,
                MessageBoxImage.Warning => PackIconKind.Alert,
                MessageBoxImage.Question => PackIconKind.HelpCircle,
                MessageBoxImage.Information => PackIconKind.Information,
                _ => PackIconKind.Information
            };

            DialogIcon.Foreground = icon switch
            {
                MessageBoxImage.Error => System.Windows.Media.Brushes.Red,
                MessageBoxImage.Warning => System.Windows.Media.Brushes.Orange,
                MessageBoxImage.Question => System.Windows.Media.Brushes.Blue,
                MessageBoxImage.Information => System.Windows.Media.Brushes.Blue,
                _ => System.Windows.Media.Brushes.Blue
            };
        }

        private void SetupButtons(MessageBoxButton button)
        {
            // Hide all buttons first
            YesButton.Visibility = Visibility.Collapsed;
            NoButton.Visibility = Visibility.Collapsed;
            CancelButton.Visibility = Visibility.Collapsed;
            OKButton.Visibility = Visibility.Collapsed;

            // Show appropriate buttons
            switch (button)
            {
                case MessageBoxButton.OK:
                    OKButton.Visibility = Visibility.Visible;
                    OKButton.IsDefault = true;
                    break;
                case MessageBoxButton.OKCancel:
                    OKButton.Visibility = Visibility.Visible;
                    CancelButton.Visibility = Visibility.Visible;
                    OKButton.IsDefault = true;
                    CancelButton.IsCancel = true;
                    break;
                case MessageBoxButton.YesNo:
                    YesButton.Visibility = Visibility.Visible;
                    NoButton.Visibility = Visibility.Visible;
                    YesButton.IsDefault = true;
                    break;
                case MessageBoxButton.YesNoCancel:
                    YesButton.Visibility = Visibility.Visible;
                    NoButton.Visibility = Visibility.Visible;
                    CancelButton.Visibility = Visibility.Visible;
                    YesButton.IsDefault = true;
                    CancelButton.IsCancel = true;
                    break;
            }
        }

        public void LoadLocalizedButtonText()
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] LoadLocalizedButtonText called");

            try
            {
                // Check if Application.Current is available
                if (Application.Current == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] ERROR: Application.Current is null");
                    YesButton.Content = "Yes";
                    NoButton.Content = "No";
                    CancelButton.Content = "Cancel";
                    OKButton.Content = "OK";
                    return;
                }

                // Try to find each resource key
                object yesResource = null;
                object noResource = null;
                object cancelResource = null;
                object okResource = null;

                try { yesResource = Application.Current.FindResource("ButtonYes"); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Error finding ButtonYes: {ex.Message}"); }
                try { noResource = Application.Current.FindResource("ButtonNo"); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Error finding ButtonNo: {ex.Message}"); }
                try { cancelResource = Application.Current.FindResource("ButtonCancel"); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Error finding ButtonCancel: {ex.Message}"); }
                try { okResource = Application.Current.FindResource("ButtonOK"); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Error finding ButtonOK: {ex.Message}"); }

                var yesText = yesResource as string ?? "Yes";
                var noText = noResource as string ?? "No";
                var cancelText = cancelResource as string ?? "Cancel";
                var okText = okResource as string ?? "OK";

                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Raw resources: Yes={yesResource}, No={noResource}, Cancel={cancelResource}, OK={okResource}");
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Button texts: Yes='{yesText}', No='{noText}', Cancel='{cancelText}', OK='{okText}'");

                YesButton.Content = yesText;
                NoButton.Content = noText;
                CancelButton.Content = cancelText;
                OKButton.Content = okText;

                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Button content set successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] ERROR in LoadLocalizedButtonText: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageDialog] Stack trace: {ex.StackTrace}");

                // Fallback to English
                YesButton.Content = "Yes";
                NoButton.Content = "No";
                CancelButton.Content = "Cancel";
                OKButton.Content = "OK";
            }
        }

        private void YesButton_Click(object sender, RoutedEventArgs e)
        {
            Result = MessageBoxResult.Yes;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }

        private void NoButton_Click(object sender, RoutedEventArgs e)
        {
            Result = MessageBoxResult.No;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Result = MessageBoxResult.Cancel;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }

        private void OKButton_Click(object sender, RoutedEventArgs e)
        {
            Result = MessageBoxResult.OK;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }
    }
}
