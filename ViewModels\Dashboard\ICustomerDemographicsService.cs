using System.Collections.ObjectModel;
using System.Threading.Tasks;
using LiveCharts;
using POSSystem.Models;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Interface for customer demographics analysis service.
    /// </summary>
    public interface ICustomerDemographicsService
    {
        /// <summary>
        /// Loads customer demographics data
        /// </summary>
        /// <returns>Collection of customer demographics</returns>
        Task<ObservableCollection<CustomerDemographic>> LoadCustomerDemographicsAsync();
        
        /// <summary>
        /// Creates a pie chart series for customer demographics
        /// </summary>
        /// <param name="demographics">The customer demographics</param>
        /// <returns>Chart series collection</returns>
        SeriesCollection CreateCustomerDemographicsSeries(ObservableCollection<CustomerDemographic> demographics);
    }
} 