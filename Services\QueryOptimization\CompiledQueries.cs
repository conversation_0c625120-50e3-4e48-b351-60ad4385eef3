using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.QueryOptimization;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// Pre-compiled EF Core queries for maximum performance
    /// Compiled queries are cached and reused, eliminating query compilation overhead
    /// Can provide 20-50% performance improvement for frequently executed queries
    /// </summary>
    public static class CompiledQueries
    {
        #region Product Compiled Queries

        /// <summary>
        /// Compiled query for getting products by page (most frequently used)
        /// </summary>
        public static readonly Func<POSDbContext, int, int, IAsyncEnumerable<ProductListItem>> GetProductsPagedCompiled =
            EF.CompileAsyncQuery((POSDbContext context, int skip, int take) =>
                context.Products
                    .AsNoTracking()
                    .Where(p => p.IsActive)
                    .Select(p => new ProductListItem
                    {
                        Id = p.Id,
                        Name = p.Name,
                        SKU = p.SKU,
                        SellingPrice = p.SellingPrice,
                        StockQuantity = p.StockQuantity,
                        CategoryName = p.Category.Name,
                        HasLowStock = p.StockQuantity <= p.ReorderPoint,
                        PrimaryBarcode = p.Barcodes.FirstOrDefault(b => b.IsPrimary).Barcode,
                        CreatedAt = p.CreatedAt
                    })
                    .OrderByDescending(p => p.CreatedAt)
                    .ThenBy(p => p.Name)
                    .Skip(skip)
                    .Take(take));

        /// <summary>
        /// Compiled query for product search (frequently used)
        /// </summary>
        public static readonly Func<POSDbContext, string, int, IAsyncEnumerable<ProductSearchResult>> SearchProductsCompiled =
            EF.CompileAsyncQuery((POSDbContext context, string searchTerm, int maxResults) =>
                context.Products
                    .AsNoTracking()
                    .Where(p => p.IsActive && (
                        p.Name.ToUpper().Contains(searchTerm) ||
                        p.SKU.ToUpper().Contains(searchTerm) ||
                        p.Barcodes.Any(b => b.Barcode.ToUpper().Contains(searchTerm))
                    ))
                    .Select(p => new ProductSearchResult
                    {
                        Id = p.Id,
                        Name = p.Name,
                        SKU = p.SKU,
                        SellingPrice = p.SellingPrice,
                        StockQuantity = p.StockQuantity,
                        CategoryName = p.Category.Name,
                        PrimaryBarcode = p.Barcodes.FirstOrDefault(b => b.IsPrimary).Barcode ?? 
                                       p.Barcodes.FirstOrDefault().Barcode,
                        MatchType = p.Name.ToUpper().Contains(searchTerm) ? "Name" :
                                   p.SKU.ToUpper().Contains(searchTerm) ? "SKU" : "Barcode"
                    })
                    .Take(maxResults));

        /// <summary>
        /// Compiled query for getting product by barcode (POS critical)
        /// </summary>
        public static readonly Func<POSDbContext, string, Product> GetProductByBarcodeCompiled =
            EF.CompileQuery((POSDbContext context, string barcode) =>
                context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .FirstOrDefault(p => p.IsActive && 
                        (p.Barcodes.Any(b => b.Barcode == barcode) || p.Barcode == barcode)));

        /// <summary>
        /// Get low stock products using optimized query (dashboard critical)
        /// Note: Using regular method instead of compiled query due to return type complexity
        /// </summary>
        public static async Task<List<LowStockAlert>> GetLowStockProductsAsync(POSDbContext context)
        {
            return await context.Products
                .AsNoTracking()
                .Where(p => p.IsActive && p.StockQuantity <= p.ReorderPoint)
                .Select(p => new LowStockAlert
                {
                    ProductId = p.Id,
                    ProductName = p.Name,
                    SKU = p.SKU,
                    CurrentStock = p.StockQuantity,
                    ReorderPoint = p.ReorderPoint,
                    CategoryName = p.Category.Name
                })
                .OrderBy(p => p.CurrentStock)
                .ToListAsync();
        }

        #endregion

        #region Sales Compiled Queries

        /// <summary>
        /// Compiled query for daily sales total (dashboard critical)
        /// </summary>
        public static readonly Func<POSDbContext, DateTime, decimal> GetDailySalesTotalCompiled =
            EF.CompileQuery((POSDbContext context, DateTime date) =>
                context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate.Date == date.Date && s.Status == "Paid")
                    .Sum(s => (decimal?)s.GrandTotal) ?? 0);

        /// <summary>
        /// Compiled query for sales summary (dashboard)
        /// </summary>
        public static readonly Func<POSDbContext, DateTime, DateTime, SalesSummary> GetSalesSummaryCompiled =
            EF.CompileQuery((POSDbContext context, DateTime startDate, DateTime endDate) =>
                context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .GroupBy(s => 1)
                    .Select(g => new SalesSummary
                    {
                        TotalSales = g.Sum(s => s.GrandTotal),
                        SalesCount = g.Count(),
                        AverageSaleAmount = g.Average(s => s.GrandTotal),
                        PaidSalesTotal = g.Where(s => s.Status == "Paid").Sum(s => s.GrandTotal),
                        UnpaidSalesTotal = g.Where(s => s.Status == "Unpaid").Sum(s => s.GrandTotal),
                        UnpaidSalesCount = g.Count(s => s.Status == "Unpaid")
                    })
                    .FirstOrDefault());

        /// <summary>
        /// Compiled query for top selling products (dashboard)
        /// </summary>
        public static readonly Func<POSDbContext, DateTime, DateTime, int, IAsyncEnumerable<TopSellingProduct>> GetTopSellingProductsCompiled =
            EF.CompileAsyncQuery((POSDbContext context, DateTime startDate, DateTime endDate, int count) =>
                context.SaleItems
                    .AsNoTracking()
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new TopSellingProduct
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.Name,
                        TotalQuantitySold = g.Sum(si => si.Quantity), // ✅ WEIGHT-BASED FIX: Remove (int) cast to preserve decimal quantities
                        TotalRevenue = g.Sum(si => si.Quantity * si.UnitPrice),
                        SalesCount = g.Count()
                    })
                    .OrderByDescending(p => p.TotalQuantitySold)
                    .Take(count));

        #endregion

        #region Statistics Compiled Queries

        /// <summary>
        /// Compiled query for product statistics (dashboard critical)
        /// </summary>
        public static readonly Func<POSDbContext, ProductStatisticsSummary> GetProductStatisticsCompiled =
            EF.CompileQuery((POSDbContext context) =>
                context.Products
                    .AsNoTracking()
                    .Where(p => p.IsActive)
                    .GroupBy(p => 1)
                    .Select(g => new ProductStatisticsSummary
                    {
                        TotalProducts = g.Count(),
                        LowStockCount = g.Count(p => p.StockQuantity <= p.ReorderPoint),
                        OutOfStockCount = g.Count(p => p.StockQuantity == 0),
                        TotalInventoryValue = g.Sum(p => p.StockQuantity * p.PurchasePrice),
                        AverageSellingPrice = g.Average(p => p.SellingPrice)
                    })
                    .FirstOrDefault());

        /// <summary>
        /// Compiled query for customer count
        /// </summary>
        public static readonly Func<POSDbContext, int> GetActiveCustomerCountCompiled =
            EF.CompileQuery((POSDbContext context) =>
                context.Customers
                    .AsNoTracking()
                    .Count(c => c.IsActive));

        /// <summary>
        /// Compiled query for unpaid sales count (alerts)
        /// </summary>
        public static readonly Func<POSDbContext, int> GetUnpaidSalesCountCompiled =
            EF.CompileQuery((POSDbContext context) =>
                context.Sales
                    .AsNoTracking()
                    .Count(s => s.Status == "Unpaid"));

        #endregion

        #region Customer Compiled Queries

        /// <summary>
        /// Compiled query for customer search
        /// </summary>
        public static readonly Func<POSDbContext, string, int, IAsyncEnumerable<CustomerListItem>> SearchCustomersCompiled =
            EF.CompileAsyncQuery((POSDbContext context, string searchTerm, int maxResults) =>
                context.Customers
                    .AsNoTracking()
                    .Where(c => c.IsActive && (
                        c.FirstName.Contains(searchTerm) ||
                        c.LastName.Contains(searchTerm) ||
                        c.Phone.Contains(searchTerm) ||
                        c.Email.Contains(searchTerm)
                    ))
                    .Select(c => new CustomerListItem
                    {
                        Id = c.Id,
                        Name = c.FirstName + " " + c.LastName,
                        Phone = c.Phone,
                        Email = c.Email,
                        TotalPurchases = c.Sales.Sum(s => s.GrandTotal),
                        LastPurchaseDate = c.Sales.Max(s => s.SaleDate)
                    })
                    .Take(maxResults));

        #endregion

        #region Inventory Compiled Queries

        /// <summary>
        /// Get expiring products using optimized query
        /// Note: Using regular method instead of compiled query due to return type complexity
        /// </summary>
        public static async Task<List<ExpiringProductAlert>> GetExpiringProductsAsync(POSDbContext context, DateTime expiryThreshold)
        {
            return await context.Products
                .AsNoTracking()
                .Where(p => p.IsActive && p.ExpiryDate.HasValue && p.ExpiryDate <= expiryThreshold)
                .Select(p => new ExpiringProductAlert
                {
                    ProductId = p.Id,
                    ProductName = p.Name,
                    SKU = p.SKU,
                    ExpiryDate = p.ExpiryDate.Value,
                    StockQuantity = p.StockQuantity,
                    DaysUntilExpiry = (int)(p.ExpiryDate.Value - DateTime.Now).TotalDays,
                    PotentialLoss = p.StockQuantity * p.PurchasePrice
                })
                .OrderBy(p => p.ExpiryDate)
                .ToListAsync();
        }

        #endregion
    }
}
