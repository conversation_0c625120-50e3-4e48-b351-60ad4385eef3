using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Data;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using System.Windows;
using System.Diagnostics;

namespace POSSystem.Services
{
    public class AlertService : IAlertService
    {
        private readonly POSDbContext _context;
        private readonly IDatabaseService _dbService;
        private const int DEFAULT_PAGE_SIZE = 10;
        private List<ProductAlert> _cachedUnreadAlerts;
        private DateTime _lastUnreadCheck = DateTime.MinValue;

        public AlertService(POSDbContext context, IDatabaseService dbService)
        {
            _context = context;
            _dbService = dbService;
        }

        public void CheckExpiringProducts()
        {
            // Check expiring products (check for products expiring in the next 30 days)
            var expiringProducts = _dbService.GetExpiringProducts(30);
            foreach (var product in expiringProducts)
            {
                var daysUntilExpiry = product.DaysUntilExpiry.GetValueOrDefault();
                string message = "";

                if (daysUntilExpiry <= 0)
                {
                    var daysExpired = (DateTime.Now - product.ExpiryDate.Value).Days;
                    // Use resource string instead of hardcoded text
                    var template = Application.Current.TryFindResource("ProductExpiredNotification")?.ToString() ?? "{0} منتهي الصلاحية منذ {1}";
                    var daysFormat = Application.Current.TryFindResource("ExpiryDaysFormat")?.ToString() ?? "{0} يوم";
                    message = string.Format(template, product.Name, string.Format(daysFormat, daysExpired));
                }
                else if (daysUntilExpiry <= 7)
                {
                    // Use resource string for urgent expiry notification
                    var template = Application.Current.TryFindResource("ProductExpiryUrgentNotification")?.ToString() ?? "{0} سينتهي خلال {1} يوم";
                    message = string.Format(template, product.Name, daysUntilExpiry);
                }
                else if (daysUntilExpiry <= 30)
                {
                    // Use resource string for warning notification
                    var template = Application.Current.TryFindResource("ProductExpiryWarningNotification")?.ToString() ?? "{0} تحذير: سينتهي خلال {1} يوم";
                    message = string.Format(template, product.Name, daysUntilExpiry);
                }

                if (!string.IsNullOrEmpty(message))
                {
                    CreateAlert(product.Id, "Expiry", message);
                }
            }

            // Check stock levels
            var products = _dbService.GetAllProducts();
            
            // Note: GetAllProducts now properly loads batches for products where TrackBatches is true
            // No need to manually load batches again
            
            // Check out of stock products first
            var outOfStockProducts = products.Where(p => p.GetTotalStock() == 0);
            foreach (var product in outOfStockProducts)
            {
                // Use resource string for out of stock notification
                var template = Application.Current.TryFindResource("OutOfStockNotification")?.ToString() ?? "{0} نفذ من المخزون";
                string message = string.Format(template, product.Name);
                CreateAlert(product.Id, "OutOfStock", message);
            }

            // Then check low stock products
            var lowStockProducts = products.Where(p => p.GetTotalStock() > 0 && p.GetTotalStock() <= p.MinimumStock);
            foreach (var product in lowStockProducts)
            {
                // Use resource string for low stock notification
                var template = Application.Current.TryFindResource("LowStockNotification")?.ToString() ?? "{0} مخزون منخفض ({1} وحدة متبقية، الحد الأدنى: {2})";
                string message = string.Format(template, product.Name, product.GetTotalStock(), product.MinimumStock);
                CreateAlert(product.Id, "LowStock", message);
            }

            // Check overdue unpaid sales
            var unpaidSales = _dbService.GetUnpaidSales()
                .Where(s => s.DueDate < DateTime.Now)
                .ToList();

            foreach (var sale in unpaidSales)
            {
                var daysOverdue = (DateTime.Now - sale.DueDate.Value).Days;
                var customerName = sale.Customer?.Name ?? "عميل";
                // Use resource string for overdue sale notification
                var template = Application.Current.TryFindResource("OverdueSaleNotification")?.ToString() ?? "فاتورة رقم {0} للعميل {1} - متأخرة {2} يوم (المبلغ: {3})";
                string message = string.Format(template, sale.InvoiceNumber, customerName, daysOverdue, (sale.GrandTotal - sale.AmountPaid).ToString("N2") + " DA");
                CreateAlert(GetFirstProductId(), "OverdueSale", message);
            }

            // Check overdue unpaid purchases
            var unpaidPurchases = _dbService.GetUnpaidPurchaseOrders()
                .Where(p => p.DueDate < DateTime.Now)
                .ToList();

            foreach (var purchase in unpaidPurchases)
            {
                var daysOverdue = (DateTime.Now - purchase.DueDate).Days;
                var supplierName = purchase.Supplier?.Name ?? "مورد";
                // Use resource string for overdue purchase notification
                var template = Application.Current.TryFindResource("OverduePurchaseNotification")?.ToString() ?? "طلب شراء رقم {0} للمورد {1} - متأخر {2} يوم (المبلغ: {3})";
                string message = string.Format(template, purchase.OrderNumber, supplierName, daysOverdue, purchase.GrandTotal.ToString("N2") + " DA");
                CreateAlert(GetFirstProductId(), "OverduePurchase", message);
            }
            
            // Clean up old "OutOfStock" alerts for products that now have stock
            CleanupInvalidStockAlerts();
        }

        private void CleanupInvalidStockAlerts()
        {
            // Get all unread OutOfStock alerts
            var outOfStockAlerts = _context.ProductAlerts
                .Include(a => a.Product)
                .Where(a => a.AlertType == "OutOfStock" && !a.IsRead)
                .ToList();
            
            if (outOfStockAlerts.Any())
            {
                // Load all products at once with their batches properly initialized
                var allProducts = _dbService.GetAllProducts();
                var productsDict = allProducts.ToDictionary(p => p.Id);
                
                // Check each alert to see if the product actually has stock now
                foreach (var alert in outOfStockAlerts)
                {
                    if (alert.Product != null && productsDict.TryGetValue(alert.Product.Id, out var product))
                    {
                        // If the product has stock (greater than 0), mark the alert as read
                        if (product.GetTotalStock() > 0)
                        {
                            alert.IsRead = true;
                            alert.ReadAt = DateTime.Now;
                        }
                    }
                }
                
                // Save changes
                _context.SaveChanges();
                
                // Clear the cached alerts to ensure fresh data
                _cachedUnreadAlerts = null;
            }
        }

        public void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product")
        {
            // Check if a similar unread alert already exists
            var existingAlert = _context.ProductAlerts
                .Where(a => a.ProductId == (referenceType == "Product" ? referenceId : 0) && 
                           a.AlertType == alertType && 
                           !a.IsRead)
                .OrderByDescending(a => a.CreatedAt)
                .FirstOrDefault();

            // Only create a new alert if there's no existing unread alert
            if (existingAlert == null)
            {
                var alert = new ProductAlert
                {
                    ProductId = referenceType == "Product" ? referenceId : GetFirstProductId(),
                    AlertType = alertType,
                    Message = message,
                    CreatedAt = DateTime.Now,
                    IsRead = false,
                    ReferenceType = referenceType,
                    ReferenceId = referenceId
                };

                _context.ProductAlerts.Add(alert);
                _context.SaveChanges();
            }
        }

        private int GetFirstProductId()
        {
            return _dbService.GetAllProducts().FirstOrDefault()?.Id ?? 1;
        }

        public List<ProductAlert> GetUnreadAlerts()
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[AlertService] GetUnreadAlerts() called at {DateTime.Now:HH:mm:ss.fff} - This will execute the heavy query with Product joins");
            #endif
            // Cache unread alerts for 10 seconds to prevent frequent database hits
            if (_cachedUnreadAlerts == null || (DateTime.Now - _lastUnreadCheck).TotalSeconds >= 10)
            {
                _cachedUnreadAlerts = _context.ProductAlerts
                    .Include(a => a.Product)
                    .Where(a => !a.IsRead && a.Product != null)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToList();
                _lastUnreadCheck = DateTime.Now;
            }
            return _cachedUnreadAlerts;
        }

        /// <summary>
        /// Gets unread alerts with minimal data (no Product details)
        /// Use this when you only need basic alert info without product details
        /// </summary>
        public List<ProductAlert> GetUnreadAlertsBasic()
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[AlertService] GetUnreadAlertsBasic() called at {DateTime.Now:HH:mm:ss.fff} - Using optimized query without Product joins");
            #endif
            return _context.ProductAlerts
                .Where(a => !a.IsRead)
                .OrderByDescending(a => a.CreatedAt)
                .ToList();
        }

        public void ClearAlertCache()
        {
            _cachedUnreadAlerts = null;
            _lastUnreadCheck = DateTime.MinValue;
        }

        public List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1)
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[AlertService] GetAllAlerts() called at {DateTime.Now:HH:mm:ss.fff} - This will execute the heavy query with Product joins");
            #endif
            var query = _context.ProductAlerts
                .Include(a => a.Product)
                .OrderByDescending(a => a.CreatedAt)
                .AsNoTracking();

            if (limit.HasValue)
            {
                int skip = (page - 1) * limit.Value;
                return query.Skip(skip)
                           .Take(limit.Value)
                           .ToList();
            }

            return query.ToList();
        }

        /// <summary>
        /// Gets all alerts with minimal data (no Product details)
        /// Use this when you only need basic alert info without product details
        /// </summary>
        public List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1)
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[AlertService] GetAllAlertsBasic() called at {DateTime.Now:HH:mm:ss.fff} - Using optimized query without Product joins");
            #endif
            var query = _context.ProductAlerts
                .OrderByDescending(a => a.CreatedAt)
                .AsNoTracking();

            if (limit.HasValue)
            {
                int skip = (page - 1) * limit.Value;
                return query.Skip(skip)
                           .Take(limit.Value)
                           .ToList();
            }

            return query.ToList();
        }

        public int GetTotalAlertsCount()
        {
            return _context.ProductAlerts.Count();
        }

        public int GetUnreadAlertsCount()
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG && VERBOSE_LOGGING
            Debug.WriteLine($"[AlertService] GetUnreadAlertsCount() called at {DateTime.Now:HH:mm:ss.fff} - Using optimized count query");
            #endif
            // Always use the optimized count query instead of loading full data
            return _context.ProductAlerts.Count(a => !a.IsRead);
        }

        public void MarkAllAlertsAsRead()
        {
            // Use a more efficient update query
            _context.Database.ExecuteSqlRaw(
                "UPDATE ProductAlerts SET IsRead = 1, ReadAt = @p0 WHERE IsRead = 0",
                DateTime.Now);

            // Clear the cache
            _cachedUnreadAlerts = null;
        }

        public void MarkAlertAsRead(int alertId)
        {
            _context.Database.ExecuteSqlRaw(
                "UPDATE ProductAlerts SET IsRead = 1, ReadAt = @p0 WHERE Id = @p1",
                DateTime.Now, alertId);

            // Update cache if it exists
            if (_cachedUnreadAlerts != null)
            {
                var alert = _cachedUnreadAlerts.FirstOrDefault(a => a.Id == alertId);
                if (alert != null)
                {
                    _cachedUnreadAlerts.Remove(alert);
                }
            }
        }

        public void ShowError(string message)
        {
            MessageBox.Show(message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
} 
