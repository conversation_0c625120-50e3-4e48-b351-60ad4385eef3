using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Services.Caching;
using POSSystem.Services.Performance;
using POSSystem.ViewModels;

namespace POSSystem.Services.Testing
{
    /// <summary>
    /// Integrated performance testing service that runs within the main application
    /// Tests the optimized ProductsViewModel performance improvements
    /// </summary>
    public class IntegratedPerformanceTestService
    {
        private readonly ProductsPerformanceMonitor _performanceMonitor;
        private readonly string _logFilePath;
        private readonly object _logLock = new object();

        public IntegratedPerformanceTestService()
        {
            _performanceMonitor = new ProductsPerformanceMonitor();
            
            // Create test log file
            var logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PerformanceTestLogs");
            Directory.CreateDirectory(logDir);
            _logFilePath = Path.Combine(logDir, $"performance_test_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.log");
            
            LogMessage("IntegratedPerformanceTestService initialized");
        }

        /// <summary>
        /// Run comprehensive performance tests on ProductsViewModel
        /// </summary>
        public async Task<PerformanceTestResults> RunProductsViewModelTestsAsync(ProductsViewModel viewModel)
        {
            var results = new PerformanceTestResults
            {
                TestStartTime = DateTime.Now,
                TestName = "ProductsViewModel Performance Test Suite"
            };

            LogMessage("=== STARTING PRODUCTS VIEWMODEL PERFORMANCE TESTS ===");
            LogMessage($"Test started at: {results.TestStartTime:yyyy-MM-dd HH:mm:ss}");

            try
            {
                // Test 1: Initial Load Performance
                var test1Result = await TestInitialLoadPerformance(viewModel);
                results.TestResults.Add(test1Result);

                // Test 2: Page Navigation Performance
                var test2Result = await TestPageNavigationPerformance(viewModel);
                results.TestResults.Add(test2Result);

                // Test 3: Category Filter Performance
                var test3Result = await TestCategoryFilterPerformance(viewModel);
                results.TestResults.Add(test3Result);

                // Test 4: Cache Performance
                var test4Result = await TestCachePerformance(viewModel);
                results.TestResults.Add(test4Result);

                // Test 5: Refresh Performance
                var test5Result = await TestRefreshPerformance(viewModel);
                results.TestResults.Add(test5Result);

                results.TestEndTime = DateTime.Now;
                results.TotalDuration = results.TestEndTime - results.TestStartTime;
                results.Success = true;

                // Generate summary
                GenerateTestSummary(results);
            }
            catch (Exception ex)
            {
                results.TestEndTime = DateTime.Now;
                results.TotalDuration = results.TestEndTime - results.TestStartTime;
                results.Success = false;
                results.ErrorMessage = ex.Message;
                
                LogMessage($"❌ Performance tests failed: {ex.Message}");
            }

            LogMessage("=== PERFORMANCE TESTS COMPLETED ===");
            return results;
        }

        private async Task<TestResult> TestInitialLoadPerformance(ProductsViewModel viewModel)
        {
            LogMessage("🧪 Test 1: Initial Load Performance");
            
            var stopwatch = Stopwatch.StartNew();
            var testId = _performanceMonitor.StartOperation("InitialLoadTest", "Testing initial product loading");

            try
            {
                await viewModel.LoadPagedProducts();
                stopwatch.Stop();
                
                var elapsed = _performanceMonitor.StopOperation("InitialLoadTest", testId, 
                    $"Loaded {viewModel.Products.Count} products");

                var result = new TestResult
                {
                    TestName = "Initial Load Performance",
                    ExecutionTime = TimeSpan.FromMilliseconds(elapsed),
                    Success = elapsed < 500, // Target: under 500ms
                    Details = $"Loaded {viewModel.Products.Count} products in {elapsed}ms",
                    PerformanceLevel = GetPerformanceLevel(elapsed)
                };

                LogMessage($"✅ Test 1 Result: {result.PerformanceLevel} - {elapsed}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor.StopOperation("InitialLoadTest", testId, $"Failed: {ex.Message}");
                
                LogMessage($"❌ Test 1 Failed: {ex.Message}");
                return new TestResult
                {
                    TestName = "Initial Load Performance",
                    ExecutionTime = stopwatch.Elapsed,
                    Success = false,
                    Details = $"Error: {ex.Message}",
                    PerformanceLevel = "Failed"
                };
            }
        }

        private async Task<TestResult> TestPageNavigationPerformance(ProductsViewModel viewModel)
        {
            LogMessage("🧪 Test 2: Page Navigation Performance");
            
            var totalTime = 0L;
            var pageCount = 3;
            var testId = _performanceMonitor.StartOperation("PageNavigationTest", $"Testing {pageCount} page navigations");

            try
            {
                for (int i = 1; i <= pageCount; i++)
                {
                    viewModel.CurrentPage = i;
                    
                    var stopwatch = Stopwatch.StartNew();
                    await viewModel.LoadPagedProducts();
                    stopwatch.Stop();
                    
                    totalTime += stopwatch.ElapsedMilliseconds;
                    LogMessage($"   Page {i}: {stopwatch.ElapsedMilliseconds}ms");
                }

                var avgTime = totalTime / pageCount;
                _performanceMonitor.StopOperation("PageNavigationTest", testId, 
                    $"Average {avgTime}ms per page");

                var result = new TestResult
                {
                    TestName = "Page Navigation Performance",
                    ExecutionTime = TimeSpan.FromMilliseconds(avgTime),
                    Success = avgTime < 300, // Target: under 300ms average (should be faster due to caching)
                    Details = $"Average {avgTime}ms per page over {pageCount} pages",
                    PerformanceLevel = GetPerformanceLevel(avgTime)
                };

                LogMessage($"✅ Test 2 Result: {result.PerformanceLevel} - {avgTime}ms average");
                return result;
            }
            catch (Exception ex)
            {
                _performanceMonitor.StopOperation("PageNavigationTest", testId, $"Failed: {ex.Message}");
                
                LogMessage($"❌ Test 2 Failed: {ex.Message}");
                return new TestResult
                {
                    TestName = "Page Navigation Performance",
                    ExecutionTime = TimeSpan.FromMilliseconds(totalTime),
                    Success = false,
                    Details = $"Error: {ex.Message}",
                    PerformanceLevel = "Failed"
                };
            }
        }

        private async Task<TestResult> TestCategoryFilterPerformance(ProductsViewModel viewModel)
        {
            LogMessage("🧪 Test 3: Category Filter Performance");
            
            var stopwatch = Stopwatch.StartNew();
            var testId = _performanceMonitor.StartOperation("CategoryFilterTest", "Testing category filtering");

            try
            {
                // Test filtering by different categories
                var originalCategory = viewModel.SelectedCategory;
                
                // Reset to show all products first
                viewModel.SelectedCategory = null;
                await viewModel.LoadPagedProducts();
                
                var filterTime = stopwatch.ElapsedMilliseconds;
                stopwatch.Stop();
                
                var elapsed = _performanceMonitor.StopOperation("CategoryFilterTest", testId, 
                    $"Category filter completed in {filterTime}ms");

                // Restore original category
                viewModel.SelectedCategory = originalCategory;

                var result = new TestResult
                {
                    TestName = "Category Filter Performance",
                    ExecutionTime = TimeSpan.FromMilliseconds(filterTime),
                    Success = filterTime < 400, // Target: under 400ms
                    Details = $"Category filtering completed in {filterTime}ms",
                    PerformanceLevel = GetPerformanceLevel(filterTime)
                };

                LogMessage($"✅ Test 3 Result: {result.PerformanceLevel} - {filterTime}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor.StopOperation("CategoryFilterTest", testId, $"Failed: {ex.Message}");
                
                LogMessage($"❌ Test 3 Failed: {ex.Message}");
                return new TestResult
                {
                    TestName = "Category Filter Performance",
                    ExecutionTime = stopwatch.Elapsed,
                    Success = false,
                    Details = $"Error: {ex.Message}",
                    PerformanceLevel = "Failed"
                };
            }
        }

        private async Task<TestResult> TestCachePerformance(ProductsViewModel viewModel)
        {
            LogMessage("🧪 Test 4: Cache Performance");
            
            var testId = _performanceMonitor.StartOperation("CacheTest", "Testing cache performance");

            try
            {
                // First load (cache miss)
                var stopwatch1 = Stopwatch.StartNew();
                await viewModel.LoadPagedProducts();
                stopwatch1.Stop();
                var firstLoadTime = stopwatch1.ElapsedMilliseconds;

                // Second load (should be cache hit)
                var stopwatch2 = Stopwatch.StartNew();
                await viewModel.LoadPagedProducts();
                stopwatch2.Stop();
                var secondLoadTime = stopwatch2.ElapsedMilliseconds;

                var improvement = ((double)(firstLoadTime - secondLoadTime) / firstLoadTime) * 100;
                
                _performanceMonitor.StopOperation("CacheTest", testId, 
                    $"Cache improvement: {improvement:F1}%");

                var result = new TestResult
                {
                    TestName = "Cache Performance",
                    ExecutionTime = TimeSpan.FromMilliseconds(secondLoadTime),
                    Success = secondLoadTime < firstLoadTime && improvement > 10, // Should be at least 10% faster
                    Details = $"First load: {firstLoadTime}ms, Second load: {secondLoadTime}ms, Improvement: {improvement:F1}%",
                    PerformanceLevel = improvement > 50 ? "Excellent" : improvement > 25 ? "Good" : "Fair"
                };

                LogMessage($"✅ Test 4 Result: {result.PerformanceLevel} - {improvement:F1}% improvement");
                return result;
            }
            catch (Exception ex)
            {
                _performanceMonitor.StopOperation("CacheTest", testId, $"Failed: {ex.Message}");
                
                LogMessage($"❌ Test 4 Failed: {ex.Message}");
                return new TestResult
                {
                    TestName = "Cache Performance",
                    ExecutionTime = TimeSpan.Zero,
                    Success = false,
                    Details = $"Error: {ex.Message}",
                    PerformanceLevel = "Failed"
                };
            }
        }

        private async Task<TestResult> TestRefreshPerformance(ProductsViewModel viewModel)
        {
            LogMessage("🧪 Test 5: Refresh Performance");
            
            var stopwatch = Stopwatch.StartNew();
            var testId = _performanceMonitor.StartOperation("RefreshTest", "Testing force refresh performance");

            try
            {
                await viewModel.ForceRefreshFromDatabase();
                stopwatch.Stop();
                
                var elapsed = _performanceMonitor.StopOperation("RefreshTest", testId, 
                    $"Force refresh completed in {stopwatch.ElapsedMilliseconds}ms");

                var result = new TestResult
                {
                    TestName = "Refresh Performance",
                    ExecutionTime = stopwatch.Elapsed,
                    Success = stopwatch.ElapsedMilliseconds < 600, // Target: under 600ms (includes cache clearing)
                    Details = $"Force refresh completed in {stopwatch.ElapsedMilliseconds}ms",
                    PerformanceLevel = GetPerformanceLevel(stopwatch.ElapsedMilliseconds)
                };

                LogMessage($"✅ Test 5 Result: {result.PerformanceLevel} - {stopwatch.ElapsedMilliseconds}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor.StopOperation("RefreshTest", testId, $"Failed: {ex.Message}");
                
                LogMessage($"❌ Test 5 Failed: {ex.Message}");
                return new TestResult
                {
                    TestName = "Refresh Performance",
                    ExecutionTime = stopwatch.Elapsed,
                    Success = false,
                    Details = $"Error: {ex.Message}",
                    PerformanceLevel = "Failed"
                };
            }
        }

        private string GetPerformanceLevel(long milliseconds)
        {
            if (milliseconds <= 200) return "Excellent";
            if (milliseconds <= 500) return "Good";
            if (milliseconds <= 1000) return "Fair";
            if (milliseconds <= 3000) return "Poor";
            return "Unacceptable";
        }

        private void GenerateTestSummary(PerformanceTestResults results)
        {
            LogMessage("");
            LogMessage("=== PERFORMANCE TEST SUMMARY ===");
            LogMessage($"Test Suite: {results.TestName}");
            LogMessage($"Duration: {results.TotalDuration.TotalSeconds:F2} seconds");
            LogMessage($"Overall Success: {(results.Success ? "✅ PASSED" : "❌ FAILED")}");
            LogMessage("");

            var passedTests = 0;
            foreach (var test in results.TestResults)
            {
                var status = test.Success ? "✅ PASS" : "❌ FAIL";
                LogMessage($"{status} {test.TestName}: {test.PerformanceLevel} ({test.ExecutionTime.TotalMilliseconds:F0}ms)");
                LogMessage($"     {test.Details}");
                
                if (test.Success) passedTests++;
            }

            LogMessage("");
            LogMessage($"Tests Passed: {passedTests}/{results.TestResults.Count}");
            LogMessage($"Success Rate: {(double)passedTests / results.TestResults.Count * 100:F1}%");
            LogMessage("=== END SUMMARY ===");
        }

        private void LogMessage(string message)
        {
            try
            {
                lock (_logLock)
                {
                    var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} {message}";
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
                    Debug.WriteLine($"[PERF_TEST] {message}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERF_TEST] Failed to log message: {ex.Message}");
            }
        }

        public string GetLogFilePath() => _logFilePath;
    }

    public class PerformanceTestResults
    {
        public string TestName { get; set; }
        public DateTime TestStartTime { get; set; }
        public DateTime TestEndTime { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public List<TestResult> TestResults { get; set; } = new List<TestResult>();
    }

    public class TestResult
    {
        public string TestName { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public bool Success { get; set; }
        public string Details { get; set; }
        public string PerformanceLevel { get; set; }
    }
}
