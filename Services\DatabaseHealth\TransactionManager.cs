using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using POSSystem.Data;

namespace POSSystem.Services.DatabaseHealth
{
    /// <summary>
    /// Service for managing database transactions with proper error handling and rollback
    /// </summary>
    public class TransactionManager
    {
        private readonly POSDbContext _context;
        private readonly ILogger<TransactionManager> _logger;

        public TransactionManager(POSDbContext context, ILogger<TransactionManager> logger = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
        }

        /// <summary>
        /// Execute an operation within a database transaction
        /// </summary>
        public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, string operationName = null)
        {
            operationName ??= "Database Operation";
            
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                _logger?.LogDebug("Starting transaction for {OperationName}", operationName);
                
                var result = await operation();
                
                await transaction.CommitAsync();
                _logger?.LogDebug("Transaction committed successfully for {OperationName}", operationName);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Transaction failed for {OperationName}, rolling back", operationName);
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Execute an operation within a database transaction (void return)
        /// </summary>
        public async Task ExecuteInTransactionAsync(Func<Task> operation, string operationName = null)
        {
            operationName ??= "Database Operation";
            
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                _logger?.LogDebug("Starting transaction for {OperationName}", operationName);
                
                await operation();
                
                await transaction.CommitAsync();
                _logger?.LogDebug("Transaction committed successfully for {OperationName}", operationName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Transaction failed for {OperationName}, rolling back", operationName);
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Execute a complex sale transaction with multiple operations
        /// </summary>
        public async Task<bool> ExecuteSaleTransactionAsync(
            Func<IDbContextTransaction, Task> saleOperation,
            string operationDescription = "Sale Transaction")
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                _logger?.LogInformation("Starting sale transaction: {Description}", operationDescription);
                
                // Execute the sale operation with access to the transaction
                await saleOperation(transaction);
                
                // Validate the transaction before committing
                await ValidateSaleTransactionAsync();
                
                await transaction.CommitAsync();
                _logger?.LogInformation("Sale transaction committed successfully: {Description}", operationDescription);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Sale transaction failed: {Description}, rolling back", operationDescription);
                await transaction.RollbackAsync();
                return false;
            }
        }

        /// <summary>
        /// Execute a bulk operation with transaction support
        /// </summary>
        public async Task<BulkOperationResult> ExecuteBulkOperationAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            int expectedAffectedRows = -1)
        {
            var result = new BulkOperationResult
            {
                OperationName = operationName,
                StartTime = DateTime.UtcNow
            };

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                _logger?.LogInformation("Starting bulk operation: {OperationName}", operationName);
                
                var operationResult = await operation();
                
                // Save changes and get affected row count
                var affectedRows = await _context.SaveChangesAsync();
                result.AffectedRows = affectedRows;
                
                // Validate expected row count if specified
                if (expectedAffectedRows > 0 && affectedRows != expectedAffectedRows)
                {
                    throw new InvalidOperationException(
                        $"Expected {expectedAffectedRows} rows to be affected, but {affectedRows} were affected");
                }
                
                await transaction.CommitAsync();
                
                result.Success = true;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
                result.Result = operationResult;
                
                _logger?.LogInformation("Bulk operation completed successfully: {OperationName}, Affected rows: {AffectedRows}, Duration: {Duration}ms",
                    operationName, affectedRows, result.Duration.TotalMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
                result.ErrorMessage = ex.Message;
                
                _logger?.LogError(ex, "Bulk operation failed: {OperationName}, Duration: {Duration}ms, rolling back",
                    operationName, result.Duration.TotalMilliseconds);
                
                await transaction.RollbackAsync();
                return result;
            }
        }

        /// <summary>
        /// Validate sale transaction integrity
        /// </summary>
        private async Task ValidateSaleTransactionAsync()
        {
            // Add validation logic here
            // For example, check that sale totals match item totals
            // Check that inventory was properly updated
            // Validate payment amounts, etc.
            
            await Task.CompletedTask; // Placeholder for validation logic
        }

        /// <summary>
        /// Execute operation with retry logic for transient failures
        /// </summary>
        public async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan? delay = null,
            string operationName = null)
        {
            delay ??= TimeSpan.FromMilliseconds(500);
            operationName ??= "Database Operation";
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (attempt < maxRetries && IsTransientError(ex))
                {
                    _logger?.LogWarning(ex, "Transient error on attempt {Attempt}/{MaxRetries} for {OperationName}, retrying in {Delay}ms",
                        attempt, maxRetries, operationName, delay.Value.TotalMilliseconds);
                    
                    await Task.Delay(delay.Value);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Operation failed on attempt {Attempt}/{MaxRetries} for {OperationName}",
                        attempt, maxRetries, operationName);
                    throw;
                }
            }
            
            throw new InvalidOperationException($"Operation {operationName} failed after {maxRetries} attempts");
        }

        /// <summary>
        /// Check if an exception represents a transient error that can be retried
        /// </summary>
        private bool IsTransientError(Exception ex)
        {
            // Common transient errors that can be retried
            return ex is TimeoutException ||
                   ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
                   ex.Message.Contains("connection", StringComparison.OrdinalIgnoreCase) ||
                   ex.Message.Contains("network", StringComparison.OrdinalIgnoreCase);
        }
    }

    /// <summary>
    /// Result of a bulk operation
    /// </summary>
    public class BulkOperationResult
    {
        public string OperationName { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int AffectedRows { get; set; }
        public string ErrorMessage { get; set; }
        public object Result { get; set; }
    }
}
