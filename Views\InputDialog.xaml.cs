using System.Windows;

namespace POSSystem.Views
{
    public partial class InputDialog : Window
    {
        public string Value { get; private set; }
        public string Prompt { get; }

        public InputDialog(string title, string prompt)
        {
            InitializeComponent();
            Title = title;
            Prompt = prompt;
            DataContext = this;
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            Value = txtValue.Text;
            DialogResult = true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
} 