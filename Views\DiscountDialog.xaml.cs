using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Linq;
using System.Windows.Threading;
using System.Diagnostics;
using System.Threading;
using System.Windows.Media;
using System.Windows.Input;
using System.Collections.ObjectModel;

namespace POSSystem.Views
{
    public partial class DiscountDialog : UserControl, IDisposable
    {
        private readonly User _currentUser;
        private readonly decimal _originalPrice;
        private readonly bool _isCartWide;
        private readonly DiscountService _discountService;
        private DiscountType _selectedDiscountType;
        private decimal _discountValue;
        private bool _isDisposed = false;
        private ObservableCollection<DiscountTypeViewModel> _discountTypes;

        public class DiscountTypeViewModel
        {
            public DiscountType Type { get; set; }
            public string Name => Type.Name;
            public bool IsSelected { get; set; }
            public int Id => Type.Id;
        }

        public DiscountDialog(User currentUser, decimal originalPrice, bool isCartWide, DiscountService discountService)
        {
            InitializeComponent();

            // Add null checks for constructor parameters
            if (currentUser == null)
                throw new ArgumentNullException(nameof(currentUser), "Current user cannot be null");
            if (discountService == null)
                throw new ArgumentNullException(nameof(discountService), "Discount service cannot be null");

            _currentUser = currentUser;
            _originalPrice = originalPrice;
            _isCartWide = isCartWide;
            _discountService = discountService;

            Debug.WriteLine($"DiscountDialog constructor - User: {currentUser?.Username}, Price: {originalPrice}, IsCartWide: {isCartWide}");

            try
            {
                LoadDiscountTypes();
                LoadDiscountReasons();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DiscountDialog constructor: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }

            // Set focus to discount value textbox when loaded using Dispatcher
            Loaded += OnLoaded;

            // Add KeyDown event handler for Enter key
            DiscountValueTextBox.KeyDown += OnDiscountValueTextBoxKeyDown;
            
            // Add Escape key handler to the whole control
            this.PreviewKeyDown += OnDialogPreviewKeyDown;
            
            Debug.WriteLine("DiscountDialog constructor completed");
        }
        
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("DiscountDialog.OnLoaded called");
            Dispatcher.BeginInvoke(new Action(() => 
            {
                DiscountValueTextBox.Focus();
                DiscountValueTextBox.SelectAll();
                Debug.WriteLine("Focus set to DiscountValueTextBox");
            }), System.Windows.Threading.DispatcherPriority.Render);
        }
        
        private void OnDiscountValueTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                Debug.WriteLine("Enter key pressed in DiscountValueTextBox");
                Apply_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                Debug.WriteLine("Escape key pressed in DiscountValueTextBox");
                CloseDialog();
                e.Handled = true;
            }
        }
        
        private void OnDialogPreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                Debug.WriteLine("Escape key detected in DiscountDialog");
                CloseDialog();
                e.Handled = true;
            }
        }
        
        private void CloseDialog()
        {
            Debug.WriteLine("CloseDialog called, attempting to close with identifier SalesDialog");
            try
            {
                DialogHost.Close("SalesDialog");
                Debug.WriteLine("DialogHost.Close called successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error closing dialog: {ex.Message}");
            }
        }

        private void LoadDiscountTypes()
        {
            Debug.WriteLine("\nStarting LoadDiscountTypes...");

            try
            {
                if (_discountService == null)
                {
                    Debug.WriteLine("ERROR: _discountService is null");
                    throw new InvalidOperationException("Discount service is not initialized");
                }

                if (_currentUser == null)
                {
                    Debug.WriteLine("ERROR: _currentUser is null");
                    throw new InvalidOperationException("Current user is not initialized");
                }

                Debug.WriteLine($"Calling GetAvailableDiscountTypes with user: {_currentUser.Username}, isCartWide: {_isCartWide}");
                var types = _discountService.GetAvailableDiscountTypes(_currentUser, _isCartWide);
                Debug.WriteLine($"Retrieved {types?.Count ?? 0} discount types from service");

                if (types == null)
                {
                    Debug.WriteLine("ERROR: GetAvailableDiscountTypes returned null");
                    throw new InvalidOperationException("No discount types available");
                }

                _discountTypes = new ObservableCollection<DiscountTypeViewModel>(
                    types.Select(t => new DiscountTypeViewModel
                    {
                        Type = t,
                        IsSelected = t.Name == "Fixed Amount"
                    })
                );

                // Set the first type as selected if no Fixed Amount type
                if (!_discountTypes.Any(t => t.IsSelected) && _discountTypes.Any())
                {
                    _discountTypes.First().IsSelected = true;
                }

                DiscountTypeComboBox.ItemsSource = _discountTypes;

                // Set initial selected type
                _selectedDiscountType = _discountTypes.FirstOrDefault(t => t.IsSelected)?.Type;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in LoadDiscountTypes: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
            if (_selectedDiscountType != null)
            {
                UpdateDiscountValuePrefix(_selectedDiscountType.Name == "Percentage");
            }
            
            Debug.WriteLine("LoadDiscountTypes completed.");
        }

        private void LoadDiscountReasons()
        {
            Debug.WriteLine("\nStarting LoadDiscountReasons...");
            
            var reasons = _discountService.GetActiveDiscountReasons();
            Debug.WriteLine($"\nRetrieved {reasons.Count} active discount reasons from database");
            
            foreach (var reason in reasons)
            {
                Debug.WriteLine($"\nProcessing reason - Code: {reason.Code}, Original Description: {reason.Description}");
                reason.InternalCode = reason.Code;
                
                // Try to find translation with exact match first
                var exactKey = $"DiscountReason{reason.Code}";
                var translation = Application.Current.TryFindResource(exactKey) as string;
                
                if (translation == null)
                {
                    // If exact match fails, try with normalized key
                    var normalizedKey = $"DiscountReason{new string(reason.Code.Where(c => char.IsLetterOrDigit(c)).ToArray())}";
                    translation = Application.Current.TryFindResource(normalizedKey) as string;
                    Debug.WriteLine($"Trying normalized key: {normalizedKey}, Found: {translation != null}");
                }
                
                if (translation != null)
                {
                    reason.Description = translation;
                    Debug.WriteLine($"Found translation: {translation}");
                }
                else
                {
                    Debug.WriteLine($"No translation found for {reason.Code}, keeping original description");
                }
                
                Debug.WriteLine($"Final Description: {reason.Description}");
            }
            
            DiscountReasonComboBox.ItemsSource = reasons;
            
            // Set default to Customer Satisfaction
            var customerSatisfactionReason = reasons.FirstOrDefault(r => r.InternalCode == "CUSTOMER");
            Debug.WriteLine($"\nSetting default selection - Found CUSTOMER reason: {customerSatisfactionReason != null}");
            if (customerSatisfactionReason != null)
            {
                DiscountReasonComboBox.SelectedItem = customerSatisfactionReason;
                Debug.WriteLine($"Set default selection to: {customerSatisfactionReason.Description}");
            }
            
            Debug.WriteLine("LoadDiscountReasons completed.");
        }

        private void DiscountType_SelectionChanged(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("DiscountType_SelectionChanged called");
            
            if (sender is RadioButton radioButton && radioButton.DataContext is DiscountTypeViewModel viewModel)
            {
                // Update selection state
                foreach (var item in _discountTypes)
                {
                    item.IsSelected = (item == viewModel);
                }
                
                _selectedDiscountType = viewModel.Type;
                Debug.WriteLine($"Selected discount type: {_selectedDiscountType.Name}");
                
                UpdateDiscountValuePrefix(_selectedDiscountType.Name == "Percentage");
                DiscountValueTextBox.Text = "";
            }
            
            ValidateDiscount();
        }

        private void UpdateDiscountValuePrefix(bool isPercentage)
        {
            string prefixText = isPercentage ? "%" : 
                (Application.Current.TryFindResource("CurrencySymbol") as string ?? "$");
            
            MaterialDesignThemes.Wpf.TextFieldAssist.SetPrefixText(DiscountValueTextBox, prefixText);
            Debug.WriteLine($"Set TextBox prefix to {prefixText}");
        }

        private void DiscountValue_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(DiscountValueTextBox.Text, out decimal value))
            {
                // Check for percentage > 100%
                if (_selectedDiscountType != null && _selectedDiscountType.Name == "Percentage" && value > 100)
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("PercentageExceeds100") as string ?? "Discount percentage cannot exceed 100%",
                        Application.Current.TryFindResource("WarningTitle") as string ?? "Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    
                    // Reset to 100%
                    DiscountValueTextBox.Text = "100";
                    _discountValue = 100;
                    return;
                }
                
                // Check if fixed amount discount exceeds original price
                if (_selectedDiscountType != null && _selectedDiscountType.Name == "Fixed Amount" && value > _originalPrice)
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("DiscountExceedsPrice") as string ?? "Discount amount cannot exceed the original price",
                        Application.Current.TryFindResource("WarningTitle") as string ?? "Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    
                    // Reset to original price
                    DiscountValueTextBox.Text = _originalPrice.ToString("0.##");
                    _discountValue = _originalPrice;
                    return;
                }

                _discountValue = value;
                Debug.WriteLine($"Discount value changed to: {_discountValue}");
                ValidateDiscount();
            }
        }

        private void ValidateDiscount()
        {
            if (_selectedDiscountType == null || _discountValue <= 0 || 
                DiscountReasonComboBox.SelectedItem == null)
            {
                return;
            }

            var canApply = _discountService.CanApplyDiscount(
                _currentUser,
                _selectedDiscountType,
                _discountValue,
                _originalPrice,
                _isCartWide);

            if (!canApply)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("InvalidDiscountPermission") as string,
                    Application.Current.TryFindResource("WarningTitle") as string,
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return;
            }

            var requiresApproval = _discountService.RequiresApproval(
                _currentUser,
                _selectedDiscountType,
                _discountValue,
                _isCartWide);

            if (requiresApproval)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("RequiresApproval") as string,
                    Application.Current.TryFindResource("InfoTitle") as string,
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("Apply_Click called");
            if (_selectedDiscountType == null || _discountValue <= 0 || 
                DiscountReasonComboBox.SelectedItem == null)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("FillRequiredFields") as string,
                    Application.Current.TryFindResource("WarningTitle") as string,
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                e.Handled = true;
                return;
            }

            // Validate discount limits
            if (_selectedDiscountType.Name == "Percentage" && _discountValue > 100)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("PercentageExceeds100") as string ?? "Discount percentage cannot exceed 100%",
                    Application.Current.TryFindResource("WarningTitle") as string ?? "Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                e.Handled = true;
                return;
            }

            if (_selectedDiscountType.Name == "Fixed Amount" && _discountValue > _originalPrice)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("DiscountExceedsPrice") as string ?? "Discount amount cannot exceed the original price",
                    Application.Current.TryFindResource("WarningTitle") as string ?? "Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                e.Handled = true;
                return;
            }

            // Validate user permissions before applying the discount
            var canApply = _discountService.CanApplyDiscount(
                _currentUser,
                _selectedDiscountType,
                _discountValue,
                _originalPrice,
                _isCartWide);

            if (!canApply)
            {
                MessageBox.Show(
                    Application.Current.TryFindResource("InvalidDiscountPermission") as string ?? 
                    "This discount cannot be applied. Please check your permissions or discount limits.",
                    Application.Current.TryFindResource("WarningTitle") as string ?? "Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                e.Handled = true;
                return;
            }

            try
            {
                // Validate all required values before creating discount
                var discountReason = DiscountReasonComboBox.SelectedItem as DiscountReason;
                if (discountReason == null)
                {
                    throw new InvalidOperationException("Invalid discount reason selected");
                }

                if (_selectedDiscountType == null)
                {
                    throw new InvalidOperationException("No discount type selected");
                }

                if (_currentUser == null)
                {
                    throw new InvalidOperationException("No user context available");
                }

                Debug.WriteLine($"Creating discount with following values:");
                Debug.WriteLine($"DiscountTypeId: {_selectedDiscountType.Id}");
                Debug.WriteLine($"DiscountValue: {_discountValue}");
                Debug.WriteLine($"OriginalPrice: {_originalPrice}");
                Debug.WriteLine($"ReasonId: {discountReason.Id}");
                Debug.WriteLine($"Comment: {CommentTextBox.Text}");
                Debug.WriteLine($"UserId: {_currentUser.Id}");
                Debug.WriteLine($"IsCartWide: {_isCartWide}");

                var discount = _discountService.CreateDiscount(
                    saleId: null,
                    saleItemId: null,
                    discountTypeId: _selectedDiscountType.Id,
                    discountValue: _discountValue,
                    originalPrice: _originalPrice,
                    reasonId: discountReason.Id,
                    comment: CommentTextBox.Text,
                    appliedByUser: _currentUser,
                    isCartWide: _isCartWide
                );

                Debug.WriteLine($"Created discount - Type: {_selectedDiscountType.Name}, Value: {_discountValue}, Reason: {discountReason.Description}");
                
                // Close the dialog with the discount result
                DialogHost.Close("SalesDialog", discount);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating discount: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Debug.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                MessageBox.Show(
                    $"Error: {ex.Message}",
                    Application.Current.TryFindResource("ErrorTitle") as string,
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                e.Handled = true;
            }
        }
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    // Unsubscribe from events
                    Loaded -= OnLoaded;
                    DiscountValueTextBox.KeyDown -= OnDiscountValueTextBoxKeyDown;
                    this.PreviewKeyDown -= OnDialogPreviewKeyDown;
                }
                
                _isDisposed = true;
            }
        }
    }
} 