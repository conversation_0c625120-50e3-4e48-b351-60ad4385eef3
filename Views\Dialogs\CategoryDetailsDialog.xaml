<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.CategoryDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:system="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="900"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:PassthroughConverter x:Key="PassthroughConverter"/>
        
        <!-- Column header styles -->
        <Style x:Key="ColumnHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="Margin" Value="8,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <!-- Row cell styles -->
        <Style x:Key="CellTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>
        
        <!-- Tab style for metrics -->
        <Style x:Key="MetricTabStyle" TargetType="TabItem" BasedOn="{StaticResource MaterialDesignNavigationRailTabItem}">
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="HeaderTemplate">
                <Setter.Value>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Width="18" Height="18" 
                                                   Margin="0,0,8,0" 
                                                   VerticalAlignment="Center"
                                                   Kind="{Binding Tag, RelativeSource={RelativeSource FindAncestor, AncestorType=TabItem}}"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Chart style -->
        <Style x:Key="MetricChart" TargetType="lvc:CartesianChart">
            <Setter Property="LegendLocation" Value="Right"/>
            <Setter Property="DisableAnimations" Value="False"/>
            <Setter Property="Margin" Value="0,16,0,0"/>
            <Setter Property="MinHeight" Value="300"/>
        </Style>
        
        <!-- Improved Loading Overlay -->
        <Style x:Key="LoadingOverlayStyle" TargetType="Grid">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="Opacity" Value="0.8"/>
            <Setter Property="Panel.ZIndex" Value="1000"/>
        </Style>
    </UserControl.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True">
        <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                             UniformCornerRadius="8"
                             materialDesign:ElevationAssist.Elevation="Dp4"
                             MinWidth="720"
                             MinHeight="540"
                             MaxHeight="{x:Static SystemParameters.PrimaryScreenHeight}">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="8,8,0,0"
                    Padding="16,16">
                <Grid>
                    <TextBlock Text="{DynamicResource DetailedCategoryStatistics}" 
                               FontSize="22"
                               FontWeight="Medium"
                               Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"/>
                    
                    <Button HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="{DynamicResource Close}"
                            Click="CloseButton_Click"
                            Padding="0"
                            Margin="0"
                            Foreground="{DynamicResource PrimaryHueMidForegroundBrush}">
                        <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                    </Button>
                </Grid>
            </Border>
            
            <!-- Content -->
            <Grid Grid.Row="1" Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Top row with search and date controls -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" MinWidth="180"/>
                        <ColumnDefinition Width="Auto" MinWidth="220"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Search Box -->
                    <TextBox Grid.Column="0"
                             x:Name="txtSearch"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="{DynamicResource SearchCategories}"
                             materialDesign:TextFieldAssist.HasClearButton="True"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,16,0"/>
                    
                    <!-- Date Control Panel -->
                    <Grid Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Previous day button with new icon -->
                        <Button Grid.Column="0"
                                Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="{DynamicResource PreviousDay}"
                                Click="PreviousDay_Click"
                                Margin="0,0,4,0"
                                Width="40" Height="40">
                            <materialDesign:PackIcon Kind="ArrowLeftBold" Width="24" Height="24"/>
                        </Button>
                        
                        <!-- Current Date Display -->
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding CurrentDateDisplay}" 
                                   FontWeight="Medium"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Center"
                                   TextWrapping="Wrap"
                                   TextAlignment="Center"
                                   MaxWidth="200"/>
                        
                        <!-- Next day button with new icon -->
                        <Button Grid.Column="2"
                                Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="{DynamicResource NextDay}"
                                Click="NextDay_Click"
                                Width="40" Height="40">
                            <materialDesign:PackIcon Kind="ArrowRightBold" Width="24" Height="24"/>
                        </Button>
                    </Grid>
                </Grid>
                
                <!-- Period Filter row -->
                <Grid Grid.Row="1" Margin="0,0,0,16">
                    <ComboBox Width="180"
                              ItemsSource="{Binding TimePeriods}"
                              SelectedItem="{Binding SelectedTimePeriod, Mode=TwoWay}"
                              DisplayMemberPath="DisplayName"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              HorizontalAlignment="Right"/>
                </Grid>

                <!-- Tab control with DataGrid -->
                <TabControl Grid.Row="2" 
                            Style="{StaticResource MaterialDesignTabControl}"
                            Background="Transparent"
                            TabStripPlacement="Top"
                            Visibility="Visible">
                    
                    <!-- Single view with no header -->
                    <TabItem Header=""
                             Visibility="Collapsed">
                        <Grid>
                            <!-- Loading Overlay - Improved with animation -->
                            <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource LoadingOverlayStyle}">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <materialDesign:Card Background="Transparent"
                                                        UniformCornerRadius="4"
                                                        Padding="12"
                                                        materialDesign:ElevationAssist.Elevation="Dp2">
                                        <StackPanel>
                                            <ProgressBar IsIndeterminate="True" 
                                                        Style="{StaticResource MaterialDesignCircularProgressBar}"
                                                        Width="48" Height="48"
                                                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                        HorizontalAlignment="Center"/>
                                            <TextBlock Text="{DynamicResource LoadingData}"
                                                        Margin="0,16,0,0"
                                                        FontSize="14"
                                                        TextAlignment="Center"
                                                        Foreground="{DynamicResource MaterialDesignBody}"/>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </StackPanel>
                            </Grid>
                            
                            <!-- Categories DataGrid - With virtualization improvements -->
                            <DataGrid x:Name="categoriesDataGrid"
                                      ItemsSource="{Binding FilteredCategories}"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True"
                                      Grid.IsSharedSizeScope="True"
                                      Margin="0,8"
                                      materialDesign:DataGridAssist.CellPadding="8"
                                      materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                      VirtualizingPanel.IsVirtualizing="True"
                                      VirtualizingPanel.VirtualizationMode="Recycling"
                                      VirtualizingPanel.ScrollUnit="Pixel"
                                      EnableRowVirtualization="True"
                                      EnableColumnVirtualization="True"
                                      LoadingRow="DataGrid_LoadingRow"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto">
                                <DataGrid.Resources>
                                    <!-- Improved cell template styles -->
                                    <Style TargetType="DataGridCell" BasedOn="{StaticResource {x:Type DataGridCell}}">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow" BasedOn="{StaticResource {x:Type DataGridRow}}">
                                        <Setter Property="BorderThickness" Value="0,0,0,1" />
                                        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}" />
                                        <Setter Property="MinHeight" Value="40" />
                                    </Style>
                                </DataGrid.RowStyle>
                                <DataGrid.Columns>
                                    <!-- Name -->
                                    <DataGridTemplateColumn Header="{DynamicResource CategoryName}" Width="*" MinWidth="150" SortMemberPath="Name">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Name, Mode=OneWay}" 
                                                         FontWeight="SemiBold"
                                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                         VerticalAlignment="Center"
                                                         TextWrapping="NoWrap"
                                                         TextTrimming="CharacterEllipsis"
                                                         Padding="8,4"
                                                         HorizontalAlignment="Left" 
                                                         UseLayoutRounding="True"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    
                                    <!-- Revenue -->
                                    <DataGridTemplateColumn Header="{DynamicResource Revenue}" Width="Auto" SortMemberPath="Revenue">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                                    <TextBlock Text="{Binding Revenue, StringFormat={}{0:N2}}" 
                                                             VerticalAlignment="Center"
                                                             Padding="8,4"/>
                                                    <TextBlock Text="{DynamicResource CurrencySymbol}" 
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    
                                    <!-- Profit -->
                                    <DataGridTemplateColumn Header="{DynamicResource Profit}" Width="Auto" SortMemberPath="Profit">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                                    <TextBlock Text="{Binding Profit, StringFormat={}{0:N2}}" 
                                                             VerticalAlignment="Center"
                                                             Padding="8,4"/>
                                                    <TextBlock Text="{DynamicResource CurrencySymbol}" 
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    
                                    <!-- Margin -->
                                    <DataGridTemplateColumn Header="{DynamicResource Margin}" Width="Auto" SortMemberPath="Margin">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Margin, StringFormat={}{0:N2}%}" 
                                                         VerticalAlignment="Center"
                                                         Padding="8,4"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    
                                    <!-- Items Sold -->
                                    <DataGridTemplateColumn Header="{DynamicResource ItemsSold}" Width="Auto" SortMemberPath="ItemsSold">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding ItemsSold, StringFormat={}{0:N0}}" 
                                                         VerticalAlignment="Center"
                                                         Padding="8,4"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>
                </TabControl>
            </Grid>
            
            <!-- Footer -->
            <Grid Grid.Row="2" 
                  Margin="16" 
                  HorizontalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Records Count (on left for RTL) -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="{Binding FilteredCategories.Count}" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text=" " 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text="{DynamicResource CategoriesFound}" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <!-- Close Button (on right for RTL) -->
                <Button Grid.Column="1"
                        Content="{DynamicResource Close}"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Click="CloseButton_Click"
                        HorizontalAlignment="Right"/>
            </Grid>
        </Grid>
        </materialDesign:Card>
    </ScrollViewer>
</UserControl> 