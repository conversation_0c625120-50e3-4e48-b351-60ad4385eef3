<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.SupplierDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="800"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <Style x:Key="DialogTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="8,8,8,8"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.8"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>
    </UserControl.Resources>
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MinWidth="700"
                         MaxWidth="900"
                         Margin="16">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="12,12,0,0"
                    Padding="24,12">
                <DockPanel LastChildFill="True">
                    <StackPanel Orientation="Horizontal" 
                              HorizontalAlignment="Left">
                        <materialDesign:PackIcon Kind="TruckDelivery" 
                                               Width="32" 
                                               Height="32" 
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource MaterialDesignPaper}"
                                               Margin="0,0,12,0"/>
                        <TextBlock x:Name="DialogTitle" 
                                 Text="{DynamicResource AddSupplier}" 
                                 FontSize="22"
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource MaterialDesignPaper}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </DockPanel>
            </Border>
            
            <!-- Content Section -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto" 
                         HorizontalScrollBarVisibility="Disabled"
                         Margin="0,8">
                <Grid Margin="24,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Left Column -->
                    <StackPanel Grid.Column="0" Grid.Row="0">
                        <TextBox x:Name="txtCompanyName"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource CompanyName}"/>
                        
                        <TextBox x:Name="txtContactPerson"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource ContactPerson}"/>
                        
                        <TextBox x:Name="txtEmail"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Email}"/>
                        
                        <TextBox x:Name="txtPhone"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Phone}"/>
                    </StackPanel>

                    <!-- Right Column -->
                    <StackPanel Grid.Column="1" Grid.Row="0">
                        <TextBox x:Name="txtWebsite"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Website}"/>
                        
                        <TextBox x:Name="txtAddress"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Address}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                        
                        <TextBox x:Name="txtNotes"
                                Style="{StaticResource DialogTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Notes}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                        
                        <CheckBox x:Name="chkIsActive"
                                 Style="{StaticResource MaterialDesignCheckBox}"
                                 Content="{DynamicResource SupplierIsActive}"
                                 IsChecked="True"
                                 Margin="8,16,8,8"/>
                    </StackPanel>
                </Grid>
            </ScrollViewer>
            
            <!-- Button Panel -->
            <Border Grid.Row="2" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,1,0,0"
                    Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Cancel Button -->
                    <Button x:Name="btnCancel" 
                            Grid.Column="1"
                            Content="{DynamicResource Cancel}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Height="40"
                            Width="120"
                            Margin="0,0,8,0"
                            Click="BtnCancel_Click"/>
                    
                    <!-- Save Button -->
                    <Button x:Name="btnSave" 
                            Grid.Column="2"
                            Content="{DynamicResource AddSupplier}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            Foreground="{DynamicResource MaterialDesignPaper}"
                            Height="40"
                            Width="120"
                            Click="BtnSave_Click"/>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:Card>
</UserControl> 