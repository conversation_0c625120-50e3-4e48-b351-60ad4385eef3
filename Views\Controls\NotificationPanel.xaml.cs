using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Services;

namespace POSSystem.Views.Controls
{
    public partial class NotificationPanel : UserControl
    {
        private readonly AlertService _alertService;
        public event EventHandler MarkAllAsReadClicked;
        public event EventHandler ViewAllClicked;

        public NotificationPanel()
        {
            InitializeComponent();
            _alertService = new AlertService(new POSSystem.Data.POSDbContext(), new DatabaseService());
        }

        public void SetNotifications(System.Collections.IEnumerable notifications)
        {
            NotificationsList.ItemsSource = notifications;
        }

        private void MarkAllAsRead_Click(object sender, RoutedEventArgs e)
        {
            _alertService.MarkAllAlertsAsRead();
            MarkAllAsReadClicked?.Invoke(this, EventArgs.Empty);
        }

        private void ViewAll_Click(object sender, RoutedEventArgs e)
        {
            var alertsWindow = new AlertsWindow(_alertService);
            alertsWindow.ShowDialog();
            ViewAllClicked?.Invoke(this, EventArgs.Empty);
        }
    }
} 