<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:POSSystem.Converters"
        xmlns:system="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="{DynamicResource LoginTitle}" Height="600" Width="1000"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            
            <!-- Styles -->
            <Style x:Key="LoginTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="Height" Value="56"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
                <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
                <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="Transparent"/>
                <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Visible"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="FontSize" Value="14"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    </Trigger>
                    <Trigger Property="IsFocused" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                        <Setter Property="BorderThickness" Value="2"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="TempPasswordTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="Height" Value="56"/>
                <Setter Property="Margin" Value="0,0,0,24"/>
                <Setter Property="Padding" Value="16,8,48,8"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="False"/>
                <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
                <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="Transparent"/>
                <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Visible"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="materialDesign:HintAssist.Hint" Value="{DynamicResource Password}"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    </Trigger>
                    <Trigger Property="IsFocused" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                        <Setter Property="BorderThickness" Value="2"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="LoginPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
                <Setter Property="Height" Value="56"/>
                <Setter Property="Margin" Value="0,0,0,24"/>
                <Setter Property="Padding" Value="16,8,48,8"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="False"/>
                <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
                <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="Transparent"/>
                <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Visible"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="FontSize" Value="14"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    </Trigger>
                    <Trigger Property="IsFocused" Value="True">
                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                        <Setter Property="BorderThickness" Value="2"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="LoginButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="48"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Border CornerRadius="16" Background="White">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="400"/>
            </Grid.ColumnDefinitions>

            <!-- Left Side - Decorative Panel -->
            <Grid Grid.Column="0" Background="{DynamicResource PrimaryHueMidBrush}">
                <!-- Close Button -->
                <Button x:Name="btnClose"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Click="btnClose_Click"
                        Width="32" Height="32"
                        Padding="0"
                        materialDesign:ButtonAssist.CornerRadius="16"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        Margin="16,16,0,0"
                        Foreground="{DynamicResource PrimaryHueMidForegroundBrush}">
                    <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                </Button>

                <!-- Welcome Content -->
                <StackPanel VerticalAlignment="Center" Margin="48,0">
                    <materialDesign:PackIcon Kind="StorefrontOutline"
                                           Width="120" Height="120"
                                           Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                           Opacity="0.9"
                                           Margin="0,0,0,32"/>
                    
                    <TextBlock Text="{DynamicResource LoginWelcome}"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             FontSize="24"
                             Margin="0,0,0,8"/>
                             
                    <TextBlock Text="{DynamicResource AppName}"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             FontSize="40"
                             FontWeight="Bold"
                             Margin="0,0,0,24"/>
                             
                    <TextBlock Text="{DynamicResource LoginSubtitle}"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             FontSize="16"
                             TextWrapping="Wrap"
                             Opacity="0.9"/>
                </StackPanel>

                <!-- Version Info -->
                <TextBlock Text="Version 1.0.0"
                         Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                         FontSize="12"
                         Opacity="0.7"
                         HorizontalAlignment="Left"
                         VerticalAlignment="Bottom"
                         Margin="48,0,0,24"/>
            </Grid>

            <!-- Right Side - Login Form -->
            <Grid Grid.Column="1" Background="{DynamicResource MaterialDesignPaper}">
                <Border Background="{DynamicResource MaterialDesignPaper}"
                        CornerRadius="16,0,0,16">
                    <Border.Effect>
                        <DropShadowEffect BlurRadius="20" 
                                        ShadowDepth="1" 
                                        Direction="180" 
                                        Color="#20000000"/>
                    </Border.Effect>
                    
                    <StackPanel Margin="48" VerticalAlignment="Center">
                        <TextBlock Text="{DynamicResource LoginTitle}"
                                 FontSize="32"
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 Margin="0,0,0,8"/>

                        <TextBlock Text="{DynamicResource LoginSubtitle}"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 FontSize="16"
                                 Margin="0,0,0,32"/>

                        <TextBox x:Name="txtUsername"
                                Style="{StaticResource LoginTextBox}"
                                materialDesign:HintAssist.Hint="{DynamicResource Username}"
                                materialDesign:TextFieldAssist.PrefixText="@"/>

                        <Grid>
                            <PasswordBox x:Name="txtPassword"
                                       Style="{StaticResource LoginPasswordBox}"
                                       materialDesign:HintAssist.Hint="{DynamicResource Password}"/>
                            <Button x:Name="btnPasswordReveal"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Height="32" Width="32"
                                    Padding="0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,24"
                                    Panel.ZIndex="1"
                                    Click="btnPasswordReveal_Click">
                                <materialDesign:PackIcon x:Name="passwordRevealIcon"
                                                       Kind="EyeOutline"
                                                       Width="20" Height="20"/>
                            </Button>
                        </Grid>

                        <Grid Margin="0,0,0,24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <CheckBox x:Name="chkRememberMe"
                                    Style="{StaticResource MaterialDesignCheckBox}"
                                    Content="{DynamicResource RememberMe}"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    Margin="0,0,0,0"/>

                            <Button Grid.Column="1"
                                    Content="{DynamicResource ForgotPassword}"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    HorizontalAlignment="Right"
                                    Padding="8,0"
                                    Click="btnForgotPassword_Click"/>
                        </Grid>

                        <Button x:Name="btnLogin"
                                Content="{DynamicResource LoginButton}"
                                Style="{StaticResource LoginButtonStyle}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                Click="btnLogin_Click"/>

                        <TextBlock x:Name="txtError"
                                 Foreground="{DynamicResource ValidationErrorBrush}"
                                 TextAlignment="Center"
                                 TextWrapping="Wrap"
                                 Margin="0,16,0,0"
                                 Visibility="Collapsed"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </Border>
</Window> 