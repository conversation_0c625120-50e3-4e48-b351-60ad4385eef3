<?xml version="1.0" encoding="utf-8"?>
<!-- UnpaidTransactionsView.xaml - Updated for proper dark theme support -->
<UserControl x:Class="POSSystem.Views.UnpaidTransactionsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:DatePastDueConverter x:Key="DatePastDueConverter"/>
            <converters:RemainingAmountConverter x:Key="RemainingAmountConverter"/>
            
            <!-- DataGrid Column Header Style -->
            <Style x:Key="CustomDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Padding" Value="10,12"/>
                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                <Setter Property="BorderThickness" Value="0,0,0,1"/>
            </Style>

            <!-- DataGrid Row Style -->
            <Style x:Key="CustomDataGridRowStyle" TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                <Setter Property="Background" Value="Transparent"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                    </Trigger>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueLightForegroundBrush}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Button Style -->
            <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="Height" Value="36"/>
                <Setter Property="Padding" Value="16,4"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- Main Content -->
        <Grid Margin="24" Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel>
                    <TextBlock Text="{DynamicResource UnpaidSalesManagement}"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             Foreground="{DynamicResource MaterialDesignHeadlineTextBrush}"/>
                    <TextBlock Text="{DynamicResource ManageUnpaidTransactionsDescription}"
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyTextBrush}"
                             Margin="0,4,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" 
                          Orientation="Horizontal" 
                          VerticalAlignment="Center">
                    <TextBox Width="250"
                            Style="{StaticResource AppTextBoxStyle}"
                            md:HintAssist.Hint="{DynamicResource SearchByInvoiceOrCustomer}"
                            md:TextFieldAssist.HasClearButton="True"
                            md:TextFieldAssist.PrefixText="🔍"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,16,0"/>
                    <ComboBox Width="150"
                             Style="{StaticResource AppComboBoxStyle}"
                             md:HintAssist.Hint="{DynamicResource FilterByStatus}"
                             SelectionChanged="StatusFilter_Changed">
                        <ComboBoxItem Content="{DynamicResource All}"/>
                        <ComboBoxItem Content="{DynamicResource Overdue}"/>
                        <ComboBoxItem Content="{DynamicResource DueToday}"/>
                    </ComboBox>
                </StackPanel>
            </Grid>

            <!-- Stats Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Unpaid Amount Card -->
                <md:Card Grid.Column="0" 
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        UniformCornerRadius="8"
                        md:ElevationAssist.Elevation="Dp2"
                        Margin="0,0,12,0">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Orientation="Horizontal" 
                                  Margin="0,0,0,12">
                            <md:PackIcon Kind="CashMultiple"
                                        Width="24" Height="24"
                                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                                        VerticalAlignment="Center"/>
                            <TextBlock Text="{DynamicResource TotalUnpaidAmount}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="12,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1"
                                 Text="{Binding UnpaidSalesTotal, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Grid>
                </md:Card>

                <!-- Overdue Sales Alert Card -->
                <md:Card Grid.Column="1"
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        UniformCornerRadius="8"
                        md:ElevationAssist.Elevation="Dp2"
                        Margin="12,0">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Orientation="Horizontal" 
                                  Margin="0,0,0,12">
                            <md:PackIcon Kind="AlertCircle"
                                        Width="24" Height="24"
                                        Foreground="{DynamicResource MaterialDesignError}"
                                        VerticalAlignment="Center"/>
                            <TextBlock Text="{DynamicResource OverdueSalesAlert}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="12,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1"
                                 Text="{Binding OverdueSalesCount}"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignError}"/>
                    </Grid>
                </md:Card>

                <!-- Past Due Amount Card -->
                <md:Card Grid.Column="2"
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        UniformCornerRadius="8"
                        md:ElevationAssist.Elevation="Dp2"
                        Margin="12,0,0,0">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Orientation="Horizontal" 
                                  Margin="0,0,0,12">
                            <md:PackIcon Kind="CalendarClock"
                                        Width="24" Height="24"
                                        Foreground="{DynamicResource MaterialDesignError}"
                                        VerticalAlignment="Center"/>
                            <TextBlock Text="{DynamicResource SalesPastDue}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="12,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1"
                                 Text="{Binding PastDueSalesAmount, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignError}"/>
                    </Grid>
                </md:Card>
            </Grid>

            <!-- Unpaid Sales List -->
            <md:Card Grid.Row="2" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    UniformCornerRadius="8"
                    md:ElevationAssist.Elevation="Dp2">
                <DockPanel Margin="20">
                    <TextBlock Text="{DynamicResource UnpaidSalesList}" 
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="{DynamicResource MaterialDesignHeadlineTextBrush}"
                             DockPanel.Dock="Top"
                             Margin="0,0,0,16"/>

                    <DataGrid ItemsSource="{Binding GroupedUnpaidSales}"
                             Style="{StaticResource AppDataGridStyle}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             BorderThickness="0"
                             Background="Transparent"
                             RowBackground="{DynamicResource MaterialDesignPaper}"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             HeadersVisibility="Column"
                             ColumnHeaderStyle="{StaticResource CustomDataGridColumnHeaderStyle}"
                             RowStyle="{StaticResource CustomDataGridRowStyle}"
                             md:DataGridAssist.CellPadding="8"
                             md:DataGridAssist.ColumnHeaderPadding="8">

                        <DataGrid.Columns>
                            <DataGridTemplateColumn Width="Auto" MinWidth="30">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ToggleButton Style="{StaticResource MaterialDesignExpanderToggleButton}"
                                                    IsChecked="{Binding IsExpanded, Mode=TwoWay}"
                                                    Foreground="{DynamicResource MaterialDesignBody}">
                                            <ToggleButton.RenderTransform>
                                                <RotateTransform Angle="0"/>
                                            </ToggleButton.RenderTransform>
                                        </ToggleButton>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTemplateColumn Header="{DynamicResource Customer}" 
                                                  Width="*"
                                                  MinWidth="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding CustomerName}"
                                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"/>
                                            <TextBlock Text="{Binding TotalUnpaidTransactions, StringFormat={}{0} unpaid invoices}"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="{DynamicResource TotalUnpaidAmount}" 
                                              Binding="{Binding TotalUnpaidAmount, StringFormat={}{0:N2} DA}"
                                              Width="Auto"
                                              MinWidth="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="Margin" Value="8,0"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="{DynamicResource PayAll}"
                                                Command="{Binding ProcessAllPaymentsCommand}"
                                                Style="{StaticResource AppPrimaryButtonStyle}"
                                                Height="36"
                                                Padding="16,4">
                                            <Button.ContentTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <md:PackIcon Kind="CashMultiple"
                                                                    Margin="0,0,8,0"/>
                                                        <TextBlock Text="{Binding}"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </Button.ContentTemplate>
                                        </Button>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </DockPanel>
            </md:Card>
        </Grid>

        <!-- Sale Details Popup -->
        <Popup x:Name="saleDetailsPopup" 
               StaysOpen="False"
               AllowsTransparency="True"
               Placement="Center">
            <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    md:ElevationAssist.Elevation="Dp3"
                    Width="800"
                    Margin="20">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                            Margin="24">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource SaleDetails}" 
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignHeadlineTextBrush}"
                                 Margin="0,0,0,24"/>
                        
                        <!-- Sale Info -->
                        <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                UniformCornerRadius="4"
                                Margin="0,0,0,24">
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Text="{DynamicResource InvoiceNumber}" 
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding InvoiceNumber}" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"/>
                                
                                <TextBlock Text="{DynamicResource SaleDate}" 
                                         Grid.Row="1"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding SaleDate, StringFormat=g}" 
                                         Grid.Row="1" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"/>
                                
                                <TextBlock Text="{DynamicResource DueDate}" 
                                         Grid.Row="2"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding DueDate, StringFormat=d, TargetNullValue='-'}" 
                                         Grid.Row="2" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"/>
                                
                                <TextBlock Text="{DynamicResource Customer}" 
                                         Grid.Row="3"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Grid.Row="3" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8">
                                    <Run Text="{Binding Customer.FirstName, Mode=OneWay}"/>
                                    <Run Text=" "/>
                                    <Run Text="{Binding Customer.LastName, Mode=OneWay}"/>
                                </TextBlock>
                            </Grid>
                        </md:Card>

                        <!-- Items List -->
                        <TextBlock Text="{DynamicResource SaleItems}" 
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignHeadlineTextBrush}"
                                 Margin="0,0,0,16"/>

                        <DataGrid ItemsSource="{Binding Items}"
                                 Style="{StaticResource AppDataGridStyle}"
                                 AutoGenerateColumns="False"
                                 IsReadOnly="True"
                                 HeadersVisibility="Column"
                                 GridLinesVisibility="Horizontal"
                                 BorderThickness="1"
                                 BorderBrush="{DynamicResource MaterialDesignDivider}"
                                 Background="Transparent"
                                 RowBackground="{DynamicResource MaterialDesignPaper}"
                                 AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                                 ColumnHeaderStyle="{StaticResource CustomDataGridColumnHeaderStyle}"
                                 RowStyle="{StaticResource CustomDataGridRowStyle}"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 Margin="0,0,0,24"
                                 Height="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource Product}" 
                                                  Binding="{Binding Product.Name}" 
                                                  Width="*">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource Quantity}" 
                                                  Binding="{Binding Quantity}" 
                                                  Width="100">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource UnitPrice}" 
                                                  Binding="{Binding UnitPrice, StringFormat={}{0:N2} DA}" 
                                                  Width="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource Total}" 
                                                  Binding="{Binding Total, StringFormat={}{0:N2} DA}" 
                                                  Width="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- Totals -->
                        <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                UniformCornerRadius="4"
                                Margin="0,0,0,24">
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Text="{DynamicResource Subtotal}" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding Subtotal, StringFormat={}{0:N2} DA}" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource Discount}" 
                                         Grid.Row="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding DiscountAmount, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="1" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource Tax}" 
                                         Grid.Row="2" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding TaxAmount, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="2" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource GrandTotal}" 
                                         Grid.Row="3" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Bold"/>
                                <TextBlock Text="{Binding GrandTotal, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="3" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Bold"/>

                                <TextBlock Text="{DynamicResource RemainingAmount}" 
                                         Grid.Row="4" 
                                         HorizontalAlignment="Right"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource MaterialDesignError}"/>
                                <TextBlock Grid.Row="4" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource MaterialDesignError}">
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource RemainingAmountConverter}">
                                            <Binding Path="GrandTotal"/>
                                            <Binding Path="AmountPaid"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </Grid>
                        </md:Card>

                        <!-- Actions -->
                        <StackPanel Orientation="Horizontal"
                                  HorizontalAlignment="Right">
                            <Button Content="{DynamicResource PrintSaleDetails}"
                                    Click="PrintSaleDetails_Click"
                                    Style="{StaticResource AppSecondaryButtonStyle}"
                                    Margin="0,0,8,0"/>
                            <Button Content="{DynamicResource Close}"
                                    Click="CloseDetails_Click"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Foreground="{DynamicResource MaterialDesignBody}"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </md:Card>
        </Popup>
    </Grid>
</UserControl> 