-- =====================================================
-- Two-Tier Invoice System Database Migration
-- =====================================================
-- This script adds support for the two-tier invoice creation system
-- where non-admin users can create draft invoices that require admin completion

-- =====================================================
-- STEP 1: Add Invoice Permission Columns to UserPermissions Table
-- =====================================================
-- Add the missing invoice permission columns to the UserPermissions table

-- Add invoice permission columns to UserPermissions table
ALTER TABLE UserPermissions ADD COLUMN CanCreateFullInvoices INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanCreateDraftInvoices INTEGER NOT NULL DEFAULT 1;
ALTER TABLE UserPermissions ADD COLUMN CanCompleteInvoiceDrafts INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanViewPendingDrafts INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanModifyInvoicePricing INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanSetPaymentTerms INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanSelectCustomersForInvoices INTEGER NOT NULL DEFAULT 1;
ALTER TABLE UserPermissions ADD COLUMN CanDeleteDraftInvoices INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanRejectDraftInvoices INTEGER NOT NULL DEFAULT 0;
ALTER TABLE UserPermissions ADD COLUMN CanManageInvoiceSettings INTEGER NOT NULL DEFAULT 0;

-- Update existing user permissions based on their roles
-- Admin users get full invoice permissions
UPDATE UserPermissions
SET CanCreateFullInvoices = 1,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 1,
    CanViewPendingDrafts = 1,
    CanModifyInvoicePricing = 1,
    CanSetPaymentTerms = 1,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 1,
    CanRejectDraftInvoices = 1,
    CanManageInvoiceSettings = 1
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name = 'Admin'
);

-- Manager users get most invoice permissions except full invoice creation and settings management
UPDATE UserPermissions
SET CanCreateFullInvoices = 0,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 1,
    CanViewPendingDrafts = 1,
    CanModifyInvoicePricing = 0,
    CanSetPaymentTerms = 1,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 0,
    CanRejectDraftInvoices = 1,
    CanManageInvoiceSettings = 0
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name = 'Manager'
);

-- Regular users (Employee/Cashier) get basic draft creation permissions
UPDATE UserPermissions
SET CanCreateFullInvoices = 0,
    CanCreateDraftInvoices = 1,
    CanCompleteInvoiceDrafts = 0,
    CanViewPendingDrafts = 0,
    CanModifyInvoicePricing = 0,
    CanSetPaymentTerms = 0,
    CanSelectCustomersForInvoices = 1,
    CanDeleteDraftInvoices = 0,
    CanRejectDraftInvoices = 0,
    CanManageInvoiceSettings = 0
WHERE UserId IN (
    SELECT u.Id FROM Users u
    INNER JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name IN ('Employee', 'Cashier')
);

-- =====================================================
-- STEP 2: Create Invoice and Related Tables
-- =====================================================

-- Create Invoice table with two-tier support
CREATE TABLE IF NOT EXISTS Invoice (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT NOT NULL UNIQUE,
    Type TEXT NOT NULL CHECK (Type IN ('Sales', 'Purchase')),
    IssueDate TEXT NOT NULL,
    DueDate TEXT NOT NULL,
    CustomerId INTEGER,
    SupplierId INTEGER,
    Subtotal REAL NOT NULL DEFAULT 0,
    DiscountAmount REAL NOT NULL DEFAULT 0,
    TaxAmount REAL NOT NULL DEFAULT 0,
    GrandTotal REAL NOT NULL DEFAULT 0,
    Status TEXT NOT NULL CHECK (Status IN ('Draft', 'Issued', 'Paid', 'Overdue', 'Cancelled')),
    PaymentTerms TEXT DEFAULT 'Net 30',
    Reference TEXT,
    Notes TEXT,
    
    -- Two-tier invoice system fields
    CreatedByUserId INTEGER NOT NULL,
    CompletedByUserId INTEGER,
    DraftCreatedAt TEXT NOT NULL,
    AdminCompletedAt TEXT,
    RequiresAdminCompletion INTEGER NOT NULL DEFAULT 0,
    
    -- Timestamps
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    UpdatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    
    -- Foreign key constraints
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
    FOREIGN KEY (CreatedByUserId) REFERENCES Users(Id),
    FOREIGN KEY (CompletedByUserId) REFERENCES Users(Id)
);

-- Create InvoiceItems table
CREATE TABLE IF NOT EXISTS InvoiceItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    ProductName TEXT NOT NULL, -- Store product name for historical accuracy
    Quantity REAL NOT NULL,
    UnitPrice REAL NOT NULL,
    Total REAL NOT NULL,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (InvoiceId) REFERENCES Invoice(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Create InvoicePayments table
CREATE TABLE IF NOT EXISTS InvoicePayments (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceId INTEGER NOT NULL,
    PaymentDate TEXT NOT NULL,
    Amount REAL NOT NULL,
    PaymentMethod TEXT NOT NULL,
    Reference TEXT,
    Notes TEXT,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (InvoiceId) REFERENCES Invoice(Id) ON DELETE CASCADE
);

-- Create DraftInvoiceNotifications table
CREATE TABLE IF NOT EXISTS DraftInvoiceNotifications (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceId INTEGER NOT NULL,
    CreatedByUserId INTEGER NOT NULL,
    NotificationType TEXT NOT NULL CHECK (NotificationType IN ('DRAFT_CREATED', 'DRAFT_COMPLETED', 'DRAFT_EXPIRED', 'DRAFT_REJECTED')),
    IsRead INTEGER NOT NULL DEFAULT 0,
    Message TEXT,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    ReadAt TEXT,
    FOREIGN KEY (InvoiceId) REFERENCES Invoice(Id) ON DELETE CASCADE,
    FOREIGN KEY (CreatedByUserId) REFERENCES Users(Id)
);

-- Create DraftInvoiceSettings table for system configuration
CREATE TABLE IF NOT EXISTS DraftInvoiceSettings (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ExpirationDays INTEGER NOT NULL DEFAULT 7,
    AutoNotifyAdmins INTEGER NOT NULL DEFAULT 1,
    AllowNonAdminCustomerSelection INTEGER NOT NULL DEFAULT 1,
    RequireReasonForDraft INTEGER NOT NULL DEFAULT 0,
    MaxDraftItemsPerInvoice INTEGER NOT NULL DEFAULT 50,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    UpdatedAt TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Insert default draft invoice settings
INSERT OR IGNORE INTO DraftInvoiceSettings (
    ExpirationDays, 
    AutoNotifyAdmins, 
    AllowNonAdminCustomerSelection, 
    RequireReasonForDraft,
    MaxDraftItemsPerInvoice,
    CreatedAt, 
    UpdatedAt
) VALUES (
    7, 1, 1, 0, 50, datetime('now'), datetime('now')
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_invoice_status ON Invoice(Status);
CREATE INDEX IF NOT EXISTS idx_invoice_requires_admin ON Invoice(RequiresAdminCompletion);
CREATE INDEX IF NOT EXISTS idx_invoice_created_by ON Invoice(CreatedByUserId);
CREATE INDEX IF NOT EXISTS idx_invoice_completed_by ON Invoice(CompletedByUserId);
CREATE INDEX IF NOT EXISTS idx_invoice_draft_created ON Invoice(DraftCreatedAt);
CREATE INDEX IF NOT EXISTS idx_invoice_type_status ON Invoice(Type, Status);

CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON InvoiceItems(InvoiceId);
CREATE INDEX IF NOT EXISTS idx_invoice_items_product ON InvoiceItems(ProductId);

CREATE INDEX IF NOT EXISTS idx_invoice_payments_invoice ON InvoicePayments(InvoiceId);
CREATE INDEX IF NOT EXISTS idx_invoice_payments_date ON InvoicePayments(PaymentDate);

CREATE INDEX IF NOT EXISTS idx_draft_notifications_unread ON DraftInvoiceNotifications(IsRead, CreatedAt);
CREATE INDEX IF NOT EXISTS idx_draft_notifications_invoice ON DraftInvoiceNotifications(InvoiceId);
CREATE INDEX IF NOT EXISTS idx_draft_notifications_user ON DraftInvoiceNotifications(CreatedByUserId);
CREATE INDEX IF NOT EXISTS idx_draft_notifications_type ON DraftInvoiceNotifications(NotificationType);

-- Create triggers for automatic timestamp updates
CREATE TRIGGER IF NOT EXISTS update_invoice_timestamp 
    AFTER UPDATE ON Invoice
    FOR EACH ROW
BEGIN
    UPDATE Invoice SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

CREATE TRIGGER IF NOT EXISTS update_draft_settings_timestamp 
    AFTER UPDATE ON DraftInvoiceSettings
    FOR EACH ROW
BEGIN
    UPDATE DraftInvoiceSettings SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

-- Create trigger to automatically create notifications when draft invoices are created
CREATE TRIGGER IF NOT EXISTS create_draft_notification 
    AFTER INSERT ON Invoice
    FOR EACH ROW
    WHEN NEW.RequiresAdminCompletion = 1 AND NEW.Status = 'Draft'
BEGIN
    INSERT INTO DraftInvoiceNotifications (
        InvoiceId, 
        CreatedByUserId, 
        NotificationType, 
        Message,
        CreatedAt
    ) VALUES (
        NEW.Id,
        NEW.CreatedByUserId,
        'DRAFT_CREATED',
        'New draft invoice ' || NEW.InvoiceNumber || ' created and requires admin completion',
        datetime('now')
    );
END;

-- Create trigger to create completion notifications
CREATE TRIGGER IF NOT EXISTS create_completion_notification 
    AFTER UPDATE ON Invoice
    FOR EACH ROW
    WHEN OLD.RequiresAdminCompletion = 1 AND NEW.RequiresAdminCompletion = 0 AND NEW.CompletedByUserId IS NOT NULL
BEGIN
    INSERT INTO DraftInvoiceNotifications (
        InvoiceId, 
        CreatedByUserId, 
        NotificationType, 
        Message,
        CreatedAt
    ) VALUES (
        NEW.Id,
        NEW.CreatedByUserId,
        'DRAFT_COMPLETED',
        'Draft invoice ' || NEW.InvoiceNumber || ' has been completed by admin',
        datetime('now')
    );
END;

-- Sample data for testing (optional - can be removed in production)
-- INSERT INTO Invoice (
--     InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
--     Subtotal, GrandTotal, Status, CreatedByUserId, 
--     DraftCreatedAt, RequiresAdminCompletion
-- ) VALUES (
--     'DRAFT-001', 'Sales', date('now'), date('now', '+30 days'), 1,
--     100.00, 100.00, 'Draft', 1,
--     datetime('now'), 1
-- );

-- Verification queries (for testing)
-- SELECT 'Invoice table created' as Status, COUNT(*) as RecordCount FROM Invoice;
-- SELECT 'InvoiceItems table created' as Status, COUNT(*) as RecordCount FROM InvoiceItems;
-- SELECT 'InvoicePayments table created' as Status, COUNT(*) as RecordCount FROM InvoicePayments;
-- SELECT 'DraftInvoiceNotifications table created' as Status, COUNT(*) as RecordCount FROM DraftInvoiceNotifications;
-- SELECT 'DraftInvoiceSettings table created' as Status, COUNT(*) as RecordCount FROM DraftInvoiceSettings;

PRAGMA foreign_keys = ON;
