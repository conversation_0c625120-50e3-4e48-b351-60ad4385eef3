using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using POSSystem.Models;
using System.Diagnostics;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Service responsible for calculating business metrics and growth comparisons.
    /// Extracted from DashboardViewModel to improve code organization and maintainability.
    /// </summary>
    public class MetricsCalculationService : IMetricsCalculationService
    {
        /// <summary>
        /// Calculates sales metrics with growth comparison
        /// </summary>
        public (decimal current, decimal growth) CalculateSalesMetrics(
            List<SaleAggregation> currentPeriod, 
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0);
            }

            // Calculate current period total
            decimal currentTotal = currentPeriod.Sum(s => s.TotalSales);

            // Calculate growth if previous period exists
            decimal growth = 0;
            if (previousPeriod != null && previousPeriod.Any())
            {
                decimal previousTotal = previousPeriod.Sum(s => s.TotalSales);
                growth = CalculateGrowthPercentage(currentTotal, previousTotal);
            }

            return (currentTotal, growth);
        }

        /// <summary>
        /// Calculates profit metrics with growth comparison
        /// </summary>
        public (decimal profit, decimal margin, decimal growth) CalculateProfitMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0, 0);
            }

            // Calculate current period totals
            decimal currentProfit = currentPeriod.Sum(s => s.TotalProfit);
            decimal currentSales = currentPeriod.Sum(s => s.TotalSales);
            decimal margin = currentSales > 0 ? (currentProfit / currentSales) * 100 : 0;

            // Calculate growth if previous period exists
            decimal growth = 0;
            if (previousPeriod != null && previousPeriod.Any())
            {
                decimal previousProfit = previousPeriod.Sum(s => s.TotalProfit);
                growth = CalculateGrowthPercentage(currentProfit, previousProfit);
            }

            return (currentProfit, margin, growth);
        }

        /// <summary>
        /// Calculates sales volume metrics (items sold) with growth comparison
        /// </summary>
        public (decimal itemsSold, decimal growth) CalculateItemsSoldMetrics( // ✅ WEIGHT-BASED FIX: Changed return type from int to decimal
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0);
            }

            // Calculate current period total
            decimal currentTotal = currentPeriod.Sum(s => s.TotalItems); // ✅ WEIGHT-BASED FIX: Changed from int to decimal

            // Calculate growth if previous period exists
            decimal growth = 0;
            if (previousPeriod != null && previousPeriod.Any())
            {
                decimal previousTotal = previousPeriod.Sum(s => s.TotalItems); // ✅ WEIGHT-BASED FIX: Changed from int to decimal
                growth = CalculateGrowthPercentage(currentTotal, previousTotal);
            }

            return (currentTotal, growth);
        }

        /// <summary>
        /// Calculates order count metrics with growth comparison
        /// </summary>
        public (int orderCount, decimal growth) CalculateOrderCountMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0);
            }

            // Calculate current period total
            int currentTotal = currentPeriod.Sum(s => s.OrderCount);

            // Calculate growth if previous period exists
            decimal growth = 0;
            if (previousPeriod != null && previousPeriod.Any())
            {
                int previousTotal = previousPeriod.Sum(s => s.OrderCount);
                growth = CalculateGrowthPercentage(currentTotal, previousTotal);
            }

            return (currentTotal, growth);
        }

        /// <summary>
        /// Calculates average order value metrics with growth comparison
        /// </summary>
        public (decimal avgOrderValue, decimal growth) CalculateAvgOrderValueMetrics(
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0);
            }

            // Calculate current period metrics
            decimal currentSales = currentPeriod.Sum(s => s.TotalSales);
            int currentOrders = currentPeriod.Sum(s => s.OrderCount);
            decimal currentAvg = currentOrders > 0 ? currentSales / currentOrders : 0;

            // Calculate growth if previous period exists
            decimal growth = 0;
            if (previousPeriod != null && previousPeriod.Any())
            {
                decimal previousSales = previousPeriod.Sum(s => s.TotalSales);
                int previousOrders = previousPeriod.Sum(s => s.OrderCount);
                decimal previousAvg = previousOrders > 0 ? previousSales / previousOrders : 0;
                
                growth = CalculateGrowthPercentage(currentAvg, previousAvg);
            }

            return (currentAvg, growth);
        }

        /// <summary>
        /// Gets metrics based on type and calculates their values
        /// </summary>
        public (decimal value, decimal growth) GetMetricByType(
            string metricType,
            List<SaleAggregation> currentPeriod,
            List<SaleAggregation> previousPeriod)
        {
            if (currentPeriod == null || !currentPeriod.Any())
            {
                return (0, 0);
            }

            switch (metricType?.ToLower())
            {
                case "sales":
                    var salesMetrics = CalculateSalesMetrics(currentPeriod, previousPeriod);
                    return salesMetrics;

                case "profit":
                    var profitMetrics = CalculateProfitMetrics(currentPeriod, previousPeriod);
                    return (profitMetrics.profit, profitMetrics.growth);

                case "margin":
                    var marginMetrics = CalculateProfitMetrics(currentPeriod, previousPeriod);
                    return (marginMetrics.margin, 0); // Margin doesn't typically use growth comparison

                case "items":
                    var itemsMetrics = CalculateItemsSoldMetrics(currentPeriod, previousPeriod);
                    return (itemsMetrics.itemsSold, itemsMetrics.growth);

                case "orders":
                    var orderMetrics = CalculateOrderCountMetrics(currentPeriod, previousPeriod);
                    return (orderMetrics.orderCount, orderMetrics.growth);

                case "avgorder":
                    var avgOrderMetrics = CalculateAvgOrderValueMetrics(currentPeriod, previousPeriod);
                    return avgOrderMetrics;

                default:
                    return (0, 0);
            }
        }

        /// <summary>
        /// Calculates the percentage growth between two values
        /// </summary>
        private decimal CalculateGrowthPercentage(decimal current, decimal previous)
        {
            if (previous == 0)
            {
                return current > 0 ? 100 : 0;
            }

            return ((current - previous) / previous) * 100;
        }
    }
} 