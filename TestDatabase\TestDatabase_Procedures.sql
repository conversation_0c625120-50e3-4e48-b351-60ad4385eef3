-- POS System Test Database - Stored Procedures and Triggers
-- This script creates stored procedures and triggers for the test database

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Create a trigger to update UpdatedAt timestamp when a record is updated
CREATE TRIGGER IF NOT EXISTS update_user_timestamp 
AFTER UPDATE ON Users
FOR EACH ROW
BEGIN
    UPDATE Users SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

-- Create a trigger to update UpdatedAt timestamp when a customer is updated
CREATE TRIGGER IF NOT EXISTS update_customer_timestamp 
AFTER UPDATE ON Customers
FOR EACH ROW
BEGIN
    UPDATE Customers SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

-- Create a trigger to update UpdatedAt timestamp when a product is updated
CREATE TRIGGER IF NOT EXISTS update_product_timestamp 
AFTER UPDATE ON Products
FOR EACH ROW
BEGIN
    UPDATE Products SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

-- Create a trigger to update inventory when a sale is made
CREATE TRIGGER IF NOT EXISTS update_inventory_after_sale
AFTER INSERT ON SaleItems
FOR EACH ROW
BEGIN
    -- Reduce the stock quantity
    UPDATE Products 
    SET StockQuantity = StockQuantity - NEW.Quantity 
    WHERE Id = NEW.ProductId;
    
    -- Add inventory transaction record
    INSERT INTO InventoryTransactions (
        TransactionType, 
        ProductId, 
        Quantity, 
        UnitPrice,
        TotalPrice,
        ReferenceNumber,
        Notes,
        TransactionDate,
        UserId
    )
    SELECT 
        'Sale', 
        NEW.ProductId, 
        -NEW.Quantity, 
        NEW.UnitPrice,
        -NEW.Total,
        s.InvoiceNumber,
        'Sale transaction',
        datetime('now'),
        s.UserId
    FROM Sales s
    WHERE s.Id = NEW.SaleId;
END;

-- Create a trigger to update loyalty points when a sale is made
CREATE TRIGGER IF NOT EXISTS update_loyalty_points_after_sale
AFTER INSERT ON Sales
FOR EACH ROW
WHEN NEW.CustomerId IS NOT NULL
BEGIN
    -- Calculate points based on grand total
    UPDATE Customers
    SET 
        LoyaltyPoints = LoyaltyPoints + (SELECT CAST(NEW.GrandTotal * lp.PointsPerDollar * lt.PointsMultiplier AS INTEGER)
                                        FROM Customers c
                                        JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
                                        JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
                                        WHERE c.Id = NEW.CustomerId),
        TotalSpent = TotalSpent + NEW.GrandTotal,
        TotalVisits = TotalVisits + 1,
        LastVisit = datetime('now'),
        UpdatedAt = datetime('now')
    WHERE Id = NEW.CustomerId;
    
    -- Add loyalty transaction record
    INSERT INTO LoyaltyTransactions (
        CustomerId,
        TransactionType,
        Points,
        PointsBalance,
        SaleId,
        Description,
        CreatedAt
    )
    SELECT 
        NEW.CustomerId,
        'Earn',
        CAST(NEW.GrandTotal * lp.PointsPerDollar * lt.PointsMultiplier AS INTEGER),
        c.LoyaltyPoints,
        NEW.Id,
        'Points earned from sale ' || NEW.InvoiceNumber,
        datetime('now')
    FROM Customers c
    JOIN LoyaltyTiers lt ON c.LoyaltyTierId = lt.Id
    JOIN LoyaltyPrograms lp ON lt.LoyaltyProgramId = lp.Id
    WHERE c.Id = NEW.CustomerId;
END;

-- Create a trigger to update loyalty tier when points threshold is reached
CREATE TRIGGER IF NOT EXISTS update_loyalty_tier
AFTER UPDATE OF LoyaltyPoints ON Customers
FOR EACH ROW
BEGIN
    -- Update the tier if points threshold is reached
    UPDATE Customers
    SET LoyaltyTierId = (
        SELECT lt.Id
        FROM LoyaltyTiers lt
        WHERE lt.LoyaltyProgramId = (
            SELECT lt2.LoyaltyProgramId 
            FROM LoyaltyTiers lt2 
            WHERE lt2.Id = NEW.LoyaltyTierId
        )
        AND lt.MinimumPoints <= NEW.LoyaltyPoints
        ORDER BY lt.MinimumPoints DESC
        LIMIT 1
    )
    WHERE Id = NEW.Id AND LoyaltyPoints != OLD.LoyaltyPoints;
END;

-- Create a trigger to automatically create a sale history record when a sale status changes
CREATE TRIGGER IF NOT EXISTS create_sale_history
AFTER UPDATE OF Status ON Sales
FOR EACH ROW
WHEN NEW.Status != OLD.Status
BEGIN
    INSERT INTO SaleHistory (
        SaleId,
        PreviousStatus,
        NewStatus,
        UserId,
        Notes,
        CreatedAt
    )
    VALUES (
        NEW.Id,
        OLD.Status,
        NEW.Status,
        NEW.UserId,
        'Status changed from ' || OLD.Status || ' to ' || NEW.Status,
        datetime('now')
    );
END;

-- Create a stored procedure to apply discount to a sale
CREATE PROCEDURE ApplyDiscount(
    IN p_SaleId INTEGER,
    IN p_DiscountTypeId INTEGER,
    IN p_DiscountValue REAL,
    IN p_ReasonId INTEGER,
    IN p_Comment TEXT,
    IN p_UserId INTEGER,
    IN p_SaleItemId INTEGER -- NULL for entire sale
)
BEGIN
    DECLARE v_OriginalPrice REAL;
    DECLARE v_FinalPrice REAL;
    
    -- Get the original price (either sale total or item total)
    IF p_SaleItemId IS NULL THEN
        SELECT Subtotal INTO v_OriginalPrice FROM Sales WHERE Id = p_SaleId;
    ELSE
        SELECT Total INTO v_OriginalPrice FROM SaleItems WHERE Id = p_SaleItemId AND SaleId = p_SaleId;
    END IF;
    
    -- Calculate final price based on discount type
    IF p_DiscountTypeId = 1 THEN -- Percentage
        SET v_FinalPrice = v_OriginalPrice * (1 - (p_DiscountValue / 100));
    ELSEIF p_DiscountTypeId = 2 THEN -- Fixed Amount
        SET v_FinalPrice = v_OriginalPrice - p_DiscountValue;
    ELSEIF p_DiscountTypeId = 3 THEN -- Price Override
        SET v_FinalPrice = p_DiscountValue;
    END IF;
    
    -- Ensure final price is not negative
    IF v_FinalPrice < 0 THEN
        SET v_FinalPrice = 0;
    END IF;
    
    -- Insert the discount record
    INSERT INTO Discounts (
        DiscountTypeId,
        DiscountValue,
        OriginalPrice,
        FinalPrice,
        ReasonId,
        Comment,
        SaleId,
        SaleItemId,
        AppliedByUserId,
        AppliedAt,
        IsActive
    ) VALUES (
        p_DiscountTypeId,
        p_DiscountValue,
        v_OriginalPrice,
        v_FinalPrice,
        p_ReasonId,
        p_Comment,
        p_SaleId,
        p_SaleItemId,
        p_UserId,
        datetime('now'),
        1
    );
    
    -- Update the sale or sale item with new price
    IF p_SaleItemId IS NULL THEN
        -- Update the entire sale
        UPDATE Sales
        SET 
            Subtotal = v_OriginalPrice,
            DiscountAmount = v_OriginalPrice - v_FinalPrice,
            GrandTotal = v_FinalPrice + TaxAmount,
            UpdatedAt = datetime('now')
        WHERE Id = p_SaleId;
    ELSE
        -- Update the individual sale item
        UPDATE SaleItems
        SET Total = v_FinalPrice
        WHERE Id = p_SaleItemId AND SaleId = p_SaleId;
        
        -- Recalculate the sale totals
        UPDATE Sales
        SET 
            Subtotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = p_SaleId),
            GrandTotal = (SELECT SUM(Total) FROM SaleItems WHERE SaleId = p_SaleId) + TaxAmount,
            DiscountAmount = (SELECT Subtotal - SUM(Total) FROM SaleItems WHERE SaleId = p_SaleId),
            UpdatedAt = datetime('now')
        WHERE Id = p_SaleId;
    END IF;
END;

-- Create a stored procedure to check if a user can apply a specific discount
CREATE PROCEDURE CanApplyDiscount(
    IN p_UserId INTEGER,
    IN p_DiscountTypeId INTEGER,
    IN p_DiscountValue REAL,
    OUT p_CanApply INTEGER,
    OUT p_RequiresApproval INTEGER,
    OUT p_Message TEXT
)
BEGIN
    DECLARE v_RoleId INTEGER;
    DECLARE v_MaxPercentage REAL;
    DECLARE v_MaxFixedAmount REAL;
    DECLARE v_MinPricePercentage REAL;
    DECLARE v_ApprovalThreshold REAL;
    DECLARE v_RequiresApproval INTEGER;
    
    -- Get the user's role
    SELECT RoleId INTO v_RoleId FROM Users WHERE Id = p_UserId;
    
    -- Get the discount permission settings
    SELECT 
        MaxPercentage,
        MaxFixedAmount,
        MinPricePercentage,
        RequiresApproval,
        ApprovalThreshold
    INTO 
        v_MaxPercentage,
        v_MaxFixedAmount,
        v_MinPricePercentage,
        v_RequiresApproval,
        v_ApprovalThreshold
    FROM DiscountPermissions
    WHERE RoleId = v_RoleId AND DiscountTypeId = p_DiscountTypeId AND IsActive = 1;
    
    -- Check if the user has permission for this discount type
    IF v_MaxPercentage IS NULL AND v_MaxFixedAmount IS NULL AND p_DiscountTypeId != 3 THEN
        SET p_CanApply = 0;
        SET p_RequiresApproval = 0;
        SET p_Message = 'You do not have permission to apply this type of discount.';
        RETURN;
    END IF;
    
    -- Check if the discount value exceeds the user's permission
    IF p_DiscountTypeId = 1 AND p_DiscountValue > v_MaxPercentage THEN
        SET p_CanApply = 0;
        SET p_RequiresApproval = 0;
        SET p_Message = 'The percentage discount exceeds your maximum allowed (' || v_MaxPercentage || '%).';
        RETURN;
    END IF;
    
    IF p_DiscountTypeId = 2 AND p_DiscountValue > v_MaxFixedAmount THEN
        SET p_CanApply = 0;
        SET p_RequiresApproval = 0;
        SET p_Message = 'The fixed amount discount exceeds your maximum allowed ($' || v_MaxFixedAmount || ').';
        RETURN;
    END IF;
    
    -- Check if approval is required
    IF v_RequiresApproval = 1 THEN
        IF (p_DiscountTypeId = 1 AND p_DiscountValue > v_ApprovalThreshold) OR
           (p_DiscountTypeId = 2 AND p_DiscountValue > v_ApprovalThreshold) THEN
            SET p_CanApply = 1;
            SET p_RequiresApproval = 1;
            SET p_Message = 'This discount requires manager approval.';
            RETURN;
        END IF;
    END IF;
    
    -- If we got here, the discount can be applied
    SET p_CanApply = 1;
    SET p_RequiresApproval = 0;
    SET p_Message = 'Discount can be applied.';
END;

-- Create a view for sales summary
CREATE VIEW IF NOT EXISTS SalesSummary AS
SELECT 
    s.Id AS SaleId,
    s.InvoiceNumber,
    s.SaleDate,
    CASE 
        WHEN c.Id IS NOT NULL THEN c.FirstName || ' ' || c.LastName
        ELSE 'Guest Customer'
    END AS CustomerName,
    u.FirstName || ' ' || u.LastName AS SalesAssociate,
    s.Subtotal,
    s.DiscountAmount,
    s.TaxAmount,
    s.GrandTotal,
    s.PaymentMethod,
    s.PaymentStatus,
    s.Status,
    s.TotalItems,
    (SELECT GROUP_CONCAT(p.Name, ', ') 
     FROM SaleItems si 
     JOIN Products p ON si.ProductId = p.Id 
     WHERE si.SaleId = s.Id) AS Products
FROM Sales s
LEFT JOIN Customers c ON s.CustomerId = c.Id
JOIN Users u ON s.UserId = u.Id;

-- Create a view for inventory status
CREATE VIEW IF NOT EXISTS InventoryStatus AS
SELECT 
    p.Id AS ProductId,
    p.SKU,
    p.Name AS ProductName,
    c.Name AS Category,
    p.StockQuantity,
    p.MinimumStock,
    p.ReorderPoint,
    p.SellingPrice,
    p.PurchasePrice,
    u.Name AS UnitOfMeasure,
    s.Name AS Supplier,
    CASE 
        WHEN p.StockQuantity <= p.MinimumStock THEN 'Critical'
        WHEN p.StockQuantity <= p.ReorderPoint THEN 'Low'
        ELSE 'Adequate'
    END AS StockStatus,
    (SELECT MAX(bs.ExpiryDate) FROM BatchStock bs WHERE bs.ProductId = p.Id) AS NextExpiry,
    (SELECT COUNT(*) FROM SaleItems si WHERE si.ProductId = p.Id) AS TimesSold
FROM Products p
JOIN Categories c ON p.CategoryId = c.Id
JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
JOIN Suppliers s ON p.SupplierId = s.Id
WHERE p.IsActive = 1;

-- Create a view for discount usage
CREATE VIEW IF NOT EXISTS DiscountUsage AS
SELECT 
    d.Id AS DiscountId,
    dt.Name AS DiscountType,
    d.DiscountValue,
    dr.Description AS Reason,
    d.OriginalPrice,
    d.FinalPrice,
    d.OriginalPrice - d.FinalPrice AS AmountSaved,
    CASE 
        WHEN dt.Id = 1 THEN d.DiscountValue || '%'
        WHEN dt.Id = 2 THEN '$' || d.DiscountValue
        WHEN dt.Id = 3 THEN 'Price Override to $' || d.FinalPrice
    END AS DiscountDescription,
    CASE 
        WHEN d.SaleItemId IS NULL THEN 'Entire Sale'
        ELSE (SELECT p.Name FROM SaleItems si JOIN Products p ON si.ProductId = p.Id WHERE si.Id = d.SaleItemId)
    END AS AppliedTo,
    s.InvoiceNumber,
    u.FirstName || ' ' || u.LastName AS AppliedBy,
    d.AppliedAt,
    d.Comment
FROM Discounts d
JOIN DiscountTypes dt ON d.DiscountTypeId = dt.Id
JOIN DiscountReasons dr ON d.ReasonId = dr.Id
JOIN Sales s ON d.SaleId = s.Id
JOIN Users u ON d.AppliedByUserId = u.Id
WHERE d.IsActive = 1; 