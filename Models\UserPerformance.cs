using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace POSSystem.Models
{
    public class UserPerformance : INotifyPropertyChanged
    {
        private decimal _targetProgress;
        private decimal _totalSales;
        private decimal _salesTarget;

        public string UserId { get; set; }
        public string UserName { get; set; }
        public decimal TotalSales 
        { 
            get => _totalSales;
            set
            {
                _totalSales = value;
                UpdateTargetProgress();
                OnPropertyChanged();
            }
        }
        public int TransactionCount { get; set; }
        public decimal AvgTransactionValue { get; set; }
        public decimal ConversionRate { get; set; }
        public TimeSpan AverageTransactionTime { get; set; }
        public int CustomersServed { get; set; }
        public decimal SalesTarget 
        { 
            get => _salesTarget;
            set
            {
                _salesTarget = value;
                UpdateTargetProgress();
                OnPropertyChanged();
            }
        }
        public decimal TargetProgress 
        { 
            get => _targetProgress;
            private set
            {
                _targetProgress = value;
                OnPropertyChanged();
            }
        }

        private void UpdateTargetProgress()
        {
            TargetProgress = SalesTarget > 0 ? (TotalSales / SalesTarget) * 100 : 0;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 