-- Update Invoice Type constraint to support Stock Reservation invoices
-- SQLite doesn't support ALTER TABLE to modify CHECK constraints, so we need to recreate the table

-- First, check if we need to update the constraint
PRAGMA table_info(Invoice);

-- Create a backup of the existing Invoice table
CREATE TABLE IF NOT EXISTS Invoice_backup AS SELECT * FROM Invoice;

-- Drop the existing Invoice table
DROP TABLE IF EXISTS Invoice;

-- Recreate the Invoice table with updated Type constraint
CREATE TABLE Invoice (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT NOT NULL UNIQUE,
    Type TEXT NOT NULL CHECK (Type IN ('Sales', 'Purchase', 'Stock Reservation')),
    IssueDate TEXT NOT NULL,
    DueDate TEXT NOT NULL,
    CustomerId INTEGER,
    SupplierId INTEGER,
    Subtotal REAL NOT NULL DEFAULT 0,
    DiscountAmount REAL NOT NULL DEFAULT 0,
    TaxAmount REAL NOT NULL DEFAULT 0,
    GrandTotal REAL NOT NULL DEFAULT 0,
    Status TEXT NOT NULL CHECK (Status IN ('Draft', 'Issued', 'Paid', 'Overdue', 'Cancelled')),
    PaymentTerms TEXT DEFAULT 'Net 30',
    Reference TEXT,
    Notes TEXT,
    
    -- Two-tier invoice system fields
    CreatedByUserId INTEGER NOT NULL,
    CompletedByUserId INTEGER,
    DraftCreatedAt TEXT NOT NULL,
    AdminCompletedAt TEXT,
    RequiresAdminCompletion INTEGER NOT NULL DEFAULT 0,
    
    -- Timestamps
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    UpdatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    
    -- Foreign key constraints
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
    FOREIGN KEY (CreatedByUserId) REFERENCES Users(Id),
    FOREIGN KEY (CompletedByUserId) REFERENCES Users(Id)
);

-- Restore data from backup
INSERT INTO Invoice (
    Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, SupplierId,
    Subtotal, DiscountAmount, TaxAmount, GrandTotal, Status, PaymentTerms,
    Reference, Notes, CreatedByUserId, CompletedByUserId, DraftCreatedAt,
    AdminCompletedAt, RequiresAdminCompletion, CreatedAt, UpdatedAt
)
SELECT 
    Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, SupplierId,
    Subtotal, DiscountAmount, TaxAmount, GrandTotal, Status, PaymentTerms,
    Reference, Notes, CreatedByUserId, CompletedByUserId, DraftCreatedAt,
    AdminCompletedAt, RequiresAdminCompletion, CreatedAt, UpdatedAt
FROM Invoice_backup;

-- Drop the backup table
DROP TABLE Invoice_backup;

-- Recreate indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_invoice_status ON Invoice(Status);
CREATE INDEX IF NOT EXISTS idx_invoice_requires_admin ON Invoice(RequiresAdminCompletion);
CREATE INDEX IF NOT EXISTS idx_invoice_created_by ON Invoice(CreatedByUserId);
CREATE INDEX IF NOT EXISTS idx_invoice_completed_by ON Invoice(CompletedByUserId);
CREATE INDEX IF NOT EXISTS idx_invoice_draft_created ON Invoice(DraftCreatedAt);
CREATE INDEX IF NOT EXISTS idx_invoice_type_status ON Invoice(Type, Status);

-- Recreate triggers for automatic timestamp updates
CREATE TRIGGER IF NOT EXISTS update_invoice_timestamp 
    AFTER UPDATE ON Invoice
    FOR EACH ROW
BEGIN
    UPDATE Invoice SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
END;

-- Verify the update
SELECT 'Invoice table updated successfully' as Status;
SELECT COUNT(*) as RecordCount FROM Invoice;

-- Enable foreign keys
PRAGMA foreign_keys = ON;
