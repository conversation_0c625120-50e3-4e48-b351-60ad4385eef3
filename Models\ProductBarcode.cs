using System;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class ProductBarcode
    {
        public int Id { get; set; }
        
        [Required]
        public string Barcode { get; set; }
        
        public int ProductId { get; set; }
        public virtual Product Product { get; set; }
        
        public bool IsPrimary { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; }
    }
} 