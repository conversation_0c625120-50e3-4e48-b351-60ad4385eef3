# POS System Performance Test Data Generator

This set of scripts generates a large volume of test data for performance testing the POS System application. Unlike the basic test data generators, these scripts create thousands of records to stress test the application's performance under load.

## Purpose

The performance test data is designed to:

1. Identify performance bottlenecks in the application under heavy load
2. Test database indexing effectiveness with large datasets
3. Verify that UI components can handle large amounts of data
4. Test the application's memory usage and resource consumption
5. Validate search and filtering capabilities with large datasets

## What Gets Generated

The performance test data includes:

- **60 days of business activity** - Cash drawers for the last 60 days
- **Hundreds of sales per day** - Varying by day of week (more on weekends, fewer on Mondays)
- **Multiple sale items per sale** - Random number of items per sale (1-10 items)
- **Realistic transaction patterns** - More frequent grocery purchases, less frequent electronics
- **Appropriate discounts** - About 20% of sales have a discount applied
- **Cash, card, and mobile payments** - Proper distribution of payment methods
- **Loyalty activity** - Points earned for purchases and occasional redemptions
- **Cash drawer activity** - Opening/closing balances with occasional variances
- **User favorites** - Random product favorites for each user

## Scripts

### Performance_Test_Data.sql
The main SQL script that generates all the test data. It uses temporary tables and custom functions to create large volumes of realistic data.

### Run_Performance_Test.bat (Windows)
Batch script for Windows users to run the performance test data generator with warnings and verification.

### run_performance_test.sh (Linux/macOS)
Shell script for Linux and macOS users that performs the same functions as the Windows batch script.

## Important Warnings

1. **Large Database Size**: This will significantly increase your database size, potentially to hundreds of megabytes.
2. **Resource Intensive**: The data generation process is resource-intensive and may take several minutes to complete.
3. **Application Performance**: The application may run slower with this volume of test data, which is intentional for stress testing.
4. **Backup First**: The scripts automatically create a backup, but you should ensure you have a separate backup of your database.

## Using the Performance Test Data

After generating the performance test data, you should:

1. Test the application's UI responsiveness with large datasets
2. Verify that search and filtering functions work efficiently
3. Test reporting features with large datasets
4. Analyze any slowdowns or bottlenecks that appear
5. Check memory usage during extended operations

## Restoring Original Data

If you want to return to your original dataset after performance testing:

1. Close any applications using the database
2. Delete the current POSSystem_Test.db
3. Rename POSSystem_Test_backup_before_perf.db to POSSystem_Test.db

This will restore your database to its state before performance testing.

## Technical Details

The data generation script:

1. Temporarily drops problematic triggers that cause issues
2. Uses SQLite performance optimizations (PRAGMA settings)
3. Creates temporary tables to stage the data before final insertion
4. Uses recursive queries to generate date ranges
5. Employs weighted random selections for product types
6. Creates realistic sales patterns based on day of week
7. Generates proper relationships between all entities

This approach ensures that the test data is both high-volume and realistic enough to properly test the application's performance. 