using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace POSLicensingSystem.Utilities
{
    /// <summary>
    /// Diagnostic utilities to help troubleshoot license issues
    /// </summary>
    public static class DiagnosticTools
    {
        /// <summary>
        /// Compares the encoding format of hardware IDs
        /// </summary>
        /// <param name="hardwareId">The hardware ID to analyze</param>
        /// <returns>A diagnostic report about the hardware ID format</returns>
        public static string AnalyzeHardwareId(string hardwareId)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("=== HARDWARE ID ANALYSIS ===");
            sb.AppendLine($"Hardware ID: {hardwareId}");
            sb.AppendLine($"Length: {hardwareId.Length} characters");
            
            // Check if it's Base64
            bool isBase64 = IsValidBase64String(hardwareId);
            sb.AppendLine($"Is valid Base64: {isBase64}");
            
            // Try to decode if it's Base64
            if (isBase64)
            {
                try
                {
                    byte[] decoded = Convert.FromBase64String(hardwareId);
                    string decodedText = Encoding.UTF8.GetString(decoded);
                    sb.AppendLine($"Decoded content: {decodedText}");
                    
                    // Check if it's a JSON structure
                    if (decodedText.StartsWith("{") && decodedText.EndsWith("}"))
                    {
                        sb.AppendLine("Appears to be JSON structure");
                    }
                    
                    // Check if it's a pipe-delimited format
                    else if (decodedText.Contains("|"))
                    {
                        sb.AppendLine("Appears to be pipe-delimited data:");
                        string[] parts = decodedText.Split('|');
                        for (int i = 0; i < parts.Length; i++)
                        {
                            sb.AppendLine($"  Part {i+1}: {parts[i]}");
                        }
                        
                        // Check for component format (Name:Value)
                        bool hasComponents = false;
                        foreach (var part in parts)
                        {
                            if (part.Contains(':'))
                            {
                                hasComponents = true;
                                break;
                            }
                        }
                        
                        if (hasComponents)
                        {
                            sb.AppendLine("Components found:");
                            foreach (var part in parts)
                            {
                                if (part.Contains(':'))
                                {
                                    string[] componentParts = part.Split(':');
                                    if (componentParts.Length == 2)
                                    {
                                        sb.AppendLine($"  {componentParts[0]}: {componentParts[1]}");
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    sb.AppendLine($"Error decoding Base64: {ex.Message}");
                }
            }
            
            // Check if it looks like a standard GUID
            if (Regex.IsMatch(hardwareId, @"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"))
            {
                sb.AppendLine("Format appears to be a standard GUID");
            }
            
            // Compare with current system ID from HardwareInfo
            string currentSystemId = HardwareInfo.GetSystemId();
            sb.AppendLine($"\nCurrent system ID: {currentSystemId}");
            sb.AppendLine($"Length: {currentSystemId.Length} characters");
            sb.AppendLine($"Matches input ID: {hardwareId == currentSystemId}");
            
            // Get raw components before hashing
            sb.AppendLine("\nRaw Hardware Components:");
            sb.AppendLine(GetRawHardwareComponents());
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Analyzes a license key to determine its format and potential issues
        /// </summary>
        /// <param name="licenseKey">The license key to analyze</param>
        /// <returns>A diagnostic report about the license key format</returns>
        public static string AnalyzeLicenseKey(string licenseKey)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("=== LICENSE KEY ANALYSIS ===");
            sb.AppendLine($"License Key: {licenseKey}");
            sb.AppendLine($"Length: {licenseKey.Length} characters");
            
            // Remove dashes for consistent analysis
            string cleanedKey = Regex.Replace(licenseKey, "[-\\s]", "");
            sb.AppendLine($"Cleaned Key Length: {cleanedKey.Length} characters");
            
            // Check if it's all hex
            bool isHex = Regex.IsMatch(cleanedKey, "^[0-9A-Fa-f]+$");
            sb.AppendLine($"Is Hex Format: {isHex}");
            
            // Check for Base64 characters
            bool hasBase64Chars = Regex.IsMatch(cleanedKey, "^[A-Za-z0-9+/=]+$");
            sb.AppendLine($"Has Base64 Characters: {hasBase64Chars}");
            
            // Look for known markers in POS System
            const string BASE64_MARKER = "##POS2024##";
            const string HEX_MARKER = "504F5353797374656D32303234"; // Hex for "POSSystem2024"
            
            bool hasBase64Marker = cleanedKey.Contains(BASE64_MARKER);
            bool hasHexMarker = cleanedKey.Contains(HEX_MARKER, StringComparison.OrdinalIgnoreCase);
            
            sb.AppendLine($"Contains Base64 Marker (##POS2024##): {hasBase64Marker}");
            sb.AppendLine($"Contains Hex Marker (POSSystem2024): {hasHexMarker}");
            
            // Check if the license key might contain a split marker
            bool potentialSplitMarker = cleanedKey.Contains("##P") && cleanedKey.Contains("4##");
            sb.AppendLine($"May have split marker: {potentialSplitMarker}");
            
            if (potentialSplitMarker)
            {
                Match match = Regex.Match(cleanedKey, "##P(.+?)4##");
                if (match.Success)
                {
                    string middlePart = match.Groups[1].Value;
                    sb.AppendLine($"Split marker content: ##P{middlePart}4##");
                    sb.AppendLine($"If this content is 'OS202', it indicates a split marker");
                }
            }
            
            // Analyze potential Base64 parts
            if (hasBase64Marker)
            {
                int markerIndex = cleanedKey.IndexOf(BASE64_MARKER);
                string base64Part = cleanedKey.Substring(0, markerIndex);
                string signaturePart = cleanedKey.Substring(markerIndex + BASE64_MARKER.Length);
                
                sb.AppendLine($"\nBase64 Data Part:");
                sb.AppendLine($"  Length: {base64Part.Length} characters");
                sb.AppendLine($"  Valid Base64: {IsValidBase64String(base64Part)}");
                
                sb.AppendLine($"\nSignature Part:");
                sb.AppendLine($"  Length: {signaturePart.Length} characters");
                
                // Try to decode the Base64 part
                if (IsValidBase64String(base64Part))
                {
                    try
                    {
                        byte[] jsonBytes = Convert.FromBase64String(base64Part);
                        string jsonString = Encoding.UTF8.GetString(jsonBytes);
                        
                        sb.AppendLine($"\nDecoded JSON:");
                        sb.AppendLine(jsonString);
                        
                        // Check for expected license properties
                        bool hasBusinessName = jsonString.Contains("BusinessName");
                        bool hasHardwareId = jsonString.Contains("HardwareId");
                        bool hasExpiration = jsonString.Contains("ExpirationDate");
                        
                        sb.AppendLine($"\nContains BusinessName: {hasBusinessName}");
                        sb.AppendLine($"Contains HardwareId: {hasHardwareId}");
                        sb.AppendLine($"Contains ExpirationDate: {hasExpiration}");
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"\nError decoding Base64 part: {ex.Message}");
                    }
                }
            }
            
            // Analyze potential Hex format (used by POSSystem)
            if (isHex)
            {
                sb.AppendLine("\nAnalyzing as Hex format (POSSystem format):");
                
                // Check if it's long enough to have data + hash
                if (cleanedKey.Length <= 64) // 32 bytes hash = 64 hex chars
                {
                    sb.AppendLine("Key is too short to contain both data and SHA256 hash");
                }
                else
                {
                    // Split into data and hash parts
                    string dataHex = cleanedKey.Substring(0, cleanedKey.Length - 64);
                    string hashHex = cleanedKey.Substring(cleanedKey.Length - 64);
                    
                    sb.AppendLine($"Data part length: {dataHex.Length} characters ({dataHex.Length/2} bytes)");
                    sb.AppendLine($"Hash part length: {hashHex.Length} characters ({hashHex.Length/2} bytes)");
                    
                    // Try to decode the data part
                    try
                    {
                        byte[] dataBytes = new byte[dataHex.Length / 2];
                        for (int i = 0; i < dataBytes.Length; i++)
                        {
                            dataBytes[i] = Convert.ToByte(dataHex.Substring(i * 2, 2), 16);
                        }
                        
                        string decodedData = Encoding.UTF8.GetString(dataBytes);
                        
                        // Check if it ends with KEY_DERIVATION_SALT (POSSystem2024)
                        if (decodedData.EndsWith("POSSystem2024"))
                        {
                            sb.AppendLine("Data ends with 'POSSystem2024' salt as expected in POSSystem format");
                            
                            // Remove the salt
                            string jsonData = decodedData.Substring(0, decodedData.Length - "POSSystem2024".Length);
                            sb.AppendLine("\nJSON data (without salt):");
                            sb.AppendLine(jsonData);
                            
                            // Check for expected license properties
                            bool hasBusinessName = jsonData.Contains("BusinessName");
                            bool hasHardwareId = jsonData.Contains("HardwareId");
                            bool hasExpiration = jsonData.Contains("ExpirationDate");
                            
                            sb.AppendLine($"\nContains BusinessName: {hasBusinessName}");
                            sb.AppendLine($"Contains HardwareId: {hasHardwareId}");
                            sb.AppendLine($"Contains ExpirationDate: {hasExpiration}");
                        }
                        else
                        {
                            sb.AppendLine("\nDecoded data (may not be valid):");
                            sb.AppendLine(decodedData);
                        }
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"\nError decoding hex data: {ex.Message}");
                    }
                }
            }
            
            sb.AppendLine("\n=== LICENSE KEY ANALYSIS COMPLETE ===");
            return sb.ToString();
        }
        
        /// <summary>
        /// Checks if a string is valid Base64
        /// </summary>
        private static bool IsValidBase64String(string base64)
        {
            if (string.IsNullOrEmpty(base64))
                return false;
                
            // Check for valid padding
            if (base64.Length % 4 != 0 && !base64.EndsWith("="))
                return false;
                
            // Check for valid characters
            return Regex.IsMatch(base64, "^[A-Za-z0-9+/]*={0,3}$");
        }
        
        /// <summary>
        /// Gets raw hardware component information
        /// </summary>
        private static string GetRawHardwareComponents()
        {
            var sb = new StringBuilder();
            
            // Use reflection to access private methods in HardwareInfo
            var type = typeof(HardwareInfo);
            
            try
            {
                // Get processor ID
                var processorMethod = type.GetMethod("GetProcessorId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (processorMethod != null)
                {
                    var processorId = processorMethod.Invoke(null, null) as string;
                    sb.AppendLine($"Processor ID: {processorId}");
                }
                
                // Get motherboard info
                var motherboardMethod = type.GetMethod("GetMotherboardInfo", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (motherboardMethod != null)
                {
                    var motherboardInfo = motherboardMethod.Invoke(null, null) as string;
                    sb.AppendLine($"Motherboard Info: {motherboardInfo}");
                }
                
                // Get BIOS info
                var biosMethod = type.GetMethod("GetBiosInfo", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (biosMethod != null)
                {
                    var biosInfo = biosMethod.Invoke(null, null) as string;
                    sb.AppendLine($"BIOS Info: {biosInfo}");
                }
                
                // Get disk drive info
                var diskMethod = type.GetMethod("GetDiskDriveInfo", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (diskMethod != null)
                {
                    var diskInfo = diskMethod.Invoke(null, null) as string;
                    sb.AppendLine($"Disk Info: {diskInfo}");
                }
                
                // Get MAC address
                var macMethod = type.GetMethod("GetMacAddress", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (macMethod != null)
                {
                    var macAddress = macMethod.Invoke(null, null) as string;
                    sb.AppendLine($"MAC Address: {macAddress}");
                }
                
                // Get OS info
                var osMethod = type.GetMethod("GetOsInfo", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (osMethod != null)
                {
                    var osInfo = osMethod.Invoke(null, null) as string;
                    sb.AppendLine($"OS Info: {osInfo}");
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine($"Error getting raw components: {ex.Message}");
            }
            
            return sb.ToString();
        }
    }
} 