using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace POSSystem.Services.UI
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: Advanced virtualization service for handling large collections with optimal performance
    /// </summary>
    public class AdvancedVirtualizationService<T> : IDisposable where T : class
    {
        private readonly ObservableCollection<T> _virtualizedCollection;
        private readonly List<T> _sourceData;
        private readonly int _pageSize;
        private readonly int _bufferSize;
        private readonly DispatcherTimer _loadTimer;
        private readonly object _lockObject = new object();
        
        private int _currentStartIndex;
        private int _currentEndIndex;
        private bool _isLoading;
        private bool _disposed;
        
        // Performance tracking
        private readonly Stopwatch _performanceStopwatch = new Stopwatch();
        private int _totalLoadOperations;
        private long _totalLoadTimeMs;

        public AdvancedVirtualizationService(int pageSize = 50, int bufferSize = 20)
        {
            _pageSize = pageSize;
            _bufferSize = bufferSize;
            _virtualizedCollection = new ObservableCollection<T>();
            _sourceData = new List<T>();
            
            // Timer for delayed loading to prevent excessive updates
            _loadTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _loadTimer.Tick += LoadTimer_Tick;

            Debug.WriteLine($"[VIRTUALIZATION] Advanced virtualization service initialized (PageSize: {_pageSize}, Buffer: {_bufferSize})");
        }

        /// <summary>
        /// ✅ CRITICAL: Get the virtualized collection for UI binding
        /// </summary>
        public ObservableCollection<T> VirtualizedCollection => _virtualizedCollection;

        /// <summary>
        /// ✅ CRITICAL: Set the source data for virtualization
        /// </summary>
        public async Task SetSourceDataAsync(IEnumerable<T> sourceData)
        {
            if (_disposed) return;

            _performanceStopwatch.Restart();

            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _sourceData.Clear();
                    _sourceData.AddRange(sourceData);
                    _currentStartIndex = 0;
                    _currentEndIndex = 0;
                }
            });

            // Load initial page on UI thread
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                LoadInitialPage();
            }, DispatcherPriority.DataBind);

            _performanceStopwatch.Stop();
            _totalLoadOperations++;
            _totalLoadTimeMs += _performanceStopwatch.ElapsedMilliseconds;

            Debug.WriteLine($"[VIRTUALIZATION] Source data set: {_sourceData.Count} items, initial load: {_performanceStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ CRITICAL: Load items for a specific range (called during scrolling)
        /// </summary>
        public void LoadRange(int startIndex, int count)
        {
            if (_disposed || _isLoading) return;

            // Normalize the range
            startIndex = Math.Max(0, startIndex);
            var endIndex = Math.Min(_sourceData.Count - 1, startIndex + count - 1);

            // Check if we need to load new data
            var needsUpdate = startIndex < _currentStartIndex - _bufferSize || 
                             endIndex > _currentEndIndex + _bufferSize ||
                             _virtualizedCollection.Count == 0;

            if (needsUpdate)
            {
                // Use timer to debounce rapid scroll events
                _loadTimer.Stop();
                _loadTimer.Tag = new LoadRequest { StartIndex = startIndex, Count = count };
                _loadTimer.Start();
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Timer tick handler for delayed loading
        /// </summary>
        private void LoadTimer_Tick(object sender, EventArgs e)
        {
            _loadTimer.Stop();
            
            if (_loadTimer.Tag is LoadRequest request)
            {
                _ = LoadRangeAsync(request.StartIndex, request.Count);
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Async range loading with performance optimization
        /// </summary>
        private async Task LoadRangeAsync(int startIndex, int count)
        {
            if (_disposed || _isLoading) return;

            _isLoading = true;
            _performanceStopwatch.Restart();

            try
            {
                List<T> itemsToLoad = null;
                int actualStartIndex = 0, actualEndIndex = 0;

                // Calculate optimal range with buffering
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        // Add buffer to reduce frequent reloads
                        actualStartIndex = Math.Max(0, startIndex - _bufferSize);
                        actualEndIndex = Math.Min(_sourceData.Count - 1, startIndex + count + _bufferSize - 1);

                        var itemCount = actualEndIndex - actualStartIndex + 1;
                        itemsToLoad = _sourceData.Skip(actualStartIndex).Take(itemCount).ToList();
                    }
                });

                // Update UI collection on UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UpdateVirtualizedCollection(itemsToLoad, actualStartIndex, actualEndIndex);
                }, DispatcherPriority.DataBind);

                _performanceStopwatch.Stop();
                _totalLoadOperations++;
                _totalLoadTimeMs += _performanceStopwatch.ElapsedMilliseconds;

                Debug.WriteLine($"[VIRTUALIZATION] Range loaded: {actualStartIndex}-{actualEndIndex} ({itemsToLoad.Count} items) in {_performanceStopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VIRTUALIZATION] Error loading range: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Update the virtualized collection efficiently
        /// </summary>
        private void UpdateVirtualizedCollection(List<T> newItems, int startIndex, int endIndex)
        {
            try
            {
                // Clear and replace for optimal performance
                _virtualizedCollection.Clear();
                
                // Add items in batches to prevent UI freezing
                const int batchSize = 25;
                for (int i = 0; i < newItems.Count; i += batchSize)
                {
                    var batch = newItems.Skip(i).Take(batchSize);
                    foreach (var item in batch)
                    {
                        _virtualizedCollection.Add(item);
                    }

                    // Yield control to UI thread periodically
                    if (i % (batchSize * 2) == 0 && i > 0)
                    {
                        Application.Current?.Dispatcher.Invoke(() => { }, DispatcherPriority.Background);
                    }
                }

                _currentStartIndex = startIndex;
                _currentEndIndex = endIndex;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [VIRTUALIZATION] Error updating collection: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Load initial page of data
        /// </summary>
        private void LoadInitialPage()
        {
            if (_disposed || _sourceData.Count == 0) return;

            var initialCount = Math.Min(_pageSize, _sourceData.Count);
            var initialItems = _sourceData.Take(initialCount).ToList();
            
            UpdateVirtualizedCollection(initialItems, 0, initialCount - 1);
        }

        /// <summary>
        /// ✅ MONITORING: Get performance statistics
        /// </summary>
        public VirtualizationPerformanceStats GetPerformanceStats()
        {
            lock (_lockObject)
            {
                return new VirtualizationPerformanceStats
                {
                    TotalSourceItems = _sourceData.Count,
                    VirtualizedItems = _virtualizedCollection.Count,
                    CurrentStartIndex = _currentStartIndex,
                    CurrentEndIndex = _currentEndIndex,
                    TotalLoadOperations = _totalLoadOperations,
                    AverageLoadTimeMs = _totalLoadOperations > 0 ? (double)_totalLoadTimeMs / _totalLoadOperations : 0,
                    IsLoading = _isLoading,
                    PageSize = _pageSize,
                    BufferSize = _bufferSize
                };
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Add item to source data
        /// </summary>
        public async Task AddItemAsync(T item)
        {
            if (_disposed || item == null) return;

            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _sourceData.Add(item);
                }
            });

            // If the new item is in the current visible range, add it to the virtualized collection
            if (_sourceData.Count - 1 <= _currentEndIndex + _bufferSize)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _virtualizedCollection.Add(item);
                    _currentEndIndex = _sourceData.Count - 1;
                }, DispatcherPriority.DataBind);
            }
        }

        /// <summary>
        /// ✅ PUBLIC API: Remove item from source data
        /// </summary>
        public async Task RemoveItemAsync(T item)
        {
            if (_disposed || item == null) return;

            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _sourceData.Remove(item);
                }
            });

            // Remove from virtualized collection if present
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _virtualizedCollection.Remove(item);
            }, DispatcherPriority.DataBind);
        }

        /// <summary>
        /// ✅ PUBLIC API: Clear all data
        /// </summary>
        public async Task ClearAsync()
        {
            if (_disposed) return;

            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _sourceData.Clear();
                    _currentStartIndex = 0;
                    _currentEndIndex = 0;
                }
            });

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _virtualizedCollection.Clear();
            }, DispatcherPriority.DataBind);
        }

        public void Dispose()
        {
            if (_disposed) return;

            _loadTimer?.Stop();
            if (_loadTimer != null)
            {
                _loadTimer.Tick -= LoadTimer_Tick;
            }

            lock (_lockObject)
            {
                _sourceData.Clear();
                _virtualizedCollection.Clear();
            }

            Debug.WriteLine($"[VIRTUALIZATION] Service disposed. Total operations: {_totalLoadOperations}, Avg time: {(_totalLoadOperations > 0 ? _totalLoadTimeMs / _totalLoadOperations : 0)}ms");
            _disposed = true;
        }

        /// <summary>
        /// Internal class for load requests
        /// </summary>
        private class LoadRequest
        {
            public int StartIndex { get; set; }
            public int Count { get; set; }
        }
    }

    /// <summary>
    /// Performance statistics for virtualization
    /// </summary>
    public class VirtualizationPerformanceStats
    {
        public int TotalSourceItems { get; set; }
        public int VirtualizedItems { get; set; }
        public int CurrentStartIndex { get; set; }
        public int CurrentEndIndex { get; set; }
        public int TotalLoadOperations { get; set; }
        public double AverageLoadTimeMs { get; set; }
        public bool IsLoading { get; set; }
        public int PageSize { get; set; }
        public int BufferSize { get; set; }
    }
}
