using System;
using System.Threading.Tasks;
using POSSystem.Tests.DependencyInjection;

namespace POSSystem.Tests.Integration
{
    /// <summary>
    /// Runner for DI validation test to verify dependency injection configuration.
    /// </summary>
    public class DIValidationRunner
    {
        /// <summary>
        /// Runs the complete DI validation test and reports results.
        /// </summary>
        public static async Task RunDIValidationAsync()
        {
            try
            {
                Console.WriteLine("Starting Dependency Injection Validation...");
                Console.WriteLine();

                var result = await DIValidationTest.ValidateDIConfigurationAsync();

                Console.WriteLine();
                Console.WriteLine("=== DI Validation Complete ===");
                
                if (result.IsValid)
                {
                    Console.WriteLine("🎉 SUCCESS: Dependency Injection configuration is valid!");
                    Console.WriteLine();
                    Console.WriteLine("✅ All core services resolve correctly");
                    Console.WriteLine("✅ All interfaces properly implemented");
                    Console.WriteLine("✅ Service lifetimes working as expected");
                    Console.WriteLine("✅ No circular dependencies detected");
                    Console.WriteLine("✅ ViewModels properly registered");
                }
                else
                {
                    Console.WriteLine("⚠️ ISSUES FOUND: DI configuration has some issues");
                    Console.WriteLine();
                    
                    if (result.FailedServices.Count > 0)
                    {
                        Console.WriteLine($"❌ Failed Services ({result.FailedServices.Count}):");
                        foreach (var service in result.FailedServices)
                        {
                            Console.WriteLine($"   • {service}");
                        }
                    }
                    
                    if (result.CircularDependencies.Count > 0)
                    {
                        Console.WriteLine($"❌ Circular Dependencies ({result.CircularDependencies.Count}):");
                        foreach (var dependency in result.CircularDependencies)
                        {
                            Console.WriteLine($"   • {dependency}");
                        }
                    }
                }

                // Show improvement metrics
                Console.WriteLine();
                Console.WriteLine("=== DI Health Metrics ===");
                Console.WriteLine($"Resolved Services: {result.ResolvedServices.Count}");
                Console.WriteLine($"Resolved ViewModels: {result.ResolvedViewModels.Count}");
                Console.WriteLine($"Valid Interfaces: {result.ValidInterfaces.Count}");
                Console.WriteLine($"Correct Lifetimes: {result.CorrectLifetimes.Count}");
                Console.WriteLine($"Service Locator Issues: {result.ServiceLocatorIssues}");
                
                // Show top recommendations
                if (result.DIRecommendations.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("📋 Top Recommendations:");
                    for (int i = 0; i < Math.Min(3, result.DIRecommendations.Count); i++)
                    {
                        Console.WriteLine($"   {i + 1}. {result.DIRecommendations[i]}");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("DI Validation completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ DI Validation failed with error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
