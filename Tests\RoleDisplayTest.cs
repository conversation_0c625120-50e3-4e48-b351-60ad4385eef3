using System;
using System.Linq;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test utility to debug the DisplayRoleName property issue
    /// </summary>
    public class RoleDisplayTest
    {
        private readonly DatabaseService _dbService;

        public RoleDisplayTest()
        {
            _dbService = new DatabaseService();
        }

        /// <summary>
        /// Tests the DisplayRoleName property for all users in the database
        /// </summary>
        public void TestAllUsersDisplayRole()
        {
            Console.WriteLine("=== ROLE DISPLAY TEST ===");
            Console.WriteLine();

            try
            {
                var users = _dbService.GetAllUsers();
                Console.WriteLine($"Found {users.Count} users in database:");
                Console.WriteLine();

                foreach (var user in users)
                {
                    Console.WriteLine($"--- User: {user.Username} (ID: {user.Id}) ---");
                    Console.WriteLine($"Database Role: {user.UserRole?.Name ?? "NULL"}");
                    Console.WriteLine($"Role ID: {user.RoleId}");
                    
                    // Check for custom permissions
                    var customPermissions = _dbService.GetUserPermissions(user.Id);
                    if (customPermissions != null)
                    {
                        Console.WriteLine("Custom Permissions Found:");
                        Console.WriteLine($"  - CanManageUsers: {customPermissions.CanManageUsers}");
                        Console.WriteLine($"  - CanAccessSettings: {customPermissions.CanAccessSettings}");
                        Console.WriteLine($"  - CanManageProducts: {customPermissions.CanManageProducts}");
                        Console.WriteLine($"  - CanViewReports: {customPermissions.CanViewReports}");
                        
                        // Get role defaults for comparison
                        var permissionsService = new UserPermissionsService(_dbService);
                        var roleDefaults = permissionsService.CreateDefaultPermissions(0, user.RoleId);
                        
                        Console.WriteLine("Role Defaults:");
                        Console.WriteLine($"  - CanManageUsers: {roleDefaults.CanManageUsers}");
                        Console.WriteLine($"  - CanAccessSettings: {roleDefaults.CanAccessSettings}");
                        Console.WriteLine($"  - CanManageProducts: {roleDefaults.CanManageProducts}");
                        Console.WriteLine($"  - CanViewReports: {roleDefaults.CanViewReports}");
                        
                        // Check if they match
                        bool permissionsMatch = 
                            customPermissions.CanManageUsers == roleDefaults.CanManageUsers &&
                            customPermissions.CanAccessSettings == roleDefaults.CanAccessSettings &&
                            customPermissions.CanManageProducts == roleDefaults.CanManageProducts &&
                            customPermissions.CanViewReports == roleDefaults.CanViewReports;
                            
                        Console.WriteLine($"Permissions Match Role Defaults: {permissionsMatch}");
                    }
                    else
                    {
                        Console.WriteLine("No Custom Permissions Found");
                    }
                    
                    // Test DisplayRoleName property
                    try
                    {
                        string displayRole = user.DisplayRoleName;
                        Console.WriteLine($"DisplayRoleName Result: '{displayRole}'");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DisplayRoleName Error: {ex.Message}");
                    }
                    
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Creates a test user with custom permissions to verify the DisplayRoleName logic
        /// </summary>
        public void CreateTestUserWithCustomPermissions()
        {
            Console.WriteLine("=== CREATING TEST USER WITH CUSTOM PERMISSIONS ===");
            Console.WriteLine();

            try
            {
                // Get Admin role
                var roles = _dbService.GetAllRoles();
                var adminRole = roles.FirstOrDefault(r => r.Name == "Admin");
                if (adminRole == null)
                {
                    Console.WriteLine("ERROR: Admin role not found!");
                    return;
                }

                // Create test user
                var testUser = new User
                {
                    Username = "displayroletest",
                    Password = "test123", // This should be hashed in real implementation
                    FirstName = "Display",
                    LastName = "Test",
                    Email = "<EMAIL>",
                    RoleId = adminRole.Id,
                    UserRole = adminRole,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                // Save user
                int userId = _dbService.CreateUser(testUser);
                Console.WriteLine($"Created test user with ID: {userId}");

                // Create custom permissions (different from Admin defaults)
                var customPermissions = new UserPermissions
                {
                    UserId = userId,
                    CanCreateSales = true,
                    CanVoidSales = false,  // Admin default would be true
                    CanApplyDiscount = true,
                    CanViewSalesHistory = true,
                    CanManageProducts = true,
                    CanManageCategories = false,  // Admin default would be true
                    CanViewInventory = true,
                    CanAdjustInventory = true,
                    CanManageExpenses = true,
                    CanManageCashDrawer = true,
                    CanViewReports = false,  // Admin default would be true
                    CanManagePrices = true,
                    CanManageCustomers = true,
                    CanManageSuppliers = true,
                    CanManageUsers = false,  // Admin default would be true
                    CanManageRoles = false,  // Admin default would be true
                    CanAccessSettings = false,  // Admin default would be true
                    CanViewLogs = false  // Admin default would be true
                };

                _dbService.SaveUserPermissions(customPermissions);
                Console.WriteLine("Saved custom permissions for test user");

                // Now test the DisplayRoleName
                var savedUser = _dbService.GetAllUsers().FirstOrDefault(u => u.Id == userId);
                if (savedUser != null)
                {
                    Console.WriteLine($"Test User Database Role: {savedUser.UserRole?.Name}");
                    Console.WriteLine($"Test User DisplayRoleName: {savedUser.DisplayRoleName}");
                    
                    if (savedUser.DisplayRoleName == "Custom")
                    {
                        Console.WriteLine("✅ SUCCESS: DisplayRoleName correctly shows 'Custom'");
                    }
                    else
                    {
                        Console.WriteLine("❌ FAILURE: DisplayRoleName should show 'Custom' but shows: " + savedUser.DisplayRoleName);
                    }
                }

                Console.WriteLine();
                Console.WriteLine("Test user created. You can now:");
                Console.WriteLine("1. Run the application");
                Console.WriteLine("2. Check the Users list to see if role shows as 'Custom'");
                Console.WriteLine("3. Login with username: displayroletest, password: test123");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to create test user: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Cleans up the test user
        /// </summary>
        public void CleanupTestUser()
        {
            try
            {
                var users = _dbService.GetAllUsers();
                var testUser = users.FirstOrDefault(u => u.Username == "displayroletest");
                if (testUser != null)
                {
                    _dbService.DeleteUser(testUser.Id);
                    Console.WriteLine("Test user deleted successfully");
                }
                else
                {
                    Console.WriteLine("Test user not found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to cleanup test user: {ex.Message}");
            }
        }
    }
}
