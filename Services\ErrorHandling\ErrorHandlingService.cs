using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.ErrorHandling
{
    /// <summary>
    /// Comprehensive error handling service that provides centralized error management,
    /// logging, user notification, and error recovery mechanisms for the POS system.
    /// </summary>
    /// <remarks>
    /// <para>This service provides enterprise-grade error handling capabilities:</para>
    /// <list type="bullet">
    /// <item><description>Centralized Error Management: Single point for all error handling logic</description></item>
    /// <item><description>User-Friendly Messages: Converts technical errors to user-friendly notifications</description></item>
    /// <item><description>Comprehensive Logging: Detailed error logging with context and stack traces</description></item>
    /// <item><description>Error Recovery: Automatic retry mechanisms and fallback strategies</description></item>
    /// <item><description>Error Statistics: Monitoring and analysis of error patterns</description></item>
    /// <item><description>Performance Impact Tracking: Monitor how errors affect system performance</description></item>
    /// </list>
    /// </remarks>
    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly ILogger<ErrorHandlingService> _logger;
        private readonly string _errorLogPath;
        private readonly Queue<ErrorLogEntry> _recentErrors;
        private readonly object _lockObject = new object();
        private Dictionary<Type, string> _userFriendlyMessages;
        private Dictionary<Type, ErrorSeverity> _errorSeverities;
        private const int MaxRecentErrors = 100;

        public ErrorHandlingService(ILogger<ErrorHandlingService> logger = null)
        {
            _logger = logger;
            _errorLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "errors.json");
            _recentErrors = new Queue<ErrorLogEntry>();

            InitializeUserFriendlyMessages();
            InitializeErrorSeverities();

            // Ensure log directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(_errorLogPath));
        }

        /// <summary>
        /// Handle exceptions with consistent logging and user notification
        /// </summary>
        public async Task<T> HandleAsync<T>(Func<Task<T>> operation, string operationName, T defaultValue = default(T), bool showUserMessage = true)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, operationName);
                
                if (showUserMessage)
                {
                    await ShowUserFriendlyErrorAsync(ex, operationName);
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// Handle synchronous operations
        /// </summary>
        public T Handle<T>(Func<T> operation, string operationName, T defaultValue = default(T), bool showUserMessage = true)
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                LogError(ex, operationName);
                
                if (showUserMessage)
                {
                    ShowUserFriendlyError(ex, operationName);
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// Handle void operations
        /// </summary>
        public bool HandleVoid(Action operation, string operationName, bool showUserMessage = true)
        {
            try
            {
                operation();
                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, operationName);
                
                if (showUserMessage)
                {
                    ShowUserFriendlyError(ex, operationName);
                }
                
                return false;
            }
        }

        /// <summary>
        /// Handle async void operations
        /// </summary>
        public async Task<bool> HandleVoidAsync(Func<Task> operation, string operationName, bool showUserMessage = true)
        {
            try
            {
                await operation();
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, operationName);
                
                if (showUserMessage)
                {
                    await ShowUserFriendlyErrorAsync(ex, operationName);
                }
                
                return false;
            }
        }

        /// <summary>
        /// Log error with structured information
        /// </summary>
        public void LogError(Exception exception, string operationName, Dictionary<string, object> additionalData = null)
        {
            var errorEntry = new ErrorLogEntry
            {
                Timestamp = DateTime.UtcNow,
                OperationName = operationName,
                ExceptionType = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                InnerException = exception.InnerException?.Message,
                AdditionalData = additionalData ?? new Dictionary<string, object>()
            };

            // Log to structured logger
            _logger?.LogError(exception, "Operation {OperationName} failed: {Message}", operationName, exception.Message);

            // Log to debug output
            Debug.WriteLine($"[ERROR] {operationName}: {exception.Message}");
            if (exception.InnerException != null)
            {
                Debug.WriteLine($"[ERROR] Inner Exception: {exception.InnerException.Message}");
            }

            // Store in recent errors queue
            lock (_lockObject)
            {
                _recentErrors.Enqueue(errorEntry);
                if (_recentErrors.Count > MaxRecentErrors)
                {
                    _recentErrors.Dequeue();
                }
            }

            // Persist to file (fire and forget)
            Task.Run(() => PersistErrorToFileAsync(errorEntry));
        }

        /// <summary>
        /// Async version of LogError
        /// </summary>
        public async Task LogErrorAsync(Exception exception, string operationName, Dictionary<string, object> additionalData = null)
        {
            LogError(exception, operationName, additionalData);
            await Task.CompletedTask;
        }

        /// <summary>
        /// Show user-friendly error message
        /// </summary>
        public void ShowUserFriendlyError(Exception exception, string operationName)
        {
            var userMessage = GetUserFriendlyMessage(exception, operationName);
            
            Application.Current?.Dispatcher?.Invoke(() =>
            {
                MessageBox.Show(userMessage, "Operation Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
            });
        }

        /// <summary>
        /// Async version of ShowUserFriendlyError
        /// </summary>
        public async Task ShowUserFriendlyErrorAsync(Exception exception, string operationName)
        {
            var userMessage = GetUserFriendlyMessage(exception, operationName);
            
            if (Application.Current?.Dispatcher != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageBox.Show(userMessage, "Operation Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
                });
            }
        }

        /// <summary>
        /// Get recent errors for debugging
        /// </summary>
        public List<ErrorLogEntry> GetRecentErrors()
        {
            lock (_lockObject)
            {
                return new List<ErrorLogEntry>(_recentErrors);
            }
        }

        /// <summary>
        /// Check if an exception is a critical system error
        /// </summary>
        public bool IsCriticalError(Exception exception)
        {
            return exception is OutOfMemoryException ||
                   exception is StackOverflowException ||
                   exception is AccessViolationException ||
                   exception is AppDomainUnloadedException;
        }

        /// <summary>
        /// Check if an exception is a transient error that might be retried
        /// </summary>
        public bool IsTransientError(Exception exception)
        {
            return exception is TimeoutException ||
                   exception is TaskCanceledException ||
                   (exception.Message?.Contains("database is locked") == true) ||
                   (exception.Message?.Contains("timeout") == true);
        }

        private string GetUserFriendlyMessage(Exception exception, string operationName)
        {
            // Map common exceptions to user-friendly messages
            return exception switch
            {
                UnauthorizedAccessException => $"You don't have permission to perform this operation: {operationName}",
                FileNotFoundException => $"A required file was not found while {operationName.ToLower()}",
                DirectoryNotFoundException => $"A required folder was not found while {operationName.ToLower()}",
                TimeoutException => $"The operation '{operationName}' took too long to complete. Please try again.",
                TaskCanceledException => $"The operation '{operationName}' was cancelled",
                ArgumentException => $"Invalid data provided for {operationName.ToLower()}",
                InvalidOperationException => $"Cannot perform {operationName.ToLower()} at this time",
                _ when exception.Message?.Contains("database") == true => $"Database error during {operationName.ToLower()}. Please try again.",
                _ when exception.Message?.Contains("network") == true => $"Network error during {operationName.ToLower()}. Please check your connection.",
                _ => $"An error occurred during {operationName.ToLower()}. Please try again or contact support if the problem persists."
            };
        }

        private async Task PersistErrorToFileAsync(ErrorLogEntry errorEntry)
        {
            try
            {
                var json = JsonSerializer.Serialize(errorEntry, new JsonSerializerOptions { WriteIndented = true });
                var logEntry = $"{json},\n";
                
                await File.AppendAllTextAsync(_errorLogPath, logEntry);
            }
            catch (Exception ex)
            {
                // Don't let logging errors crash the application
                Debug.WriteLine($"Failed to persist error log: {ex.Message}");
            }
        }

        /// <summary>
        /// Attempts to recover from an error using predefined recovery strategies.
        /// </summary>
        /// <param name="exception">The exception to recover from</param>
        /// <param name="context">Recovery context information</param>
        /// <returns>A RecoveryResult indicating the outcome</returns>
        public async Task<RecoveryResult> AttemptRecoveryAsync(Exception exception, string context = null)
        {
            try
            {
                _logger?.LogInformation("Attempting recovery for exception: {ExceptionType}", exception.GetType().Name);

                var recoveryStrategy = GetRecoveryStrategy(exception);
                if (recoveryStrategy == null)
                {
                    return new RecoveryResult
                    {
                        Success = false,
                        Message = "No recovery strategy available for this error type."
                    };
                }

                var result = await recoveryStrategy.ExecuteAsync(exception, context);

                _logger?.LogInformation("Recovery attempt completed. Success: {Success}", result.Success);
                return result;
            }
            catch (Exception recoveryException)
            {
                _logger?.LogError(recoveryException, "Error during recovery attempt");
                return new RecoveryResult
                {
                    Success = false,
                    Message = "Recovery attempt failed due to an unexpected error."
                };
            }
        }

        /// <summary>
        /// Gets error statistics for monitoring and analysis.
        /// </summary>
        /// <param name="timeRange">The time range for statistics</param>
        /// <returns>Error statistics for the specified time range</returns>
        public async Task<ErrorStatistics> GetErrorStatisticsAsync(TimeSpan timeRange)
        {
            try
            {
                var cutoffTime = DateTime.Now - timeRange;
                var recentErrors = new List<ErrorLogEntry>();

                lock (_lockObject)
                {
                    recentErrors.AddRange(_recentErrors.Where(e => e.Timestamp >= cutoffTime));
                }

                var stats = new ErrorStatistics
                {
                    TimeRange = timeRange,
                    TotalErrors = recentErrors.Count,
                    CriticalErrors = recentErrors.Count(e => GetErrorSeverity(e.ExceptionType) == ErrorSeverity.Critical),
                    HighSeverityErrors = recentErrors.Count(e => GetErrorSeverity(e.ExceptionType) == ErrorSeverity.High),
                    MediumSeverityErrors = recentErrors.Count(e => GetErrorSeverity(e.ExceptionType) == ErrorSeverity.Medium),
                    LowSeverityErrors = recentErrors.Count(e => GetErrorSeverity(e.ExceptionType) == ErrorSeverity.Low),
                    MostCommonErrors = recentErrors.GroupBy(e => e.ExceptionType)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .Select(g => $"{g.Key} ({g.Count()} occurrences)")
                        .ToList(),
                    ErrorTrends = recentErrors.GroupBy(e => e.Timestamp.Date)
                        .ToDictionary(g => g.Key, g => g.Count())
                };

                await Task.Delay(10); // Simulate async operation
                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving error statistics");
                throw;
            }
        }

        /// <summary>
        /// Gets the error severity for a specific exception type name.
        /// </summary>
        private ErrorSeverity GetErrorSeverity(string exceptionTypeName)
        {
            try
            {
                var type = Type.GetType(exceptionTypeName);
                if (type != null && _errorSeverities.TryGetValue(type, out var severity))
                {
                    return severity;
                }
            }
            catch
            {
                // Ignore type resolution errors
            }

            return ErrorSeverity.Medium; // Default severity
        }

        /// <summary>
        /// Gets a recovery strategy for the specified exception.
        /// </summary>
        private IRecoveryStrategy GetRecoveryStrategy(Exception exception)
        {
            return exception switch
            {
                TimeoutException => new RetryRecoveryStrategy(),
                IOException => new RetryRecoveryStrategy(),
                System.Data.SqlClient.SqlException => new DatabaseRecoveryStrategy(),
                _ => null
            };
        }

        /// <summary>
        /// Initializes user-friendly error messages for common exception types.
        /// </summary>
        private void InitializeUserFriendlyMessages()
        {
            _userFriendlyMessages = new Dictionary<Type, string>
            {
                { typeof(UnauthorizedAccessException), "You don't have permission to perform this action. Please contact your administrator." },
                { typeof(FileNotFoundException), "A required file could not be found. Please check your installation." },
                { typeof(DirectoryNotFoundException), "A required directory could not be found. Please check your installation." },
                { typeof(IOException), "A file operation failed. Please check that files are not in use by another program." },
                { typeof(OutOfMemoryException), "The system is running low on memory. Please close other applications and try again." },
                { typeof(StackOverflowException), "The application encountered a serious error. Please restart the application." },
                { typeof(ArgumentNullException), "Invalid data was provided. Please check your input and try again." },
                { typeof(ArgumentException), "Invalid data was provided. Please check your input and try again." },
                { typeof(InvalidOperationException), "This operation cannot be performed at this time. Please try again later." },
                { typeof(NotSupportedException), "This operation is not supported. Please try a different approach." },
                { typeof(TimeoutException), "The operation took too long to complete. Please check your connection and try again." }
            };
        }

        /// <summary>
        /// Initializes error severity mappings for different exception types.
        /// </summary>
        private void InitializeErrorSeverities()
        {
            _errorSeverities = new Dictionary<Type, ErrorSeverity>
            {
                { typeof(OutOfMemoryException), ErrorSeverity.Critical },
                { typeof(StackOverflowException), ErrorSeverity.Critical },
                { typeof(UnauthorizedAccessException), ErrorSeverity.High },
                { typeof(FileNotFoundException), ErrorSeverity.Medium },
                { typeof(DirectoryNotFoundException), ErrorSeverity.Medium },
                { typeof(IOException), ErrorSeverity.Medium },
                { typeof(TimeoutException), ErrorSeverity.Medium },
                { typeof(ArgumentNullException), ErrorSeverity.Low },
                { typeof(ArgumentException), ErrorSeverity.Low },
                { typeof(InvalidOperationException), ErrorSeverity.Medium },
                { typeof(NotSupportedException), ErrorSeverity.Low }
            };
        }
    }

    /// <summary>
    /// Error log entry structure
    /// </summary>
    public class ErrorLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string OperationName { get; set; }
        public string ExceptionType { get; set; }
        public string Message { get; set; }
        public string StackTrace { get; set; }
        public string InnerException { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; }
    }
}
