using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class DatePastDueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Console.WriteLine($"DatePastDueConverter - Input value: {value}"); // Debug log
            
            if (value == null)
            {
                Console.WriteLine("DatePastDueConverter - Value is null, returning false"); // Debug log
                return false;
            }
            
            DateTime dueDate;
            if (value is DateTime dt)
            {
                dueDate = dt;
                Console.WriteLine($"DatePastDueConverter - Direct DateTime value: {dueDate}"); // Debug log
            }
            else
            {
                // Try to get the underlying DateTime value if it's nullable
                var nullableDate = value as DateTime?;
                if (!nullableDate.HasValue)
                {
                    Console.WriteLine("DatePastDueConverter - Nullable DateTime has no value, returning false"); // Debug log
                    return false;
                }
                dueDate = nullableDate.Value;
                Console.WriteLine($"DatePastDueConverter - Nullable DateTime value: {dueDate}"); // Debug log
            }
            
            var result = dueDate.Date < DateTime.Now.Date;
            Console.WriteLine($"DatePastDueConverter - Is past due: {result}"); // Debug log
            return result;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}