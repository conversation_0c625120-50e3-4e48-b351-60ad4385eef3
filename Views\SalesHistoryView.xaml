<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.SalesHistoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <!-- DataGrid Styles - Updated for theme awareness -->
        <Style x:Key="ModernDataGrid" TargetType="DataGrid">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="RowHeight" Value="40"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="False"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="RowHeaderWidth" Value="0"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>

        <!-- Special style to hide "Loading Data" text -->
        <Style x:Key="HideLoadingTextStyle" TargetType="TextBlock">
            <Style.Triggers>
                <Trigger Property="Text" Value="Loading Data">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </Trigger>
                <Trigger Property="Text" Value="...Loading Data">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- DataGrid Column Header Style - Updated for theme awareness -->
        <Style x:Key="CustomDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignSurfaceBackground}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,12"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
        </Style>

        <!-- Modern ComboBox Style - Updated for theme awareness -->
        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,5"/>
        </Style>

        <!-- Modern DatePicker Style - Updated for theme awareness -->
        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- Modern TextBox Style - Updated for theme awareness -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,5"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- Button Styles - Updated for theme awareness -->
        <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidForegroundBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,4"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="4"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="30" Background="Transparent">
        <Grid.Resources>
            <!-- Apply the Hide Loading style to all TextBlocks in this grid -->
            <Style TargetType="TextBlock" BasedOn="{StaticResource HideLoadingTextStyle}"/>
        </Grid.Resources>
        
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" 
                  Text="{DynamicResource SalesHistory}"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Foreground="{DynamicResource MaterialDesignBody}"
                  Margin="0,0,0,20"/>

        <!-- Filters -->
        <md:Card Grid.Row="1" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                Foreground="{DynamicResource MaterialDesignBody}"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" Margin="20,15">
                <TextBlock Text="{DynamicResource Period}"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"
                          Foreground="{DynamicResource MaterialDesignBody}"
                          VerticalAlignment="Center" 
                          Margin="0,0,10,0"/>
                <ComboBox x:Name="cbPeriod" 
                         Width="120" 
                         SelectedIndex="0"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Foreground="{DynamicResource MaterialDesignBody}"
                         Background="{DynamicResource MaterialDesignPaper}"
                         SelectionChanged="PeriodFilter_Changed"
                         Margin="0,0,16,0">
                    <ComboBoxItem Content="{DynamicResource TimePeriod_Today}"/>
                    <ComboBoxItem Content="{DynamicResource TimePeriod_Yesterday}"/>
                    <ComboBoxItem Content="{DynamicResource TimePeriod_Last7Days}"/>
                    <ComboBoxItem Content="{DynamicResource TimePeriod_Last30Days}"/>
                    <ComboBoxItem Content="{DynamicResource TimePeriod_Custom}"/>
                </ComboBox>
                <DatePicker x:Name="dpStartDate" 
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Background="{DynamicResource MaterialDesignPaper}"
                           SelectedDateChanged="DateFilter_Changed"
                           Visibility="{Binding IsCustomPeriod, 
                                      Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,0,16,0"/>
                <DatePicker x:Name="dpEndDate"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Background="{DynamicResource MaterialDesignPaper}"
                           SelectedDateChanged="DateFilter_Changed"
                           Visibility="{Binding IsCustomPeriod, 
                                      Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,0,16,0"/>
                <TextBox x:Name="txtSearch" 
                        Width="250"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Background="{DynamicResource MaterialDesignPaper}"
                        md:HintAssist.Hint="{DynamicResource SalesHistorySearch}"
                        TextChanged="Search_TextChanged"/>
                <Button x:Name="btnRefresh"
                        Margin="16,0,0,0"
                        Click="RefreshData_Click"
                        ToolTip="{DynamicResource RefreshData}"
                        Style="{StaticResource MaterialDesignIconButton}">
                    <md:PackIcon Kind="Refresh" />
                </Button>
            </StackPanel>
        </md:Card>

        <!-- Sales List -->
        <md:Card Grid.Row="2" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                Foreground="{DynamicResource MaterialDesignBody}"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1">
            <DockPanel Margin="20">
                <Grid DockPanel.Dock="Top" Margin="0,0,0,15">
                    <TextBlock Text="{DynamicResource SalesList}"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBody}"/>
                    <TextBlock Text="{Binding StatusMessage}"
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Center"/>
                </Grid>

                <Grid>

                    <DataGrid x:Name="SalesDataGrid"
                            ItemsSource="{Binding Sales}"
                            Style="{StaticResource MaterialDesignDataGrid}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            BorderThickness="0"
                            Background="Transparent"
                            RowBackground="{DynamicResource MaterialDesignPaper}"
                            AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                            Foreground="{DynamicResource MaterialDesignBody}"
                            SelectionChanged="Sale_Selected"
                            EnableRowVirtualization="True"
                            VirtualizingStackPanel.VirtualizationMode="Recycling"
                            VirtualizingStackPanel.IsVirtualizing="True"
                            ScrollViewer.IsDeferredScrollingEnabled="True"
                            ScrollViewer.ScrollChanged="DataGrid_ScrollChanged"
                            md:DataGridAssist.CellPadding="12,8"
                            md:DataGridAssist.ColumnHeaderPadding="12,8"
                            ColumnWidth="*">
                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="{DynamicResource ReprintReceipt}"
                                        Click="ReprintReceipt_Click">
                                    <MenuItem.Icon>
                                        <md:PackIcon Kind="Printer"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="{DynamicResource PreviewReceipt}"
                                        Click="PreviewReceipt_Click">
                                    <MenuItem.Icon>
                                        <md:PackIcon Kind="PrinterEye"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="{DynamicResource SaveAsPDF}"
                                        Click="SaveReceiptAsPdf_Click">
                                    <MenuItem.Icon>
                                        <md:PackIcon Kind="FilePdfBox"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="{DynamicResource ViewDetails}"
                                        Click="ViewDetails_Click">
                                    <MenuItem.Icon>
                                        <md:PackIcon Kind="Eye"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}"/>
                            <!-- Enhanced row style to ensure proper height for buttons -->
                            <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                <Setter Property="MinHeight" Value="56"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                            </Style>
                            <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="MinHeight" Value="56"/>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="{DynamicResource SaleDate}"
                                              Binding="{Binding SaleDate, StringFormat=g}"
                                              Width="Auto"
                                              MinWidth="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource SalesHistoryInvoiceNumber}"
                                              Binding="{Binding InvoiceNumber}" 
                                              Width="Auto"
                                              MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource Customer}"
                                              Binding="{Binding Customer, Converter={StaticResource CustomerFullNameConverter}}"
                                              Width="*"
                                              MinWidth="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource SaleItems}"
                                              Binding="{Binding TotalItems}"
                                              Width="Auto"
                                              MinWidth="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource Total}"
                                              Binding="{Binding GrandTotal, StringFormat={}{0:N2} DA}"
                                              Width="Auto"
                                              MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource Status}"
                                              Binding="{Binding Status, Converter={StaticResource SaleStatusConverter}}"
                                              Width="Auto"
                                              MinWidth="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="300">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  Margin="8,4">
                                            <Button Content="{DynamicResource ViewDetails}"
                                                    Click="ViewDetails_Click"
                                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                                    Background="{DynamicResource PrimaryHueMidBrush}"
                                                    Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                                    Height="32"
                                                    Padding="12,4"
                                                    Margin="2,0"
                                                    FontSize="11"/>
                                            <Button Content="{DynamicResource PrintReceipt}"
                                                    Click="ReprintReceipt_Click"
                                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                                    Height="32"
                                                    Padding="12,4"
                                                    Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                    BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                                    BorderThickness="1"
                                                    Margin="2,0"
                                                    FontSize="11">
                                                <Button.ToolTip>
                                                    <ToolTip>
                                                        <TextBlock Text="{DynamicResource ReprintReceiptTooltip}"/>
                                                    </ToolTip>
                                                </Button.ToolTip>
                                            </Button>
                                            <Button Content="{DynamicResource VoidRefund}"
                                                    Click="VoidRefund_Click"
                                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                                    Height="32"
                                                    Padding="12,4"
                                                    Foreground="#D32F2F"
                                                    BorderBrush="#D32F2F"
                                                    BorderThickness="1"
                                                    Margin="2,0"
                                                    FontSize="11"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Optimized Loading Indicator -->
                    <Border Background="{DynamicResource MaterialDesignPaper}"
                            Opacity="0.95"
                            CornerRadius="8"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  Margin="40">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Width="32"
                                       Height="32"
                                       IsIndeterminate="True"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="{DynamicResource LoadingData}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     HorizontalAlignment="Center"
                                     Margin="0,12,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </DockPanel>
        </md:Card>

        <!-- Sale Details Popup -->
        <Popup x:Name="saleDetailsPopup" 
               StaysOpen="False"
               AllowsTransparency="True"
               Placement="Center">
            <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    md:ElevationAssist.Elevation="Dp3"
                    Width="800"
                    Margin="20">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                            Margin="24">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource SaleDetails}" 
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 Margin="0,0,0,24"/>
                        
                        <!-- Sale Info -->
                        <md:Card Background="{DynamicResource MaterialDesignSurfaceBackground}"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                UniformCornerRadius="4"
                                Margin="0,0,0,24">
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Text="{DynamicResource InvoiceNumber}" 
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding InvoiceNumber}" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"/>
                                
                                <TextBlock Text="{DynamicResource SaleDate}" 
                                         Grid.Row="1"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding SaleDate, StringFormat=g}" 
                                         Grid.Row="1" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"/>
                                
                                <TextBlock Text="{DynamicResource Customer}" 
                                         Grid.Row="2"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Grid.Row="2" 
                                         Grid.Column="1"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="16,0,0,8"
                                         Text="{Binding Customer, Converter={StaticResource CustomerFullNameConverter}}"/>
                            </Grid>
                        </md:Card>

                        <!-- Items List -->
                        <DataGrid ItemsSource="{Binding Items}"
                                 Style="{StaticResource MaterialDesignDataGrid}"
                                 AutoGenerateColumns="False"
                                 IsReadOnly="True"
                                 HeadersVisibility="Column"
                                 GridLinesVisibility="Horizontal"
                                 BorderThickness="1"
                                 BorderBrush="{DynamicResource MaterialDesignDivider}"
                                 Background="Transparent"
                                 RowBackground="{DynamicResource MaterialDesignPaper}"
                                 AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 Margin="0,0,0,24"
                                 Height="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource Product}" 
                                                  Binding="{Binding Product.Name}" 
                                                  Width="*">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource Quantity}" 
                                                  Binding="{Binding Quantity}" 
                                                  Width="100">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource UnitPrice}" 
                                                  Binding="{Binding UnitPrice, StringFormat={}{0:N2} DA}" 
                                                  Width="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="{DynamicResource Total}" 
                                                  Binding="{Binding Total, StringFormat={}{0:N2} DA}" 
                                                  Width="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- Totals -->
                        <md:Card Background="{DynamicResource MaterialDesignSurfaceBackground}"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                UniformCornerRadius="4"
                                Margin="0,0,0,24">
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Text="{DynamicResource Subtotal}" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding Subtotal, StringFormat={}{0:N2} DA}" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource Discount}" 
                                         Grid.Row="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding DiscountAmount, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="1" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource Tax}" 
                                         Grid.Row="2" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <TextBlock Text="{Binding TaxAmount, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="2" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>

                                <TextBlock Text="{DynamicResource GrandTotal}" 
                                         Grid.Row="3" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                                <TextBlock Text="{Binding GrandTotal, StringFormat={}{0:N2} DA}" 
                                         Grid.Row="3" 
                                         Grid.Column="1" 
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         FontWeight="Medium"/>
                            </Grid>
                        </md:Card>

                        <Button Content="{DynamicResource Close}" 
                                Click="CloseDetails_Click"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                HorizontalAlignment="Right"/>
                    </StackPanel>
                </ScrollViewer>
            </md:Card>
        </Popup>

        <!-- Void/Refund Popup -->
        <Popup x:Name="voidRefundPopup" 
               StaysOpen="False"
               AllowsTransparency="True"
               Placement="Center">
            <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    md:ElevationAssist.Elevation="Dp3"
                    Width="500"
                    Margin="20">
                <StackPanel Margin="24">
                    <TextBlock Text="{DynamicResource VoidRefundSale}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Margin="0,0,0,16"/>

                    <!-- Modified TextBlock for better RTL support -->
                    <TextBlock Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             TextWrapping="Wrap"
                             Margin="0,0,0,24">
                        <Run Text="{DynamicResource VoidRefundConfirmationPrefix}"/>
                        <Run Text="{Binding InvoiceNumber}"/>
                        <Run Text="{DynamicResource VoidRefundConfirmationSuffix}"/>
                    </TextBlock>

                    <ComboBox x:Name="voidRefundReasonComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            Foreground="{DynamicResource MaterialDesignBody}"
                            Background="{DynamicResource MaterialDesignPaper}"
                            md:HintAssist.Hint="{DynamicResource SelectReason}"
                            Margin="0,0,0,16"/>

                    <TextBox x:Name="voidRefundCommentTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Background="{DynamicResource MaterialDesignPaper}"
                           md:HintAssist.Hint="{DynamicResource AdditionalComments}"
                           TextWrapping="Wrap"
                           AcceptsReturn="True"
                           Height="100"
                           Margin="0,0,0,24"/>

                    <StackPanel Orientation="Horizontal" 
                              HorizontalAlignment="Right">
                        <Button x:Name="cancelButton"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                BorderBrush="{DynamicResource MaterialDesignDivider}"
                                Click="CloseVoidRefund_Click"
                                Margin="0,0,8,0">
                            <!-- Explicitly use text based on FlowDirection -->
                            <Button.Content>
                                <TextBlock>
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}, Path=FlowDirection}" Value="RightToLeft">
                                                    <Setter Property="Text" Value="إلغاء"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}, Path=FlowDirection}" Value="LeftToRight">
                                                    <Setter Property="Text" Value="Cancel"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Button.Content>
                        </Button>
                        <Button x:Name="confirmButton"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#D32F2F"
                                BorderBrush="#D32F2F"
                                Click="ConfirmVoidRefund_Click">
                            <!-- Explicitly use text based on FlowDirection -->
                            <Button.Content>
                                <TextBlock>
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}, Path=FlowDirection}" Value="RightToLeft">
                                                    <Setter Property="Text" Value="تأكيد"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}, Path=FlowDirection}" Value="LeftToRight">
                                                    <Setter Property="Text" Value="Confirm"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </md:Card>
        </Popup>
    </Grid>
</UserControl> 