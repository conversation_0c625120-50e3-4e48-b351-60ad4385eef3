using POSSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.ProductManagement
{
    /// <summary>
    /// Interface for product management operations
    /// </summary>
    public interface IProductManagementService
    {
        /// <summary>
        /// Get all products with basic information
        /// </summary>
        Task<List<Product>> GetAllProductsAsync();

        /// <summary>
        /// Get all products with full details including barcodes and batches
        /// </summary>
        Task<List<Product>> GetAllProductsWithFullDetailsAsync();

        /// <summary>
        /// Get product by ID
        /// </summary>
        Task<Product> GetProductByIdAsync(int id);

        /// <summary>
        /// Get product by barcode
        /// </summary>
        Task<Product> GetProductByBarcodeAsync(string barcode);

        /// <summary>
        /// Add new product
        /// </summary>
        Task<int> AddProductAsync(Product product);

        /// <summary>
        /// Update existing product
        /// </summary>
        Task<bool> UpdateProductAsync(Product product);

        /// <summary>
        /// Delete product (soft delete if has transactions, hard delete otherwise)
        /// </summary>
        Task<bool> DeleteProductAsync(int id);

        /// <summary>
        /// Update product stock quantity
        /// </summary>
        Task<bool> UpdateProductStockAsync(int productId, decimal quantity);

        /// <summary>
        /// Check if product exists by barcode
        /// </summary>
        Task<bool> ProductExistsAsync(string barcode);

        /// <summary>
        /// Search products by name, SKU, or barcode
        /// </summary>
        Task<List<Product>> SearchProductsAsync(string searchTerm);

        /// <summary>
        /// Get low stock products
        /// </summary>
        Task<List<Product>> GetLowStockProductsAsync();

        /// <summary>
        /// Get expiring products
        /// </summary>
        Task<List<Product>> GetExpiringProductsAsync(int daysThreshold = 30);
    }
}
