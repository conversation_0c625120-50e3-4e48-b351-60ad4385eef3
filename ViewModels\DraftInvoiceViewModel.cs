using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Models.DTOs;
using POSSystem.Services;
using POSSystem.Helpers;
using CommunityToolkit.Mvvm.Input;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ViewModel for the simplified draft invoice dialog (non-admin users)
    /// </summary>
    public class DraftInvoiceViewModel : INotifyPropertyChanged
    {
        private readonly DraftInvoiceService _draftInvoiceService;
        private readonly UserPermissionsService _permissionsService;
        private readonly AuthenticationService _authService;
        private readonly DatabaseService _dbService;

        // Private fields
        private Customer _selectedCustomer;
        private Product _selectedProduct;
        private decimal _productQuantity = 1;
        private bool _isLoading;
        private string _statusMessage;

        public DraftInvoiceViewModel(
            DraftInvoiceService draftInvoiceService,
            UserPermissionsService permissionsService,
            AuthenticationService authService,
            DatabaseService dbService)
        {
            _draftInvoiceService = draftInvoiceService ?? throw new ArgumentNullException(nameof(draftInvoiceService));
            _permissionsService = permissionsService ?? throw new ArgumentNullException(nameof(permissionsService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));

            // Initialize collections
            DraftItems = new ObservableCollection<DraftInvoiceItemDto>();
            AvailableCustomers = new ObservableCollection<Customer>();
            AvailableProducts = new ObservableCollection<Product>();

            // Initialize commands
            AddProductCommand = new RelayCommand(_ => AddProduct(), _ => CanAddProduct());
            RemoveItemCommand = new RelayCommand<DraftInvoiceItemDto>(item => RemoveItem(item));
            SaveDraftCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(SaveDraftAsync, CanSaveDraft);
            CancelCommand = new RelayCommand(_ => Cancel());

            // Initialize with current user permissions
            InitializePermissions();
            
            // Load data
            _ = LoadDataAsync();
        }

        #region Properties

        public ObservableCollection<DraftInvoiceItemDto> DraftItems { get; }
        public ObservableCollection<Customer> AvailableCustomers { get; }
        public ObservableCollection<Product> AvailableProducts { get; }

        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                _selectedCustomer = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CustomerDisplay));
            }
        }

        public Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UnitPriceDisplay));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public decimal ProductQuantity
        {
            get => _productQuantity;
            set
            {
                _productQuantity = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(EstimatedLineTotal));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // Computed properties
        public bool CanSelectCustomer => _permissionsService.CanSelectCustomersForInvoices();
        
        public string CustomerDisplay => SelectedCustomer?.FirstName + " " + SelectedCustomer?.LastName ?? "No customer selected";
        
        public string UnitPriceDisplay => SelectedProduct?.SellingPrice.ToString("C") ?? "$0.00";
        
        public decimal EstimatedLineTotal => SelectedProduct != null ? SelectedProduct.SellingPrice * ProductQuantity : 0;
        
        public decimal Subtotal => DraftItems.Sum(item => item.Total);
        
        public string SubtotalDisplay => Subtotal.ToString("C");
        
        public int ItemCount => DraftItems.Count;
        
        public bool HasItems => ItemCount > 0;

        public string PermissionMessage => _permissionsService.CanCreateFullInvoices() 
            ? "You can create complete invoices directly."
            : "This will create a draft invoice for admin completion. Final pricing, taxes, and payment terms will be set by an administrator.";

        #endregion

        #region Commands

        public ICommand AddProductCommand { get; }
        public ICommand RemoveItemCommand { get; }
        public ICommand SaveDraftCommand { get; }
        public ICommand CancelCommand { get; }

        #endregion

        #region Command Implementations

        private bool CanAddProduct()
        {
            return SelectedProduct != null && ProductQuantity > 0 && !IsLoading;
        }

        private void AddProduct()
        {
            try
            {
                if (SelectedProduct == null) return;

                // Check if product already exists in the list
                var existingItem = DraftItems.FirstOrDefault(item => item.ProductId == SelectedProduct.Id);
                if (existingItem != null)
                {
                    // Update quantity of existing item
                    existingItem.Quantity += ProductQuantity;
                    existingItem.CalculateTotal();
                }
                else
                {
                    // Add new item
                    var newItem = DraftInvoiceItemDto.FromProduct(SelectedProduct, ProductQuantity);
                    DraftItems.Add(newItem);
                }

                // Reset selection
                SelectedProduct = null;
                ProductQuantity = 1;

                // Update computed properties
                OnPropertyChanged(nameof(Subtotal));
                OnPropertyChanged(nameof(SubtotalDisplay));
                OnPropertyChanged(nameof(ItemCount));
                OnPropertyChanged(nameof(HasItems));
                
                CommandManager.InvalidateRequerySuggested();

                StatusMessage = "Product added successfully.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error adding product: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_VM] Error adding product: {ex.Message}");
            }
        }

        private void RemoveItem(DraftInvoiceItemDto item)
        {
            try
            {
                if (item != null && DraftItems.Contains(item))
                {
                    DraftItems.Remove(item);
                    
                    // Update computed properties
                    OnPropertyChanged(nameof(Subtotal));
                    OnPropertyChanged(nameof(SubtotalDisplay));
                    OnPropertyChanged(nameof(ItemCount));
                    OnPropertyChanged(nameof(HasItems));
                    
                    CommandManager.InvalidateRequerySuggested();

                    StatusMessage = "Item removed successfully.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error removing item: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_VM] Error removing item: {ex.Message}");
            }
        }

        private bool CanSaveDraft()
        {
            return HasItems && !IsLoading;
        }

        private async Task SaveDraftAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Saving draft invoice...";

                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    StatusMessage = "Error: User not authenticated.";
                    return;
                }

                // Create draft DTO
                var draftDto = new DraftInvoiceDto
                {
                    Type = "Sales",
                    IssueDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CustomerId = SelectedCustomer?.Id,
                    CreatedByUserId = currentUser.Id,
                    RequiresAdminCompletion = !_permissionsService.CanCreateFullInvoices()
                };

                // Add items
                foreach (var item in DraftItems)
                {
                    draftDto.Items.Add(item);
                }

                draftDto.CalculateTotals();

                // Save draft
                var result = await _draftInvoiceService.CreateDraftInvoiceAsync(draftDto, currentUser);

                if (result.Success)
                {
                    StatusMessage = "Draft invoice saved successfully!";
                    
                    // Show success message
                    var message = _permissionsService.CanCreateFullInvoices()
                        ? "Invoice created successfully!"
                        : "Draft invoice created successfully!\n\nAn administrator will review and complete the invoice details.\nYou will be notified when the invoice is ready.";

                    MessageBox.Show(message, "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Close dialog with success result
                    CloseDialog(true);
                }
                else
                {
                    StatusMessage = $"Error: {result.Message}";
                    if (result.Errors.Any())
                    {
                        var errorDetails = string.Join("\n", result.Errors);
                        MessageBox.Show($"Validation errors:\n{errorDetails}", "Validation Error", 
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving draft: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_VM] Error saving draft: {ex.Message}");
                MessageBox.Show($"An error occurred while saving the draft invoice:\n{ex.Message}", 
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void Cancel()
        {
            CloseDialog(false);
        }

        #endregion

        #region Helper Methods

        private void InitializePermissions()
        {
            OnPropertyChanged(nameof(CanSelectCustomer));
            OnPropertyChanged(nameof(PermissionMessage));
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Loading data...";

                await Task.Run(() =>
                {
                    try
                    {
                        // Load customers if user has permission
                        if (CanSelectCustomer)
                        {
                            var customers = _dbService.GetCustomers().Where(c => c.IsActive).ToList();
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                AvailableCustomers.Clear();
                                foreach (var customer in customers)
                                {
                                    AvailableCustomers.Add(customer);
                                }
                            });
                        }

                        // Load products
                        var products = _dbService.GetProducts().Where(p => p.IsActive).ToList();
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AvailableProducts.Clear();
                            foreach (var product in products)
                            {
                                AvailableProducts.Add(product);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_VM] Error in background loading: {ex.Message}");
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            StatusMessage = $"Error loading data: {ex.Message}";
                        });
                        return;
                    }
                });

                StatusMessage = "Ready to create draft invoice.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading data: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[DRAFT_INVOICE_VM] Error loading data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CloseDialog(bool result)
        {
            // This will be handled by the dialog host or window
            DialogResult = result;
            RequestClose?.Invoke();
        }

        #endregion

        #region Events

        public event PropertyChangedEventHandler PropertyChanged;
        public event Action RequestClose;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public bool? DialogResult { get; private set; }

        #endregion
    }
}
