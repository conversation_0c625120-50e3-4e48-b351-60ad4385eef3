-- Create Roles table
CREATE TABLE IF NOT EXISTS Roles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);

-- Create Users table
CREATE TABLE IF NOT EXISTS Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL,
    Password TEXT NOT NULL,
    FirstName TEXT NOT NULL,
    LastName TEXT NOT NULL,
    Email TEXT,
    Phone TEXT,
    RoleId INTEGER NOT NULL,
    IsActive INTEGER NOT NULL DEFAULT 1,
    PhotoPath TEXT DEFAULT 'default-user.png',
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL,
    FOREIGN KEY (RoleId) REFERENCES Roles(Id)
);

-- Create Categories table
CREATE TABLE IF NOT EXISTS Categories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1
);

-- Create Suppliers table
CREATE TABLE IF NOT EXISTS Suppliers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CompanyName TEXT NOT NULL,
    ContactPerson TEXT,
    Email TEXT,
    Phone TEXT,
    Address TEXT,
    Website TEXT,
    Notes TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL
);

-- Create Products table
CREATE TABLE IF NOT EXISTS Products (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    SKU TEXT,
    Barcode TEXT,
    Description TEXT,
    PurchasePrice DECIMAL(18,2) NOT NULL,
    SellingPrice DECIMAL(18,2) NOT NULL,
    StockQuantity INTEGER NOT NULL DEFAULT 0,
    MinimumStock INTEGER NOT NULL DEFAULT 0,
    ReorderPoint INTEGER NOT NULL DEFAULT 0,
    CategoryId INTEGER NOT NULL,
    SupplierId INTEGER,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL,
    FOREIGN KEY (CategoryId) REFERENCES Categories(Id),
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
);

-- Create Customers table
CREATE TABLE IF NOT EXISTS Customers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    FirstName TEXT NOT NULL,
    LastName TEXT NOT NULL,
    Email TEXT,
    Phone TEXT,
    Address TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1,
    LoyaltyCode TEXT,
    LoyaltyPoints DECIMAL(18,2) DEFAULT 0,
    LastVisit TEXT,
    TotalVisits INTEGER DEFAULT 0,
    TotalSpent DECIMAL(18,2) DEFAULT 0,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL
);

-- Create Sales table
CREATE TABLE IF NOT EXISTS Sales (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT NOT NULL,
    SaleDate TEXT NOT NULL,
    DueDate TEXT,
    CustomerId INTEGER,
    UserId INTEGER NOT NULL,
    Subtotal DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) NOT NULL,
    TaxAmount DECIMAL(18,2) NOT NULL,
    GrandTotal DECIMAL(18,2) NOT NULL,
    PaymentMethod TEXT NOT NULL,
    PaymentStatus TEXT NOT NULL,
    AmountPaid DECIMAL(18,2) NOT NULL,
    Change DECIMAL(18,2) NOT NULL,
    Status TEXT NOT NULL,
    TotalItems INTEGER NOT NULL,
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);

-- Create SaleItems table
CREATE TABLE IF NOT EXISTS SaleItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    Total DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id)
);

-- Insert default roles
INSERT INTO Roles (Id, Name, Description, IsActive, CreatedAt) VALUES 
(1, 'Admin', 'System Administrator', 1, datetime('now')),
(2, 'Manager', 'Store Manager', 1, datetime('now')),
(3, 'Cashier', 'Store Cashier', 1, datetime('now'));

-- Insert default admin user with RoleId
INSERT INTO Users (Username, Password, FirstName, LastName, Email, Phone, RoleId, IsActive, CreatedAt, UpdatedAt)
VALUES ('admin', 'admin123', 'Admin', 'User', '<EMAIL>', '************', 1, 1, datetime('now'), datetime('now'));

-- Sample categories
INSERT INTO Categories (Name, Description, IsActive) VALUES 
('Beverages', 'Drinks and liquid refreshments', 1),
('Snacks', 'Light food and snacks', 1),
('Electronics', 'Electronic devices and accessories', 1),
('Groceries', 'Food and household items', 1);

-- Sample supplier
INSERT INTO Suppliers (
    CompanyName, ContactPerson, Email, Phone,
    Address, Website, IsActive, CreatedAt, UpdatedAt
) VALUES (
    'ABC Supplies', 'John Smith', '<EMAIL>', '555-0123',
    '789 Supply St', 'www.abcsupplies.com', 1, datetime('now'), datetime('now')
);

-- Sample products
INSERT INTO Products (
    Name, SKU, Barcode, Description, 
    PurchasePrice, SellingPrice, StockQuantity, 
    MinimumStock, ReorderPoint, CategoryId, 
    SupplierId, IsActive, CreatedAt, UpdatedAt
) VALUES 
('Cola', 'BEV001', '*********', 'Refreshing cola drink', 
0.50, 1.00, 100, 20, 40, 1, 
1, 1, datetime('now'), datetime('now')),
('Chips', 'SNK001', '*********', 'Potato chips', 
0.75, 1.50, 50, 10, 20, 2, 
1, 1, datetime('now'), datetime('now')),
('USB Cable', 'ELC001', '*********', 'USB Type-C cable', 
2.00, 5.00, 30, 5, 10, 3, 
1, 1, datetime('now'), datetime('now')),
('Bread', 'GRO001', '*********', 'Fresh bread', 
1.00, 2.00, 40, 10, 15, 4, 
1, 1, datetime('now'), datetime('now'));

-- Sample customers
INSERT INTO Customers (
    FirstName, LastName, Email, Phone,
    Address, IsActive, LoyaltyCode,
    LoyaltyPoints, LastVisit, TotalVisits,
    TotalSpent, CreatedAt, UpdatedAt
) VALUES
('John', 'Doe', '<EMAIL>', '************',
'123 Main St', 1, 'JD12345', 100, datetime('now'),
5, 500.00, datetime('now'), datetime('now')),
('Jane', 'Smith', '<EMAIL>', '************',
'456 Oak Ave', 1, 'JS67890', 250, datetime('now'),
8, 800.00, datetime('now'), datetime('now'));

-- =====================================================
-- Two-Tier Invoice System Integration
-- =====================================================
-- Execute the two-tier invoice system migration
-- This includes Invoice, InvoiceItems, InvoicePayments,
-- DraftInvoiceNotifications, and DraftInvoiceSettings tables