using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Views.Dialogs
{
    public partial class SupplierDialog : UserControl
    {
        private readonly DatabaseService _dbService;
        private Supplier _existingSupplier;
        private bool _isEditMode;
        private readonly string _dialogIdentifier;

        public Supplier Result { get; private set; }

        public SupplierDialog(Supplier supplier = null, string dialogIdentifier = "RootDialog")
        {
            InitializeComponent();
            
            _dbService = new DatabaseService();
            _dialogIdentifier = dialogIdentifier;
            
            // If a supplier is provided, we're in edit mode
            if (supplier != null)
            {
                _existingSupplier = supplier;
                _isEditMode = true;
                LoadSupplierData(supplier);
                DialogTitle.Text = (string)Application.Current.Resources["EditSupplier"];
                btnSave.Content = (string)Application.Current.Resources["Update"];
            }
            else
            {
                _isEditMode = false;
                DialogTitle.Text = (string)Application.Current.Resources["AddSupplier"];
                btnSave.Content = (string)Application.Current.Resources["AddSupplier"];
            }
        }

        private void LoadSupplierData(Supplier supplier)
        {
            txtCompanyName.Text = supplier.Name;
            txtContactPerson.Text = supplier.ContactName;
            txtEmail.Text = supplier.Email;
            txtPhone.Text = supplier.Phone;
            txtAddress.Text = supplier.Address;
            txtWebsite.Text = supplier.Website;
            txtNotes.Text = supplier.Notes;
            chkIsActive.IsChecked = supplier.IsActive;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.Close(_dialogIdentifier);
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["CompanyNameRequired"],
                        (string)Application.Current.Resources["ValidationError"],
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var supplier = _isEditMode ? _existingSupplier : new Supplier();
                
                supplier.Name = txtCompanyName.Text.Trim();
                supplier.ContactName = txtContactPerson.Text.Trim();
                supplier.Email = txtEmail.Text.Trim();
                supplier.Phone = txtPhone.Text.Trim();
                supplier.Address = txtAddress.Text.Trim();
                supplier.Website = txtWebsite.Text.Trim();
                supplier.Notes = txtNotes.Text.Trim();
                supplier.IsActive = chkIsActive.IsChecked ?? true;

                if (_isEditMode)
                {
                    supplier.UpdatedAt = DateTime.Now;
                    _dbService.UpdateSupplier(supplier);
                }
                else
                {
                    supplier.CreatedAt = DateTime.Now;
                    _dbService.AddSupplier(supplier);
                }

                Result = supplier;
                DialogHost.Close(_dialogIdentifier, supplier);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    string.Format((string)Application.Current.Resources["ErrorSavingSupplier"], ex.Message),
                    (string)Application.Current.Resources["ErrorTitle"],
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 