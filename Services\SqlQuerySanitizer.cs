using System;
using System.Text.RegularExpressions;
using System.Diagnostics;

namespace POSSystem.Services
{
    /// <summary>
    /// Utility class to sanitize SQL queries and fix common typos
    /// </summary>
    public static class SqlQuerySanitizer
    {
        /// <summary>
        /// Fix common SQL function typos in a query
        /// </summary>
        /// <param name="sqlQuery">The SQL query to sanitize</param>
        /// <returns>The sanitized SQL query</returns>
        public static string SanitizeQuery(string sqlQuery)
        {
            if (string.IsNullOrEmpty(sqlQuery))
                return sqlQuery;
            
            string result = sqlQuery;
            
            // Fix INSTRO to INSTR
            if (result.Contains("INSTRO", StringComparison.OrdinalIgnoreCase))
            {
                // Log the original query for debugging
                Debug.WriteLine($"Fixed SQL query with INSTRO typo: {result}");
                
                // Replace INSTRO with INSTR (case-insensitive)
                result = Regex.Replace(result, "INSTRO", "INSTR", RegexOptions.IgnoreCase);
            }
            
            return result;
        }
    }
}
