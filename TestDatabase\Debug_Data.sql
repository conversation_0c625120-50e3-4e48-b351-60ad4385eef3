-- Debug Basic Test Data Generator
-- This script attempts to debug issues with insert operations

-- Output statements to report progress
SELECT 'Starting debug script...';

-- Temporarily disable foreign key constraints
PRAGMA foreign_keys = OFF;
SELECT 'Foreign keys disabled';

-- Check if tables exist
SELECT 'Sales table exists:' || (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='Sales');
SELECT 'SaleItems table exists:' || (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='SaleItems');
SELECT 'CashDrawers table exists:' || (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='CashDrawers');

-- Verify schema
SELECT 'Sales table schema:';
SELECT sql FROM sqlite_master WHERE type='table' AND name='Sales';

BEGIN TRANSACTION;
SELECT 'Transaction started';

-- Create a simple test sale without using any complex features
SELECT 'Inserting test sale...';
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, 
                  TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  ('DEBUG-INV-001', datetime('now'), 1, 1, 50.00, 0.00, 0.00, 50.00, 50.00, 0.00, 'Cash', 'Paid', 'Completed', 1);

-- Check if sale was inserted
SELECT 'Sales count after insert: ' || (SELECT COUNT(*) FROM Sales);
SELECT 'Last inserted sale ID: ' || last_insert_rowid();

-- Add a simple sale item
SELECT 'Inserting sale item...';
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES
  (last_insert_rowid(), 1, 1, 50.00, 50.00);

-- Check if sale item was inserted
SELECT 'SaleItems count: ' || (SELECT COUNT(*) FROM SaleItems);

-- Add a simple cash drawer
SELECT 'Inserting cash drawer...';
INSERT INTO CashDrawers (OpeningBalance, CurrentBalance, ExpectedBalance, ActualBalance, 
                         Difference, Status, OpenedAt, ClosedAt, OpenedById, ClosedById, Notes)
VALUES
  (500.00, 550.00, 550.00, 550.00, 0.00, 'Closed', datetime('now'), 
   datetime('now', '+8 hours'), 1, 1, 'Debug drawer');

-- Check if cash drawer was inserted
SELECT 'CashDrawers count: ' || (SELECT COUNT(*) FROM CashDrawers);

-- Add a simple inventory transaction
SELECT 'Inserting inventory transaction...';
INSERT INTO InventoryTransactions (ProductId, TransactionType, Quantity, UnitPrice, 
                                 Reference, Notes, TransactionDate, UserId)
VALUES
  (1, 'Sale', -1, 50.00, 'DEBUG-INV-001', 'Debug transaction', 
   datetime('now'), 1);

-- Check if inventory transaction was inserted
SELECT 'InventoryTransactions count: ' || (SELECT COUNT(*) FROM InventoryTransactions);

-- Add a simple loyalty transaction
SELECT 'Inserting loyalty transaction...';
INSERT INTO LoyaltyTransactions (CustomerId, Points, Description, TransactionDate)
VALUES
  (1, 50, 'Debug points from sale DEBUG-INV-001', datetime('now'));

-- Check if loyalty transaction was inserted
SELECT 'LoyaltyTransactions count: ' || (SELECT COUNT(*) FROM LoyaltyTransactions);

-- Finally, commit the transaction
SELECT 'Committing transaction...';
COMMIT;
SELECT 'Transaction committed';

-- Re-enable foreign key constraints
PRAGMA foreign_keys = ON;
SELECT 'Foreign keys re-enabled';

SELECT 'Script completed successfully'; 