using System;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Models;
using System.Windows.Input;

namespace POSSystem.Views
{
    /// <summary>
    /// Interaction logic for InvoiceDialogView.xaml
    /// </summary>
    public partial class InvoiceDialogView : UserControl
    {
        public InvoiceDialogView()
        {
            InitializeComponent();


                // Subscribe to ViewModel events
                this.DataContextChanged += (s, e) => AttachViewModelHandlers(e.NewValue as InvoiceViewModel);
                AttachViewModelHandlers(this.DataContext as InvoiceViewModel);

            // Add handler for the Loaded event to setup debugging
            this.Loaded += InvoiceDialogView_Loaded;
        }

        private void InvoiceDialogView_Loaded(object sender, RoutedEventArgs e)
        {
            // Get the ViewModel
            if (this.DataContext is InvoiceViewModel viewModel)
            {
                // Wire up to Subtotal property change for debugging
                viewModel.PropertyChanged += (s, args) => {
                    if (args.PropertyName == "InvoiceItems" ||
                        args.PropertyName == "Subtotal" ||
                        args.PropertyName == "GrandTotal")
                    {
                        DebugViewModel(viewModel);
                    }
                };

                // Debug current state
                DebugViewModel(viewModel);

                // Add a button for diagnostic
                var diagButton = new Button
                {
                    Content = "Diagnose Invoice",
                    Margin = new Thickness(5),
                    Padding = new Thickness(10, 5, 10, 5),
                    HorizontalAlignment = HorizontalAlignment.Left,
                    VerticalAlignment = VerticalAlignment.Top
                };

                diagButton.Click += (s, args) => DiagnoseInvoice(viewModel);

                // Add the button to the visual tree
                if (this.Content is Panel panel)
                {
                    panel.Children.Add(diagButton);
                }
            }
        }

        private void DebugViewModel(InvoiceViewModel viewModel)
        {
            try
            {
                // Log invoice state
                Console.WriteLine($"Items: {viewModel.InvoiceItems?.Count ?? 0}, " +
                                 $"Subtotal: {viewModel.Subtotal}, " +
                                 $"GrandTotal: {viewModel.GrandTotal}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Debug error: {ex.Message}");
            }
        }

        private void DiagnoseInvoice(InvoiceViewModel viewModel)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("=== INVOICE DIAGNOSTIC INFO ===");

                // Basic invoice info
                sb.AppendLine($"Invoice Number: '{viewModel.InvoiceNumber}'");
                sb.AppendLine($"Invoice Type: '{viewModel.InvoiceType}'");
                sb.AppendLine($"Status: '{viewModel.Status}'");

                // Customer/Supplier info
                sb.AppendLine($"Customer ID: {viewModel.CustomerId}");
                sb.AppendLine($"Supplier ID: {viewModel.SupplierId}");

                // Items info
                sb.AppendLine($"Items Count: {viewModel.InvoiceItems?.Count ?? 0}");

                if (viewModel.InvoiceItems != null && viewModel.InvoiceItems.Count > 0)
                {
                    sb.AppendLine("Invoice Items:");
                    int index = 0;
                    foreach (var item in viewModel.InvoiceItems)
                    {
                        if (item == null)
                        {
                            sb.AppendLine($"  [{index}]: NULL ITEM");
                        }
                        else
                        {
                            sb.AppendLine($"  [{index}]: ProductId={item.ProductId}, Qty={item.Quantity}, Price={item.UnitPrice}, Total={item.Total}");
                        }
                        index++;
                    }
                }

                // Check save conditions
                sb.AppendLine("\n=== SAVE BUTTON CONDITIONS ===");
                bool canSave = true;

                // Check invoice number
                if (string.IsNullOrWhiteSpace(viewModel.InvoiceNumber))
                {
                    sb.AppendLine("❌ Invoice Number is missing");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine("✓ Invoice Number is present");
                }

                // Check invoice type
                if (string.IsNullOrWhiteSpace(viewModel.InvoiceType))
                {
                    sb.AppendLine("❌ Invoice Type is missing");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine("✓ Invoice Type is present");
                }

                // Check status
                if (string.IsNullOrWhiteSpace(viewModel.Status))
                {
                    sb.AppendLine("❌ Status is missing");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine("✓ Status is present");
                }

                // Check items
                if (viewModel.InvoiceItems == null || viewModel.InvoiceItems.Count == 0)
                {
                    sb.AppendLine("❌ No invoice items added");
                    canSave = false;
                }
                else
                {
                    sb.AppendLine($"✓ Invoice has {viewModel.InvoiceItems.Count} items");
                }

                // Check customer/supplier
                if (viewModel.InvoiceType == "Sales")
                {
                    if (!viewModel.CustomerId.HasValue)
                    {
                        sb.AppendLine("❌ Customer is not selected for Sales invoice");
                        canSave = false;
                    }
                    else
                    {
                        sb.AppendLine("✓ Customer is selected");
                    }
                }

                if (viewModel.InvoiceType == "Purchase")
                {
                    if (!viewModel.SupplierId.HasValue)
                    {
                        sb.AppendLine("❌ Supplier is not selected for Purchase invoice");
                        canSave = false;
                    }
                    else
                    {
                        sb.AppendLine("✓ Supplier is selected");
                    }
                }

                sb.AppendLine($"FINAL RESULT: Can{(canSave ? "" : "not")} save invoice");

                // Show diagnostic info
                MessageBox.Show(sb.ToString(), "Invoice Diagnostic", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in diagnostic: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



		private void AttachViewModelHandlers(InvoiceViewModel vm)
		{
		    if (vm == null) return;
		    vm.FocusQuantityRequested -= Vm_FocusQuantityRequested;
		    vm.FocusQuantityRequested += Vm_FocusQuantityRequested;
		}

		private void Vm_FocusQuantityRequested(object sender, EventArgs e)
		{
		    try
		    {
		        // Use Dispatcher to ensure UI updates are complete before setting focus
		        Dispatcher.BeginInvoke(new Action(() =>
		        {
		            // Find the quantity textbox and focus/select all
		            var qty = this.FindName("txtQuantity") as TextBox;
		            if (qty != null)
		            {
		                qty.Focus();
		                qty.SelectAll();
		            }
		        }), System.Windows.Threading.DispatcherPriority.Input);
		    }
		    catch { }
		}

        private void TxtQuantity_GotFocus(object sender, RoutedEventArgs e)
        {
            // Automatically select all text when quantity field gains focus
            if (sender is TextBox textBox)
            {
                // Use Dispatcher to ensure the text is selected after the focus event completes
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    textBox.SelectAll();
                }), System.Windows.Threading.DispatcherPriority.Input);
            }
        }
        }
    }
