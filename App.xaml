﻿<?xml version="1.0" encoding="utf-8"?>
<Application x:Class="POSSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:POSSystem"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:models="clr-namespace:POSSystem.Models"
             xmlns:System="clr-namespace:System;assembly=mscorlib"
             xmlns:system="clr-namespace:System;assembly=mscorlib"
             Exit="App_Exit">
    
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design Resources -->
                <materialDesign:BundledTheme BaseTheme="Light" 
                                           PrimaryColor="Blue" 
                                           SecondaryColor="Blue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Application Styles -->
                <ResourceDictionary Source="Resources/Styles.xaml"/>
                <!-- PerformanceLite.xaml will be merged dynamically at runtime if enabled -->

                <!-- Converters -->
                <ResourceDictionary Source="Resources/Converters.xaml"/>

                <!-- Localization Resources - Loaded dynamically by App.xaml.cs based on language setting -->
                <ResourceDictionary Source="Resources/Strings.xaml"/>

                <!-- Font Resources -->
                <ResourceDictionary>
                    <!-- 7-Segment Digital Display Font -->
                    <FontFamily x:Key="SevenSegmentFont">pack://application:,,,/POSSystem;component/Fonts/7segment.ttf#DSEG7 Modern</FontFamily>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Theme -->
            <materialDesign:CustomColorTheme x:Key="MaterialDesignCustomTheme" BaseTheme="Light" PrimaryColor="#0078D4" SecondaryColor="#00B7C3"/>
            
            <!-- ✅ PERFORMANCE FIX: Add missing MaterialDesign color resources to prevent lookup failures -->
            <Color x:Key="PrimaryHueLightColor">#64B5F6</Color>
            <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="{StaticResource PrimaryHueLightColor}"/>

            <!-- Custom Brushes for Theming -->
            <SolidColorBrush x:Key="MaterialDesignHeadlineTextBrush" Color="{Binding Color, Source={StaticResource MaterialDesignDarkForeground}}"/>
            <SolidColorBrush x:Key="MaterialDesignBodyTextBrush" Color="{Binding Color, Source={StaticResource MaterialDesignBody}}"/>
            
            <!-- Primary Button Style -->
            <Style x:Key="PrimaryButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="#4CAF50"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#45A049"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Secondary Button Style -->
            <Style x:Key="SecondaryButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="#E0E0E0"/>
                <Setter Property="Foreground" Value="#333333"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#D0D0D0"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Danger Button Style -->
            <Style x:Key="DangerButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="#f44336"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#d32f2f"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Navigation Button Style -->
            <Style x:Key="NavButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Height" Value="45"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Padding" Value="20,0"/>
                <Setter Property="Margin" Value="0,2"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6">
                                <ContentPresenter x:Name="contentPresenter"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#3FFFFFFF"/>
                                    <Setter TargetName="border" Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect ShadowDepth="1" 
                                                            BlurRadius="4" 
                                                            Opacity="0.2"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#4FFFFFFF"/>
                                    <Setter TargetName="contentPresenter" Property="RenderTransform">
                                        <Setter.Value>
                                            <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Active Navigation Button Style -->
            <Style x:Key="NavButtonActive" TargetType="Button" BasedOn="{StaticResource NavButton}">
                <Setter Property="Background" Value="#3FFFFFFF"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Border x:Name="background" 
                                        Background="{TemplateBinding Background}"
                                        CornerRadius="6">
                                    <Border.Effect>
                                        <DropShadowEffect ShadowDepth="1" 
                                                        BlurRadius="4" 
                                                        Opacity="0.2"/>
                                    </Border.Effect>
                                </Border>
                                <Border x:Name="highlight"
                                        Width="4"
                                        HorizontalAlignment="Left"
                                        Background="#4CAF50"
                                        CornerRadius="2"/>
                                <ContentPresenter x:Name="contentPresenter"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="background" Property="Background" Value="#4FFFFFFF"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="contentPresenter" Property="RenderTransform">
                                        <Setter.Value>
                                            <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Window Control Button Style -->
            <Style x:Key="WindowControlButton" TargetType="Button">
                <Setter Property="Width" Value="46"/>
                <Setter Property="Height" Value="32"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ContentPresenter x:Name="contentPresenter"
                                                HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#3FFFFFFF"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="contentPresenter" Property="RenderTransform">
                                        <Setter.Value>
                                            <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Window Close Button Style -->
            <Style x:Key="WindowCloseButton" TargetType="Button" BasedOn="{StaticResource WindowControlButton}">
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#E81123"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- DataGrid Styles -->
            <Style x:Key="DataGridStyle" TargetType="DataGrid">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="#DDD"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="RowHeaderWidth" Value="0"/>
                <Setter Property="HorizontalGridLinesBrush" Value="#EEE"/>
                <Setter Property="VerticalGridLinesBrush" Value="#EEE"/>
            </Style>

            <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="White"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#F5F5F5"/>
                    </Trigger>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="#E3F2FD"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="DefaultParagraphStyle" TargetType="Paragraph">
                <Setter Property="Margin" Value="0 2"/>
            </Style>

            <Style x:Key="DefaultTableStyle" TargetType="Table">
                <Setter Property="Margin" Value="0 10"/>
            </Style>
           
            <Style x:Key="ProductCardStyle" TargetType="Border">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="4" Opacity="0.2" ShadowDepth="2"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#F5F5F5"/>
                        <Setter Property="BorderBrush" Value="#4CAF50"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Enums -->
            <ObjectDataProvider x:Key="ExpenseCategoryEnum" 
                              MethodName="GetValues" 
                              ObjectType="{x:Type System:Enum}">
                <ObjectDataProvider.MethodParameters>
                    <x:Type TypeName="models:ExpenseCategory"/>
                </ObjectDataProvider.MethodParameters>
            </ObjectDataProvider>

            <ObjectDataProvider x:Key="ExpenseFrequencyEnum" 
                              MethodName="GetValues" 
                              ObjectType="{x:Type System:Enum}">
                <ObjectDataProvider.MethodParameters>
                    <x:Type TypeName="models:ExpenseFrequency"/>
                </ObjectDataProvider.MethodParameters>
            </ObjectDataProvider>

            <converters:ReportTypeTranslationConverter x:Key="ReportTypeTranslationConverter"/>
            <converters:NumberFormatConverter x:Key="NumberFormatConverter"/>
            <converters:EnumToResourceConverter x:Key="EnumToResourceConverter"/>

            <!-- Dashboard Resources -->
            <System:String x:Key="DashboardTitle">Dashboard</System:String>

            <!-- Common Resources -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
     
</Application>
