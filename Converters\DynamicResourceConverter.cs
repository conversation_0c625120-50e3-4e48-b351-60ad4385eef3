using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    /// <summary>
    /// Converter that looks up a dynamic resource by key
    /// </summary>
    public class DynamicResourceConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Check if value is a resource key
            if (value == null) return null;
            
            var key = value.ToString();
            
            // Try to find the resource
            var resource = Application.Current.TryFindResource(key);
            
            // Return the resource if found, otherwise return the original value
            return resource ?? key;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 