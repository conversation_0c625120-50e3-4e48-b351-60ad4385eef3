using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.ViewModels;
using POSSystem.Services;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace POSSystem.Tests.UserExperience
{
    /// <summary>
    /// ✅ NEW: User experience validation tests for dashboard optimization
    /// Tests responsiveness, progressive loading, and real-world usage scenarios
    /// </summary>
    [TestClass]
    public class DashboardUXTests
    {
        private POSDbContext _context;
        private DatabaseService _databaseService;
        private DashboardViewModel _dashboardViewModel;

        [TestInitialize]
        public void Setup()
        {
            var options = new DbContextOptionsBuilder<POSDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new POSDbContext(options);
            _databaseService = new DatabaseService(_context);
            _dashboardViewModel = new DashboardViewModel(_databaseService);
            
            SeedRealisticTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _dashboardViewModel?.Dispose();
            _context?.Dispose();
        }

        /// <summary>
        /// ✅ UX TEST: Dashboard shows initial data within 500ms (perceived performance)
        /// </summary>
        [TestMethod]
        public async Task Dashboard_InitialLoad_ShowsDataWithin500ms()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();
            var initialDataShown = false;

            // Monitor when initial data becomes available
            _dashboardViewModel.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(_dashboardViewModel.TodaySales) && 
                    _dashboardViewModel.TodaySales > 0 && 
                    !initialDataShown)
                {
                    initialDataShown = true;
                    stopwatch.Stop();
                }
            };

            // Act
            await _dashboardViewModel.LoadDashboardDataAsync();

            // Assert
            Assert.IsTrue(initialDataShown, "Initial data was not shown");
            Assert.IsTrue(stopwatch.ElapsedMilliseconds <= 500, 
                $"Initial data took {stopwatch.ElapsedMilliseconds}ms to show, expected <= 500ms");

            Debug.WriteLine($"✅ Initial data shown in {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// ✅ UX TEST: Progressive loading provides smooth user experience
        /// </summary>
        [TestMethod]
        public async Task Dashboard_ProgressiveLoading_SmoothExperience()
        {
            // Arrange
            var loadingStages = new List<LoadingStage>();
            var stopwatch = Stopwatch.StartNew();

            // Monitor loading stages
            _dashboardViewModel.PropertyChanged += (s, e) =>
            {
                var stage = new LoadingStage
                {
                    PropertyName = e.PropertyName,
                    Timestamp = stopwatch.ElapsedMilliseconds,
                    IsLoading = _dashboardViewModel.IsLoading
                };
                loadingStages.Add(stage);
            };

            // Act
            await _dashboardViewModel.LoadDashboardDataAsync();
            stopwatch.Stop();

            // Assert - Check that loading happens in stages
            var essentialDataStage = loadingStages.FirstOrDefault(s => s.PropertyName == nameof(_dashboardViewModel.TodaySales));
            var chartDataStage = loadingStages.FirstOrDefault(s => s.PropertyName == nameof(_dashboardViewModel.SalesTrendSeries));
            var detailedDataStage = loadingStages.LastOrDefault(s => !s.IsLoading);

            Assert.IsNotNull(essentialDataStage, "Essential data stage not found");
            
            if (chartDataStage != null)
            {
                Assert.IsTrue(chartDataStage.Timestamp > essentialDataStage.Timestamp, 
                    "Chart data should load after essential data");
            }

            Debug.WriteLine($"✅ Progressive loading stages: Essential({essentialDataStage?.Timestamp}ms), Charts({chartDataStage?.Timestamp}ms), Complete({detailedDataStage?.Timestamp}ms)");
        }

        /// <summary>
        /// ✅ UX TEST: Dashboard remains responsive during data loading
        /// </summary>
        [TestMethod]
        public async Task Dashboard_RemainsResponsive_DuringLoading()
        {
            // Arrange
            var responsivenessTimes = new List<long>();
            var loadingTask = _dashboardViewModel.LoadDashboardDataAsync();

            // Act - Test responsiveness during loading
            for (int i = 0; i < 5; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                
                // Simulate user interaction (property access)
                var todaySales = _dashboardViewModel.TodaySales;
                var isLoading = _dashboardViewModel.IsLoading;
                var startDate = _dashboardViewModel.StartDate;
                
                stopwatch.Stop();
                responsivenessTimes.Add(stopwatch.ElapsedMilliseconds);
                
                await Task.Delay(50); // Small delay between checks
            }

            await loadingTask;

            // Assert - UI should remain responsive (< 50ms response time)
            var maxResponseTime = responsivenessTimes.Max();
            var avgResponseTime = responsivenessTimes.Average();

            Assert.IsTrue(maxResponseTime < 50, 
                $"Max response time {maxResponseTime}ms during loading, expected < 50ms");
            
            Assert.IsTrue(avgResponseTime < 20, 
                $"Average response time {avgResponseTime:F1}ms during loading, expected < 20ms");

            Debug.WriteLine($"✅ Responsiveness during loading: Max {maxResponseTime}ms, Avg {avgResponseTime:F1}ms");
        }

        /// <summary>
        /// ✅ UX TEST: Date range changes provide immediate feedback
        /// </summary>
        [TestMethod]
        public async Task Dashboard_DateRangeChange_ImmediateFeedback()
        {
            // Arrange
            await _dashboardViewModel.LoadDashboardDataAsync(); // Initial load
            var feedbackTimes = new List<long>();

            // Act - Test multiple date range changes
            var dateRanges = new[]
            {
                (DateTime.Today.AddDays(-7), DateTime.Today),
                (DateTime.Today.AddDays(-30), DateTime.Today),
                (DateTime.Today.AddDays(-90), DateTime.Today)
            };

            foreach (var (startDate, endDate) in dateRanges)
            {
                var stopwatch = Stopwatch.StartNew();
                var feedbackReceived = false;

                _dashboardViewModel.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(_dashboardViewModel.IsLoading) && !feedbackReceived)
                    {
                        feedbackReceived = true;
                        stopwatch.Stop();
                    }
                };

                _dashboardViewModel.StartDate = startDate;
                _dashboardViewModel.EndDate = endDate;

                // Wait for feedback or timeout
                var timeout = Task.Delay(1000);
                var feedbackTask = Task.Run(async () =>
                {
                    while (!feedbackReceived && !timeout.IsCompleted)
                    {
                        await Task.Delay(10);
                    }
                });

                await Task.WhenAny(feedbackTask, timeout);
                
                if (feedbackReceived)
                {
                    feedbackTimes.Add(stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    feedbackTimes.Add(1000); // Timeout
                }
            }

            // Assert
            var maxFeedbackTime = feedbackTimes.Max();
            var avgFeedbackTime = feedbackTimes.Average();

            Assert.IsTrue(maxFeedbackTime < 200, 
                $"Max feedback time {maxFeedbackTime}ms, expected < 200ms");
            
            Assert.IsTrue(avgFeedbackTime < 100, 
                $"Average feedback time {avgFeedbackTime:F1}ms, expected < 100ms");

            Debug.WriteLine($"✅ Date range feedback: Max {maxFeedbackTime}ms, Avg {avgFeedbackTime:F1}ms");
        }

        /// <summary>
        /// ✅ UX TEST: Error handling doesn't break user experience
        /// </summary>
        [TestMethod]
        public async Task Dashboard_ErrorHandling_MaintainsUX()
        {
            // Arrange - Create a scenario that might cause errors
            var errorCount = 0;
            var recoveryTime = TimeSpan.Zero;

            // Simulate error conditions
            _dashboardViewModel.StartDate = DateTime.Today.AddYears(-10); // Very old date
            _dashboardViewModel.EndDate = DateTime.Today.AddYears(1); // Future date

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Act
                await _dashboardViewModel.LoadDashboardDataAsync();
            }
            catch (Exception)
            {
                errorCount++;
            }

            stopwatch.Stop();
            recoveryTime = stopwatch.Elapsed;

            // Assert - Dashboard should handle errors gracefully
            Assert.IsTrue(errorCount <= 1, "Too many unhandled errors");
            Assert.IsTrue(recoveryTime.TotalSeconds < 5, 
                $"Error recovery took {recoveryTime.TotalSeconds:F1}s, expected < 5s");
            
            // Dashboard should still be usable
            Assert.IsFalse(_dashboardViewModel.IsLoading, "Dashboard stuck in loading state after error");

            Debug.WriteLine($"✅ Error handling: {errorCount} errors, recovery in {recoveryTime.TotalSeconds:F1}s");
        }

        /// <summary>
        /// ✅ UX TEST: Real-world usage scenario simulation
        /// </summary>
        [TestMethod]
        public async Task Dashboard_RealWorldUsage_PerformanceAcceptable()
        {
            // Arrange - Simulate typical user workflow
            var workflowSteps = new List<WorkflowStep>();

            // Act - Simulate real user interactions
            var stopwatch = Stopwatch.StartNew();

            // Step 1: Initial dashboard load (user opens dashboard)
            var step1Start = stopwatch.ElapsedMilliseconds;
            await _dashboardViewModel.LoadDashboardDataAsync();
            workflowSteps.Add(new WorkflowStep("Initial Load", stopwatch.ElapsedMilliseconds - step1Start));

            // Step 2: Check today's performance
            var step2Start = stopwatch.ElapsedMilliseconds;
            _dashboardViewModel.StartDate = DateTime.Today;
            _dashboardViewModel.EndDate = DateTime.Now;
            await Task.Delay(100); // Simulate user reading data
            workflowSteps.Add(new WorkflowStep("Today's Data", stopwatch.ElapsedMilliseconds - step2Start));

            // Step 3: Compare with last week
            var step3Start = stopwatch.ElapsedMilliseconds;
            _dashboardViewModel.StartDate = DateTime.Today.AddDays(-7);
            _dashboardViewModel.EndDate = DateTime.Today;
            await Task.Delay(100);
            workflowSteps.Add(new WorkflowStep("Week Comparison", stopwatch.ElapsedMilliseconds - step3Start));

            // Step 4: Check monthly trends
            var step4Start = stopwatch.ElapsedMilliseconds;
            _dashboardViewModel.StartDate = DateTime.Today.AddDays(-30);
            _dashboardViewModel.EndDate = DateTime.Today;
            await _dashboardViewModel.LoadSalesTrendDataAsync();
            workflowSteps.Add(new WorkflowStep("Monthly Trends", stopwatch.ElapsedMilliseconds - step4Start));

            // Step 5: Review product performance
            var step5Start = stopwatch.ElapsedMilliseconds;
            await _dashboardViewModel.LoadProductPerformanceAsync();
            workflowSteps.Add(new WorkflowStep("Product Performance", stopwatch.ElapsedMilliseconds - step5Start));

            stopwatch.Stop();

            // Assert - Each step should complete within reasonable time
            foreach (var step in workflowSteps)
            {
                var expectedTime = step.Name switch
                {
                    "Initial Load" => 2000,
                    "Today's Data" => 500,
                    "Week Comparison" => 500,
                    "Monthly Trends" => 1000,
                    "Product Performance" => 1000,
                    _ => 1000
                };

                Assert.IsTrue(step.Duration <= expectedTime, 
                    $"{step.Name} took {step.Duration}ms, expected <= {expectedTime}ms");
            }

            var totalTime = workflowSteps.Sum(s => s.Duration);
            Assert.IsTrue(totalTime <= 5000, 
                $"Total workflow took {totalTime}ms, expected <= 5000ms");

            Debug.WriteLine($"✅ Real-world workflow completed in {totalTime}ms:");
            foreach (var step in workflowSteps)
            {
                Debug.WriteLine($"   {step.Name}: {step.Duration}ms");
            }
        }

        /// <summary>
        /// ✅ UX TEST: Dashboard handles concurrent user interactions
        /// </summary>
        [TestMethod]
        public async Task Dashboard_ConcurrentInteractions_HandledGracefully()
        {
            // Arrange
            var interactionTasks = new List<Task>();
            var interactionResults = new List<InteractionResult>();

            // Act - Simulate concurrent user interactions
            for (int i = 0; i < 5; i++)
            {
                var interactionId = i;
                interactionTasks.Add(Task.Run(async () =>
                {
                    var stopwatch = Stopwatch.StartNew();
                    try
                    {
                        // Simulate different user actions
                        switch (interactionId % 3)
                        {
                            case 0:
                                _dashboardViewModel.StartDate = DateTime.Today.AddDays(-interactionId * 7);
                                break;
                            case 1:
                                await _dashboardViewModel.LoadSalesTrendDataAsync();
                                break;
                            case 2:
                                await _dashboardViewModel.LoadProductPerformanceAsync();
                                break;
                        }
                        
                        stopwatch.Stop();
                        interactionResults.Add(new InteractionResult
                        {
                            InteractionId = interactionId,
                            Duration = stopwatch.ElapsedMilliseconds,
                            Success = true
                        });
                    }
                    catch (Exception ex)
                    {
                        stopwatch.Stop();
                        interactionResults.Add(new InteractionResult
                        {
                            InteractionId = interactionId,
                            Duration = stopwatch.ElapsedMilliseconds,
                            Success = false,
                            Error = ex.Message
                        });
                    }
                }));
            }

            await Task.WhenAll(interactionTasks);

            // Assert
            var successfulInteractions = interactionResults.Count(r => r.Success);
            var averageDuration = interactionResults.Average(r => r.Duration);
            var maxDuration = interactionResults.Max(r => r.Duration);

            Assert.IsTrue(successfulInteractions >= 4, 
                $"Only {successfulInteractions}/5 interactions succeeded");
            
            Assert.IsTrue(averageDuration < 2000, 
                $"Average interaction time {averageDuration:F1}ms, expected < 2000ms");
            
            Assert.IsTrue(maxDuration < 5000, 
                $"Max interaction time {maxDuration}ms, expected < 5000ms");

            Debug.WriteLine($"✅ Concurrent interactions: {successfulInteractions}/5 successful, avg {averageDuration:F1}ms, max {maxDuration}ms");
        }

        /// <summary>
        /// ✅ HELPER: Seed realistic test data
        /// </summary>
        private void SeedRealisticTestData()
        {
            var random = new Random(42);

            // Add products
            for (int i = 1; i <= 100; i++)
            {
                _context.Products.Add(new Models.Product
                {
                    Id = i,
                    Name = $"Product {i}",
                    SKU = $"SKU{i:D3}",
                    SellingPrice = 10 + (i % 50),
                    PurchasePrice = 5 + (i % 25),
                    StockQuantity = 100 - (i % 20),
                    IsActive = true
                });
            }

            // Add sales with realistic patterns
            for (int i = 1; i <= 500; i++)
            {
                var daysAgo = random.Next(0, 90);
                var saleDate = DateTime.Today.AddDays(-daysAgo);
                
                _context.Sales.Add(new Models.Sale
                {
                    Id = i,
                    SaleDate = saleDate,
                    GrandTotal = 25 + random.Next(0, 200),
                    Subtotal = 20 + random.Next(0, 180),
                    Status = "Completed",
                    PaymentStatus = random.Next(0, 10) > 8 ? "Unpaid" : "Paid"
                });
            }

            _context.SaveChanges();
        }
    }

    /// <summary>
    /// ✅ HELPER: Loading stage tracking
    /// </summary>
    public class LoadingStage
    {
        public string PropertyName { get; set; }
        public long Timestamp { get; set; }
        public bool IsLoading { get; set; }
    }

    /// <summary>
    /// ✅ HELPER: Workflow step tracking
    /// </summary>
    public class WorkflowStep
    {
        public string Name { get; set; }
        public long Duration { get; set; }

        public WorkflowStep(string name, long duration)
        {
            Name = name;
            Duration = duration;
        }
    }

    /// <summary>
    /// ✅ HELPER: Interaction result tracking
    /// </summary>
    public class InteractionResult
    {
        public int InteractionId { get; set; }
        public long Duration { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }
}
