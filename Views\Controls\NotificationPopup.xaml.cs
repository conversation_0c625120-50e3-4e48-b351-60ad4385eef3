using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media.Animation;
using Microsoft.Extensions.DependencyInjection;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Data;
using POSSystem.Models;

namespace POSSystem.Views.Controls
{
    public partial class NotificationPopup : UserControl
    {
        private Storyboard _showStoryboard;
        private Storyboard _hideStoryboard;
        private readonly IAlertService _alertService;
        private Popup _popup;
        private Grid _container;

        public event EventHandler NotificationCountChanged;
        public event EventHandler MarkAllAsReadClicked;
        public event EventHandler ViewAllClicked;

        public static readonly DependencyProperty IsOpenProperty =
            DependencyProperty.Register(nameof(IsOpen), typeof(bool), typeof(NotificationPopup),
                new PropertyMetadata(false, OnIsOpenChanged));

        public bool IsOpen
        {
            get => (bool)GetValue(IsOpenProperty);
            set => SetValue(IsOpenProperty, value);
        }

        private static void OnIsOpenChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is NotificationPopup popup)
            {
                if ((bool)e.NewValue)
                    popup.Show();
                else
                    popup.Hide();
            }
        }

        public static readonly DependencyProperty PlacementTargetProperty =
            DependencyProperty.Register(nameof(PlacementTarget), typeof(UIElement), typeof(NotificationPopup),
                new PropertyMetadata(null, OnPlacementTargetChanged));

        public UIElement PlacementTarget
        {
            get => (UIElement)GetValue(PlacementTargetProperty);
            set => SetValue(PlacementTargetProperty, value);
        }

        private static void OnPlacementTargetChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is NotificationPopup popup && popup._popup != null)
            {
                popup._popup.PlacementTarget = e.NewValue as UIElement;
            }
        }

        public NotificationPopup()
        {
            try
            {
                InitializeComponent();

                // Try to get AlertService from DI container, fallback to null if not available
                // This might not be available during XAML parsing, so we handle it gracefully
                try
                {
                    if (App.ServiceProvider != null)
                    {
                        _alertService = App.ServiceProvider.GetService<IAlertService>();
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Warning: App.ServiceProvider is null during NotificationPopup construction");
                        _alertService = null;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Could not get AlertService from DI container: {ex.Message}");
                    _alertService = null;
                }

                Initialize();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in NotificationPopup constructor: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Set minimal state to prevent further errors
                _alertService = null;
                _showStoryboard = null;
                _hideStoryboard = null;

                // Don't rethrow - allow the control to be created even if initialization fails
            }
        }

        public NotificationPopup(IAlertService alertService)
        {
            InitializeComponent();
            _alertService = alertService;
            Initialize();
        }

        private void Initialize()
        {
            try
            {
                _popup = PART_Popup;
                _container = PART_Container;

                // Safely load storyboard resources
                try
                {
                    _showStoryboard = (Storyboard)FindResource("ShowPopup");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Could not load ShowPopup storyboard: {ex.Message}");
                    _showStoryboard = null;
                }

                try
                {
                    _hideStoryboard = (Storyboard)FindResource("HidePopup");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Could not load HidePopup storyboard: {ex.Message}");
                    _hideStoryboard = null;
                }

                if (_popup != null)
                {
                    _popup.CustomPopupPlacementCallback = new CustomPopupPlacementCallback(PlacePopup);

                    _popup.Opened += (s, e) =>
                    {
                        if (_container != null)
                        {
                            try
                            {
                                Mouse.Capture(_container, CaptureMode.SubTree);
                                _showStoryboard?.Begin(_container);
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Warning: Error in popup opened event: {ex.Message}");
                            }
                        }
                    };

                    _popup.Closed += (s, e) =>
                    {
                        try
                        {
                            Mouse.Capture(null);
                            IsOpen = false;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Warning: Error in popup closed event: {ex.Message}");
                        }
                    };
                }

                this.AddHandler(MouseDownEvent, new MouseButtonEventHandler((s, e) =>
                {
                    try
                    {
                        if (_container != null && !IsMouseOverPopup(e.GetPosition(_container)))
                        {
                            Hide();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Warning: Error in mouse down handler: {ex.Message}");
                    }
                }), true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing NotificationPopup: {ex.Message}");
                // Don't rethrow - allow the control to be created even if initialization partially fails
            }
        }

        private CustomPopupPlacement[] PlacePopup(Size popupSize, Size targetSize, Point offset)
        {
            return new[]
            {
                new CustomPopupPlacement(
                    new Point(targetSize.Width - popupSize.Width, targetSize.Height + 8),
                    PopupPrimaryAxis.Horizontal)
            };
        }

        private bool IsMouseOverPopup(Point mousePosition)
        {
            if (_container == null) return false;
            var bounds = new Rect(new Point(0, 0), _container.RenderSize);
            return bounds.Contains(mousePosition);
        }

        public void Show()
        {
            try
            {
                if (_popup != null)
                {
                    _popup.IsOpen = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing NotificationPopup: {ex.Message}");
            }
        }

        public void Hide()
        {
            try
            {
                if (_popup != null)
                {
                    if (_container != null && _hideStoryboard != null)
                    {
                        _hideStoryboard.Completed += (s, e) =>
                        {
                            try
                            {
                                _popup.IsOpen = false;
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Error closing popup: {ex.Message}");
                            }
                        };
                        _hideStoryboard.Begin(_container);
                    }
                    else
                    {
                        // Fallback: close popup directly if storyboard is not available
                        _popup.IsOpen = false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error hiding NotificationPopup: {ex.Message}");
            }
        }

        public void SetNotifications(List<Notification> notifications)
        {
            try
            {
                if (_container != null)
                {
                    _container.DataContext = notifications;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting notifications: {ex.Message}");
            }
        }

        private void MarkAllAsRead_Click(object sender, RoutedEventArgs e)
        {
            MarkAllAsReadClicked?.Invoke(this, EventArgs.Empty);
            Hide();
        }

        private void ViewAll_Click(object sender, RoutedEventArgs e)
        {
            ViewAllClicked?.Invoke(this, EventArgs.Empty);
            Hide();
        }

        private void UpdateNotificationCount()
        {
            try
            {
                if (_alertService != null)
                {
                    var unreadCount = _alertService.GetUnreadAlerts().Count;
                    if (unreadCount == 0)
                    {
                        Hide();
                    }
                }
                NotificationCountChanged?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating notification count in NotificationPopup: {ex.Message}");
            }
        }
    }
} 