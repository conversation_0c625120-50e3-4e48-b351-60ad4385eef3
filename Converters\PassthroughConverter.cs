using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    /// <summary>
    /// Passes through the values from a multi-binding to a command parameter
    /// </summary>
    public class PassthroughConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            return values.Clone();
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 