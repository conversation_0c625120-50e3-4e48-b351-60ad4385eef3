<Window x:Class="POSSystem.Views.BatchStockWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{DynamicResource ManageBatchStock}"
        MinWidth="1000"
        MinHeight="600"
        Width="1200"
        Height="750"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        Style="{StaticResource AppWindowStyle}">

    <!-- DialogHost for handling nested dialogs -->
    <md:DialogHost Identifier="BatchStockDialog" Margin="0">
        <!-- Main Dialog Card with Enhanced Styling -->
        <md:Card x:Name="MainDialogCard"
                 Background="{DynamicResource MaterialDesignPaper}"
                 Foreground="{DynamicResource MaterialDesignBody}"
                 md:ElevationAssist.Elevation="Dp6"
                 Margin="20"
                 MaxWidth="1600"
                 MaxHeight="1200">

            <Grid Margin="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header Section with Gradient Background -->
                <Border Grid.Row="0"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        CornerRadius="4,4,0,0">
                    <Grid Margin="24,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{Binding Product.Name}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"/>
                            <TextBlock Text="{DynamicResource BatchStockManagement}"
                                      FontSize="16"
                                      Foreground="White"
                                      Opacity="0.9"/>
                        </StackPanel>

                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Command="{x:Static md:DialogHost.CloseDialogCommand}"
                                Foreground="White"
                                ToolTip="{DynamicResource Close}">
                            <md:PackIcon Kind="Close" />
                        </Button>
                    </Grid>
                </Border>

                <!-- Statistics Cards Section -->
                <Grid Grid.Row="1" Margin="24,16,24,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Total Stock Card -->
                    <md:Card Grid.Column="0"
                             Margin="0,0,8,0"
                             md:ElevationAssist.Elevation="Dp2"
                             Background="{DynamicResource MaterialDesignCardBackground}">
                        <Border Padding="16">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource TotalStock}"
                                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="{Binding TotalStock}"
                                          FontSize="28"
                                          FontWeight="Bold"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            </StackPanel>
                        </Border>
                    </md:Card>

                    <!-- Average Cost Card -->
                    <md:Card Grid.Column="1"
                             Margin="8,0"
                             md:ElevationAssist.Elevation="Dp2"
                             Background="{DynamicResource MaterialDesignCardBackground}">
                        <Border Padding="16">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource AverageCost}"
                                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="{Binding AverageCost, StringFormat=C2}"
                                          FontSize="28"
                                          FontWeight="Bold"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            </StackPanel>
                        </Border>
                    </md:Card>

                    <!-- Expiring Soon Card -->
                    <md:Card Grid.Column="2"
                             Margin="8,0,0,0"
                             md:ElevationAssist.Elevation="Dp2"
                             Background="{DynamicResource MaterialDesignCardBackground}">
                        <Border Padding="16">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource ExpiringSoon}"
                                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="{Binding ExpiringSoon}"
                                          FontSize="28"
                                          FontWeight="Bold"
                                          Foreground="#FF6B47"/>
                            </StackPanel>
                        </Border>
                    </md:Card>
                </Grid>

                <!-- Batch List Section -->
                <md:Card Grid.Row="2"
                         Margin="24,0,24,16"
                         md:ElevationAssist.Elevation="Dp2"
                         Background="{DynamicResource MaterialDesignCardBackground}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Card Header -->
                        <Border Grid.Row="0"
                                Background="{DynamicResource MaterialDesignDivider}"
                                Padding="16,12">
                            <TextBlock Text="{DynamicResource BatchList}"
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                      FontWeight="SemiBold"/>
                        </Border>

                        <!-- DataGrid with Modern Styling -->
                        <DataGrid Grid.Row="1"
                                  ItemsSource="{Binding Batches}"
                                  x:Name="BatchesList"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  Margin="16"
                                  Style="{StaticResource MaterialDesignDataGrid}"
                                  ColumnHeaderStyle="{StaticResource MaterialDesignDataGridColumnHeader}"
                                  CellStyle="{StaticResource MaterialDesignDataGridCell}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource BatchNumber}"
                                                   Binding="{Binding BatchNumber}"
                                                   Width="150"/>
                                <DataGridTextColumn Header="{DynamicResource Quantity}"
                                                   Binding="{Binding Quantity}"
                                                   Width="100"/>
                                <DataGridTextColumn Header="{DynamicResource PurchasePrice}"
                                                   Binding="{Binding PurchasePrice, StringFormat=C2}"
                                                   Width="120"/>
                                <DataGridTextColumn Header="{DynamicResource SellingPrice}"
                                                   Binding="{Binding SellingPrice, StringFormat=C2}"
                                                   Width="120"/>
                                <DataGridTextColumn Header="{DynamicResource ManufacturingDate}"
                                                   Binding="{Binding ManufactureDate, StringFormat=d}"
                                                   Width="120"/>
                                <DataGridTextColumn Header="{DynamicResource ExpiryDate}"
                                                   Binding="{Binding ExpiryDate, StringFormat=d}"
                                                   Width="120"/>
                                <DataGridTextColumn Header="{DynamicResource Location}"
                                                   Binding="{Binding Location}"
                                                   Width="120"/>
                                <DataGridTextColumn Header="{DynamicResource Created}"
                                                   Binding="{Binding CreatedAt, StringFormat=d}"
                                                   Width="100"/>
                                <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="160">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Content="{DynamicResource Edit}"
                                                        Click="EditBatch_Click"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Height="32"
                                                        Margin="0,0,8,0"
                                                        Padding="12,0"/>
                                                <Button Content="{DynamicResource AddStock}"
                                                        Click="AddStock_Click"
                                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                                        Height="32"
                                                        Padding="12,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </md:Card>

                <!-- Action Buttons Section -->
                <Border Grid.Row="3"
                        Background="{DynamicResource MaterialDesignDivider}"
                        Padding="24,16">
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Right">
                        <Button Click="AddBatch_Click"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="White"
                                Height="40"
                                Padding="24,0"
                                Margin="0,0,16,0"
                                md:ButtonAssist.CornerRadius="4">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Plus"
                                            VerticalAlignment="Center"
                                            Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource AddNewBatch}"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Content="{DynamicResource Close}"
                                Click="Close_Click"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Height="40"
                                Padding="24,0"
                                md:ButtonAssist.CornerRadius="4"/>
                    </StackPanel>
                </Border>
            </Grid>
        </md:Card>
    </md:DialogHost>
</Window>