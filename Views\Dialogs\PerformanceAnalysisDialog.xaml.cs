using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using POSSystem.Services.Performance;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Performance analysis dialog for SaleViewGrid component
    /// </summary>
    public partial class PerformanceAnalysisDialog : Window
    {
        private readonly Views.Layouts.SalesViewGrid _gridView;
        private PerformanceAnalysisReport _currentReport;

        public PerformanceAnalysisDialog(Views.Layouts.SalesViewGrid gridView)
        {
            InitializeComponent();
            _gridView = gridView ?? throw new ArgumentNullException(nameof(gridView));
            
            // Initialize UI
            UpdateUI(null);
        }

        /// <summary>
        /// Run performance analysis
        /// </summary>
        private async void RunAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading overlay
                LoadingOverlay.Visibility = Visibility.Visible;
                RunAnalysisButton.IsEnabled = false;

                Debug.WriteLine("[PERFORMANCE DIALOG] Starting performance analysis...");

                // Run the analysis
                _currentReport = await _gridView.RunPerformanceAnalysisAsync();

                // Update UI with results
                UpdateUI(_currentReport);

                // Enable save button
                SaveReportButton.IsEnabled = true;

                Debug.WriteLine("[PERFORMANCE DIALOG] Performance analysis completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERFORMANCE DIALOG] Analysis failed: {ex.Message}");
                MessageBox.Show($"Performance analysis failed: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Hide loading overlay
                LoadingOverlay.Visibility = Visibility.Collapsed;
                RunAnalysisButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Save performance report to file
        /// </summary>
        private void SaveReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentReport == null) return;

            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "Save Performance Report",
                    Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                    DefaultExt = "txt",
                    FileName = $"SaleViewGrid_Performance_Report_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var testRunner = new PerformanceTestRunner();
                    // Note: We would need to expose the report generation method or create the content here
                    File.WriteAllText(saveDialog.FileName, GenerateReportSummary(_currentReport));
                    
                    MessageBox.Show($"Performance report saved to:\n{saveDialog.FileName}", "Report Saved", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERFORMANCE DIALOG] Failed to save report: {ex.Message}");
                MessageBox.Show($"Failed to save report: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// Update UI with performance analysis results
        /// </summary>
        private void UpdateUI(PerformanceAnalysisReport report)
        {
            if (report == null)
            {
                // Reset UI to initial state
                ScoreText.Text = "--/100";
                GradeText.Text = "--";
                LoadTimeGrid.ItemsSource = null;
                GridLayoutGrid.ItemsSource = null;
                MemoryUsageText.Text = "No data available";
                ResponsivenessText.Text = "No data available";
                VirtualizationText.Text = "No data available";
                MemoryLeakText.Text = "No data available";
                RecommendationsList.ItemsSource = null;
                return;
            }

            // Update score and grade
            var score = report.CalculateOverallScore();
            var grade = report.GetPerformanceGrade();
            
            ScoreText.Text = $"{score:F1}/100";
            GradeText.Text = grade;

            // Color code the score
            ScoreText.Foreground = GetScoreBrush(score);
            GradeText.Foreground = GetGradeBrush(grade);

            // Update data grids
            LoadTimeGrid.ItemsSource = report.LoadTimeTests;
            GridLayoutGrid.ItemsSource = report.GridLayoutTests;

            // Update performance metrics
            if (report.MemoryUsageTest != null)
            {
                MemoryUsageText.Text = $"{report.MemoryUsageTest.MemoryEfficiency}\n" +
                                      $"Peak: {report.MemoryUsageTest.PeakMemory:F1} MB\n" +
                                      $"Leak Indicator: {report.MemoryUsageTest.MemoryLeakIndicator:F1} MB";
            }

            if (report.UIResponsivenessTest != null)
            {
                ResponsivenessText.Text = $"{report.UIResponsivenessTest.ResponsivenessRating}\n" +
                                         $"Resize: {report.UIResponsivenessTest.ResizeResponseTime} ms\n" +
                                         $"Scroll: {report.UIResponsivenessTest.ScrollPerformance} ms";
            }

            if (report.VirtualizationTest != null)
            {
                VirtualizationText.Text = $"{report.VirtualizationTest.VirtualizationEfficiency}\n" +
                                         $"Realized: {report.VirtualizationTest.RealizedContainers:N0}/{report.VirtualizationTest.TotalItems:N0}\n" +
                                         $"Ratio: {report.VirtualizationTest.VirtualizationRatio:P1}";
            }

            if (report.MemoryLeakTest != null)
            {
                MemoryLeakText.Text = $"{report.MemoryLeakTest.LeakRisk} Risk\n" +
                                     $"Growth: {report.MemoryLeakTest.MemoryGrowth:F1} MB\n" +
                                     $"Over 5 cycles";
            }

            // Update recommendations
            var allRecommendations = report.Recommendations.Concat(report.Warnings.Select(w => $"⚠️ {w}")).ToList();
            RecommendationsList.ItemsSource = allRecommendations;
        }

        /// <summary>
        /// Get brush color for performance score
        /// </summary>
        private Brush GetScoreBrush(double score)
        {
            if (score >= 90) return new SolidColorBrush(Colors.Green);
            if (score >= 80) return new SolidColorBrush(Colors.Orange);
            if (score >= 70) return new SolidColorBrush(Colors.DarkOrange);
            return new SolidColorBrush(Colors.Red);
        }

        /// <summary>
        /// Get brush color for performance grade
        /// </summary>
        private Brush GetGradeBrush(string grade)
        {
            return grade switch
            {
                "A" => new SolidColorBrush(Colors.Green),
                "B" => new SolidColorBrush(Colors.Orange),
                "C" => new SolidColorBrush(Colors.DarkOrange),
                _ => new SolidColorBrush(Colors.Red)
            };
        }

        /// <summary>
        /// Generate a summary report for saving
        /// </summary>
        private string GenerateReportSummary(PerformanceAnalysisReport report)
        {
            var summary = $@"SaleViewGrid Performance Analysis Report
Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

OVERALL PERFORMANCE
Score: {report.CalculateOverallScore():F1}/100
Grade: {report.GetPerformanceGrade()}

LOAD TIME PERFORMANCE
{string.Join("\n", report.LoadTimeTests.Select(t => 
    $"  {t.ProductCount} products: {t.TotalLoadTime}ms ({t.PerformanceRating})"))}

GRID LAYOUT OPTIMIZATION
{string.Join("\n", report.GridLayoutTests.Select(t => 
    $"  {t.Scenario}: {t.ActualColumns} columns, {t.SpacingRatio:F2}x spacing ({t.SpacingQuality})"))}

MEMORY & PERFORMANCE
Memory Efficiency: {report.MemoryUsageTest?.MemoryEfficiency ?? "N/A"}
UI Responsiveness: {report.UIResponsivenessTest?.ResponsivenessRating ?? "N/A"}
Virtualization: {report.VirtualizationTest?.VirtualizationEfficiency ?? "N/A"}
Memory Leak Risk: {report.MemoryLeakTest?.LeakRisk ?? "N/A"}

RECOMMENDATIONS
{string.Join("\n", report.Recommendations.Select(r => $"• {r}"))}

WARNINGS
{string.Join("\n", report.Warnings.Select(w => $"⚠️ {w}"))}
";
            return summary;
        }
    }
}
