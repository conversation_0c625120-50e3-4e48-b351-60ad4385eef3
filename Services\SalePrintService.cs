using POSSystem.Models;
using POSSystem.Services.Interfaces;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Xps.Packaging;
using System.IO;
using System.Windows.Xps;
using System.Linq;

namespace POSSystem.Services
{
    public class SalePrintService : ISalePrintService
    {
        public bool PrintSale(Sale sale)
        {
            try
            {
                if (sale == null)
                {
                    MessageBox.Show("No sale to print.", "Print Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // Create the print document
                var document = CreateSaleDocument(sale);
                
                // Show print dialog
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // Print the document
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, $"Sale {sale.InvoiceNumber}");
                    return true;
                }
                
                // User cancelled printing
                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing sale: {ex.Message}", "Print Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private FlowDocument CreateSaleDocument(Sale sale)
        {
            var document = new FlowDocument
            {
                PagePadding = new Thickness(50),
                FontFamily = new FontFamily("Arial"),
                FontSize = 12
            };

            // Add header
            var headerParagraph = new Paragraph
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center
            };
            headerParagraph.Inlines.Add(new Run("SALES RECEIPT"));
            document.Blocks.Add(headerParagraph);

            // Add sale information
            var saleParagraph = new Paragraph { Margin = new Thickness(0, 10, 0, 0) };
            saleParagraph.Inlines.Add(new Run($"Invoice Number: {sale.InvoiceNumber}") { FontWeight = FontWeights.Bold });
            saleParagraph.Inlines.Add(new LineBreak());
            saleParagraph.Inlines.Add(new Run($"Date: {sale.SaleDate:yyyy-MM-dd HH:mm}"));
            saleParagraph.Inlines.Add(new LineBreak());
            saleParagraph.Inlines.Add(new Run($"Status: {sale.PaymentStatus ?? "Unknown"}"));
            saleParagraph.Inlines.Add(new LineBreak());

            // Add customer info if available
            if (sale.Customer != null)
            {
                saleParagraph.Inlines.Add(new Run($"Customer: {sale.Customer.FirstName} {sale.Customer.LastName}"));
                saleParagraph.Inlines.Add(new LineBreak());
            }

            document.Blocks.Add(saleParagraph);

            // Add items table
            if (sale.Items != null && sale.Items.Any())
            {
                var itemsTable = new Table { CellSpacing = 0 };
                
                // Define columns
                itemsTable.Columns.Add(new TableColumn { Width = new GridLength(40) });    // #
                itemsTable.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) }); // Product
                itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) }); // Quantity
                itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) }); // Unit Price
                itemsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) }); // Total

                // Add table header
                var headerRow = new TableRow { Background = Brushes.LightGray };
                headerRow.Cells.Add(CreateHeaderCell("#"));
                headerRow.Cells.Add(CreateHeaderCell("Product"));
                headerRow.Cells.Add(CreateHeaderCell("Quantity"));
                headerRow.Cells.Add(CreateHeaderCell("Unit Price"));
                headerRow.Cells.Add(CreateHeaderCell("Total"));
                
                itemsTable.RowGroups.Add(new TableRowGroup());
                itemsTable.RowGroups[0].Rows.Add(headerRow);

                // Add items
                int itemNumber = 1;
                foreach (var item in sale.Items)
                {
                    var row = new TableRow();
                    row.Cells.Add(CreateCell(itemNumber.ToString()));
                    row.Cells.Add(CreateCell(item.Product?.Name ?? "Unknown Product"));
                    row.Cells.Add(CreateCell(item.Quantity.ToString()));
                    row.Cells.Add(CreateCell($"{item.UnitPrice:N2}"));
                    row.Cells.Add(CreateCell($"{item.Total:N2}"));
                    
                    itemsTable.RowGroups[0].Rows.Add(row);
                    itemNumber++;
                }

                document.Blocks.Add(itemsTable);
            }

            // Add totals
            var totalsParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 20, 0, 0)
            };
            
            totalsParagraph.Inlines.Add(new Run($"Subtotal: {sale.Subtotal:N2}"));
            totalsParagraph.Inlines.Add(new LineBreak());
            
            if (sale.DiscountAmount > 0)
            {
                totalsParagraph.Inlines.Add(new Run($"Discount: {sale.DiscountAmount:N2}"));
                totalsParagraph.Inlines.Add(new LineBreak());
            }
            
            if (sale.TaxAmount > 0)
            {
                totalsParagraph.Inlines.Add(new Run($"Tax: {sale.TaxAmount:N2}"));
                totalsParagraph.Inlines.Add(new LineBreak());
            }
            
            totalsParagraph.Inlines.Add(new Run($"Grand Total: {sale.GrandTotal:N2}") { FontWeight = FontWeights.Bold });
            totalsParagraph.Inlines.Add(new LineBreak());
            
            if (sale.AmountPaid > 0)
            {
                totalsParagraph.Inlines.Add(new Run($"Amount Paid: {sale.AmountPaid:N2}"));
                totalsParagraph.Inlines.Add(new LineBreak());
                
                decimal remainingAmount = sale.GrandTotal - sale.AmountPaid;
                if (remainingAmount > 0)
                {
                    totalsParagraph.Inlines.Add(new Run($"Balance Due: {remainingAmount:N2}") { FontWeight = FontWeights.Bold });
                    totalsParagraph.Inlines.Add(new LineBreak());
                }
            }
            
            document.Blocks.Add(totalsParagraph);

            // Add footer
            var footerParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 30, 0, 0)
            };
            footerParagraph.Inlines.Add(new Run("Thank you for your business!"));
            document.Blocks.Add(footerParagraph);

            return document;
        }

        private TableCell CreateHeaderCell(string text)
        {
            var cell = new TableCell
            {
                Padding = new Thickness(5),
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1)
            };
            
            var paragraph = new Paragraph
            {
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center
            };
            paragraph.Inlines.Add(new Run(text));
            cell.Blocks.Add(paragraph);
            
            return cell;
        }

        private TableCell CreateCell(string text)
        {
            var cell = new TableCell
            {
                Padding = new Thickness(5),
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1)
            };
            
            var paragraph = new Paragraph
            {
                TextAlignment = TextAlignment.Right
            };
            paragraph.Inlines.Add(new Run(text));
            cell.Blocks.Add(paragraph);
            
            return cell;
        }

        // Interface implementation methods
        public void PrintReceipt(Sale sale)
        {
            PrintSale(sale);
        }

        public async Task PrintReceiptAsync(Sale sale)
        {
            await Task.Run(() => PrintSale(sale));
        }

        public void PreviewReceipt(Sale sale)
        {
            // Simple preview implementation
            PrintSale(sale);
        }
    }
}