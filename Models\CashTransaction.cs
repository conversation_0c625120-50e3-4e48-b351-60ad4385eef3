using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    public class CashTransaction : INotifyPropertyChanged
    {
        private int _id;
        private int _cashDrawerId;
        private string _type; // "In" or "Out"
        private decimal _amount;
        private string? _reason;
        private string? _notes;
        private DateTime _timestamp;
        private User _performedBy;
        private int? _performedById;
        private string? _reference;

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public int CashDrawerId
        {
            get => _cashDrawerId;
            set
            {
                _cashDrawerId = value;
                OnPropertyChanged();
            }
        }

        public string Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged();
            }
        }

        public decimal Amount
        {
            get => _amount;
            set
            {
                _amount = value;
                OnPropertyChanged();
            }
        }

        public string? Reason
        {
            get => _reason;
            set
            {
                _reason = value;
                OnPropertyChanged();
            }
        }

        public string? Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                _timestamp = value;
                OnPropertyChanged();
            }
        }

        [ForeignKey("PerformedBy")]
        public int? PerformedById
        {
            get => _performedById;
            set
            {
                _performedById = value;
                OnPropertyChanged();
            }
        }

        public virtual User PerformedBy
        {
            get => _performedBy;
            set
            {
                _performedBy = value;
                PerformedById = value?.Id;
                OnPropertyChanged();
            }
        }

        public string? Reference
        {
            get => _reference;
            set
            {
                _reference = value;
                OnPropertyChanged();
            }
        }

        public CashTransaction()
        {
            Timestamp = DateTime.Now;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 