-- Create BatchStock table if it doesn't exist
CREATE TABLE IF NOT EXISTS "BatchStock" (
    "Id" INTEGER NOT NULL,
    "ProductId" INTEGER NOT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Quantity" DECIMAL(18,3) NOT NULL DEFAULT 0,
    "ManufactureDate" TEXT NOT NULL,
    "ExpiryDate" TEXT,
    "PurchasePrice" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "SellingPrice" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "Location" TEXT,
    "Notes" TEXT,
    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "PK_BatchStock" PRIMARY KEY("Id" AUTOINCREMENT),
    CONSTRAINT "FK_BatchStock_Products_ProductId" FOREIGN KEY("ProductId")
        REFERENCES "Products"("Id") ON DELETE CASCADE
);

-- Create index for better performance
CREATE INDEX "IX_BatchStock_ProductId" ON "BatchStock"("ProductId"); 