using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <summary>
    /// Migration to add bulk/pack pricing functionality to the POS system.
    /// This adds the ProductPriceTier table to support tiered pricing based on quantity thresholds.
    /// </summary>
    public partial class AddBulkPricingSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create ProductPriceTiers table
            migrationBuilder.CreateTable(
                name: "ProductPriceTiers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    MinimumQuantity = table.Column<decimal>(type: "decimal(18,3)", precision: 18, scale: 3, nullable: false),
                    MaximumQuantity = table.Column<decimal>(type: "decimal(18,3)", precision: 18, scale: 3, nullable: true),
                    UnitPrice = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    PackPrice = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TierName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductPriceTiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductPriceTiers_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create performance indexes
            migrationBuilder.CreateIndex(
                name: "IX_ProductPriceTiers_ProductId_MinimumQuantity",
                table: "ProductPriceTiers",
                columns: new[] { "ProductId", "MinimumQuantity" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductPriceTiers_ProductId_Active_Dates",
                table: "ProductPriceTiers",
                columns: new[] { "ProductId", "IsActive", "EffectiveDate", "ExpirationDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductPriceTiers_Active_Dates",
                table: "ProductPriceTiers",
                columns: new[] { "IsActive", "EffectiveDate", "ExpirationDate" });

            // Add some sample bulk pricing data for demonstration
            migrationBuilder.InsertData(
                table: "ProductPriceTiers",
                columns: new[] { "ProductId", "MinimumQuantity", "MaximumQuantity", "UnitPrice", "PackPrice", "TierName", "Description", "IsActive", "DisplayOrder", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    // Example: Product ID 1 - Single unit pricing (if it exists)
                    // Note: In a real migration, you'd want to check if products exist first
                    // This is just for demonstration purposes
                });

            // Add check constraints to ensure data integrity
            migrationBuilder.Sql(@"
                CREATE TRIGGER check_price_tier_quantity_range
                BEFORE INSERT ON ProductPriceTiers
                FOR EACH ROW
                WHEN NEW.MaximumQuantity IS NOT NULL AND NEW.MaximumQuantity <= NEW.MinimumQuantity
                BEGIN
                    SELECT RAISE(ABORT, 'MaximumQuantity must be greater than MinimumQuantity');
                END;
            ");

            migrationBuilder.Sql(@"
                CREATE TRIGGER check_price_tier_quantity_range_update
                BEFORE UPDATE ON ProductPriceTiers
                FOR EACH ROW
                WHEN NEW.MaximumQuantity IS NOT NULL AND NEW.MaximumQuantity <= NEW.MinimumQuantity
                BEGIN
                    SELECT RAISE(ABORT, 'MaximumQuantity must be greater than MinimumQuantity');
                END;
            ");

            migrationBuilder.Sql(@"
                CREATE TRIGGER check_price_tier_positive_values
                BEFORE INSERT ON ProductPriceTiers
                FOR EACH ROW
                WHEN NEW.MinimumQuantity <= 0 OR NEW.UnitPrice <= 0 OR (NEW.PackPrice IS NOT NULL AND NEW.PackPrice <= 0)
                BEGIN
                    SELECT RAISE(ABORT, 'MinimumQuantity, UnitPrice, and PackPrice must be positive values');
                END;
            ");

            migrationBuilder.Sql(@"
                CREATE TRIGGER check_price_tier_positive_values_update
                BEFORE UPDATE ON ProductPriceTiers
                FOR EACH ROW
                WHEN NEW.MinimumQuantity <= 0 OR NEW.UnitPrice <= 0 OR (NEW.PackPrice IS NOT NULL AND NEW.PackPrice <= 0)
                BEGIN
                    SELECT RAISE(ABORT, 'MinimumQuantity, UnitPrice, and PackPrice must be positive values');
                END;
            ");

            // Add trigger to automatically update UpdatedAt timestamp
            migrationBuilder.Sql(@"
                CREATE TRIGGER update_product_price_tier_timestamp
                AFTER UPDATE ON ProductPriceTiers
                FOR EACH ROW
                BEGIN
                    UPDATE ProductPriceTiers SET UpdatedAt = datetime('now') WHERE Id = NEW.Id;
                END;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop triggers first
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS check_price_tier_quantity_range;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS check_price_tier_quantity_range_update;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS check_price_tier_positive_values;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS check_price_tier_positive_values_update;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS update_product_price_tier_timestamp;");

            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_ProductPriceTiers_ProductId_MinimumQuantity",
                table: "ProductPriceTiers");

            migrationBuilder.DropIndex(
                name: "IX_ProductPriceTiers_ProductId_Active_Dates",
                table: "ProductPriceTiers");

            migrationBuilder.DropIndex(
                name: "IX_ProductPriceTiers_Active_Dates",
                table: "ProductPriceTiers");

            // Drop table
            migrationBuilder.DropTable(
                name: "ProductPriceTiers");
        }
    }
}
