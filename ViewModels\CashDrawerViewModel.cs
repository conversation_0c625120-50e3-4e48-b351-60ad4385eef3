using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Views;
using POSSystem.Commands;
using MaterialDesignThemes.Wpf;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace POSSystem.ViewModels
{
    public class CashDrawerViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly ICashDrawerService _cashDrawerService;
        private readonly IDatabaseService _dbService;
        private CashDrawer _currentDrawer;
        private ObservableCollection<CashTransaction> _currentTransactions;
        private decimal _totalCashSales;
        private decimal _totalPayouts;
        private ICommand _openDrawerCommand;
        private ICommand _closeDrawerCommand;
        private ICommand _addPayoutCommand;
        private ICommand _reconcileDrawerCommand;
        private decimal _openingBalance;
        private decimal _difference;
        private ICommand _manageBusinessExpensesCommand;
        private ICommand _refreshCommand;
        private System.Windows.Threading.DispatcherTimer _autoRefreshTimer;
        private bool _disposed;

        public CashDrawerViewModel(ICashDrawerService cashDrawerService, IDatabaseService dbService)
        {
            _cashDrawerService = cashDrawerService;
            _dbService = dbService;
            _currentTransactions = new ObservableCollection<CashTransaction>();
            
            // Subscribe to cash drawer updates
            _cashDrawerService.CashDrawerUpdated += CashDrawerService_CashDrawerUpdated;
            
            LoadCurrentDrawer();
            
            // Set up auto-refresh timer
            _autoRefreshTimer = new System.Windows.Threading.DispatcherTimer();
            _autoRefreshTimer.Interval = TimeSpan.FromSeconds(60); // Refresh every 60 seconds (reduced frequency)
            _autoRefreshTimer.Tick += async (s, e) => await RefreshData();
            _autoRefreshTimer.Start();
        }

        public ICommand RefreshCommand => _refreshCommand ??= new RelayCommand(async _ => await RefreshData());

        public async Task RefreshData()
        {
            try
            {
                // First, do a lightweight check to see if anything has changed
                var basicDrawer = _cashDrawerService.GetCurrentDrawerBasic();

                // If no drawer exists and we had one before, or if drawer status changed, do full refresh
                bool needsFullRefresh = false;

                if (basicDrawer == null && _currentDrawer != null)
                {
                    // Drawer was closed
                    needsFullRefresh = true;
                }
                else if (basicDrawer != null && _currentDrawer == null)
                {
                    // New drawer was opened
                    needsFullRefresh = true;
                }
                else if (basicDrawer != null && _currentDrawer != null)
                {
                    // Check if it's a different drawer or status changed
                    if (basicDrawer.Id != _currentDrawer.Id || basicDrawer.Status != _currentDrawer.Status)
                    {
                        needsFullRefresh = true;
                    }
                }

                if (needsFullRefresh)
                {
                    // Only load full data when something actually changed
                    var updatedDrawer = _cashDrawerService.GetCurrentDrawer();
                    if (updatedDrawer != null)
                    {
                        _currentDrawer = updatedDrawer;
                        if (_currentDrawer.Transactions != null)
                        {
                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                CurrentTransactions = new ObservableCollection<CashTransaction>(_currentDrawer.Transactions);
                                UpdateTotals();
                                OnPropertyChanged(nameof(OpeningBalance));
                                OnPropertyChanged(nameof(Status));
                                OnPropertyChanged(nameof(IsDrawerOpen));
                                OnPropertyChanged(nameof(CanOpenDrawer));
                                OnPropertyChanged(nameof(CanCloseDrawer));
                                OnPropertyChanged(nameof(ExpectedBalance));
                                OnPropertyChanged(nameof(ActualBalance));
                                OnPropertyChanged(nameof(Difference));
                                OnPropertyChanged(nameof(OpenedBy));
                                OnPropertyChanged(nameof(OpenedAt));
                            });
                        }
                    }
                    else
                    {
                        // No drawer exists, clear current data
                        _currentDrawer = null;
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            CurrentTransactions = new ObservableCollection<CashTransaction>();
                            UpdateTotals();
                            OnPropertyChanged(nameof(OpeningBalance));
                            OnPropertyChanged(nameof(Status));
                            OnPropertyChanged(nameof(IsDrawerOpen));
                            OnPropertyChanged(nameof(CanOpenDrawer));
                            OnPropertyChanged(nameof(CanCloseDrawer));
                            OnPropertyChanged(nameof(ExpectedBalance));
                            OnPropertyChanged(nameof(ActualBalance));
                            OnPropertyChanged(nameof(Difference));
                            OnPropertyChanged(nameof(OpenedBy));
                            OnPropertyChanged(nameof(OpenedAt));
                        });
                    }
                }
                // If nothing changed, skip the expensive query
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error refreshing cash drawer data: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void LoadCurrentDrawer()
        {
            _currentDrawer = _cashDrawerService.GetCurrentDrawer();
            if (_currentDrawer != null && _currentDrawer.Transactions != null)
            {
                CurrentTransactions = new ObservableCollection<CashTransaction>(_currentDrawer.Transactions);
                UpdateTotals();
            }
            else
            {
                CurrentTransactions = new ObservableCollection<CashTransaction>();
            }
            OnPropertyChanged(nameof(OpeningBalance));
            OnPropertyChanged(nameof(Status));
            OnPropertyChanged(nameof(IsDrawerOpen));
            OnPropertyChanged(nameof(CanOpenDrawer));
            OnPropertyChanged(nameof(CanCloseDrawer));
            OnPropertyChanged(nameof(CurrentTransactions));
            OnPropertyChanged(nameof(ExpectedBalance));
            OnPropertyChanged(nameof(ActualBalance));
            OnPropertyChanged(nameof(Difference));
            OnPropertyChanged(nameof(OpenedBy));
            OnPropertyChanged(nameof(OpenedAt));
        }

        public string Status => _currentDrawer?.Status ?? "No Active Drawer";
        public bool IsDrawerOpen => _currentDrawer?.Status == "Open";
        public bool CanOpenDrawer => _currentDrawer == null || _currentDrawer.Status == "Closed";
        public bool CanCloseDrawer => IsDrawerOpen;

        public ObservableCollection<CashTransaction> CurrentTransactions
        {
            get => _currentTransactions;
            private set
            {
                _currentTransactions = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalCashSales
        {
            get => _totalCashSales;
            private set
            {
                _totalCashSales = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalPayouts
        {
            get => _totalPayouts;
            private set
            {
                _totalPayouts = value;
                OnPropertyChanged();
            }
        }

        public decimal OpeningBalance => _currentDrawer?.OpeningBalance ?? 0;

        public decimal OpeningBalanceDisplay
        {
            get => _openingBalance;
            set
            {
                _openingBalance = value;
                OnPropertyChanged();
            }
        }

        public decimal ExpectedBalance => _currentDrawer?.ExpectedBalance ?? 0;
        
        public decimal ActualBalance
        {
            get => _currentDrawer?.ActualBalance ?? 0;
            set
            {
                if (_currentDrawer != null)
                {
                    _currentDrawer.ActualBalance = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Difference));
                }
            }
        }

        public decimal Difference => _currentDrawer?.Difference ?? 0;

        public ICommand OpenDrawerCommand => _openDrawerCommand ??= new RelayCommand(_ => OpenDrawer(), _ => CanOpenDrawer);
        public ICommand CloseDrawerCommand => _closeDrawerCommand ??= new RelayCommand(_ => CloseDrawer(), _ => CanCloseDrawer);
        public ICommand AddPayoutCommand => _addPayoutCommand ??= new RelayCommand(_ => AddPayout(), _ => IsDrawerOpen);
        public ICommand ReconcileDrawerCommand => _reconcileDrawerCommand ??= new RelayCommand(_ => ReconcileDrawer(), _ => IsDrawerOpen);
        public ICommand ManageBusinessExpensesCommand => _manageBusinessExpensesCommand ??= new RelayCommand(ExecuteManageBusinessExpenses);

        private async void OpenDrawer()
        {
            try
            {
                var user = await _dbService.GetDefaultUserAsync();
                if (user == null)
                {
                    System.Windows.MessageBox.Show("No user found to open drawer.", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                var dialog = new OpenDrawerDialog();
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is decimal openingBalance)
                {
                    if (openingBalance <= 0)
                    {
                        System.Windows.MessageBox.Show("Opening balance must be greater than zero.", "Error",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    var drawer = new CashDrawer
                    {
                        OpeningBalance = openingBalance,
                        OpenedById = user.Id,
                        ClosedById = null,
                        ClosedBy = null,
                        Status = "Open",
                        Transactions = new ObservableCollection<CashTransaction>()
                    };

                    _cashDrawerService.OpenDrawer(drawer);
                    LoadCurrentDrawer();
                }
            }
            catch (Exception ex)
            {
                var errorMessage = ex.InnerException != null 
                    ? $"Error opening drawer: {ex.Message}\nDetails: {ex.InnerException.Message}"
                    : $"Error opening drawer: {ex.Message}";
                    
                System.Windows.MessageBox.Show(errorMessage, "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void CloseDrawer()
        {
            try
            {
                if (_currentDrawer == null) return;

                var dialog = new CloseDrawerDialog(_currentDrawer);
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is CloseDrawerResult closeResult)
                {
                    _currentDrawer.ActualBalance = closeResult.ActualBalance;
                    _currentDrawer.Notes = closeResult.Notes;
                    _cashDrawerService.CloseDrawer(_currentDrawer);
                    LoadCurrentDrawer();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error closing drawer: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void AddPayout()
        {
            try
            {
                if (_currentDrawer == null) return;

                var user = _dbService.GetDefaultUser();
                if (user == null)
                {
                    System.Windows.MessageBox.Show("No user found to perform payout.", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                var dialog = new PayoutDialog();
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is PayoutInfo payoutInfo)
                {
                    var transaction = new CashTransaction
                    {
                        Amount = payoutInfo.Amount,
                        Type = "Payout",
                        Reason = payoutInfo.Reason,
                        Notes = payoutInfo.Reason,
                        Reference = payoutInfo.Reference,
                        Timestamp = DateTime.Now,
                        CashDrawerId = _currentDrawer.Id,
                        PerformedById = user.Id
                    };

                    _cashDrawerService.AddTransaction(transaction);
                    LoadCurrentDrawer();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error adding payout: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void ReconcileDrawer()
        {
            try
            {
                if (_currentDrawer == null) return;

                var dialog = new CashDrawerReconciliationDialog(_currentDrawer, _cashDrawerService);
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is CashDrawer reconciledDrawer)
                {
                    _cashDrawerService.CloseDrawer(reconciledDrawer);
                    LoadCurrentDrawer();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error reconciling drawer: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void ExecuteManageBusinessExpenses(object parameter)
        {
            var dialog = new BusinessExpenseDialog();
            await DialogHost.Show(dialog, "RootDialog");
            await LoadCashDrawerData();
        }

        private void UpdateTotals()
        {
            if (_currentDrawer == null || _currentTransactions == null)
            {
                TotalCashSales = 0;
                TotalPayouts = 0;
                return;
            }

            decimal sales = 0;
            decimal payouts = 0;

            foreach (var transaction in _currentTransactions)
            {
                if (transaction.Type == "Sale")
                    sales += transaction.Amount;
                else if (transaction.Type == "Out")
                    payouts += Math.Abs(transaction.Amount);
            }

            TotalCashSales = sales;
            TotalPayouts = payouts;
            OnPropertyChanged(nameof(ExpectedBalance));
            OnPropertyChanged(nameof(OpenedBy));
            OnPropertyChanged(nameof(OpenedAt));
        }

        public User OpenedBy => _currentDrawer?.OpenedBy;
        public DateTime? OpenedAt => _currentDrawer?.OpenedAt;

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }

        private async Task LoadTransactions()
        {
            if (_currentDrawer != null)
            {
                var transactions = _cashDrawerService.GetTransactions(_currentDrawer);
                CurrentTransactions = new ObservableCollection<CashTransaction>(transactions);
            }
        }

        private async Task LoadCashDrawerData()
        {
            if (_currentDrawer != null)
            {
                await LoadTransactions();
                UpdateTotals();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _autoRefreshTimer?.Stop();
                    _autoRefreshTimer = null;
                    // Unsubscribe from events
                    if (_cashDrawerService != null)
                    {
                        _cashDrawerService.CashDrawerUpdated -= CashDrawerService_CashDrawerUpdated;
                    }
                }
                _disposed = true;
            }
        }

        ~CashDrawerViewModel()
        {
            Dispose(false);
        }

        private async void CashDrawerService_CashDrawerUpdated(object sender, CashDrawerEventArgs e)
        {
            if (e.Drawer != null)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _currentDrawer = e.Drawer;
                    if (_currentDrawer.Transactions != null)
                    {
                        CurrentTransactions = new ObservableCollection<CashTransaction>(_currentDrawer.Transactions);
                        UpdateTotals();
                    }
                    OnPropertyChanged(nameof(OpeningBalance));
                    OnPropertyChanged(nameof(Status));
                    OnPropertyChanged(nameof(IsDrawerOpen));
                    OnPropertyChanged(nameof(CanOpenDrawer));
                    OnPropertyChanged(nameof(CanCloseDrawer));
                    OnPropertyChanged(nameof(ExpectedBalance));
                    OnPropertyChanged(nameof(ActualBalance));
                    OnPropertyChanged(nameof(Difference));
                    OnPropertyChanged(nameof(OpenedBy));
                    OnPropertyChanged(nameof(OpenedAt));
                });
            }
        }
    }
}