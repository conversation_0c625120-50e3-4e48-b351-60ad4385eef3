<Window x:Class="POSLicensingSystem.Views.TestLicensingView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:POSLicensingSystem.ViewModels"
        xmlns:converters="clr-namespace:POSLicensingSystem.Converters"
        mc:Ignorable="d"
        Title="License System Test Utility" Height="650" Width="800"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        Icon="{StaticResource AppIconImage}"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <converters:BooleanInverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.DataContext>
        <viewmodels:TestLicensingViewModel />
    </Window.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" 
                   Text="License System Test Utility" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Generation Panel -->
            <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" Margin="0,0,10,0">
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="Generate Test License Key" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,20"/>
                        
                        <!-- Business Name -->
                        <TextBox Text="{Binding BusinessName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="Business Name"
                                 Margin="0,0,0,16"/>
                        
                        <!-- System ID -->
                        <TextBox Text="{Binding SystemId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="System ID"
                                 Margin="0,0,0,16"/>
                        
                        <!-- Expiration Date -->
                        <DatePicker SelectedDate="{Binding ExpirationDate, Mode=TwoWay}"
                                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                    materialDesign:HintAssist.Hint="Expiration Date"
                                    Margin="0,0,0,16"/>
                        
                        <!-- Format selection -->
                        <CheckBox Content="Use Hex Format (POSSystem Compatible)" 
                                  IsChecked="{Binding UseHexFormat, Mode=TwoWay}"
                                  Style="{StaticResource MaterialDesignCheckBox}"
                                  Margin="0,0,0,16"/>
                        
                        <!-- Generate Button -->
                        <Button Content="Generate License Key"
                                Command="{Binding GenerateTestKeyCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="0,0,0,16"/>
                        
                        <!-- Generated License Key Result -->
                        <TextBox Text="{Binding GeneratedKey, Mode=TwoWay}"
                                 IsReadOnly="True"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 Height="120"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="Generated License Key"/>
                    </StackPanel>
                </materialDesign:Card>
            </ScrollViewer>
            
            <!-- Validation Panel -->
            <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" Margin="10,0,0,0">
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="Validate License Key" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,20"/>
                        
                        <!-- Business Name -->
                        <TextBox Text="{Binding ValidationBusinessName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="Business Name"
                                 Margin="0,0,0,16"/>
                        
                        <!-- System ID -->
                        <TextBox Text="{Binding ValidationSystemId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="System ID"
                                 Margin="0,0,0,16"/>
                        
                        <!-- License Key -->
                        <TextBox Text="{Binding LicenseKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 Height="80"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="License Key To Validate"
                                 Margin="0,0,0,16"/>
                        
                        <!-- Copy From Generated Button -->
                        <Button Content="Copy From Generated Key"
                                Command="{Binding CopyFromGeneratedCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,0,16"/>
                        
                        <!-- Validate Button -->
                        <Button Content="Validate License Key"
                                Command="{Binding ValidateTestKeyCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="0,0,0,16"/>
                        
                        <!-- Validation Result -->
                        <materialDesign:Card 
                            Background="{Binding ValidationResultColor}"
                            Padding="16"
                            Margin="0,0,0,16">
                            <TextBlock Text="{Binding ValidationResult}"
                                      Foreground="{Binding ValidationTextColor}"
                                      TextWrapping="Wrap"/>
                        </materialDesign:Card>
                        
                        <!-- Debug Output -->
                        <Expander Header="Debug Information" IsExpanded="True">
                            <TextBox Text="{Binding DebugOutput, Mode=TwoWay}"
                                     IsReadOnly="True"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     Height="100"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     FontFamily="Consolas"/>
                        </Expander>
                    </StackPanel>
                </materialDesign:Card>
            </ScrollViewer>
        </Grid>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="Run Diagnostics"
                    Command="{Binding RunDiagnosticsCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Foreground="White"
                    Margin="0,0,10,0"/>
            <Button Content="Clear Debug Log"
                    Command="{Binding ClearDebugOutputCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"/>
            <Button Content="Close"
                    Command="{Binding CloseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
        </StackPanel>
    </Grid>
</Window> 