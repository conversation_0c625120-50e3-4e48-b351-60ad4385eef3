<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.UsersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="632.5" d:DesignWidth="800"
             Background="{DynamicResource AppBackgroundGradient}">
    <UserControl.DataContext>
        <vm:UsersViewModel/>
    </UserControl.DataContext>
    
    <!-- Add DialogHost wrapper -->
    <md:DialogHost Identifier="RootDialog">
        <Grid Margin="20" Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Statistics Panel -->
            <md:Card Grid.Row="0" 
                   Grid.Column="0"
                   Background="{DynamicResource MaterialDesignCardBackground}"
                   Foreground="{DynamicResource MaterialDesignBody}"
                   md:ElevationAssist.Elevation="Dp2"
                   UniformCornerRadius="4"
                   Margin="0,0,0,10">
                <StackPanel Margin="16">
                    <TextBlock Text="{DynamicResource UserStatistics}" 
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Foreground="{DynamicResource MaterialDesignBody}"
                              Margin="0,0,0,15"/>
                          
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Total Users -->
                        <StackPanel Grid.Column="0" Margin="10">
                            <TextBlock Text="{DynamicResource TotalUsers}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="AccountGroup" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="{Binding TotalUsers}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Active Users -->
                        <StackPanel Grid.Column="1" Margin="10">
                            <TextBlock Text="{DynamicResource ActiveUsers}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="AccountCheck" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                <TextBlock Text="{Binding ActiveUsers}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Total Sales -->
                        <StackPanel Grid.Column="2" Margin="10">
                            <TextBlock Text="{DynamicResource TotalSales}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="Cash" 
                                            Width="24" 
                                            Height="24"
                                            Foreground="{DynamicResource PrimaryHueLightBrush}"/>
                                <TextBlock Text="{Binding TotalSales, StringFormat={}{0:N2} DA}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{DynamicResource MaterialDesignBody}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </md:Card>

            <!-- Header with Search and Add Button -->
            <md:Card Grid.Row="1" 
                   Grid.Column="0"
                   Background="{DynamicResource MaterialDesignCardBackground}"
                   Foreground="{DynamicResource MaterialDesignBody}"
                   md:ElevationAssist.Elevation="Dp2"
                   UniformCornerRadius="4"
                   Margin="0,0,0,10">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             md:HintAssist.Hint="{DynamicResource SearchUsersHint}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Background="{DynamicResource MaterialDesignPaper}"
                             Margin="0,0,10,0"/>

                    <ComboBox Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"
                             Width="150"
                             md:HintAssist.Hint="{DynamicResource Role}"
                             ItemsSource="{Binding FilterRoles}"
                             DisplayMemberPath="DisplayName"
                             SelectedItem="{Binding SelectedRoleFilter}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Background="{DynamicResource MaterialDesignPaper}"
                             Margin="0,0,10,0"/>
                             
                    <!-- Add New User Button -->
                    <Button Grid.Column="2"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                           BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                           md:ButtonAssist.CornerRadius="4"
                           Click="AddNewUser_Click">
                        <StackPanel Orientation="Horizontal">
                            <md:PackIcon Kind="AccountPlus" 
                                       Width="24" 
                                       Height="24"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource AddUser}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </md:Card>

            <!-- Users List -->
            <md:Card Grid.Row="2" 
                   Grid.Column="0"
                   Background="{DynamicResource MaterialDesignCardBackground}"
                   Foreground="{DynamicResource MaterialDesignBody}"
                   md:ElevationAssist.Elevation="Dp2"
                   UniformCornerRadius="4"
                   Margin="0,0,0,0">
                <DataGrid ItemsSource="{Binding Users}"
                          Style="{StaticResource AppDataGridStyle}"
                          SelectedItem="{Binding SelectedUser}"
                          Background="Transparent"
                          Foreground="{DynamicResource MaterialDesignBody}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          RowBackground="{DynamicResource MaterialDesignPaper}"
                          AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                          GridLinesVisibility="None">
                    <DataGrid.Resources>
                        <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                                       Color="{DynamicResource MaterialDesignSelection}"/>
                        <SolidColorBrush x:Key="{x:Static SystemColors.ControlBrushKey}" 
                                       Color="Transparent"/>
                        <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" 
                                       Color="{DynamicResource MaterialDesignPaper}"/>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="{DynamicResource Username}" 
                                          Binding="{Binding Username}"
                                          Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTemplateColumn Header="{DynamicResource FullName}" Width="*">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Foreground="{DynamicResource MaterialDesignBody}">
                                        <Run Text="{Binding FirstName}"/>
                                        <Run Text=" "/>
                                        <Run Text="{Binding LastName}"/>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="{DynamicResource Role}"
                                          Binding="{Binding DisplayRoleName}"
                                          Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTemplateColumn Header="{DynamicResource Status}" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding IsActive, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='Active|Inactive'}"
                                             Foreground="{Binding IsActive, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='#4CAF50|#9E9E9E'}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="{DynamicResource Edit}"
                                                Click="EditUser_Click"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Height="35"
                                                Width="80"
                                                Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                                Margin="0,0,4,0"/>
                                        <Button Content="{DynamicResource Delete}"
                                                Command="{Binding DataContext.DeleteUserCommand, 
                                                        RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Height="35"
                                                Width="80"
                                                Foreground="Red"
                                                BorderBrush="Red"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </md:Card>
        </Grid>
    </md:DialogHost>
</UserControl> 