using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POSSystem.Models
{
    /// <summary>
    /// Represents a payment made against an invoice in the two-tier invoice system
    /// </summary>
    public class InvoicePayment
    {
        public InvoicePayment()
        {
            PaymentDate = DateTime.Now;
            CreatedAt = DateTime.Now;
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Invoice")]
        public int InvoiceId { get; set; }

        [Required]
        public DateTime PaymentDate { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [MaxLength(50)]
        public string PaymentMethod { get; set; }

        [MaxLength(100)]
        public string Reference { get; set; }

        /// <summary>
        /// Legacy property for compatibility
        /// </summary>
        [MaxLength(100)]
        public string ReferenceNumber
        {
            get => Reference;
            set => Reference = value;
        }

        [MaxLength(500)]
        public string Notes { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public virtual Invoice Invoice { get; set; }

        // Computed properties for UI
        [NotMapped]
        public string AmountDisplay => Amount.ToString("C");

        [NotMapped]
        public string PaymentDateDisplay => PaymentDate.ToString("MMM dd, yyyy");

        [NotMapped]
        public string PaymentMethodDisplay => PaymentMethod switch
        {
            "Cash" => "Cash",
            "Card" => "Credit/Debit Card",
            "Check" => "Check",
            "BankTransfer" => "Bank Transfer",
            "Online" => "Online Payment",
            _ => PaymentMethod
        };

        // Helper methods
        public bool IsValid(out string errorMessage)
        {
            errorMessage = string.Empty;

            if (Amount <= 0)
            {
                errorMessage = "Payment amount must be greater than 0.";
                return false;
            }

            if (string.IsNullOrWhiteSpace(PaymentMethod))
            {
                errorMessage = "Payment method is required.";
                return false;
            }

            if (PaymentDate > DateTime.Now)
            {
                errorMessage = "Payment date cannot be in the future.";
                return false;
            }

            return true;
        }

        public static InvoicePayment CreateCashPayment(decimal amount, string reference = null)
        {
            return new InvoicePayment
            {
                Amount = amount,
                PaymentMethod = "Cash",
                Reference = reference
            };
        }

        public static InvoicePayment CreateCardPayment(decimal amount, string reference = null)
        {
            return new InvoicePayment
            {
                Amount = amount,
                PaymentMethod = "Card",
                Reference = reference
            };
        }

        public static InvoicePayment CreateCheckPayment(decimal amount, string checkNumber)
        {
            return new InvoicePayment
            {
                Amount = amount,
                PaymentMethod = "Check",
                Reference = checkNumber
            };
        }

        public static InvoicePayment CreateBankTransferPayment(decimal amount, string transferReference)
        {
            return new InvoicePayment
            {
                Amount = amount,
                PaymentMethod = "BankTransfer",
                Reference = transferReference
            };
        }
    }
}