using System.Windows.Controls;
using POSSystem.ViewModels;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for DraftInvoiceDialog.xaml
    /// </summary>
    public partial class DraftInvoiceDialog : UserControl
    {
        public DraftInvoiceDialog()
        {
            InitializeComponent();
        }

        public DraftInvoiceDialog(DraftInvoiceViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to the RequestClose event to handle dialog closing
            if (viewModel != null)
            {
                viewModel.RequestClose += OnRequestClose;
            }
        }

        private void OnRequestClose()
        {
            // This will be handled by the dialog host (MaterialDesign DialogHost)
            // The dialog result will be available through the ViewModel's DialogResult property
        }

        /// <summary>
        /// Gets the dialog result from the ViewModel
        /// </summary>
        public bool? DialogResult => (DataContext as DraftInvoiceViewModel)?.DialogResult;
    }
}
