﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Icons\point-of-sale.ico</ApplicationIcon>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <DefineConstants>WINDOWS</DefineConstants>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <NoWarn>CA1416;NU1701</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\Images\default-user.png" />
    <None Remove="Resources\Images\no-image.png" />
    <None Remove="Resources\Sounds\cart-add.wav" />
    <None Remove="Resources\Sounds\payment-success.wav" />
  </ItemGroup>

  <!-- Exclude POSLicensingSystem folder from compilation -->
  <ItemGroup>
    <Compile Remove="POSLicensingSystem\**" />
    <EmbeddedResource Remove="POSLicensingSystem\**" />
    <None Remove="POSLicensingSystem\**" />
    <Page Remove="POSLicensingSystem\**" />
  </ItemGroup>

  <!-- Exclude Tools folder from compilation -->
  <ItemGroup>
    <Compile Remove="Tools\**" />
    <EmbeddedResource Remove="Tools\**" />
    <None Remove="Tools\**" />
    <Page Remove="Tools\**" />
  </ItemGroup>

  <!-- Exclude DatabaseFix folder from compilation -->
  <ItemGroup>
    <Compile Remove="DatabaseFix\**" />
    <EmbeddedResource Remove="DatabaseFix\**" />
    <None Remove="DatabaseFix\**" />
    <Page Remove="DatabaseFix\**" />
  </ItemGroup>

  <!-- Exclude Tests folder from main compilation -->
  <ItemGroup>
    <Compile Remove="Tests\**" />
    <Compile Remove="POSSystem.Tests\**" />
    <EmbeddedResource Remove="Tests\**" />
    <EmbeddedResource Remove="POSSystem.Tests\**" />
    <None Remove="Tests\**" />
    <None Remove="POSSystem.Tests\**" />
    <Page Remove="Tests\**" />
    <Page Remove="POSSystem.Tests\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\Sounds\cart-add.wav">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\Sounds\payment-success.wav">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Resources\Images\default-user.png" />
    <EmbeddedResource Include="Resources\Images\no-image.png" />
  </ItemGroup>



  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.4.22" />
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
    <PackageReference Include="System.Management" Version="9.0.1" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Icons\point-of-sale.ico" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Fonts\*.ttf" />
  </ItemGroup>

  <ItemGroup>
    <Page Update="Views\Layouts\SalesViewGrid.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>

  <ItemGroup>
    <Page Update="Views\Dialogs\*.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Utils\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="DbUtils\UpdateDbSchema.cs" />
    <None Include="DbUtils\UpdateDbSchema.cs" />
  </ItemGroup>

  <!-- WPF-specific references that include printing support -->
  <!-- Removed redundant FrameworkReference - it's implicitly included by .NET SDK -->

</Project>
