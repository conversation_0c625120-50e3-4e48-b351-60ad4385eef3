using System.Collections.Generic;
using POSSystem.Models;

namespace POSSystem.Services.InventoryManagement
{
    public interface IStockService
    {
        // Increments stock for purchase receipts; creates/updates batches as needed and syncs totals
        void IncreaseStock(int productId, decimal quantity, string reason = null, string batchNumber = null);

        // Increments stock with complete batch information for purchase receipts
        void IncreaseStock(int productId, decimal quantity, string reason = null, string batchNumber = null,
            decimal? purchasePrice = null, DateTime? expiryDate = null, DateTime? manufactureDate = null,
            string location = null, string notes = null, decimal? sellingPrice = null);

        // Force creation of a brand-new batch entry regardless of existing batch numbers
        void IncreaseStockNewBatch(int productId, decimal quantity, string reason = null, string batchNumber = null,
            decimal? purchasePrice = null, DateTime? expiryDate = null, DateTime? manufactureDate = null,
            string location = null, string notes = null, decimal? sellingPrice = null);

        // Decrements stock for sales; uses FEFO/FIFO on batches and syncs totals
        void DecreaseStock(int productId, decimal quantity, string reason = null);

        // Set absolute stock (used by reservation adjustments when not batch-tracked)
        void SetStock(int productId, decimal newQuantity, string reason = null);

        // Notify UI about stock change (helper)
        void NotifyStockChanged(int productId);

        // Bulk operations for invoices
        void IncreaseStockBulk(IEnumerable<(int productId, decimal qty)> items);
        void DecreaseStockBulk(IEnumerable<(int productId, decimal qty)> items);
    }

}
