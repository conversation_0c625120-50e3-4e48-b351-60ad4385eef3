﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;

namespace POSSystem.Models
{
    /// <summary>
    /// Represents a complete sales transaction in the POS system, including all items, payments, and financial calculations.
    /// </summary>
    /// <remarks>
    /// <para>The Sale entity is the core business object for all sales transactions and includes:</para>
    /// <list type="bullet">
    /// <item><description>Transaction Details: Invoice number, sale date, customer information</description></item>
    /// <item><description>Financial Calculations: Subtotal, discounts, taxes, grand total</description></item>
    /// <item><description>Payment Information: Payment method, status, amount paid</description></item>
    /// <item><description>Line Items: Collection of SaleItem objects representing purchased products</description></item>
    /// <item><description>Audit Trail: User tracking and transaction status</description></item>
    /// </list>
    /// <para>The class implements INotifyPropertyChanged for real-time UI updates during transaction processing.</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Create a new sale transaction
    /// var sale = new Sale
    /// {
    ///     CustomerId = 123,
    ///     UserId = 1,
    ///     PaymentMethod = "Credit Card"
    /// };
    ///
    /// // Add items to the sale
    /// sale.Items.Add(new SaleItem { ProductId = 1, Quantity = 2, UnitPrice = 25.99m });
    ///
    /// // Calculate totals
    /// sale.CalculateTotals();
    /// </code>
    /// </example>
    public class Sale : INotifyPropertyChanged
    {
        private decimal _amountPaid;
        private string _paymentStatus;

        public Sale()
        {
            InvoiceNumber = string.Empty;
            Items = new List<SaleItem>();
            Payments = new List<Payment>();
            SaleDate = DateTime.Now;
            Status = "Pending";
            PaymentStatus = "Pending";
            PaymentMethod = "Cash";
        }

        /// <summary>
        /// Gets or sets the unique identifier for this sale transaction.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the unique invoice number for this sale transaction.
        /// This is typically displayed to customers and used for transaction lookup.
        /// </summary>
        [Required]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Gets or sets the date and time when this sale transaction occurred.
        /// </summary>
        public DateTime SaleDate { get; set; }

        /// <summary>
        /// Gets or sets the optional due date for payment (used for credit sales).
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Gets or sets the optional customer ID associated with this sale.
        /// Null for anonymous/walk-in customers.
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user (cashier/employee) who processed this sale.
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Gets or sets the subtotal amount before discounts and taxes.
        /// This is calculated as the sum of all line item totals.
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// Gets or sets the total discount amount applied to this sale.
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Gets or sets the total tax amount for this sale.
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Gets or sets the final total amount for this sale.
        /// Calculated as: Subtotal - DiscountAmount + TaxAmount
        /// </summary>
        public decimal GrandTotal { get; set; }
        
        [Required]
        public string PaymentMethod { get; set; }
        
        [Required]
        public string PaymentStatus 
        { 
            get => _paymentStatus;
            set
            {
                if (_paymentStatus != value)
                {
                    _paymentStatus = value;
                    OnPropertyChanged();
                }
            }
        }
        
        public decimal AmountPaid 
        { 
            get => _amountPaid;
            set
            {
                if (_amountPaid != value)
                {
                    _amountPaid = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RemainingAmount));
                }
            }
        }

        [NotMapped]
        public decimal RemainingAmount => Math.Max(0, GrandTotal - AmountPaid);

        public decimal Change { get; set; }
        
        [Required]
        public string Status { get; set; }
        
        public int TotalItems { get; set; }

        [NotMapped]
        public bool IsOverdue => DueDate.HasValue && DueDate.Value.Date < DateTime.Now.Date;
        
        public virtual Customer? Customer { get; set; }
        
        [Required]
        public virtual User User { get; set; }
        
        public virtual ICollection<SaleItem> Items { get; set; }
        public virtual ICollection<Payment> Payments { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
