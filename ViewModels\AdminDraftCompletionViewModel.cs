using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Models.DTOs;
using POSSystem.Services;
using POSSystem.Helpers;
using CommunityToolkit.Mvvm.Input;

namespace POSSystem.ViewModels
{
    /// <summary>
    /// ViewModel for the admin draft completion interface
    /// </summary>
    public class AdminDraftCompletionViewModel : INotifyPropertyChanged
    {
        private readonly DraftInvoiceService _draftInvoiceService;
        private readonly AuthenticationService _authService;
        private readonly UserPermissionsService _permissionsService;

        // Private fields
        private Invoice _draftInvoice;
        private string _invoiceNumber;
        private DateTime _issueDate;
        private DateTime _dueDate;
        private string _paymentTerms;
        private string _reference;
        private string _notes;
        private decimal _discountAmount;
        private decimal _taxAmount;
        private bool _isLoading;
        private string _statusMessage;

        public AdminDraftCompletionViewModel(
            Invoice draftInvoice,
            DraftInvoiceService draftInvoiceService,
            AuthenticationService authService,
            UserPermissionsService permissionsService)
        {
            _draftInvoice = draftInvoice ?? throw new ArgumentNullException(nameof(draftInvoice));
            _draftInvoiceService = draftInvoiceService ?? throw new ArgumentNullException(nameof(draftInvoiceService));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _permissionsService = permissionsService ?? throw new ArgumentNullException(nameof(permissionsService));

            // Initialize collections
            DraftItems = new ObservableCollection<InvoiceItem>();
            PaymentTermsOptions = new ObservableCollection<string>
            {
                "Net 30", "Net 15", "Net 7", "Due on Receipt", "Cash on Delivery", "Net 60", "Net 90"
            };

            // Initialize commands
            CompleteInvoiceCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(CompleteInvoiceAsync, CanCompleteInvoice);
            SaveDraftCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(SaveDraftAsync, CanSaveDraft);
            RejectDraftCommand = new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(RejectDraftAsync, CanRejectDraft);
            CancelCommand = new RelayCommand(_ => Cancel());

            // Initialize with draft data
            InitializeFromDraft();
        }

        #region Properties

        public Invoice DraftInvoice
        {
            get => _draftInvoice;
            set
            {
                _draftInvoice = value;
                OnPropertyChanged();
                InitializeFromDraft();
            }
        }

        public ObservableCollection<InvoiceItem> DraftItems { get; }
        public ObservableCollection<string> PaymentTermsOptions { get; }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                _invoiceNumber = value;
                OnPropertyChanged();
                UpdateCanExecuteCommands();
            }
        }

        public DateTime IssueDate
        {
            get => _issueDate;
            set
            {
                _issueDate = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IssueDateDisplay));
                UpdateCanExecuteCommands();
            }
        }

        public DateTime DueDate
        {
            get => _dueDate;
            set
            {
                _dueDate = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DueDateDisplay));
                UpdateCanExecuteCommands();
            }
        }

        public string PaymentTerms
        {
            get => _paymentTerms;
            set
            {
                _paymentTerms = value;
                OnPropertyChanged();
            }
        }

        public string Reference
        {
            get => _reference;
            set
            {
                _reference = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                _discountAmount = Math.Max(0, value);
                OnPropertyChanged();
                OnPropertyChanged(nameof(DiscountAmountDisplay));
                OnPropertyChanged(nameof(GrandTotal));
                OnPropertyChanged(nameof(GrandTotalDisplay));
            }
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set
            {
                _taxAmount = Math.Max(0, value);
                OnPropertyChanged();
                OnPropertyChanged(nameof(TaxAmountDisplay));
                OnPropertyChanged(nameof(GrandTotal));
                OnPropertyChanged(nameof(GrandTotalDisplay));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                UpdateCanExecuteCommands();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // Computed properties
        public string DraftInfo => DraftInvoice != null 
            ? $"Created by {DraftInvoice.CreatedByUserName} on {DraftInvoice.DraftCreatedAt:MMM dd, yyyy} ({DraftInvoice.DaysPending} days ago)"
            : "Draft information not available";

        public string IssueDateDisplay => IssueDate.ToString("MMM dd, yyyy");
        public string DueDateDisplay => DueDate.ToString("MMM dd, yyyy");
        public string DiscountAmountDisplay => DiscountAmount.ToString("C");
        public string TaxAmountDisplay => TaxAmount.ToString("C");

        public decimal Subtotal => DraftItems.Sum(item => item.Total);
        public string SubtotalDisplay => Subtotal.ToString("C");

        public decimal GrandTotal => Subtotal - DiscountAmount + TaxAmount;
        public string GrandTotalDisplay => GrandTotal.ToString("C");

        public string CustomerName => DraftInvoice?.CustomerName ?? "No customer selected";
        public int ItemCount => DraftItems.Count;

        #endregion

        #region Commands

        public ICommand CompleteInvoiceCommand { get; }
        public ICommand SaveDraftCommand { get; }
        public ICommand RejectDraftCommand { get; }
        public ICommand CancelCommand { get; }

        #endregion

        #region Command Implementations

        private bool CanCompleteInvoice()
        {
            return !IsLoading && 
                   !string.IsNullOrWhiteSpace(InvoiceNumber) && 
                   IssueDate <= DueDate &&
                   _permissionsService.CanCompleteInvoiceDrafts();
        }

        private async Task CompleteInvoiceAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Completing invoice...";

                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    StatusMessage = "Error: User not authenticated.";
                    return;
                }

                // Create completion data
                var completionData = new DraftInvoiceDto
                {
                    Id = DraftInvoice.Id,
                    InvoiceNumber = InvoiceNumber,
                    IssueDate = IssueDate,
                    DueDate = DueDate,
                    PaymentTerms = PaymentTerms,
                    Reference = Reference,
                    Notes = Notes,
                    DiscountAmount = DiscountAmount,
                    TaxAmount = TaxAmount
                };

                var result = await _draftInvoiceService.CompleteDraftInvoiceAsync(
                    DraftInvoice.Id, completionData, currentUser);

                if (result.Success)
                {
                    StatusMessage = "Invoice completed successfully!";
                    MessageBox.Show("Draft invoice has been completed and finalized successfully!", 
                                  "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    CloseDialog(true);
                }
                else
                {
                    StatusMessage = $"Error: {result.Message}";
                    MessageBox.Show($"Failed to complete invoice:\n{result.Message}", 
                                  "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error completing invoice: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[ADMIN_COMPLETION_VM] Error completing invoice: {ex.Message}");
                MessageBox.Show($"An error occurred while completing the invoice:\n{ex.Message}", 
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveDraft()
        {
            return !IsLoading && _permissionsService.CanCompleteInvoiceDrafts();
        }

        private async Task SaveDraftAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Saving draft changes...";

                // In a real implementation, you would save the current state as a draft
                // For now, we'll just show a message
                await Task.Delay(1000); // Simulate save operation

                StatusMessage = "Draft changes saved successfully.";
                MessageBox.Show("Draft changes have been saved. You can continue editing later.", 
                              "Draft Saved", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving draft: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[ADMIN_COMPLETION_VM] Error saving draft: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanRejectDraft()
        {
            return !IsLoading && _permissionsService.CanRejectDraftInvoices();
        }

        private async Task RejectDraftAsync()
        {
            try
            {
                var result = MessageBox.Show(
                    "Are you sure you want to reject this draft invoice?\nThis action cannot be undone.", 
                    "Confirm Rejection", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                IsLoading = true;
                StatusMessage = "Rejecting draft invoice...";

                // In a real implementation, you would call a reject method
                // For now, we'll simulate the operation
                await Task.Delay(1000);

                StatusMessage = "Draft invoice rejected.";
                MessageBox.Show("Draft invoice has been rejected. The creator will be notified.", 
                              "Draft Rejected", MessageBoxButton.OK, MessageBoxImage.Information);
                CloseDialog(false);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error rejecting draft: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[ADMIN_COMPLETION_VM] Error rejecting draft: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void Cancel()
        {
            CloseDialog(false);
        }

        #endregion

        #region Helper Methods

        private void InitializeFromDraft()
        {
            if (DraftInvoice == null) return;

            try
            {
                // Initialize form fields with draft data
                InvoiceNumber = DraftInvoice.InvoiceNumber;
                IssueDate = DraftInvoice.IssueDate;
                DueDate = DraftInvoice.DueDate;
                PaymentTerms = DraftInvoice.PaymentTerms ?? "Net 30";
                Reference = DraftInvoice.Reference;
                Notes = DraftInvoice.Notes;
                DiscountAmount = DraftInvoice.DiscountAmount;
                TaxAmount = DraftInvoice.TaxAmount;

                // Load draft items
                DraftItems.Clear();
                if (DraftInvoice.Items != null)
                {
                    foreach (var item in DraftInvoice.Items)
                    {
                        DraftItems.Add(item);
                    }
                }

                // Update computed properties
                OnPropertyChanged(nameof(DraftInfo));
                OnPropertyChanged(nameof(CustomerName));
                OnPropertyChanged(nameof(ItemCount));
                OnPropertyChanged(nameof(Subtotal));
                OnPropertyChanged(nameof(SubtotalDisplay));
                OnPropertyChanged(nameof(GrandTotal));
                OnPropertyChanged(nameof(GrandTotalDisplay));

                StatusMessage = "Ready to complete draft invoice.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading draft data: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[ADMIN_COMPLETION_VM] Error initializing from draft: {ex.Message}");
            }
        }

        private void UpdateCanExecuteCommands()
        {
            CommandManager.InvalidateRequerySuggested();
        }

        private void CloseDialog(bool result)
        {
            DialogResult = result;
            RequestClose?.Invoke();
        }

        #endregion

        #region Events

        public event PropertyChangedEventHandler PropertyChanged;
        public event Action RequestClose;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public bool? DialogResult { get; private set; }

        #endregion
    }
}
