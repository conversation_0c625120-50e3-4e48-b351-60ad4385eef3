﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddBusinessExpensesTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BusinessExpenses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Description = table.Column<string>(type: "TEXT", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", nullable: false),
                    Date = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Category = table.Column<int>(type: "INTEGER", nullable: false),
                    Frequency = table.Column<int>(type: "INTEGER", nullable: false),
                    NextDueDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: false),
                    CashDrawerId = table.Column<int>(type: "INTEGER", nullable: true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BusinessExpenses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BusinessExpenses_CashDrawers_CashDrawerId",
                        column: x => x.CashDrawerId,
                        principalTable: "CashDrawers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_BusinessExpenses_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6378), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6343), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6379) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6387), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6385), new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(6388) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 62, DateTimeKind.Local).AddTicks(1066));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7728));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7733));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 59, DateTimeKind.Local).AddTicks(7737));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6960));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6965));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6974));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6978));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6985));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6989));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(6994));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 15, 19, 56, 64, DateTimeKind.Local).AddTicks(7001));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 15, 19, 56, 60, DateTimeKind.Local).AddTicks(1650), new DateTime(2025, 2, 5, 15, 19, 56, 60, DateTimeKind.Local).AddTicks(1655) });

            migrationBuilder.CreateIndex(
                name: "IX_BusinessExpenses_CashDrawerId",
                table: "BusinessExpenses",
                column: "CashDrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessExpenses_UserId",
                table: "BusinessExpenses",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BusinessExpenses");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4592), new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4570), new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4592) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4598), new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4596), new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(4598) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 954, DateTimeKind.Local).AddTicks(5954));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5828));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5833));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 951, DateTimeKind.Local).AddTicks(5836));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8293));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8297));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8307));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8310));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8315));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8318));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8321));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 12, 43, 35, 957, DateTimeKind.Local).AddTicks(8352));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 12, 43, 35, 952, DateTimeKind.Local).AddTicks(103), new DateTime(2025, 2, 5, 12, 43, 35, 952, DateTimeKind.Local).AddTicks(109) });
        }
    }
}
