<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes">
    <!-- Lite profile: removes heavy effects, keeps minimal feedback -->

    <!-- Disable or minimize hover animations -->
    <Storyboard x:Key="ProductCardHoverEnterAnimation"/>
    <Storyboard x:Key="ProductCardHoverExitAnimation"/>

    <!-- Prefer low-quality scaling for images -->
    <Style TargetType="Image">
        <Setter Property="RenderOptions.BitmapScalingMode" Value="LowQuality"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- Product Card style without shadows -->
    <Style x:Key="ModernProductCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FAFBFF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Remove card elevation shadows globally -->
    <Style TargetType="md:Card">
        <Setter Property="md:ElevationAssist.Elevation" Value="Dp0"/>
    </Style>

</ResourceDictionary>

