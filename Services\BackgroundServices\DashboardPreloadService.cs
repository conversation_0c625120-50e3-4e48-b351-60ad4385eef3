using System;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using POSSystem.Services.Interfaces;
using POSSystem.Services.QueryOptimization;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Services.BackgroundServices
{
    /// <summary>
    /// ✅ NEW: Background service for preloading dashboard data to improve user experience
    /// Preloads critical dashboard data before user navigation to reduce perceived loading time
    /// </summary>
    public class DashboardPreloadService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DashboardPreloadService> _logger;
        private readonly Timer _preloadTimer;
        private readonly object _lockObject = new object();
        private bool _isPreloading = false;
        private DateTime _lastPreloadTime = DateTime.MinValue;

        // Preload intervals
        private static readonly TimeSpan PRELOAD_INTERVAL = TimeSpan.FromMinutes(10);
        private static readonly TimeSpan CACHE_REFRESH_INTERVAL = TimeSpan.FromMinutes(5);

        public DashboardPreloadService(IServiceProvider serviceProvider, ILogger<DashboardPreloadService> logger = null)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger;

            // Initialize timer for periodic preloading
            _preloadTimer = new Timer(OnPreloadTimer, null, TimeSpan.Zero, PRELOAD_INTERVAL);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger?.LogInformation("DashboardPreloadService started");

            try
            {
                // Initial preload
                await PreloadDashboardDataAsync(stoppingToken);

                // Keep service running
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogInformation("DashboardPreloadService was cancelled");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in DashboardPreloadService");
            }
        }

        /// <summary>
        /// Timer callback for periodic preloading
        /// </summary>
        private async void OnPreloadTimer(object state)
        {
            try
            {
                await PreloadDashboardDataAsync(CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in preload timer callback");
                Debug.WriteLine($"DashboardPreloadService timer error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CORE: Preload critical dashboard data in background
        /// </summary>
        public async Task PreloadDashboardDataAsync(CancellationToken cancellationToken = default)
        {
            lock (_lockObject)
            {
                if (_isPreloading)
                {
                    Debug.WriteLine("DashboardPreloadService: Preload already in progress, skipping");
                    return;
                }
                _isPreloading = true;
            }

            try
            {
                var stopwatch = Stopwatch.StartNew();
                Debug.WriteLine("DashboardPreloadService: Starting background preload...");

                using var scope = _serviceProvider.CreateScope();
                var dashboardQueryService = scope.ServiceProvider.GetService<DashboardQueryService>();
                var databaseService = scope.ServiceProvider.GetService<IDatabaseService>();

                if (dashboardQueryService == null || databaseService == null)
                {
                    Debug.WriteLine("DashboardPreloadService: Required services not available");
                    return;
                }

                // Define common date ranges for preloading
                var today = DateTime.Today;
                var yesterday = today.AddDays(-1);
                var thisWeek = today.AddDays(-7);
                var thisMonth = today.AddDays(-30);
                var now = DateTime.Now;

                // ✅ PRELOAD 1: Essential metrics for common periods
                var preloadTasks = new[]
                {
                    PreloadEssentialMetricsAsync(dashboardQueryService, today, now, "Today", cancellationToken),
                    PreloadEssentialMetricsAsync(dashboardQueryService, yesterday, yesterday.AddDays(1), "Yesterday", cancellationToken),
                    PreloadEssentialMetricsAsync(dashboardQueryService, thisWeek, now, "This Week", cancellationToken),
                    PreloadEssentialMetricsAsync(dashboardQueryService, thisMonth, now, "This Month", cancellationToken),
                };

                await Task.WhenAll(preloadTasks);

                // ✅ PRELOAD 2: Sales trend data for charts
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await dashboardQueryService.GetSalesTrendAsync(thisWeek, now);
                        await dashboardQueryService.GetSalesTrendAsync(thisMonth, now);
                        Debug.WriteLine("DashboardPreloadService: Sales trend data preloaded");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error preloading sales trend: {ex.Message}");
                    }
                }, cancellationToken);

                // ✅ PRELOAD 3: Product performance data
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await dashboardQueryService.GetTopProductsAsync(thisWeek, now, 25);
                        await dashboardQueryService.GetLowStockProductsAsync();
                        Debug.WriteLine("DashboardPreloadService: Product data preloaded");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error preloading product data: {ex.Message}");
                    }
                }, cancellationToken);

                stopwatch.Stop();
                _lastPreloadTime = DateTime.Now;

                Debug.WriteLine($"DashboardPreloadService: Background preload completed in {stopwatch.ElapsedMilliseconds}ms");
                _logger?.LogInformation($"Dashboard data preloaded in {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("DashboardPreloadService: Preload was cancelled");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardPreloadService error: {ex.Message}");
                _logger?.LogError(ex, "Error preloading dashboard data");
            }
            finally
            {
                lock (_lockObject)
                {
                    _isPreloading = false;
                }
            }
        }

        /// <summary>
        /// ✅ HELPER: Preload essential metrics for a specific period
        /// </summary>
        private async Task PreloadEssentialMetricsAsync(
            DashboardQueryService queryService, 
            DateTime startDate, 
            DateTime endDate, 
            string periodName,
            CancellationToken cancellationToken)
        {
            try
            {
                var metrics = await queryService.GetEssentialMetricsAsync(startDate, endDate);
                Debug.WriteLine($"DashboardPreloadService: Preloaded {periodName} metrics - Sales: {metrics.TotalSales:C}, Transactions: {metrics.TransactionCount}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading {periodName} metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PUBLIC: Force immediate preload (called when user is about to navigate to dashboard)
        /// </summary>
        public async Task ForcePreloadAsync()
        {
            try
            {
                Debug.WriteLine("DashboardPreloadService: Force preload requested");
                await PreloadDashboardDataAsync(CancellationToken.None);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in force preload: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ STATUS: Check if preload is current
        /// </summary>
        public bool IsPreloadCurrent()
        {
            return DateTime.Now - _lastPreloadTime < CACHE_REFRESH_INTERVAL;
        }

        public override void Dispose()
        {
            _preloadTimer?.Dispose();
            base.Dispose();
        }
    }
}
