using System;
using System.Windows;
using System.Globalization;

namespace POSSystem
{
    /// <summary>
    /// Simple test class to verify localization keys are working
    /// </summary>
    public static class LocalizationTest
    {
        public static void TestResourceKeys()
        {
            Console.WriteLine("=== Localization Test ===");
            
            // Test Out of Stock Dialog Keys
            TestResourceKey("OutOfStockCreateInvoiceTitle");
            TestResourceKey("OutOfStockCreateInvoiceMessage");
            TestResourceKey("OutOfStockTitle");
            TestResourceKey("OutOfStockMessage");
            
            // Test Print Dialog Keys
            TestResourceKey("ReprintReceiptDialog");
            TestResourceKey("ReprintReceiptFullMessage");
            TestResourceKey("PrintingStatus");
            TestResourceKey("ReceiptPrintingFailed");
            TestResourceKey("PrintResult");
            
            Console.WriteLine("=== Test Complete ===");
        }
        
        private static void TestResourceKey(string key)
        {
            try
            {
                var resource = Application.Current?.FindResource(key) as string;
                if (resource != null)
                {
                    Console.WriteLine($"✓ {key}: {resource.Substring(0, Math.Min(50, resource.Length))}...");
                }
                else
                {
                    Console.WriteLine($"✗ {key}: NOT FOUND");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ {key}: ERROR - {ex.Message}");
            }
        }
        
        public static void TestLanguageSwitching()
        {
            Console.WriteLine("\n=== Language Switching Test ===");

            // Test English
            Console.WriteLine("Testing English...");
            App.ApplyLanguage("en");
            TestResourceKey("OutOfStockCreateInvoiceTitle");
            TestResourceKey("ButtonYes");
            TestResourceKey("ButtonNo");
            TestResourceKey("ButtonCancel");

            // Test Arabic
            Console.WriteLine("Testing Arabic...");
            App.ApplyLanguage("ar");
            TestResourceKey("OutOfStockCreateInvoiceTitle");
            TestResourceKey("ButtonYes");
            TestResourceKey("ButtonNo");
            TestResourceKey("ButtonCancel");

            // Test French
            Console.WriteLine("Testing French...");
            App.ApplyLanguage("fr");
            TestResourceKey("OutOfStockCreateInvoiceTitle");
            TestResourceKey("ButtonYes");
            TestResourceKey("ButtonNo");
            TestResourceKey("ButtonCancel");

            // Reset to English
            App.ApplyLanguage("en");
            Console.WriteLine("=== Language Test Complete ===");
        }

        public static async System.Threading.Tasks.Task TestLocalizedMessageBox()
        {
            Console.WriteLine("\n=== LocalizedMessageBox Test ===");

            try
            {
                // Test different languages
                string[] languages = { "en", "fr", "ar" };

                foreach (var lang in languages)
                {
                    Console.WriteLine($"Testing {lang}...");
                    App.ApplyLanguage(lang);

                    // This would show the dialog in the current language
                    // Note: In actual testing, you would see the dialog with localized buttons
                    Console.WriteLine($"  - Button texts would be: {Application.Current.FindResource("ButtonYes")}, {Application.Current.FindResource("ButtonNo")}, {Application.Current.FindResource("ButtonCancel")}");
                }

                App.ApplyLanguage("en");
                Console.WriteLine("=== LocalizedMessageBox Test Complete ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in LocalizedMessageBox test: {ex.Message}");
            }
        }

        /// <summary>
        /// Test method to show an actual localized dialog (for manual testing)
        /// </summary>
        public static async System.Threading.Tasks.Task ShowTestDialog()
        {
            try
            {
                Console.WriteLine("Showing test dialog...");
                var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(
                    "This is a test message to verify localization is working.",
                    "Test Dialog",
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                Console.WriteLine($"Dialog result: {result}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error showing test dialog: {ex.Message}");
            }
        }

        /// <summary>
        /// Test out-of-stock dialog resource keys specifically
        /// </summary>
        public static void TestOutOfStockResources()
        {
            Console.WriteLine("\n=== Out of Stock Resource Test ===");

            string[] languages = { "en", "fr", "ar" };

            foreach (var lang in languages)
            {
                Console.WriteLine($"\nTesting {lang.ToUpper()}:");
                App.ApplyLanguage(lang);

                var title = Application.Current.FindResource("OutOfStockCreateInvoiceTitle") as string ?? "NOT FOUND";
                var message = Application.Current.FindResource("OutOfStockCreateInvoiceMessage") as string ?? "NOT FOUND";
                var buttonYes = Application.Current.FindResource("ButtonYes") as string ?? "NOT FOUND";
                var buttonNo = Application.Current.FindResource("ButtonNo") as string ?? "NOT FOUND";

                Console.WriteLine($"  Title: {title}");
                Console.WriteLine($"  Message: {message.Substring(0, Math.Min(50, message.Length))}...");
                Console.WriteLine($"  Button Yes: {buttonYes}");
                Console.WriteLine($"  Button No: {buttonNo}");
            }

            // Reset to English
            App.ApplyLanguage("en");
            Console.WriteLine("\n=== Out of Stock Resource Test Complete ===");
        }

        /// <summary>
        /// Test button resources specifically with detailed debugging
        /// </summary>
        public static void TestButtonResources()
        {
            Console.WriteLine("\n=== Button Resource Test ===");

            string[] languages = { "en", "fr", "ar" };
            string[] buttonKeys = { "ButtonYes", "ButtonNo", "ButtonCancel", "ButtonOK" };

            foreach (var lang in languages)
            {
                Console.WriteLine($"\nTesting {lang.ToUpper()} button resources:");
                App.ApplyLanguage(lang);

                foreach (var key in buttonKeys)
                {
                    try
                    {
                        var resource = Application.Current.FindResource(key);
                        var text = resource as string ?? "NOT STRING";
                        Console.WriteLine($"  {key}: '{text}' (Type: {resource?.GetType().Name ?? "NULL"})");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  {key}: ERROR - {ex.Message}");
                    }
                }
            }

            // Reset to English
            App.ApplyLanguage("en");
            Console.WriteLine("\n=== Button Resource Test Complete ===");
        }
    }
}
