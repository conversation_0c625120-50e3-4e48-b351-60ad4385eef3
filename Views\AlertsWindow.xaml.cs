using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Views
{
    public partial class AlertsWindow : Window
    {
        private readonly IAlertService _alertService;
        public event EventHandler NotificationCountChanged;
        private int _currentPage = 1;
        private const int PAGE_SIZE = 20;
        private int _totalPages;
        private bool _isLoading;
        private int _totalAlerts;

        public AlertsWindow(IAlertService alertService)
        {
            InitializeComponent();
            _alertService = alertService;
            LoadAlerts();
            SetupScrollViewer();
        }

        private void SetupScrollViewer()
        {
            var scrollViewer = GetScrollViewer(AlertsList);
            if (scrollViewer != null)
            {
                scrollViewer.ScrollChanged += ScrollViewer_ScrollChanged;
            }
        }

        private void ScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            var scrollViewer = (ScrollViewer)sender;
            
            // Check if we're near the bottom
            if (scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight * 0.8 && !_isLoading)
            {
                LoadMoreAlerts();
            }
        }

        private async void LoadMoreAlerts()
        {
            if (_isLoading || _currentPage >= _totalPages) 
            {
                LoadingIndicator.Visibility = Visibility.Collapsed;
                return;
            }
            
            _isLoading = true;
            LoadingIndicator.Visibility = Visibility.Visible;

            try
            {
                _currentPage++;
                var alerts = _alertService.GetAllAlerts(PAGE_SIZE, _currentPage);
                
                foreach (var alert in alerts)
                {
                    ((IList<ProductAlert>)AlertsList.ItemsSource).Add(alert);
                }
                UpdateNotificationCounter();

                // Hide loading indicator if we've loaded all items
                if (_currentPage >= _totalPages)
                {
                    LoadingIndicator.Visibility = Visibility.Collapsed;
                }
            }
            finally
            {
                _isLoading = false;
            }
        }

        private void LoadAlerts()
        {
            try
            {
                // Get total count and calculate total pages
                _totalAlerts = _alertService.GetTotalAlertsCount();
                _totalPages = (int)Math.Ceiling((double)_totalAlerts / PAGE_SIZE);

                // Load first page
                var alerts = _alertService.GetAllAlerts(PAGE_SIZE, 1);
                AlertsList.ItemsSource = new List<ProductAlert>(alerts);
                
                UpdateNotificationCounter();
                NotificationCountChanged?.Invoke(this, EventArgs.Empty);

                // If we have less items than page size, hide loading indicator
                if (alerts.Count < PAGE_SIZE)
                {
                    LoadingIndicator.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                var errorTitle = FindResourceString("DialogErrorTitle") ?? "Error";
                var errorTemplate = FindResourceString("ErrorLoadingData") ?? "Error loading data: {0}";
                var errorMessage = string.Format(errorTemplate, ex.Message);
                
                MessageBox.Show(errorMessage, errorTitle, MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private ScrollViewer GetScrollViewer(DependencyObject depObj)
        {
            if (depObj == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);
                
                if (child is ScrollViewer scrollViewer)
                    return scrollViewer;

                var childResult = GetScrollViewer(child);
                if (childResult != null)
                    return childResult;
            }
            return null;
        }

        private void UpdateNotificationCounter()
        {
            var loadedCount = ((IList<ProductAlert>)AlertsList.ItemsSource)?.Count ?? 0;
            var template = FindResourceString("ShowingNotifications") ?? "Showing {0} of {1} notifications";
            NotificationCounter.Text = string.Format(template, loadedCount, _totalAlerts);
        }

        private void MarkAsRead_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var alert = (ProductAlert)button.DataContext;
            _alertService.MarkAlertAsRead(alert.Id);
            
            // Remove from the current list
            ((IList<ProductAlert>)AlertsList.ItemsSource).Remove(alert);
            
            UpdateNotificationCounter();
            NotificationCountChanged?.Invoke(this, EventArgs.Empty);
        }

        private void MarkAllAsRead_Click(object sender, RoutedEventArgs e)
        {
            _alertService.MarkAllAlertsAsRead();
            LoadAlerts(); // Reload the list
            NotificationCountChanged?.Invoke(this, EventArgs.Empty);
        }
        
        private string FindResourceString(string key)
        {
            try
            {
                return Application.Current.TryFindResource(key)?.ToString();
            }
            catch
            {
                return null;
            }
        }
    }
} 