# Unified Data Access Migration Guide

## Overview

This guide explains how to migrate ViewModels from the mixed data access patterns (DatabaseService, Repositories, Management Services) to the new **UnifiedDataService** pattern.

## Benefits of UnifiedDataService

1. **Consistent API**: Single interface for all data operations
2. **Automatic Fallback**: Uses new management services with DatabaseService fallback
3. **Better Performance**: Leverages optimized management services
4. **Easier Testing**: Single service to mock
5. **Future-Proof**: Easy to swap implementations

## Migration Steps

### Step 1: Update Constructor

**Before (Mixed Pattern):**
```csharp
public ProductsViewModel(
    DatabaseService dbService,
    RepositoryServiceAdapter repositoryAdapter,
    IProductManagementService productService)
{
    _dbService = dbService;
    _repositoryAdapter = repositoryAdapter;
    _productService = productService;
}
```

**After (Unified Pattern):**
```csharp
public ProductsViewModel(IUnifiedDataService dataService)
{
    _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
}
```

### Step 2: Update Data Access Calls

**Before (Multiple Services):**
```csharp
// Mixed calls to different services
var products = await _productService.GetAllProductsAsync();
var sales = await _dbService.GetSalesForPeriodAsync(startDate, endDate);
var customers = _repositoryAdapter.GetCustomersAsync();
```

**After (Unified Service):**
```csharp
// Single service for all operations
var products = await _dataService.GetAllProductsAsync();
var sales = await _dataService.GetSalesAsync(startDate, endDate);
var customers = await _dataService.GetCustomersAsync();
```

### Step 3: High-Traffic Operations Migration Priority

**Priority 1 (Immediate Migration):**
- Product operations (GetProductsAsync, SearchProductsAsync)
- Sales operations (GetSalesAsync, SaveSaleAsync)
- Dashboard metrics (GetSalesCountAndTotalAsync)

**Priority 2 (Next Phase):**
- Customer operations
- User operations
- Inventory operations

**Priority 3 (Final Phase):**
- Statistics operations
- Complex analytics

## Example: ProductsViewModel Migration

### Before
```csharp
public class ProductsViewModel : INotifyPropertyChanged
{
    private readonly DatabaseService _dbService;
    private readonly RepositoryServiceAdapter _repositoryAdapter;
    private readonly IProductManagementService _productService;

    public ProductsViewModel(
        DatabaseService dbService,
        RepositoryServiceAdapter repositoryAdapter,
        IProductManagementService productService)
    {
        _dbService = dbService;
        _repositoryAdapter = repositoryAdapter;
        _productService = productService;
    }

    private async Task LoadProductsAsync()
    {
        try
        {
            // Complex logic to choose which service to use
            List<Product> products;
            if (_repositoryAdapter != null)
            {
                products = await _repositoryAdapter.GetProductsAsync();
            }
            else if (_productService != null)
            {
                products = await _productService.GetAllProductsAsync();
            }
            else
            {
                products = _dbService.GetAllProducts();
            }
            
            Products.Clear();
            foreach (var product in products)
            {
                Products.Add(product);
            }
        }
        catch (Exception ex)
        {
            // Handle error
        }
    }
}
```

### After
```csharp
public class ProductsViewModel : INotifyPropertyChanged
{
    private readonly IUnifiedDataService _dataService;

    public ProductsViewModel(IUnifiedDataService dataService)
    {
        _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
    }

    private async Task LoadProductsAsync()
    {
        try
        {
            // Simple, consistent API with automatic fallback
            var products = await _dataService.GetAllProductsAsync();
            
            Products.Clear();
            foreach (var product in products)
            {
                Products.Add(product);
            }
        }
        catch (Exception ex)
        {
            // Handle error
        }
    }
}
```

## Example: SaleViewModel Migration

### Before
```csharp
private async Task SaveSaleAsync()
{
    try
    {
        int saleId;
        if (_salesService != null)
        {
            saleId = await _salesService.SaveSaleAsync(currentSale);
        }
        else
        {
            saleId = _dbService.SaveSale(currentSale);
        }
        // Handle success
    }
    catch (Exception ex)
    {
        // Handle error
    }
}
```

### After
```csharp
private async Task SaveSaleAsync()
{
    try
    {
        var saleId = await _dataService.SaveSaleAsync(currentSale);
        // Handle success
    }
    catch (Exception ex)
    {
        // Handle error
    }
}
```

## Example: DashboardViewModel Migration

### Before
```csharp
private async Task LoadDashboardDataAsync()
{
    // Complex adapter pattern
    var adapter = new DatabaseServiceAdapter(_dbService);
    var sales = await adapter.GetSalesAsync(startDate, endDate);
    var lowStock = await adapter.GetLowStockProductsAsync();
    var customers = await adapter.GetCustomersAsync();
}
```

### After
```csharp
private async Task LoadDashboardDataAsync()
{
    // Simple unified calls
    var sales = await _dataService.GetSalesAsync(startDate, endDate);
    var lowStock = await _dataService.GetLowStockProductsAsync();
    var customers = await _dataService.GetCustomersAsync();
}
```

## Testing Benefits

### Before (Multiple Mocks)
```csharp
[Test]
public void TestProductLoading()
{
    var mockDbService = new Mock<IDatabaseService>();
    var mockRepository = new Mock<RepositoryServiceAdapter>();
    var mockProductService = new Mock<IProductManagementService>();
    
    // Setup multiple mocks...
    var viewModel = new ProductsViewModel(mockDbService.Object, mockRepository.Object, mockProductService.Object);
}
```

### After (Single Mock)
```csharp
[Test]
public void TestProductLoading()
{
    var mockDataService = new Mock<IUnifiedDataService>();
    mockDataService.Setup(x => x.GetAllProductsAsync()).ReturnsAsync(testProducts);
    
    var viewModel = new ProductsViewModel(mockDataService.Object);
}
```

## Migration Checklist

- [ ] Update ViewModel constructor to use IUnifiedDataService
- [ ] Replace all data access calls with unified service methods
- [ ] Remove unused service dependencies
- [ ] Update unit tests to use single mock
- [ ] Test fallback behavior works correctly
- [ ] Verify performance improvements

## Rollback Strategy

If issues arise, the UnifiedDataService can be temporarily disabled by:
1. Commenting out the service registration
2. Reverting ViewModel constructors
3. The old services remain available as fallback

## Performance Monitoring

Monitor these metrics after migration:
- Database query count reduction
- Response time improvements
- Memory usage optimization
- Error rate changes

## Next Steps

After ViewModels are migrated:
1. Gradually remove direct DatabaseService dependencies
2. Enhance management services with more optimizations
3. Add caching layer to UnifiedDataService
4. Implement analytics service for complex operations
