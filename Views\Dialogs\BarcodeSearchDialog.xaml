<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.BarcodeSearchDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="420" d:DesignWidth="400"
             Background="Transparent">
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MaxWidth="400"
                         Margin="16">
        <Grid>
            <!-- Header Section with Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="12,12,0,0"
                    Padding="24,16">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Barcode" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="{DynamicResource BarcodeSearchTitle}" 
                             FontSize="22"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section -->
            <StackPanel Grid.Row="1" Margin="24,20">
                <!-- Barcode Entry Field -->
                <StackPanel Margin="0,0,0,24">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="BarcodeScan" 
                                               Width="20" 
                                               Height="20"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="{DynamicResource EnterBarcode}" 
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                 FontWeight="Medium"/>
                    </StackPanel>
                    
                    <TextBox x:Name="txtBarcode"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="{DynamicResource ScanOrTypeBarcode}"
                           materialDesign:TextFieldAssist.HasClearButton="True"
                           FontSize="18"
                           Margin="0,0,0,4"
                           KeyDown="TxtBarcode_KeyDown"/>
                    
                    <TextBlock Text="{DynamicResource PressEnterToSearch}" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Margin="4,0,0,0"/>
                </StackPanel>
                
                <!-- Information Message -->
                <TextBlock Text="{DynamicResource ScanBarcodeDescription}"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         TextWrapping="Wrap"
                         TextAlignment="Center"
                         Margin="0,0,0,24"/>
                
                <!-- Result Message -->
                <TextBlock x:Name="txtResult"
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         Foreground="{DynamicResource SecondaryHueMidBrush}"
                         TextWrapping="Wrap"
                         TextAlignment="Center"
                         Visibility="Collapsed"
                         Margin="0,0,0,24"/>
                
                <!-- Button Panel with Material Design Styling -->
                <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                      BorderThickness="0,1,0,0"
                      Padding="0,24,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Cancel Button -->
                        <Button x:Name="btnCancel" 
                              Grid.Column="0"
                              Content="{DynamicResource Cancel}"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Height="40"
                              Margin="0,0,8,0"
                              HorizontalAlignment="Stretch"
                              Click="BtnCancel_Click"/>
                        
                        <!-- Search Button -->
                        <Button x:Name="btnSearch" 
                              Grid.Column="1"
                              Style="{StaticResource MaterialDesignRaisedButton}"
                              Background="{DynamicResource PrimaryHueMidBrush}"
                              Foreground="{DynamicResource MaterialDesignPaper}"
                              materialDesign:ButtonAssist.CornerRadius="6"
                              Height="40"
                              Margin="8,0,0,0"
                              HorizontalAlignment="Stretch"
                              Click="BtnSearch_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" 
                                                       Width="18" 
                                                       Height="18"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Search}"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl> 