<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:System="clr-namespace:System;assembly=mscorlib"
             FlowDirection="{Binding CurrentFlowDirection}"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            
            <Style x:Key="SettingsNavButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="50"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="0"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Margin" Value="0,2"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Selection indicator -->
                                <Border x:Name="SelectionIndicator"
                                        Width="4"
                                        HorizontalAlignment="Left"
                                        Background="{DynamicResource PrimaryHueMidBrush}"
                                        CornerRadius="0,2,2,0"
                                        Opacity="0"/>

                                <!-- Button background -->
                                <Border x:Name="ButtonBackground"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="{Binding Path=(materialDesign:ButtonAssist.CornerRadius), RelativeSource={RelativeSource TemplatedParent}}"/>

                                <!-- Content -->
                                <ContentPresenter x:Name="ContentPresenter"
                                                Margin="{TemplateBinding Padding}"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}"/>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="ButtonBackground" Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                </Trigger>
                                <Trigger Property="Tag" Value="Selected">
                                    <Setter TargetName="ButtonBackground" Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter TargetName="SelectionIndicator" Property="Opacity" Value="1"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="ButtonBackground" Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <Style x:Key="SettingsSectionHeader" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
            </Style>
            
            <Style x:Key="SettingsCard" TargetType="Border">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="24"/>
                <Setter Property="Margin" Value="0,0,0,24"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="2" Direction="270" Color="#20000000" Opacity="0.3"/>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <Style x:Key="SettingsLabel" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="Margin" Value="0,0,0,8"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Background="{DynamicResource MaterialDesignPaper}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="240"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Navigation Sidebar -->
        <Border Grid.Column="0" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,0,1,0">
            <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

                <!-- Settings Title -->
        <TextBlock Text="{DynamicResource Settings}" 
                 FontSize="24" 
                 FontWeight="SemiBold" 
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Margin="24,32,24,24"/>
                
                <!-- Navigation -->
                <StackPanel Grid.Row="1" Margin="0,10,0,0">
                    <Button x:Name="btnLanguageRegional" Style="{StaticResource SettingsNavButton}"
                            Tag="Selected"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Earth"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource LanguageAndRegional}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnTheme" Style="{StaticResource SettingsNavButton}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Palette"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource ThemeSettings}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnCompany" Style="{StaticResource SettingsNavButton}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Store"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource CompanySettings}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnLoyalty" Style="{StaticResource SettingsNavButton}"
                            Visibility="{Binding HasLoyaltyAccess, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CardAccountDetails"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource LoyaltyProgramSettings}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnDiscountPermissions" Style="{StaticResource SettingsNavButton}"
                            Visibility="{Binding HasLoyaltyAccess, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Percent"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource DiscountPermissions}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnReceiptPrinting" Style="{StaticResource SettingsNavButton}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource ReceiptPrinting}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="btnDatabase" Style="{StaticResource SettingsNavButton}"
                            Click="OnNavButtonClick">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Database"
                                                   Width="24"
                                                   Height="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,16,0"/>
                            <TextBlock Text="{DynamicResource DatabaseSettings}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Settings Content -->
        <ScrollViewer Grid.Column="1" x:Name="SettingsScrollViewer" Padding="32,32,32,80">
            <!-- TabControl with no headers, for navigation -->
            <TabControl x:Name="SettingsTabs" Margin="0" BorderThickness="0" Background="Transparent"
                        Padding="0" SelectedIndex="0" TabStripPlacement="Left" Visibility="Visible">
                <!-- Language & Regional Tab -->
                <TabItem x:Name="LanguageRegionalTab">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <StackPanel Margin="0">
                            <!-- Header Section -->
                            <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Earth"
                                                           Width="32"
                                                           Height="32"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           Margin="0,0,16,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{DynamicResource LanguageAndRegionalSettings}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="20"/>
                                        <TextBlock Text="{DynamicResource LanguageAndRegionalDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  TextWrapping="Wrap"
                                                  FontSize="12"
                                                  LineHeight="16"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Main Content Grid -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column - Language Settings -->
                                <StackPanel Grid.Column="0">
                                    <!-- Language Configuration Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="Translate"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource LanguageConfiguration}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource SelectDisplayLanguageDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,16"/>

                                            <TextBlock Text="{DynamicResource SelectLanguage}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <ComboBox ItemsSource="{Binding AvailableLanguages}"
                                                     SelectedValue="{Binding Settings.Display.Language}"
                                                     SelectedValuePath="Code"
                                                     DisplayMemberPath="DisplayName"
                                                     materialDesign:HintAssist.Hint="{DynamicResource Language}"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                     Margin="0,0,0,16"/>

                                            <Border Background="{DynamicResource MaterialDesignSelection}"
                                                    CornerRadius="4"
                                                    Padding="12">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Information"
                                                                           Width="16"
                                                                           Height="16"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                           Margin="0,0,8,0"/>
                                                    <TextBlock Text="{DynamicResource RestartRequired}"
                                                              Foreground="{DynamicResource MaterialDesignBody}"
                                                              FontSize="11"
                                                              VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>

                                <!-- Right Column - Regional Settings -->
                                <StackPanel Grid.Column="2">
                                    <!-- Regional Configuration Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="MapMarker"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource RegionalConfiguration}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource RegionalConfigurationDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,16"/>

                                            <!-- Date Format -->
                                            <TextBlock Text="{DynamicResource DateFormat}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>
                                            <ComboBox ItemsSource="{Binding AvailableDateFormats}"
                                                     SelectedValue="{Binding Settings.Regional.DateFormat}"
                                                     SelectedValuePath="Code"
                                                     DisplayMemberPath="DisplayName"
                                                     materialDesign:HintAssist.Hint="{DynamicResource DateFormat}"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                     Margin="0,0,0,16"/>

                                            <!-- Currency -->
                                            <TextBlock Text="{DynamicResource CurrencySymbol}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>
                                            <ComboBox ItemsSource="{Binding AvailableCurrencies}"
                                                     SelectedValue="{Binding Settings.Regional.Currency}"
                                                     SelectedValuePath="Code"
                                                     DisplayMemberPath="DisplayName"
                                                     materialDesign:HintAssist.Hint="{DynamicResource Currency}"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>

                            <!-- Preview Section -->
                            <Border Style="{StaticResource SettingsCard}">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Kind="Eye"
                                                               Width="20"
                                                               Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               Margin="0,0,12,0"/>

                                        <TextBlock Grid.Column="1"
                                                  Text="{DynamicResource FormatPreview}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="16"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <TextBlock Text="{DynamicResource FormatPreviewDescription}"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                                              FontSize="12"
                                              Margin="0,0,0,16"/>

                                    <Border Background="{DynamicResource MaterialDesignSelection}"
                                            CornerRadius="8"
                                            Padding="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="1*"/>
                                                <ColumnDefinition Width="1*"/>
                                                <ColumnDefinition Width="1*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Date Preview -->
                                            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                                <TextBlock Text="{DynamicResource DateFormatLabel}"
                                                          FontWeight="SemiBold"
                                                          FontSize="11"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Source={x:Static System:DateTime.Now}, StringFormat='{}{0:d}'}"
                                                          FontSize="12"
                                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </StackPanel>

                                            <!-- Currency Preview -->
                                            <StackPanel Grid.Column="1" Margin="8,0">
                                                <TextBlock Text="{DynamicResource CurrencyFormatLabel}"
                                                          FontWeight="SemiBold"
                                                          FontSize="11"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="$1,234.56"
                                                          FontSize="12"
                                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </StackPanel>

                                            <!-- Number Preview -->
                                            <StackPanel Grid.Column="2" Margin="16,0,0,0">
                                                <TextBlock Text="{DynamicResource NumberFormatLabel}"
                                                          FontWeight="SemiBold"
                                                          FontSize="11"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="1,234.56"
                                                          FontSize="12"
                                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
                
                <!-- Theme Tab -->
                <TabItem x:Name="ThemeTab">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <StackPanel Margin="0">
                            <!-- Header Section -->
                            <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Palette"
                                                           Width="32"
                                                           Height="32"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           Margin="0,0,16,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{DynamicResource ThemeAndAppearanceSettings}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="20"/>
                                        <TextBlock Text="{DynamicResource ThemeAndAppearanceDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  TextWrapping="Wrap"
                                                  FontSize="12"
                                                  LineHeight="16"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Main Content Grid -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column - Settings -->
                                <StackPanel Grid.Column="0">
                                    <!-- Theme Mode Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="WeatherNight"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource ThemeModeLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <!-- Dark/Light Mode Toggle -->
                                            <Border Background="{DynamicResource MaterialDesignSelection}"
                                                    CornerRadius="8"
                                                    Padding="16">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="WeatherSunny"
                                                                               Width="18"
                                                                               Height="18"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,0"/>
                                                        <TextBlock Text="{DynamicResource LightMode}"
                                                                  VerticalAlignment="Center"
                                                                  FontWeight="Medium"/>
                                                    </StackPanel>

                                                    <ToggleButton Grid.Column="1"
                                                                 IsChecked="{Binding IsDarkTheme}"
                                                                 Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                                 ToolTip="{DynamicResource DarkThemeToggle}"
                                                                 HorizontalAlignment="Center"
                                                                 VerticalAlignment="Center"/>

                                                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                                        <TextBlock Text="{DynamicResource DarkMode}"
                                                                  VerticalAlignment="Center"
                                                                  FontWeight="Medium"
                                                                  Margin="0,0,8,0"/>
                                                        <materialDesign:PackIcon Kind="WeatherNight"
                                                                               Width="18"
                                                                               Height="18"
                                                                               VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </StackPanel>
                                    </Border>

                                    <!-- Performance Lite UI Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="Speedometer"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="Performance Lite UI"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                                    <TextBlock Text="Disable costly visual effects and animations for higher FPS"
                                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                               FontSize="12"
                                                               Margin="0,0,0,6"/>
                                                    <TextBlock Text="Applies on next app restart"
                                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                               FontSize="10"/>
                                                </StackPanel>

                                                <ToggleButton Grid.Column="1"
                                                              IsChecked="{Binding PerformanceLiteUI, Mode=TwoWay}"
                                                              Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                              ToolTip="Use low-cost UI rendering"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- Theme Presets Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="FormatPaint"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource ThemePresetsLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource ThemePresetsDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,12"/>

                                            <ComboBox x:Name="ThemePresetsComboBox"
                                                     ItemsSource="{Binding AvailableThemes}"
                                                     SelectedValue="{Binding SelectedTheme}"
                                                     SelectedValuePath="Code"
                                                     DisplayMemberPath="DisplayName"
                                                     materialDesign:HintAssist.Hint="{DynamicResource SelectTheme}"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}"/>

                                            <!-- Performance Profile -->
                                            <TextBlock Text="Performance Profile" Margin="0,16,0,8"/>
                                            <ComboBox x:Name="PerformanceProfileComboBox"
                                                     SelectedValue="{Binding PerformanceProfile}"
                                                     materialDesign:HintAssist.Hint="Choose: Standard / Lite / UltraLite"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}">
                                                <ComboBoxItem Content="Standard"/>
                                                <ComboBoxItem Content="Lite"/>
                                                <ComboBoxItem Content="UltraLite"/>
                                            </ComboBox>
                                        </StackPanel>
                                    </Border>
                                    <!-- Color Customization Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="ColorLens"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource ColorCustomizationLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource ColorCustomizationDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,16"/>

                                            <!-- Color Picker Section -->
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Custom Color Picker -->
                                                <StackPanel Grid.Row="0" Margin="0,0,0,16">
                                                    <TextBlock Text="{DynamicResource CustomColorLabel}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <materialDesign:ColorPicker Color="{Binding SelectedColor}"
                                                                              Width="280"
                                                                              HorizontalAlignment="Left"/>
                                                </StackPanel>

                                                <!-- Color Presets -->
                                                <StackPanel Grid.Row="1">
                                                    <TextBlock Text="{DynamicResource QuickColorPresetsLabel}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <WrapPanel Orientation="Horizontal">
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#2196F3" BorderBrush="#1976D2" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#2196F3"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Material Blue">
                                                            <materialDesign:PackIcon Kind="Check"
                                                                                   Width="16" Height="16"
                                                                                   Foreground="White"
                                                                                   Visibility="Collapsed"/>
                                                        </Button>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#4CAF50" BorderBrush="#388E3C" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#4CAF50"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Material Green"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#F44336" BorderBrush="#D32F2F" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#F44336"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Material Red"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#9C27B0" BorderBrush="#7B1FA2" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#9C27B0"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Material Purple"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#FF9800" BorderBrush="#F57C00" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#FF9800"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Material Orange"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#607D8B" BorderBrush="#455A64" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#607D8B"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Blue Grey"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#795548" BorderBrush="#5D4037" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#795548"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Brown"/>
                                                        <Button Width="40" Height="40" Padding="0" Margin="0,0,8,8"
                                                               Background="#009688" BorderBrush="#00796B" BorderThickness="2"
                                                               Command="{Binding SetColorCommand}" CommandParameter="#009688"
                                                               materialDesign:ButtonAssist.CornerRadius="20"
                                                               ToolTip="Teal"/>
                                                    </WrapPanel>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- Sales Layout Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="Sales Interface Layout"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="Choose the layout style for your sales interface"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,12"/>

                                            <ComboBox x:Name="SalesLayoutComboBox"
                                                     ItemsSource="{Binding AvailableSalesLayoutThemes}"
                                                     SelectedValue="{Binding Settings.Display.SalesLayoutTheme}"
                                                     SelectedValuePath="Code"
                                                     DisplayMemberPath="DisplayName"
                                                     materialDesign:HintAssist.Hint="{DynamicResource ChangeLayout}"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"
                                                     Background="{DynamicResource MaterialDesignPaper}"
                                                     BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                     Margin="0,0,0,8"/>

                                            <Border Background="{DynamicResource MaterialDesignSelection}"
                                                    CornerRadius="4"
                                                    Padding="12"
                                                    Visibility="{Binding SelectedItem, ElementName=SalesLayoutComboBox, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <TextBlock Text="{Binding SelectedItem.Description, ElementName=SalesLayoutComboBox}"
                                                          FontStyle="Italic"
                                                          Foreground="{DynamicResource MaterialDesignBody}"
                                                          TextWrapping="Wrap"
                                                          FontSize="11"/>
                                            </Border>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                                <!-- Right Column - Preview Panel -->
                                <Border Grid.Column="2"
                                        Style="{StaticResource SettingsCard}"
                                        MinHeight="500">
                                    <StackPanel>
                                        <Grid Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Kind="Eye"
                                                                   Width="20"
                                                                   Height="20"
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                   Margin="0,0,12,0"/>

                                            <TextBlock Grid.Column="1"
                                                      Text="{DynamicResource LivePreview}"
                                                      Style="{StaticResource SettingsSectionHeader}"
                                                      FontSize="16"
                                                      VerticalAlignment="Center"/>
                                        </Grid>

                                        <TextBlock Text="{DynamicResource LivePreviewDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  FontSize="11"
                                                  Margin="0,0,0,16"/>

                                        <!-- Enhanced Preview Content -->
                                        <Border Background="{DynamicResource MaterialDesignSelection}"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="0,0,0,16">
                                            <StackPanel>
                                                <!-- Sample Header -->
                                                <Grid Margin="0,0,0,12">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <materialDesign:PackIcon Kind="Store"
                                                                           Width="16"
                                                                           Height="16"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                           Margin="0,0,8,0"/>

                                                    <TextBlock Grid.Column="1"
                                                              Text="{DynamicResource SamplePOSInterface}"
                                                              FontWeight="SemiBold"
                                                              FontSize="12"/>
                                                </Grid>

                                                <!-- Sample Content -->
                                                <TextBlock Text="{DynamicResource SampleProductName}"
                                                          FontSize="11"
                                                          Margin="0,0,0,8"/>

                                                <TextBlock Text="{DynamicResource SamplePrice}"
                                                          FontSize="11"
                                                          Margin="0,0,0,12"/>

                                                <!-- Sample Buttons -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Button Content="{DynamicResource AddToCart}"
                                                            Style="{StaticResource MaterialDesignRaisedButton}"
                                                            FontSize="10"
                                                            Padding="8,4"
                                                            Margin="0,0,8,0"/>

                                                    <Button Content="{DynamicResource Details}"
                                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            FontSize="10"
                                                            Padding="8,4"/>
                                                </StackPanel>

                                                <!-- Sample Toggle -->
                                                <Grid Margin="0,0,0,12">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Text="{DynamicResource Available}"
                                                              FontSize="11"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>

                                                    <ToggleButton Grid.Column="1"
                                                                 Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                                 IsChecked="True"
                                                                 HorizontalAlignment="Left"/>
                                                </Grid>

                                                <!-- Sample Input -->
                                                <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                        materialDesign:HintAssist.Hint="{DynamicResource SearchProductsHint}"
                                                        FontSize="11"
                                                        Height="32"
                                                        Margin="0,0,0,12"/>

                                                <!-- Primary Color Sample -->
                                                <Border Background="{DynamicResource PrimaryHueMidBrush}"
                                                        Height="32"
                                                        CornerRadius="4">
                                                    <TextBlock Text="{DynamicResource PrimaryThemeColorLabel}"
                                                              Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                                              VerticalAlignment="Center"
                                                              HorizontalAlignment="Center"
                                                              FontSize="11"
                                                              FontWeight="Medium"/>
                                                </Border>
                                            </StackPanel>
                                        </Border>

                                        <!-- Theme Info -->
                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                                BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Padding="12">
                                            <StackPanel>
                                                <TextBlock Text="{DynamicResource CurrentThemeInfoLabel}"
                                                          FontWeight="SemiBold"
                                                          FontSize="11"
                                                          Margin="0,0,0,8"/>

                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <TextBlock Grid.Row="0" Grid.Column="0"
                                                              Text="{DynamicResource ModeLabel}"
                                                              FontSize="10"
                                                              Margin="0,0,8,4"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1"
                                                              Text="{Binding IsDarkTheme, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='Dark|Light'}"
                                                              FontSize="10"
                                                              FontWeight="Medium"
                                                              Margin="0,0,0,4"/>

                                                    <TextBlock Grid.Row="1" Grid.Column="0"
                                                              Text="{DynamicResource PresetLabel}"
                                                              FontSize="10"
                                                              Margin="0,0,8,4"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1"
                                                              Text="{Binding SelectedTheme}"
                                                              FontSize="10"
                                                              FontWeight="Medium"
                                                              Margin="0,0,0,4"/>

                                                    <TextBlock Grid.Row="2" Grid.Column="0"
                                                              Text="{DynamicResource LayoutLabel}"
                                                              FontSize="10"
                                                              Margin="0,0,8,0"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1"
                                                              Text="{Binding Settings.Display.SalesLayoutTheme}"
                                                              FontSize="10"
                                                              FontWeight="Medium"/>
                                                </Grid>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
                
                <!-- Company Tab -->
                <TabItem x:Name="CompanyTab">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <StackPanel Margin="0">
                            <!-- Header Section -->
                            <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Store"
                                                           Width="32"
                                                           Height="32"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           Margin="0,0,16,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{DynamicResource CompanyInformation}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="20"/>
                                        <TextBlock Text="{DynamicResource CompanyInformationDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  TextWrapping="Wrap"
                                                  FontSize="12"
                                                  LineHeight="16"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Main Content Grid -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column - Company Details -->
                                <StackPanel Grid.Column="0">
                                    <!-- Basic Information Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="Information"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource BasicInformation}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="1*"/>
                                                    <ColumnDefinition Width="16"/>
                                                    <ColumnDefinition Width="1*"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Company Name -->
                                                <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,0,16">
                                                    <TextBlock Text="{DynamicResource CompanyName}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <TextBox Text="{Binding Settings.Company.CompanyName, UpdateSourceTrigger=PropertyChanged}"
                                                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                            materialDesign:HintAssist.Hint="{DynamicResource CompanyName}"
                                                            Foreground="{DynamicResource MaterialDesignBody}"
                                                            Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                            CaretBrush="{DynamicResource MaterialDesignBody}"/>
                                                </StackPanel>

                                                <!-- Phone -->
                                                <StackPanel Grid.Column="2" Grid.Row="0" Margin="0,0,0,16">
                                                    <TextBlock Text="{DynamicResource Phone}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <TextBox Text="{Binding Settings.Company.Phone, UpdateSourceTrigger=PropertyChanged}"
                                                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                            materialDesign:HintAssist.Hint="{DynamicResource Phone}"
                                                            Foreground="{DynamicResource MaterialDesignBody}"
                                                            Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                            CaretBrush="{DynamicResource MaterialDesignBody}"/>
                                                </StackPanel>

                                                <!-- Email -->
                                                <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,0,16">
                                                    <TextBlock Text="{DynamicResource Email}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <TextBox Text="{Binding Settings.Company.Email, UpdateSourceTrigger=PropertyChanged}"
                                                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                            materialDesign:HintAssist.Hint="{DynamicResource Email}"
                                                            Foreground="{DynamicResource MaterialDesignBody}"
                                                            Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                            CaretBrush="{DynamicResource MaterialDesignBody}"/>
                                                </StackPanel>

                                                <!-- Tax Rate -->
                                                <StackPanel Grid.Column="2" Grid.Row="1" Margin="0,0,0,16">
                                                    <TextBlock Text="{DynamicResource TaxRate}"
                                                              Style="{StaticResource SettingsLabel}"
                                                              FontSize="12"
                                                              Margin="0,0,0,8"/>
                                                    <TextBox Text="{Binding Settings.Company.TaxRate, UpdateSourceTrigger=PropertyChanged, StringFormat=P1}"
                                                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                            materialDesign:HintAssist.Hint="{DynamicResource TaxRate}"
                                                            Foreground="{DynamicResource MaterialDesignBody}"
                                                            Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                            CaretBrush="{DynamicResource MaterialDesignBody}"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- Address Information Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="MapMarker"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource AddressInformation}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource Address}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>
                                            <TextBox Text="{Binding Settings.Company.Address, UpdateSourceTrigger=PropertyChanged}"
                                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                    materialDesign:HintAssist.Hint="{DynamicResource Address}"
                                                    Foreground="{DynamicResource MaterialDesignBody}"
                                                    Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                    CaretBrush="{DynamicResource MaterialDesignBody}"
                                                    Margin="0,0,0,16"/>

                                            <!-- Receipt Footer -->
                                            <TextBlock Text="{DynamicResource ReceiptFooter}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>
                                            <TextBox Text="{Binding Settings.Company.ReceiptFooter, UpdateSourceTrigger=PropertyChanged}"
                                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                    Height="80"
                                                    TextWrapping="Wrap"
                                                    AcceptsReturn="True"
                                                    materialDesign:HintAssist.Hint="{DynamicResource ReceiptFooter}"
                                                    VerticalAlignment="Stretch"
                                                    VerticalScrollBarVisibility="Auto"
                                                    Foreground="{DynamicResource MaterialDesignBody}"
                                                    Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                    CaretBrush="{DynamicResource MaterialDesignBody}"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                                <!-- Right Column - Logo & Branding -->
                                <StackPanel Grid.Column="2">
                                    <!-- Company Logo Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="Image"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource CompanyLogo}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource CompanyLogoDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,16"/>

                                            <!-- Logo Display -->
                                            <Border Width="180"
                                                    Height="120"
                                                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                    BorderThickness="2"
                                                    CornerRadius="8"
                                                    Background="{DynamicResource MaterialDesignSelection}"
                                                    Margin="0,0,0,16">

                                                <!-- Logo Image -->
                                                <Grid>
                                                    <!-- Placeholder when no logo is available -->
                                                    <StackPanel x:Name="LogoPlaceholder"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"
                                                               Visibility="{Binding IsLogoEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <materialDesign:PackIcon Kind="ImageOutline"
                                                                               Width="48"
                                                                               Height="48"
                                                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                               Margin="0,0,0,8"/>
                                                        <TextBlock Text="{DynamicResource NoLogo}"
                                                                  FontSize="11"
                                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                  HorizontalAlignment="Center"/>
                                                    </StackPanel>

                                                    <!-- Actual Logo Image -->
                                                    <Image x:Name="LogoImage"
                                                         Source="{Binding LogoImageSource}"
                                                         Stretch="Uniform"
                                                         Margin="8"
                                                         Visibility="{Binding IsLogoEmpty, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                                                </Grid>
                                            </Border>

                                            <!-- Logo Action Buttons -->
                                            <StackPanel>
                                                <Button Command="{Binding UploadLogoCommand}"
                                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                                        Margin="0,0,0,8"
                                                        HorizontalAlignment="Stretch">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="Upload"
                                                                               Width="16"
                                                                               Height="16"
                                                                               Margin="0,0,8,0"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{DynamicResource UploadLogo}"
                                                                 VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Button>

                                                <Button Command="{Binding RemoveLogoCommand}"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        IsEnabled="{Binding HasLogo}"
                                                        HorizontalAlignment="Stretch">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="Delete"
                                                                               Width="16"
                                                                               Height="16"
                                                                               Margin="0,0,8,0"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{DynamicResource RemoveLogo}"
                                                                 VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
                
                <!-- Loyalty Tab -->
                <TabItem x:Name="LoyaltyTab" 
                        Visibility="{Binding HasLoyaltyAccess, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <materialDesign:PackIcon Kind="CardAccountDetails" 
                                                   Width="28" 
                                                   Height="28" 
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,16,0"/>
                            
                            <TextBlock Grid.Column="1" 
                                       Text="{DynamicResource LoyaltyProgramSettings}" 
                                       Style="{StaticResource SettingsSectionHeader}"/>
                        </Grid>
                        
                        <Border Style="{StaticResource SettingsCard}">
                        <local:LoyaltyProgramView x:Name="loyaltyProgramView" x:FieldModifier="private"/>
                        </Border>
                    </StackPanel>
                </TabItem>

                <!-- Discount Permissions Tab -->
                <TabItem x:Name="DiscountPermissionsTab" 
                        Visibility="{Binding HasLoyaltyAccess, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <materialDesign:PackIcon Kind="Percent" 
                                                   Width="28" 
                                                   Height="28" 
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,16,0"/>
                            
                            <TextBlock Grid.Column="1" 
                                       Text="{DynamicResource DiscountPermissionsSettings}" 
                                       Style="{StaticResource SettingsSectionHeader}"/>
                        </Grid>
                        
                        <Border Style="{StaticResource SettingsCard}">
                            <local:DiscountPermissionsView x:Name="discountPermissionsView" x:FieldModifier="private"/>
                        </Border>
                    </StackPanel>
                </TabItem>

                <!-- Database Tab -->
                <TabItem x:Name="DatabaseTab">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <StackPanel Margin="0">
                            <!-- Header Section -->
                            <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Database"
                                                           Width="32"
                                                           Height="32"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           Margin="0,0,16,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{DynamicResource DatabaseManagement}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="20"/>
                                        <TextBlock Text="{DynamicResource DatabaseManagementDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  TextWrapping="Wrap"
                                                  FontSize="12"
                                                  LineHeight="16"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Database Location Card -->
                            <Border Style="{StaticResource SettingsCard}">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Kind="DatabaseSettings"
                                                               Width="20"
                                                               Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               Margin="0,0,12,0"/>

                                        <TextBlock Grid.Column="1"
                                                  Text="{DynamicResource DatabaseLocationLabel}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="16"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <TextBlock Text="{DynamicResource DatabaseLocationDescription}"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                                              FontSize="12"
                                              Margin="0,0,0,16"/>

                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBox Text="{Binding DatabaseLocation}"
                                                 IsReadOnly="True"
                                                 materialDesign:HintAssist.Hint="{DynamicResource CurrentDatabaseLocation}"
                                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                 Margin="0,0,8,0"/>

                                        <Button Grid.Column="1"
                                                Command="{Binding ChangeDatabaseLocationCommand}"
                                                Style="{StaticResource MaterialDesignRaisedButton}">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FolderOpen"
                                                                       Width="16"
                                                                       Height="16"
                                                                       Margin="0,0,8,0"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{DynamicResource Change}" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>

                                    <Border Background="{DynamicResource MaterialDesignSelection}"
                                            CornerRadius="4"
                                            Padding="12">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Information"
                                                                   Width="16"
                                                                   Height="16"
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                   Margin="0,0,8,0"/>
                                            <TextBlock Text="{DynamicResource DatabaseLocationDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBody}"
                                                      TextWrapping="Wrap"
                                                      FontSize="11"
                                                      VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- Backup & Restore Card -->
                            <Border Style="{StaticResource SettingsCard}">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Kind="Backup"
                                                               Width="20"
                                                               Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               Margin="0,0,12,0"/>

                                        <TextBlock Grid.Column="1"
                                                  Text="{DynamicResource BackupAndRestore}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="16"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <TextBlock Text="{DynamicResource BackupAndRestoreDescription}"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                                              FontSize="12"
                                              Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="1*"/>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="1*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Backup Section -->
                                        <Border Grid.Column="0"
                                                Background="{DynamicResource MaterialDesignSelection}"
                                                CornerRadius="8"
                                                Padding="16">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <materialDesign:PackIcon Kind="DatabaseExport"
                                                                           Width="18"
                                                                           Height="18"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                           Margin="0,0,8,0"/>
                                                    <TextBlock Text="{DynamicResource CreateBackup}"
                                                              FontWeight="SemiBold"
                                                              FontSize="12"
                                                              VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <TextBlock Text="{DynamicResource CreateBackupDescription}"
                                                          FontSize="11"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,12"/>

                                                <Button Command="{Binding BackupDatabaseCommand}"
                                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                                        HorizontalAlignment="Stretch">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="DatabaseExport"
                                                                               Width="16"
                                                                               Height="16"
                                                                               Margin="0,0,8,0"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{DynamicResource BackupDatabase}" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </Border>

                                        <!-- Restore Section -->
                                        <Border Grid.Column="2"
                                                Background="{DynamicResource MaterialDesignSelection}"
                                                CornerRadius="8"
                                                Padding="16">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <materialDesign:PackIcon Kind="DatabaseImport"
                                                                           Width="18"
                                                                           Height="18"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                           Margin="0,0,8,0"/>
                                                    <TextBlock Text="{DynamicResource RestoreBackup}"
                                                              FontWeight="SemiBold"
                                                              FontSize="12"
                                                              VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <TextBlock Text="{DynamicResource RestoreBackupDescription}"
                                                          FontSize="11"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,12"/>

                                                <Button Command="{Binding RestoreDatabaseCommand}"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        HorizontalAlignment="Stretch">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="DatabaseImport"
                                                                               Width="16"
                                                                               Height="16"
                                                                               Margin="0,0,8,0"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{DynamicResource RestoreDatabase}" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                            BorderThickness="1"
                                            CornerRadius="4"
                                            Padding="12"
                                            Margin="0,16,0,0">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                <materialDesign:PackIcon Kind="Information"
                                                                       Width="16"
                                                                       Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{DynamicResource ImportantInformation}"
                                                          FontWeight="SemiBold"
                                                          FontSize="11"/>
                                            </StackPanel>

                                            <TextBlock Text="{DynamicResource BackupDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      TextWrapping="Wrap"
                                                      FontSize="11"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="{DynamicResource RestartRequired}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="11"
                                                      FontStyle="Italic"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Receipt Printing Tab -->
                <TabItem x:Name="ReceiptPrintingTab">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <StackPanel Margin="0">
                            <!-- Header Section -->
                            <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Printer"
                                                           Width="32"
                                                           Height="32"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                           Margin="0,0,16,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{DynamicResource ReceiptPrintingSettingsLabel}"
                                                  Style="{StaticResource SettingsSectionHeader}"
                                                  FontSize="20"/>
                                        <TextBlock Text="{DynamicResource ReceiptPrintingDescription}"
                                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                  TextWrapping="Wrap"
                                                  FontSize="12"
                                                  LineHeight="16"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Main Content Grid -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column - General Settings -->
                                <StackPanel Grid.Column="0">
                                    <!-- General Printing Settings Card -->
                                    <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="PrinterSettings"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource GeneralSettingsLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <CheckBox x:Name="chkAutoPrint"
                                                    Content="{DynamicResource AutoPrintReceiptsLabel}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkShowPrintDialog"
                                                    Content="{DynamicResource ShowPrintDialogLabel}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="False"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkSaveAsPdfBackup"
                                                    Content="{DynamicResource SaveReceiptsAsPDFLabel}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="False"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkEnablePrintPreview"
                                                    Content="{DynamicResource EnablePrintPreviewLabel}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,16"/>

                                            <TextBlock Text="{DynamicResource PDFBackupDirectoryLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBox x:Name="txtPdfBackupPath"
                                                       Text="C:\POS Receipts"
                                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                       materialDesign:HintAssist.Hint="PDF backup directory path"
                                                       Margin="0,0,8,0"/>

                                                <Button Grid.Column="1"
                                                      Content="{DynamicResource Browse}"
                                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                                      Click="BrowsePdfPath_Click"
                                                      Height="36"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- Printer Configuration Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="Printer"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource PrinterConfigurationLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource DefaultPrinterLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <ComboBox x:Name="cmbDefaultPrinter"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     materialDesign:HintAssist.Hint="{DynamicResource DefaultPrinterLabel}"
                                                     Margin="0,0,0,16"/>

                                            <TextBlock Text="{DynamicResource PrinterTypeLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <ComboBox x:Name="cmbPrinterType"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     materialDesign:HintAssist.Hint="{DynamicResource PrinterTypeLabel}"
                                                     Margin="0,0,0,16">
                                                <ComboBoxItem Content="{DynamicResource StandardPrinter}"/>
                                                <ComboBoxItem Content="{DynamicResource ThermalPrinter}"/>
                                                <ComboBoxItem Content="{DynamicResource PDFExport}"/>
                                            </ComboBox>

                                            <TextBlock Text="{DynamicResource PaperSizeLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <ComboBox x:Name="cmbPaperSize"
                                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                     materialDesign:HintAssist.Hint="{DynamicResource PaperSizeLabel}"
                                                     Margin="0,0,0,16">
                                                <ComboBoxItem Content="{DynamicResource A4}"/>
                                                <ComboBoxItem Content="{DynamicResource Letter}"/>
                                                <ComboBoxItem Content="{DynamicResource Thermal80mm}"/>
                                                <ComboBoxItem Content="{DynamicResource Thermal58mm}"/>
                                            </ComboBox>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>

                                <!-- Right Column - Template Settings -->
                                <StackPanel Grid.Column="2">
                                    <!-- Receipt Template Card -->
                                    <Border Style="{StaticResource SettingsCard}" Margin="0,0,0,16">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="FileDocumentOutline"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource ReceiptTemplateLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <CheckBox x:Name="chkIncludeLogo"
                                                    Content="{DynamicResource IncludeCompanyLogo}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkIncludeCompanyInfo"
                                                    Content="{DynamicResource IncludeCompanyInformation}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkIncludeCustomerInfo"
                                                    Content="{DynamicResource IncludeCustomerInformation}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkIncludeItemDetails"
                                                    Content="{DynamicResource IncludeItemDetails}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,12"/>

                                            <CheckBox x:Name="chkIncludePaymentInfo"
                                                    Content="{DynamicResource IncludePaymentInformation}"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    IsChecked="True"
                                                    Margin="0,0,0,16"/>

                                            <TextBlock Text="{DynamicResource FontSizeLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <Slider x:Name="sliderFontSize"
                                                  Minimum="8"
                                                  Maximum="16"
                                                  Value="12"
                                                  TickFrequency="1"
                                                  IsSnapToTickEnabled="True"
                                                  Style="{StaticResource MaterialDesignSlider}"
                                                  Margin="0,0,0,16"/>

                                            <TextBlock Text="{DynamicResource FooterTextLabel}"
                                                      Style="{StaticResource SettingsLabel}"
                                                      FontSize="12"
                                                      Margin="0,0,0,8"/>

                                            <TextBox x:Name="txtFooterText"
                                                   Text="Thank you for your business!"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   materialDesign:HintAssist.Hint="Custom footer text"
                                                   AcceptsReturn="True"
                                                   TextWrapping="Wrap"
                                                   MaxLines="3"
                                                   Height="80"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Test Printing Card -->
                                    <Border Style="{StaticResource SettingsCard}">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Kind="TestTube"
                                                                       Width="20"
                                                                       Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       Margin="0,0,12,0"/>

                                                <TextBlock Grid.Column="1"
                                                          Text="{DynamicResource TestPrintingLabel}"
                                                          Style="{StaticResource SettingsSectionHeader}"
                                                          FontSize="16"
                                                          VerticalAlignment="Center"/>
                                            </Grid>

                                            <TextBlock Text="{DynamicResource TestPrintingDescription}"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      FontSize="12"
                                                      Margin="0,0,0,16"/>

                                            <StackPanel Orientation="Horizontal">
                                                <Button x:Name="btnTestPrint"
                                                      Content="{DynamicResource TestPrint}"
                                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                                      Background="{DynamicResource PrimaryHueMidBrush}"
                                                      Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                                      Click="TestPrint_Click"
                                                      Margin="0,0,8,0"/>

                                                <Button x:Name="btnPreviewTest"
                                                      Content="{DynamicResource PreviewTest}"
                                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                                      Click="PreviewTest_Click"
                                                      Margin="0,0,8,0"/>

                                                <Button x:Name="btnSaveTestPdf"
                                                      Content="{DynamicResource SaveTestPDF}"
                                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                                      Click="SaveTestPdf_Click"
                                                      Margin="0,0,8,0"/>

                                                <Button x:Name="btnTestConfiguration"
                                                      Content="{DynamicResource TestConfiguration}"
                                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                                      Click="TestConfiguration_Click"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- Floating Action Button -->
        <Button Style="{StaticResource MaterialDesignFloatingActionButton}"
                Command="{Binding SaveCommand}"
                materialDesign:ButtonAssist.CornerRadius="28"
                Background="{DynamicResource PrimaryHueMidBrush}"
                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                Grid.Column="1"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Margin="0,0,32,32"
                Width="56"
                Height="56">
            <materialDesign:PackIcon Kind="ContentSave"
                                   Width="24"
                                   Height="24"/>
        </Button>
    </Grid>
</UserControl> 