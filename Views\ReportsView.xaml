<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <!-- DataGrid Column Header Style - Updated for theme awareness -->
        <Style x:Key="CustomDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignSurfaceBackground}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,12"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
        </Style>

        <!-- Loading Overlay Style -->
        <Style x:Key="LoadingOverlayStyle" TargetType="Grid">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="Opacity" Value="0.8"/>
            <Setter Property="Visibility" Value="Collapsed"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                    <Setter Property="Visibility" Value="Visible"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="30" Background="Transparent">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                  Text="{DynamicResource ReportsManagement}"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Foreground="{DynamicResource MaterialDesignBody}"
                  Margin="0,0,0,20"/>

        <!-- Stats Cards -->
        <WrapPanel Grid.Row="1" Margin="0,0,0,10">
            <md:Card Background="{DynamicResource MaterialDesignCardBackground}" 
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    Margin="0,0,10,0" 
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="15,10">
                    <TextBlock Text="{DynamicResource TotalSales}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                        <md:PackIcon Kind="Cash" 
                                    Width="20" Height="20"
                                    Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                        <TextBlock Text="{Binding TotalSales, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="{DynamicResource SecondaryHueMidBrush}"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>

            <md:Card Background="{DynamicResource MaterialDesignCardBackground}" 
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    Margin="0,0,10,0" 
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="15,10">
                    <TextBlock Text="{DynamicResource TotalPurchases}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                        <md:PackIcon Kind="CartOutline" 
                                    Width="20" Height="20"
                                    Foreground="#F44336"/>
                        <TextBlock Text="{Binding TotalPurchases, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="#F44336"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>

            <md:Card Background="{DynamicResource MaterialDesignCardBackground}" 
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    Margin="0,0,10,0" 
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="15,10">
                    <TextBlock Text="{DynamicResource NetProfit}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                        <md:PackIcon Kind="TrendingUp" 
                                    Width="20" Height="20"
                                    Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="{Binding NetProfit, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>

            <md:Card Background="{DynamicResource MaterialDesignCardBackground}" 
                    Foreground="{DynamicResource MaterialDesignBody}"
                    UniformCornerRadius="8"
                    Width="250"
                    md:ElevationAssist.Elevation="Dp1">
                <StackPanel Margin="15,10">
                    <TextBlock Text="{DynamicResource TotalTransactions}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Opacity="0.87"/>
                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                        <md:PackIcon Kind="SwapHorizontal" 
                                    Width="20" Height="20"
                                    Foreground="{DynamicResource SecondaryHueDarkBrush}"/>
                        <TextBlock Text="{Binding TotalTransactions, StringFormat={}{0:N0}}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="{DynamicResource SecondaryHueDarkBrush}"
                                 Margin="8,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </md:Card>
        </WrapPanel>

        <!-- Filter Options -->
        <md:Card Grid.Row="2" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                Foreground="{DynamicResource MaterialDesignBody}"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1"
                Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" Margin="16">
                <ComboBox Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Width="200"
                         Margin="0,0,16,0"
                         Foreground="{DynamicResource MaterialDesignBody}"
                         Background="{DynamicResource MaterialDesignPaper}"
                         md:HintAssist.Hint="{DynamicResource ReportType}"
                         SelectionChanged="ReportType_Changed"
                         SelectedValuePath="Tag"
                         DisplayMemberPath="Content"
                         SelectedValue="{Binding SelectedReportType}"
                         IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                    <ComboBoxItem Tag="SalesSummary">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource SalesSummary}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                    <ComboBoxItem Tag="PurchaseOrdersSummary">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource PurchaseInvoicesSummary}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                    <ComboBoxItem Tag="InventoryStatus">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource InventoryStatus}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                    <ComboBoxItem Tag="TopProducts">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource TopProducts}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                    <ComboBoxItem Tag="CustomerActivity">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource CustomerActivity}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                    <ComboBoxItem Tag="SupplierActivity">
                        <ComboBoxItem.Content>
                            <TextBlock Text="{DynamicResource SupplierActivity}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </ComboBoxItem.Content>
                    </ComboBoxItem>
                </ComboBox>

                <DatePicker Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Width="150"
                           Margin="0,0,16,0"
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Background="{DynamicResource MaterialDesignPaper}"
                           md:HintAssist.Hint="{DynamicResource StartDate}"
                           SelectedDate="{Binding StartDate}"
                           SelectedDateChanged="StartDate_Changed"
                           IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

                <DatePicker Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Width="150"
                           Margin="0,0,16,0"
                           Foreground="{DynamicResource MaterialDesignBody}"
                           Background="{DynamicResource MaterialDesignPaper}"
                           md:HintAssist.Hint="{DynamicResource EndDate}"
                           SelectedDate="{Binding EndDate}"
                           SelectedDateChanged="EndDate_Changed"
                           IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                        Click="ExportToExcel_Click"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <md:PackIcon Kind="FileExcel" 
                                    Width="20" Height="20"
                                    Margin="0,0,8,0"/>
                        <TextBlock Text="{DynamicResource ExportToExcel}"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </md:Card>

        <!-- Report Content -->
        <md:Card Grid.Row="3" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                Foreground="{DynamicResource MaterialDesignBody}"
                UniformCornerRadius="8"
                md:ElevationAssist.Elevation="Dp1">
            <Grid>
                <DockPanel Margin="16">
                    <TextBlock DockPanel.Dock="Top" 
                             Text="{Binding SelectedReportType, Converter={StaticResource ReportTypeTranslationConverter}}"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Margin="0,0,0,15"/>
                    
                    <DataGrid x:Name="reportDataGrid"
                             DockPanel.Dock="Top"
                             ItemsSource="{Binding ReportData}"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             BorderThickness="0"
                             Background="Transparent"
                             RowBackground="{DynamicResource MaterialDesignPaper}"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Height="Auto"
                             VerticalAlignment="Stretch"
                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                             LoadingRow="ReportDataGrid_LoadingRow"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.ScrollUnit="Pixel"
                             VirtualizingPanel.CacheLength="5"
                             VirtualizingPanel.CacheLengthUnit="Page">
                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}"/>
                            <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                            </Style>
                        </DataGrid.Resources>
                    </DataGrid>
                </DockPanel>

                <!-- Loading Overlay -->
                <Grid Style="{StaticResource LoadingOverlayStyle}">
                    <StackPanel HorizontalAlignment="Center" 
                              VerticalAlignment="Center"
                              MaxWidth="300">
                        <TextBlock Text="{DynamicResource LoadingReport}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,16"/>
                                 
                        <ProgressBar Style="{StaticResource MaterialDesignLinearProgressBar}"
                                   Value="{Binding ProgressValue}"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   Height="10"
                                   Margin="0,0,0,8"/>
                                   
                        <TextBlock Text="{Binding ProgressValue, StringFormat={}{0:N0}%}"
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 Foreground="{DynamicResource MaterialDesignBody}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </md:Card>
    </Grid>
</UserControl> 