﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaymentDate",
                table: "Sales");

            migrationBuilder.DropColumn(
                name: "Amount",
                table: "SaleHistory");

            migrationBuilder.DropColumn(
                name: "Date",
                table: "SaleHistory");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "SaleHistory");

            migrationBuilder.AddColumn<string>(
                name: "ImageData",
                table: "Products",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LoyaltyPoints",
                table: "Products",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "PointsCalculationMethod",
                table: "LoyaltyPrograms",
                type: "TEXT",
                nullable: false,
                defaultValue: "PerDollar");

            migrationBuilder.CreateTable(
                name: "UserFavorites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserFavorites", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserFavorites_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserFavorites_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8352), new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8328), new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8353) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8358), new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8357), new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(8359) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "PointsCalculationMethod" },
                values: new object[] { new DateTime(2025, 2, 13, 11, 48, 53, 559, DateTimeKind.Local).AddTicks(3714), "PerDollar" });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(9168));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(9170));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 557, DateTimeKind.Local).AddTicks(9173));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2650));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2653));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2665));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2667));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2671));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2673));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2676));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 13, 11, 48, 53, 561, DateTimeKind.Local).AddTicks(2681));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 13, 11, 48, 53, 558, DateTimeKind.Local).AddTicks(1465), new DateTime(2025, 2, 13, 11, 48, 53, 558, DateTimeKind.Local).AddTicks(1468) });

            migrationBuilder.CreateIndex(
                name: "IX_Products_Description",
                table: "Products",
                column: "Description");

            migrationBuilder.CreateIndex(
                name: "IX_Products_Name",
                table: "Products",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Products_Name_SKU",
                table: "Products",
                columns: new[] { "Name", "SKU" });

            migrationBuilder.CreateIndex(
                name: "IX_Products_SKU_Description",
                table: "Products",
                columns: new[] { "SKU", "Description" });

            migrationBuilder.CreateIndex(
                name: "IX_UserFavorites_ProductId",
                table: "UserFavorites",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_UserFavorites_UserId_ProductId",
                table: "UserFavorites",
                columns: new[] { "UserId", "ProductId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserFavorites");

            migrationBuilder.DropIndex(
                name: "IX_Products_Description",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Products_Name",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Products_Name_SKU",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Products_SKU_Description",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "ImageData",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "LoyaltyPoints",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "PointsCalculationMethod",
                table: "LoyaltyPrograms");

            migrationBuilder.AddColumn<DateTime>(
                name: "PaymentDate",
                table: "Sales",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Amount",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "Date",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "SaleHistory",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6625), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6596), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6625) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6630), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6629), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(6631) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 324, DateTimeKind.Local).AddTicks(6327));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7402));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7406));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(7409));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9161));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9164));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9172));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9174));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9178));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9180));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9183));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 8, 13, 33, 37, 326, DateTimeKind.Local).AddTicks(9195));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(9827), new DateTime(2025, 2, 8, 13, 33, 37, 322, DateTimeKind.Local).AddTicks(9833) });
        }
    }
}
