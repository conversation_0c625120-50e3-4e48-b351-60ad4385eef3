-- Fix Invoice Reference field to be nullable
-- This script handles the case where the Reference field might have been created as NOT NULL

-- Check if Invoice table exists and has Reference column
-- If it exists and is NOT NULL, we need to recreate the table

-- Create a backup of existing data
CREATE TABLE IF NOT EXISTS Invoice_temp AS SELECT * FROM Invoice WHERE 1=0;

-- Insert existing data into temp table
INSERT INTO Invoice_temp SELECT * FROM Invoice;

-- Drop the original table
DROP TABLE IF EXISTS Invoice;

-- Recreate the Invoice table with proper schema
CREATE TABLE Invoice (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT NOT NULL,
    Type TEXT NOT NULL,
    IssueDate TEXT NOT NULL,
    DueDate TEXT NOT NULL,
    CustomerId INTEGER,
    SupplierId INTEGER,
    Subtotal DECIMAL(18,2) NOT NULL DEFAULT 0,
    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    GrandTotal DECIMAL(18,2) NOT NULL DEFAULT 0,
    Status TEXT NOT NULL DEFAULT 'Draft',
    PaymentTerms TEXT,
    Reference TEXT,  -- This is now nullable
    Notes TEXT,
    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TEXT,
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
);

-- Copy data back from temp table
INSERT INTO Invoice (Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
                   SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
                   Status, PaymentTerms, Reference, Notes, CreatedAt, UpdatedAt)
SELECT Id, InvoiceNumber, Type, IssueDate, DueDate, CustomerId, 
       SupplierId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, 
       Status, PaymentTerms, Reference, Notes, CreatedAt, UpdatedAt
FROM Invoice_temp;

-- Drop the temp table
DROP TABLE Invoice_temp;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS IX_Invoice_CustomerId ON Invoice(CustomerId);
CREATE INDEX IF NOT EXISTS IX_Invoice_SupplierId ON Invoice(SupplierId);
CREATE INDEX IF NOT EXISTS IX_Invoice_InvoiceNumber ON Invoice(InvoiceNumber);
CREATE INDEX IF NOT EXISTS IX_Invoice_Status ON Invoice(Status);
