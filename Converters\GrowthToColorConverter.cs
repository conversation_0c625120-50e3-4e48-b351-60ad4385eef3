using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace POSSystem.Converters
{
    public class GrowthToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal growth)
            {
                if (growth > 0)
                    return new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                else if (growth < 0)
                    return new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red
                else
                    return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // Gray
            }

            return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // Default Gray
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 