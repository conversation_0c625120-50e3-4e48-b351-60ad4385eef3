using System;
using System.Windows;
using POSSystem.Views.Controls;

namespace POSSystem
{
    /// <summary>
    /// Simple test to verify NotificationPopup can be instantiated without errors
    /// </summary>
    public class NotificationPopupTest
    {
        public static void TestNotificationPopupInstantiation()
        {
            try
            {
                Console.WriteLine("Testing NotificationPopup instantiation...");
                
                // This should not throw an exception now
                var popup = new NotificationPopup();
                
                Console.WriteLine("✅ NotificationPopup instantiated successfully!");
                
                // Test basic methods
                popup.Show();
                Console.WriteLine("✅ Show() method works");
                
                popup.Hide();
                Console.WriteLine("✅ Hide() method works");
                
                popup.SetNotifications(new System.Collections.Generic.List<POSSystem.Models.Notification>());
                Console.WriteLine("✅ SetNotifications() method works");
                
                Console.WriteLine("🎉 All NotificationPopup tests passed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ NotificationPopup test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
