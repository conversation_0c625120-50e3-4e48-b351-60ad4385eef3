using System;
using System.Windows;
using System.Windows.Controls;
using POSSystem.Views.Layouts;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class LayoutThemeService
    {
        private readonly ISettingsService _settingsService;
        
        public LayoutThemeService()
        {
            _settingsService = new SettingsService();
        }
        
        /// <summary>
        /// Gets the appropriate layout view based on the current settings
        /// </summary>
        /// <returns>A UserControl representing the active layout theme</returns>
        public UserControl GetSalesLayoutView()
        {
            string layoutTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

            return layoutTheme switch
            {
                "Compact" => new SalesViewCompact(),
                "Grid" => new SalesViewGrid(),
                "Modern" => new SalesViewModern(),
                "Standard" => new SalesViewStandard(),
                _ => new SalesViewGrid() // Grid layout as fallback
            };
        }
        
        /// <summary>
        /// Checks if the current sales layout should use a custom layout
        /// </summary>
        /// <returns>True if a custom layout should be used, false to use the standard layout</returns>
        public bool ShouldUseCustomLayout()
        {
            string layoutTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";
            return layoutTheme != "Standard";
        }
    }
} 