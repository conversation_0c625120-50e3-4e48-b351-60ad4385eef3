using System.Windows.Controls;
using POSSystem.ViewModels.Dashboard;
using POSSystem.Services;
using System.Threading.Tasks;
using System;

namespace POSSystem.Views.Dialogs
{
    public partial class LowStockStatsDetailsDialog : UserControl
    {
        private readonly LowStockStatsDetailsViewModel _viewModel;

        public LowStockStatsDetailsDialog(RefactoredDashboardViewModel dashboardViewModel)
        {
            InitializeComponent();
            _viewModel = new LowStockStatsDetailsViewModel(dashboardViewModel, (DatabaseService)App.DbService);
            DataContext = _viewModel;

            // ✅ CRITICAL FIX: Don't call LoadDataAsync here - it's already called in InitializeAsync()
            // This matches the pattern of ProfitStatsDetailsDialog and other working dialogs
        }
    }
}