using System;
using POSSystem.Tests;

namespace POSSystem.Tests
{
    /// <summary>
    /// Console application to run the role display test
    /// </summary>
    class RunRoleDisplayTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("POS System - Role Display Test Utility");
            Console.WriteLine("=====================================");
            Console.WriteLine();

            var test = new RoleDisplayTest();

            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "create":
                        test.CreateTestUserWithCustomPermissions();
                        break;
                    case "test":
                        test.TestAllUsersDisplayRole();
                        break;
                    case "cleanup":
                        test.CleanupTestUser();
                        break;
                    default:
                        ShowUsage();
                        break;
                }
            }
            else
            {
                // Run all tests by default
                Console.WriteLine("Running all tests...");
                Console.WriteLine();
                
                test.TestAllUsersDisplayRole();
                Console.WriteLine();
                test.CreateTestUserWithCustomPermissions();
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static void ShowUsage()
        {
            Console.WriteLine("Usage:");
            Console.WriteLine("  RunRoleDisplayTest.exe create   - Create test user with custom permissions");
            Console.WriteLine("  RunRoleDisplayTest.exe test     - Test DisplayRoleName for all users");
            Console.WriteLine("  RunRoleDisplayTest.exe cleanup  - Delete test user");
            Console.WriteLine("  RunRoleDisplayTest.exe          - Run all tests");
        }
    }
}
