using POSSystem.Models;
using POSSystem.Models.Printing;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for cash drawer services
    /// </summary>
    public interface ICashDrawerService
    {
        // Event for cash drawer updates
        event EventHandler<CashDrawerEventArgs> CashDrawerUpdated;

        CashDrawer GetActiveCashDrawer();
        CashDrawer GetCurrentDrawer();
        CashDrawer GetCurrentDrawerBasic();
        CashDrawer GetCurrentDrawerWithUsers();
        CashDrawer GetCurrentDrawerWithDetails();
        void OpenCashDrawer(decimal openingBalance, int userId);
        void OpenDrawer(CashDrawer drawer);
        void CloseDrawer(CashDrawer drawer);
        Task CloseDrawerAsync(CashDrawer drawer); // ✅ PERFORMANCE: Async version
        void CloseCashDrawer(int cashDrawerId, decimal closingBalance, int userId);
        List<CashDrawer> GetCashDrawerHistory();
        void AddCashTransaction(CashTransaction transaction);
        void AddTransaction(CashTransaction transaction);
        Task AddTransactionAsync(CashTransaction transaction); // ✅ PERFORMANCE: Async version
        List<CashTransaction> GetCashTransactions(int cashDrawerId);
        List<CashTransaction> GetTransactions(CashDrawer drawer);
        decimal GetTotalCashSales(CashDrawer drawer);
        decimal GetTotalPayouts(CashDrawer drawer);
    }

    /// <summary>
    /// Interface for customer services
    /// </summary>
    public interface ICustomerService
    {
        List<Customer> GetAllCustomers();
        Customer GetCustomerById(int id);
        Customer GetCustomerByLoyaltyCode(string loyaltyCode);
        void AddCustomer(Customer customer);
        void UpdateCustomer(Customer customer);
        void DeleteCustomer(int id);
        void UpdateLoyaltyPoints(int customerId, int points);
        void AddLoyaltyTransaction(Customer customer, decimal points, string description);
    }

    /// <summary>
    /// Interface for discount services
    /// </summary>
    public interface IDiscountService
    {
        List<DiscountType> GetAllDiscountTypes();
        List<DiscountReason> GetAllDiscountReasons();
        bool CanApplyDiscount(int userId, decimal discountAmount);
        void ApplyDiscount(int saleId, int discountTypeId, decimal discountValue, string reason);
        void ApplyItemDiscount(CartItem item, Discount discount);
        void ApplyCartWideDiscount(Cart cart, Discount discount);
    }

    /// <summary>
    /// Interface for favorite services
    /// </summary>
    public interface IFavoriteService
    {
        List<UserFavorite> GetUserFavorites(int userId);
        void AddFavorite(int userId, int productId);
        void RemoveFavorite(int userId, int productId);
        bool IsFavorite(int userId, int productId);
    }

    /// <summary>
    /// Interface for user permissions services
    /// </summary>
    public interface IUserPermissionsService
    {
        UserPermissions GetUserPermissions(int userId);
        void UpdateUserPermissions(UserPermissions permissions);
        bool HasPermission(int userId, string permission);
    }

    /// <summary>
    /// Interface for chart services
    /// </summary>
    public interface IChartService
    {
        Task<List<SalesTrend>> GetSalesTrendsAsync(System.DateTime startDate, System.DateTime endDate);
        Task<List<TopProductItem>> GetTopProductsAsync(int count = 10);
        Task<decimal> GetTotalSalesAsync(System.DateTime startDate, System.DateTime endDate);
    }

    /// <summary>
    /// Interface for sound services
    /// </summary>
    public interface ISoundService
    {
        void PlaySound(string soundName);
        void PlayCartAddSound();
        void PlayErrorSound();
        void PlaySuccessSound();
        void SetVolume(double volume);
        bool IsMuted { get; set; }
    }

    /// <summary>
    /// Interface for invoice number services
    /// </summary>
    public interface IInvoiceNumberService
    {
        string GetNextInvoiceNumber();
        string GenerateInvoiceNumber(System.DateTime date);
        void ResetInvoiceCounter();
    }

    /// <summary>
    /// Interface for product lookup services
    /// </summary>
    public interface IProductLookupService
    {
        Product FindProductByBarcode(string barcode);
        List<Product> SearchProducts(string searchTerm);
        Product FindProductBySku(string sku);
    }

    /// <summary>
    /// Interface for invoice printing services
    /// </summary>
    public interface IInvoicePrintService
    {
        void PrintInvoice(Invoice invoice);
        Task PrintInvoiceAsync(Invoice invoice);
        void PreviewInvoice(Invoice invoice);
    }

    /// <summary>
    /// Interface for sale printing services
    /// </summary>
    public interface ISalePrintService
    {
        void PrintReceipt(Sale sale);
        Task PrintReceiptAsync(Sale sale);
        void PreviewReceipt(Sale sale);
    }

    /// <summary>
    /// Enhanced interface for receipt printing services with advanced features
    /// </summary>
    public interface IEnhancedReceiptPrintService
    {
        /// <summary>
        /// Print receipt for a sale with automatic printer selection
        /// </summary>
        Task<bool> PrintReceiptAsync(Sale sale, bool showDialog = false);

        /// <summary>
        /// Print receipt with specific printer configuration and template
        /// </summary>
        Task<bool> PrintReceiptAsync(Sale sale, PrinterConfiguration printerConfig,
            ReceiptTemplate template, bool showDialog = false);

        /// <summary>
        /// Preview receipt before printing
        /// </summary>
        Task<bool> PreviewReceiptAsync(Sale sale, ReceiptTemplate template = null);

        /// <summary>
        /// Save receipt as PDF file
        /// </summary>
        Task<bool> SaveReceiptAsPdfAsync(Sale sale, string filePath, ReceiptTemplate template = null);

        /// <summary>
        /// Get available printer configurations
        /// </summary>
        Task<List<PrinterConfiguration>> GetPrinterConfigurationsAsync();

        /// <summary>
        /// Get available receipt templates
        /// </summary>
        Task<List<ReceiptTemplate>> GetReceiptTemplatesAsync();

        /// <summary>
        /// Get print job history for a sale
        /// </summary>
        Task<List<ReceiptPrintJob>> GetPrintJobHistoryAsync(int saleId);

        /// <summary>
        /// Get current receipt print settings
        /// </summary>
        ReceiptPrintSettings GetPrintSettings();

        /// <summary>
        /// Update receipt print settings
        /// </summary>
        Task<bool> UpdatePrintSettingsAsync(ReceiptPrintSettings settings);
    }
}
