using System;
using System.Threading.Tasks;
using POSSystem.ViewModels.Dashboard.Commands;
using static POSSystem.ViewModels.Dashboard.Commands.DashboardCommandManager;

namespace POSSystem.ViewModels.Dashboard.Services
{
    /// <summary>
    /// Interface for dashboard data operations, providing abstraction for data management
    /// and enabling better testability and separation of concerns.
    /// </summary>
    public interface IDashboardDataService
    {
        /// <summary>
        /// Refreshes all dashboard data asynchronously.
        /// </summary>
        /// <returns>A task representing the asynchronous refresh operation</returns>
        Task RefreshAllDataAsync();

        /// <summary>
        /// Sets the time period for dashboard metrics.
        /// </summary>
        /// <param name="period">The time period to apply</param>
        void SetTimePeriod(DashboardTimePeriod period);

        /// <summary>
        /// Exports dashboard data to the specified format.
        /// </summary>
        /// <param name="format">The export format (e.g., "CSV", "Excel", "PDF")</param>
        /// <returns>A task representing the asynchronous export operation</returns>
        Task ExportDataAsync(string format);

        /// <summary>
        /// Shows detailed view for a specific metric or data point.
        /// </summary>
        /// <param name="parameter">The parameter identifying what to show in detail</param>
        void ShowDetailView(object parameter);

        /// <summary>
        /// Hides the currently displayed detail view.
        /// </summary>
        void HideDetailView();

        /// <summary>
        /// Applies a custom date range filter to dashboard data.
        /// </summary>
        /// <param name="dateRange">The date range to apply</param>
        /// <returns>A task representing the asynchronous filter operation</returns>
        Task ApplyDateRangeAsync(DateRange dateRange);

        /// <summary>
        /// Clears all applied filters and returns to default view.
        /// </summary>
        /// <returns>A task representing the asynchronous clear operation</returns>
        Task ClearFiltersAsync();

        /// <summary>
        /// Gets the current sales metrics for the selected period.
        /// </summary>
        /// <returns>Sales metrics data</returns>
        Task<SalesMetrics> GetSalesMetricsAsync();

        /// <summary>
        /// Gets the current profit metrics for the selected period.
        /// </summary>
        /// <returns>Profit metrics data</returns>
        Task<ProfitMetrics> GetProfitMetricsAsync();

        /// <summary>
        /// Gets the top-selling products for the selected period.
        /// </summary>
        /// <param name="count">Number of top products to retrieve</param>
        /// <returns>Top products data</returns>
        Task<TopProductsData> GetTopProductsAsync(int count = 10);

        /// <summary>
        /// Gets customer analytics for the selected period.
        /// </summary>
        /// <returns>Customer analytics data</returns>
        Task<CustomerAnalytics> GetCustomerAnalyticsAsync();
    }

    /// <summary>
    /// Interface for dashboard chart operations, providing abstraction for chart management.
    /// </summary>
    public interface IDashboardChartService
    {
        /// <summary>
        /// Refreshes all charts with current data.
        /// </summary>
        /// <returns>A task representing the asynchronous refresh operation</returns>
        Task RefreshAllChartsAsync();

        /// <summary>
        /// Sets the chart type for dashboard charts.
        /// </summary>
        /// <param name="chartType">The chart type to apply</param>
        void SetChartType(ChartType chartType);

        /// <summary>
        /// Zooms into a specific area of a chart.
        /// </summary>
        /// <param name="parameter">The zoom parameter (coordinates, range, etc.)</param>
        void ZoomChart(object parameter);

        /// <summary>
        /// Resets chart zoom to default view.
        /// </summary>
        void ResetZoom();

        /// <summary>
        /// Updates the sales trend chart with new data.
        /// </summary>
        /// <param name="data">Sales trend data</param>
        Task UpdateSalesTrendChartAsync(SalesTrendData data);

        /// <summary>
        /// Updates the profit chart with new data.
        /// </summary>
        /// <param name="data">Profit data</param>
        Task UpdateProfitChartAsync(ProfitData data);

        /// <summary>
        /// Updates the product performance chart with new data.
        /// </summary>
        /// <param name="data">Product performance data</param>
        Task UpdateProductChartAsync(ProductPerformanceData data);


    }

    #region Data Transfer Objects

    /// <summary>
    /// Represents sales metrics for dashboard display.
    /// </summary>
    public class SalesMetrics
    {
        public decimal TodaySales { get; set; }
        public decimal WeekSales { get; set; }
        public decimal MonthSales { get; set; }
        public decimal SalesGrowth { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    /// <summary>
    /// Represents profit metrics for dashboard display.
    /// </summary>
    public class ProfitMetrics
    {
        public decimal GrossProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal ProfitGrowth { get; set; }
        public decimal NetProfit { get; set; }
        public decimal CostOfGoodsSold { get; set; }
    }

    /// <summary>
    /// Represents top products data for dashboard display.
    /// </summary>
    public class TopProductsData
    {
        public System.Collections.Generic.List<TopProductItem> Products { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DashboardTimePeriod Period { get; set; }
    }

    /// <summary>
    /// Represents a single top product item.
    /// </summary>
    public class TopProductItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal Revenue { get; set; }
        public int QuantitySold { get; set; }
        public decimal Profit { get; set; }
    }

    /// <summary>
    /// Represents customer analytics data for dashboard display.
    /// </summary>
    public class CustomerAnalytics
    {
        public int TotalCustomers { get; set; }
        public int NewCustomers { get; set; }
        public int ReturningCustomers { get; set; }
        public decimal CustomerRetentionRate { get; set; }
        public decimal AverageCustomerValue { get; set; }
    }

    /// <summary>
    /// Represents sales trend data for chart display.
    /// </summary>
    public class SalesTrendData
    {
        public System.Collections.Generic.List<SalesTrendPoint> DataPoints { get; set; }
        public string[] Labels { get; set; }
        public DashboardTimePeriod Period { get; set; }
    }

    /// <summary>
    /// Represents a single point in the sales trend.
    /// </summary>
    public class SalesTrendPoint
    {
        public DateTime Date { get; set; }
        public decimal Sales { get; set; }
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// Represents profit data for chart display.
    /// </summary>
    public class ProfitData
    {
        public System.Collections.Generic.List<ProfitDataPoint> DataPoints { get; set; }
        public string[] Labels { get; set; }
        public DashboardTimePeriod Period { get; set; }
    }

    /// <summary>
    /// Represents a single profit data point.
    /// </summary>
    public class ProfitDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Profit { get; set; }
        public decimal Margin { get; set; }
    }

    /// <summary>
    /// Represents product performance data for chart display.
    /// </summary>
    public class ProductPerformanceData
    {
        public System.Collections.Generic.List<ProductPerformancePoint> DataPoints { get; set; }
        public string[] Labels { get; set; }
        public string MetricType { get; set; }
    }

    /// <summary>
    /// Represents a single product performance point.
    /// </summary>
    public class ProductPerformancePoint
    {
        public string ProductName { get; set; }
        public decimal Value { get; set; }
        public string Category { get; set; }
    }



    #endregion
}
