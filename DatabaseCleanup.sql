-- POS System Database Cleanup Script
-- This script fixes foreign key constraint violations after migration
-- Created: 2025-01-10

-- Disable foreign key constraints temporarily for cleanup
PRAGMA foreign_keys = OFF;

BEGIN TRANSACTION;

-- ============================================================================
-- CLEANUP 1: Remove orphaned SaleItems that reference non-existent Sales
-- ============================================================================

DELETE FROM SaleItems 
WHERE SaleId NOT IN (SELECT Id FROM Sales);

-- ============================================================================
-- CLEANUP 2: Remove orphaned SaleItems that reference non-existent Products
-- ============================================================================

DELETE FROM SaleItems 
WHERE ProductId NOT IN (SELECT Id FROM Products);

-- ============================================================================
-- CLEANUP 3: Remove orphaned ProductBarcodes that reference non-existent Products
-- ============================================================================

DELETE FROM ProductBarcodes 
WHERE ProductId NOT IN (SELECT Id FROM Products);

-- ============================================================================
-- CLEANUP 4: Remove orphaned PurchaseOrderItems that reference non-existent PurchaseOrders
-- ============================================================================

DELETE FROM PurchaseOrderItems 
WHERE PurchaseOrderId NOT IN (SELECT Id FROM PurchaseOrders);

-- ============================================================================
-- CLEANUP 5: Remove orphaned BatchStock that reference non-existent Products
-- ============================================================================

DELETE FROM BatchStock 
WHERE ProductId NOT IN (SELECT Id FROM Products);

-- ============================================================================
-- CLEANUP 6: Remove orphaned ProductAlerts that reference non-existent Products
-- ============================================================================

DELETE FROM ProductAlerts 
WHERE ProductId NOT IN (SELECT Id FROM Products);

-- ============================================================================
-- CLEANUP 7: Remove orphaned Discounts that reference non-existent Sales
-- ============================================================================

DELETE FROM Discounts 
WHERE SaleId IS NOT NULL AND SaleId NOT IN (SELECT Id FROM Sales);

-- ============================================================================
-- CLEANUP 8: Update sqlite_sequence to ensure proper auto-increment values
-- ============================================================================

-- Update sequence values to prevent ID conflicts
UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Users
) WHERE name = 'Users';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Products
) WHERE name = 'Products';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Categories
) WHERE name = 'Categories';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Sales
) WHERE name = 'Sales';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM SaleItems
) WHERE name = 'SaleItems';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Customers
) WHERE name = 'Customers';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Suppliers
) WHERE name = 'Suppliers';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM ProductBarcodes
) WHERE name = 'ProductBarcodes';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Payments
) WHERE name = 'Payments';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM InventoryTransactions
) WHERE name = 'InventoryTransactions';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM BatchStock
) WHERE name = 'BatchStock';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM ProductAlerts
) WHERE name = 'ProductAlerts';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM Discounts
) WHERE name = 'Discounts';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM PurchaseOrders
) WHERE name = 'PurchaseOrders';

UPDATE sqlite_sequence SET seq = (
    SELECT COALESCE(MAX(Id), 0) FROM PurchaseOrderItems
) WHERE name = 'PurchaseOrderItems';

-- ============================================================================
-- CLEANUP 9: Verify data integrity after cleanup
-- ============================================================================

-- Re-enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Commit the cleanup transaction
COMMIT;

-- Final verification
SELECT 'Cleanup completed successfully. Final data counts:' as Status;
SELECT 'Users: ' || COUNT(*) FROM Users;
SELECT 'Products: ' || COUNT(*) FROM Products;
SELECT 'Categories: ' || COUNT(*) FROM Categories;
SELECT 'Sales: ' || COUNT(*) FROM Sales;
SELECT 'SaleItems: ' || COUNT(*) FROM SaleItems;
SELECT 'Customers: ' || COUNT(*) FROM Customers;
SELECT 'Suppliers: ' || COUNT(*) FROM Suppliers;
SELECT 'ProductBarcodes: ' || COUNT(*) FROM ProductBarcodes;
SELECT 'Payments: ' || COUNT(*) FROM Payments;
SELECT 'InventoryTransactions: ' || COUNT(*) FROM InventoryTransactions;

-- Check for remaining foreign key violations
PRAGMA foreign_key_check;
