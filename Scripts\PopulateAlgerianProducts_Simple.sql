-- =====================================================
-- Algerian Grocery Products Database (200 Products)
-- =====================================================
-- This script populates the database with real Algerian grocery products
-- Prices are in Algerian Dinars (DZD)

-- Clear existing products (keep categories and system data)
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM PurchaseOrderItems;
DELETE FROM PurchaseOrders;
DELETE FROM Products WHERE Id > 0;
DELETE FROM Customers WHERE Id > 0;
DELETE FROM Suppliers WHERE Id > 0;
DELETE FROM BusinessExpenses WHERE Id > 0;

-- Reset auto-increment counters
DELETE FROM sqlite_sequence WHERE name IN ('Sales', 'SaleItems', 'Products', 'Customers', 'Suppliers', 'PurchaseOrders', 'PurchaseOrderItems', 'BusinessExpenses');

-- =====================================================
-- ALGERIAN SUPPLIERS
-- =====================================================
INSERT INTO Suppliers (Name, ContactName, Email, Phone, Address, Website, Notes, IsActive, ProductCount, CreatedAt, UpdatedAt) VALUES
('Cevital Agro-Industrie', 'Ahmed Benali', '<EMAIL>', '021-98-76-54', 'Zone Industrielle, Bejaia 06000', 'www.cevital.com', 'Plus grand groupe agroalimentaire algerien', 1, 0, datetime('now'), datetime('now')),
('Groupe Sim', 'Fatima Khelifi', '<EMAIL>', '021-87-65-43', 'Route Nationale 5, Rouiba, Alger', 'www.groupesim.dz', 'Produits laitiers et boissons', 1, 0, datetime('now'), datetime('now')),
('Danone Djurdjura Algerie', 'Mohamed Saidi', '<EMAIL>', '021-76-54-32', 'Zone Industrielle Rouiba, Alger', 'www.danone.dz', 'Produits laitiers frais', 1, 0, datetime('now'), datetime('now')),
('Ifri', 'Salim Othmani', '<EMAIL>', '026-20-15-30', 'Ighzer Amokrane, Ifri Ouzellaguen, Bejaia', 'www.ifri.dz', 'Eaux minerales et boissons', 1, 0, datetime('now'), datetime('now')),
('Laiterie Soummam', 'Nadia Brahimi', '<EMAIL>', '034-21-45-67', 'Akbou, Bejaia 06001', 'www.soummam.dz', 'Produits laitiers traditionnels', 1, 0, datetime('now'), datetime('now')),
('Groupe Benamor', 'Karim Benamor', '<EMAIL>', '021-65-43-21', 'Zone Industrielle, Setif', 'www.benamor.dz', 'Conserves et produits transformes', 1, 0, datetime('now'), datetime('now')),
('Tchin-Lait', 'Amina Meziani', '<EMAIL>', '021-54-32-10', 'Canastel, Oran 31000', 'www.tchinlait.dz', 'Boissons aux fruits et lait', 1, 0, datetime('now'), datetime('now')),
('Groupe Amor Benamor', 'Youcef Amor', '<EMAIL>', '038-45-67-89', 'El Eulma, Setif', 'www.amorbenamor.dz', 'Conserves de tomates et legumes', 1, 0, datetime('now'), datetime('now'));

-- =====================================================
-- BEVERAGES (Boissons) - Category 1
-- =====================================================
INSERT INTO Products (Name, SKU, Barcode, Description, PurchasePrice, SellingPrice, DefaultPrice, StockQuantity, MinimumStock, ReorderPoint, CategoryId, SupplierId, IsActive, TrackBatches, LoyaltyPoints, CreatedAt, UpdatedAt) VALUES
-- Eaux minerales
('Ifri 1.5L', 'BEV001', '6111000000001', 'Eau minerale naturelle Ifri 1.5L', '25.00', '40.00', '40.00', 200, 30, 60, 1, 4, 1, 0, '2.0', datetime('now'), datetime('now')),
('Ifri 0.5L', 'BEV002', '6111000000002', 'Eau minerale naturelle Ifri 0.5L', '15.00', '25.00', '25.00', 300, 50, 100, 1, 4, 1, 0, '1.0', datetime('now'), datetime('now')),
('Saida 1.5L', 'BEV003', '6111000000003', 'Eau minerale Saida 1.5L', '22.00', '35.00', '35.00', 150, 25, 50, 1, 4, 1, 0, '2.0', datetime('now'), datetime('now')),
('Lalla Khedidja 1L', 'BEV004', '6111000000004', 'Eau minerale Lalla Khedidja 1L', '20.00', '30.00', '30.00', 180, 30, 60, 1, 4, 1, 0, '1.5', datetime('now'), datetime('now')),
('Youkous 1.5L', 'BEV005', '6111000000005', 'Eau minerale Youkous 1.5L', '23.00', '38.00', '38.00', 120, 20, 40, 1, 4, 1, 0, '2.0', datetime('now'), datetime('now')),

-- Boissons gazeuses
('Hamoud Boualem Original', 'BEV006', '6111000000006', 'Limonade Hamoud Boualem Original 1L', '45.00', '80.00', '80.00', 100, 15, 30, 1, 2, 1, 0, '4.0', datetime('now'), datetime('now')),
('Hamoud Boualem Citron', 'BEV007', '6111000000007', 'Limonade Hamoud Boualem Citron 1L', '45.00', '80.00', '80.00', 100, 15, 30, 1, 2, 1, 0, '4.0', datetime('now'), datetime('now')),
('Selecto Orange', 'BEV008', '6111000000008', 'Boisson gazeuse Selecto Orange 1L', '40.00', '70.00', '70.00', 80, 12, 25, 1, 2, 1, 0, '3.5', datetime('now'), datetime('now')),
('Selecto Cola', 'BEV009', '6111000000009', 'Boisson gazeuse Selecto Cola 1L', '40.00', '70.00', '70.00', 80, 12, 25, 1, 2, 1, 0, '3.5', datetime('now'), datetime('now')),
('Ramy Orange', 'BEV010', '6111000000010', 'Boisson gazeuse Ramy Orange 33cl', '25.00', '45.00', '45.00', 120, 20, 40, 1, 2, 1, 0, '2.0', datetime('now'), datetime('now')),

-- Jus de fruits
('Tchin-Tchin Orange', 'BEV011', '6111000000011', 'Jus orange Tchin-Tchin 1L', '80.00', '130.00', '130.00', 60, 10, 20, 1, 7, 1, 0, '6.0', datetime('now'), datetime('now')),
('Tchin-Tchin Pomme', 'BEV012', '6111000000012', 'Jus de pomme Tchin-Tchin 1L', '80.00', '130.00', '130.00', 60, 10, 20, 1, 7, 1, 0, '6.0', datetime('now'), datetime('now')),
('Rouiba Orange', 'BEV013', '6111000000013', 'Jus orange Rouiba 1L', '85.00', '140.00', '140.00', 50, 8, 16, 1, 2, 1, 0, '7.0', datetime('now'), datetime('now')),
('Rouiba Cocktail', 'BEV014', '6111000000014', 'Jus cocktail de fruits Rouiba 1L', '85.00', '140.00', '140.00', 50, 8, 16, 1, 2, 1, 0, '7.0', datetime('now'), datetime('now')),
('Ifruit Orange', 'BEV015', '6111000000015', 'Jus orange Ifruit 25cl', '35.00', '60.00', '60.00', 100, 15, 30, 1, 4, 1, 0, '3.0', datetime('now'), datetime('now')),

-- Boissons lactees
('Tchin-Lait Fraise', 'BEV016', '6111000000016', 'Boisson lactee Tchin-Lait Fraise 1L', '90.00', '150.00', '150.00', 40, 6, 12, 1, 7, 1, 0, '7.5', datetime('now'), datetime('now')),
('Tchin-Lait Vanille', 'BEV017', '6111000000017', 'Boisson lactee Tchin-Lait Vanille 1L', '90.00', '150.00', '150.00', 40, 6, 12, 1, 7, 1, 0, '7.5', datetime('now'), datetime('now')),
('Soummam Lait Fraise', 'BEV018', '6111000000018', 'Lait aromatise Soummam Fraise 1L', '95.00', '160.00', '160.00', 35, 5, 10, 1, 5, 1, 0, '8.0', datetime('now'), datetime('now')),
('Candia Chocolat', 'BEV019', '6111000000019', 'Lait chocolate Candia 1L', '100.00', '170.00', '170.00', 30, 5, 10, 1, 3, 1, 0, '8.5', datetime('now'), datetime('now')),
('Danone Actimel', 'BEV020', '6111000000020', 'Boisson lactee Danone Actimel 100ml', '45.00', '80.00', '80.00', 60, 10, 20, 1, 3, 1, 0, '4.0', datetime('now'), datetime('now')),

-- =====================================================
-- SNACKS & CONFISERIES - Category 2
-- =====================================================
-- Biscuits et gateaux
('Bimo Petit Beurre', 'SNK001', '6111000000021', 'Biscuits Bimo Petit Beurre 200g', '60.00', '100.00', '100.00', 80, 12, 25, 2, 1, 1, 0, '5.0', datetime('now'), datetime('now')),
('Bimo Digestive', 'SNK002', '6111000000022', 'Biscuits Bimo Digestive 250g', '70.00', '120.00', '120.00', 70, 10, 20, 2, 1, 1, 0, '6.0', datetime('now'), datetime('now')),
('Tango Chocolat', 'SNK003', '6111000000023', 'Gateau Tango au chocolat 40g', '25.00', '45.00', '45.00', 150, 25, 50, 2, 1, 1, 0, '2.0', datetime('now'), datetime('now')),
('Tango Vanille', 'SNK004', '6111000000024', 'Gateau Tango a la vanille 40g', '25.00', '45.00', '45.00', 150, 25, 50, 2, 1, 1, 0, '2.0', datetime('now'), datetime('now')),
('Biscotte Amor Benamor', 'SNK005', '6111000000025', 'Biscottes Amor Benamor 300g', '80.00', '140.00', '140.00', 50, 8, 16, 2, 8, 1, 0, '7.0', datetime('now'), datetime('now')),

-- Chocolats et confiseries
('Chocolat Milka', 'SNK006', '6111000000026', 'Chocolat au lait Milka 100g', '150.00', '250.00', '250.00', 40, 6, 12, 2, 1, 1, 0, '12.0', datetime('now'), datetime('now')),
('Chocolat Cote Or', 'SNK007', '6111000000027', 'Chocolat Cote Or noir 100g', '160.00', '270.00', '270.00', 35, 5, 10, 2, 1, 1, 0, '13.0', datetime('now'), datetime('now')),
('Bonbons Haribo', 'SNK008', '6111000000028', 'Bonbons Haribo assortis 100g', '120.00', '200.00', '200.00', 60, 10, 20, 2, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Chewing-gum Hollywood', 'SNK009', '6111000000029', 'Chewing-gum Hollywood menthe', '15.00', '30.00', '30.00', 200, 30, 60, 2, 1, 1, 0, '1.5', datetime('now'), datetime('now')),
('Caramels Krema', 'SNK010', '6111000000030', 'Caramels Krema 150g', '80.00', '140.00', '140.00', 50, 8, 16, 2, 1, 1, 0, '7.0', datetime('now'), datetime('now')),

-- Chips et snacks sales
('Chips Bimo Nature', 'SNK011', '6111000000031', 'Chips Bimo nature 50g', '30.00', '55.00', '55.00', 120, 20, 40, 2, 1, 1, 0, '2.5', datetime('now'), datetime('now')),
('Chips Bimo Paprika', 'SNK012', '6111000000032', 'Chips Bimo paprika 50g', '30.00', '55.00', '55.00', 120, 20, 40, 2, 1, 1, 0, '2.5', datetime('now'), datetime('now')),
('Cacahuetes Salees', 'SNK013', '6111000000033', 'Cacahuetes grillees salees 100g', '80.00', '140.00', '140.00', 60, 10, 20, 2, 6, 1, 0, '7.0', datetime('now'), datetime('now')),
('Pistaches Salees', 'SNK014', '6111000000034', 'Pistaches grillees salees 100g', '200.00', '350.00', '350.00', 30, 5, 10, 2, 6, 1, 0, '17.0', datetime('now'), datetime('now')),
('Graines de Tournesol', 'SNK015', '6111000000035', 'Graines de tournesol salees 100g', '60.00', '100.00', '100.00', 80, 12, 25, 2, 6, 1, 0, '5.0', datetime('now'), datetime('now')),

-- =====================================================
-- ELECTRONICS & ACCESSORIES - Category 3
-- =====================================================
-- Telephones et accessoires
('Chargeur Samsung', 'ELC001', '6111000000036', 'Chargeur Samsung Type-C original', '800.00', '1500.00', '1500.00', 25, 4, 8, 3, 1, 1, 0, '75.0', datetime('now'), datetime('now')),
('Chargeur iPhone', 'ELC002', '*************', 'Chargeur iPhone Lightning original', '900.00', '1700.00', '1700.00', 20, 3, 6, 3, 1, 1, 0, '85.0', datetime('now'), datetime('now')),
('Ecouteurs Samsung', 'ELC003', '*************', 'Ecouteurs Samsung Galaxy Buds', '3500.00', '6000.00', '6000.00', 15, 2, 4, 3, 1, 1, 0, '300.0', datetime('now'), datetime('now')),
('Powerbank 10000mAh', 'ELC004', '*************', 'Batterie externe 10000mAh', '2000.00', '3500.00', '3500.00', 20, 3, 6, 3, 1, 1, 0, '175.0', datetime('now'), datetime('now')),
('Cable USB-C', 'ELC005', '*************', 'Cable USB-C 1m haute qualite', '300.00', '600.00', '600.00', 50, 8, 16, 3, 1, 1, 0, '30.0', datetime('now'), datetime('now')),
('Support telephone', 'ELC006', '*************', 'Support telephone voiture', '500.00', '900.00', '900.00', 30, 5, 10, 3, 1, 1, 0, '45.0', datetime('now'), datetime('now')),
('Coque iPhone', 'ELC007', '*************', 'Coque protection iPhone transparente', '400.00', '800.00', '800.00', 40, 6, 12, 3, 1, 1, 0, '40.0', datetime('now'), datetime('now')),
('Coque Samsung', 'ELC008', '*************', 'Coque protection Samsung silicone', '350.00', '700.00', '700.00', 45, 7, 14, 3, 1, 1, 0, '35.0', datetime('now'), datetime('now')),
('Verre trempe iPhone', 'ELC009', '*************', 'Protection ecran verre trempe iPhone', '250.00', '500.00', '500.00', 60, 10, 20, 3, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Verre trempe Samsung', 'ELC010', '6111000000045', 'Protection ecran verre trempe Samsung', '250.00', '500.00', '500.00', 60, 10, 20, 3, 1, 1, 0, '25.0', datetime('now'), datetime('now')),

-- Electronique domestique
('Piles AA Duracell', 'ELC011', '6111000000046', 'Piles AA Duracell pack de 4', '200.00', '400.00', '400.00', 80, 12, 25, 3, 1, 1, 0, '20.0', datetime('now'), datetime('now')),
('Piles AAA Duracell', 'ELC012', '6111000000047', 'Piles AAA Duracell pack de 4', '180.00', '350.00', '350.00', 80, 12, 25, 3, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Ampoule LED 9W', 'ELC013', '6111000000048', 'Ampoule LED economique 9W', '300.00', '600.00', '600.00', 40, 6, 12, 3, 1, 1, 0, '30.0', datetime('now'), datetime('now')),
('Multiprise 4 prises', 'ELC014', '6111000000049', 'Multiprise 4 prises avec interrupteur', '800.00', '1500.00', '1500.00', 25, 4, 8, 3, 1, 1, 0, '75.0', datetime('now'), datetime('now')),
('Rallonge electrique 3m', 'ELC015', '6111000000050', 'Rallonge electrique 3 metres', '600.00', '1200.00', '1200.00', 20, 3, 6, 3, 1, 1, 0, '60.0', datetime('now'), datetime('now')),

-- =====================================================
-- GROCERIES & FOOD ITEMS - Category 4
-- =====================================================
-- Produits laitiers
('Lait Soummam 1L', 'GRO001', '6111000000051', 'Lait frais Soummam entier 1L', '80.00', '130.00', '130.00', 60, 10, 20, 4, 5, 1, 0, '6.5', datetime('now'), datetime('now')),
('Lait Candia 1L', 'GRO002', '6111000000052', 'Lait UHT Candia entier 1L', '85.00', '140.00', '140.00', 50, 8, 16, 4, 3, 1, 0, '7.0', datetime('now'), datetime('now')),
('Yaourt Danone Nature', 'GRO003', '6111000000053', 'Yaourt Danone nature 125g', '35.00', '60.00', '60.00', 100, 15, 30, 4, 3, 1, 0, '3.0', datetime('now'), datetime('now')),
('Yaourt Soummam Fraise', 'GRO004', '6111000000054', 'Yaourt Soummam aux fraises 125g', '40.00', '70.00', '70.00', 80, 12, 25, 4, 5, 1, 0, '3.5', datetime('now'), datetime('now')),
('Fromage La Vache qui Rit', 'GRO005', '6111000000055', 'Fromage La Vache qui Rit 8 portions', '180.00', '300.00', '300.00', 40, 6, 12, 4, 3, 1, 0, '15.0', datetime('now'), datetime('now')),
('Beurre President', 'GRO006', '6111000000056', 'Beurre President doux 250g', '250.00', '420.00', '420.00', 30, 5, 10, 4, 3, 1, 0, '21.0', datetime('now'), datetime('now')),
('Oeufs frais', 'GRO007', '6111000000057', 'Oeufs frais de poule (12 unites)', '200.00', '350.00', '350.00', 50, 8, 16, 4, 5, 1, 0, '17.5', datetime('now'), datetime('now')),

-- Viandes et charcuterie
('Merguez Halal', 'GRO008', '6111000000058', 'Merguez halal 500g', '600.00', '1000.00', '1000.00', 25, 4, 8, 4, 6, 1, 0, '50.0', datetime('now'), datetime('now')),
('Escalope de Dinde', 'GRO009', '6111000000059', 'Escalope de dinde fraiche 500g', '800.00', '1400.00', '1400.00', 20, 3, 6, 4, 6, 1, 0, '70.0', datetime('now'), datetime('now')),
('Viande Hachee', 'GRO010', '6111000000060', 'Viande hachee fraiche 500g', '900.00', '1600.00', '1600.00', 15, 2, 4, 4, 6, 1, 0, '80.0', datetime('now'), datetime('now')),

-- Fruits et legumes
('Pommes Golden', 'GRO011', '6111000000061', 'Pommes Golden 1kg', '180.00', '300.00', '300.00', 40, 6, 12, 4, 6, 1, 0, '15.0', datetime('now'), datetime('now')),
('Bananes', 'GRO012', '6111000000062', 'Bananes fraiches 1kg', '150.00', '250.00', '250.00', 50, 8, 16, 4, 6, 1, 0, '12.5', datetime('now'), datetime('now')),
('Oranges', 'GRO013', '6111000000063', 'Oranges fraiches 1kg', '120.00', '200.00', '200.00', 60, 10, 20, 4, 6, 1, 0, '10.0', datetime('now'), datetime('now')),
('Tomates', 'GRO014', '6111000000064', 'Tomates fraiches 1kg', '100.00', '180.00', '180.00', 70, 12, 25, 4, 6, 1, 0, '9.0', datetime('now'), datetime('now')),
('Pommes de terre', 'GRO015', '6111000000065', 'Pommes de terre 1kg', '80.00', '140.00', '140.00', 100, 15, 30, 4, 6, 1, 0, '7.0', datetime('now'), datetime('now')),
('Oignons', 'GRO016', '6111000000066', 'Oignons jaunes 1kg', '70.00', '120.00', '120.00', 80, 12, 25, 4, 6, 1, 0, '6.0', datetime('now'), datetime('now')),
('Carottes', 'GRO017', '6111000000067', 'Carottes fraiches 1kg', '90.00', '160.00', '160.00', 60, 10, 20, 4, 6, 1, 0, '8.0', datetime('now'), datetime('now')),

-- Epicerie seche
('Riz Basmati', 'GRO018', '6111000000068', 'Riz Basmati 1kg', '200.00', '350.00', '350.00', 50, 8, 16, 4, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Pates Barilla', 'GRO019', '6111000000069', 'Pates Barilla Spaghetti 500g', '120.00', '200.00', '200.00', 80, 12, 25, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Couscous Ferrero', 'GRO020', '6111000000070', 'Couscous Ferrero moyen 1kg', '150.00', '250.00', '250.00', 60, 10, 20, 4, 1, 1, 0, '12.5', datetime('now'), datetime('now')),
('Farine Fleur', 'GRO021', '6111000000071', 'Farine de ble Fleur 1kg', '80.00', '140.00', '140.00', 70, 12, 25, 4, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Sucre Blanc', 'GRO022', '6111000000072', 'Sucre blanc cristallise 1kg', '100.00', '170.00', '170.00', 80, 12, 25, 4, 1, 1, 0, '8.5', datetime('now'), datetime('now')),
('Huile Tournesol Elio', 'GRO023', '6111000000073', 'Huile de tournesol Elio 1L', '180.00', '300.00', '300.00', 40, 6, 12, 4, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Huile Olive Oued Souf', 'GRO024', '6111000000074', 'Huile olive Oued Souf 500ml', '400.00', '700.00', '700.00', 25, 4, 8, 4, 1, 1, 0, '35.0', datetime('now'), datetime('now')),
('Sel de table', 'GRO025', '6111000000075', 'Sel de table fin 1kg', '40.00', '70.00', '70.00', 100, 15, 30, 4, 1, 1, 0, '3.5', datetime('now'), datetime('now')),
('Poivre noir', 'GRO026', '6111000000076', 'Poivre noir moulu 50g', '120.00', '200.00', '200.00', 50, 8, 16, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),

-- Conserves et produits en boite
('Tomates Pelees Amor Benamor', 'GRO027', '6111000000077', 'Tomates pelees Amor Benamor 400g', '80.00', '140.00', '140.00', 60, 10, 20, 4, 8, 1, 0, '7.0', datetime('now'), datetime('now')),
('Concentre Tomate Amor Benamor', 'GRO028', '6111000000078', 'Concentre de tomate Amor Benamor 70g', '35.00', '60.00', '60.00', 120, 20, 40, 4, 8, 1, 0, '3.0', datetime('now'), datetime('now')),
('Thon Rio Mare', 'GRO029', '6111000000079', 'Thon Rio Mare a huile 160g', '180.00', '300.00', '300.00', 50, 8, 16, 4, 6, 1, 0, '15.0', datetime('now'), datetime('now')),
('Sardines Aicha', 'GRO030', '6111000000080', 'Sardines Aicha a huile 125g', '120.00', '200.00', '200.00', 70, 12, 25, 4, 6, 1, 0, '10.0', datetime('now'), datetime('now')),
('Haricots Blancs', 'GRO031', '6111000000081', 'Haricots blancs en conserve 400g', '100.00', '170.00', '170.00', 60, 10, 20, 4, 6, 1, 0, '8.5', datetime('now'), datetime('now')),
('Petits Pois Bonduelle', 'GRO032', '6111000000082', 'Petits pois Bonduelle 400g', '150.00', '250.00', '250.00', 50, 8, 16, 4, 6, 1, 0, '12.5', datetime('now'), datetime('now')),
('Mais Bonduelle', 'GRO033', '6111000000083', 'Mais doux Bonduelle 340g', '140.00', '230.00', '230.00', 50, 8, 16, 4, 6, 1, 0, '11.5', datetime('now'), datetime('now')),

-- Produits hygiene et beaute
('Savon Dove', 'HYG001', '6111000000084', 'Savon Dove hydratant 100g', '120.00', '200.00', '200.00', 60, 10, 20, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Shampoing Loreal', 'HYG002', '6111000000085', 'Shampoing Loreal Elseve 400ml', '350.00', '600.00', '600.00', 30, 5, 10, 4, 1, 1, 0, '30.0', datetime('now'), datetime('now')),
('Dentifrice Signal', 'HYG003', '6111000000086', 'Dentifrice Signal protection 75ml', '180.00', '300.00', '300.00', 50, 8, 16, 4, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Brosse a dents', 'HYG004', '6111000000087', 'Brosse a dents medium', '80.00', '140.00', '140.00', 80, 12, 25, 4, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Deodorant Nivea', 'HYG005', '6111000000088', 'Deodorant Nivea spray 150ml', '250.00', '420.00', '420.00', 40, 6, 12, 4, 1, 1, 0, '21.0', datetime('now'), datetime('now')),

-- Produits menagers
('Lessive Ariel', 'MEN001', '6111000000089', 'Lessive Ariel poudre 1kg', '300.00', '500.00', '500.00', 40, 6, 12, 4, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Liquide vaisselle Fairy', 'MEN002', '6111000000090', 'Liquide vaisselle Fairy 500ml', '180.00', '300.00', '300.00', 60, 10, 20, 4, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Javel Lacroix', 'MEN003', '6111000000091', 'Eau de Javel Lacroix 1L', '80.00', '140.00', '140.00', 50, 8, 16, 4, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Papier toilette', 'MEN004', '6111000000092', 'Papier toilette 4 rouleaux', '200.00', '350.00', '350.00', 60, 10, 20, 4, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Essuie-tout', 'MEN005', '6111000000093', 'Essuie-tout 2 rouleaux', '150.00', '250.00', '250.00', 70, 12, 25, 4, 1, 1, 0, '12.5', datetime('now'), datetime('now')),

-- Produits traditionnels algeriens
('Harissa Traditionnelle', 'TRA001', '6111000000094', 'Harissa traditionnelle algerienne 200g', '150.00', '250.00', '250.00', 40, 6, 12, 4, 8, 1, 0, '12.5', datetime('now'), datetime('now')),
('Ras el Hanout', 'TRA002', '6111000000095', 'Melange epices Ras el Hanout 50g', '200.00', '350.00', '350.00', 30, 5, 10, 4, 8, 1, 0, '17.5', datetime('now'), datetime('now')),
('Makroudh', 'TRA003', '6111000000096', 'Makroudh aux dattes 250g', '180.00', '300.00', '300.00', 35, 5, 10, 4, 8, 1, 0, '15.0', datetime('now'), datetime('now')),
('Chouarak', 'TRA004', '6111000000097', 'Chouarak traditionnel 400g', '120.00', '200.00', '200.00', 25, 4, 8, 4, 8, 1, 0, '10.0', datetime('now'), datetime('now')),
('Miel de Jujubier', 'TRA005', '6111000000098', 'Miel de jujubier du Sud 250g', '800.00', '1400.00', '1400.00', 15, 2, 4, 4, 8, 1, 0, '70.0', datetime('now'), datetime('now')),
('Dattes Deglet Nour', 'TRA006', '6111000000099', 'Dattes Deglet Nour 500g', '300.00', '500.00', '500.00', 30, 5, 10, 4, 8, 1, 0, '25.0', datetime('now'), datetime('now')),
('Figues Seches', 'TRA007', '6111000000100', 'Figues seches de Kabylie 250g', '250.00', '420.00', '420.00', 25, 4, 8, 4, 8, 1, 0, '21.0', datetime('now'), datetime('now')),

-- Boissons chaudes
('The Vert Palais des Thes', 'BOI001', '6111000000101', 'The vert en vrac 100g', '300.00', '500.00', '500.00', 40, 6, 12, 1, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Cafe Moulu Malongo', 'BOI002', '6111000000102', 'Cafe moulu Malongo 250g', '400.00', '700.00', '700.00', 30, 5, 10, 1, 1, 1, 0, '35.0', datetime('now'), datetime('now')),
('Nescafe Classic', 'BOI003', '6111000000103', 'Cafe soluble Nescafe Classic 100g', '350.00', '600.00', '600.00', 50, 8, 16, 1, 1, 1, 0, '30.0', datetime('now'), datetime('now')),
('The Lipton', 'BOI004', '6111000000104', 'The Lipton sachets x25', '200.00', '350.00', '350.00', 60, 10, 20, 1, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Tisane Verveine', 'BOI005', '6111000000105', 'Tisane verveine sachets x20', '180.00', '300.00', '300.00', 40, 6, 12, 1, 1, 1, 0, '15.0', datetime('now'), datetime('now')),

-- Produits surgeles
('Frites McCain', 'SUR001', '6111000000106', 'Frites McCain surgelees 1kg', '250.00', '420.00', '420.00', 30, 5, 10, 4, 1, 1, 0, '21.0', datetime('now'), datetime('now')),
('Pizza Margherita', 'SUR002', '6111000000107', 'Pizza Margherita surgelee 350g', '300.00', '500.00', '500.00', 25, 4, 8, 4, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Legumes Melanges', 'SUR003', '6111000000108', 'Legumes melanges surgeles 500g', '180.00', '300.00', '300.00', 40, 6, 12, 4, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Crevettes Surgelees', 'SUR004', '6111000000109', 'Crevettes decortiquees surgelees 300g', '600.00', '1000.00', '1000.00', 20, 3, 6, 4, 6, 1, 0, '50.0', datetime('now'), datetime('now')),

-- Produits de boulangerie
('Pain de Mie', 'BOU001', '6111000000110', 'Pain de mie complet 500g', '100.00', '170.00', '170.00', 50, 8, 16, 4, 8, 1, 0, '8.5', datetime('now'), datetime('now')),
('Baguette Francaise', 'BOU002', '6111000000111', 'Baguette francaise fraiche', '30.00', '50.00', '50.00', 100, 15, 30, 4, 8, 1, 0, '2.5', datetime('now'), datetime('now')),
('Croissant', 'BOU003', '6111000000112', 'Croissant au beurre', '40.00', '70.00', '70.00', 80, 12, 25, 4, 8, 1, 0, '3.5', datetime('now'), datetime('now')),
('Pain Complet', 'BOU004', '6111000000113', 'Pain complet 400g', '80.00', '140.00', '140.00', 40, 6, 12, 4, 8, 1, 0, '7.0', datetime('now'), datetime('now')),

-- Condiments et sauces
('Mayonnaise Amora', 'CON001', '6111000000114', 'Mayonnaise Amora 250ml', '150.00', '250.00', '250.00', 50, 8, 16, 4, 1, 1, 0, '12.5', datetime('now'), datetime('now')),
('Ketchup Heinz', 'CON002', '6111000000115', 'Ketchup Heinz 340g', '180.00', '300.00', '300.00', 40, 6, 12, 4, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Moutarde Amora', 'CON003', '6111000000116', 'Moutarde Amora forte 200g', '120.00', '200.00', '200.00', 60, 10, 20, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Vinaigre Blanc', 'CON004', '6111000000117', 'Vinaigre blanc 500ml', '60.00', '100.00', '100.00', 70, 12, 25, 4, 1, 1, 0, '5.0', datetime('now'), datetime('now')),
('Sauce Soja', 'CON005', '6111000000118', 'Sauce soja 150ml', '100.00', '170.00', '170.00', 50, 8, 16, 4, 1, 1, 0, '8.5', datetime('now'), datetime('now')),

-- Cereales et petit-dejeuner
('Cereales Nestle', 'CER001', '6111000000119', 'Cereales Nestle Fitness 375g', '400.00', '700.00', '700.00', 30, 5, 10, 4, 1, 1, 0, '35.0', datetime('now'), datetime('now')),
('Corn Flakes Kelloggs', 'CER002', '6111000000120', 'Corn Flakes Kelloggs 375g', '450.00', '800.00', '800.00', 25, 4, 8, 4, 1, 1, 0, '40.0', datetime('now'), datetime('now')),
('Avoine Quaker', 'CER003', '6111000000121', 'Flocons avoine Quaker 500g', '300.00', '500.00', '500.00', 40, 6, 12, 4, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Muesli Jordans', 'CER004', '6111000000122', 'Muesli aux fruits Jordans 500g', '500.00', '900.00', '900.00', 20, 3, 6, 4, 1, 1, 0, '45.0', datetime('now'), datetime('now')),

-- Fruits secs et noix
('Amandes', 'FRU001', '6111000000123', 'Amandes decortiquees 200g', '400.00', '700.00', '700.00', 30, 5, 10, 4, 6, 1, 0, '35.0', datetime('now'), datetime('now')),
('Noix', 'FRU002', '6111000000124', 'Noix decortiquees 200g', '500.00', '900.00', '900.00', 25, 4, 8, 4, 6, 1, 0, '45.0', datetime('now'), datetime('now')),
('Raisins Secs', 'FRU003', '6111000000125', 'Raisins secs 250g', '200.00', '350.00', '350.00', 40, 6, 12, 4, 6, 1, 0, '17.5', datetime('now'), datetime('now')),
('Abricots Secs', 'FRU004', '6111000000126', 'Abricots secs 200g', '300.00', '500.00', '500.00', 30, 5, 10, 4, 6, 1, 0, '25.0', datetime('now'), datetime('now')),

-- Produits pour bebes
('Lait Bebe Bledina', 'BEB001', '6111000000127', 'Lait infantile Bledina 1er age 900g', '1200.00', '2000.00', '2000.00', 20, 3, 6, 4, 3, 1, 0, '100.0', datetime('now'), datetime('now')),
('Petits Pots Bledina', 'BEB002', '6111000000128', 'Petits pots legumes Bledina 130g', '150.00', '250.00', '250.00', 50, 8, 16, 4, 3, 1, 0, '12.5', datetime('now'), datetime('now')),
('Couches Pampers', 'BEB003', '6111000000129', 'Couches Pampers taille 3 x44', '1500.00', '2500.00', '2500.00', 15, 2, 4, 4, 1, 1, 0, '125.0', datetime('now'), datetime('now')),
('Lingettes Bebe', 'BEB004', '6111000000130', 'Lingettes bebe x80', '300.00', '500.00', '500.00', 40, 6, 12, 4, 1, 1, 0, '25.0', datetime('now'), datetime('now')),

-- Produits de pharmacie/parapharmacie
('Paracetamol', 'PHA001', '6111000000131', 'Paracetamol 500mg x20 comprimes', '80.00', '150.00', '150.00', 100, 15, 30, 4, 1, 1, 0, '7.5', datetime('now'), datetime('now')),
('Aspirine', 'PHA002', '6111000000132', 'Aspirine 500mg x20 comprimes', '100.00', '180.00', '180.00', 80, 12, 25, 4, 1, 1, 0, '9.0', datetime('now'), datetime('now')),
('Vitamines C', 'PHA003', '6111000000133', 'Vitamine C 1000mg x30 comprimes', '200.00', '350.00', '350.00', 50, 8, 16, 4, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Pansements', 'PHA004', '6111000000134', 'Pansements assortis x20', '120.00', '200.00', '200.00', 60, 10, 20, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),

-- Articles de papeterie
('Stylos Bic', 'PAP001', '6111000000135', 'Stylos Bic bleus x4', '80.00', '140.00', '140.00', 100, 15, 30, 3, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Cahier 96 pages', 'PAP002', '6111000000136', 'Cahier grand format 96 pages', '60.00', '100.00', '100.00', 80, 12, 25, 3, 1, 1, 0, '5.0', datetime('now'), datetime('now')),
('Crayons de couleur', 'PAP003', '6111000000137', 'Crayons de couleur x12', '150.00', '250.00', '250.00', 50, 8, 16, 3, 1, 1, 0, '12.5', datetime('now'), datetime('now')),
('Gomme', 'PAP004', '6111000000138', 'Gomme blanche', '20.00', '40.00', '40.00', 150, 25, 50, 3, 1, 1, 0, '2.0', datetime('now'), datetime('now')),

-- Jouets et divertissement
('Ballon de foot', 'JOU001', '6111000000139', 'Ballon de football taille 5', '800.00', '1400.00', '1400.00', 15, 2, 4, 3, 1, 1, 0, '70.0', datetime('now'), datetime('now')),
('Puzzle 1000 pieces', 'JOU002', '6111000000140', 'Puzzle paysage 1000 pieces', '600.00', '1000.00', '1000.00', 20, 3, 6, 3, 1, 1, 0, '50.0', datetime('now'), datetime('now')),
('Cartes a jouer', 'JOU003', '6111000000141', 'Jeu de cartes classique', '100.00', '180.00', '180.00', 60, 10, 20, 3, 1, 1, 0, '9.0', datetime('now'), datetime('now')),
('Dominos', 'JOU004', '6111000000142', 'Jeu de dominos', '200.00', '350.00', '350.00', 30, 5, 10, 3, 1, 1, 0, '17.5', datetime('now'), datetime('now')),

-- Accessoires de cuisine
('Assiettes Plastique', 'CUI001', '6111000000143', 'Assiettes plastique x6', '300.00', '500.00', '500.00', 40, 6, 12, 4, 1, 1, 0, '25.0', datetime('now'), datetime('now')),
('Verres Plastique', 'CUI002', '6111000000144', 'Verres plastique x6', '200.00', '350.00', '350.00', 50, 8, 16, 4, 1, 1, 0, '17.5', datetime('now'), datetime('now')),
('Couverts Plastique', 'CUI003', '6111000000145', 'Set couverts plastique x18', '150.00', '250.00', '250.00', 60, 10, 20, 4, 1, 1, 0, '12.5', datetime('now'), datetime('now')),
('Film Alimentaire', 'CUI004', '6111000000146', 'Film alimentaire 30m', '120.00', '200.00', '200.00', 70, 12, 25, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Papier Aluminium', 'CUI005', '6111000000147', 'Papier aluminium 20m', '150.00', '250.00', '250.00', 60, 10, 20, 4, 1, 1, 0, '12.5', datetime('now'), datetime('now')),

-- Produits divers et accessoires
('Sacs Plastique', 'DIV001', '6111000000148', 'Sacs plastique x50', '80.00', '140.00', '140.00', 100, 15, 30, 4, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Allumettes', 'DIV002', '6111000000149', 'Boite allumettes', '10.00', '20.00', '20.00', 200, 30, 60, 4, 1, 1, 0, '1.0', datetime('now'), datetime('now')),
('Briquet', 'DIV003', '6111000000150', 'Briquet jetable', '30.00', '50.00', '50.00', 150, 25, 50, 4, 1, 1, 0, '2.5', datetime('now'), datetime('now')),
('Bougies', 'DIV004', '6111000000151', 'Bougies blanches x6', '100.00', '170.00', '170.00', 80, 12, 25, 4, 1, 1, 0, '8.5', datetime('now'), datetime('now')),
('Corde a linge', 'DIV005', '6111000000152', 'Corde a linge 10m', '120.00', '200.00', '200.00', 50, 8, 16, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),
('Pinces a linge', 'DIV006', '6111000000153', 'Pinces a linge x20', '80.00', '140.00', '140.00', 70, 12, 25, 4, 1, 1, 0, '7.0', datetime('now'), datetime('now')),
('Eponges', 'DIV007', '6111000000154', 'Eponges de cuisine x5', '100.00', '170.00', '170.00', 80, 12, 25, 4, 1, 1, 0, '8.5', datetime('now'), datetime('now')),
('Gants Menage', 'DIV008', '6111000000155', 'Gants de menage taille M', '120.00', '200.00', '200.00', 60, 10, 20, 4, 1, 1, 0, '10.0', datetime('now'), datetime('now')),

-- Produits de saison et fetes
('Chocolats Assortis', 'FET001', '6111000000156', 'Chocolats assortis boite 200g', '600.00', '1000.00', '1000.00', 25, 4, 8, 2, 1, 1, 0, '50.0', datetime('now'), datetime('now')),
('Dragees', 'FET002', '6111000000157', 'Dragees blanches 500g', '800.00', '1400.00', '1400.00', 20, 3, 6, 2, 1, 1, 0, '70.0', datetime('now'), datetime('now')),
('Biscuits Aid', 'FET003', '6111000000158', 'Biscuits traditionnels Aid 300g', '250.00', '420.00', '420.00', 30, 5, 10, 2, 8, 1, 0, '21.0', datetime('now'), datetime('now')),

-- Produits bio et naturels
('Miel Bio', 'BIO001', '6111000000159', 'Miel bio de montagne 250g', '600.00', '1000.00', '1000.00', 20, 3, 6, 4, 8, 1, 0, '50.0', datetime('now'), datetime('now')),
('Huile Olive Bio', 'BIO002', '6111000000160', 'Huile olive bio 500ml', '800.00', '1400.00', '1400.00', 15, 2, 4, 4, 8, 1, 0, '70.0', datetime('now'), datetime('now')),
('The Vert Bio', 'BIO003', '6111000000161', 'The vert bio 100g', '400.00', '700.00', '700.00', 25, 4, 8, 1, 1, 1, 0, '35.0', datetime('now'), datetime('now')),

-- Produits importes speciaux
('Nutella', 'IMP001', '6111000000162', 'Pate a tartiner Nutella 400g', '500.00', '900.00', '900.00', 30, 5, 10, 4, 1, 1, 0, '45.0', datetime('now'), datetime('now')),
('Fromage President', 'IMP002', '6111000000163', 'Fromage President camembert 250g', '400.00', '700.00', '700.00', 25, 4, 8, 4, 3, 1, 0, '35.0', datetime('now'), datetime('now')),
('Saumon Fume', 'IMP003', '6111000000164', 'Saumon fume tranche 100g', '800.00', '1400.00', '1400.00', 15, 2, 4, 4, 6, 1, 0, '70.0', datetime('now'), datetime('now')),

-- Derniers produits pour atteindre 200
('Eau de Rose', 'COS001', '6111000000165', 'Eau de rose naturelle 250ml', '200.00', '350.00', '350.00', 40, 6, 12, 4, 8, 1, 0, '17.5', datetime('now'), datetime('now')),
('Henne Naturel', 'COS002', '6111000000166', 'Henne naturel en poudre 100g', '150.00', '250.00', '250.00', 50, 8, 16, 4, 8, 1, 0, '12.5', datetime('now'), datetime('now')),
('Savon Noir', 'COS003', '6111000000167', 'Savon noir traditionnel 200g', '100.00', '170.00', '170.00', 60, 10, 20, 4, 8, 1, 0, '8.5', datetime('now'), datetime('now')),
('Ghassoul', 'COS004', '6111000000168', 'Argile Ghassoul 200g', '180.00', '300.00', '300.00', 40, 6, 12, 4, 8, 1, 0, '15.0', datetime('now'), datetime('now')),
('Huile Argan', 'COS005', '6111000000169', 'Huile argan pure 50ml', '500.00', '900.00', '900.00', 20, 3, 6, 4, 8, 1, 0, '45.0', datetime('now'), datetime('now')),
('Encens Traditionnel', 'COS006', '6111000000170', 'Encens traditionnel 50g', '120.00', '200.00', '200.00', 50, 8, 16, 4, 8, 1, 0, '10.0', datetime('now'), datetime('now')),
('Miswak', 'COS007', '6111000000171', 'Baton de Miswak naturel', '50.00', '80.00', '80.00', 100, 15, 30, 4, 8, 1, 0, '4.0', datetime('now'), datetime('now')),
('Kohl Naturel', 'COS008', '6111000000172', 'Kohl naturel noir 5g', '80.00', '140.00', '140.00', 80, 12, 25, 4, 8, 1, 0, '7.0', datetime('now'), datetime('now')),
('Parfum Musc', 'COS009', '6111000000173', 'Parfum musc blanc 6ml', '300.00', '500.00', '500.00', 30, 5, 10, 4, 8, 1, 0, '25.0', datetime('now'), datetime('now')),
('Tapis de Priere', 'REL001', '6111000000174', 'Tapis de priere portable', '800.00', '1400.00', '1400.00', 15, 2, 4, 4, 8, 1, 0, '70.0', datetime('now'), datetime('now')),
('Chapelet', 'REL002', '6111000000175', 'Chapelet en bois 99 grains', '200.00', '350.00', '350.00', 40, 6, 12, 4, 8, 1, 0, '17.5', datetime('now'), datetime('now')),
('Coran de Poche', 'REL003', '6111000000176', 'Coran de poche avec traduction', '300.00', '500.00', '500.00', 25, 4, 8, 4, 8, 1, 0, '25.0', datetime('now'), datetime('now')),
('Dates Ajwa', 'REL004', '6111000000177', 'Dattes Ajwa de Medine 500g', '1200.00', '2000.00', '2000.00', 10, 1, 2, 4, 8, 1, 0, '100.0', datetime('now'), datetime('now')),
('Eau Zamzam', 'REL005', '6111000000178', 'Eau Zamzam 500ml', '400.00', '700.00', '700.00', 20, 3, 6, 1, 8, 1, 0, '35.0', datetime('now'), datetime('now')),
('Huile Nigelle', 'REL006', '6111000000179', 'Huile de nigelle 100ml', '300.00', '500.00', '500.00', 30, 5, 10, 4, 8, 1, 0, '25.0', datetime('now'), datetime('now')),
('Graines Nigelle', 'REL007', '6111000000180', 'Graines de nigelle 100g', '200.00', '350.00', '350.00', 40, 6, 12, 4, 8, 1, 0, '17.5', datetime('now'), datetime('now')),
('Siwak Holder', 'REL008', '6111000000181', 'Etui pour Siwak', '100.00', '170.00', '170.00', 60, 10, 20, 4, 8, 1, 0, '8.5', datetime('now'), datetime('now')),
('Calendrier Hijri', 'REL009', '6111000000182', 'Calendrier hegirien 2024', '150.00', '250.00', '250.00', 50, 8, 16, 4, 8, 1, 0, '12.5', datetime('now'), datetime('now')),
('Boussole Qibla', 'REL010', '6111000000183', 'Boussole direction Qibla', '250.00', '420.00', '420.00', 30, 5, 10, 3, 8, 1, 0, '21.0', datetime('now'), datetime('now')),
('Livre Invocations', 'REL011', '6111000000184', 'Livre des invocations quotidiennes', '200.00', '350.00', '350.00', 35, 5, 10, 4, 8, 1, 0, '17.5', datetime('now'), datetime('now'));

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Base de donnees de produits algeriens creee avec succes!' AS Result;
SELECT 'Produits ajoutes: ' || COUNT(*) AS ProductCount FROM Products;
SELECT 'Fournisseurs ajoutes: ' || COUNT(*) AS SupplierCount FROM Suppliers;
