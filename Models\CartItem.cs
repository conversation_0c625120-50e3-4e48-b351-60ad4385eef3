﻿using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.ObjectModel;
using System.Linq;
using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using POSSystem.Services;
using POSSystem.Data;

namespace POSSystem.Models
{
    public class CartItem : INotifyPropertyChanged
    {
        public int Id { get; set; }
        private decimal _quantity;
        private decimal _unitPrice;
        private decimal _total;
        private Product _product;
        private ObservableCollection<Discount> _discounts;
        private ProductPriceTier _appliedPriceTier;
        private decimal _bulkSavings;
        private static readonly BulkPricingService _bulkPricingService = new BulkPricingService();
        private bool _isCalculatingBulkPricing = false;
        private bool _hasAttemptedPricingRefresh = false;

        // ✅ PERFORMANCE FIX: Control debug logging to prevent frame rate drops
        private static bool _debugLoggingEnabled = false;

        /// <summary>
        /// Enable or disable debug logging for cart calculations (for troubleshooting)
        /// </summary>
        public static void SetDebugLogging(bool enabled)
        {
            _debugLoggingEnabled = enabled;
        }

        public int ProductId { get; set; }
        public int SaleId { get; set; }

        // Batch-specific unit price to use for this cart line if coming from a specific batch (FIFO)
        public decimal? BatchUnitPrice { get; set; }

        public CartItem()
        {
            _discounts = new ObservableCollection<Discount>();
            _discounts.CollectionChanged += (s, e) =>
            {
                CalculateTotal();
                OnPropertyChanged(nameof(DiscountPercentage));
                OnPropertyChanged(nameof(HasDiscount));
                OnPropertyChanged(nameof(DiscountAmount));
            };
        }

        public Product Product
        {
            get => _product;
            set
            {
                _product = value;
                _hasAttemptedPricingRefresh = false; // Reset refresh flag when product changes
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        /// <summary>
        /// Quantity of the product in the cart.
        /// For weight-based products, this can be a decimal value (e.g., 2.5 kg).
        /// For unit-based products, this should be a whole number (e.g., 3 pieces).
        /// </summary>
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
                #if DEBUG
                if (_debugLoggingEnabled)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART_QUANTITY] Quantity changing for {Product?.Name ?? "Unknown"}: {_quantity} → {value}");
                }
                #endif

                _quantity = value;
                OnPropertyChanged();

                CalculateTotal();

                #if DEBUG
                if (_debugLoggingEnabled)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART_QUANTITY] CalculateTotal() completed - Final unit price: {UnitPrice}, Total: {Total}");
                }
                #endif
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                _unitPrice = value;
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        public ObservableCollection<Discount> Discounts
        {
            get
            {
                if (_discounts == null)
                {
                    _discounts = new ObservableCollection<Discount>();
                    _discounts.CollectionChanged += (s, e) => CalculateTotal();
                }
                return _discounts;
            }
            set
            {
                _discounts = value;
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        public decimal DiscountAmount
        {
            get
            {
                decimal totalDiscount = 0;
                if (Discounts != null)
                {
                    foreach (var discount in Discounts)
                    {
                        // DiscountValue already contains the monetary amount for both percentage and fixed discounts
                        totalDiscount += discount.DiscountValue;
                    }
                }
                return totalDiscount;
            }
        }

        public decimal Total
        {
            get => (UnitPrice * Quantity) - DiscountAmount;
            set
            {
                _total = value;
                OnPropertyChanged();
            }
        }

        public bool HasDiscount => Discounts != null && Discounts.Any();

        /// <summary>
        /// Helper property to determine if this cart item represents a service
        /// </summary>
        public bool IsService => Product?.Type == ProductType.Service;

        /// <summary>
        /// Helper property to get the appropriate item type display text
        /// </summary>
        public string ItemTypeDisplay => IsService ? "Service" : "Product";

        public decimal DiscountedTotal => Total;

        public decimal OriginalTotal => Quantity * UnitPrice;

        // ===== BULK PRICING PROPERTIES =====

        /// <summary>
        /// The pricing tier applied to this cart item (if any)
        /// </summary>
        public ProductPriceTier AppliedPriceTier
        {
            get => _appliedPriceTier;
            private set
            {
                _appliedPriceTier = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasBulkPricing));
                OnPropertyChanged(nameof(BulkPricingDisplay));
                OnPropertyChanged(nameof(BulkSavingsDisplay));
            }
        }

        /// <summary>
        /// Amount saved due to bulk pricing
        /// </summary>
        public decimal BulkSavings
        {
            get => _bulkSavings;
            private set
            {
                _bulkSavings = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(BulkSavingsDisplay));
                OnPropertyChanged(nameof(HasBulkPricing));
            }
        }

        /// <summary>
        /// Whether this item has bulk pricing applied
        /// </summary>
        public bool HasBulkPricing => AppliedPriceTier != null && BulkSavings > 0;

        /// <summary>
        /// Display text for bulk pricing information
        /// </summary>
        public string BulkPricingDisplay
        {
            get
            {
                if (AppliedPriceTier != null)
                {
                    return $"Bulk: {AppliedPriceTier.GetDisplayText()}";
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// Display text for bulk savings
        /// </summary>
        public string BulkSavingsDisplay
        {
            get
            {
                if (HasBulkPricing)
                {
                    var percentage = OriginalTotal > 0 ? (BulkSavings / OriginalTotal) * 100 : 0;
                    return $"Save {BulkSavings:C2} ({percentage:F1}%)";
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// Regular unit price baseline (without bulk pricing)
        /// Prefers batch-specific unit price when available; otherwise falls back to product price
        /// </summary>
        public decimal RegularUnitPrice => BatchUnitPrice ?? (Product?.SellingPrice ?? 0);

        /// <summary>
        /// Total price at regular pricing (without bulk discounts)
        /// </summary>
        public decimal RegularTotal => Quantity * RegularUnitPrice;

        public decimal DiscountPercentage
        {
            get
            {
                if (!HasDiscount) return 0;

                decimal totalPercentage = 0;
                decimal itemTotal = UnitPrice * Quantity;

                if (Discounts != null && itemTotal > 0)
                {
                    foreach (var discount in Discounts)
                    {
                        if (discount?.DiscountType?.Name == "Percentage")
                        {
                            // DiscountValue contains the monetary amount, so calculate percentage from it
                            decimal percentage = (discount.DiscountValue / itemTotal) * 100;
                            totalPercentage += percentage;
                        }
                    }
                }

                return Math.Round(totalPercentage, 2);
            }
        }

        private void CalculateTotal()
        {
            // ✅ PERFORMANCE FIX: Reduced debug logging to prevent frame rate drops
            #if DEBUG
            if (_debugLoggingEnabled)
            {
                System.Diagnostics.Debug.WriteLine($"[CART_CALCULATE] CalculateTotal called for {Product?.Name ?? "Unknown"}");
            }
            #endif

            // Apply bulk pricing if available (only if not already calculating)
            if (!_isCalculatingBulkPricing)
            {
                ApplyBulkPricing();
            }

            // Calculate total with current unit price and discounts
            var newTotal = (UnitPrice * Quantity) - DiscountAmount;

            Total = newTotal;
            OnPropertyChanged(nameof(Total));

            #if DEBUG
            if (_debugLoggingEnabled)
            {
                System.Diagnostics.Debug.WriteLine($"[CART_CALCULATE] CalculateTotal completed - Final Total: {Total}");
            }
            #endif
        }

        /// <summary>
        /// Applies bulk pricing to this cart item if applicable
        /// </summary>
        public void ApplyBulkPricing()
        {
            #if DEBUG
            if (_debugLoggingEnabled)
            {
                System.Diagnostics.Debug.WriteLine($"[CART_BULK_PRICING] ApplyBulkPricing called for product: {Product?.Name ?? "NULL"} (ID: {Product?.Id ?? -1})");
            }
            #endif

            // ✅ CRITICAL FIX: If product doesn't have pricing tiers loaded, try to refresh them (only once)
            if (Product != null && (Product.PriceTiers == null || !Product.PriceTiers.Any()) && Product.Id > 0 && !_hasAttemptedPricingRefresh)
            {
                _hasAttemptedPricingRefresh = true;
                _ = Task.Run(async () => await RefreshProductPricingTiers());
                return; // Exit early, will be called again after refresh
            }

            if (_isCalculatingBulkPricing || Product == null || !Product.HasBulkPricing)
            {
                if (Product == null || !Product.HasBulkPricing)
                {
                    // Reset bulk pricing if not applicable
                    AppliedPriceTier = null;
                    BulkSavings = 0;
                    SetUnitPriceDirectly(RegularUnitPrice);
                }
                return;
            }

            try
            {
                _isCalculatingBulkPricing = true;

                var bulkPricingResult = _bulkPricingService.CalculateBestPricing(Product, Quantity);

                if (bulkPricingResult.HasBulkDiscount)
                {
                    AppliedPriceTier = bulkPricingResult.AppliedTier;
                    BulkSavings = bulkPricingResult.TotalSavings;
                    SetUnitPriceDirectly(bulkPricingResult.EffectiveUnitPrice);

                    #if DEBUG
                    if (_debugLoggingEnabled)
                    {
                        System.Diagnostics.Debug.WriteLine($"[CART_BULK_PRICING] Bulk pricing applied - New unit price: {UnitPrice}, Savings: {BulkSavings}");
                    }
                    #endif
                }
                else
                {
                    // No bulk pricing applies
                    AppliedPriceTier = null;
                    BulkSavings = 0;
                    SetUnitPriceDirectly(RegularUnitPrice);
                }
            }
            catch (Exception ex)
            {
                #if DEBUG
                System.Diagnostics.Debug.WriteLine($"[CART_BULK_PRICING] Error applying bulk pricing: {ex.Message}");
                #endif
                // Fallback to regular pricing
                AppliedPriceTier = null;
                BulkSavings = 0;
                SetUnitPriceDirectly(RegularUnitPrice);
            }
            finally
            {
                _isCalculatingBulkPricing = false;
            }
        }

        /// <summary>
        /// Sets the unit price directly without triggering CalculateTotal to prevent circular dependency
        /// </summary>
        private void SetUnitPriceDirectly(decimal price)
        {
            _unitPrice = price;
            OnPropertyChanged(nameof(UnitPrice));
        }

        /// <summary>
        /// Forces recalculation of bulk pricing (useful when product pricing tiers change)
        /// </summary>
        public void RefreshBulkPricing()
        {
            _hasAttemptedPricingRefresh = false; // Reset the flag to allow refresh if needed
            ApplyBulkPricing();
            CalculateTotal();
        }

        /// <summary>
        /// Refreshes the product's pricing tiers from the database
        /// </summary>
        private async Task RefreshProductPricingTiers()
        {
            if (Product?.Id <= 0) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Refreshing pricing tiers for product {Product.Name} (ID: {Product.Id})");

                using (var context = new POSDbContext())
                {
                    // First check if any pricing tiers exist for this product (active or inactive)
                    var allPriceTiers = await context.ProductPriceTiers
                        .AsNoTracking()
                        .Where(pt => pt.ProductId == Product.Id)
                        .ToListAsync();

                    var priceTiers = allPriceTiers.Where(pt => pt.IsActive).ToList();

                    System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Found {allPriceTiers.Count} total pricing tiers ({priceTiers.Count} active) in database for product {Product.Id}");

                    if (allPriceTiers.Any() && !priceTiers.Any())
                    {
                        System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] WARNING: Product has pricing tiers but none are active!");
                        foreach (var tier in allPriceTiers)
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Inactive tier: MinQty={tier.MinimumQuantity}, UnitPrice={tier.UnitPrice}, IsActive={tier.IsActive}");
                        }
                    }

                    // Update the product's pricing tiers on the UI thread
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Product.PriceTiers = priceTiers;
                        System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Updated product pricing tiers, HasBulkPricing: {Product.HasBulkPricing}");

                        // Only trigger bulk pricing recalculation if pricing tiers were actually found
                        if (priceTiers.Any())
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Found pricing tiers, triggering bulk pricing recalculation");
                            ApplyBulkPricing();
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] No active pricing tiers found, skipping bulk pricing recalculation");
                            // Reset to regular pricing since no bulk pricing is available
                            AppliedPriceTier = null;
                            BulkSavings = 0;
                            SetUnitPriceDirectly(RegularUnitPrice);

                            // Mark that we've confirmed this product has no bulk pricing to prevent future refresh attempts
                            _hasAttemptedPricingRefresh = true;
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CART_REFRESH] Error refreshing pricing tiers: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets quantity suggestions for better bulk pricing
        /// </summary>
        /// <returns>List of quantity suggestions</returns>
        public List<QuantitySuggestion> GetQuantitySuggestions()
        {
            if (Product == null || !Product.HasBulkPricing)
                return new List<QuantitySuggestion>();

            return _bulkPricingService.GetQuantitySuggestions(Product, Quantity);
        }

        public void AddDiscount(Discount discount)
        {
            if (discount == null) return;
            Discounts.Add(discount);
        }

        public void RemoveDiscount(Discount discount)
        {
            if (discount == null) return;
            Discounts.Remove(discount);
        }

        public void ClearDiscounts()
        {
            Discounts.Clear();
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}