using System;
using System.Collections.Concurrent;
using System.IO;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using System.Linq;

namespace POSSystem.Services.Images
{
    /// <summary>
    /// In-memory cache for decoded thumbnails created from base64 strings.
    /// Stores frozen BitmapSource instances sized for UI display to reduce CPU and memory.
    /// </summary>
    public sealed class ImageCacheService
    {
        private static readonly Lazy<ImageCacheService> _lazy = new(() => new ImageCacheService());
        public static ImageCacheService Instance => _lazy.Value;

        private readonly ConcurrentDictionary<string, WeakReference<BitmapSource>> _cache = new();
        private readonly object _cleanupLock = new();
        private const int MaxItems = 120; // More conservative cap to limit memory footprint

        private ImageCacheService() { }
        private const long WorkingSetThresholdBytes = 700L * 1024 * 1024; // 700 MB
        private DateTime _lastAdaptiveCleanup = DateTime.MinValue;
        private static long GetWorkingSetBytes()
        {
            try { return System.Diagnostics.Process.GetCurrentProcess().WorkingSet64; } catch { return 0; }
        }

        /// <summary>
        /// Get a frozen BitmapSource from base64 content, decoded at the specified width/height.
        /// Uses cache if available, otherwise decodes off the UI thread.
        /// </summary>
        public async Task<BitmapSource> GetFromBase64Async(string base64, int decodePixelWidth, int? decodePixelHeight = null, string cacheKeyOverride = null)
        {
            if (string.IsNullOrWhiteSpace(base64)) return null;

            var key = BuildKey(base64, decodePixelWidth, decodePixelHeight, cacheKeyOverride);

            if (_cache.TryGetValue(key, out var weak) && weak.TryGetTarget(out var existing) && existing != null)
            {
                // Adaptive cleanup if memory is high
                AdaptiveCleanupIfNeeded();
                return existing;
            }

            // Decode off the UI thread
            var image = await Task.Run(() => Decode(base64, decodePixelWidth, decodePixelHeight));
            if (image == null) return null;

            // Adaptive cleanup if memory is high
            // Log cache miss decode event for correlation
            System.Diagnostics.Debug.WriteLine($"[IMAGE-CACHE] Decode miss: key={key}, size={decodePixelWidth}x{(decodePixelHeight?.ToString() ?? "-")}");
            AdaptiveCleanupIfNeeded();
            _cache[key] = new WeakReference<BitmapSource>(image);

            // Opportunistic cleanup
            if (_cache.Count > MaxItems)
            {
                Cleanup();
            }

            return image;
        }

        private static string BuildKey(string base64, int w, int? h, string overrideKey)
        {
            if (!string.IsNullOrEmpty(overrideKey))
            {
                return $"{overrideKey}:{w}x{(h ?? 0)}";
            }
            // Use MD5 to avoid storing entire base64 string as key; cost is acceptable and done once per image value
            try
            {
                using var md5 = MD5.Create();
                var bytes = System.Text.Encoding.UTF8.GetBytes(base64);
                var hash = md5.ComputeHash(bytes);
                return $"{Convert.ToHexString(hash)}:{w}x{(h ?? 0)}";
            }
            catch
            {
                // Fallback to runtime hash code if MD5 fails for any reason
                return $"{base64.GetHashCode()}:{w}x{(h ?? 0)}";
            }
        }

        private static BitmapSource Decode(string base64, int decodePixelWidth, int? decodePixelHeight)
        {
            try
            {
                const int MaxDecodeDimension = 512; // clamp to keep decoded images lightweight
                int w = decodePixelWidth > 0 ? Math.Min(decodePixelWidth, MaxDecodeDimension) : 0;
                int? h = (decodePixelHeight.HasValue && decodePixelHeight.Value > 0)
                    ? Math.Min(decodePixelHeight.Value, MaxDecodeDimension)
                    : null;

                var bytes = Convert.FromBase64String(base64);
                using var ms = new MemoryStream(bytes);
                var bmp = new BitmapImage();
                bmp.BeginInit();
                bmp.CacheOption = BitmapCacheOption.OnLoad; // load fully so we can close stream
                if (w > 0) bmp.DecodePixelWidth = w;
                if (h.HasValue && h.Value > 0) bmp.DecodePixelHeight = h.Value;
                bmp.StreamSource = ms;
                bmp.CreateOptions = BitmapCreateOptions.IgnoreImageCache;
                bmp.EndInit();
                bmp.Freeze(); // allow cross-thread usage
                return bmp;
            }
            catch
            {
                return null;
            }
        }

        private void Cleanup()
        {
            // Standard cleanup of dead references
            lock (_cleanupLock)
            {
                foreach (var kvp in _cache)
                {
                    if (!kvp.Value.TryGetTarget(out _))
                    {
                        _cache.TryRemove(kvp.Key, out _);
                    }
                }
            }
        }

        private void AdaptiveCleanupIfNeeded()
        {
            var now = DateTime.UtcNow;
            if ((now - _lastAdaptiveCleanup).TotalSeconds < 10) return; // avoid thrash
            var ws = GetWorkingSetBytes();
            if (ws <= 0 || ws < WorkingSetThresholdBytes) return;

            int removedDead = 0;
            int before;
            lock (_cleanupLock)
            {
                before = _cache.Count;
                foreach (var kvp in _cache)
                {
                    if (!kvp.Value.TryGetTarget(out _))
                    {
                        if (_cache.TryRemove(kvp.Key, out _)) removedDead++;
                    }
                }
            }
            _lastAdaptiveCleanup = now;
            int after = _cache.Count;
            var wsMB = ws / (1024 * 1024);
            System.Diagnostics.Debug.WriteLine($"[IMAGE-CACHE] Adaptive cleanup triggered at WS={wsMB}MB; removedDead={removedDead}, remaining={after}");
        }




    }
}

