using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.UI
{
    /// <summary>
    /// ✅ CRITICAL UI OPTIMIZATION: Monitor and track UI rendering performance to identify bottlenecks and optimize rendering
    /// </summary>
    public class UIRenderingPerformanceMonitor : IDisposable
    {
        private readonly ILogger<UIRenderingPerformanceMonitor> _logger;
        private readonly ConcurrentDictionary<string, RenderingMetrics> _renderingMetrics;
        private readonly ConcurrentQueue<RenderingAlert> _renderingAlerts;
        private readonly Timer _reportingTimer;
        private readonly Timer _frameRateTimer;
        private readonly object _lockObject = new object();

        private long _frameCount;
        private DateTime _lastFrameTime;
        private double _currentFPS;
        private bool _disposed;

        // ✅ PERFORMANCE FIX: Control debug output frequency to reduce overhead
        private DateTime _lastDebugOutput = DateTime.MinValue;
        private const int DEBUG_OUTPUT_INTERVAL_SECONDS = 10;

        // Performance thresholds for POS system UI
        private const int SLOW_RENDERING_THRESHOLD_MS = 16; // 60 FPS = 16.67ms per frame
        private const int CRITICAL_RENDERING_THRESHOLD_MS = 33; // 30 FPS = 33.33ms per frame
        private const int MAX_METRICS_RETENTION = 500;
        private const int MAX_ALERTS_RETENTION = 50;

        public UIRenderingPerformanceMonitor(ILogger<UIRenderingPerformanceMonitor> logger = null)
        {
            _logger = logger;
            _renderingMetrics = new ConcurrentDictionary<string, RenderingMetrics>();
            _renderingAlerts = new ConcurrentQueue<RenderingAlert>();
            _lastFrameTime = DateTime.Now;

            // Report performance metrics every 30 seconds
            _reportingTimer = new Timer(ReportPerformanceMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            // Monitor frame rate every second
            _frameRateTimer = new Timer(UpdateFrameRate, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

            // Hook into WPF rendering events
            CompositionTarget.Rendering += OnCompositionTargetRendering;

            Debug.WriteLine("✅ [UI-RENDER-MONITOR] UI Rendering Performance Monitor initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Track UI operation performance
        /// </summary>
        public IDisposable TrackUIOperation(string operationName, string operationType = "UI")
        {
            return new UIOperationTracker(this, operationName, operationType);
        }

        /// <summary>
        /// ✅ CRITICAL: Track rendering performance for a specific UI element
        /// </summary>
        public void TrackElementRendering(FrameworkElement element, string elementName = null)
        {
            if (element == null || _disposed) return;

            elementName = elementName ?? element.GetType().Name;
            
            element.LayoutUpdated += (s, e) =>
            {
                if (_disposed) return;
                RecordRenderingEvent($"{elementName}_Layout", "Layout", 0, true);
            };

            element.SizeChanged += (s, e) =>
            {
                if (_disposed) return;
                RecordRenderingEvent($"{elementName}_SizeChanged", "SizeChange", 0, true);
            };
        }

        /// <summary>
        /// ✅ INTERNAL: Record rendering operation metrics
        /// </summary>
        internal void RecordRenderingEvent(string operationName, string operationType, long renderTimeMs, bool wasSuccessful)
        {
            try
            {
                var key = $"{operationType}:{operationName}";
                
                _renderingMetrics.AddOrUpdate(key, 
                    new RenderingMetrics
                    {
                        OperationName = operationName,
                        OperationType = operationType,
                        RenderCount = 1,
                        TotalRenderTimeMs = renderTimeMs,
                        MinRenderTimeMs = renderTimeMs,
                        MaxRenderTimeMs = renderTimeMs,
                        SuccessfulRenders = wasSuccessful ? 1 : 0,
                        LastRenderTime = DateTime.Now
                    },
                    (existingKey, existingMetrics) =>
                    {
                        existingMetrics.RenderCount++;
                        existingMetrics.TotalRenderTimeMs += renderTimeMs;
                        existingMetrics.MinRenderTimeMs = Math.Min(existingMetrics.MinRenderTimeMs, renderTimeMs);
                        existingMetrics.MaxRenderTimeMs = Math.Max(existingMetrics.MaxRenderTimeMs, renderTimeMs);
                        existingMetrics.LastRenderTime = DateTime.Now;
                        
                        if (wasSuccessful)
                        {
                            existingMetrics.SuccessfulRenders++;
                        }
                        
                        return existingMetrics;
                    });

                // Check for slow rendering alerts
                if (renderTimeMs >= SLOW_RENDERING_THRESHOLD_MS)
                {
                    var alertLevel = renderTimeMs >= CRITICAL_RENDERING_THRESHOLD_MS ? AlertLevel.Critical : AlertLevel.Warning;
                    
                    var alert = new RenderingAlert
                    {
                        OperationName = operationName,
                        OperationType = operationType,
                        RenderTimeMs = renderTimeMs,
                        Timestamp = DateTime.Now,
                        Level = alertLevel
                    };
                    
                    _renderingAlerts.Enqueue(alert);
                    
                    // Limit alert queue size
                    while (_renderingAlerts.Count > MAX_ALERTS_RETENTION)
                    {
                        _renderingAlerts.TryDequeue(out _);
                    }

                    // ✅ PERFORMANCE FIX: Throttle slow rendering alerts to reduce debug output overhead
                    var now = DateTime.Now;
                    if ((now - _lastDebugOutput).TotalSeconds >= DEBUG_OUTPUT_INTERVAL_SECONDS)
                    {
                        Debug.WriteLine($"🚨 [UI-RENDER-MONITOR] {alertLevel} slow rendering detected: {operationName} took {renderTimeMs}ms");
                        _lastDebugOutput = now;
                    }
                }

                // Limit metrics retention
                if (_renderingMetrics.Count > MAX_METRICS_RETENTION)
                {
                    CleanupOldMetrics();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-RENDER-MONITOR] Error recording rendering metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Handle WPF rendering events for frame rate monitoring
        /// </summary>
        private void OnCompositionTargetRendering(object sender, EventArgs e)
        {
            if (_disposed) return;

            try
            {
                Interlocked.Increment(ref _frameCount);

                // ✅ PERFORMANCE FIX: Record actual rendering activity
                RecordRenderingEvent("CompositionTarget", "Render", 0, true);
            }
            catch (Exception ex)
            {
                // Silently handle exceptions to prevent performance impact
                Debug.WriteLine($"[UI-RENDER-MONITOR] Rendering event error: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Update frame rate calculation
        /// </summary>
        private void UpdateFrameRate(object state)
        {
            try
            {
                var currentTime = DateTime.Now;
                var elapsed = (currentTime - _lastFrameTime).TotalSeconds;
                
                if (elapsed >= 1.0)
                {
                    var currentFrameCount = Interlocked.Exchange(ref _frameCount, 0);
                    _currentFPS = currentFrameCount / elapsed;
                    _lastFrameTime = currentTime;

                    // ✅ PERFORMANCE FIX: Throttle debug output to reduce overhead
                    if (_currentFPS < 30 && _currentFPS > 0)
                    {
                        var now = DateTime.Now;
                        if ((now - _lastDebugOutput).TotalSeconds >= DEBUG_OUTPUT_INTERVAL_SECONDS)
                        {
                            Debug.WriteLine($"⚠️ [UI-RENDER-MONITOR] Low frame rate detected: {_currentFPS:F1} FPS");
                            _lastDebugOutput = now;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-RENDER-MONITOR] Error updating frame rate: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ MONITORING: Get current rendering performance statistics
        /// </summary>
        public UIRenderingPerformanceStats GetPerformanceStats()
        {
            lock (_lockObject)
            {
                var metrics = _renderingMetrics.Values.ToList();
                var alerts = _renderingAlerts.ToList();

                return new UIRenderingPerformanceStats
                {
                    TotalRenderOperations = metrics.Sum(m => m.RenderCount),
                    UniqueOperationTypes = metrics.Count,
                    AverageRenderTimeMs = metrics.Any() ? metrics.Average(m => m.AverageRenderTimeMs) : 0,
                    CurrentFPS = _currentFPS,
                    SlowRenderingCount = alerts.Count(a => a.Level == AlertLevel.Warning),
                    CriticalRenderingCount = alerts.Count(a => a.Level == AlertLevel.Critical),
                    TopSlowOperations = metrics
                        .OrderByDescending(m => m.AverageRenderTimeMs)
                        .Take(10)
                        .ToList(),
                    RecentAlerts = alerts
                        .OrderByDescending(a => a.Timestamp)
                        .Take(20)
                        .ToList()
                };
            }
        }

        /// <summary>
        /// ✅ MONITORING: Get detailed rendering performance report
        /// </summary>
        public string GetDetailedRenderingReport()
        {
            var stats = GetPerformanceStats();
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== UI Rendering Performance Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("📊 Overall Statistics:");
            report.AppendLine($"  Total Render Operations: {stats.TotalRenderOperations:N0}");
            report.AppendLine($"  Unique Operation Types: {stats.UniqueOperationTypes}");
            report.AppendLine($"  Average Render Time: {stats.AverageRenderTimeMs:F1}ms");
            report.AppendLine($"  Current Frame Rate: {stats.CurrentFPS:F1} FPS");
            report.AppendLine($"  Slow Renders (>{SLOW_RENDERING_THRESHOLD_MS}ms): {stats.SlowRenderingCount}");
            report.AppendLine($"  Critical Renders (>{CRITICAL_RENDERING_THRESHOLD_MS}ms): {stats.CriticalRenderingCount}");
            report.AppendLine();

            if (stats.TopSlowOperations.Any())
            {
                report.AppendLine("🐌 Top 10 Slowest Rendering Operations:");
                foreach (var operation in stats.TopSlowOperations)
                {
                    var successRate = operation.RenderCount > 0 ? (operation.SuccessfulRenders * 100.0 / operation.RenderCount) : 0;
                    report.AppendLine($"  • {operation.OperationType}:{operation.OperationName}");
                    report.AppendLine($"    Avg: {operation.AverageRenderTimeMs:F1}ms | Count: {operation.RenderCount} | Success: {successRate:F1}%");
                    report.AppendLine($"    Range: {operation.MinRenderTimeMs}ms - {operation.MaxRenderTimeMs}ms");
                }
                report.AppendLine();
            }

            if (stats.RecentAlerts.Any())
            {
                report.AppendLine("🚨 Recent Rendering Alerts:");
                foreach (var alert in stats.RecentAlerts.Take(10))
                {
                    var icon = alert.Level == AlertLevel.Critical ? "🔴" : "🟠";
                    report.AppendLine($"  {icon} {alert.Timestamp:HH:mm:ss} - {alert.OperationType}:{alert.OperationName} ({alert.RenderTimeMs}ms)");
                }
            }

            return report.ToString();
        }

        /// <summary>
        /// ✅ INTERNAL: Report performance metrics periodically
        /// </summary>
        private void ReportPerformanceMetrics(object state)
        {
            try
            {
                var stats = GetPerformanceStats();
                
                Debug.WriteLine($"[UI-RENDER-MONITOR] Performance Summary: {stats.TotalRenderOperations} renders, avg {stats.AverageRenderTimeMs:F1}ms, {stats.CurrentFPS:F1} FPS");
                
                if (stats.SlowRenderingCount > 0 || stats.CriticalRenderingCount > 0)
                {
                    Debug.WriteLine($"[UI-RENDER-MONITOR] ⚠️ Rendering Issues: {stats.SlowRenderingCount} slow, {stats.CriticalRenderingCount} critical renders");
                }

                // Log to application logger if available
                _logger?.LogInformation("UI Rendering Performance: {TotalRenders} renders, avg {AvgTime}ms, {FPS} FPS, {SlowCount} slow renders", 
                    stats.TotalRenderOperations, stats.AverageRenderTimeMs, stats.CurrentFPS, stats.SlowRenderingCount);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-RENDER-MONITOR] Error reporting metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Clean up old metrics to prevent memory growth
        /// </summary>
        private void CleanupOldMetrics()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddHours(-2);
                var keysToRemove = _renderingMetrics
                    .Where(kvp => kvp.Value.LastRenderTime < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _renderingMetrics.TryRemove(key, out _);
                }

                if (keysToRemove.Count > 0)
                {
                    Debug.WriteLine($"[UI-RENDER-MONITOR] Cleaned up {keysToRemove.Count} old rendering metrics");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-RENDER-MONITOR] Error cleaning up metrics: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                CompositionTarget.Rendering -= OnCompositionTargetRendering;
                
                _reportingTimer?.Dispose();
                _frameRateTimer?.Dispose();
                
                // Final performance report
                var finalReport = GetDetailedRenderingReport();
                Debug.WriteLine(finalReport);

                Debug.WriteLine("✅ [UI-RENDER-MONITOR] UI Rendering Performance Monitor disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [UI-RENDER-MONITOR] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// UI operation tracker - implements IDisposable for using statement
    /// </summary>
    internal class UIOperationTracker : IDisposable
    {
        private readonly UIRenderingPerformanceMonitor _monitor;
        private readonly string _operationName;
        private readonly string _operationType;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;

        public UIOperationTracker(UIRenderingPerformanceMonitor monitor, string operationName, string operationType)
        {
            _monitor = monitor;
            _operationName = operationName;
            _operationType = operationType;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            if (_disposed) return;

            _stopwatch.Stop();
            _monitor.RecordRenderingEvent(_operationName, _operationType, _stopwatch.ElapsedMilliseconds, true);
            _disposed = true;
        }
    }

    /// <summary>
    /// Data structures for rendering performance monitoring
    /// </summary>
    public class RenderingMetrics
    {
        public string OperationName { get; set; }
        public string OperationType { get; set; }
        public long RenderCount { get; set; }
        public long TotalRenderTimeMs { get; set; }
        public long MinRenderTimeMs { get; set; }
        public long MaxRenderTimeMs { get; set; }
        public long SuccessfulRenders { get; set; }
        public DateTime LastRenderTime { get; set; }
        
        public double AverageRenderTimeMs => RenderCount > 0 ? (double)TotalRenderTimeMs / RenderCount : 0;
    }

    public class RenderingAlert
    {
        public string OperationName { get; set; }
        public string OperationType { get; set; }
        public long RenderTimeMs { get; set; }
        public DateTime Timestamp { get; set; }
        public AlertLevel Level { get; set; }
    }

    public class UIRenderingPerformanceStats
    {
        public long TotalRenderOperations { get; set; }
        public int UniqueOperationTypes { get; set; }
        public double AverageRenderTimeMs { get; set; }
        public double CurrentFPS { get; set; }
        public int SlowRenderingCount { get; set; }
        public int CriticalRenderingCount { get; set; }
        public List<RenderingMetrics> TopSlowOperations { get; set; } = new List<RenderingMetrics>();
        public List<RenderingAlert> RecentAlerts { get; set; } = new List<RenderingAlert>();
    }

    public enum AlertLevel
    {
        Warning,
        Critical
    }
}
