<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.BarcodePrintingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource PrintBarcodes}" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterScreen"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="{DynamicResource PrintBarcodes}"
                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                 Margin="0,0,0,16"/>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Product List -->
            <Border Style="{StaticResource ContentCardStyle}"
                    Margin="0,0,8,0">
                <DockPanel>
                    <TextBox x:Name="txtSearch"
                            DockPanel.Dock="Top"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            md:HintAssist.Hint="{DynamicResource BarcodePrintingSearchHint}"
                            Margin="0,0,0,8"
                            TextChanged="TxtSearch_TextChanged"/>
                            
                    <StackPanel DockPanel.Dock="Top" Orientation="Horizontal" Margin="0,0,0,8">
                        <Button x:Name="btnSelectAll" 
                                Content="{DynamicResource SelectAll}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,8,0"
                                Click="BtnSelectAll_Click"/>
                        <Button x:Name="btnDeselectAll" 
                                Content="{DynamicResource DeselectAll}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="BtnDeselectAll_Click"/>
                    </StackPanel>

                    <ListView x:Name="ProductsList"
                            SelectionMode="Extended">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="" Width="30">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox IsChecked="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                      Click="BarcodeCheckbox_Click"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="{DynamicResource ProductName}" DisplayMemberBinding="{Binding Name}"/>
                                <GridViewColumn Header="{DynamicResource PrimaryBarcode}">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Barcodes, Converter={StaticResource PrimaryBarcodeConverter}}"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </DockPanel>
            </Border>

            <!-- Print Settings -->
            <Border Grid.Column="1"
                    Style="{StaticResource ContentCardStyle}"
                    Margin="8,0,0,0">
                <StackPanel>
                    <TextBlock Text="{DynamicResource PrintSettings}"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,0,0,16"/>

                    <ComboBox x:Name="cmbBarcodeType"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            md:HintAssist.Hint="{DynamicResource BarcodeType}"
                            Margin="0,0,0,8">
                        <ComboBoxItem Content="EAN-13"/>
                        <ComboBoxItem Content="Code 128"/>
                        <ComboBoxItem Content="QR Code"/>
                    </ComboBox>

                    <ComboBox x:Name="cmbLabelSize"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            md:HintAssist.Hint="{DynamicResource LabelSize}"
                            Margin="0,0,0,8">
                        <ComboBoxItem Content="{DynamicResource LabelSizeSmall}"/>
                        <ComboBoxItem Content="{DynamicResource LabelSizeMedium}"/>
                        <ComboBoxItem Content="{DynamicResource LabelSizeLarge}"/>
                    </ComboBox>

                    <TextBox x:Name="txtCopies"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           md:HintAssist.Hint="{DynamicResource CopiesPerBarcode}"
                           Text="1"
                           Margin="0,0,0,8"/>

                    <ComboBox x:Name="cmbBarcodesPerPage"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            md:HintAssist.Hint="{DynamicResource BarcodesPerPage}"
                            SelectedIndex="0"
                            Margin="0,0,0,8">
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="2"/>
                        <ComboBoxItem Content="4"/>
                        <ComboBoxItem Content="6"/>
                        <ComboBoxItem Content="8"/>
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="12"/>
                    </ComboBox>

                    <CheckBox x:Name="chkIncludePrice"
                            Content="{DynamicResource IncludePrice}"
                            Style="{StaticResource MaterialDesignCheckBox}"
                            Margin="0,8"/>

                    <CheckBox x:Name="chkIncludeName"
                            Content="{DynamicResource IncludeProductName}"
                            Style="{StaticResource MaterialDesignCheckBox}"
                            Margin="0,8"/>

                    <!-- Preview Area -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                            BorderThickness="1"
                            Margin="0,16"
                            Height="150">
                        <Image x:Name="PreviewImage"
                               Stretch="Uniform"
                               Margin="8"/>
                    </Border>

                    <Button x:Name="btnPreview"
                            Content="{DynamicResource Preview}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="BtnPreview_Click"
                            Margin="0,8"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Footer -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,16,0,0">
            <Button x:Name="btnCancel"
                    Content="{DynamicResource Cancel}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="BtnCancel_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="btnPrint"
                    Content="{DynamicResource Print}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="BtnPrint_Click"/>
        </StackPanel>
    </Grid>
</Window> 