using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class MultiBooleanToVisibilityConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // Check if all values are boolean and true
            bool visible = values.All(v => v is bool && (bool)v);
            
            return visible ? Visibility.Visible : Visibility.Collapsed;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 