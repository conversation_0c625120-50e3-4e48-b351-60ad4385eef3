using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Models
{
    public class Notification
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public NotificationType Type { get; set; }
        public ICommand ActionCommand { get; set; }
        public string IconKind { get; set; }
        public Brush IconBackground { get; set; }
        public string TimeAgo => GetTimeAgo();

        // Additional properties for product-specific notifications
        public int? ProductId { get; set; }
        public string AlertType { get; set; }
        public ProductAlert SourceAlert { get; set; }

        public string GetTimeAgo()
        {
            var span = DateTime.Now - CreatedAt;
            
            // Get resource strings for time ago messages
            var justNowString = (string)Application.Current.TryFindResource("TimeAgo_JustNow") ?? "Just now";
            var minutesAgoFormat = (string)Application.Current.TryFindResource("TimeAgo_MinutesAgo") ?? "{0} minutes ago";
            var hoursAgoFormat = (string)Application.Current.TryFindResource("TimeAgo_HoursAgo") ?? "{0} hours ago";
            var daysAgoFormat = (string)Application.Current.TryFindResource("TimeAgo_DaysAgo") ?? "{0} days ago";
            
            if (span.TotalDays >= 1)
            {
                return string.Format(daysAgoFormat, (int)span.TotalDays);
            }
            if (span.TotalHours >= 1)
            {
                return string.Format(hoursAgoFormat, (int)span.TotalHours);
            }
            if (span.TotalMinutes >= 1)
            {
                return string.Format(minutesAgoFormat, (int)span.TotalMinutes);
            }
            
            return justNowString;
        }
    }

    public enum NotificationType
    {
        Alert,
        Warning,
        Info,
        Success
    }
} 