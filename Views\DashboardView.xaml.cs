using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using LiveCharts.Wpf.Charts.Base;
using POSSystem.ViewModels;
using POSSystem.ViewModels.Dashboard;
using System.Windows.Threading;
using System.Windows.Media;
using System.Threading.Tasks;
using System.Collections.Generic;
using POSSystem.Services;
using POSSystem.Views.Dialogs;
using MaterialDesignThemes.Wpf;
using System.Linq;
using POSSystem.Services.Interfaces;

namespace POSSystem.Views
{
    public partial class DashboardView : UserControl
    {
        private readonly RefactoredDashboardViewModel _viewModel;
        private DispatcherTimer _refreshTimer;

        public DashboardView()
        {
            InitializeComponent();

            // ✅ Use proper DI for dashboard services
            try
            {
                _viewModel = (RefactoredDashboardViewModel)App.ServiceProvider?.GetService(typeof(RefactoredDashboardViewModel));

                if (_viewModel == null)
                {
                    // Fallback: create with proper DI services
                    var dbService = (DatabaseService)App.ServiceProvider?.GetService(typeof(IDatabaseService)) ??
                        throw new InvalidOperationException("DatabaseService not available from DI");
                    var dataProvider = new DatabaseServiceAdapter(dbService);
                    _viewModel = new RefactoredDashboardViewModel(dataProvider);
                }

                DataContext = _viewModel;
                System.Diagnostics.Debug.WriteLine("[DASHBOARD_VIEW] Successfully initialized with DI services");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DASHBOARD_VIEW] Error initializing with DI: {ex.Message}");
                throw;
            }

            SetupRefreshTimer();
            Loaded += DashboardView_Loaded;
            DataContextChanged += DashboardView_DataContextChanged;
        }

        private async void DashboardView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // ✅ PERFORMANCE FIX: Load dashboard data on background thread to prevent UI blocking
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Load initial data on background thread
                        await _viewModel.LoadDashboardDataAsync();

                        // Load all sections since they're all visible
                        await Task.WhenAll(
                            _viewModel.LoadSectionAsync("Overview"),
                            _viewModel.LoadSectionAsync("ProductPerformance"),
                            _viewModel.LoadSectionAsync("CustomerInsights")
                        );

                        // Start the refresh timer after initial load (on UI thread)
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            _refreshTimer.Start();
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in background dashboard loading: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DashboardView_Loaded: {ex.Message}");
            }
        }

        private void SetupRefreshTimer()
        {
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5) // Refresh every 5 minutes
            };
            
            // Replace full refresh with selective refresh
            _refreshTimer.Tick += async (s, e) => 
            {
                await RefreshCriticalDataOnly();
            };
        }
        
        private async Task RefreshCriticalDataOnly()
        {
            try
            {
                // Skip refresh if a dialog is open to avoid potential conflicts
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialog is open, skipping dashboard refresh");
                    return;
                }

                // Refresh all dashboard data since everything is visible
                if (_viewModel != null)
                {
                    await Task.WhenAll(
                        _viewModel.RefreshAlertsAsync(),
                        _viewModel.RefreshQuickStatsAsync(),
                        _viewModel.RefreshSalesTrendAsync()
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during dashboard refresh: {ex.Message}");
            }
        }
        
        private async Task LoadDashboardData()
        {
            try
            {
                // ✅ PERFORMANCE FIX: Load dashboard data on background thread
                await Task.Run(async () =>
                {
                    // Load initial dashboard data
                    await _viewModel.LoadDashboardDataAsync();

                    // Force an immediate refresh of quick stats
                    await _viewModel.RefreshQuickStatsAsync();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }

        private async Task LoadAllSections()
        {
            if (_viewModel != null)
            {
                // Load all sections since they're all visible
                await Task.WhenAll(
                    _viewModel.LoadSectionAsync("Overview"),
                    _viewModel.LoadSectionAsync("ProductPerformance"),
                    _viewModel.LoadSectionAsync("CustomerInsights")
                );
            }
        }

        private void OnRefreshClicked(object sender, System.Windows.RoutedEventArgs e)
        {
            // ✅ PERFORMANCE FIX: Run refresh on background thread to prevent UI blocking
            _ = LoadDashboardData();
        }



        private void DashboardView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is RefactoredDashboardViewModel oldViewModel)
            {
                oldViewModel.ScrollToSectionRequested -= ViewModel_ScrollToSectionRequested;
            }

            if (e.NewValue is RefactoredDashboardViewModel newViewModel)
            {
                newViewModel.ScrollToSectionRequested += ViewModel_ScrollToSectionRequested;
            }
        }

        private void ViewModel_ScrollToSectionRequested(object sender, string sectionName)
        {
            // We don't need scrolling anymore, but we can still highlight the section
            // or switch to appropriate tab if needed
            switch (sectionName)
            {
                case "Overview":
                    OverviewSection.Focus();
                    break;
                case "Product Performance":
                    ProductPerformanceSection.Focus();
                    break;

            }
        }

        private void TabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!(sender is TabControl tabControl) || !(DataContext is RefactoredDashboardViewModel viewModel))
                return;

            // Get the selected tab
            if (tabControl.SelectedItem is TabItem selectedTab && selectedTab.Tag != null)
            {
                string tabKey = selectedTab.Tag.ToString();
                string tabGroup = tabControl.Tag?.ToString() ?? "Unknown";
                
                // Since RefactoredDashboardViewModel might not have this method,
                // we'll just log the tab change for now
                System.Diagnostics.Debug.WriteLine($"Changed active tab to {tabGroup}_{tabKey}");
            }
        }
        
        private async void OnCategoryDetailsClicked(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if a dialog is already open to prevent conflicts
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialog already open, ignoring category details click");
                    return;
                }

                // Create the category details dialog
                var dialog = new Dialogs.CategoryDetailsDialog(_viewModel);

                // Show the dialog using the DialogHost with identifier "RootDialog"
                await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, "RootDialog");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("No loaded DialogHost"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost error: {ex.Message}");
                MessageBox.Show(
                    "Could not find the DialogHost with identifier 'RootDialog'.",
                    "Dialog Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected in category details: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing category details dialog: {ex.Message}");
                MessageBox.Show(
                    $"Error showing category details: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async void StatsCard_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is MaterialDesignThemes.Wpf.Card card && card.Tag is string statType)
                {
                    // Check if a dialog is already open to prevent conflicts
                    if (DialogHost.IsDialogOpen("RootDialog"))
                    {
                        System.Diagnostics.Debug.WriteLine($"Dialog already open, ignoring {statType} card click");
                        return;
                    }

                    switch (statType)
                    {
                        case "Sales":
                            var salesDialog = new StatsDetailsDialog(_viewModel, "Sales");
                            await DialogHost.Show(salesDialog, "RootDialog");
                            break;
                        case "UnpaidSales":
                            var unpaidSalesDialog = new UnpaidSalesStatsDetailsDialog(_viewModel);
                            await DialogHost.Show(unpaidSalesDialog, "RootDialog");
                            break;
                        case "Expiry":
                            var expiryDialog = new ExpiryStatsDetailsDialog(_viewModel);
                            await DialogHost.Show(expiryDialog, "RootDialog");
                            break;
                        case "Profit":
                            var profitDialog = new ProfitStatsDetailsDialog(_viewModel, "Profit");
                            await DialogHost.Show(profitDialog, "RootDialog");
                            break;
                        case "Expenses":
                            var expensesDialog = new ExpensesStatsDetailsDialog(_viewModel);
                            await DialogHost.Show(expensesDialog, "RootDialog");
                            break;
                        // Add other cases as needed
                    }
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected for {sender}: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
                // The user can try clicking again if needed
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing stats dialog: {ex.Message}");
                MessageBox.Show(
                    $"Error showing statistics: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
    }
} 