using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace POSSystem.Migrations
{
    public partial class AddDiscountTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create DiscountTypes table for predefined discount types
            migrationBuilder.CreateTable(
                name: "DiscountTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DiscountTypes", x => x.Id);
                });

            // Create DiscountReasons table for predefined reason codes
            migrationBuilder.CreateTable(
                name: "DiscountReasons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Code = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DiscountReasons", x => x.Id);
                });

            // Create DiscountPermissions table
            migrationBuilder.CreateTable(
                name: "DiscountPermissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false),
                    DiscountTypeId = table.Column<int>(type: "INTEGER", nullable: false),
                    MaxPercentage = table.Column<decimal>(type: "DECIMAL(5,2)", nullable: true),
                    MaxFixedAmount = table.Column<decimal>(type: "DECIMAL(10,2)", nullable: true),
                    MinPricePercentage = table.Column<decimal>(type: "DECIMAL(5,2)", nullable: true),
                    RequiresApproval = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false),
                    ApprovalThreshold = table.Column<decimal>(type: "DECIMAL(10,2)", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DiscountPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DiscountPermissions_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DiscountPermissions_DiscountTypes_DiscountTypeId",
                        column: x => x.DiscountTypeId,
                        principalTable: "DiscountTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create Discounts table
            migrationBuilder.CreateTable(
                name: "Discounts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    SaleId = table.Column<int>(type: "INTEGER", nullable: true),
                    SaleItemId = table.Column<int>(type: "INTEGER", nullable: true),
                    DiscountTypeId = table.Column<int>(type: "INTEGER", nullable: false),
                    DiscountValue = table.Column<decimal>(type: "DECIMAL(10,2)", nullable: false),
                    OriginalPrice = table.Column<decimal>(type: "DECIMAL(10,2)", nullable: false),
                    FinalPrice = table.Column<decimal>(type: "DECIMAL(10,2)", nullable: false),
                    ReasonId = table.Column<int>(type: "INTEGER", nullable: false),
                    Comment = table.Column<string>(type: "TEXT", nullable: true),
                    AppliedByUserId = table.Column<int>(type: "INTEGER", nullable: false),
                    ApprovedByUserId = table.Column<int>(type: "INTEGER", nullable: true),
                    AppliedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ApprovedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Discounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Discounts_Sales_SaleId",
                        column: x => x.SaleId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Discounts_SaleItems_SaleItemId",
                        column: x => x.SaleItemId,
                        principalTable: "SaleItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Discounts_Users_AppliedByUserId",
                        column: x => x.AppliedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Discounts_Users_ApprovedByUserId",
                        column: x => x.ApprovedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Discounts_DiscountTypes_DiscountTypeId",
                        column: x => x.DiscountTypeId,
                        principalTable: "DiscountTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Discounts_DiscountReasons_ReasonId",
                        column: x => x.ReasonId,
                        principalTable: "DiscountReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create indexes
            migrationBuilder.CreateIndex(
                name: "IX_DiscountPermissions_RoleId_DiscountTypeId",
                table: "DiscountPermissions",
                columns: new[] { "RoleId", "DiscountTypeId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Discounts_SaleId",
                table: "Discounts",
                column: "SaleId");

            migrationBuilder.CreateIndex(
                name: "IX_Discounts_SaleItemId",
                table: "Discounts",
                column: "SaleItemId");

            migrationBuilder.CreateIndex(
                name: "IX_Discounts_AppliedByUserId",
                table: "Discounts",
                column: "AppliedByUserId");

            // Insert default discount types
            migrationBuilder.InsertData(
                table: "DiscountTypes",
                columns: new[] { "Id", "Name", "Description" },
                values: new object[,]
                {
                    { 1, "Percentage", "Percentage off the original price" },
                    { 2, "Fixed Amount", "Fixed amount off the original price" },
                    { 3, "Price Override", "Override with a specific price" }
                });

            // Insert default discount reasons
            migrationBuilder.InsertData(
                table: "DiscountReasons",
                columns: new[] { "Id", "Code", "Description", "IsActive" },
                values: new object[,]
                {
                    { 1, "MANAGER", "Manager Special", true },
                    { 2, "DAMAGED", "Damaged Item", true },
                    { 3, "PRICEMATCH", "Price Match", true },
                    { 4, "CUSTOMER", "Customer Satisfaction", true },
                    { 5, "PROMO", "Promotion", true },
                    { 6, "BULK", "Bulk Purchase", true },
                    { 7, "LOYALTY", "Loyalty Discount", true }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(name: "Discounts");
            migrationBuilder.DropTable(name: "DiscountPermissions");
            migrationBuilder.DropTable(name: "DiscountReasons");
            migrationBuilder.DropTable(name: "DiscountTypes");
        }
    }
} 