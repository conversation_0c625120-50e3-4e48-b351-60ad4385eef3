# Receipt Printing System Documentation

## Overview

The Enhanced Receipt Printing System provides comprehensive receipt printing functionality for the POS system, including support for multiple printer types, configurable templates, PDF export, print preview, and robust error handling.

## Features

### Core Features
- **Automatic Receipt Printing**: Print receipts automatically after successful sale completion
- **Manual Receipt Reprinting**: Reprint receipts for past transactions from sales history
- **Print Preview**: Preview receipts before printing with zoom and navigation controls
- **PDF Export**: Save receipts as PDF/XPS files for backup and archival
- **Multiple Printer Support**: Support for thermal printers, standard printers, and PDF export
- **Configurable Templates**: Customizable receipt templates with business information
- **Error Handling**: Robust error handling with retry mechanisms and user-friendly messages

### Advanced Features
- **Print Job Tracking**: Track all print jobs with status, timestamps, and error logging
- **Template Management**: Multiple receipt templates with different configurations
- **Printer Configuration**: Configure multiple printers with different settings
- **Settings UI**: Comprehensive settings interface for all printing options
- **Test Printing**: Test print functionality with sample data

## Architecture

### Core Components

#### 1. Models (`Models/Printing/`)
- **ReceiptTemplate**: Defines receipt layout and content options
- **PrinterConfiguration**: Stores printer settings and connection details
- **ReceiptPrintSettings**: Global printing preferences and defaults
- **ReceiptPrintJob**: Tracks individual print jobs and their status

#### 2. Services (`Services/Printing/`)
- **EnhancedReceiptPrintService**: Main printing service with full functionality
- **ReceiptPdfExportService**: Handles PDF/XPS export functionality
- **IEnhancedReceiptPrintService**: Interface for the printing service

#### 3. UI Components
- **PaymentProcessingView**: Includes receipt printing options during payment
- **SalesHistoryView**: Provides reprint functionality for past sales
- **ReceiptPreviewWindow**: Print preview dialog with zoom and export options
- **SettingsView**: Receipt printing configuration interface

## Usage

### Basic Usage

#### 1. Automatic Printing During Sales
```csharp
// In PaymentProcessingViewModel
if (PrintReceiptEnabled)
{
    await HandleReceiptPrintingAsync();
}
```

#### 2. Manual Reprinting
```csharp
// From sales history
var success = await _receiptPrintService.PrintReceiptAsync(sale, showDialog: true);
```

#### 3. Print Preview
```csharp
// Preview before printing
await _receiptPrintService.PreviewReceiptAsync(sale);
```

#### 4. PDF Export
```csharp
// Save as PDF
await _receiptPrintService.SaveReceiptAsPdfAsync(sale, filePath);
```

### Configuration

#### Receipt Templates
- **Standard Template**: Full-featured template for standard printers
- **Thermal Template**: Optimized for thermal printers (80mm/58mm)
- **Custom Templates**: Create custom templates with specific layouts

#### Printer Types
- **Standard**: Regular desktop/office printers (A4, Letter)
- **Thermal**: Point-of-sale thermal printers (80mm, 58mm)
- **PDF**: Export to PDF/XPS files

#### Settings Options
- Auto-print after sale completion
- Show print dialog before printing
- Save PDF backup copies
- Enable print preview
- Configure retry attempts and timeouts

## Database Schema

### Tables Created
1. **ReceiptTemplates**: Template definitions and settings
2. **PrinterConfigurations**: Printer setup and connection details
3. **ReceiptPrintSettings**: Global printing preferences
4. **ReceiptPrintJobs**: Print job history and tracking

### Migration
Run the SQL migration script: `Migrations/AddReceiptPrintingTables.sql`

## Integration Points

### 1. Payment Processing
- Added receipt printing options to `PaymentProcessingView`
- Integrated with `PaymentProcessingViewModel.ProcessPayment()`
- Options for immediate printing, print dialog, and PDF backup

### 2. Sales History
- Added "Reprint Receipt" button to sales history grid
- Context menu with print, preview, and PDF export options
- Integration with `SalesHistoryViewModel`

### 3. Settings Management
- New "Receipt Printing" section in settings
- Template configuration and printer setup
- Test printing functionality

## Error Handling

### Print Failures
- Graceful handling of printer connectivity issues
- User-friendly error messages
- Automatic retry mechanisms with configurable attempts
- Fallback to print dialog if default printer fails

### Data Validation
- Template validation for required fields
- Printer configuration validation
- File path validation for PDF export
- Sale data validation before printing

## Testing

### Test Coverage
- Unit tests for core printing functionality
- Template and printer configuration validation
- PDF export service testing
- Integration testing with sales workflow

### Manual Testing
- Test print functionality in settings
- Preview test receipts
- Save test PDF files
- Verify different printer types and paper sizes

## Performance Considerations

### Optimization Features
- Asynchronous printing operations
- Background PDF generation
- Efficient document creation
- Minimal UI blocking during print operations

### Resource Management
- Proper disposal of print documents
- Memory-efficient template rendering
- Cleanup of temporary files
- Connection pooling for printer resources

## Troubleshooting

### Common Issues

#### 1. Printer Not Found
- Check printer installation and drivers
- Verify printer name in configuration
- Test with Windows print dialog

#### 2. Print Quality Issues
- Adjust template font size and layout
- Check printer DPI settings
- Verify paper size configuration

#### 3. PDF Export Failures
- Check file permissions in export directory
- Verify disk space availability
- Ensure valid file path characters

#### 4. Template Rendering Issues
- Validate template configuration
- Check for missing company information
- Verify sale data completeness

## Future Enhancements

### Planned Features
- Barcode/QR code generation on receipts
- Email receipt functionality
- Cloud backup integration
- Advanced template designer
- Multi-language receipt support
- Custom paper size support

### API Extensions
- REST API for external printing
- Webhook notifications for print jobs
- Batch printing capabilities
- Print queue management

## Support

### Logging
- Debug logging to console and files
- Print job tracking in database
- Error logging with stack traces
- Performance monitoring

### Diagnostics
- Test print functionality
- Printer connectivity checks
- Template validation tools
- Configuration verification

For technical support or feature requests, please refer to the main POS system documentation or contact the development team.
