-- Drop existing tables if they exist
DROP TABLE IF EXISTS "PurchaseOrderItems";
DROP TABLE IF EXISTS "PurchaseOrders";

-- Create PurchaseOrders table
CREATE TABLE "PurchaseOrders" (
    "Id" INTEGER NOT NULL,
    "OrderNumber" TEXT NOT NULL,
    "OrderDate" TEXT NOT NULL,
    "DueDate" TEXT NOT NULL,
    "SupplierId" INTEGER NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'Pending',
    "PaymentMethod" TEXT,
    "PaymentReference" TEXT COLLATE RTRIM,
    "PaymentDate" TEXT,
    "CreatedAt" TEXT NOT NULL,
    "CreatedByUserId" INTEGER,
    "UpdatedAt" TEXT,
    "Notes" TEXT,
    "Subtotal" decimal(18, 2) NOT NULL DEFAULT '0.0',
    "TaxAmount" decimal(18, 2) NOT NULL DEFAULT '0.0',
    "GrandTotal" decimal(18, 2) NOT NULL DEFAULT '0.0',
    CONSTRAINT "PK_PurchaseOrders" PRIMARY KEY("Id" AUTOINCREMENT),
    CONSTRAINT "FK_PurchaseOrders_Suppliers_SupplierId" FOREIGN KEY("SupplierId") 
        REFERENCES "Suppliers"("Id") ON DELETE RESTRICT
);

-- Create PurchaseOrderItems table
CREATE TABLE "PurchaseOrderItems" (
    "Id" INTEGER NOT NULL,
    "PurchaseOrderId" INTEGER NOT NULL,
    "ProductId" INTEGER NOT NULL,
    "Quantity" INTEGER NOT NULL,
    "UnitCost" decimal(18, 2) NOT NULL,
    "Notes" TEXT,
    CONSTRAINT "PK_PurchaseOrderItems" PRIMARY KEY("Id" AUTOINCREMENT),
    CONSTRAINT "FK_PurchaseOrderItems_Products_ProductId" FOREIGN KEY("ProductId") 
        REFERENCES "Products"("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PurchaseOrderItems_PurchaseOrders_PurchaseOrderId" FOREIGN KEY("PurchaseOrderId") 
        REFERENCES "PurchaseOrders"("Id") ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX "IX_PurchaseOrders_SupplierId" ON "PurchaseOrders"("SupplierId");
CREATE INDEX "IX_PurchaseOrderItems_ProductId" ON "PurchaseOrderItems"("ProductId");
CREATE INDEX "IX_PurchaseOrderItems_PurchaseOrderId" ON "PurchaseOrderItems"("PurchaseOrderId"); 