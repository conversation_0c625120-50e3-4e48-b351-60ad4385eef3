using System;
using System.Threading.Tasks;
using POSSystem.Helpers;
using POSSystem.Services;
using POSSystem.ViewModels;
using POSSystem.Models;

namespace POSSystem.Tests
{
    /// <summary>
    /// Integration tests for Phase 2 UI components and workflows
    /// </summary>
    public class Phase2IntegrationTests
    {
        /// <summary>
        /// Test service locator initialization and basic service availability
        /// </summary>
        public static async Task<bool> TestServiceLocatorInitializationAsync()
        {
            try
            {
                Console.WriteLine("🔧 Testing Service Locator Initialization...");

                // Reset service locator to ensure clean test
                ServiceLocator.Reset();

                // Initialize POS services
                ServiceLocator.InitializePOSServices();

                // Test service availability
                var dbService = ServiceLocator.Current.GetInstance<DatabaseService>();
                var authService = ServiceLocator.Current.GetInstance<AuthenticationService>();
                var permissionsService = ServiceLocator.Current.GetInstance<UserPermissionsService>();
                var notificationService = ServiceLocator.Current.GetInstance<DraftInvoiceNotificationService>();
                var draftInvoiceService = ServiceLocator.Current.GetInstance<DraftInvoiceService>();

                Console.WriteLine("  ✅ DatabaseService: " + (dbService != null ? "Available" : "Missing"));
                Console.WriteLine("  ✅ AuthenticationService: " + (authService != null ? "Available" : "Missing"));
                Console.WriteLine("  ✅ UserPermissionsService: " + (permissionsService != null ? "Available" : "Missing"));
                Console.WriteLine("  ✅ DraftInvoiceNotificationService: " + (notificationService != null ? "Available" : "Missing"));
                Console.WriteLine("  ✅ DraftInvoiceService: " + (draftInvoiceService != null ? "Available" : "Missing"));

                var allServicesAvailable = dbService != null && authService != null && 
                                         permissionsService != null && notificationService != null && 
                                         draftInvoiceService != null;

                Console.WriteLine($"Service Locator Test: {(allServicesAvailable ? "PASSED" : "FAILED")}");
                return allServicesAvailable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Service Locator Test FAILED: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test ViewModel creation and basic functionality
        /// </summary>
        public static async Task<bool> TestViewModelCreationAsync()
        {
            try
            {
                Console.WriteLine("🎨 Testing ViewModel Creation...");

                // Ensure services are initialized
                ServiceLocator.InitializePOSServices();

                var dbService = ServiceLocator.Current.GetInstance<DatabaseService>();
                var authService = ServiceLocator.Current.GetInstance<AuthenticationService>();
                var permissionsService = ServiceLocator.Current.GetInstance<UserPermissionsService>();
                var notificationService = ServiceLocator.Current.GetInstance<DraftInvoiceNotificationService>();
                var draftInvoiceService = ServiceLocator.Current.GetInstance<DraftInvoiceService>();

                // Test DraftInvoiceViewModel creation
                var draftViewModel = new DraftInvoiceViewModel(draftInvoiceService, permissionsService, authService, dbService);
                Console.WriteLine("  ✅ DraftInvoiceViewModel: " + (draftViewModel != null ? "Created" : "Failed"));

                // Test ProductToInvoiceConfirmationViewModel creation
                var testProduct = new Product { Id = 1, Name = "Test Product", SellingPrice = 10.00m };
                var confirmationViewModel = new ProductToInvoiceConfirmationViewModel(testProduct, permissionsService, dbService);
                Console.WriteLine("  ✅ ProductToInvoiceConfirmationViewModel: " + (confirmationViewModel != null ? "Created" : "Failed"));

                // Test PendingDraftsViewModel creation
                var pendingDraftsViewModel = new PendingDraftsViewModel(draftInvoiceService, notificationService, authService, permissionsService);
                Console.WriteLine("  ✅ PendingDraftsViewModel: " + (pendingDraftsViewModel != null ? "Created" : "Failed"));

                var allViewModelsCreated = draftViewModel != null && confirmationViewModel != null && pendingDraftsViewModel != null;

                Console.WriteLine($"ViewModel Creation Test: {(allViewModelsCreated ? "PASSED" : "FAILED")}");
                return allViewModelsCreated;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ViewModel Creation Test FAILED: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test command functionality
        /// </summary>
        public static async Task<bool> TestCommandFunctionalityAsync()
        {
            try
            {
                Console.WriteLine("⚡ Testing Command Functionality...");

                // Test RelayCommand
                bool commandExecuted = false;
                var relayCommand = new RelayCommand(() => commandExecuted = true);
                
                Console.WriteLine("  ✅ RelayCommand created: " + (relayCommand != null));
                Console.WriteLine("  ✅ RelayCommand can execute: " + relayCommand.CanExecute(null));
                
                relayCommand.Execute(null);
                Console.WriteLine("  ✅ RelayCommand executed: " + commandExecuted);

                // Test AsyncRelayCommand
                bool asyncCommandExecuted = false;
                var asyncCommand = new AsyncRelayCommand(async () => 
                {
                    await Task.Delay(10);
                    asyncCommandExecuted = true;
                });

                Console.WriteLine("  ✅ AsyncRelayCommand created: " + (asyncCommand != null));
                Console.WriteLine("  ✅ AsyncRelayCommand can execute: " + asyncCommand.CanExecute(null));
                
                asyncCommand.Execute(null);
                await Task.Delay(100); // Wait for async execution
                Console.WriteLine("  ✅ AsyncRelayCommand executed: " + asyncCommandExecuted);

                var allCommandsWork = commandExecuted && asyncCommandExecuted;

                Console.WriteLine($"Command Functionality Test: {(allCommandsWork ? "PASSED" : "FAILED")}");
                return allCommandsWork;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Command Functionality Test FAILED: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test permission system integration
        /// </summary>
        public static async Task<bool> TestPermissionSystemAsync()
        {
            try
            {
                Console.WriteLine("🔐 Testing Permission System...");

                ServiceLocator.InitializePOSServices();
                var permissionsService = ServiceLocator.Current.GetInstance<UserPermissionsService>();

                // Test default permissions
                var adminPermissions = permissionsService.CreateDefaultPermissions("Admin");
                var cashierPermissions = permissionsService.CreateDefaultPermissions("Cashier");

                Console.WriteLine("  ✅ Admin can create full invoices: " + adminPermissions.CanCreateFullInvoices);
                Console.WriteLine("  ✅ Admin can complete drafts: " + adminPermissions.CanCompleteInvoiceDrafts);
                Console.WriteLine("  ✅ Cashier can create drafts: " + cashierPermissions.CanCreateDraftInvoices);
                Console.WriteLine("  ✅ Cashier cannot create full invoices: " + !cashierPermissions.CanCreateFullInvoices);

                var permissionsWork = adminPermissions.CanCreateFullInvoices && 
                                    adminPermissions.CanCompleteInvoiceDrafts &&
                                    cashierPermissions.CanCreateDraftInvoices && 
                                    !cashierPermissions.CanCreateFullInvoices;

                Console.WriteLine($"Permission System Test: {(permissionsWork ? "PASSED" : "FAILED")}");
                return permissionsWork;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Permission System Test FAILED: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Run all Phase 2 integration tests
        /// </summary>
        public static async Task<bool> RunAllPhase2TestsAsync()
        {
            try
            {
                Console.WriteLine("🚀 Phase 2 Integration Tests - Starting...\n");

                var serviceLocatorTest = await TestServiceLocatorInitializationAsync();
                Console.WriteLine();

                var viewModelTest = await TestViewModelCreationAsync();
                Console.WriteLine();

                var commandTest = await TestCommandFunctionalityAsync();
                Console.WriteLine();

                var permissionTest = await TestPermissionSystemAsync();
                Console.WriteLine();

                var allTestsPassed = serviceLocatorTest && viewModelTest && commandTest && permissionTest;

                Console.WriteLine("=== Phase 2 Integration Test Results ===");
                Console.WriteLine($"Service Locator: {(serviceLocatorTest ? "✅ PASSED" : "❌ FAILED")}");
                Console.WriteLine($"ViewModel Creation: {(viewModelTest ? "✅ PASSED" : "❌ FAILED")}");
                Console.WriteLine($"Command Functionality: {(commandTest ? "✅ PASSED" : "❌ FAILED")}");
                Console.WriteLine($"Permission System: {(permissionTest ? "✅ PASSED" : "❌ FAILED")}");
                Console.WriteLine($"\nOverall Result: {(allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED")}");

                if (allTestsPassed)
                {
                    Console.WriteLine("\n🎉 Phase 2 UI Implementation is ready for integration!");
                }
                else
                {
                    Console.WriteLine("\n⚠️ Some tests failed. Please review and fix issues before proceeding.");
                }

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Phase 2 Integration Tests FAILED: {ex.Message}");
                return false;
            }
        }
    }
}
