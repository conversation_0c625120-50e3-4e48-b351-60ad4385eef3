CREATE TABLE ProductPrices (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    PurchasePrice DECIMAL(18,2) NOT NULL,
    SellingPrice DECIMAL(18,2) NOT NULL,
    EffectiveDate TEXT NOT NULL,
    Source TEXT,
    Notes TEXT,
    PurchaseOrderId INTEGER,
    CreatedAt TEXT NOT NULL,
    FOREIGN KEY(ProductId) REFERENCES Products(Id),
    FOREIGN KEY(PurchaseOrderId) REFERENCES PurchaseOrders(Id)
); 