<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.LocalizedMessageDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <materialDesign:Card Padding="24" MinWidth="300" MaxWidth="500">
        <StackPanel>
            <!-- Icon and Title Row -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16" HorizontalAlignment="Left">
                <materialDesign:PackIcon x:Name="DialogIcon" 
                                       Kind="Information" 
                                       Width="24" 
                                       Height="24" 
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                <TextBlock x:Name="TitleTextBlock"
                          Text="Title"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          VerticalAlignment="Center"/>
            </StackPanel>
            
            <!-- Message Content -->
            <TextBlock x:Name="MessageTextBlock"
                      Text="Message content goes here"
                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                      TextWrapping="Wrap"
                      Margin="0,0,0,24"/>
            
            <!-- Button Panel -->
            <StackPanel x:Name="ButtonPanel" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right">
                
                <!-- Yes Button -->
                <Button x:Name="YesButton"
                       Content="Yes"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="8,0,0,0"
                       Click="YesButton_Click"
                       Visibility="Collapsed"/>
                
                <!-- No Button -->
                <Button x:Name="NoButton"
                       Content="No"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="8,0,0,0"
                       Click="NoButton_Click"
                       Visibility="Collapsed"/>
                
                <!-- Cancel Button -->
                <Button x:Name="CancelButton"
                       Content="Cancel"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="8,0,0,0"
                       Click="CancelButton_Click"
                       Visibility="Collapsed"/>
                
                <!-- OK Button -->
                <Button x:Name="OKButton"
                       Content="OK"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="8,0,0,0"
                       Click="OKButton_Click"
                       Visibility="Collapsed"/>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
