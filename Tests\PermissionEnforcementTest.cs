using System;
using System.Linq;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Tests
{
    /// <summary>
    /// Test utility to verify permission enforcement is working correctly
    /// </summary>
    public class PermissionEnforcementTest
    {
        private readonly DatabaseService _dbService;
        private readonly AuthenticationService _authService;
        private readonly UserPermissionsService _permissionsService;

        public PermissionEnforcementTest()
        {
            _dbService = new DatabaseService();
            _authService = new AuthenticationService(_dbService);
            _permissionsService = new UserPermissionsService(_dbService);
        }

        /// <summary>
        /// Comprehensive test of permission enforcement for a specific user
        /// </summary>
        public void TestUserPermissionEnforcement(string username)
        {
            System.Diagnostics.Debug.WriteLine($"=== PERMISSION ENFORCEMENT TEST FOR USER: {username} ===");

            try
            {
                // Step 1: Authenticate the user
                var user = _dbService.GetAllUsers().FirstOrDefault(u => u.Username == username && u.IsActive);
                if (user == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ User '{username}' not found or inactive");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"✓ User found: {user.Username} (ID: {user.Id}, Role: {user.UserRole?.Name})");

                // Step 2: Check if user has custom permissions
                var customPermissions = _permissionsService.GetUserPermissions(user.Id);
                if (customPermissions == null)
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ User has no custom permissions - will use role-based defaults");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✓ User has custom permissions:");
                    LogPermissions(customPermissions);
                }

                // Step 3: Initialize authentication service
                _authService.Login(user.Username, "test"); // Assuming test password
                System.Diagnostics.Debug.WriteLine($"✓ Authentication service initialized for user");

                // Step 4: Test key permissions
                TestPermission("users.manage", "User Management");
                TestPermission("products.manage", "Product Management");
                TestPermission("sales.create", "Sales Creation");
                TestPermission("sales.void", "Sales Voiding");
                TestPermission("reports.view", "Reports Viewing");
                TestPermission("settings.access", "Settings Access");
                TestPermission("expenses.manage", "Expense Management");
                TestPermission("customers.manage", "Customer Management");

                // Step 5: Test unknown permissions (should always be denied)
                TestPermission("unknown.permission", "Unknown Permission (should be denied)");
                TestPermission("test.fake", "Fake Permission (should be denied)");

                System.Diagnostics.Debug.WriteLine($"=== PERMISSION TEST COMPLETED FOR USER: {username} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ ERROR during permission test: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Test a specific permission and log the result
        /// </summary>
        private void TestPermission(string permission, string description)
        {
            try
            {
                var hasPermission = _authService.HasPermission(permission);
                var status = hasPermission ? "✅ GRANTED" : "❌ DENIED";
                System.Diagnostics.Debug.WriteLine($"  {status} - {description} ({permission})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"  ❌ ERROR - {description} ({permission}): {ex.Message}");
            }
        }

        /// <summary>
        /// Log all permissions for a user
        /// </summary>
        private void LogPermissions(UserPermissions permissions)
        {
            System.Diagnostics.Debug.WriteLine($"    Sales Permissions:");
            System.Diagnostics.Debug.WriteLine($"      - CanCreateSales: {permissions.CanCreateSales}");
            System.Diagnostics.Debug.WriteLine($"      - CanVoidSales: {permissions.CanVoidSales}");
            System.Diagnostics.Debug.WriteLine($"      - CanApplyDiscount: {permissions.CanApplyDiscount}");
            System.Diagnostics.Debug.WriteLine($"      - CanViewSalesHistory: {permissions.CanViewSalesHistory}");

            System.Diagnostics.Debug.WriteLine($"    Product Permissions:");
            System.Diagnostics.Debug.WriteLine($"      - CanManageProducts: {permissions.CanManageProducts}");
            System.Diagnostics.Debug.WriteLine($"      - CanManageCategories: {permissions.CanManageCategories}");
            System.Diagnostics.Debug.WriteLine($"      - CanViewInventory: {permissions.CanViewInventory}");
            System.Diagnostics.Debug.WriteLine($"      - CanAdjustInventory: {permissions.CanAdjustInventory}");

            System.Diagnostics.Debug.WriteLine($"    Financial Permissions:");
            System.Diagnostics.Debug.WriteLine($"      - CanManageExpenses: {permissions.CanManageExpenses}");
            System.Diagnostics.Debug.WriteLine($"      - CanManageCashDrawer: {permissions.CanManageCashDrawer}");
            System.Diagnostics.Debug.WriteLine($"      - CanViewReports: {permissions.CanViewReports}");
            System.Diagnostics.Debug.WriteLine($"      - CanManagePrices: {permissions.CanManagePrices}");

            System.Diagnostics.Debug.WriteLine($"    Customer & Supplier Permissions:");
            System.Diagnostics.Debug.WriteLine($"      - CanManageCustomers: {permissions.CanManageCustomers}");
            System.Diagnostics.Debug.WriteLine($"      - CanManageSuppliers: {permissions.CanManageSuppliers}");

            System.Diagnostics.Debug.WriteLine($"    Administrative Permissions:");
            System.Diagnostics.Debug.WriteLine($"      - CanManageUsers: {permissions.CanManageUsers}");
            System.Diagnostics.Debug.WriteLine($"      - CanManageRoles: {permissions.CanManageRoles}");
            System.Diagnostics.Debug.WriteLine($"      - CanAccessSettings: {permissions.CanAccessSettings}");
            System.Diagnostics.Debug.WriteLine($"      - CanViewLogs: {permissions.CanViewLogs}");
        }

        /// <summary>
        /// Test all users in the system
        /// </summary>
        public void TestAllUsers()
        {
            System.Diagnostics.Debug.WriteLine("=== TESTING ALL USERS IN SYSTEM ===");

            var users = _dbService.GetAllUsers().Where(u => u.IsActive).ToList();
            System.Diagnostics.Debug.WriteLine($"Found {users.Count} active users");

            foreach (var user in users)
            {
                TestUserPermissionEnforcement(user.Username);
                System.Diagnostics.Debug.WriteLine(""); // Add spacing between users
            }

            System.Diagnostics.Debug.WriteLine("=== ALL USERS TESTED ===");
        }

        /// <summary>
        /// Quick test to verify the permission system is working
        /// </summary>
        public bool QuickPermissionTest()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== QUICK PERMISSION SYSTEM TEST ===");

                // Test 1: Check if UserPermissionsService can load permissions
                var testUser = _dbService.GetAllUsers().FirstOrDefault(u => u.IsActive);
                if (testUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ No active users found for testing");
                    return false;
                }

                var permissions = _permissionsService.GetUserPermissions(testUser.Id);
                System.Diagnostics.Debug.WriteLine($"✓ Permission loading test passed for user {testUser.Username}");

                // Test 2: Check if AuthenticationService can check permissions
                _authService.Login(testUser.Username, "test");
                var hasPermission = _authService.HasPermission("sales.create");
                System.Diagnostics.Debug.WriteLine($"✓ Permission checking test passed - result: {hasPermission}");

                System.Diagnostics.Debug.WriteLine("=== QUICK TEST PASSED ===");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ QUICK TEST FAILED: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Verify that custom permissions override role-based permissions
        /// </summary>
        public void TestCustomPermissionOverride(string username)
        {
            System.Diagnostics.Debug.WriteLine($"=== TESTING CUSTOM PERMISSION OVERRIDE FOR: {username} ===");

            var user = _dbService.GetAllUsers().FirstOrDefault(u => u.Username == username && u.IsActive);
            if (user == null)
            {
                System.Diagnostics.Debug.WriteLine($"❌ User '{username}' not found");
                return;
            }

            var customPermissions = _permissionsService.GetUserPermissions(user.Id);
            if (customPermissions == null)
            {
                System.Diagnostics.Debug.WriteLine($"ℹ️ User '{username}' has no custom permissions - using role defaults");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✓ User '{username}' has custom permissions");
            System.Diagnostics.Debug.WriteLine($"  Database Role: {user.UserRole?.Name}");

            // Test that custom permissions are used instead of role defaults
            _authService.Login(user.Username, "test");

            // If user has custom permissions, the result should match custom permissions, not role defaults
            var canManageUsers = _authService.HasPermission("users.manage");
            var expectedCanManageUsers = customPermissions.CanManageUsers;

            if (canManageUsers == expectedCanManageUsers)
            {
                System.Diagnostics.Debug.WriteLine($"✅ Custom permission override working: CanManageUsers = {canManageUsers}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ Custom permission override FAILED:");
                System.Diagnostics.Debug.WriteLine($"    Expected (custom): {expectedCanManageUsers}");
                System.Diagnostics.Debug.WriteLine($"    Actual (result): {canManageUsers}");
                System.Diagnostics.Debug.WriteLine($"    This indicates role-based fallback is being used incorrectly!");
            }

            System.Diagnostics.Debug.WriteLine($"=== CUSTOM PERMISSION OVERRIDE TEST COMPLETED ===");
        }
    }
}
