<?xml version="1.0" encoding="utf-8"?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <!-- Common -->
    <system:String x:Key="AppName">Point of Sale System</system:String>
    <system:String x:Key="Loading">Loading...</system:String>
    <system:String x:Key="Save">Save</system:String>
    <system:String x:Key="Delete">Delete</system:String>
    <system:String x:Key="Edit">Edit</system:String>
    <system:String x:Key="Add">Add</system:String>
    <system:String x:Key="Search">Search</system:String>
    <system:String x:Key="Clear">Clear</system:String>
    <system:String x:Key="Confirm">Confirm</system:String>
    <system:String x:Key="Print">Print</system:String>
    <system:String x:Key="Export">Export</system:String>
    <system:String x:Key="Import">Import</system:String>
    <system:String x:Key="Refresh">Refresh</system:String>
    <system:String x:Key="Filter">Filter</system:String>

    <!-- Missing Navigation Resources -->
    <system:String x:Key="CustomProduct">Custom Product</system:String>
    <system:String x:Key="SaleNavigation">Sale Navigation</system:String>
    <system:String x:Key="PreviousSale">Previous Sale</system:String>
    <system:String x:Key="NextSale">Next Sale</system:String>
    <system:String x:Key="SaveChanges">Save Changes</system:String>
    <system:String x:Key="ExitEditMode">Exit Edit Mode</system:String>
    <system:String x:Key="Payment">Payment</system:String>
    <system:String x:Key="Sort">Sort</system:String>
    <system:String x:Key="Details">Details</system:String>
    <system:String x:Key="Status">Status</system:String>
    <system:String x:Key="Value">Value</system:String>
    <system:String x:Key="Close">Close</system:String>
    <system:String x:Key="Customer">Customer</system:String>
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="DueDate">Due Date</system:String>
    <system:String x:Key="Amount">Amount</system:String>
    <system:String x:Key="PaymentMethod">Payment Method</system:String>
    <system:String x:Key="Actions">Actions</system:String>
    <system:String x:Key="View">View Details</system:String>
    <system:String x:Key="DA">DA</system:String>
    
    <!-- Dashboard -->
    <system:String x:Key="TodaysSales">Today's Sales</system:String>
    <system:String x:Key="BusinessOverview">Business Overview</system:String>
    <system:String x:Key="NoData">No Data</system:String>

    <!-- Transactions -->
    <system:String x:Key="UnpaidTransactions">Unpaid Transactions</system:String>
    <system:String x:Key="PayAll">Pay All</system:String>
    <system:String x:Key="PaymentAmount">Payment Amount</system:String>
    <system:String x:Key="PartialPayment">Partial Payment</system:String>
    <system:String x:Key="EnablePartialPayment">Enable Partial Payment</system:String>
    <system:String x:Key="RemainingAmount">Remaining Amount</system:String>
    <system:String x:Key="InvalidPaymentAmount">Please enter a valid payment amount</system:String>
    <system:String x:Key="BatchPayment">Batch Payment</system:String>
    <system:String x:Key="Cash">Cash</system:String>
    <system:String x:Key="Card">Card</system:String>
    <system:String x:Key="Cancel">Cancel</system:String>
    <system:String x:Key="ProcessPayment">Process Payment</system:String>
    <system:String x:Key="SaleIsOverdue">This sale is overdue</system:String>

    <!-- Invoices -->
    <system:String x:Key="Invoices">Invoices</system:String>
    <system:String x:Key="InvoiceNumber">Invoice Number</system:String>
    <system:String x:Key="SearchInvoices">Search invoices...</system:String>
    <system:String x:Key="SortBy">Sort by</system:String>
    <system:String x:Key="TotalAmount">Total Amount</system:String>

    <!-- Status -->
    <system:String x:Key="StatusCompleted">Completed</system:String>
    <system:String x:Key="StatusCancelled">Cancelled</system:String>
    <system:String x:Key="StatusInStock">In Stock</system:String>
    <system:String x:Key="StatusLowStock">Low Stock</system:String>
    <system:String x:Key="StatusOutOfStock">Out of Stock</system:String>
    <system:String x:Key="Unpaid">Unpaid</system:String>

    <!-- Products -->
    <system:String x:Key="SearchByBarcode">Search by barcode</system:String>
    <system:String x:Key="BarcodeSearch">Enter barcode</system:String>
    <system:String x:Key="ProductSearchHint">Search by name or barcode</system:String>
    <system:String x:Key="EditProduct">Edit Product</system:String>
    <system:String x:Key="AllProducts">All Products</system:String>
    <system:String x:Key="ProductQuantity">Quantity</system:String>
    <system:String x:Key="ProductProfit">Profit</system:String>
    <system:String x:Key="ProductTotalSales">Total Sales</system:String>
    <system:String x:Key="SelectProduct">Select Product</system:String>
    <system:String x:Key="SelectCategory">Select Category</system:String>
    <system:String x:Key="ProductName">Product Name</system:String>
    <system:String x:Key="AddProduct">Add Product</system:String>

    <!-- Services -->
    <system:String x:Key="Service">Service</system:String>
    <system:String x:Key="Services">Services</system:String>
    <system:String x:Key="ServiceName">Service Name</system:String>
    <system:String x:Key="ServiceDescription">Service Description</system:String>
    <system:String x:Key="ServiceQuantity">Service Quantity</system:String>
    <system:String x:Key="ServicePrice">Service Price</system:String>
    <system:String x:Key="ServiceCost">Service Cost</system:String>
    <system:String x:Key="AddService">Add Service</system:String>
    <system:String x:Key="EditService">Edit Service</system:String>
    <system:String x:Key="SelectService">Select Service</system:String>
    <system:String x:Key="ServiceType">Service Type</system:String>
    <system:String x:Key="ProductType">Product Type</system:String>
    <system:String x:Key="ItemType">Item Type</system:String>

    <!-- Time Periods -->
    <system:String x:Key="TimePeriod">Time Period</system:String>
    <system:String x:Key="TimePeriod_Today">Today</system:String>
    <system:String x:Key="TimePeriod_ThisWeek">This Week</system:String>
    <system:String x:Key="TimePeriod_ThisMonth">This Month</system:String>
    <system:String x:Key="TimePeriod_ThisYear">This Year</system:String>
    <system:String x:Key="SelectPeriod">Select Period</system:String>

    <!-- Profit Statistics -->
    <system:String x:Key="ProfitStatsTitle">Profit Statistics</system:String>
    <system:String x:Key="ProfitStatsFor">Profit Statistics for</system:String>
    <system:String x:Key="TotalProfit">Total Profit</system:String>
    <system:String x:Key="ProfitMargin">Profit Margin</system:String>
    <system:String x:Key="AverageProfit">Average Profit</system:String>
    <system:String x:Key="ProfitTrend">Profit Trend</system:String>
    <system:String x:Key="HourlyDistribution">Hourly Distribution</system:String>
    <system:String x:Key="DailyDistribution">Daily Distribution</system:String>
    <system:String x:Key="TopProducts">Top Products</system:String>
    <system:String x:Key="GrossProfit">Gross Profit</system:String>
    <system:String x:Key="NetProfit">Net Profit</system:String>
    <system:String x:Key="ProfitType">Profit Type</system:String>
    <system:String x:Key="ProfitByHour">Profit by Hour</system:String>
    <system:String x:Key="ProfitByDay">Profit by Day</system:String>
    <system:String x:Key="TopPerformingProducts">Top Performing Products</system:String>

    <!-- Expiry Stats -->
    <system:String x:Key="TotalExpiringProducts">Total Expiring Products</system:String>
    <system:String x:Key="ExpiredProducts">Expired Products</system:String>
    <system:String x:Key="NearExpiryProducts">Near Expiry Products</system:String>
    <system:String x:Key="PotentialLoss">Potential Loss</system:String>
    <system:String x:Key="ProductsNeedingAttention">Products needing attention</system:String>
    <system:String x:Key="ProductsExpired">Products have expired</system:String>
    <system:String x:Key="ProductsExpiringSoon">Products expiring soon</system:String>
    <system:String x:Key="EstimatedLossValue">Estimated loss value</system:String>
    <system:String x:Key="ExpiryStats">Expiry Statistics</system:String>
    <system:String x:Key="DetailedExpiryMetrics">Detailed expiry metrics and trends</system:String>
    <system:String x:Key="ExpiryTrend">Expiry Trend</system:String>
    <system:String x:Key="CategoryDistribution">Category Distribution</system:String>
    <system:String x:Key="ExpiryByDays">Expiry by Days</system:String>
    <system:String x:Key="ExpiringProducts">Expiring Products</system:String>

    <!-- Unpaid Sales Stats -->
    <system:String x:Key="UnpaidSales">Unpaid Sales</system:String>
    <system:String x:Key="DetailedUnpaidSalesMetrics">Detailed unpaid sales metrics and trends</system:String>
    <system:String x:Key="TotalUnpaidSales">Total Unpaid Sales</system:String>
    <system:String x:Key="TotalUnpaidAmount">Total Unpaid Amount</system:String>
    <system:String x:Key="PastDueSales">Past Due Sales</system:String>
    <system:String x:Key="PastDueAmount">Past Due Amount</system:String>
    <system:String x:Key="UnpaidTrends">Unpaid Sales Trends</system:String>
    <system:String x:Key="AgeDistribution">Age Distribution</system:String>
    <system:String x:Key="AmountRanges">Amount Ranges</system:String>
    <system:String x:Key="UnpaidSalesList">Unpaid Sales List</system:String>

    <!-- User Permissions -->
    <system:String x:Key="SalesPermissions">Sales Permissions</system:String>
    <system:String x:Key="CreateSales">Create Sales</system:String>
    <system:String x:Key="VoidSales">Void Sales</system:String>
    <system:String x:Key="ApplyDiscounts">Apply Discounts</system:String>
    <system:String x:Key="ViewSalesHistory">View Sales History</system:String>
    <system:String x:Key="ProductPermissions">Product Permissions</system:String>
    <system:String x:Key="ManageProducts">Manage Products</system:String>
    <system:String x:Key="ManageCategories">Manage Categories</system:String>
    <system:String x:Key="ViewInventory">View Inventory</system:String>
    <system:String x:Key="AdjustInventory">Adjust Inventory</system:String>
    <system:String x:Key="FinancialPermissions">Financial Permissions</system:String>
    <system:String x:Key="ManageExpenses">Manage Expenses</system:String>
    <system:String x:Key="ManageCashDrawer">Manage Cash Drawer</system:String>
    <system:String x:Key="ViewReports">View Reports</system:String>
    <system:String x:Key="ManagePrices">Manage Prices</system:String>
    <system:String x:Key="CustomerSupplierPermissions">Customer &amp; Supplier Permissions</system:String>
    <system:String x:Key="ManageCustomers">Manage Customers</system:String>
    <system:String x:Key="ManageSuppliers">Manage Suppliers</system:String>
    <system:String x:Key="AdministrativePermissions">Administrative Permissions</system:String>
    <system:String x:Key="ManageUsers">Manage Users</system:String>
    <system:String x:Key="ManageRoles">Manage Roles</system:String>
    <system:String x:Key="AccessSettings">Access Settings</system:String>
    <system:String x:Key="ViewLogs">View Logs</system:String>
    <system:String x:Key="SelectAll">Select All</system:String>
    <system:String x:Key="DeselectAll">Deselect All</system:String>
    <system:String x:Key="ClearAll">Clear All</system:String>

    <!-- Company Settings -->
    <system:String x:Key="CompanySettings">Company Settings</system:String>
    <system:String x:Key="CompanyName">Company Name</system:String>
    <system:String x:Key="Address">Address</system:String>
    <system:String x:Key="Phone">Phone</system:String>
    <system:String x:Key="Email">Email</system:String>
    <system:String x:Key="TaxRate">Tax Rate</system:String>
    <system:String x:Key="ReceiptFooter">Receipt Footer</system:String>
    <system:String x:Key="CompanyLogo">Company Logo</system:String>
    <system:String x:Key="UploadLogo">Upload Logo</system:String>
    <system:String x:Key="RemoveLogo">Remove Logo</system:String>
    <system:String x:Key="ConfirmRemoveLogo">Are you sure you want to remove the company logo?</system:String>
    <system:String x:Key="LogoTooLarge">Logo image is too large. Please select an image smaller than 1MB.</system:String>
    <system:String x:Key="ErrorLoadingLogo">Error loading logo</system:String>
    <system:String x:Key="ErrorSavingCompanySettings">Error saving company settings</system:String>

    <!-- Password Reset -->
    <system:String x:Key="ResetPasswordTitle">Password Reset Instructions</system:String>
    <system:String x:Key="ResetPasswordInstructions">To reset your password, please contact your system administrator. They will be able to help you regain access to your account.</system:String>

    <!-- Enhanced Printing -->
    <system:String x:Key="Summary">Summary</system:String>
    <system:String x:Key="TotalInvoices">Total Invoices</system:String>
    <system:String x:Key="ReportDate">Report Date</system:String>
    <system:String x:Key="GeneratedOn">Generated on</system:String>
    <system:String x:Key="UnpaidInvoices">Unpaid Invoices</system:String>

</ResourceDictionary>