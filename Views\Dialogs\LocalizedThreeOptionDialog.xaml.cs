using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Custom localized three-option dialog for insufficient stock scenarios
    /// </summary>
    public partial class LocalizedThreeOptionDialog : UserControl
    {
        public enum ThreeOptionResult
        {
            None = 0,
            FirstOption = 1,
            SecondOption = 2,
            Cancel = 3
        }

        public enum MessageBoxImage
        {
            None = 0,
            Error = 16,
            Question = 32,
            Warning = 48,
            Information = 64
        }

        public ThreeOptionResult Result { get; private set; } = ThreeOptionResult.None;

        public LocalizedThreeOptionDialog()
        {
            InitializeComponent();
        }

        public static async System.Threading.Tasks.Task<ThreeOptionResult> ShowAsync(
            string message,
            string title = "",
            string firstOptionText = "Option 1",
            string secondOptionText = "Option 2",
            string cancelText = "Cancel",
            MessageBoxImage icon = MessageBoxImage.Information)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] ShowAsync called with message: '{message.Substring(0, Math.Min(50, message.Length))}...'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] ShowAsync title: '{title}'");

            var dialog = new LocalizedThreeOptionDialog();
            dialog.SetupDialog(message, title, firstOptionText, secondOptionText, cancelText, icon);

            try
            {
                // Try different DialogHost identifiers in order of preference
                string[] identifiers = { "SalesDialog", "MainWindowCashDrawerDialog", "RootDialog", "MainSalesDialog", "MainDialog" };

                foreach (var identifier in identifiers)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Trying DialogHost identifier: {identifier}");

                        await DialogHost.Show(dialog, identifier);
                        System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Successfully showed dialog with identifier: {identifier}");
                        return dialog.Result;
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] DialogHost identifier '{identifier}' not found: {ex.Message}");
                        // Try next identifier
                        continue;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Error with identifier '{identifier}': {ex.Message}");
                        continue;
                    }
                }

                // If no DialogHost works, try without identifier
                try
                {
                    System.Diagnostics.Debug.WriteLine("[LocalizedThreeOptionDialog] Trying DialogHost without identifier");

                    await DialogHost.Show(dialog);
                    System.Diagnostics.Debug.WriteLine("[LocalizedThreeOptionDialog] Successfully showed dialog without identifier");
                    return dialog.Result;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Failed to show dialog without identifier: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Outer exception: {ex.Message}");
            }

            // Fallback to standard MessageBox if all else fails
            System.Diagnostics.Debug.WriteLine("[LocalizedThreeOptionDialog] Falling back to standard MessageBox");
            var standardResult = System.Windows.MessageBox.Show(message, title,
                System.Windows.MessageBoxButton.YesNoCancel,
                (System.Windows.MessageBoxImage)icon);
            
            return standardResult switch
            {
                System.Windows.MessageBoxResult.Yes => ThreeOptionResult.FirstOption,
                System.Windows.MessageBoxResult.No => ThreeOptionResult.SecondOption,
                _ => ThreeOptionResult.Cancel
            };
        }

        private void SetupDialog(string message, string title, string firstOptionText, string secondOptionText, string cancelText, MessageBoxImage icon)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] SetupDialog called");
            System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Message: '{message}'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedThreeOptionDialog] Title: '{title}'");

            // Set message and title
            MessageTextBlock.Text = message;
            TitleTextBlock.Text = string.IsNullOrEmpty(title) ? GetDefaultTitle(icon) : title;

            // Set button text
            FirstOptionButton.Content = firstOptionText;
            SecondOptionButton.Content = secondOptionText;
            CancelButton.Content = cancelText;

            // Set icon
            SetDialogIcon(icon);

            // Set default button
            FirstOptionButton.IsDefault = true;
        }

        private string GetDefaultTitle(MessageBoxImage icon)
        {
            return icon switch
            {
                MessageBoxImage.Error => Application.Current?.FindResource("ErrorTitle") as string ?? "Error",
                MessageBoxImage.Warning => Application.Current?.FindResource("WarningTitle") as string ?? "Warning",
                MessageBoxImage.Question => Application.Current?.FindResource("QuestionTitle") as string ?? "Question",
                MessageBoxImage.Information => Application.Current?.FindResource("InformationTitle") as string ?? "Information",
                _ => Application.Current?.FindResource("InformationTitle") as string ?? "Information"
            };
        }

        private void SetDialogIcon(MessageBoxImage icon)
        {
            DialogIcon.Kind = icon switch
            {
                MessageBoxImage.Error => PackIconKind.AlertCircle,
                MessageBoxImage.Warning => PackIconKind.Alert,
                MessageBoxImage.Question => PackIconKind.HelpCircle,
                MessageBoxImage.Information => PackIconKind.Information,
                _ => PackIconKind.Information
            };

            DialogIcon.Foreground = icon switch
            {
                MessageBoxImage.Error => System.Windows.Media.Brushes.Red,
                MessageBoxImage.Warning => System.Windows.Media.Brushes.Orange,
                MessageBoxImage.Question => System.Windows.Media.Brushes.Blue,
                MessageBoxImage.Information => System.Windows.Media.Brushes.Blue,
                _ => System.Windows.Media.Brushes.Blue
            };
        }

        private void FirstOptionButton_Click(object sender, RoutedEventArgs e)
        {
            Result = ThreeOptionResult.FirstOption;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }

        private void SecondOptionButton_Click(object sender, RoutedEventArgs e)
        {
            Result = ThreeOptionResult.SecondOption;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Result = ThreeOptionResult.Cancel;
            DialogHost.CloseDialogCommand.Execute(null, this);
        }
    }
}
