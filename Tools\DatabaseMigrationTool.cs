using System;
using System.IO;
using System.Threading.Tasks;
using POSSystem.Services.Migration;

namespace POSSystem.Tools
{
    /// <summary>
    /// ✅ STANDALONE MIGRATION TOOL: Command-line tool for database migration
    /// Usage: DatabaseMigrationTool.exe [--backup-only] [--no-backup] [--help]
    /// </summary>
    public class DatabaseMigrationTool
    {
        public static async Task<int> Main(string[] args)
        {
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("POS System Database Migration Tool");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine();

            try
            {
                // Parse command line arguments
                var options = ParseArguments(args);
                if (options == null) return 1; // Help was shown or invalid args

                // Initialize migration service
                var migrationService = new DatabaseMigrationService();

                // Show database status
                await ShowDatabaseStatus();

                if (options.BackupOnly)
                {
                    Console.WriteLine("Creating backup only...");
                    await <PERSON>reateBackupOnly();
                    return 0;
                }

                // Confirm migration
                if (!options.AutoConfirm && !ConfirmMigration())
                {
                    Console.WriteLine("Migration cancelled by user.");
                    return 0;
                }

                // Perform migration
                Console.WriteLine("Starting migration...");
                Console.WriteLine();

                var result = await migrationService.MigrateProductDataAsync();

                // Show results
                Console.WriteLine();
                Console.WriteLine("=".PadRight(60, '='));
                Console.WriteLine("MIGRATION RESULTS");
                Console.WriteLine("=".PadRight(60, '='));
                Console.WriteLine(result.GetSummary());

                if (result.IsSuccess)
                {
                    Console.WriteLine("✅ Migration completed successfully!");
                    return 0;
                }
                else
                {
                    Console.WriteLine("❌ Migration failed!");
                    return 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Fatal error: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("Stack trace:");
                Console.WriteLine(ex.StackTrace);
                return 1;
            }
        }

        private static MigrationToolOptions ParseArguments(string[] args)
        {
            var options = new MigrationToolOptions();

            foreach (var arg in args)
            {
                switch (arg.ToLower())
                {
                    case "--help":
                    case "-h":
                        ShowHelp();
                        return null;
                    case "--backup-only":
                        options.BackupOnly = true;
                        break;
                    case "--no-backup":
                        options.CreateBackup = false;
                        break;
                    case "--auto-confirm":
                    case "-y":
                        options.AutoConfirm = true;
                        break;
                    default:
                        Console.WriteLine($"Unknown argument: {arg}");
                        ShowHelp();
                        return null;
                }
            }

            return options;
        }

        private static void ShowHelp()
        {
            Console.WriteLine("Database Migration Tool - Migrates product data from pos2.db to pos.db");
            Console.WriteLine();
            Console.WriteLine("Usage: DatabaseMigrationTool.exe [options]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  --help, -h        Show this help message");
            Console.WriteLine("  --backup-only     Create backup only, don't migrate");
            Console.WriteLine("  --no-backup       Skip backup creation (not recommended)");
            Console.WriteLine("  --auto-confirm, -y Auto-confirm migration without prompting");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  DatabaseMigrationTool.exe");
            Console.WriteLine("  DatabaseMigrationTool.exe --auto-confirm");
            Console.WriteLine("  DatabaseMigrationTool.exe --backup-only");
        }

        private static async Task ShowDatabaseStatus()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var legacyDbPath = Path.Combine(baseDirectory, "pos2.db");
            var currentDbPath = Path.Combine(baseDirectory, "pos.db");

            Console.WriteLine("Database Status:");
            Console.WriteLine($"  Legacy DB (pos2.db): {(File.Exists(legacyDbPath) ? "✅ Found" : "❌ Not found")}");
            Console.WriteLine($"  Current DB (pos.db): {(File.Exists(currentDbPath) ? "✅ Found" : "❌ Not found")}");

            if (File.Exists(legacyDbPath))
            {
                var legacyCount = await GetProductCount(legacyDbPath);
                Console.WriteLine($"  Legacy products: {legacyCount}");
            }

            if (File.Exists(currentDbPath))
            {
                var currentCount = await GetProductCount(currentDbPath);
                Console.WriteLine($"  Current products: {currentCount}");
            }

            Console.WriteLine();
        }

        private static async Task<int> GetProductCount(string dbPath)
        {
            try
            {
                using var connection = new System.Data.SQLite.SQLiteConnection($"Data Source={dbPath};Version=3;");
                await connection.OpenAsync();
                
                var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM Products WHERE IsActive = 1 OR IsActive IS NULL";
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
            catch
            {
                return 0;
            }
        }

        private static bool ConfirmMigration()
        {
            Console.WriteLine("⚠️  WARNING: This will migrate product data from pos2.db to pos.db");
            Console.WriteLine("   A backup will be created automatically.");
            Console.WriteLine();
            Console.Write("Do you want to continue? (y/N): ");
            
            var response = Console.ReadLine()?.Trim().ToLower();
            return response == "y" || response == "yes";
        }

        private static async Task CreateBackupOnly()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var currentDbPath = Path.Combine(baseDirectory, "pos.db");
            
            if (!File.Exists(currentDbPath))
            {
                Console.WriteLine("❌ Current database (pos.db) not found!");
                return;
            }

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            var backupPath = Path.Combine(baseDirectory, $"pos_backup_{timestamp}.db");
            
            File.Copy(currentDbPath, backupPath);
            Console.WriteLine($"✅ Backup created: {backupPath}");
        }
    }

    public class MigrationToolOptions
    {
        public bool BackupOnly { get; set; }
        public bool CreateBackup { get; set; } = true;
        public bool AutoConfirm { get; set; }
    }
}
