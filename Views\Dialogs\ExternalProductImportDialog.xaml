<UserControl x:Class="POSSystem.Views.Dialogs.ExternalProductImportDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="500" Height="400">
    
    <materialDesign:Card Margin="16" Padding="16">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <StackPanel Grid.Row="0" Margin="0,0,0,16">
                <TextBlock Text="Product Found in External Database" 
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,8"/>
                <TextBlock Text="Would you like to import this product into your inventory?"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
            
            <!-- Product Details -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="0,0,0,16">
                    
                    <!-- Product Image -->
                    <Border Background="{DynamicResource MaterialDesignCardBackground}"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="1"
                           CornerRadius="4"
                           Height="120"
                           Margin="0,0,0,16"
                           HorizontalAlignment="Center"
                           Width="120">
                        <Image x:Name="imgProduct" 
                              Stretch="UniformToFill"
                              Source="/Images/no-image.png"/>
                    </Border>
                    
                    <!-- Product Information -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Name -->
                        <materialDesign:PackIcon Grid.Row="0" Grid.Column="0" 
                                               Kind="Package" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Product Name" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtProductName" 
                                      Text="Sample Product"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                                      TextWrapping="Wrap"/>
                        </StackPanel>
                        
                        <!-- Price -->
                        <materialDesign:PackIcon Grid.Row="1" Grid.Column="0" 
                                               Kind="CurrencyUsd" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Selling Price" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtSellingPrice" 
                                      Text="$0.00"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </StackPanel>
                        
                        <!-- Purchase Price -->
                        <materialDesign:PackIcon Grid.Row="2" Grid.Column="0" 
                                               Kind="ShoppingCart" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Purchase Price" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtPurchasePrice" 
                                      Text="$0.00"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </StackPanel>
                        
                        <!-- Category -->
                        <materialDesign:PackIcon Grid.Row="3" Grid.Column="0" 
                                               Kind="Tag" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Category" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtCategory" 
                                      Text="Uncategorized"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </StackPanel>
                        
                        <!-- Barcode -->
                        <materialDesign:PackIcon Grid.Row="4" Grid.Column="0" 
                                               Kind="Barcode" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="4" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Barcode" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtBarcode" 
                                      Text=""
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </StackPanel>
                        
                        <!-- Description -->
                        <materialDesign:PackIcon Grid.Row="5" Grid.Column="0" 
                                               Kind="Information" 
                                               Margin="0,0,8,8"
                                               VerticalAlignment="Top"/>
                        <StackPanel Grid.Row="5" Grid.Column="1" Margin="0,0,0,8">
                            <TextBlock Text="Description" 
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="txtDescription" 
                                      Text="No description available"
                                      Style="{StaticResource MaterialDesignBody2TextBlock}"
                                      TextWrapping="Wrap"
                                      MaxHeight="60"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </ScrollViewer>
            
            <!-- Action Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="btnCancel"
                       Content="Cancel"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="0,0,8,0"
                       Click="btnCancel_Click"/>
                <Button x:Name="btnImport"
                       Content="Import &amp; Edit"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Click="btnImport_Click"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
