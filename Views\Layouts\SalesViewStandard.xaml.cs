using System.Windows.Controls;
using POSSystem.ViewModels;
using POSSystem.Services;

namespace POSSystem.Views.Layouts
{
    public partial class SalesViewStandard : UserControl
    {
        private SaleViewModel ViewModel => (SaleViewModel)DataContext;
        
        public SalesViewStandard()
        {
            InitializeComponent();
            
            // Forward the DataContext to the embedded SalesView
            this.DataContextChanged += (s, e) => 
            {
                if (originalSalesView != null)
                {
                    originalSalesView.DataContext = this.DataContext;
                    
                    // ✅ CRITICAL FIX: Load products in background to prevent UI blocking
                    if (DataContext is SaleViewModel vm && vm.FilteredProducts.Count == 0)
                    {
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await vm.RefreshProducts();
                                System.Diagnostics.Debug.WriteLine("[SALESVIEW-STANDARD] Background product loading completed");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEW-STANDARD] Error loading products: {ex.Message}");
                            }
                        });
                    }
                }
            };
            
            // Ensure the embedded SalesView uses the DialogService
            // This is a workaround to make sure any dialogs shown by the original SalesView
            // are properly routed through our DialogService
            if (originalSalesView != null)
            {
                // We can't directly modify the SalesView's DialogHost usage,
                // but we can ensure it's properly initialized
                originalSalesView.Loaded += (s, e) =>
                {
                    // ✅ CRITICAL FIX: Force product refresh in background to prevent UI blocking
                    if (DataContext is SaleViewModel vm)
                    {
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await vm.RefreshProducts();
                                System.Diagnostics.Debug.WriteLine("[SALESVIEW-STANDARD-EMBEDDED] Background product refresh completed");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SALESVIEW-STANDARD-EMBEDDED] Error refreshing products: {ex.Message}");
                            }
                        });
                    }
                };
            }
        }
    }
} 