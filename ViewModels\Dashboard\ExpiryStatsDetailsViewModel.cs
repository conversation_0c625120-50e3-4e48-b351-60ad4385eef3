using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System.Windows;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels;
using POSSystem.Data;
using System.Windows.Threading;

namespace POSSystem.ViewModels.Dashboard
{
    public class ExpiryStatsDetailsViewModel : INotifyPropertyChanged
    {
        private readonly RefactoredDashboardViewModel _dashboardViewModel;
        private readonly DatabaseService _dbService;
        private readonly ProductsViewModel _productsViewModel;
        private bool _isLoading;
        private string _title;
        private string _subtitle;
        private ICommand _editProductCommand;
        
        // Statistics
        private int _totalExpiringProducts;
        private int _expiredProducts;
        private int _nearExpiryProducts;
        private decimal _potentialLoss;
        
        // Collections
        private ObservableCollection<ProductWithExpiryStatus> _expiringProducts;
        private ObservableCollection<Category> _categories;
        private Category _selectedCategory;
        private bool _isCategoryFilterEnabled;
        
        // Chart data
        private SeriesCollection _expiryTrendSeries;
        private string[] _expiryTrendLabels;
        private SeriesCollection _categoryDistributionSeries;
        private SeriesCollection _expiryByDaysSeries;
        private string[] _expiryByDaysLabels;
        
        // Period selection
        private List<TrendPeriodItem> _trendPeriods;
        private TrendPeriodItem _selectedTrendPeriod;

        public event PropertyChangedEventHandler PropertyChanged;

        public ExpiryStatsDetailsViewModel(
            RefactoredDashboardViewModel dashboardViewModel,
            DatabaseService dbService)
        {
            _dashboardViewModel = dashboardViewModel;
            _dbService = dbService;
            _productsViewModel = new ProductsViewModel(new AlertService(new POSDbContext(), dbService));

            // Initialize collections
            ExpiringProducts = new ObservableCollection<ProductWithExpiryStatus>();
            Categories = new ObservableCollection<Category>();
            IsCategoryFilterEnabled = false;
            
            // Initialize trend periods
            TrendPeriods = new List<TrendPeriodItem>
            {
                new TrendPeriodItem { ResourceKey = "TimePeriod_Today" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisWeek" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisMonth" },
                new TrendPeriodItem { ResourceKey = "TimePeriod_ThisYear" }
            };
            
            // Set initial period
            SelectedTrendPeriod = TrendPeriods.First();
            
            // Set initial title
            Title = Application.Current.TryFindResource("ExpiryStats")?.ToString() ?? "Expiry Statistics";
            Subtitle = Application.Current.TryFindResource("DetailedExpiryMetrics")?.ToString() ?? "Detailed expiry metrics and trends";
            
            // Initialize chart series
            ExpiryTrendSeries = new SeriesCollection();
            CategoryDistributionSeries = new SeriesCollection();
            ExpiryByDaysSeries = new SeriesCollection();
            
            // Load initial data
            _ = LoadDataAsync();
        }

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Subtitle
        {
            get => _subtitle;
            set { _subtitle = value; OnPropertyChanged(); }
        }

        public int TotalExpiringProducts
        {
            get => _totalExpiringProducts;
            set { _totalExpiringProducts = value; OnPropertyChanged(); }
        }

        public int ExpiredProducts
        {
            get => _expiredProducts;
            set { _expiredProducts = value; OnPropertyChanged(); }
        }

        public int NearExpiryProducts
        {
            get => _nearExpiryProducts;
            set { _nearExpiryProducts = value; OnPropertyChanged(); }
        }

        public decimal PotentialLoss
        {
            get => _potentialLoss;
            set { _potentialLoss = value; OnPropertyChanged(); }
        }

        public ObservableCollection<ProductWithExpiryStatus> ExpiringProducts
        {
            get => _expiringProducts;
            set { _expiringProducts = value; OnPropertyChanged(); }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set { _categories = value; OnPropertyChanged(); }
        }

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set 
            { 
                _selectedCategory = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public bool IsCategoryFilterEnabled
        {
            get => _isCategoryFilterEnabled;
            set 
            { 
                _isCategoryFilterEnabled = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public SeriesCollection ExpiryTrendSeries
        {
            get => _expiryTrendSeries;
            set { _expiryTrendSeries = value; OnPropertyChanged(); }
        }

        public string[] ExpiryTrendLabels
        {
            get => _expiryTrendLabels;
            set { _expiryTrendLabels = value; OnPropertyChanged(); }
        }

        public SeriesCollection CategoryDistributionSeries
        {
            get => _categoryDistributionSeries;
            set { _categoryDistributionSeries = value; OnPropertyChanged(); }
        }

        public SeriesCollection ExpiryByDaysSeries
        {
            get => _expiryByDaysSeries;
            set { _expiryByDaysSeries = value; OnPropertyChanged(); }
        }

        public string[] ExpiryByDaysLabels
        {
            get => _expiryByDaysLabels;
            set { _expiryByDaysLabels = value; OnPropertyChanged(); }
        }

        public List<TrendPeriodItem> TrendPeriods
        {
            get => _trendPeriods;
            set { _trendPeriods = value; OnPropertyChanged(); }
        }

        public TrendPeriodItem SelectedTrendPeriod
        {
            get => _selectedTrendPeriod;
            set 
            { 
                _selectedTrendPeriod = value;
                OnPropertyChanged();
                _ = LoadDataAsync();
            }
        }

        public Func<double, string> CurrencyFormatter => value => $"{value:N2} DA";

        public Func<double, string> NumberFormatter => value => value.ToString("N0");

        public ICommand EditProductCommand
        {
            get
            {
                return _editProductCommand ?? (_editProductCommand = new RelayCommand<Product>(async (product) =>
                {
                    if (product != null)
                    {
                        try
                        {
                            // Store the current state before closing the expiry dialog
                            var currentCategory = SelectedCategory;
                            var currentPeriod = SelectedTrendPeriod;

                            // Close the current expiry dialog using the command
                            DialogHost.CloseDialogCommand.Execute(null, null);

                            // Create and show the product edit dialog
                            var dialog = new ProductDialog(_productsViewModel, "RootDialog", product);
                            var result = await DialogHost.Show(dialog, "RootDialog");

                            // After product edit is done, refresh data
                            await LoadDataAsync();

                            // Restore the previous state
                            SelectedCategory = currentCategory;
                            SelectedTrendPeriod = currentPeriod;

                            // Show the expiry stats dialog again
                            var expiryDialog = new ExpiryStatsDetailsDialog(_dashboardViewModel);
                            await DialogHost.Show(expiryDialog, "RootDialog");
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(
                                $"Error editing product: {ex.Message}",
                                "Error",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);

                            // Show the expiry stats dialog again even if there's an error
                            var expiryDialog = new ExpiryStatsDetailsDialog(_dashboardViewModel);
                            await DialogHost.Show(expiryDialog, "RootDialog");
                        }
                    }
                }));
            }
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                var products = await _dbService.GetExpiringProductsAsync();
                
                if (IsCategoryFilterEnabled && SelectedCategory != null)
                {
                    products = products.Where(p => p.CategoryId == SelectedCategory.Id).ToList();
                }

                TotalExpiringProducts = products.Count;
                ExpiredProducts = products.Count(p => p.ExpiryDate.HasValue && p.ExpiryDate.Value <= DateTime.Now);
                NearExpiryProducts = products.Count(p => p.ExpiryDate.HasValue && p.ExpiryDate.Value > DateTime.Now && p.ExpiryDate.Value <= DateTime.Now.AddDays(30));
                PotentialLoss = products.Sum(p => p.StockQuantity * p.PurchasePrice);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    ExpiringProducts.Clear();
                    foreach (var product in products.Where(p => p.ExpiryDate.HasValue)
                        .OrderBy(p => p.ExpiryDate)
                        .Select(p => new ProductWithExpiryStatus(p)))
                    {
                        ExpiringProducts.Add(product);
                    }
                });

                // Load categories if not loaded
                if (!Categories.Any())
                {
                    var categories = await _dbService.GetAllCategoriesAsync();
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        Categories.Clear();
                        foreach (var category in categories)
                        {
                            Categories.Add(category);
                        }
                    });
                }

                // Update charts
                await UpdateChartsAsync(products);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading expiry stats data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task UpdateChartsAsync(List<Product> products)
        {
            try
            {
                // Prepare data on background thread
                var trendData = products
                    .Where(p => p.ExpiryDate.HasValue)
                    .GroupBy(p => p.ExpiryDate.Value.Date)
                    .OrderBy(g => g.Key)
                    .Take(30)
                    .Select(g => new { Date = g.Key, Count = g.Count() })
                    .ToList();

                var trendValues = new ChartValues<int>(trendData.Select(d => d.Count));
                var trendLabels = trendData.Select(d => d.Date.ToString("MMM dd, yyyy")).ToArray();

                var categoryData = products
                    .GroupBy(p => p.CategoryId)
                    .Select(g => new
                    {
                        Category = Categories.FirstOrDefault(c => c.Id == g.Key)?.Name ?? "Uncategorized",
                        Count = g.Count()
                    })
                    .OrderByDescending(x => x.Count)
                    .ToList();

                var daysData = products
                    .Where(p => p.ExpiryDate.HasValue)
                    .GroupBy(p => (p.ExpiryDate.Value.Date - DateTime.Now.Date).Days)
                    .OrderBy(g => g.Key)
                    .Take(7)
                    .Select(g => new { Days = g.Key, Count = g.Count() })
                    .ToList();

                var daysValues = new ChartValues<int>(daysData.Select(d => d.Count));
                var daysLabels = daysData.Select(d => d.Days switch
                {
                    0 => "Today",
                    1 => "Tomorrow",
                    _ => $"In {d.Days} days"
                }).ToArray();

                // Update UI on the dispatcher
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Create and update trend series
                    ExpiryTrendSeries = new SeriesCollection
                    {
                        new LineSeries
                        {
                            Title = "Expiring Products",
                            Values = trendValues,
                            PointGeometry = DefaultGeometries.Circle,
                            PointGeometrySize = 8
                        }
                    };
                    ExpiryTrendLabels = trendLabels;

                    // Create and update category distribution series
                    var categoryColors = new List<Color>
                    {
                        Colors.DodgerBlue,
                        Colors.OrangeRed,
                        Colors.ForestGreen,
                        Colors.Purple,
                        Colors.Gold
                    };

                    var categorySeries = new SeriesCollection();
                    var categoryIndex = 0;
                    foreach (var category in categoryData)
                    {
                        var color = categoryColors[categoryIndex % categoryColors.Count];
                        categorySeries.Add(new PieSeries
                        {
                            Title = category.Category,
                            Values = new ChartValues<int> { category.Count },
                            DataLabels = true,
                            Fill = new SolidColorBrush(color)
                        });
                        categoryIndex++;
                    }
                    CategoryDistributionSeries = categorySeries;

                    // Create and update expiry by days series
                    ExpiryByDaysSeries = new SeriesCollection
                    {
                        new ColumnSeries
                        {
                            Title = "Products",
                            Values = daysValues,
                            DataLabels = true
                        }
                    };
                    ExpiryByDaysLabels = daysLabels;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating charts: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ProductWithExpiryStatus : Product
    {
        public new bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now;
        public bool IsNearlyExpired => ExpiryDate.HasValue && 
                                      ExpiryDate.Value > DateTime.Now && 
                                      ExpiryDate.Value <= DateTime.Now.AddDays(30);

        public ProductWithExpiryStatus(Product product)
        {
            // Copy all properties from the original product
            Id = product.Id;
            Name = product.Name;
            Description = product.Description;
            Barcode = product.Barcode;
            CategoryId = product.CategoryId;
            Category = product.Category;
            PurchasePrice = product.PurchasePrice;
            SellingPrice = product.SellingPrice;
            StockQuantity = product.StockQuantity;
            MinimumStock = product.MinimumStock;
            ExpiryDate = product.ExpiryDate;
            // ... copy any other properties ...
        }
    }

    public class TrendPeriodItem
    {
        public string ResourceKey { get; set; }
        
        public string DisplayName
        {
            get
            {
                return Application.Current.TryFindResource(ResourceKey)?.ToString() ?? ResourceKey;
            }
        }
    }
} 