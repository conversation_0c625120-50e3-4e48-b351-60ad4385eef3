﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace POSSystem.Migrations
{
    /// <inheritdoc />
    public partial class SeedDiscountData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8021), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(7986), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8022) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8033), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8030), new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(8034) });

            migrationBuilder.InsertData(
                table: "DiscountReasons",
                columns: new[] { "Id", "Code", "Description", "IsActive" },
                values: new object[,]
                {
                    { 1, "MANAGER", "Manager Special", true },
                    { 2, "DAMAGED", "Damaged Item", true },
                    { 3, "PRICEMATCH", "Price Match", true },
                    { 4, "CUSTOMER", "Customer Satisfaction", true },
                    { 5, "PROMO", "Promotion", true },
                    { 6, "BULK", "Bulk Purchase", true },
                    { 7, "LOYALTY", "Loyalty Discount", true }
                });

            migrationBuilder.InsertData(
                table: "DiscountTypes",
                columns: new[] { "Id", "Description", "Name" },
                values: new object[,]
                {
                    { 1, "Percentage off the original price", "Percentage" },
                    { 2, "Fixed amount off the original price", "Fixed Amount" },
                    { 3, "Override with a specific price", "Price Override" }
                });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 365, DateTimeKind.Local).AddTicks(2499));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9861));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9867));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 360, DateTimeKind.Local).AddTicks(9872));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6057));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6063));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6075));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6079));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6084));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6088));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6092));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 5, 0, 21, 45, 368, DateTimeKind.Local).AddTicks(6132));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 5, 0, 21, 45, 361, DateTimeKind.Local).AddTicks(5979), new DateTime(2025, 2, 5, 0, 21, 45, 361, DateTimeKind.Local).AddTicks(5989) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "DiscountReasons",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "DiscountTypes",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "DiscountTypes",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "DiscountTypes",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6081), new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6050), new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6082) });

            migrationBuilder.UpdateData(
                table: "Customers",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "LastVisit", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6090), new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6088), new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(6091) });

            migrationBuilder.UpdateData(
                table: "LoyaltyPrograms",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 701, DateTimeKind.Local).AddTicks(9889));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(7346));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(7350));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 698, DateTimeKind.Local).AddTicks(7353));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5789));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5794));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5808));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5812));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5818));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5822));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5827));

            migrationBuilder.UpdateData(
                table: "UnitsOfMeasure",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 2, 4, 21, 43, 9, 707, DateTimeKind.Local).AddTicks(5863));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 2, 4, 21, 43, 9, 699, DateTimeKind.Local).AddTicks(1234), new DateTime(2025, 2, 4, 21, 43, 9, 699, DateTimeKind.Local).AddTicks(1244) });
        }
    }
}
