using System;

namespace POSSystem.Models
{
    /// <summary>
    /// ✅ NEW: Sales metrics data model for dashboard performance optimization
    /// </summary>
    public class SalesMetrics
    {
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue => TransactionCount > 0 ? TotalSales / TransactionCount : 0;
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
    }
}
