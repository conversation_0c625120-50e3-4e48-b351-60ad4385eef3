using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.ViewModels;
using POSSystem.Models;

namespace POSSystem.Converters
{
    public class ItemEditModeConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 2 || values[0] == null || values[1] == null)
                return System.Windows.Visibility.Collapsed;

            if (values[0] is InvoiceViewModel viewModel && values[1] is InvoiceItem item)
            {
                bool isEditing = viewModel.IsItemBeingEdited(item);

                // If parameter is "Inverse", return opposite visibility
                if (parameter?.ToString() == "Inverse")
                {
                    return isEditing ? System.Windows.Visibility.Collapsed : System.Windows.Visibility.Visible;
                }

                // Default: show when editing
                return isEditing ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            }

            return System.Windows.Visibility.Collapsed;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
