# Profit Calculation Standards

## Overview
This document outlines the standardized approach for profit calculations across the POS system, ensuring consistency and accuracy while maintaining performance.

## Standardized Calculation Method

### Primary Method: FIFO Cost Basis
All profit calculations now use the actual cost basis determined by FIFO (First In, First Out) allocation:

```csharp
profit = saleItem.Quantity * (saleItem.UnitPrice - saleItem.ActualCostBasis)
```

### Fallback Method: Product Average Cost
When ActualCostBasis is not available (legacy data or custom products):

```csharp
profit = saleItem.Quantity * (saleItem.UnitPrice - saleItem.Product.PurchasePrice)
```

## Implementation Details

### 1. SaleItem.ActualCostBasis Property
- **Purpose**: Stores the actual cost basis used for each sale item based on FIFO allocation
- **Calculation**: Determined during sale processing using `Product.CalculateFIFOCostBasis(quantity)`
- **Database**: Stored in SaleItems table with precision(18,2)
- **Default**: 0 (triggers fallback to product average cost)

### 2. FIFO Cost Calculation
For batch-tracked products:
- Uses oldest batches first (ordered by Created<PERSON><PERSON>, then Id)
- Calculates weighted average cost based on quantities from each batch
- Handles partial batch consumption accurately

For non-batch products:
- Uses the product's current PurchasePrice

### 3. Performance Considerations

#### Fast Dashboard Loading (Estimated)
For performance-critical dashboard views that need fast loading:
- Uses estimated profit margin (25% default)
- Avoids loading full entity graphs
- Clearly documented as estimates

#### Detailed Views (Exact)
For detailed profit analysis and reports:
- Uses actual FIFO cost basis calculations
- Loads complete sale item data
- Provides accurate profit figures

## Updated Components

### Core Calculation Methods
1. **DashboardModels.CalculateProfit()** - Uses FIFO cost basis
2. **DashboardViewModel.CalculateMetricValue()** - Uses FIFO cost basis
3. **ProfitStatsDetailsViewModel.CalculateProfitForSale()** - Uses FIFO cost basis
4. **UnifiedDataService.GetTotalProfitAsync()** - Uses FIFO cost basis

### Performance-Optimized Methods (Estimated)
1. **DashboardViewModel.UpdateQuickStatsAsync()** - Uses estimated margin for speed
2. **DashboardViewModel.CalculateProfitMetricsValues()** - Falls back to estimated if exact fails

## Migration Strategy

### Existing Data
- SQL migration script updates existing SaleItems with ActualCostBasis = Product.PurchasePrice
- Provides reasonable fallback for historical data
- New sales automatically capture FIFO cost basis

### Backward Compatibility
- System gracefully handles ActualCostBasis = 0 by falling back to product cost
- No breaking changes to existing functionality
- Gradual improvement in accuracy as new sales are processed

## Configuration Options

### Estimated Profit Margin
- Default: 25% (0.25m)
- Location: Various ViewModels (can be centralized in configuration)
- Purpose: Fast dashboard calculations when exact data unavailable

### FIFO vs Average Cost
- System automatically uses FIFO for batch-tracked products
- Falls back to average cost for non-batch products
- Future enhancement: Support for LIFO or weighted average methods

## Benefits

1. **Accuracy**: Actual cost basis reflects real inventory costs
2. **Consistency**: Single standardized calculation method
3. **Performance**: Maintains fast dashboard loading with estimates
4. **Flexibility**: Graceful fallback for missing data
5. **Auditability**: Cost basis stored with each transaction

## Testing Recommendations

1. **Unit Tests**: Verify FIFO cost calculations with various batch scenarios
2. **Integration Tests**: Ensure profit calculations match across all components
3. **Performance Tests**: Validate dashboard loading times remain acceptable
4. **Data Migration Tests**: Verify existing data migrates correctly

## Future Enhancements

1. **Configurable Costing Methods**: Support LIFO, weighted average
2. **Real-time Cost Updates**: Update cost basis when purchase prices change
3. **Profit Margin Analysis**: Compare estimated vs actual margins
4. **Cost Variance Reporting**: Track differences between estimated and actual costs
