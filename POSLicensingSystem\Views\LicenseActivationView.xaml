<Window x:Class="POSLicensingSystem.Views.LicenseActivationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:POSLicensingSystem.ViewModels"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:data="clr-namespace:System.Windows.Data;assembly=PresentationFramework"
        xmlns:converters="clr-namespace:POSLicensingSystem.Converters"
        mc:Ignorable="d"
        Title="POS License Activation" Height="550" Width="650"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        Icon="{StaticResource AppIconImage}"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <converters:BooleanInverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.DataContext>
        <viewmodels:LicenseActivationViewModel />
    </Window.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="POS System License Activation" 
                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,20"/>
            
            <TextBlock Text="Please follow these steps to activate your software:"
                     TextWrapping="Wrap"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,20"/>
        </StackPanel>

        <StackPanel Grid.Row="1" Margin="0,10">
            <!-- Step 1: System ID -->
            <GroupBox Header="Step 1: Generate System ID" 
                      Style="{StaticResource MaterialDesignGroupBox}"
                      Margin="0,0,0,20">
                <StackPanel Margin="0,10">
                    <TextBox Text="{Binding SystemId, Mode=TwoWay}"
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="Your System ID"
                             Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="Generate System ID"
                                Command="{Binding GenerateSystemIdCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"/>
                        
                        <Button Content="Copy System ID"
                                Command="{Binding CopySystemIdCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
            <!-- Step 2: License Data -->
            <GroupBox Header="Step 2: Enter License Information" 
                      Style="{StaticResource MaterialDesignGroupBox}"
                      Margin="0,0,0,20">
                <StackPanel Margin="0,10">
                    <TextBox Text="{Binding BusinessName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="Business Name"
                             Margin="0,0,0,10"
                             IsEnabled="{Binding IsActivating, Converter={StaticResource BooleanInverter}}"/>
                    
                    <TextBox Text="{Binding LicenseKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="License Key (Format: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX)"
                             Margin="0,0,0,10"
                             IsEnabled="{Binding IsActivating, Converter={StaticResource BooleanInverter}}"/>
                    
                    <!-- Activation error message with improved styling -->
                    <materialDesign:Card 
                        Background="#FFEBEE"
                        Padding="8"
                        Margin="0,10,0,0"
                        Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Alert" Foreground="#D32F2F" VerticalAlignment="Top" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                      Foreground="#D32F2F"
                                      TextWrapping="Wrap"/>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </GroupBox>
            
            <!-- Instructions -->
            <GroupBox Header="Instructions" 
                      Style="{StaticResource MaterialDesignGroupBox}">
                <StackPanel Margin="0,10">
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        1. Generate your System ID by clicking the button above.
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        2. Copy your System ID and send it to your system administrator or support team.
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        3. Enter your Business Name and the License Key provided by your administrator.
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap">
                        4. Click the Activate License button to complete activation.
                    </TextBlock>
                </StackPanel>
            </GroupBox>
        </StackPanel>

        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="Exit"
                    Command="{Binding ExitCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"
                    IsEnabled="{Binding IsActivating, Converter={StaticResource BooleanInverter}}"/>
            
            <!-- Normal button (shown when not activating) -->
            <Button Content="Activate License"
                    Command="{Binding ActivateCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Visibility="{Binding IsActivating, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
            
            <!-- Progress button (shown when activating) -->
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                    IsEnabled="False"
                    Visibility="{Binding IsActivating, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Loading" Margin="0,0,8,0">
                        <materialDesign:PackIcon.RenderTransform>
                            <RotateTransform x:Name="pacIconRotation" Angle="0" />
                        </materialDesign:PackIcon.RenderTransform>
                    </materialDesign:PackIcon>
                    <TextBlock Text="Activating..."/>
                </StackPanel>
                <Button.Triggers>
                    <EventTrigger RoutedEvent="Button.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation
                                    Storyboard.TargetName="pacIconRotation"
                                    Storyboard.TargetProperty="Angle"
                                    From="0"
                                    To="360"
                                    Duration="0:0:1"
                                    RepeatBehavior="Forever" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Button.Triggers>
            </Button>
        </StackPanel>
    </Grid>
</Window> 