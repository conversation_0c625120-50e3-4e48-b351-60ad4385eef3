using System;
using System.Globalization;
using System.Windows;
using POSSystem.Converters;

namespace POSSystem
{
    /// <summary>
    /// Simple test program to verify currency format converter works correctly
    /// </summary>
    public class CurrencyFormatTest
    {
        public static void TestCurrencyFormatConverter()
        {
            Console.WriteLine("=== Currency Format Converter Test ===");
            
            try
            {
                // Initialize Application.Current.Resources for testing
                if (Application.Current == null)
                {
                    var app = new Application();
                    app.Resources = new ResourceDictionary();
                }

                var converter = new CurrencyFormatConverter();
                decimal testAmount = 123.45m;

                Console.WriteLine("\n1. Testing English Currency Format:");
                Application.Current.Resources["CurrencyFormat"] = "{0:N2} DA";
                Application.Current.Resources["CurrencySymbol"] = "DA";
                
                var englishResult = converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: {testAmount}");
                Console.WriteLine($"   Output: {englishResult}");
                Console.WriteLine($"   Expected: 123.45 DA");
                Console.WriteLine($"   ✓ Test {(englishResult.ToString() == "123.45 DA" ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n2. Testing Arabic Currency Format:");
                Application.Current.Resources["CurrencyFormat"] = "{0:N2} د.ج";
                Application.Current.Resources["CurrencySymbol"] = "د.ج";
                
                var arabicResult = converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: {testAmount}");
                Console.WriteLine($"   Output: {arabicResult}");
                Console.WriteLine($"   Expected: 123.45 د.ج");
                Console.WriteLine($"   ✓ Test {(arabicResult.ToString() == "123.45 د.ج" ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n3. Testing Fallback (No CurrencyFormat):");
                Application.Current.Resources.Remove("CurrencyFormat");
                Application.Current.Resources["CurrencySymbol"] = "DA";
                
                var fallbackResult = converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: {testAmount}");
                Console.WriteLine($"   Output: {fallbackResult}");
                Console.WriteLine($"   Expected: 123.45 DA");
                Console.WriteLine($"   ✓ Test {(fallbackResult.ToString() == "123.45 DA" ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n4. Testing Default Fallback (No CurrencySymbol):");
                Application.Current.Resources.Remove("CurrencySymbol");
                
                var defaultResult = converter.Convert(testAmount, typeof(string), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: {testAmount}");
                Console.WriteLine($"   Output: {defaultResult}");
                Console.WriteLine($"   Expected: 123.45 DA");
                Console.WriteLine($"   ✓ Test {(defaultResult.ToString() == "123.45 DA" ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n5. Testing Null Value:");
                Application.Current.Resources["CurrencySymbol"] = "DA";
                
                var nullResult = converter.Convert(null, typeof(string), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: null");
                Console.WriteLine($"   Output: {nullResult}");
                Console.WriteLine($"   Expected: 0.00 DA");
                Console.WriteLine($"   ✓ Test {(nullResult.ToString() == "0.00 DA" ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n6. Testing ConvertBack:");
                var backResult = converter.ConvertBack("123.45 DA", typeof(decimal), null, CultureInfo.InvariantCulture);
                Console.WriteLine($"   Input: \"123.45 DA\"");
                Console.WriteLine($"   Output: {backResult}");
                Console.WriteLine($"   Expected: 123.45");
                Console.WriteLine($"   ✓ Test {(backResult.Equals(123.45m) ? "PASSED" : "FAILED")}");

                Console.WriteLine("\n=== All Tests Completed ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed with exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
