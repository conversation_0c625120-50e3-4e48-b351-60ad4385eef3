using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using POSSystem.ViewModels.Dashboard.Commands;
using POSSystem.ViewModels.Dashboard.Services;
using static POSSystem.ViewModels.Dashboard.Commands.DashboardCommandManager;

namespace POSSystem.ViewModels.Dashboard.State
{
    /// <summary>
    /// Manages the state of the dashboard, providing centralized state management
    /// and property change notifications for the dashboard interface.
    /// </summary>
    /// <remarks>
    /// This class extracts state management logic from the main DashboardViewModel to:
    /// - Centralize state management in a single location
    /// - Provide better organization of state-related properties
    /// - Enable easier testing of state changes
    /// - Improve maintainability by separating concerns
    /// </remarks>
    public class DashboardStateManager : INotifyPropertyChanged
    {
        #region Private Fields

        private bool _isLoading = false;
        private bool _isRefreshing = false;
        private string _loadingMessage = string.Empty;
        private DashboardTimePeriod _selectedPeriod = DashboardTimePeriod.Today;
        private DateTime _startDate = DateTime.Today;
        private DateTime _endDate = DateTime.Today;
        private bool _isDetailViewVisible = false;
        private string _detailViewTitle = string.Empty;
        private object _detailViewContent = null;
        private ChartType _selectedChartType = ChartType.Line;
        private bool _isChartZoomed = false;
        private string _errorMessage = string.Empty;
        private bool _hasError = false;
        private SalesMetrics _salesMetrics;
        private ProfitMetrics _profitMetrics;
        private TopProductsData _topProducts;
        private CustomerAnalytics _customerAnalytics;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets whether the dashboard is currently loading data.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets whether the dashboard is currently refreshing data.
        /// </summary>
        public bool IsRefreshing
        {
            get => _isRefreshing;
            set => SetProperty(ref _isRefreshing, value);
        }

        /// <summary>
        /// Gets or sets the loading message displayed to the user.
        /// </summary>
        public string LoadingMessage
        {
            get => _loadingMessage;
            set => SetProperty(ref _loadingMessage, value);
        }

        /// <summary>
        /// Gets or sets the currently selected time period for dashboard metrics.
        /// </summary>
        public DashboardTimePeriod SelectedPeriod
        {
            get => _selectedPeriod;
            set
            {
                if (SetProperty(ref _selectedPeriod, value))
                {
                    UpdateDateRangeForPeriod(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets the start date for the current date range filter.
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        /// <summary>
        /// Gets or sets the end date for the current date range filter.
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        /// <summary>
        /// Gets or sets whether the detail view is currently visible.
        /// </summary>
        public bool IsDetailViewVisible
        {
            get => _isDetailViewVisible;
            set => SetProperty(ref _isDetailViewVisible, value);
        }

        /// <summary>
        /// Gets or sets the title of the detail view.
        /// </summary>
        public string DetailViewTitle
        {
            get => _detailViewTitle;
            set => SetProperty(ref _detailViewTitle, value);
        }

        /// <summary>
        /// Gets or sets the content of the detail view.
        /// </summary>
        public object DetailViewContent
        {
            get => _detailViewContent;
            set => SetProperty(ref _detailViewContent, value);
        }

        /// <summary>
        /// Gets or sets the currently selected chart type.
        /// </summary>
        public ChartType SelectedChartType
        {
            get => _selectedChartType;
            set => SetProperty(ref _selectedChartType, value);
        }

        /// <summary>
        /// Gets or sets whether charts are currently zoomed.
        /// </summary>
        public bool IsChartZoomed
        {
            get => _isChartZoomed;
            set => SetProperty(ref _isChartZoomed, value);
        }

        /// <summary>
        /// Gets or sets the current error message, if any.
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (SetProperty(ref _errorMessage, value))
                {
                    HasError = !string.IsNullOrEmpty(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets whether there is currently an error state.
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// Gets or sets the current sales metrics data.
        /// </summary>
        public SalesMetrics SalesMetrics
        {
            get => _salesMetrics;
            set => SetProperty(ref _salesMetrics, value);
        }

        /// <summary>
        /// Gets or sets the current profit metrics data.
        /// </summary>
        public ProfitMetrics ProfitMetrics
        {
            get => _profitMetrics;
            set => SetProperty(ref _profitMetrics, value);
        }

        /// <summary>
        /// Gets or sets the current top products data.
        /// </summary>
        public TopProductsData TopProducts
        {
            get => _topProducts;
            set => SetProperty(ref _topProducts, value);
        }

        /// <summary>
        /// Gets or sets the current customer analytics data.
        /// </summary>
        public CustomerAnalytics CustomerAnalytics
        {
            get => _customerAnalytics;
            set => SetProperty(ref _customerAnalytics, value);
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets whether the dashboard is currently busy (loading or refreshing).
        /// </summary>
        public bool IsBusy => IsLoading || IsRefreshing;

        /// <summary>
        /// Gets the current date range as a formatted string.
        /// </summary>
        public string DateRangeText
        {
            get
            {
                if (StartDate.Date == EndDate.Date)
                {
                    return StartDate.ToString("MMM dd, yyyy");
                }
                return $"{StartDate:MMM dd, yyyy} - {EndDate:MMM dd, yyyy}";
            }
        }

        /// <summary>
        /// Gets the period display text for the UI.
        /// </summary>
        public string PeriodDisplayText
        {
            get
            {
                return SelectedPeriod switch
                {
                    DashboardTimePeriod.Today => "Today's",
                    DashboardTimePeriod.Week => "Week's",
                    DashboardTimePeriod.Month => "Month's",
                    DashboardTimePeriod.Quarter => "Quarter's",
                    DashboardTimePeriod.Year => "Year's",
                    DashboardTimePeriod.Custom => "Custom Period",
                    _ => "Today's"
                };
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the loading state with an optional message.
        /// </summary>
        /// <param name="isLoading">Whether the dashboard is loading</param>
        /// <param name="message">Optional loading message</param>
        public void SetLoadingState(bool isLoading, string message = "")
        {
            IsLoading = isLoading;
            LoadingMessage = message;
        }

        /// <summary>
        /// Sets the refreshing state with an optional message.
        /// </summary>
        /// <param name="isRefreshing">Whether the dashboard is refreshing</param>
        /// <param name="message">Optional refreshing message</param>
        public void SetRefreshingState(bool isRefreshing, string message = "")
        {
            IsRefreshing = isRefreshing;
            if (isRefreshing && !string.IsNullOrEmpty(message))
            {
                LoadingMessage = message;
            }
        }

        /// <summary>
        /// Sets the error state with a message.
        /// </summary>
        /// <param name="errorMessage">The error message to display</param>
        public void SetErrorState(string errorMessage)
        {
            ErrorMessage = errorMessage;
            IsLoading = false;
            IsRefreshing = false;
        }

        /// <summary>
        /// Clears the current error state.
        /// </summary>
        public void ClearErrorState()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        /// <summary>
        /// Shows the detail view with specified title and content.
        /// </summary>
        /// <param name="title">The title for the detail view</param>
        /// <param name="content">The content to display in the detail view</param>
        public void ShowDetailView(string title, object content)
        {
            DetailViewTitle = title;
            DetailViewContent = content;
            IsDetailViewVisible = true;
        }

        /// <summary>
        /// Hides the detail view and clears its content.
        /// </summary>
        public void HideDetailView()
        {
            IsDetailViewVisible = false;
            DetailViewTitle = string.Empty;
            DetailViewContent = null;
        }

        /// <summary>
        /// Resets the dashboard state to default values.
        /// </summary>
        public void ResetToDefaults()
        {
            SelectedPeriod = DashboardTimePeriod.Today;
            SelectedChartType = ChartType.Line;
            IsChartZoomed = false;
            HideDetailView();
            ClearErrorState();
            SetLoadingState(false);
            SetRefreshingState(false);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Updates the date range based on the selected time period.
        /// </summary>
        /// <param name="period">The time period to apply</param>
        private void UpdateDateRangeForPeriod(DashboardTimePeriod period)
        {
            var today = DateTime.Today;
            
            switch (period)
            {
                case DashboardTimePeriod.Today:
                    StartDate = today;
                    EndDate = today;
                    break;
                case DashboardTimePeriod.Week:
                    StartDate = today.AddDays(-7);
                    EndDate = today;
                    break;
                case DashboardTimePeriod.Month:
                    StartDate = today.AddDays(-30);
                    EndDate = today;
                    break;
                case DashboardTimePeriod.Quarter:
                    StartDate = today.AddDays(-90);
                    EndDate = today;
                    break;
                case DashboardTimePeriod.Year:
                    StartDate = today.AddDays(-365);
                    EndDate = today;
                    break;
                // Custom period doesn't change dates automatically
            }

            // Notify that computed properties may have changed
            OnPropertyChanged(nameof(DateRangeText));
            OnPropertyChanged(nameof(PeriodDisplayText));
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
