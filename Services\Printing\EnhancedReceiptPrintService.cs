using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Models.Printing;
using POSSystem.Services.Interfaces;
using POSSystem.Helpers;
using POSSystem.Data;

namespace POSSystem.Services.Printing
{
    /// <summary>
    /// Enhanced receipt printing service with support for multiple printer types,
    /// configurable templates, PDF generation, and robust error handling
    /// </summary>
    public class EnhancedReceiptPrintService : IEnhancedReceiptPrintService
    {
        private readonly DatabaseService _dbService;
        private readonly SettingsService _settingsService;
        private readonly ILogger<EnhancedReceiptPrintService> _logger;
        private readonly ReceiptPdfExportService _pdfExportService;
        
        // Default settings
        private ReceiptPrintSettings _printSettings;
        private readonly object _settingsLock = new object();
        
        public EnhancedReceiptPrintService(
            DatabaseService dbService = null,
            SettingsService settingsService = null,
            ILogger<EnhancedReceiptPrintService> logger = null)
        {
            _dbService = dbService ?? new DatabaseService();
            _settingsService = settingsService ?? new SettingsService();
            _logger = logger;
            _pdfExportService = new ReceiptPdfExportService();

            InitializeReceiptPrintingAsync();
        }

        private async void InitializeReceiptPrintingAsync()
        {
            try
            {
                // Initialize database tables and default data
                using var context = new POSDbContext();
                var initializer = new ReceiptPrintingDatabaseInitializer(context);
                await initializer.InitializeAsync();

                // Load print settings after initialization
                LoadPrintSettings();
            }
            catch (Exception ex)
            {
                LogError($"Error initializing receipt printing system: {ex.Message}", ex);
                // Load default settings as fallback
                LoadPrintSettings();
            }
        }
        
        /// <summary>
        /// Print receipt for a sale with automatic printer selection
        /// </summary>
        public async Task<bool> PrintReceiptAsync(Sale sale, bool showDialog = false)
        {
            try
            {
                if (sale == null)
                {
                    LogError("Cannot print receipt: Sale is null");
                    return false;
                }
                
                var printerConfig = await GetDefaultPrinterConfigAsync();
                var template = await GetDefaultReceiptTemplateAsync();
                
                return await PrintReceiptAsync(sale, printerConfig, template, showDialog);
            }
            catch (Exception ex)
            {
                LogError($"Error printing receipt for sale {sale?.Id}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Print receipt with specific printer configuration and template
        /// </summary>
        public async Task<bool> PrintReceiptAsync(Sale sale, PrinterConfiguration printerConfig, 
            ReceiptTemplate template, bool showDialog = false)
        {
            try
            {
                if (sale == null || printerConfig == null || template == null)
                {
                    LogError("Cannot print receipt: Missing required parameters");
                    return false;
                }
                
                // Create print job record
                var printJob = await CreatePrintJobAsync(sale, printerConfig, template);
                
                try
                {
                    printJob.StartedAt = DateTime.Now;
                    printJob.Status = "Printing";
                    await UpdatePrintJobAsync(printJob);
                    
                    // Generate receipt document
                    var document = await CreateReceiptDocumentAsync(sale, template);
                    
                    // Print based on printer type
                    bool success = false;
                    switch (printerConfig.PrinterType.ToLower())
                    {
                        case "thermal":
                            success = await PrintThermalReceiptAsync(document, printerConfig, showDialog);
                            break;
                        case "standard":
                            success = await PrintStandardReceiptAsync(document, printerConfig, showDialog);
                            break;
                        case "pdf":
                            success = await SaveReceiptAsPdfAsync(document, sale, printerConfig);
                            break;
                        default:
                            success = await PrintStandardReceiptAsync(document, printerConfig, showDialog);
                            break;
                    }
                    
                    // Update print job status
                    printJob.CompletedAt = DateTime.Now;
                    printJob.Status = success ? "Completed" : "Failed";
                    if (!success)
                    {
                        printJob.ErrorMessage = "Print operation failed";
                    }
                    
                    await UpdatePrintJobAsync(printJob);
                    
                    // Save PDF backup if enabled
                    if (success && _printSettings.SaveAsPdfBackup)
                    {
                        try
                        {
                            bool backupSuccess = await SaveReceiptAsPdfBackupAsync(document, sale);
                            if (backupSuccess)
                            {
                                LogInfo($"PDF backup saved successfully for sale {sale.Id}");
                            }
                            else
                            {
                                LogError($"PDF backup failed for sale {sale.Id} - but printing was successful");
                            }
                        }
                        catch (Exception backupEx)
                        {
                            LogError($"PDF backup error for sale {sale.Id}: {backupEx.Message}", backupEx);
                            // Don't fail the entire print operation due to backup failure
                        }
                    }
                    
                    return success;
                }
                catch (Exception ex)
                {
                    printJob.Status = "Failed";
                    printJob.ErrorMessage = ex.Message;
                    printJob.CompletedAt = DateTime.Now;
                    await UpdatePrintJobAsync(printJob);
                    throw;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error printing receipt for sale {sale.Id}: {ex.Message}", ex);
                
                // Show user-friendly error message
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        $"Failed to print receipt: {ex.Message}\n\nPlease check your printer connection and try again.",
                        "Print Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                });
                
                return false;
            }
        }
        
        /// <summary>
        /// Preview receipt before printing
        /// </summary>
        public async Task<bool> PreviewReceiptAsync(Sale sale, ReceiptTemplate template = null)
        {
            try
            {
                return await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var previewWindow = new Views.Dialogs.ReceiptPreviewWindow(sale, this);
                    previewWindow.Owner = Application.Current.MainWindow;
                    return previewWindow.ShowDialog() == true;
                });
            }
            catch (Exception ex)
            {
                LogError($"Error previewing receipt for sale {sale.Id}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Save receipt as PDF file
        /// </summary>
        public async Task<bool> SaveReceiptAsPdfAsync(Sale sale, string filePath, ReceiptTemplate template = null)
        {
            try
            {
                template = template ?? await GetDefaultReceiptTemplateAsync();
                var document = await CreateReceiptDocumentAsync(sale, template);

                // Validate export path
                if (!_pdfExportService.ValidateAndCreateExportPath(filePath, out string errorMessage))
                {
                    LogError($"Invalid export path: {errorMessage}");

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(
                            $"Invalid export path: {errorMessage}",
                            "Export Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    });

                    return false;
                }

                // Use unique file name if file already exists
                var uniqueFilePath = _pdfExportService.GetUniqueFileName(filePath);

                // Export to PDF (currently XPS format)
                return await _pdfExportService.ExportReceiptToPdfAsync(document, uniqueFilePath, sale);
            }
            catch (Exception ex)
            {
                LogError($"Error saving receipt as PDF for sale {sale.Id}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Get available printer configurations
        /// </summary>
        public async Task<List<PrinterConfiguration>> GetPrinterConfigurationsAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return context.Set<PrinterConfiguration>()
                    .Where(p => p.IsActive)
                    .Include(p => p.ReceiptTemplate)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogError($"Error getting printer configurations: {ex.Message}", ex);
                return new List<PrinterConfiguration>();
            }
        }
        
        /// <summary>
        /// Get available receipt templates
        /// </summary>
        public async Task<List<ReceiptTemplate>> GetReceiptTemplatesAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return context.Set<ReceiptTemplate>()
                    .Where(t => t.IsActive)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogError($"Error getting receipt templates: {ex.Message}", ex);
                return new List<ReceiptTemplate>();
            }
        }
        
        /// <summary>
        /// Get print job history for a sale
        /// </summary>
        public async Task<List<ReceiptPrintJob>> GetPrintJobHistoryAsync(int saleId)
        {
            try
            {
                using var context = new POSDbContext();
                return context.Set<ReceiptPrintJob>()
                    .Where(j => j.SaleId == saleId)
                    .Include(j => j.PrinterConfig)
                    .Include(j => j.ReceiptTemplate)
                    .Include(j => j.User)
                    .OrderByDescending(j => j.CreatedAt)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogError($"Error getting print job history for sale {saleId}: {ex.Message}", ex);
                return new List<ReceiptPrintJob>();
            }
        }
        
        private async Task<PrinterConfiguration> GetDefaultPrinterConfigAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await Task.FromResult(
                    context.Set<PrinterConfiguration>()
                        .Include(p => p.ReceiptTemplate)
                        .FirstOrDefault(p => p.IsDefault && p.IsActive) ??
                    context.Set<PrinterConfiguration>()
                        .Include(p => p.ReceiptTemplate)
                        .FirstOrDefault(p => p.IsActive)
                );
            }
            catch (Exception ex)
            {
                LogError($"Error getting default printer config: {ex.Message}", ex);
                return null;
            }
        }

        private async Task<ReceiptTemplate> GetDefaultReceiptTemplateAsync()
        {
            try
            {
                using var context = new POSDbContext();
                return await Task.FromResult(
                    context.Set<ReceiptTemplate>()
                        .FirstOrDefault(t => t.IsDefault && t.IsActive) ??
                    context.Set<ReceiptTemplate>()
                        .FirstOrDefault(t => t.IsActive)
                );
            }
            catch (Exception ex)
            {
                LogError($"Error getting default receipt template: {ex.Message}", ex);
                return null;
            }
        }

        private async Task<ReceiptPrintJob> CreatePrintJobAsync(Sale sale, PrinterConfiguration printerConfig, ReceiptTemplate template)
        {
            try
            {
                var currentUser = _dbService.GetDefaultUser();
                var printJob = new ReceiptPrintJob
                {
                    SaleId = sale.Id,
                    PrinterConfigId = printerConfig.Id,
                    ReceiptTemplateId = template.Id,
                    Status = "Pending",
                    UserId = currentUser?.Id ?? 1,
                    CreatedAt = DateTime.Now
                };

                using var context = new POSDbContext();
                context.Set<ReceiptPrintJob>().Add(printJob);
                await context.SaveChangesAsync();

                return printJob;
            }
            catch (Exception ex)
            {
                LogError($"Error creating print job: {ex.Message}", ex);
                throw;
            }
        }

        private async Task UpdatePrintJobAsync(ReceiptPrintJob printJob)
        {
            try
            {
                using var context = new POSDbContext();
                context.Set<ReceiptPrintJob>().Update(printJob);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                LogError($"Error updating print job: {ex.Message}", ex);
            }
        }

        private async Task<FlowDocument> CreateReceiptDocumentAsync(Sale sale, ReceiptTemplate template)
        {
            return await Task.Run(() =>
            {
                var document = new FlowDocument();

                // Enhanced document styling
                document.PageWidth = template.PaperWidth * 8; // Convert to pixels
                document.FontSize = template.FontSize;
                document.FontFamily = new FontFamily("Segoe UI, Arial, sans-serif"); // Modern, readable font
                document.PagePadding = new Thickness(20, 15, 20, 15); // Better margins
                document.LineHeight = 1.2; // Improved line spacing
                document.Background = Brushes.White;

                // Add company header if enabled
                if (template.IncludeCompanyInfo)
                {
                    AddEnhancedCompanyHeader(document, template);
                }

                // Add receipt header with better formatting
                AddEnhancedReceiptHeader(document, sale);

                // Add customer info if enabled
                if (template.IncludeCustomerInfo && sale.Customer != null)
                {
                    AddEnhancedCustomerInfo(document, sale.Customer);
                }

                // Add items with improved table layout
                if (template.IncludeItemDetails)
                {
                    AddEnhancedItemDetails(document, sale);
                }

                // Add enhanced totals section
                AddEnhancedTotals(document, sale);

                // Add payment info if enabled
                if (template.IncludePaymentInfo)
                {
                    AddEnhancedPaymentInfo(document, sale);
                }

                // Add professional footer
                if (!string.IsNullOrEmpty(template.FooterText))
                {
                    AddEnhancedFooter(document, template.FooterText);
                }

                return document;
            });
        }

        private void LoadPrintSettings()
        {
            lock (_settingsLock)
            {
                try
                {
                    using var context = new POSDbContext();

                    // Check if the ReceiptPrintSettings table exists
                    if (context.Database.CanConnect())
                    {
                        _printSettings = context.Set<ReceiptPrintSettings>().FirstOrDefault();
                    }

                    // If no settings found or database issue, create default settings
                    if (_printSettings == null)
                    {
                        _printSettings = CreateDefaultPrintSettings();
                        LogInfo("Using default print settings - database table may not exist yet");
                    }
                    else
                    {
                        LogInfo("Print settings loaded successfully from database");
                    }
                }
                catch (Exception ex)
                {
                    LogError($"Error loading print settings: {ex.Message}", ex);
                    _printSettings = CreateDefaultPrintSettings();
                    LogInfo("Using default print settings due to database error");
                }
            }
        }

        private ReceiptPrintSettings CreateDefaultPrintSettings()
        {
            var defaultPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "POS Receipts"
            );

            // Ensure the default directory exists
            try
            {
                Directory.CreateDirectory(defaultPath);
            }
            catch (Exception ex)
            {
                LogError($"Could not create default PDF backup directory: {ex.Message}", ex);
                defaultPath = Path.GetTempPath(); // Fallback to temp directory
            }

            return new ReceiptPrintSettings
            {
                AutoPrintEnabled = true,
                ShowPrintDialog = false,
                SaveAsPdfBackup = false,
                PdfBackupPath = defaultPath,
                EnablePrintPreview = true,
                PrintTimeoutSeconds = 30,
                RetryFailedPrints = true,
                MaxRetryAttempts = 3
            };
        }

        /// <summary>
        /// Get current receipt print settings
        /// </summary>
        public ReceiptPrintSettings GetPrintSettings()
        {
            lock (_settingsLock)
            {
                if (_printSettings == null)
                {
                    LoadPrintSettings();
                }
                return _printSettings;
            }
        }

        /// <summary>
        /// Update receipt print settings
        /// </summary>
        public async Task<bool> UpdatePrintSettingsAsync(ReceiptPrintSettings settings)
        {
            try
            {
                lock (_settingsLock)
                {
                    using var context = new POSDbContext();

                    // Check if settings exist
                    var existingSettings = context.Set<ReceiptPrintSettings>().FirstOrDefault();

                    if (existingSettings != null)
                    {
                        // Update existing settings
                        existingSettings.AutoPrintEnabled = settings.AutoPrintEnabled;
                        existingSettings.ShowPrintDialog = settings.ShowPrintDialog;
                        existingSettings.SaveAsPdfBackup = settings.SaveAsPdfBackup;
                        existingSettings.PdfBackupPath = settings.PdfBackupPath;
                        existingSettings.EnablePrintPreview = settings.EnablePrintPreview;
                        existingSettings.PrintTimeoutSeconds = settings.PrintTimeoutSeconds;
                        existingSettings.RetryFailedPrints = settings.RetryFailedPrints;
                        existingSettings.MaxRetryAttempts = settings.MaxRetryAttempts;
                        existingSettings.DefaultPrinterConfigId = settings.DefaultPrinterConfigId;
                        existingSettings.DefaultReceiptTemplateId = settings.DefaultReceiptTemplateId;

                        context.Set<ReceiptPrintSettings>().Update(existingSettings);
                    }
                    else
                    {
                        // Create new settings
                        context.Set<ReceiptPrintSettings>().Add(settings);
                    }

                    context.SaveChanges();

                    // Update cached settings
                    _printSettings = existingSettings ?? settings;

                    LogInfo("Print settings updated successfully");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error updating print settings: {ex.Message}", ex);
                return false;
            }
        }

        private void LogError(string message, Exception ex = null)
        {
            _logger?.LogError(ex, message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT ERROR] {message}");
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT ERROR] Exception: {ex}");
            }
        }

        private void LogInfo(string message)
        {
            _logger?.LogInformation(message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT PRINT INFO] {message}");
        }

        private void AddEnhancedCompanyHeader(FlowDocument document, ReceiptTemplate template)
        {
            var companyName = _settingsService.GetSetting("CompanyName") ?? "Your Business Name";
            var companyAddress = _settingsService.GetSetting("CompanyAddress") ?? "";
            var companyPhone = _settingsService.GetSetting("CompanyPhone") ?? "";
            var companyEmail = _settingsService.GetSetting("CompanyEmail") ?? "";
            var companyWebsite = _settingsService.GetSetting("CompanyWebsite") ?? "";

            // Company name with enhanced styling
            var companyNameParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8),
                LineHeight = 1.1
            };

            var companyNameRun = new Run(companyName.ToUpper())
            {
                FontWeight = FontWeights.Bold,
                FontSize = template.FontSize + 6,
                Foreground = Brushes.Black
            };
            companyNameParagraph.Inlines.Add(companyNameRun);
            document.Blocks.Add(companyNameParagraph);

            // Company details with better formatting
            if (!string.IsNullOrEmpty(companyAddress) || !string.IsNullOrEmpty(companyPhone) ||
                !string.IsNullOrEmpty(companyEmail) || !string.IsNullOrEmpty(companyWebsite))
            {
                var detailsParagraph = new Paragraph
                {
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 15),
                    FontSize = template.FontSize - 1,
                    LineHeight = 1.3
                };

                bool hasDetails = false;

                if (!string.IsNullOrEmpty(companyAddress))
                {
                    detailsParagraph.Inlines.Add(new Run(companyAddress) { Foreground = Brushes.DarkGray });
                    hasDetails = true;
                }

                if (!string.IsNullOrEmpty(companyPhone))
                {
                    if (hasDetails) detailsParagraph.Inlines.Add(new LineBreak());
                    detailsParagraph.Inlines.Add(new Run($"Tel: {companyPhone}") { Foreground = Brushes.DarkGray });
                    hasDetails = true;
                }

                if (!string.IsNullOrEmpty(companyEmail))
                {
                    if (hasDetails) detailsParagraph.Inlines.Add(new LineBreak());
                    detailsParagraph.Inlines.Add(new Run($"Email: {companyEmail}") { Foreground = Brushes.DarkGray });
                    hasDetails = true;
                }

                if (!string.IsNullOrEmpty(companyWebsite))
                {
                    if (hasDetails) detailsParagraph.Inlines.Add(new LineBreak());
                    detailsParagraph.Inlines.Add(new Run(companyWebsite) { Foreground = Brushes.DarkGray });
                }

                document.Blocks.Add(detailsParagraph);
            }

            // Professional separator
            AddStyledSeparator(document, SeparatorStyle.Double);
        }

        private void AddEnhancedReceiptHeader(FlowDocument document, Sale sale)
        {
            // Receipt title with enhanced styling
            var titleParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 10, 0, 15),
                LineHeight = 1.2
            };

            var receiptTitle = new Run("SALES RECEIPT")
            {
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Foreground = Brushes.Black
            };
            titleParagraph.Inlines.Add(receiptTitle);
            document.Blocks.Add(titleParagraph);

            // Receipt details in a structured format
            var detailsTable = new Table
            {
                CellSpacing = 0,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 0, 15)
            };

            // Define columns for receipt details
            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            detailsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var rowGroup = new TableRowGroup();

            // Invoice number row
            var invoiceRow = new TableRow();
            invoiceRow.Cells.Add(CreateDetailCell("Receipt No:", true));
            invoiceRow.Cells.Add(CreateDetailCell(sale.InvoiceNumber ?? $"RCP-{sale.Id:D6}", false, TextAlignment.Right));
            rowGroup.Rows.Add(invoiceRow);

            // Date and time row
            var dateRow = new TableRow();
            dateRow.Cells.Add(CreateDetailCell("Date & Time:", true));
            dateRow.Cells.Add(CreateDetailCell(sale.SaleDate.ToString("dd/MM/yyyy HH:mm"), false, TextAlignment.Right));
            rowGroup.Rows.Add(dateRow);

            // Cashier row (if available)
            if (sale.User != null)
            {
                var cashierRow = new TableRow();
                cashierRow.Cells.Add(CreateDetailCell("Cashier:", true));
                var cashierName = !string.IsNullOrEmpty(sale.User.FirstName) && !string.IsNullOrEmpty(sale.User.LastName)
                    ? $"{sale.User.FirstName} {sale.User.LastName}"
                    : sale.User.Username;
                cashierRow.Cells.Add(CreateDetailCell(cashierName, false, TextAlignment.Right));
                rowGroup.Rows.Add(cashierRow);
            }

            detailsTable.RowGroups.Add(rowGroup);
            document.Blocks.Add(detailsTable);

            AddStyledSeparator(document, SeparatorStyle.Single);
        }

        private void AddEnhancedCustomerInfo(FlowDocument document, Customer customer)
        {
            // Customer section header
            var customerHeaderParagraph = new Paragraph
            {
                Margin = new Thickness(0, 10, 0, 5),
                FontWeight = FontWeights.SemiBold,
                FontSize = 13
            };
            customerHeaderParagraph.Inlines.Add(new Run("CUSTOMER INFORMATION"));
            document.Blocks.Add(customerHeaderParagraph);

            // Customer details table
            var customerTable = new Table
            {
                CellSpacing = 0,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 0, 15)
            };

            customerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            customerTable.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });

            var rowGroup = new TableRowGroup();

            // Customer name
            var nameRow = new TableRow();
            nameRow.Cells.Add(CreateDetailCell("Name:", true));
            nameRow.Cells.Add(CreateDetailCell(customer.FullName, false, TextAlignment.Left));
            rowGroup.Rows.Add(nameRow);

            // Phone number
            if (!string.IsNullOrEmpty(customer.Phone))
            {
                var phoneRow = new TableRow();
                phoneRow.Cells.Add(CreateDetailCell("Phone:", true));
                phoneRow.Cells.Add(CreateDetailCell(customer.Phone, false, TextAlignment.Left));
                rowGroup.Rows.Add(phoneRow);
            }

            // Email
            if (!string.IsNullOrEmpty(customer.Email))
            {
                var emailRow = new TableRow();
                emailRow.Cells.Add(CreateDetailCell("Email:", true));
                emailRow.Cells.Add(CreateDetailCell(customer.Email, false, TextAlignment.Left));
                rowGroup.Rows.Add(emailRow);
            }

            // Loyalty points (if applicable)
            if (customer.LoyaltyPoints > 0)
            {
                var loyaltyRow = new TableRow();
                loyaltyRow.Cells.Add(CreateDetailCell("Loyalty Points:", true));
                loyaltyRow.Cells.Add(CreateDetailCell(customer.LoyaltyPoints.ToString("N0"), false, TextAlignment.Left));
                rowGroup.Rows.Add(loyaltyRow);
            }

            customerTable.RowGroups.Add(rowGroup);
            document.Blocks.Add(customerTable);

            AddStyledSeparator(document, SeparatorStyle.Single);
        }

        private void AddEnhancedItemDetails(FlowDocument document, Sale sale)
        {
            // Items section header
            var itemsHeaderParagraph = new Paragraph
            {
                Margin = new Thickness(0, 15, 0, 10),
                FontWeight = FontWeights.SemiBold,
                FontSize = 13
            };
            itemsHeaderParagraph.Inlines.Add(new Run("ITEMS PURCHASED"));
            document.Blocks.Add(itemsHeaderParagraph);

            var table = new Table
            {
                CellSpacing = 0,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 0, 15)
            };

            // Enhanced column definitions with better proportions
            table.Columns.Add(new TableColumn { Width = new GridLength(3.5, GridUnitType.Star) }); // Item
            table.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) }); // Qty
            table.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) }); // Price
            table.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) }); // Total

            var rowGroup = new TableRowGroup();

            // Enhanced header row with better styling
            var headerRow = new TableRow { Background = Brushes.LightGray };
            headerRow.Cells.Add(CreateEnhancedTableCell("ITEM", true, TextAlignment.Left, true));
            headerRow.Cells.Add(CreateEnhancedTableCell("QTY", true, TextAlignment.Center, true));
            headerRow.Cells.Add(CreateEnhancedTableCell("PRICE", true, TextAlignment.Right, true));
            headerRow.Cells.Add(CreateEnhancedTableCell("TOTAL", true, TextAlignment.Right, true));
            rowGroup.Rows.Add(headerRow);

            // Item rows with enhanced formatting
            bool alternateRow = false;
            foreach (var item in sale.Items)
            {
                var row = new TableRow();
                if (alternateRow)
                {
                    row.Background = new SolidColorBrush(Color.FromRgb(248, 248, 248)); // Very light gray
                }

                // Item name only (no product code for compact display)
                var itemName = item.Product?.Name ?? "Unknown Item";

                row.Cells.Add(CreateEnhancedTableCell(itemName, false, TextAlignment.Left));
                row.Cells.Add(CreateEnhancedTableCell(item.Quantity.ToString("N0"), false, TextAlignment.Center));
                row.Cells.Add(CreateEnhancedTableCell($"{item.UnitPrice:N2}", false, TextAlignment.Right));
                row.Cells.Add(CreateEnhancedTableCell($"{item.Total:N2}", false, TextAlignment.Right, false, true));

                rowGroup.Rows.Add(row);
                alternateRow = !alternateRow;
            }

            table.RowGroups.Add(rowGroup);
            document.Blocks.Add(table);

            AddStyledSeparator(document, SeparatorStyle.Single);
        }

        private TableCell CreateTableCell(string text, bool isHeader = false)
        {
            var cell = new TableCell();
            var paragraph = new Paragraph(new Run(text));

            if (isHeader)
            {
                paragraph.FontWeight = FontWeights.Bold;
            }

            paragraph.Margin = new Thickness(2);
            paragraph.TextAlignment = TextAlignment.Left;
            cell.Blocks.Add(paragraph);

            return cell;
        }

        // Enhanced helper methods for better formatting
        private enum SeparatorStyle
        {
            Single,
            Double,
            Dotted
        }

        private void AddStyledSeparator(FlowDocument document, SeparatorStyle style)
        {
            var separatorParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 8, 0, 8),
                FontFamily = new FontFamily("Consolas, Courier New"),
                FontSize = 10
            };

            string separatorText = style switch
            {
                SeparatorStyle.Double => new string('=', 60),
                SeparatorStyle.Dotted => new string('.', 60),
                _ => new string('-', 60)
            };

            separatorParagraph.Inlines.Add(new Run(separatorText) { Foreground = Brushes.Gray });
            document.Blocks.Add(separatorParagraph);
        }

        private TableCell CreateDetailCell(string text, bool isLabel, TextAlignment alignment = TextAlignment.Left, Brush foreground = null)
        {
            var cell = new TableCell();
            var paragraph = new Paragraph(new Run(text))
            {
                Margin = new Thickness(3, 2, 3, 2),
                TextAlignment = alignment,
                FontSize = 11
            };

            if (isLabel)
            {
                paragraph.FontWeight = FontWeights.SemiBold;
                paragraph.Foreground = foreground ?? Brushes.DarkGray;
            }
            else
            {
                paragraph.Foreground = foreground ?? Brushes.Black;
            }

            cell.Blocks.Add(paragraph);
            return cell;
        }

        private TableCell CreateEnhancedTableCell(string text, bool isHeader = false, TextAlignment alignment = TextAlignment.Left,
            bool isHeaderRow = false, bool isTotalColumn = false)
        {
            var cell = new TableCell();
            var paragraph = new Paragraph(new Run(text))
            {
                Margin = new Thickness(5, 3, 5, 3),
                TextAlignment = alignment,
                LineHeight = 1.2
            };

            if (isHeader || isHeaderRow)
            {
                paragraph.FontWeight = FontWeights.Bold;
                paragraph.FontSize = 11;
                paragraph.Foreground = Brushes.Black;
            }
            else
            {
                paragraph.FontSize = 10;
                paragraph.Foreground = Brushes.Black;
            }

            if (isTotalColumn)
            {
                paragraph.FontWeight = FontWeights.SemiBold;
            }

            cell.Blocks.Add(paragraph);
            return cell;
        }

        private TableCell CreateTotalCell(string text, bool isBold = false, bool isRightAligned = false,
            Brush foreground = null, double fontSize = 12)
        {
            var cell = new TableCell();
            var paragraph = new Paragraph(new Run(text))
            {
                Margin = new Thickness(5, 3, 5, 3),
                TextAlignment = isRightAligned ? TextAlignment.Right : TextAlignment.Left,
                FontSize = fontSize
            };

            if (isBold)
            {
                paragraph.FontWeight = FontWeights.Bold;
            }

            paragraph.Foreground = foreground ?? Brushes.Black;
            cell.Blocks.Add(paragraph);
            return cell;
        }

        private void AddEnhancedTotals(FlowDocument document, Sale sale)
        {
            // Totals section with professional table layout
            var totalsTable = new Table
            {
                CellSpacing = 0,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 15, 0, 15)
            };

            // Two columns: label and amount
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var rowGroup = new TableRowGroup();

            // Subtotal
            var subtotalRow = new TableRow();
            subtotalRow.Cells.Add(CreateTotalCell("Subtotal:", false));
            subtotalRow.Cells.Add(CreateTotalCell($"{sale.Subtotal:N2} DA", false, true));
            rowGroup.Rows.Add(subtotalRow);

            // Discount (if applicable)
            if (sale.DiscountAmount > 0)
            {
                var discountRow = new TableRow();
                discountRow.Cells.Add(CreateTotalCell("Discount:", false));
                discountRow.Cells.Add(CreateTotalCell($"-{sale.DiscountAmount:N2} DA", false, true, Brushes.Red));
                rowGroup.Rows.Add(discountRow);
            }

            // Tax (if applicable)
            if (sale.TaxAmount > 0)
            {
                var taxRow = new TableRow();
                taxRow.Cells.Add(CreateTotalCell("Tax:", false));
                taxRow.Cells.Add(CreateTotalCell($"{sale.TaxAmount:N2} DA", false, true));
                rowGroup.Rows.Add(taxRow);
            }

            // Add separator before grand total
            var separatorRow = new TableRow();
            var separatorCell = new TableCell
            {
                ColumnSpan = 2,
                BorderThickness = new Thickness(0, 1, 0, 0),
                BorderBrush = Brushes.Black,
                Padding = new Thickness(0, 5, 0, 5)
            };
            separatorCell.Blocks.Add(new Paragraph());
            separatorRow.Cells.Add(separatorCell);
            rowGroup.Rows.Add(separatorRow);

            // Grand Total with enhanced styling
            var totalRow = new TableRow { Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)) };
            totalRow.Cells.Add(CreateTotalCell("GRAND TOTAL:", true, false, Brushes.Black, 16));
            totalRow.Cells.Add(CreateTotalCell($"{sale.GrandTotal:N2} DA", true, true, Brushes.Black, 16));
            rowGroup.Rows.Add(totalRow);

            totalsTable.RowGroups.Add(rowGroup);
            document.Blocks.Add(totalsTable);
        }

        private void AddEnhancedPaymentInfo(FlowDocument document, Sale sale)
        {
            // Payment section header
            var paymentHeaderParagraph = new Paragraph
            {
                Margin = new Thickness(0, 15, 0, 10),
                FontWeight = FontWeights.SemiBold,
                FontSize = 13
            };
            paymentHeaderParagraph.Inlines.Add(new Run("PAYMENT DETAILS"));
            document.Blocks.Add(paymentHeaderParagraph);

            // Payment details table
            var paymentTable = new Table
            {
                CellSpacing = 0,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 0, 15)
            };

            paymentTable.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) });
            paymentTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var rowGroup = new TableRowGroup();

            // Payment method
            var methodRow = new TableRow();
            methodRow.Cells.Add(CreateDetailCell("Payment Method:", true));
            methodRow.Cells.Add(CreateDetailCell(sale.PaymentMethod ?? "Cash", false, TextAlignment.Right));
            rowGroup.Rows.Add(methodRow);

            // Amount paid
            var paidRow = new TableRow();
            paidRow.Cells.Add(CreateDetailCell("Amount Paid:", true));
            paidRow.Cells.Add(CreateDetailCell($"{sale.AmountPaid:N2} DA", false, TextAlignment.Right));
            rowGroup.Rows.Add(paidRow);

            // Change (if applicable)
            if (sale.PaymentMethod == "Cash" && sale.AmountPaid > sale.GrandTotal)
            {
                var change = sale.AmountPaid - sale.GrandTotal;
                var changeRow = new TableRow();
                changeRow.Cells.Add(CreateDetailCell("Change Given:", true));
                changeRow.Cells.Add(CreateDetailCell($"{change:N2} DA", false, TextAlignment.Right, Brushes.Green));
                rowGroup.Rows.Add(changeRow);
            }

            // Payment status
            var statusRow = new TableRow();
            statusRow.Cells.Add(CreateDetailCell("Status:", true));
            var statusColor = sale.PaymentStatus == "Paid" ? Brushes.Green : Brushes.Orange;
            statusRow.Cells.Add(CreateDetailCell(sale.PaymentStatus ?? "Completed", false, TextAlignment.Right, statusColor));
            rowGroup.Rows.Add(statusRow);

            paymentTable.RowGroups.Add(rowGroup);
            document.Blocks.Add(paymentTable);

            AddStyledSeparator(document, SeparatorStyle.Single);
        }

        private void AddEnhancedFooter(FlowDocument document, string footerText)
        {
            AddStyledSeparator(document, SeparatorStyle.Double);

            // Custom footer text
            if (!string.IsNullOrEmpty(footerText))
            {
                var customFooterParagraph = new Paragraph
                {
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 15, 0, 10),
                    FontSize = 11,
                    FontStyle = FontStyles.Italic,
                    Foreground = Brushes.DarkGray
                };
                customFooterParagraph.Inlines.Add(new Run(footerText));
                document.Blocks.Add(customFooterParagraph);
            }

            // Thank you message with enhanced styling
            var thankYouParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 10, 0, 15),
                FontWeight = FontWeights.SemiBold,
                FontSize = 12
            };
            thankYouParagraph.Inlines.Add(new Run("Thank you for your business!"));
            document.Blocks.Add(thankYouParagraph);

            // Additional footer information
            var infoFooterParagraph = new Paragraph
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 5, 0, 10),
                FontSize = 9,
                Foreground = Brushes.Gray
            };

            infoFooterParagraph.Inlines.Add(new Run("Please keep this receipt for your records"));
            infoFooterParagraph.Inlines.Add(new LineBreak());
            infoFooterParagraph.Inlines.Add(new Run($"Generated on {DateTime.Now:dd/MM/yyyy HH:mm:ss}"));

            // Add return policy if configured
            var returnPolicy = _settingsService.GetSetting("ReturnPolicy");
            if (!string.IsNullOrEmpty(returnPolicy))
            {
                infoFooterParagraph.Inlines.Add(new LineBreak());
                infoFooterParagraph.Inlines.Add(new Run(returnPolicy));
            }

            document.Blocks.Add(infoFooterParagraph);
        }

        private async Task<bool> PrintThermalReceiptAsync(FlowDocument document, PrinterConfiguration printerConfig, bool showDialog)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var printDialog = new PrintDialog();

                    if (!string.IsNullOrEmpty(printerConfig.PrinterName))
                    {
                        // Try to select the specific printer
                        var printServer = new System.Printing.LocalPrintServer();
                        var printer = printServer.GetPrintQueue(printerConfig.PrinterName);
                        if (printer != null)
                        {
                            printDialog.PrintQueue = printer;
                        }
                    }

                    // Configure for thermal printing
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;

                    // Set thermal paper size (80mm width)
                    var thermalWidth = 3.15 * 96; // 80mm in pixels at 96 DPI
                    paginator.PageSize = new Size(thermalWidth, double.MaxValue);

                    if (showDialog)
                    {
                        if (printDialog.ShowDialog() != true)
                            return false;
                    }

                    printDialog.PrintDocument(paginator, $"Receipt");
                    LogInfo($"Thermal receipt printed successfully");
                    return true;
                }
                catch (Exception ex)
                {
                    LogError($"Error printing thermal receipt: {ex.Message}", ex);
                    return false;
                }
            });
        }

        private async Task<bool> PrintStandardReceiptAsync(FlowDocument document, PrinterConfiguration printerConfig, bool showDialog)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var printDialog = new PrintDialog();

                    if (!string.IsNullOrEmpty(printerConfig.PrinterName))
                    {
                        // Try to select the specific printer
                        var printServer = new System.Printing.LocalPrintServer();
                        var printer = printServer.GetPrintQueue(printerConfig.PrinterName);
                        if (printer != null)
                        {
                            printDialog.PrintQueue = printer;
                        }
                    }

                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;

                    // Set standard paper size based on configuration
                    Size pageSize = printerConfig.PaperSize?.ToLower() switch
                    {
                        "a4" => new Size(8.27 * 96, 11.69 * 96),
                        "letter" => new Size(8.5 * 96, 11 * 96),
                        _ => new Size(8.5 * 96, 11 * 96)
                    };

                    paginator.PageSize = pageSize;

                    if (showDialog)
                    {
                        if (printDialog.ShowDialog() != true)
                            return false;
                    }

                    printDialog.PrintDocument(paginator, $"Receipt");
                    LogInfo($"Standard receipt printed successfully");
                    return true;
                }
                catch (Exception ex)
                {
                    LogError($"Error printing standard receipt: {ex.Message}", ex);
                    return false;
                }
            });
        }

        private async Task<bool> SaveReceiptAsPdfAsync(FlowDocument document, Sale sale, PrinterConfiguration printerConfig, string customPath = null)
        {
            try
            {
                string fileName = customPath ?? Path.Combine(
                    _printSettings.PdfBackupPath ?? Path.GetTempPath(),
                    $"Receipt_{sale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.xps"
                );

                LogInfo($"Attempting to save receipt to: {fileName}");

                // Validate and create directory
                var directory = Path.GetDirectoryName(fileName);
                if (string.IsNullOrEmpty(directory))
                {
                    LogError("Invalid file path - directory is null or empty");
                    return false;
                }

                if (!Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                        LogInfo($"Created directory: {directory}");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to create directory {directory}: {ex.Message}", ex);
                        return false;
                    }
                }

                // Check write permissions
                try
                {
                    var testFile = Path.Combine(directory, $"test_{Guid.NewGuid()}.tmp");
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                }
                catch (Exception ex)
                {
                    LogError($"No write permission to directory {directory}: {ex.Message}", ex);
                    return false;
                }

                // Create XPS document on UI thread to avoid threading issues
                bool success = await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        using (var xpsDocument = new XpsDocument(fileName, FileAccess.Write))
                        {
                            var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                            var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                            xpsWriter.Write(paginator);
                        }

                        LogInfo($"Receipt saved successfully as XPS: {fileName}");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error creating XPS document: {ex.Message}", ex);
                        return false;
                    }
                });

                if (success)
                {
                    // Only show success message for manual saves, not automatic backups
                    if (customPath != null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show(
                                $"Receipt saved successfully to:\n{fileName}\n\nNote: File saved as XPS format which can be viewed with Windows XPS Viewer.",
                                "Receipt Saved",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        });
                    }
                    else
                    {
                        LogInfo($"PDF backup saved successfully: {fileName}");
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                LogError($"Error saving receipt as PDF: {ex.Message}", ex);

                // Show error message for manual saves
                if (customPath != null)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(
                            $"Error saving receipt: {ex.Message}",
                            "Save Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    });
                }

                return false;
            }
        }

        private async Task<bool> SaveReceiptAsPdfBackupAsync(FlowDocument document, Sale sale)
        {
            try
            {
                if (string.IsNullOrEmpty(_printSettings.PdfBackupPath))
                {
                    LogError("PDF backup path is not configured - skipping PDF backup");
                    return false;
                }

                LogInfo($"Attempting PDF backup to: {_printSettings.PdfBackupPath}");

                // Generate backup file path
                var fileName = Path.Combine(
                    _printSettings.PdfBackupPath,
                    $"Receipt_{sale.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.xps"
                );

                return await SaveReceiptAsPdfAsync(document, sale, null, fileName);
            }
            catch (Exception ex)
            {
                LogError($"Error in PDF backup process: {ex.Message}", ex);
                return false;
            }
        }
    }
}
