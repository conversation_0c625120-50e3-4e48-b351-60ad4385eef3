using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.ViewModels;
using POSSystem.Views.Dialogs;

namespace POSSystem.Views
{
    public partial class UsersView : UserControl
    {
        private readonly DatabaseService _dbService;
        private UsersViewModel ViewModel => (UsersViewModel)DataContext;
        
        public UsersView()
        {
            InitializeComponent();
            _dbService = new DatabaseService();
            LoadData();
        }
        
        private async void AddNewUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Ensure roles are loaded
                if (ViewModel.Roles == null || !ViewModel.Roles.Any())
                {
                    ViewModel.LoadData();
                }

                var roles = ViewModel.Roles?.Where(r => r.IsActive).ToList();
                
                if (roles == null || !roles.Any())
                {
                    MessageBox.Show(
                        (string)Application.Current.Resources["NoRolesAvailable"],
                        (string)Application.Current.Resources["Error"],
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Opening UserDialog with {roles.Count} roles");
                foreach (var role in roles)
                {
                    System.Diagnostics.Debug.WriteLine($"Role: {role.Name} (ID: {role.Id})");
                }

                // Check if a dialog is already open to prevent conflicts
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialog already open, ignoring add user click");
                    return;
                }

                var dialog = new UserDialog(null, roles);
                var result = await DialogHost.Show(dialog, "RootDialog");
                
                if (result is User newUser)
                {
                    // The user has already been added to the database by the dialog
                    // Just refresh the user list in the view model
                    ViewModel.LoadData();
                    
                    MessageBox.Show(
                        (string)Application.Current.Resources["UserSavedSuccessfully"], 
                        (string)Application.Current.Resources["SuccessTitle"], 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected in add user: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddNewUser_Click: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                MessageBox.Show(
                    $"Error adding new user: {ex.Message}",
                    (string)Application.Current.Resources["Error"],
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
        
        private async void EditUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if a dialog is already open to prevent conflicts
                if (DialogHost.IsDialogOpen("RootDialog"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialog already open, ignoring edit user click");
                    return;
                }

                var button = (Button)sender;
                var user = (User)button.DataContext;

                var dialog = new UserDialog(user, ViewModel.Roles.ToList());
                var result = await DialogHost.Show(dialog, "RootDialog");

                if (result is User updatedUser)
                {
                    // The user has already been updated in the database by the dialog
                    // Just refresh the user list in the view model
                    ViewModel.LoadData();

                    MessageBox.Show(
                        (string)Application.Current.Resources["UserSavedSuccessfully"],
                        (string)Application.Current.Resources["SuccessTitle"],
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost is already open") || ex.Message.Contains("already open"))
            {
                System.Diagnostics.Debug.WriteLine($"DialogHost conflict detected in edit user: {ex.Message}");
                // Silently ignore this error as it's a timing issue, not a real error
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in EditUser_Click: {ex.Message}");
                MessageBox.Show(
                    $"Error editing user: {ex.Message}",
                    (string)Application.Current.Resources["Error"],
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void LoadData()
        {
            try
            {
                // Initialize the ViewModel if it's null
                if (DataContext == null)
                {
                    DataContext = new UsersViewModel();
                }

                // Load data through the ViewModel
                ViewModel.LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error loading data: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
    }
} 