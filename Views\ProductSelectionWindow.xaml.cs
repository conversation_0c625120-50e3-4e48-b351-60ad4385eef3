using System.Collections.ObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.ViewModels;

namespace POSSystem.Views
{
    public partial class ProductSelectionWindow : Window
    {
        private readonly DatabaseService _dbService = new();
        private ObservableCollection<Product> _allProducts;
        public ObservableCollection<Product> FilteredProducts { get; private set; }
        private const int PageSize = 300;
        private int _loadedCount = 0;
        private List<Product> _filteredBacking = new List<Product>();
        public ObservableCollection<Category> Categories { get; private set; }
        public Product SelectedProduct { get; private set; }
        private ScrollChangedEventHandler _scrollHandler;

	        private POSSystem.Services.Memory.AdvancedMemoryManager _memory;


        public ProductSelectionWindow()
        {
            InitializeComponent();
            _memory = App.Current?.Resources["AdvancedMemoryManager"] as POSSystem.Services.Memory.AdvancedMemoryManager;
            _memory?.MarkActivity("ProductSelectionWindow: Initialize");

            DataContext = this;
            LoadData();

            // Subscribe to category updates
            CategoriesViewModel.CategoryChanged += OnCategoryChanged;

            // Hook scroll for incremental loading via routed event on DataGrid's ScrollViewer
            _scrollHandler = (s, e) =>
            {
                if (e.VerticalOffset + e.ViewportHeight >= e.ExtentHeight - 50)
                {
                    LoadNextPage();
                }
            };
            ProductsGrid.AddHandler(ScrollViewer.ScrollChangedEvent, _scrollHandler);
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
            // Unsubscribe from category updates
            CategoriesViewModel.CategoryChanged -= OnCategoryChanged;

            // Detach scroll handler if attached
            if (_scrollHandler != null)
            {
                ProductsGrid.RemoveHandler(ScrollViewer.ScrollChangedEvent, _scrollHandler);
                _scrollHandler = null;
            }

            // Aggressive release of large references
            try
            {
                ProductsGrid.ItemsSource = null;
                FilteredProducts?.Clear();
                Categories?.Clear();
                _filteredBacking?.Clear();
                _allProducts = null;
                _filteredBacking = null;
                FilteredProducts = null;
                Categories = null;
                DataContext = null;
            }
            catch { }

            // Dispose database service to release DbContext and connections
            _dbService?.Dispose();
        }

        private void OnCategoryChanged(object sender, CategoryUpdateEventArgs e)
        {
            // Refresh your categories here
            LoadCategories();
        }

        private async void LoadData()
        {
            try
            {
                // ✅ FIX: Use GetAllProductsWithFullDetailsAsync to include batch relationships
                // This ensures StockQuantity property calculates correctly for batch-tracked products
                var products = await _dbService.GetAllProductsWithFullDetailsAsync();
                _allProducts = new ObservableCollection<Product>(products);
                // Initialize empty, paging will add items
                FilteredProducts = new ObservableCollection<Product>();
                Categories = new ObservableCollection<Category>(_dbService.GetAllCategories());

                // Add "All Categories" option
                Categories.Insert(0, new Category { Id = 0, Name = Application.Current.FindResource("AllCategories").ToString() });
                CategoryFilter.SelectedIndex = 0;

                System.Diagnostics.Debug.WriteLine($"[PRODUCT-SELECTION] Loaded {products.Count} products with batch data");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT-SELECTION] Error loading products: {ex.Message}");

                // Fallback to basic method if async fails
                _allProducts = new ObservableCollection<Product>(_dbService.GetAllProducts());
                // Initialize empty, paging will add items
                FilteredProducts = new ObservableCollection<Product>();
                Categories = new ObservableCollection<Category>(_dbService.GetAllCategories());

                // Add "All Categories" option
                Categories.Insert(0, new Category { Id = 0, Name = Application.Current.FindResource("AllCategories").ToString() });
                CategoryFilter.SelectedIndex = 0;
            }
        }

        private void LoadCategories()
        {
            // Update categories on the UI thread
            Dispatcher.Invoke(() =>
            {
                var selectedCategoryId = (CategoryFilter.SelectedItem as Category)?.Id ?? 0;
                Categories = new ObservableCollection<Category>(_dbService.GetAllCategories());
                Categories.Insert(0, new Category { Id = 0, Name = Application.Current.FindResource("AllCategories").ToString() });

                // Restore previous selection if possible
                CategoryFilter.SelectedItem = Categories.FirstOrDefault(c => c.Id == selectedCategoryId);
                if (CategoryFilter.SelectedItem == null)
                {
                    CategoryFilter.SelectedIndex = 0;
                }

                // Reapply filters with new categories
                ApplyFilters();
            });
        }

        private void ApplyFilters()
        {
            var searchText = SearchBox.Text.ToLower();
            var selectedCategory = CategoryFilter.SelectedItem as Category;
            _memory?.MarkActivity($"ProductSelectionWindow: ApplyFilters start, source={_allProducts?.Count} filteredBacking~");

            var filteredProducts = _allProducts.Where(p =>
                (string.IsNullOrEmpty(searchText) ||
                 p.Name.ToLower().Contains(searchText) ||
                 (p.Barcodes != null && p.Barcodes.Any(b => b.Barcode.ToLower().Contains(searchText)))) &&
                (selectedCategory == null || selectedCategory.Id == 0 || p.CategoryId == selectedCategory.Id));

            // Apply paging from the built enumerable
            _filteredBacking = filteredProducts.ToList();
            _loadedCount = 0;
            FilteredProducts.Clear();
            LoadNextPage();
            return;
        }

        private void LoadNextPage()
        {
            if (_filteredBacking == null || FilteredProducts == null) return;
            _memory?.MarkActivity($"ProductSelectionWindow: LoadNextPage {_loadedCount}->{Math.Min(_filteredBacking?.Count ?? 0, _loadedCount + PageSize)} (total {_filteredBacking?.Count})");
            int remaining = _filteredBacking.Count - _loadedCount;
            if (remaining <= 0) return;
            int take = Math.Min(PageSize, remaining);
            for (int i = 0; i < take; i++)
            {
                FilteredProducts.Add(_filteredBacking[_loadedCount + i]);
            }
            _loadedCount += take;
        }


        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SelectProduct_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.DataContext is Product product)
            {
                SelectedProduct = product;
                DialogResult = true;
                Close();
            }
        }

        private void ProductsGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ProductsGrid.SelectedItem is Product product)
            {
                SelectedProduct = product;
                DialogResult = true;
                Close();
            }
        }
    }
}