using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using POSSystem.Data;

namespace POSSystem.Services.Dashboard
{
    /// <summary>
    /// ✅ NEW: Background service for preloading dashboard data
    /// Ensures instant dialog display by keeping data warm in cache
    /// </summary>
    public class DashboardPreloadService
    {
        private readonly LowStockDataService _lowStockDataService;
        private readonly Timer _preloadTimer;
        private readonly object _lockObject = new object();
        private bool _isPreloading = false;
        private bool _isDisposed = false;

        // Preload every 5 minutes to keep cache warm
        private const int PRELOAD_INTERVAL_MINUTES = 5;

        public DashboardPreloadService()
        {
            _lowStockDataService = new LowStockDataService(new POSDbContext());
            
            // Start background preloading timer
            _preloadTimer = new Timer(PreloadCallback, null, TimeSpan.Zero, TimeSpan.FromMinutes(PRELOAD_INTERVAL_MINUTES));
            
            Debug.WriteLine("DashboardPreloadService: Background preloading started");
        }

        /// <summary>
        /// ✅ NEW: Timer callback for background preloading
        /// </summary>
        private async void PreloadCallback(object state)
        {
            if (_isDisposed || _isPreloading) return;

            lock (_lockObject)
            {
                if (_isPreloading) return;
                _isPreloading = true;
            }

            try
            {
                await PreloadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardPreloadService: Preload failed - {ex.Message}");
            }
            finally
            {
                lock (_lockObject)
                {
                    _isPreloading = false;
                }
            }
        }

        /// <summary>
        /// ✅ NEW: Preload all dashboard data types
        /// </summary>
        private async Task PreloadDashboardDataAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Preload low stock data in background
                await _lowStockDataService.PreloadDataAsync();

                stopwatch.Stop();
                Debug.WriteLine($"DashboardPreloadService: Preload completed in {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardPreloadService: Error during preload - {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ NEW: Force immediate preload (called when user navigates to dashboard)
        /// </summary>
        public async Task ForcePreloadAsync()
        {
            if (_isDisposed) return;

            try
            {
                Debug.WriteLine("DashboardPreloadService: Force preload requested");
                await PreloadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardPreloadService: Force preload failed - {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ NEW: Invalidate cache when data changes (called after product updates)
        /// </summary>
        public async Task InvalidateAndRefreshAsync()
        {
            if (_isDisposed) return;

            try
            {
                Debug.WriteLine("DashboardPreloadService: Invalidating cache and refreshing");
                
                // Clear cache
                _lowStockDataService.InvalidateCache();
                
                // Immediately reload fresh data
                await PreloadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DashboardPreloadService: Invalidate and refresh failed - {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ NEW: Get the low stock data service for direct access
        /// </summary>
        public LowStockDataService GetLowStockDataService()
        {
            return _lowStockDataService;
        }

        /// <summary>
        /// ✅ NEW: Check if data is available in cache
        /// </summary>
        public async Task<bool> IsDataAvailableAsync()
        {
            try
            {
                var data = await _lowStockDataService.GetLowStockProductDataAsync();
                return data != null && data.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// ✅ NEW: Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;
            _preloadTimer?.Dispose();
            
            Debug.WriteLine("DashboardPreloadService: Disposed");
        }
    }

    /// <summary>
    /// ✅ NEW: Singleton instance for application-wide access
    /// </summary>
    public static class DashboardPreloadManager
    {
        private static DashboardPreloadService _instance;
        private static readonly object _lock = new object();

        public static DashboardPreloadService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DashboardPreloadService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// ✅ NEW: Initialize the preload service (call from App.xaml.cs)
        /// </summary>
        public static void Initialize()
        {
            var _ = Instance; // This will create the instance and start preloading
            Debug.WriteLine("DashboardPreloadManager: Initialized");
        }

        /// <summary>
        /// ✅ NEW: Cleanup on application shutdown
        /// </summary>
        public static void Shutdown()
        {
            lock (_lock)
            {
                _instance?.Dispose();
                _instance = null;
            }
            Debug.WriteLine("DashboardPreloadManager: Shutdown");
        }
    }
}
