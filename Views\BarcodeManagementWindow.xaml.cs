using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Views
{
    public partial class BarcodeManagementWindow : Window
    {
        private readonly DatabaseService _dbService;
        private readonly Product _product;
        private ObservableCollection<ProductBarcode> _barcodes;

        public ObservableCollection<ProductBarcode> Barcodes => _barcodes;

        public BarcodeManagementWindow(Product product, DatabaseService dbService)
        {
            InitializeComponent();
            _dbService = dbService;
            _product = product;
            _barcodes = new ObservableCollection<ProductBarcode>();

            // Initialize window
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set product info
            txtProductInfo.Text = $"{_product.Name}";

            // Load existing barcodes
            foreach (var barcode in _product.Barcodes)
            {
                _barcodes.Add(barcode);
            }

            // Set DataGrid source
            BarcodesList.ItemsSource = _barcodes;
        }

        private void GenerateBarcode_Click(object sender, RoutedEventArgs e)
        {
            // Generate a random 13-digit EAN-13 barcode
            Random random = new Random();
            string barcode = "200"; // Start with 200 for internal products

            // Generate 9 random digits
            for (int i = 0; i < 9; i++)
            {
                barcode += random.Next(10).ToString();
            }

            // Calculate check digit
            int sum = 0;
            for (int i = 0; i < 12; i++)
            {
                int digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }
            int checkDigit = (10 - (sum % 10)) % 10;
            barcode += checkDigit.ToString();

            txtBarcode.Text = barcode;
        }

        private void AddBarcode_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string barcodeText = txtBarcode.Text?.Trim();

                if (string.IsNullOrWhiteSpace(barcodeText))
                {
                    MessageBox.Show("Please enter a barcode", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Check if barcode already exists in the current list
                if (_barcodes.Any(b => b.Barcode == barcodeText))
                {
                    MessageBox.Show("This barcode is already in the list", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!_dbService.ValidateEAN13(barcodeText))
                {
                    MessageBox.Show("Invalid barcode format. Barcode must be 8-14 digits.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Only check database for existing products (not new products with ID = -1)
                if (_product.Id != -1 && !_dbService.IsValidBarcodeForProduct(barcodeText, _product.Id))
                {
                    MessageBox.Show("Barcode already exists for another product", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // For new products (ID = -1), we can't check the database, but we should still validate against existing barcodes
                if (_product.Id == -1)
                {
                    // Check if barcode exists in database for any product
                    if (!_dbService.IsValidBarcode(barcodeText))
                    {
                        MessageBox.Show("Barcode already exists for another product", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                var barcode = new ProductBarcode
                {
                    Barcode = barcodeText,
                    IsPrimary = _barcodes.Count == 0, // First barcode is primary
                    CreatedAt = DateTime.Now,
                    Description = "Default barcode"
                };

                _barcodes.Add(barcode);
                BarcodesList.Items.Refresh();
                txtBarcode.Clear();

                MessageBox.Show("Barcode added successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding barcode: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveBarcode_Click(object sender, RoutedEventArgs e)
        {
            var barcode = (sender as FrameworkElement)?.DataContext as ProductBarcode;
            if (barcode != null)
            {
                _barcodes.Remove(barcode);

                // If we removed the primary barcode and there are other barcodes,
                // make the first one primary
                if (barcode.IsPrimary && _barcodes.Any())
                {
                    _barcodes.First().IsPrimary = true;
                }
            }
        }

        private void SetPrimaryBarcode_Click(object sender, RoutedEventArgs e)
        {
            var selectedBarcode = (sender as FrameworkElement)?.DataContext as ProductBarcode;
            if (selectedBarcode != null)
            {
                foreach (var barcode in _barcodes)
                {
                    barcode.IsPrimary = (barcode == selectedBarcode);
                }
                BarcodesList.Items.Refresh();
            }
        }

        private void SaveChanges_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate that we have barcodes to save
                if (_barcodes.Count == 0)
                {
                    MessageBox.Show("Please add at least one barcode before saving.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Make sure we have at least one primary barcode
                if (!_barcodes.Any(b => b.IsPrimary))
                {
                    _barcodes.First().IsPrimary = true;
                }

                // Validate all barcodes before saving
                foreach (var barcode in _barcodes)
                {
                    if (!_dbService.ValidateEAN13(barcode.Barcode))
                    {
                        MessageBox.Show($"Invalid barcode format: '{barcode.Barcode}'. Please remove or correct it.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Check if this is a new product (ID = -1) or existing product
                if (_product.Id == -1)
                {
                    // For new products, just update the product's barcode collection in memory
                    // The actual database save will happen when the product is saved in ProductDialog
                    if (_product.Barcodes != null)
                    {
                        _product.Barcodes.Clear();
                        foreach (var barcode in _barcodes)
                        {
                            _product.Barcodes.Add(barcode);
                        }
                    }
                    else
                    {
                        _product.Barcodes = _barcodes.ToHashSet();
                    }

                    MessageBox.Show($"Barcodes prepared for new product! They will be saved when you save the product.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // For existing products, save to database immediately
                    // Clear existing barcodes first
                    try
                    {
                        _dbService.ClearProductBarcodes(_product.Id);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Failed to clear existing barcodes: {ex.Message}", ex);
                    }

                    // Save new barcodes one by one with detailed error handling
                    int savedCount = 0;
                    foreach (var barcode in _barcodes)
                    {
                        try
                        {
                            _dbService.AddBarcodeToProduct(_product.Id, barcode.Barcode, barcode.IsPrimary, barcode.Description);
                            savedCount++;
                        }
                        catch (InvalidOperationException ex) when (ex.Message.Contains("already exists"))
                        {
                            throw new Exception($"Failed to add barcode '{barcode.Barcode}': {ex.Message}", ex);
                        }
                        catch (Exception ex)
                        {
                            throw new Exception($"Failed to add barcode '{barcode.Barcode}': {ex.Message}", ex);
                        }
                    }

                    // Update the product's barcodes collection
                    if (_product.Barcodes != null)
                    {
                        _product.Barcodes.Clear();
                        foreach (var barcode in _barcodes)
                        {
                            _product.Barcodes.Add(barcode);
                        }
                    }
                    else
                    {
                        _product.Barcodes = _barcodes.ToHashSet();
                    }

                    MessageBox.Show($"Successfully saved {savedCount} barcode(s) to database!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving barcodes: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
} 