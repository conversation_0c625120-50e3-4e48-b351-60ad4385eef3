<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Background Gradients -->
    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#0078D4" Offset="0.0"/>
        <GradientStop Color="#0067B5" Offset="1.0"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#00B7C3" Offset="0.0"/>
        <GradientStop Color="#00A5B0" Offset="1.0"/>
    </LinearGradientBrush>

    <!-- Missing System Brushes -->
    <SolidColorBrush x:Key="SystemBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SystemAlertBrush" Color="#FF5722"/>
    <SolidColorBrush x:Key="SystemForegroundBrush" Color="#212121"/>
    <SolidColorBrush x:Key="SystemForegroundMediumBrush" Color="#757575"/>

    <!-- Modern Product Card Color Palette -->
    <SolidColorBrush x:Key="ProductCardBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="ProductCardBorderBrush" Color="#E8EAF0"/>
    <SolidColorBrush x:Key="ProductCardHoverBackgroundBrush" Color="#FAFBFF"/>
    <SolidColorBrush x:Key="ProductCardHoverBorderBrush" Color="#3B82F6"/>

    <!-- Status Colors -->
    <SolidColorBrush x:Key="OutOfStockBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="LowStockBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="InStockBrush" Color="#10B981"/>

    <!-- Text Colors -->
    <SolidColorBrush x:Key="ProductNameTextBrush" Color="#1A1A1A"/>
    <SolidColorBrush x:Key="ProductStockTextBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="ProductPriceTextBrush" Color="#FFFFFF"/>

    <LinearGradientBrush x:Key="SuccessGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#4CAF50" Offset="0.0"/>
        <GradientStop Color="#43A047" Offset="1.0"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="LightGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#F5F9FC" Offset="0.0"/>
        <GradientStop Color="#EDF4F9" Offset="1.0"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DarkGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#192656" Offset="0.0"/>
        <GradientStop Color="#151F45" Offset="1.0"/>
    </LinearGradientBrush>

    <!-- Common Window Style -->
    <Style x:Key="AppWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="TextElement.Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="TextElement.FontWeight" Value="Regular"/>
        <Setter Property="TextElement.FontSize" Value="13"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Ideal"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="Auto"/>
        <Setter Property="FontFamily" Value="{md:MaterialDesignFont}"/>
    </Style>

    <!-- Enhanced Card Styles -->
    <Style x:Key="ContentCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="10" ShadowDepth="1" Opacity="0.2" Color="#000000"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PrimaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="10" ShadowDepth="1" Opacity="0.3" Color="#0078D4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SecondaryGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="10" ShadowDepth="1" Opacity="0.3" Color="#00B7C3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="LightCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource LightGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.1" Color="#000000"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DarkCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource DarkGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="10" ShadowDepth="1" Opacity="0.3" Color="#192656"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Dashboard Summary Card Style -->
    <Style x:Key="DashboardSummaryCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="10"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FAFCFF"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="15" ShadowDepth="3" Opacity="0.2" Color="#2196F3"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Enhanced Grid Background Style -->
    <Style x:Key="ContentGridStyle" TargetType="Grid">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="{DynamicResource AppMainBackgroundColor}" Offset="0.0"/>
                    <GradientStop Color="{DynamicResource AppSecondaryBackgroundColor}" Offset="1.0"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Enhanced Input Control Styles -->
    <Style x:Key="AppComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignFilledComboBox}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,12"/>
        <Setter Property="md:HintAssist.FloatingScale" Value="0.85"/>
        <Setter Property="md:TextFieldAssist.UnderlineBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <Style x:Key="AppTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignFilledTextBox}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,12"/>
        <Setter Property="md:HintAssist.FloatingScale" Value="0.85"/>
        <Setter Property="md:TextFieldAssist.UnderlineBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <Style x:Key="AppDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignFilledDatePicker}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,12"/>
        <Setter Property="md:HintAssist.FloatingScale" Value="0.85"/>
        <Setter Property="md:TextFieldAssist.UnderlineBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <!-- DataGrid Column Header Style -->
    <Style x:Key="AppDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- Enhanced DataGrid Styles -->
    <Style x:Key="AppDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="md:DataGridAssist.CellPadding" Value="12 8 8 8"/>
        <Setter Property="md:DataGridAssist.ColumnHeaderPadding" Value="12"/>
        <Setter Property="RowBackground" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignBackground}"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="RowHeaderWidth" Value="0"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="ColumnHeaderStyle" Value="{StaticResource AppDataGridColumnHeaderStyle}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.1" Color="#000000"/>
            </Setter.Value>
        </Setter>
        <Style.Resources>
            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                           Color="#2196F3"/>
            <SolidColorBrush x:Key="{x:Static SystemColors.ControlBrushKey}" 
                           Color="Transparent"/>
            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" 
                           Color="White"/>
        </Style.Resources>
    </Style>

    <!-- Enhanced Button Styles -->
    <Style x:Key="AppPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.2" Color="#0078D4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="AppSecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="BorderThickness" Value="1.5"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6"/>
    </Style>

    <Style x:Key="AppSuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="45"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Background" Value="{StaticResource SuccessGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.2" Color="#4CAF50"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="AppDeleteButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="Height" Value="35"/>
        <Setter Property="Width" Value="90"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="Foreground" Value="#F44336"/>
        <Setter Property="BorderBrush" Value="#F44336"/>
        <Setter Property="BorderThickness" Value="1.5"/>
        <Setter Property="md:ButtonAssist.CornerRadius" Value="6"/>
    </Style>

    <!-- Enhanced Product Card Styles -->
    <Style x:Key="ModernProductCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Setter Property="Padding" Value="0"/>
        <!-- Performance-optimized shadow using Border instead of DropShadowEffect -->
        <!-- Removed DropShadowEffect for product card style to minimize per-frame work -->
        <!-- Accessibility: Focus visual -->
        <Setter Property="FocusVisualStyle">
            <Setter.Value>
                <Style>
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <Border BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                        BorderThickness="2"
                                        CornerRadius="12"
                                        Margin="-2"/>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FAFBFF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="12" ShadowDepth="2" Opacity="0.15" Color="#2196F3"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="#FAFBFF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Product Card Image Container Style -->
    <Style x:Key="ProductImageContainerStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#F8F9FA" Offset="0.0"/>
                    <GradientStop Color="#F1F3F4" Offset="1.0"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="CornerRadius" Value="12,12,0,0"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- Product Card Info Panel Style -->
    <Style x:Key="ProductInfoPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="Padding" Value="10,6"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- Product Card Stock Panel Style -->
    <Style x:Key="ProductStockPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F9FA"/>
        <Setter Property="Padding" Value="8,3"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- Product Card Price Panel Style -->
    <Style x:Key="ProductPricePanelStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="CornerRadius" Value="0,0,12,12"/>
        <Setter Property="Padding" Value="10,6"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- Product Card Status Indicator Style -->
    <Style x:Key="ProductStatusIndicatorStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="0,12,0,12"/>
        <Setter Property="Width" Value="28"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="HorizontalAlignment" Value="Right"/>
        <Setter Property="VerticalAlignment" Value="Top"/>
        <Setter Property="Panel.ZIndex" Value="2"/>
    </Style>

    <!-- Enhanced Typography Styles with High Contrast Support -->
    <Style x:Key="ProductNameTextStyle" TargetType="TextBlock">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Foreground" Value="#1A1A1A"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="MaxHeight" Value="36"/>
        <Setter Property="LineHeight" Value="18"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ProductStockTextStyle" TargetType="TextBlock">
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Foreground" Value="#6B7280"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ProductPriceTextStyle" TargetType="TextBlock">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!-- Action Button Styles -->
    <Style x:Key="ProductActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Setter Property="Width" Value="28"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="4" ShadowDepth="1" Opacity="0.1" Color="#000000"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F0F4FF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Favorite Toggle Button Style -->
    <Style x:Key="ProductFavoriteButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource MaterialDesignActionToggleButton}">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Setter Property="Width" Value="28"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="4" ShadowDepth="1" Opacity="0.1" Color="#000000"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FFF5F5"/>
                <Setter Property="BorderBrush" Value="#F87171"/>
            </Trigger>
            <Trigger Property="IsChecked" Value="True">
                <Setter Property="Background" Value="#FEF2F2"/>
                <Setter Property="BorderBrush" Value="#F87171"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Loading Animation for Product Cards -->
    <Style x:Key="ProductCardLoadingStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#F3F4F6" Offset="0"/>
                    <GradientStop Color="#E5E7EB" Offset="0.5"/>
                    <GradientStop Color="#F3F4F6" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E8EAF0"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard RepeatBehavior="Forever">
                        <DoubleAnimation Storyboard.TargetProperty="(Border.Background).(LinearGradientBrush.Transform).(TranslateTransform.X)"
                                       From="-100" To="100" Duration="0:0:1.5"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Enhanced Interaction Animations -->
    <Storyboard x:Key="ProductCardHoverEnterAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                         To="1.01" Duration="0:0:0.10">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                         To="1.01" Duration="0:0:0.10">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ProductCardHoverExitAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                         To="1.0" Duration="0:0:0.10">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                         To="1.0" Duration="0:0:0.10">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Legacy Product Card Style (kept for compatibility) -->
    <Style x:Key="ProductCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFFFF"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.15" Color="#000000"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F9FAFF"/>
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.25" Color="#2196F3"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Loyalty Card Style -->
    <Style x:Key="LoyaltyCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource DarkGradientBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="12" ShadowDepth="2" Opacity="0.35" Color="#000000"/>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary> 