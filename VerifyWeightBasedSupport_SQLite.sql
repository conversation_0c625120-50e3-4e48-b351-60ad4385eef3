-- SQLite Verification Script: Check Weight-Based Product Support
-- Run this script in SQLite DB Browser to verify if the weight-based product migration has been applied

-- Check if IsWeightBased column exists in Products table
SELECT 'Checking Products table structure...' as Step;

-- Check for IsWeightBased column
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ IsWeightBased column EXISTS in Products table'
        ELSE '❌ IsWeightBased column MISSING from Products table'
    END as Result
FROM pragma_table_info('Products') 
WHERE name = 'IsWeightBased';

-- Show Products table structure
SELECT 'Products table structure:' as Info;
SELECT name as ColumnName, type as DataType, [notnull], dflt_value as DefaultValue
FROM pragma_table_info('Products');

-- Check SaleItems table Quantity column type
SELECT 'Checking SaleItems table Quantity column...' as Step;

SELECT 
    CASE 
        WHEN type LIKE '%DECIMAL%' OR type LIKE '%REAL%' OR type LIKE '%NUMERIC%' THEN 
            '✅ SaleItems.Quantity supports decimal values (type: ' || type || ')'
        WHEN type LIKE '%INT%' THEN 
            '⚠️ SaleItems.Quantity is INTEGER type (may need migration)'
        ELSE 
            '❓ SaleItems.Quantity has unexpected type: ' || type
    END as Result
FROM pragma_table_info('SaleItems') 
WHERE name = 'Quantity';

-- Check CartItems table Quantity column type
SELECT 'Checking CartItems table Quantity column...' as Step;

SELECT 
    CASE 
        WHEN type LIKE '%DECIMAL%' OR type LIKE '%REAL%' OR type LIKE '%NUMERIC%' THEN 
            '✅ CartItems.Quantity supports decimal values (type: ' || type || ')'
        WHEN type LIKE '%INT%' THEN 
            '⚠️ CartItems.Quantity is INTEGER type (may need migration)'
        ELSE 
            '❓ CartItems.Quantity has unexpected type: ' || type
    END as Result
FROM pragma_table_info('CartItems') 
WHERE name = 'Quantity';

-- Check if any weight-based products exist (only if column exists)
SELECT 'Checking for existing weight-based products...' as Step;

-- First check if the column exists
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM pragma_table_info('Products') WHERE name = 'IsWeightBased') > 0 THEN
            (SELECT 
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        '✅ Found ' || COUNT(*) || ' weight-based products'
                    ELSE 
                        '⚠️ No weight-based products found - create test products'
                END
             FROM Products WHERE IsWeightBased = 1)
        ELSE 
            '❌ Cannot check - IsWeightBased column does not exist'
    END as Result;

-- Show examples of weight-based products (if any exist and column exists)
SELECT 'Examples of weight-based products:' as Info;

SELECT 
    Id,
    Name,
    SKU,
    IsWeightBased,
    StockQuantity
FROM Products 
WHERE IsWeightBased = 1
LIMIT 5;

-- Check for products with barcodes
SELECT 'Checking products with barcodes...' as Step;

-- Check if ProductBarcodes table exists
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='ProductBarcodes') > 0 THEN
            '✅ ProductBarcodes table exists'
        ELSE 
            '❌ ProductBarcodes table not found'
    END as Result;

-- Count products with barcodes (if table exists)
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='ProductBarcodes') > 0 THEN
            '✅ Found ' || (SELECT COUNT(DISTINCT ProductId) FROM ProductBarcodes) || ' products with barcodes'
        ELSE 
            'Cannot check - ProductBarcodes table does not exist'
    END as Result;

-- Check weight-based products with barcodes (if both table and column exist)
SELECT 'Weight-based products with barcodes:' as Info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='ProductBarcodes') > 0 
             AND (SELECT COUNT(*) FROM pragma_table_info('Products') WHERE name = 'IsWeightBased') > 0 THEN
            (SELECT 
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        '✅ Found ' || COUNT(*) || ' weight-based products with barcodes'
                    ELSE 
                        '⚠️ No weight-based products have barcodes yet'
                END
             FROM Products p
             INNER JOIN ProductBarcodes pb ON p.Id = pb.ProductId
             WHERE p.IsWeightBased = 1)
        ELSE 
            '❌ Cannot check - missing table or column'
    END as Result;

-- Summary
SELECT '=== SUMMARY ===' as Summary;

SELECT 
    'Migration Status: ' ||
    CASE 
        WHEN (SELECT COUNT(*) FROM pragma_table_info('Products') WHERE name = 'IsWeightBased') > 0 THEN
            'IsWeightBased column exists ✅'
        ELSE 
            'IsWeightBased column MISSING ❌'
    END as Status;

SELECT 
    'Next Steps: ' ||
    CASE 
        WHEN (SELECT COUNT(*) FROM pragma_table_info('Products') WHERE name = 'IsWeightBased') = 0 THEN
            'Run migration script to add IsWeightBased column'
        WHEN (SELECT COUNT(*) FROM Products WHERE IsWeightBased = 1) = 0 THEN
            'Create test weight-based products'
        ELSE 
            'Test search and barcode functionality'
    END as NextSteps;
