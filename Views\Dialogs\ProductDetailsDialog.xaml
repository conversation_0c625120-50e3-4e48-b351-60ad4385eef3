<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.ProductDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="768.5" d:DesignWidth="500"
             Background="Transparent">

    <UserControl.Resources>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignPaper}"
                         UniformCornerRadius="16"
                         materialDesign:ElevationAssist.Elevation="Dp3"
                         Margin="16"
                         MaxWidth="600">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="16,16,0,0"
                    Padding="24,20">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="InformationOutline" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignPaper}"
                                           Margin="0,0,16,0"/>
                    <TextBlock Text="{DynamicResource ProductDetails}"
                             FontSize="24"
                             FontWeight="Medium"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="28,24" Margin="0,0,22,0">
                <StackPanel>
                    <!-- Image and Basic Info -->
                    <Grid Margin="0,0,0,16" Width="420" Height="173">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Product Image -->
                        <materialDesign:Card Grid.Column="0" 
                                           Background="{DynamicResource PrimaryHueLightBrush}"
                                           UniformCornerRadius="10"
                                           Width="160" 
                                           Height="160"
                                           Margin="0,0,24,0"
                                           Opacity="0.9"
                                           Padding="0">
                            <Grid>
                                <!-- Default Icon (when no image) -->
                                <materialDesign:PackIcon Kind="ShoppingOutline" 
                                                       Width="80" 
                                                       Height="80"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Opacity="0.7">
                                    <materialDesign:PackIcon.Style>
                                        <Style TargetType="materialDesign:PackIcon">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Product.ImageData}" Value="{x:Null}">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Product.ImageData}" Value="">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </materialDesign:PackIcon.Style>
                                </materialDesign:PackIcon>
                                
                                <!-- Product Image -->
                                <Image Source="{Binding Product.ImageData, Converter={StaticResource Base64ToImageConverter}}"
                                       Stretch="Uniform"
                                       Width="140"
                                       Height="140"
                                       RenderOptions.BitmapScalingMode="HighQuality"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                    <Image.Style>
                                        <Style TargetType="Image">
                                            <Setter Property="Visibility" Value="Visible"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Product.ImageData}" Value="{x:Null}">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Product.ImageData}" Value="">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Image.Style>
                                </Image>
                            </Grid>
                        </materialDesign:Card>
                        
                        <!-- Product Info -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Top" Margin="0,4,0,0">
                            <!-- Product Name -->
                            <TextBlock Text="{Binding Product.Name}"
                                     FontSize="24"
                                     FontWeight="SemiBold"
                                     TextWrapping="Wrap"
                                     Margin="0,0,0,8"
                                     Foreground="{DynamicResource PrimaryHueDarkBrush}"/>
                            
                            <!-- Category -->
                            <Grid Margin="0,4,0,4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <materialDesign:PackIcon Grid.Column="0" 
                                                       Kind="Tag"
                                                       Width="16"
                                                       Height="16"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       Opacity="0.7"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                
                                <TextBlock Grid.Column="1" 
                                         Text="{Binding CategoryName}"
                                         FontSize="15"
                                         VerticalAlignment="Center"/>
                            </Grid>
                            
                            <!-- Barcode -->
                            <Grid Margin="0,4,0,4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <materialDesign:PackIcon Grid.Column="0" 
                                                       Kind="Barcode"
                                                       Width="16"
                                                       Height="16"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       Opacity="0.7"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                
                                <TextBlock Grid.Column="1" 
                                         Text="{Binding PrimaryBarcode}"
                                         FontSize="15"
                                         VerticalAlignment="Center"/>
                            </Grid>
                            
                            <!-- Stock Status -->
                            <materialDesign:Card Margin="0,12,0,0"
                                              UniformCornerRadius="8"
                                              Padding="12,8"
                                              Background="{Binding StockStatusBackground}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="{Binding StockStatusIcon}"
                                                           Width="20"
                                                           Height="20"
                                                           Foreground="{Binding StockStatusColor}"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    
                                    <TextBlock Text="{Binding StockStatusText}"
                                             Foreground="{Binding StockStatusColor}"
                                             FontWeight="Medium"
                                             FontSize="15"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Price -->
                    <materialDesign:Card Background="{DynamicResource PrimaryHueDarkBrush}"
                                      UniformCornerRadius="12"
                                      Padding="20,16"
                                      Margin="0,8,0,20" Width="433">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CurrencyUsd"
                                                       Width="24"
                                                       Height="24"
                                                       Foreground="{DynamicResource MaterialDesignPaper}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                
                                <TextBlock Text="{DynamicResource Price}"
                                         FontSize="16"
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource MaterialDesignPaper}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Column="1"
                                     Text="{Binding PriceFormatted}"
                                     FontSize="26"
                                     FontWeight="Bold"
                                     Foreground="{DynamicResource MaterialDesignPaper}"
                                     HorizontalAlignment="Right"
                                     VerticalAlignment="Center"/>
                        </Grid>
                    </materialDesign:Card>
                    
                    <!-- Details Section -->
                    <Separator Margin="0,0,0,16" Background="{DynamicResource MaterialDesignDivider}" Opacity="0.6"/>
                    
                    <TextBlock Text="{DynamicResource ProductDetails}"
                             FontSize="18"
                             FontWeight="SemiBold"
                             Margin="0,0,0,12"
                             Foreground="{DynamicResource PrimaryHueDarkBrush}"/>
                    
                    <!-- Stock Info -->
                    <materialDesign:Card Margin="0,4,0,4"
                                      Background="#0A000000"
                                      UniformCornerRadius="8"
                                      Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="PackageVariantClosed"
                                                   Width="20"
                                                   Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,12,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{DynamicResource StockLevel}"
                                         FontWeight="Medium"
                                         FontSize="15"
                                         Margin="0,0,0,4"/>
                                
                                <TextBlock Text="{Binding StockText}"
                                         FontSize="18"
                                         FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                    
                    <!-- Description (if available) -->
                    <materialDesign:Card Margin="0,16,0,8"
                                      Background="#0A000000"
                                      UniformCornerRadius="8"
                                      Padding="16,12"
                                      Visibility="{Binding HasDescription, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="InformationOutline"
                                                   Width="20"
                                                   Height="20"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Top"
                                                   Margin="0,0,12,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{DynamicResource Description}"
                                         FontWeight="Medium"
                                         FontSize="15"
                                         Margin="0,0,0,8"/>
                                
                                <TextBlock Text="{Binding Product.Description}"
                                         TextWrapping="Wrap"
                                         FontSize="15"
                                         LineHeight="22"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                    
                    <!-- Buttons -->
                    <StackPanel Orientation="Horizontal"
                              HorizontalAlignment="Right"
                              Margin="0,20,0,0">
                        <Button Content="Close"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Width="120"
                              Height="42"
                              FontSize="15"
                              Margin="0,0,12,0"
                              Click="CloseButton_Click"/>

                        <!-- Create Invoice Button (Always Available in Details Dialog) -->
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              Width="158"
                              Height="42"
                              FontSize="15"
                              Background="{DynamicResource PrimaryHueMidBrush}"
                              Margin="0,0,12,0"
                              Click="CreateInvoiceButton_Click"
                              Tag="{Binding Product}"
                              Visibility="{Binding CanCreateInvoices, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocumentPlus"
                                                       Width="20"
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="{DynamicResource CreateInvoice}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              Width="158"
                              Height="42"
                              FontSize="15"
                              IsEnabled="{Binding CanAddToCart}"
                              Background="{DynamicResource SecondaryHueMidBrush}"
                              Click="AddToCartButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CartPlus"
                                                       Width="20"
                                                       Height="20"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="{DynamicResource AddToCart}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </materialDesign:Card>
</UserControl> 