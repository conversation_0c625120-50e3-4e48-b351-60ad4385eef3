using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Documents;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Models.Printing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Linq;
using System.Windows.Media;
using System.Globalization;
using WpfParagraph = System.Windows.Documents.Paragraph;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace POSSystem.Services.Printing
{
    /// <summary>
    /// Service for exporting receipts to PDF format
    /// </summary>
    public class ReceiptPdfExportService
    {
        private readonly ILogger<ReceiptPdfExportService> _logger;

        public ReceiptPdfExportService(ILogger<ReceiptPdfExportService> logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// Export receipt to PDF file using iTextSharp
        /// </summary>
        public async Task<bool> ExportReceiptToPdfAsync(FlowDocument document, string filePath, Sale sale)
        {
            try
            {
                LogInfo($"Starting PDF export for sale {sale.Id} to {filePath}");

                // Validate input parameters
                if (document == null)
                {
                    LogError("Document is null - cannot export");
                    return false;
                }

                if (string.IsNullOrEmpty(filePath))
                {
                    LogError("File path is null or empty - cannot export");
                    return false;
                }

                // Ensure directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (string.IsNullOrEmpty(directory))
                {
                    LogError("Invalid file path - directory is null or empty");
                    return false;
                }

                if (!Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                        LogInfo($"Created directory: {directory}");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to create directory {directory}: {ex.Message}", ex);
                        return false;
                    }
                }

                // Ensure the file has .pdf extension
                if (!filePath.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    filePath = Path.ChangeExtension(filePath, ".pdf");
                }

                // Generate PDF using iTextSharp
                bool success = await Task.Run(() => CreatePdfFromSale(sale, filePath));

                if (success)
                {
                    LogInfo($"Receipt exported successfully to PDF: {filePath}");
                }

                return success;
            }
            catch (Exception ex)
            {
                LogError($"Error exporting receipt to PDF: {ex.Message}", ex);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        $"Error exporting receipt: {ex.Message}\n\nPlease check:\n" +
                        "- File path is valid and accessible\n" +
                        "- You have write permissions to the directory\n" +
                        "- The file is not open in another application",
                        "Export Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                });

                return false;
            }
        }

        /// <summary>
        /// Export receipt to XPS file (alternative to PDF)
        /// </summary>
        public async Task<bool> ExportReceiptToXpsAsync(FlowDocument document, string filePath, Sale sale)
        {
            try
            {
                LogInfo($"Starting XPS export for sale {sale.Id} to {filePath}");

                // Ensure directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await Task.Run(() =>
                {
                    using (var xpsDocument = new XpsDocument(filePath, FileAccess.Write))
                    {
                        var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                        xpsWriter.Write(((IDocumentPaginatorSource)document).DocumentPaginator);
                    }
                });

                LogInfo($"Receipt exported successfully to XPS: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error exporting receipt to XPS: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Get suggested file name for receipt export
        /// </summary>
        public string GetSuggestedFileName(Sale sale, string extension = "xps")
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = $"Receipt_{sale.InvoiceNumber}_{timestamp}.{extension}";
            
            // Remove invalid file name characters
            foreach (var invalidChar in Path.GetInvalidFileNameChars())
            {
                fileName = fileName.Replace(invalidChar, '_');
            }
            
            return fileName;
        }

        /// <summary>
        /// Get default export directory
        /// </summary>
        public string GetDefaultExportDirectory()
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var exportPath = Path.Combine(documentsPath, "POS Receipts");
            
            if (!Directory.Exists(exportPath))
            {
                Directory.CreateDirectory(exportPath);
            }
            
            return exportPath;
        }

        /// <summary>
        /// Check if file already exists and suggest alternative name
        /// </summary>
        public string GetUniqueFileName(string filePath)
        {
            if (!File.Exists(filePath))
                return filePath;

            var directory = Path.GetDirectoryName(filePath);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);
            
            int counter = 1;
            string newFilePath;
            
            do
            {
                var newFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                newFilePath = Path.Combine(directory, newFileName);
                counter++;
            }
            while (File.Exists(newFilePath));
            
            return newFilePath;
        }

        /// <summary>
        /// Validate export path and create directory if needed
        /// </summary>
        public bool ValidateAndCreateExportPath(string filePath, out string errorMessage)
        {
            errorMessage = null;
            
            try
            {
                // Check if path is valid
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    errorMessage = "File path cannot be empty.";
                    return false;
                }

                // Check for invalid characters
                var fileName = Path.GetFileName(filePath);
                if (fileName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
                {
                    errorMessage = "File name contains invalid characters.";
                    return false;
                }

                // Check directory
                var directory = Path.GetDirectoryName(filePath);
                if (string.IsNullOrWhiteSpace(directory))
                {
                    errorMessage = "Invalid directory path.";
                    return false;
                }

                // Create directory if it doesn't exist
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Check if we can write to the directory
                var testFile = Path.Combine(directory, $"test_{Guid.NewGuid()}.tmp");
                try
                {
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                }
                catch
                {
                    errorMessage = "Cannot write to the specified directory. Please check permissions.";
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"Error validating export path: {ex.Message}";
                return false;
            }
        }

        private void LogInfo(string message)
        {
            _logger?.LogInformation(message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF EXPORT] {message}");
        }

        private void LogError(string message, Exception ex = null)
        {
            _logger?.LogError(ex, message);
            System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF EXPORT ERROR] {message}");
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"[RECEIPT PDF EXPORT ERROR] Exception: {ex}");
            }
        }

        /// <summary>
        /// Create PDF document from sale data using iTextSharp
        /// </summary>
        private bool CreatePdfFromSale(Sale sale, string filePath)
        {
            try
            {
                // Create PDF document
                var document = new Document(PageSize.A4, 40, 40, 40, 40);

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                using (var writer = PdfWriter.GetInstance(document, fileStream))
                {
                    document.Open();

                    // Set up fonts
                    var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16, BaseColor.Black);
                    var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12, BaseColor.Black);
                    var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.Black);
                    var smallFont = FontFactory.GetFont(FontFactory.HELVETICA, 8, BaseColor.Gray);

                    // Add title
                    var title = new PdfParagraph("RECEIPT", titleFont)
                    {
                        Alignment = Element.ALIGN_CENTER,
                        SpacingAfter = 20
                    };
                    document.Add(title);

                    // Add company info (if available)
                    AddCompanyInfo(document, normalFont);

                    // Add receipt details
                    AddReceiptDetails(document, sale, headerFont, normalFont);

                    // Add customer info (if available)
                    if (sale.Customer != null)
                    {
                        AddCustomerInfo(document, sale, headerFont, normalFont);
                    }

                    // Add items table
                    AddItemsTable(document, sale, headerFont, normalFont);

                    // Add totals
                    AddTotals(document, sale, headerFont, normalFont);

                    // Add footer
                    AddFooter(document, smallFont);

                    document.Close();
                }

                LogInfo($"PDF created successfully: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error creating PDF: {ex.Message}", ex);
                return false;
            }
        }

        private void AddCompanyInfo(Document document, Font font)
        {
            try
            {
                // Add company name and basic info
                var companyInfo = new PdfParagraph("POS System\nPoint of Sale Receipt", font)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 15
                };
                document.Add(companyInfo);
            }
            catch (Exception ex)
            {
                LogError($"Error adding company info: {ex.Message}", ex);
            }
        }

        private void AddReceiptDetails(Document document, Sale sale, Font headerFont, Font normalFont)
        {
            try
            {
                var table = new PdfPTable(2) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 1, 1 });

                // Receipt details
                table.AddCell(new PdfPCell(new Phrase("Receipt #:", headerFont)) { Border = Rectangle.NO_BORDER });
                table.AddCell(new PdfPCell(new Phrase(sale.InvoiceNumber ?? sale.Id.ToString(), normalFont)) { Border = Rectangle.NO_BORDER });

                table.AddCell(new PdfPCell(new Phrase("Date:", headerFont)) { Border = Rectangle.NO_BORDER });
                table.AddCell(new PdfPCell(new Phrase(sale.SaleDate.ToString("yyyy-MM-dd HH:mm"), normalFont)) { Border = Rectangle.NO_BORDER });

                table.AddCell(new PdfPCell(new Phrase("Payment Method:", headerFont)) { Border = Rectangle.NO_BORDER });
                table.AddCell(new PdfPCell(new Phrase(sale.PaymentMethod ?? "Cash", normalFont)) { Border = Rectangle.NO_BORDER });

                table.SpacingAfter = 15;
                document.Add(table);
            }
            catch (Exception ex)
            {
                LogError($"Error adding receipt details: {ex.Message}", ex);
            }
        }

        private void AddCustomerInfo(Document document, Sale sale, Font headerFont, Font normalFont)
        {
            try
            {
                var customerHeader = new PdfParagraph("Customer Information", headerFont)
                {
                    SpacingBefore = 10,
                    SpacingAfter = 5
                };
                document.Add(customerHeader);

                var customerInfo = new PdfParagraph($"Name: {sale.Customer.Name}\nPhone: {sale.Customer.Phone ?? "N/A"}", normalFont)
                {
                    SpacingAfter = 15
                };
                document.Add(customerInfo);
            }
            catch (Exception ex)
            {
                LogError($"Error adding customer info: {ex.Message}", ex);
            }
        }

        private void AddItemsTable(Document document, Sale sale, Font headerFont, Font normalFont)
        {
            try
            {
                var itemsHeader = new PdfParagraph("Items", headerFont)
                {
                    SpacingBefore = 10,
                    SpacingAfter = 5
                };
                document.Add(itemsHeader);

                var table = new PdfPTable(4) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 3, 1, 1, 1.5f });

                // Table headers
                table.AddCell(new PdfPCell(new Phrase("Product", headerFont)) { BackgroundColor = BaseColor.LightGray });
                table.AddCell(new PdfPCell(new Phrase("Qty", headerFont)) { BackgroundColor = BaseColor.LightGray, HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("Price", headerFont)) { BackgroundColor = BaseColor.LightGray, HorizontalAlignment = Element.ALIGN_RIGHT });
                table.AddCell(new PdfPCell(new Phrase("Total", headerFont)) { BackgroundColor = BaseColor.LightGray, HorizontalAlignment = Element.ALIGN_RIGHT });

                // Add items
                foreach (var item in sale.Items)
                {
                    table.AddCell(new PdfPCell(new Phrase(item.Product?.Name ?? "Unknown Product", normalFont)));
                    table.AddCell(new PdfPCell(new Phrase(item.Quantity.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase($"{item.UnitPrice:N2} DA", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT });
                    table.AddCell(new PdfPCell(new Phrase($"{item.Total:N2} DA", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT });
                }

                table.SpacingAfter = 15;
                document.Add(table);
            }
            catch (Exception ex)
            {
                LogError($"Error adding items table: {ex.Message}", ex);
            }
        }

        private void AddTotals(Document document, Sale sale, Font headerFont, Font normalFont)
        {
            try
            {
                var totalsTable = new PdfPTable(2) { WidthPercentage = 60, HorizontalAlignment = Element.ALIGN_RIGHT };
                totalsTable.SetWidths(new float[] { 1, 1 });

                // Subtotal
                totalsTable.AddCell(new PdfPCell(new Phrase("Subtotal:", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.Subtotal:N2} DA", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });

                // Discount (if any)
                if (sale.DiscountAmount > 0)
                {
                    totalsTable.AddCell(new PdfPCell(new Phrase("Discount:", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                    totalsTable.AddCell(new PdfPCell(new Phrase($"-{sale.DiscountAmount:N2} DA", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                }

                // Tax (if any)
                if (sale.TaxAmount > 0)
                {
                    totalsTable.AddCell(new PdfPCell(new Phrase("Tax:", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                    totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.TaxAmount:N2} DA", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                }

                // Total
                totalsTable.AddCell(new PdfPCell(new Phrase("Total:", headerFont)) { Border = Rectangle.TOP_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.GrandTotal:N2} DA", headerFont)) { Border = Rectangle.TOP_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });

                // Amount Paid
                totalsTable.AddCell(new PdfPCell(new Phrase("Amount Paid:", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.AmountPaid:N2} DA", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });

                // Change (if any)
                var change = sale.AmountPaid - sale.GrandTotal;
                if (change > 0)
                {
                    totalsTable.AddCell(new PdfPCell(new Phrase("Change:", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                    totalsTable.AddCell(new PdfPCell(new Phrase($"{change:N2} DA", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                }

                totalsTable.SpacingAfter = 20;
                document.Add(totalsTable);
            }
            catch (Exception ex)
            {
                LogError($"Error adding totals: {ex.Message}", ex);
            }
        }

        private void AddFooter(Document document, Font font)
        {
            try
            {
                var footer = new PdfParagraph($"Thank you for your business!\nGenerated on {DateTime.Now:yyyy-MM-dd HH:mm:ss}", font)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingBefore = 20
                };
                document.Add(footer);
            }
            catch (Exception ex)
            {
                LogError($"Error adding footer: {ex.Message}", ex);
            }
        }
    }
}
