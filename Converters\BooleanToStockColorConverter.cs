using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace POSSystem.Converters
{
    public class BooleanToStockColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isLowStock)
            {
                return isLowStock 
                    ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#f44336"))  // Red for low stock
                    : new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4caf50")); // Green for normal stock
            }
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9e9e9e")); // Grey for unknown status
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 