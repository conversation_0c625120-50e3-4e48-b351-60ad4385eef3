using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Microsoft.Data.Sqlite;
using System.Reflection;
using System.Linq;
using POSSystem.Services;

namespace POSSystem.Utilities
{
    /// <summary>
    /// Utility class to fix common SQL function typos in the application
    /// </summary>
    public static class SqlFunctionFix
    {
        /// <summary>
        /// Apply SQL function fixes to ensure correct syntax in dynamic queries
        /// </summary>
        public static void ApplyFixes()
        {
            try
            {
                // Log that we're starting the fix process
                Debug.WriteLine("Applying SQL function typo fixes...");
                
                // Get database connection string
                string connectionString = GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    Debug.WriteLine("Failed to get connection string for SQL fixes");
                    return;
                }
                
                // Test and fix the connection by executing a query that uses INSTR correctly
                FixInstrFunctions(connectionString);
                
                Debug.WriteLine("SQL function fixes applied successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying SQL function fixes: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Gets the database connection string
        /// </summary>
        private static string GetConnectionString()
        {
            try
            {
                // Try to get the connection string from DatabaseService
                var dbService = new DatabaseService();
                var fieldInfo = typeof(DatabaseService).GetField("_connectionString", 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                
                if (fieldInfo != null)
                {
                    string connectionString = fieldInfo.GetValue(dbService) as string;
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        return connectionString;
                    }
                }
                
                // Fallback to default location if we can't get it from the service
                return $"Data Source={Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db")}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting connection string: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Test and fix INSTR function usage
        /// </summary>
        private static void FixInstrFunctions(string connectionString)
        {
            try
            {
                using (var connection = new SqliteConnection(connectionString))
                {
                    connection.Open();
                    
                    // Create a temp table for testing
                    using (var command = connection.CreateCommand())
                    {
                        // Drop the temp table if it exists
                        command.CommandText = "DROP TABLE IF EXISTS SqlFunctionTest";
                        command.ExecuteNonQuery();
                        
                        // Create a temp table
                        command.CommandText = "CREATE TABLE SqlFunctionTest (text_value TEXT)";
                        command.ExecuteNonQuery();
                        
                        // Insert a test value
                        command.CommandText = "INSERT INTO SqlFunctionTest VALUES ('test-value')";
                        command.ExecuteNonQuery();
                        
                        // Test a correct INSTR function
                        command.CommandText = "SELECT INSTR(text_value, '-') FROM SqlFunctionTest";
                        var result = command.ExecuteScalar();
                        Debug.WriteLine($"INSTR test result: {result}");
                        
                        // Clean up
                        command.CommandText = "DROP TABLE SqlFunctionTest";
                        command.ExecuteNonQuery();
                    }
                    
                    // If we get to this point without exceptions, INSTR works correctly
                    Debug.WriteLine("INSTR function test succeeded");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error testing/fixing INSTR functions: {ex.Message}");
                
                if (ex.Message.Contains("INSTRO"))
                {
                    Debug.WriteLine("Detected INSTRO typo in SQL functions. This requires manual code fixes.");
                }
            }
        }
    }
}
