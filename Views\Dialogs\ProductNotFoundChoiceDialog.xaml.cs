using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;

namespace POSSystem.Views.Dialogs
{
    public partial class ProductNotFoundChoiceDialog : UserControl
    {
        public string Barcode { get; private set; }
        public bool SearchExternalDatabase { get; private set; }
        public bool CreateNewProduct { get; private set; }

        public ProductNotFoundChoiceDialog(string barcode)
        {
            InitializeComponent();
            Barcode = barcode;
            SearchExternalDatabase = false;
            CreateNewProduct = false;

            // Set the barcode in the display
            txtBarcode.Text = barcode;
        }

        private void SearchExternal_Click(object sender, MouseButtonEventArgs e)
        {
            SearchExternalDatabase = true;
            CreateNewProduct = false;
            DialogHost.Close("SalesDialog", this);
        }

        private void CreateNew_Click(object sender, MouseButtonEventArgs e)
        {
            SearchExternalDatabase = false;
            CreateNewProduct = true;
            DialogHost.Close("SalesDialog", this);
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            SearchExternalDatabase = false;
            CreateNewProduct = false;
            DialogHost.Close("SalesDialog", false);
        }
    }
}
