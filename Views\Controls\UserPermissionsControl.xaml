<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Controls.UserPermissionsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!-- Sales Permissions -->
            <Expander Header="{DynamicResource SalesPermissions}" 
                      IsExpanded="True"
                      Style="{StaticResource MaterialDesignExpander}"
                      Margin="0,0,0,8">
                <StackPanel Margin="16,8">
                    <CheckBox Content="{DynamicResource CreateSales}"
                              IsChecked="{Binding Permissions.CanCreateSales, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource VoidSales}"
                              IsChecked="{Binding Permissions.CanVoidSales, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ApplyDiscounts}"
                              IsChecked="{Binding Permissions.CanApplyDiscount, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ViewSalesHistory}"
                              IsChecked="{Binding Permissions.CanViewSalesHistory, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                </StackPanel>
            </Expander>

            <!-- Product Permissions -->
            <Expander Header="{DynamicResource ProductPermissions}" 
                      IsExpanded="True"
                      Style="{StaticResource MaterialDesignExpander}"
                      Margin="0,0,0,8">
                <StackPanel Margin="16,8">
                    <CheckBox Content="{DynamicResource ManageProducts}"
                              IsChecked="{Binding Permissions.CanManageProducts, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ManageCategories}"
                              IsChecked="{Binding Permissions.CanManageCategories, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ViewInventory}"
                              IsChecked="{Binding Permissions.CanViewInventory, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource AdjustInventory}"
                              IsChecked="{Binding Permissions.CanAdjustInventory, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                </StackPanel>
            </Expander>

            <!-- Financial Permissions -->
            <Expander Header="{DynamicResource FinancialPermissions}" 
                      IsExpanded="True"
                      Style="{StaticResource MaterialDesignExpander}"
                      Margin="0,0,0,8">
                <StackPanel Margin="16,8">
                    <CheckBox Content="{DynamicResource ManageExpenses}"
                              IsChecked="{Binding Permissions.CanManageExpenses, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ManageCashDrawer}"
                              IsChecked="{Binding Permissions.CanManageCashDrawer, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ViewReports}"
                              IsChecked="{Binding Permissions.CanViewReports, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ManagePrices}"
                              IsChecked="{Binding Permissions.CanManagePrices, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                </StackPanel>
            </Expander>

            <!-- Customer & Supplier Permissions -->
            <Expander Header="{DynamicResource CustomerSupplierPermissions}" 
                      IsExpanded="True"
                      Style="{StaticResource MaterialDesignExpander}"
                      Margin="0,0,0,8">
                <StackPanel Margin="16,8">
                    <CheckBox Content="{DynamicResource ManageCustomers}"
                              IsChecked="{Binding Permissions.CanManageCustomers, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ManageSuppliers}"
                              IsChecked="{Binding Permissions.CanManageSuppliers, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                </StackPanel>
            </Expander>

            <!-- Administrative Permissions -->
            <Expander Header="{DynamicResource AdministrativePermissions}" 
                      IsExpanded="True"
                      Style="{StaticResource MaterialDesignExpander}"
                      Margin="0,0,0,8">
                <StackPanel Margin="16,8">
                    <CheckBox Content="{DynamicResource ManageUsers}"
                              IsChecked="{Binding Permissions.CanManageUsers, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ManageRoles}"
                              IsChecked="{Binding Permissions.CanManageRoles, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource AccessSettings}"
                              IsChecked="{Binding Permissions.CanAccessSettings, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                    <CheckBox Content="{DynamicResource ViewLogs}"
                              IsChecked="{Binding Permissions.CanViewLogs, Mode=TwoWay}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Margin="0,4"/>
                </StackPanel>
            </Expander>

            <!-- Quick Actions -->
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        Margin="0,16,0,0">
                <Button Content="{DynamicResource SelectAll}"
                        Command="{Binding SelectAllCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0"/>
                <Button Content="{DynamicResource ClearAll}"
                        Command="{Binding ClearAllCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl> 