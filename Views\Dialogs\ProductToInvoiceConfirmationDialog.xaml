<UserControl x:Class="POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             Background="Transparent">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- Custom styles for the confirmation dialog -->
        <Style x:Key="ConfirmationCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="24"/>
            <Setter Property="MaxWidth" Value="500"/>
            <Setter Property="MaxHeight" Value="600"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        </Style>

        <Style x:Key="ProductInfoCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="0,16"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
        </Style>

        <Style x:Key="EstimatedTotalCard" TargetType="Border">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Margin" Value="0,16"/>
        </Style>
    </UserControl.Resources>

    <materialDesign:Card Style="{StaticResource ConfirmationCard}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="FileDocumentPlus"
                                       Width="32" Height="32"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       VerticalAlignment="Center">
                    <materialDesign:PackIcon.Margin>
                        <Thickness Left="0" Top="0" Right="12" Bottom="0"/>
                    </materialDesign:PackIcon.Margin>
                </materialDesign:PackIcon>
                <TextBlock Text="{Binding DialogTitle}"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Scrollable Content Area -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         MaxHeight="400">
                <StackPanel>
                    <!-- Product Information -->
                    <materialDesign:Card Style="{StaticResource ProductInfoCard}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Product Image -->
                            <Border Grid.Column="0"
                                   Width="80" Height="80"
                                   CornerRadius="8"
                                   Background="{DynamicResource MaterialDesignDivider}">
                                <Border.Margin>
                                    <Thickness Left="0" Top="0" Right="16" Bottom="0"/>
                                </Border.Margin>
                                <Grid>
                                    <Image Source="{Binding ProductImagePath}"
                                           Stretch="UniformToFill"
                                           RenderOptions.BitmapScalingMode="HighQuality"/>
                                    <!-- Fallback icon if no image -->
                                    <materialDesign:PackIcon Kind="Package"
                                                           Width="32" Height="32"
                                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Grid>
                            </Border>

                            <!-- Product Details -->
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="{Binding ProductName}"
                                          FontWeight="Bold"
                                          FontSize="16"
                                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                <TextBlock Text="{Binding ProductPrice}"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                                          FontWeight="Medium"
                                          FontSize="14"
                                          Margin="0,4,0,0"/>
                                <TextBlock Text="{Binding ProductCategory}"
                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Quantity and Customer Selection -->
                    <Grid Margin="0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Quantity Input -->
                        <TextBox Grid.Column="0"
                                materialDesign:HintAssist.Hint="{DynamicResource QuantityHint}"
                                Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                        <!-- Customer Selection -->
                        <ComboBox Grid.Column="2"
                                 materialDesign:HintAssist.Hint="{DynamicResource CustomerOptionalHint}"
                                 ItemsSource="{Binding AvailableCustomers}"
                                 SelectedItem="{Binding SelectedCustomer}"
                                 DisplayMemberPath="FullName"
                                 IsEnabled="{Binding CanSelectCustomer}"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                    </Grid>

                    <!-- Estimated Total -->
                    <Border Style="{StaticResource EstimatedTotalCard}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                <TextBlock Text="{DynamicResource EstimatedTotal}"
                                          FontWeight="Medium"
                                          Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                <TextBlock Text="{Binding QuantityDisplay}"
                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>

                            <TextBlock Grid.Column="1"
                                      Text="{Binding EstimatedTotalDisplay}"
                                      FontWeight="Bold"
                                      FontSize="18"
                                      VerticalAlignment="Center"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        </Grid>
                    </Border>

                    <!-- Permission Message -->
                    <materialDesign:Card Padding="16"
                                       Background="{DynamicResource MaterialDesignCardBackground}"
                                       Margin="0,16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="Information"
                                                       Width="20" Height="20"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Top">
                                    <materialDesign:PackIcon.Margin>
                                        <Thickness Left="0" Top="2" Right="8" Bottom="0"/>
                                    </materialDesign:PackIcon.Margin>
                                </materialDesign:PackIcon>
                                <TextBlock Text="{DynamicResource InvoiceInformation}"
                                          FontWeight="Medium"
                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                            </StackPanel>

                            <TextBlock Text="{Binding PermissionMessage}"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      TextWrapping="Wrap">
                                <TextBlock.Margin>
                                    <Thickness Left="28" Top="0" Right="0" Bottom="0"/>
                                </TextBlock.Margin>
                            </TextBlock>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons (Fixed at bottom) -->
            <StackPanel Grid.Row="2"
                       Orientation="Horizontal"
                       HorizontalAlignment="Right"
                       Margin="0,16,0,0">
                <Button Content="{DynamicResource Cancel}"
                       Command="{Binding CancelCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}">
                    <Button.Margin>
                        <Thickness Left="0" Top="0" Right="8" Bottom="0"/>
                    </Button.Margin>
                </Button>
                <Button Content="{Binding CreateButtonText}"
                       Command="{Binding CreateInvoiceCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
