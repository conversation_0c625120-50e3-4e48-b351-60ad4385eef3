using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ErrorHandling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.SalesManagement
{
    /// <summary>
    /// Focused service for sales management operations
    /// Extracted from the large DatabaseService to improve maintainability
    /// </summary>
    public class SalesManagementService : ISalesManagementService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<SalesManagementService> _logger;
        private readonly IErrorHandlingService _errorHandler;

        public SalesManagementService(
            POSDbContext context,
            ILogger<SalesManagementService> logger = null,
            IErrorHandlingService errorHandler = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _errorHandler = errorHandler;
        }

        /// <summary>
        /// Save a new sale
        /// </summary>
        public async Task<int> SaveSaleAsync(Sale sale)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateSale(sale);

                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Add the sale
                    _context.Sales.Add(sale);
                    await _context.SaveChangesAsync();

                    // Update product stock for each item
                    foreach (var item in sale.Items)
                    {
                        var product = await _context.Products.FindAsync(item.ProductId);
                        if (product != null)
                        {
                            product.StockQuantity -= item.Quantity; // Direct decimal subtraction
                            product.UpdatedAt = DateTime.Now;
                        }
                    }

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger?.LogInformation("Successfully saved sale {SaleId} with {ItemCount} items", sale.Id, sale.Items.Count);
                    return sale.Id;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }, "Save Sale", 0);

            return result;
        }

        /// <summary>
        /// Get sale by ID
        /// </summary>
        public async Task<Sale> GetSaleByIdAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Sale ID must be greater than 0", nameof(id));

                return await _context.Sales
                    .AsNoTracking()
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Customer)
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.Id == id);
            }, "Get Sale By ID", null);

            return result;
        }

        /// <summary>
        /// Get sales for a date range
        /// </summary>
        public async Task<List<Sale>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Sales
                    .AsNoTracking()
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Customer)
                    .Include(s => s.User)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync();
            }, "Get Sales For Period", new List<Sale>());

            return result ?? new List<Sale>();
        }

        /// <summary>
        /// Get recent sales with limit
        /// </summary>
        public async Task<List<Sale>> GetRecentSalesAsync(int limit = 50)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Sales
                    .AsNoTracking()
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Customer)
                    .Include(s => s.User)
                    .OrderByDescending(s => s.SaleDate)
                    .Take(limit)
                    .ToListAsync();
            }, "Get Recent Sales", new List<Sale>());

            return result ?? new List<Sale>();
        }

        /// <summary>
        /// Get sales total for a specific date
        /// </summary>
        public async Task<decimal> GetSalesTotalAsync(DateTime date)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddSeconds(-1);

                return await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .SumAsync(s => s.GrandTotal);
            }, "Get Sales Total", 0m);

            return result;
        }

        /// <summary>
        /// Get sales count and total for a period
        /// </summary>
        public async Task<(int count, decimal total)> GetSalesCountAndTotalAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                var sales = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => s.GrandTotal)
                    .ToListAsync();

                return (sales.Count, sales.Sum());
            }, "Get Sales Count And Total", (0, 0m));

            return result;
        }

        /// <summary>
        /// Get unpaid sales
        /// </summary>
        public async Task<List<Sale>> GetUnpaidSalesAsync()
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Sales
                    .AsNoTracking()
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Customer)
                    .Where(s => s.PaymentStatus == "Unpaid" || s.PaymentStatus == "Partial")
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync();
            }, "Get Unpaid Sales", new List<Sale>());

            return result ?? new List<Sale>();
        }

        /// <summary>
        /// Update sale
        /// </summary>
        public async Task<bool> UpdateSaleAsync(Sale sale)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateSale(sale);

                if (sale.Id <= 0)
                    throw new ArgumentException("Sale ID must be greater than 0 for updates", nameof(sale));

                var existingSale = await _context.Sales
                    .Include(s => s.Items)
                    .FirstOrDefaultAsync(s => s.Id == sale.Id);

                if (existingSale == null)
                    throw new InvalidOperationException($"Sale with ID {sale.Id} not found");

                // Update sale properties
                existingSale.GrandTotal = sale.GrandTotal;
                existingSale.PaymentMethod = sale.PaymentMethod;
                existingSale.PaymentStatus = sale.PaymentStatus;
                existingSale.DiscountAmount = sale.DiscountAmount;
                existingSale.TaxAmount = sale.TaxAmount;
                existingSale.CustomerId = sale.CustomerId;

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated sale {SaleId}", sale.Id);
                return true;
            }, "Update Sale", false);

            return result;
        }

        /// <summary>
        /// Delete sale
        /// </summary>
        public async Task<bool> DeleteSaleAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Sale ID must be greater than 0", nameof(id));

                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    var sale = await _context.Sales
                        .Include(s => s.Items)
                        .FirstOrDefaultAsync(s => s.Id == id);

                    if (sale == null)
                        throw new InvalidOperationException($"Sale with ID {id} not found");

                    // Restore product stock
                    foreach (var item in sale.Items)
                    {
                        var product = await _context.Products.FindAsync(item.ProductId);
                        if (product != null)
                        {
                            product.StockQuantity += (int)Math.Ceiling(item.Quantity); // Round up for stock restoration
                            product.UpdatedAt = DateTime.Now;
                        }
                    }

                    // Remove sale items first (due to foreign key constraints)
                    _context.SaleItems.RemoveRange(sale.Items);
                    
                    // Remove the sale
                    _context.Sales.Remove(sale);

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger?.LogInformation("Successfully deleted sale {SaleId}", id);
                    return true;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }, "Delete Sale", false);

            return result;
        }

        /// <summary>
        /// Get sales by payment method
        /// </summary>
        public async Task<List<Sale>> GetSalesByPaymentMethodAsync(string paymentMethod, DateTime startDate, DateTime endDate)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (string.IsNullOrWhiteSpace(paymentMethod))
                    throw new ArgumentException("Payment method cannot be empty", nameof(paymentMethod));

                return await _context.Sales
                    .AsNoTracking()
                    .Include(s => s.Items)
                        .ThenInclude(si => si.Product)
                    .Include(s => s.Customer)
                    .Where(s => s.PaymentMethod == paymentMethod &&
                               s.SaleDate >= startDate &&
                               s.SaleDate <= endDate)
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync();
            }, "Get Sales By Payment Method", new List<Sale>());

            return result ?? new List<Sale>();
        }

        /// <summary>
        /// Get average transaction value for a period
        /// </summary>
        public async Task<decimal> GetAverageTransactionValueAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                var sales = await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => s.GrandTotal)
                    .ToListAsync();

                return sales.Any() ? sales.Average() : 0m;
            }, "Get Average Transaction Value", 0m);

            return result;
        }

        /// <summary>
        /// Get payment method distribution for a period
        /// </summary>
        public async Task<Dictionary<string, decimal>> GetPaymentMethodDistributionAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Sales
                    .AsNoTracking()
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .GroupBy(s => s.PaymentMethod)
                    .Select(g => new { PaymentMethod = g.Key, Total = g.Sum(s => s.GrandTotal) })
                    .ToDictionaryAsync(x => x.PaymentMethod, x => x.Total);
            }, "Get Payment Method Distribution", new Dictionary<string, decimal>());

            return result ?? new Dictionary<string, decimal>();
        }

        /// <summary>
        /// Validate sale data
        /// </summary>
        private void ValidateSale(Sale sale)
        {
            if (sale == null)
                throw new ArgumentNullException(nameof(sale), "Sale cannot be null");

            if (sale.Items == null || !sale.Items.Any())
                throw new ArgumentException("Sale must have at least one item", nameof(sale));

            if (sale.GrandTotal < 0)
                throw new ArgumentException("Grand total cannot be negative", nameof(sale));

            if (string.IsNullOrWhiteSpace(sale.PaymentMethod))
                throw new ArgumentException("Payment method cannot be empty", nameof(sale));

            foreach (var item in sale.Items)
            {
                if (item.Quantity <= 0)
                    throw new ArgumentException($"Item quantity must be greater than 0 for product {item.ProductId}", nameof(sale));

                if (item.UnitPrice < 0)
                    throw new ArgumentException($"Item unit price cannot be negative for product {item.ProductId}", nameof(sale));
            }
        }
    }
}
