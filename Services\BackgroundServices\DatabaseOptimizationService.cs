using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using POSSystem.Services.QueryOptimization;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace POSSystem.Services.BackgroundServices
{
    /// <summary>
    /// Background service that applies database optimizations on startup
    /// </summary>
    public class DatabaseOptimizationService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DatabaseOptimizationService> _logger;

        public DatabaseOptimizationService(
            IServiceProvider serviceProvider,
            ILogger<DatabaseOptimizationService> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                // Wait a bit for the application to fully start
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

                if (stoppingToken.IsCancellationRequested)
                    return;

                _logger.LogInformation("Starting database optimization service...");

                using var scope = _serviceProvider.CreateScope();
                var indexService = scope.ServiceProvider.GetRequiredService<DatabaseIndexService>();

                // Check if indexes are already optimized
                var isOptimized = await indexService.AreIndexesOptimizedAsync();
                
                if (!isOptimized)
                {
                    _logger.LogInformation("Database indexes not optimized, applying performance indexes...");
                    await indexService.ApplyPerformanceIndexesAsync();
                    
                    // Analyze database for better query planning
                    await indexService.AnalyzeDatabaseAsync();
                    
                    _logger.LogInformation("Database optimization completed successfully");
                }
                else
                {
                    _logger.LogInformation("Database indexes already optimized");
                }

                // Get and log database statistics
                var stats = await indexService.GetDatabaseStatsAsync();
                _logger.LogInformation("Database Statistics: {Stats}", stats.ToString());

                // Schedule periodic optimization (once per day)
                await SchedulePeriodicOptimizationAsync(indexService, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in database optimization service");
            }
        }

        private async Task SchedulePeriodicOptimizationAsync(DatabaseIndexService indexService, CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    // Wait 24 hours
                    await Task.Delay(TimeSpan.FromHours(24), stoppingToken);

                    if (stoppingToken.IsCancellationRequested)
                        break;

                    _logger.LogInformation("Running periodic database optimization...");

                    try
                    {
                        // Analyze database statistics
                        await indexService.AnalyzeDatabaseAsync();
                        
                        // Get updated statistics
                        var stats = await indexService.GetDatabaseStatsAsync();
                        _logger.LogInformation("Periodic optimization completed. Database Statistics: {Stats}", stats.ToString());
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error during periodic database optimization");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                _logger.LogInformation("Database optimization service stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in periodic database optimization");
            }
        }
    }

    /// <summary>
    /// Service for manual database optimization operations
    /// </summary>
    public class ManualDatabaseOptimizationService
    {
        private readonly DatabaseIndexService _indexService;
        private readonly ILogger<ManualDatabaseOptimizationService> _logger;

        public ManualDatabaseOptimizationService(
            DatabaseIndexService indexService,
            ILogger<ManualDatabaseOptimizationService> logger = null)
        {
            _indexService = indexService ?? throw new ArgumentNullException(nameof(indexService));
            _logger = logger;
        }

        /// <summary>
        /// Perform full database optimization
        /// </summary>
        public async Task<OptimizationResult> OptimizeDatabaseAsync()
        {
            var result = new OptimizationResult();
            var startTime = DateTime.Now;

            try
            {
                _logger?.LogInformation("Starting manual database optimization...");

                // Step 1: Apply indexes
                var indexesOptimized = await _indexService.AreIndexesOptimizedAsync();
                if (!indexesOptimized)
                {
                    await _indexService.ApplyPerformanceIndexesAsync();
                    result.IndexesApplied = true;
                }

                // Step 2: Analyze database
                await _indexService.AnalyzeDatabaseAsync();
                result.DatabaseAnalyzed = true;

                // Step 3: Get statistics
                result.Statistics = await _indexService.GetDatabaseStatsAsync();

                result.Success = true;
                result.Duration = DateTime.Now - startTime;
                result.Message = "Database optimization completed successfully";

                _logger?.LogInformation("Manual database optimization completed in {Duration}ms", result.Duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Error = ex.Message;
                result.Duration = DateTime.Now - startTime;
                
                _logger?.LogError(ex, "Error during manual database optimization");
            }

            return result;
        }

        /// <summary>
        /// Perform database file optimization (VACUUM)
        /// </summary>
        public async Task<OptimizationResult> OptimizeDatabaseFileAsync()
        {
            var result = new OptimizationResult();
            var startTime = DateTime.Now;

            try
            {
                _logger?.LogInformation("Starting database file optimization (VACUUM)...");

                // Get size before optimization
                var statsBefore = await _indexService.GetDatabaseStatsAsync();
                
                // Perform VACUUM
                await _indexService.OptimizeDatabaseFileAsync();
                
                // Get size after optimization
                var statsAfter = await _indexService.GetDatabaseStatsAsync();
                
                result.Success = true;
                result.Duration = DateTime.Now - startTime;
                result.Statistics = statsAfter;
                result.Message = $"Database file optimized. Size reduced from {statsBefore.DatabaseSizeMB:F2} MB to {statsAfter.DatabaseSizeMB:F2} MB";

                _logger?.LogInformation("Database file optimization completed in {Duration}ms. {Message}", 
                    result.Duration.TotalMilliseconds, result.Message);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Error = ex.Message;
                result.Duration = DateTime.Now - startTime;
                
                _logger?.LogError(ex, "Error during database file optimization");
            }

            return result;
        }

        /// <summary>
        /// Get current database optimization status
        /// </summary>
        public async Task<OptimizationStatus> GetOptimizationStatusAsync()
        {
            try
            {
                var status = new OptimizationStatus
                {
                    IsOptimized = await _indexService.AreIndexesOptimizedAsync(),
                    Statistics = await _indexService.GetDatabaseStatsAsync(),
                    IndexInformation = await _indexService.GetIndexInformationAsync(),
                    CheckedAt = DateTime.Now
                };

                return status;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting optimization status");
                return new OptimizationStatus
                {
                    IsOptimized = false,
                    Error = ex.Message,
                    CheckedAt = DateTime.Now
                };
            }
        }
    }

    /// <summary>
    /// Result of database optimization operation
    /// </summary>
    public class OptimizationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string Error { get; set; }
        public TimeSpan Duration { get; set; }
        public bool IndexesApplied { get; set; }
        public bool DatabaseAnalyzed { get; set; }
        public DatabaseStats Statistics { get; set; }
    }

    /// <summary>
    /// Current database optimization status
    /// </summary>
    public class OptimizationStatus
    {
        public bool IsOptimized { get; set; }
        public DatabaseStats Statistics { get; set; }
        public string IndexInformation { get; set; }
        public string Error { get; set; }
        public DateTime CheckedAt { get; set; }
    }
}
