using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class UnitOfMeasureNameConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return string.Empty;
            
            string name = value.ToString();
            string resourceKey = $"UnitOfMeasure_{name}";
            
            try
            {
                var translation = Application.Current.TryFindResource(resourceKey);
                return translation != null ? translation.ToString() : name;
            }
            catch
            {
                return name;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 