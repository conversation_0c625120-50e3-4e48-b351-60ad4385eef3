-- Create temporary table with correct schema
CREATE TABLE IF NOT EXISTS Categories_temp (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT NULL
);

-- Copy data from old table to temp table
INSERT INTO Categories_temp (Id, Name, Description)
SELECT Id, Name, COALESCE(Description, '') FROM Categories;

-- Drop the old table
DROP TABLE Categories;

-- Rename temp table to Categories
ALTER TABLE Categories_temp RENAME TO Categories; 