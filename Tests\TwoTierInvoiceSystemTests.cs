using System;
using System.Threading.Tasks;
using POSSystem.Models;
using POSSystem.Models.DTOs;
using POSSystem.Services;

namespace POSSystem.Tests
{
    /// <summary>
    /// Basic tests for the two-tier invoice system Phase 1 implementation
    /// </summary>
    public class TwoTierInvoiceSystemTests
    {
        private readonly DatabaseService _dbService;
        private readonly AuthenticationService _authService;
        private readonly UserPermissionsService _permissionsService;
        private readonly DraftInvoiceService _draftInvoiceService;
        private readonly DraftInvoiceNotificationService _notificationService;
        private readonly TwoTierInvoiceSystemInitializer _initializer;

        public TwoTierInvoiceSystemTests()
        {
            // Initialize services (in a real test, these would be mocked or use a test database)
            _dbService = new DatabaseService();
            _authService = new AuthenticationService(_dbService);
            _permissionsService = new UserPermissionsService(_authService, _dbService);
            _notificationService = new DraftInvoiceNotificationService(_dbService, _authService);
            _draftInvoiceService = new DraftInvoiceService(_dbService, _notificationService, _permissionsService);
            _initializer = new TwoTierInvoiceSystemInitializer(_dbService);
        }

        /// <summary>
        /// Test the complete Phase 1 setup
        /// </summary>
        public async Task<bool> RunPhase1TestsAsync()
        {
            try
            {
                Console.WriteLine("=== Two-Tier Invoice System Phase 1 Tests ===\n");

                // Test 1: System Initialization
                Console.WriteLine("Test 1: System Initialization");
                var initResult = await TestSystemInitializationAsync();
                Console.WriteLine($"Result: {(initResult ? "PASS" : "FAIL")}\n");

                // Test 2: Permission System
                Console.WriteLine("Test 2: Permission System");
                var permissionResult = await TestPermissionSystemAsync();
                Console.WriteLine($"Result: {(permissionResult ? "PASS" : "FAIL")}\n");

                // Test 3: Draft Invoice Creation (Mock)
                Console.WriteLine("Test 3: Draft Invoice Creation (Mock)");
                var draftResult = await TestDraftInvoiceCreationAsync();
                Console.WriteLine($"Result: {(draftResult ? "PASS" : "FAIL")}\n");

                // Test 4: Settings Management
                Console.WriteLine("Test 4: Settings Management");
                var settingsResult = await TestSettingsManagementAsync();
                Console.WriteLine($"Result: {(settingsResult ? "PASS" : "FAIL")}\n");

                // Test 5: System Status
                Console.WriteLine("Test 5: System Status");
                var statusResult = await TestSystemStatusAsync();
                Console.WriteLine($"Result: {(statusResult ? "PASS" : "FAIL")}\n");

                var allTestsPassed = initResult && permissionResult && draftResult && settingsResult && statusResult;
                
                Console.WriteLine("=== Test Summary ===");
                Console.WriteLine($"Overall Result: {(allTestsPassed ? "ALL TESTS PASSED" : "SOME TESTS FAILED")}");
                
                return allTestsPassed;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test execution failed: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TestSystemInitializationAsync()
        {
            try
            {
                var result = await _initializer.InitializeAsync();
                Console.WriteLine($"  - Database initialization: {(result ? "Success" : "Failed")}");
                
                if (result)
                {
                    var status = await _initializer.GetSystemStatusAsync();
                    Console.WriteLine($"  - System status: {status.StatusMessage}");
                    return status.IsInitialized;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TestPermissionSystemAsync()
        {
            try
            {
                // Test default permissions for different roles
                var adminPermissions = _permissionsService.CreateDefaultPermissions("Admin");
                var cashierPermissions = _permissionsService.CreateDefaultPermissions("Cashier");
                
                Console.WriteLine($"  - Admin can create full invoices: {adminPermissions.CanCreateFullInvoices}");
                Console.WriteLine($"  - Admin can complete drafts: {adminPermissions.CanCompleteInvoiceDrafts}");
                Console.WriteLine($"  - Cashier can create drafts: {cashierPermissions.CanCreateDraftInvoices}");
                Console.WriteLine($"  - Cashier cannot create full invoices: {!cashierPermissions.CanCreateFullInvoices}");
                
                // Verify expected behavior
                var expectedResults = adminPermissions.CanCreateFullInvoices &&
                                    adminPermissions.CanCompleteInvoiceDrafts &&
                                    cashierPermissions.CanCreateDraftInvoices &&
                                    !cashierPermissions.CanCreateFullInvoices;
                
                return expectedResults;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TestDraftInvoiceCreationAsync()
        {
            try
            {
                // Create a mock draft invoice DTO
                var draftDto = new DraftInvoiceDto
                {
                    InvoiceNumber = "TEST-DRAFT-001",
                    Type = "Sales",
                    IssueDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CreatedByUserId = 1, // Mock user ID
                    RequiresAdminCompletion = true
                };

                // Add a mock item
                draftDto.Items.Add(new DraftInvoiceItemDto
                {
                    ProductId = 1,
                    ProductName = "Test Product",
                    Quantity = 2,
                    UnitPrice = 10.00m
                });

                draftDto.CalculateTotals();

                // Validate the DTO
                var isValid = draftDto.IsValid(out var errors);
                Console.WriteLine($"  - Draft DTO validation: {(isValid ? "Valid" : "Invalid")}");
                
                if (!isValid)
                {
                    foreach (var error in errors)
                    {
                        Console.WriteLine($"    - Error: {error}");
                    }
                }

                Console.WriteLine($"  - Calculated subtotal: {draftDto.Subtotal:C}");
                Console.WriteLine($"  - Item count: {draftDto.ItemCount}");
                
                return isValid && draftDto.Subtotal == 20.00m && draftDto.ItemCount == 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TestSettingsManagementAsync()
        {
            try
            {
                // Test default settings creation
                var defaultSettings = DraftInvoiceSettings.GetDefaultSettings();
                Console.WriteLine($"  - Default expiration days: {defaultSettings.ExpirationDays}");
                Console.WriteLine($"  - Auto notify admins: {defaultSettings.AutoNotifyAdmins}");
                Console.WriteLine($"  - Max items per draft: {defaultSettings.MaxDraftItemsPerInvoice}");
                
                // Test settings validation
                var isValid = defaultSettings.IsValid(out var errorMessage);
                Console.WriteLine($"  - Settings validation: {(isValid ? "Valid" : $"Invalid - {errorMessage}")}");
                
                // Test expiration calculation
                var testDate = DateTime.Now.AddDays(-10);
                var isExpired = defaultSettings.IsDraftExpired(testDate);
                Console.WriteLine($"  - Draft expiration test (10 days old): {(isExpired ? "Expired" : "Not expired")}");
                
                return isValid && isExpired; // Should be expired after 10 days with default 7-day expiration
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TestSystemStatusAsync()
        {
            try
            {
                var status = await _initializer.GetSystemStatusAsync();
                
                Console.WriteLine($"  - System initialized: {status.IsInitialized}");
                Console.WriteLine($"  - Pending drafts: {status.PendingDraftsCount}");
                Console.WriteLine($"  - Total drafts: {status.TotalDraftsCount}");
                Console.WriteLine($"  - Unread notifications: {status.UnreadNotificationsCount}");
                Console.WriteLine($"  - Status message: {status.StatusMessage}");
                
                return status.IsInitialized;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test notification creation (mock)
        /// </summary>
        public async Task<bool> TestNotificationSystemAsync()
        {
            try
            {
                Console.WriteLine("=== Notification System Test ===");
                
                // Test notification creation methods
                var draftCreatedNotification = DraftInvoiceNotification.CreateDraftCreatedNotification(
                    1, 1, "TEST-001");
                
                var draftCompletedNotification = DraftInvoiceNotification.CreateDraftCompletedNotification(
                    1, 1, "TEST-001", "Admin User");
                
                Console.WriteLine($"  - Draft created notification: {draftCreatedNotification.Message}");
                Console.WriteLine($"  - Draft completed notification: {draftCompletedNotification.Message}");
                Console.WriteLine($"  - Notification icon: {draftCreatedNotification.IconKind}");
                Console.WriteLine($"  - Notification color: {draftCreatedNotification.NotificationColor}");
                
                return !string.IsNullOrEmpty(draftCreatedNotification.Message) &&
                       !string.IsNullOrEmpty(draftCompletedNotification.Message);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Run a comprehensive test of all Phase 1 components
        /// </summary>
        public static async Task<bool> RunComprehensiveTestAsync()
        {
            try
            {
                var tests = new TwoTierInvoiceSystemTests();
                
                Console.WriteLine("Starting comprehensive Phase 1 tests...\n");
                
                var phase1Result = await tests.RunPhase1TestsAsync();
                var notificationResult = await tests.TestNotificationSystemAsync();
                
                var overallResult = phase1Result && notificationResult;
                
                Console.WriteLine("\n=== Final Results ===");
                Console.WriteLine($"Phase 1 Tests: {(phase1Result ? "PASSED" : "FAILED")}");
                Console.WriteLine($"Notification Tests: {(notificationResult ? "PASSED" : "FAILED")}");
                Console.WriteLine($"Overall: {(overallResult ? "ALL TESTS PASSED" : "SOME TESTS FAILED")}");
                
                if (overallResult)
                {
                    Console.WriteLine("\n✅ Two-Tier Invoice System Phase 1 is ready for Phase 2!");
                }
                else
                {
                    Console.WriteLine("\n❌ Issues found in Phase 1 implementation. Please review and fix before proceeding.");
                }
                
                return overallResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Comprehensive test failed: {ex.Message}");
                return false;
            }
        }
    }
}
