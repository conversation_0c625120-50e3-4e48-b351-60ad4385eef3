using System.Threading.Tasks;
using POSSystem.Views.Dialogs;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Localized replacement for MessageBox that shows properly translated button text
    /// </summary>
    public static class LocalizedMessageBox
    {
        public enum MessageBoxResult
        {
            None = 0,
            OK = 1,
            Cancel = 2,
            Yes = 6,
            No = 7
        }

        public enum MessageBoxButton
        {
            OK = 0,
            OKCancel = 1,
            YesNoCancel = 3,
            YesNo = 4
        }

        public enum MessageBoxImage
        {
            None = 0,
            Error = 16,
            Question = 32,
            Warning = 48,
            Information = 64
        }

        public enum ThreeOptionResult
        {
            None = 0,
            FirstOption = 1,
            SecondOption = 2,
            Cancel = 3
        }

        /// <summary>
        /// Shows a localized message dialog with translated button text
        /// </summary>
        public static async Task<MessageBoxResult> ShowAsync(
            string message, 
            string title = "", 
            MessageBoxButton button = MessageBoxButton.OK, 
            MessageBoxImage icon = MessageBoxImage.Information)
        {
            var result = await LocalizedMessageDialog.ShowAsync(
                message, 
                title, 
                (LocalizedMessageDialog.MessageBoxButton)button, 
                (LocalizedMessageDialog.MessageBoxImage)icon);

            return (MessageBoxResult)result;
        }

        /// <summary>
        /// Shows a localized information message
        /// </summary>
        public static async Task ShowInfoAsync(string message, string title = "")
        {
            await ShowAsync(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Shows a localized error message
        /// </summary>
        public static async Task ShowErrorAsync(string message, string title = "")
        {
            await ShowAsync(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// Shows a localized warning message
        /// </summary>
        public static async Task ShowWarningAsync(string message, string title = "")
        {
            await ShowAsync(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// Shows a localized confirmation dialog
        /// </summary>
        public static async Task<bool> ShowConfirmationAsync(string message, string title = "")
        {
            var result = await ShowAsync(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// Shows a localized dialog with three custom options
        /// </summary>
        public static ThreeOptionResult ShowThreeOption(
            string message,
            string title = "",
            string firstOptionText = "Option 1",
            string secondOptionText = "Option 2",
            string cancelText = "Cancel",
            MessageBoxImage icon = MessageBoxImage.Question)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] ShowThreeOption called");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Message: '{message.Substring(0, Math.Min(50, message.Length))}...'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Title: '{title}'");

            try
            {
                // Use Task.Run to avoid deadlocks
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Calling ShowThreeOptionAsync via Task.Run");
                var result = Task.Run(async () => await ShowThreeOptionAsync(message, title, firstOptionText, secondOptionText, cancelText, icon)).Result;
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] ShowThreeOptionAsync completed with result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Exception in ShowThreeOption: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Falling back to standard MessageBox");

                // Fallback to standard MessageBox with Yes/No/Cancel if there are issues
                var standardResult = System.Windows.MessageBox.Show(message, title,
                    System.Windows.MessageBoxButton.YesNoCancel,
                    (System.Windows.MessageBoxImage)icon);

                return standardResult switch
                {
                    System.Windows.MessageBoxResult.Yes => ThreeOptionResult.FirstOption,
                    System.Windows.MessageBoxResult.No => ThreeOptionResult.SecondOption,
                    _ => ThreeOptionResult.Cancel
                };
            }
        }

        /// <summary>
        /// Shows a localized dialog with three custom options (async version)
        /// </summary>
        public static async Task<ThreeOptionResult> ShowThreeOptionAsync(
            string message,
            string title = "",
            string firstOptionText = "Option 1",
            string secondOptionText = "Option 2",
            string cancelText = "Cancel",
            MessageBoxImage icon = MessageBoxImage.Question)
        {
            var result = await LocalizedThreeOptionDialog.ShowAsync(
                message,
                title,
                firstOptionText,
                secondOptionText,
                cancelText,
                (LocalizedThreeOptionDialog.MessageBoxImage)icon);

            return (ThreeOptionResult)result;
        }

        /// <summary>
        /// Shows a localized yes/no/cancel dialog
        /// </summary>
        public static async Task<MessageBoxResult> ShowYesNoCancelAsync(string message, string title = "")
        {
            return await ShowAsync(message, title, MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
        }

        /// <summary>
        /// Synchronous wrapper for ShowAsync - use only when async is not possible
        /// </summary>
        public static MessageBoxResult Show(
            string message,
            string title = "",
            MessageBoxButton button = MessageBoxButton.OK,
            MessageBoxImage icon = MessageBoxImage.Information)
        {
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Synchronous Show called");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Message: '{message.Substring(0, Math.Min(50, message.Length))}...'");
            System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Title: '{title}'");

            try
            {
                // Use Task.Run to avoid deadlocks
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Calling ShowAsync via Task.Run");
                var result = Task.Run(async () => await ShowAsync(message, title, button, icon)).Result;
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] ShowAsync completed with result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Exception in Show: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[LocalizedMessageBox] Falling back to standard MessageBox");

                // Fallback to standard MessageBox if there are issues
                var standardResult = System.Windows.MessageBox.Show(message, title,
                    (System.Windows.MessageBoxButton)button,
                    (System.Windows.MessageBoxImage)icon);
                return (MessageBoxResult)standardResult;
            }
        }
    }
}
