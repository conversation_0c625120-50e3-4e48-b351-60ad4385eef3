using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using System.Configuration;
using POSSystem.Models;  // Add reference to Models namespace
using System.Linq;
using System.Collections.Generic;
using System.Windows.Media;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Data;  // Added this line
using POSSystem.ViewModels;  // Add this line for ViewModels
using MaterialDesignThemes.Wpf;  // Add this for MaterialDesign controls
using POSSystem.Views.Controls;  // Add this line for NotificationPopup
using System.Collections.ObjectModel;  // Add this line for ObservableCollection
using System.Threading.Tasks;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using POSSystem.Views.Layouts;
using System.Windows.Interop;  // Add this for HwndSource
using Microsoft.Extensions.DependencyInjection;  // Add this for GetService<T>()
using POSSystem.Views.Dialogs;  // Add this for ProductDialog

namespace POSSystem.Views
{
    public partial class MainWindow : Window, IDisposable, INotifyPropertyChanged
    {
        private DateTime sessionStartTime;
        private User currentUser;
        private bool isSidebarExpanded = false; // Always start collapsed
        private int unreadNotifications = 3; // Example initial value
        private DispatcherTimer clockTimer;
        private readonly IAlertService _alertService;
        private DispatcherTimer _alertTimer;
        private SaleViewModel _salesViewModel;
        private Button[] _navigationButtons;
        private readonly IAuthenticationService _authService;
        private int _cachedUnreadCount = 0;
        private DateTime _lastNotificationCheck = DateTime.MinValue;
        private DispatcherTimer _combinedTimer;
        private Dictionary<string, WeakReference<UserControl>> _viewCache;
        private bool _isDisposed;
        private List<ProductAlert> _cachedAlerts;
        private List<Notification> _cachedNotifications;
        private readonly POSDbContext _context;
        private readonly DatabaseService _dbService;
        private bool _isLoading;
        private Style _defaultNavStyle;
        private Style _activeNavStyle;
        private const int NOTIFICATION_CACHE_DURATION_SECONDS = 30;
        private const int NOTIFICATION_BATCH_SIZE = 20;
        private bool _isLoadingNotifications;
        private readonly SettingsService _settingsService;
        private readonly ThemeService _themeService;
        private UserControl _currentView;
        private const int WM_SWITCHUSER = 0x0400 + 1;

        // Add the property for CurrentView
        public string CurrentView { get; set; }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsLoading)));
                }
            }
        }

        public MainWindow()
        {
            System.Diagnostics.Debug.WriteLine("[MAINWINDOW] Parameterless constructor called - THIS SHOULD NOT HAPPEN!");
            InitializeComponent();
            
            // Set FlowDirection based on current language
            string language = ConfigurationManager.AppSettings["Language"] ?? "en";
            this.FlowDirection = language == "ar" ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
            
            // Initialize settings and services
            _viewCache = new Dictionary<string, WeakReference<UserControl>>();
            _cachedAlerts = new List<ProductAlert>();
            _cachedNotifications = new List<Notification>();
            
            // Load application theme settings
            _settingsService = new SettingsService();
            _themeService = new ThemeService();
            _dbService = App.ServiceProvider?.GetService<DatabaseService>() ?? new DatabaseService();
            _authService = new AuthenticationService(_dbService);
            _context = new POSDbContext();
            _alertService = new AlertService(_context, _dbService);
            
            // Set up keyboard handling for global shortcuts
            KeyDown += MainWindow_KeyDown;

            // Initialize other UI components
            CurrentView = "Sales";
            _navigationButtons = new Button[0];
            
            // Set up timer handlers
            _alertTimer = new DispatcherTimer();
            _alertTimer.Interval = TimeSpan.FromMinutes(15); // Check alerts every 15 minutes
            _alertTimer.Tick += (sender, e) => CheckForAlerts();
            
            _combinedTimer = new DispatcherTimer();
            _combinedTimer.Interval = TimeSpan.FromMinutes(5); // Check every 5 minutes
            _combinedTimer.Tick += (sender, e) => RefreshNotificationsAndAlerts();
            
            Loaded += MainWindow_Loaded;
        }

        public MainWindow(IAuthenticationService authService)
        {
            System.Diagnostics.Debug.WriteLine("[MAINWINDOW] Constructor with AuthenticationService called - THIS IS CORRECT!");
            InitializeComponent();
            
            // Set FlowDirection based on current language
            string language = ConfigurationManager.AppSettings["Language"] ?? "en";
            this.FlowDirection = language == "ar" ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
            
            // Add message hook for user switching
            var source = PresentationSource.FromVisual(this) as HwndSource;
            if (source != null)
            {
                source.AddHook(WndProc);
            }
            
            InitializeViewCache();
            
            // Set window to full screen
            WindowState = WindowState.Maximized;
            
            _authService = authService;
            System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] AuthService received: {_authService?.CurrentUser?.Username ?? "NULL"} (ID: {_authService?.CurrentUser?.Id ?? -1})");
            // ✅ Use proper DI for AlertService
            _alertService = App.ServiceProvider?.GetService<IAlertService>() ??
                throw new InvalidOperationException("AlertService not available from DI. Ensure ServiceConfiguration is properly initialized.");

            // ✅ Create SaleViewModel using service provider with proper DI
            _salesViewModel = App.ServiceProvider?.GetService<SaleViewModel>() ??
                throw new InvalidOperationException("SaleViewModel not available from DI. Ensure ServiceConfiguration is properly initialized.");
            InitializeSession();
            InitializeNavigationButtons();
            
            // Changed from DashboardView to SalesView
            var salesView = GetOrCreateView("Sales") as SalesViewWithLayouts ?? new SalesViewWithLayouts();
            NavigateToView(salesView);
            UpdateActiveButton(btnSales); // Update active button to Sales instead of Dashboard
            SetupCombinedTimer();


        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            var source = PresentationSource.FromVisual(this) as HwndSource;
            if (source != null)
            {
                source.AddHook(WndProc);
            }
        }

        private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            if (msg == WM_SWITCHUSER)
            {
                Dispatcher.Invoke(async () =>
                {
                    try
                    {
                        // Stop all timers and background operations
                        _combinedTimer?.Stop();
                        _alertTimer?.Stop();

                        // Clear cached data
                        _viewCache?.Clear();
                        _cachedAlerts?.Clear();
                        _cachedNotifications?.Clear();

                        // Check if cash drawer needs to be handled
                        using var dbService = App.ServiceProvider?.GetService<DatabaseService>() ?? new DatabaseService();
                        var cashDrawerService = new CashDrawerService(dbService);
                        var currentDrawer = cashDrawerService.GetCurrentDrawerBasic();
                        bool drawerClosed = true;

                        if (currentDrawer != null && currentDrawer.Status == "Open")
                        {
                            // Get full drawer details for closing (needed for ExpectedBalance calculation)
                            var fullDrawer = cashDrawerService.GetCurrentDrawerWithDetails();
                            var dialog = new CloseDrawerDialog(fullDrawer);
                            var dialogHost = FindVisualParent<DialogHost>(this);
                            
                            if (dialogHost != null)
                            {
                                dialogHost.Identifier = "MainWindowCashDrawerDialog";
                                var result = await dialogHost.ShowDialog(dialog);
                                if (result is CloseDrawerResult closeResult)
                                {
                                    fullDrawer.ActualBalance = closeResult.ActualBalance;
                                    fullDrawer.Notes = closeResult.Notes;
                                    cashDrawerService.CloseDrawer(fullDrawer);
                                }
                                else
                                {
                                    drawerClosed = false;
                                }
                            }
                        }

                        if (drawerClosed)
                        {
                            // Show the login window for user switching
                            var loginWindow = new LoginWindow { Owner = this };
                            this.Hide(); // Hide the main window while showing login

                            if (loginWindow.ShowDialog() == true)
                            {
                                // Update user information and refresh UI
                                LoadUserInformation();
                                UpdateMenuVisibility();
                                
                                // Navigate to default view (Sales)
                                var salesView = GetOrCreateView("Sales") as SalesViewWithLayouts ?? new SalesViewWithLayouts();
                                NavigateToView(salesView);
                                UpdateActiveButton(btnSales);

                                // Restart timers
                                SetupCombinedTimer();
                                this.Show(); // Show the main window again
                            }
                            else
                            {
                                this.Show(); // Show the main window if login was cancelled
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Error";
                        var errorMessageFormat = Application.Current.FindResource("DialogUserSwitchError") as string ?? "Error during user switch: {0}";
                        var errorMessage = string.Format(errorMessageFormat, ex.Message);

                        _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync(errorMessage, errorTitle));
                        this.Show(); // Ensure the window is shown in case of error
                    }
                });
                handled = true;
            }
            return IntPtr.Zero;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Initial check for alerts and notifications
                await Task.Run(() => _alertService.CheckExpiringProducts());
                UpdateNotificationCount();
                
                // Start the combined timer for periodic updates
                SetupCombinedTimer();
                
                // Check cash drawer status
                await CheckCashDrawerStatus();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in MainWindow_Loaded: {ex.Message}");
            }
        }

        private void InitializeViewCache()
        {
            _viewCache = new Dictionary<string, WeakReference<UserControl>>();
        }

        private UserControl GetOrCreateView(string viewName)
        {
            if (_viewCache.TryGetValue(viewName, out var weakRef) && weakRef.TryGetTarget(out var cachedView))
            {
                return cachedView;
            }

            // Pre-initialize SaleViewModel for Sales view BEFORE creating the view
            if (viewName == "Sales")
            {
                // Ensure sales view model is initialized only once
                if (_salesViewModel == null)
                {
                    // ✅ Create SaleViewModel using service provider with proper DI
                    _salesViewModel = App.ServiceProvider?.GetService<SaleViewModel>() ??
                        throw new InvalidOperationException("SaleViewModel not available from DI. Ensure ServiceConfiguration is properly initialized.");
                }
            }

            var view = CreateView(viewName);

            // Set DataContext for Sales view
            if (viewName == "Sales")
            {
                view.DataContext = _salesViewModel;
            }
            
            // Enable bitmap caching for the view
            view.CacheMode = new BitmapCache { EnableClearType = true, SnapsToDevicePixels = true };
            
            _viewCache[viewName] = new WeakReference<UserControl>(view);
            return view;
        }

        private async void InitializeViewDataAsync(UserControl view, string viewName)
        {
            try
            {
                if (viewName == "Sales" && view.DataContext is SaleViewModel saleVM)
                {
                    // Load initial data in background
                    await Task.Run(async () =>
                    {
                        await Dispatcher.InvokeAsync(async () =>
                        {
                            try
                            {
                                await saleVM.RefreshProducts();
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Error refreshing products: {ex.Message}");
                            }
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                await Dispatcher.InvokeAsync(() =>
                {
                    MessageBox.Show($"Error initializing {viewName} view: {ex.Message}",
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        private UserControl CreateView(string viewName)
        {
            if (viewName == "Sales")
            {
                // Use the SalesViewWithLayouts instead of direct layout selection
                var salesView = new SalesViewWithLayouts();
                salesView.DataContext = _salesViewModel;
                return salesView;
            }
            
            // Handle other views as before
            return viewName switch
            {
                "Dashboard" => new DashboardView(), // DashboardView creates its own RefactoredDashboardViewModel
                "Products" => new ProductsView { DataContext = App.ServiceProvider?.GetService<ProductsViewModel>() },
                "Categories" => new CategoriesView { DataContext = App.ServiceProvider?.GetService<CategoriesViewModel>() },
                "Customers" => new CustomersView { DataContext = App.ServiceProvider?.GetService<CustomersViewModel>() },
                "Users" => new UsersView { DataContext = App.ServiceProvider?.GetService<UsersViewModel>() },
                "Suppliers" => new SuppliersView { DataContext = App.ServiceProvider?.GetService<SuppliersViewModel>() },
                "PurchaseOrders" => new InvoicesView { DataContext = App.ServiceProvider?.GetService<InvoiceViewModel>() },
                "CashDrawer" => new CashDrawerView { DataContext = App.ServiceProvider?.GetService<CashDrawerViewModel>() },
                "SalesHistory" => new SalesHistoryView { DataContext = App.ServiceProvider?.GetService<SalesHistoryViewModel>() },
                "UnpaidTransactions" => new UnpaidTransactionsView { DataContext = App.ServiceProvider?.GetService<UnpaidTransactionsViewModel>() },
                "Reports" => new ReportsView { DataContext = App.ServiceProvider?.GetService<ReportsViewModel>() },
                "Settings" => new SettingsView { DataContext = App.ServiceProvider?.GetService<SettingsViewModel>() },
                "CashDrawerHistory" => new CashDrawerHistoryView { DataContext = App.ServiceProvider?.GetService<CashDrawerHistoryViewModel>() },
                "Expenses" => new ExpensesView { DataContext = App.ServiceProvider?.GetService<BusinessExpenseViewModel>() },
                "Invoices" => new InvoicesView { DataContext = App.ServiceProvider?.GetService<InvoiceViewModel>() },
                _ => throw new ArgumentException($"Unknown view: {viewName}")
            };
        }

        private void InitializeSession()
        {
            // Initialize session timer
            sessionStartTime = DateTime.Now;
            LoadUserInformation();
            txtStoreName.Text = ConfigurationManager.AppSettings["StoreName"] ?? "My POS Store";
            UpdateNotificationCount();
        }

        private void LoadUserInformation()
        {
            var currentUser = _authService.CurrentUser;
            if (currentUser != null)
            {
                txtUserName.Text = $"{currentUser.FirstName} {currentUser.LastName}";
                txtUserRole.Text = currentUser.DisplayRoleName;

                try
                {
                    // Use the Photo property which handles both custom and default photos
                    userPhoto.Source = currentUser.Photo;
                    userPhoto.Visibility = Visibility.Visible;
                    defaultUserIcon.Visibility = Visibility.Collapsed;
                }
                catch (Exception ex)
                {
                    // Log the error but don't crash - show default icon instead
                    System.Diagnostics.Debug.WriteLine($"Error loading user photo: {ex.Message}");
                    userPhoto.Visibility = Visibility.Collapsed;
                    defaultUserIcon.Visibility = Visibility.Visible;
                }

                // Show/hide menu items based on user role
                UpdateMenuVisibility();
            }
        }

        private string GetResourcesPath()
        {
            try
            {
                var exePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var exeDir = System.IO.Path.GetDirectoryName(exePath);
                var resourcesDir = System.IO.Path.Combine(exeDir, "Resources", "Images");

                // Create the directory if it doesn't exist
                if (!System.IO.Directory.Exists(resourcesDir))
                {
                    System.IO.Directory.CreateDirectory(resourcesDir);

                    // Copy default photo if it exists in the source location
                    var defaultPhotoName = "default-user.png";
                    var sourceDefaultPhoto = System.IO.Path.Combine(exeDir, "..", "Resources", "Images", defaultPhotoName);
                    var targetDefaultPhoto = System.IO.Path.Combine(resourcesDir, defaultPhotoName);
                    
                    if (System.IO.File.Exists(sourceDefaultPhoto) && !System.IO.File.Exists(targetDefaultPhoto))
                    {
                        System.IO.File.Copy(sourceDefaultPhoto, targetDefaultPhoto, true);
                    }
                }

                return resourcesDir;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting resources path: {ex.Message}");
                // If all else fails, use the current directory
                var fallbackPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Images");
                if (!System.IO.Directory.Exists(fallbackPath))
                {
                    System.IO.Directory.CreateDirectory(fallbackPath);
                }
                return fallbackPath;
            }
        }

        private void UpdateMenuVisibility()
        {
            // Use custom permissions instead of role-based checks
            System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Updating menu visibility for user: {_authService.CurrentUser?.Username}");

            // User management
            var canManageUsers = _authService.HasPermission("users.manage");
            btnUsers.Visibility = canManageUsers ? Visibility.Visible : Visibility.Collapsed;
            System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Users button: {(canManageUsers ? "VISIBLE" : "HIDDEN")}");

            // Financial features
            var canViewReports = _authService.HasPermission("reports.view");
            btnReports.Visibility = canViewReports ? Visibility.Visible : Visibility.Collapsed;
            System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Reports button: {(canViewReports ? "VISIBLE" : "HIDDEN")}");

            var canManageExpenses = _authService.HasPermission("expenses.manage");
            btnExpenses.Visibility = canManageExpenses ? Visibility.Visible : Visibility.Collapsed;
            System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Expenses button: {(canManageExpenses ? "VISIBLE" : "HIDDEN")}");

            // Product management
            if (btnProducts != null)
            {
                var canManageProducts = _authService.HasPermission("products.manage");
                btnProducts.Visibility = canManageProducts ? Visibility.Visible : Visibility.Collapsed;
                System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Products button: {(canManageProducts ? "VISIBLE" : "HIDDEN")}");
            }

            // Customer management
            if (btnCustomers != null)
            {
                var canManageCustomers = _authService.HasPermission("customers.manage");
                btnCustomers.Visibility = canManageCustomers ? Visibility.Visible : Visibility.Collapsed;
                System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Customers button: {(canManageCustomers ? "VISIBLE" : "HIDDEN")}");
            }

            // Settings access
            if (btnSettings != null)
            {
                var canAccessSettings = _authService.HasPermission("settings.access");
                btnSettings.Visibility = canAccessSettings ? Visibility.Visible : Visibility.Collapsed;
                System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Settings button: {(canAccessSettings ? "VISIBLE" : "HIDDEN")}");
            }

            // Hide the purchase orders button entirely since we're using the invoice system now
            btnPurchaseOrders.Visibility = Visibility.Collapsed;

            // Log current user permissions for debugging
            LogCurrentUserPermissions();
        }

        /// <summary>
        /// Log current user permissions for debugging
        /// </summary>
        private void LogCurrentUserPermissions()
        {
            try
            {
                if (_authService.CurrentUser != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] === CURRENT USER PERMISSIONS DEBUG ===");
                    System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] User: {_authService.CurrentUser.Username} (Role: {_authService.CurrentUser.UserRole?.Name})");

                    // Check if user has custom permissions
                    var dbService = new POSSystem.Services.DatabaseService();
                    var permissionsService = new POSSystem.Services.UserPermissionsService(dbService);
                    var customPermissions = permissionsService.GetUserPermissions(_authService.CurrentUser.Id);

                    if (customPermissions != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] User has CUSTOM permissions:");
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW]   - CanManageUsers: {customPermissions.CanManageUsers}");
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW]   - CanManageProducts: {customPermissions.CanManageProducts}");
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW]   - CanViewReports: {customPermissions.CanViewReports}");
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW]   - CanAccessSettings: {customPermissions.CanAccessSettings}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] User has NO custom permissions - using role defaults");
                    }

                    System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] === END PERMISSIONS DEBUG ===");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MAINWINDOW] Error logging permissions: {ex.Message}");
            }
        }

        private void SessionTimer_Tick(object sender, EventArgs e)
        {
            var sessionDuration = DateTime.Now - sessionStartTime;
            txtSessionTime.Text = $"Session: {sessionDuration.Hours:D2}:{sessionDuration.Minutes:D2}:{sessionDuration.Seconds:D2}";
        }

        private void ClockTimer_Tick(object sender, EventArgs e)
        {
            UpdateClock();
        }

        private void UpdateClock()
        {
            var txtClock = this.FindName("txtClock") as TextBlock;
            if (txtClock != null)
            {
                txtClock.Text = DateTime.Now.ToString("h:mm tt");
            }
        }

        private async void btnLogout_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show confirmation dialog before logout
                var confirmMessage = FindResourceString("DialogConfirmLogout") as string ?? "Are you sure you want to logout?";
                var confirmTitle = FindResourceString("DialogConfirmTitle") as string ?? "Confirm";

                var logoutResult = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(confirmMessage, confirmTitle,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                if (logoutResult == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.No)
                {
                    return;
                }

                using var dbService = App.ServiceProvider?.GetService<DatabaseService>() ?? new DatabaseService();
                var cashDrawerService = new CashDrawerService(dbService);
                var currentDrawer = cashDrawerService.GetCurrentDrawerBasic();
                bool drawerClosed = true;

                if (currentDrawer != null && currentDrawer.Status == "Open")
                {
                    // Get full drawer details for closing (needed for ExpectedBalance calculation)
                    var fullDrawer = cashDrawerService.GetCurrentDrawerWithDetails();
                    var dialog = new CloseDrawerDialog(fullDrawer);
                    
                    // Find the root DialogHost in the visual tree
                    var dialogHost = FindVisualParent<DialogHost>(this);
                    if (dialogHost != null)
                    {
                        // Set a unique identifier for cash drawer operations
                        dialogHost.Identifier = "MainWindowCashDrawerDialog";
                        var result = await dialogHost.ShowDialog(dialog);
                        if (result is CloseDrawerResult closeResult)
                        {
                            fullDrawer.ActualBalance = closeResult.ActualBalance;
                            fullDrawer.Notes = closeResult.Notes;
                            cashDrawerService.CloseDrawer(fullDrawer);
                        }
                        else
                        {
                            // User cancelled drawer closing
                            drawerClosed = false;
                        }
                    }
                    else
                    {
                        // Fallback to using specific identifier if DialogHost not found
                        var result = await DialogHost.Show(dialog, "MainWindowCashDrawerDialog");
                        if (result is CloseDrawerResult closeResult)
                        {
                            currentDrawer.ActualBalance = closeResult.ActualBalance;
                            currentDrawer.Notes = closeResult.Notes;
                            cashDrawerService.CloseDrawer(currentDrawer);
                        }
                        else
                        {
                            // User cancelled drawer closing
                            drawerClosed = false;
                        }
                    }
                }

                // Only proceed with logout if drawer is closed or was already closed
                if (drawerClosed)
                {
                    // Perform logout actions
                    _combinedTimer?.Stop();
                    _alertTimer?.Stop();
                    _authService.Logout();
                    
                    // Show login window
                    var loginWindow = new LoginWindow();
                    loginWindow.Show();
                    Close();
                }
            }
            catch (Exception ex)
            {
                var errorMessage = string.Format(
                    FindResourceString("DialogErrorDuringLogout") as string ?? "Error during logout: {0}", 
                    ex.Message);
                    
                MessageBox.Show(
                    errorMessage,
                    FindResourceString("DialogErrorTitle") as string ?? "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        // Helper method to find parent of type T in visual tree
        private static T FindVisualParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parentObject = VisualTreeHelper.GetParent(child);
            if (parentObject == null) return null;
            
            if (parentObject is T parent)
                return parent;
            
            return FindVisualParent<T>(parentObject);
        }

        #region Window Controls

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                if (WindowState == WindowState.Normal)
                {
                    WindowState = WindowState.Maximized;
                    maximizeIcon.Kind = PackIconKind.WindowRestore;
                }
                else
                {
                    WindowState = WindowState.Normal;
                    maximizeIcon.Kind = PackIconKind.WindowMaximize;
                }
            }
            else
            {
                DragMove();
            }
        }

        private void btnMinimize_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void btnMaximize_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Normal)
            {
                WindowState = WindowState.Maximized;
                maximizeIcon.Kind = PackIconKind.WindowRestore;
            }
            else
            {
                WindowState = WindowState.Normal;
                maximizeIcon.Kind = PackIconKind.WindowMaximize;
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        #endregion

        #region Navigation

        public async void NavigateToView(UserControl view)
        {
            try
            {
                if (view == null) return;

                // Store the current view for keyboard shortcuts
                _currentView = view;

                // Show loading indicator if needed
                IsLoading = true;

                // Switch the view immediately
                await Dispatcher.InvokeAsync(() =>
                {
                    mainContent.Content = view;
                }, DispatcherPriority.Render);

                // Initialize the view asynchronously if needed
                if (view.DataContext is IAsyncInitializable initializable)
                {
                    await Task.Run(async () => await initializable.InitializeAsync());
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void btnDashboard_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Dashboard"));
            UpdateActiveButton(btnDashboard);
        }

        private async void btnSales_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                IsLoading = true;
                UpdateActiveButton(btnSales);

                var salesView = GetOrCreateView("Sales");
                NavigateToView(salesView);

                // Refresh products asynchronously after showing the view
                if (_salesViewModel != null)
                {
                    await Task.Run(async () =>
                    {
                        await Dispatcher.InvokeAsync(async () =>
                        {
                            try
                            {
                                await _salesViewModel.RefreshProducts();
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Error refreshing products: {ex.Message}");
                            }
                        });
                    });
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void btnProducts_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Products"));
            UpdateActiveButton(btnProducts);
        }

        private void btnCategories_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Categories"));
            UpdateActiveButton(btnCategories);
        }

        private void btnCustomers_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Customers"));
            UpdateActiveButton(btnCustomers);
        }

        private void btnUsers_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Users"));
            UpdateActiveButton(btnUsers);
        }

        private void btnSuppliers_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Suppliers"));
            UpdateActiveButton(btnSuppliers);
        }

        private void btnPurchaseOrders_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Invoices"));
            UpdateActiveButton(btnInvoices);
        }

        private void btnSalesHistory_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("SalesHistory"));
            UpdateActiveButton(btnSalesHistory);
        }

        private void btnUnpaidTransactions_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("UnpaidTransactions"));
            UpdateActiveButton(btnUnpaidTransactions);
        }

        private void btnReports_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Reports"));
            UpdateActiveButton(btnReports);
        }

        private void btnExpenses_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Expenses"));
            UpdateActiveButton(btnExpenses);
        }

        private void btnSettings_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Settings"));
            UpdateActiveButton(null);
        }

        /// <summary>
        /// ✅ DEBUG LOG VIEWER: Opens the debug log viewer window
        /// </summary>
        private void btnDebugLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var debugViewer = new DebugLogViewer();
                debugViewer.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening debug log viewer: {ex.Message}",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCashDrawer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var view = GetOrCreateView("CashDrawer");
                if (view.DataContext == null)
                {
                    // ✅ Use proper DI for CashDrawerViewModel
                    view.DataContext = App.ServiceProvider?.GetService<CashDrawerViewModel>() ??
                        throw new InvalidOperationException("CashDrawerViewModel not available from DI. Ensure ServiceConfiguration is properly initialized.");
                }
                NavigateToView(view);
                UpdateActiveButton(sender as Button);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading cash drawer: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCashDrawerHistory_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("CashDrawerHistory"));
            UpdateActiveButton(btnCashDrawer);
        }

        private void btnInvoices_Click(object sender, RoutedEventArgs e)
        {
            NavigateToView(GetOrCreateView("Invoices"));
            UpdateActiveButton(btnInvoices);
        }

        private void UpdateActiveButton(Button activeButton)
        {
            if (_navigationButtons == null || _defaultNavStyle == null || _activeNavStyle == null)
                return;

            // Use cached styles for better performance
            foreach (var button in _navigationButtons)
            {
                if (button != null)
                {
                    button.Style = button == activeButton ? _activeNavStyle : _defaultNavStyle;
                }
            }
        }

        #endregion

        private void btnToggleSidebar_Click(object sender, RoutedEventArgs e)
        {
            // Keep sidebar collapsed, do nothing when toggle button is clicked
            return;
        }

        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj == null) yield break;

            var stack = new Stack<DependencyObject>();
            stack.Push(depObj);

            while (stack.Count > 0)
            {
                var current = stack.Pop();
                
                if (current is T t)
                    yield return t;

                for (int i = VisualTreeHelper.GetChildrenCount(current) - 1; i >= 0; i--)
                {
                    var child = VisualTreeHelper.GetChild(current, i);
                    if (child != null)
                        stack.Push(child);
                }
            }
        }

        public void UpdateNotificationCount()
        {
            try
            {
                // Use optimized count query instead of loading full alerts
                var unreadCount = Math.Min(_alertService.GetUnreadAlertsCount(), NOTIFICATION_BATCH_SIZE);
                _cachedUnreadCount = unreadCount;
                
                // Update the badge on UI thread
                Dispatcher.Invoke(() =>
                {
                    notificationCount.Text = unreadCount.ToString();
                    notificationBadge.Visibility = unreadCount > 0 ? Visibility.Visible : Visibility.Collapsed;
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating notification count: {ex.Message}");
            }
        }

        private async void btnNotifications_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if popup is currently open to implement toggle behavior
                bool isCurrentlyOpen = NotificationPopup.IsOpen;

                // If popup is open, just toggle it closed
                if (isCurrentlyOpen)
                {
                    NotificationPopup.Hide();
                    return;
                }

                bool loadFromDatabase = _cachedNotifications == null ||
                    (DateTime.Now - _lastNotificationCheck).TotalSeconds > NOTIFICATION_CACHE_DURATION_SECONDS;

                if (loadFromDatabase)
                {
                    _isLoadingNotifications = true;

                    // Get alerts from database with batch size limit
                    _cachedAlerts = await Task.Run(() =>
                        _alertService.GetUnreadAlerts()
                        .Take(NOTIFICATION_BATCH_SIZE)
                        .ToList());

                    _lastNotificationCheck = DateTime.Now;
                    _cachedUnreadCount = _cachedAlerts.Count;

                    // Transform alerts to notifications
                    _cachedNotifications = _cachedAlerts.Select(alert => new Notification
                    {
                        Id = alert.Id,
                        Title = GetAlertTitle(alert.AlertType),
                        Message = GetLocalizedMessage(alert),
                        CreatedAt = alert.CreatedAt,
                        IsRead = alert.IsRead,
                        Type = GetNotificationType(alert.AlertType),
                        ActionCommand = new RelayCommand(_ => HandleNotificationAction(alert)),
                        IconKind = GetNotificationIcon(alert.AlertType),
                        IconBackground = GetNotificationBackground(alert.AlertType),
                        ProductId = alert.ProductId,
                        AlertType = alert.AlertType,
                        SourceAlert = alert
                    }).ToList();

                    // Update UI on main thread
                    await Dispatcher.InvokeAsync(() =>
                    {
                        NotificationPopup.SetNotifications(_cachedNotifications);
                        UpdateNotificationBadge();
                    });

                    _isLoadingNotifications = false;
                }

                // Show the popup
                NotificationPopup.Show();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading notifications: {ex.Message}");
                _isLoadingNotifications = false;
            }
        }

        private string GetAlertTitle(string alertType) => alertType switch
        {
            "Expiry" => Application.Current.FindResource("ProductExpiredTitle")?.ToString() ?? "منتج منتهي الصلاحية",
            "LowStock" => Application.Current.FindResource("LowStockTitle")?.ToString() ?? "تنبيه المخزون المنخفض",
            "OutOfStock" => Application.Current.FindResource("NotificationTitle_OutOfStock")?.ToString() ?? "نفذ المخزون",
            "OverdueSale" => Application.Current.FindResource("NotificationTitle_OverdueSale")?.ToString() ?? "دفعة بيع متأخرة",
            "OverduePurchase" => Application.Current.FindResource("NotificationTitle_OverduePurchase")?.ToString() ?? "دفعة شراء متأخرة",
            _ => alertType
        };

        private NotificationType GetNotificationType(string alertType) => alertType switch
        {
            "Warning" => NotificationType.Warning,
            "Error" => NotificationType.Alert,
            "Info" => NotificationType.Info,
            "Expiry" => NotificationType.Warning,
            "LowStock" => NotificationType.Warning,
            "OutOfStock" => NotificationType.Alert,
            "OverdueSale" => NotificationType.Alert,
            "OverduePurchase" => NotificationType.Alert,
            _ => NotificationType.Info
        };

        private async void HandleNotificationAction(ProductAlert alert)
        {
            NotificationPopup.Hide();

            switch (alert.AlertType)
            {
                case "Expiry":
                case "LowStock":
                case "OutOfStock":
                    // For product-related alerts, open the ProductDialog with the specific product
                    await OpenProductDialogForAlert(alert);
                    break;
                case "OverdueSale":
                    NavigateToView(GetOrCreateView("UnpaidTransactions"));
                    UpdateActiveButton(btnUnpaidTransactions);
                    break;
                case "OverduePurchase":
                    NavigateToView(GetOrCreateView("Invoices"));
                    UpdateActiveButton(btnInvoices);
                    break;
            }
        }

        private async Task OpenProductDialogForAlert(ProductAlert alert)
        {
            try
            {
                // Get the product from the database
                using var dbService = App.ServiceProvider?.GetService<DatabaseService>() ?? new DatabaseService();
                var product = dbService.GetProductById(alert.ProductId);

                if (product == null)
                {
                    MessageBox.Show("Product not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Get or create ProductsViewModel without navigating to Products view
                var productsViewModel = App.ServiceProvider?.GetService<ProductsViewModel>();
                if (productsViewModel == null)
                {
                    // Create a new instance if not available from DI
                    var repositoryAdapter = App.ServiceProvider?.GetService<RepositoryServiceAdapter>() ??
                        throw new InvalidOperationException("RepositoryServiceAdapter not available from DI");
                    var alertService = App.ServiceProvider?.GetService<IAlertService>() ?? _alertService;
                    productsViewModel = new ProductsViewModel(repositoryAdapter, alertService, dbService);
                    await productsViewModel.LoadInitialData();
                }

                // Create the ProductDialog with the product data
                var productDialog = new ProductDialog(productsViewModel, "MainWindowCashDrawerDialog", product);

                // Use MainWindow's existing DialogHost identifier to show the dialog directly
                var result = await DialogHost.Show(productDialog, "MainWindowCashDrawerDialog");

                if (result is Product savedProduct)
                {
                    // Product was successfully updated
                    Debug.WriteLine($"Product '{savedProduct.Name}' updated successfully from notification.");

                    // Refresh the ProductsViewModel if it exists in the Products view
                    var productsView = _viewCache.Values
                        .Where(wr => wr.TryGetTarget(out var view) && view is ProductsView)
                        .Select(wr => { wr.TryGetTarget(out var view); return view as ProductsView; })
                        .FirstOrDefault();

                    if (productsView?.DataContext is ProductsViewModel existingViewModel)
                    {
                        await existingViewModel.LoadInitialData();
                    }

                    // Refresh notifications to reflect any changes
                    _cachedNotifications = null;
                    UpdateNotificationCount();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening product dialog from notification: {ex.Message}");
                MessageBox.Show($"Error opening product dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateNotificationBadge()
        {
            Dispatcher.Invoke(() =>
            {
                notificationCount.Text = _cachedUnreadCount.ToString();
                notificationBadge.Visibility = _cachedUnreadCount > 0 ? Visibility.Visible : Visibility.Collapsed;
            });
        }

        private void SetupCombinedTimer()
        {
            _combinedTimer = new DispatcherTimer();
            _combinedTimer.Tick += (s, e) => 
            {
                // Update clock and session time every second
                if (txtClock.IsVisible)
                    UpdateClock();
                if (txtSessionTime.IsVisible)
                    UpdateSessionTime();

                // Check notifications every 5 minutes (reduced frequency)
                if ((DateTime.Now - _lastNotificationCheck).TotalSeconds >= 300)
                {
                    _alertService.CheckExpiringProducts();
                    UpdateNotificationCount();
                    _lastNotificationCheck = DateTime.Now;
                }
            };
            _combinedTimer.Interval = TimeSpan.FromSeconds(1);
            _combinedTimer.Start();

            // Initial updates
            UpdateClock();
            UpdateSessionTime();
        }

        private void UpdateSessionTime()
        {
            var sessionDuration = DateTime.Now - sessionStartTime;
            txtSessionTime.Text = $"Session: {sessionDuration.Hours:D2}:{sessionDuration.Minutes:D2}:{sessionDuration.Seconds:D2}";
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    _combinedTimer?.Stop();
                    _viewCache?.Clear();
                    _salesViewModel = null;
                }
                _isDisposed = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
            Dispose();
        }

        private void InitializeNavigationButtons()
        {
            // Cache the styles for better performance
            _defaultNavStyle = (Style)this.TryFindResource("SidebarButton");
            _activeNavStyle = (Style)this.TryFindResource("SidebarButtonActive");

            // Initialize all navigation buttons at once
            _navigationButtons = new[]
            {
                btnDashboard,
                btnSales,
                btnProducts,
                btnCategories,
                btnCustomers,
                btnUsers,
                btnSuppliers,
                btnPurchaseOrders,
                btnCashDrawer,
                btnExpenses,
                btnSalesHistory,
                btnUnpaidTransactions,
                btnReports
            };

            // Set initial styles and cache mode for better performance
            foreach (var button in _navigationButtons)
            {
                if (button != null)
                {
                    button.Style = _defaultNavStyle;
                    button.CacheMode = new BitmapCache();
                }
            }
        }

        private async Task CheckCashDrawerStatus()
        {
            try
            {
                using var dbService = App.ServiceProvider?.GetService<DatabaseService>() ?? new DatabaseService();
                var cashDrawerService = new CashDrawerService(dbService);
                var currentDrawer = cashDrawerService.GetCurrentDrawerBasic();

                if (currentDrawer == null)
                {
                    // Add a small delay to ensure DialogHost is ready
                    await Task.Delay(100);
                    
                    var dialog = new OpenDrawerDialog();
                    // Use the same specific identifier for cash drawer operations
                    var result = await DialogHost.Show(dialog, "MainWindowCashDrawerDialog");

                    if (result is decimal openingBalance)
                    {
                        var user = _authService.CurrentUser;
                        var drawer = new CashDrawer
                        {
                            OpeningBalance = openingBalance,
                            OpenedById = user.Id,
                            OpenedBy = user,
                            Status = "Open",
                            Transactions = new ObservableCollection<CashTransaction>()
                        };

                        cashDrawerService.OpenDrawer(drawer);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Error";
                var errorMessageFormat = Application.Current.FindResource("DialogCashDrawerError") as string ?? "Error checking cash drawer status: {0}";
                var errorMessage = string.Format(errorMessageFormat, ex.Message);

                _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync(errorMessage, errorTitle));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        private string GetLocalizedMessage(ProductAlert alert)
        {
            // Get product name - preserve in original language
            string productName = alert.Product?.Name ?? "Product";
            
            switch (alert.AlertType)
            {
                case "Expiry":
                    if (alert.Product?.ExpiryDate.HasValue == true)
                    {
                        var daysExpired = (DateTime.Now - alert.Product.ExpiryDate.Value).Days;
                        if (daysExpired > 0)
                        {
                            // For expired products
                            var template = FindResourceString("ProductExpiredNotification") as string ?? "{0} expired {1} days ago";
                            var daysFormat = FindResourceString("ExpiryDaysFormat") as string ?? "{0} days";
                            return string.Format(template, productName, string.Format(daysFormat, daysExpired));
                        }
                        else
                        {
                            // For products expiring soon
                            var daysToExpiry = (alert.Product.ExpiryDate.Value - DateTime.Now).Days;
                            
                            if (daysToExpiry <= 3) // Urgent expiry
                            {
                                var template = FindResourceString("ProductExpiryUrgentNotification") as string ?? "{0} will expire in {1} days";
                                return string.Format(template, productName, daysToExpiry);
                            }
                            else // Warning expiry
                            {
                                var template = FindResourceString("ProductExpiryWarningNotification") as string ?? "Warning: {0} will expire in {1} days";
                                return string.Format(template, productName, daysToExpiry);
                            }
                        }
                    }
                    break;

                case "LowStock":
                    int currentStock = alert.Product?.GetTotalStock() ?? 0;
                    int minStock = alert.Product?.MinimumStock ?? 0;
                    var lowStockTemplate = FindResourceString("LowStockNotification") as string ?? "{0} low stock ({1} units left, minimum: {2})";
                    return string.Format(lowStockTemplate, productName, currentStock, minStock);

                case "OutOfStock":
                    var outOfStockTemplate = FindResourceString("OutOfStockNotification") as string ?? "{0} is out of stock";
                    return string.Format(outOfStockTemplate, productName);

                case "OverdueSale":
                    var overdueSaleTemplate = FindResourceString("OverdueSaleNotification") as string ?? "Invoice #{0} for customer {1} - {2} days overdue (Amount: {3})";
                    return alert.Message;

                case "OverduePurchase":
                    var overduePurchaseTemplate = FindResourceString("OverduePurchaseNotification") as string ?? "Purchase order #{0} for supplier {1} - {2} days overdue (Amount: {3})";
                    return alert.Message;
            }

            return alert.Message;
        }

        private string FindResourceString(string key)
        {
            try
            {
                return Application.Current.TryFindResource(key)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        // Add these new methods to get appropriate icons and colors
        private string GetNotificationIcon(string alertType) => alertType switch
        {
            "Expiry" => "AlertCircleOutline",
            "LowStock" => "AlertOctagonOutline",
            "OutOfStock" => "PackageVariantRemove",
            "OverdueSale" => "CalendarClock",
            "OverduePurchase" => "CalendarAlert",
            _ => "Bell"
        };

        private Brush GetNotificationBackground(string alertType)
        {
            var converter = new BrushConverter();
            return alertType switch
            {
                "Expiry" => (Brush)converter.ConvertFromString("#F44336"),       // Red for expired products
                "LowStock" => (Brush)converter.ConvertFromString("#FF9800"),     // Orange for low stock
                "OutOfStock" => (Brush)converter.ConvertFromString("#E91E63"),   // Pink for out of stock
                "OverdueSale" => (Brush)converter.ConvertFromString("#9C27B0"),  // Purple for overdue sales
                "OverduePurchase" => (Brush)converter.ConvertFromString("#673AB7"), // Deep Purple for overdue purchases
                _ => (Brush)converter.ConvertFromString("#2196F3")               // Blue for default
            };
        }

        // These methods are required for building and are referenced in XAML
        private void ExecuteSearchCommand(object sender, ExecutedRoutedEventArgs e)
        {
            Debug.WriteLine("ExecuteSearchCommand called");
            var salesViewGrid = FindVisualChild<SalesViewGrid>(this);
            if (salesViewGrid != null)
            {
                salesViewGrid.FocusSearchBox();
                e.Handled = true;
                Debug.WriteLine("Search command handled");
            }
            else
            {
                Debug.WriteLine("SalesViewGrid not found for search command");
            }
        }

        private void CanExecuteSearchCommand(object sender, CanExecuteRoutedEventArgs e)
        {
            // Enable the command only when we're in a sales view
            var salesViewGrid = FindVisualChild<SalesViewGrid>(this);
            e.CanExecute = salesViewGrid != null;
        }

        private async void ExecutePaymentCommand(object sender, ExecutedRoutedEventArgs e)
        {
            Debug.WriteLine("ExecutePaymentCommand called");
            var salesViewGrid = FindVisualChild<SalesViewGrid>(this);
            if (salesViewGrid != null)
            {
                var viewModel = salesViewGrid.DataContext as SaleViewModel;
                if (viewModel != null && viewModel.HasCartItems)
                {
                    try
                    {
                        await salesViewGrid.ShowPaymentDialog();
                        e.Handled = true;
                        Debug.WriteLine("Payment command handled");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error showing payment dialog: {ex.Message}");
                        MessageBox.Show("Error opening payment dialog. Please try again.", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    Debug.WriteLine("Cannot execute payment command - no items in cart");
                }
            }
            else
            {
                Debug.WriteLine("SalesViewGrid not found for payment command");
            }
        }

        private void CanExecutePaymentCommand(object sender, CanExecuteRoutedEventArgs e)
        {
            // Enable the command only when we're in a sales view with items in the cart
            var salesViewGrid = FindVisualChild<SalesViewGrid>(this);
            if (salesViewGrid != null)
            {
                var viewModel = salesViewGrid.DataContext as SaleViewModel;
                e.CanExecute = viewModel != null && viewModel.HasCartItems;
            }
            else
            {
                e.CanExecute = false;
            }
        }

        // Helper method to get the current SaleViewModel
        private SaleViewModel GetCurrentSaleViewModel()
        {
            if (_currentView is SalesViewGrid salesViewGrid)
            {
                return salesViewGrid.DataContext as SaleViewModel;
            }
            
            return null;
        }
        
        // Helper method to find and focus the search box
        private bool FindAndFocusSearchBox()
        {
            if (_currentView is SalesViewGrid salesViewGrid)
            {
                var txtSearch = FindVisualChild<TextBox>(salesViewGrid, tb => tb.Name == "txtSearch");
                if (txtSearch != null)
                {
                    txtSearch.Focus();
                    return true;
                }
            }
            
            return false;
        }
        
        // Helper method to find visual children
        private T FindVisualChild<T>(DependencyObject parent, Func<T, bool> condition = null) where T : DependencyObject
        {
            if (parent == null)
                return null;
            
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                
                if (child is T typedChild && (condition == null || condition(typedChild)))
                    return typedChild;
                
                var result = FindVisualChild<T>(child, condition);
                if (result != null)
                    return result;
            }
            
            return null;
        }

        private async void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            // Add debugging to help track key events
            Debug.WriteLine($"MainWindow received key: {e.Key}, handled: {e.Handled}");
            
            // Don't process shortcuts if a dialog is open
            if (DialogHost.IsDialogOpen("RootDialog") || DialogHost.IsDialogOpen("SalesDialog"))
            {
                Debug.WriteLine("Dialog is open, not processing shortcut");
                return;
            }
            
            // Get current SaleViewModel if on Sales screen
            var saleViewModel = GetCurrentSaleViewModel();
            
            switch (e.Key)
            {
                case Key.F12: // Performance debug control
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.ShowDebugControlDialog();
                        e.Handled = true;
                        return;
                    }
                    break;

                case Key.F3: // Search shortcut
                    Debug.WriteLine("F3 key detected in MainWindow");
                    if (saleViewModel != null)
                    {
                        // Find and focus the search box
                        Debug.WriteLine("Attempting to find and focus search box");
                        if (FindAndFocusSearchBox())
                        {
                            e.Handled = true;
                            Debug.WriteLine("Search box focused and handled");
                        }
                        else
                        {
                            Debug.WriteLine("Failed to find search box");
                        }
                    }
                    break;
                    
                case Key.F4: // Payment shortcut
                    Debug.WriteLine("F4 key detected in MainWindow");
                    if (saleViewModel != null)
                    {
                        Debug.WriteLine($"Cart has items: {saleViewModel.HasCartItems}");
                        // Trigger payment dialog - removed the HasCartItems check to help diagnose
                        if (_currentView is SalesViewGrid salesViewGrid)
                        {
                            try
                            {
                                Debug.WriteLine("Attempting to show payment dialog");
                                await salesViewGrid.ShowPaymentDialog();
                                e.Handled = true;
                                Debug.WriteLine("Payment dialog shown and handled");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error showing payment dialog: {ex.Message}");
                                MessageBox.Show("Error opening payment dialog. Please try again.", "Error",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                        else if (_currentView is SalesView salesView)
                        {
                            // For older sales view implementation
                            try
                            {
                                Debug.WriteLine("Attempting to call ProcessPayment on older view");
                                var methodInfo = salesView.GetType().GetMethod("ProcessPayment_Click", 
                                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                                if (methodInfo != null)
                                {
                                    methodInfo.Invoke(salesView, new object[] { salesView, new RoutedEventArgs() });
                                    Debug.WriteLine("Successfully called ProcessPayment");
                                }
                                else
                                {
                                    Debug.WriteLine("Failed to find ProcessPayment_Click method");
                                }
                                e.Handled = true;
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error calling ProcessPayment: {ex.Message}");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"Current view is not a sales view: {_currentView?.GetType().Name ?? "null"}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine("SaleViewModel is null");
                    }
                    break;
            }
        }

        private void CheckForAlerts()
        {
            // Run CheckExpiringProducts to identify and create alerts
            _alertService.CheckExpiringProducts();
            
            // Force refresh notifications to ensure the UI is updated
            ForceRefreshNotifications();
        }

        private void ForceRefreshNotifications()
        {
            // Clear all notification caches
            _cachedAlerts = null;
            _cachedNotifications = null;
            _lastNotificationCheck = DateTime.MinValue;
            
            // Clear the alert service cache
            _alertService.ClearAlertCache();
            
            // Update the notification count in the UI
            UpdateNotificationCount();
        }

        private void RefreshNotificationsAndAlerts()
        {
            CheckForAlerts();
        }

        private async void btnSwitchUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Launch a new instance with the switch-user argument
                var startInfo = new ProcessStartInfo
                {
                    FileName = Process.GetCurrentProcess().MainModule.FileName,
                    Arguments = "switch-user",
                    UseShellExecute = true
                };
                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error switching user: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // Interface for view models that need async initialization
    public interface IAsyncInitializable
    {
        Task InitializeAsync();
    }
} 