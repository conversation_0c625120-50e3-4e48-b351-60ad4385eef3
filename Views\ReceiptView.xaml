﻿<UserControl x:Class="POSSystem.Views.ReceiptView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignWidth="300">
    <FlowDocument>
        <!-- Header content -->
        <Paragraph TextAlignment="Left">
            <Run FontSize="16" FontWeight="Bold" Text="POS SYSTEM"/>
            <LineBreak/>
            <Run Text="123 Main Street"/>
            <LineBreak/>
            <Run Text="New York, NY 10001"/>
            <LineBreak/>
            <Run Text="Tel: (*************"/>
        </Paragraph>

        <Paragraph TextAlignment="Left">
            <Run Text="{Binding SaleDate, StringFormat='Date: {0:MM/dd/yyyy hh:mm tt}', Mode=OneWay}"/>
            <LineBreak/>
            <Run Text="----------------------------------------"/>
        </Paragraph>

        <Table>
            <Table.Columns>
                <TableColumn Width="140"/>
                <TableColumn Width="60"/>
                <TableColumn Width="80"/>
            </Table.Columns>

            <!-- Header Row -->
            <TableRowGroup>
                <TableRow Background="#EEEEEE" FontWeight="Bold">
                    <TableCell>
                        <Paragraph>Item</Paragraph>
                    </TableCell>
                    <TableCell>
                        <Paragraph>Qty</Paragraph>
                    </TableCell>
                    <TableCell>
                        <Paragraph>Total</Paragraph>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell ColumnSpan="3">
                        <Paragraph>
                            <Run Text="----------------------------------------"/>
                        </Paragraph>
                    </TableCell>
                </TableRow>
            </TableRowGroup>

            <!-- Dynamic Items -->
            <TableRowGroup>
                <TableRow>
                    <TableCell ColumnSpan="3">
                        <BlockUIContainer>
                            <ItemsControl ItemsSource="{Binding SaleItems}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="140"/>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="80"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="{Binding Product.Name}" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="1" Text="{Binding Quantity}" TextAlignment="Center"/>
                                            <TextBlock Grid.Column="2" Text="{Binding Total, StringFormat={}{0:N2} DA}" TextAlignment="Right"/>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </BlockUIContainer>
                    </TableCell>
                </TableRow>
            </TableRowGroup>
        </Table>

        <!-- Totals section -->
        <Paragraph TextAlignment="Left">
            <Run Text="----------------------------------------"/>
            <LineBreak/>
            <Run Text="{Binding Subtotal, StringFormat='Subtotal: {0:N2} DA', Mode=OneWay}"/>
            <LineBreak/>
            <Run Text="{Binding DiscountAmount, StringFormat='Discount: {0:N2} DA', Mode=OneWay}"/>
            <LineBreak/>
            <Run Text="{Binding TaxAmount, StringFormat='Tax: {0:N2} DA', Mode=OneWay}"/>
            <LineBreak/>
            <Run Text="{Binding GrandTotal, StringFormat='TOTAL: {0:N2} DA', Mode=OneWay}" FontWeight="Bold"/>
            <LineBreak/>
            <Run Text="----------------------------------------"/>
        </Paragraph>

        <Paragraph TextAlignment="Left" FontStyle="Italic">
            <Run Text="Thank you for your business!"/>
            <LineBreak/>
            <Run Text="www.yourpossystem.com"/>
        </Paragraph>
    </FlowDocument>
</UserControl>