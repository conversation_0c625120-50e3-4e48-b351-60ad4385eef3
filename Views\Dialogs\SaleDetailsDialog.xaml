<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Dialogs.SaleDetailsDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             mc:Ignorable="d"
             Width="900"
             MaxWidth="900"
             MinWidth="600"
             MaxHeight="800"
             x:Name="SaleDetailsRoot">

    <UserControl.Resources>
        <ResourceDictionary>
            <!-- Import Converters -->
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/POSSystem;component/Resources/Converters.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Local Converters -->
            <converters:GreaterThanZeroConverter x:Key="GreaterThanZeroConverter" xmlns:converters="clr-namespace:POSSystem.Converters"/>

        <!-- Enhanced Dialog Animations -->
        <Storyboard x:Key="DialogOpenAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1"
                           Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <QuarticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                           From="0.9" To="1.0"
                           Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <QuarticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                           From="0.9" To="1.0"
                           Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <QuarticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Enhanced Card Style with Modern Animations -->
        <Style x:Key="SaleDetailsCard" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="UniformCornerRadius" Value="12"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <TranslateTransform/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                               To="-2" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                               To="0" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced Button Style with Modern Gradients -->
        <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Margin" Value="0,0,12,0"/>
            <Setter Property="Padding" Value="16,0"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1.05" Duration="0:0:0.1"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1.05" Duration="0:0:0.1"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1.0" Duration="0:0:0.1"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1.0" Duration="0:0:0.1"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Primary Action Button Style with Gradient -->
        <Style x:Key="PrimaryActionButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#0078D4" Offset="0.0"/>
                        <GradientStop Color="#0067B5" Offset="1.0"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Enhanced DataGrid Row Style with Smooth Animations -->
        <Style x:Key="DataGridRowStyle" TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="#E3F2FD" Opacity="0.8"/>
                        </Setter.Value>
                    </Setter>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               To="1" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                               To="0.8" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource Primary200}" Opacity="0.6"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Status Color Converter -->
        <Style x:Key="PaymentStatusStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody1TextBlock}">
            <Setter Property="FontWeight" Value="Medium"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding PaymentStatus}" Value="Paid">
                    <Setter Property="Foreground" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentStatus}" Value="Partially Paid">
                    <Setter Property="Foreground" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentStatus}" Value="Unpaid">
                    <Setter Property="Foreground" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                        UniformCornerRadius="12"
                        materialDesign:ElevationAssist.Elevation="Dp4"
                        RenderTransformOrigin="0.5,0.5"
                        Opacity="0">
        <materialDesign:Card.RenderTransform>
            <ScaleTransform ScaleX="0.9" ScaleY="0.9"/>
        </materialDesign:Card.RenderTransform>
        <materialDesign:Card.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource DialogOpenAnimation}"/>
            </EventTrigger>
        </materialDesign:Card.Triggers>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Enhanced Header with Gradient Background -->
            <Grid Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}">
                <Border CornerRadius="12,12,0,0" Padding="24,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel>
                            <TextBlock Text="{DynamicResource SaleDetails}"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     Foreground="White"
                                     FontWeight="Medium"/>
                            <TextBlock Text="{Binding InvoiceNumber, StringFormat='Invoice: {0}'}"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Foreground="White"
                                     Opacity="0.9"
                                     Margin="0,4,0,0"/>
                        </StackPanel>

                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                                Foreground="White"
                                VerticalAlignment="Top">
                            <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                        </Button>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced Content Section with Consistent Spacing -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         Padding="24,20,24,0"
                         Margin="0,0,0,8">
                <StackPanel>
                    <!-- Transaction Information Card -->
                    <materialDesign:Card Style="{StaticResource SaleDetailsCard}">
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <TextBlock Text="{DynamicResource TransactionInformation}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <Rectangle Height="1"
                                         Fill="{DynamicResource MaterialDesignDivider}"
                                         VerticalAlignment="Bottom"
                                         Margin="0,20,0,0"/>
                            </Grid>

                            <!-- Transaction Details Grid -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Sale Date -->
                                <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,16,12">
                                    <TextBlock Text="{DynamicResource SaleDate}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding SaleDate, StringFormat='dddd, MMMM dd, yyyy'}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Margin="0,2,0,0"/>
                                </StackPanel>

                                <!-- Due Date -->
                                <StackPanel Grid.Column="1" Grid.Row="0" Margin="16,0,0,12">
                                    <TextBlock Text="{DynamicResource DueDate}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding DueDate, StringFormat='dddd, MMMM dd, yyyy'}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Margin="0,2,0,0"/>
                                </StackPanel>

                                <!-- Customer -->
                                <StackPanel Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" Margin="0,0,0,12">
                                    <TextBlock Text="{DynamicResource Customer}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Margin="0,2,0,0">
                                        <Run Text="{Binding Customer.FirstName, Mode=OneWay}"/>
                                        <Run Text=" "/>
                                        <Run Text="{Binding Customer.LastName, Mode=OneWay}"/>
                                    </TextBlock>
                                </StackPanel>

                                <!-- Payment Status -->
                                <StackPanel Grid.Column="0" Grid.Row="2" Margin="0,0,16,12">
                                    <TextBlock Text="{DynamicResource PaymentStatus}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding PaymentStatus}"
                                             Style="{StaticResource PaymentStatusStyle}"
                                             Margin="0,2,0,0"/>
                                </StackPanel>

                                <!-- Payment Method -->
                                <StackPanel Grid.Column="1" Grid.Row="2" Margin="16,0,0,12">
                                    <TextBlock Text="{DynamicResource PaymentMethod}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Text="{Binding PaymentMethod}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                             Margin="0,2,0,0"/>
                                </StackPanel>

                                <!-- Remaining Amount (if any) -->
                                <StackPanel Grid.Column="0" Grid.Row="3" Grid.ColumnSpan="2" Margin="0,0,0,0">
                                    <StackPanel.Style>
                                        <Style TargetType="StackPanel">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RemainingAmount, Mode=OneWay, Converter={StaticResource GreaterThanZeroConverter}}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="{DynamicResource RemainingAmount}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontWeight="Medium"/>
                                    <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Foreground="#F44336"
                                             FontWeight="Medium"
                                             Margin="0,2,0,0">
                                        <Run Text="{Binding RemainingAmount, Mode=OneWay, StringFormat={}{0:N2}}"/>
                                        <Run Text="{DynamicResource DA}"/>
                                    </TextBlock>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Items Section -->
                    <materialDesign:Card Style="{StaticResource SaleDetailsCard}">
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header with Item Count -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Text="{DynamicResource SaleItems}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                                <materialDesign:Chip Grid.Column="1"
                                                   Content="{Binding Items.Count, StringFormat={}{0} items}"
                                                   Background="{DynamicResource PrimaryHueLightBrush}"
                                                   Foreground="{DynamicResource PrimaryHueLightForegroundBrush}"
                                                   FontSize="12"
                                                   Height="24"/>

                                <Rectangle Grid.ColumnSpan="2"
                                         Height="1"
                                         Fill="{DynamicResource MaterialDesignDivider}"
                                         VerticalAlignment="Bottom"
                                         Margin="0,20,0,0"/>
                            </Grid>

                            <!-- Enhanced DataGrid with Modern Styling -->
                            <DataGrid Grid.Row="1"
                                     ItemsSource="{Binding Items}"
                                     RowStyle="{StaticResource DataGridRowStyle}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     IsReadOnly="True"
                                     Style="{StaticResource MaterialDesignDataGrid}"
                                     materialDesign:DataGridAssist.CellPadding="12,8"
                                     materialDesign:DataGridAssist.ColumnHeaderPadding="12,8"
                                     HeadersVisibility="Column"
                                     GridLinesVisibility="None"
                                     BorderThickness="0"
                                     Background="Transparent"
                                     RowBackground="Transparent"
                                     AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                                     RowHeight="56"
                                     MaxHeight="300"
                                     ScrollViewer.CanContentScroll="True">
                                <DataGrid.Resources>
                                    <!-- Enhanced Cell Style -->
                                    <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="{DynamicResource Product}"
                                                      Binding="{Binding Product.Name}"
                                                      Width="2*">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                                <Setter Property="FontWeight" Value="Medium"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="{DynamicResource Quantity}"
                                                      Binding="{Binding Quantity}"
                                                      Width="80">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="{DynamicResource UnitPrice}"
                                                      Width="120">
                                        <DataGridTextColumn.Binding>
                                            <MultiBinding StringFormat="{}{0:N2} DA">
                                                <MultiBinding.Converter>
                                                    <local:HistoricalPriceConverter/>
                                                </MultiBinding.Converter>
                                                <Binding Path="Total"/>
                                                <Binding Path="Quantity"/>
                                                <Binding Path="UnitPrice"/>
                                            </MultiBinding>
                                        </DataGridTextColumn.Binding>
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="{DynamicResource Total}"
                                                      Binding="{Binding Total, StringFormat={}{0:N2} DA}"
                                                      Width="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>
                    <!-- Financial Summary Card -->
                    <materialDesign:Card Style="{StaticResource SaleDetailsCard}" Margin="0,0,0,0">
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <TextBlock Text="{DynamicResource FinancialSummary}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <Rectangle Height="1"
                                         Fill="{DynamicResource MaterialDesignDivider}"
                                         VerticalAlignment="Bottom"
                                         Margin="0,20,0,0"/>
                            </Grid>

                            <!-- Financial Details -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="140"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Subtotal -->
                                <TextBlock Grid.Column="0" Grid.Row="0"
                                         Text="{DynamicResource Subtotal}"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,16,8"/>
                                <TextBlock Grid.Column="1" Grid.Row="0"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,0,8">
                                    <Run Text="{Binding Subtotal, StringFormat={}{0:N2}}"/>
                                    <Run Text="{DynamicResource DA}"/>
                                </TextBlock>

                                <!-- Discount -->
                                <TextBlock Grid.Column="0" Grid.Row="1"
                                         Text="{DynamicResource Discount}"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,16,8"/>
                                <TextBlock Grid.Column="1" Grid.Row="1"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Foreground="#F44336"
                                         Margin="0,0,0,8">
                                    <Run Text="-"/>
                                    <Run Text="{Binding DiscountAmount, StringFormat={}{0:N2}}"/>
                                    <Run Text="{DynamicResource DA}"/>
                                </TextBlock>

                                <!-- Tax -->
                                <TextBlock Grid.Column="0" Grid.Row="2"
                                         Text="{DynamicResource Tax}"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,16,8"/>
                                <TextBlock Grid.Column="1" Grid.Row="2"
                                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,0,8">
                                    <Run Text="{Binding TaxAmount, StringFormat={}{0:N2}}"/>
                                    <Run Text="{DynamicResource DA}"/>
                                </TextBlock>

                                <!-- Separator -->
                                <Rectangle Grid.Column="0" Grid.Row="3" Grid.ColumnSpan="2"
                                         Height="1"
                                         Fill="{DynamicResource MaterialDesignDivider}"
                                         Margin="0,8,0,12"/>

                                <!-- Grand Total -->
                                <TextBlock Grid.Column="0" Grid.Row="4"
                                         Text="{DynamicResource GrandTotal}"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         FontWeight="SemiBold"
                                         HorizontalAlignment="Right"
                                         Margin="0,0,16,0"/>
                                <TextBlock Grid.Column="1" Grid.Row="4"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         FontWeight="SemiBold"
                                         HorizontalAlignment="Right"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}">
                                    <Run Text="{Binding GrandTotal, StringFormat={}{0:N2}}"/>
                                    <Run Text="{DynamicResource DA}"/>
                                </TextBlock>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Enhanced Action Section -->
            <Grid Grid.Row="2" Background="{DynamicResource MaterialDesignBackground}">
                <Border Padding="24,20" CornerRadius="0,0,12,12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Enhanced Action Buttons -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <!-- Print Actions with Enhanced Styling -->
                            <Button x:Name="ReprintReceiptButton"
                                    Content="{DynamicResource ReprintReceipt}"
                                    Style="{StaticResource PrimaryActionButtonStyle}"
                                    Click="ReprintReceipt_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="{DynamicResource ReprintReceiptTooltip}"/>
                                </Button.ToolTip>
                            </Button>

                            <Button x:Name="PreviewReceiptButton"
                                    Content="{DynamicResource PreviewReceipt}"
                                    Style="{StaticResource ActionButtonStyle}"
                                    Background="{DynamicResource MaterialDesignBody}"
                                    Foreground="White"
                                    Click="PreviewReceipt_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="{DynamicResource PreviewReceiptTooltip}"/>
                                </Button.ToolTip>
                            </Button>

                            <Button x:Name="SavePdfButton"
                                    Content="{DynamicResource SaveAsPDF}"
                                    Style="{StaticResource ActionButtonStyle}"
                                    Background="#4CAF50"
                                    Foreground="White"
                                    Click="SaveReceiptAsPdf_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="{DynamicResource SaveAsPDFTooltip}"/>
                                </Button.ToolTip>
                            </Button>
                        </StackPanel>

                        <!-- Enhanced Close Button -->
                        <Button Grid.Column="1"
                                Content="{DynamicResource Close}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Height="45"
                                Padding="24,0"
                                materialDesign:ButtonAssist.CornerRadius="8"
                                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="{DynamicResource PrimaryHueMidBrush}"
                                FontWeight="SemiBold"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </materialDesign:Card>
</UserControl>