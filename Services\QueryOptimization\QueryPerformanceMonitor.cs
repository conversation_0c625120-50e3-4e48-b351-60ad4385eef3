using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.QueryOptimization
{
    /// <summary>
    /// ✅ CRITICAL DATABASE OPTIMIZATION: Monitor and track database query performance to identify slow queries and N+1 problems
    /// </summary>
    public class QueryPerformanceMonitor : IDisposable
    {
        private readonly ILogger<QueryPerformanceMonitor> _logger;
        private readonly ConcurrentDictionary<string, QueryMetrics> _queryMetrics;
        private readonly ConcurrentQueue<SlowQueryAlert> _slowQueryAlerts;
        private readonly Timer _reportingTimer;
        private readonly Timer _alertCheckTimer;
        private readonly object _lockObject = new object();
        private bool _disposed;

        // Performance thresholds for POS system
        private const int SLOW_QUERY_THRESHOLD_MS = 500;
        private const int CRITICAL_QUERY_THRESHOLD_MS = 1000;
        private const int MAX_METRICS_RETENTION = 1000;
        private const int MAX_ALERTS_RETENTION = 100;

        public QueryPerformanceMonitor(ILogger<QueryPerformanceMonitor> logger = null)
        {
            _logger = logger;
            _queryMetrics = new ConcurrentDictionary<string, QueryMetrics>();
            _slowQueryAlerts = new ConcurrentQueue<SlowQueryAlert>();

            // Report performance metrics every 5 minutes
            _reportingTimer = new Timer(ReportPerformanceMetrics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            // Check for performance alerts every minute
            _alertCheckTimer = new Timer(CheckPerformanceAlerts, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

            Debug.WriteLine("✅ [QUERY-MONITOR] Query Performance Monitor initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Track query execution time and detect performance issues
        /// </summary>
        public IDisposable TrackQuery(string queryName, string queryType = "Unknown")
        {
            return new QueryTracker(this, queryName, queryType);
        }

        /// <summary>
        /// ✅ INTERNAL: Record query execution metrics
        /// </summary>
        internal void RecordQueryExecution(string queryName, string queryType, long executionTimeMs, bool wasSuccessful)
        {
            try
            {
                var key = $"{queryType}:{queryName}";
                
                _queryMetrics.AddOrUpdate(key, 
                    new QueryMetrics
                    {
                        QueryName = queryName,
                        QueryType = queryType,
                        ExecutionCount = 1,
                        TotalExecutionTimeMs = executionTimeMs,
                        MinExecutionTimeMs = executionTimeMs,
                        MaxExecutionTimeMs = executionTimeMs,
                        SuccessfulExecutions = wasSuccessful ? 1 : 0,
                        LastExecutionTime = DateTime.Now
                    },
                    (existingKey, existingMetrics) =>
                    {
                        existingMetrics.ExecutionCount++;
                        existingMetrics.TotalExecutionTimeMs += executionTimeMs;
                        existingMetrics.MinExecutionTimeMs = Math.Min(existingMetrics.MinExecutionTimeMs, executionTimeMs);
                        existingMetrics.MaxExecutionTimeMs = Math.Max(existingMetrics.MaxExecutionTimeMs, executionTimeMs);
                        existingMetrics.LastExecutionTime = DateTime.Now;
                        
                        if (wasSuccessful)
                        {
                            existingMetrics.SuccessfulExecutions++;
                        }
                        
                        return existingMetrics;
                    });

                // Check for slow query alerts
                if (executionTimeMs >= SLOW_QUERY_THRESHOLD_MS)
                {
                    var alertLevel = executionTimeMs >= CRITICAL_QUERY_THRESHOLD_MS ? AlertLevel.Critical : AlertLevel.Warning;
                    
                    var alert = new SlowQueryAlert
                    {
                        QueryName = queryName,
                        QueryType = queryType,
                        ExecutionTimeMs = executionTimeMs,
                        Timestamp = DateTime.Now,
                        Level = alertLevel
                    };
                    
                    _slowQueryAlerts.Enqueue(alert);
                    
                    // Limit alert queue size
                    while (_slowQueryAlerts.Count > MAX_ALERTS_RETENTION)
                    {
                        _slowQueryAlerts.TryDequeue(out _);
                    }

                    Debug.WriteLine($"🚨 [QUERY-MONITOR] {alertLevel} slow query detected: {queryName} took {executionTimeMs}ms");
                }

                // Limit metrics retention
                if (_queryMetrics.Count > MAX_METRICS_RETENTION)
                {
                    CleanupOldMetrics();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [QUERY-MONITOR] Error recording query metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ MONITORING: Get current performance statistics
        /// </summary>
        public QueryPerformanceStats GetPerformanceStats()
        {
            lock (_lockObject)
            {
                var metrics = _queryMetrics.Values.ToList();
                var alerts = _slowQueryAlerts.ToList();

                return new QueryPerformanceStats
                {
                    TotalQueries = metrics.Sum(m => m.ExecutionCount),
                    UniqueQueryTypes = metrics.Count,
                    AverageExecutionTimeMs = metrics.Any() ? metrics.Average(m => m.AverageExecutionTimeMs) : 0,
                    SlowQueryCount = alerts.Count(a => a.Level == AlertLevel.Warning),
                    CriticalQueryCount = alerts.Count(a => a.Level == AlertLevel.Critical),
                    TopSlowQueries = metrics
                        .OrderByDescending(m => m.AverageExecutionTimeMs)
                        .Take(10)
                        .ToList(),
                    RecentAlerts = alerts
                        .OrderByDescending(a => a.Timestamp)
                        .Take(20)
                        .ToList()
                };
            }
        }

        /// <summary>
        /// ✅ MONITORING: Get detailed report of query performance
        /// </summary>
        public string GetDetailedPerformanceReport()
        {
            var stats = GetPerformanceStats();
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== Database Query Performance Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("📊 Overall Statistics:");
            report.AppendLine($"  Total Queries Executed: {stats.TotalQueries:N0}");
            report.AppendLine($"  Unique Query Types: {stats.UniqueQueryTypes}");
            report.AppendLine($"  Average Execution Time: {stats.AverageExecutionTimeMs:F1}ms");
            report.AppendLine($"  Slow Queries (>{SLOW_QUERY_THRESHOLD_MS}ms): {stats.SlowQueryCount}");
            report.AppendLine($"  Critical Queries (>{CRITICAL_QUERY_THRESHOLD_MS}ms): {stats.CriticalQueryCount}");
            report.AppendLine();

            if (stats.TopSlowQueries.Any())
            {
                report.AppendLine("🐌 Top 10 Slowest Queries:");
                foreach (var query in stats.TopSlowQueries)
                {
                    var successRate = query.ExecutionCount > 0 ? (query.SuccessfulExecutions * 100.0 / query.ExecutionCount) : 0;
                    report.AppendLine($"  • {query.QueryType}:{query.QueryName}");
                    report.AppendLine($"    Avg: {query.AverageExecutionTimeMs:F1}ms | Count: {query.ExecutionCount} | Success: {successRate:F1}%");
                    report.AppendLine($"    Range: {query.MinExecutionTimeMs}ms - {query.MaxExecutionTimeMs}ms");
                }
                report.AppendLine();
            }

            if (stats.RecentAlerts.Any())
            {
                report.AppendLine("🚨 Recent Performance Alerts:");
                foreach (var alert in stats.RecentAlerts.Take(10))
                {
                    var icon = alert.Level == AlertLevel.Critical ? "🔴" : "🟠";
                    report.AppendLine($"  {icon} {alert.Timestamp:HH:mm:ss} - {alert.QueryType}:{alert.QueryName} ({alert.ExecutionTimeMs}ms)");
                }
            }

            return report.ToString();
        }

        /// <summary>
        /// ✅ INTERNAL: Report performance metrics periodically
        /// </summary>
        private void ReportPerformanceMetrics(object state)
        {
            try
            {
                var stats = GetPerformanceStats();
                
                Debug.WriteLine($"[QUERY-MONITOR] Performance Summary: {stats.TotalQueries} queries, avg {stats.AverageExecutionTimeMs:F1}ms");
                
                if (stats.SlowQueryCount > 0 || stats.CriticalQueryCount > 0)
                {
                    Debug.WriteLine($"[QUERY-MONITOR] ⚠️ Performance Issues: {stats.SlowQueryCount} slow, {stats.CriticalQueryCount} critical queries");
                }

                // Log to application logger if available
                _logger?.LogInformation("Query Performance: {TotalQueries} queries, avg {AvgTime}ms, {SlowCount} slow queries", 
                    stats.TotalQueries, stats.AverageExecutionTimeMs, stats.SlowQueryCount);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [QUERY-MONITOR] Error reporting metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Check for performance alerts and patterns
        /// </summary>
        private void CheckPerformanceAlerts(object state)
        {
            try
            {
                var recentAlerts = _slowQueryAlerts
                    .Where(a => a.Timestamp > DateTime.Now.AddMinutes(-5))
                    .ToList();

                if (recentAlerts.Count > 10)
                {
                    Debug.WriteLine($"🚨 [QUERY-MONITOR] High frequency of slow queries detected: {recentAlerts.Count} in last 5 minutes");
                    
                    var criticalQueries = recentAlerts
                        .Where(a => a.Level == AlertLevel.Critical)
                        .GroupBy(a => a.QueryName)
                        .Where(g => g.Count() > 2)
                        .ToList();

                    foreach (var group in criticalQueries)
                    {
                        Debug.WriteLine($"🔴 [QUERY-MONITOR] Repeated critical performance issue: {group.Key} ({group.Count()} occurrences)");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [QUERY-MONITOR] Error checking alerts: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Clean up old metrics to prevent memory growth
        /// </summary>
        private void CleanupOldMetrics()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddHours(-24);
                var keysToRemove = _queryMetrics
                    .Where(kvp => kvp.Value.LastExecutionTime < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _queryMetrics.TryRemove(key, out _);
                }

                if (keysToRemove.Count > 0)
                {
                    Debug.WriteLine($"[QUERY-MONITOR] Cleaned up {keysToRemove.Count} old query metrics");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [QUERY-MONITOR] Error cleaning up metrics: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _reportingTimer?.Dispose();
                _alertCheckTimer?.Dispose();
                
                // Final performance report
                var finalReport = GetDetailedPerformanceReport();
                Debug.WriteLine(finalReport);

                Debug.WriteLine("✅ [QUERY-MONITOR] Query Performance Monitor disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [QUERY-MONITOR] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Query execution tracker - implements IDisposable for using statement
    /// </summary>
    internal class QueryTracker : IDisposable
    {
        private readonly QueryPerformanceMonitor _monitor;
        private readonly string _queryName;
        private readonly string _queryType;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;

        public QueryTracker(QueryPerformanceMonitor monitor, string queryName, string queryType)
        {
            _monitor = monitor;
            _queryName = queryName;
            _queryType = queryType;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            if (_disposed) return;

            _stopwatch.Stop();
            _monitor.RecordQueryExecution(_queryName, _queryType, _stopwatch.ElapsedMilliseconds, true);
            _disposed = true;
        }
    }

    /// <summary>
    /// Data structures for performance monitoring
    /// </summary>
    public class QueryMetrics
    {
        public string QueryName { get; set; }
        public string QueryType { get; set; }
        public long ExecutionCount { get; set; }
        public long TotalExecutionTimeMs { get; set; }
        public long MinExecutionTimeMs { get; set; }
        public long MaxExecutionTimeMs { get; set; }
        public long SuccessfulExecutions { get; set; }
        public DateTime LastExecutionTime { get; set; }
        
        public double AverageExecutionTimeMs => ExecutionCount > 0 ? (double)TotalExecutionTimeMs / ExecutionCount : 0;
    }

    public class SlowQueryAlert
    {
        public string QueryName { get; set; }
        public string QueryType { get; set; }
        public long ExecutionTimeMs { get; set; }
        public DateTime Timestamp { get; set; }
        public AlertLevel Level { get; set; }
    }

    public class QueryPerformanceStats
    {
        public long TotalQueries { get; set; }
        public int UniqueQueryTypes { get; set; }
        public double AverageExecutionTimeMs { get; set; }
        public int SlowQueryCount { get; set; }
        public int CriticalQueryCount { get; set; }
        public List<QueryMetrics> TopSlowQueries { get; set; } = new List<QueryMetrics>();
        public List<SlowQueryAlert> RecentAlerts { get; set; } = new List<SlowQueryAlert>();
    }

    public enum AlertLevel
    {
        Warning,
        Critical
    }
}
