using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.Migration
{
    /// <summary>
    /// ✅ DATABASE MIGRATION SERVICE: Migrates product data from legacy pos2.db to current pos.db
    /// </summary>
    public class DatabaseMigrationService
    {
        private readonly ILogger<DatabaseMigrationService> _logger;
        private readonly string _currentDbPath;
        private readonly string _legacyDbPath;

        public DatabaseMigrationService(ILogger<DatabaseMigrationService> logger = null)
        {
            _logger = logger;
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _currentDbPath = Path.Combine(baseDirectory, "pos.db");
            _legacyDbPath = Path.Combine(baseDirectory, "pos2.db");
        }

        /// <summary>
        /// Migrates product data from legacy database to current database
        /// </summary>
        public async Task<MigrationResult> MigrateProductDataAsync()
        {
            var result = new MigrationResult();
            
            try
            {
                // Step 1: Validate databases exist
                if (!ValidateDatabases(result))
                    return result;

                // Step 2: Create backup of current database
                await CreateBackupAsync(result);

                // Step 3: Analyze legacy database schema
                var legacySchema = await AnalyzeLegacySchemaAsync();
                result.LegacyProductCount = legacySchema.ProductCount;

                // Step 4: Migrate categories first (dependencies)
                await MigrateCategoriesAsync(result);

                // Step 5: Migrate products
                await MigrateProductsAsync(result);

                // Step 6: Migrate product barcodes
                await MigrateProductBarcodesAsync(result);

                // Step 7: Validate migration integrity
                await ValidateMigrationIntegrityAsync(result);

                result.IsSuccess = true;
                result.Message = $"Migration completed successfully. Migrated {result.MigratedProductCount} products and {result.MigratedCategoryCount} categories.";
                
                _logger?.LogInformation("Database migration completed successfully");
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"Migration failed: {ex.Message}";
                result.Errors.Add(ex.Message);
                _logger?.LogError(ex, "Database migration failed");
            }

            return result;
        }

        private bool ValidateDatabases(MigrationResult result)
        {
            if (!File.Exists(_legacyDbPath))
            {
                result.Errors.Add($"Legacy database not found at: {_legacyDbPath}");
                return false;
            }

            if (!File.Exists(_currentDbPath))
            {
                result.Errors.Add($"Current database not found at: {_currentDbPath}");
                return false;
            }

            return true;
        }

        private async Task CreateBackupAsync(MigrationResult result)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupPath = Path.Combine(Path.GetDirectoryName(_currentDbPath), $"pos_backup_{timestamp}.db");
                
                File.Copy(_currentDbPath, backupPath);
                result.BackupPath = backupPath;
                
                _logger?.LogInformation($"Database backup created at: {backupPath}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create database backup: {ex.Message}");
            }
        }

        private async Task<LegacyDatabaseSchema> AnalyzeLegacySchemaAsync()
        {
            var schema = new LegacyDatabaseSchema();
            
            using var connection = new SQLiteConnection($"Data Source={_legacyDbPath};Version=3;");
            await connection.OpenAsync();

            // Get table structure
            var tablesCmd = connection.CreateCommand();
            tablesCmd.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'";
            
            using var reader = await tablesCmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                schema.Tables.Add(reader.GetString(0));
            }

            // Count products
            if (schema.Tables.Contains("Products"))
            {
                var countCmd = connection.CreateCommand();
                countCmd.CommandText = "SELECT COUNT(*) FROM Products";
                schema.ProductCount = Convert.ToInt32(await countCmd.ExecuteScalarAsync());
            }

            // Count categories
            if (schema.Tables.Contains("Categories"))
            {
                var countCmd = connection.CreateCommand();
                countCmd.CommandText = "SELECT COUNT(*) FROM Categories";
                schema.CategoryCount = Convert.ToInt32(await countCmd.ExecuteScalarAsync());
            }

            return schema;
        }

        private async Task MigrateCategoriesAsync(MigrationResult result)
        {
            using var legacyConn = new SQLiteConnection($"Data Source={_legacyDbPath};Version=3;");
            using var currentConn = new SQLiteConnection($"Data Source={_currentDbPath};Version=3;");
            
            await legacyConn.OpenAsync();
            await currentConn.OpenAsync();

            // Check if Categories table exists in legacy database
            var checkTableCmd = legacyConn.CreateCommand();
            checkTableCmd.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='Categories'";
            var tableExists = await checkTableCmd.ExecuteScalarAsync() != null;

            if (!tableExists)
            {
                _logger?.LogWarning("Categories table not found in legacy database");
                return;
            }

            // Get legacy categories
            var selectCmd = legacyConn.CreateCommand();
            selectCmd.CommandText = "SELECT Id, Name, Description FROM Categories WHERE IsActive = 1 OR IsActive IS NULL";
            
            using var reader = await selectCmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var id = reader.GetInt32(0);
                var name = reader.GetString(1);
                var description = reader.IsDBNull(2) ? "" : reader.GetString(2);

                // Check if category already exists in current database
                var existsCmd = currentConn.CreateCommand();
                existsCmd.CommandText = "SELECT COUNT(*) FROM Categories WHERE Name = @Name";
                existsCmd.Parameters.AddWithValue("@Name", name);
                
                var exists = Convert.ToInt32(await existsCmd.ExecuteScalarAsync()) > 0;
                
                if (!exists)
                {
                    // Insert new category
                    var insertCmd = currentConn.CreateCommand();
                    insertCmd.CommandText = @"
                        INSERT INTO Categories (Name, Description, IsActive) 
                        VALUES (@Name, @Description, 1)";
                    insertCmd.Parameters.AddWithValue("@Name", name);
                    insertCmd.Parameters.AddWithValue("@Description", description);
                    
                    await insertCmd.ExecuteNonQueryAsync();
                    result.MigratedCategoryCount++;
                }
            }
        }

        private async Task MigrateProductsAsync(MigrationResult result)
        {
            using var legacyConn = new SQLiteConnection($"Data Source={_legacyDbPath};Version=3;");
            using var currentConn = new SQLiteConnection($"Data Source={_currentDbPath};Version=3;");
            
            await legacyConn.OpenAsync();
            await currentConn.OpenAsync();

            // Get legacy products with category mapping
            var selectCmd = legacyConn.CreateCommand();
            selectCmd.CommandText = @"
                SELECT p.Id, p.Name, p.SKU, p.Description, p.PurchasePrice, p.SellingPrice, 
                       p.StockQuantity, p.MinimumStock, p.ReorderPoint, p.Barcode,
                       c.Name as CategoryName, p.IsActive
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.IsActive = 1 OR p.IsActive IS NULL";
            
            using var reader = await selectCmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                try
                {
                    var productData = ExtractProductData(reader);
                    
                    // Get or create category in current database
                    var categoryId = await GetOrCreateCategoryAsync(currentConn, productData.CategoryName);
                    
                    // Check if product already exists (by SKU or Name)
                    if (!await ProductExistsAsync(currentConn, productData.SKU, productData.Name))
                    {
                        await InsertProductAsync(currentConn, productData, categoryId);
                        result.MigratedProductCount++;
                    }
                    else
                    {
                        result.SkippedProductCount++;
                    }
                }
                catch (Exception ex)
                {
                    result.Errors.Add($"Error migrating product: {ex.Message}");
                    _logger?.LogError(ex, "Error migrating individual product");
                }
            }
        }

        private async Task MigrateProductBarcodesAsync(MigrationResult result)
        {
            // Implementation for migrating product barcodes if they exist in separate table
            // This is a placeholder for future enhancement
            await Task.CompletedTask;
        }

        private async Task ValidateMigrationIntegrityAsync(MigrationResult result)
        {
            using var currentConn = new SQLiteConnection($"Data Source={_currentDbPath};Version=3;");
            await currentConn.OpenAsync();

            // Validate that migrated products have valid categories
            var validationCmd = currentConn.CreateCommand();
            validationCmd.CommandText = @"
                SELECT COUNT(*) FROM Products p 
                LEFT JOIN Categories c ON p.CategoryId = c.Id 
                WHERE c.Id IS NULL";
            
            var orphanedProducts = Convert.ToInt32(await validationCmd.ExecuteScalarAsync());
            
            if (orphanedProducts > 0)
            {
                result.Warnings.Add($"{orphanedProducts} products have invalid category references");
            }

            result.FinalProductCount = await GetProductCountAsync(currentConn);
        }

        private ProductMigrationData ExtractProductData(System.Data.Common.DbDataReader reader)
        {
            return new ProductMigrationData
            {
                Id = reader.GetInt32(0),
                Name = reader.GetString(1),
                SKU = reader.IsDBNull(2) ? null : reader.GetString(2),
                Description = reader.IsDBNull(3) ? "" : reader.GetString(3),
                PurchasePrice = reader.IsDBNull(4) ? 0 : Convert.ToDecimal(reader.GetValue(4)),
                SellingPrice = reader.IsDBNull(5) ? 0 : Convert.ToDecimal(reader.GetValue(5)),
                StockQuantity = reader.IsDBNull(6) ? 0 : Convert.ToDecimal(reader.GetValue(6)),
                MinimumStock = reader.IsDBNull(7) ? 0 : reader.GetInt32(7),
                ReorderPoint = reader.IsDBNull(8) ? 0 : reader.GetInt32(8),
                Barcode = reader.IsDBNull(9) ? null : reader.GetString(9),
                CategoryName = reader.IsDBNull(10) ? "General" : reader.GetString(10),
                IsActive = reader.IsDBNull(11) ? true : reader.GetInt32(11) == 1
            };
        }

        private async Task<int> GetOrCreateCategoryAsync(SQLiteConnection connection, string categoryName)
        {
            // First try to find existing category
            var selectCmd = connection.CreateCommand();
            selectCmd.CommandText = "SELECT Id FROM Categories WHERE Name = @Name LIMIT 1";
            selectCmd.Parameters.AddWithValue("@Name", categoryName);

            var result = await selectCmd.ExecuteScalarAsync();
            if (result != null)
            {
                return Convert.ToInt32(result);
            }

            // Create new category if not found
            var insertCmd = connection.CreateCommand();
            insertCmd.CommandText = @"
                INSERT INTO Categories (Name, Description, IsActive)
                VALUES (@Name, @Description, 1);
                SELECT last_insert_rowid();";
            insertCmd.Parameters.AddWithValue("@Name", categoryName);
            insertCmd.Parameters.AddWithValue("@Description", $"Migrated category: {categoryName}");

            return Convert.ToInt32(await insertCmd.ExecuteScalarAsync());
        }

        private async Task<bool> ProductExistsAsync(SQLiteConnection connection, string sku, string name)
        {
            var cmd = connection.CreateCommand();
            cmd.CommandText = @"
                SELECT COUNT(*) FROM Products
                WHERE (SKU = @SKU AND SKU IS NOT NULL AND SKU != '')
                   OR Name = @Name";
            cmd.Parameters.AddWithValue("@SKU", sku ?? "");
            cmd.Parameters.AddWithValue("@Name", name);

            return Convert.ToInt32(await cmd.ExecuteScalarAsync()) > 0;
        }

        private async Task InsertProductAsync(SQLiteConnection connection, ProductMigrationData product, int categoryId)
        {
            var cmd = connection.CreateCommand();
            cmd.CommandText = @"
                INSERT INTO Products (
                    Name, SKU, Description, PurchasePrice, SellingPrice, DefaultPrice,
                    StockQuantity, MinimumStock, ReorderPoint, CategoryId, IsActive,
                    CreatedAt, UpdatedAt, Type, TrackBatches, LoyaltyPoints
                ) VALUES (
                    @Name, @SKU, @Description, @PurchasePrice, @SellingPrice, @SellingPrice,
                    @StockQuantity, @MinimumStock, @ReorderPoint, @CategoryId, 1,
                    datetime('now'), datetime('now'), 0, 0, 0
                )";

            cmd.Parameters.AddWithValue("@Name", product.Name);
            cmd.Parameters.AddWithValue("@SKU", product.SKU ?? "");
            cmd.Parameters.AddWithValue("@Description", product.Description);
            cmd.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
            cmd.Parameters.AddWithValue("@SellingPrice", product.SellingPrice);
            cmd.Parameters.AddWithValue("@StockQuantity", product.StockQuantity);
            cmd.Parameters.AddWithValue("@MinimumStock", product.MinimumStock);
            cmd.Parameters.AddWithValue("@ReorderPoint", product.ReorderPoint);
            cmd.Parameters.AddWithValue("@CategoryId", categoryId);

            await cmd.ExecuteNonQueryAsync();

            // Add barcode if exists
            if (!string.IsNullOrEmpty(product.Barcode))
            {
                var productId = await GetLastInsertRowIdAsync(connection);
                await AddProductBarcodeAsync(connection, productId, product.Barcode);
            }
        }

        private async Task<long> GetLastInsertRowIdAsync(SQLiteConnection connection)
        {
            var cmd = connection.CreateCommand();
            cmd.CommandText = "SELECT last_insert_rowid()";
            return Convert.ToInt64(await cmd.ExecuteScalarAsync());
        }

        private async Task AddProductBarcodeAsync(SQLiteConnection connection, long productId, string barcode)
        {
            var cmd = connection.CreateCommand();
            cmd.CommandText = @"
                INSERT INTO ProductBarcodes (ProductId, Barcode, Description, IsPrimary, CreatedAt)
                VALUES (@ProductId, @Barcode, 'Migrated barcode', 1, datetime('now'))";
            cmd.Parameters.AddWithValue("@ProductId", productId);
            cmd.Parameters.AddWithValue("@Barcode", barcode);

            await cmd.ExecuteNonQueryAsync();
        }

        private async Task<int> GetProductCountAsync(SQLiteConnection connection)
        {
            var cmd = connection.CreateCommand();
            cmd.CommandText = "SELECT COUNT(*) FROM Products WHERE IsActive = 1";
            return Convert.ToInt32(await cmd.ExecuteScalarAsync());
        }
    }
}
