<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="534.797" d:DesignWidth="863.597">

    <!-- Adding DialogHost to handle dialog popups -->
    <md:DialogHost Identifier="RootDialog" SnackbarMessageQueue="{Binding ElementName=MainSnackbar, Path=MessageQueue}">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Statistics Panel -->
            <Border Grid.Row="0" 
                Style="{StaticResource ContentCardStyle}"
                    Margin="0,0,0,10">
                <StackPanel>
                    <TextBlock Text="{DynamicResource CustomerStatistics}"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Total Customers -->
                        <StackPanel Grid.Column="0" Margin="10">
                            <TextBlock Text="{DynamicResource TotalCustomers}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="AccountGroup" 
                                        Width="24" 
                                        Height="24"
                                        Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock x:Name="txtTotalCustomers"
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Active Customers -->
                        <StackPanel Grid.Column="1" Margin="10">
                            <TextBlock Text="{DynamicResource ActiveCustomers}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="AccountCheck" 
                                        Width="24" 
                                        Height="24"
                                        Foreground="#4CAF50"/>
                                <TextBlock x:Name="txtActiveCustomers"
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Total Revenue -->
                        <StackPanel Grid.Column="2" Margin="10">
                            <TextBlock Text="{DynamicResource TotalRevenue}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <md:PackIcon Kind="Cash" 
                                        Width="24" 
                                        Height="24"
                                        Foreground="#2196F3"/>
                                <TextBlock x:Name="txtTotalRevenue"
                                     Text="0.00 DA"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     Margin="8,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Search and Filters -->
            <Border Grid.Row="1" 
                Style="{StaticResource ContentCardStyle}"
                    Margin="0,0,0,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                        x:Name="txtSearch"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        md:HintAssist.Hint="{DynamicResource SearchCustomersHint}"
                        KeyDown="SearchBox_KeyDown"
                        TextChanged="SearchBox_TextChanged"
                        Margin="0,0,10,0"/>

                    <ComboBox Grid.Column="1"
                         x:Name="StatusFilter"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Width="150"
                         md:HintAssist.Hint="{DynamicResource Status}"
                             SelectionChanged="StatusFilter_SelectionChanged"
                             Margin="0,0,10,0">
                        <ComboBoxItem Content="{DynamicResource AllCustomers}"/>
                        <ComboBoxItem Content="{DynamicResource ActiveOnly}"/>
                        <ComboBoxItem Content="{DynamicResource Inactive}"/>
                    </ComboBox>

                    <!-- Add New Customer Button -->
                    <Button Grid.Column="2"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            Foreground="{DynamicResource MaterialDesignPaper}"
                            md:ButtonAssist.CornerRadius="4"
                            Click="AddNewCustomer_Click">
                        <StackPanel Orientation="Horizontal">
                            <md:PackIcon Kind="AccountPlus" 
                                        Width="24" 
                                        Height="24"
                                        VerticalAlignment="Center"
                                        Margin="0,0,8,0"/>
                            <TextBlock Text="{DynamicResource AddNew}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </Border>

            <!-- Customers DataGrid -->
            <Border Grid.Row="2" 
                    Style="{StaticResource ContentCardStyle}">
                <DataGrid ItemsSource="{Binding Customers}"
                     Style="{StaticResource AppDataGridStyle}"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     SelectionMode="Single"
                     IsReadOnly="True">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="{DynamicResource ID}" Binding="{Binding Id}" Width="50"/>
                        <DataGridTextColumn Header="{DynamicResource FirstName}" Binding="{Binding FirstName}" Width="*"/>
                        <DataGridTextColumn Header="{DynamicResource LastName}" Binding="{Binding LastName}" Width="*"/>
                        <DataGridTextColumn Header="{DynamicResource Email}" Binding="{Binding Email}" Width="*"/>
                        <DataGridTextColumn Header="{DynamicResource Phone}" Binding="{Binding Phone}" Width="100"/>
                        <DataGridTemplateColumn Header="{DynamicResource LoyaltyPoints}" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding LoyaltyPoints, StringFormat=N0}"
                                         HorizontalAlignment="Right"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Header="{DynamicResource TotalSpent}" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding TotalSpent, StringFormat={}{0:N2} DA}"
                                         HorizontalAlignment="Right"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Header="{DynamicResource Status}" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding IsActive, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='Active|Inactive'}"
                                         Foreground="{Binding IsActive, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='#4CAF50|#9E9E9E'}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Action Buttons -->
                        <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="{DynamicResource Edit}"
                                            Click="EditCustomer_Click"
                                            Style="{StaticResource AppSecondaryButtonStyle}"
                                            Height="35"
                                            Width="80"
                                            Margin="0,0,4,0"/>
                                        <Button Content="{DynamicResource Delete}"
                                            Click="DeleteCustomer_Click"
                                            Style="{StaticResource AppDeleteButtonStyle}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- Snackbar for notifications -->
            <md:Snackbar x:Name="MainSnackbar" 
                        Grid.Row="0" 
                Grid.RowSpan="3" 
                        HorizontalAlignment="Stretch" 
                        VerticalAlignment="Bottom"/>
        </Grid>
    </md:DialogHost>
</UserControl> 