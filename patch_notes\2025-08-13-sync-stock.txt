Context:
- We introduced batch-tracking sync helper into DatabaseService to keep Products.StockQuantity aligned with the sum of BatchStock quantities.

Key changes:
1) Added private helper: SyncProductStockFromBatches(SqliteConnection connection, int productId, SqliteTransaction transaction = null)
   - Executes UPDATE Products SET StockQuantity = COALESCE(SUM(BatchStock.Quantity), 0)
   - Accepts optional transaction
2) Added public convenience method SyncProductStockFromBatches(int productId)
   - Opens a connection and calls the private helper
3) Calls to sync added after batch mutations:
   - AddBatchStock (after insert)
   - AddStockToBatch (after update)
   - UpdateBatch (after update)
   - UpdateProductStock for batch-tracked products after deductions

Notes:
- Adjusted GetBatchesForProduct to read Quantity as decimal
- Fixed accidental insertion inside GetBatchesForProduct earlier
- Build was run and errors were fixed iteratively; run build again to verify.

