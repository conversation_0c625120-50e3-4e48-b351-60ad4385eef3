using System.Windows.Media;

namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for theme management services
    /// </summary>
    public interface IThemeService
    {
        /// <summary>
        /// Applies a custom theme to the application
        /// </summary>
        /// <param name="isDarkTheme">Whether to use dark theme</param>
        /// <param name="primaryColor">Primary theme color</param>
        void ApplyCustomTheme(bool isDarkTheme, Color primaryColor);

        /// <summary>
        /// Gets the current theme color
        /// </summary>
        /// <returns>Current primary color</returns>
        Color GetCurrentThemeColor();

        /// <summary>
        /// Gets whether dark theme is currently active
        /// </summary>
        /// <returns>True if dark theme is active, false otherwise</returns>
        bool IsDarkTheme();

        /// <summary>
        /// Applies a predefined theme by name
        /// </summary>
        /// <param name="themeName">Name of the theme to apply</param>
        void ApplyTheme(string themeName);

        /// <summary>
        /// Gets available theme names
        /// </summary>
        /// <returns>Array of available theme names</returns>
        string[] GetAvailableThemes();
    }
}
