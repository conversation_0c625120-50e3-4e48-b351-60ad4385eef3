using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.IO;
using System.Printing;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Interfaces;

namespace POSSystem.Views.Dialogs
{
    public partial class LoyaltyCardView : UserControl
    {
        private Customer _customer;
        private bool _showingFrontSide = true;
        private readonly ISettingsService _settingsService;
        private BitmapImage _companyLogo;
        
        public LoyaltyCardView(Customer customer)
        {
            InitializeComponent();
            
            _settingsService = new SettingsService();
            _customer = customer;

            // Load company logo
            LoadCompanyLogo();
            
            if (_customer != null)
            {
                LoadCustomerData();
            }
            
            // Update card with logo
            UpdateCardWithLogo();
        }
        
        private void LoadCustomerData()
        {
            // Set customer information
            txtCustomerName.Text = _customer.FullName;
            
            // Format loyalty code with LC- prefix
            string formattedCode = FormatLoyaltyCode(_customer.LoyaltyCode);
            txtLoyaltyCode.Text = $"Loyalty Code: {formattedCode}";
            
            // Set card front data
            cardFrontName.Text = _customer.FullName;
            cardFrontCode.Text = formattedCode;
            
            // Set card back data
            cardBackCode.Text = formattedCode;
            cardBarcodeCode.Text = formattedCode;
        }
        
        /// <summary>
        /// Formats a loyalty code with LC- prefix
        /// </summary>
        private string FormatLoyaltyCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return "LC-000000";
                
            // If code already has the prefix, return it as is
            if (code.StartsWith("LC-"))
                return code;
                
            // Otherwise add the prefix
            return $"LC-{code}";
        }
        
        private void BtnShowFront_Click(object sender, RoutedEventArgs e)
        {
            ShowFrontSide();
        }
        
        private void BtnShowBack_Click(object sender, RoutedEventArgs e)
        {
            ShowBackSide();
        }
        
        private void ShowFrontSide()
        {
            _showingFrontSide = true;
            
            // Update UI to show front side
            frontCardPreview.Visibility = Visibility.Visible;
            backCardPreview.Visibility = Visibility.Collapsed;
            
            // Update button styles
            btnShowFront.Style = (Style)FindResource("AppPrimaryButtonStyle");
            btnShowBack.Style = (Style)FindResource("AppSecondaryButtonStyle");
        }
        
        private void ShowBackSide()
        {
            _showingFrontSide = false;
            
            // Update UI to show back side
            frontCardPreview.Visibility = Visibility.Collapsed;
            backCardPreview.Visibility = Visibility.Visible;
            
            // Update button styles
            btnShowFront.Style = (Style)FindResource("AppSecondaryButtonStyle");
            btnShowBack.Style = (Style)FindResource("AppPrimaryButtonStyle");
        }
        
        private void BtnPrintCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Determine which side to print
                UIElement elementToPrint = _showingFrontSide ? frontCardPreview : backCardPreview;
                
                // Print the current side
                PrintCardSide(elementToPrint);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error printing loyalty card: {ex.Message}", 
                    "Print Error", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            }
        }
        
        private void PrintCardSide(UIElement cardSide)
        {
            // Create PrintDialog
            PrintDialog printDialog = new PrintDialog();
            
            if (printDialog.ShowDialog() == true)
            {
                // Standard credit card dimensions: ~85.6mm x 53.98mm (ratio 1.58:1)
                // These dimensions ensure the card is printed at a standard size
                double cardWidth = 85.6;  // in mm
                double cardHeight = 54.0; // in mm
                
                // Convert mm to device-independent pixels (1 inch = 25.4mm, 1 inch = 96 DPI)
                double cardWidthInPixels = cardWidth / 25.4 * 96;
                double cardHeightInPixels = cardHeight / 25.4 * 96;
                
                // Create a visual brush from the card element
                VisualBrush cardBrush = new VisualBrush(cardSide);
                
                // Create a drawing visual to hold the card
                DrawingVisual drawingVisual = new DrawingVisual();
                using (DrawingContext drawingContext = drawingVisual.RenderOpen())
                {
                    // Calculate positioning to center on page
                    double xPosition = (printDialog.PrintableAreaWidth - cardWidthInPixels) / 2;
                    double yPosition = (printDialog.PrintableAreaHeight - cardHeightInPixels) / 2;
                    
                    // Create a rectangle for the card with standard card dimensions
                    Rect rect = new Rect(new Point(xPosition, yPosition), 
                                         new Size(cardWidthInPixels, cardHeightInPixels));
                    
                    // Draw the card
                    drawingContext.DrawRectangle(cardBrush, null, rect);
                }
                
                // Print the card
                printDialog.PrintVisual(drawingVisual, "Loyalty Card");
                
                // Show confirmation
                MessageBox.Show(
                    _showingFrontSide ? "Card front side printed successfully!" : "Card back side printed successfully!",
                    "Print Complete",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }
        
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog
            DialogHost.Close("RootDialog");
        }

        private void LoadCompanyLogo()
        {
            try
            {
                // Get the company logo from settings service
                string logoBase64 = _settingsService.GetCompanyLogo();
                if (!string.IsNullOrEmpty(logoBase64))
                {
                    // Convert Base64 string to BitmapImage
                    byte[] imageData = Convert.FromBase64String(logoBase64);
                    var imageSource = new BitmapImage();

                    using (var ms = new MemoryStream(imageData))
                    {
                        imageSource.BeginInit();
                        imageSource.StreamSource = ms;
                        imageSource.CacheOption = BitmapCacheOption.OnLoad;
                        imageSource.EndInit();
                    }

                    _companyLogo = imageSource;
                }
            }
            catch (Exception)
            {
                // If there's any error loading the logo, set to null
                _companyLogo = null;
            }
        }

        private void UpdateCardWithLogo()
        {
            // Update front card logo
            if (_companyLogo != null && FrontLogoContainer != null)
            {
                FrontLogoContainer.Children.Clear();
                var frontImage = new Image
                {
                    Source = _companyLogo,
                    Width = 80,
                    Height = 80,
                    Stretch = Stretch.Uniform
                };
                FrontLogoContainer.Children.Add(frontImage);
            }
            
            // Update back card logo
            if (_companyLogo != null && BackLogoContainer != null)
            {
                BackLogoContainer.Children.Clear();
                var backImage = new Image
                {
                    Source = _companyLogo,
                    Width = 60,
                    Height = 40,
                    Stretch = Stretch.Uniform,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 4)
                };
                BackLogoContainer.Children.Add(backImage);
                
                // Keep the store name
                var storeName = new TextBlock
                {
                    Text = "YOUR STORE", 
                    FontSize = 8, 
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E1D0B3")),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Center
                };
                BackLogoContainer.Children.Add(storeName);
            }
        }
    }
} 