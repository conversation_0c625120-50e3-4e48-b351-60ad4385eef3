using System;
using System.Collections.Generic;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// Comprehensive performance analysis report for SaleViewGrid
    /// </summary>
    public class PerformanceAnalysisReport
    {
        public DateTime TestStartTime { get; set; }
        public DateTime TestEndTime { get; set; }
        public TimeSpan TotalTestDuration { get; set; }
        public string GridViewType { get; set; }
        
        public BaselineMetrics BaselineMetrics { get; set; }
        public List<LoadTimeTest> LoadTimeTests { get; set; } = new List<LoadTimeTest>();
        public List<GridLayoutTest> GridLayoutTests { get; set; } = new List<GridLayoutTest>();
        public MemoryUsageTest MemoryUsageTest { get; set; }
        public UIResponsivenessTest UIResponsivenessTest { get; set; }
        public VirtualizationTest VirtualizationTest { get; set; }
        public MemoryLeakTest MemoryLeakTest { get; set; }
        
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// Generate overall performance score (0-100)
        /// </summary>
        public double CalculateOverallScore()
        {
            double score = 100.0;
            
            // Deduct points for performance issues
            if (LoadTimeTests.Count > 0)
            {
                var avgLoadTime = LoadTimeTests.Average(t => t.TotalLoadTime);
                if (avgLoadTime > 1000) score -= 20; // Slow loading
                else if (avgLoadTime > 500) score -= 10;
            }

            if (MemoryUsageTest != null && MemoryUsageTest.MemoryLeakIndicator > 5)
                score -= 15; // Memory leak concerns

            if (UIResponsivenessTest != null && UIResponsivenessTest.ResizeResponseTime > 200)
                score -= 10; // Slow UI response

            if (VirtualizationTest != null && !VirtualizationTest.IsVirtualizationEffective)
                score -= 25; // Virtualization not working

            if (GridLayoutTests.Any(t => !t.IsOptimal))
                score -= 10; // Layout optimization issues

            return Math.Max(0, score);
        }

        /// <summary>
        /// Generate performance grade (A-F)
        /// </summary>
        public string GetPerformanceGrade()
        {
            var score = CalculateOverallScore();
            return score >= 90 ? "A" :
                   score >= 80 ? "B" :
                   score >= 70 ? "C" :
                   score >= 60 ? "D" : "F";
        }
    }

    /// <summary>
    /// Baseline performance metrics
    /// </summary>
    public class BaselineMetrics
    {
        public double InitialMemoryUsage { get; set; } // MB
        public int InitialProductCount { get; set; }
        public int CurrentColumns { get; set; }
        public double GridWidth { get; set; }
        public double GridHeight { get; set; }
        public long UIThreadResponseTime { get; set; } // ms
    }

    /// <summary>
    /// Load time performance test results
    /// </summary>
    public class LoadTimeTest
    {
        public int ProductCount { get; set; }
        public long TotalLoadTime { get; set; } // ms
        public long CollectionUpdateTime { get; set; } // ms
        public double MemoryBefore { get; set; } // MB
        public double MemoryAfter { get; set; } // MB
        public double MemoryIncrease { get; set; } // MB
        
        public bool IsPerformant => TotalLoadTime < 500 && MemoryIncrease < 50;
        public string PerformanceRating => TotalLoadTime < 200 ? "Excellent" :
                                         TotalLoadTime < 500 ? "Good" :
                                         TotalLoadTime < 1000 ? "Fair" : "Poor";
    }

    /// <summary>
    /// Grid layout optimization test results
    /// </summary>
    public class GridLayoutTest
    {
        public string Scenario { get; set; }
        public int ProductCount { get; set; }
        public int ExpectedColumns { get; set; }
        public int ActualColumns { get; set; }
        public double GridWidth { get; set; }
        public double SpacingRatio { get; set; }
        public long CalculationTime { get; set; } // ms
        public bool IsOptimal { get; set; }
        
        public string SpacingQuality => SpacingRatio <= 1.2 ? "Excellent" :
                                       SpacingRatio <= 1.5 ? "Good" :
                                       SpacingRatio <= 2.0 ? "Fair" : "Poor";
    }

    /// <summary>
    /// Memory usage analysis results
    /// </summary>
    public class MemoryUsageTest
    {
        public double BaselineMemory { get; set; } // MB
        public double PeakMemory { get; set; } // MB
        public double PostCleanupMemory { get; set; } // MB
        public double MemoryLeakIndicator { get; set; } // MB
        
        public bool HasMemoryLeak => MemoryLeakIndicator > 5;
        public string MemoryEfficiency => MemoryLeakIndicator < 2 ? "Excellent" :
                                         MemoryLeakIndicator < 5 ? "Good" :
                                         MemoryLeakIndicator < 10 ? "Fair" : "Poor";
    }

    /// <summary>
    /// UI responsiveness test results
    /// </summary>
    public class UIResponsivenessTest
    {
        public long ResizeResponseTime { get; set; } // ms
        public long ScrollPerformance { get; set; } // ms
        public long GridUpdateTime { get; set; } // ms
        
        public bool IsResponsive => ResizeResponseTime < 100 && ScrollPerformance < 50;
        public string ResponsivenessRating => (ResizeResponseTime < 100 && ScrollPerformance < 50) ? "Excellent" :
                                             (ResizeResponseTime < 200 && ScrollPerformance < 100) ? "Good" :
                                             (ResizeResponseTime < 500 && ScrollPerformance < 200) ? "Fair" : "Poor";
    }

    /// <summary>
    /// Virtualization effectiveness test results
    /// </summary>
    public class VirtualizationTest
    {
        public int TotalItems { get; set; }
        public int RealizedContainers { get; set; }
        public double VirtualizationRatio { get; set; }
        public bool IsVirtualizationEffective { get; set; }
        
        public string VirtualizationEfficiency => VirtualizationRatio < 0.05 ? "Excellent" :
                                                 VirtualizationRatio < 0.1 ? "Good" :
                                                 VirtualizationRatio < 0.2 ? "Fair" : "Poor";
    }

    /// <summary>
    /// Memory leak detection test results
    /// </summary>
    public class MemoryLeakTest
    {
        public double InitialMemory { get; set; } // MB
        public double FinalMemory { get; set; } // MB
        public double MemoryGrowth { get; set; } // MB
        public bool HasPotentialLeak { get; set; }
        
        public string LeakRisk => MemoryGrowth < 2 ? "None" :
                                 MemoryGrowth < 5 ? "Low" :
                                 MemoryGrowth < 10 ? "Medium" : "High";
    }

    /// <summary>
    /// Individual performance metric
    /// </summary>
    public class PerformanceMetric
    {
        public string Name { get; set; }
        public double Value { get; set; }
        public string Unit { get; set; }
        public DateTime Timestamp { get; set; }
        public string Category { get; set; }
    }

    /// <summary>
    /// Performance comparison data
    /// </summary>
    public class PerformanceComparison
    {
        public string MetricName { get; set; }
        public double PreviousValue { get; set; }
        public double CurrentValue { get; set; }
        public double PercentageChange { get; set; }
        public bool IsImprovement { get; set; }
        public string ChangeDescription { get; set; }
    }
}
