using Microsoft.VisualStudio.TestTools.UnitTesting;
using POSSystem.Models;
using System;
using System.Collections.Generic;

namespace POSSystem.Tests
{
    [TestClass]
    public class BulkPricingStackOverflowTest
    {
        [TestMethod]
        public void CartItem_ApplyBulkPricing_DoesNotCauseStackOverflow()
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 1.00m,
                PriceTiers = new List<ProductPriceTier>
                {
                    new ProductPriceTier
                    {
                        Id = 1,
                        ProductId = 1,
                        MinimumQuantity = 5m,
                        UnitPrice = 0.80m,
                        TierName = "5-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }
            };

            // Set up the navigation property
            foreach (var tier in product.PriceTiers)
            {
                tier.Product = product;
            }

            var cartItem = new CartItem
            {
                Product = product,
                Quantity = 10m,
                UnitPrice = product.SellingPrice
            };

            // Act & Assert - This should not cause a StackOverflowException
            try
            {
                cartItem.ApplyBulkPricing();
                
                // Verify that bulk pricing was applied correctly
                Assert.IsTrue(cartItem.HasBulkPricing);
                Assert.IsNotNull(cartItem.AppliedPriceTier);
                Assert.AreEqual(0.80m, cartItem.UnitPrice);
                Assert.IsTrue(cartItem.BulkSavings > 0);
            }
            catch (StackOverflowException)
            {
                Assert.Fail("StackOverflowException occurred - circular dependency not resolved");
            }
        }

        [TestMethod]
        public void Cart_AddItemWithBulkPricing_DoesNotCauseStackOverflow()
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 1.00m,
                PriceTiers = new List<ProductPriceTier>
                {
                    new ProductPriceTier
                    {
                        Id = 1,
                        ProductId = 1,
                        MinimumQuantity = 5m,
                        UnitPrice = 0.80m,
                        TierName = "5-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }
            };

            // Set up the navigation property
            foreach (var tier in product.PriceTiers)
            {
                tier.Product = product;
            }

            var cart = new Cart();

            // Act & Assert - This should not cause a StackOverflowException
            try
            {
                cart.AddItem(product, 10m);
                
                // Verify that the item was added with bulk pricing
                Assert.AreEqual(1, cart.Items.Count);
                Assert.IsTrue(cart.HasBulkPricing);
                Assert.IsTrue(cart.TotalBulkSavings > 0);
            }
            catch (StackOverflowException)
            {
                Assert.Fail("StackOverflowException occurred - circular dependency not resolved");
            }
        }

        [TestMethod]
        public void CartItem_QuantityChange_DoesNotCauseStackOverflow()
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 1.00m,
                PriceTiers = new List<ProductPriceTier>
                {
                    new ProductPriceTier
                    {
                        Id = 1,
                        ProductId = 1,
                        MinimumQuantity = 5m,
                        UnitPrice = 0.80m,
                        TierName = "5-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }
            };

            // Set up the navigation property
            foreach (var tier in product.PriceTiers)
            {
                tier.Product = product;
            }

            var cartItem = new CartItem
            {
                Product = product,
                Quantity = 3m,
                UnitPrice = product.SellingPrice
            };

            // Act & Assert - This should not cause a StackOverflowException
            try
            {
                // Change quantity multiple times to test for circular dependencies
                cartItem.Quantity = 5m;  // Should trigger bulk pricing
                cartItem.Quantity = 10m; // Should maintain bulk pricing
                cartItem.Quantity = 2m;  // Should remove bulk pricing
                cartItem.Quantity = 8m;  // Should re-apply bulk pricing
                
                // Verify final state
                Assert.IsTrue(cartItem.HasBulkPricing);
                Assert.AreEqual(0.80m, cartItem.UnitPrice);
            }
            catch (StackOverflowException)
            {
                Assert.Fail("StackOverflowException occurred during quantity changes - circular dependency not resolved");
            }
        }

        [TestMethod]
        public void CartItem_RefreshBulkPricing_DoesNotCauseStackOverflow()
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 1.00m,
                PriceTiers = new List<ProductPriceTier>
                {
                    new ProductPriceTier
                    {
                        Id = 1,
                        ProductId = 1,
                        MinimumQuantity = 5m,
                        UnitPrice = 0.80m,
                        TierName = "5-Pack",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }
            };

            // Set up the navigation property
            foreach (var tier in product.PriceTiers)
            {
                tier.Product = product;
            }

            var cartItem = new CartItem
            {
                Product = product,
                Quantity = 10m,
                UnitPrice = product.SellingPrice
            };

            // Act & Assert - This should not cause a StackOverflowException
            try
            {
                // Call RefreshBulkPricing multiple times
                cartItem.RefreshBulkPricing();
                cartItem.RefreshBulkPricing();
                cartItem.RefreshBulkPricing();
                
                // Verify that bulk pricing is still correctly applied
                Assert.IsTrue(cartItem.HasBulkPricing);
                Assert.AreEqual(0.80m, cartItem.UnitPrice);
            }
            catch (StackOverflowException)
            {
                Assert.Fail("StackOverflowException occurred during RefreshBulkPricing - circular dependency not resolved");
            }
        }
    }
}
