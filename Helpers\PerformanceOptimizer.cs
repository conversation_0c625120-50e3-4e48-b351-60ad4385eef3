using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using System.Windows.Interop;

namespace POSSystem.Helpers
{
    /// <summary>
    /// Static class to apply performance optimizations throughout the application
    /// </summary>
    public static class PerformanceOptimizer
    {
        /// <summary>
        /// Apply all performance optimizations to the application
        /// </summary>
        public static void OptimizeApplication()
        {
            // Configure rendering settings
            if (System.Windows.Media.RenderOptions.ProcessRenderMode != null)
            {
                System.Windows.Media.RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.Default;
            }

            // Set up global event handlers
            EventManager.RegisterClassHandler(typeof(DataGrid), DataGrid.LoadedEvent, 
                new RoutedEventHandler(OnDataGridLoaded));
                
            EventManager.RegisterClassHandler(typeof(ListBox), ListBox.LoadedEvent,
                new RoutedEventHandler(OnListBoxLoaded));
                
            EventManager.RegisterClassHandler(typeof(TextBox), TextBox.TextChangedEvent,
                new TextChangedEventHandler(OnTextBoxTextChanged), true);
                
            // Enable WPF performance tweaks
            Application.Current.Dispatcher.Thread.Priority = System.Threading.ThreadPriority.AboveNormal;
        }
        
        /// <summary>
        /// Apply optimization when a DataGrid is loaded
        /// </summary>
        private static void OnDataGridLoaded(object sender, RoutedEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                try
                {
                    DataGridOptimizationHelper.OptimizeDataGrid(dataGrid);
                    
                    // Delay the binding optimization to ensure all columns are loaded
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() => {
                        try
                        {
                            DataGridOptimizationHelper.OptimizeProductColumnBindings(dataGrid);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but don't crash the application
                            System.Diagnostics.Debug.WriteLine($"Error during DataGrid binding optimization: {ex.Message}");
                        }
                    }));
                }
                catch (Exception ex)
                {
                    // Log any optimization errors but don't crash the application
                    System.Diagnostics.Debug.WriteLine($"Error during DataGrid optimization: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// Apply optimization when a ListBox is loaded
        /// </summary>
        private static void OnListBoxLoaded(object sender, RoutedEventArgs e)
        {
            if (sender is ListBox listBox)
            {
                // Enable virtualization
                VirtualizingPanel.SetIsVirtualizing(listBox, true);
                VirtualizingPanel.SetVirtualizationMode(listBox, VirtualizationMode.Recycling);
                VirtualizingPanel.SetCacheLengthUnit(listBox, VirtualizationCacheLengthUnit.Page);
                VirtualizingPanel.SetCacheLength(listBox, new VirtualizationCacheLength(1, 2));
                
                // Apply bitmap caching
                listBox.CacheMode = new BitmapCache { EnableClearType = true, SnapsToDevicePixels = true };
                RenderOptions.SetBitmapScalingMode(listBox, BitmapScalingMode.HighQuality);
                RenderOptions.SetClearTypeHint(listBox, ClearTypeHint.Enabled);
            }
        }
        
        /// <summary>
        /// Apply debouncing for search TextBoxes
        /// </summary>
        private static void OnTextBoxTextChanged(object sender, TextChangedEventArgs e)
        {
            // Only apply to search boxes
            if (sender is TextBox textBox && 
                (textBox.Name?.Contains("search", StringComparison.OrdinalIgnoreCase) == true ||
                 textBox.Name?.Contains("filter", StringComparison.OrdinalIgnoreCase) == true ||
                 textBox.Name == "txtSearch"))
            {
                // Check if we already attached our handler
                if (!IsDebounceHandlerAttached(textBox))
                {
                    textBox.SetValue(DebounceHandlerAttachedProperty, true);
                    
                    var viewModel = textBox.DataContext;
                    var searchProperty = viewModel?.GetType().GetProperty("SearchText");
                    
                    if (searchProperty != null)
                    {
                        // Create a debounced action for the search text
                        SearchHelper.AttachDebouncedSearch(textBox, newText => {
                            searchProperty.SetValue(viewModel, newText);
                        });
                    }
                }
            }
        }
        
        // Attached property to track whether we've already attached a debounce handler
        private static readonly DependencyProperty DebounceHandlerAttachedProperty = 
            DependencyProperty.RegisterAttached("DebounceHandlerAttached", typeof(bool), 
                typeof(PerformanceOptimizer), new PropertyMetadata(false));
                
        private static bool IsDebounceHandlerAttached(TextBox textBox)
        {
            return (bool)textBox.GetValue(DebounceHandlerAttachedProperty);
        }
        
        /// <summary>
        /// Apply optimizations to a specific element and its visual tree
        /// </summary>
        public static void OptimizeElement(FrameworkElement element)
        {
            if (element == null) return;
            
            // Apply high-performance rendering settings
            PerformanceHelper.OptimizeRendering(element);
            
            // Optimize controls recursively
            OptimizeControls(element);
        }
        
        /// <summary>
        /// Recursively optimize controls in the visual tree
        /// </summary>
        private static void OptimizeControls(DependencyObject element)
        {
            if (element == null) return;
            
            // Apply specific optimizations based on control type
            if (element is DataGrid dataGrid)
            {
                DataGridOptimizationHelper.OptimizeDataGrid(dataGrid);
            }
            else if (element is ListBox listBox)
            {
                PerformanceHelper.EnableVirtualization(listBox);
            }
            else if (element is ItemsControl itemsControl && !(itemsControl is ComboBox))
            {
                PerformanceHelper.EnableVirtualization(itemsControl);
            }
            
            // Optimize children
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                OptimizeControls(VisualTreeHelper.GetChild(element, i));
            }
        }
    }
} 