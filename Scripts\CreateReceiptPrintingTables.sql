-- Create Receipt Printing Tables
-- This script creates the receipt printing tables manually to avoid migration conflicts

-- Create ReceiptTemplates table
CREATE TABLE IF NOT EXISTS "ReceiptTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ReceiptTemplates" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL CONSTRAINT "CK_ReceiptTemplates_Name_MaxLength" CHECK (length("Name") <= 100),
    "Description" TEXT CONSTRAINT "CK_ReceiptTemplates_Description_MaxLength" CHECK (length("Description") <= 500),
    "TemplateType" TEXT NOT NULL CONSTRAINT "CK_ReceiptTemplates_TemplateType_MaxLength" CHECK (length("TemplateType") <= 50),
    "PaperWidth" INTEGER NOT NULL,
    "FontSize" INTEGER NOT NULL,
    "IncludeLogo" INTEGER NOT NULL,
    "IncludeCompanyInfo" INTEGER NOT NULL,
    "IncludeCustomerInfo" INTEGER NOT NULL,
    "IncludeItemDetails" INTEGER NOT NULL,
    "IncludePaymentInfo" INTEGER NOT NULL,
    "IncludeBarcode" INTEGER NOT NULL,
    "FooterText" TEXT CONSTRAINT "CK_ReceiptTemplates_FooterText_MaxLength" CHECK (length("FooterText") <= 500),
    "IsDefault" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ModifiedDate" TEXT NOT NULL,
    "AdvancedSettings" TEXT
);

-- Create PrinterConfigurations table
CREATE TABLE IF NOT EXISTS "PrinterConfigurations" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PrinterConfigurations" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL CONSTRAINT "CK_PrinterConfigurations_Name_MaxLength" CHECK (length("Name") <= 100),
    "PrinterType" TEXT NOT NULL CONSTRAINT "CK_PrinterConfigurations_PrinterType_MaxLength" CHECK (length("PrinterType") <= 50),
    "PrinterName" TEXT CONSTRAINT "CK_PrinterConfigurations_PrinterName_MaxLength" CHECK (length("PrinterName") <= 200),
    "PaperSize" TEXT CONSTRAINT "CK_PrinterConfigurations_PaperSize_MaxLength" CHECK (length("PaperSize") <= 50),
    "PrintQuality" TEXT CONSTRAINT "CK_PrinterConfigurations_PrintQuality_MaxLength" CHECK (length("PrintQuality") <= 50),
    "Copies" INTEGER NOT NULL,
    "IsDefault" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "ConnectionSettings" TEXT CONSTRAINT "CK_PrinterConfigurations_ConnectionSettings_MaxLength" CHECK (length("ConnectionSettings") <= 1000),
    "ReceiptTemplateId" INTEGER,
    CONSTRAINT "FK_PrinterConfigurations_ReceiptTemplates_ReceiptTemplateId" FOREIGN KEY ("ReceiptTemplateId") REFERENCES "ReceiptTemplates" ("Id") ON DELETE SET NULL
);

-- Create ReceiptPrintSettings table
CREATE TABLE IF NOT EXISTS "ReceiptPrintSettings" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ReceiptPrintSettings" PRIMARY KEY AUTOINCREMENT,
    "AutoPrintEnabled" INTEGER NOT NULL,
    "ShowPrintDialog" INTEGER NOT NULL,
    "SaveAsPdfBackup" INTEGER NOT NULL,
    "PdfBackupPath" TEXT CONSTRAINT "CK_ReceiptPrintSettings_PdfBackupPath_MaxLength" CHECK (length("PdfBackupPath") <= 500),
    "DefaultPrinterConfigId" INTEGER,
    "DefaultReceiptTemplateId" INTEGER,
    "EnablePrintPreview" INTEGER NOT NULL,
    "PrintTimeoutSeconds" INTEGER NOT NULL,
    "RetryFailedPrints" INTEGER NOT NULL,
    "MaxRetryAttempts" INTEGER NOT NULL,
    CONSTRAINT "FK_ReceiptPrintSettings_PrinterConfigurations_DefaultPrinterConfigId" FOREIGN KEY ("DefaultPrinterConfigId") REFERENCES "PrinterConfigurations" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_ReceiptPrintSettings_ReceiptTemplates_DefaultReceiptTemplateId" FOREIGN KEY ("DefaultReceiptTemplateId") REFERENCES "ReceiptTemplates" ("Id") ON DELETE SET NULL
);

-- Create ReceiptPrintJobs table
CREATE TABLE IF NOT EXISTS "ReceiptPrintJobs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ReceiptPrintJobs" PRIMARY KEY AUTOINCREMENT,
    "SaleId" INTEGER NOT NULL,
    "Status" TEXT NOT NULL CONSTRAINT "CK_ReceiptPrintJobs_Status_MaxLength" CHECK (length("Status") <= 50),
    "PrinterConfigId" INTEGER NOT NULL,
    "ReceiptTemplateId" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "StartedAt" TEXT,
    "CompletedAt" TEXT,
    "ErrorMessage" TEXT CONSTRAINT "CK_ReceiptPrintJobs_ErrorMessage_MaxLength" CHECK (length("ErrorMessage") <= 1000),
    "RetryCount" INTEGER NOT NULL,
    "UserId" INTEGER NOT NULL,
    CONSTRAINT "FK_ReceiptPrintJobs_PrinterConfigurations_PrinterConfigId" FOREIGN KEY ("PrinterConfigId") REFERENCES "PrinterConfigurations" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_ReceiptPrintJobs_ReceiptTemplates_ReceiptTemplateId" FOREIGN KEY ("ReceiptTemplateId") REFERENCES "ReceiptTemplates" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_ReceiptPrintJobs_Sales_SaleId" FOREIGN KEY ("SaleId") REFERENCES "Sales" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ReceiptPrintJobs_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE RESTRICT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "IX_PrinterConfigurations_ReceiptTemplateId" ON "PrinterConfigurations" ("ReceiptTemplateId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintJobs_PrinterConfigId" ON "ReceiptPrintJobs" ("PrinterConfigId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintJobs_ReceiptTemplateId" ON "ReceiptPrintJobs" ("ReceiptTemplateId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintJobs_SaleId" ON "ReceiptPrintJobs" ("SaleId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintJobs_UserId" ON "ReceiptPrintJobs" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintSettings_DefaultPrinterConfigId" ON "ReceiptPrintSettings" ("DefaultPrinterConfigId");
CREATE INDEX IF NOT EXISTS "IX_ReceiptPrintSettings_DefaultReceiptTemplateId" ON "ReceiptPrintSettings" ("DefaultReceiptTemplateId");

-- Insert default receipt template
INSERT OR IGNORE INTO "ReceiptTemplates" (
    "Id", "Name", "Description", "TemplateType", "PaperWidth", "FontSize", 
    "IncludeLogo", "IncludeCompanyInfo", "IncludeCustomerInfo", "IncludeItemDetails", 
    "IncludePaymentInfo", "IncludeBarcode", "FooterText", "IsDefault", "IsActive", 
    "CreatedDate", "ModifiedDate"
) VALUES (
    1, 'Default Receipt Template', 'Standard receipt template with all basic information', 
    'Standard', 48, 12, 1, 1, 1, 1, 1, 0, 'Thank you for your business!', 
    1, 1, datetime('now'), datetime('now')
);

-- Insert default printer configuration
INSERT OR IGNORE INTO "PrinterConfigurations" (
    "Id", "Name", "PrinterType", "PaperSize", "PrintQuality", "Copies", 
    "IsDefault", "IsActive", "ReceiptTemplateId"
) VALUES (
    1, 'Default Printer', 'Standard', 'A4', 'Normal', 1, 1, 1, 1
);

-- Insert default print settings
INSERT OR IGNORE INTO "ReceiptPrintSettings" (
    "Id", "AutoPrintEnabled", "ShowPrintDialog", "SaveAsPdfBackup", 
    "PdfBackupPath", "DefaultPrinterConfigId", "DefaultReceiptTemplateId", 
    "EnablePrintPreview", "PrintTimeoutSeconds", "RetryFailedPrints", "MaxRetryAttempts"
) VALUES (
    1, 1, 0, 0, NULL, 1, 1, 1, 30, 1, 3
);

-- Mark the migration as applied (add to migrations history)
INSERT OR IGNORE INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250710203902_AddReceiptPrintingTables', '8.0.11');

-- Verify tables were created
SELECT 'ReceiptTemplates created: ' || COUNT(*) FROM sqlite_master WHERE type='table' AND name='ReceiptTemplates';
SELECT 'PrinterConfigurations created: ' || COUNT(*) FROM sqlite_master WHERE type='table' AND name='PrinterConfigurations';
SELECT 'ReceiptPrintSettings created: ' || COUNT(*) FROM sqlite_master WHERE type='table' AND name='ReceiptPrintSettings';
SELECT 'ReceiptPrintJobs created: ' || COUNT(*) FROM sqlite_master WHERE type='table' AND name='ReceiptPrintJobs';

-- Verify default data was inserted
SELECT 'Default templates: ' || COUNT(*) FROM ReceiptTemplates WHERE IsDefault = 1;
SELECT 'Default printers: ' || COUNT(*) FROM PrinterConfigurations WHERE IsDefault = 1;
SELECT 'Default settings: ' || COUNT(*) FROM ReceiptPrintSettings;

PRAGMA foreign_key_check;
