using POSSystem.Models;
using POSSystem.ViewModels;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Linq;
using System.Windows.Data;
using System.Windows.Media;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;
using System.Timers;
using System.Diagnostics;
using System.Threading;
using System.Windows.Threading;
using System.Windows.Controls.Primitives;

namespace POSSystem.Views
{
    public partial class SalesView : UserControl
    {
        // Static Commands for Keyboard Shortcuts
        public static readonly RoutedCommand FocusSearchCommand = new RoutedCommand();
        public static readonly RoutedCommand ShowCustomProductCommand = new RoutedCommand();
        public static readonly RoutedCommand ClearFilterCommand = new RoutedCommand();
        public static readonly RoutedCommand ShowPaymentCommand = new RoutedCommand();
        public static readonly RoutedCommand IncreaseQuantityCommand = new RoutedCommand();
        public static readonly RoutedCommand DecreaseQuantityCommand = new RoutedCommand();

        private ScrollViewer _scrollViewer;
        private SaleViewModel ViewModel => (SaleViewModel)DataContext;
        private System.Timers.Timer _searchTimer;
        private string _lastSearchText = string.Empty;
        private CancellationTokenSource _searchCancellation;
        private const int PAGE_SIZE = 50;
        private bool _isLoadingMore = false;
        private readonly ISettingsService _settingsService;

        public SalesView()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            
            if (DataContext == null)
            {
                DataContext = new SaleViewModel();
            }

            // Command Bindings
            CommandBindings.Add(new CommandBinding(FocusSearchCommand, FocusSearch_Executed));
            CommandBindings.Add(new CommandBinding(ShowCustomProductCommand, ManualAdd_Click));
            CommandBindings.Add(new CommandBinding(ClearFilterCommand, ClearFilter_Click));
            CommandBindings.Add(new CommandBinding(ShowPaymentCommand, ShowPayment_Executed));
            CommandBindings.Add(new CommandBinding(IncreaseQuantityCommand, IncreaseQuantity_Executed));
            CommandBindings.Add(new CommandBinding(DecreaseQuantityCommand, DecreaseQuantity_Executed));

            // Initialize search timer
            _searchTimer = new System.Timers.Timer(300); // 300ms delay
            _searchTimer.Elapsed += SearchTimer_Elapsed;
            _searchTimer.AutoReset = false;

            Loaded += SalesView_Loaded;
        }

        private void SalesView_Loaded(object sender, RoutedEventArgs e)
        {
            _scrollViewer = FindVisualChild<ScrollViewer>(this);
            if (_scrollViewer != null)
            {
                _scrollViewer.ScrollChanged += ScrollViewer_ScrollChanged;
            }

            // ✅ CRITICAL FIX: Initialize products in background to prevent UI blocking
            _ = Task.Run(async () =>
            {
                try
                {
                    await ViewModel.RefreshProducts();
                    System.Diagnostics.Debug.WriteLine("[SALESVIEW] Background product initialization completed");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALESVIEW] Error initializing products: {ex.Message}");
                }
            });
            
            // Add button to open layout theme selection
            CreateThemeSelectionButton();
        }

        private async void ScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            var scrollViewer = (ScrollViewer)sender;
            if (scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight * 0.8 && ViewModel != null)
            {
                try
                {
                    await ViewModel.LoadMoreProducts();
                }
                catch (Exception ex)
                {
                    // Log error and show user-friendly message
                    System.Diagnostics.Debug.WriteLine($"Error loading more products: {ex.Message}");
                    // Don't show MessageBox in scroll event as it would be disruptive
                }
            }
        }

        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                
                var result = (child as T) ?? FindVisualChild<T>(child);
                if (result != null) return result;
            }
            return null;
        }

        private void FocusSearch_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            txtSearch?.Focus();
            txtSearch?.SelectAll();
        }

        private void ShowPayment_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            if (ViewModel?.HasCartItems == true)
            {
                // Show payment dialog
                ShowPaymentDialog();
            }
        }

        private void IncreaseQuantity_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            if (ViewModel?.SelectedCartItem != null)
            {
                var item = ViewModel.SelectedCartItem;
                if (item.Product.StockQuantity > item.Quantity || item.Product.Id < 0) // Allow unlimited for custom products
                {
                    // For weight-based products, increase by 0.1, for unit-based products, increase by 1
                    decimal increment = item.Product.IsWeightBased ? 0.1m : 1m;
                    ViewModel.AddToCart(item.Product, increment);
                }
            }
        }

        private void DecreaseQuantity_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            if (ViewModel?.SelectedCartItem != null)
            {
                var item = ViewModel.SelectedCartItem;

                // For weight-based products, decrease by 0.1, for unit-based products, decrease by 1
                decimal decrement = item.Product.IsWeightBased ? 0.1m : 1m;
                decimal newQuantity = item.Quantity - decrement;

                // Minimum quantity check - for weight-based products, minimum is 0.1, for unit-based, minimum is 1
                decimal minimumQuantity = item.Product.IsWeightBased ? 0.1m : 1m;

                if (newQuantity >= minimumQuantity)
                {
                    item.Quantity = newQuantity;
                    ViewModel.CalculateTotals();
                }
                else
                {
                    ViewModel.RemoveFromCart(item.Product.Id);
                }
            }
        }

        private async void ManualAdd_Click(object sender, RoutedEventArgs e)
        {
            var content = new StackPanel { Margin = new Thickness(16) };

            // Create input fields
            var txtName = new TextBox { 
                Margin = new Thickness(0, 8, 0, 8),
                Text = Application.Current.TryFindResource("CustomProduct") as string ?? "Custom Product",
                IsReadOnly = true  // Make name field read-only since it's a default
            };
            txtName.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, Application.Current.TryFindResource("Product") as string ?? "Product Name");
            txtName.Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"];

            var txtPrice = new TextBox { 
                Margin = new Thickness(0, 8, 0, 8),
                TabIndex = 0
            };
            txtPrice.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, Application.Current.TryFindResource("Price") as string ?? "Price");
            txtPrice.Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"];

            var txtQuantity = new TextBox { 
                Margin = new Thickness(0, 8, 0, 16), 
                Text = "1",
                TabIndex = 1
            };
            txtQuantity.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, Application.Current.TryFindResource("Quantity") as string ?? "Quantity");
            txtQuantity.Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"];

            // Add title
            content.Children.Add(new TextBlock
            {
                Text = Application.Current.TryFindResource("AddCustomProduct") as string ?? "Add Custom Product",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            });

            // Add input fields
            content.Children.Add(txtName);
            content.Children.Add(txtPrice);
            content.Children.Add(txtQuantity);

            // Add buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 8, 0, 0)
            };

            var cancelButton = new Button
            {
                Content = Application.Current.TryFindResource("Cancel") as string ?? "Cancel",
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Margin = new Thickness(0, 0, 8, 0),
                Command = DialogHost.CloseDialogCommand,
                TabIndex = 3
            };

            var addButton = new Button
            {
                Content = Application.Current.TryFindResource("AddToCart") as string ?? "Add to Cart",
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                IsDefault = true,
                TabIndex = 2
            };

            bool isProcessing = false;

            void processAndClose()
            {
                if (isProcessing) return;
                isProcessing = true;

                try
                {
                    if (!decimal.TryParse(txtPrice.Text, out decimal price) || price <= 0)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("ValidSellingPriceRequired") as string ?? "Please enter a valid price!",
                            Application.Current.TryFindResource("ValidationError") as string ?? "Validation Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        txtPrice.Focus();
                        return;
                    }

                    // ✅ WEIGHT-BASED FIX: Use decimal.TryParse to support decimal quantities for weight-based products
                    if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("ValidQuantityRequired") as string ?? "Please enter a valid quantity!",
                            Application.Current.TryFindResource("ValidationError") as string ?? "Validation Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        txtQuantity.Focus();
                        return;
                    }

                    // Create custom product
                    var customProduct = new Product
                    {
                        Id = -1, // Special ID for custom products
                        Name = txtName.Text,
                        SellingPrice = price,
                        PurchasePrice = price * 0.8m, // Assume 20% margin
                        StockQuantity = int.MaxValue // No stock limit for custom products
                    };

                    ViewModel.AddToCart(customProduct, quantity);
                    DialogHost.CloseDialogCommand.Execute(null, null);
                }
                finally
                {
                    isProcessing = false;
                }
            }

            // Add keyboard shortcuts
            txtPrice.KeyDown += (s, args) =>
            {
                if (args.Key == Key.Enter)
                {
                    txtQuantity.Focus();
                    txtQuantity.SelectAll();
                    args.Handled = true;
                }
            };

            txtQuantity.KeyDown += (s, args) =>
            {
                if (args.Key == Key.Enter)
                {
                    processAndClose();
                    args.Handled = true;
                }
            };

            addButton.Click += (s, args) => processAndClose();

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(addButton);
            content.Children.Add(buttonPanel);

            // Set up focus handling
            bool focusSet = false;
            txtPrice.Loaded += (s, args) =>
            {
                if (!focusSet)
                {
                    focusSet = true;
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        txtPrice.Focus();
                        txtPrice.SelectAll();
                    }), System.Windows.Threading.DispatcherPriority.Input);
                }
            };

            // Show dialog with focus handling
            var dialogOpenedEventHandler = new DialogOpenedEventHandler((s, args) =>
            {
                if (!focusSet)
                {
                    focusSet = true;
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        txtPrice.Focus();
                        txtPrice.SelectAll();
                    }), System.Windows.Threading.DispatcherPriority.Input);
                }
            });

            await DialogHost.Show(content, "SalesDialog", dialogOpenedEventHandler);
        }

        private async void AddToCart_Click(object sender, RoutedEventArgs e)
        {
            var selectedProduct = (Product)((FrameworkElement)sender).DataContext;
            if (selectedProduct != null)
            {
                // Check if product has enough stock before adding to cart (skip for services)
                // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                if (selectedProduct.Id >= 0 &&
                    selectedProduct.Type != ProductType.Service &&
                    selectedProduct.StockQuantity <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"[OutOfStock] Product {selectedProduct.Name} is out of stock, showing confirmation prompt");

                    // Show confirmation prompt for out-of-stock products
                    var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                    var message = $"This product is currently out of stock (0 items available).\n\nWould you like to create a reservation invoice for this product instead?";

                    var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                    if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User confirmed reservation creation for {selectedProduct.Name}");
                        // User wants to create a reservation - open the reservation dialog
                        await CreateReservationInvoice_ClickInternal(selectedProduct);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User declined reservation creation for {selectedProduct.Name}");
                        // User declined - do nothing, return to normal browsing
                    }
                    return;
                }

                // ✅ WEIGHT-BASED PRODUCT FIX: Use AddToCartCommand for consistent behavior
                if (ViewModel.AddToCartCommand.CanExecute(selectedProduct))
                {
                    ViewModel.AddToCartCommand.Execute(selectedProduct);
                }
            }
        }

        private void RemoveItem_Click(object sender, RoutedEventArgs e)
        {
            if (sender is FrameworkElement element && element.DataContext is CartItem cartItem)
            {
                ViewModel.RemoveFromCart(cartItem.Product.Id);
            }
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                _searchTimer.Stop();
                
                string searchText = txtSearch?.Text?.Trim() ?? "";
                
                // If the search text is exactly 13 digits, treat it as a barcode
                if (searchText.Length == 13 && searchText.All(char.IsDigit))
                {
                    ProcessBarcodeAsync(searchText);
                    return;
                }
                
                // For non-barcode searches, use the normal search functionality
                if (searchText != _lastSearchText)
                {
                    _lastSearchText = searchText;
                    _searchCancellation?.Cancel();
                    _searchCancellation = new CancellationTokenSource();
                    _ = ViewModel.FilterProducts(searchText, false, _searchCancellation.Token);
                }
            }
        }

        private void InitializeSearchTimer()
        {
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
                _searchTimer.Elapsed -= SearchTimer_Elapsed;
                _searchTimer.Dispose();
            }
            
            _searchTimer = new System.Timers.Timer(500); // Increase debounce time to 500ms
            _searchTimer.Elapsed += SearchTimer_Elapsed;
            _searchTimer.AutoReset = false;
        }

        private void txtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_searchTimer == null)
            {
                InitializeSearchTimer();
            }

            // Reset and restart the timer
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private async void SearchTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            try 
            {
                // Cancel any previous search
                if (_searchCancellation != null)
                {
                    _searchCancellation.Cancel();
                    _searchCancellation.Dispose();
                }
                _searchCancellation = new CancellationTokenSource();
                
                // Get search text on UI thread
                string searchText = await Dispatcher.InvokeAsync(() => txtSearch.Text);
                
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    await Dispatcher.InvokeAsync(() => ViewModel.ResetProductList());
                    return;
                }

                // Do work on background thread
                bool isBarcodeSearch = searchText.Length >= 8 && searchText.All(char.IsDigit);
                
                // Check cancellation
                _searchCancellation.Token.ThrowIfCancellationRequested();
                
                // Update UI in batches on UI thread with lower priority
                await ViewModel.FilterProducts(searchText, isBarcodeSearch, _searchCancellation.Token);
            }
            catch (OperationCanceledException)
            {
                // Search was cancelled, ignore
            }
            catch (Exception ex)
            {
                await Dispatcher.InvokeAsync(() => 
                    MessageBox.Show($"Error during search: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error)
                );
            }
        }

        private void IncreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            var cartItem = ((FrameworkElement)sender).DataContext as CartItem;
            if (cartItem != null)
            {
                // ✅ FIX: Use batch-aware increment method from SaleViewModel
                // This ensures proper batch switching when batches are depleted

                // Set the selected cart item so the ViewModel knows which item to modify
                ViewModel.SelectedCartItem = cartItem;

                // Determine increment based on weight-based flag
                decimal increment = cartItem.Product.IsWeightBased ? 0.1m : 1m;

                if (cartItem.Product.TrackBatches)
                {
                    // Use batch-aware increment that handles batch switching automatically
                    ViewModel.IncreaseCartItemQuantityRespectingBatches(cartItem, increment);
                }
                else
                {
                    // Check stock before increasing for non-batch products
                    if (cartItem.Product.Id != -1 && cartItem.Product.StockQuantity <= cartItem.Quantity)
                    {
                        // ✅ FIX: Use standardized out-of-stock handling with reservation prompt
                        string unit = cartItem.Product.IsWeightBased ?
                            (cartItem.Product.UnitOfMeasure?.Abbreviation ?? "units") : "items";

                        if (cartItem.Product.StockQuantity <= 0)
                        {
                            // Product is completely out of stock - show reservation prompt
                            _ = Task.Run(async () =>
                            {
                                var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                                var message = $"This product is currently out of stock (0 {unit} available).\n\nWould you like to create a reservation invoice for this product instead?";

                                var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                                if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                                {
                                    Application.Current.Dispatcher.Invoke(() =>
                                    {
                                        // Use the public AddToCart method which will handle the out-of-stock scenario
                                        // and trigger the invoice creation workflow
                                        ViewModel.AddToCart(cartItem.Product);
                                    });
                                }
                            });
                        }
                        else
                        {
                            // Product has some stock but not enough for the requested quantity
                            var title = Application.Current.FindResource("StockLimitTitle") as string ?? "Stock Limit";
                            var message = $"Cannot add more. Only {cartItem.Product.StockQuantity:F3} {unit} available in stock.";
                            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                        return;
                    }

                    // Simple increment for non-batch products
                    cartItem.Quantity += increment;
                    ViewModel.CalculateTotals(); // Force recalculation
                }
            }
        }

        private void DecreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            var cartItem = ((FrameworkElement)sender).DataContext as CartItem;
            if (cartItem != null)
            {
                // ✅ FIX: Use batch-aware decrement method from SaleViewModel
                // This ensures proper batch handling when decreasing quantities

                // Set the selected cart item so the ViewModel knows which item to modify
                ViewModel.SelectedCartItem = cartItem;

                // Determine decrement based on weight-based flag
                decimal decrement = cartItem.Product.IsWeightBased ? 0.1m : 1m;

                if (cartItem.Product.TrackBatches)
                {
                    // Use batch-aware decrement that handles batch boundaries properly
                    if (cartItem.Quantity <= decrement)
                    {
                        // Remove the item if quantity would go to zero or below
                        ViewModel.RemoveFromCart(cartItem.Product.Id);
                        return;
                    }

                    ViewModel.DecreaseCartItemQuantityRespectingBatches(cartItem, decrement);
                }
                else
                {
                    // Simple decrement for non-batch products
                    decimal newQuantity = cartItem.Quantity - decrement;

                    // Minimum quantity check - for weight-based products, minimum is 0.1, for unit-based, minimum is 1
                    decimal minimumQuantity = cartItem.Product.IsWeightBased ? 0.1m : 1m;

                    if (newQuantity >= minimumQuantity)
                    {
                        cartItem.Quantity = newQuantity;
                        ViewModel.CalculateTotals(); // Force recalculation
                    }
                    else
                    {
                        // Remove the item if it would go below minimum quantity
                        ViewModel.RemoveFromCart(cartItem.Product.Id);
                    }
                }
            }
        }

        private void NewCart_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.CreateNewCart();
        }

        private void CloseCart_Click(object sender, RoutedEventArgs e)
        {
            if (ViewModel.ActiveCart != null)
            {
                ViewModel.CloseCart(ViewModel.ActiveCart);
            }
        }

        private async void ProductList_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is ListView listView && listView.SelectedItem is Product selectedProduct)
            {
                // Check stock before adding to cart (use decimal StockQuantity to avoid flooring decimals to 0)
                if (selectedProduct.Id >= 0 && selectedProduct.StockQuantity <= 0m)
                {
                    System.Diagnostics.Debug.WriteLine($"[OutOfStock] Product {selectedProduct.Name} is out of stock, showing confirmation prompt");

                    // Show confirmation prompt for out-of-stock products
                    var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                    var message = $"This product is currently out of stock (0 items available).\n\nWould you like to create a reservation invoice for this product instead?";

                    var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                    if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User confirmed reservation creation for {selectedProduct.Name}");
                        // User wants to create a reservation - open the reservation dialog
                        await CreateReservationInvoice_ClickInternal(selectedProduct);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[OutOfStock] User declined reservation creation for {selectedProduct.Name}");
                        // User declined - do nothing, return to normal browsing
                    }
                    return;
                }

                // ✅ WEIGHT-BASED PRODUCT FIX: Use AddToCartCommand for consistent behavior
                if (ViewModel.AddToCartCommand.CanExecute(selectedProduct))
                {
                    ViewModel.AddToCartCommand.Execute(selectedProduct);
                }
            }
        }

        private async void Product_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && element.DataContext is Product product)
            {
                if (product.StockQuantity <= 0m)
                {
                    // Check if user has invoice permissions
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;
                    bool canCreateInvoices = permissionsService?.CanCreateDraftInvoices() == true || permissionsService?.CanCreateFullInvoices() == true;

                    if (canCreateInvoices)
                    {
                        var result = MessageBox.Show(
                            $"This product is out of stock (0 items available).\n\nWould you like to create an invoice for this product instead?",
                            "Out of Stock - Create Invoice?",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            await CreateInvoiceFromOutOfStockProduct(product);
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.Show("This product is out of stock!", "Out of Stock", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    return;
                }

                // Use the AddToCartCommand instead of directly calling AddToCart
                if (ViewModel.AddToCartCommand.CanExecute(product))
                {
                    ViewModel.AddToCartCommand.Execute(product);
                }
            }
        }

        private async Task CreateInvoiceFromOutOfStockProduct(Product product)
        {
            try
            {
                // Get required services
                var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                if (permissionsService == null || dbService == null)
                {
                    MessageBox.Show("Required services not available.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Create and show confirmation dialog
                var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                // Show invoice confirmation dialog
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(confirmationDialog, "SalesDialog");

                if (confirmationDialog.DialogResult?.Confirmed == true)
                {
                    var invoiceResult = confirmationDialog.DialogResult;

                    if (invoiceResult.CreateFullInvoice)
                    {
                        // Admin user - create full invoice directly
                        MessageBox.Show($"Full invoice creation for {invoiceResult.Product.Name} would be implemented here.",
                            "Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        // Non-admin user - create draft invoice
                        MessageBox.Show($"Draft invoice creation for {invoiceResult.Product.Name} would be implemented here.",
                            "Draft Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error in CreateInvoiceFromOutOfStockProduct: {ex.Message}");
                MessageBox.Show($"Error creating invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CreateReservationInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Prevent event bubbling to avoid conflicts
                e.Handled = true;

                if (sender is FrameworkElement element && element.Tag is Product product)
                {
                    await CreateReservationInvoice_ClickInternal(product);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error in CreateReservationInvoice_Click: {ex.Message}");
                MessageBox.Show($"Error creating reservation invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateReservationInvoice_ClickInternal(Product product)
        {
            try
            {
                if (product != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[RESERVATION] CreateReservationInvoice_ClickInternal called for product: {product.Name}");

                    // Get required services using established pattern
                    var dbService = POSSystem.Helpers.ServiceLocator.Current?.GetInstance<POSSystem.Services.DatabaseService>();
                    var permissionsService = dbService != null ? new POSSystem.Services.UserPermissionsService(dbService) : null;

                    if (permissionsService == null || dbService == null)
                    {
                        MessageBox.Show("Reservation services are not available. Please restart the application.",
                                      "Service Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // Create and show confirmation dialog (let dialog handle permission checks)
                    var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbService);
                    var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                    var result = await MaterialDesignThemes.Wpf.DialogHost.Show(confirmationDialog, "SalesDialog");

                    if (result is POSSystem.ViewModels.ProductToInvoiceResult invoiceResult && invoiceResult.Confirmed)
                    {
                        // Only create stock reservation, don't open draft invoice dialog
                        await CreateStockReservationOnly(invoiceResult);
                    }

                    System.Diagnostics.Debug.WriteLine($"[RESERVATION] Product reservation workflow completed for: {product.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error in CreateReservationInvoice_ClickInternal: {ex.Message}");
                MessageBox.Show($"Error creating reservation invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Creates stock reservation only without opening draft invoice dialog
        /// </summary>
        private async Task CreateStockReservationOnly(POSSystem.ViewModels.ProductToInvoiceResult invoiceResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Creating stock reservation for product: {invoiceResult.Product.Name}");

                // Use modern DI container instead of ServiceLocator
                var authService = App.ServiceProvider?.GetService(typeof(IAuthenticationService)) as AuthenticationService;
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as DatabaseService;
                var stockService = App.ServiceProvider?.GetService(typeof(POSSystem.Services.InventoryManagement.IStockService)) as POSSystem.Services.InventoryManagement.IStockService;

                if (authService == null || dbService == null || stockService == null)
                {
                    throw new InvalidOperationException("Required services are not available");
                }

                // Add reserved stock to product
                await AddReservedStockToProduct(invoiceResult.Product, invoiceResult.Quantity, stockService, dbService, authService);

                // Refresh the product display to show updated stock
                await RefreshProductDisplay(invoiceResult.Product.Id);

                // Show success message
                MessageBox.Show($"Stock reservation created successfully!\n\nProduct: {invoiceResult.Product.Name}\nQuantity: {invoiceResult.Quantity}\n\nThe product now has stock available for sale.",
                    "Stock Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Stock reservation completed for: {invoiceResult.Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error creating stock reservation: {ex.Message}");
                MessageBox.Show($"Error creating stock reservation: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateFullInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Creating full invoice for product: {result.Product.Name}");

                // Implementation would integrate with the existing full invoice creation system
                MessageBox.Show($"Full reservation invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})",
                    "Reservation Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error creating full invoice: {ex.Message}");
                throw;
            }
        }

        private async Task CreateDraftInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Creating draft reservation invoice for product: {result.Product.Name}");

                // Implementation would integrate with the existing draft invoice creation system
                MessageBox.Show($"Draft reservation invoice created successfully for {result.Product.Name} (Qty: {result.Quantity})\n\nThis reservation will be available for admin completion.",
                    "Draft Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[RESERVATION] Error creating draft invoice: {ex.Message}");
                throw;
            }
        }

        private async void ProcessPayment_Click(object sender, RoutedEventArgs e)
        {
            if (ViewModel.CartItems == null || !ViewModel.CartItems.Any())
            {
                MessageBox.Show("Cart is empty!", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var content = new Grid { Margin = new Thickness(32) };
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Title
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Summary Card
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Payment Method
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Amount Panel
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Buttons

            // Title
            var titlePanel = new StackPanel { Margin = new Thickness(0, 0, 0, 24) };
            var title = new TextBlock
            {
                Text = Application.Current.TryFindResource("PaymentTitle") as string ?? "Payment",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline5TextBlock"],
                Margin = new Thickness(0, 0, 0, 8),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            var subtitle = new TextBlock
            {
                Text = Application.Current.TryFindResource("CompleteTransaction") as string ?? "Complete Transaction",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Opacity = 0.7,
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            titlePanel.Children.Add(title);
            titlePanel.Children.Add(subtitle);
            Grid.SetRow(titlePanel, 0);
            content.Children.Add(titlePanel);

            // Summary Card
            var summaryCard = CreateSummaryCard();
            Grid.SetRow(summaryCard, 1);
            content.Children.Add(summaryCard);

            // Amount Tendered Section
            var amountPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            var amountLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("AmountTendered") as string ?? "Amount Tendered",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            
            var txtAmountTendered = new TextBox
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                Margin = new Thickness(0, 0, 0, 8),
                FlowDirection = FlowDirection.LeftToRight,
                TextAlignment = TextAlignment.Right
            };
            
            var txtChange = new TextBlock
            {
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                Margin = new Thickness(0, 0, 0, 16),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };

            amountPanel.Children.Add(amountLabel);
            amountPanel.Children.Add(txtAmountTendered);
            amountPanel.Children.Add(txtChange);
            Grid.SetRow(amountPanel, 3);
            content.Children.Add(amountPanel);

            // Due Date Section (initially hidden)
            var dueDatePanel = new StackPanel 
            { 
                Margin = new Thickness(0, 0, 0, 24),
                Visibility = Visibility.Collapsed
            };
            var dueDateLabel = new TextBlock
            {
                Text = "Due Date",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            var datePicker = new DatePicker
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedDatePicker"],
                SelectedDate = DateTime.Now.AddDays(30)
            };
            dueDatePanel.Children.Add(dueDateLabel);
            dueDatePanel.Children.Add(datePicker);

            // Payment Method Section
            var paymentMethodPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 24) };
            var paymentMethodLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("PaymentMethod") as string ?? "Payment Method",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            
            // Payment Method Buttons Panel
            var paymentButtonsPanel = new UniformGrid 
            { 
                Rows = 1,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var cashButton = new Button
            {
                Content = Application.Current.TryFindResource("Cash") as string ?? "Cash",
                Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"], // Set as default selected
                Margin = new Thickness(0, 0, 8, 0),
                Height = 40,
                Tag = "Cash"
            };

            var cardButton = new Button
            {
                Content = Application.Current.TryFindResource("Card") as string ?? "Card",
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"],
                Margin = new Thickness(0, 0, 8, 0),
                Height = 40,
                Tag = "Card"
            };

            var unpaidButton = new Button
            {
                Content = Application.Current.TryFindResource("Unpaid") as string ?? "Unpaid",
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"],
                Height = 40,
                Tag = "Unpaid"
            };

            string selectedPaymentMethod = "Cash"; // Default selection
            void UpdatePaymentButtonStyles(Button selectedButton)
            {
                foreach (Button btn in paymentButtonsPanel.Children)
                {
                    btn.Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"];
                }
                selectedButton.Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"];
                selectedPaymentMethod = selectedButton.Tag.ToString();
            }

            cashButton.Click += (s, e) => 
            {
                UpdatePaymentButtonStyles(cashButton);
                amountPanel.Visibility = Visibility.Visible;
                dueDatePanel.Visibility = Visibility.Collapsed;
            };

            cardButton.Click += (s, e) => 
            {
                UpdatePaymentButtonStyles(cardButton);
                amountPanel.Visibility = Visibility.Visible;
                dueDatePanel.Visibility = Visibility.Collapsed;
            };

            unpaidButton.Click += async (s, e) => 
            {
                if (ViewModel.SelectedCustomer == null || ViewModel.SelectedCustomer.Id == -1)
                {
                    DialogHost.Close("SalesDialog");
                    await ShowCustomerSelectionDialog();
                    
                    if (ViewModel.SelectedCustomer == null || ViewModel.SelectedCustomer.Id == -1)
                    {
                        UpdatePaymentButtonStyles(cashButton);
                        return;
                    }
                    await DialogHost.Show(content, "SalesDialog");
                }
                UpdatePaymentButtonStyles(unpaidButton);
                amountPanel.Visibility = Visibility.Collapsed;
                dueDatePanel.Visibility = Visibility.Visible;
            };

            paymentButtonsPanel.Children.Add(cashButton);
            paymentButtonsPanel.Children.Add(cardButton);
            paymentButtonsPanel.Children.Add(unpaidButton);

            paymentMethodPanel.Children.Add(paymentMethodLabel);
            paymentMethodPanel.Children.Add(paymentButtonsPanel);
            paymentMethodPanel.Children.Add(dueDatePanel);

            Grid.SetRow(paymentMethodPanel, 2);
            content.Children.Add(paymentMethodPanel);

            // Event handlers
            txtAmountTendered.TextChanged += (s, e) =>
            {
                if (decimal.TryParse(txtAmountTendered.Text, out decimal tendered))
                {
                    decimal change = tendered - ViewModel.GrandTotal;
                    txtChange.Text = change >= 0 ? change.ToString("C2") : 
                        Application.Current.TryFindResource("InsufficientAmount") as string ?? "Insufficient amount";
                    txtChange.Foreground = change >= 0 ? Brushes.Green : Brushes.Red;
                }
                else
                {
                    txtChange.Text = string.Empty;
                }
            };

            // Buttons
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 16, 0, 0),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            Grid.SetRow(buttonsPanel, 4);

            var cancelButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Content = Application.Current.TryFindResource("Cancel") as string ?? "Cancel",
                Margin = new Thickness(0, 0, 8, 0)
            };
            cancelButton.Click += (s, e) =>
            {
                DialogHost.Close("SalesDialog", null);
            };

            var processButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Content = Application.Current.TryFindResource("ProcessPaymentButton") as string ?? "Process Payment"
            };
            processButton.Click += async (s, e) =>
            {
                if (selectedPaymentMethod == "Unpaid")
                {
                    if (datePicker.SelectedDate == null)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("DueDateRequired") as string ?? 
                                "Due date is required for unpaid sales.",
                            Application.Current.TryFindResource("InvalidInput") as string ?? "Invalid Input",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    if (await ViewModel.ProcessPayment(selectedPaymentMethod, 0, datePicker.SelectedDate))
                    {
                        DialogHost.Close("SalesDialog", true);
                    }
                }
                else if (decimal.TryParse(txtAmountTendered.Text, out decimal amountTendered))
                {
                    if (amountTendered >= ViewModel.GrandTotal)
                    {
                        if (await ViewModel.ProcessPayment(selectedPaymentMethod, amountTendered))
                        {
                            DialogHost.Close("SalesDialog", true);
                        }
                    }
                    else
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("AmountTenderedMustBeGreater") as string ?? 
                                "Amount tendered must be greater than or equal to the total amount.",
                            Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("PleaseEnterValidAmount") as string ?? 
                            "Please enter a valid amount.",
                        Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            };

            buttonsPanel.Children.Add(cancelButton);
            buttonsPanel.Children.Add(processButton);
            Grid.SetRow(buttonsPanel, 4);
            content.Children.Add(buttonsPanel);

            // Set initial amount and focus
            txtAmountTendered.Text = ViewModel.GrandTotal.ToString("F2");
            txtAmountTendered.SelectAll();
            txtAmountTendered.Focus();

            // Add keyboard handling
            txtAmountTendered.KeyDown += (s, e) =>
            {
                if (e.Key == Key.Enter)
                {
                    processButton.RaiseEvent(new RoutedEventArgs(Button.ClickEvent));
                    e.Handled = true;
                }
            };

            var result = await DialogHost.Show(content, "SalesDialog");
            if (result is bool success && success)
            {
                // Cart is already cleared in ViewModel.ProcessPayment
                // No need to create a new cart automatically
            }
        }

        public async void AddCustomer_Click(object sender, RoutedEventArgs e)
        {
            await ShowCustomerSelectionDialog();
        }

        private async Task ShowCustomerSelectionDialog()
        {
            var content = new Grid { Margin = new Thickness(20) };
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Title
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Search panel
            content.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Customer list
            content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Button panel

            // Title
            var titleBlock = new TextBlock
            {
                Text = (string)Application.Current.Resources["SelectCustomer"],
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            };
            Grid.SetRow(titleBlock, 0);
            content.Children.Add(titleBlock);

            // Search panel
            var searchPanel = new Grid
            {
                Margin = new Thickness(0, 0, 0, 16)
            };
            searchPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Search box
            searchPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto }); // Loyalty card input
            searchPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto }); // Add new button

            // Search box
            var searchBox = new TextBox
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                Margin = new Thickness(0, 0, 16, 0)
            };
            searchBox.SetValue(HintAssist.HintProperty, (string)Application.Current.Resources["CustomerSearchHint"]);
            Grid.SetColumn(searchBox, 0);

            // Loyalty card input
            var loyaltyCardBox = new TextBox
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                Width = 150,
                Margin = new Thickness(0, 0, 16, 0)
            };
            loyaltyCardBox.SetValue(HintAssist.HintProperty, (string)Application.Current.Resources["LoyaltyCardNumber"]);
            Grid.SetColumn(loyaltyCardBox, 1);

            // Add new customer button
            var addNewButton = new Button
            {
                Content = (string)Application.Current.Resources["AddNew"],
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"]
            };
            Grid.SetColumn(addNewButton, 2);

            searchPanel.Children.Add(searchBox);
            searchPanel.Children.Add(loyaltyCardBox);
            searchPanel.Children.Add(addNewButton);
            Grid.SetRow(searchPanel, 1);
            content.Children.Add(searchPanel);

            // Customer list
            var customerList = new ListView
            {
                Style = (Style)Application.Current.Resources["MaterialDesignListView"],
                Margin = new Thickness(0, 0, 0, 16),
                MinHeight = 300,
                MaxHeight = 400
            };

            var gridView = new GridView();
            gridView.Columns.Add(new GridViewColumn
            {
                Header = Application.Current.TryFindResource("Name") as string ?? "Name",
                DisplayMemberBinding = new MultiBinding
                {
                    StringFormat = "{0} {1}",
                    Bindings = {
                        new Binding("FirstName"),
                        new Binding("LastName")
                    }
                },
                Width = 200
            });

            gridView.Columns.Add(new GridViewColumn
            {
                Header = Application.Current.TryFindResource("Email") as string ?? "Email",
                DisplayMemberBinding = new Binding("Email"),
                Width = 200
            });

            gridView.Columns.Add(new GridViewColumn
            {
                Header = Application.Current.TryFindResource("Phone") as string ?? "Phone",
                DisplayMemberBinding = new Binding("Phone"),
                Width = 150
            });

            gridView.Columns.Add(new GridViewColumn
            {
                Header = Application.Current.TryFindResource("LoyaltyCard") as string ?? "Loyalty Card",
                DisplayMemberBinding = new Binding("LoyaltyCode"),
                Width = 120
            });

            customerList.View = gridView;
            Grid.SetRow(customerList, 2);
            content.Children.Add(customerList);

            // ✅ PERFORMANCE FIX: Load customers asynchronously to prevent UI blocking
            LoadCustomersAsync(customerList, searchBox);

            // Search functionality will be set up in LoadCustomersAsync

            // Loyalty card search
            loyaltyCardBox.KeyDown += (s, args) =>
            {
                if (args.Key == Key.Enter)
                {
                    var cardNumber = loyaltyCardBox.Text.Trim();
                    if (!string.IsNullOrEmpty(cardNumber))
                    {
                        // Get customers from the ListView's ItemsSource
                        if (customerList.ItemsSource is IEnumerable<Customer> customers)
                        {
                            var customer = customers.FirstOrDefault(c => c.LoyaltyCode == cardNumber);
                            if (customer != null)
                            {
                                customerList.SelectedItem = customer;
                                addNewButton.IsEnabled = false;
                            }
                            else
                            {
                                MessageBox.Show((string)Application.Current.Resources["CustomerNotFoundMessage"],
                                    (string)Application.Current.Resources["CustomerNotFound"],
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                    }
                }
            };

            customerList.MouseDoubleClick += (s, args) =>
            {
                if (customerList.SelectedItem is Customer customer)
                {
                    ViewModel.SelectedCustomer = customer;
                    DialogHost.Close("SalesDialog");

                }
            };

            // Button panel
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 16, 0, 0)
            };

            var cancelButton = new Button
            {
                Content = (string)Application.Current.Resources["Cancel"],
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Margin = new Thickness(0, 0, 8, 0),
                Command = DialogHost.CloseDialogCommand
            };

            var selectButton = new Button
            {
                Content = (string)Application.Current.Resources["SelectCustomer"],
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                IsEnabled = false
            };

            customerList.SelectionChanged += (s, args) =>
            {
                selectButton.IsEnabled = customerList.SelectedItem != null;
            };

            selectButton.Click += (s, args) =>
            {
                if (customerList.SelectedItem is Customer customer)
                {
                    ViewModel.SelectedCustomer = customer;
                    DialogHost.Close("SalesDialog");
                }
            };

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(selectButton);
            Grid.SetRow(buttonPanel, 3);
            content.Children.Add(buttonPanel);

            await DialogHost.Show(content, "SalesDialog");
        }

        private async void PopularItems_Click(object sender, RoutedEventArgs e)
        {
            // Load and display popular items
            await ViewModel.LoadPopularProducts();
        }

        private void RecentSales_Click(object sender, RoutedEventArgs e)
        {
            // Show recent sales items
            var recentProducts = ViewModel.LoadRecentSaleProducts();
            ViewModel.FilteredProducts = new ObservableCollection<Product>(recentProducts);
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox?.SelectedItem is Category selectedCategory)
            {
                ViewModel.FilterProductsByCategory(selectedCategory.Id);
            }
            else
            {
                // When nothing is selected, show all products
                ViewModel.ResetProductList();
            }
        }

        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            txtSearch.Clear();
            ViewModel.ShowingPopularItems = true;
            _ = ViewModel.RefreshProducts();
        }

        private void ClearFilter_Click(object sender, RoutedEventArgs e)
        {
            // Clear category selection if exists
            var categoryComboBox = this.FindName("categoryFilter") as ComboBox;
            if (categoryComboBox != null)
            {
                categoryComboBox.SelectedIndex = -1;
            }

            // Reset to show popular items
            ViewModel.ShowingPopularItems = true;
            _ = ViewModel.RefreshProducts();
        }

        private void DataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid grid)
            {
                ViewModel.SelectedCartItem = grid.SelectedItem as CartItem;
            }
        }

        private async void ApplyItemDiscount_Click(object sender, RoutedEventArgs e)
        {
            if (ViewModel.SelectedCartItem == null) return;

            var dialog = new DiscountDialog(
                ViewModel.CurrentUser,
                ViewModel.SelectedCartItem.Total,
                false,
                new DiscountService(new DatabaseService()));

            if (await DialogHost.Show(dialog, "SalesDialog") is Discount discount)
            {
                ViewModel.DiscountService.ApplyItemDiscount(ViewModel.SelectedCartItem, discount);
                ViewModel.CalculateTotals();
            }
        }

        private async void ApplyCartDiscount_Click(object sender, RoutedEventArgs e)
        {
            if (!ViewModel.HasCartItems) return;

            var dialog = new DiscountDialog(
                ViewModel.CurrentUser,
                ViewModel.CurrentCart.Subtotal,
                true,
                new DiscountService(new DatabaseService()));

            if (await DialogHost.Show(dialog, "SalesDialog") is Discount discount)
            {
                ViewModel.DiscountService.ApplyCartWideDiscount(ViewModel.CurrentCart, discount);
                ViewModel.CalculateTotals();
            }
        }

        private async void SetQuantity_Click(object sender, RoutedEventArgs e)
        {
            var cartItem = ((FrameworkElement)sender).DataContext as CartItem;
            if (cartItem == null) return;

            var content = new StackPanel { Margin = new Thickness(16) };

            // Add title
            content.Children.Add(new TextBlock
            {
                Text = Application.Current.TryFindResource("SetQuantity") as string ?? "Set Quantity",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                Margin = new Thickness(0, 0, 0, 16)
            });

            // Add quantity input
            var txtNewQuantity = new TextBox 
            { 
                Margin = new Thickness(0, 8, 0, 16),
                Text = cartItem.Quantity.ToString(),
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"]
            };
            txtNewQuantity.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, 
                Application.Current.TryFindResource("EnterQuantity") as string ?? "Enter Quantity");
            content.Children.Add(txtNewQuantity);

            // Add buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 8, 0, 0)
            };

            var cancelButton = new Button
            {
                Content = Application.Current.TryFindResource("Cancel") as string ?? "Cancel",
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                Margin = new Thickness(0, 0, 8, 0),
                Command = DialogHost.CloseDialogCommand
            };

            var confirmButton = new Button
            {
                Content = Application.Current.TryFindResource("Confirm") as string ?? "Confirm",
                Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                IsDefault = true
            };

            void processQuantityChange()
            {
                // ✅ WEIGHT-BASED FIX: Use decimal.TryParse to support decimal quantities for weight-based products
                if (decimal.TryParse(txtNewQuantity.Text, out decimal newQuantity) && newQuantity > 0)
                {
                    // Check stock for regular products (not custom products or services)
                    // ✅ FIX: Use StockQuantity directly to avoid additional database queries
                    if (cartItem.Product.Id != -1 &&
                        cartItem.Product.Type != ProductType.Service &&
                        cartItem.Product.StockQuantity < newQuantity)
                    {
                        var stockDisplay = cartItem.Product.IsWeightBased ?
                            cartItem.Product.StockQuantity.ToString("0.###") :
                            cartItem.Product.StockQuantity.ToString("F0");

                        var message = string.Format(
                            Application.Current.TryFindResource("OnlyItemsAvailable") as string ?? "Only {0} items available!",
                            stockDisplay);
                        MessageBox.Show(message);
                        return;
                    }

                    cartItem.Quantity = newQuantity;
                    ViewModel.CalculateTotals();
                    DialogHost.CloseDialogCommand.Execute(null, null);
                }
                else
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("InvalidQuantity") as string ?? "Please enter a valid quantity!");
                }
            }

            // Add keyboard shortcut
            txtNewQuantity.KeyDown += (s, args) =>
            {
                if (args.Key == Key.Enter)
                {
                    processQuantityChange();
                    args.Handled = true;
                }
            };

            confirmButton.Click += (s, args) => processQuantityChange();

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(confirmButton);
            content.Children.Add(buttonPanel);

            // Show dialog with focus on the quantity input
            var dialogOpenedEventHandler = new DialogOpenedEventHandler((s, args) =>
            {
                txtNewQuantity.Focus();
                txtNewQuantity.SelectAll();
            });

            await DialogHost.Show(content, "SalesDialog", dialogOpenedEventHandler);
        }

        private void ClearCustomer_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SelectedCustomer = null;
        }

        private void RedeemPoints_Click(object sender, RoutedEventArgs e)
        {
            ShowLoyaltyPointsRedemptionDialog();
        }

        private Card CreateSummaryCard()
        {
            var summaryCard = new Card
            {
                Margin = new Thickness(0, 0, 0, 24),
                Padding = new Thickness(16),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5")),
                UniformCornerRadius = 8
            };
            var summaryPanel = new StackPanel();

            // Subtotal
            var subtotalPanel = new Grid { Margin = new Thickness(0, 4, 0, 4) };
            subtotalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            subtotalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            var subtotalLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("Subtotal") as string ?? "Subtotal",
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"]
            };
            var subtotalValue = new TextBlock
            {
                Text = ViewModel.Subtotal.ToString("C2"),
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetColumn(subtotalLabel, 0);
            Grid.SetColumn(subtotalValue, 1);
            subtotalPanel.Children.Add(subtotalLabel);
            subtotalPanel.Children.Add(subtotalValue);
            summaryPanel.Children.Add(subtotalPanel);

            // Discount (if any)
            if (ViewModel.DiscountAmount > 0)
            {
                var discountPanel = new Grid { Margin = new Thickness(0, 4, 0, 4) };
                discountPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                discountPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                var discountLabel = new TextBlock
                {
                    Text = Application.Current.TryFindResource("Discount") as string ?? "Discount",
                    Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"]
                };
                var discountValue = new TextBlock
                {
                    Text = $"-{ViewModel.DiscountAmount:C2}",
                    Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetColumn(discountLabel, 0);
                Grid.SetColumn(discountValue, 1);
                discountPanel.Children.Add(discountLabel);
                discountPanel.Children.Add(discountValue);
                summaryPanel.Children.Add(discountPanel);
            }

            // Separator
            summaryPanel.Children.Add(new Separator { Margin = new Thickness(0, 8, 0, 8) });

            // Total
            var totalPanel = new Grid { Margin = new Thickness(0, 8, 0, 4) };
            totalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            totalPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            var totalLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("TotalAmountDue") as string ?? "Total Amount Due",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"]
            };
            var totalValue = new TextBlock
            {
                Text = ViewModel.GrandTotal.ToString("C2"),
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Foreground = (Brush)Application.Current.Resources["PrimaryHueMidBrush"],
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetColumn(totalLabel, 0);
            Grid.SetColumn(totalValue, 1);
            totalPanel.Children.Add(totalLabel);
            totalPanel.Children.Add(totalValue);
            summaryPanel.Children.Add(totalPanel);

            summaryCard.Content = summaryPanel;
            return summaryCard;
        }

        private async void ShowPaymentDialog()
        {
            var grid = new Grid
            {
                Width = 400,
                Margin = new Thickness(16),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Title
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Summary
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Payment Method
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Amount Panel
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Quick Amount Buttons
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Action Buttons

            // Title Panel - More compact
            var titlePanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            var title = new TextBlock
            {
                Text = Application.Current.TryFindResource("PaymentDialog") as string ?? "Payment Dialog",
                Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"]
            };
            titlePanel.Children.Add(title);
            Grid.SetRow(titlePanel, 0);
            grid.Children.Add(titlePanel);

            // Summary Card
            var summaryCard = CreateSummaryCard();
            Grid.SetRow(summaryCard, 1);
            grid.Children.Add(summaryCard);

            // Payment Method Section - More compact
            var paymentMethodPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            var paymentMethodLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("PaymentMethod") as string ?? "Payment Method",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            
            var paymentButtonsPanel = new UniformGrid 
            { 
                Rows = 1,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var cashButton = new Button
            {
                Content = Application.Current.TryFindResource("Cash") as string ?? "Cash",
                Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"], // Set as default selected
                Margin = new Thickness(0, 0, 8, 0),
                Height = 36,
                Tag = "Cash"
            };

            var cardButton = new Button
            {
                Content = Application.Current.TryFindResource("Card") as string ?? "Card",
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"],
                Margin = new Thickness(0, 0, 8, 0),
                Height = 36,
                Tag = "Card"
            };

            var unpaidButton = new Button
            {
                Content = Application.Current.TryFindResource("Unpaid") as string ?? "Unpaid",
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"],
                Height = 36,
                Tag = "Unpaid"
            };

            string selectedPaymentMethod = "Cash"; // Default selection
            void UpdatePaymentButtonStyles(Button selectedButton)
            {
                foreach (Button btn in paymentButtonsPanel.Children)
                {
                    btn.Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"];
                }
                selectedButton.Style = (Style)Application.Current.Resources["MaterialDesignRaisedButton"];
                selectedPaymentMethod = selectedButton.Tag.ToString();
            }

            // Amount Tendered Section - More compact
            var amountPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 8) };
            var amountCard = new Card
            {
                Padding = new Thickness(12),
                UniformCornerRadius = 8
            };
            var amountCardContent = new StackPanel();

            var amountLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("AmountTendered") as string ?? "Amount Tendered",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };

            var txtAmountTendered = new TextBox
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                Margin = new Thickness(0, 0, 0, 8),
                FlowDirection = FlowDirection.LeftToRight,
                TextAlignment = TextAlignment.Right
            };

            var txtChange = new TextBlock
            {
                Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                Margin = new Thickness(0, 0, 0, 16),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };

            amountCardContent.Children.Add(amountLabel);
            amountCardContent.Children.Add(txtAmountTendered);
            amountCardContent.Children.Add(txtChange);
            amountCard.Content = amountCardContent;
            amountPanel.Children.Add(amountCard);
            Grid.SetRow(amountPanel, 3);
            grid.Children.Add(amountPanel);

            // Quick Amount Buttons Panel
            var quickAmountPanel = new UniformGrid 
            { 
                Rows = 1,
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            void CreateQuickAmountButton(decimal amount)
            {
                var btn = new Button
                {
                    Content = amount.ToString("N0"),
                    Style = (Style)Application.Current.Resources["MaterialDesignOutlinedButton"],
                    Margin = new Thickness(4, 0, 4, 0),
                    Height = 36,
                    Width = 100,
                    FontSize = 14
                };
                btn.Click += (s, e) => 
                {
                    txtAmountTendered.Text = amount.ToString("F2");
                    txtAmountTendered.Focus();
                    txtAmountTendered.SelectAll();
                };
                quickAmountPanel.Children.Add(btn);
            }

            CreateQuickAmountButton(500);
            CreateQuickAmountButton(1000);
            CreateQuickAmountButton(2000);

            Grid.SetRow(quickAmountPanel, 4);
            grid.Children.Add(quickAmountPanel);
            quickAmountPanel.Visibility = Visibility.Visible; // Make sure it's visible by default for cash payment

            // Due Date Section (initially hidden)
            var dueDatePanel = new StackPanel 
            { 
                Margin = new Thickness(0, 0, 0, 16),
                Visibility = Visibility.Collapsed
            };
            var dueDateLabel = new TextBlock
            {
                Text = Application.Current.TryFindResource("DueDate") as string ?? "Due Date",
                Style = (Style)Application.Current.Resources["MaterialDesignSubtitle1TextBlock"],
                Margin = new Thickness(0, 0, 0, 8)
            };
            var datePicker = new DatePicker
            {
                Style = (Style)Application.Current.Resources["MaterialDesignOutlinedDatePicker"],
                SelectedDate = DateTime.Now.AddDays(30)
            };
            dueDatePanel.Children.Add(dueDateLabel);
            dueDatePanel.Children.Add(datePicker);

            cashButton.Click += (s, e) => 
            {
                UpdatePaymentButtonStyles(cashButton);
                amountPanel.Visibility = Visibility.Visible;
                quickAmountPanel.Visibility = Visibility.Visible;
                dueDatePanel.Visibility = Visibility.Collapsed;
            };

            cardButton.Click += (s, e) => 
            {
                UpdatePaymentButtonStyles(cardButton);
                amountPanel.Visibility = Visibility.Visible;
                quickAmountPanel.Visibility = Visibility.Collapsed;
                dueDatePanel.Visibility = Visibility.Collapsed;
            };

            unpaidButton.Click += async (s, e) => 
            {
                if (ViewModel.SelectedCustomer == null || ViewModel.SelectedCustomer.Id == -1)
                {
                    DialogHost.Close("SalesDialog");
                    await ShowCustomerSelectionDialog();
                    
                    if (ViewModel.SelectedCustomer == null || ViewModel.SelectedCustomer.Id == -1)
                    {
                        UpdatePaymentButtonStyles(cashButton);
                        return;
                    }
                    await DialogHost.Show(grid, "SalesDialog");
                }
                UpdatePaymentButtonStyles(unpaidButton);
                amountPanel.Visibility = Visibility.Collapsed;
                quickAmountPanel.Visibility = Visibility.Collapsed;
                dueDatePanel.Visibility = Visibility.Visible;
            };

            paymentButtonsPanel.Children.Add(cashButton);
            paymentButtonsPanel.Children.Add(cardButton);
            paymentButtonsPanel.Children.Add(unpaidButton);

            paymentMethodPanel.Children.Add(paymentMethodLabel);
            paymentMethodPanel.Children.Add(paymentButtonsPanel);
            paymentMethodPanel.Children.Add(dueDatePanel);

            Grid.SetRow(paymentMethodPanel, 2);
            grid.Children.Add(paymentMethodPanel);

            // Event handlers
            txtAmountTendered.TextChanged += (s, e) =>
            {
                if (decimal.TryParse(txtAmountTendered.Text, out decimal tendered))
                {
                    decimal change = tendered - ViewModel.GrandTotal;
                    txtChange.Text = change >= 0 ? change.ToString("C2") : 
                        Application.Current.TryFindResource("InsufficientAmount") as string ?? "Insufficient amount";
                    txtChange.Foreground = change >= 0 ? Brushes.Green : Brushes.Red;
                }
                else
                {
                    txtChange.Text = string.Empty;
                }
            };

            // Action Buttons Panel - More compact
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 8, 0, 0),
                FlowDirection = Application.Current.MainWindow?.FlowDirection ?? FlowDirection.LeftToRight
            };
            Grid.SetRow(buttonsPanel, 5);

            var cancelButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Content = Application.Current.TryFindResource("Cancel") as string ?? "Cancel",
                Margin = new Thickness(0, 0, 8, 0)
            };
            cancelButton.Click += (s, e) =>
            {
                DialogHost.Close("SalesDialog", null);
            };

            var processButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Content = Application.Current.TryFindResource("ProcessPaymentButton") as string ?? "Process Payment"
            };
            processButton.Click += async (s, e) =>
            {
                if (selectedPaymentMethod == "Unpaid")
                {
                    if (datePicker.SelectedDate == null)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("DueDateRequired") as string ?? 
                                "Due date is required for unpaid sales.",
                            Application.Current.TryFindResource("InvalidInput") as string ?? "Invalid Input",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    if (await ViewModel.ProcessPayment(selectedPaymentMethod, 0, datePicker.SelectedDate))
                    {
                        DialogHost.Close("SalesDialog", true);
                    }
                }
                else if (decimal.TryParse(txtAmountTendered.Text, out decimal amountTendered))
                {
                    if (amountTendered >= ViewModel.GrandTotal)
                    {
                        if (await ViewModel.ProcessPayment(selectedPaymentMethod, amountTendered))
                        {
                            DialogHost.Close("SalesDialog", true);
                        }
                    }
                    else
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("AmountTenderedMustBeGreater") as string ?? 
                                "Amount tendered must be greater than or equal to the total amount.",
                            Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("PleaseEnterValidAmount") as string ?? 
                            "Please enter a valid amount.",
                        Application.Current.TryFindResource("InvalidAmount") as string ?? "Invalid Amount",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            };

            buttonsPanel.Children.Add(cancelButton);
            buttonsPanel.Children.Add(processButton);
            grid.Children.Add(buttonsPanel);

            var result = await DialogHost.Show(grid, "SalesDialog");
            if (result is bool success && success)
            {
                // Cart is already cleared in ViewModel.ProcessPayment
                // No need to create a new cart automatically
            }
        }

        private async void ProductsScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            if (_isLoadingMore) return;

            try
            {
                ScrollViewer scrollViewer;
                if (sender is ScrollViewer sv)
                {
                    scrollViewer = sv;
                }
                else if (sender is ListBox listBox)
                {
                    scrollViewer = GetScrollViewer(listBox);
                    if (scrollViewer == null) return;
                }
                else return;

                // Check if we're near the bottom (80% of scroll)
                if (scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight * 0.8)
                {
                    _isLoadingMore = true;
                    
                    try
                    {
                        // Load more products in batches
                        await Dispatcher.InvokeAsync(
                            async () => await ViewModel.LoadMoreProducts(),
                            DispatcherPriority.Background
                        );
                    }
                    finally
                    {
                        _isLoadingMore = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading more products: {ex.Message}");
                _isLoadingMore = false;
            }
        }

        private ScrollViewer GetScrollViewer(DependencyObject element)
        {
            if (element == null) return null;
            
            var scrollViewer = element as ScrollViewer;
            if (scrollViewer != null) return scrollViewer;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                var child = VisualTreeHelper.GetChild(element, i);
                var result = GetScrollViewer(child);
                if (result != null) return result;
            }
            
            return null;
        }

        public async void ShowLoyaltyPointsRedemptionDialog()
        {
            try
            {
                Debug.WriteLine("Debug: Creating loyalty points redemption dialog");
                
                var content = new Grid { Margin = new Thickness(32) };
                content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                content.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var titleBlock = new TextBlock
                {
                    Text = Application.Current.TryFindResource("RedeemPoints") as string ?? "Redeem Points",
                    Style = (Style)Application.Current.Resources["MaterialDesignHeadline6TextBlock"],
                    Margin = new Thickness(0, 0, 0, 16)
                };
                Grid.SetRow(titleBlock, 0);
                content.Children.Add(titleBlock);

                // Add points available information
                var pointsInfoBlock = new TextBlock
                {
                    Text = $"Available Points: {ViewModel.CustomerLoyaltyPoints:N0}",
                    Style = (Style)Application.Current.Resources["MaterialDesignBody1TextBlock"],
                    Margin = new Thickness(0, 0, 0, 16),
                    Foreground = (Brush)Application.Current.Resources["PrimaryHueMidBrush"]
                };
                Grid.SetRow(pointsInfoBlock, 1);
                content.Children.Add(pointsInfoBlock);

                var pointsInput = new TextBox
                {
                    Style = (Style)Application.Current.Resources["MaterialDesignOutlinedTextBox"],
                    Margin = new Thickness(0, 8, 0, 16),
                    Text = "0",
                    Name = "PointsInput"
                };

                // Add PreviewTextInput event handler
                pointsInput.PreviewTextInput += (sender, e) =>
                {
                    // Only allow digits and one decimal point
                    if (!char.IsDigit(e.Text[0]) && e.Text[0] != '.')
                    {
                        e.Handled = true;
                        return;
                    }

                    if (e.Text[0] == '.' && ((TextBox)sender).Text.Contains('.'))
                    {
                        e.Handled = true;
                    }
                };

                // Add TextChanged event handler
                pointsInput.TextChanged += (sender, e) =>
                {
                    var textBox = (TextBox)sender;
                    if (decimal.TryParse(textBox.Text, out decimal value))
                    {
                        if (value > ViewModel.CustomerLoyaltyPoints)
                        {
                            textBox.Text = ViewModel.CustomerLoyaltyPoints.ToString();
                            textBox.SelectionStart = textBox.Text.Length;
                        }
                    }
                };

                Grid.SetRow(pointsInput, 2);
                content.Children.Add(pointsInput);

                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetRow(buttonPanel, 3);

                var cancelButton = new Button
                {
                    Content = Application.Current.TryFindResource("Cancel") as string ?? "Cancel",
                    Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                    Margin = new Thickness(0, 0, 8, 0),
                    Command = DialogHost.CloseDialogCommand,
                    CommandParameter = false
                };

                var redeemButton = new Button
                {
                    Content = Application.Current.TryFindResource("Redeem") as string ?? "Redeem",
                    Style = (Style)Application.Current.Resources["MaterialDesignFlatButton"],
                    IsDefault = true
                };

                buttonPanel.Children.Add(cancelButton);
                buttonPanel.Children.Add(redeemButton);
                content.Children.Add(buttonPanel);

                // Handle the redeem button click
                redeemButton.Click += (s, e) =>
                {
                    Debug.WriteLine("Debug: Redeem button clicked");
                    try
                    {
                        string pointsText = pointsInput.Text?.Trim() ?? "0";
                        Debug.WriteLine($"Debug: Attempting to parse points value: {pointsText}");

                        if (string.IsNullOrWhiteSpace(pointsText) || !decimal.TryParse(pointsText, out decimal enteredPoints))
                        {
                            MessageBox.Show("Please enter a valid number of points.", "Invalid Input",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        Debug.WriteLine($"Debug: Parsed points value: {enteredPoints}");

                        var loyaltyProgram = ViewModel.GetActiveLoyaltyProgram();
                        if (loyaltyProgram == null)
                        {
                            MessageBox.Show("Loyalty program is not configured.", "Configuration Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        if (enteredPoints <= 0)
                        {
                            MessageBox.Show("Please enter a number greater than zero.", "Invalid Points",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        if (enteredPoints > ViewModel.CustomerLoyaltyPoints)
                        {
                            MessageBox.Show($"You can only redeem up to {ViewModel.CustomerLoyaltyPoints:N0} points.", "Invalid Points",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        decimal initialDiscountValue = enteredPoints * loyaltyProgram.MonetaryValuePerPoint;
                        Debug.WriteLine($"Debug: Calculated discount value: {initialDiscountValue}");

                        if (initialDiscountValue > ViewModel.CurrentCart.GrandTotal)
                        {
                            MessageBox.Show($"The discount value ({initialDiscountValue:C2}) cannot exceed the total amount ({ViewModel.CurrentCart.GrandTotal:C2}).", 
                                "Invalid Points",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        // Store the points and discount value for redemption after payment
                        ViewModel.CurrentCart.PendingLoyaltyPoints = enteredPoints;
                        ViewModel.CurrentCart.PendingLoyaltyDiscount = initialDiscountValue;

                        // Apply the discount to the cart
                        ViewModel.ApplyLoyaltyDiscount(initialDiscountValue);
                        Debug.WriteLine("Debug: Points applied successfully");

                        // Close the dialog with success using the command
                        DialogHost.CloseDialogCommand.Execute(true, redeemButton);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Debug: Error in redeem button click handler - {ex}");
                        MessageBox.Show($"An error occurred while redeeming points: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                };

                // Set initial focus to the points input
                pointsInput.Loaded += (s, e) =>
                {
                    pointsInput.Focus();
                    pointsInput.SelectAll();
                };

                Debug.WriteLine("Debug: About to show dialog");
                var result = await DialogHost.Show(content, "SalesDialog");
                Debug.WriteLine($"Debug: Dialog closed with result: {result}");

                if (result is bool success && !success)
                {
                    // User cancelled - reset any pending loyalty points/discount
                    ViewModel.CurrentCart.PendingLoyaltyPoints = 0;
                    ViewModel.CurrentCart.PendingLoyaltyDiscount = 0;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Debug: Error in ShowLoyaltyPointsRedemptionDialog - {ex}");
                MessageBox.Show($"An error occurred: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateThemeSelectionButton()
        {
            // Create a button to open theme selection
            var themeButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignIconButton"),
                ToolTip = Application.Current.FindResource("ChangeLayout") as string,
                Margin = new Thickness(8),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Width = 36,
                Height = 36
            };
            
            themeButton.Content = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.ViewDashboard,
                Width = 24,
                Height = 24
            };
            
            themeButton.Click += ThemeButton_Click;
            
            // Add to the main grid - using the correct way to access the Grid inside DialogHost
            if (this.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost && 
                dialogHost.Content is Grid mainGrid)
            {
                Grid.SetColumn(themeButton, 0);
                Grid.SetRow(themeButton, 0);
                mainGrid.Children.Add(themeButton);
            }
        }

        private async void ThemeButton_Click(object sender, RoutedEventArgs e)
        {
            await ShowThemeSelectionDialog();
        }

        private async Task ShowThemeSelectionDialog()
        {
            var view = new StackPanel { Margin = new Thickness(16) };
            
            // Create title
            var title = new TextBlock
            {
                Text = Application.Current.FindResource("SelectLayout") as string,
                Style = (Style)FindResource("MaterialDesignHeadline6TextBlock"),
                Margin = new Thickness(0, 0, 0, 16)
            };
            view.Children.Add(title);
            
            // Current layout theme
            var currentTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";
            
            // Create layout options
            var themes = new[]
            {
                new { Code = "Standard", Name = Application.Current.FindResource("StandardLayout") as string, Description = "Default sales layout with product cards and cart" },
                new { Code = "Compact", Name = Application.Current.FindResource("CompactLayout") as string, Description = "Compact layout with smaller product cards and simplified cart" },
                new { Code = "Modern", Name = Application.Current.FindResource("ModernLayout") as string, Description = "Modern layout with large product images and enhanced visual elements" },
                new { Code = "Grid", Name = Application.Current.FindResource("GridLayout") as string, Description = "Grid-based layout with tabular product listing and detailed information" }
            };
            
            foreach (var theme in themes)
            {
                var isSelected = theme.Code == currentTheme;
                
                var card = new MaterialDesignThemes.Wpf.Card
                {
                    Margin = new Thickness(0, 0, 0, 8),
                    Padding = new Thickness(16, 12, 16, 12),
                    Background = isSelected 
                        ? (SolidColorBrush)FindResource("PrimaryHueLightBrush") 
                        : (SolidColorBrush)FindResource("MaterialDesignCardBackground"),
                    UniformCornerRadius = 4
                };
                
                var panel = new StackPanel();
                
                var header = new TextBlock
                {
                    Text = theme.Name,
                    Style = (Style)FindResource("MaterialDesignSubtitle1TextBlock"),
                    FontWeight = isSelected ? FontWeights.Bold : FontWeights.Normal
                };
                
                var description = new TextBlock
                {
                    Text = theme.Description,
                    Style = (Style)FindResource("MaterialDesignBody2TextBlock"),
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.8,
                    Margin = new Thickness(0, 4, 0, 0)
                };
                
                panel.Children.Add(header);
                panel.Children.Add(description);
                
                card.Content = panel;
                
                // Make the card clickable
                card.Tag = theme.Code;
                var cardBorder = new Border();
                cardBorder.Child = card;
                cardBorder.MouseDown += ThemeCard_MouseDown;
                cardBorder.Cursor = Cursors.Hand;
                
                view.Children.Add(cardBorder);
            }
            
            // Add note
            var note = new TextBlock
            {
                Text = Application.Current.FindResource("LayoutChangeRestart") as string,
                Style = (Style)FindResource("MaterialDesignBody2TextBlock"),
                Foreground = (SolidColorBrush)FindResource("MaterialDesignBodyLight"),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 16, 0, 0)
            };
            view.Children.Add(note);
            
            var dialog = new MaterialDesignThemes.Wpf.DialogHost();
            
            var result = await MaterialDesignThemes.Wpf.DialogHost.Show(view, "SalesDialog");
        }

        private void ThemeCard_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && 
                border.Child is MaterialDesignThemes.Wpf.Card card && 
                card.Tag is string themeCode)
            {
                // Save the selected theme
                _settingsService.SaveSetting("SalesLayoutTheme", themeCode);
                
                // Ask user if they want to restart
                var result = MessageBox.Show(
                    (Application.Current.FindResource("LayoutChangeRestart") as string) + " " +
                    (Application.Current.FindResource("RestartNow") as string),
                    (Application.Current.FindResource("ThemeChanged") as string),
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // Restart the application
                    System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    Application.Current.Shutdown();
                }
                
                // Close the dialog
                DialogHost.Close("SalesDialog");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Async customer loading to prevent UI blocking
        /// </summary>
        private async void LoadCustomersAsync(ListView customerList, TextBox searchBox)
        {
            List<Customer> allCustomers = null;

            try
            {
                // Show loading indicator
                customerList.ItemsSource = new[] { new { FirstName = "Loading...", LastName = "", Email = "", Phone = "", LoyaltyCode = "" } };

                // Load customers on background thread
                allCustomers = await Task.Run(() =>
                {
                    var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as IDatabaseService ?? new DatabaseService();
                    return dbService.GetAllCustomers()
                        .Where(c => c.Id != -1) // Exclude "No Customer" option
                        .ToList();
                });

                // Update UI on main thread
                customerList.ItemsSource = allCustomers;

                // Set up search functionality with the loaded customers
                searchBox.TextChanged += (s, args) =>
                {
                    if (allCustomers == null) return;

                    var searchText = searchBox.Text.ToLower();
                    var filtered = allCustomers.Where(c =>
                        $"{c.FirstName} {c.LastName}".ToLower().Contains(searchText) ||
                        c.Email?.ToLower().Contains(searchText) == true ||
                        c.Phone?.ToLower().Contains(searchText) == true ||
                        c.LoyaltyCode?.ToLower() == searchText // Add exact match for loyalty code
                    );
                    customerList.ItemsSource = filtered;
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
                customerList.ItemsSource = new List<Customer>();
            }
        }

        private async void ProcessBarcodeAsync(string barcode)
        {
            try
            {
                // Use the enhanced barcode lookup method
                var product = await ViewModel.GetProductByBarcodeWithExternalLookupAsync(barcode);

                if (product != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Found product by barcode: {product.Name}");

                    // ✅ BARCODE SCANNER FIX: Get fresh product data from the same source as product cards
                    // This ensures the barcode scanner uses the same complete product data with accurate stock information
                    var freshProduct = ViewModel.FilteredProducts?.FirstOrDefault(p => p.Id == product.Id);
                    if (freshProduct != null)
                    {
                        product = freshProduct; // Use the fresh product data with complete batch information
                        System.Diagnostics.Debug.WriteLine($"[CART DEBUG] Using fresh product data for: {product.Name}");
                    }

                    // ✅ BARCODE SCANNER FIX: Use AddToCartCommand for consistency with product cards
                    // Remove stock validation here - let AddToCartCommand handle it the same way as product cards
                    if (ViewModel.AddToCartCommand.CanExecute(product))
                    {
                        ViewModel.AddToCartCommand.Execute(product);
                    }
                    txtSearch.Clear(); // Clear the search box after adding
                }
                else
                {
                    MessageBox.Show("Product not found", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing barcode: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Adds reserved stock to a product and creates inventory batch if needed
        /// </summary>
        private async Task AddReservedStockToProduct(Product product, decimal quantity,
            POSSystem.Services.InventoryManagement.IStockService stockService,
            DatabaseService dbService, AuthenticationService authService)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Adding {quantity} reserved stock to product {product.Name} (ID: {product.Id})");

                var currentUser = authService.CurrentUser;
                if (currentUser == null)
                {
                    throw new InvalidOperationException("User not authenticated");
                }

                // Get the most recent batch information for batch-tracked products
                BatchStock mostRecentBatch = null;
                if (product.TrackBatches)
                {
                    var batches = dbService.GetBatchesForProduct(product.Id);
                    mostRecentBatch = batches?.OrderByDescending(b => b.CreatedAt).FirstOrDefault();
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Product tracks batches. Found {batches?.Count ?? 0} existing batches");
                }

                // Create new batch entry for the reserved stock
                if (product.TrackBatches)
                {
                    var reservationBatch = new BatchStock
                    {
                        ProductId = product.Id,
                        BatchNumber = $"RESERVE-{DateTime.Now:yyyyMMddHHmmss}",
                        Quantity = quantity,
                        ManufactureDate = DateTime.Now,
                        ExpiryDate = mostRecentBatch?.ExpiryDate, // Use expiry from most recent batch if available
                        PurchasePrice = mostRecentBatch?.PurchasePrice ?? 0m, // Use purchase price from most recent batch
                        SellingPrice = mostRecentBatch?.SellingPrice ?? product.SellingPrice,
                        Location = "Reserved Stock",
                        Notes = $"Stock reserved for out-of-stock product. Based on batch: {mostRecentBatch?.BatchNumber ?? "N/A"}",
                        CreatedAt = DateTime.Now
                    };

                    dbService.AddBatchStock(reservationBatch);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created reservation batch: {reservationBatch.BatchNumber}");
                }
                else
                {
                    // For non-batch tracked products, use the stock service to increase stock
                    stockService.IncreaseStock(product.Id, quantity, $"Stock reservation for out-of-stock product", null);
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Increased stock for non-batch product by {quantity}");
                }

                // Create inventory transaction for tracking
                var transaction = new InventoryTransaction
                {
                    ProductId = product.Id,
                    TransactionType = "Stock Reservation",
                    Quantity = (int)Math.Ceiling(quantity), // Convert decimal to int, rounding up
                    TransactionDate = DateTime.Now,
                    UserId = currentUser.Id,
                    Notes = $"Stock reserved for out-of-stock product: {product.Name}. Reserved stock added via reserve invoice workflow."
                };

                dbService.AddInventoryTransaction(transaction);
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created inventory transaction for tracking");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error adding reserved stock: {ex.Message}");
                throw new Exception($"Failed to add reserved stock: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Refreshes the product display to show updated stock information
        /// </summary>
        private async Task RefreshProductDisplay(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Refreshing display for product ID: {productId}");

                // Get the updated product from database
                var dbService = App.ServiceProvider?.GetService(typeof(IDatabaseService)) as DatabaseService;
                if (dbService == null) return;

                var updatedProduct = dbService.GetProductById(productId);
                if (updatedProduct == null) return;

                // Update the product in the ViewModel's collection if it exists
                var viewModel = DataContext as SaleViewModel;
                if (viewModel != null)
                {
                    // Find and update the product in the collections
                    var productInAllProducts = viewModel.AllProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInAllProducts != null)
                    {
                        // Update stock quantity and other properties
                        productInAllProducts.StockQuantity = updatedProduct.StockQuantity;
                        System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Updated product stock to: {updatedProduct.StockQuantity}");
                    }

                    var productInFilteredProducts = viewModel.FilteredProducts?.FirstOrDefault(p => p.Id == productId);
                    if (productInFilteredProducts != null)
                    {
                        productInFilteredProducts.StockQuantity = updatedProduct.StockQuantity;
                    }

                    // Trigger UI refresh by refreshing the view model's data
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // Call the view model's refresh method if available
                        if (viewModel.RefreshProductsCommand?.CanExecute(null) == true)
                        {
                            viewModel.RefreshProductsCommand.Execute(null);
                        }
                        else
                        {
                            // Alternative: Force a UI refresh by invalidating commands
                            CommandManager.InvalidateRequerySuggested();
                        }
                    });
                }

                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Product display refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCT_REFRESH] Error refreshing product display: {ex.Message}");
            }
        }
    }
}
