﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.ProductsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <converters:PrimaryBarcodeConverter x:Key="PrimaryBarcodeConverter"/>
        <converters:StockQuantityConverter x:Key="StockQuantityConverter"/>
        <converters:UnitOfMeasureNameConverter x:Key="UnitOfMeasureNameConverter"/>
        <converters:StockDisplayConverter x:Key="StockDisplayConverter"/>
    </UserControl.Resources>

    <!-- Add DialogHost wrapper -->
    <md:DialogHost Identifier="RootDialog">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Section with Statistics and Actions -->
            <md:Card Grid.Row="0" Margin="0,0,0,16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Statistics Cards -->
                    <UniformGrid Grid.Row="0" Rows="1" Margin="16,16,16,8">
                        <!-- Total Products Card -->
                        <md:Card Margin="8" md:ElevationAssist.Elevation="Dp1">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <TextBlock Text="{DynamicResource TotalProducts}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.6"/>
                                <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0">
                                    <md:PackIcon Kind="Package" 
                                            Width="24" Height="24"
                                            Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    <TextBlock Text="{Binding TotalProducts}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Margin="8,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </md:Card>

                        <!-- Low Stock Card -->
                        <md:Card Margin="8" md:ElevationAssist.Elevation="Dp1">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <TextBlock Text="{DynamicResource LowStock}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.6"/>
                                <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0">
                                    <md:PackIcon Kind="AlertCircle" 
                                            Width="24" Height="24"
                                            Foreground="#ff9800"/>
                                    <TextBlock Text="{Binding LowStockCount}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Margin="8,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </md:Card>

                        <!-- Out of Stock Card -->
                        <md:Card Margin="8" md:ElevationAssist.Elevation="Dp1">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <TextBlock Text="{DynamicResource OutOfStock}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.6"/>
                                <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0">
                                    <md:PackIcon Kind="AlertOctagon" 
                                            Width="24" Height="24"
                                            Foreground="#f44336"/>
                                    <TextBlock Text="{Binding OutOfStockCount}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Margin="8,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </md:Card>

                        <!-- Inventory Value Card -->
                        <md:Card Margin="8" md:ElevationAssist.Elevation="Dp1">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <TextBlock Text="{DynamicResource InventoryValue}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.6"/>
                                <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0">
                                    <md:PackIcon Kind="Cash" 
                                            Width="24" Height="24"
                                            Foreground="#4caf50"/>
                                    <TextBlock Text="{Binding InventoryValue, StringFormat={}{0:N2} DA}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Margin="8,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </md:Card>

                        <!-- Inventory Cost Card -->
                        <md:Card Margin="8" md:ElevationAssist.Elevation="Dp1">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <TextBlock Text="{DynamicResource InventoryCost}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.6"/>
                                <StackPanel Grid.Row="1" 
                                      Orientation="Horizontal" 
                                      Margin="0,8,0,0">
                                    <md:PackIcon Kind="Cash" 
                                            Width="24" Height="24"
                                            Foreground="#2196f3"/>
                                    <TextBlock Text="{Binding InventoryCost, StringFormat={}{0:N2} DA}"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Margin="8,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </md:Card>
                    </UniformGrid>

                    <!-- Search and Filters -->
                    <Grid Grid.Row="1" Margin="16,8,16,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                            x:Name="txtSearch"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            md:HintAssist.Hint="{DynamicResource ProductSearchHint}"
                            KeyDown="SearchBox_KeyDown"
                            TextChanged="SearchBox_TextChanged"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,16,0"/>

                        <ComboBox Grid.Column="1"
                             x:Name="CategoryFilter"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"
                             Width="200"
                             Margin="0,0,16,0"
                             md:HintAssist.Hint="{DynamicResource Category}"
                             ItemsSource="{Binding Categories}"
                             DisplayMemberPath="Name"
                             SelectedItem="{Binding SelectedCategory}"
                             SelectionChanged="CategoryFilter_SelectionChanged"/>

                        <ComboBox Grid.Column="2"
                             x:Name="StockFilter"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"
                             Width="150"
                             Margin="0,0,16,0"
                             md:HintAssist.Hint="{DynamicResource Status}"
                             SelectedValuePath="Tag"
                             DisplayMemberPath="Content"
                             SelectedValue="{Binding SelectedStockFilter, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                             SelectionChanged="StockFilter_SelectionChanged">
                            <ComboBoxItem Content="{DynamicResource AllStock}" Tag="All"/>
                            <ComboBoxItem Content="{DynamicResource LowStock}" Tag="LowStock"/>
                            <ComboBoxItem Content="{DynamicResource OutOfStock}" Tag="OutOfStock"/>
                        </ComboBox>

                        <Button Grid.Column="3"
                                Command="{Binding ClearFiltersCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                ToolTip="{DynamicResource ClearFilters}"
                                Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FilterRemove" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource ClearFilters}"/>
                            </StackPanel>
                        </Button>

                        <!-- Refresh Button -->
                        <Button Grid.Column="4"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="RefreshProducts_Click"
                                ToolTip="{DynamicResource Refresh}"
                                Margin="8,0,16,0">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Refresh"
                                        Width="20" Height="20"
                                        Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Refresh}"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="5"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="PrintBarcodes_Click"
                                Margin="0,0,16,0"
                                ToolTip="{DynamicResource PrintBarcodes}">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Barcode"
                                        Width="20" Height="20"
                                        Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource PrintBarcodes}"/>
                            </StackPanel>
                        </Button>

                        <!-- Add New Product Button -->
                        <Button Grid.Column="7"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="{DynamicResource MaterialDesignPaper}"
                                Click="AddNewProduct_Click"
                                ToolTip="{DynamicResource AddProduct}">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="Plus"
                                           Width="20" Height="20"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource AddProduct}"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </md:Card>

            <!-- Main Content Area (Products List Only) -->
            <md:Card Grid.Row="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Loading Indicator -->
                    <md:Card Grid.Row="0"
                            Background="{DynamicResource MaterialDesignCardBackground}"
                            Opacity="0.9"
                            Panel.ZIndex="1000"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center" 
                                  VerticalAlignment="Center">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       IsIndeterminate="True"
                                       Value="0"
                                       Width="24"
                                       Height="24"/>
                            <TextBlock Text="{DynamicResource LoadingProducts}"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     Margin="0,8,0,0"/>
                        </StackPanel>
                    </md:Card>

                    <!-- Products DataGrid -->
                    <DataGrid Grid.Row="0"
                             ItemsSource="{Binding ProductsView}"
                             Style="{StaticResource AppDataGridStyle}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             SelectionMode="Single"
                             IsReadOnly="True"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             VirtualizingStackPanel.VirtualizationMode="Recycling"
                             VirtualizingStackPanel.IsVirtualizing="True"
                             VirtualizingStackPanel.IsVirtualizingWhenGrouping="True"
                             VirtualizingStackPanel.CacheLength="5"
                             VirtualizingStackPanel.CacheLengthUnit="Page"
                             ScrollViewer.IsDeferredScrollingEnabled="True"
                             ScrollViewer.CanContentScroll="True"
                             Background="Transparent"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             BorderBrush="{DynamicResource MaterialDesignDivider}"
                             RowBackground="{DynamicResource MaterialDesignPaper}"
                             AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                             GridLinesVisibility="None"
                             RenderOptions.CachingHint="Cache"
                             Margin="1">
                        <DataGrid.Resources>
                            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                                           Color="#2196F3"/>
                            <SolidColorBrush x:Key="{x:Static SystemColors.ControlBrushKey}" 
                                           Color="Transparent"/>
                            <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" 
                                           Color="White"/>
                        </DataGrid.Resources>
                        <DataGrid.CacheMode>
                            <BitmapCache EnableClearType="True" 
                                       SnapsToDevicePixels="True" 
                                       RenderAtScale="1.0" />
                        </DataGrid.CacheMode>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="{DynamicResource ProductName}" 
                                              Binding="{Binding Name}" 
                                              Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="{DynamicResource Barcode}" 
                                                  Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Barcodes, Converter={StaticResource PrimaryBarcodeConverter}}"
                                                 TextTrimming="CharacterEllipsis"
                                                 Foreground="{DynamicResource MaterialDesignBody}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="{DynamicResource Stock}" 
                                              Binding="{Binding ., Converter={StaticResource StockQuantityConverter}}"
                                              Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource Price}"
                                              Binding="{Binding CurrentBatchPrice, StringFormat={}{0:N2} DA}"
                                              Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="{DynamicResource Status}" 
                                                  Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="Circle" 
                                                        Width="14" Height="14"
                                                        Margin="0,0,4,0"
                                                        Foreground="{Binding IsActive, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='#4CAF50|#9E9E9E'}"/>
                                            <TextBlock Text="{Binding IsActive, Converter={StaticResource BooleanToStatusConverter}}"
                                                     Foreground="{DynamicResource MaterialDesignBody}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    ToolTip="{DynamicResource EditProduct}"
                                                    Click="EditProduct_Click"
                                                    Margin="0,0,5,0"
                                                    Foreground="{DynamicResource MaterialDesignBody}">
                                                <md:PackIcon Kind="Edit" />
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    ToolTip="{DynamicResource DeleteProduct}"
                                                    Click="DeleteProduct_Click"
                                                    Margin="0,0,5,0"
                                                    Foreground="{DynamicResource MaterialDesignBody}">
                                                <md:PackIcon Kind="Delete" />
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    ToolTip="{DynamicResource ManageBatches}"
                                                    Click="ManageBatches_Click"
                                                    Foreground="{DynamicResource MaterialDesignBody}">
                                                <md:PackIcon Kind="Package" />
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Pagination Controls -->
                    <StackPanel Grid.Row="1" 
                              Orientation="Horizontal" 
                              HorizontalAlignment="Center"
                              Margin="16">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding FirstPageCommand}"
                                IsEnabled="{Binding CanGoToPreviousPage}"
                                Foreground="{DynamicResource MaterialDesignBody}">
                            <md:PackIcon Kind="PageFirst"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding PreviousPageCommand}"
                                IsEnabled="{Binding CanGoToPreviousPage}"
                                Margin="8,0"
                                Foreground="{DynamicResource MaterialDesignBody}">
                            <md:PackIcon Kind="ChevronLeft"/>
                        </Button>
                        <TextBlock VerticalAlignment="Center" 
                                 Margin="16,0"
                                 Foreground="{DynamicResource MaterialDesignBody}">
                            <Run Text="{Binding CurrentPage}"/>
                            <Run Text="/"/>
                            <Run Text="{Binding TotalPages}"/>
                        </TextBlock>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding NextPageCommand}"
                                IsEnabled="{Binding CanGoToNextPage}"
                                Margin="8,0"
                                Foreground="{DynamicResource MaterialDesignBody}">
                            <md:PackIcon Kind="ChevronRight"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding LastPageCommand}"
                                IsEnabled="{Binding CanGoToNextPage}"
                                Foreground="{DynamicResource MaterialDesignBody}">
                            <md:PackIcon Kind="PageLast"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </md:Card>
        </Grid>
    </md:DialogHost>
</UserControl>