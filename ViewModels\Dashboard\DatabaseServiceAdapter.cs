using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.ViewModels.Dashboard
{
    /// <summary>
    /// Adapter class that implements IDashboardDataProvider interface
    /// using the existing DatabaseService.
    /// </summary>
    public class DatabaseServiceAdapter : IDashboardDataProvider
    {
        private readonly DatabaseService _databaseService;
        
        public DatabaseServiceAdapter(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
        }
        
        /// <summary>
        /// Gets sales by date range adapting to existing database methods
        /// </summary>
        public async Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            // Using the correct method name found in DatabaseService
            return _databaseService.GetSalesByDateRange(startDate, endDate);
        }
        
        /// <summary>
        /// Alias for GetSalesByDateRangeAsync to provide a consistent interface
        /// </summary>
        public async Task<List<Sale>> GetSalesAsync(DateTime startDate, DateTime endDate)
        {
            return await _databaseService.GetSalesForPeriodAsync(startDate, endDate);
        }
        
        /// <summary>
        /// Gets dashboard alerts by querying various alert-related data
        /// </summary>
        public async Task<DashboardAlertData> GetDashboardAlertsAsync()
        {
            var result = new DashboardAlertData();

            // ✅ Convert to fully async operations
            result.LowStockProducts = await _databaseService.GetLowStockProductsAsync();
            result.ExpiringProducts = await GetExpiringProductsAsync(30);

            return result;
        }
        
        /// <summary>
        /// Gets top selling products with their sales data
        /// </summary>
        public async Task<List<Product>> GetTopSellingProductsAsync(int count)
        {
            return await _databaseService.GetTopSellingProductsAsync(count);
        }
        
        /// <summary>
        /// Gets top selling products with their sales data for a specific date range
        /// </summary>
        public async Task<List<Product>> GetTopSellingProductsAsync(int count, DateTime startDate, DateTime endDate)
        {
            // Get products from database service using date range
            List<Product> products;

            try
            {
                // Use the new date-filtered method
                products = await _databaseService.GetTopSellingProductsForPeriodAsync(count, startDate, endDate);
                System.Diagnostics.Debug.WriteLine($"Using date-filtered product retrieval for period {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                System.Diagnostics.Debug.WriteLine($"Retrieved {products?.Count ?? 0} products with date filtering");
            }
            catch (Exception ex)
            {
                // Fall back to the standard method if there's an error
                System.Diagnostics.Debug.WriteLine($"Error using date-filtered product retrieval: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Falling back to standard product retrieval");
                products = await _databaseService.GetTopSellingProductsAsync(count);
                System.Diagnostics.Debug.WriteLine($"Retrieved {products?.Count ?? 0} products with fallback method");
            }
            
            // Ensure all products have a valid name
            foreach (var product in products)
            {
                if (string.IsNullOrWhiteSpace(product.Name))
                {
                    product.Name = "Unnamed Product";
                    System.Diagnostics.Debug.WriteLine($"WARNING: Fixed null or empty name for product ID: {product.Id}");
                }
            }
            
            return products;
        }
        
        /// <summary>
        /// Gets a category by ID
        /// </summary>
        public Category GetCategoryById(int categoryId)
        {
            // Handle null case by returning null
            if (categoryId <= 0)
            {
                return null;
            }
            
            return _databaseService.GetCategoryById(categoryId);
        }

        /// <summary>
        /// Gets customer data for demographics reporting
        /// </summary>
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            return await _databaseService.GetAllCustomersAsync();
        }

        /// <summary>
        /// Gets sales data for a specific date range for customer demographics
        /// </summary>
        public async Task<List<Sale>> GetCustomerSalesAsync(int customerId, DateTime startDate, DateTime endDate)
        {
            return await Task.Run(() => 
                _databaseService.GetSalesByDateRange(startDate, endDate)
                    .Where(s => s.CustomerId == customerId)
                    .ToList()
            );
        }

        /// <summary>
        /// Gets business expenses for a specified date range
        /// </summary>
        public async Task<List<BusinessExpense>> GetBusinessExpensesAsync(DateTime startDate, DateTime endDate)
        {
            // Use the database service to get real expense data from the database
            return await _databaseService.GetBusinessExpensesForPeriodAsync(startDate, endDate);
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            // ✅ Use proper async method if available, otherwise use Task.Run for legacy compatibility
            return await Task.Run(() => _databaseService.GetAllUsers());
        }

        public async Task<List<Product>> GetExpiringProductsAsync(int daysThreshold)
        {
            // ✅ Convert to async operation
            return await Task.Run(() => _databaseService.GetExpiringProducts(daysThreshold));
        }

        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            return await _databaseService.GetLowStockProductsAsync();
        }

        public async Task<List<PurchaseOrder>> GetUnpaidPurchaseOrdersAsync()
        {
            // ✅ Convert to async operation
            return await Task.Run(() => _databaseService.GetUnpaidPurchaseOrders());
        }

        public async Task<List<Sale>> GetUnpaidSalesAsync()
        {
            // ✅ Use existing async method if available
            try
            {
                return await _databaseService.GetUnpaidSalesAsync();
            }
            catch
            {
                // Fallback to synchronous wrapped in Task.Run
                return await Task.Run(() => _databaseService.GetUnpaidSales());
            }
        }

        public async Task<List<Customer>> GetCustomersAsync()
        {
            // ✅ Use proper async method if available, otherwise use Task.Run for legacy compatibility
            return await Task.Run(() => _databaseService.GetAllCustomers());
        }

        // Legacy synchronous methods for backward compatibility
        public List<Product> GetExpiringProducts(int daysThreshold)
        {
            return _databaseService.GetExpiringProducts(daysThreshold);
        }

        public List<Sale> GetUnpaidSales()
        {
            return _databaseService.GetUnpaidSales();
        }
    }
}
