using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;

namespace POSSystem.Services.DatabaseHealth
{
    /// <summary>
    /// Service for monitoring database connection health and performance
    /// </summary>
    public class DatabaseHealthService : BackgroundService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<DatabaseHealthService> _logger;
        private readonly Timer _healthCheckTimer;
        
        // Health metrics
        private DatabaseHealthMetrics _currentMetrics;
        private readonly object _metricsLock = new object();
        
        // Configuration
        private readonly TimeSpan _healthCheckInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _connectionTimeout = TimeSpan.FromSeconds(30);

        public DatabaseHealthService(POSDbContext context, ILogger<DatabaseHealthService> logger = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _currentMetrics = new DatabaseHealthMetrics();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger?.LogInformation("Database health monitoring started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformHealthCheckAsync();
                    await Task.Delay(_healthCheckInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error during database health check");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }

            _logger?.LogInformation("Database health monitoring stopped");
        }

        /// <summary>
        /// Perform comprehensive database health check
        /// </summary>
        private async Task PerformHealthCheckAsync()
        {
            var startTime = DateTime.UtcNow;
            var metrics = new DatabaseHealthMetrics
            {
                CheckTime = startTime
            };

            try
            {
                // Test basic connectivity
                await TestConnectivityAsync(metrics);
                
                // Test query performance
                await TestQueryPerformanceAsync(metrics);
                
                // Check database size and growth
                await CheckDatabaseSizeAsync(metrics);
                
                // Check for long-running queries
                await CheckLongRunningQueriesAsync(metrics);
                
                metrics.IsHealthy = true;
                metrics.OverallResponseTime = DateTime.UtcNow - startTime;
                
                _logger?.LogDebug("Database health check completed successfully in {Duration}ms", 
                    metrics.OverallResponseTime.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                metrics.IsHealthy = false;
                metrics.LastError = ex.Message;
                metrics.OverallResponseTime = DateTime.UtcNow - startTime;
                
                _logger?.LogWarning(ex, "Database health check failed");
            }

            // Update current metrics thread-safely
            lock (_metricsLock)
            {
                _currentMetrics = metrics;
            }
        }

        /// <summary>
        /// Test basic database connectivity
        /// </summary>
        private async Task TestConnectivityAsync(DatabaseHealthMetrics metrics)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Simple connectivity test
                await _context.Database.CanConnectAsync();
                metrics.ConnectivityResponseTime = DateTime.UtcNow - startTime;
                metrics.CanConnect = true;
                
                _logger?.LogDebug("Database connectivity test passed in {Duration}ms", 
                    metrics.ConnectivityResponseTime.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                metrics.ConnectivityResponseTime = DateTime.UtcNow - startTime;
                metrics.CanConnect = false;
                _logger?.LogError(ex, "Database connectivity test failed");
                throw;
            }
        }

        /// <summary>
        /// Test query performance with sample queries
        /// </summary>
        private async Task TestQueryPerformanceAsync(DatabaseHealthMetrics metrics)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Test simple count query
                var productCount = await _context.Products.CountAsync();
                var customerCount = await _context.Customers.CountAsync();
                var saleCount = await _context.Sales.CountAsync();
                
                metrics.QueryResponseTime = DateTime.UtcNow - startTime;
                metrics.RecordCounts = new Dictionary<string, int>
                {
                    ["Products"] = productCount,
                    ["Customers"] = customerCount,
                    ["Sales"] = saleCount
                };
                
                _logger?.LogDebug("Query performance test completed in {Duration}ms. Products: {Products}, Customers: {Customers}, Sales: {Sales}", 
                    metrics.QueryResponseTime.TotalMilliseconds, productCount, customerCount, saleCount);
            }
            catch (Exception ex)
            {
                metrics.QueryResponseTime = DateTime.UtcNow - startTime;
                _logger?.LogError(ex, "Query performance test failed");
                throw;
            }
        }

        /// <summary>
        /// Check database size and growth patterns
        /// </summary>
        private async Task CheckDatabaseSizeAsync(DatabaseHealthMetrics metrics)
        {
            try
            {
                // For SQLite, we can check the database file size
                var connectionString = _context.Database.GetConnectionString();
                if (connectionString?.Contains("Data Source=") == true)
                {
                    var dbPath = connectionString.Split("Data Source=")[1].Split(';')[0];
                    if (System.IO.File.Exists(dbPath))
                    {
                        var fileInfo = new System.IO.FileInfo(dbPath);
                        metrics.DatabaseSizeBytes = fileInfo.Length;
                        metrics.DatabaseSizeMB = Math.Round(fileInfo.Length / (1024.0 * 1024.0), 2);
                        
                        _logger?.LogDebug("Database size: {SizeMB} MB", metrics.DatabaseSizeMB);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Could not determine database size");
            }
        }

        /// <summary>
        /// Check for potential performance issues
        /// </summary>
        private async Task CheckLongRunningQueriesAsync(DatabaseHealthMetrics metrics)
        {
            try
            {
                // For SQLite, we can't easily check for long-running queries
                // But we can test if the database is responsive
                var startTime = DateTime.UtcNow;
                
                // Test a slightly more complex query
                var recentSalesCount = await _context.Sales
                    .Where(s => s.SaleDate >= DateTime.Today.AddDays(-7))
                    .CountAsync();
                
                var complexQueryTime = DateTime.UtcNow - startTime;
                metrics.ComplexQueryResponseTime = complexQueryTime;
                
                // Flag if queries are taking too long
                if (complexQueryTime.TotalSeconds > 5)
                {
                    _logger?.LogWarning("Complex query took {Duration}ms - potential performance issue", 
                        complexQueryTime.TotalMilliseconds);
                }
                
                _logger?.LogDebug("Complex query test completed in {Duration}ms", 
                    complexQueryTime.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Long-running query check failed");
                throw;
            }
        }

        /// <summary>
        /// Get current health metrics
        /// </summary>
        public DatabaseHealthMetrics GetCurrentMetrics()
        {
            lock (_metricsLock)
            {
                return _currentMetrics.Clone();
            }
        }

        /// <summary>
        /// Perform immediate health check
        /// </summary>
        public async Task<DatabaseHealthMetrics> PerformImmediateHealthCheckAsync()
        {
            await PerformHealthCheckAsync();
            return GetCurrentMetrics();
        }

        /// <summary>
        /// Check if database is currently healthy
        /// </summary>
        public bool IsHealthy()
        {
            lock (_metricsLock)
            {
                return _currentMetrics.IsHealthy && 
                       _currentMetrics.CanConnect && 
                       _currentMetrics.CheckTime > DateTime.UtcNow.AddMinutes(-10); // Recent check
            }
        }

        public override void Dispose()
        {
            _healthCheckTimer?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// Database health metrics
    /// </summary>
    public class DatabaseHealthMetrics
    {
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;
        public bool IsHealthy { get; set; }
        public bool CanConnect { get; set; }
        public string LastError { get; set; }
        
        // Performance metrics
        public TimeSpan OverallResponseTime { get; set; }
        public TimeSpan ConnectivityResponseTime { get; set; }
        public TimeSpan QueryResponseTime { get; set; }
        public TimeSpan ComplexQueryResponseTime { get; set; }
        
        // Database info
        public long DatabaseSizeBytes { get; set; }
        public double DatabaseSizeMB { get; set; }
        public Dictionary<string, int> RecordCounts { get; set; } = new();
        
        public DatabaseHealthMetrics Clone()
        {
            return new DatabaseHealthMetrics
            {
                CheckTime = CheckTime,
                IsHealthy = IsHealthy,
                CanConnect = CanConnect,
                LastError = LastError,
                OverallResponseTime = OverallResponseTime,
                ConnectivityResponseTime = ConnectivityResponseTime,
                QueryResponseTime = QueryResponseTime,
                ComplexQueryResponseTime = ComplexQueryResponseTime,
                DatabaseSizeBytes = DatabaseSizeBytes,
                DatabaseSizeMB = DatabaseSizeMB,
                RecordCounts = new Dictionary<string, int>(RecordCounts)
            };
        }
    }
}
