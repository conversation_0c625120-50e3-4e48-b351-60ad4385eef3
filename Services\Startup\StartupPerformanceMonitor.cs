using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.Startup
{
    /// <summary>
    /// ✅ CRITICAL STARTUP OPTIMIZATION: Monitor and track application startup performance to identify bottlenecks
    /// </summary>
    public class StartupPerformanceMonitor : IDisposable
    {
        private readonly ILogger<StartupPerformanceMonitor> _logger;
        private readonly ConcurrentDictionary<string, StartupPhaseMetrics> _phaseMetrics;
        private readonly Stopwatch _totalStartupStopwatch;
        private readonly object _lockObject = new object();
        private bool _disposed;

        // Performance thresholds for startup phases
        private const int FAST_PHASE_THRESHOLD_MS = 100;
        private const int SLOW_PHASE_THRESHOLD_MS = 500;
        private const int CRITICAL_PHASE_THRESHOLD_MS = 1000;

        public StartupPerformanceMonitor(ILogger<StartupPerformanceMonitor> logger = null)
        {
            _logger = logger;
            _phaseMetrics = new ConcurrentDictionary<string, StartupPhaseMetrics>();
            _totalStartupStopwatch = Stopwatch.StartNew();

            Debug.WriteLine("✅ [STARTUP-MONITOR] Startup Performance Monitor initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Track a startup phase performance
        /// </summary>
        public IDisposable TrackStartupPhase(string phaseName, string category = "General")
        {
            return new StartupPhaseTracker(this, phaseName, category);
        }

        /// <summary>
        /// ✅ INTERNAL: Record startup phase execution metrics
        /// </summary>
        internal void RecordPhaseExecution(string phaseName, string category, long executionTimeMs, bool wasSuccessful, string details = null)
        {
            try
            {
                var key = $"{category}:{phaseName}";
                var totalElapsed = _totalStartupStopwatch.ElapsedMilliseconds;
                
                var metrics = new StartupPhaseMetrics
                {
                    PhaseName = phaseName,
                    Category = category,
                    ExecutionTimeMs = executionTimeMs,
                    TotalElapsedMs = totalElapsed,
                    WasSuccessful = wasSuccessful,
                    Details = details,
                    Timestamp = DateTime.Now
                };

                _phaseMetrics.TryAdd(key, metrics);

                // Determine performance level
                var performanceLevel = GetPerformanceLevel(executionTimeMs);
                var icon = GetPerformanceIcon(performanceLevel);

                Debug.WriteLine($"{icon} [STARTUP-MONITOR] {category}:{phaseName} - {executionTimeMs}ms (Total: {totalElapsed}ms)");

                if (performanceLevel == PerformanceLevel.Slow || performanceLevel == PerformanceLevel.Critical)
                {
                    Debug.WriteLine($"⚠️ [STARTUP-BOTTLENECK] Slow startup phase detected: {phaseName} took {executionTimeMs}ms");
                    if (!string.IsNullOrEmpty(details))
                    {
                        Debug.WriteLine($"   Details: {details}");
                    }
                }

                // Log to application logger if available
                _logger?.LogInformation("Startup Phase: {PhaseName} completed in {ExecutionTime}ms (Total: {TotalTime}ms)", 
                    phaseName, executionTimeMs, totalElapsed);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-MONITOR] Error recording phase metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Mark startup as complete and generate final report
        /// </summary>
        public void CompleteStartup()
        {
            _totalStartupStopwatch.Stop();
            var totalTime = _totalStartupStopwatch.ElapsedMilliseconds;

            Debug.WriteLine($"🎉 [STARTUP-COMPLETE] Application startup completed in {totalTime}ms");

            // Generate detailed startup report
            var report = GenerateStartupReport();
            Debug.WriteLine(report);

            // Log final metrics
            _logger?.LogInformation("Application startup completed in {TotalTime}ms with {PhaseCount} phases", 
                totalTime, _phaseMetrics.Count);
        }

        /// <summary>
        /// ✅ MONITORING: Generate comprehensive startup performance report
        /// </summary>
        public string GenerateStartupReport()
        {
            lock (_lockObject)
            {
                var report = new System.Text.StringBuilder();
                var totalTime = _totalStartupStopwatch.ElapsedMilliseconds;

                report.AppendLine("=== Application Startup Performance Report ===");
                report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"Total Startup Time: {totalTime}ms");
                report.AppendLine();

                var phases = _phaseMetrics.Values.OrderBy(p => p.TotalElapsedMs).ToList();
                
                // Summary by category
                var categoryStats = phases
                    .GroupBy(p => p.Category)
                    .Select(g => new
                    {
                        Category = g.Key,
                        TotalTime = g.Sum(p => p.ExecutionTimeMs),
                        PhaseCount = g.Count(),
                        AverageTime = g.Average(p => p.ExecutionTimeMs)
                    })
                    .OrderByDescending(c => c.TotalTime)
                    .ToList();

                report.AppendLine("📊 Performance by Category:");
                foreach (var category in categoryStats)
                {
                    var percentage = (category.TotalTime * 100.0) / totalTime;
                    report.AppendLine($"  • {category.Category}: {category.TotalTime}ms ({percentage:F1}%) - {category.PhaseCount} phases, avg {category.AverageTime:F1}ms");
                }
                report.AppendLine();

                // Slowest phases
                var slowestPhases = phases
                    .OrderByDescending(p => p.ExecutionTimeMs)
                    .Take(10)
                    .ToList();

                if (slowestPhases.Any())
                {
                    report.AppendLine("🐌 Top 10 Slowest Startup Phases:");
                    foreach (var phase in slowestPhases)
                    {
                        var icon = GetPerformanceIcon(GetPerformanceLevel(phase.ExecutionTimeMs));
                        var percentage = (phase.ExecutionTimeMs * 100.0) / totalTime;
                        report.AppendLine($"  {icon} {phase.Category}:{phase.PhaseName} - {phase.ExecutionTimeMs}ms ({percentage:F1}%)");
                        if (!string.IsNullOrEmpty(phase.Details))
                        {
                            report.AppendLine($"      Details: {phase.Details}");
                        }
                    }
                    report.AppendLine();
                }

                // Timeline
                report.AppendLine("⏱️ Startup Timeline:");
                foreach (var phase in phases)
                {
                    var icon = GetPerformanceIcon(GetPerformanceLevel(phase.ExecutionTimeMs));
                    report.AppendLine($"  {phase.TotalElapsedMs:D4}ms: {icon} {phase.Category}:{phase.PhaseName} ({phase.ExecutionTimeMs}ms)");
                }

                // Performance recommendations
                var recommendations = GeneratePerformanceRecommendations(phases);
                if (recommendations.Any())
                {
                    report.AppendLine();
                    report.AppendLine("💡 Performance Recommendations:");
                    foreach (var recommendation in recommendations)
                    {
                        report.AppendLine($"  • {recommendation}");
                    }
                }

                return report.ToString();
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Generate performance recommendations based on metrics
        /// </summary>
        private List<string> GeneratePerformanceRecommendations(List<StartupPhaseMetrics> phases)
        {
            var recommendations = new List<string>();

            // Check for slow database operations
            var dbPhases = phases.Where(p => p.Category.Contains("Database") && p.ExecutionTimeMs > SLOW_PHASE_THRESHOLD_MS).ToList();
            if (dbPhases.Any())
            {
                recommendations.Add($"Database operations are slow ({dbPhases.Count} phases > {SLOW_PHASE_THRESHOLD_MS}ms). Consider connection pooling and schema caching.");
            }

            // Check for slow service initialization
            var servicePhases = phases.Where(p => p.Category.Contains("Service") && p.ExecutionTimeMs > SLOW_PHASE_THRESHOLD_MS).ToList();
            if (servicePhases.Any())
            {
                recommendations.Add($"Service initialization is slow ({servicePhases.Count} services > {SLOW_PHASE_THRESHOLD_MS}ms). Consider lazy loading and parallel initialization.");
            }

            // Check for slow UI initialization
            var uiPhases = phases.Where(p => p.Category.Contains("UI") && p.ExecutionTimeMs > SLOW_PHASE_THRESHOLD_MS).ToList();
            if (uiPhases.Any())
            {
                recommendations.Add($"UI initialization is slow ({uiPhases.Count} phases > {SLOW_PHASE_THRESHOLD_MS}ms). Consider progressive loading and virtualization.");
            }

            // Check total startup time
            var totalTime = _totalStartupStopwatch.ElapsedMilliseconds;
            if (totalTime > 3000)
            {
                recommendations.Add($"Total startup time is {totalTime}ms. Target should be under 2000ms for optimal user experience.");
            }

            return recommendations;
        }

        /// <summary>
        /// ✅ INTERNAL: Get performance level based on execution time
        /// </summary>
        private PerformanceLevel GetPerformanceLevel(long executionTimeMs)
        {
            if (executionTimeMs <= FAST_PHASE_THRESHOLD_MS) return PerformanceLevel.Fast;
            if (executionTimeMs <= SLOW_PHASE_THRESHOLD_MS) return PerformanceLevel.Normal;
            if (executionTimeMs <= CRITICAL_PHASE_THRESHOLD_MS) return PerformanceLevel.Slow;
            return PerformanceLevel.Critical;
        }

        /// <summary>
        /// ✅ INTERNAL: Get performance icon for display
        /// </summary>
        private string GetPerformanceIcon(PerformanceLevel level)
        {
            return level switch
            {
                PerformanceLevel.Fast => "🚀",
                PerformanceLevel.Normal => "✅",
                PerformanceLevel.Slow => "🟠",
                PerformanceLevel.Critical => "🔴",
                _ => "❓"
            };
        }

        /// <summary>
        /// ✅ PUBLIC API: Get current startup statistics
        /// </summary>
        public StartupStatistics GetStartupStatistics()
        {
            lock (_lockObject)
            {
                var phases = _phaseMetrics.Values.ToList();
                return new StartupStatistics
                {
                    TotalStartupTimeMs = _totalStartupStopwatch.ElapsedMilliseconds,
                    TotalPhases = phases.Count,
                    SuccessfulPhases = phases.Count(p => p.WasSuccessful),
                    AveragePhaseTimeMs = phases.Any() ? phases.Average(p => p.ExecutionTimeMs) : 0,
                    SlowestPhaseTimeMs = phases.Any() ? phases.Max(p => p.ExecutionTimeMs) : 0,
                    FastestPhaseTimeMs = phases.Any() ? phases.Min(p => p.ExecutionTimeMs) : 0,
                    IsStartupComplete = !_totalStartupStopwatch.IsRunning
                };
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                if (_totalStartupStopwatch.IsRunning)
                {
                    CompleteStartup();
                }

                Debug.WriteLine("✅ [STARTUP-MONITOR] Startup Performance Monitor disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [STARTUP-MONITOR] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Startup phase tracker - implements IDisposable for using statement
    /// </summary>
    internal class StartupPhaseTracker : IDisposable
    {
        private readonly StartupPerformanceMonitor _monitor;
        private readonly string _phaseName;
        private readonly string _category;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;

        public StartupPhaseTracker(StartupPerformanceMonitor monitor, string phaseName, string category)
        {
            _monitor = monitor;
            _phaseName = phaseName;
            _category = category;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            if (_disposed) return;

            _stopwatch.Stop();
            _monitor.RecordPhaseExecution(_phaseName, _category, _stopwatch.ElapsedMilliseconds, true);
            _disposed = true;
        }
    }

    /// <summary>
    /// Data structures for startup performance monitoring
    /// </summary>
    public class StartupPhaseMetrics
    {
        public string PhaseName { get; set; }
        public string Category { get; set; }
        public long ExecutionTimeMs { get; set; }
        public long TotalElapsedMs { get; set; }
        public bool WasSuccessful { get; set; }
        public string Details { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class StartupStatistics
    {
        public long TotalStartupTimeMs { get; set; }
        public int TotalPhases { get; set; }
        public int SuccessfulPhases { get; set; }
        public double AveragePhaseTimeMs { get; set; }
        public long SlowestPhaseTimeMs { get; set; }
        public long FastestPhaseTimeMs { get; set; }
        public bool IsStartupComplete { get; set; }
    }

    public enum PerformanceLevel
    {
        Fast,
        Normal,
        Slow,
        Critical
    }
}
