using System;
using System.Windows;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using System.Configuration;
using System.Linq;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class ThemeService : IThemeService
    {
        public void ApplyTheme(string themeName)
        {
            ApplyTheme(themeName, null);
        }

        public void ApplyTheme(string themeName, Color? primaryColor = null)
        {
            // Store the language dictionaries so they can be restored after theme change
            var langResources = PreserveLanguageResources();
            
            var paletteHelper = new PaletteHelper();
            var theme = paletteHelper.GetTheme();

            switch (themeName?.ToLower())
            {
                case "dark":
                    theme.SetBaseTheme(Theme.Dark);
                    theme.SetPrimaryColor(primaryColor ?? Color.FromRgb(33, 150, 243)); // Default blue if no color specified
                    break;
                case "light":
                    theme.SetBaseTheme(Theme.Light);
                    theme.SetPrimaryColor(primaryColor ?? Color.FromRgb(33, 150, 243)); // Default blue if no color specified
                    break;
                case "custom":
                    // Custom theme will be handled by ApplyCustomTheme
                    return;
                default:
                    // Default to light theme with blue accent
                    theme.SetBaseTheme(Theme.Light);
                    theme.SetPrimaryColor(Color.FromRgb(33, 150, 243));
                    break;
            }

            paletteHelper.SetTheme(theme);
            
            // Restore language resources after theme is applied
            RestoreLanguageResources(langResources);
        }

        public void ApplyCustomTheme(bool isDark, Color primaryColor)
        {
            // Store the language dictionaries so they can be restored after theme change
            var langResources = PreserveLanguageResources();
            
            var paletteHelper = new PaletteHelper();
            var theme = paletteHelper.GetTheme();
            
            // Apply to current theme
            theme.SetBaseTheme(isDark ? Theme.Dark : Theme.Light);
            theme.SetPrimaryColor(primaryColor);

            paletteHelper.SetTheme(theme);
            
            // Restore language resources after theme is applied
            RestoreLanguageResources(langResources);
        }
        
        /// <summary>
        /// Temporarily stores language resource dictionaries before theme changes
        /// </summary>
        private ResourceDictionary[] PreserveLanguageResources()
        {
            return Application.Current.Resources.MergedDictionaries
                .Where(rd => rd?.Source != null && 
                       (rd.Source.OriginalString.Contains("/Strings.") || 
                        rd.Source.OriginalString.Contains("\\Strings.")))
                .ToArray();
        }
        
        /// <summary>
        /// Restores language resource dictionaries after theme changes
        /// </summary>
        private void RestoreLanguageResources(ResourceDictionary[] languageDictionaries)
        {
            if (languageDictionaries == null || languageDictionaries.Length == 0)
                return;
                
            // Ensure we don't have duplicates
            var toRemove = Application.Current.Resources.MergedDictionaries
                .Where(rd => rd?.Source != null && 
                       (rd.Source.OriginalString.Contains("/Strings.") || 
                        rd.Source.OriginalString.Contains("\\Strings.")))
                .ToList();
                
            foreach (var rd in toRemove)
            {
                Application.Current.Resources.MergedDictionaries.Remove(rd);
            }
            
            // Add back the language dictionaries
            foreach (var dict in languageDictionaries)
            {
                Application.Current.Resources.MergedDictionaries.Add(dict);
            }
        }

        public Color GetCurrentThemeColor()
        {
            try
            {
                var paletteHelper = new PaletteHelper();
                var theme = paletteHelper.GetTheme();
                return theme.PrimaryMid.Color;
            }
            catch (Exception)
            {
                return Color.FromRgb(33, 150, 243); // Default blue
            }
        }

        public bool IsDarkTheme()
        {
            try
            {
                var paletteHelper = new PaletteHelper();
                var theme = paletteHelper.GetTheme();
                return theme.GetBaseTheme() == BaseTheme.Dark;
            }
            catch (Exception)
            {
                return false; // Default to light theme
            }
        }

        public string[] GetAvailableThemes()
        {
            return new string[] { "Light", "Dark", "Custom" };
        }
    }
}