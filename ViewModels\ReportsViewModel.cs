using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using POSSystem.Models;
using POSSystem.Services;
using System.Data.SQLite;
using System.Windows;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using System.Threading;
using System.Data.SqlClient;
using System.Diagnostics;

namespace POSSystem.ViewModels
{
    public class ReportsViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly DatabaseService _dbService;
        private DateTime _startDate;
        private DateTime _endDate;
        private string _selectedReportType;
        private ObservableCollection<object> _reportData;
        private decimal _totalSales;
        private decimal _totalPurchases;
        private decimal _netProfit;
        private int _totalTransactions;
        private string _connectionString;
        private bool _isLoading;
        private readonly Dispatcher _dispatcher;
        private CancellationTokenSource _cancellationTokenSource;
        private double _progressValue;
        private readonly IEnumerable<string> AvailableReports = new List<string>
        {
            "SalesSummary",
            "PurchaseOrdersSummary", // Keep this for backward compatibility but will use invoices
            "InventoryStatus",
            "TopProducts",
            "CustomerActivity",
            "SupplierActivity"
        };

        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(); }
        }

        public double ProgressValue
        {
            get => _progressValue;
            set
            {
                _progressValue = value;
                OnPropertyChanged(nameof(ProgressValue));
            }
        }

        public ReportsViewModel()
        {
            _dbService = new DatabaseService();
            _connectionString = "Data Source=" + Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
            _startDate = DateTime.Now.AddDays(-30);
            _endDate = DateTime.Now;
            _dispatcher = Application.Current.Dispatcher;

            ReportTypes = new ObservableCollection<string>(AvailableReports);
            _selectedReportType = ReportTypes[0];
            ReportData = new ObservableCollection<object>();

            // ✅ PERFORMANCE FIX: Don't load data in constructor to prevent UI thread blocking
            // Data will be loaded when the view is actually displayed or when explicitly requested
            // This prevents the massive UI thread blocking we were seeing
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Load initial data asynchronously when view is displayed
        /// This prevents UI thread blocking during constructor execution
        /// </summary>
        public async Task LoadInitialDataAsync()
        {
            try
            {
                // 🐛 DEBUG: Track initial data loading
                System.Diagnostics.Debug.WriteLine($"[INITIAL LOAD] LoadInitialDataAsync called - IsLoading: {IsLoading}");

                // Load data in background thread to prevent UI blocking
                await Task.Run(async () => await LoadReportAsync());

                // 🐛 DEBUG: Track completion
                System.Diagnostics.Debug.WriteLine($"[INITIAL LOAD] LoadInitialDataAsync completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INITIAL LOAD] Error loading initial report data: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[INITIAL LOAD] Stack trace: {ex.StackTrace}");
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set {
                _startDate = value;
                OnPropertyChanged();
                // 🐛 DEBUG: Log date filter change
                System.Diagnostics.Debug.WriteLine($"[DATE FILTER] StartDate changed to: {_startDate:yyyy-MM-dd}");
                _ = LoadReportAsync();
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set {
                _endDate = value;
                OnPropertyChanged();
                // 🐛 DEBUG: Log date filter change
                System.Diagnostics.Debug.WriteLine($"[DATE FILTER] EndDate changed to: {_endDate:yyyy-MM-dd}");
                _ = LoadReportAsync();
            }
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set { _selectedReportType = value; OnPropertyChanged(); _ = LoadReportAsync(); }
        }

        public ObservableCollection<string> ReportTypes { get; }

        public ObservableCollection<object> ReportData
        {
            get => _reportData;
            set 
            { 
                _reportData = value;
                OnPropertyChanged();
                // Debug log collection update
                System.Diagnostics.Debug.WriteLine($"ReportData updated with {value?.Count ?? 0} items");
            }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set { _totalSales = value; OnPropertyChanged(); }
        }

        public decimal TotalPurchases
        {
            get => _totalPurchases;
            set { _totalPurchases = value; OnPropertyChanged(); }
        }

        public decimal NetProfit
        {
            get => _netProfit;
            set { _netProfit = value; OnPropertyChanged(); }
        }

        public int TotalTransactions
        {
            get => _totalTransactions;
            set { _totalTransactions = value; OnPropertyChanged(); }
        }

        public void LoadReport()
        {
            try
            {
                IsLoading = true;
                _cancellationTokenSource = new CancellationTokenSource();

                switch (SelectedReportType)
                {
                    case "SalesSummary":
                        LoadSalesSummary();
                        break;
                    case "PurchaseOrdersSummary":
                        LoadPurchaseInvoicesSummary(); // Changed to use invoices
                        break;
                    case "InventoryStatus":
                        LoadInventoryStatus();
                        break;
                    case "TopProducts":
                        LoadTopProducts();
                        break;
                    case "CustomerActivity":
                        LoadCustomerActivity();
                        break;
                    case "SupplierActivity":
                        LoadSupplierActivity();
                        break;
                }
            }
            catch (Exception ex)
            {
                ReportData.Clear();
                MessageBox.Show($"Error loading report: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void LoadSalesSummary()
        {
            try
            {
                // Initialize empty collection first
                ReportData = new ObservableCollection<object>();
                TotalSales = 0;
                TotalPurchases = 0;
                NetProfit = 0;
                TotalTransactions = 0;

                using var connection = new SQLiteConnection(_connectionString);
                connection.Open();

                var reportItems = new ObservableCollection<object>();
                
                // Get detailed sales data
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT s.SaleDate as Date, 
                               s.InvoiceNumber as TransactionNumber,
                               'Sale' as Type,
                               CASE 
                                   WHEN c.FirstName IS NULL OR c.LastName IS NULL THEN 'Walk-in Customer'
                                   ELSE c.FirstName || ' ' || c.LastName 
                               END as Name,
                               COUNT(si.Id) as ItemCount,
                               s.GrandTotal as Amount,
                               s.Status
                        FROM Sales s
                        LEFT JOIN Customers c ON s.CustomerId = c.Id
                        LEFT JOIN SaleItems si ON s.Id = si.SaleId
                        WHERE s.SaleDate BETWEEN @StartDate AND @EndDate
                        GROUP BY s.Id, s.SaleDate, s.InvoiceNumber, s.Status, s.GrandTotal, c.FirstName, c.LastName
                        ORDER BY s.SaleDate DESC";
                    
                    var startDate = StartDate.Date;
                    var endDate = EndDate.Date.AddDays(1).AddSeconds(-1);
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    using var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        var item = new
                        {
                            Date = reader.GetDateTime(0),
                            TransactionNumber = reader.GetString(1),
                            Type = reader.GetString(2),
                            Name = reader.IsDBNull(3) ? "Walk-in Customer" : reader.GetString(3),
                            ItemCount = reader.GetInt32(4),
                            Amount = reader.GetDecimal(5),
                            Status = reader.GetString(6)
                        };
                        reportItems.Add(item);
                        
                        // Debug log each item
                        System.Diagnostics.Debug.WriteLine($"Loaded item: Date={item.Date:d}, TransactionNumber={item.TransactionNumber}, Type={item.Type}, Name={item.Name}, ItemCount={item.ItemCount}, Amount={item.Amount:C2}, Status={item.Status}");
                    }
                }

                // Calculate summary statistics
                if (reportItems.Any())
                {
                    TotalSales = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Amount);
                    TotalTransactions = reportItems.Count;
                    
                    // Debug log totals
                    System.Diagnostics.Debug.WriteLine($"Total Sales: {TotalSales:C2}, Total Transactions: {TotalTransactions}");
                    
                    // Get total purchases for net profit calculation
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT COALESCE(SUM(GrandTotal), 0) 
                            FROM PurchaseOrders 
                            WHERE OrderDate BETWEEN @StartDate AND @EndDate 
                            AND Status != 'Cancelled'";
                        
                        command.Parameters.AddWithValue("@StartDate", StartDate.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@EndDate", EndDate.ToString("yyyy-MM-dd HH:mm:ss"));
                        
                        TotalPurchases = Convert.ToDecimal(command.ExecuteScalar());
                    }

                    NetProfit = TotalSales - TotalPurchases;
                }

                ReportData = reportItems;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sales summary:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", 
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                ReportData = new ObservableCollection<object>();
            }
        }

        private void LoadPurchaseInvoicesSummary()
        {
            // Clear any existing data
            ReportData.Clear();
            
            // Query for purchase invoices instead of purchase orders
            var sql = @"
                SELECT 
                    i.Id,
                    i.InvoiceNumber as TransactionNumber,
                    'Purchase' as Type,
                    s.Name,
                    COUNT(ii.Id) as ItemCount,
                    i.GrandTotal as Amount,
                    i.Status
                FROM Invoices i
                LEFT JOIN Suppliers s ON i.SupplierId = s.Id
                LEFT JOIN InvoiceItems ii ON i.Id = ii.InvoiceId
                WHERE i.Type = 'Purchase'
                AND i.IssueDate BETWEEN @startDate AND @endDate
                GROUP BY i.Id
                ORDER BY i.IssueDate DESC";

            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@startDate", StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@endDate", EndDate.ToString("yyyy-MM-dd 23:59:59"));

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var item = new
                            {
                                Id = reader.GetInt32(0),
                                TransactionNumber = reader.GetString(1),
                                Type = reader.GetString(2),
                                Name = reader.IsDBNull(3) ? "Unknown Supplier" : reader.GetString(3),
                                ItemCount = reader.GetInt32(4),
                                Amount = reader.GetDecimal(5),
                                Status = reader.GetString(6),
                                Date = DateTime.Now // Placeholder, will be sorted by query's ORDER BY
                            };
                            ReportData.Add(item);
                        }
                    }
                }
            }

            // Calculate purchase totals
            TotalPurchases = ReportData.Cast<dynamic>().Sum(x => (decimal)x.Amount);
            TotalTransactions = ReportData.Count;
        }

        private void LoadInventoryStatus()
        {
            try
            {
                IsLoading = true;
                ReportData?.Clear();
                
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT 
                                p.Name AS ProductName,
                                p.SKU AS SKU,
                                c.Name AS Category,
                                p.StockQuantity AS Stock,
                                p.MinimumStock AS ReorderLevel,
                                CASE 
                                    WHEN p.StockQuantity <= 0 THEN 'Out of Stock'
                                    WHEN p.StockQuantity <= p.MinimumStock THEN 'Low Stock'
                                    ELSE 'In Stock' 
                                END AS Status,
                                p.PurchasePrice AS UnitCost,
                                p.SellingPrice AS UnitPrice,
                                p.StockQuantity * p.PurchasePrice AS TotalValue
                            FROM Products p
                            LEFT JOIN Categories c ON p.CategoryId = c.Id
                            ORDER BY p.Name";

                        var reportItems = new ObservableCollection<object>();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                reportItems.Add(new
                                {
                                    ProductName = reader.IsDBNull(0) ? "Unknown" : reader.GetString(0),
                                    SKU = reader.IsDBNull(1) ? "N/A" : reader.GetString(1),
                                    Category = reader.IsDBNull(2) ? "Uncategorized" : reader.GetString(2),
                                    Stock = reader.GetInt32(3),
                                    ReorderLevel = reader.GetInt32(4),
                                    Status = reader.GetString(5),
                                    UnitCost = reader.GetDecimal(6),
                                    UnitPrice = reader.GetDecimal(7),
                                    TotalValue = reader.GetDecimal(8)
                                });
                            }
                        }

                        ReportData = reportItems;
                        TotalTransactions = reportItems.Count;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading inventory status: {ex.Message}");
                ReportData = new ObservableCollection<object>();
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void LoadTopProducts()
        {
            try
            {
                IsLoading = true;
                ReportData?.Clear();
                
                // Use a direct SQL query for better performance
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT 
                                p.Name AS ProductName,
                                c.Name AS Category,
                                SUM(si.Quantity) AS QuantitySold,
                                SUM(si.Total) AS Revenue,
                                SUM(si.Quantity * p.PurchasePrice) AS Cost,
                                SUM(si.Total - (si.Quantity * p.PurchasePrice)) AS Profit,
                                CASE 
                                    WHEN SUM(si.Total) > 0 
                                    THEN (SUM(si.Total - (si.Quantity * p.PurchasePrice)) / SUM(si.Total)) * 100
                                    ELSE 0 
                                END AS ProfitMargin
                            FROM Products p
                            JOIN SaleItems si ON p.Id = si.ProductId
                            JOIN Sales s ON si.SaleId = s.Id
                            LEFT JOIN Categories c ON p.CategoryId = c.Id
                            WHERE s.SaleDate BETWEEN @StartDate AND @EndDate
                            GROUP BY p.Name, c.Name
                            ORDER BY Revenue DESC
                            LIMIT 20";

                        command.Parameters.AddWithValue("@StartDate", StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", EndDate.AddDays(1).ToString("yyyy-MM-dd"));

                        var reportItems = new ObservableCollection<object>();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                reportItems.Add(new
                                {
                                    ProductName = reader.IsDBNull(0) ? "Unknown" : reader.GetString(0),
                                    Category = reader.IsDBNull(1) ? "Uncategorized" : reader.GetString(1),
                                    QuantitySold = reader.GetInt32(2),
                                    Revenue = reader.GetDecimal(3),
                                    Cost = reader.GetDecimal(4),
                                    Profit = reader.GetDecimal(5),
                                    ProfitMargin = reader.GetDouble(6)
                                });
                            }
                        }

                        ReportData = reportItems;
                        TotalTransactions = reportItems.Count;
                        TotalSales = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Revenue);
                        TotalPurchases = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Cost);
                        NetProfit = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Profit);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading top products: {ex.Message}");
                ReportData = new ObservableCollection<object>();
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void LoadCustomerActivity()
        {
            try
            {
                var customers = _dbService.GetAllCustomers();
                var sales = _dbService.GetSalesByDateRange(StartDate, EndDate);

                var customerActivity = customers
                    .Select(c => new
                    {
                        Customer = c.Name,
                        TotalPurchases = sales.Count(s => s.CustomerId == c.Id),
                        TotalSpent = sales.Where(s => s.CustomerId == c.Id).Sum(s => s.GrandTotal),
                        LastPurchase = sales.Where(s => s.CustomerId == c.Id)
                                          .OrderByDescending(s => s.SaleDate)
                                          .FirstOrDefault()?.SaleDate
                    })
                    .Where(x => x.TotalPurchases > 0)
                    .OrderByDescending(x => x.TotalSpent);

                ReportData = new ObservableCollection<object>(customerActivity);
                TotalTransactions = customerActivity.Count();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading customer activity: {ex.Message}");
            }
        }

        private void LoadSupplierActivity()
        {
            try
            {
                var reportItems = new ObservableCollection<object>();
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT 
                                s.Name as SupplierName,
                                COUNT(p.Id) as TotalOrders,
                                COALESCE(SUM(p.GrandTotal), 0) as TotalAmount,
                                MAX(p.OrderDate) as LastOrder
                            FROM Suppliers s
                            LEFT JOIN PurchaseOrders p ON p.SupplierId = s.Id 
                                AND p.OrderDate BETWEEN @StartDate AND @EndDate
                            GROUP BY s.Id, s.Name
                            HAVING COUNT(p.Id) > 0
                            ORDER BY TotalAmount DESC";

                        command.Parameters.AddWithValue("@StartDate", StartDate.ToString("s"));
                        command.Parameters.AddWithValue("@EndDate", EndDate.ToString("s"));

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                reportItems.Add(new
                                {
                                    Name = reader.GetString(0),
                                    Type = "Supplier",
                                    ItemCount = reader.GetInt32(1),
                                    Amount = reader.GetDecimal(2),
                                    Date = reader.GetDateTime(3),
                                    TransactionNumber = "-",
                                    Status = "Active"
                                });
                            }
                        }
                    }
                }

                ReportData = reportItems;
                TotalTransactions = reportItems.Count;
                TotalPurchases = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Amount);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading supplier activity: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                ReportData = new ObservableCollection<object>();
            }
        }

        public async Task LoadReportAsync()
        {
            if (IsLoading) return;

            // 🐛 DEBUG: Log report loading start
            System.Diagnostics.Debug.WriteLine($"[REPORT LOAD] Starting LoadReportAsync - ReportType: {SelectedReportType}, StartDate: {StartDate:yyyy-MM-dd}, EndDate: {EndDate:yyyy-MM-dd}");

            // Cancel any previous load operation
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _cancellationTokenSource.Token;

            try
            {
                IsLoading = true;
                ProgressValue = 0;
                
                // Clear existing data first to provide immediate feedback to user
                await _dispatcher.InvokeAsync(() => {
                    ReportData?.Clear();
                    TotalSales = 0;
                    TotalPurchases = 0;
                    NetProfit = 0;
                    TotalTransactions = 0;
                }, DispatcherPriority.Background);

                // Use a cached context for all operations during this load
                using (var context = new POSDbContext())
                {
                    // Set No Tracking for significant performance improvement
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    
                    // Enable split queries for better performance with related data
                    context.ChangeTracker.AutoDetectChangesEnabled = false;
                    context.Database.SetCommandTimeout(TimeSpan.FromMinutes(2));
                    
                    ProgressValue = 10;
                    if (cancellationToken.IsCancellationRequested) return;

                    // Load data in batches
                    const int batchSize = 1000;
                    var reportItems = new List<object>();

                    switch (SelectedReportType)
                    {
                        case "SalesSummary":
                            await LoadSalesSummaryAsync(context, cancellationToken);
                            break;
                        case "PurchaseOrdersSummary":
                            await LoadPurchaseInvoicesSummaryAsync(cancellationToken);
                            break;
                        case "InventoryStatus":
                            await LoadInventoryStatusAsync(context, cancellationToken);
                            break;
                        case "TopProducts":
                            await LoadTopProductsAsync(context, cancellationToken);
                            break;
                        case "CustomerActivity":
                            await LoadCustomerActivityAsync(context, cancellationToken);
                            break;
                        case "SupplierActivity":
                            await LoadSupplierActivityAsync(context, cancellationToken);
                            break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Operation was canceled, no need to show an error
            }
            catch (Exception ex)
            {
                await _dispatcher.InvokeAsync(() =>
                    MessageBox.Show($"Error loading report: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error)
                );
                System.Diagnostics.Debug.WriteLine($"Report loading error: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                IsLoading = false;
                ProgressValue = 100;
            }
        }

        private async Task LoadSalesSummaryAsync(POSDbContext context, CancellationToken cancellationToken = default)
        {
            var startDate = StartDate.Date;
            var endDate = EndDate.Date.AddDays(1).AddSeconds(-1);

            if (cancellationToken.IsCancellationRequested) return;
            
            try
            {
                // Use compiled query for better performance
                var salesQuery = EF.CompileAsyncQuery((POSDbContext ctx) =>
                    ctx.Sales
                        .AsNoTracking()
                        .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                        .OrderByDescending(s => s.SaleDate)
                        .Select(s => new
                        {
                            s.SaleDate,
                            s.InvoiceNumber,
                            CustomerName = s.Customer != null ? s.Customer.FirstName + " " + s.Customer.LastName : "Walk-in Customer",
                            ItemCount = s.Items.Count,
                            s.GrandTotal,
                            s.Status
                        }));

                // Execute the compiled query
                var reportItems = new List<object>();
                const int batchSize = 500;
                var currentBatch = new List<object>();
                
                await foreach (var sale in salesQuery(context).WithCancellation(cancellationToken))
                {
                    var item = new
                    {
                        Date = sale.SaleDate,
                        TransactionNumber = sale.InvoiceNumber,
                        Type = "Sale",
                        Name = sale.CustomerName,
                        ItemCount = sale.ItemCount,
                        Amount = sale.GrandTotal,
                        Status = sale.Status
                    };
                    
                    currentBatch.Add(item);
                    
                    if (currentBatch.Count >= batchSize)
                    {
                        reportItems.AddRange(currentBatch);
                        // Update UI with current batch
                        await _dispatcher.InvokeAsync(() =>
                        {
                            ReportData = new ObservableCollection<object>(reportItems);
                        }, DispatcherPriority.Background);
                        
                        currentBatch.Clear();
                        
                        // Update progress
                        ProgressValue = Math.Min(80, (double)reportItems.Count / (reportItems.Count + batchSize) * 70 + 10);
                    }
                    
                    if (cancellationToken.IsCancellationRequested) return;
                }
                
                // Add remaining items
                if (currentBatch.Any())
                {
                    reportItems.AddRange(currentBatch);
                }

                // Calculate totals on the client side
                var totalSales = reportItems.Cast<dynamic>().Sum(x => (decimal)x.Amount);
                var totalTransactions = reportItems.Count;

                // Get purchase orders data and calculate total on client side
                var purchaseOrders = await context.PurchaseOrders
                    .AsNoTracking()
                    .Where(p => p.OrderDate >= startDate && 
                               p.OrderDate <= endDate &&
                               p.Status != "Cancelled")
                    .Select(p => new { p.GrandTotal })
                    .ToListAsync(cancellationToken);

                var purchaseTotal = purchaseOrders.Sum(p => p.GrandTotal);
                var netProfit = totalSales - purchaseTotal;

                ProgressValue = 90;
                if (cancellationToken.IsCancellationRequested) return;

                // Final UI update
                await _dispatcher.InvokeAsync(() =>
                {
                    ReportData = new ObservableCollection<object>(reportItems);
                    TotalSales = totalSales;
                    TotalPurchases = purchaseTotal;
                    NetProfit = netProfit;
                    TotalTransactions = totalTransactions;

                    // 🐛 DEBUG: Log card stats update for Sales Summary
                    System.Diagnostics.Debug.WriteLine($"[CARD STATS] Sales Summary - TotalSales: {TotalSales:C2}, TotalPurchases: {TotalPurchases:C2}, NetProfit: {NetProfit:C2}, TotalTransactions: {TotalTransactions}");
                }, DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadSalesSummaryAsync: {ex.Message}\n{ex.StackTrace}");
                throw;
            }
        }

        private async Task LoadPurchaseInvoicesSummaryAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                using var context = new POSDbContext();
                // Query for purchase invoices
                var invoicesQuery = context.Invoice
                    .Include(i => i.Supplier)
                    .Include(i => i.Items)
                    .Where(i => i.Type == "Purchase" && 
                           i.IssueDate >= StartDate &&
                           i.IssueDate <= EndDate.AddDays(1).AddSeconds(-1))
                    .OrderByDescending(i => i.IssueDate);
                
                cancellationToken.ThrowIfCancellationRequested();
                
                var invoices = await invoicesQuery.ToListAsync(cancellationToken);
                
                cancellationToken.ThrowIfCancellationRequested();
                
                var totalPurchases = invoices.Sum((Invoice x) => x.GrandTotal);
                var totalTransactions = invoices.Count;
                
                cancellationToken.ThrowIfCancellationRequested();
                
                var reportItems = invoices.Select(i => new
                {
                    Id = i.Id,
                    TransactionNumber = i.InvoiceNumber,
                    Type = "Purchase",
                    Name = i.Supplier?.Name ?? "Unknown Supplier",
                    ItemCount = i.Items?.Count ?? 0,
                    Amount = i.GrandTotal,
                    Status = i.Status,
                    Date = i.IssueDate
                }).ToList();
                
                await _dispatcher.InvokeAsync(() =>
                {
                    ReportData.Clear();
                    foreach (var item in reportItems)
                    {
                        ReportData.Add(item);
                    }
                    TotalPurchases = totalPurchases;
                    TotalTransactions = totalTransactions;

                    // 🐛 DEBUG: Log card stats update for Purchase Orders Summary
                    System.Diagnostics.Debug.WriteLine($"[CARD STATS] Purchase Orders - TotalPurchases: {TotalPurchases:C2}, TotalTransactions: {TotalTransactions}");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading purchase invoice summary: {ex.Message}");
                throw;
            }
        }

        private async Task LoadInventoryStatusAsync(POSDbContext context, CancellationToken cancellationToken = default)
        {
            ProgressValue = 30;
            if (cancellationToken.IsCancellationRequested) return;

            // Fetch basic product data without the calculated fields
            var productQuery = context.Products
                    .AsNoTracking()
                    .Select(p => new
                    {
                        p.Name,
                    p.SKU,
                    p.StockQuantity,
                    p.MinimumStock,
                    CategoryName = p.Category.Name,
                    p.PurchasePrice,
                    p.SellingPrice
                });
            
            // Execute query
            var products = await productQuery.ToListAsync(cancellationToken);
            
            ProgressValue = 60;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Calculate values in memory where SQLite decimal limitations don't apply
            var productsWithValue = products.Select(p => new 
            {
                p.Name,
                        p.SKU,
                        p.StockQuantity,
                        p.MinimumStock,
                p.CategoryName,
                p.PurchasePrice,
                p.SellingPrice,
                Value = p.StockQuantity * p.PurchasePrice
            }).ToList();
            
            // Calculate totals
            var totalValue = productsWithValue.Sum(p => p.Value);
            var productCount = productsWithValue.Count;
            
            ProgressValue = 80;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Format the data for display
            var reportItems = productsWithValue.Select(p => new
            {
                ProductName = p.Name,
                SKU = p.SKU,
                Category = p.CategoryName,
                Stock = p.StockQuantity,
                ReorderLevel = p.MinimumStock,
                Status = p.StockQuantity <= p.MinimumStock ? "Low Stock" : "In Stock",
                UnitCost = p.PurchasePrice,
                UnitPrice = p.SellingPrice,
                TotalValue = p.Value
            }).ToList<object>();

            ProgressValue = 90;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Update UI in a single batch
                await _dispatcher.InvokeAsync(() =>
                {
                ReportData = new ObservableCollection<object>(reportItems);
                TotalPurchases = totalValue; // Using this field to show total inventory value
                TotalTransactions = productCount;
                }, DispatcherPriority.Background);
        }

        private async Task LoadTopProductsAsync(POSDbContext context, CancellationToken cancellationToken = default)
        {
            var startDate = StartDate.Date;
            var endDate = EndDate.Date.AddDays(1).AddSeconds(-1);
            
            ProgressValue = 30;
            if (cancellationToken.IsCancellationRequested) return;
            
            // First fetch the base data without complex aggregations
            var saleItemsQuery = context.SaleItems
                .AsNoTracking()
                .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                .Select(si => new 
                {
                    si.ProductId,
                    ProductName = si.Product.Name,
                    CategoryName = si.Product.Category.Name ?? "Uncategorized",
                    si.Quantity,
                    si.Total,
                    PurchasePrice = si.Product.PurchasePrice
                });
                
            // Perform the aggregation client-side
            var saleItems = await saleItemsQuery.ToListAsync(cancellationToken);
            
            ProgressValue = 50;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Process the data client-side
                var topProducts = saleItems
                .GroupBy(si => new { si.ProductId, si.ProductName, si.CategoryName })
                    .Select(g => new
                    {
                    g.Key.ProductId,
                    g.Key.ProductName,
                    g.Key.CategoryName,
                        QuantitySold = g.Sum(si => si.Quantity),
                    Revenue = g.Sum(si => si.Total),
                    Cost = g.Sum(si => si.Quantity * si.PurchasePrice),
                    Profit = g.Sum(si => si.Total - (si.Quantity * si.PurchasePrice))
                    })
                .OrderByDescending(x => x.QuantitySold)
                .Take(20)
                    .ToList();

            ProgressValue = 60;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Calculate totals
            var totalRevenue = topProducts.Sum(p => p.Revenue);
            var totalProfit = topProducts.Sum(p => p.Profit);
            var productCount = topProducts.Count;
            
            ProgressValue = 80;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Format data for display
            var reportItems = topProducts.Select(p => new
            {
                ProductName = p.ProductName,
                Category = p.CategoryName,
                QuantitySold = p.QuantitySold,
                Revenue = p.Revenue,
                Cost = p.Cost,
                Profit = p.Profit,
                ProfitMargin = p.Revenue > 0 ? p.Profit / p.Revenue * 100 : 0
            }).ToList<object>();
            
            ProgressValue = 90;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Update UI in a single batch
                await _dispatcher.InvokeAsync(() =>
                {
                ReportData = new ObservableCollection<object>(reportItems);
                TotalSales = totalRevenue;
                NetProfit = totalProfit;
                TotalTransactions = productCount;
                }, DispatcherPriority.Background);
        }

        private async Task LoadCustomerActivityAsync(POSDbContext context, CancellationToken cancellationToken = default)
        {
            var startDate = StartDate.Date;
            var endDate = EndDate.Date.AddDays(1).AddSeconds(-1);
            
            ProgressValue = 30;
            if (cancellationToken.IsCancellationRequested) return;
            
            // First fetch the base data
            var salesQuery = context.Sales
                .AsNoTracking()
                .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate && s.CustomerId != null)
                .Select(s => new
                {
                    s.CustomerId,
                    FirstName = s.Customer.FirstName,
                    LastName = s.Customer.LastName,
                    s.SaleDate,
                    s.GrandTotal
                });
                
            // Execute query to get raw data
            var sales = await salesQuery.ToListAsync(cancellationToken);
            
            ProgressValue = 50;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Process the data client-side
            var customerActivity = sales
                .GroupBy(s => new { s.CustomerId, s.FirstName, s.LastName })
                .Select(g => new
                {
                    g.Key.CustomerId,
                    CustomerName = g.Key.FirstName + " " + g.Key.LastName,
                    TransactionCount = g.Count(),
                    TotalSpent = g.Sum(s => s.GrandTotal),
                    AverageSpent = g.Average(s => s.GrandTotal),
                    LastPurchaseDate = g.Max(s => s.SaleDate)
                })
                .OrderByDescending(c => c.TotalSpent)
                    .ToList();

            ProgressValue = 60;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Calculate totals
            var totalRevenue = customerActivity.Sum(c => c.TotalSpent);
            var customerCount = customerActivity.Count;
            
            ProgressValue = 90;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Update UI in a single batch
                await _dispatcher.InvokeAsync(() =>
                {
                    ReportData = new ObservableCollection<object>(customerActivity);
                TotalSales = totalRevenue;
                TotalTransactions = customerCount;
                }, DispatcherPriority.Background);
        }

        private async Task LoadSupplierActivityAsync(POSDbContext context, CancellationToken cancellationToken = default)
        {
            var startDate = StartDate.Date;
            var endDate = EndDate.Date.AddDays(1).AddSeconds(-1);
            
            ProgressValue = 30;
            if (cancellationToken.IsCancellationRequested) return;
            
            // First fetch the base data
            var purchaseOrdersQuery = context.PurchaseOrders
                .AsNoTracking()
                .Where(p => p.OrderDate >= startDate && p.OrderDate <= endDate)
                .Select(p => new
                {
                    p.SupplierId,
                    SupplierName = p.Supplier.Name,
                    p.OrderDate,
                    p.GrandTotal
                });
                
            // Execute query to get raw data
            var purchaseOrders = await purchaseOrdersQuery.ToListAsync(cancellationToken);
            
            ProgressValue = 50;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Process the data client-side
            var supplierActivity = purchaseOrders
                .GroupBy(p => new { p.SupplierId, p.SupplierName })
                .Select(g => new
                {
                    g.Key.SupplierId,
                    SupplierName = g.Key.SupplierName,
                    OrderCount = g.Count(),
                    TotalSpent = g.Sum(p => p.GrandTotal),
                    AverageOrderValue = g.Average(p => p.GrandTotal),
                    LastOrderDate = g.Max(p => p.OrderDate)
                })
                .OrderByDescending(s => s.TotalSpent)
                    .ToList();

            ProgressValue = 60;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Calculate totals
            var totalSpent = supplierActivity.Sum(s => s.TotalSpent);
            var supplierCount = supplierActivity.Count;
            
            ProgressValue = 90;
            if (cancellationToken.IsCancellationRequested) return;
            
            // Update UI in a single batch
                await _dispatcher.InvokeAsync(() =>
                {
                    ReportData = new ObservableCollection<object>(supplierActivity);
                TotalPurchases = totalSpent;
                TotalTransactions = supplierCount;
                }, DispatcherPriority.Background);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #region IDisposable Implementation

        private bool _disposed = false;

        /// <summary>
        /// Dispose of resources to prevent memory leaks
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // Cancel any ongoing operations
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;

                    // Clear collections
                    ReportData?.Clear();

                    System.Diagnostics.Debug.WriteLine("ReportsViewModel disposed successfully");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error during ReportsViewModel disposal: {ex.Message}");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~ReportsViewModel()
        {
            Dispose(false);
        }

        #endregion
    }
}