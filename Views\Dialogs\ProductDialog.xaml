<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.ProductDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="828.667" d:DesignWidth="550"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <converters:UnitOfMeasureNameConverter x:Key="UnitOfMeasureNameConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Enhanced Styles for Modern Professional Look -->
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CompactSectionBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="1" BlurRadius="6"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <!-- Collapsible Section Styles -->
        <Style x:Key="CollapsibleSectionBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CollapsibleHeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="12,12,0,0"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="CollapsibleContentStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="0,0,12,12"/>
            <Setter Property="Padding" Value="20,0,20,16"/>
        </Style>

        <Style x:Key="ExpandCollapseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="24"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#FF3F51B5"
                                            Opacity="0.3" ShadowDepth="0" BlurRadius="8"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#FF3F51B5"
                                            Opacity="0.3" ShadowDepth="0" BlurRadius="8"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="44"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.2" ShadowDepth="2" BlurRadius="6"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#000000" Opacity="0.3" ShadowDepth="4" BlurRadius="12"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
            <Setter Property="Height" Value="44"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#000000" Opacity="0.1" ShadowDepth="2" BlurRadius="6"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <md:Card Background="{DynamicResource MaterialDesignCardBackground}"
            UniformCornerRadius="16"
            md:ElevationAssist.Elevation="Dp8"
            MaxWidth="580"
            Margin="20">
        <Grid>
            <!-- Header Section with Enhanced Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Enhanced Header with Gradient Background -->
            <Border Grid.Row="0"
                    CornerRadius="16,16,0,0"
                    Padding="28,20">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF3F51B5" Offset="0"/>
                        <GradientStop Color="#FF303F9F" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center">
                    <md:PackIcon Kind="Package"
                                Width="36"
                                Height="36"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource MaterialDesignPaper}"
                                Margin="0,0,16,0">
                        <md:PackIcon.Effect>
                            <DropShadowEffect Color="#000000" Opacity="0.3" ShadowDepth="1" BlurRadius="3"/>
                        </md:PackIcon.Effect>
                    </md:PackIcon>
                    <TextBlock x:Name="DialogTitle"
                             Text="{DynamicResource AddProduct}"
                             FontSize="24"
                             FontWeight="SemiBold"
                             Foreground="{DynamicResource MaterialDesignPaper}"
                             VerticalAlignment="Center">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#000000" Opacity="0.3" ShadowDepth="1" BlurRadius="3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Enhanced Content Section -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="0,0,8,0">
                <Grid Margin="28,24,28,28">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Compact Product Type & Sales Method Selection -->
                    <Border Grid.Row="0" Style="{StaticResource CompactSectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Compact Header -->
                            <TextBlock Grid.Row="0"
                                     Text="{DynamicResource ProductConfiguration}"
                                     FontWeight="SemiBold"
                                     FontSize="14"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                     Margin="0,0,0,8"/>

                            <!-- Horizontal Layout for Both Sections -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Item Type Section -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{DynamicResource Type}"
                                             FontWeight="Medium"
                                             FontSize="12"
                                             Opacity="0.8"
                                             Margin="0,0,0,6"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <RadioButton x:Name="rbProduct"
                                                   Content="📦 Product"
                                                   IsChecked="True"
                                                   GroupName="ItemType"
                                                   Style="{StaticResource MaterialDesignChoiceChipPrimaryRadioButton}"
                                                   Checked="ItemType_Changed"
                                                   Unchecked="ItemType_Changed"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Padding="12,6"
                                                   Margin="0,0,8,0"/>
                                        <RadioButton x:Name="rbService"
                                                   Content="🛠️ Service"
                                                   GroupName="ItemType"
                                                   Style="{StaticResource MaterialDesignChoiceChipPrimaryRadioButton}"
                                                   Checked="ItemType_Changed"
                                                   Unchecked="ItemType_Changed"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Padding="12,6"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Vertical Separator -->
                                <Border Grid.Column="1"
                                        Width="1"
                                        Background="{DynamicResource MaterialDesignDivider}"
                                        Margin="16,0"
                                        Opacity="0.3"/>

                                <!-- Sales Method Section -->
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="{DynamicResource SalesMethod}"
                                             FontWeight="Medium"
                                             FontSize="12"
                                             Opacity="0.8"
                                             Margin="0,0,0,6"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <RadioButton x:Name="rbUnitBased"
                                                   GroupName="SalesMethod"
                                                   Style="{StaticResource MaterialDesignChoiceChipPrimaryRadioButton}"
                                                   Click="UnitBased_Click"
                                                   Margin="0,0,8,0"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Padding="10,6"
                                                   ToolTip="Sell by whole units (e.g., 1 piece, 5 items)">
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon Kind="Counter" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="{DynamicResource Units}" FontWeight="Medium"/>
                                            </StackPanel>
                                        </RadioButton>
                                        <RadioButton x:Name="rbWeightBased"
                                                   GroupName="SalesMethod"
                                                   Style="{StaticResource MaterialDesignChoiceChipPrimaryRadioButton}"
                                                   Click="WeightBased_Click"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Padding="10,6"
                                                   ToolTip="Sell by weight/volume with decimal quantities (e.g., 2.5 kg, 1.75 L)">
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon Kind="Scale" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="{DynamicResource Weight}" FontWeight="Medium"/>
                                            </StackPanel>
                                        </RadioButton>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Enhanced Basic Info Section -->
                    <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0"
                                     Text="{DynamicResource BasicInformation}"
                                     Style="{StaticResource SectionHeaderStyle}"/>

                            <!-- Name -->
                            <TextBox x:Name="txtName"
                                    Grid.Row="1"
                                    Style="{StaticResource EnhancedTextBoxStyle}"
                                    md:HintAssist.Hint="{DynamicResource ProductName}"
                                    Margin="0,0,0,16"/>

                            <!-- Category -->
                            <Grid Grid.Row="2" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <ComboBox x:Name="cmbCategory"
                                     Grid.Column="0"
                                     Style="{StaticResource EnhancedComboBoxStyle}"
                                     md:HintAssist.Hint="{DynamicResource Category}"
                                     ItemsSource="{Binding Categories}"
                                     DisplayMemberPath="Name"
                                     SelectedValuePath="Id"
                                     SelectedValue="{Binding SelectedCategoryId}"
                                     SelectedItem="{Binding SelectedCategory}"
                                     SelectionChanged="CmbCategory_SelectionChanged"
                                     Margin="0,0,8,0"/>

                                <Button x:Name="btnAddCategory"
                                     Grid.Column="1"
                                     Style="{StaticResource MaterialDesignIconButton}"
                                     Width="40" Height="40"
                                     Padding="4"
                                     Click="AddCategory_Click"
                                     ToolTip="{DynamicResource AddCategory}">
                                    <md:PackIcon Kind="FolderPlus" Width="22" Height="22"/>
                                </Button>
                            </Grid>

                            <!-- Enhanced Quantity and Unit of Measure -->
                            <Grid Grid.Row="3" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Enhanced Quantity Input with Visual Indicator -->
                                <Grid Grid.Column="0" Margin="0,0,8,0">
                                    <TextBox x:Name="txtQuantity"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             md:HintAssist.Hint="{DynamicResource ProductQuantity}"
                                             Text="{Binding ProductQuantity, StringFormat=F3}"
                                             PreviewTextInput="DecimalValidation_PreviewTextInput"
                                             IsEnabled="True"/>

                                    <!-- Enhanced Sales Method Indicator -->
                                    <Border x:Name="salesMethodIndicator"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Top"
                                            Margin="0,6,12,0"
                                            Background="{DynamicResource PrimaryHueMidBrush}"
                                            CornerRadius="12"
                                            Padding="8,4"
                                            Visibility="{Binding IsWeightBased, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#000000" Opacity="0.2" ShadowDepth="1" BlurRadius="4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <md:PackIcon Kind="Scale"
                                                       Width="14"
                                                       Height="14"
                                                       Foreground="White"
                                                       Margin="0,0,4,0"/>
                                            <TextBlock Text="Weight/Volume"
                                                     FontSize="11"
                                                     Foreground="White"
                                                     FontWeight="Medium"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <ComboBox x:Name="cmbUnitOfMeasure"
                                         Grid.Column="1"
                                         Style="{StaticResource EnhancedComboBoxStyle}"
                                         md:HintAssist.Hint="{DynamicResource UnitOfMeasure}"
                                         ItemsSource="{Binding UnitsOfMeasure}"
                                         SelectedValuePath="Id"
                                         SelectedValue="{Binding SelectedUnitOfMeasureId}"
                                         SelectionChanged="CmbUnitOfMeasure_SelectionChanged"
                                         Margin="8,0,0,0">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name, Converter={StaticResource UnitOfMeasureNameConverter}}" FontSize="14"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </Grid>

                            <!-- Enhanced Barcode Section -->
                            <Grid Grid.Row="4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="txtBarcode"
                                         Grid.Column="0"
                                         Style="{StaticResource EnhancedTextBoxStyle}"
                                         md:HintAssist.Hint="{DynamicResource PrimaryBarcode}"
                                         KeyDown="TxtBarcode_KeyDown"
                                         Margin="0,0,8,0"/>

                                <StackPanel Grid.Column="1"
                                          Orientation="Horizontal">
                                    <Button x:Name="btnGenerateBarcode"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Width="40" Height="40"
                                            Padding="4"
                                            Margin="0,0,8,0"
                                            Click="GenerateBarcode_Click"
                                            ToolTip="{DynamicResource GenerateBarcode}">
                                        <md:PackIcon Kind="Plus" Width="22" Height="22"/>
                                    </Button>

                                    <Button x:Name="btnManageBarcodes"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Width="40" Height="40"
                                            Padding="4"
                                            Click="ManageBarcodes_Click"
                                            ToolTip="{DynamicResource ManageBarcodes}">
                                        <md:PackIcon Kind="Barcode" Width="22" Height="22"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>
                    
                    <!-- Enhanced Pricing and Stock Section -->
                    <Border Grid.Row="2" Style="{StaticResource SectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0"
                                     Text="{DynamicResource PricingStock}"
                                     Style="{StaticResource SectionHeaderStyle}"/>

                            <!-- Enhanced Pricing -->
                            <Grid Grid.Row="1" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="txtPurchasePrice"
                                        Grid.Column="0"
                                        Style="{StaticResource EnhancedTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource PurchasePrice}"
                                        PreviewTextInput="NumberValidation_PreviewTextInput"
                                        Margin="0,0,8,0"/>

                                <TextBox x:Name="txtSellingPrice"
                                        Grid.Column="1"
                                        Style="{StaticResource EnhancedTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource SellingPrice}"
                                        PreviewTextInput="NumberValidation_PreviewTextInput"
                                        Margin="8,0,0,0"/>
                            </Grid>
                        
                            <!-- Enhanced Stock and Minimum Stock -->
                            <Grid Grid.Row="2" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="txtMinimumStock"
                                        Grid.Column="0"
                                        Style="{StaticResource EnhancedTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource MinimumStock}"
                                        Text="5"
                                        PreviewTextInput="IntegerValidation_PreviewTextInput"
                                        Margin="0,0,8,0"/>

                                <Grid Grid.Column="1" Margin="8,0,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Enhanced Current Stock Input with Visual Indicators -->
                                    <Grid Grid.Column="0" Margin="0,0,8,0">
                                        <TextBox x:Name="txtCurrentStock"
                                                Style="{StaticResource EnhancedTextBoxStyle}"
                                                md:HintAssist.Hint="{DynamicResource CurrentStock}"
                                                PreviewTextInput="DecimalValidation_PreviewTextInput"/>

                                        <!-- Unit Type Indicator for Stock Field -->
                                        <Border x:Name="stockUnitIndicator"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                Margin="0,6,12,0"
                                                Background="{DynamicResource SecondaryHueMidBrush}"
                                                CornerRadius="10"
                                                Padding="6,3"
                                                Visibility="Collapsed">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#000000" Opacity="0.15" ShadowDepth="1" BlurRadius="3"/>
                                            </Border.Effect>
                                            <StackPanel Orientation="Horizontal">
                                                <md:PackIcon x:Name="stockUnitIcon"
                                                           Kind="Scale"
                                                           Width="12"
                                                           Height="12"
                                                           Foreground="White"
                                                           Margin="0,0,3,0"/>
                                                <TextBlock x:Name="stockUnitText"
                                                         Text="Weight"
                                                         FontSize="10"
                                                         Foreground="White"
                                                         FontWeight="Medium"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <Button x:Name="btnManageStock"
                                            Grid.Column="1"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Width="40" Height="40"
                                            Padding="4"
                                            Click="ManageBatches_Click"
                                            ToolTip="{DynamicResource ManageBatches}"
                                            Visibility="{Binding ElementName=chkTrackBatches, Path=IsChecked, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <md:PackIcon Kind="Package" Width="22" Height="22"/>
                                    </Button>
                                </Grid>
                            </Grid>
                        
                            <!-- Enhanced Description -->
                            <Grid Grid.Row="3">
                                <TextBox x:Name="txtDescription"
                                        Style="{StaticResource EnhancedTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource Description}"
                                        TextWrapping="Wrap"
                                        MinHeight="60"
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"/>
                            </Grid>
                        </Grid>
                    </Border>
                    
                    <!-- Batch Information Section - Hidden in Edit Mode -->
                    <Grid x:Name="BatchInfoSection" Grid.Row="3" Margin="0,0,0,16" Visibility="Collapsed">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!-- Section Header -->
                        <TextBlock Grid.Row="0"
                                 Text="{DynamicResource BatchInformation}"
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 Margin="0,0,0,8"/>

                        <!-- Batch Statistics -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Batch Count -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="{DynamicResource BatchCount}"
                                         FontSize="12"
                                         Opacity="0.7"/>
                                <TextBlock x:Name="txtBatchCount"
                                         Text="0"
                                         FontWeight="Medium"
                                         FontSize="16"/>
                            </StackPanel>

                            <!-- Batch Quantity -->
                            <StackPanel Grid.Column="1" Margin="8,0,8,0">
                                <TextBlock Text="{DynamicResource BatchQuantity}"
                                         FontSize="12"
                                         Opacity="0.7"/>
                                <TextBlock x:Name="txtBatchQuantity"
                                         Text="0"
                                         FontWeight="Medium"
                                         FontSize="16"/>
                            </StackPanel>

                            <!-- Manage Batches Button -->
                            <Button Grid.Column="2"
                                  Content="{DynamicResource ManageBatches}"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Click="ManageBatches_Click"
                                  Margin="8,0,0,0"
                                  VerticalAlignment="Center"/>
                        </Grid>
                    </Grid>

                    <!-- Collapsible Bulk Pricing Section -->
                    <Border x:Name="BulkPricingSection" Grid.Row="4" Style="{StaticResource CollapsibleSectionBorderStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Collapsible Header -->
                            <Border Grid.Row="0" Style="{StaticResource CollapsibleHeaderStyle}"
                                    MouseLeftButtonDown="BulkPricingHeader_Click">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <md:PackIcon Grid.Column="0"
                                               Kind="ScaleBalance"
                                               Width="20" Height="20"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,8,0"/>

                                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                        <TextBlock Text="{DynamicResource BulkPackPricing}"
                                                 Style="{StaticResource SectionHeaderStyle}"
                                                 VerticalAlignment="Center"
                                                 Margin="0"/>

                                        <!-- Pricing Tiers Count Indicator -->
                                        <Border x:Name="PricingTiersIndicator"
                                                Background="{DynamicResource SecondaryHueMidBrush}"
                                                CornerRadius="10"
                                                Padding="6,2"
                                                Margin="8,0,0,0"
                                                Visibility="Collapsed">
                                            <TextBlock x:Name="PricingTiersCount"
                                                     Text="0"
                                                     FontSize="11"
                                                     FontWeight="Medium"
                                                     Foreground="White"/>
                                        </Border>
                                    </StackPanel>

                                    <Button Grid.Column="2"
                                          x:Name="btnAddPriceTier"
                                          Style="{StaticResource MaterialDesignIconButton}"
                                          Width="32" Height="32"
                                          Padding="4"
                                          Click="AddPriceTier_Click"
                                          ToolTip="Add new pricing tier"
                                          Margin="0,0,8,0">
                                        <md:PackIcon Kind="Plus" Width="16" Height="16"/>
                                    </Button>

                                    <Button Grid.Column="3"
                                          x:Name="btnExpandCollapseBulkPricing"
                                          Style="{StaticResource ExpandCollapseButtonStyle}"
                                          Click="BulkPricingHeader_Click"
                                          ToolTip="Expand/Collapse Bulk Pricing">
                                        <md:PackIcon x:Name="iconExpandCollapseBulkPricing"
                                                   Kind="ChevronDown"
                                                   Width="16" Height="16"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}">
                                            <md:PackIcon.RenderTransform>
                                                <RotateTransform x:Name="rotateTransformBulkPricing" Angle="0"/>
                                            </md:PackIcon.RenderTransform>
                                        </md:PackIcon>
                                    </Button>
                                </Grid>
                            </Border>

                            <!-- Collapsible Content -->
                            <Border x:Name="BulkPricingContent" Grid.Row="1" Style="{StaticResource CollapsibleContentStyle}"
                                    Visibility="Collapsed">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Pricing Tiers List -->
                                    <Border Grid.Row="0"
                                          Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                          CornerRadius="8"
                                          Padding="12"
                                          Margin="0,0,0,12"
                                          MinHeight="120">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                    HorizontalScrollBarVisibility="Disabled"
                                                    MaxHeight="200">
                                            <ItemsControl x:Name="PriceTiersList">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                                              CornerRadius="6"
                                                              Padding="12,8"
                                                              Margin="0,0,0,8"
                                                              BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                              BorderThickness="1">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <StackPanel Grid.Column="0">
                                                                    <TextBlock Text="{Binding DisplayName}"
                                                                             FontWeight="Medium"
                                                                             FontSize="13"/>
                                                                    <TextBlock Text="{Binding PriceDisplay}"
                                                                             FontSize="12"
                                                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                             Margin="0,2,0,0"/>
                                                                </StackPanel>

                                                                <Button Grid.Column="1"
                                                                      Style="{StaticResource MaterialDesignIconButton}"
                                                                      Width="28" Height="28"
                                                                      Padding="2"
                                                                      Margin="4,0"
                                                                      Click="EditPriceTier_Click"
                                                                      CommandParameter="{Binding}"
                                                                      ToolTip="Edit pricing tier">
                                                                    <md:PackIcon Kind="Pencil" Width="12" Height="12"/>
                                                                </Button>

                                                                <Button Grid.Column="2"
                                                                      Style="{StaticResource MaterialDesignIconButton}"
                                                                      Width="28" Height="28"
                                                                      Padding="2"
                                                                      Foreground="#D32F2F"
                                                                      Click="DeletePriceTier_Click"
                                                                      CommandParameter="{Binding}"
                                                                      ToolTip="Delete pricing tier">
                                                                    <md:PackIcon Kind="Delete" Width="12" Height="12"/>
                                                                </Button>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </Border>

                                    <!-- Bulk Pricing Info -->
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                 Text="{DynamicResource ConfigureQuantityBasedPricing}"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                 TextWrapping="Wrap"
                                                 VerticalAlignment="Center"/>

                                        <CheckBox Grid.Column="1"
                                                x:Name="chkEnableBulkPricing"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Content="Enable bulk pricing"
                                                FontSize="12"
                                                Checked="EnableBulkPricing_CheckedChanged"
                                                Unchecked="EnableBulkPricing_CheckedChanged"/>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Product Image and Options Section -->
                    <Grid Grid.Row="5" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Product Image -->
                        <StackPanel Grid.Column="0">
                            <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Width="130" Height="130">
                                <Grid>
                                    <!-- Placeholder when no image -->
                                    <StackPanel x:Name="noImagePlaceholder"
                                              VerticalAlignment="Center"
                                              HorizontalAlignment="Center">
                                        <md:PackIcon Kind="Image"
                                                    Width="40" Height="40"
                                                    Opacity="0.3"/>
                                        <TextBlock Text="{DynamicResource NoImage}"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 Opacity="0.3"
                                                 Margin="0,4,0,0"/>
                                    </StackPanel>
                                    
                                    <!-- Product Image -->
                                    <Image x:Name="productImage"
                                           Stretch="Uniform"
                                           RenderOptions.BitmapScalingMode="HighQuality"/>
                                </Grid>
                            </Border>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,8,0,0" HorizontalAlignment="Center">
                                <Button x:Name="btnUploadImage"
                                        Style="{StaticResource MaterialDesignIconButton}"
                                        Width="36" Height="36"
                                        Padding="2"
                                        Margin="0,0,6,0"
                                        Click="UploadImage_Click"
                                        ToolTip="{DynamicResource UploadProductImage}">
                                    <md:PackIcon Kind="Upload" Width="20" Height="20"/>
                                </Button>
                                
                                <Button x:Name="btnRemoveImage"
                                        Style="{StaticResource MaterialDesignIconButton}"
                                        Width="36" Height="36"
                                        Padding="2"
                                        Foreground="#D32F2F"
                                        Click="RemoveImage_Click"
                                        ToolTip="{DynamicResource RemoveProductImage}">
                                    <md:PackIcon Kind="Delete" Width="20" Height="20"/>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                        
                        <!-- Product Options -->
                        <StackPanel Grid.Column="1" Margin="16,0,0,0">
                            <CheckBox x:Name="chkIsActive"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Content="{DynamicResource ProductIsActive}"
                                     IsChecked="True"
                                     Margin="0,0,0,8"/>
                            
                            <CheckBox x:Name="chkIsFavorite"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Margin="0,0,0,8"
                                     ToolTip="{DynamicResource MarkAsFavoriteTooltip}">
                                <StackPanel Orientation="Horizontal">
                                    <md:PackIcon Kind="Star" Margin="0,0,8,0" />
                                    <TextBlock Text="{DynamicResource AddToFavorites}" />
                                </StackPanel>
                            </CheckBox>
                            
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <CheckBox x:Name="chkHasExpiry"
                                         Grid.Column="0"
                                         Style="{StaticResource MaterialDesignCheckBox}"
                                         Content="{DynamicResource ProductHasExpiry}"
                                         VerticalAlignment="Center"/>
                                
                                <DatePicker x:Name="dpExpiryDate"
                                           Grid.Column="1"
                                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                           md:HintAssist.Hint="{DynamicResource ExpiryDate}"
                                           IsEnabled="{Binding IsChecked, ElementName=chkHasExpiry}"
                                           Margin="12,0,0,0"/>
                            </Grid>
                            
                            <CheckBox x:Name="chkTrackBatches"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Content="{DynamicResource TrackBatches}"
                                     Checked="chkTrackBatches_CheckedChanged"
                                     Unchecked="chkTrackBatches_CheckedChanged"
                                     IsChecked="True"/>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Enhanced Button Panel -->
                    <Border Grid.Row="6"
                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                            BorderThickness="0,2,0,0"
                            Padding="0,24,0,0"
                            Margin="0,24,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Enhanced Cancel Button -->
                            <Button x:Name="btnCancel"
                                  Grid.Column="0"
                                  Content="{DynamicResource Cancel}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Margin="0,0,12,0"
                                  HorizontalAlignment="Stretch"
                                  Click="BtnCancel_Click"/>

                            <!-- Enhanced Save Button -->
                            <Button x:Name="btnSave"
                                  Grid.Column="1"
                                  Content="{DynamicResource Save}"
                                  Click="Save_Click"
                                  Style="{StaticResource PrimaryButtonStyle}"
                                  Margin="12,0,0,0"
                                  HorizontalAlignment="Stretch"/>
                        </Grid>
                    </Border>
                </Grid>
            </ScrollViewer>
        </Grid>
    </md:Card>
</UserControl> 
