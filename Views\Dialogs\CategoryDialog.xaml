<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Dialogs.CategoryDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views.Dialogs"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="500"
             Background="Transparent">
    
    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
    </UserControl.Resources>
    
    <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                         UniformCornerRadius="12"
                         materialDesign:ElevationAssist.Elevation="Dp4"
                         MaxWidth="500"
                         Margin="16">
        <Grid>
            <!-- Header Section with Gradient Background -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header with Gradient Background -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource PrimaryHueMidBrush}" 
                    CornerRadius="12,12,0,0"
                    Padding="24,16">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderPlus" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                           Margin="0,0,12,0"/>
                    <TextBlock x:Name="DialogTitle" 
                             Text="{DynamicResource AddCategory}" 
                             FontSize="22"
                             FontWeight="Medium"
                             Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Content Section -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled">
                <StackPanel Margin="24,20">
                    <!-- Category Name -->
                    <TextBox x:Name="txtName"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="{DynamicResource CategoryName}"
                           Margin="0,0,0,16"/>
                    
                    <!-- Category Description -->
                    <TextBox x:Name="txtDescription"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="{DynamicResource CategoryDescription}"
                           Height="100"
                           TextWrapping="Wrap"
                           AcceptsReturn="True"
                           Margin="0,0,0,24"/>
                    
                    <!-- Button Panel -->
                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="0,20,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Cancel Button -->
                            <Button x:Name="btnCancel" 
                                  Grid.Column="0"
                                  Content="{DynamicResource Cancel}"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Height="40"
                                  Margin="0,0,8,0"
                                  HorizontalAlignment="Stretch"
                                  Click="BtnCancel_Click"/>
                            
                            <!-- Save Button -->
                            <Button x:Name="btnSave" 
                                  Grid.Column="1"
                                  Content="{DynamicResource AddCategory}"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Background="{DynamicResource PrimaryHueMidBrush}"
                                  Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                  Height="40"
                                  Margin="8,0,0,0"
                                  HorizontalAlignment="Stretch"
                                  Click="BtnSave_Click"/>
                        </Grid>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </materialDesign:Card>
</UserControl> 