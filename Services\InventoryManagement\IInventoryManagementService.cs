using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace POSSystem.Services.InventoryManagement
{
    /// <summary>
    /// Interface for inventory management operations
    /// </summary>
    public interface IInventoryManagementService
    {
        /// <summary>
        /// Get all categories
        /// </summary>
        Task<List<Category>> GetAllCategoriesAsync();

        /// <summary>
        /// Get category by ID
        /// </summary>
        Task<Category> GetCategoryByIdAsync(int id);

        /// <summary>
        /// Add new category
        /// </summary>
        Task<int> AddCategoryAsync(Category category);

        /// <summary>
        /// Update existing category
        /// </summary>
        Task<bool> UpdateCategoryAsync(Category category);

        /// <summary>
        /// Delete category
        /// </summary>
        Task<bool> DeleteCategoryAsync(int id);

        /// <summary>
        /// Add batch stock
        /// </summary>
        Task<int> AddBatchStockAsync(BatchStock batch);

        /// <summary>
        /// Get batches for product
        /// </summary>
        Task<List<BatchStock>> GetBatchesForProductAsync(int productId);

        /// <summary>
        /// Update batch stock
        /// </summary>
        Task<bool> UpdateBatchStockAsync(BatchStock batch);

        /// <summary>
        /// Add stock to batch
        /// </summary>
        Task<bool> AddStockToBatchAsync(int batchId, int quantity);

        /// <summary>
        /// Get inventory transactions
        /// </summary>
        Task<List<InventoryTransaction>> GetInventoryTransactionsAsync(int? productId = null);

        /// <summary>
        /// Add inventory transaction
        /// </summary>
        Task<int> AddInventoryTransactionAsync(InventoryTransaction transaction);

        /// <summary>
        /// Update product stock with reason tracking
        /// </summary>
        Task<bool> UpdateProductStockWithReasonAsync(int productId, decimal newQuantity, string reason);

        /// <summary>
        /// Get top selling products for a period
        /// </summary>
        Task<List<Product>> GetTopSellingProductsAsync(int count, DateTime startDate, DateTime endDate);
    }
}
