using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;

namespace POSSystem.Services.Startup
{
    /// <summary>
    /// ✅ CRITICAL STARTUP OPTIMIZATION: Progressive UI loading with splash screen for better perceived performance
    /// </summary>
    public class ProgressiveUILoader : IDisposable
    {
        private readonly ILogger<ProgressiveUILoader> _logger;
        private readonly StartupPerformanceMonitor _performanceMonitor;
        private readonly LazyLoadingManager _lazyLoadingManager;
        private SplashScreenWindow _splashScreen;
        private bool _disposed;

        // Configuration
        private const int MIN_SPLASH_DISPLAY_TIME_MS = 1000; // Minimum 1 second to avoid flicker
        private const int MAX_SPLASH_DISPLAY_TIME_MS = 10000; // Maximum 10 seconds

        public ProgressiveUILoader(StartupPerformanceMonitor performanceMonitor = null,
            LazyLoadingManager lazyLoadingManager = null,
            ILogger<ProgressiveUILoader> logger = null)
        {
            _performanceMonitor = performanceMonitor;
            _lazyLoadingManager = lazyLoadingManager;
            _logger = logger;

            Debug.WriteLine("✅ [PROGRESSIVE-UI] Progressive UI Loader initialized");
        }

        /// <summary>
        /// ✅ CRITICAL: Show splash screen and begin progressive loading
        /// </summary>
        public async Task ShowSplashAndLoadAsync(Func<IProgress<ProgressInfo>, CancellationToken, Task> loadingFunc,
            CancellationToken cancellationToken = default)
        {
            using var overallTracker = _performanceMonitor?.TrackStartupPhase("ProgressiveUILoading", "UI");

            var splashStartTime = DateTime.Now;
            bool splashShown = false;

            try
            {
                // Try to show splash screen
                try
                {
                    ShowSplashScreen();
                    splashShown = true;
                    Debug.WriteLine("[PROGRESSIVE-UI] Splash screen shown successfully");
                }
                catch (Exception splashEx)
                {
                    Debug.WriteLine($"⚠️ [PROGRESSIVE-UI] Failed to show splash screen: {splashEx.Message}");
                    // Continue without splash screen
                }

                // Create progress reporter (works even without splash screen)
                var progress = new Progress<ProgressInfo>(splashShown ? UpdateSplashProgress : (info) =>
                {
                    Debug.WriteLine($"[PROGRESSIVE-UI] Progress: {info.Percentage}% - {info.Message}");
                });

                // Start loading process
                var loadingTask = loadingFunc(progress, cancellationToken);

                // Ensure minimum splash display time only if splash was shown
                if (splashShown)
                {
                    var minDisplayTask = Task.Delay(MIN_SPLASH_DISPLAY_TIME_MS, cancellationToken);
                    await Task.WhenAll(loadingTask, minDisplayTask);
                }
                else
                {
                    await loadingTask;
                }

                Debug.WriteLine($"[PROGRESSIVE-UI] Loading completed, splash displayed for {(DateTime.Now - splashStartTime).TotalMilliseconds:F0}ms");
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("[PROGRESSIVE-UI] Progressive loading cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error during progressive loading: {ex.Message}");
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Stack trace: {ex.StackTrace}");

                if (splashShown)
                {
                    UpdateSplashProgress(new ProgressInfo
                    {
                        Message = $"Error: {ex.Message}",
                        Percentage = 0,
                        IsError = true
                    });
                }
                throw;
            }
            finally
            {
                // Hide splash screen if it was shown
                if (splashShown)
                {
                    HideSplashScreen();
                }
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Show the splash screen
        /// </summary>
        private void ShowSplashScreen()
        {
            using var splashTracker = _performanceMonitor?.TrackStartupPhase("ShowSplashScreen", "UI");
            
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    _splashScreen = new SplashScreenWindow();
                    _splashScreen.Show();
                    
                    // Bring to front
                    _splashScreen.Activate();
                    _splashScreen.Topmost = true;
                    _splashScreen.Topmost = false; // Reset topmost to allow normal window behavior
                    
                    Debug.WriteLine("[PROGRESSIVE-UI] Splash screen displayed");
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error showing splash screen: {ex.Message}");
                _logger?.LogError(ex, "Error showing splash screen");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Update splash screen progress
        /// </summary>
        private void UpdateSplashProgress(ProgressInfo progressInfo)
        {
            try
            {
                Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    _splashScreen?.UpdateProgress(progressInfo);
                }, DispatcherPriority.Normal);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error updating splash progress: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Hide the splash screen
        /// </summary>
        private void HideSplashScreen()
        {
            using var hideTracker = _performanceMonitor?.TrackStartupPhase("HideSplashScreen", "UI");
            
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (_splashScreen != null)
                    {
                        _splashScreen.Close();
                        _splashScreen = null;
                        Debug.WriteLine("[PROGRESSIVE-UI] Splash screen hidden");
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error hiding splash screen: {ex.Message}");
                _logger?.LogError(ex, "Error hiding splash screen");
            }
        }

        /// <summary>
        /// ✅ CRITICAL: Execute standard progressive loading sequence
        /// </summary>
        public async Task ExecuteStandardLoadingSequenceAsync(IServiceProvider serviceProvider,
            CancellationToken cancellationToken = default)
        {
            try
            {
                Debug.WriteLine("[PROGRESSIVE-UI] Starting standard loading sequence");

                await ShowSplashAndLoadAsync(async (progress, ct) =>
                {
                    try
                    {
                        var loadingSteps = GetStandardLoadingSteps(serviceProvider);
                        var totalSteps = loadingSteps.Count;

                        Debug.WriteLine($"[PROGRESSIVE-UI] Executing {totalSteps} loading steps");

                        for (int i = 0; i < totalSteps; i++)
                        {
                            ct.ThrowIfCancellationRequested();

                            var step = loadingSteps[i];
                            var percentage = (int)((i * 100.0) / totalSteps);

                            progress.Report(new ProgressInfo
                            {
                                Message = step.Description,
                                Percentage = percentage,
                                CurrentStep = i + 1,
                                TotalSteps = totalSteps
                            });

                            using var stepTracker = _performanceMonitor?.TrackStartupPhase(step.Name, "ProgressiveLoading");

                            try
                            {
                                await step.ExecuteFunc(ct);
                                Debug.WriteLine($"✅ [PROGRESSIVE-UI] Step completed: {step.Name}");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Step failed: {step.Name} - {ex.Message}");

                                if (step.IsRequired)
                                {
                                    throw;
                                }
                                else
                                {
                                    Debug.WriteLine($"⚠️ [PROGRESSIVE-UI] Optional step failed, continuing: {step.Name}");
                                }
                            }
                        }

                        // Final progress update
                        progress.Report(new ProgressInfo
                        {
                            Message = "Startup complete!",
                            Percentage = 100,
                            CurrentStep = totalSteps,
                            TotalSteps = totalSteps
                        });
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error in loading sequence: {ex.Message}");
                        progress.Report(new ProgressInfo
                        {
                            Message = $"Error: {ex.Message}",
                            Percentage = 0,
                            IsError = true
                        });
                        throw;
                    }

                }, cancellationToken);

                Debug.WriteLine("✅ [PROGRESSIVE-UI] Standard loading sequence completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error in ExecuteStandardLoadingSequenceAsync: {ex.Message}");
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// ✅ INTERNAL: Get standard loading steps
        /// </summary>
        private List<LoadingStep> GetStandardLoadingSteps(IServiceProvider serviceProvider)
        {
            return new List<LoadingStep>
            {
                new LoadingStep
                {
                    Name = "InitializeCore",
                    Description = "Initializing core services...",
                    IsRequired = true,
                    ExecuteFunc = async (ct) =>
                    {
                        // Core initialization would happen here
                        await Task.Delay(200, ct); // Simulate work
                    }
                },

                new LoadingStep
                {
                    Name = "ConnectDatabase",
                    Description = "Connecting to database...",
                    IsRequired = true,
                    ExecuteFunc = async (ct) =>
                    {
                        // Database connection would happen here
                        await Task.Delay(300, ct); // Simulate work
                    }
                },

                new LoadingStep
                {
                    Name = "LoadConfiguration",
                    Description = "Loading configuration...",
                    IsRequired = true,
                    ExecuteFunc = async (ct) =>
                    {
                        // Configuration loading would happen here
                        await Task.Delay(150, ct); // Simulate work
                    }
                },

                new LoadingStep
                {
                    Name = "InitializeUI",
                    Description = "Initializing user interface...",
                    IsRequired = true,
                    ExecuteFunc = async (ct) =>
                    {
                        // UI initialization would happen here
                        await Task.Delay(400, ct); // Simulate work
                    }
                },

                new LoadingStep
                {
                    Name = "PreloadComponents",
                    Description = "Preloading components...",
                    IsRequired = false,
                    ExecuteFunc = async (ct) =>
                    {
                        if (_lazyLoadingManager != null)
                        {
                            await _lazyLoadingManager.PreloadHighPriorityComponentsAsync(ct);
                        }
                        else
                        {
                            await Task.Delay(250, ct); // Simulate work
                        }
                    }
                },

                new LoadingStep
                {
                    Name = "FinalizeStartup",
                    Description = "Finalizing startup...",
                    IsRequired = true,
                    ExecuteFunc = async (ct) =>
                    {
                        // Final startup tasks would happen here
                        await Task.Delay(100, ct); // Simulate work
                    }
                }
            };
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                HideSplashScreen();
                Debug.WriteLine("✅ [PROGRESSIVE-UI] Progressive UI Loader disposed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ [PROGRESSIVE-UI] Error during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Simple splash screen window
    /// </summary>
    public class SplashScreenWindow : Window
    {
        private System.Windows.Controls.TextBlock _messageText;
        private System.Windows.Controls.ProgressBar _progressBar;
        private System.Windows.Controls.TextBlock _percentageText;

        public SplashScreenWindow()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // Window properties
            Title = "POS System";
            Width = 400;
            Height = 200;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            WindowStyle = WindowStyle.None;
            ResizeMode = ResizeMode.NoResize;
            Background = System.Windows.Media.Brushes.White;
            
            // Create layout
            var grid = new System.Windows.Controls.Grid();
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // Title
            var titleText = new System.Windows.Controls.TextBlock
            {
                Text = "POS System",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(20)
            };
            System.Windows.Controls.Grid.SetRow(titleText, 0);
            grid.Children.Add(titleText);

            // Message
            _messageText = new System.Windows.Controls.TextBlock
            {
                Text = "Starting up...",
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(20, 10, 20, 10)
            };
            System.Windows.Controls.Grid.SetRow(_messageText, 1);
            grid.Children.Add(_messageText);

            // Progress bar
            _progressBar = new System.Windows.Controls.ProgressBar
            {
                Height = 20,
                Margin = new Thickness(40, 10, 40, 10),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            System.Windows.Controls.Grid.SetRow(_progressBar, 2);
            grid.Children.Add(_progressBar);

            // Percentage
            _percentageText = new System.Windows.Controls.TextBlock
            {
                Text = "0%",
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(20, 5, 20, 20)
            };
            System.Windows.Controls.Grid.SetRow(_percentageText, 3);
            grid.Children.Add(_percentageText);

            Content = grid;
        }

        public void UpdateProgress(ProgressInfo progressInfo)
        {
            if (progressInfo.IsError)
            {
                _messageText.Text = progressInfo.Message;
                _messageText.Foreground = System.Windows.Media.Brushes.Red;
                return;
            }

            _messageText.Text = progressInfo.Message;
            _progressBar.Value = progressInfo.Percentage;
            _percentageText.Text = $"{progressInfo.Percentage}%";

            if (progressInfo.TotalSteps > 0)
            {
                _percentageText.Text += $" ({progressInfo.CurrentStep}/{progressInfo.TotalSteps})";
            }
        }
    }

    /// <summary>
    /// Data structures for progressive loading
    /// </summary>
    public class ProgressInfo
    {
        public string Message { get; set; }
        public int Percentage { get; set; }
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public bool IsError { get; set; }
    }

    public class LoadingStep
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsRequired { get; set; }
        public Func<CancellationToken, Task> ExecuteFunc { get; set; }
    }
}
