<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.Controls.NotificationPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="350">

    <UserControl.Resources>
        <!-- Removed TimeAgoConverter since we're using localized strings directly -->
    </UserControl.Resources>

    <Border Background="White" 
            CornerRadius="8" 
            BorderBrush="#E0E0E0" 
            BorderThickness="1"
            Effect="{DynamicResource MaterialDesignElevationShadow2}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Background="#F5F5F5" Height="48">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="{DynamicResource Notifications}" 
                         Margin="16,0,0,0"
                         VerticalAlignment="Center"
                         FontWeight="SemiBold"
                         FontSize="16"/>
                
                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Height="32" 
                        Margin="8,0"
                        Click="MarkAllAsRead_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CheckAll" 
                                               Width="16" 
                                               Height="16"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="{DynamicResource MarkAllAsRead}"/>
                    </StackPanel>
                </Button>
            </Grid>

            <!-- Notifications List -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto"
                         MaxHeight="400">
                <ItemsControl x:Name="NotificationsList">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White" 
                                    BorderBrush="#E0E0E0" 
                                    BorderThickness="0,0,0,1"
                                    Padding="16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Notification Icon -->
                                    <Border Width="40" 
                                            Height="40" 
                                            CornerRadius="20"
                                            Background="{Binding IconBackground}">
                                        <materialDesign:PackIcon Kind="{Binding IconKind}"
                                                               Width="20"
                                                               Height="20"
                                                               Foreground="White"/>
                                    </Border>

                                    <!-- Notification Content -->
                                    <StackPanel Grid.Column="1" 
                                              Margin="12,0,0,0">
                                        <TextBlock Text="{Binding Title}"
                                                 FontWeight="SemiBold"/>
                                        <TextBlock Text="{Binding Message}"
                                                 TextWrapping="Wrap"
                                                 Foreground="#666666"
                                                 Margin="0,4,0,0"/>
                                        <TextBlock Text="{Binding TimeAgo}"
                                                 Foreground="#999999"
                                                 FontSize="11"
                                                 Margin="0,4,0,0"/>
                                    </StackPanel>

                                    <!-- Action Button -->
                                    <Button Grid.Column="2"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            Height="32"
                                            Margin="8,0,0,0"
                                            Command="{Binding ActionCommand}">
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </Button>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>

            <!-- Footer -->
            <Button Grid.Row="2"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Height="48"
                    Click="ViewAll_Click">
                <TextBlock Text="{DynamicResource ViewAllNotifications}" 
                         Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            </Button>
        </Grid>
    </Border>
</UserControl> 