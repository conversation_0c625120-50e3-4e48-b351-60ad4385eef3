-- August Test Sales Data
-- Creates sales for August 1st, 2025 to test the Month filter

-- Create sales for today (August 1st, 2025) at different times
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount,
                  TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems)
VALUES
  -- Morning sales
  ('AUG-001', '2025-08-01 09:15:00', 1, 1, 125.50, 0.00, 0.00, 125.50, 125.50, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('AUG-002', '2025-08-01 10:30:00', NULL, 1, 89.99, 0.00, 0.00, 89.99, 90.00, 0.01, 'Cash', 'Paid', 'Completed', 1),
  ('AUG-003', '2025-08-01 11:45:00', 2, 1, 234.75, 23.48, 0.00, 211.27, 211.27, 0.00, 'Card', 'Paid', 'Completed', 3),

  -- Afternoon sales
  ('AUG-004', '2025-08-01 13:20:00', 1, 1, 67.50, 0.00, 0.00, 67.50, 70.00, 2.50, 'Cash', 'Paid', 'Completed', 1),
  ('AUG-005', '2025-08-01 14:15:00', 1, 1, 156.25, 0.00, 0.00, 156.25, 156.25, 0.00, 'Mobile', 'Paid', 'Completed', 2),
  ('AUG-006', '2025-08-01 15:45:00', NULL, 1, 45.99, 0.00, 0.00, 45.99, 50.00, 4.01, 'Cash', 'Paid', 'Completed', 1),

  -- Evening sales
  ('AUG-007', '2025-08-01 17:30:00', 2, 1, 298.50, 0.00, 0.00, 298.50, 298.50, 0.00, 'Card', 'Paid', 'Completed', 4),
  ('AUG-008', '2025-08-01 18:15:00', 1, 1, 78.25, 7.83, 0.00, 70.42, 70.42, 0.00, 'Card', 'Paid', 'Completed', 2),
  ('AUG-009', '2025-08-01 19:00:00', NULL, 1, 112.75, 0.00, 0.00, 112.75, 115.00, 2.25, 'Cash', 'Paid', 'Completed', 3);

-- Add sale items for each sale
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total)
VALUES
  -- AUG-001 items (2 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-001'), 1, 1, 75.50, 75.50),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-001'), 2, 1, 50.00, 50.00),

  -- AUG-002 items (1 item)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-002'), 1, 1, 89.99, 89.99),

  -- AUG-003 items (3 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-003'), 2, 1, 125.00, 125.00),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-003'), 1, 2, 35.00, 70.00),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-003'), 3, 1, 39.75, 39.75),

  -- AUG-004 items (1 item)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-004'), 2, 1, 67.50, 67.50),

  -- AUG-005 items (2 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-005'), 1, 1, 99.99, 99.99),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-005'), 3, 1, 56.26, 56.26),

  -- AUG-006 items (1 item)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-006'), 2, 1, 45.99, 45.99),

  -- AUG-007 items (4 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-007'), 1, 2, 75.50, 151.00),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-007'), 2, 1, 67.50, 67.50),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-007'), 3, 1, 45.00, 45.00),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-007'), 1, 1, 35.00, 35.00),

  -- AUG-008 items (2 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-008'), 2, 1, 42.50, 42.50),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-008'), 3, 1, 35.75, 35.75),

  -- AUG-009 items (3 items)
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-009'), 1, 1, 65.00, 65.00),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-009'), 2, 1, 25.50, 25.50),
  ((SELECT Id FROM Sales WHERE InvoiceNumber = 'AUG-009'), 3, 1, 22.25, 22.25);

-- Verify the data was inserted
SELECT 'Sales created for August 1st, 2025:' AS Message;
SELECT 
    InvoiceNumber,
    SaleDate,
    GrandTotal,
    PaymentMethod,
    Status
FROM Sales 
WHERE date(SaleDate) = '2025-08-01'
ORDER BY SaleDate;

SELECT 'Total sales amount for August 1st:' AS Message;
SELECT 
    COUNT(*) AS TotalSales,
    SUM(GrandTotal) AS TotalAmount
FROM Sales 
WHERE date(SaleDate) = '2025-08-01';
