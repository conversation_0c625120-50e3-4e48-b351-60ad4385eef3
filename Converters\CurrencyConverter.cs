using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class CurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return $"{decimalValue:N2} DA";
            }
            else if (value is double doubleValue)
            {
                return $"{doubleValue:N2} DA";
            }
            return "0.00 DA";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                stringValue = stringValue.Replace("DA", "").Trim();
                if (decimal.TryParse(stringValue, out decimal result))
                {
                    return result;
                }
            }
            return 0.00m;
        }
    }
} 