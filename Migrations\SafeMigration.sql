-- SAFE MIGRATION: Add Weight-Based Product Support
-- This version includes extra safety checks and detailed logging
-- Execute this in SQL Server Management Studio

SET NOCOUNT ON;
PRINT '=== STARTING WEIGHT-BASED PRODUCT MIGRATION ===';
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';

-- Begin transaction for safety
BEGIN TRANSACTION WeightBasedMigration;

BEGIN TRY
    -- =====================================================
    -- SAFETY CHECK: Verify required tables exist
    -- =====================================================
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Products')
    BEGIN
        RAISERROR('Products table does not exist. Cannot proceed with migration.', 16, 1);
        RETURN;
    END
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SaleItems')
    BEGIN
        RAISERROR('SaleItems table does not exist. Cannot proceed with migration.', 16, 1);
        RETURN;
    END
    
    PRINT '✅ Required tables verified';
    
    -- =====================================================
    -- STEP 1: Add IsWeightBased column to Products table
    -- =====================================================
    
    PRINT '';
    PRINT 'STEP 1: Adding IsWeightBased column to Products table...';
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
    BEGIN
        ALTER TABLE Products 
        ADD IsWeightBased BIT NOT NULL DEFAULT 0;
        
        PRINT '✅ Added IsWeightBased column to Products table';
        
        -- Verify the column was added
        IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'IsWeightBased')
        BEGIN
            PRINT '✅ Verified: IsWeightBased column exists';
        END
        ELSE
        BEGIN
            RAISERROR('Failed to add IsWeightBased column', 16, 1);
        END
    END
    ELSE
    BEGIN
        PRINT '⚠️  IsWeightBased column already exists in Products table';
    END
    
    -- =====================================================
    -- STEP 2: Update SaleItems table for decimal quantities
    -- =====================================================
    
    PRINT '';
    PRINT 'STEP 2: Updating SaleItems.Quantity to support decimal values...';
    
    -- Check current data type
    DECLARE @CurrentDataType VARCHAR(50);
    SELECT @CurrentDataType = DATA_TYPE 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';
    
    PRINT 'Current SaleItems.Quantity data type: ' + @CurrentDataType;
    
    IF @CurrentDataType = 'int'
    BEGIN
        -- Count existing records for safety
        DECLARE @SaleItemCount INT;
        SELECT @SaleItemCount = COUNT(*) FROM SaleItems;
        PRINT 'Found ' + CAST(@SaleItemCount AS VARCHAR(10)) + ' existing sale items';
        
        -- Update the column type
        ALTER TABLE SaleItems 
        ALTER COLUMN Quantity DECIMAL(18,3) NOT NULL;
        
        PRINT '✅ Updated SaleItems.Quantity to decimal(18,3)';
        
        -- Verify the change
        SELECT @CurrentDataType = DATA_TYPE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';
        
        IF @CurrentDataType = 'decimal'
        BEGIN
            PRINT '✅ Verified: SaleItems.Quantity is now decimal';
        END
        ELSE
        BEGIN
            RAISERROR('Failed to update SaleItems.Quantity data type', 16, 1);
        END
    END
    ELSE IF @CurrentDataType = 'decimal'
    BEGIN
        PRINT '✅ SaleItems.Quantity is already decimal type';
    END
    ELSE
    BEGIN
        PRINT '⚠️  SaleItems.Quantity has unexpected data type: ' + @CurrentDataType;
    END
    
    -- =====================================================
    -- STEP 3: Create performance indexes
    -- =====================================================
    
    PRINT '';
    PRINT 'STEP 3: Creating performance indexes...';
    
    -- Index for weight-based product queries
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Products_IsWeightBased')
    BEGIN
        CREATE INDEX IX_Products_IsWeightBased ON Products (IsWeightBased);
        PRINT '✅ Created index IX_Products_IsWeightBased';
    END
    ELSE
    BEGIN
        PRINT '✅ Index IX_Products_IsWeightBased already exists';
    END
    
    -- =====================================================
    -- STEP 4: Set default values for existing products
    -- =====================================================
    
    PRINT '';
    PRINT 'STEP 4: Setting default values for existing products...';
    
    -- Count products that need updating
    DECLARE @ProductsToUpdate INT;
    SELECT @ProductsToUpdate = COUNT(*) 
    FROM Products 
    WHERE IsWeightBased IS NULL;
    
    IF @ProductsToUpdate > 0
    BEGIN
        UPDATE Products 
        SET IsWeightBased = 0 
        WHERE IsWeightBased IS NULL;
        
        PRINT '✅ Updated ' + CAST(@ProductsToUpdate AS VARCHAR(10)) + ' products to unit-based (IsWeightBased = 0)';
    END
    ELSE
    BEGIN
        PRINT '✅ All existing products already have IsWeightBased values';
    END
    
    -- =====================================================
    -- STEP 5: Verification
    -- =====================================================
    
    PRINT '';
    PRINT 'STEP 5: Final verification...';
    
    -- Verify Products table
    SELECT 
        'Products Summary' as TableInfo,
        COUNT(*) as TotalProducts,
        SUM(CASE WHEN IsWeightBased = 1 THEN 1 ELSE 0 END) as WeightBasedProducts,
        SUM(CASE WHEN IsWeightBased = 0 THEN 1 ELSE 0 END) as UnitBasedProducts
    FROM Products;
    
    -- Verify SaleItems column
    SELECT 
        'SaleItems.Quantity' as ColumnInfo,
        DATA_TYPE,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'SaleItems' AND COLUMN_NAME = 'Quantity';
    
    -- Commit the transaction
    COMMIT TRANSACTION WeightBasedMigration;
    
    PRINT '';
    PRINT '🎉 MIGRATION COMPLETED SUCCESSFULLY! 🎉';
    PRINT '';
    PRINT 'Summary of changes:';
    PRINT '- Added IsWeightBased column to Products table';
    PRINT '- Updated SaleItems.Quantity to decimal(18,3)';
    PRINT '- Created performance indexes';
    PRINT '- Set all existing products to unit-based by default';
    PRINT '';
    PRINT 'Your POS system now supports weight-based products!';
    PRINT 'Next steps:';
    PRINT '1. Build and run your application';
    PRINT '2. Test creating weight-based products';
    PRINT '3. Test decimal quantities in sales';
    
END TRY
BEGIN CATCH
    -- Rollback on error
    ROLLBACK TRANSACTION WeightBasedMigration;
    
    PRINT '';
    PRINT '❌ MIGRATION FAILED - TRANSACTION ROLLED BACK';
    PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10));
    PRINT 'Error Message: ' + ERROR_MESSAGE();
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10));
    PRINT '';
    PRINT 'Your database has been restored to its original state.';
    PRINT 'Please review the error and try again.';
    
    -- Re-raise the error
    THROW;
END CATCH

SET NOCOUNT OFF;
