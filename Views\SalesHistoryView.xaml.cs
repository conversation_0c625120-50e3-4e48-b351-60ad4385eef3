using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services.Interfaces;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using System.Windows.Controls.Primitives;
using System.Collections.Generic;
using System.Windows.Media;
using System.Linq;
using System.Windows.Threading;

namespace POSSystem.Views
{
    public partial class SalesHistoryView : UserControl
    {
        private SalesHistoryViewModel ViewModel => (SalesHistoryViewModel)DataContext;
        private bool _isLoadingMore;

        public SalesHistoryView()
        {
            InitializeComponent();
            DataContext = new SalesHistoryViewModel();
            
            // Cleanup when the view is unloaded
            Unloaded += (s, e) =>
            {
                if (DataContext is SalesHistoryViewModel vm)
                {
                    // ✅ CRITICAL MEMORY FIX: Use proper disposal pattern
                    vm.Dispose();
                }
            };
            
            // Subscribe to Loaded event to check for any UI issues
            Loaded += SalesHistoryView_Loaded;
        }

        private void SalesHistoryView_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("[SALES-HISTORY-VIEW] SalesHistoryView_Loaded called");

            // ✅ CRITICAL FIX: Initialize default filter to ensure UI and ViewModel synchronization
            if (DataContext is SalesHistoryViewModel vm)
            {
                // Ensure the filter matches the ComboBox default selection (Today)
                vm.InitializeDefaultFilter();
            }

            // ✅ CRITICAL FIX: Move heavy visual tree operations to background to prevent UI blocking
            _ = Task.Run(async () =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("[SALES-HISTORY-VIEW] Starting background visual tree cleanup");

                    // Perform heavy visual tree operations in background
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        CheckForLoadingText(this);
                    }, System.Windows.Threading.DispatcherPriority.Background);

                    // Schedule additional checks with lower priority to avoid blocking
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        CheckForLoadingText(this);
                        CleanupLoadingTextElements();
                    }, System.Windows.Threading.DispatcherPriority.ApplicationIdle);

                    System.Diagnostics.Debug.WriteLine("[SALES-HISTORY-VIEW] Background visual tree cleanup completed");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY-VIEW] Error in background cleanup: {ex.Message}");
                }
            });

            System.Diagnostics.Debug.WriteLine("[SALES-HISTORY-VIEW] SalesHistoryView_Loaded completed");
        }

        // ✅ CRITICAL FIX: Separate method for heavy visual tree operations
        private void CleanupLoadingTextElements()
        {
            try
            {
                // Special handling for any static "Loading Data" text
                var allTextBlocks = FindVisualChildren<TextBlock>(this).ToList();
                foreach (var textBlock in allTextBlocks)
                {
                    string text = textBlock.Text?.Trim();
                    if (!string.IsNullOrEmpty(text) &&
                        (text.Contains("Loading Data") || text == "...Loading Data" || text == "Loading..." || text == "..."))
                    {
                        textBlock.Visibility = Visibility.Collapsed;

                        // Also hide parent container if it doesn't have other visible children
                        if (VisualTreeHelper.GetParent(textBlock) is FrameworkElement parent)
                        {
                            var hasOtherVisibleChildren = false;

                            // Check if parent has other visible children
                            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                            {
                                var child = VisualTreeHelper.GetChild(parent, i);
                                if (child != textBlock && child is UIElement uiElement && uiElement.Visibility == Visibility.Visible)
                                {
                                    hasOtherVisibleChildren = true;
                                    break;
                                }
                            }

                            if (!hasOtherVisibleChildren)
                            {
                                parent.Visibility = Visibility.Collapsed;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY-VIEW] Error cleaning up loading text: {ex.Message}");
            }
        }

        // ✅ CRITICAL FIX: Optimized helper method to reduce UI blocking
        private void CheckForLoadingText(DependencyObject parent)
        {
            try
            {
                // ✅ PERFORMANCE FIX: Only update layout if absolutely necessary
                if (parent is UIElement element && !element.IsArrangeValid)
                {
                    element.UpdateLayout();
                }

                // ✅ PERFORMANCE FIX: Limit depth to prevent excessive traversal
                CheckForLoadingTextRecursive(parent, 0, maxDepth: 10);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES-HISTORY-VIEW] Error in CheckForLoadingText: {ex.Message}");
            }
        }

        private void CheckForLoadingTextRecursive(DependencyObject parent, int currentDepth, int maxDepth)
        {
            if (currentDepth >= maxDepth) return;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                
                // Common loading indicator patterns
                bool hideElement = false;
                
                // Check TextBlock
                if (child is TextBlock textBlock)
                {
                    string text = textBlock.Text?.ToLower() ?? "";
                    if (text.Contains("loading") || text.Contains("load") || text == "..." || 
                        text.StartsWith("...") || text.Contains("data"))
                    {
                        hideElement = true;
                    }
                }
                
                // Check ContentControl (Button, Label, etc.)
                if (child is ContentControl contentControl)
                {
                    if (contentControl.Content is string contentText)
                    {
                        string text = contentText.ToLower();
                        if (text.Contains("loading") || text.Contains("load") || text == "..." || 
                            text.StartsWith("...") || text.Contains("data"))
                        {
                            hideElement = true;
                        }
                    }
                }
                
                // Apply hiding if needed
                if (hideElement && child is UIElement uiElement)
                {
                    uiElement.Visibility = Visibility.Collapsed;
                }

                // Continue recursively with depth limit
                CheckForLoadingTextRecursive(child, currentDepth + 1, maxDepth);
            }
        }

        private void PeriodFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is SalesHistoryViewModel vm && cbPeriod.SelectedItem is ComboBoxItem item)
            {
                vm.UpdateSalesFilter(item.Content.ToString());
            }
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is SalesHistoryViewModel vm)
            {
                vm.UpdateCustomDateRange(dpStartDate.SelectedDate, dpEndDate.SelectedDate);
            }
        }

        private void Search_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (DataContext is SalesHistoryViewModel vm)
            {
                vm.SearchText = txtSearch.Text;
            }
        }

        private void Sale_Selected(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid grid && grid.SelectedItem is Sale sale)
            {
                ViewModel.SelectedSale = sale;
            }
        }

        private async void DataGrid_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // Check if we're near the bottom of the scroll
                bool isNearBottom = scrollViewer.VerticalOffset >= scrollViewer.ScrollableHeight * 0.8;

                if (isNearBottom && !_isLoadingMore && ViewModel != null)
                {
                    _isLoadingMore = true;
                    try
                    {
                        await ViewModel.LoadMoreItems();
                    }
                    finally
                    {
                        _isLoadingMore = false;
                    }
                }
            }
        }

        private async void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var sale = (Sale)button.DataContext;

            try
            {
                // Show enhanced sale details dialog
                var dialog = new POSSystem.Views.Dialogs.SaleDetailsDialog(sale);

                // Try different DialogHost identifiers based on context
                string[] dialogIdentifiers = { "RootDialog", "MainWindowCashDrawerDialog" };
                bool dialogShown = false;

                foreach (var identifier in dialogIdentifiers)
                {
                    try
                    {
                        // Close any existing dialogs first
                        if (MaterialDesignThemes.Wpf.DialogHost.IsDialogOpen(identifier))
                        {
                            MaterialDesignThemes.Wpf.DialogHost.Close(identifier);
                            await Task.Delay(100);
                        }

                        await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, identifier);
                        dialogShown = true;
                        break;
                    }
                    catch (InvalidOperationException)
                    {
                        // This DialogHost identifier doesn't exist, try the next one
                        continue;
                    }
                }

                // If no DialogHost worked, try without identifier
                if (!dialogShown)
                {
                    await MaterialDesignThemes.Wpf.DialogHost.Show(dialog);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error viewing sale details: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async void ReprintReceipt_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var sale = (Sale)button.DataContext;

            if (sale == null)
            {
                MessageBox.Show("No sale selected for receipt printing.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // Disable button during printing
                button.IsEnabled = false;
                var printingText = Application.Current.FindResource("PrintingStatus") as string ?? "Printing...";
                button.Content = printingText;

                // Show options dialog for receipt printing
                var title = Application.Current.FindResource("ReprintReceiptDialog") as string ?? "Reprint Receipt";
                var messageFormat = Application.Current.FindResource("ReprintReceiptFullMessage") as string ?? "Reprint receipt for Invoice #{0}?\n\nClick Yes to print directly, No to show print dialog, or Cancel to abort.";
                var message = string.Format(messageFormat, sale.InvoiceNumber);

                var result = await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(
                    message,
                    title,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNoCancel,
                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Cancel)
                {
                    return;
                }

                bool showDialog = result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.No;
                bool success = await ViewModel.ReprintReceiptAsync(sale, showDialog);

                if (!success)
                {
                    var failureTitle = Application.Current.FindResource("PrintResult") as string ?? "Print Result";
                    var failureMessage = Application.Current.FindResource("ReceiptPrintingFailed") as string ?? "Receipt printing failed or was cancelled.";

                    await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(
                        failureMessage,
                        failureTitle,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.OK,
                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error reprinting receipt: {ex.Message}",
                    "Print Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                // Re-enable button
                button.IsEnabled = true;
                button.Content = "Reprint Receipt";
            }
        }

        private async void PreviewReceipt_Click(object sender, RoutedEventArgs e)
        {
            Sale sale = null;

            // Get sale from context menu or button
            if (sender is MenuItem menuItem)
            {
                // Get selected sale from DataGrid
                sale = SalesDataGrid.SelectedItem as Sale;
            }
            else if (sender is Button button)
            {
                sale = button.DataContext as Sale;
            }

            if (sale == null)
            {
                MessageBox.Show("No sale selected for receipt preview.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                await ViewModel.PreviewReceiptAsync(sale);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error previewing receipt: {ex.Message}",
                    "Preview Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async void SaveReceiptAsPdf_Click(object sender, RoutedEventArgs e)
        {
            Sale sale = null;

            // Get sale from context menu or button
            if (sender is MenuItem menuItem)
            {
                // Get selected sale from DataGrid
                sale = SalesDataGrid.SelectedItem as Sale;
            }
            else if (sender is Button button)
            {
                sale = button.DataContext as Sale;
            }

            if (sale == null)
            {
                MessageBox.Show("No sale selected for PDF export.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                await ViewModel.SaveReceiptAsPdfAsync(sale);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error saving receipt as PDF: {ex.Message}",
                    "Save Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void VoidRefund_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var sale = (Sale)button.DataContext;

            // Determine the correct flow direction
            FlowDirection direction = FlowDirection.LeftToRight;
            if (Application.Current.MainWindow.FlowDirection == FlowDirection.RightToLeft)
            {
                direction = FlowDirection.RightToLeft;
            }
            
            // Set the DataContext for the popup content
            if (voidRefundPopup.Child is Card card)
            {
                card.DataContext = sale;
                
                // Set flow direction for the card
                card.FlowDirection = direction;
                
                // Find all elements within the popup and set their flow direction
                foreach (var textBlock in FindVisualChildren<TextBlock>(card))
                {
                    textBlock.FlowDirection = direction;
                }
                
                // Find and update all buttons in the popup
                var buttons = FindVisualChildren<Button>(card).ToList();
                foreach (var btn in buttons)
                {
                    btn.FlowDirection = direction;
                    
                    // Ensure we have the right language for button content
                    if (btn.Name == "cancelButton")
                    {
                        btn.Content = Application.Current.TryFindResource("Cancel");
                    }
                    else if (btn.Name == "confirmButton")
                    {
                        btn.Content = Application.Current.TryFindResource("Confirm");
                    }
                }
                
                // Find and explicitly set Cancel and Confirm button content
                foreach (var stackPanel in FindVisualChildren<StackPanel>(card))
                {
                    stackPanel.FlowDirection = direction;
                }
                
                // Find buttons by name and set their content explicitly
                Button cancelBtn = FindVisualChildren<Button>(card).FirstOrDefault(b => b.Name == "cancelButton");
                if (cancelBtn != null)
                {
                    cancelBtn.Content = Application.Current.TryFindResource("Cancel");
                }
                
                Button confirmBtn = FindVisualChildren<Button>(card).FirstOrDefault(b => b.Name == "confirmButton");
                if (confirmBtn != null)
                {
                    confirmBtn.Content = Application.Current.TryFindResource("Confirm");
                }
            }

            // Set flow direction for direct references to elements
            voidRefundReasonComboBox.FlowDirection = direction;
            voidRefundCommentTextBox.FlowDirection = direction;
            
            // Load void/refund reasons into ComboBox with properly localized strings
            voidRefundReasonComboBox.ItemsSource = new[]
            {
                Application.Current.TryFindResource("VoidSale") as string,
                Application.Current.TryFindResource("FullRefund") as string,
                Application.Current.TryFindResource("PartialRefund") as string
            };
            voidRefundReasonComboBox.SelectedIndex = 0;
            
            // Clear the comment textbox
            voidRefundCommentTextBox.Text = string.Empty;
            
            voidRefundPopup.IsOpen = true;
        }

        // Helper method to find all visual children of a specific type
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        private void CloseDetails_Click(object sender, RoutedEventArgs e)
        {
            saleDetailsPopup.IsOpen = false;
        }

        private void CloseVoidRefund_Click(object sender, RoutedEventArgs e)
        {
            voidRefundPopup.IsOpen = false;
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is SalesHistoryViewModel vm)
            {
                vm.RefreshSales();
            }
        }

        private async void ConfirmVoidRefund_Click(object sender, RoutedEventArgs e)
        {
            if (voidRefundPopup.Child is Card card && card.DataContext is Sale sale)
            {
                if (string.IsNullOrWhiteSpace(voidRefundCommentTextBox.Text))
                {
                    MessageBox.Show(
                        Application.Current.TryFindResource("PleaseProvideReason") as string,
                        Application.Current.TryFindResource("ValidationError") as string,
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                bool success = false;
                var selectedReason = voidRefundReasonComboBox.SelectedItem as string;

                if (selectedReason == Application.Current.TryFindResource("VoidSale") as string)
                {
                    success = ViewModel.VoidSale(sale, voidRefundCommentTextBox.Text);
                }
                else if (selectedReason == Application.Current.TryFindResource("FullRefund") as string)
                {
                    success = ViewModel.RefundSale(sale, sale.GrandTotal, voidRefundCommentTextBox.Text);
                }
                else if (selectedReason == Application.Current.TryFindResource("PartialRefund") as string)
                {
                    // For partial refund, we should show another dialog to enter the amount
                    // This is simplified for now - you might want to add a proper amount input dialog
                    var amount = sale.GrandTotal / 2; // Example: 50% refund
                    success = ViewModel.RefundSale(sale, amount, voidRefundCommentTextBox.Text);
                }

                if (success)
                {
                    voidRefundPopup.IsOpen = false;
                    MessageBox.Show(
                        Application.Current.TryFindResource("OperationSuccessful") as string,
                        Application.Current.TryFindResource("Success") as string,
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
        }
    }
} 