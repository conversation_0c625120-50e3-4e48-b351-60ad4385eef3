﻿using POSSystem.Models;
using POSSystem.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Xml.Linq;
using POSSystem.Services;
using POSSystem.Data;
using Microsoft.EntityFrameworkCore;
using POSSystem.Helpers;
using Microsoft.Win32;
using POSSystem.Views.Dialogs;
using MaterialDesignThemes.Wpf;
using POSSystem.Views;

namespace POSSystem.Views
{
    /// <summary>
    /// Interaction logic for ProductsView.xaml
    /// </summary>
    public partial class ProductsView : UserControl
    {
        private ProductsViewModel ViewModel => (ProductsViewModel)DataContext;

        public ProductsView()
        {
            InitializeComponent();

            // Don't create ViewModel here - let <PERSON><PERSON><PERSON><PERSON> set it via DI
            // This prevents conflicts and ensures we use the shared instance

            // Only load data once when the view is first created, not every time it's shown
            Loaded += ProductsView_Loaded;

            // Enable virtualization for better performance
            var scrollViewer = GetDescendantByType<ScrollViewer>(this);
            if (scrollViewer != null)
            {
                scrollViewer.IsDeferredScrollingEnabled = true;
                scrollViewer.CanContentScroll = true;
            }

            // Subscribe to sale completed event
            ViewModels.SaleViewModel.SaleCompleted += OnSaleCompleted;

            // Unsubscribe when control is unloaded
            this.Unloaded += (s, e) => ViewModels.SaleViewModel.SaleCompleted -= OnSaleCompleted;
        }

        private bool _isDataLoaded = false;

        private async void ProductsView_Loaded(object sender, RoutedEventArgs e)
        {
            // Only load data once, not every time the view is shown
            if (!_isDataLoaded && ViewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Loading initial data for the first time");

                // Set initial filter now that ViewModel is available
                if (StockFilter.Items.Count > 0)
                {
                    StockFilter.SelectedIndex = 0;
                    if (StockFilter.SelectedItem is ComboBoxItem item)
                    {
                        ViewModel.SelectedStockFilter = item.Tag?.ToString();
                    }
                }

                // ✅ FIX: Only load categories, not products (which would overwrite correctly loaded product data)
                // Products are already loaded by LoadPagedProductsAsync during ViewModel initialization
                await ViewModel.LoadCategoriesOnly();
                _isDataLoaded = true;

                // Debug: Check if categories loaded
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Categories loaded: {ViewModel.Categories?.Count ?? 0}");

                // Force UI update for category combobox if needed
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    if (ViewModel.Categories?.Count > 0 && CategoryFilter.Items.Count == 0)
                    {
                        CategoryFilter.GetBindingExpression(ComboBox.ItemsSourceProperty)?.UpdateTarget();
                        CategoryFilter.Items.Refresh();
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] CategoryFilter.Items.Count after refresh: {CategoryFilter.Items.Count}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Render);
            }
            else if (ViewModel != null)
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] View loaded but data already exists - skipping reload");
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Existing categories: {ViewModel.Categories?.Count ?? 0}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] View loaded but ViewModel is null");
            }
        }



        private void OnSaleCompleted(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[PRODUCTSVIEW] *** OnSaleCompleted called! Sender: {sender?.GetType()?.Name}, ViewModel: {ViewModel?.GetType()?.Name}");

            // Refresh the products view
            if (ViewModel != null)
            {
                _ = Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[PRODUCTSVIEW] Sale completed - refreshing product list");
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTSVIEW] Current Products count: {ViewModel.Products?.Count ?? 0}");

                        // Force a complete refresh by clearing cache and reloading
                        await ForceRefreshProducts();

                        System.Diagnostics.Debug.WriteLine($"[PRODUCTSVIEW] Product list refreshed after sale completion. New count: {ViewModel.Products?.Count ?? 0}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PRODUCTSVIEW] Error refreshing products after sale: {ex.Message}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Background);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTSVIEW] ViewModel is null - cannot refresh products");
            }
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // The binding will already update the ViewModel, so we don't need to manually set it
            // This prevents double-updates that can cause issues
            // If we need any additional logic on text changed, it can go here
        }

        private void SearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                if (ViewModel != null)
                {
                    // Force immediate search on Enter key
                    ViewModel.TriggerImmediateSearch();
                }
            }
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel != null)
            {
                ViewModel.SelectedCategory = CategoryFilter.SelectedItem as Category;
            }
        }

        private void StockFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel != null && sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem item)
            {
                // Update the filter in the ViewModel
                ViewModel.SelectedStockFilter = item.Tag?.ToString();
            }
        }

        // Helper method to find descendant control by type
        private static T GetDescendantByType<T>(DependencyObject element) where T : DependencyObject
        {
            if (element == null) return null;
            if (element is T) return element as T;
            DependencyObject foundElement = null;
            if (element is FrameworkElement)
            {
                (element as FrameworkElement).ApplyTemplate();
            }
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                DependencyObject visual = VisualTreeHelper.GetChild(element, i);
                foundElement = GetDescendantByType<T>(visual);
                if (foundElement != null)
                    break;
            }
            return foundElement as T;
        }

        private void NumberValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            e.Handled = !decimal.TryParse(((TextBox)sender).Text + e.Text, out _);
        }

        private void IntegerValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            e.Handled = !int.TryParse(((TextBox)sender).Text + e.Text, out _);
        }

        private async void AddNewProduct_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Opening add product dialog...");
            var dialog = new ProductDialog(ViewModel, "RootDialog");
            var result = await DialogHost.Show(dialog, "RootDialog");

            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Dialog result: {result?.GetType().Name ?? "null"}");

            if (result is Product newProduct)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Product returned from dialog - ID: {newProduct.Id}, Name: {newProduct.Name}");

                // Force a complete refresh to ensure the new product appears
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Forcing complete refresh...");

                try
                {
                    // Reset to first page to ensure we see the new product
                    ViewModel.CurrentPage = 1;

                    // Clear any search filters that might hide the new product
                    ViewModel.SearchText = string.Empty;
                    ViewModel.SelectedCategory = null;
                    ViewModel.SelectedStockFilter = null;

                    // Add a small delay to ensure database transaction is committed
                    await Task.Delay(100);

                    // Force refresh using the database refresh method
                    await ViewModel.ForceRefreshFromDatabase();

                    System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Product list refreshed successfully");

                    var successMessage = FindResource("DialogProductSavedSuccess") as string ?? "Product saved successfully!";
                    var successTitle = FindResource("DialogSuccessTitle") as string ?? "Success";
                    MessageBox.Show(successMessage, successTitle, MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Error during refresh: {ex.Message}");
                    MessageBox.Show($"Product was added but there was an error refreshing the list: {ex.Message}",
                        "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Dialog was cancelled or returned null");
            }
        }

        private async void EditProduct_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var product = (Product)button.DataContext;

            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Opening edit dialog for product ID: {product.Id}, Name: {product.Name}");

            var dialog = new ProductDialog(ViewModel, "RootDialog", product);
            var result = await DialogHost.Show(dialog, "RootDialog");

            System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Edit dialog result: {result?.GetType().Name ?? "null"}");

            if (result is Product updatedProduct)
            {
                System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Product returned from edit dialog - ID: {updatedProduct.Id}, Name: {updatedProduct.Name}");

                // The product has already been updated in the database by the dialog
                // Just refresh the product list in the view model
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Refreshing product list after edit...");
                await ViewModel.LoadPagedProducts();

                var successMessage = FindResource("DialogProductSavedSuccess") as string ?? "Product saved successfully!";
                var successTitle = FindResource("DialogSuccessTitle") as string ?? "Success";
                MessageBox.Show(successMessage, successTitle, MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Edit dialog was cancelled or returned null");
            }
        }

        private async void DeleteProduct_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var product = (Product)button.DataContext;

            var confirmTemplate = FindResource("DialogConfirmDeleteProduct") as string ?? "Are you sure you want to delete this product?";
            var confirmMessage = confirmTemplate;
            
            if (!string.IsNullOrEmpty(product.Name))
            {
                // If the message has a product name placeholder, format it; otherwise use generic message
                confirmMessage = confirmTemplate.Contains("{0}") ? 
                    string.Format(confirmTemplate, product.Name) : 
                    $"{confirmTemplate} - {product.Name}";
            }
            
            var confirmTitle = FindResource("DialogConfirmTitle") as string ?? "Confirm Delete";
            
            var result = MessageBox.Show(
                confirmMessage,
                confirmTitle,
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ViewModel.DeleteProduct(product.Id);
                    await ViewModel.LoadPagedProducts();
                    
                    var successMessage = FindResource("DialogProductDeletedSuccess") as string ?? "Product deleted successfully!";
                    var successTitle = FindResource("DialogSuccessTitle") as string ?? "Success";
                    MessageBox.Show(successMessage, successTitle, MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    var errorTemplate = FindResource("DialogErrorDeletingProduct") as string ?? "Error deleting product: {0}";
                    var errorMessage = string.Format(errorTemplate, ex.Message);
                    var errorTitle = FindResource("DialogErrorTitle") as string ?? "Error";
                    MessageBox.Show(errorMessage, errorTitle, MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void ManageBatches_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var product = (Product)button.DataContext;

            // Open the batch management window for this product
            var dbService = (DatabaseService)App.ServiceProvider?.GetService(typeof(DatabaseService));
            var batchStockWindow = new BatchStockWindow(product, dbService);
            batchStockWindow.Owner = Window.GetWindow(this);
            batchStockWindow.ShowDialog();
            
            // Refresh the products list after managing batches
            await ViewModel.LoadPagedProducts();
            
            // Log for debugging
            System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Refreshed product data after managing batches for '{product.Name}' (ID: {product.Id})");
            
            var refreshedProduct = ViewModel.Products.FirstOrDefault(p => p.Id == product.Id);
            if (refreshedProduct != null)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH DEBUG] Updated product '{refreshedProduct.Name}': StockQuantity={refreshedProduct.StockQuantity}, BatchCount={refreshedProduct.Batches?.Count ?? 0}, BatchStockTotal={(refreshedProduct.Batches?.Sum(b => b.Quantity) ?? 0)}");
            }
        }

        private void PrintBarcodes_Click(object sender, RoutedEventArgs e)
        {
            // Open the BarcodePrintingWindow with the DatabaseService
            var dbService = (DatabaseService)App.ServiceProvider?.GetService(typeof(DatabaseService));
            var barcodePrintingWindow = new BarcodePrintingWindow(dbService);

            // Show the window
            barcodePrintingWindow.Owner = Window.GetWindow(this);
            barcodePrintingWindow.ShowDialog();
        }

        private async void RefreshProducts_Click(object sender, RoutedEventArgs e)
        {
            await ForceRefreshProducts();
        }

        private async Task ForceRefreshProducts()
        {
            if (ViewModel != null)
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Starting force refresh...");

                    // Reset the data loaded flag to force a complete refresh
                    _isDataLoaded = false;

                    // Use the ViewModel's force refresh method for better database handling
                    await ViewModel.ForceRefreshFromDatabase();

                    // ✅ FIX: Only reload categories, not products (which would overwrite correct batch data)
                    // await ViewModel.LoadInitialData(); // ❌ REMOVED: This overwrites correct product data
                    await ViewModel.LoadCategoriesOnly(); // ✅ Load only categories

                    // Mark as loaded again
                    _isDataLoaded = true;

                    System.Diagnostics.Debug.WriteLine("[PRODUCTS_VIEW] Force refresh completed");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PRODUCTS_VIEW] Error during force refresh: {ex.Message}");
                    MessageBox.Show($"Error refreshing products: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }
}
