using System;
using System.Globalization;
using System.Windows.Data;
using POSSystem.Models;

namespace POSSystem.Converters
{
    public class StockQuantityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Product product)
            {
                // ✅ FIX: Use StockQuantity directly since it's already calculated correctly during loading
                // This avoids additional database queries that GetTotalStockDecimal() would make
                var stockDecimal = product.StockQuantity;
                var result = stockDecimal % 1 == 0 ? stockDecimal.ToString("F0") : stockDecimal.ToString("N3");

                // 🔍 DEBUG: Log converter values to identify UI display issues
                System.Diagnostics.Debug.WriteLine($"[STOCK-CONVERTER] Product {product.Id} ({product.Name}): StockQuantity = {stockDecimal}, Display = {result}, TrackBatches = {product.TrackBatches}, BatchesLoaded = {product.Batches?.Count ?? 0}");

                return result;
            }
            return "0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 