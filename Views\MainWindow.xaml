<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="POSSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POSSystem.Views"
        xmlns:controls="clr-namespace:POSSystem.Views.Controls"
        xmlns:vm="clr-namespace:POSSystem.ViewModels"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource AppName}" Height="720" Width="1280"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="CanResize"
        Icon="pack://application:,,,/Icons/point-of-sale.ico"
        Style="{StaticResource AppWindowStyle}">
    
    <Window.Resources>
        <!-- Drop Shadow Effects -->
        <DropShadowEffect x:Key="CardShadow" BlurRadius="10" ShadowDepth="1" Direction="315" Color="#********" Opacity="0.2"/>
        <DropShadowEffect x:Key="SmallShadow" BlurRadius="4" ShadowDepth="1" Direction="315" Color="#10000000" Opacity="0.15"/>
        
        <!-- Gradients -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="{DynamicResource SystemBackgroundColor}" Offset="0"/>
            <GradientStop Color="{DynamicResource SystemBackgroundLightColor}" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="AccentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="{DynamicResource PrimaryHueMidColor}" Offset="0"/>
            <GradientStop Color="{DynamicResource PrimaryHueDarkColor}" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- New background gradient for main app background -->
        <LinearGradientBrush x:Key="AppBackgroundGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="{DynamicResource AppMainBackgroundColor}" Offset="0"/>
            <GradientStop Color="{DynamicResource AppSecondaryBackgroundColor}" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- System Colors for Light Theme -->
        <Color x:Key="SystemBackgroundColor">#FFFFFF</Color>
        <Color x:Key="SystemBackgroundLightColor">#F8F9FA</Color>
        <Color x:Key="SystemBackgroundMediumColor">#F0F0F0</Color>
        
        <!-- Dynamic Background Colors (Light/Dark theme aware) -->
        <!-- Light Theme Colors -->
        <Color x:Key="AppMainBackgroundColorLight">#EEF2F7</Color>
        <Color x:Key="AppSecondaryBackgroundColorLight">#F5F8FC</Color>
        
        <!-- Dark Theme Colors -->
        <Color x:Key="AppMainBackgroundColorDark">#1E1E1E</Color>
        <Color x:Key="AppSecondaryBackgroundColorDark">#2D2D30</Color>
        
        <!-- Dynamic Background Colors that respond to theme changes -->
        <Color x:Key="AppMainBackgroundColor">#EEF2F7</Color>
        <Color x:Key="AppSecondaryBackgroundColor">#F5F8FC</Color>
        
        <Color x:Key="SystemForegroundColor">#333333</Color>
        <Color x:Key="SystemForegroundMediumColor">#707070</Color>
        <Color x:Key="SystemForegroundLightColor">#909090</Color>
        <Color x:Key="SystemBorderColor">#E8E8E8</Color>
        <Color x:Key="SystemHoverColor">#E8F4F6</Color>
        <Color x:Key="SystemAccentLightColor">#E8F4F6</Color>
        <Color x:Key="SystemAccentMediumColor">#D6EDF1</Color>
        <Color x:Key="SystemAccentDarkColor">#C2E6EB</Color>
        <Color x:Key="SystemAlertColor">#FF5252</Color>
        
        <!-- SolidColorBrushes for convenience -->
        <SolidColorBrush x:Key="SystemBackgroundBrush" Color="{DynamicResource SystemBackgroundColor}"/>
        <SolidColorBrush x:Key="SystemBackgroundLightBrush" Color="{DynamicResource SystemBackgroundLightColor}"/>
        <SolidColorBrush x:Key="SystemBackgroundMediumBrush" Color="{DynamicResource SystemBackgroundMediumColor}"/>
        <SolidColorBrush x:Key="AppMainBackgroundBrush" Color="{DynamicResource AppMainBackgroundColor}"/>
        <SolidColorBrush x:Key="AppSecondaryBackgroundBrush" Color="{DynamicResource AppSecondaryBackgroundColor}"/>
        <SolidColorBrush x:Key="SystemForegroundBrush" Color="{DynamicResource SystemForegroundColor}"/>
        <SolidColorBrush x:Key="SystemForegroundMediumBrush" Color="{DynamicResource SystemForegroundMediumColor}"/>
        <SolidColorBrush x:Key="SystemForegroundLightBrush" Color="{DynamicResource SystemForegroundLightColor}"/>
        <SolidColorBrush x:Key="SystemBorderBrush" Color="{DynamicResource SystemBorderColor}"/>
        <SolidColorBrush x:Key="SystemHoverBrush" Color="{DynamicResource SystemHoverColor}"/>
        <SolidColorBrush x:Key="SystemAccentLightBrush" Color="{DynamicResource SystemAccentLightColor}"/>
        <SolidColorBrush x:Key="SystemAccentMediumBrush" Color="{DynamicResource SystemAccentMediumColor}"/>
        <SolidColorBrush x:Key="SystemAccentDarkBrush" Color="{DynamicResource SystemAccentDarkColor}"/>
        <SolidColorBrush x:Key="SystemAlertBrush" Color="{DynamicResource SystemAlertColor}"/>
        
        <!-- Notification Button Style (updated) -->
        <Style x:Key="NotificationButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButton}">
            <Setter Property="Background" Value="Transparent"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource SystemAccentLightColor}" Opacity="0.9"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <DataTrigger Binding="{Binding ElementName=NotificationPopup, Path=IsOpen}" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource SystemAccentLightColor}" Opacity="0.9"/>
                        </Setter.Value>
                    </Setter>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        
        <!-- Improved Sidebar Icon Button Styles with light theme -->
        <Style x:Key="SidebarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="40"/>
            <Setter Property="Margin" Value="0,4"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="ToolTipService.InitialShowDelay" Value="800"/>
            <Setter Property="ToolTipService.ShowDuration" Value="3000"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Border x:Name="border" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="10">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="{DynamicResource PrimaryHueLightColor}" Opacity="0.1"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" 
                                                           To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" 
                                                           To="1.05" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" 
                                                           To="1.0" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" 
                                                           To="1.0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- New Active Sidebar Button Style -->
        <Style x:Key="SidebarButtonActive" TargetType="Button" BasedOn="{StaticResource SidebarButton}">
            <Setter Property="Background">
                <Setter.Value>
                    <SolidColorBrush Color="{DynamicResource PrimaryHueLightColor}" Opacity="0.15"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Border x:Name="background" 
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="10">
                                <ContentPresenter HorizontalAlignment="Center" 
                                                VerticalAlignment="Center"/>
                            </Border>
                            <Border x:Name="highlight"
                                    Width="3"
                                    Height="20"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Margin="0,0,0,0"
                                    Background="{DynamicResource PrimaryHueMidBrush}"
                                    CornerRadius="1.5"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="background" Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="{DynamicResource PrimaryHueLightColor}" Opacity="0.25"/>
                                    </Setter.Value>
                                </Setter>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" 
                                                           To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" 
                                                           To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="highlight" 
                                                           Storyboard.TargetProperty="Height" 
                                                           To="24" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" 
                                                           To="1.0" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" 
                                                           To="1.0" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="highlight" 
                                                           Storyboard.TargetProperty="Height" 
                                                           To="20" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Improved Window Control Button-->
        <Style x:Key="WindowControlButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="Width" Value="36"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Card Style for Content Panels -->
        <Style x:Key="ContentCard" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemBackgroundBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemBorderBrush}"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Margin" Value="12,8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="10" ShadowDepth="1" Direction="315" Color="#********" Opacity="0.18"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Stat Card Style -->
        <Style x:Key="StatCard" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemBackgroundBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemBorderBrush}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="8" ShadowDepth="1" Direction="315" Color="#15000000" Opacity="0.15"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- New Enhanced Grid Style -->
        <Style x:Key="EnhancedGridStyle" TargetType="Grid">
            <Setter Property="Background" Value="{DynamicResource SystemBackgroundBrush}"/>
            <Setter Property="Margin" Value="4"/>
        </Style>
        
        <!-- Enhanced Data Grid Container Style -->
        <Style x:Key="DataGridContainer" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SystemBackgroundBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemBorderBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10,6"/>
            <Setter Property="Padding" Value="2"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="8" ShadowDepth="1" Direction="315" Color="#15000000" Opacity="0.15"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1" BorderBrush="{DynamicResource MaterialDesignDivider}" Background="{DynamicResource AppBackgroundGradient}">
        <materialDesign:DialogHost Identifier="MainWindowCashDrawerDialog">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="64"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Updated Light Theme Header Bar -->
                <Border Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown" Background="{DynamicResource MaterialDesignPaper}" BorderThickness="0,0,0,1" BorderBrush="{DynamicResource MaterialDesignDivider}">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="4" Opacity="0.1" Direction="270"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Modernized Window Title and Store Info -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="20,0,0,0" VerticalAlignment="Center">
                            <Border Width="36" Height="36" CornerRadius="8" Margin="0,0,12,0">
                                <Border.Background>
                                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                        <GradientStop Color="{DynamicResource PrimaryHueMidColor}" Offset="0"/>
                                        <GradientStop Color="{DynamicResource PrimaryHueDarkColor}" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <Border.Effect>
                                    <DropShadowEffect BlurRadius="4" ShadowDepth="1" Direction="315" Color="#30000000" Opacity="0.2"/>
                                </Border.Effect>
                                <materialDesign:PackIcon Kind="Store" Width="20" Height="20" Foreground="{DynamicResource PrimaryHueMidForegroundBrush}" 
                                                       VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="POS System" Foreground="{DynamicResource MaterialDesignBody}" FontSize="15" FontWeight="SemiBold"/>
                                <TextBlock x:Name="txtStoreName" Text="Store Name" Foreground="{DynamicResource MaterialDesignBodyLight}" FontSize="12">
                                    <TextBlock.Opacity>0.9</TextBlock.Opacity>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>

                        <!-- Enhanced Center Status Information -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Border CornerRadius="8" Padding="12,6" Margin="0,0,12,0" Background="{DynamicResource MaterialDesignCardBackground}" BorderThickness="1" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Clock" Width="16" Height="16" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    <TextBlock x:Name="txtClock" Text="12:00 PM" Foreground="{DynamicResource MaterialDesignBody}" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            <Border CornerRadius="8" Padding="12,6" Background="{DynamicResource MaterialDesignCardBackground}" BorderThickness="1" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Timer" Width="16" Height="16" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    <TextBlock x:Name="txtSessionTime" Text="Session: 02:45:30" Foreground="{DynamicResource MaterialDesignBody}" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- Updated User Information -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,16,0">
                            <!-- Settings -->
                            <Button x:Name="btnSettings"
                                    Style="{StaticResource WindowControlButton}"
                                    Width="36" Height="36"
                                    Padding="0"
                                    Click="btnSettings_Click"
                                    ToolTip="{DynamicResource Settings}">
                                <materialDesign:PackIcon Kind="Cog" Width="18" Height="18" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Button>

                            <!-- ✅ Debug Log Viewer -->
                            <Button x:Name="btnDebugLogs"
                                    Style="{StaticResource WindowControlButton}"
                                    Width="36" Height="36"
                                    Padding="0"
                                    Click="btnDebugLogs_Click"
                                    ToolTip="Debug Log Viewer">
                                <materialDesign:PackIcon Kind="FileDocumentOutline" Width="18" Height="18" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Button>

                            <!-- Improved Notifications button -->
                            <Grid Margin="6,0">
                                <Button x:Name="btnNotifications" 
                                        Style="{StaticResource NotificationButtonStyle}"
                                        Width="36" Height="36"
                                        Padding="0"
                                        Click="btnNotifications_Click">
                                    <Grid>
                                        <materialDesign:PackIcon Kind="Bell" Width="18" Height="18" Foreground="{DynamicResource SystemForegroundMediumBrush}"/>
                                    </Grid>
                                </Button>
                                
                                <Grid HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-2,-2,0">
                                    <Border x:Name="notificationBadge"
                                            Background="{DynamicResource SystemAlertBrush}"
                                            CornerRadius="10"
                                            MinWidth="18"
                                            Height="18"
                                            Visibility="Collapsed">
                                        <Border.Effect>
                                            <DropShadowEffect BlurRadius="2" ShadowDepth="1" Direction="315" Color="#30000000" Opacity="0.2"/>
                                        </Border.Effect>
                                        <TextBlock x:Name="notificationCount"
                                                  Text="0"
                                                  Foreground="White"
                                                  FontSize="10"
                                                  FontWeight="Bold"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Margin="4,0"/>
                                    </Border>
                                </Grid>
                                
                                <controls:NotificationPopup x:Name="NotificationPopup" 
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Top"
                                                          PlacementTarget="{Binding ElementName=btnNotifications}"/>
                            </Grid>
                            
                            <!-- Enhanced User Profile with Dropdown -->
                            <Border CornerRadius="20" Padding="8,4" Background="{DynamicResource MaterialDesignCardBackground}" BorderThickness="1" BorderBrush="{DynamicResource MaterialDesignDivider}" Margin="8,0,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0"
                                            Width="32" Height="32" 
                                            CornerRadius="16" 
                                            Background="{DynamicResource MaterialDesignCardBackground}">
                                        <Border.Effect>
                                            <DropShadowEffect BlurRadius="4" ShadowDepth="1" Direction="315" Color="#********" Opacity="0.15"/>
                                        </Border.Effect>
                                        <Grid>
                                            <materialDesign:PackIcon Kind="AccountCircle" 
                                                                   Width="22" 
                                                                   Height="22"
                                                                   x:Name="defaultUserIcon"
                                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                            <Image x:Name="userPhoto" 
                                                   Stretch="UniformToFill"
                                                   Visibility="Collapsed"/>
                                        </Grid>
                                    </Border>

                                    <StackPanel Grid.Column="1" 
                                                VerticalAlignment="Center" 
                                                Margin="8,0,0,0">
                                        <TextBlock x:Name="txtUserName" 
                                                   Text="John Doe" 
                                                   Foreground="{DynamicResource MaterialDesignBody}" 
                                                   FontSize="13" 
                                                   FontWeight="SemiBold"/>
                                        <TextBlock x:Name="txtUserRole" 
                                                   Text="Administrator" 
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}">
                                                    <TextBlock.Opacity>0.8</TextBlock.Opacity>
                                                </TextBlock>
                                    </StackPanel>

                                    <!-- User Menu Dropdown -->
                                    <materialDesign:PopupBox Grid.Column="2"
                                                            Margin="4,0,0,0"
                                                            PlacementMode="BottomAndAlignRightEdges"
                                                            StaysOpen="False"
                                                            Foreground="{DynamicResource SystemForegroundMediumBrush}">
                                        <materialDesign:PopupBox.ToggleContent>
                                            <materialDesign:PackIcon Kind="ChevronDown" 
                                                                   Width="16" 
                                                                   Height="16"/>
                                        </materialDesign:PopupBox.ToggleContent>
                                        <StackPanel>
                                            <Button x:Name="btnSwitchUser"
                                                    Style="{StaticResource MaterialDesignFlatButton}"
                                                    Click="btnSwitchUser_Click"
                                                    Height="36">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="AccountSwitch" 
                                                                           Width="18" 
                                                                           Height="18" 
                                                                           VerticalAlignment="Center"/>
                                                    <TextBlock Text="Switch User" 
                                                             Margin="8,0,0,0" 
                                                             VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Button>
                                            <Separator Margin="0,4"/>
                                            <Button x:Name="btnLogout"
                                                    Style="{StaticResource MaterialDesignFlatButton}"
                                                    Click="btnLogout_Click"
                                                    Height="36">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Logout" 
                                                                           Width="18" 
                                                                           Height="18" 
                                                                           Foreground="{DynamicResource SystemAlertBrush}"
                                                                           VerticalAlignment="Center"/>
                                                    <TextBlock Text="Logout" 
                                                             Foreground="{DynamicResource SystemAlertBrush}"
                                                             Margin="8,0,0,0" 
                                                             VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Button>
                                        </StackPanel>
                                    </materialDesign:PopupBox>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- Updated Window Controls -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="btnMinimize" 
                                    Style="{StaticResource WindowControlButton}"
                                    Width="36" Height="36"
                                    Padding="0"
                                    Click="btnMinimize_Click">
                                <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Button>
                            <Button x:Name="btnMaximize" 
                                    Style="{StaticResource WindowControlButton}"
                                    Width="36" Height="36"
                                    Padding="0"
                                    Click="btnMaximize_Click">
                                <materialDesign:PackIcon x:Name="maximizeIcon" Kind="WindowMaximize" Width="16" Height="16" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Button>
                            <Button x:Name="btnClose" 
                                    Style="{StaticResource WindowControlButton}"
                                    Width="36" Height="36"
                                    Padding="0"
                                    Click="btnClose_Click">
                                <materialDesign:PackIcon Kind="WindowClose" Width="16" Height="16" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Main Content -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition x:Name="sidebarColumn" Width="64"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Light, Modern Sidebar -->
                    <Border Grid.Column="0" Background="{DynamicResource MaterialDesignPaper}" BorderThickness="0,0,1,0" BorderBrush="{DynamicResource MaterialDesignDivider}">
                        <Grid>
                            <Grid.CacheMode>
                                <BitmapCache EnableClearType="True" SnapsToDevicePixels="True" />
                            </Grid.CacheMode>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="20"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Navigation Menu with UI Virtualization -->
                            <ScrollViewer Grid.Row="1" 
                                         x:Name="navigationScrollViewer"
                                         VerticalScrollBarVisibility="Auto"
                                         VirtualizingPanel.IsVirtualizing="True"
                                         VirtualizingPanel.VirtualizationMode="Recycling"
                                         VirtualizingPanel.CacheLength="2,2"
                                         VirtualizingPanel.CacheLengthUnit="Page"
                                         ScrollViewer.IsDeferredScrollingEnabled="True"
                                         Margin="0,10,0,0">
                                <StackPanel>
                                    <!-- Menu Groups -->
                                    <StackPanel x:Name="menuGroups" Margin="12,0">
                                        <!-- Main Menu -->
                                        <Button x:Name="btnDashboard"
                                                Click="btnDashboard_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Dashboard}">
                                            <materialDesign:PackIcon Kind="ViewDashboard" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnSales"
                                                Click="btnSales_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Sales}">
                                            <materialDesign:PackIcon Kind="CartOutline" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <!-- Separator -->
                                        <Separator Margin="0,8" Background="{DynamicResource MaterialDesignDivider}" Opacity="0.3"/>

                                        <!-- Inventory Group -->
                                        <Button x:Name="btnProducts"
                                                Click="btnProducts_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Products}">
                                            <materialDesign:PackIcon Kind="Package"
                                                                   Width="20"
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnCategories"
                                                Click="btnCategories_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Categories}">
                                            <materialDesign:PackIcon Kind="Shape"
                                                                   Width="20"
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnPurchaseOrders"
                                                Click="btnPurchaseOrders_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource PurchaseOrders}"
                                                Visibility="Collapsed">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ShoppingOutline" Width="24" Height="24" />
                                                <TextBlock Text="{DynamicResource PurchaseOrders}" Margin="12,0,0,0" />
                                            </StackPanel>
                                        </Button>

                                        <!-- Separator -->
                                        <Separator Margin="0,8" Background="{DynamicResource MaterialDesignDivider}" Opacity="0.3"/>

                                        <!-- Business Group -->
                                        <Button x:Name="btnCustomers"
                                                Click="btnCustomers_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Customers}">
                                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnUsers"
                                                Click="btnUsers_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource UserManagement}">
                                            <materialDesign:PackIcon Kind="AccountCog" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnSuppliers"
                                                Click="btnSuppliers_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Suppliers}">
                                            <materialDesign:PackIcon Kind="TruckDelivery" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <!-- Separator -->
                                        <Separator Margin="0,8" Background="{DynamicResource MaterialDesignDivider}" Opacity="0.3"/>

                                        <!-- Finance Group -->
                                        <Button x:Name="btnCashDrawer"
                                                Click="btnCashDrawer_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource CashDrawer}">
                                            <materialDesign:PackIcon Kind="CashRegister"
                                                                   Width="20"
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnExpenses"
                                                Click="btnExpenses_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource BusinessExpenses}">
                                            <materialDesign:PackIcon Kind="CashMultiple"
                                                                   Width="20"
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnInvoices"
                                                Click="btnInvoices_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="Invoices">
                                            <materialDesign:PackIcon Kind="FileDocument"
                                                                   Width="20"
                                                                   Height="20"/>
                                        </Button>

                                        <!-- Reports Group -->
                                        <Button x:Name="btnSalesHistory"
                                                Click="btnSalesHistory_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource SalesHistory}">
                                            <materialDesign:PackIcon Kind="History" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnUnpaidTransactions"
                                                Click="btnUnpaidTransactions_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource UnpaidTransactions}">
                                            <materialDesign:PackIcon Kind="CashClock" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>

                                        <Button x:Name="btnReports"
                                                Click="btnReports_Click"
                                                Style="{StaticResource SidebarButton}"
                                                ToolTip="{DynamicResource Reports}">
                                            <materialDesign:PackIcon Kind="ChartBar" 
                                                                   Width="20" 
                                                                   Height="20"/>
                                        </Button>
                                    </StackPanel>
                                </StackPanel>
                            </ScrollViewer>

                            <!-- Version Info with improved styling -->
                            <Border Grid.Row="2" 
                                  CornerRadius="8" Margin="10,0,10,16" Padding="0,4"
                                  Background="{DynamicResource MaterialDesignCardBackground}" 
                                  BorderThickness="1" 
                                  BorderBrush="{DynamicResource MaterialDesignDivider}">
                                <TextBlock x:Name="versionInfo"
                                          Text="v1.0.0" 
                                          Foreground="{DynamicResource MaterialDesignBodyLight}" 
                                          FontSize="11" 
                                          HorizontalAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Main Content Area with Cache -->
                    <Grid Grid.Column="1" Background="{DynamicResource AppBackgroundGradient}">
                        <Grid.CacheMode>
                            <BitmapCache />
                        </Grid.CacheMode>
                        <ContentControl x:Name="mainContent" 
                                      Grid.Column="1" 
                                      Margin="16"
                                      VirtualizingStackPanel.IsVirtualizing="True"
                                      VirtualizingStackPanel.VirtualizationMode="Recycling" />
                    </Grid>
                </Grid>
            </Grid>
        </materialDesign:DialogHost>
    </Border>
</Window> 