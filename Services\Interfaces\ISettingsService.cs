namespace POSSystem.Services.Interfaces
{
    /// <summary>
    /// Interface for application settings management
    /// </summary>
    public interface ISettingsService
    {
        /// <summary>
        /// Gets a setting value by key
        /// </summary>
        /// <param name="key">Setting key</param>
        /// <returns>Setting value or null if not found</returns>
        string GetSetting(string key);

        /// <summary>
        /// Sets a setting value
        /// </summary>
        /// <param name="key">Setting key</param>
        /// <param name="value">Setting value</param>
        void SetSetting(string key, string value);

        /// <summary>
        /// Removes a setting
        /// </summary>
        /// <param name="key">Setting key to remove</param>
        void RemoveSetting(string key);

        /// <summary>
        /// Checks if a setting exists
        /// </summary>
        /// <param name="key">Setting key</param>
        /// <returns>True if setting exists, false otherwise</returns>
        bool HasSetting(string key);

        /// <summary>
        /// Saves all settings to persistent storage
        /// </summary>
        void SaveSettings();

        /// <summary>
        /// Reloads settings from persistent storage
        /// </summary>
        void ReloadSettings();

        /// <summary>
        /// Saves a specific setting (alias for SetSetting)
        /// </summary>
        /// <param name="key">Setting key</param>
        /// <param name="value">Setting value</param>
        void SaveSetting(string key, string value);

        /// <summary>
        /// Gets the company logo path
        /// </summary>
        /// <returns>Company logo path</returns>
        string GetCompanyLogo();
    }
}
