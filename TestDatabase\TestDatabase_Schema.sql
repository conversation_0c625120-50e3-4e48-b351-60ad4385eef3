-- POS System Test Database - Schema Creation Script
-- This script creates the basic database structure for testing

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Create Audit table
CREATE TABLE IF NOT EXISTS Audit (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId INTEGER NOT NULL,
    TableName TEXT NOT NULL,
    RecordId INTEGER NOT NULL,
    Action TEXT NOT NULL,
    OldValues TEXT NOT NULL,
    NewValues TEXT NOT NULL,
    Timestamp TEXT NOT NULL,
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);

-- Create Roles table
CREATE TABLE IF NOT EXISTS Roles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT NOT NULL,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);

-- Create Users table
CREATE TABLE IF NOT EXISTS Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL,
    Password TEXT NOT NULL,
    FirstName TEXT NOT NULL,
    LastName TEXT NOT NULL,
    Email TEXT NOT NULL,
    Phone TEXT NOT NULL,
    PhotoPath TEXT NOT NULL DEFAULT 'default-user.png',
    RoleId INTEGER NOT NULL,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE RESTRICT
);

-- Create LoyaltyProgram table
CREATE TABLE IF NOT EXISTS LoyaltyPrograms (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT NOT NULL,
    PointsPerDollar NUMERIC(18,2) NOT NULL,
    MonetaryValuePerPoint NUMERIC(18,2) NOT NULL,
    PointsCalculationMethod TEXT NOT NULL DEFAULT 'PerDollar',
    MinimumPointsRedemption NUMERIC NOT NULL,
    ExpiryMonths INTEGER NOT NULL,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL
);

-- Create LoyaltyTier table
CREATE TABLE IF NOT EXISTS LoyaltyTiers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    LoyaltyProgramId INTEGER NOT NULL,
    MinimumPoints NUMERIC(18,2) NOT NULL,
    PointsMultiplier NUMERIC(18,2) NOT NULL,
    Benefits TEXT NOT NULL,
    FOREIGN KEY (LoyaltyProgramId) REFERENCES LoyaltyPrograms(Id) ON DELETE CASCADE
);

-- Create Customers table
CREATE TABLE IF NOT EXISTS Customers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    FirstName TEXT NOT NULL,
    LastName TEXT NOT NULL,
    Email TEXT NOT NULL,
    Phone TEXT NOT NULL,
    Address TEXT NOT NULL,
    LoyaltyCode TEXT NOT NULL,
    LoyaltyPoints NUMERIC DEFAULT 0,
    TotalSpent NUMERIC DEFAULT 0,
    LoyaltyTierId INTEGER,
    TotalVisits INTEGER DEFAULT 0,
    LastVisit TEXT,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (LoyaltyTierId) REFERENCES LoyaltyTiers(Id)
);

-- Create LoyaltyTransactions table
CREATE TABLE IF NOT EXISTS LoyaltyTransactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CustomerId INTEGER NOT NULL,
    Points NUMERIC(18,2) NOT NULL,
    Description TEXT NOT NULL,
    TransactionDate TEXT NOT NULL,
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id) ON DELETE CASCADE
);

-- Create Suppliers table
CREATE TABLE IF NOT EXISTS Suppliers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    ContactName TEXT NOT NULL,
    Email TEXT NOT NULL,
    Phone TEXT NOT NULL,
    Address TEXT NOT NULL,
    Website TEXT,
    Notes TEXT,
    ProductCount INTEGER DEFAULT 0,
    IsActive INTEGER DEFAULT 1,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (ContactName) REFERENCES Customers(Id)
);

-- Create Categories table
CREATE TABLE IF NOT EXISTS Categories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT NOT NULL,
    ParentCategoryId INTEGER,
    IsActive INTEGER NOT NULL,
    FOREIGN KEY (ParentCategoryId) REFERENCES Categories(Id)
);

-- Create UnitsOfMeasure table
CREATE TABLE IF NOT EXISTS UnitsOfMeasure (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Abbreviation TEXT NOT NULL,
    Type TEXT NOT NULL,
    BaseUnitId INTEGER,
    ConversionFactor NUMERIC(18,6),
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (BaseUnitId) REFERENCES UnitsOfMeasure(Id) ON DELETE RESTRICT
);

-- Create Products table
CREATE TABLE IF NOT EXISTS Products (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT,
    SKU TEXT,
    CategoryId INTEGER NOT NULL,
    UnitOfMeasureId INTEGER,
    SellingPrice NUMERIC(18,2) NOT NULL,
    PurchasePrice NUMERIC(18,2) NOT NULL,
    StockQuantity INTEGER NOT NULL,
    MinimumStock INTEGER NOT NULL,
    ReorderPoint INTEGER NOT NULL,
    SupplierId INTEGER,
    LoyaltyPoints NUMERIC NOT NULL,
    TrackBatches INTEGER DEFAULT 0,
    ExpiryDate TEXT,
    ImageData TEXT,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL,
    FOREIGN KEY (CategoryId) REFERENCES Categories(Id) ON DELETE CASCADE,
    FOREIGN KEY (UnitOfMeasureId) REFERENCES UnitsOfMeasure(Id) ON DELETE RESTRICT,
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
);

-- Create ProductBarcodes table
CREATE TABLE IF NOT EXISTS ProductBarcodes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    Barcode TEXT NOT NULL UNIQUE,
    Description TEXT NOT NULL,
    IsPrimary INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
);

-- Create BatchStock table
CREATE TABLE IF NOT EXISTS BatchStock (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    BatchNumber TEXT NOT NULL,
    Quantity INTEGER NOT NULL,
    PurchasePrice NUMERIC NOT NULL,
    ManufactureDate TEXT NOT NULL,
    ExpiryDate TEXT,
    Location TEXT,
    Notes TEXT,
    CreatedAt TEXT NOT NULL,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
);

-- Create ProductPrices table
CREATE TABLE IF NOT EXISTS ProductPrices (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    Price NUMERIC NOT NULL,
    PriceType TEXT NOT NULL,
    EffectiveDate TEXT NOT NULL,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
);

-- Create InventoryTransactions table
CREATE TABLE IF NOT EXISTS InventoryTransactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    TransactionType TEXT NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice NUMERIC NOT NULL,
    TransactionDate TEXT NOT NULL,
    Reference TEXT NOT NULL,
    Notes TEXT NOT NULL,
    UserId INTEGER NOT NULL,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
);

-- Create Sales table
CREATE TABLE IF NOT EXISTS Sales (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT NOT NULL,
    SaleDate TEXT NOT NULL,
    CustomerId INTEGER,
    UserId INTEGER NOT NULL,
    Subtotal NUMERIC(18,2) NOT NULL,
    DiscountAmount NUMERIC(18,2) NOT NULL,
    TaxAmount NUMERIC(18,2) NOT NULL,
    GrandTotal NUMERIC(18,2) NOT NULL,
    AmountPaid NUMERIC(18,2) NOT NULL,
    Change NUMERIC(18,2) NOT NULL,
    PaymentMethod TEXT NOT NULL,
    PaymentStatus TEXT NOT NULL,
    Status TEXT NOT NULL,
    TotalItems INTEGER NOT NULL,
    DueDate TEXT,
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id) ON DELETE RESTRICT,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE RESTRICT
);

-- Create SaleItems table
CREATE TABLE IF NOT EXISTS SaleItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice NUMERIC NOT NULL,
    Total NUMERIC NOT NULL,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
);

-- Create Payments table
CREATE TABLE IF NOT EXISTS Payments (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    Amount NUMERIC NOT NULL,
    PaymentMethod TEXT NOT NULL,
    ReferenceNumber TEXT NOT NULL,
    PaymentDate TEXT NOT NULL,
    Status TEXT NOT NULL,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE
);

-- Create SaleHistory table
CREATE TABLE IF NOT EXISTS SaleHistory (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SaleId INTEGER NOT NULL,
    UserId INTEGER NOT NULL,
    Action TEXT NOT NULL,
    ActionDate TEXT NOT NULL,
    Reason TEXT NOT NULL,
    AdjustmentAmount NUMERIC NOT NULL,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE RESTRICT
);

-- Create DiscountTypes table
CREATE TABLE IF NOT EXISTS DiscountTypes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Description TEXT NOT NULL
);

-- Create DiscountReasons table
CREATE TABLE IF NOT EXISTS DiscountReasons (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Code TEXT NOT NULL,
    Description TEXT NOT NULL,
    IsActive INTEGER NOT NULL
);

-- Create Discounts table
CREATE TABLE IF NOT EXISTS Discounts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    DiscountTypeId INTEGER NOT NULL,
    DiscountValue DECIMAL(10,2) NOT NULL,
    OriginalPrice DECIMAL(10,2) NOT NULL,
    FinalPrice DECIMAL(10,2) NOT NULL,
    ReasonId INTEGER NOT NULL,
    Comment TEXT NOT NULL,
    SaleId INTEGER,
    SaleItemId INTEGER,
    AppliedByUserId INTEGER NOT NULL,
    ApprovedByUserId INTEGER,
    AppliedAt TEXT NOT NULL,
    ApprovedAt TEXT,
    IsActive INTEGER NOT NULL,
    FOREIGN KEY (DiscountTypeId) REFERENCES DiscountTypes(Id) ON DELETE CASCADE,
    FOREIGN KEY (ReasonId) REFERENCES DiscountReasons(Id) ON DELETE CASCADE,
    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE RESTRICT,
    FOREIGN KEY (SaleItemId) REFERENCES SaleItems(Id) ON DELETE RESTRICT,
    FOREIGN KEY (AppliedByUserId) REFERENCES Users(Id) ON DELETE RESTRICT,
    FOREIGN KEY (ApprovedByUserId) REFERENCES Users(Id) ON DELETE RESTRICT
);

-- Create DiscountPermissions table
CREATE TABLE IF NOT EXISTS DiscountPermissions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    RoleId INTEGER NOT NULL,
    DiscountTypeId INTEGER NOT NULL,
    MaxPercentage NUMERIC(5,2),
    MaxFixedAmount NUMERIC(10,2),
    MinPricePercentage NUMERIC(5,2),
    RequiresApproval INTEGER NOT NULL,
    ApprovalThreshold NUMERIC,
    IsActive INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
    FOREIGN KEY (DiscountTypeId) REFERENCES DiscountTypes(Id) ON DELETE CASCADE
);

-- Create CashDrawers table
CREATE TABLE IF NOT EXISTS CashDrawers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OpeningBalance NUMERIC(18,2) NOT NULL,
    CurrentBalance NUMERIC(18,2) NOT NULL,
    ExpectedBalance NUMERIC(18,2) NOT NULL,
    ActualBalance NUMERIC(18,2) NOT NULL,
    Difference NUMERIC(18,2) NOT NULL,
    Status TEXT NOT NULL,
    OpenedAt TEXT NOT NULL,
    ClosedAt TEXT,
    OpenedById INTEGER NOT NULL,
    ClosedById INTEGER,
    Notes TEXT,
    FOREIGN KEY (OpenedById) REFERENCES Users(Id) ON DELETE RESTRICT,
    FOREIGN KEY (ClosedById) REFERENCES Users(Id) ON DELETE RESTRICT
);

-- Create CashTransactions table
CREATE TABLE IF NOT EXISTS CashTransactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CashDrawerId INTEGER NOT NULL,
    Type TEXT NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Timestamp TEXT NOT NULL,
    Reference TEXT,
    Reason TEXT,
    Notes TEXT,
    PerformedById INTEGER,
    FOREIGN KEY (CashDrawerId) REFERENCES CashDrawers(Id) ON DELETE CASCADE,
    FOREIGN KEY (PerformedById) REFERENCES Users(Id) ON DELETE RESTRICT
);

-- Create BusinessExpenses table
CREATE TABLE IF NOT EXISTS BusinessExpenses (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Description TEXT NOT NULL,
    Amount NUMERIC(18,2) NOT NULL,
    Date TEXT NOT NULL,
    Category INTEGER NOT NULL,
    Notes TEXT,
    Frequency INTEGER NOT NULL,
    NextDueDate TEXT,
    UserId INTEGER NOT NULL,
    CashDrawerId INTEGER,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE RESTRICT,
    FOREIGN KEY (CashDrawerId) REFERENCES CashDrawers(Id) ON DELETE RESTRICT
);

-- Create PurchaseOrders table
CREATE TABLE IF NOT EXISTS PurchaseOrders (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OrderNumber TEXT NOT NULL,
    OrderDate TEXT NOT NULL,
    DueDate TEXT NOT NULL,
    SupplierId INTEGER NOT NULL,
    Subtotal DECIMAL(18,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    GrandTotal DECIMAL(18,2) DEFAULT 0,
    Status TEXT DEFAULT 'Pending',
    Notes TEXT NOT NULL,
    CreatedByUserId INTEGER NOT NULL,
    PaymentMethod TEXT NOT NULL,
    PaymentReference TEXT NOT NULL,
    PaymentDate TEXT,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT,
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id) ON DELETE RESTRICT,
    FOREIGN KEY (CreatedByUserId) REFERENCES Users(Id)
);

-- Create PurchaseOrderItems table
CREATE TABLE IF NOT EXISTS PurchaseOrderItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PurchaseOrderId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    SellingPrice DECIMAL(18,2) NOT NULL,
    BatchNumber TEXT NOT NULL,
    Location TEXT NOT NULL,
    Notes TEXT NOT NULL,
    ExpiryDate TEXT,
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE RESTRICT
);

-- Create UserFavorites table
CREATE TABLE IF NOT EXISTS UserFavorites (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId INTEGER NOT NULL,
    ProductId INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE,
    UNIQUE(UserId, ProductId)
);

-- Create ProductAlerts table
CREATE TABLE IF NOT EXISTS ProductAlerts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    AlertType TEXT NOT NULL,
    Message TEXT NOT NULL,
    CreatedAt TEXT NOT NULL,
    IsRead INTEGER DEFAULT 0,
    ReadAt TEXT,
    ReferenceType TEXT NOT NULL,
    ReferenceId INTEGER,
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
);

-- Create indexes for most used search columns
CREATE INDEX IF NOT EXISTS idx_products_name ON Products(Name);
CREATE INDEX IF NOT EXISTS idx_products_sku ON Products(SKU);
CREATE INDEX IF NOT EXISTS idx_products_description ON Products(Description);
CREATE INDEX IF NOT EXISTS idx_products_name_sku ON Products(Name, SKU);
CREATE INDEX IF NOT EXISTS idx_products_sku_description ON Products(SKU, Description);
CREATE INDEX IF NOT EXISTS idx_barcodes_barcode ON ProductBarcodes(Barcode);
CREATE INDEX IF NOT EXISTS idx_customers_loyalty_code ON Customers(LoyaltyCode);
CREATE INDEX IF NOT EXISTS idx_sales_invoice_number ON Sales(InvoiceNumber); 