using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.ViewModels;

namespace POSSystem.Views.Dialogs
{
    public partial class ExternalProductImportDialog : UserControl
    {
        public Product ExternalProduct { get; private set; }
        public string Barcode { get; private set; }
        public bool ImportConfirmed { get; private set; }
        public bool OpenAddProductDialog { get; private set; }

        public ExternalProductImportDialog(Product externalProduct, string barcode)
        {
            InitializeComponent();
            ExternalProduct = externalProduct;
            Barcode = barcode;
            ImportConfirmed = false;
            OpenAddProductDialog = false;

            LoadProductData();
        }

        private void LoadProductData()
        {
            if (ExternalProduct == null) return;

            // Set product information
            txtProductName.Text = ExternalProduct.Name ?? "Unknown Product";
            txtSellingPrice.Text = ExternalProduct.SellingPrice.ToString("C");
            txtPurchasePrice.Text = ExternalProduct.PurchasePrice.ToString("C");
            txtCategory.Text = ExternalProduct.Category?.Name ?? "Uncategorized";
            txtBarcode.Text = Barcode ?? "No barcode";
            txtDescription.Text = !string.IsNullOrWhiteSpace(ExternalProduct.Description) 
                ? ExternalProduct.Description 
                : "No description available";

            // Load product image if available
            LoadProductImage();
        }

        private void LoadProductImage()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(ExternalProduct.ImageData))
                {
                    // Try to load image from base64 data
                    var imageBytes = Convert.FromBase64String(ExternalProduct.ImageData);
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.StreamSource = new MemoryStream(imageBytes);
                    bitmap.EndInit();
                    imgProduct.Source = bitmap;
                }
                else
                {
                    // Use default no-image placeholder
                    imgProduct.Source = new BitmapImage(new Uri("pack://application:,,,/Images/no-image.png"));
                }
            }
            catch (Exception)
            {
                // If image loading fails, use default placeholder
                try
                {
                    imgProduct.Source = new BitmapImage(new Uri("pack://application:,,,/Images/no-image.png"));
                }
                catch
                {
                    // If even the placeholder fails, leave it empty
                    imgProduct.Source = null;
                }
            }
        }

        private void btnImport_Click(object sender, RoutedEventArgs e)
        {
            ImportConfirmed = true;
            OpenAddProductDialog = true;
            DialogHost.Close("SalesDialog", this);
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            ImportConfirmed = false;
            DialogHost.Close("SalesDialog", false);
        }
    }
}
