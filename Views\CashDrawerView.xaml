<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.CashDrawerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POSSystem.Views"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             Background="{DynamicResource AppBackgroundGradient}">

    <UserControl.Resources>
        <converters:LessThanZeroConverter x:Key="LessThanZeroConverter"/>
        <converters:GreaterThanZeroConverter x:Key="GreaterThanZeroConverter"/>
        <converters:TransactionTypeConverter x:Key="TransactionTypeConverter"/>
        <converters:TransactionTypeToIconConverter x:Key="TransactionTypeToIconConverter"/>
        <converters:DrawerStatusConverter x:Key="DrawerStatusConverter"/>
    </UserControl.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid Margin="16" Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" UniformCornerRadius="8">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="{DynamicResource CashDrawerManagement}"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 FontWeight="Bold"
                                 Margin="0,0,0,8"/>
                        <TextBlock Text="{Binding Status, Converter={StaticResource DrawerStatusConverter}}"
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Command="{Binding OpenDrawerCommand}"
                                IsEnabled="{Binding CanOpenDrawer}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashRegister" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource OpenDrawer}"/>
                            </StackPanel>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Command="{Binding CloseDrawerCommand}"
                                IsEnabled="{Binding CanCloseDrawer}"
                                Background="{DynamicResource SecondaryHueMidBrush}"
                                Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashLock" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource CloseDrawer}"/>
                            </StackPanel>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="ViewHistory_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="History" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource ViewHistory}"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Balance Summary Cards -->
            <UniformGrid Grid.Row="1" Columns="3" Margin="0,0,0,16">
                <!-- Expected Balance Card -->
                <materialDesign:Card Margin="0,0,8,0" UniformCornerRadius="8"
                                   Background="{DynamicResource MaterialDesignBackground}">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="{DynamicResource ExpectedBalance}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                        <TextBlock Grid.Row="1"
                                 Text="{Binding ExpectedBalance, StringFormat={}{0:N2} DA}"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Actual Balance Card -->
                <materialDesign:Card Margin="8,0" UniformCornerRadius="8"
                                   Background="{DynamicResource MaterialDesignBackground}">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="{DynamicResource ActualBalance}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                        <TextBox Grid.Row="1"
                                Text="{Binding ActualBalance, StringFormat={}{0:N2}, UpdateSourceTrigger=PropertyChanged, ConverterCulture=en-US}"
                                Style="{StaticResource MaterialDesignFilledTextBox}"
                                FontSize="24"
                                FontWeight="Bold"
                                IsEnabled="{Binding IsDrawerOpen}"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Difference Card -->
                <materialDesign:Card Margin="8,0,0,0" UniformCornerRadius="8"
                                   Background="{DynamicResource MaterialDesignBackground}">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="{DynamicResource Difference}"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Opacity="0.6"/>
                        <TextBlock Grid.Row="1"
                                 Text="{Binding Difference, StringFormat={}{0:N2} DA}"
                                 FontWeight="Bold">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline4TextBlock}">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Difference}" Value="0">
                                            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Difference, Converter={StaticResource LessThanZeroConverter}}" Value="True">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignError}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Difference, Converter={StaticResource GreaterThanZeroConverter}}" Value="True">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignSuccess}"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Transactions List -->
            <materialDesign:Card Grid.Row="2" Margin="0,0,0,16" UniformCornerRadius="8">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Transactions Header -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="{DynamicResource Transactions}"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 FontWeight="Medium"/>

                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Command="{Binding AddPayoutCommand}"
                                IsEnabled="{Binding IsDrawerOpen}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashMinus" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource AddPayout}"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- Transactions DataGrid -->
                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding CurrentTransactions}"
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="{DynamicResource Time}"
                                              Binding="{Binding Timestamp, StringFormat=\{0:HH:mm:ss\}}"
                                              Width="Auto"/>
                            <DataGridTemplateColumn Header="{DynamicResource Type}"
                                                  Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="{Binding Type, Converter={StaticResource TransactionTypeToIconConverter}}"
                                                                   Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding Type, Converter={StaticResource TransactionTypeConverter}, ConverterParameter=Type}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="{DynamicResource Amount}"
                                              Binding="{Binding Amount, StringFormat={}{0:N2} DA}"
                                              Width="Auto">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Type}" Value="Out">
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignError}"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Type}" Value="In">
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignSuccess}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="{DynamicResource Reason}"
                                              Binding="{Binding Reason, Converter={StaticResource TransactionTypeConverter}, ConverterParameter=Reason}"
                                              Width="*"/>
                            <DataGridTextColumn Header="{DynamicResource Reference}"
                                              Binding="{Binding Reference}"
                                              Width="Auto"/>
                            <DataGridTextColumn Header="{DynamicResource PerformedBy}"
                                              Binding="{Binding PerformedBy.Username}"
                                              Width="Auto"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Summary -->
            <materialDesign:Card Grid.Row="3" UniformCornerRadius="8">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Opening Info -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="{DynamicResource OpeningInfo}"
                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                 FontWeight="Medium"
                                 Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="{DynamicResource OpenedBy}" Margin="0,0,16,4" Opacity="0.6"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding OpenedBy.Username}" FontWeight="Medium"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="{DynamicResource OpenedAt}" Margin="0,0,16,4" Opacity="0.6"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding OpenedAt, StringFormat=\{0:g\}}" FontWeight="Medium"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="{DynamicResource OpeningBalance}" Margin="0,0,16,4" Opacity="0.6"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding OpeningBalance, StringFormat={}{0:N2} DA}" FontWeight="Medium"/>
                        </Grid>
                    </StackPanel>

                    <!-- Current Totals -->
                    <StackPanel Grid.Column="1" Margin="32,0,0,0">
                        <TextBlock Text="{DynamicResource CurrentTotals}"
                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                 FontWeight="Medium"
                                 Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="{DynamicResource CashSales}" Margin="0,0,16,4" Opacity="0.6"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TotalCashSales, StringFormat={}{0:N2} DA}"
                                     Foreground="{DynamicResource MaterialDesignSuccess}" FontWeight="Medium"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="{DynamicResource Payouts}" Margin="0,0,16,4" Opacity="0.6"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TotalPayouts, StringFormat={}{0:N2} DA}"
                                     Foreground="{DynamicResource MaterialDesignError}" FontWeight="Medium"/>
                        </Grid>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </materialDesign:DialogHost>
</UserControl> 