using System.Windows.Controls;
using System.Text.RegularExpressions;
using System.Windows.Input;

namespace POSSystem.Views
{
    public partial class LoyaltyProgramView : UserControl
    {
        public LoyaltyProgramView()
        {
            InitializeComponent();
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            // Allow decimal numbers with up to 2 decimal places
            Regex regex = new Regex(@"^[0-9]*(?:\.[0-9]*)?$");
            string newText = ((TextBox)sender).Text + e.Text;
            e.Handled = !regex.IsMatch(newText);
        }

        private void IntegerValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            // Allow only integers
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }
    }
} 