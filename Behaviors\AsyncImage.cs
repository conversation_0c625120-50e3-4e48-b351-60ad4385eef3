using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using POSSystem.Services.Images;

namespace POSSystem.Behaviors
{
    /// <summary>
    /// Attached behavior that asynchronously decodes a base64 image at the desired size
    /// using ImageCacheService and assigns a frozen BitmapSource to Image.Source.
    /// Prevents UI thread stalls during list virtualization and scrolling.
    /// </summary>
    public static class AsyncImage
    {
        public static readonly DependencyProperty Base64SourceProperty =
            DependencyProperty.RegisterAttached(
                "Base64Source",
                typeof(string),
                typeof(AsyncImage),
                new PropertyMetadata(null, OnBase64SourceChanged));

        public static void SetBase64Source(Image element, string value) => element.SetValue(Base64SourceProperty, value);
        public static string GetBase64Source(Image element) => (string)element.GetValue(Base64SourceProperty);

        // Internal token to avoid race conditions with recycled containers
        private static readonly DependencyProperty LoadTokenProperty =
            DependencyProperty.RegisterAttached("LoadToken", typeof(Guid), typeof(AsyncImage), new PropertyMetadata(Guid.Empty));

        private static Guid GetLoadToken(DependencyObject obj) => (Guid)obj.GetValue(LoadTokenProperty);
        private static void SetLoadToken(DependencyObject obj, Guid value) => obj.SetValue(LoadTokenProperty, value);

        private static async void OnBase64SourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is not Image image)
                return;

            var token = Guid.NewGuid();
            SetLoadToken(image, token);

            // If cleared, also clear the image to show placeholder
            if (e.NewValue is not string base64 || string.IsNullOrWhiteSpace(base64))
            {
                image.Source = null;
                return;
            }

            try
            {
                // Determine intended display size
                int width = (int)Math.Max(1, image.Width > 0 ? image.Width : 75);
                int height = (int)Math.Max(1, image.Height > 0 ? image.Height : 75);

                var bmp = await ImageCacheService.Instance.GetFromBase64Async(base64, width, height);

                // Ensure this result is still relevant for this element
                if (GetLoadToken(image) != token)
                    return;

                image.Source = bmp;
                var mode = (width <= 100 && height <= 100) ? BitmapScalingMode.LowQuality : BitmapScalingMode.HighQuality;
                RenderOptions.SetBitmapScalingMode(image, mode);
                RenderOptions.SetCachingHint(image, CachingHint.Cache);
                image.UseLayoutRounding = true;
                image.SnapsToDevicePixels = true;
            }
            catch
            {
                // ignore - keep placeholder
                image.Source = null;
            }
        }
    }
}

