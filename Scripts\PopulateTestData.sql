-- =====================================================
-- POS System Test Data Population Script
-- =====================================================
-- This script populates the database with comprehensive test data
-- Run this script to add sample data for testing purposes

-- Clear existing data (except essential system data)
DELETE FROM SaleItems;
DELETE FROM Sales;
DELETE FROM PurchaseOrderItems;
DELETE FROM PurchaseOrders;
DELETE FROM Products WHERE Id > 0;
DELETE FROM Categories WHERE Id > 4; -- Keep default categories
DELETE FROM Customers WHERE Id > 0;
DELETE FROM Suppliers WHERE Id > 0;
DELETE FROM BusinessExpenses WHERE Id > 0;

-- Reset auto-increment counters
DELETE FROM sqlite_sequence WHERE name IN ('Sales', 'SaleItems', 'Products', 'Customers', 'Suppliers', 'PurchaseOrders', 'PurchaseOrderItems', 'BusinessExpenses');

-- =====================================================
-- SUPPLIERS
-- =====================================================
INSERT INTO Suppliers (Name, ContactName, Email, Phone, Address, Website, Notes, IsActive, ProductCount, CreatedAt, UpdatedAt) VALUES
('TechWorld Electronics', 'John Smith', '<EMAIL>', '555-0101', '123 Tech Street, San Francisco, CA 94105', 'www.techworld.com', 'Electronics supplier', 1, 0, datetime('now'), datetime('now')),
('Fresh Foods Wholesale', 'Maria Garcia', '<EMAIL>', '555-0102', '456 Market Ave, Los Angeles, CA 90210', 'www.freshfoods.com', 'Food and grocery supplier', 1, 0, datetime('now'), datetime('now')),
('Global Beverages Inc', 'David Chen', '<EMAIL>', '555-0103', '789 Drink Blvd, Chicago, IL 60601', 'www.globalbev.com', 'Beverage distributor', 1, 0, datetime('now'), datetime('now')),
('Office Supplies Plus', 'Sarah Johnson', '<EMAIL>', '555-0104', '321 Business Rd, New York, NY 10001', 'www.officesupplies.com', 'Office supplies', 1, 0, datetime('now'), datetime('now'));

-- =====================================================
-- CUSTOMERS
-- =====================================================
INSERT INTO Customers (FirstName, LastName, Email, Phone, Address, IsActive, LoyaltyCode, LoyaltyPoints, LastVisit, TotalVisits, TotalSpent, CreatedAt, UpdatedAt) VALUES
('Alice', 'Johnson', '<EMAIL>', '555-1001', '123 Main St, Springfield, IL 62701', 1, 'CUST001', '150.0', datetime('now', '-2 days'), 5, '245.50', datetime('now'), datetime('now')),
('Bob', 'Smith', '<EMAIL>', '555-1002', '456 Oak Ave, Springfield, IL 62702', 1, 'CUST002', '75.0', datetime('now', '-1 day'), 3, '125.75', datetime('now'), datetime('now')),
('Carol', 'Davis', '<EMAIL>', '555-1003', '789 Pine St, Springfield, IL 62703', 1, 'CUST003', '200.0', datetime('now', '-3 days'), 8, '389.25', datetime('now'), datetime('now')),
('David', 'Wilson', '<EMAIL>', '555-1004', '321 Elm Dr, Springfield, IL 62704', 1, 'CUST004', '50.0', datetime('now', '-5 days'), 2, '89.99', datetime('now'), datetime('now')),
('Emma', 'Brown', '<EMAIL>', '555-1005', '654 Maple Ln, Springfield, IL 62705', 1, 'CUST005', '125.0', datetime('now', '-1 day'), 4, '198.50', datetime('now'), datetime('now'));

-- =====================================================
-- PRODUCTS
-- =====================================================
-- Beverages
INSERT INTO Products (Name, SKU, Barcode, Description, PurchasePrice, SellingPrice, DefaultPrice, StockQuantity, MinimumStock, ReorderPoint, CategoryId, SupplierId, IsActive, TrackBatches, LoyaltyPoints, CreatedAt, UpdatedAt) VALUES
('Coca Cola 330ml', 'BEV001', '1234567890123', 'Classic Coca Cola can', '0.45', '1.25', '1.25', 150, 20, 40, 1, 3, 1, 0, '5.0', datetime('now'), datetime('now')),
('Pepsi 330ml', 'BEV002', '1234567890124', 'Pepsi cola can', '0.43', '1.20', '1.20', 120, 20, 40, 1, 3, 1, 0, '5.0', datetime('now'), datetime('now')),
('Orange Juice 1L', 'BEV003', '1234567890125', 'Fresh orange juice', '1.20', '2.50', '2.50', 80, 15, 30, 1, 3, 1, 0, '10.0', datetime('now'), datetime('now')),
('Water Bottle 500ml', 'BEV004', '1234567890126', 'Pure drinking water', '0.25', '0.75', '0.75', 200, 30, 60, 1, 3, 1, 0, '2.0', datetime('now'), datetime('now')),
('Energy Drink 250ml', 'BEV005', '1234567890127', 'High caffeine energy drink', '0.80', '2.00', '2.00', 60, 10, 20, 1, 3, 1, 0, '8.0', datetime('now'), datetime('now'));

-- Snacks
INSERT INTO Products (Name, SKU, Barcode, Description, PurchasePrice, SellingPrice, DefaultPrice, StockQuantity, MinimumStock, ReorderPoint, CategoryId, SupplierId, IsActive, TrackBatches, LoyaltyPoints, CreatedAt, UpdatedAt) VALUES
('Potato Chips 150g', 'SNK001', '2234567890123', 'Crispy potato chips', '0.85', '2.25', '2.25', 100, 15, 30, 2, 2, 1, 0, '8.0', datetime('now'), datetime('now')),
('Chocolate Bar 100g', 'SNK002', '2234567890124', 'Milk chocolate bar', '1.10', '2.75', '2.75', 80, 12, 25, 2, 2, 1, 0, '10.0', datetime('now'), datetime('now')),
('Peanuts 200g', 'SNK003', '2234567890125', 'Roasted salted peanuts', '1.50', '3.50', '3.50', 60, 10, 20, 2, 2, 1, 0, '12.0', datetime('now'), datetime('now')),
('Cookies Pack', 'SNK004', '2234567890126', 'Assorted cookies pack', '1.25', '3.00', '3.00', 70, 12, 25, 2, 2, 1, 0, '10.0', datetime('now'), datetime('now')),
('Granola Bar', 'SNK005', '2234567890127', 'Healthy granola bar', '0.95', '2.50', '2.50', 90, 15, 30, 2, 2, 1, 0, '8.0', datetime('now'), datetime('now'));

-- Electronics
INSERT INTO Products (Name, SKU, Barcode, Description, PurchasePrice, SellingPrice, DefaultPrice, StockQuantity, MinimumStock, ReorderPoint, CategoryId, SupplierId, IsActive, TrackBatches, LoyaltyPoints, CreatedAt, UpdatedAt) VALUES
('USB Cable Type-C', 'ELC001', '*************', 'USB Type-C charging cable', '2.50', '8.99', '8.99', 50, 8, 15, 3, 1, 1, 0, '15.0', datetime('now'), datetime('now')),
('Phone Charger', 'ELC002', '*************', 'Universal phone charger', '4.00', '12.99', '12.99', 40, 6, 12, 3, 1, 1, 0, '20.0', datetime('now'), datetime('now')),
('Bluetooth Earbuds', 'ELC003', '*************', 'Wireless bluetooth earbuds', '15.00', '39.99', '39.99', 25, 4, 8, 3, 1, 1, 0, '50.0', datetime('now'), datetime('now')),
('Power Bank 10000mAh', 'ELC004', '*************', 'Portable power bank', '12.00', '29.99', '29.99', 30, 5, 10, 3, 1, 1, 0, '40.0', datetime('now'), datetime('now')),
('Phone Case', 'ELC005', '*************', 'Protective phone case', '3.50', '9.99', '9.99', 60, 10, 20, 3, 1, 1, 0, '15.0', datetime('now'), datetime('now'));

-- Groceries
INSERT INTO Products (Name, SKU, Barcode, Description, PurchasePrice, SellingPrice, DefaultPrice, StockQuantity, MinimumStock, ReorderPoint, CategoryId, SupplierId, IsActive, TrackBatches, LoyaltyPoints, CreatedAt, UpdatedAt) VALUES
('Bread Loaf', 'GRO001', '*************', 'Fresh white bread', '1.20', '2.99', '2.99', 40, 8, 15, 4, 2, 1, 0, '8.0', datetime('now'), datetime('now')),
('Milk 1L', 'GRO002', '*************', 'Fresh whole milk', '1.50', '3.25', '3.25', 35, 6, 12, 4, 2, 1, 0, '10.0', datetime('now'), datetime('now')),
('Eggs Dozen', 'GRO003', '*************', 'Fresh farm eggs', '2.00', '4.50', '4.50', 50, 8, 16, 4, 2, 1, 0, '12.0', datetime('now'), datetime('now')),
('Bananas 1kg', 'GRO004', '*************', 'Fresh bananas', '1.80', '3.99', '3.99', 30, 5, 10, 4, 2, 1, 0, '10.0', datetime('now'), datetime('now')),
('Apples 1kg', 'GRO005', '4234567890127', 'Red apples', '2.20', '4.99', '4.99', 25, 4, 8, 4, 2, 1, 0, '12.0', datetime('now'), datetime('now'));

-- =====================================================
-- SAMPLE SALES DATA (Last 30 days)
-- =====================================================
-- Recent sales (today)
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems) VALUES
('INV-' || strftime('%Y%m%d', 'now') || '-001', datetime('now', '-2 hours'), 1, 1, '15.50', '0.00', '0.00', '15.50', '20.00', '4.50', 'Cash', 'Paid', 'Completed', 3),
('INV-' || strftime('%Y%m%d', 'now') || '-002', datetime('now', '-1 hour'), 2, 1, '28.75', '0.00', '0.00', '28.75', '28.75', '0.00', 'Card', 'Paid', 'Completed', 4),
('INV-' || strftime('%Y%m%d', 'now') || '-003', datetime('now', '-30 minutes'), NULL, 1, '12.99', '0.00', '0.00', '12.99', '15.00', '2.01', 'Cash', 'Paid', 'Completed', 1);

-- Yesterday's sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems) VALUES
('INV-' || strftime('%Y%m%d', 'now', '-1 day') || '-001', datetime('now', '-1 day', '10:30:00'), 3, 1, '45.25', '2.25', '0.00', '43.00', '43.00', '0.00', 'Card', 'Paid', 'Completed', 6),
('INV-' || strftime('%Y%m%d', 'now', '-1 day') || '-002', datetime('now', '-1 day', '14:15:00'), 1, 1, '32.50', '0.00', '0.00', '32.50', '35.00', '2.50', 'Cash', 'Paid', 'Completed', 4),
('INV-' || strftime('%Y%m%d', 'now', '-1 day') || '-003', datetime('now', '-1 day', '16:45:00'), 4, 1, '67.80', '0.00', '0.00', '67.80', '67.80', '0.00', 'Card', 'Paid', 'Completed', 8);

-- Week ago sales
INSERT INTO Sales (InvoiceNumber, SaleDate, CustomerId, UserId, Subtotal, DiscountAmount, TaxAmount, GrandTotal, AmountPaid, Change, PaymentMethod, PaymentStatus, Status, TotalItems) VALUES
('INV-' || strftime('%Y%m%d', 'now', '-7 days') || '-001', datetime('now', '-7 days', '09:15:00'), 2, 1, '89.95', '4.50', '0.00', '85.45', '85.45', '0.00', 'Card', 'Paid', 'Completed', 12),
('INV-' || strftime('%Y%m%d', 'now', '-7 days') || '-002', datetime('now', '-7 days', '13:30:00'), 5, 1, '156.75', '0.00', '0.00', '156.75', '160.00', '3.25', 'Cash', 'Paid', 'Completed', 18),
('INV-' || strftime('%Y%m%d', 'now', '-7 days') || '-003', datetime('now', '-7 days', '17:20:00'), NULL, 1, '23.50', '0.00', '0.00', '23.50', '25.00', '1.50', 'Cash', 'Paid', 'Completed', 3);

-- =====================================================
-- SALE ITEMS for the above sales
-- =====================================================
-- Today's sale items
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total) VALUES
-- Sale 1 (ID will be 1)
(1, 1, 2, '1.25', '2.50'),  -- 2x Coca Cola
(1, 6, 1, '2.25', '2.25'),  -- 1x Potato Chips
(1, 16, 1, '2.99', '2.99'),  -- 1x Bread

-- Sale 2 (ID will be 2)
(2, 11, 1, '8.99', '8.99'),  -- 1x USB Cable
(2, 7, 1, '2.75', '2.75'),   -- 1x Chocolate Bar
(2, 17, 1, '3.25', '3.25'),  -- 1x Milk
(2, 18, 1, '4.50', '4.50'),  -- 1x Eggs

-- Sale 3 (ID will be 3)
(3, 12, 1, '12.99', '12.99'); -- 1x Phone Charger

-- Yesterday's sale items
INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, Total) VALUES
-- Sale 4
(4, 13, 1, 39.99, 39.99), -- 1x Bluetooth Earbuds
(4, 4, 2, 0.75, 1.50),    -- 2x Water Bottle
(4, 8, 1, 3.50, 3.50),    -- 1x Peanuts

-- Sale 5
(5, 19, 2, 3.99, 7.98),   -- 2x Bananas
(5, 20, 1, 4.99, 4.99),   -- 1x Apples
(5, 2, 3, 1.20, 3.60),    -- 3x Pepsi

-- Sale 6
(6, 14, 1, 29.99, 29.99), -- 1x Power Bank
(6, 15, 2, 9.99, 19.98),  -- 2x Phone Case
(6, 5, 1, 2.00, 2.00);    -- 1x Energy Drink

-- =====================================================
-- BUSINESS EXPENSES
-- =====================================================
INSERT INTO BusinessExpenses (Description, Amount, Category, Date, Frequency, Notes, UserId) VALUES
('Monthly Rent', '2500.00', 1, date('now', 'start of month'), 1, 'Store rent payment', 1),
('Electricity Bill', '180.50', 2, date('now', '-5 days'), 1, 'Monthly electricity bill', 1),
('Office Supplies', '125.75', 3, date('now', '-3 days'), 0, 'Printer paper, pens, folders', 1),
('Marketing Materials', '350.00', 4, date('now', '-7 days'), 0, 'Promotional flyers and banners', 1),
('Equipment Maintenance', '95.00', 5, date('now', '-10 days'), 0, 'POS system maintenance', 1);

-- =====================================================
-- PURCHASE ORDERS
-- =====================================================
INSERT INTO PurchaseOrders (OrderNumber, OrderDate, DueDate, SupplierId, Subtotal, TaxAmount, GrandTotal, Status, PaymentMethod, Notes, CreatedByUserId, CreatedAt) VALUES
('PO-' || strftime('%Y%m%d', 'now', '-5 days') || '-001', date('now', '-5 days'), date('now', '+2 days'), 1, 450.00, 0.00, 450.00, 'Pending', 'Bank Transfer', 'Electronics restock order', 1, datetime('now')),
('PO-' || strftime('%Y%m%d', 'now', '-3 days') || '-002', date('now', '-3 days'), date('now', '+4 days'), 2, 280.50, 0.00, 280.50, 'In Transit', 'Bank Transfer', 'Weekly grocery order', 1, datetime('now')),
('PO-' || strftime('%Y%m%d', 'now', '-1 day') || '-003', date('now', '-1 day'), date('now', '+6 days'), 3, 195.75, 0.00, 195.75, 'Pending', 'Bank Transfer', 'Beverage restock', 1, datetime('now'));

-- =====================================================
-- PURCHASE ORDER ITEMS
-- =====================================================
INSERT INTO PurchaseOrderItems (PurchaseOrderId, ProductId, Quantity, UnitCost, SellingPrice) VALUES
-- PO 1 - Electronics
(1, 11, 20, 2.50, 8.99),   -- USB Cables
(1, 12, 15, 4.00, 12.99),   -- Phone Chargers
(1, 13, 8, 15.00, 39.99),  -- Bluetooth Earbuds
(1, 14, 10, 12.00, 29.99), -- Power Banks
(1, 15, 20, 3.50, 9.99),   -- Phone Cases

-- PO 2 - Groceries
(2, 16, 30, 1.20, 2.99),   -- Bread
(2, 17, 25, 1.50, 3.25),   -- Milk
(2, 18, 40, 2.00, 4.50),   -- Eggs
(2, 19, 35, 1.80, 3.99),   -- Bananas
(2, 20, 30, 2.20, 4.99),   -- Apples

-- PO 3 - Beverages
(3, 1, 50, 0.45, 1.25),    -- Coca Cola
(3, 2, 40, 0.43, 1.20),    -- Pepsi
(3, 3, 30, 1.20, 2.50),    -- Orange Juice
(3, 4, 80, 0.25, 0.75),    -- Water Bottles
(3, 5, 25, 0.80, 2.00);    -- Energy Drinks

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Test data population completed successfully!' AS Result;
SELECT 'Products added: ' || COUNT(*) AS ProductCount FROM Products;
SELECT 'Customers added: ' || COUNT(*) AS CustomerCount FROM Customers;
SELECT 'Sales created: ' || COUNT(*) AS SalesCount FROM Sales;
SELECT 'Suppliers added: ' || COUNT(*) AS SupplierCount FROM Suppliers;
