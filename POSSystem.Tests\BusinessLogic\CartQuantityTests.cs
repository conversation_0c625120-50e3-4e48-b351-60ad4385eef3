using Xunit;
using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;
using System.Linq;

namespace POSSystem.Tests.BusinessLogic
{
    public class CartQuantityTests
    {
        private SaleViewModel _viewModel;
        private Product _testProduct;

        public CartQuantityTests()
        {
            // Create a mock database service for testing
            var mockDbService = new MockDatabaseService();

            // Initialize the SaleViewModel with the mock service
            _viewModel = new SaleViewModel(mockDbService);

            // Create a test product
            _testProduct = new Product
            {
                Id = 1,
                Name = "Test Product",
                SellingPrice = 10.00m,
                StockQuantity = 100,
                Type = ProductType.Product,
                IsActive = true
            };
        }

        [Fact]
        public void AddToCart_WithDefaultQuantity_ShouldAddQuantityOfOne()
        {
            // Arrange
            _viewModel.CreateNewCart();

            // Act
            bool result = _viewModel.AddToCart(_testProduct);

            // Assert
            Assert.True(result);
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(1, cartItem.Quantity);
            Assert.Equal(_testProduct.Id, cartItem.Product.Id);
        }

        [Fact]
        public void AddToCart_WithExplicitQuantityOne_ShouldAddQuantityOfOne()
        {
            // Arrange
            _viewModel.CreateNewCart();

            // Act
            bool result = _viewModel.AddToCart(_testProduct, 1);

            // Assert
            Assert.True(result);
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(1, cartItem.Quantity);
        }

        [Fact]
        public void AddToCart_SameProductTwice_ShouldIncreaseQuantity()
        {
            // Arrange
            _viewModel.CreateNewCart();

            // Act
            _viewModel.AddToCart(_testProduct, 1);
            _viewModel.AddToCart(_testProduct, 1);

            // Assert
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(2, cartItem.Quantity);
        }

        [Fact]
        public void AddToCartCommand_ShouldAddQuantityOfOne()
        {
            // Arrange
            _viewModel.CreateNewCart();

            // Act
            _viewModel.AddToCartCommand.Execute(_testProduct);

            // Assert
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(1, cartItem.Quantity);
        }

        [Fact]
        public void AddToCart_WithCustomQuantity_ShouldAddCorrectQuantity()
        {
            // Arrange
            _viewModel.CreateNewCart();
            int customQuantity = 5;

            // Act
            bool result = _viewModel.AddToCart(_testProduct, customQuantity);

            // Assert
            Assert.True(result);
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(customQuantity, cartItem.Quantity);
        }

        [Fact]
        public void BarcodeScanner_And_ProductCard_ShouldProduceIdenticalResults()
        {
            // This test verifies the fix for the barcode scanner quantity calculation bug
            // Both barcode scanning and product card clicks should add exactly 1 unit

            // Arrange
            _viewModel.CreateNewCart();
            var testProduct1 = new Product
            {
                Id = 100,
                Name = "Test Product 1",
                SellingPrice = 10.00m,
                StockQuantity = 50,
                IsWeightBased = false // Unit-based product
            };
            var testProduct2 = new Product
            {
                Id = 101,
                Name = "Test Product 2",
                SellingPrice = 15.00m,
                StockQuantity = 50,
                IsWeightBased = false // Unit-based product
            };

            // Act - Simulate barcode scanner behavior (direct AddToCart call with explicit quantity)
            bool barcodeResult = _viewModel.AddToCart(testProduct1, 1.0m);

            // Act - Simulate product card click behavior (AddToCartCommand)
            _viewModel.AddToCartCommand.Execute(testProduct2);

            // Assert - Both methods should produce identical results
            Assert.True(barcodeResult);
            Assert.Equal(2, _viewModel.CurrentCart.Items.Count); // Two different products

            var barcodeItem = _viewModel.CurrentCart.Items.FirstOrDefault(i => i.Product.Id == testProduct1.Id);
            var productCardItem = _viewModel.CurrentCart.Items.FirstOrDefault(i => i.Product.Id == testProduct2.Id);

            Assert.NotNull(barcodeItem);
            Assert.NotNull(productCardItem);

            // Both should have exactly 1 unit
            Assert.Equal(1, barcodeItem.Quantity);
            Assert.Equal(1, productCardItem.Quantity);

            // Both should have correct pricing
            Assert.Equal(testProduct1.SellingPrice, barcodeItem.UnitPrice);
            Assert.Equal(testProduct2.SellingPrice, productCardItem.UnitPrice);

            // Total quantities should be identical
            Assert.Equal(barcodeItem.Quantity, productCardItem.Quantity);
        }

        [Fact]
        public void BarcodeScanner_And_ProductCard_SameProduct_ShouldIncrementCorrectly()
        {
            // This test verifies that scanning the same product multiple times
            // produces the same result as clicking the product card multiple times

            // Arrange
            _viewModel.CreateNewCart();

            // Act - Add same product via barcode scanner (simulated)
            _viewModel.AddToCart(_testProduct, 1.0m);

            // Act - Add same product via product card click (simulated)
            _viewModel.AddToCartCommand.Execute(_testProduct);

            // Assert - Should have one cart item with quantity 2
            Assert.Equal(1, _viewModel.CurrentCart.Items.Count);

            var cartItem = _viewModel.CurrentCart.Items.First();
            Assert.Equal(2, cartItem.Quantity); // 1 from barcode + 1 from product card
            Assert.Equal(_testProduct.Id, cartItem.Product.Id);
        }
    }

    // Mock database service for testing
    public class MockDatabaseService : IDbService
    {
        private List<Category> _categories = new List<Category>();

        public void AddCategory(Category category)
        {
            _categories.Add(category);
        }

        public void UpdateCategory(Category category)
        {
            var existing = _categories.FirstOrDefault(c => c.Id == category.Id);
            if (existing != null)
            {
                existing.Name = category.Name;
                existing.Description = category.Description;
            }
        }

        public void DeleteCategory(int id)
        {
            _categories.RemoveAll(c => c.Id == id);
        }

        public List<Category> GetAllCategories()
        {
            return _categories;
        }
    }
}
