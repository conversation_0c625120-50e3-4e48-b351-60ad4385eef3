using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Models;
using POSSystem.Services.ErrorHandling;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.InventoryManagement
{
    /// <summary>
    /// Focused service for inventory management operations
    /// Extracted from the large DatabaseService to improve maintainability
    /// </summary>
    public class InventoryManagementService : IInventoryManagementService
    {
        private readonly POSDbContext _context;
        private readonly ILogger<InventoryManagementService> _logger;
        private readonly IErrorHandlingService _errorHandler;

        public InventoryManagementService(
            POSDbContext context,
            ILogger<InventoryManagementService> logger = null,
            IErrorHandlingService errorHandler = null)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _errorHandler = errorHandler;
        }

        /// <summary>
        /// Get all categories
        /// </summary>
        public async Task<List<Category>> GetAllCategoriesAsync()
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.Categories
                    .AsNoTracking()
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }, "Get All Categories", new List<Category>()) ?? new List<Category>();
        }

        /// <summary>
        /// Get category by ID
        /// </summary>
        public async Task<Category> GetCategoryByIdAsync(int id)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Category ID must be greater than 0", nameof(id));

                return await _context.Categories
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.Id == id);
            }, "Get Category By ID", null) ?? null;
        }

        /// <summary>
        /// Add new category
        /// </summary>
        public async Task<int> AddCategoryAsync(Category category)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateCategory(category);

                // Check for duplicate name
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Name == category.Name);

                if (existingCategory != null)
                    throw new InvalidOperationException($"Category with name '{category.Name}' already exists");

                // Note: Category model doesn't have CreatedAt/UpdatedAt in current schema

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully added category {CategoryId} - {CategoryName}", category.Id, category.Name);
                return category.Id;
            }, "Add Category", 0);

            return result;
        }

        /// <summary>
        /// Update existing category
        /// </summary>
        public async Task<bool> UpdateCategoryAsync(Category category)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateCategory(category);

                if (category.Id <= 0)
                    throw new ArgumentException("Category ID must be greater than 0 for updates", nameof(category));

                var existingCategory = await _context.Categories.FindAsync(category.Id);
                if (existingCategory == null)
                    throw new InvalidOperationException($"Category with ID {category.Id} not found");

                // Check for duplicate name if changed
                if (category.Name != existingCategory.Name)
                {
                    var duplicateCategory = await _context.Categories
                        .FirstOrDefaultAsync(c => c.Name == category.Name && c.Id != category.Id);
                    
                    if (duplicateCategory != null)
                        throw new InvalidOperationException($"Category with name '{category.Name}' already exists");
                }

                // Update properties
                existingCategory.Name = category.Name;
                existingCategory.Description = category.Description;
                // Note: Category model doesn't have UpdatedAt in current schema

                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated category {CategoryId} - {CategoryName}", category.Id, category.Name);
                return true;
            }, "Update Category", false);

            return result;
        }

        /// <summary>
        /// Delete category
        /// </summary>
        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (id <= 0)
                    throw new ArgumentException("Category ID must be greater than 0", nameof(id));

                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                    throw new InvalidOperationException($"Category with ID {id} not found");

                // Check if category has any products
                var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id);
                if (hasProducts)
                {
                    throw new InvalidOperationException($"Cannot delete category '{category.Name}' because it contains products. Please move or delete the products first.");
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully deleted category {CategoryId} - {CategoryName}", id, category.Name);
                return true;
            }, "Delete Category", false);

            return result;
        }

        /// <summary>
        /// Add batch stock - DISABLED: BatchStock not implemented in current schema
        /// </summary>
        public async Task<int> AddBatchStockAsync(BatchStock batch)
        {
            // TODO: Implement when BatchStock is added to the database schema
            _logger?.LogWarning("AddBatchStockAsync called but BatchStock is not implemented in current schema");
            return await Task.FromResult(0);
        }

        /// <summary>
        /// Get batches for product - DISABLED: BatchStock not implemented in current schema
        /// </summary>
        public async Task<List<BatchStock>> GetBatchesForProductAsync(int productId)
        {
            // TODO: Implement when BatchStock is added to the database schema
            _logger?.LogWarning("GetBatchesForProductAsync called but BatchStock is not implemented in current schema");
            return await Task.FromResult(new List<BatchStock>());
        }

        /// <summary>
        /// Update batch stock - DISABLED: BatchStock not implemented in current schema
        /// </summary>
        public async Task<bool> UpdateBatchStockAsync(BatchStock batch)
        {
            // TODO: Implement when BatchStock is added to the database schema
            _logger?.LogWarning("UpdateBatchStockAsync called but BatchStock is not implemented in current schema");
            return await Task.FromResult(false);
        }

        /// <summary>
        /// Add stock to batch - DISABLED: BatchStock not implemented in current schema
        /// </summary>
        public async Task<bool> AddStockToBatchAsync(int batchId, int quantity)
        {
            // TODO: Implement when BatchStock is added to the database schema
            _logger?.LogWarning("AddStockToBatchAsync called but BatchStock is not implemented in current schema");
            return await Task.FromResult(false);
        }

        /// <summary>
        /// Get inventory transactions
        /// </summary>
        public async Task<List<InventoryTransaction>> GetInventoryTransactionsAsync(int? productId = null)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                IQueryable<InventoryTransaction> query = _context.InventoryTransactions
                    .AsNoTracking()
                    .Include(it => it.Product);

                if (productId.HasValue)
                {
                    query = query.Where(it => it.ProductId == productId.Value);
                }

                return await query
                    .OrderByDescending(it => it.TransactionDate)
                    .ToListAsync();
            }, "Get Inventory Transactions", new List<InventoryTransaction>()) ?? new List<InventoryTransaction>();
        }

        /// <summary>
        /// Add inventory transaction
        /// </summary>
        public async Task<int> AddInventoryTransactionAsync(InventoryTransaction transaction)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                ValidateInventoryTransaction(transaction);

                transaction.TransactionDate = DateTime.Now;

                _context.InventoryTransactions.Add(transaction);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully added inventory transaction {TransactionId} for product {ProductId}",
                    transaction.Id, transaction.ProductId);
                return transaction.Id;
            }, "Add Inventory Transaction", 0);

            return result;
        }

        /// <summary>
        /// Update product stock with reason tracking
        /// </summary>
        public async Task<bool> UpdateProductStockWithReasonAsync(int productId, decimal newQuantity, string reason)
        {
            var result = await _errorHandler?.HandleAsync(async () =>
            {
                if (productId <= 0)
                    throw new ArgumentException("Product ID must be greater than 0", nameof(productId));

                if (newQuantity < 0)
                    throw new ArgumentException("Stock quantity cannot be negative", nameof(newQuantity));

                if (string.IsNullOrWhiteSpace(reason))
                    throw new ArgumentException("Reason cannot be empty", nameof(reason));

                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                    throw new InvalidOperationException($"Product with ID {productId} not found");

                // Skip stock updates for services
                if (product.Type == ProductType.Service)
                {
                    _logger?.LogInformation("Skipping stock update for service {ProductId} - {ProductName}", productId, product.Name);
                    return true; // Return success but don't update stock
                }

                var oldQuantity = product.StockQuantity;
                var quantityChange = newQuantity - oldQuantity;

                // Update product stock
                product.StockQuantity = newQuantity;
                product.UpdatedAt = DateTime.Now;

                // Create inventory transaction record
                var transaction = new InventoryTransaction
                {
                    ProductId = productId,
                    TransactionType = quantityChange > 0 ? "Stock In" : "Stock Out",
                    Quantity = (int)Math.Round(Math.Abs(quantityChange)), // Round decimal to int for inventory tracking
                    Notes = $"{reason}. Previous: {oldQuantity}, New: {newQuantity}",
                    TransactionDate = DateTime.Now,
                    UserId = 1 // TODO: Get actual user ID from context
                };

                _context.InventoryTransactions.Add(transaction);
                await _context.SaveChangesAsync();

                _logger?.LogInformation("Successfully updated stock for product {ProductId} from {OldQuantity} to {NewQuantity}, reason: {Reason}", 
                    productId, oldQuantity, newQuantity, reason);
                return true;
            }, "Update Product Stock With Reason", false);

            return result;
        }

        /// <summary>
        /// Get top selling products for a period
        /// </summary>
        public async Task<List<Product>> GetTopSellingProductsAsync(int count, DateTime startDate, DateTime endDate)
        {
            return await _errorHandler?.HandleAsync(async () =>
            {
                return await _context.SaleItems
                    .AsNoTracking()
                    .Include(si => si.Product)
                        .ThenInclude(p => p.Category)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => si.ProductId)
                    .Select(g => new
                    {
                        Product = g.First().Product,
                        TotalQuantitySold = g.Sum(si => si.Quantity),
                        TotalRevenue = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(x => x.TotalQuantitySold)
                    .Take(count)
                    .Select(x => x.Product)
                    .ToListAsync();
            }, "Get Top Selling Products", new List<Product>()) ?? new List<Product>();
        }

        /// <summary>
        /// Validate category data
        /// </summary>
        private void ValidateCategory(Category category)
        {
            if (category == null)
                throw new ArgumentNullException(nameof(category), "Category cannot be null");

            if (string.IsNullOrWhiteSpace(category.Name))
                throw new ArgumentException("Category name cannot be empty", nameof(category));
        }

        /// <summary>
        /// Validate batch stock data
        /// </summary>
        private void ValidateBatchStock(BatchStock batch)
        {
            if (batch == null)
                throw new ArgumentNullException(nameof(batch), "Batch stock cannot be null");

            if (batch.ProductId <= 0)
                throw new ArgumentException("Product ID must be greater than 0", nameof(batch));

            if (string.IsNullOrWhiteSpace(batch.BatchNumber))
                throw new ArgumentException("Batch number cannot be empty", nameof(batch));

            if (batch.Quantity < 0)
                throw new ArgumentException("Batch quantity cannot be negative", nameof(batch));

            if (batch.PurchasePrice < 0)
                throw new ArgumentException("Purchase price cannot be negative", nameof(batch));
        }

        /// <summary>
        /// Validate inventory transaction data
        /// </summary>
        private void ValidateInventoryTransaction(InventoryTransaction transaction)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction), "Inventory transaction cannot be null");

            if (transaction.ProductId <= 0)
                throw new ArgumentException("Product ID must be greater than 0", nameof(transaction));

            if (string.IsNullOrWhiteSpace(transaction.TransactionType))
                throw new ArgumentException("Transaction type cannot be empty", nameof(transaction));

            if (transaction.Quantity < 0)
                throw new ArgumentException("Transaction quantity cannot be negative", nameof(transaction));
        }
    }
}
