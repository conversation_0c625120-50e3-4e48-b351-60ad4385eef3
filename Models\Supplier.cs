using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class Supplier
    {
        public Supplier()
        {
            Name = string.Empty;
            ContactName = string.Empty;
            Email = string.Empty;
            Phone = string.Empty;
            Address = string.Empty;
            Products = new List<Product>();
            PurchaseOrders = new List<PurchaseOrder>();
        }

        [Key]
        public int Id { get; set; }

        [Required]
        public string Name { get; set; }

        // Helper property for UI binding
        public string CompanyName => Name;

        [Required]
        public string ContactName { get; set; }

        [Required]
        public string Email { get; set; }

        [Required]
        public string Phone { get; set; }

        [Required]
        public string Address { get; set; }

        public string? Website { get; set; }

        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        public int ProductCount { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public virtual ICollection<Product> Products { get; set; }

        [InverseProperty(nameof(PurchaseOrder.Supplier))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
    }
} 