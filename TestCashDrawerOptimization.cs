using System;
using System.Diagnostics;
using POSSystem.Services;
using POSSystem.Data;

namespace POSSystem.Tests
{
    /// <summary>
    /// Simple test to verify cash drawer query optimizations
    /// </summary>
    public class CashDrawerOptimizationTest
    {
        public static void RunTest()
        {
            Console.WriteLine("Testing Cash Drawer Query Optimizations...");
            
            try
            {
                var dbService = new DatabaseService();
                var cashDrawerService = new CashDrawerService(dbService);
                
                // Test 1: Basic drawer query (should be fast)
                Console.WriteLine("\n1. Testing GetCurrentDrawerBasic()...");
                var stopwatch = Stopwatch.StartNew();
                var basicDrawer = cashDrawerService.GetCurrentDrawerBasic();
                stopwatch.Stop();
                Console.WriteLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"   Result: {(basicDrawer != null ? $"Found drawer ID {basicDrawer.Id}" : "No open drawer")}");
                
                // Test 2: Drawer with users (should be moderate)
                Console.WriteLine("\n2. Testing GetCurrentDrawerWithUsers()...");
                stopwatch.Restart();
                var drawerWithUsers = cashDrawerService.GetCurrentDrawerWithUsers();
                stopwatch.Stop();
                Console.WriteLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"   Result: {(drawerWithUsers != null ? $"Found drawer with user info" : "No open drawer")}");
                
                // Test 3: Full drawer query (should be slower due to transactions)
                Console.WriteLine("\n3. Testing GetCurrentDrawerWithDetails()...");
                stopwatch.Restart();
                var fullDrawer = cashDrawerService.GetCurrentDrawerWithDetails();
                stopwatch.Stop();
                Console.WriteLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"   Result: {(fullDrawer != null ? $"Found drawer with {fullDrawer.Transactions?.Count ?? 0} transactions" : "No open drawer")}");
                
                // Test 4: Legacy method (should be same as full details)
                Console.WriteLine("\n4. Testing GetCurrentDrawer() (legacy)...");
                stopwatch.Restart();
                var legacyDrawer = cashDrawerService.GetCurrentDrawer();
                stopwatch.Stop();
                Console.WriteLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"   Result: {(legacyDrawer != null ? $"Found drawer with {legacyDrawer.Transactions?.Count ?? 0} transactions" : "No open drawer")}");
                
                Console.WriteLine("\nOptimization test completed successfully!");
                Console.WriteLine("Use GetCurrentDrawerBasic() for status checks to improve performance.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during test: {ex.Message}");
            }
        }
    }
}
