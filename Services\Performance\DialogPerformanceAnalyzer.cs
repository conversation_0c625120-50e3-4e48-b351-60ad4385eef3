using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using MaterialDesignThemes.Wpf;
using POSSystem.Views.Dialogs;
using POSSystem.ViewModels;
using POSSystem.Models;
using POSSystem.Services.UI;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// Performance analyzer specifically for dialog opening and rendering performance
    /// </summary>
    public class DialogPerformanceAnalyzer
    {
        private readonly Stopwatch _stopwatch = new Stopwatch();

        /// <summary>
        /// Comprehensive dialog performance analysis
        /// </summary>
        public async Task<DialogPerformanceReport> AnalyzeDialogPerformanceAsync(SaleViewModel viewModel)
        {
            var report = new DialogPerformanceReport
            {
                TestStartTime = DateTime.Now
            };

            try
            {
                Debug.WriteLine("[DIALOG_PERF] Starting dialog performance analysis...");

                // Test CustomProductDialog performance
                await TestCustomProductDialogPerformance(report);

                // Test ProductDetailsDialog performance
                await TestProductDetailsDialogPerformance(report, viewModel);

                // Test PaymentProcessingDialog performance
                await TestPaymentProcessingDialogPerformance(report, viewModel);

                // Test dialog caching effectiveness
                await TestDialogCachingPerformance(report);

                report.TestEndTime = DateTime.Now;
                report.TotalTestDuration = report.TestEndTime - report.TestStartTime;

                Debug.WriteLine($"[DIALOG_PERF] Analysis completed in {report.TotalTestDuration.TotalMilliseconds:F2}ms");
                return report;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[DIALOG_PERF] Error during analysis: {ex.Message}");
                report.ErrorMessage = ex.Message;
                return report;
            }
        }

        private async Task TestCustomProductDialogPerformance(DialogPerformanceReport report)
        {
            var test = new DialogPerformanceTest { DialogType = "CustomProductDialog" };

            try
            {
                // Test dialog creation time
                _stopwatch.Restart();
                
                CustomProductDialog dialog = null;
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    dialog = DialogCacheService.Instance.GetOrCreateCustomProductDialog();
                }, DispatcherPriority.Normal);

                _stopwatch.Stop();
                test.CreationTime = _stopwatch.ElapsedMilliseconds;

                // Test dialog show time (simulate)
                _stopwatch.Restart();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Simulate dialog preparation for showing
                    dialog.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    dialog.Arrange(new Rect(dialog.DesiredSize));
                }, DispatcherPriority.Normal);
                _stopwatch.Stop();
                test.ShowTime = _stopwatch.ElapsedMilliseconds;

                test.TotalTime = test.CreationTime + test.ShowTime;
                test.Success = true;

                Debug.WriteLine($"[DIALOG_PERF] CustomProductDialog: Creation={test.CreationTime}ms, Show={test.ShowTime}ms, Total={test.TotalTime}ms");
            }
            catch (Exception ex)
            {
                test.ErrorMessage = ex.Message;
                test.Success = false;
                Debug.WriteLine($"[DIALOG_PERF] CustomProductDialog test failed: {ex.Message}");
            }

            report.CustomProductDialogTest = test;
        }

        private async Task TestProductDetailsDialogPerformance(DialogPerformanceReport report, SaleViewModel viewModel)
        {
            var test = new DialogPerformanceTest { DialogType = "ProductDetailsDialog" };

            try
            {
                // Create a test product
                var testProduct = new Product
                {
                    Id = 1,
                    Name = "Test Product",
                    SellingPrice = 10.00m,
                    StockQuantity = 100
                };

                // Test dialog creation time
                _stopwatch.Restart();
                
                ProductDetailsDialog dialog = null;
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    dialog = DialogCacheService.Instance.GetOrCreateProductDetailsDialog(testProduct, viewModel);
                }, DispatcherPriority.Normal);

                _stopwatch.Stop();
                test.CreationTime = _stopwatch.ElapsedMilliseconds;

                // Test dialog show time (simulate)
                _stopwatch.Restart();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Simulate dialog preparation for showing
                    dialog.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    dialog.Arrange(new Rect(dialog.DesiredSize));
                }, DispatcherPriority.Normal);
                _stopwatch.Stop();
                test.ShowTime = _stopwatch.ElapsedMilliseconds;

                test.TotalTime = test.CreationTime + test.ShowTime;
                test.Success = true;

                Debug.WriteLine($"[DIALOG_PERF] ProductDetailsDialog: Creation={test.CreationTime}ms, Show={test.ShowTime}ms, Total={test.TotalTime}ms");
            }
            catch (Exception ex)
            {
                test.ErrorMessage = ex.Message;
                test.Success = false;
                Debug.WriteLine($"[DIALOG_PERF] ProductDetailsDialog test failed: {ex.Message}");
            }

            report.ProductDetailsDialogTest = test;
        }

        private async Task TestPaymentProcessingDialogPerformance(DialogPerformanceReport report, SaleViewModel viewModel)
        {
            var test = new DialogPerformanceTest { DialogType = "PaymentProcessingDialog" };

            try
            {
                // Test ViewModel creation time
                _stopwatch.Restart();
                
                PaymentProcessingViewModel paymentViewModel = null;
                await Task.Run(() =>
                {
                    paymentViewModel = new PaymentProcessingViewModel(viewModel, "TestDialog");
                });

                _stopwatch.Stop();
                test.CreationTime = _stopwatch.ElapsedMilliseconds;

                // Test View creation time
                _stopwatch.Restart();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var paymentView = new POSSystem.Views.PaymentProcessingView
                    {
                        DataContext = paymentViewModel
                    };
                    
                    // Simulate dialog preparation for showing
                    paymentView.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    paymentView.Arrange(new Rect(paymentView.DesiredSize));
                }, DispatcherPriority.Normal);
                _stopwatch.Stop();
                test.ShowTime = _stopwatch.ElapsedMilliseconds;

                test.TotalTime = test.CreationTime + test.ShowTime;
                test.Success = true;

                Debug.WriteLine($"[DIALOG_PERF] PaymentProcessingDialog: Creation={test.CreationTime}ms, Show={test.ShowTime}ms, Total={test.TotalTime}ms");
            }
            catch (Exception ex)
            {
                test.ErrorMessage = ex.Message;
                test.Success = false;
                Debug.WriteLine($"[DIALOG_PERF] PaymentProcessingDialog test failed: {ex.Message}");
            }

            report.PaymentProcessingDialogTest = test;
        }

        private async Task TestDialogCachingPerformance(DialogPerformanceReport report)
        {
            var test = new DialogCachingTest();

            try
            {
                // Test first-time creation
                _stopwatch.Restart();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var dialog1 = DialogCacheService.Instance.GetOrCreateCustomProductDialog();
                }, DispatcherPriority.Normal);
                _stopwatch.Stop();
                test.FirstCreationTime = _stopwatch.ElapsedMilliseconds;

                // Test cached retrieval
                _stopwatch.Restart();
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var dialog2 = DialogCacheService.Instance.GetOrCreateCustomProductDialog();
                }, DispatcherPriority.Normal);
                _stopwatch.Stop();
                test.CachedRetrievalTime = _stopwatch.ElapsedMilliseconds;

                test.PerformanceImprovement = test.FirstCreationTime > 0 ? 
                    ((double)(test.FirstCreationTime - test.CachedRetrievalTime) / test.FirstCreationTime) * 100 : 0;

                var cacheStats = DialogCacheService.Instance.GetCacheStats();
                test.CacheHitRate = cacheStats.DialogCount > 0 ? 100.0 : 0.0; // Simplified calculation
                test.Success = true;

                Debug.WriteLine($"[DIALOG_PERF] Caching: First={test.FirstCreationTime}ms, Cached={test.CachedRetrievalTime}ms, Improvement={test.PerformanceImprovement:F1}%");
            }
            catch (Exception ex)
            {
                test.ErrorMessage = ex.Message;
                test.Success = false;
                Debug.WriteLine($"[DIALOG_PERF] Caching test failed: {ex.Message}");
            }

            report.CachingTest = test;
        }
    }

    #region Report Classes

    public class DialogPerformanceReport
    {
        public DateTime TestStartTime { get; set; }
        public DateTime TestEndTime { get; set; }
        public TimeSpan TotalTestDuration { get; set; }
        public string ErrorMessage { get; set; }

        public DialogPerformanceTest CustomProductDialogTest { get; set; }
        public DialogPerformanceTest ProductDetailsDialogTest { get; set; }
        public DialogPerformanceTest PaymentProcessingDialogTest { get; set; }
        public DialogCachingTest CachingTest { get; set; }

        public bool IsSuccessful => string.IsNullOrEmpty(ErrorMessage) &&
                                   (CustomProductDialogTest?.Success ?? false) &&
                                   (ProductDetailsDialogTest?.Success ?? false) &&
                                   (PaymentProcessingDialogTest?.Success ?? false) &&
                                   (CachingTest?.Success ?? false);

        public double AverageDialogCreationTime
        {
            get
            {
                var tests = new[] { CustomProductDialogTest, ProductDetailsDialogTest, PaymentProcessingDialogTest };
                double total = 0;
                int count = 0;

                foreach (var test in tests)
                {
                    if (test?.Success == true)
                    {
                        total += test.CreationTime;
                        count++;
                    }
                }

                return count > 0 ? total / count : 0;
            }
        }
    }

    public class DialogPerformanceTest
    {
        public string DialogType { get; set; }
        public long CreationTime { get; set; }
        public long ShowTime { get; set; }
        public long TotalTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class DialogCachingTest
    {
        public long FirstCreationTime { get; set; }
        public long CachedRetrievalTime { get; set; }
        public double PerformanceImprovement { get; set; }
        public double CacheHitRate { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion
}
