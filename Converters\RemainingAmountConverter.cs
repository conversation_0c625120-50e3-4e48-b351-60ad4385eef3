using System;
using System.Globalization;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class RemainingAmountConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length != 2 || values[0] == null || values[1] == null)
                return "0.00 DA";

            if (decimal.TryParse(values[0].ToString(), out decimal grandTotal) &&
                decimal.TryParse(values[1].ToString(), out decimal amountPaid))
            {
                decimal remainingAmount = Math.Max(0, grandTotal - amountPaid);
                return $"{remainingAmount:N2} DA";
            }

            return "0.00 DA";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 