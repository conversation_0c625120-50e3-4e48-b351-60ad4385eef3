<?xml version="1.0" encoding="utf-8" ?>
<UserControl x:Class="POSSystem.Views.DiscountDialog"
             x:Name="discountDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="450">
    <materialDesign:Card Width="450" Margin="16" materialDesign:ElevationAssist.Elevation="Dp2" UniformCornerRadius="8" Padding="0">
        <StackPanel Margin="0,0,35,0">
            <!-- Header -->
            <Grid Background="{DynamicResource PrimaryHueMidBrush}" Height="48">
                <StackPanel Orientation="Horizontal" Margin="16,0" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Tag" Width="24" Height="24" Foreground="{DynamicResource PrimaryHueMidForegroundBrush}" VerticalAlignment="Center"/>
                    <TextBlock Text="{DynamicResource ApplyDiscount}" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="18" Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"/>
                </StackPanel>
            </Grid>

            <!-- Content -->
            <StackPanel Margin="16">
                <!-- Discount Type Selection -->
                <TextBlock Text="{DynamicResource DiscountType}" Style="{StaticResource MaterialDesignBody1TextBlock}" Margin="0,0,0,8"/>
                <ItemsControl x:Name="DiscountTypeComboBox" Margin="0,0,0,16">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <RadioButton GroupName="DiscountTypes" 
                                       Content="{Binding Name, Converter={StaticResource ResourceKeyConverter}}"
                                       IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                       Style="{StaticResource MaterialDesignTabRadioButton}"
                                       Margin="0,0,8,0"
                                       Checked="DiscountType_SelectionChanged"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!-- Discount Value -->
                <TextBlock Text="{DynamicResource DiscountValue}" Style="{StaticResource MaterialDesignBody1TextBlock}" Margin="0,0,0,8"/>
                <TextBox x:Name="DiscountValueTextBox" 
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="Enter discount value"
                         materialDesign:TextFieldAssist.PrefixText=""
                         TextChanged="DiscountValue_TextChanged"
                         Margin="0,0,0,16"/>

                <!-- Discount Reason -->
                <TextBlock Text="{DynamicResource Reason}" Style="{StaticResource MaterialDesignBody1TextBlock}" Margin="0,0,0,8"/>
                <ComboBox x:Name="DiscountReasonComboBox"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          materialDesign:HintAssist.Hint="{DynamicResource SelectReason}"
                          DisplayMemberPath="Description"
                          Margin="0,0,0,16"/>

                <!-- Comment -->
                <TextBlock Text="{DynamicResource Comment}" Style="{StaticResource MaterialDesignBody1TextBlock}" Margin="0,0,0,8"/>
                <TextBox x:Name="CommentTextBox"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="{DynamicResource OptionalComment}"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         Height="80"
                         Margin="0,0,0,16"/>

                <!-- Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                            Content="{DynamicResource Cancel}"
                            Margin="0,0,8,0"/>
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="Apply_Click"
                            Content="{DynamicResource Apply}"/>
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</UserControl> 