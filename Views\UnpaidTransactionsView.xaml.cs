using POSSystem.Models;
using POSSystem.ViewModels;
using POSSystem.Services;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Xps.Packaging;
using System.IO;
using System.Windows.Xps;
using System.Linq;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Views
{
    public partial class UnpaidTransactionsView : UserControl
    {
        private UnpaidTransactionsViewModel ViewModel => (UnpaidTransactionsViewModel)DataContext;

        public UnpaidTransactionsView()
        {
            InitializeComponent();

            // Only set DataContext if it's not already set (allows DI injection from MainWindow)
            if (DataContext == null)
            {
                System.Diagnostics.Debug.WriteLine("[UNPAID VIEW DEBUG] DataContext is null, creating new UnpaidTransactionsViewModel");
                DataContext = App.ServiceProvider?.GetService<UnpaidTransactionsViewModel>() ??
                             new UnpaidTransactionsViewModel(new DatabaseService());
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[UNPAID VIEW DEBUG] DataContext already set via DI");
            }
        }

        private void ProcessSalePayment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Sale sale)
            {
                var paymentDialog = new PaymentDialog(sale);
                paymentDialog.ShowDialog();

                if (paymentDialog.DialogResult == true)
                {
                    ViewModel.ProcessSalePayment(sale, paymentDialog.PaymentAmount);
                }
            }
        }

        private async void ShowSaleDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Sale sale)
            {
                try
                {
                    // Show enhanced sale details dialog
                    var dialog = new POSSystem.Views.Dialogs.SaleDetailsDialog(sale);

                    // Try different DialogHost identifiers based on context
                    string[] dialogIdentifiers = { "RootDialog", "MainWindowCashDrawerDialog" };
                    bool dialogShown = false;

                    foreach (var identifier in dialogIdentifiers)
                    {
                        try
                        {
                            // Close any existing dialogs first
                            if (MaterialDesignThemes.Wpf.DialogHost.IsDialogOpen(identifier))
                            {
                                MaterialDesignThemes.Wpf.DialogHost.Close(identifier);
                                await System.Threading.Tasks.Task.Delay(100);
                            }

                            await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, identifier);
                            dialogShown = true;
                            break;
                        }
                        catch (InvalidOperationException)
                        {
                            // This DialogHost identifier doesn't exist, try the next one
                            continue;
                        }
                    }

                    // If no DialogHost worked, try without identifier
                    if (!dialogShown)
                    {
                        await MaterialDesignThemes.Wpf.DialogHost.Show(dialog);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Error viewing sale details: {ex.Message}",
                        "Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private void CloseDetails_Click(object sender, RoutedEventArgs e)
        {
            saleDetailsPopup.IsOpen = false;
        }

        private void PrintSaleDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Sale sale)
            {
                try
                {
                    // Create print service and print the sale
                    var printService = new SalePrintService();
                    bool success = printService.PrintSale(sale);
                    
                    if (!success)
                    {
                        MessageBox.Show(
                            Application.Current.TryFindResource("PrintingCancelled") as string ?? "Printing was cancelled.",
                            Application.Current.TryFindResource("Information") as string ?? "Information",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Error printing sale details: {ex.Message}",
                        Application.Current.TryFindResource("Error") as string ?? "Error",
                    MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private void StatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is UnpaidTransactionsViewModel viewModel && sender is ComboBox comboBox)
            {
                var selectedItem = comboBox.SelectedItem as ComboBoxItem;
                if (selectedItem == null) return;

                var filterText = selectedItem.Content.ToString();
                var today = DateTime.Now.Date;

                // Get all unpaid sales
                var allSales = viewModel.UnpaidSales != null ? 
                    viewModel.UnpaidSales.ToList() : 
                    new List<Sale>();

                // Apply filter to individual sales
                IEnumerable<Sale> filteredSales;
                if (filterText == Application.Current.FindResource("All") as string)
                {
                    filteredSales = allSales;
                }
                else if (filterText == Application.Current.FindResource("Overdue") as string)
                {
                    filteredSales = allSales.Where(s => s.DueDate.HasValue && s.DueDate.Value.Date < today);
                }
                else if (filterText == Application.Current.FindResource("DueToday") as string)
                {
                    filteredSales = allSales.Where(s => s.DueDate.HasValue && s.DueDate.Value.Date == today);
                }
                else
                {
                    return;
                }

                // Group the filtered sales by customer
                var groupedSales = filteredSales
                    .GroupBy(s => new { 
                        CustomerId = s.CustomerId ?? -1, 
                        CustomerName = s.Customer != null ? $"{s.Customer.FirstName} {s.Customer.LastName}" : "Guest Customer" 
                    })
                    .Select(g => new UnpaidTransactionsViewModel.CustomerUnpaidGroup(viewModel.ProcessAllCustomerPayments)
                    {
                        CustomerName = g.Key.CustomerName,
                        Sales = new ObservableCollection<Sale>(g.OrderByDescending(s => s.SaleDate)),
                        TotalUnpaidAmount = g.Sum(s => s.RemainingAmount),
                        TotalUnpaidTransactions = g.Count()
                    })
                    .OrderByDescending(g => g.TotalUnpaidAmount);

                // Update the view model
                viewModel.GroupedUnpaidSales = new ObservableCollection<UnpaidTransactionsViewModel.CustomerUnpaidGroup>(groupedSales);
                viewModel.UnpaidSales = new ObservableCollection<Sale>(filteredSales);

                // Update totals
                var overdueSales = allSales.Where(s => s.DueDate.HasValue && s.DueDate.Value.Date < today).ToList();
                viewModel.UnpaidSalesTotal = filteredSales.Sum(s => s.RemainingAmount);
                viewModel.OverdueSalesCount = overdueSales.Count();
                viewModel.HasOverdueSales = overdueSales.Any();
                viewModel.PastDueSalesAmount = overdueSales.Sum(s => s.RemainingAmount);
            }
        }
    }
} 