<UserControl x:Class="POSSystem.Views.Dialogs.AdminDraftCompletionDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             Background="Transparent">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        
        <!-- Custom styles for the admin completion dialog -->
        <Style x:Key="AdminDialogCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="24"/>
            <Setter Property="MaxWidth" Value="1000"/>
            <Setter Property="MinHeight" Value="700"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        </Style>

        <Style x:Key="SectionCard" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
        </Style>

        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>
    </UserControl.Resources>

    <materialDesign:Card Style="{StaticResource AdminDialogCard}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="FileDocumentCheck" 
                                       Width="32" Height="32" 
                                       Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                       VerticalAlignment="Center"/>
                <TextBlock Text="Complete Draft Invoice" 
                          Style="{StaticResource HeaderTextBlock}"
                          Margin="12,0,0,0" 
                          VerticalAlignment="Center"/>
                <materialDesign:Chip Content="Admin Review" 
                                   Margin="16,0,0,0" 
                                   Background="{DynamicResource PrimaryHueMidBrush}"
                                   Foreground="White"/>
            </StackPanel>

            <!-- Draft Information -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource SectionCard}">
                <StackPanel>
                    <TextBlock Text="Draft Information" 
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              Margin="0,0,0,8"/>
                    <TextBlock Text="{Binding DraftInfo}" 
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              TextWrapping="Wrap"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Main Content -->
            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="0,8,0,0">
                <StackPanel>
                    <!-- Invoice Details Section -->
                    <materialDesign:Card Style="{StaticResource SectionCard}">
                        <StackPanel>
                            <TextBlock Text="Invoice Details" 
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                      Margin="0,0,0,12"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="16"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <!-- Invoice Number -->
                                <TextBox Grid.Column="0" Grid.Row="0"
                                        materialDesign:HintAssist.Hint="Invoice Number"
                                        Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <!-- Issue Date -->
                                <DatePicker Grid.Column="2" Grid.Row="0"
                                           materialDesign:HintAssist.Hint="Issue Date"
                                           SelectedDate="{Binding IssueDate}"
                                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                                
                                <!-- Due Date -->
                                <DatePicker Grid.Column="4" Grid.Row="0"
                                           materialDesign:HintAssist.Hint="Due Date"
                                           SelectedDate="{Binding DueDate}"
                                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                                
                                <!-- Payment Terms -->
                                <ComboBox Grid.Column="0" Grid.Row="2"
                                         materialDesign:HintAssist.Hint="Payment Terms"
                                         ItemsSource="{Binding PaymentTermsOptions}"
                                         SelectedItem="{Binding PaymentTerms}"
                                         IsEditable="True"
                                         Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                                
                                <!-- Reference -->
                                <TextBox Grid.Column="2" Grid.Row="2"
                                        materialDesign:HintAssist.Hint="Reference (Optional)"
                                        Text="{Binding Reference}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <!-- Customer -->
                                <TextBox Grid.Column="4" Grid.Row="2"
                                        materialDesign:HintAssist.Hint="Customer"
                                        Text="{Binding CustomerName}"
                                        IsReadOnly="True"
                                        Background="{DynamicResource MaterialDesignDivider}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                    
                    <!-- Items Section -->
                    <materialDesign:Card Style="{StaticResource SectionCard}">
                        <StackPanel>
                            <TextBlock Text="Invoice Items" 
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                      Margin="0,0,0,12"/>
                            
                            <!-- Items Header -->
                            <Grid Background="{DynamicResource MaterialDesignDivider}" 
                                  Margin="0,0,0,8" 
                                  Height="32">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="Product" 
                                          VerticalAlignment="Center" 
                                          Margin="8,0"
                                          FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" Text="Quantity" 
                                          VerticalAlignment="Center" 
                                          TextAlignment="Center"
                                          FontWeight="Medium"/>
                                <TextBlock Grid.Column="2" Text="Unit Price" 
                                          VerticalAlignment="Center" 
                                          TextAlignment="Right"
                                          FontWeight="Medium"/>
                                <TextBlock Grid.Column="3" Text="Total" 
                                          VerticalAlignment="Center" 
                                          TextAlignment="Right" 
                                          Margin="0,0,8,0"
                                          FontWeight="Medium"/>
                            </Grid>
                            
                            <!-- Items List -->
                            <ItemsControl ItemsSource="{Binding DraftItems}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="{DynamicResource MaterialDesignDivider}" 
                                               BorderThickness="0,0,0,1" 
                                               Padding="0,8">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="80"/>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="100"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0" Margin="8,0">
                                                    <TextBlock Text="{Binding ProductName}" 
                                                              FontWeight="Medium"/>
                                                    <TextBlock Text="{Binding ProductSku}" 
                                                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                </StackPanel>
                                                
                                                <TextBlock Grid.Column="1" 
                                                          Text="{Binding QuantityDisplay}" 
                                                          VerticalAlignment="Center" 
                                                          TextAlignment="Center"/>
                                                
                                                <TextBlock Grid.Column="2" 
                                                          Text="{Binding UnitPriceDisplay}" 
                                                          VerticalAlignment="Center" 
                                                          TextAlignment="Right"/>
                                                
                                                <TextBlock Grid.Column="3" 
                                                          Text="{Binding TotalDisplay}" 
                                                          VerticalAlignment="Center" 
                                                          TextAlignment="Right" 
                                                          FontWeight="Bold"
                                                          Margin="0,0,8,0"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </materialDesign:Card>
                    
                    <!-- Pricing Section -->
                    <materialDesign:Card Style="{StaticResource SectionCard}">
                        <StackPanel>
                            <TextBlock Text="Pricing &amp; Totals"
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                      Margin="0,0,0,12"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="16"/>
                                    <ColumnDefinition Width="200"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Discount Amount -->
                                <TextBox Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Discount Amount"
                                        Text="{Binding DiscountAmount, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <!-- Tax Amount -->
                                <TextBox Grid.Column="2"
                                        materialDesign:HintAssist.Hint="Tax Amount"
                                        Text="{Binding TaxAmount, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <!-- Totals Summary -->
                                <StackPanel Grid.Column="4">
                                    <Grid Margin="0,0,0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Subtotal:" FontWeight="Medium"/>
                                        <TextBlock Grid.Column="1" Text="{Binding SubtotalDisplay}" FontWeight="Bold"/>
                                    </Grid>
                                    
                                    <Grid Margin="0,0,0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Discount:" FontWeight="Medium"/>
                                        <TextBlock Grid.Column="1" Text="{Binding DiscountAmountDisplay}" FontWeight="Bold"/>
                                    </Grid>
                                    
                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Tax:" FontWeight="Medium"/>
                                        <TextBlock Grid.Column="1" Text="{Binding TaxAmountDisplay}" FontWeight="Bold"/>
                                    </Grid>
                                    
                                    <Border Background="{DynamicResource PrimaryHueMidBrush}" 
                                           Padding="8,4" 
                                           CornerRadius="4">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" 
                                                      Text="Grand Total:" 
                                                      FontWeight="Bold" 
                                                      Foreground="White"/>
                                            <TextBlock Grid.Column="1" 
                                                      Text="{Binding GrandTotalDisplay}" 
                                                      FontWeight="Bold" 
                                                      FontSize="16"
                                                      Foreground="White"/>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                    
                    <!-- Notes Section -->
                    <materialDesign:Card Style="{StaticResource SectionCard}">
                        <TextBox materialDesign:HintAssist.Hint="Notes (Optional)"
                                Text="{Binding Notes}"
                                AcceptsReturn="True"
                                TextWrapping="Wrap"
                                MinLines="3"
                                MaxLines="5"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>
            
            <!-- Status Message -->
            <TextBlock Grid.Row="3" 
                      Text="{Binding StatusMessage}" 
                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                      HorizontalAlignment="Center"
                      Margin="0,8"
                      Visibility="{Binding StatusMessage, Converter={StaticResource BoolToVisibilityConverter}}"/>
            
            <!-- Action Buttons -->
            <StackPanel Grid.Row="4" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right" 
                       Margin="0,16,0,0">
                <Button Content="Reject Draft" 
                       Command="{Binding RejectDraftCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Foreground="{DynamicResource SecondaryHueMidBrush}"
                       BorderBrush="{DynamicResource SecondaryHueMidBrush}"
                       Margin="0,0,8,0"/>
                <Button Content="Save Draft" 
                       Command="{Binding SaveDraftCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,8,0"/>
                <Button Content="Cancel" 
                       Command="{Binding CancelCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,8,0"/>
                <Button Content="Complete Invoice" 
                       Command="{Binding CompleteInvoiceCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"/>
            </StackPanel>
            
            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="5" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Value="0"
                                IsIndeterminate="True"
                                Width="48" Height="48"/>
                    <TextBlock Text="{Binding StatusMessage}" 
                              Style="{StaticResource MaterialDesignBody1TextBlock}"
                              Foreground="White"
                              HorizontalAlignment="Center"
                              Margin="0,16,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </materialDesign:Card>
</UserControl>
