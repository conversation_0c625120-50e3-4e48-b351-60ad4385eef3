<Window x:Class="POSSystem.Views.AddBatchDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{DynamicResource AddNewBatch}"
        MinWidth="600"
        MinHeight="500"
        Width="700"
        Height="600"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        Style="{StaticResource AppWindowStyle}">

    <!-- DialogHost for handling nested dialogs -->
    <md:DialogHost Identifier="AddBatchDialog" Margin="0">
        <!-- Main Dialog Card with Enhanced Styling -->
        <md:Card x:Name="MainDialogCard"
                 Background="{DynamicResource MaterialDesignPaper}"
                 Foreground="{DynamicResource MaterialDesignBody}"
                 md:ElevationAssist.Elevation="Dp6"
                 Margin="20"
                 MaxWidth="800"
                 MaxHeight="700">

            <Grid Margin="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header Section with Gradient Background -->
                <Border Grid.Row="0"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        CornerRadius="4,4,0,0">
                    <Grid Margin="24,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{DynamicResource AddNewBatch}"
                                      FontSize="20"
                                      FontWeight="Bold"
                                      Foreground="White"/>
                            <TextBlock Text="{DynamicResource BatchDetails}"
                                      FontSize="14"
                                      Foreground="White"
                                      Opacity="0.9"/>
                        </StackPanel>

                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Command="{x:Static md:DialogHost.CloseDialogCommand}"
                                Foreground="White"
                                ToolTip="{DynamicResource Close}">
                            <md:PackIcon Kind="Close" />
                        </Button>
                    </Grid>
                </Border>

                <!-- Content Section with Tab-Based Interface -->
                <TabControl Grid.Row="1"
                           Margin="24,16,24,0"
                           Style="{StaticResource MaterialDesignTabControl}">

                    <!-- Basic Information Tab -->
                    <TabItem Header="{DynamicResource BatchDetails}"
                            Style="{StaticResource MaterialDesignTabItem}">
                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                     Margin="0,16,0,0">
                            <StackPanel>

                                <!-- Batch Number -->
                                <TextBox x:Name="txtBatchNumber"
                                        Style="{StaticResource AppTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource BatchNumber}"
                                        md:HintAssist.IsFloating="True"/>

                                <!-- Quantity -->
                                <TextBox x:Name="txtQuantity"
                                        Style="{StaticResource AppTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource Quantity}"
                                        md:HintAssist.IsFloating="True"
                                        PreviewTextInput="DecimalValidation_PreviewTextInput"/>

                                <!-- Purchase Price -->
                                <TextBox x:Name="txtPurchasePrice"
                                        Style="{StaticResource AppTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource PurchasePrice}"
                                        md:HintAssist.IsFloating="True"/>

                                <!-- Selling Price -->
                                <TextBox x:Name="txtSellingPrice"
                                         Style="{StaticResource AppTextBoxStyle}"
                                         md:HintAssist.Hint="{DynamicResource SellingPrice}"
                                         md:HintAssist.IsFloating="True"
                                         PreviewTextInput="DecimalValidation_PreviewTextInput"/>

                                <!-- Manufacturing Date -->
                                <DatePicker x:Name="dpManufactureDate"
                                           Style="{StaticResource AppDatePickerStyle}"
                                           md:HintAssist.Hint="{DynamicResource ManufacturingDate}"
                                           md:HintAssist.IsFloating="True"/>

                                <!-- Expiry Date -->
                                <DatePicker x:Name="dpExpiryDate"
                                           Style="{StaticResource AppDatePickerStyle}"
                                           md:HintAssist.Hint="{DynamicResource ExpiryDate}"
                                           md:HintAssist.IsFloating="True"/>

                                <!-- Location -->
                                <TextBox x:Name="txtLocation"
                                        Style="{StaticResource AppTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource Location}"
                                        md:HintAssist.IsFloating="True"/>

                                <!-- Notes -->
                                <TextBox x:Name="txtNotes"
                                        Style="{StaticResource AppTextBoxStyle}"
                                        md:HintAssist.Hint="{DynamicResource Notes}"
                                        md:HintAssist.IsFloating="True"
                                        Height="80"
                                        TextWrapping="Wrap"
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"/>

                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>
                </TabControl>

                <!-- Action Buttons Section -->
                <Border Grid.Row="2"
                        Background="{DynamicResource MaterialDesignDivider}"
                        Padding="24,16">
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Right">
                        <Button Click="Save_Click"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="White"
                                Height="40"
                                Padding="32,0"
                                Margin="0,0,16,0"
                                md:ButtonAssist.CornerRadius="4">
                            <StackPanel Orientation="Horizontal">
                                <md:PackIcon Kind="ContentSave"
                                            VerticalAlignment="Center"
                                            Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource Save}"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Content="{DynamicResource Cancel}"
                                Click="Cancel_Click"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Height="40"
                                Padding="32,0"
                                md:ButtonAssist.CornerRadius="4"/>
                    </StackPanel>
                </Border>
            </Grid>
        </md:Card>
    </md:DialogHost>
</Window>