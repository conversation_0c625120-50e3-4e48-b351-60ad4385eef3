using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using POSSystem.Models;

namespace POSSystem.Models
{
    public class Invoice
    {
        public Invoice()
        {
            Items = new HashSet<InvoiceItem>();
            Payments = new HashSet<InvoicePayment>();
            Notifications = new HashSet<DraftInvoiceNotification>();
            IssueDate = DateTime.Now;
            DueDate = DateTime.Now.AddDays(30);
            Status = "Draft";
            PaymentTerms = "Net 30";
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; }

        [Required]
        [MaxLength(20)]
        public string Type { get; set; } // Sales/Purchase

        [Required]
        public DateTime IssueDate { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        [ForeignKey("Customer")]
        public int? CustomerId { get; set; }

        [ForeignKey("Supplier")]
        public int? SupplierId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Subtotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal GrandTotal { get; set; }

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } // Draft/Issued/Paid/Overdue/Cancelled

        [MaxLength(100)]
        public string PaymentTerms { get; set; }

        [MaxLength(100)]
        public string Reference { get; set; }

        public string Notes { get; set; }

        // Two-tier invoice system properties
        [Required]
        [ForeignKey("CreatedByUser")]
        public int CreatedByUserId { get; set; }

        [ForeignKey("CompletedByUser")]
        public int? CompletedByUserId { get; set; }

        [Required]
        public DateTime DraftCreatedAt { get; set; }

        public DateTime? AdminCompletedAt { get; set; }

        public bool RequiresAdminCompletion { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; }
        public virtual Supplier Supplier { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual User CompletedByUser { get; set; }
        public virtual ICollection<InvoiceItem> Items { get; set; }
        public virtual ICollection<InvoicePayment> Payments { get; set; }
        public virtual ICollection<DraftInvoiceNotification> Notifications { get; set; }

        // Computed properties for UI
        [NotMapped]
        public bool IsDraft => Status == "Draft";

        [NotMapped]
        public bool IsPendingAdminCompletion => RequiresAdminCompletion && IsDraft;

        [NotMapped]
        public string CreatedByUserName => CreatedByUser?.FirstName + " " + CreatedByUser?.LastName;

        [NotMapped]
        public string CompletedByUserName => CompletedByUser?.FirstName + " " + CompletedByUser?.LastName;

        [NotMapped]
        public int ItemCount => Items?.Count ?? 0;

        [NotMapped]
        public int DaysPending => IsPendingAdminCompletion ? (DateTime.Now - DraftCreatedAt).Days : 0;

        [NotMapped]
        public bool IsOverdue => DaysPending > 7; // Configurable threshold

        [NotMapped]
        public string CustomerName => Customer?.FirstName + " " + Customer?.LastName;

        [NotMapped]
        public string SupplierName => Supplier?.CompanyName;

        [NotMapped]
        public string StatusDisplayText => IsPendingAdminCompletion ? "Pending Admin Completion" : Status;

        // Helper methods
        public void MarkAsCompleted(User completedByUser)
        {
            RequiresAdminCompletion = false;
            CompletedByUserId = completedByUser.Id;
            CompletedByUser = completedByUser;
            AdminCompletedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;

            // Change status from Draft to Issued when completed
            if (Status == "Draft")
            {
                Status = "Issued";
            }
        }

        public void CalculateTotals()
        {
            if (Items != null && Items.Any())
            {
                Subtotal = Items.Sum(i => i.Total);
                GrandTotal = Subtotal - DiscountAmount + TaxAmount;
            }
            else
            {
                Subtotal = 0;
                GrandTotal = 0;
            }
        }

        public bool CanBeModified()
        {
            return Status == "Draft" || Status == "Issued";
        }

        public bool CanBeDeleted()
        {
            return Status == "Draft" || (Status == "Issued" && !Payments.Any());
        }
    }
}